#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
最终重构验证测试
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append('.')

def test_new_modules():
    """测试新模块导入"""
    print("=== 重构验证测试 ===")
    print()
    
    success_count = 0
    total_count = 0
    
    # 测试新模块导入
    modules_to_test = [
        ('ui.processors.RegisterUpdateProcessor', 'RegisterUpdateProcessor'),
        ('ui.managers.ApplicationLifecycleManager', 'ApplicationLifecycleManager'),
        ('ui.managers.UIUtilityManager', 'UIUtilityManager'),
        ('ui.managers.RegisterDisplayManager', 'RegisterDisplayManager'),
        ('ui.coordinators.EventCoordinator', 'EventCoordinator'),
        ('ui.factories.ToolWindowFactory', 'ToolWindowFactory')
    ]
    
    for module_path, class_name in modules_to_test:
        total_count += 1
        try:
            module = __import__(module_path, fromlist=[class_name])
            getattr(module, class_name)
            print(f'✓ {class_name} 导入成功')
            success_count += 1
        except Exception as e:
            print(f'✗ {class_name} 导入失败: {e}')
    
    print()
    print("=== 检查文件结构 ===")
    
    # 检查新创建的文件
    files_to_check = [
        'ui/processors/RegisterUpdateProcessor.py',
        'ui/managers/ApplicationLifecycleManager.py', 
        'ui/managers/UIUtilityManager.py',
        'ui/processors/__init__.py',
        'ui/managers/RegisterDisplayManager.py',
        'ui/coordinators/EventCoordinator.py',
        'ui/factories/ToolWindowFactory.py'
    ]
    
    file_success = 0
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f'✓ {file_path} 存在')
            file_success += 1
        else:
            print(f'✗ {file_path} 不存在')
    
    print()
    print("=== 重构完成统计 ===")
    
    # 统计主窗口文件行数
    try:
        with open('ui/windows/RegisterMainWindow.py', 'r', encoding='utf-8') as f:
            lines = len(f.readlines())
        print(f'主窗口文件当前行数: {lines}')
    except Exception as e:
        print(f'无法读取主窗口文件: {e}')
    
    print()
    print(f"模块导入测试: {success_count}/{total_count} 成功")
    print(f"文件结构检查: {file_success}/{len(files_to_check)} 成功")
    
    if success_count == total_count and file_success == len(files_to_check):
        print("🎉 重构验证完成！所有测试通过！")
        return True
    else:
        print("❌ 部分测试失败，需要检查")
        return False

if __name__ == "__main__":
    success = test_new_modules()
    sys.exit(0 if success else 1)
