#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
选择性寄存器操作插件
允许用户选择特定的寄存器进行批量读写操作
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QPushButton, QTreeWidget, QTreeWidgetItem,
                             QGroupBox, QCheckBox, QProgressBar, QTextEdit,
                             QSplitter, QMessageBox, QComboBox, QLineEdit,
                             QTabWidget, QTableWidget, QTableWidgetItem,
                             QHeaderView, QAbstractItemView)
from PyQt5.QtCore import pyqtSignal, Qt, QTimer
from PyQt5.QtGui import QFont
from core.services.plugin.PluginManager import IToolWindowPlugin
from utils.Log import get_module_logger

# 导入配置管理器
try:
    from plugins.config.selective_register_config import get_config, RegisterGroupMatcher
except ImportError:
    # 如果配置模块不存在，使用默认配置
    get_config = None
    RegisterGroupMatcher = None

logger = get_module_logger(__name__)


class SelectiveRegisterWindow(QWidget):
    """选择性寄存器操作窗口"""
    
    # 定义信号
    window_closed = pyqtSignal()
    
    def __init__(self, parent=None):
        """初始化选择性寄存器操作窗口"""
        super().__init__(parent)

        # 加载配置
        self.config = get_config() if get_config else None

        # 设置窗口属性
        self._setup_window()

        # 获取主窗口引用
        self.main_window = parent
        self.register_manager = None
        self.spi_service = None

        # 操作状态
        self.is_operating = False
        self.selected_registers = set()
        self.register_groups = {}

        # 从配置加载模板
        self.templates = self._load_templates_from_config()

        # 分组匹配器
        self.group_matcher = self._create_group_matcher()

        self._setup_ui()
        self._connect_signals()
        self._initialize_data()

    def _setup_window(self):
        """设置窗口属性"""
        if self.config:
            window_config = self.config.get_window_config()
            title = window_config.get("title", "选择性寄存器操作")
            width = window_config.get("width", 900)
            height = window_config.get("height", 700)
            min_width = window_config.get("min_width", 600)
            min_height = window_config.get("min_height", 500)
        else:
            title = "选择性寄存器操作"
            width, height = 900, 700
            min_width, min_height = 600, 500

        self.setWindowTitle(title)
        self.resize(width, height)
        self.setMinimumSize(min_width, min_height)

        # 设置窗口标志
        from PyQt5.QtCore import Qt
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint |
                           Qt.WindowMinimizeButtonHint | Qt.WindowMaximizeButtonHint)

    def _load_templates_from_config(self):
        """从配置加载模板"""
        templates = {}

        if self.config:
            preset_templates = self.config.get_preset_templates()
            for template in preset_templates:
                name = template.get("name", "")
                addresses = template.get("addresses", [])
                if name and addresses:
                    templates[name] = addresses
        else:
            # 默认模板
            templates = {
                "PLL控制寄存器": ["0x50", "0x63", "0x65", "0x67", "0x69", "0x6B", "0x6C"],
                "时钟输出0-1": ["0x10", "0x11", "0x12", "0x13", "0x14", "0x15", "0x16", "0x17"],
                "时钟输出2-3": ["0x18", "0x19", "0x1A", "0x1B", "0x1C", "0x1D", "0x1E", "0x1F"],
                "时钟输出4-5": ["0x20", "0x21", "0x22", "0x23", "0x24", "0x25", "0x26", "0x27"],
                "SYSREF控制": ["0x49", "0x4A", "0x4C", "0x4E", "0x53", "0x54", "0x55"],
                "电源管理": ["0x02", "0x50", "0x83"],
            }

        return templates

    def _create_group_matcher(self):
        """创建分组匹配器"""
        if self.config and RegisterGroupMatcher:
            group_definitions = self.config.get_register_groups()
            return RegisterGroupMatcher(group_definitions)
        return None

    def _get_button_text(self, key: str, default: str = "") -> str:
        """获取按钮文本"""
        if self.config:
            return self.config.get_button_text(key, default)
        return default

    def _get_label_text(self, key: str, default: str = "", **kwargs) -> str:
        """获取标签文本"""
        if self.config:
            return self.config.get_label_text(key, default, **kwargs)
        return default.format(**kwargs) if kwargs else default

    def _get_message_text(self, key: str, default: str = "", **kwargs) -> str:
        """获取消息文本"""
        if self.config:
            return self.config.get_message_text(key, default, **kwargs)
        return default.format(**kwargs) if kwargs else default

    def _get_tab_text(self, key: str, default: str = "") -> str:
        """获取标签页文本"""
        if self.config:
            return self.config.get_tab_text(key, default)
        return default

    def _get_group_text(self, key: str, default: str = "") -> str:
        """获取分组文本"""
        if self.config:
            return self.config.get_group_text(key, default)
        return default

    def _get_table_headers(self, key: str, default: list = None) -> list:
        """获取表格标题"""
        if self.config:
            headers = self.config.get_table_headers(key)
            return headers if headers else (default or [])
        return default or []

    def _get_button_text(self, key: str, default: str = "") -> str:
        """获取按钮文本"""
        if self.config:
            return self.config.get_button_text(key, default)
        return default

    def _get_label_text(self, key: str, default: str = "", **kwargs) -> str:
        """获取标签文本"""
        if self.config:
            return self.config.get_label_text(key, default, **kwargs)
        return default.format(**kwargs) if kwargs else default

    def _get_message_text(self, key: str, default: str = "", **kwargs) -> str:
        """获取消息文本"""
        if self.config:
            return self.config.get_message_text(key, default, **kwargs)
        return default.format(**kwargs) if kwargs else default

    def _get_tab_text(self, key: str, default: str = "") -> str:
        """获取标签页文本"""
        if self.config:
            return self.config.get_tab_text(key, default)
        return default

    def _get_group_text(self, key: str, default: str = "") -> str:
        """获取分组文本"""
        if self.config:
            return self.config.get_group_text(key, default)
        return default

    def _get_table_headers(self, key: str, default: list = None) -> list:
        """获取表格标题"""
        if self.config:
            headers = self.config.get_table_headers(key)
            return headers if headers else (default or [])
        return default or []

    def _apply_progress_bar_style(self, progress_bar):
        """为进度条应用绿色样式"""
        try:
            from ui.styles.ProgressBarStyleManager import apply_green_progress_style
            apply_green_progress_style(progress_bar, "default")
        except ImportError:
            # 如果样式管理器不可用，使用内联样式
            progress_bar.setStyleSheet("""
                QProgressBar {
                    border: 2px solid #C0C0C0;
                    border-radius: 5px;
                    background-color: #F0F0F0;
                    text-align: center;
                    font-weight: bold;
                    color: #333333;
                }

                QProgressBar::chunk {
                    background-color: qlineargradient(
                        x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #7ED321,
                        stop: 0.5 #5CB85C,
                        stop: 1 #4CAF50
                    );
                    border-radius: 3px;
                    margin: 1px;
                }
            """)
        except Exception as e:
            logger.warning(f"应用进度条样式失败: {str(e)}")

    def _setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 创建各个标签页
        self._create_selection_tab()
        self._create_templates_tab()
        self._create_operation_tab()
        
        # 底部操作按钮
        self._create_bottom_buttons(layout)
        
    def _create_selection_tab(self):
        """创建寄存器选择标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 顶部控制区域
        control_layout = QHBoxLayout()

        # 全选/全不选按钮
        self.select_all_btn = QPushButton(self._get_button_text("select_all", "全选"))
        self.select_none_btn = QPushButton(self._get_button_text("select_none", "全不选"))
        self.select_group_btn = QPushButton(self._get_button_text("select_group", "按组选择"))

        control_layout.addWidget(QLabel(self._get_label_text("quick_select", "快速选择:")))
        control_layout.addWidget(self.select_all_btn)
        control_layout.addWidget(self.select_none_btn)
        control_layout.addWidget(self.select_group_btn)
        control_layout.addStretch()

        # 搜索框
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText(self._get_label_text("search_placeholder", "搜索寄存器..."))
        control_layout.addWidget(QLabel(self._get_label_text("search", "搜索:")))
        control_layout.addWidget(self.search_edit)

        layout.addLayout(control_layout)

        # 寄存器树形视图
        self.register_tree = QTreeWidget()
        headers = self._get_table_headers("register_tree", ["寄存器", "地址", "当前值", "描述"])
        self.register_tree.setHeaderLabels(headers)
        self.register_tree.setSelectionMode(QAbstractItemView.MultiSelection)
        layout.addWidget(self.register_tree)

        # 选择统计
        self.selection_label = QLabel(self._get_label_text("selection_count", "已选择: 0 个寄存器", count=0))
        layout.addWidget(self.selection_label)

        tab_title = self._get_tab_text("register_selection", "寄存器选择")
        self.tab_widget.addTab(tab, tab_title)
        
    def _create_templates_tab(self):
        """创建模板管理标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 预设模板区域
        templates_group = QGroupBox(self._get_group_text("preset_templates", "预设模板"))
        templates_layout = QVBoxLayout(templates_group)

        self.template_combo = QComboBox()
        self.template_combo.addItems(list(self.templates.keys()))

        template_buttons_layout = QHBoxLayout()
        self.load_template_btn = QPushButton(self._get_button_text("load_template", "加载模板"))
        self.save_template_btn = QPushButton(self._get_button_text("save_template", "保存为模板"))
        self.delete_template_btn = QPushButton(self._get_button_text("delete_template", "删除模板"))

        template_buttons_layout.addWidget(self.load_template_btn)
        template_buttons_layout.addWidget(self.save_template_btn)
        template_buttons_layout.addWidget(self.delete_template_btn)

        templates_layout.addWidget(QLabel(self._get_label_text("template_select", "选择模板:")))
        templates_layout.addWidget(self.template_combo)
        templates_layout.addLayout(template_buttons_layout)

        layout.addWidget(templates_group)

        # 模板内容显示
        content_group = QGroupBox(self._get_group_text("template_content", "模板内容"))
        content_layout = QVBoxLayout(content_group)

        self.template_table = QTableWidget()
        self.template_table.setColumnCount(3)
        headers = self._get_table_headers("template_table", ["地址", "寄存器名", "描述"])
        self.template_table.setHorizontalHeaderLabels(headers)
        self.template_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

        content_layout.addWidget(self.template_table)
        layout.addWidget(content_group)

        layout.addStretch()
        tab_title = self._get_tab_text("template_management", "模板管理")
        self.tab_widget.addTab(tab, tab_title)
        
    def _create_operation_tab(self):
        """创建操作监控标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 操作状态区域
        status_group = QGroupBox(self._get_group_text("operation_status", "操作状态"))
        status_layout = QVBoxLayout(status_group)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)

        # 应用绿色样式
        self._apply_progress_bar_style(self.progress_bar)

        status_layout.addWidget(self.progress_bar)

        # 状态标签
        self.status_label = QLabel(self._get_label_text("status_ready", "就绪"))
        status_layout.addWidget(self.status_label)

        layout.addWidget(status_group)

        # 操作日志
        log_group = QGroupBox(self._get_group_text("operation_log", "操作日志"))
        log_layout = QVBoxLayout(log_group)

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(200)
        log_layout.addWidget(self.log_text)

        # 日志控制按钮
        log_buttons_layout = QHBoxLayout()
        self.clear_log_btn = QPushButton(self._get_button_text("clear_log", "清空日志"))
        self.export_log_btn = QPushButton(self._get_button_text("export_log", "导出日志"))
        log_buttons_layout.addWidget(self.clear_log_btn)
        log_buttons_layout.addWidget(self.export_log_btn)
        log_buttons_layout.addStretch()

        log_layout.addLayout(log_buttons_layout)
        layout.addWidget(log_group)

        layout.addStretch()
        tab_title = self._get_tab_text("operation_monitor", "操作监控")
        self.tab_widget.addTab(tab, tab_title)
        
    def _create_bottom_buttons(self, layout):
        """创建底部操作按钮"""
        button_layout = QHBoxLayout()

        # 主要操作按钮
        self.read_selected_btn = QPushButton(self._get_button_text("read_selected", "读取选中寄存器"))
        self.write_selected_btn = QPushButton(self._get_button_text("write_selected", "写入选中寄存器"))
        self.cancel_btn = QPushButton(self._get_button_text("cancel_operation", "取消操作"))

        # 设置按钮样式
        self.read_selected_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        self.write_selected_btn.setStyleSheet("QPushButton { background-color: #2196F3; color: white; font-weight: bold; }")
        self.cancel_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; }")

        self.cancel_btn.setVisible(False)

        button_layout.addWidget(self.read_selected_btn)
        button_layout.addWidget(self.write_selected_btn)
        button_layout.addWidget(self.cancel_btn)
        button_layout.addStretch()

        # 关闭按钮
        self.close_btn = QPushButton(self._get_button_text("close", "关闭"))
        button_layout.addWidget(self.close_btn)

        layout.addLayout(button_layout)
        
    def _connect_signals(self):
        """连接信号"""
        # 选择相关信号
        self.select_all_btn.clicked.connect(self._select_all_registers)
        self.select_none_btn.clicked.connect(self._select_none_registers)
        self.search_edit.textChanged.connect(self._filter_registers)
        
        # 模板相关信号
        self.load_template_btn.clicked.connect(self._load_template)
        self.template_combo.currentTextChanged.connect(self._show_template_content)
        
        # 操作相关信号
        self.read_selected_btn.clicked.connect(self._read_selected_registers)
        self.write_selected_btn.clicked.connect(self._write_selected_registers)
        self.cancel_btn.clicked.connect(self._cancel_operation)
        
        # 日志相关信号
        self.clear_log_btn.clicked.connect(self._clear_log)
        
        # 窗口关闭信号
        self.close_btn.clicked.connect(self.close)
        
        # 树形视图选择变化
        self.register_tree.itemSelectionChanged.connect(self._update_selection)
        
    def _initialize_data(self):
        """初始化数据"""
        try:
            if self.main_window:
                self.register_manager = getattr(self.main_window, 'register_manager', None)
                self.spi_service = getattr(self.main_window, 'spi_service', None)

            self._populate_register_tree()
            self._show_template_content()
            self._log_message("插件初始化完成")

        except Exception as e:
            logger.error(f"初始化数据失败: {str(e)}")
            self._log_message(f"初始化失败: {str(e)}")

    def _populate_register_tree(self):
        """填充寄存器树"""
        try:
            self.register_tree.clear()

            if not self.register_manager:
                self._log_message("警告: 寄存器管理器未找到")
                return

            # 获取所有寄存器
            registers = self.register_manager.get_all_registers()

            # 按功能分组
            self._create_register_groups(registers)

            # 创建树形结构
            for group_name, group_registers in self.register_groups.items():
                group_item = QTreeWidgetItem([group_name, "", "", f"{len(group_registers)} 个寄存器"])
                group_item.setFlags(group_item.flags() | Qt.ItemIsTristate | Qt.ItemIsUserCheckable)
                group_item.setCheckState(0, Qt.Unchecked)

                for reg_info in group_registers:
                    addr = reg_info['address']
                    name = f"R{int(addr, 16)} ({addr})"
                    current_value = self.register_manager.get_register_value(addr)
                    description = reg_info.get('description', '')

                    reg_item = QTreeWidgetItem([name, addr, f"0x{current_value:04X}", description])
                    reg_item.setFlags(reg_item.flags() | Qt.ItemIsUserCheckable)
                    reg_item.setCheckState(0, Qt.Unchecked)
                    reg_item.setData(0, Qt.UserRole, addr)  # 存储地址

                    group_item.addChild(reg_item)

                self.register_tree.addTopLevelItem(group_item)
                group_item.setExpanded(True)

            self._log_message(f"已加载 {len(registers)} 个寄存器")

        except Exception as e:
            logger.error(f"填充寄存器树失败: {str(e)}")
            self._log_message(f"加载寄存器失败: {str(e)}")

    def _create_register_groups(self, registers):
        """创建寄存器分组"""
        if self.group_matcher:
            # 使用配置化的分组匹配器
            self.register_groups = self.group_matcher.create_register_groups(registers)
        else:
            # 使用默认分组逻辑
            self.register_groups = self._create_default_register_groups(registers)

    def _create_default_register_groups(self, registers):
        """创建默认寄存器分组（当配置不可用时使用）"""
        groups = {
            "设备信息": [],
            "电源管理": [],
            "时钟输入": [],
            "PLL控制": [],
            "时钟输出0-1": [],
            "时钟输出2-3": [],
            "时钟输出4-5": [],
            "时钟输出6-7": [],
            "时钟输出8-9": [],
            "时钟输出10-11": [],
            "时钟输出12-13": [],
            "SYSREF控制": [],
            "同步控制": [],
            "其他": []
        }

        for reg in registers:
            addr = reg['address']
            addr_int = int(addr, 16)

            # 根据地址范围分组
            if addr_int == 0x00:
                groups["设备信息"].append(reg)
            elif addr_int in [0x02, 0x50, 0x83]:
                groups["电源管理"].append(reg)
            elif 0x55 <= addr_int <= 0x60:
                groups["时钟输入"].append(reg)
            elif addr_int in [0x63, 0x65, 0x67, 0x69, 0x6B, 0x6C, 0x70, 0x72, 0x76, 0x77, 0x79, 0x7A]:
                groups["PLL控制"].append(reg)
            elif 0x10 <= addr_int <= 0x17:
                groups["时钟输出0-1"].append(reg)
            elif 0x18 <= addr_int <= 0x1F:
                groups["时钟输出2-3"].append(reg)
            elif 0x20 <= addr_int <= 0x27:
                groups["时钟输出4-5"].append(reg)
            elif 0x28 <= addr_int <= 0x2F:
                groups["时钟输出6-7"].append(reg)
            elif 0x30 <= addr_int <= 0x37:
                groups["时钟输出8-9"].append(reg)
            elif 0x38 <= addr_int <= 0x3F:
                groups["时钟输出10-11"].append(reg)
            elif 0x40 <= addr_int <= 0x47:
                groups["时钟输出12-13"].append(reg)
            elif addr_int in [0x49, 0x4A, 0x4C, 0x4E]:
                groups["SYSREF控制"].append(reg)
            elif addr_int in [0x53, 0x54, 0x55]:
                groups["同步控制"].append(reg)
            else:
                groups["其他"].append(reg)

        # 移除空分组
        return {k: v for k, v in groups.items() if v}

    def _select_all_registers(self):
        """全选寄存器"""
        try:
            for i in range(self.register_tree.topLevelItemCount()):
                group_item = self.register_tree.topLevelItem(i)
                group_item.setCheckState(0, Qt.Checked)
                for j in range(group_item.childCount()):
                    child_item = group_item.child(j)
                    child_item.setCheckState(0, Qt.Checked)
            self._update_selection()
            self._log_message(self._get_message_text("all_selected", "已全选所有寄存器"))
        except Exception as e:
            logger.error(f"全选失败: {str(e)}")

    def _select_none_registers(self):
        """全不选寄存器"""
        try:
            for i in range(self.register_tree.topLevelItemCount()):
                group_item = self.register_tree.topLevelItem(i)
                group_item.setCheckState(0, Qt.Unchecked)
                for j in range(group_item.childCount()):
                    child_item = group_item.child(j)
                    child_item.setCheckState(0, Qt.Unchecked)
            self._update_selection()
            self._log_message(self._get_message_text("none_selected", "已取消选择所有寄存器"))
        except Exception as e:
            logger.error(f"取消选择失败: {str(e)}")

    def _filter_registers(self, text):
        """过滤寄存器"""
        try:
            for i in range(self.register_tree.topLevelItemCount()):
                group_item = self.register_tree.topLevelItem(i)
                group_visible = False

                for j in range(group_item.childCount()):
                    child_item = group_item.child(j)
                    reg_text = child_item.text(0) + child_item.text(1) + child_item.text(3)

                    if not text or text.lower() in reg_text.lower():
                        child_item.setHidden(False)
                        group_visible = True
                    else:
                        child_item.setHidden(True)

                group_item.setHidden(not group_visible)

        except Exception as e:
            logger.error(f"过滤失败: {str(e)}")

    def _update_selection(self):
        """更新选择状态"""
        try:
            self.selected_registers.clear()

            for i in range(self.register_tree.topLevelItemCount()):
                group_item = self.register_tree.topLevelItem(i)
                for j in range(group_item.childCount()):
                    child_item = group_item.child(j)
                    if child_item.checkState(0) == Qt.Checked:
                        addr = child_item.data(0, Qt.UserRole)
                        if addr:
                            self.selected_registers.add(addr)

            count = len(self.selected_registers)
            self.selection_label.setText(self._get_label_text("selection_count", "已选择: {count} 个寄存器", count=count))

            # 更新按钮状态
            self.read_selected_btn.setEnabled(count > 0 and not self.is_operating)
            self.write_selected_btn.setEnabled(count > 0 and not self.is_operating)

        except Exception as e:
            logger.error(f"更新选择状态失败: {str(e)}")

    def _load_template(self):
        """加载模板"""
        try:
            template_name = self.template_combo.currentText()
            if template_name not in self.templates:
                return

            # 先清空选择
            self._select_none_registers()

            # 选择模板中的寄存器
            template_addrs = self.templates[template_name]
            selected_count = 0

            for i in range(self.register_tree.topLevelItemCount()):
                group_item = self.register_tree.topLevelItem(i)
                for j in range(group_item.childCount()):
                    child_item = group_item.child(j)
                    addr = child_item.data(0, Qt.UserRole)
                    if addr in template_addrs:
                        child_item.setCheckState(0, Qt.Checked)
                        selected_count += 1

            self._update_selection()
            self._log_message(f"已加载模板 '{template_name}'，选择了 {selected_count} 个寄存器")

        except Exception as e:
            logger.error(f"加载模板失败: {str(e)}")
            self._log_message(f"加载模板失败: {str(e)}")

    def _show_template_content(self):
        """显示模板内容"""
        try:
            template_name = self.template_combo.currentText()
            if template_name not in self.templates:
                return

            template_addrs = self.templates[template_name]
            self.template_table.setRowCount(len(template_addrs))

            for row, addr in enumerate(template_addrs):
                # 地址
                addr_item = QTableWidgetItem(addr)
                self.template_table.setItem(row, 0, addr_item)

                # 寄存器名
                reg_num = int(addr, 16)
                name_item = QTableWidgetItem(f"R{reg_num}")
                self.template_table.setItem(row, 1, name_item)

                # 描述（从寄存器管理器获取）
                description = ""
                if self.register_manager:
                    try:
                        registers = self.register_manager.get_all_registers()
                        for reg in registers:
                            if reg['address'] == addr:
                                description = reg.get('description', '')
                                break
                    except Exception:
                        pass

                desc_item = QTableWidgetItem(description)
                self.template_table.setItem(row, 2, desc_item)

        except Exception as e:
            logger.error(f"显示模板内容失败: {str(e)}")

    def _read_selected_registers(self):
        """读取选中的寄存器"""
        try:
            if not self.selected_registers:
                QMessageBox.warning(self, "警告", "请先选择要读取的寄存器")
                return

            if not self.spi_service:
                QMessageBox.warning(self, "错误", "SPI服务未找到")
                return

            # 确认操作
            reply = QMessageBox.question(
                self,
                "确认读取",
                f"确定要读取选中的 {len(self.selected_registers)} 个寄存器吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply == QMessageBox.No:
                return

            self._start_operation("读取")

            # 转换为列表并排序
            addr_list = sorted(list(self.selected_registers), key=lambda x: int(x, 16))

            self._log_message(f"开始读取 {len(addr_list)} 个寄存器...")

            # 使用批量读取
            self.spi_service.batch_read_registers(addr_list)

            # 设置进度
            self.progress_bar.setMaximum(len(addr_list))
            self.progress_bar.setValue(0)

            # 连接读取完成信号
            if hasattr(self.spi_service, 'register_read'):
                self.spi_service.register_read.connect(self._on_register_read)

        except Exception as e:
            logger.error(f"读取寄存器失败: {str(e)}")
            self._log_message(f"读取失败: {str(e)}")
            self._finish_operation()

    def _write_selected_registers(self):
        """写入选中的寄存器"""
        try:
            if not self.selected_registers:
                QMessageBox.warning(self, "警告", "请先选择要写入的寄存器")
                return

            if not self.spi_service or not self.register_manager:
                QMessageBox.warning(self, "错误", "SPI服务或寄存器管理器未找到")
                return

            # 确认操作
            reply = QMessageBox.question(
                self,
                "确认写入",
                f"确定要写入选中的 {len(self.selected_registers)} 个寄存器吗？\n"
                "此操作将会覆盖设备中对应寄存器的值。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.No:
                return

            self._start_operation("写入")

            # 准备写入数据
            write_operations = []
            for addr in self.selected_registers:
                value = self.register_manager.get_register_value(addr)
                write_operations.append((addr, value))

            # 按地址排序
            write_operations.sort(key=lambda x: int(x[0], 16))

            self._log_message(f"开始写入 {len(write_operations)} 个寄存器...")

            # 使用批量写入
            self.spi_service.batch_write_registers(write_operations)

            # 设置进度
            self.progress_bar.setMaximum(len(write_operations))
            self.progress_bar.setValue(0)

            # 连接写入完成信号
            if hasattr(self.spi_service, 'register_written'):
                self.spi_service.register_written.connect(self._on_register_written)

        except Exception as e:
            logger.error(f"写入寄存器失败: {str(e)}")
            self._log_message(f"写入失败: {str(e)}")
            self._finish_operation()

    def _start_operation(self, operation_type):
        """开始操作"""
        self.is_operating = True
        self.status_label.setText(f"正在{operation_type}...")
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 禁用操作按钮
        self.read_selected_btn.setEnabled(False)
        self.write_selected_btn.setEnabled(False)
        self.cancel_btn.setVisible(True)

        # 切换到操作监控标签页
        self.tab_widget.setCurrentIndex(2)

    def _finish_operation(self):
        """完成操作"""
        self.is_operating = False
        self.status_label.setText("就绪")
        self.progress_bar.setVisible(False)

        # 恢复按钮状态
        self._update_selection()
        self.cancel_btn.setVisible(False)

        # 断开信号连接
        try:
            if hasattr(self.spi_service, 'register_read'):
                self.spi_service.register_read.disconnect(self._on_register_read)
        except Exception:
            pass

        try:
            if hasattr(self.spi_service, 'register_written'):
                self.spi_service.register_written.disconnect(self._on_register_written)
        except Exception:
            pass

    def _cancel_operation(self):
        """取消操作"""
        try:
            self._log_message("用户取消操作")
            self._finish_operation()
        except Exception as e:
            logger.error(f"取消操作失败: {str(e)}")

    def _on_register_read(self, addr, value):
        """寄存器读取完成回调"""
        try:
            if addr in self.selected_registers:
                current_progress = self.progress_bar.value()
                self.progress_bar.setValue(current_progress + 1)

                self._log_message(f"读取完成: {addr} = 0x{value:04X}")

                # 更新树形视图中的值
                self._update_register_value_in_tree(addr, value)

                # 检查是否全部完成
                if self.progress_bar.value() >= self.progress_bar.maximum():
                    self._log_message("所有寄存器读取完成")
                    self._finish_operation()

        except Exception as e:
            logger.error(f"处理读取回调失败: {str(e)}")

    def _on_register_written(self, addr, value):
        """寄存器写入完成回调"""
        try:
            if addr in self.selected_registers:
                current_progress = self.progress_bar.value()
                self.progress_bar.setValue(current_progress + 1)

                self._log_message(f"写入完成: {addr} = 0x{value:04X}")

                # 检查是否全部完成
                if self.progress_bar.value() >= self.progress_bar.maximum():
                    self._log_message("所有寄存器写入完成")
                    self._finish_operation()

        except Exception as e:
            logger.error(f"处理写入回调失败: {str(e)}")

    def _update_register_value_in_tree(self, addr, value):
        """更新树形视图中的寄存器值"""
        try:
            for i in range(self.register_tree.topLevelItemCount()):
                group_item = self.register_tree.topLevelItem(i)
                for j in range(group_item.childCount()):
                    child_item = group_item.child(j)
                    if child_item.data(0, Qt.UserRole) == addr:
                        child_item.setText(2, f"0x{value:04X}")
                        break
        except Exception as e:
            logger.error(f"更新树形视图值失败: {str(e)}")

    def _clear_log(self):
        """清空日志"""
        self.log_text.clear()

    def _log_message(self, message):
        """记录日志消息"""
        try:
            from datetime import datetime
            timestamp = datetime.now().strftime("%H:%M:%S")
            formatted_message = f"[{timestamp}] {message}"
            self.log_text.append(formatted_message)

            # 自动滚动到底部
            scrollbar = self.log_text.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())

        except Exception as e:
            logger.error(f"记录日志失败: {str(e)}")

    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            if self.is_operating:
                reply = QMessageBox.question(
                    self,
                    "确认关闭",
                    "当前有操作正在进行，确定要关闭窗口吗？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )

                if reply == QMessageBox.No:
                    event.ignore()
                    return

                self._cancel_operation()

            self.window_closed.emit()
            super().closeEvent(event)

        except Exception as e:
            logger.error(f"关闭窗口失败: {str(e)}")
            super().closeEvent(event)


class SelectiveRegisterPlugin(IToolWindowPlugin):
    """选择性寄存器操作插件"""

    def __init__(self):
        """初始化插件"""
        self.context = None
        self.window_instance = None
        self.config = get_config() if get_config else None

    @property
    def name(self) -> str:
        if self.config:
            plugin_info = self.config.get_plugin_info()
            return plugin_info.get("name", "选择性寄存器操作")
        return "选择性寄存器操作"

    @property
    def version(self) -> str:
        if self.config:
            plugin_info = self.config.get_plugin_info()
            return plugin_info.get("version", "1.0.1")
        return "1.0.1"

    @property
    def description(self) -> str:
        if self.config:
            plugin_info = self.config.get_plugin_info()
            return plugin_info.get("description", "允许用户选择特定的寄存器进行批量读写操作，支持分组选择和模板管理")
        return "允许用户选择特定的寄存器进行批量读写操作，支持分组选择和模板管理"

    @property
    def menu_text(self) -> str:
        return self.name

    @property
    def icon_path(self) -> str:
        return None  # 没有图标

    def initialize(self, context):
        """初始化插件

        Args:
            context: 应用程序上下文（通常是主窗口）
        """
        self.context = context
        logger.info(f"插件 '{self.name}' 初始化完成")

    def create_window(self, parent=None):
        """创建工具窗口

        Args:
            parent: 父窗口

        Returns:
            工具窗口实例
        """
        if self.window_instance is None:
            self.window_instance = SelectiveRegisterWindow(parent)
            self.window_instance.window_closed.connect(self._on_window_closed)
            logger.info(f"创建 '{self.name}' 工具窗口")

        return self.window_instance

    def _on_window_closed(self):
        """窗口关闭处理"""
        self.window_instance = None
        logger.info(f"'{self.name}' 工具窗口已关闭")

    def cleanup(self):
        """清理插件资源"""
        if self.window_instance:
            self.window_instance.close()
            self.window_instance = None

        logger.info(f"插件 '{self.name}' 已清理")


# 插件入口点
# 插件管理器会自动发现这个类
plugin_class = SelectiveRegisterPlugin
