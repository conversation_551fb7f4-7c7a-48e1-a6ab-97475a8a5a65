#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
现代化的BaseHandler基类
使用RegisterManager API而不是直接操作寄存器对象
这是重构的第一步，新的工具窗口应该继承这个类
"""

from PyQt5 import QtWidgets, QtCore
from PyQt5.QtCore import pyqtSignal
from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class ModernBaseHandler(QtWidgets.QWidget):
    """现代化的基础处理器类，使用RegisterManager API"""

    # 窗口关闭信号
    window_closed = pyqtSignal()
    
    def __init__(self, parent=None, register_manager=None, enable_scroll=None, **kwargs):
        """初始化现代化基础处理器

        Args:
            parent: 父窗口
            register_manager: RegisterManager实例
            enable_scroll: 是否启用滚动区域，None表示自动检测
            **kwargs: 其他参数（用于向后兼容，如register_repo等）
        """
        super().__init__(parent)

        # 保存RegisterManager引用
        self.register_manager = register_manager
        self.parent = parent

        # 向后兼容：如果传入了register_repo参数，记录但不使用
        if 'register_repo' in kwargs:
            logger.debug("ModernBaseHandler: 收到register_repo参数，但现代化处理器不再使用此参数")

        # 创建主布局
        main_layout = QtWidgets.QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)  # 移除边距

        # 创建内容widget
        self.content_widget = QtWidgets.QWidget()

        # 设置合理的大小策略，让控件能够适应容器大小
        self.content_widget.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)

        # 决定是否使用滚动区域
        self.scroll_area = None
        if enable_scroll is None:
            # 自动检测是否需要滚动区域
            enable_scroll = self._should_enable_scroll()

        if enable_scroll:
            self._setup_scroll_area(main_layout)
        else:
            # 直接将内容widget添加到主布局
            main_layout.addWidget(self.content_widget)

        # 确保content_widget可见
        self.content_widget.setVisible(True)
        
        # 控件映射表（控件名 -> 寄存器地址）
        self.widget_register_map = {}

        # 添加标志位防止循环更新（参考原BaseHandler实现）
        self._updating_from_widget = False

        # 连接全局寄存器更新信号（添加安全检查）
        try:
            bus_instance = RegisterUpdateBus.instance()
            if bus_instance:
                bus_instance.register_updated.connect(self.on_global_register_updated)
                logger.debug("成功连接到RegisterUpdateBus")
            else:
                logger.warning("RegisterUpdateBus实例为None，跳过信号连接")
        except Exception as e:
            logger.warning(f"连接RegisterUpdateBus时出错: {str(e)}，继续初始化")

        # 初始化标志位
        self._initialization_complete = False

        # 延迟初始化，给子类时间完成UI设置
        QtCore.QTimer.singleShot(0, self._complete_initialization)

    def _complete_initialization(self):
        """完成初始化流程"""
        if self._initialization_complete:
            return

        try:
            logger.info("ModernBaseHandler: 开始完成初始化")

            # 1. 子类可以重写此方法来设置寄存器默认值
            self._setup_register_defaults()

            # 2. 执行标准的初始化流程
            if hasattr(self, 'ui'):
                logger.info("UI已设置，开始构建控件映射")
                self._build_widget_register_mapping()
                self._connect_widget_signals()
                self._initialize_widget_values()
            else:
                if self._requires_ui_mapping():
                    logger.warning("UI未设置，跳过控件映射构建")
                else:
                    logger.debug("此处理器不需要UI映射，跳过控件映射构建")

            # 3. 子类可以重写此方法来执行特定的后初始化
            self._post_initialization_setup()

            # 4. 设置窗口的默认显示尺寸
            self._set_window_default_size()

            self._initialization_complete = True
            logger.info("ModernBaseHandler: 初始化完成")

        except Exception as e:
            logger.error(f"完成初始化时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _setup_register_defaults(self):
        """设置寄存器默认值（子类可重写）"""
        pass

    def _post_initialization_setup(self):
        """后初始化设置（子类可重写）"""
        pass

    def _should_enable_scroll(self):
        """自动检测是否需要启用滚动区域

        Returns:
            bool: 如果需要滚动区域则返回True
        """
        # 定义需要滚动区域的处理器类名
        scroll_required_handlers = {
            'ModernPLLHandler',
            'ModernClkOutputsHandler',
            'ModernSetModesHandler',
            'ModernClkinControlHandler',
            'ModernSyncSysRefHandler'
        }

        # 获取当前类名
        class_name = self.__class__.__name__

        # 检查是否在需要滚动的列表中
        return class_name in scroll_required_handlers

    def _setup_scroll_area(self, main_layout):
        """设置滚动区域

        Args:
            main_layout: 主布局
        """
        try:
            from PyQt5.QtWidgets import QScrollArea
            from PyQt5.QtCore import Qt

            # 创建滚动区域
            self.scroll_area = QScrollArea(self)
            self.scroll_area.setWidgetResizable(True)

            # 设置滚动条策略 - 根据需要显示滚动条
            self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
            self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

            # 设置content_widget的最小尺寸以适应大UI
            # 根据处理器类型设置不同的最小尺寸
            min_size = self._get_content_minimum_size()
            self.content_widget.setMinimumSize(min_size[0], min_size[1])

            # 将content_widget设置到滚动区域
            self.scroll_area.setWidget(self.content_widget)

            # 将滚动区域添加到主布局
            main_layout.addWidget(self.scroll_area)

            logger.info(f"{self.__class__.__name__}: 已设置滚动区域，最小尺寸: {min_size}")

        except Exception as e:
            logger.error(f"设置滚动区域时出错: {str(e)}")
            # 如果滚动区域设置失败，回退到直接添加content_widget
            main_layout.addWidget(self.content_widget)

    def _get_content_minimum_size(self):
        """获取内容区域的最小尺寸

        Returns:
            tuple: (width, height) 最小尺寸
        """
        # 根据处理器类型返回不同的最小尺寸
        # 注意：这些尺寸应该基于实际UI文件的尺寸，并留有一定余量
        size_mapping = {
            'ModernPLLHandler': (2024, 1220),  # 基于用户要求的窗口尺寸2074x1320，减去边距(50+100)
            'ModernClkOutputsHandler': (1312, 1516),  # 基于用户要求的窗口尺寸1362x1616，减去边距(50+100)
            'ModernSetModesHandler': (720, 380),  # 基于Ui_setModes实际尺寸(669x324)，增加余量
            'ModernClkinControlHandler': (1987, 716),  # 基于用户要求的窗口尺寸2037x816，减去边距(50+100)
            'ModernSyncSysRefHandler': (1740, 923),  # 基于用户要求的窗口尺寸1790x1023，减去边距(50+100)
            'ExampleToolWindow': (800, 1000)  # 示例工具窗口也适当增大
        }

        class_name = self.__class__.__name__
        return size_mapping.get(class_name, (700, 600))  # 默认尺寸也增加一些

    def _get_window_default_size(self):
        """
        根据处理器类型获取窗口的默认显示尺寸

        Returns:
            tuple: (width, height) 窗口尺寸
        """
        # 窗口尺寸通常比内容尺寸稍大一些，以容纳边框、标题栏等
        content_size = self._get_content_minimum_size()
        # 为窗口边框、标题栏等预留空间
        window_width = content_size[0] + 50
        window_height = content_size[1] + 100
        return (window_width, window_height)

    def _set_window_default_size(self):
        """设置窗口的默认显示尺寸和位置"""
        try:
            # 获取推荐的窗口尺寸
            window_size = self._get_window_default_size()

            # 设置窗口尺寸
            self.resize(window_size[0], window_size[1])

            # 设置窗口居中显示
            self._center_window()

            # 设置窗口置顶功能
            self._setup_window_activation()

            logger.info(f"{self.__class__.__name__}: 设置窗口默认尺寸为 {window_size[0]} x {window_size[1]}，已居中显示")

        except Exception as e:
            logger.error(f"设置窗口默认尺寸时出错: {str(e)}")

    def _center_window(self):
        """将窗口居中显示在屏幕上"""
        try:
            from PyQt5.QtWidgets import QApplication

            # 获取屏幕几何信息
            desktop = QApplication.desktop()
            screen_geometry = desktop.screenGeometry()

            # 确保窗口已经有正确的尺寸
            self.updateGeometry()

            # 获取窗口尺寸
            window_size = self.size()

            # 计算窗口居中位置
            center_x = (screen_geometry.width() - window_size.width()) // 2
            center_y = (screen_geometry.height() - window_size.height()) // 2

            # 确保窗口不会超出屏幕边界
            center_x = max(0, center_x)
            center_y = max(0, center_y)

            # 设置窗口位置
            self.move(center_x, center_y)

            logger.info(f"{self.__class__.__name__}: 窗口已居中显示在 ({center_x}, {center_y})，窗口尺寸: {window_size.width()}x{window_size.height()}")

        except Exception as e:
            logger.error(f"居中窗口时出错: {str(e)}")

    def _setup_window_activation(self):
        """设置窗口激活功能（点击置顶）"""
        try:
            from PyQt5.QtCore import Qt

            # 设置窗口属性，使其能够接收焦点
            self.setFocusPolicy(Qt.ClickFocus)

            # 安装事件过滤器来处理鼠标点击
            self.installEventFilter(self)

            logger.debug(f"{self.__class__.__name__}: 已设置窗口激活功能")

        except Exception as e:
            logger.error(f"设置窗口激活功能时出错: {str(e)}")

    def eventFilter(self, obj, event):
        """事件过滤器，处理窗口激活"""
        try:
            from PyQt5.QtCore import QEvent

            # 当窗口接收到鼠标按下事件时，将其置顶
            if obj == self and event.type() == QEvent.MouseButtonPress:
                self.raise_()
                self.activateWindow()

            return super().eventFilter(obj, event)

        except Exception as e:
            logger.error(f"处理窗口事件时出错: {str(e)}")
            return False

    def enable_scroll_area(self, width=None, height=None):
        """动态启用滚动区域

        Args:
            width: 内容区域宽度，None表示使用默认值
            height: 内容区域高度，None表示使用默认值
        """
        if self.scroll_area is not None:
            logger.debug("滚动区域已经启用")
            return

        try:
            from PyQt5.QtWidgets import QScrollArea
            from PyQt5.QtCore import Qt

            # 获取当前主布局
            main_layout = self.layout()

            # 移除现有的content_widget
            main_layout.removeWidget(self.content_widget)

            # 创建滚动区域
            self.scroll_area = QScrollArea(self)
            self.scroll_area.setWidgetResizable(True)

            # 设置滚动条策略
            self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
            self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

            # 设置内容区域尺寸
            if width is None or height is None:
                default_size = self._get_content_minimum_size()
                width = width or default_size[0]
                height = height or default_size[1]

            self.content_widget.setMinimumSize(width, height)

            # 将content_widget设置到滚动区域
            self.scroll_area.setWidget(self.content_widget)

            # 将滚动区域添加到主布局
            main_layout.addWidget(self.scroll_area)

            logger.info(f"动态启用滚动区域，尺寸: {width}x{height}")

        except Exception as e:
            logger.error(f"动态启用滚动区域时出错: {str(e)}")

    def disable_scroll_area(self):
        """动态禁用滚动区域"""
        if self.scroll_area is None:
            logger.debug("滚动区域未启用")
            return

        try:
            # 获取当前主布局
            main_layout = self.layout()

            # 从滚动区域中取出content_widget
            self.scroll_area.takeWidget()

            # 移除滚动区域
            main_layout.removeWidget(self.scroll_area)
            self.scroll_area.deleteLater()
            self.scroll_area = None

            # 重置content_widget的最小尺寸
            self.content_widget.setMinimumSize(0, 0)

            # 直接将content_widget添加到主布局
            main_layout.addWidget(self.content_widget)

            logger.info("已禁用滚动区域")

        except Exception as e:
            logger.error(f"禁用滚动区域时出错: {str(e)}")

    def adjust_content_size_to_fit(self):
        """调整内容尺寸以适应所有子控件"""
        if not hasattr(self, 'content_widget') or not self.content_widget:
            return

        try:
            # 计算所有子控件的边界矩形
            max_x, max_y = 0, 0

            for child in self.content_widget.findChildren(QtWidgets.QWidget):
                if child.isVisible():
                    geometry = child.geometry()
                    child_max_x = geometry.x() + geometry.width()
                    child_max_y = geometry.y() + geometry.height()

                    max_x = max(max_x, child_max_x)
                    max_y = max(max_y, child_max_y)

            # 添加一些边距
            margin = 50
            required_width = max_x + margin
            required_height = max_y + margin

            # 获取当前最小尺寸
            current_min_size = self.content_widget.minimumSize()
            current_width = current_min_size.width()
            current_height = current_min_size.height()

            # 如果需要更大的尺寸，则更新
            new_width = max(current_width, required_width)
            new_height = max(current_height, required_height)

            if new_width != current_width or new_height != current_height:
                self.content_widget.setMinimumSize(new_width, new_height)
                logger.info(f"调整内容尺寸: {current_width}x{current_height} -> {new_width}x{new_height}")

                # 如果有滚动区域，确保它能正确显示新尺寸
                if hasattr(self, 'scroll_area') and self.scroll_area:
                    self.scroll_area.updateGeometry()

        except Exception as e:
            logger.error(f"调整内容尺寸时出错: {str(e)}")

    def ensure_content_fully_visible(self):
        """确保内容完全可见"""
        try:
            # 首先调整内容尺寸
            self.adjust_content_size_to_fit()

            # 如果没有滚动区域但内容很大，自动启用滚动
            if not hasattr(self, 'scroll_area') or not self.scroll_area:
                content_size = self.content_widget.minimumSize()
                window_size = self.size()

                # 如果内容尺寸超出窗口尺寸的80%，启用滚动
                if (content_size.width() > window_size.width() * 0.8 or
                    content_size.height() > window_size.height() * 0.8):
                    logger.info("内容尺寸较大，自动启用滚动区域")
                    self.enable_scroll_area()

        except Exception as e:
            logger.error(f"确保内容可见时出错: {str(e)}")

    def _post_init(self):
        """向后兼容的初始化方法（已废弃，请使用新的初始化流程）"""
        logger.warning("_post_init方法已废弃，请使用新的初始化流程")
        # 如果子类直接调用了_post_init，我们仍然执行初始化
        if not self._initialization_complete:
            self._complete_initialization()
    
    def _requires_ui_mapping(self):
        """检查当前处理器是否需要UI映射

        Returns:
            bool: 如果需要UI映射则返回True
        """
        # 定义需要UI映射的处理器类名
        ui_required_handlers = {
            'ModernSetModesHandler',
            'ModernClkinControlHandler',
            'ModernPLLHandler',
            'ModernSyncSysRefHandler',
            'ModernClkOutputsHandler'
        }

        # 获取当前类名
        class_name = self.__class__.__name__

        # 检查是否在需要UI的列表中
        return class_name in ui_required_handlers

    def _build_widget_register_mapping(self):
        """构建控件到寄存器的映射关系"""
        if not self.register_manager:
            logger.warning("RegisterManager未设置，跳过控件映射构建")
            return

        if not hasattr(self, 'ui'):
            logger.warning("UI未设置，跳过控件映射构建")
            return

        # 获取所有寄存器的控件映射
        all_widgets = set()

        # 收集UI中的所有控件名
        for attr_name in dir(self.ui):
            if not attr_name.startswith('_') and not callable(getattr(self.ui, attr_name)):
                try:
                    attr = getattr(self.ui, attr_name)
                    if isinstance(attr, QtWidgets.QWidget):
                        all_widgets.add(attr_name)
                        logger.debug(f"发现UI控件: {attr_name} ({type(attr).__name__})")
                except Exception as e:
                    logger.debug(f"检查属性 {attr_name} 时出错: {str(e)}")

        logger.info(f"UI中发现 {len(all_widgets)} 个控件")

        # 为每个寄存器构建映射
        for addr in self.register_manager.register_objects.keys():
            widget_map = self.register_manager.get_widget_register_mapping(addr)
            for widget_name, widget_info in widget_map.items():
                if widget_name in all_widgets:
                    self.widget_register_map[widget_name] = widget_info
                    logger.info(f"映射控件 {widget_name} 到寄存器 {addr}")
                else:
                    logger.debug(f"寄存器 {addr} 的控件 {widget_name} 在UI中未找到")

        logger.info(f"成功构建 {len(self.widget_register_map)} 个控件映射")
    
    def _connect_widget_signals(self):
        """连接控件信号"""
        for widget_name in self.widget_register_map.keys():
            try:
                widget = getattr(self.ui, widget_name)
                widget_info = self.widget_register_map[widget_name]
                widget_type = widget_info.get("widget_type")
                
                # 根据控件类型连接相应的信号
                if widget_type == "checkbox" and isinstance(widget, QtWidgets.QCheckBox):
                    widget.stateChanged.connect(lambda state, name=widget_name: self._on_widget_changed(name, state == 2))
                elif widget_type == "spinbox" and isinstance(widget, QtWidgets.QSpinBox):
                    widget.valueChanged.connect(lambda value, name=widget_name: self._on_widget_changed(name, value))
                elif widget_type == "combobox" and isinstance(widget, QtWidgets.QComboBox):
                    widget.currentIndexChanged.connect(lambda index, name=widget_name: self._on_widget_changed(name, index))
                elif widget_type == "lineedit" and isinstance(widget, QtWidgets.QLineEdit):
                    widget.textChanged.connect(lambda text, name=widget_name: self._on_widget_changed(name, text))
                    
                logger.debug(f"已连接控件 {widget_name} 的信号")
                
            except AttributeError:
                logger.warning(f"控件 {widget_name} 不存在，跳过信号连接")
            except Exception as e:
                logger.error(f"连接控件 {widget_name} 信号时出错: {str(e)}")
    
    def _initialize_widget_values(self):
        """初始化控件值"""
        for widget_name, widget_info in self.widget_register_map.items():
            try:
                # widget = getattr(self.ui, widget_name)
                reg_addr = widget_info["register_addr"]
                bit_def = widget_info["bit_def"]
                bit_name = bit_def["name"]

                # 检查是否是手动初始化的控件
                is_manually_initialized = bit_def.get("manually_initialized", False)

                if is_manually_initialized or reg_addr == "manual":
                    # 对于手动初始化的控件，跳过从RegisterManager获取值
                    logger.debug(f"控件 {widget_name} 是手动初始化的控件，跳过从RegisterManager初始化")
                    continue

                # 从RegisterManager获取当前位字段值
                bit_value = self.register_manager.get_bit_field_value(reg_addr, bit_name)

                # 更新控件状态
                self._update_widget_ui(widget_name, bit_value)

            except Exception as e:
                logger.error(f"初始化控件 {widget_name} 值时出错: {str(e)}")
    
    def _on_widget_changed(self, widget_name, value):
        """处理控件值变化"""
        try:
            logger.debug(f"控件 {widget_name} 值变化: {value}")

            if widget_name not in self.widget_register_map:
                logger.warning(f"未知控件: {widget_name}")
                return

            widget_info = self.widget_register_map[widget_name]
            reg_addr = widget_info["register_addr"]

            # 转换控件值为寄存器值
            register_value = self._convert_widget_value_to_register_value(widget_name, value)

            # 检查是否为跨寄存器控件
            bit_def = widget_info.get("bit_def", {})
            is_cross_register = bit_def.get("is_cross_register", False)
            
            if is_cross_register:
                # 对于跨寄存器控件，直接更新寄存器值
                try:
                    old_value = self.register_manager.get_register_value(reg_addr)
                    self.register_manager.set_register_value(reg_addr, register_value)
                    new_reg_value = register_value
                    actual_addr = self.register_manager._normalize_register_address(reg_addr)
                    success = True
                    logger.info(f"跨寄存器控件 {widget_name} 直接更新寄存器 {actual_addr}: 0x{old_value:04X} -> 0x{new_reg_value:04X}")
                except Exception as e:
                    logger.error(f"更新跨寄存器控件 {widget_name} 失败: {str(e)}")
                    success = False
            else:
                # 通过RegisterManager更新寄存器值（普通控件）
                success, actual_addr, new_reg_value = self.register_manager.update_widget_value(
                    widget_name, register_value, reg_addr
                )

            if success:
                logger.info(f"成功更新寄存器 {actual_addr}: 控件 {widget_name} = {value} -> 寄存器值 = 0x{new_reg_value:04X}")

                # 设置标志，防止接收自己发送的更新信号（参考原BaseHandler实现）
                self._updating_from_widget = True

                # 设置标志，表示这是由控件变化触发的更新（用于区分读取操作）
                self._widget_triggered_update = True

                # 在主窗口设置控件触发标志，让表格处理器能够检测到
                main_window = self._get_main_window()
                if main_window:
                    try:
                        # 验证main_window是否是有效的对象实例
                        if callable(main_window):
                            logger.error(f"ModernBaseHandler: main_window是一个函数/方法而不是对象实例: {type(main_window)}")
                            logger.error(f"ModernBaseHandler: main_window内容: {main_window}")
                        elif hasattr(main_window, '__class__'):
                            setattr(main_window, '_widget_triggered_update', True)
                            logger.debug(f"ModernBaseHandler: 成功在主窗口设置控件触发标志: {type(main_window)}")
                        else:
                            logger.error(f"ModernBaseHandler: main_window不是有效的对象实例: {type(main_window)}")
                    except Exception as e:
                        logger.error(f"ModernBaseHandler: 设置主窗口控件触发标志时出错: {str(e)}")
                        logger.error(f"ModernBaseHandler: main_window类型: {type(main_window)}")
                        import traceback
                        traceback.print_exc()

                # 发送寄存器更新信号到全局总线（添加安全检查）
                try:
                    bus_instance = RegisterUpdateBus.instance()
                    if bus_instance:
                        bus_instance.register_updated.emit(actual_addr, new_reg_value)
                    else:
                        logger.warning("RegisterUpdateBus实例为None，跳过信号发送")
                except Exception as e:
                    logger.warning(f"发送RegisterUpdateBus信号时出错: {str(e)}")

                # 控件修改后必须自动写入到芯片，确保寄存器值与芯片同步
                # 这是基本功能，不依赖自动写入设置（修复跳转后自动写入问题）
                final_register_value = new_reg_value
                logger.info(f"现代化Handler: 控件修改后自动写入寄存器 {actual_addr} = 0x{final_register_value:04X} 到芯片")
                self._auto_write_register_to_chip(actual_addr, final_register_value)

                # 只有在非批量操作时才触发寄存器表格跳转（参考原实现）
                # 并且只有控件变化才触发跳转，读取操作不触发跳转
                if not self._is_in_batch_operation():
                    logger.info(f"现代化Handler: 控件 {widget_name} 变化触发寄存器表格跳转到 {actual_addr}")
                    self._trigger_register_table_navigation(actual_addr)
                else:
                    logger.debug("现代化Handler: 批量操作中，跳过寄存器表格跳转")

                # 子类需要覆盖此方法以处理频率计算等特定功能（参考原BaseHandler实现）
                self.handle_register_value_change(widget_name)

                # 通知子类处理值变化
                self.on_register_value_changed(widget_name, actual_addr, new_reg_value)

                # 重置控件触发标志
                self._widget_triggered_update = False

                # 延迟清除主窗口的控件触发标志，确保所有信号处理完成
                if main_window:
                    try:
                        # 再次验证main_window是否有效
                        if callable(main_window):
                            logger.error(f"ModernBaseHandler: 清除标志时main_window是函数: {type(main_window)}")
                        elif hasattr(main_window, '__class__'):
                            from PyQt5.QtCore import QTimer
                            QTimer.singleShot(200, lambda: self._clear_widget_update_flag(main_window))
                        else:
                            logger.error(f"ModernBaseHandler: 清除标志时main_window无效: {type(main_window)}")
                    except Exception as e:
                        logger.error(f"ModernBaseHandler: 设置清除标志定时器时出错: {str(e)}")
            else:
                if is_cross_register:
                    logger.error(f"更新跨寄存器控件失败: 控件 {widget_name}，寄存器地址 {reg_addr}")
                else:
                    logger.error(f"更新寄存器失败: 控件 {widget_name}")

        except Exception as e:
            logger.error(f"处理控件 {widget_name} 值变化时出错: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def _convert_widget_value_to_register_value(self, widget_name, widget_value):
        """将控件值转换为寄存器值"""
        widget_info = self.widget_register_map[widget_name]
        widget_type = widget_info.get("widget_type")
        
        if widget_type == "checkbox":
            # 检查是否是反向逻辑（Enb结尾的控件）
            if widget_name.endswith("Enb"):
                return 0 if widget_value else 1
            else:
                return 1 if widget_value else 0
        elif widget_type in ["spinbox", "combobox"]:
            return int(widget_value)
        elif widget_type == "lineedit":
            try:
                return int(widget_value)
            except ValueError:
                return 0
        else:
            return int(widget_value)
    
    def _update_widget_ui(self, widget_name, bit_value):
        """更新控件UI状态"""
        # 直接调用带有完整错误处理的方法
        self._update_widget_ui_with_signal_block(widget_name, bit_value)

    def _update_widget_ui_with_signal_block(self, widget_name, bit_value):
        """更新控件UI状态（使用信号阻断防止触发控件变化事件）"""
        try:
            widget = getattr(self.ui, widget_name)
            widget_info = self.widget_register_map[widget_name]
            widget_type = widget_info.get("widget_type")

            # 检查bit_value是否为None，如果是则使用寄存器配置文件中的默认值
            if bit_value is None:
                logger.debug(f"控件 {widget_name} 的bit_value为None，尝试使用寄存器配置默认值")
                # 尝试从寄存器配置中获取默认值
                default_value = widget_info.get("default", "0")
                if widget_type == "checkbox":
                    bit_value = 0 if default_value == "0" else 1
                elif widget_type in ["spinbox", "combobox"]:
                    # 对于spinbox和combobox，尝试解析默认值
                    try:
                        if isinstance(default_value, str) and len(default_value) > 2 and all(c in '01' for c in default_value):
                            # 二进制字符串
                            bit_value = int(default_value, 2)
                            logger.debug(f"控件 {widget_name} 使用二进制默认值: {default_value} -> {bit_value}")
                        else:
                            # 十进制字符串或数字
                            bit_value = int(default_value)
                            logger.debug(f"控件 {widget_name} 使用十进制默认值: {default_value}")
                    except (ValueError, TypeError):
                        logger.warning(f"控件 {widget_name} 无法解析默认值 {default_value}，使用0")
                        bit_value = 0
                elif widget_type == "lineedit":
                    bit_value = 0
                else:
                    bit_value = 0

            # 阻断信号以避免循环更新（参考原BaseHandler实现）
            widget.blockSignals(True)

            try:
                # 根据控件类型更新UI
                if widget_type == "checkbox":
                    # 检查是否是反向逻辑（Enb结尾的控件）
                    if widget_name.endswith("Enb"):
                        widget.setChecked(bit_value == 0)
                    else:
                        widget.setChecked(bit_value == 1)
                elif widget_type == "spinbox":
                    # 确保bit_value是整数
                    try:
                        int_value = int(bit_value)
                        widget.setValue(int_value)
                    except (ValueError, TypeError):
                        logger.warning(f"控件 {widget_name} 的bit_value无法转换为整数: {bit_value}，使用0")
                        widget.setValue(0)
                elif widget_type == "combobox":
                    # 确保bit_value是有效的索引
                    try:
                        int_value = int(bit_value)

                        # 特殊处理PLL2R3控件（值映射为[0,1,2,4]）
                        if widget_name == "PLL2R3":
                            # 查找对应的索引
                            target_index = -1
                            for i in range(widget.count()):
                                item_data = widget.itemData(i)
                                if item_data == int_value:
                                    target_index = i
                                    break

                            if target_index >= 0:
                                widget.setCurrentIndex(target_index)
                                logger.debug(f"PLL2R3控件设置: bit_value={int_value} -> 索引={target_index}")
                            else:
                                logger.warning(f"PLL2R3控件的bit_value {int_value} 未找到对应索引，使用0")
                                widget.setCurrentIndex(0)
                        else:
                            # 常规ComboBox处理
                            if widget.count() == 0:
                                # ComboBox还没有选项，延迟设置
                                logger.debug(f"控件 {widget_name} 还没有选项，延迟设置值 {bit_value}")
                                # 存储待设置的值，稍后在选项设置完成后再设置
                                if not hasattr(self, '_pending_combobox_values'):
                                    self._pending_combobox_values = {}
                                self._pending_combobox_values[widget_name] = int_value
                            elif 0 <= int_value < widget.count():
                                widget.setCurrentIndex(int_value)
                            else:
                                # 特殊处理PLL2C1和PLL2C3控件
                                if widget_name in ["PLL2C1", "PLL2C3"]:
                                    # 这些控件的寄存器默认值是4，但有效选项只有0,1,2
                                    # 当bit_value为4时，设置为2（对应40pF）
                                    if int_value == 4:
                                        widget.setCurrentIndex(2)
                                        logger.info(f"控件 {widget_name} 的bit_value为4，特殊处理设置为索引2 (40pF)")
                                    else:
                                        logger.warning(f"控件 {widget_name} 的bit_value超出范围: {bit_value}，使用0")
                                        widget.setCurrentIndex(0)
                                else:
                                    logger.warning(f"控件 {widget_name} 的bit_value超出范围: {bit_value}，使用0")
                                    widget.setCurrentIndex(0)
                    except (ValueError, TypeError):
                        logger.warning(f"控件 {widget_name} 的bit_value无法转换为整数: {bit_value}，使用0")
                        widget.setCurrentIndex(0)
                elif widget_type == "lineedit":
                    widget.setText(str(bit_value))

                logger.debug(f"已更新控件 {widget_name} 的UI状态为 {bit_value}")

            finally:
                # 恢复信号连接
                widget.blockSignals(False)

        except AttributeError:
            logger.warning(f"控件 {widget_name} 不存在")
        except Exception as e:
            logger.error(f"更新控件 {widget_name} UI时出错: {str(e)}")
    
    def on_global_register_updated(self, reg_addr, reg_value):
        """处理全局寄存器更新信号"""
        try:
            logger.debug(f"收到寄存器更新信号: {reg_addr} = 0x{reg_value:04X}")

            # 检查是否是自己发送的更新信号（参考原BaseHandler实现）
            if hasattr(self, '_updating_from_widget') and self._updating_from_widget:
                logger.debug(f"忽略自己发送的更新信号: {reg_addr}")
                self._updating_from_widget = False  # 重置标志
                return

            # 查找受影响的控件
            affected_widgets = []
            for widget_name, widget_info in self.widget_register_map.items():
                if widget_info["register_addr"] == reg_addr:
                    affected_widgets.append(widget_name)

            if not affected_widgets:
                return

            # 更新所有受影响的控件
            for widget_name in affected_widgets:
                widget_info = self.widget_register_map[widget_name]
                bit_def = widget_info["bit_def"]
                bit_name = bit_def["name"]

                # 从RegisterManager获取最新的位字段值
                bit_value = self.register_manager.get_bit_field_value(reg_addr, bit_name)

                # 更新控件UI（使用信号阻断防止触发控件变化事件）
                self._update_widget_ui_with_signal_block(widget_name, bit_value)

            # 通知子类处理全局更新
            self.on_global_register_update(reg_addr, reg_value)

        except Exception as e:
            logger.error(f"处理全局寄存器更新时出错: {str(e)}")
    
    def _is_auto_write_enabled(self):
        """检查是否启用自动写入"""
        try:
            # 检查是否在测试环境中
            if self._is_in_test_environment():
                logger.debug("现代化Handler: 测试环境中，跳过自动写入检查")
                return False

            main_window = self._get_main_window()
            if main_window:
                auto_write_mode = getattr(main_window, 'auto_write_mode', False)
                logger.debug(f"现代化Handler: 检查自动写入状态 - 主窗口: {type(main_window).__name__}, auto_write_mode: {auto_write_mode}")
                return auto_write_mode
            else:
                logger.debug("现代化Handler: 无法获取主窗口，自动写入检查失败")
                return False
        except Exception as e:
            logger.error(f"现代化Handler: 检查自动写入状态时出错: {str(e)}")
            return False
    
    def _auto_write_register_to_chip(self, reg_addr, reg_value):
        """自动将寄存器值写入到芯片（参考原BaseHandler实现）

        Args:
            reg_addr: 寄存器地址
            reg_value: 寄存器值
        """
        try:
            # 获取主窗口实例（参考原实现）
            main_window = None
            if hasattr(self, 'main_window'):
                main_window = self.main_window
                logger.debug(f"现代化Handler: 通过 self.main_window 获取主窗口: {type(main_window).__name__}")
            elif hasattr(self, 'parent') and hasattr(self.parent, 'main_window'):
                main_window = self.parent.main_window
                logger.debug(f"现代化Handler: 通过 self.parent.main_window 获取主窗口: {type(main_window).__name__}")
            elif hasattr(self, 'parent') and hasattr(self.parent, 'parent'):
                main_window = self.parent.parent
                logger.debug(f"现代化Handler: 通过 self.parent.parent 获取主窗口: {type(main_window).__name__}")

            if not main_window:
                logger.warning("现代化Handler: 无法获取主窗口实例，跳过自动写入")
                return

            # 检查是否有寄存器操作服务
            if not hasattr(main_window, 'register_service'):
                logger.warning(f"现代化Handler: 主窗口 {type(main_window).__name__} 没有 register_service 属性，跳过自动写入")
                logger.debug(f"现代化Handler: 主窗口可用属性: {[attr for attr in dir(main_window) if not attr.startswith('_')]}")
                return

            # 执行写入操作（参考原实现）
            logger.info(f"现代化Handler: 开始自动写入寄存器 {reg_addr} = 0x{reg_value:04X} 到芯片")
            result = main_window.register_service.write_register(reg_addr, reg_value)
            logger.info(f"现代化Handler: 自动写入操作完成，返回结果: {result}")

        except Exception as e:
            logger.error(f"现代化Handler: 自动写入寄存器时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def _trigger_register_table_navigation(self, reg_addr):
        """触发寄存器表格跳转到对应的寄存器页面（参考原实现）

        只有控件变化才触发跳转，读取操作不触发跳转

        Args:
            reg_addr: 寄存器地址
        """
        try:
            # 检查是否是由控件变化触发的更新
            if not getattr(self, '_widget_triggered_update', False):
                logger.debug(f"现代化Handler: 非控件触发的更新，跳过寄存器表格跳转到 {reg_addr}")
                return

            # 获取主窗口实例
            main_window = self._get_main_window()
            if not main_window:
                logger.debug("现代化Handler: 无法获取主窗口，跳过寄存器表格跳转")
                return

            # 检查是否在测试环境中（放宽检查条件）
            if self._is_in_test_environment():
                logger.info(f"现代化Handler: 测试环境中，但仍尝试寄存器表格跳转到 {reg_addr}")
                # 不直接返回，继续尝试跳转

            logger.info(f"现代化Handler: 控件变化触发寄存器表格跳转到 {reg_addr}")

            # 检查主窗口是否有必要的组件
            if hasattr(main_window, 'table_handler'):
                logger.info(f"现代化Handler: 找到 table_handler: {type(main_window.table_handler)}")
            else:
                logger.warning("现代化Handler: 主窗口没有 table_handler")

            # 调用主窗口的寄存器选择方法，触发表格跳转
            if hasattr(main_window, 'on_register_selected'):
                logger.info(f"现代化Handler: 调用 main_window.on_register_selected({reg_addr})")
                main_window.on_register_selected(reg_addr)
                logger.info("现代化Handler: 寄存器表格跳转调用完成")
            else:
                logger.warning("现代化Handler: 主窗口没有 on_register_selected 方法")
                # 尝试其他可能的方法
                self._try_alternative_navigation_methods(main_window, reg_addr)

        except Exception as e:
            logger.error(f"触发寄存器表格跳转时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _try_alternative_navigation_methods(self, main_window, reg_addr):
        """尝试其他可能的寄存器导航方法"""
        try:
            # 方法1：直接调用table_handler的show_bit_fields方法
            if hasattr(main_window, 'table_handler') and hasattr(main_window.table_handler, 'show_bit_fields'):
                logger.info(f"现代化Handler: 尝试直接调用 table_handler.show_bit_fields({reg_addr})")
                # 获取寄存器值
                if hasattr(main_window, 'register_manager'):
                    reg_value = main_window.register_manager.get_register_value(reg_addr)
                    main_window.table_handler.show_bit_fields(reg_addr, reg_value)
                    logger.info("现代化Handler: 直接调用table_handler成功")
                    return

            # 方法2：尝试调用事件协调器
            if hasattr(main_window, 'event_coordinator') and hasattr(main_window.event_coordinator, 'handle_bit_field_selected'):
                logger.info(f"现代化Handler: 尝试调用 event_coordinator.handle_bit_field_selected({reg_addr})")
                main_window.event_coordinator.handle_bit_field_selected(reg_addr)
                logger.info("现代化Handler: 调用事件协调器成功")
                return

            # 方法3：尝试调用显示管理器
            if hasattr(main_window, 'display_manager') and hasattr(main_window.display_manager, 'update_bit_field_display'):
                logger.info(f"现代化Handler: 尝试调用 display_manager.update_bit_field_display({reg_addr})")
                if hasattr(main_window, 'register_manager'):
                    reg_value = main_window.register_manager.get_register_value(reg_addr)
                    main_window.display_manager.update_bit_field_display(reg_addr, reg_value)
                    logger.info("现代化Handler: 调用显示管理器成功")
                    return

            logger.warning("现代化Handler: 所有替代导航方法都失败")

        except Exception as e:
            logger.error(f"尝试替代导航方法时出错: {str(e)}")

    def _get_main_window(self):
        """获取主窗口引用"""
        try:
            # 方法1：直接从self获取main_window
            if hasattr(self, 'main_window') and self.main_window:
                main_window = self.main_window
                # 检查是否是函数或方法
                if callable(main_window):
                    logger.warning(f"ModernBaseHandler: self.main_window 是一个函数/方法: {type(main_window)}")
                    # 如果是函数，尝试调用它获取实际的主窗口
                    try:
                        main_window = main_window()
                        logger.info(f"ModernBaseHandler: 通过调用函数获取主窗口: {type(main_window)}")
                    except Exception as e:
                        logger.error(f"ModernBaseHandler: 调用main_window函数失败: {str(e)}")
                        main_window = None

                # 验证是否是有效的主窗口对象
                if main_window and hasattr(main_window, '__class__'):
                    return main_window

            # 方法2：从parent获取main_window
            if hasattr(self, 'parent') and self.parent and hasattr(self.parent, 'main_window'):
                main_window = self.parent.main_window
                # 检查是否是函数或方法
                if callable(main_window):
                    logger.warning(f"ModernBaseHandler: self.parent.main_window 是一个函数/方法: {type(main_window)}")
                    try:
                        main_window = main_window()
                        logger.info(f"ModernBaseHandler: 通过调用parent的函数获取主窗口: {type(main_window)}")
                    except Exception as e:
                        logger.error(f"ModernBaseHandler: 调用parent.main_window函数失败: {str(e)}")
                        main_window = None

                if main_window and hasattr(main_window, '__class__'):
                    return main_window

            # 方法3：parent本身就是主窗口
            if hasattr(self, 'parent') and self.parent and hasattr(self.parent, 'parent'):
                main_window = self.parent.parent
                if main_window and hasattr(main_window, '__class__'):
                    return main_window

            logger.debug("ModernBaseHandler: 无法获取主窗口引用")
            return None

        except Exception as e:
            logger.error(f"ModernBaseHandler: 获取主窗口时出错: {str(e)}")
            return None

    def _is_in_batch_operation(self):
        """检查是否正在进行批量操作（参考原实现）"""
        try:
            from core.services.BatchOperationState import BatchOperationState
            return BatchOperationState.instance().is_in_batch_operation()
        except Exception as e:
            logger.debug(f"检查批量操作状态时出错: {str(e)}")
            return False

    def _is_in_test_environment(self):
        """检查是否在测试环境中"""
        try:
            # 方法1：检查是否有主窗口
            if not self._get_main_window():
                return True

            # 方法2：检查调用栈中是否包含测试相关的模块
            import inspect
            frame = inspect.currentframe()
            while frame:
                filename = frame.f_code.co_filename
                if 'test_' in filename or 'pytest' in filename or '__main__' in frame.f_globals.get('__name__', ''):
                    return True
                frame = frame.f_back

            # 方法3：检查sys.argv中是否包含测试脚本
            import sys
            if len(sys.argv) > 0:
                script_name = sys.argv[0]
                if 'test_' in script_name or 'pytest' in script_name:
                    return True

            return False
        except Exception as e:
            logger.debug(f"检查测试环境时出错: {str(e)}")
            return False

    def apply_pending_combobox_values(self):
        """应用待设置的ComboBox值"""
        if not hasattr(self, '_pending_combobox_values'):
            return

        try:
            for widget_name, pending_value in self._pending_combobox_values.items():
                try:
                    widget = getattr(self.ui, widget_name)
                    if hasattr(widget, 'count') and widget.count() > 0:
                        if 0 <= pending_value < widget.count():
                            widget.blockSignals(True)
                            widget.setCurrentIndex(pending_value)
                            widget.blockSignals(False)
                            logger.info(f"已应用待设置的ComboBox值: {widget_name} = {pending_value}")
                        else:
                            logger.warning(f"待设置的ComboBox值超出范围: {widget_name} = {pending_value}，使用0")
                            widget.blockSignals(True)
                            widget.setCurrentIndex(0)
                            widget.blockSignals(False)
                    else:
                        logger.warning(f"ComboBox {widget_name} 仍然没有选项，跳过设置")
                except AttributeError:
                    logger.warning(f"控件 {widget_name} 不存在，跳过设置")
                except Exception as e:
                    logger.error(f"应用ComboBox值时出错 {widget_name}: {str(e)}")

            # 清空待设置的值
            self._pending_combobox_values.clear()
            logger.info("所有待设置的ComboBox值已应用完成")

        except Exception as e:
            logger.error(f"应用待设置的ComboBox值时出错: {str(e)}")
    
    def _clear_widget_update_flag(self, main_window):
        """清除控件更新标志"""
        try:
            # 验证main_window是否是有效的对象实例
            if callable(main_window):
                logger.error(f"ModernBaseHandler: _clear_widget_update_flag收到函数而不是对象: {type(main_window)}")
                return

            if main_window and hasattr(main_window, '__class__') and hasattr(main_window, '_widget_triggered_update'):
                delattr(main_window, '_widget_triggered_update')
                logger.debug("ModernBaseHandler: 已清除控件更新标志")
        except Exception as e:
            logger.warning(f"ModernBaseHandler: 清除控件更新标志时出错: {str(e)}")
            logger.warning(f"ModernBaseHandler: main_window类型: {type(main_window)}")

    # === 子类需要实现的方法 ===

    def handle_register_value_change(self, widget_name):
        """处理寄存器值变化的回调函数，子类应覆盖此方法以实现特定功能（参考原BaseHandler实现）"""
        # 基类默认实现为空，子类可以重写
        _ = widget_name  # 避免未使用参数警告

    def on_register_value_changed(self, widget_name, reg_addr, reg_value):
        """寄存器值变化回调，子类可以重写此方法"""
        # 基类默认实现为空，子类可以重写
        _ = widget_name, reg_addr, reg_value  # 避免未使用参数警告

    def on_global_register_update(self, reg_addr, reg_value):
        """全局寄存器更新回调，子类可以重写此方法"""
        # 基类默认实现为空，子类可以重写
        _ = reg_addr, reg_value  # 避免未使用参数警告

    def refresh_all_widgets(self):
        """刷新所有控件状态以同步寄存器值

        这个方法会遍历所有已注册的控件，从寄存器管理器获取最新值并更新控件显示
        主要用于配置文件加载后的批量刷新
        """
        try:
            logger.debug(f"{self.__class__.__name__}: 开始刷新所有控件...")

            if not hasattr(self, 'widget_register_map') or not self.widget_register_map:
                logger.debug(f"{self.__class__.__name__}: 没有注册的控件需要刷新")
                return

            refreshed_count = 0
            for widget_name, widget_info in self.widget_register_map.items():
                try:
                    reg_addr = widget_info["register_addr"]

                    # 检查是否是手动控件（如频率输入框）
                    widget_type = widget_info.get("widget_type", "")
                    if widget_type == "lineedit" and "freq" in widget_name.lower():
                        # 对于频率输入框，跳过寄存器值更新，保持当前显示值
                        logger.debug(f"{self.__class__.__name__}: 跳过频率输入控件 {widget_name} 的寄存器值更新")
                        continue

                    # 从寄存器管理器获取最新值
                    current_value = None
                    if self.register_manager and hasattr(self.register_manager, 'get_register_value'):
                        current_value = self.register_manager.get_register_value(reg_addr)
                    else:
                        # 回退到主窗口的寄存器字典
                        main_window = getattr(self, 'main_window', None) or self.parent
                        if main_window and hasattr(main_window, 'registers') and reg_addr in main_window.registers:
                            current_value = main_window.registers[reg_addr]

                    if current_value is not None:
                        # 更新控件显示（不触发信号发送）
                        self._updating_from_widget = True
                        try:
                            self._update_widget_from_register(widget_name, current_value)
                            refreshed_count += 1
                            logger.debug(f"{self.__class__.__name__}: 已刷新控件 {widget_name} = 0x{current_value:04X}")
                        finally:
                            self._updating_from_widget = False
                    else:
                        logger.debug(f"{self.__class__.__name__}: 无法获取寄存器 {reg_addr} 的值，跳过控件 {widget_name}")

                except Exception as e:
                    logger.warning(f"{self.__class__.__name__}: 刷新控件 {widget_name} 时出错: {str(e)}")

            logger.info(f"{self.__class__.__name__}: 成功刷新了 {refreshed_count} 个控件")

        except Exception as e:
            logger.error(f"{self.__class__.__name__}: 刷新所有控件时出错: {str(e)}")

    def _update_widget_from_register(self, widget_name, reg_value):
        """从寄存器值更新单个控件

        Args:
            widget_name: 控件名称
            reg_value: 寄存器值
        """
        try:
            if widget_name not in self.widget_register_map:
                logger.warning(f"控件 {widget_name} 未在映射表中找到")
                return

            widget_info = self.widget_register_map[widget_name]

            # 提取控件对应的位值
            bit_value = self._extract_bit_value(reg_value, widget_info)

            # 更新控件UI
            self._update_widget_ui_with_signal_block(widget_name, bit_value)

        except Exception as e:
            logger.error(f"从寄存器更新控件 {widget_name} 时出错: {str(e)}")

    def _extract_bit_value(self, reg_value, widget_info):
        """从寄存器值中提取控件对应的位值

        Args:
            reg_value: 寄存器值
            widget_info: 控件信息字典

        Returns:
            int: 提取的位值
        """
        try:
            bit_def = widget_info.get("bit_def", {})
            bit_range = bit_def.get("bit", "0")

            # 解析位范围
            if ':' in bit_range:
                # 位范围格式：如 "7:4"
                high, low = map(int, bit_range.split(':'))
                # 创建掩码并提取位值
                mask = ((1 << (high - low + 1)) - 1) << low
                bit_value = (reg_value & mask) >> low
            else:
                # 单个位格式：如 "3"
                bit_pos = int(bit_range)
                bit_value = (reg_value >> bit_pos) & 1

            logger.debug(f"从寄存器值 0x{reg_value:04X} 提取位 {bit_range} = {bit_value}")
            return bit_value

        except Exception as e:
            logger.error(f"提取位值时出错: {str(e)}")
            return 0

    def closeEvent(self, event):
        """处理窗口关闭事件"""
        try:
            # 缓存LineEdit控件值（如果子类实现了相关方法）
            if hasattr(self, '_cache_all_lineedit_values'):
                try:
                    self._cache_all_lineedit_values()
                    logger.debug(f"{self.__class__.__name__} 窗口关闭时已缓存LineEdit值")
                except Exception as cache_error:
                    logger.error(f"缓存LineEdit值时出错: {str(cache_error)}")

            # 发送窗口关闭信号
            self.window_closed.emit()
            logger.info(f"{self.__class__.__name__} 窗口关闭，已发送关闭信号")
        except Exception as e:
            logger.error(f"处理 {self.__class__.__name__} 窗口关闭事件时发生错误: {str(e)}")

        # 调用父类方法继续处理关闭事件
        super().closeEvent(event)
