import os
import re
from PyQt5.QtWidgets import (QLineEdit, QPushButton, QHBoxLayout, QLabel,
                            QMessageBox, QComboBox, QWidget,
                            QListWidget, QListWidgetItem, QCompleter)
from PyQt5.QtCore import QStringListModel
from PyQt5.QtCore import Qt, pyqtSignal, QObject, QSettings
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class RegisterIOHandler(QObject):
    """处理寄存器输入/输出控制相关的操作"""

    # 定义信号
    read_requested = pyqtSignal(int)  # 请求读取寄存器
    write_requested = pyqtSignal(int, int)  # 请求写入寄存器(地址, 值)
    read_all_requested = pyqtSignal()  # 请求读取所有寄存器
    write_all_requested = pyqtSignal()  # 请求写入所有寄存器
    save_requested = pyqtSignal()  # 请求保存配置
    load_requested = pyqtSignal()  # 请求加载配置
    value_changed = pyqtSignal(str, int)  # 寄存器值已更改(地址, 新值)
    search_requested = pyqtSignal(str) # 请求搜索位段
    bit_field_selected = pyqtSignal(str) # 位段被选中 (传递寄存器地址)
    
    def batch_read_registers(self, addresses):
        """批量读取寄存器实际值"""
        try:
            # 使用SPI服务直接读取寄存器
            result = {}
            for addr in addresses:
                if self.spi_service:
                    value = self.spi_service.read_register(addr)
                    result[addr] = value
                else:
                    # 如果SPI服务不可用，从register_manager获取缓存值
                    result[addr] = self.register_manager.get_register_value(addr) if self.register_manager else 0
            return result
        except Exception as e:
            QMessageBox.critical(self.parent, "读取错误", f"批量读取失败: {str(e)}")
            return {}

    def __init__(self, parent=None, register_manager=None, spi_service=None):
        super().__init__(parent)
        self.parent = parent
        self.register_manager = register_manager
        self.spi_service = spi_service # 直接使用传入的SPI服务
        self.rx_value_label = None
        self.rx_value_edit = None
        self.addr_label = None
        self.addr_lineedit = None
        self.port_combo = None
        self.refresh_button = None
        self.read_button = None
        self.write_button = None
        self.read_all_button = None
        self.write_all_button = None
        self.save_button = None
        self.load_button = None
        self.settings = QSettings("FSJ", "FSJRead")
        self.value_label = None  # 存储R0 Value:标签的引用
        self.search_results_list = None # 搜索结果列表
        self.search_edit = None # Ensure search_edit is initialized
        self.value_line_edit = None # Ensure value_line_edit is initialized
        self.addr_line_edit = None # Ensure addr_line_edit is initialized

        # 添加标志位防止程序化更新时触发写操作
        self._updating_display = False

        # 连接SPI服务的端口刷新信号
        if self.spi_service:
            self.spi_service.ports_refreshed.connect(self._update_port_combo)

    def create_rx_value_layout(self):
        """创建寄存器值读写布局"""
        # 创建水平布局（COM端口选择、地址和值显示全部在一行）
        top_layout = QHBoxLayout()

        # 添加左侧间隔，让整体右移
        top_layout.addSpacing(20)  # 添加20像素的左侧间隔

        # COM端口选择部分
        port_label = QLabel('COM端口:')
        self.port_combo = QComboBox()
        self.port_combo.setMinimumWidth(350)  # 适中的最小宽度，能显示完整端口信息但不过宽
        self.port_combo.setMaximumWidth(450)  # 适中的最大宽度，不会覆盖搜索框
        # 设置下拉框的大小策略为固定，避免过度扩展
        from PyQt5.QtWidgets import QSizePolicy
        self.port_combo.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)

        self.refresh_btn = QPushButton('刷新')
        self.refresh_btn.clicked.connect(self.refresh_ports)

        top_layout.addWidget(port_label)
        top_layout.addWidget(self.port_combo)
        top_layout.addWidget(self.refresh_btn)

        # 在刷新按钮和Address标签之间添加更大的间隔
        top_layout.addSpacing(25)  # 增加间隔，从15改为25像素

        # 地址和值部分 - 创建一个居中的子布局
        addr_value_layout = QHBoxLayout()

        # 地址部分
        addr_label = QLabel("Address:")
        addr_label.setStyleSheet("font-weight: bold; color: #333;")  # 加粗标签
        self.addr_line_edit = QLineEdit()
        self.addr_line_edit.setReadOnly(True)
        self.addr_line_edit.setFixedWidth(160)  # 增加宽度，从80改为160
        self.addr_line_edit.setAlignment(Qt.AlignCenter)
        self.addr_line_edit.setStyleSheet("""
            QLineEdit {
                border: 2px solid #ccc;
                border-radius: 5px;
                padding: 5px;
                font-size: 12px;
                font-weight: bold;
                background-color: #f9f9f9;
            }
        """)

        addr_value_layout.addWidget(addr_label)
        addr_value_layout.addWidget(self.addr_line_edit)
        addr_value_layout.addSpacing(20)  # 地址和值之间的间隔

        # 值部分
        self.value_label = QLabel("R0 Value:")  # 使用"R0 Value:"标签匹配图二
        self.value_label.setStyleSheet("font-weight: bold; color: #333;")  # 加粗标签
        self.value_line_edit = QLineEdit()
        self.value_line_edit.setFixedWidth(200)  # 增加宽度，从120改为200
        self.value_line_edit.setReadOnly(False)
        self.value_line_edit.setAlignment(Qt.AlignCenter)
        self.value_line_edit.setStyleSheet("""
            QLineEdit {
                border: 2px solid #4CAF50;
                border-radius: 5px;
                padding: 5px;
                font-size: 12px;
                font-weight: bold;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #45a049;
                background-color: #f0fff0;
            }
        """)
        self.value_line_edit.returnPressed.connect(lambda: self.on_value_changed(self.value_line_edit.text()))

        addr_value_layout.addWidget(self.value_label)
        addr_value_layout.addWidget(self.value_line_edit)

        # 将地址值布局添加到主布局
        top_layout.addLayout(addr_value_layout)

        # 添加更多弹性空间，让控件更居中
        top_layout.addStretch(3)  # 增加弹性空间
        
        # 搜索控件
        search_label = QLabel("搜索位段:")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入位段名称关键字")
        self.search_edit.setMinimumWidth(400)
        # 添加自动补全功能
        self.completer = QCompleter()
        self.completer.setCaseSensitivity(Qt.CaseInsensitive)  # 不区分大小写
        self.completer.setFilterMode(Qt.MatchContains)  # 包含匹配而不是前缀匹配
        self.completer.setCompletionMode(QCompleter.PopupCompletion)  # 设置为弹出窗口模式
        self.search_edit.setCompleter(self.completer)
        
        # 监听文本变化和自动补全项选择
        self.search_edit.textChanged.connect(self._handle_search_click)
        self.completer.activated.connect(self._handle_completer_activated)
        
        self.search_btn = QPushButton("搜索")
        self.search_btn.clicked.connect(self._handle_search_click) # 连接到新的处理方法
        
        # 将搜索标签和搜索框添加到顶部水平布局
        top_layout.addWidget(search_label)
        top_layout.addWidget(self.search_edit)
        # Add the search button to layout
        top_layout.addWidget(self.search_btn)

        # 创建搜索结果列表
        self.search_results_list = QListWidget()
        self.search_results_list.setVisible(False) # 初始隐藏
        self.search_results_list.setMaximumHeight(150) # 限制最大高度
        self.search_results_list.itemClicked.connect(self._handle_result_selection)

        # 设置结果列表的父控件，以便它可以覆盖其他控件
        # 确保 self.parent 是一个合适的容器，例如主窗口或包含此布局的QWidget
        # if self.parent:
        #     self.search_results_list.setParent(self.parent)
        # Make the list a top-level window that behaves like a popup
        self.search_results_list.setWindowFlags(Qt.Popup) # 设置为弹出窗口样式

        # 安装事件过滤器以处理键盘事件和焦点事件
        self.search_edit.installEventFilter(self)
        self.search_results_list.installEventFilter(self)

        # 创建一个容器 QWidget 来容纳整个顶部 IO 布局
        io_widget = QWidget()
        # 将 top_layout 设置为该容器的布局
        io_widget.setLayout(top_layout)

        # 返回包含所有IO控件的容器 QWidget
        return io_widget

    def toggle_buttons(self, enable):
        """启用/禁用所有按钮"""
        # Include search_btn if it exists
        buttons_to_toggle = [self.read_btn, self.write_btn, 
                             self.read_all_btn, self.write_all_btn,
                             self.dumpall_btn, self.save_btn, self.load_btn]
        if hasattr(self, 'search_btn') and self.search_btn:
            buttons_to_toggle.append(self.search_btn)
            
        for button in buttons_to_toggle:
            if button:
                button.setEnabled(enable)

    def get_current_value(self):
        """获取当前输入框中的值"""
        input_text = self.value_line_edit.text().strip()
        if input_text.startswith("0x"):
            try:
                return int(input_text, 16)
            except ValueError:
                return None
        else:
            try:
                return int(input_text, 16) if all(c in "0123456789ABCDEFabcdef" for c in input_text) else int(input_text)
            except ValueError:
                return None
                
    def update_search_results(self, results):
        """更新搜索结果列表"""
        self.search_results_list.clear()
        if results:
            for bit_name, addr, _ in results:  # 使用 _ 表示未使用的 reg_name 参数
                item_text = f"{bit_name}"
                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, addr) # 将地址存储在 UserRole 中
                self.search_results_list.addItem(item)

            # 计算列表位置 - 相对于屏幕
            search_edit_global_pos = self.search_edit.mapToGlobal(self.search_edit.rect().bottomLeft())
            self.search_results_list.move(search_edit_global_pos)

            self.search_results_list.setFixedWidth(self.search_edit.width()) # 设置宽度与搜索框一致
            if not self.search_results_list.isVisible():
                 self.search_results_list.show() # Use show() for popup window
            if self.search_results_list.count() > 0:
                self.search_results_list.setCurrentRow(0) # 默认选中第一项
            # self.search_results_list.raise_() # raise_() might not be needed for Qt.Popup
            # self.search_results_list.activateWindow() # Try activating
            # self.search_results_list.setFocus() # Avoid stealing focus immediately
        else:
            self.search_results_list.hide() # Use hide() for popup window

    def _handle_result_selection(self, item):
        """处理列表项点击或回车选择"""
        if item:
            selected_addr = item.data(Qt.UserRole)
            selected_text = item.text().split(' (')[0] # 获取位段名称部分
            logger.info(f"搜索结果选中: {item.text()}, 地址: {selected_addr}")
            # Temporarily block signals to prevent recursive search trigger
            self.search_edit.blockSignals(True)
            self.search_edit.setText(selected_text) # 将选中的位段名称填入搜索框
            self.search_edit.blockSignals(False)
            self.search_results_list.hide() # 选择后隐藏列表
            self.bit_field_selected.emit(selected_addr) # Emit signal with address
            # self.search_edit.setFocus() # Return focus to search edit - might not be needed
            # Clear selection in the list? Maybe not necessary as it's hidden.
            # self.search_results_list.setCurrentRow(-1)


    def _check_focus_and_hide_list(self):
        """检查焦点是否仍在搜索框或结果列表内，如果不在则隐藏列表"""
        if not self.search_edit.hasFocus() and not self.search_results_list.hasFocus():
            self.search_results_list.hide()

    def refresh_ports(self):
        """请求SPI服务刷新可用串口列表"""
        if self.spi_service:
            logger.info("RegisterIOHandler: Requesting SPI service to refresh ports.")
            self.spi_service.refresh_available_ports()
        else:
            logger.warning("RegisterIOHandler: SPI service not available, cannot refresh ports.")
            self._update_port_combo([]) # 使用空列表更新，以显示无端口状态

    def _update_port_combo(self, port_details):
        """根据SPI服务提供的端口列表更新COM端口下拉框"""
        self.port_combo.clear()
        if port_details:
            for port_info in port_details:
                device = port_info.get('device')
                description = port_info.get('description', 'N/A')
                display_text = f"{device}: {description[:80]}"  # 进一步增加描述长度，从30改为80字符，确保完整显示
                self.port_combo.addItem(display_text, device)
                logger.info(f"RegisterIOHandler: Added COM port to combo: {device}")
            
            # 选择上次配置的端口（如果有）
            configured_port = self.settings.value("SPI/Port", "")
            if configured_port:
                for i in range(self.port_combo.count()):
                    port_data = self.port_combo.itemData(i)
                    if port_data == configured_port:
                        self.port_combo.setCurrentIndex(i)
                        break
            
            # 自动选择第一个端口并设置（如果当前没有选择或选择无效）
            if self.port_combo.currentIndex() == -1 and self.port_combo.count() > 0:
                self.port_combo.setCurrentIndex(0)
            
            # 触发一次端口选择变化，以确保SPI服务端口被设置
            self.port_selection_changed(self.port_combo.currentIndex())

        else:
            self.port_combo.addItem("未检测到COM端口", "")
            logger.info("RegisterIOHandler: No COM ports detected or provided by service.")
            # 确保在没有端口时，SPI服务也被告知（例如通过设置端口为None或特定值）
            if self.spi_service:
                self.spi_service.set_port(None) # 或者根据spi_service的set_port实现来传递合适的值
    
    def port_selection_changed(self, index):
        """端口选择更改事件处理"""
        if index < 0 or not self.port_combo:
            return
            
        selected_port = self.port_combo.currentData()
        if not selected_port:
            return
            
        # 保存选择的端口
        self.settings.setValue("SPI/Port", selected_port)
        
        # 更新SPI端口
        if self.spi_service:
            success = self.spi_service.set_port(selected_port)
            if success:
                logger.info(f"已设置SPI端口: {selected_port}")
            else:
                logger.error(f"设置SPI端口失败: {selected_port}")
    
    def on_value_changed(self, text):
        """处理值文本变化事件"""
        # 如果正在程序化更新显示，不触发写操作
        if self._updating_display:
            logger.debug("IOHandler: 程序化更新显示中，跳过写入操作")
            return

        # 检查文本是否有效
        if not text.strip():
            return

        # 验证输入格式
        if not self.validate_hex_input(text):
            # 如果验证失败，恢复旧值或清空
            current_addr = self.addr_line_edit.text()
            if current_addr and self.register_manager:
                try:
                    addr_int = int(current_addr, 16) if current_addr.startswith("0x") else int(current_addr)
                    old_value = self.register_manager.get_register_value(addr_int)
                    self.set_value_display(old_value) # 恢复旧值
                except (ValueError, KeyError):
                    self.value_line_edit.setText("0000") # 或清空
            else:
                self.value_line_edit.setText("0000")
            return

        try:
            # 尝试解析十六进制值 (验证已通过，这里应该能成功)
            if text.lower().startswith("0x"):
                value = int(text, 16)
            else:
                # 允许直接输入十六进制字符，无需0x前缀
                value = int(text, 16)

            # 获取当前寄存器地址
            addr_text = self.addr_line_edit.text()
            if not addr_text:
                return

            addr_int = int(addr_text, 16) if addr_text.startswith("0x") else int(addr_text)

            # 发出写入请求信号
            logger.info(f"IOHandler: 用户输入触发写入请求, 地址: 0x{addr_int:02X}, 值: 0x{value:04X}")
            self.write_requested.emit(addr_int, value)
            # 同步更新寄存器仓库
            # self.register_repo.set_register_value(addr_int, value)

            # 不再发送 value_changed 信号，因为写入请求会触发更新
            # self.value_changed.emit(addr_text.replace("0x", ""), value)

        except ValueError:
            # 理论上验证后不应进入此分支，但保留以防万一
            logger.warning(f"无效的十六进制值: {text}")
            QMessageBox.warning(self.parent, "输入错误", "请输入有效的十六进制值 (例如 0x1A 或 1A)")
            # 恢复旧值
            current_addr = self.addr_line_edit.text()
            if current_addr and self.register_manager:
                try:
                    addr_int = int(current_addr, 16) if current_addr.startswith("0x") else int(current_addr)
                    old_value = self.register_manager.get_register_value(addr_int)
                    self.set_value_display(old_value)
                except (ValueError, KeyError):
                    self.value_line_edit.setText("0000")
            else:
                self.value_line_edit.setText("0000")

    def _handle_search_click(self, text_or_checked_state=None):
        """处理搜索文本变化或按钮点击事件（改进版：移除字符长度限制）"""
        # Determine search_text based on the type of the input argument
        if isinstance(text_or_checked_state, str):
            search_text_raw = text_or_checked_state
        else:  # Argument is likely from button click (bool) or None
            search_text_raw = self.search_edit.text()

        search_text = search_text_raw.strip()  # Strip the determined text
        logger.info(f"搜索请求: '{search_text}' (raw: '{search_text_raw}')")

        # Stop any existing timer first.
        if hasattr(self, 'search_timer') and self.search_timer.isActive():
            self.search_timer.stop()

        if not search_text:  # If stripped text is empty
            self.search_results_list.hide()
            return

        # 改进：移除字符长度限制，允许用户输入任意长度的搜索文本
        # 对于任何非空输入都立即执行搜索，提升用户体验
        if self.register_manager:
            results = self.register_manager.search_bit_fields(search_text)
            model = QStringListModel([r[0] for r in results] if results else [])
            self.completer.setModel(model)

            self.search_requested.emit(search_text)
            self.update_search_results(results)
            
    def _handle_completer_activated(self, text):
        """处理自动补全项选择事件"""
        # 查找匹配的位段地址并触发选择
        results = self.register_manager.search_bit_fields(text)
        if results:
            # 更新搜索框文本并触发选择
            self.search_edit.setText(text)
            self.bit_field_selected.emit(results[0][1])
            # 立即隐藏下拉列表
            if hasattr(self, 'search_results_list') and self.search_results_list.isVisible():
                self.search_results_list.hide()


    def _check_focus_and_hide_list(self):
        """检查焦点是否仍在搜索框或结果列表内，如果不在则隐藏列表"""
        if not self.search_edit.hasFocus() and not self.search_results_list.hasFocus():
            self.search_results_list.hide()


    def set_address_display(self, addr):
        """更新地址显示"""
        self._updating_display = True
        try:
            self.addr_line_edit.setText(f"0x{addr:02X}")
            self.value_label.setText(f"R{addr:X} Value:") # Dynamically update the value label
        finally:
            self._updating_display = False

    def set_value_display(self, value):
        """设置值显示"""
        if self.value_line_edit:
            self._updating_display = True
            try:
                self.value_line_edit.setText(f"0x{value:04X}")
            finally:
                self._updating_display = False

    def get_selected_port(self):
        """获取当前选择的COM端口"""
        if self.port_combo and self.port_combo.currentIndex() >= 0:
            return self.port_combo.currentData()
        return None

    def validate_hex_input(self, input_text):
        """统一验证十六进制输入规则"""
        # 清理输入空格并转为大写
        cleaned_input = input_text.strip().upper()
        
        # 处理0x前缀
        if cleaned_input.startswith('0X'):
            cleaned_input = cleaned_input[2:]
        
        # 有效性检查
        if not re.fullmatch(r'^[0-9A-F]{1,4}$', cleaned_input):
            QMessageBox.warning(
                self.parent,
                "输入格式错误",
                "请输入1-4位十六进制数\n有效格式示例: ABCD 或 0x123",
                QMessageBox.Ok
            )
            return False
        
        # 数值范围检查
        int_value = int(cleaned_input, 16)
        if not (0 <= int_value <= 0xFFFF):
            QMessageBox.warning(
                self.parent,
                "数值越界",
                "输入值超出范围 (0x0000 - 0xFFFF)",
                QMessageBox.Ok
            )
            return False
        
        return True

    def validate_register_input(self, text):
        """验证寄存器输入"""
        if not text:
            return True
        try:
            value = int(text, 16) if text.startswith("0x") else int(text)
            bits = self.get_current_bits()
            max_value = (1 << bits) - 1
            return 0 <= value <= max_value
        except ValueError:
            return False

    def read_button_clicked(self):
        """读取按钮点击事件"""
        addr = self.addr_line_edit.text()
        if addr:
            try:
                # 尝试将地址转换为整数
                addr_int = int(addr, 16) if addr.startswith("0x") else int(addr)
                self.read_requested.emit(addr_int)
            except ValueError:
                QMessageBox.warning(self.parent, "错误", "请输入有效的地址值")
    
    def write_button_clicked(self):
        """写入按钮点击事件"""
        addr = self.addr_line_edit.text()
        value = self.get_current_value()
        
        if not addr or value is None:
            QMessageBox.warning(self.parent, "错误", "请输入有效的地址和值")
            return
            
        try:
            addr_int = int(addr, 16) if addr.startswith("0x") else int(addr)
            self.write_requested.emit(addr_int, value)
            # 注意：不再直接更新寄存器仓库，而是通过信号机制处理
        except ValueError:
            QMessageBox.warning(self.parent, "错误", "请输入有效的地址和值")
    
    def read_all_button_clicked(self):
        """读取全部按钮点击事件"""
        self.read_all_requested.emit()
    
    def register_buttons(self, read_btn, write_btn, read_all_btn, write_all_btn, dumpall_btn, save_btn, load_btn):
        """注册按钮引用，以便切换启用/禁用状态"""
        self.read_btn = read_btn
        self.write_btn = write_btn 
        self.read_all_btn = read_all_btn
        self.write_all_btn = write_all_btn
        self.dumpall_btn = dumpall_btn
        self.save_btn = save_btn
        self.load_btn = load_btn
        
        # 绑定事件处理器
        # self.read_btn.clicked.connect(self.read_button_clicked)
        # self.write_btn.clicked.connect(self.write_button_clicked)
        # self.read_all_btn.clicked.connect(self.read_all_button_clicked)
        # self.write_all_btn.clicked.connect(self.write_all_button_clicked)
        # self.dumpall_btn.clicked.connect(self.dumpall_button_clicked)
        # self.save_btn.clicked.connect(self.save_button_clicked)
        # self.load_btn.clicked.connect(self.load_button_clicked)
    
    def search_button_clicked(self):
        """搜索按钮点击事件"""
        search_text = self.search_edit.text().strip()
        if not search_text:
            QMessageBox.information(self.parent, "提示", "请输入要搜索的位段名称")
            return
            
        # 调用父窗口的搜索方法
        if self.parent and hasattr(self.parent, "search_bit_field"):
            self.parent.search_bit_field(search_text)
        else:
            QMessageBox.warning(self.parent, "错误", "搜索功能未实现")