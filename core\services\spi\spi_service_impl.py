from PyQt5.QtCore import pyqtSignal, QMutex, QMutexLocker
from .spiPrivacy import SPIManager
from .spi_interface import ISPIService
import time
from queue import Queue, Empty
import sys
import random
import traceback
import serial.tools.list_ports
from utils.Log import get_module_logger

logger = get_module_logger(__name__)

class SPIServiceImpl(ISPIService):
    """SPI服务的具体实现"""

    # 从 ISPIService 继承的信号，在此重新声明以确保正确性
    spi_operation_complete = pyqtSignal(str, int, bool)  # (address, value, is_read)
    spi_error_occurred = pyqtSignal(str)
    operation_timeout = pyqtSignal()
    ports_refreshed = pyqtSignal(list) # 信号：端口列表已刷新
    simulation_mode_changed = pyqtSignal(bool) # 信号：模拟模式状态改变
    connection_status_changed = pyqtSignal(bool) # 信号：连接状态改变
    error_occurred = pyqtSignal(str) # 信号：发生错误

    # 为了兼容性，添加operation_complete信号作为spi_operation_complete的别名
    operation_complete = pyqtSignal(str, int, bool)

    # 类常量
    MAX_QUEUE_SIZE = 900
    DEFAULT_TIMEOUT = 2.0  # 减少超时时间到2秒，提高批量操作速度

    @property
    def is_port_selected(self) -> bool:
        """检查是否已选择端口"""
        return self._current_port is not None

    def __init__(self, byte_order='big', parent=None):
        super().__init__(parent)
        self._spi_device = None
        self.active = True
        self.operations = Queue(maxsize=self.MAX_QUEUE_SIZE)  # 初始化队列
        self.current_op_id = 0
        self.lock = QMutex()
        self.byte_order = byte_order  # 新增字节序配置
        self._simulation_mode = False  # 使用私有变量存储模拟模式状态

        # 模拟模式下使用的寄存器缓存
        self._simulated_registers = {}

    @property
    def simulation_mode(self) -> bool:
        """获取当前是否为模拟模式"""
        return self._simulation_mode

    def set_simulation_mode(self, enabled: bool):
        """设置是否启用模拟模式
        
        Args:
            enabled: 布尔值，True表示启用模拟模式，False表示禁用
        """
        # 如果要禁用模拟模式，先检查SPI是否可用
        if not enabled:
            spi_available = self.check_spi_available()
            if not spi_available:
                # SPI不可用，无法禁用模拟模式
                self._simulation_mode = True
                self.spi_error_occurred.emit("SPI连接不可用，无法禁用模拟模式")
                return
        
        # 更新模拟模式状态
        self._simulation_mode = enabled
        self.simulation_mode_changed.emit(enabled) # 发射信号
        self.connection_status_changed.emit(self.is_connected) # 反映硬件连接状态的变化

        if enabled:
            # 启用模拟模式时，可以清空模拟寄存器缓存，也可以保留之前的值
            # self._simulated_registers = {}
            logger.info("已启用模拟模式")
        else:
            logger.info("已禁用模拟模式，使用实际SPI硬件")

    @property
    def is_connected(self) -> bool:
        """检查是否已连接到实际硬件设备"""
        if self._simulation_mode:
            return False # 模拟模式下不认为已连接
        # 检查SPI设备是否已初始化且串口已打开
        return self._spi_device and hasattr(self._spi_device, 'ser') and self._spi_device.ser and self._spi_device.ser.is_open

    def get_connection_status(self) -> dict:
        """获取详细的连接状态信息"""
        connected = self.is_connected
        status = {
            'connected': connected,
            'mode': 'Simulation' if self._simulation_mode else 'Hardware',
            'port': None
        }
        if connected and self._spi_device and hasattr(self._spi_device, 'ser') and self._spi_device.ser:
            status['port'] = self._spi_device.ser.port
        
        # 发射连接状态改变信号 (如果状态有变)
        # 注意: 需要一个机制来跟踪之前的状态以避免频繁发射信号
        # self.connection_status_changed.emit(connected) 
        
        return status

    def cleanup(self):
        """清理SPI资源"""
        # 停止处理操作
        self.active = False

        # 清空操作队列
        with QMutexLocker(self.lock):
            while not self.operations.empty():
                try:
                    self.operations.get_nowait()
                except Empty:
                    break

        # 关闭SPI设备
        self._close_spi_device()
        self.connection_status_changed.emit(False) # 清理后连接断开

        # 停止工作线程
        if hasattr(self, 'worker_thread') and self.worker_thread.isRunning():
            self.worker_thread.quit()
            self.worker_thread.wait(1000)  # 等待最多1秒钟线程结束
            logger.info("SPI服务线程已停止")

        logger.info("SPI资源已清理")

    def read_register(self, address: str):
        """读取单个寄存器"""
        self.add_operation('read', address)

    def write_register(self, address: str, value: int):
        """写入单个寄存器"""
        self.add_operation('write', address, value)

    def batch_read_registers(self, addresses: list):
        """批量读取寄存器"""
        # 注意：当前实现是顺序添加读取操作到队列
        # 实际硬件交互可能需要更优化的批量指令
        logger.info(f"准备批量读取 {len(addresses)} 个寄存器")
        for address in addresses:
            self.add_operation('read', address)
        logger.info(f"已将 {len(addresses)} 个读取操作添加到队列")

    def batch_write_registers(self, operations: list):
        """批量写入寄存器

        Args:
            operations: 包含字典的列表，每个字典包含 'address' 和 'value'
                      例如: [{'address': '0x01', 'value': 0x1234}, ...]
        """
        # 注意：当前实现是顺序添加写入操作到队列
        # 实际硬件交互可能需要更优化的批量指令
        logger.info(f"准备批量写入 {len(operations)} 个寄存器")
        for op in operations:
            address = op.get('address')
            value = op.get('value')
            if address is not None and value is not None:
                self.add_operation('write', address, value)
            else:
                logger.warning(f"跳过无效的批量写入操作: {op}")
        logger.info(f"已将 {len(operations)} 个写入操作添加到队列")

    def refresh_available_ports(self):
        """刷新可用端口列表，并发出包含设备和描述的信号"""
        try:
            port_details = []
            for port in serial.tools.list_ports.comports():
                port_details.append({'device': port.device, 'description': port.description})
            self.ports_refreshed.emit(port_details)
            logger.info(f"SPI Service: Refreshed ports, found: {port_details}")
            return port_details
        except Exception as e:
            logger.error(f"SPI Service: Error refreshing ports: {str(e)}")
            self.ports_refreshed.emit([]) 
            return []

    def initialize(self):
        """初始化SPI设备

        Returns:
            bool: 初始化是否成功
        """
        try:
            with QMutexLocker(self.lock):
                # 初始化时不自动连接端口，只创建空的SPI设备引用
                # 这样避免了自动检测端口导致的状态不一致问题
                self._spi_device = None

                # 默认启用模拟模式，等待用户明确选择端口后再切换到硬件模式
                self._simulation_mode = True
                logger.info("SPI服务初始化完成，默认使用模拟模式")

                # 创建并启动工作线程
                from PyQt5.QtCore import QThread
                self.worker_thread = QThread()
                self.moveToThread(self.worker_thread)

                # 连接线程启动信号到process_operations方法
                self.worker_thread.started.connect(self.process_operations)

                # 启动线程
                self.worker_thread.start()
                logger.info("SPI服务线程已启动")

                return True
        except Exception as e:
            self.error_occurred.emit(f"硬件连接失败: {str(e)}")
            self._simulation_mode = True
            logger.info(f"SPI初始化失败，已启用模拟模式: {str(e)}")
            return True

    def add_operation(self, op_type, address, value=None):
        """添加操作到队列"""
        if self.operations.full():
            raise RuntimeError("操作队列已满")

        with QMutexLocker(self.lock):
            self.current_op_id += 1
            self.operations.put({
                'id': self.current_op_id,
                'type': op_type,
                'address': address,
                'value': value
            })
        # print(f'操作{self.current_op_id}添加到队列: {op_type} {address} {value}')  # 减少日志输出

    def clear_queue(self):
        """清空操作队列"""
        with QMutexLocker(self.lock):
            while not self.operations.empty():
                try:
                    self.operations.get_nowait()
                except Empty:
                    break
            logger.info("操作队列已清空")

    def clear_operation_queue(self):
        """清空操作队列 - 为了兼容性提供的别名方法"""
        self.clear_queue()

    def cancel_operation(self, op_id):
        """取消指定ID的操作"""
        with QMutexLocker(self.lock):
            # 创建新队列来存储保留的操作
            new_queue = Queue(maxsize=self.MAX_QUEUE_SIZE)

            # 从原队列中取出所有操作，保留非目标操作
            while not self.operations.empty():
                try:
                    op = self.operations.get_nowait()
                    if op.get('id') != op_id:
                        new_queue.put(op)
                except Empty:
                    break

            # 替换原队列
            self.operations = new_queue

    def check_spi_available(self, silent=False):
        """检查SPI接口是否可用

        Args:
            silent: 是否静默模式，不输出日志

        Returns:
            bool: 如果SPI接口可用返回True，否则返回False
        """
        try:
            with QMutexLocker(self.lock):
                # 检查SPI设备是否已初始化且串口已打开
                if self._spi_device and hasattr(self._spi_device, 'ser') and self._spi_device.ser and self._spi_device.ser.is_open:
                    if not silent:
                        logger.info("SPI接口已连接并可用")
                    return True

                # 如果没有SPI设备，在静默模式下直接返回False，避免自动创建设备
                if silent and not self._spi_device:
                    return False

                if not silent:
                    logger.info("SPI接口不可用，尝试重新初始化")
                # 尝试重新初始化SPI设备
                try:
                    self._spi_device = SPIManager()
                    if self._spi_device and hasattr(self._spi_device, 'ser') and self._spi_device.ser and self._spi_device.ser.is_open:
                        if not silent:
                            logger.info("成功重新初始化SPI接口")
                        return True
                    else:
                        if not silent:
                            logger.warning("无法初始化SPI接口")
                        return False
                except Exception as spi_init_error:
                    if not silent:
                        logger.error(f"SPI设备初始化失败: {str(spi_init_error)}")
                    return False
        except Exception as e:
            # 记录错误但不发送错误信号
            if not silent:
                logger.error(f"SPI设备检查失败: {str(e)}")
            return False
    
    def set_port(self, port_name: str) -> bool:
        """设置SPI使用的COM端口

        Args:
            port_name: 字符串，COM端口名称，如"COM1"

        Returns:
            bool: 设置成功返回True，否则返回False
        """
        try:
            with QMutexLocker(self.lock):
                # 防止重复设置相同端口导致的无限循环
                if hasattr(self, '_setting_port') and self._setting_port:
                    logger.warning(f"端口设置正在进行中，跳过重复调用: {port_name}")
                    return False

                # 检查是否已连接到同一端口
                if self._spi_device and hasattr(self._spi_device, 'ser') and self._spi_device.ser and \
                   self._spi_device.ser.is_open and getattr(self._spi_device, 'port_name', None) == port_name:
                    logger.info(f"已连接到端口 {port_name}，跳过重复设置")
                    return True

                # 设置标志，防止重复调用
                self._setting_port = True

                try:
                    # 如果当前有SPI设备连接，先关闭
                    if self._spi_device:
                        try:
                            self._spi_device.close()
                            logger.info("已关闭原有SPI连接")
                        except Exception as e:
                            logger.error(f"关闭原COM端口失败: {str(e)}")

                    # 重新初始化SPI设备，使用新的端口
                    try:
                        self._spi_device = SPIManager(port=port_name)
                        if self._spi_device and hasattr(self._spi_device, 'ser') and self._spi_device.ser and self._spi_device.ser.is_open:
                            logger.info(f"成功连接到SPI端口: {port_name}")
                            # 端口连接成功，禁用模拟模式
                            self._simulation_mode = False
                            self.connection_status_changed.emit(True)
                            return True
                        else:
                            logger.warning(f"无法连接到SPI端口: {port_name}")
                            # 端口连接失败，启用模拟模式
                            self._simulation_mode = True
                            self.connection_status_changed.emit(False)
                            return False
                    except Exception as e:
                        logger.error(f"连接SPI端口 {port_name} 失败: {str(e)}")
                        self._simulation_mode = True
                        self.connection_status_changed.emit(False)
                        return False
                finally:
                    # 清除标志
                    self._setting_port = False

        except Exception as e:
            logger.error(f"设置端口时发生错误: {str(e)}")
            self._simulation_mode = True
            self.connection_status_changed.emit(False)
            # 确保清除标志
            if hasattr(self, '_setting_port'):
                self._setting_port = False
            return False
    
    def _close_spi_device(self):
        """关闭SPI设备连接"""
        try:
            if self._spi_device is not None:
                # 实际代码应该替换为真实的SPI设备关闭操作
                # self._spi_device.close()
                self._spi_device = None
        except Exception as e:
            print(f"关闭SPI设备失败: {str(e)}")

    def port_selected(self):
        """检查是否已选择端口
        
        Returns:
            bool: 如果已选择端口返回True，否则返回False
        """
        try:
            with QMutexLocker(self.lock):
                if self._simulation_mode:
                    # 模拟模式下总是认为端口已选择
                    return True
                
                # 检查SPI设备是否有效且端口已打开
                if (self._spi_device and 
                    hasattr(self._spi_device, 'ser') and 
                    self._spi_device.ser and 
                    self._spi_device.ser.is_open):
                    return True
                
                return False
        except Exception:
            return False

    def process_operations(self):
        """处理SPI操作队列"""
        logger.info("SPI操作处理线程已启动")
        while self.active:
            if self.operations.empty():
                time.sleep(0.01)  # 减少CPU使用率
                continue
                
            try:
                current_op = self.operations.get(timeout=0.1)
            except Empty:
                continue
            except Exception as e:
                error_msg = f"获取操作失败: {str(e)}"
                logger.error(error_msg)
                self.error_occurred.emit(error_msg)
                continue

            try:
                op_id = current_op['id']
                op_type = current_op['type']
                address = current_op['address']
                value = current_op['value']

                logger.info(f'操作{op_id}开始: {op_type} {address} {value}')
                
                # 检查是否使用模拟模式
                if self._simulation_mode:
                    # 使用模拟模式处理操作
                    if op_type == 'read':
                        # 读取操作处理
                        simulated_value = self._get_simulated_value(address)
                        logger.info(f"模拟模式: 读取寄存器 {address}，值 0x{simulated_value:04X}")
                        # 只发送一个完成信号，避免重复回调
                        self.spi_operation_complete.emit(address, simulated_value, True)
                    elif op_type == 'write':
                        # 写入操作处理
                        if value is not None:
                            value_int = int(value) & 0xFFFF
                            self._simulated_registers[address] = value_int
                            logger.info(f"模拟模式: 写入寄存器 {address}，值 0x{value_int:04X}")
                            # 只发送一个完成信号，避免重复回调
                            self.spi_operation_complete.emit(address, value_int, False)
                        else:
                            error_msg = "模拟写入失败: 无效的值"
                            logger.error(error_msg)
                            self.error_occurred.emit(error_msg)
                    
                    # 进一步减少模拟延迟，提高批量操作速度
                    # time.sleep(0.005)  # 完全移除模拟延迟，最大化速度

                else:
                    # 实际硬件操作
                    # 检查SPI是否可用
                    logger.debug(f"检查SPI可用性，操作ID: {op_id}")
                    spi_available = self.check_spi_available()
                    
                    if not spi_available:
                        # 如果SPI不可用，尝试自动恢复或切换到模拟模式
                        try:
                            logger.info(f"SPI不可用，尝试重连，操作ID: {op_id}")
                            self._reconnect()
                            if not self.check_spi_available():
                                # 重连失败，自动切换到模拟模式
                                self._simulation_mode = True
                                error_msg = "SPI连接不可用，已自动切换到模拟模式"
                                logger.warning(error_msg)
                                self.error_occurred.emit(error_msg)
                                # 重新处理当前操作（递归调用）
                                self.operations.put(current_op)
                                continue
                        except Exception as conn_error:
                            error_msg = f"SPI连接失败: {str(conn_error)}"
                            logger.error(error_msg)
                            self.error_occurred.emit(error_msg)
                            self._simulation_mode = True
                            # 重新处理当前操作
                            self.operations.put(current_op)
                            continue
                    
                    if op_type == 'write':
                        reg_num = int(address, 16)
                        start_time = time.time()
                        success = False
                        logger.info(f"开始执行写操作，地址: {address}，值: {value}，操作ID: {op_id}")
                        
                        # 尝试写入，带超时处理
                        while time.time() - start_time < self.DEFAULT_TIMEOUT:
                            try:
                                success = self._spi_device.write_spi(
                                    register_address=reg_num,
                                    data=value,
                                )
                                if success:
                                    logger.info(f"写操作成功，地址: {address}，值: {value}，操作ID: {op_id}")
                                    break
                                logger.debug(f"写操作尝试失败，重试中，操作ID: {op_id}")
                                # time.sleep(0.02)  # 移除写操作重试延迟，最大化速度
                            except Exception as write_error:
                                logger.error(f"写操作异常: {str(write_error)}，操作ID: {op_id}")
                                # time.sleep(0.02)  # 移除写操作重试延迟，最大化速度
                        
                        if not success:
                            timeout_msg = f"写操作超时，地址: {address}，操作ID: {op_id}"
                            logger.error(timeout_msg)
                            self.operation_timeout.emit()
                            continue

                        # 无论是否在模拟模式下，都保存寄存器值以便下次读取时使用
                        self._simulated_registers[address] = int(value) & 0xFFFF

                        # 只发送一个完成信号，避免重复回调
                        self.spi_operation_complete.emit(address, value, False)
                        logger.info(f"操作{op_id}成功: 地址{address} 值0x{value:04X}")

                        # 进一步减少操作间隔延迟，提高批量操作速度
                        # time.sleep(0.001)  # 完全移除延迟，最大化速度

                    elif op_type == 'read':
                        reg_num = int(address, 16)
                        
                        # 先检查是否已有模拟值，有则优先使用
                        # if address in self._simulated_registers:
                        #     value = self._simulated_registers[address]
                        #     logger.info(f"从缓存中读取寄存器 {address}，值 0x{value:04X}")
                        #     self.spi_operation_complete.emit(address, value, True)
                        #     continue
                        
                        logger.info(f"开始执行读操作，地址: {address}，操作ID: {op_id}")
                        start_time = time.time()
                        success = False
                        received_data = None
                        
                        # 尝试读取，带超时处理
                        while time.time() - start_time < self.DEFAULT_TIMEOUT:
                            try:
                                success, received_data = self._spi_device.read_spi(
                                    register_address=reg_num,
                                )
                                if success:
                                    logger.info(f"读操作成功，地址: {address}，操作ID: {op_id}")
                                    break
                                logger.debug(f"读操作尝试失败，重试中，操作ID: {op_id}")
                                # time.sleep(0.01)  # 移除重试延迟，最大化速度
                            except Exception as read_error:
                                logger.error(f"读操作异常: {str(read_error)}，操作ID: {op_id}")
                                # time.sleep(0.01)  # 移除重试延迟，最大化速度

                        if not success:
                            timeout_msg = f"读操作超时，地址: {address}，操作ID: {op_id}"
                            logger.error(timeout_msg)
                            self.operation_timeout.emit()
                            continue

                        # 使用规范化的数据解析方法
                        try:
                            # 获取该寄存器配置的位长度（需要实现_get_bit_length方法）
                            logger.debug(f"操作{op_id}数据解析: {received_data} , type: {type(received_data)}， "
                                f"address type: {type(address)}")
                            value = received_data
                            
                            # 缓存读取到的值
                            self._simulated_registers[address] = value
                            
                        except Exception as e:
                            error_msg = f"操作{op_id}数据解析失败: {str(e)}"
                            logger.error(error_msg)
                            self.error_occurred.emit(error_msg)
                            continue

                        # 只发送一个完成信号，避免重复回调
                        self.spi_operation_complete.emit(address, value, True)  # int格式
                        logger.info(f"操作{op_id}成功: 地址{address} 读取值0x{value:04X}")

                        # 进一步减少操作间隔延迟，提高批量操作速度
                        # time.sleep(0.001)  # 完全移除延迟，最大化速度

            except Exception as e:
                error_msg = f"操作{op_id}失败: {str(e)}"
                self.error_occurred.emit(error_msg)
                print(error_msg)
                traceback.print_exc()
            # 移除 task_done() 调用，因为使用的是标准Queue而不是queue.Queue
                
    def _get_simulated_value(self, addr):
        """获取模拟寄存器的值
        
        Args:
            addr: 寄存器地址
            
        Returns:
            int: 模拟的寄存器值
        """
        # 如果已经有模拟值，则返回
        if addr in self._simulated_registers:
            return self._simulated_registers[addr]
            
        # 否则生成一个随机值
        random_value = random.randint(0, 0xFFFF)
        self._simulated_registers[addr] = random_value
        return random_value

    def _check_connection(self):
        """检查SPI连接状态"""
        with QMutexLocker(self.lock):
            return self._spi_device and self._spi_device.ser and self._spi_device.ser.is_open

    def _reconnect(self):
        """执行重连逻辑"""
        if self._spi_device:  # 添加检查
            self._spi_device.close()
        self._spi_device = SPIManager()
        # if not self._spi_device.initialize_spi():
        #     raise ConnectionError("SPI重连失败")

    def stop(self):
        """停止SPI操作处理"""
        with QMutexLocker(self.lock):
            self.active = False
            if self._spi_device:
                self._spi_device.close()
            # 清空操作队列，但保持队列对象的完整性
            while not self.operations.empty():
                try:
                    self.operations.get_nowait()
                except Empty:
                    break

    def _handle_simulated_operation(self, op_type, addr, value):
        """处理模拟模式下的操作
        
        Args:
            op_type: 操作类型，'read'或'write'
            addr: 寄存器地址
            value: 要写入的值（如果是写操作）
        """
        if op_type == 'read':
            # 模拟读取操作
            if addr in self._simulated_registers:
                # 使用之前写入的值
                simulated_value = self._simulated_registers[addr]
            else:
                # 没有之前的值，生成随机值
                simulated_value = random.randint(0, 0xFFFF)
                self._simulated_registers[addr] = simulated_value
            
            # 进一步减少模拟延迟，提高批量操作速度
            # time.sleep(0.002)  # 完全移除模拟延迟，最大化速度
            
            # 只发送一个完成信号，避免重复回调
            self.spi_operation_complete.emit(addr, simulated_value, True)
        
        elif op_type == 'write':
            # 模拟写入操作
            if value is not None:
                # 确保值有效
                value = int(value) & 0xFFFF
                
                # 保存到模拟寄存器
                self._simulated_registers[addr] = value
                
                # 进一步减少模拟延迟，提高批量操作速度
                # time.sleep(0.002)  # 完全移除模拟延迟，最大化速度
                
                # 只发送一个完成信号，避免重复回调
                self.spi_operation_complete.emit(addr, value, False)
            else:
                raise ValueError("写入操作需要有效的值")

    def _handle_real_operation(self, op_type, addr, value):
        """处理实际SPI操作
        
        Args:
            op_type: 操作类型，'read'或'write'
            addr: 寄存器地址
            value: 要写入的值（如果是写操作）
        """
        # 转换地址为整数
        try:
            addr_int = int(addr, 16) if isinstance(addr, str) else int(addr)
            logger.info(f"实际操作: {op_type} 地址 {addr_int}, 值 = 0x{value:04X}")
        except ValueError:
            raise ValueError(f"无效的寄存器地址: {addr}")
        
        if op_type == 'read':
            # 实际的SPI读取操作代码（示例）
            # 实际项目中应该替换为真正的SPI读取代码
            # read_value = self._spi_device.read_register(addr_int)
            
            # 模拟读取，实际项目中应该删除
            read_value = random.randint(0, 0xFFFF)
            
            # 只发送一个完成信号，避免重复回调
            self.spi_operation_complete.emit(addr, read_value, True)
            
        elif op_type == 'write':
            if value is not None:
                # 确保值有效
                value_int = int(value) & 0xFFFF

                # 实际的SPI写入操作代码（示例）
                # 实际项目中应该替换为真正的SPI写入代码
                # self._spi_device.write_register(addr_int, value_int)

                # 只发送一个完成信号，避免重复回调
                self.spi_operation_complete.emit(addr, value_int, False)
            else:
                raise ValueError("写入操作需要有效的值")

    def run(self):
        """工作线程主循环"""
        while self.active:
            if not self.process_operations():
                # 减少空闲等待时间，提高响应速度
                time.sleep(0.001)  # 从10ms减少到1ms
        
        # 工作线程结束前关闭SPI设备
        self._close_spi_device()

# 修改测试代码部分
if __name__ == '__main__':
    from PyQt5.QtCore import QCoreApplication, QThread
    
    def handle_operation_complete(addr, value, is_read):
        op_type = "读取" if is_read else "写入"
        print(f"操作完成: {op_type} 地址 {addr}, 值 = 0x{value:04X}")

    def handle_error(error_msg):
        print(f"错误: {error_msg}")

    def handle_timeout(timeout_msg):
        print(f"超时: {timeout_msg}")

    # 创建Qt应用程序实例
    app = QCoreApplication([])
    
    # 创建工作线程
    thread = QThread()
    
    # 创建SPIServiceImpl实例
    worker = SPIServiceImpl()
    worker.moveToThread(thread)
    
    # 连接信号
    worker.operation_complete.connect(handle_operation_complete)
    worker.error_occurred.connect(handle_error)
    worker.operation_timeout.connect(handle_timeout)
    
    # 连接线程启动信号到process_operations
    thread.started.connect(worker.process_operations)
    
    # 初始化SPI
    if not worker.initialize():
        print("SPI初始化失败")
        sys.exit(1)
    
    # 启动线程
    thread.start()
    
    # 添加测试操作
    try:
        # 测试读操作
        print("\n测试读操作:")
        worker.add_operation('read', '0x00')  # 读取地址0的值
        
        # 测试写操作
        print("\n测试写操作:")
        worker.add_operation('write', '0x00', 0x2600)  # 写入地址0的值
        
        # 再次读取以验证写入
        print("\n验证写入:")
        worker.add_operation('read', '0x00')
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        worker.stop()
        thread.quit()
        thread.wait()
        sys.exit(1)
    
    # 添加清理代码
    def cleanup():
        print("正在清理...")
        worker.stop()
        thread.quit()
        thread.wait()
        app.quit()
    
    # 设置定时器在5秒后清理并退出
    from PyQt5.QtCore import QTimer
    timer = QTimer()
    timer.singleShot(5000, cleanup)
    
    # 运行事件循环
    sys.exit(app.exec_())