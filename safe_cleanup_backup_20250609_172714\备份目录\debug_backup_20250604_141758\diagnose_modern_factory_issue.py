#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
诊断现代化工厂初始化问题
找出导致"现代化工厂不可用"警告的具体原因
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def diagnose_modern_factory_issue():
    """诊断现代化工厂初始化问题"""
    print("=" * 60)
    print("🔍 诊断现代化工厂初始化问题")
    print("=" * 60)
    
    # 1. 测试ModernToolWindowFactory导入
    print("\n1. 测试ModernToolWindowFactory导入...")
    try:
        from ui.factories.ModernToolWindowFactory import ModernToolWindowFactory
        print("   ✓ ModernToolWindowFactory导入成功")
    except Exception as e:
        print(f"   ❌ ModernToolWindowFactory导入失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    # 2. 测试创建模拟主窗口
    print("\n2. 创建模拟主窗口...")
    try:
        # 创建模拟的RegisterManager
        from core.services.register.RegisterManager import RegisterManager
        import json
        
        # 加载寄存器配置
        config_path = os.path.join('lib', 'register.json')
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                registers_config = json.load(f)
        else:
            # 创建最小的模拟配置
            registers_config = {
                "0x00": {"name": "Test Register", "bits": []},
                "0x01": {"name": "Test Register 2", "bits": []}
            }
        
        register_manager = RegisterManager(registers_config)
        
        # 创建模拟主窗口
        class MockMainWindow:
            def __init__(self):
                self.register_manager = register_manager
                self.auto_write_mode = False
        
        mock_main_window = MockMainWindow()
        print("   ✓ 模拟主窗口创建成功")
        
    except Exception as e:
        print(f"   ❌ 模拟主窗口创建失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    # 3. 测试ModernToolWindowFactory初始化
    print("\n3. 测试ModernToolWindowFactory初始化...")
    try:
        modern_factory = ModernToolWindowFactory(mock_main_window)
        print("   ✓ ModernToolWindowFactory初始化成功")
    except Exception as e:
        print(f"   ❌ ModernToolWindowFactory初始化失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    # 4. 测试ToolWindowFactory初始化
    print("\n4. 测试ToolWindowFactory初始化...")
    try:
        from ui.factories.ToolWindowFactory import ToolWindowFactory
        tool_factory = ToolWindowFactory(mock_main_window)
        print("   ✓ ToolWindowFactory初始化成功")
        
        # 检查现代化工厂状态
        if tool_factory.use_modern_factory:
            print("   ✓ 现代化工厂已启用")
        else:
            print("   ❌ 现代化工厂未启用")
            return False
            
        if tool_factory.modern_factory:
            print("   ✓ 现代化工厂实例存在")
        else:
            print("   ❌ 现代化工厂实例不存在")
            return False
            
    except Exception as e:
        print(f"   ❌ ToolWindowFactory初始化失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    # 5. 测试迁移配置文件
    print("\n5. 检查迁移配置文件...")
    try:
        config_file = os.path.join('config', 'migration.json')
        if os.path.exists(config_file):
            print(f"   ✓ 迁移配置文件存在: {config_file}")
            
            import json
            with open(config_file, 'r', encoding='utf-8') as f:
                migration_config = json.load(f)
            
            print(f"   ✓ 迁移配置加载成功，包含 {len(migration_config.get('handlers', {}))} 个处理器配置")
        else:
            print(f"   ⚠️  迁移配置文件不存在: {config_file}")
            print("   这不是错误，将使用默认配置")
            
    except Exception as e:
        print(f"   ❌ 迁移配置文件检查失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # 6. 测试现代化处理器导入
    print("\n6. 测试现代化处理器导入...")
    modern_handlers = [
        'ui.handlers.ModernSetModesHandler.ModernSetModesHandler',
        'ui.handlers.ModernClkinControlHandler.ModernClkinControlHandler',
        'ui.handlers.ModernPLLHandler.ModernPLLHandler',
        'ui.handlers.ModernSyncSysRefHandler.ModernSyncSysRefHandler',
        'ui.handlers.ModernClkOutputsHandler.ModernClkOutputsHandler'
    ]
    
    failed_imports = []
    
    for handler_path in modern_handlers:
        try:
            module_path, class_name = handler_path.rsplit('.', 1)
            import importlib
            module = importlib.import_module(module_path)
            handler_class = getattr(module, class_name)
            print(f"   ✓ {handler_path}: 导入成功")
        except Exception as e:
            print(f"   ❌ {handler_path}: 导入失败 - {str(e)}")
            failed_imports.append(handler_path)
    
    if failed_imports:
        print(f"\n   ❌ 有 {len(failed_imports)} 个现代化处理器导入失败")
        return False
    else:
        print(f"\n   ✅ 所有 {len(modern_handlers)} 个现代化处理器导入成功")
    
    # 7. 测试窗口创建
    print("\n7. 测试窗口创建...")
    try:
        # 测试创建一个简单的窗口
        window = tool_factory.modern_factory.create_window_by_type('set_modes')
        if window:
            print("   ✓ 现代化窗口创建成功")
            # 清理
            if hasattr(window, 'close'):
                window.close()
        else:
            print("   ❌ 现代化窗口创建返回None")
            return False
            
    except Exception as e:
        print(f"   ❌ 现代化窗口创建失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    # 8. 总结
    print("\n" + "=" * 60)
    print("📊 诊断结果总结")
    print("=" * 60)
    print("✅ 现代化工厂初始化完全正常")
    print("✅ 所有现代化处理器都可以正常导入")
    print("✅ 窗口创建功能正常")
    print("\n🤔 如果在实际运行中仍然出现'现代化工厂不可用'警告，")
    print("   可能的原因包括：")
    print("   1. 主窗口的register_manager属性未正确初始化")
    print("   2. 某些依赖模块在实际运行环境中不可用")
    print("   3. 权限或路径问题")
    print("   4. 运行时的异常导致初始化失败")
    
    return True

if __name__ == "__main__":
    success = diagnose_modern_factory_issue()
    sys.exit(0 if success else 1)
