#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
版本服务
负责管理应用程序版本信息，提供版本号查询功能
"""

import os
import json
from pathlib import Path
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class VersionService:
    """版本服务类"""
    
    _instance = None
    
    def __init__(self, version_file='version.json'):
        """初始化版本服务
        
        Args:
            version_file: 版本配置文件路径
        """
        self.version_file = Path(version_file)
        self.version_data = self._load_version()
    
    @classmethod
    def instance(cls):
        """获取单例实例"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def _load_version(self):
        """加载版本信息"""
        # 尝试从多个可能的位置加载版本文件
        # 优先使用packaging目录下的版本文件
        possible_paths = [
            self.version_file,
            Path('packaging/config/version.json'),  # 主版本文件（开发环境）
            Path('_internal/packaging/config/version.json'),  # 打包后环境
            Path('packaging/version.json'),         # 备用版本文件
            Path('version.json'),                   # 根目录版本文件（兼容性）
            Path('../packaging/config/version.json'),
            Path('../packaging/version.json'),
            Path('../version.json'),
            Path('../../version.json'),
        ]
        
        for path in possible_paths:
            if path.exists():
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        logger.info(f"成功加载版本文件: {path}")
                        return data
                except Exception as e:
                    logger.warning(f"无法加载版本文件 {path}: {e}")
        
        # 如果找不到版本文件，返回默认版本信息
        logger.warning("未找到版本文件，使用默认版本信息")
        return self._get_default_version()
    
    def _get_default_version(self):
        """获取默认版本信息"""
        return {
            "version": {"major": 1, "minor": 0, "patch": 0, "build": 0},
            "app_info": {
                "name": "FSJ04832 寄存器配置工具",
                "description": "用于配置和管理FSJ04832寄存器的专业工具",
                "company": "FSJ Technology",
                "copyright": "© 2024 FSJ Technology",
                "author": "开发团队"
            },
            "build_info": {
                "last_build_date": "",
                "build_type": "release",
                "target_platform": "windows"
            }
        }
    
    def get_version_string(self):
        """获取完整版本字符串
        
        Returns:
            str: 版本字符串，格式为 "主版本.次版本.补丁版本.构建号"
        """
        try:
            v = self.version_data['version']
            return f"{v['major']}.{v['minor']}.{v['patch']}.{v['build']}"
        except (KeyError, TypeError):
            logger.error("版本数据格式错误，使用默认版本")
            return "*******"
    
    def get_short_version_string(self):
        """获取短版本字符串（不包含构建号）
        
        Returns:
            str: 短版本字符串，格式为 "主版本.次版本.补丁版本"
        """
        try:
            v = self.version_data['version']
            return f"{v['major']}.{v['minor']}.{v['patch']}"
        except (KeyError, TypeError):
            logger.error("版本数据格式错误，使用默认版本")
            return "1.0.0"
    
    def get_app_name(self):
        """获取应用程序名称
        
        Returns:
            str: 应用程序名称
        """
        try:
            return self.version_data['app_info']['name']
        except (KeyError, TypeError):
            logger.error("应用信息格式错误，使用默认名称")
            return "FSJ04832 寄存器配置工具"
    
    def get_app_title_with_version(self):
        """获取带版本号的应用程序标题

        Returns:
            str: 带版本号的标题，格式为 "应用名称 v完整版本号"
        """
        app_name = self.get_app_name()
        version = self.get_version_string()
        return f"{app_name} v{version}"
    
    def get_full_app_title_with_version(self):
        """获取带完整版本号的应用程序标题
        
        Returns:
            str: 带完整版本号的标题，格式为 "应用名称 v完整版本号"
        """
        app_name = self.get_app_name()
        version = self.get_version_string()
        return f"{app_name} v{version}"
    
    def get_app_description(self):
        """获取应用程序描述
        
        Returns:
            str: 应用程序描述
        """
        try:
            return self.version_data['app_info']['description']
        except (KeyError, TypeError):
            return "用于配置和管理FSJ04832寄存器的专业工具"
    
    def get_company(self):
        """获取公司名称
        
        Returns:
            str: 公司名称
        """
        try:
            return self.version_data['app_info']['company']
        except (KeyError, TypeError):
            return "FSJ Technology"
    
    def get_copyright(self):
        """获取版权信息
        
        Returns:
            str: 版权信息
        """
        try:
            return self.version_data['app_info']['copyright']
        except (KeyError, TypeError):
            return "© 2024 FSJ Technology"
    
    def get_build_date(self):
        """获取构建日期
        
        Returns:
            str: 构建日期
        """
        try:
            return self.version_data['build_info']['last_build_date']
        except (KeyError, TypeError):
            return ""
    
    def get_build_type(self):
        """获取构建类型

        Returns:
            str: 构建类型
        """
        try:
            return self.version_data['build_info']['build_type']
        except (KeyError, TypeError):
            return "release"

    def get_exe_name(self):
        """获取可执行文件名称

        Returns:
            str: 可执行文件名称，格式为 "FSJConfigTool主版本.次版本.补丁版本.构建号"
        """
        try:
            v = self.version_data['version']
            return f"FSJConfigTool{v['major']}.{v['minor']}.{v['patch']}.{v['build']}"
        except (KeyError, TypeError):
            logger.error("版本数据格式错误，使用默认可执行文件名")
            return "FSJConfigTool*******"
    
    def get_all_version_info(self):
        """获取所有版本信息
        
        Returns:
            dict: 包含所有版本信息的字典
        """
        return {
            'version_string': self.get_version_string(),
            'short_version': self.get_short_version_string(),
            'app_name': self.get_app_name(),
            'app_title': self.get_app_title_with_version(),
            'full_app_title': self.get_full_app_title_with_version(),
            'description': self.get_app_description(),
            'company': self.get_company(),
            'copyright': self.get_copyright(),
            'build_date': self.get_build_date(),
            'build_type': self.get_build_type()
        }
    
    def reload_version(self):
        """重新加载版本信息"""
        self.version_data = self._load_version()
        logger.info("版本信息已重新加载")
    
    def is_development_build(self):
        """判断是否为开发构建
        
        Returns:
            bool: 如果构建号大于0，则认为是开发构建
        """
        try:
            build_num = self.version_data['version']['build']
            return build_num > 0
        except (KeyError, TypeError):
            return False
