#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试构建脚本
用于验证构建环境和spec文件
"""

import os
import sys
from pathlib import Path

def test_build_environment():
    """测试构建环境"""
    print("=" * 60)
    print("FSJ04832 构建环境测试")
    print("=" * 60)
    
    # 检查当前目录
    current_dir = Path.cwd()
    print(f"当前目录: {current_dir}")
    
    # 检查项目根目录
    if current_dir.name == 'packaging':
        project_root = current_dir.parent
    else:
        project_root = current_dir
    
    print(f"项目根目录: {project_root}")
    
    # 检查关键文件
    files_to_check = [
        'main.py',
        'version.json',
        'packaging/scripts/build.spec',
        'packaging/scripts/build_exe.py',
        'images/logo.ico'
    ]
    
    print("\n检查关键文件:")
    all_files_exist = True
    for file_path in files_to_check:
        full_path = project_root / file_path
        exists = full_path.exists()
        status = "✓" if exists else "✗"
        print(f"  {status} {file_path}")
        if not exists:
            all_files_exist = False
    
    # 检查Python模块
    print("\n检查Python模块:")
    modules_to_check = [
        'PyQt5',
        'serial',
        'json',
        'pathlib'
    ]
    
    all_modules_available = True
    for module in modules_to_check:
        try:
            __import__(module)
            print(f"  ✓ {module}")
        except ImportError:
            print(f"  ✗ {module}")
            all_modules_available = False
    
    # 检查PyInstaller
    print("\n检查PyInstaller:")
    try:
        import PyInstaller
        print(f"  ✓ PyInstaller 版本: {PyInstaller.__version__}")
        pyinstaller_available = True
    except ImportError:
        print("  ✗ PyInstaller 未安装")
        pyinstaller_available = False
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果:")
    print(f"  文件检查: {'通过' if all_files_exist else '失败'}")
    print(f"  模块检查: {'通过' if all_modules_available else '失败'}")
    print(f"  PyInstaller: {'可用' if pyinstaller_available else '不可用'}")
    
    if all_files_exist and all_modules_available and pyinstaller_available:
        print("\n✓ 构建环境检查通过，可以进行构建")
        return True
    else:
        print("\n✗ 构建环境检查失败，请解决上述问题")
        return False

def test_spec_file():
    """测试spec文件"""
    print("\n" + "=" * 60)
    print("Spec文件测试")
    print("=" * 60)
    
    # 找到spec文件
    current_dir = Path.cwd()
    if current_dir.name == 'packaging':
        spec_file = current_dir / 'scripts' / 'build.spec'
    else:
        spec_file = current_dir / 'packaging' / 'scripts' / 'build.spec'
    
    print(f"Spec文件路径: {spec_file}")
    
    if not spec_file.exists():
        print("✗ Spec文件不存在")
        return False
    
    try:
        with open(spec_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("✓ Spec文件可读")
        
        # 检查关键内容
        checks = [
            ('main.py', 'main.py' in content),
            ('Analysis', 'Analysis(' in content),
            ('EXE', 'EXE(' in content),
            ('hiddenimports', 'hiddenimports' in content),
            ('icon', 'icon=' in content)
        ]
        
        print("\nSpec文件内容检查:")
        all_checks_pass = True
        for name, check in checks:
            status = "✓" if check else "✗"
            print(f"  {status} {name}")
            if not check:
                all_checks_pass = False
        
        return all_checks_pass
        
    except Exception as e:
        print(f"✗ 读取Spec文件失败: {e}")
        return False

if __name__ == "__main__":
    print("开始构建环境测试...")
    
    env_ok = test_build_environment()
    spec_ok = test_spec_file()
    
    print("\n" + "=" * 60)
    if env_ok and spec_ok:
        print("✓ 所有测试通过，构建环境就绪")
        sys.exit(0)
    else:
        print("✗ 测试失败，请检查并修复问题")
        sys.exit(1)
