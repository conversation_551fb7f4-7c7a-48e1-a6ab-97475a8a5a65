#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试现代化UI事件处理器
验证事件处理、按钮操作、模式切换等功能
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_modern_ui_event():
    """测试现代化UI事件处理器"""
    try:
        print("=" * 60)
        print("测试现代化UI事件处理器")
        print("=" * 60)
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 导入必要的模块
        from ui.handlers.ModernUIEventHandler import ModernUIEventHandler
        
        print("1. 创建现代化UI事件处理器...")
        try:
            handler = ModernUIEventHandler.create_for_testing()
            print("   ✓ 现代化UI事件处理器创建成功")
            print(f"   窗口标题: {handler.windowTitle()}")
            
            # 检查事件配置
            if hasattr(handler, 'event_config'):
                config = handler.event_config
                print(f"   ✓ 事件配置初始化成功")
                print(f"     批量操作保护: {config['enable_batch_protection']}")
                print(f"     值验证: {config['enable_value_validation']}")
                print(f"     状态消息: {config['enable_status_messages']}")
                print(f"     自动刷新: {config['auto_refresh_view']}")
                print(f"     模拟模式: {config['simulation_mode']}")
                print(f"     预加载: {config['preload_enabled']}")
                print(f"     当前语言: {config['current_language']}")
            else:
                print("   ❌ 事件配置未初始化")
                return False
                
        except Exception as e:
            print(f"   ❌ 创建处理器失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
        print("2. 测试事件统计功能...")
        try:
            stats = handler.event_stats
            print(f"   初始事件统计:")
            print(f"     读取请求: {stats['read_requests']}")
            print(f"     写入请求: {stats['write_requests']}")
            print(f"     值变化: {stats['value_changes']}")
            print(f"     模式切换: {stats['mode_toggles']}")
            print(f"     错误数: {stats['errors']}")
            print("   ✓ 事件统计功能正常")
            
        except Exception as e:
            print(f"   ❌ 事件统计测试失败: {str(e)}")
        
        print("3. 测试按钮事件处理...")
        try:
            # 测试读取按钮点击
            print("   测试读取按钮点击...")
            initial_read_count = handler.event_stats['read_requests']
            handler.handle_read_button_click()
            new_read_count = handler.event_stats['read_requests']
            
            if new_read_count > initial_read_count:
                print(f"   ✓ 读取按钮点击处理成功 ({initial_read_count} -> {new_read_count})")
            else:
                print("   ❌ 读取按钮点击统计未更新")
            
            # 测试写入按钮点击
            print("   测试写入按钮点击...")
            initial_write_count = handler.event_stats['write_requests']
            handler.handle_write_button_click()
            new_write_count = handler.event_stats['write_requests']
            
            if new_write_count > initial_write_count:
                print(f"   ✓ 写入按钮点击处理成功 ({initial_write_count} -> {new_write_count})")
            else:
                print("   ❌ 写入按钮点击统计未更新")
            
        except Exception as e:
            print(f"   ❌ 按钮事件处理测试失败: {str(e)}")
        
        print("4. 测试寄存器操作...")
        try:
            # 测试读取请求
            print("   测试寄存器读取请求...")
            handler.handle_read_requested(0x02)
            print("   ✓ 寄存器读取请求处理成功")
            
            # 测试写入请求
            print("   测试寄存器写入请求...")
            handler.handle_write_requested(0x02, 0x1234)
            print("   ✓ 寄存器写入请求处理成功")
            
            # 测试IO写入请求
            print("   测试IO写入请求...")
            handler.handle_io_write_request(0x02, 0x5678)
            print("   ✓ IO写入请求处理成功")
            
        except Exception as e:
            print(f"   ❌ 寄存器操作测试失败: {str(e)}")
        
        print("5. 测试值变化处理...")
        try:
            # 测试值变化事件
            print("   测试值变化事件...")
            initial_change_count = handler.event_stats['value_changes']
            handler.on_register_value_changed("test_widget", "0x02", 0x9ABC)
            new_change_count = handler.event_stats['value_changes']
            
            if new_change_count > initial_change_count:
                print(f"   ✓ 值变化事件处理成功 ({initial_change_count} -> {new_change_count})")
            else:
                print("   ❌ 值变化事件统计未更新")
            
            # 测试输入值变化
            print("   测试输入值变化...")
            handler.handle_value_changed("0x02", 0xDEF0)
            print("   ✓ 输入值变化处理成功")
            
            # 测试位段选中
            print("   测试位段选中...")
            handler.handle_bit_field_selected("0x02")
            print("   ✓ 位段选中处理成功")
            
        except Exception as e:
            print(f"   ❌ 值变化处理测试失败: {str(e)}")
        
        print("6. 测试模式切换...")
        try:
            # 测试模拟模式切换
            print("   测试模拟模式切换...")
            initial_mode = handler.event_config['simulation_mode']
            initial_toggle_count = handler.event_stats['mode_toggles']
            
            handler.toggle_simulation_mode(not initial_mode)
            
            new_mode = handler.event_config['simulation_mode']
            new_toggle_count = handler.event_stats['mode_toggles']
            
            if new_mode != initial_mode and new_toggle_count > initial_toggle_count:
                print(f"   ✓ 模拟模式切换成功 ({initial_mode} -> {new_mode})")
            else:
                print("   ❌ 模拟模式切换失败")
            
            # 测试预加载切换
            print("   测试预加载切换...")
            initial_preload = handler.event_config['preload_enabled']
            handler.toggle_preload(not initial_preload)
            new_preload = handler.event_config['preload_enabled']
            
            if new_preload != initial_preload:
                print(f"   ✓ 预加载切换成功 ({initial_preload} -> {new_preload})")
            else:
                print("   ❌ 预加载切换失败")
            
        except Exception as e:
            print(f"   ❌ 模式切换测试失败: {str(e)}")
        
        print("7. 测试辅助方法...")
        try:
            # 测试地址标准化
            test_addresses = [0x02, "0x02", "02", "2"]
            print("   测试地址标准化...")
            for addr in test_addresses:
                normalized = handler._normalize_register_address(addr)
                print(f"     {addr} -> {normalized}")
            print("   ✓ 地址标准化测试成功")
            
            # 测试值验证
            print("   测试值验证...")
            valid_values = [0x0000, 0x1234, 0xFFFF]
            invalid_values = [-1, 0x10000, 0x20000]
            
            for value in valid_values:
                result = handler._validate_register_value(value)
                print(f"     0x{value:04X}: {'有效' if result else '无效'}")
            
            for value in invalid_values:
                result = handler._validate_register_value(value)
                print(f"     {value}: {'有效' if result else '无效'}")
            
            print("   ✓ 值验证测试成功")
            
        except Exception as e:
            print(f"   ❌ 辅助方法测试失败: {str(e)}")
        
        print("8. 测试UI功能...")
        try:
            # 测试高级设置（不会实际显示对话框，只测试方法调用）
            print("   测试高级设置...")
            handler.show_advanced_settings()
            print("   ✓ 高级设置调用成功")
            
            # 测试关于对话框
            print("   测试关于对话框...")
            handler.show_about_dialog()
            print("   ✓ 关于对话框调用成功")
            
            # 测试语言切换
            print("   测试语言切换...")
            class MockAction:
                def data(self):
                    return "en_US"
            
            mock_action = MockAction()
            handler.handle_language_change(mock_action)
            
            if handler.event_config['current_language'] == "en_US":
                print("   ✓ 语言切换成功")
            else:
                print("   ❌ 语言切换失败")
            
        except Exception as e:
            print(f"   ❌ UI功能测试失败: {str(e)}")
        
        print("9. 测试状态获取...")
        try:
            status = handler.get_current_status()
            print(f"   当前状态:")
            print(f"     模拟模式: {status.get('simulation_mode', 'N/A')}")
            print(f"     预加载: {status.get('preload_enabled', 'N/A')}")
            print(f"     当前语言: {status.get('current_language', 'N/A')}")
            
            event_stats = status.get('event_stats', {})
            print(f"   事件统计:")
            print(f"     读取请求: {event_stats.get('read_requests', 0)}")
            print(f"     写入请求: {event_stats.get('write_requests', 0)}")
            print(f"     值变化: {event_stats.get('value_changes', 0)}")
            print(f"     模式切换: {event_stats.get('mode_toggles', 0)}")
            print(f"     错误数: {event_stats.get('errors', 0)}")
            
            print("   ✓ 状态获取成功")
            
        except Exception as e:
            print(f"   ❌ 状态获取测试失败: {str(e)}")
        
        print("10. 测试信号连接...")
        try:
            # 检查信号是否正确定义
            signals = [
                'read_button_clicked', 
                'write_button_clicked', 
                'simulation_mode_toggled',
                'preload_toggled',
                'language_changed'
            ]
            
            for signal_name in signals:
                if hasattr(handler, signal_name):
                    signal = getattr(handler, signal_name)
                    print(f"   ✓ 信号 {signal_name} 已定义: {type(signal)}")
                else:
                    print(f"   ❌ 信号 {signal_name} 未定义")
            
        except Exception as e:
            print(f"   ❌ 信号连接测试失败: {str(e)}")
        
        print("11. 生成测试报告...")
        try:
            final_status = handler.get_current_status()
            final_stats = final_status.get('event_stats', {})
            
            print(f"   最终测试报告:")
            print(f"     处理器类型: {type(handler).__name__}")
            print(f"     窗口标题: {handler.windowTitle()}")
            print(f"     模拟模式: {final_status.get('simulation_mode', False)}")
            print(f"     预加载启用: {final_status.get('preload_enabled', False)}")
            print(f"     当前语言: {final_status.get('current_language', 'N/A')}")
            print(f"     总读取请求: {final_stats.get('read_requests', 0)}")
            print(f"     总写入请求: {final_stats.get('write_requests', 0)}")
            print(f"     总值变化: {final_stats.get('value_changes', 0)}")
            print(f"     总模式切换: {final_stats.get('mode_toggles', 0)}")
            print(f"     总错误数: {final_stats.get('errors', 0)}")
            
        except Exception as e:
            print(f"   ❌ 生成测试报告时出错: {str(e)}")
        
        # 显示窗口（可选）
        # handler.show()
        
        print("\n" + "=" * 60)
        print("🎉 现代化UI事件处理器测试完成！")
        print("=" * 60)
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_modern_ui_event()
    sys.exit(0 if success else 1)
