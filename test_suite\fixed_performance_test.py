#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
FSJ04832寄存器配置工具 - 修复后的性能测试
解决了"地址找不到"错误的性能测试版本
"""

import sys
import os
import time
import json
import unittest
from unittest.mock import Mock, patch
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from PyQt5.QtTest import QTest

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from test_config import TestConfig
from test_utils import TestUtils

class FixedPerformanceTest(unittest.TestCase):
    """修复后的性能测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        cls.app = TestUtils.get_test_app()
        cls.test_config = TestConfig()
        
    def setUp(self):
        """每个测试方法前的初始化"""
        pass
        
    def tearDown(self):
        """每个测试方法后的清理"""
        pass
        
    def get_valid_register_addresses(self):
        """获取有效的寄存器地址列表"""
        # 基于实际register.json文件中存在的地址
        valid_addresses = [
            # 实际存在的基础配置寄存器
            "0x00", "0x02", "0x03", "0x04", "0x05", "0x06", "0x0C",

            # 实际存在的PLL配置寄存器
            "0x10", "0x11", "0x12", "0x13", "0x14", "0x15", "0x16", "0x17",
            "0x18", "0x19", "0x1A", "0x1B", "0x1C", "0x1D", "0x1E", "0x1F",
            "0x20", "0x21", "0x22", "0x23", "0x24", "0x25", "0x26", "0x27",
            "0x28", "0x29", "0x2A", "0x2B", "0x2C", "0x2D", "0x2E", "0x2F",

            # 实际存在的时钟输出配置寄存器
            "0x30", "0x31", "0x32", "0x33", "0x34", "0x35", "0x36", "0x37",
            "0x38", "0x39", "0x3A", "0x3B", "0x3C", "0x3D", "0x3E", "0x3F",
            "0x40", "0x41", "0x42", "0x43", "0x44", "0x45", "0x46", "0x47",
            "0x48", "0x49", "0x4A", "0x4C", "0x4E", "0x4F",

            # 实际存在的同步和参考配置寄存器
            "0x50", "0x51", "0x52", "0x53", "0x54", "0x55", "0x56", "0x57",
            "0x58", "0x59", "0x5A", "0x5B", "0x5C", "0x5D", "0x5E", "0x5F",
            "0x60", "0x61", "0x63", "0x65", "0x67", "0x69", "0x6B", "0x6C", "0x6F",

            # 实际存在的高级功能寄存器
            "0x70", "0x72", "0x76", "0x77", "0x79", "0x7A", "0x7E"
        ]

        return valid_addresses
        
    def test_01_application_startup_time(self):
        """测试应用程序启动时间"""
        print("\n=== 测试应用程序启动时间 ===")
        
        startup_times = []
        
        for i in range(3):  # 测试3次取平均值
            start_time = time.time()
            
            try:
                from ui.windows.RegisterMainWindow import RegisterMainWindow
                
                # 创建主窗口
                main_window = RegisterMainWindow()
                
                end_time = time.time()
                startup_time = end_time - start_time
                startup_times.append(startup_time)
                
                print(f"   第{i+1}次启动时间: {startup_time:.3f}秒")
                
                # 关闭窗口
                main_window.close()
                QTest.qWait(100)
                
            except Exception as e:
                print(f"   第{i+1}次启动失败: {str(e)}")
                startup_times.append(5.0)  # 失败时记录较大值
                
        avg_startup_time = sum(startup_times) / len(startup_times)
        
        print(f"✅ 平均启动时间: {avg_startup_time:.3f}秒")
        
        # 验证启动时间在合理范围内（5秒内）
        self.assertLess(avg_startup_time, 5.0, "启动时间过长")
        
    def test_02_event_bus_performance_fixed(self):
        """测试事件总线性能（修复版）"""
        print("\n=== 测试事件总线性能（修复版） ===")
        
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            
            event_bus = RegisterUpdateBus.instance()
            
            # 获取有效的寄存器地址
            valid_addresses = self.get_valid_register_addresses()
            signal_count = 100  # 减少信号数量，使用有效地址
            received_count = 0
            
            def signal_handler(addr, value):
                nonlocal received_count
                received_count += 1
                
            event_bus.register_updated.connect(signal_handler)
            
            # 使用有效地址发送信号
            start_time = time.time()
            
            for i in range(signal_count):
                addr = valid_addresses[i % len(valid_addresses)]
                event_bus.emit_register_updated(addr, i % 0xFFFF)
                
            # 等待信号处理完成
            QTest.qWait(200)
            
            end_time = time.time()
            duration = end_time - start_time
            
            signals_per_second = signal_count / duration if duration > 0 else 0
            
            print(f"   发送信号数: {signal_count}")
            print(f"   接收信号数: {received_count}")
            print(f"   使用地址数: {len(valid_addresses)}")
            print(f"   处理时间: {duration:.3f}秒")
            print(f"   处理速度: {signals_per_second:.0f}信号/秒")
            
            # 验证性能指标
            self.assertEqual(received_count, signal_count, "信号丢失")
            self.assertGreater(signals_per_second, 100, "事件总线性能不足")
            
            print("✅ 事件总线性能测试通过")
            
        except Exception as e:
            print(f"❌ 事件总线性能测试失败: {str(e)}")
            raise
            
    def test_03_register_manager_safe_operations(self):
        """测试寄存器管理器安全操作"""
        print("\n=== 测试寄存器管理器安全操作 ===")
        
        try:
            from core.services.register.RegisterManager import RegisterManager
            
            # 创建测试寄存器配置
            test_registers = {}
            valid_addresses = self.get_valid_register_addresses()[:10]  # 使用前10个地址
            
            for addr in valid_addresses:
                test_registers[addr] = {
                    "name": f"TEST_REG_{addr}",
                    "value": 0x0000,
                    "bits": {}
                }
            
            register_manager = RegisterManager(test_registers)
            
            # 测试有效地址操作
            success_count = 0
            for addr in valid_addresses:
                try:
                    # 设置值
                    register_manager.set_register_value(addr, 0x1234)
                    # 获取值
                    value = register_manager.get_register_value(addr)
                    if value == 0x1234:
                        success_count += 1
                except Exception as e:
                    print(f"   地址 {addr} 操作失败: {str(e)}")
                    
            print(f"   成功操作地址数: {success_count}/{len(valid_addresses)}")
            
            # 测试无效地址的错误处理
            invalid_addresses = ["0xFF", "0xFE", "0xFD"]
            error_count = 0
            
            for addr in invalid_addresses:
                try:
                    register_manager.get_register_value(addr)
                except ValueError as e:
                    if "未知的寄存器地址" in str(e):
                        error_count += 1
                        
            print(f"   正确处理无效地址数: {error_count}/{len(invalid_addresses)}")
            
            # 验证操作成功率
            success_rate = success_count / len(valid_addresses)
            self.assertGreaterEqual(success_rate, 0.8, "寄存器操作成功率过低")
            
            print("✅ 寄存器管理器安全操作测试通过")
            
        except Exception as e:
            print(f"❌ 寄存器管理器安全操作测试失败: {str(e)}")
            raise
            
    def test_04_memory_usage_monitoring(self):
        """测试内存使用监控"""
        print("\n=== 测试内存使用监控 ===")
        
        try:
            import psutil
            process = psutil.Process()
            
            # 记录初始内存
            initial_memory = process.memory_info().rss / 1024 / 1024
            
            # 执行一些操作
            valid_addresses = self.get_valid_register_addresses()
            
            # 创建多个事件总线实例（测试单例模式）
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            
            bus_instances = []
            for i in range(10):
                bus = RegisterUpdateBus.instance()
                bus_instances.append(bus)
                
            # 验证单例模式
            all_same = all(bus is bus_instances[0] for bus in bus_instances)
            self.assertTrue(all_same, "事件总线单例模式失效")
            
            # 发送一些信号
            for i in range(50):
                addr = valid_addresses[i % len(valid_addresses)]
                bus_instances[0].emit_register_updated(addr, i)
                
            QTest.qWait(100)
            
            # 记录操作后内存
            final_memory = process.memory_info().rss / 1024 / 1024
            memory_increase = final_memory - initial_memory
            
            print(f"   初始内存: {initial_memory:.1f} MB")
            print(f"   最终内存: {final_memory:.1f} MB")
            print(f"   内存增长: {memory_increase:.1f} MB")
            print(f"   事件总线实例数: {len(bus_instances)} (应该都是同一个)")
            
            # 验证内存使用合理
            self.assertLess(memory_increase, 50.0, "内存增长过多")
            
            print("✅ 内存使用监控测试通过")
            
        except Exception as e:
            print(f"❌ 内存使用监控测试失败: {str(e)}")
            raise
            
    def test_05_ui_response_time(self):
        """测试UI响应时间"""
        print("\n=== 测试UI响应时间 ===")
        
        try:
            from PyQt5.QtWidgets import QPushButton, QLineEdit
            
            # 创建测试控件
            button = QPushButton("测试按钮")
            line_edit = QLineEdit()
            
            # 测试按钮点击响应时间
            click_times = []
            for i in range(10):
                start_time = time.time()
                QTest.mouseClick(button, 1)  # 左键点击
                end_time = time.time()
                click_times.append(end_time - start_time)
                
            avg_click_time = sum(click_times) / len(click_times)
            
            # 测试文本输入响应时间
            input_times = []
            test_text = "测试文本输入"
            for i in range(10):
                line_edit.clear()
                start_time = time.time()
                line_edit.setText(test_text)
                end_time = time.time()
                input_times.append(end_time - start_time)
                
            avg_input_time = sum(input_times) / len(input_times)
            
            print(f"   平均按钮点击响应时间: {avg_click_time*1000:.2f}毫秒")
            print(f"   平均文本输入响应时间: {avg_input_time*1000:.2f}毫秒")
            
            # 验证响应时间在合理范围内（100毫秒内）
            self.assertLess(avg_click_time, 0.1, "按钮响应时间过长")
            self.assertLess(avg_input_time, 0.1, "文本输入响应时间过长")
            
            print("✅ UI响应时间测试通过")
            
        except Exception as e:
            print(f"❌ UI响应时间测试失败: {str(e)}")
            raise
            
    def test_06_configuration_loading_performance(self):
        """测试配置加载性能"""
        print("\n=== 测试配置加载性能 ===")
        
        try:
            # 测试配置文件加载时间
            config_files = [
                "config/default.json",
                "config/register.json"
            ]
            
            loading_times = []
            
            for config_file in config_files:
                if os.path.exists(config_file):
                    start_time = time.time()
                    
                    try:
                        with open(config_file, 'r', encoding='utf-8') as f:
                            config = json.load(f)
                            
                        end_time = time.time()
                        loading_time = end_time - start_time
                        loading_times.append(loading_time)
                        
                        print(f"   {config_file}: {loading_time*1000:.2f}毫秒 ({len(config)}项)")
                        
                    except Exception as e:
                        print(f"   {config_file}: 加载失败 - {str(e)}")
                        
            if loading_times:
                avg_loading_time = sum(loading_times) / len(loading_times)
                print(f"   平均配置加载时间: {avg_loading_time*1000:.2f}毫秒")
                
                # 验证加载时间合理（1秒内）
                self.assertLess(avg_loading_time, 1.0, "配置加载时间过长")
                
            print("✅ 配置加载性能测试通过")
            
        except Exception as e:
            print(f"❌ 配置加载性能测试失败: {str(e)}")
            raise

def run_fixed_performance_tests():
    """运行修复后的性能测试"""
    print("⚡ 开始FSJ04832寄存器配置工具修复后的性能测试")
    print("=" * 60)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(FixedPerformanceTest)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果摘要
    print("\n" + "=" * 60)
    print("📊 修复后性能测试结果摘要:")
    print(f"   总测试数: {result.testsRun}")
    print(f"   成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"   失败: {len(result.failures)}")
    print(f"   错误: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"   - {test}")
            
    if result.errors:
        print("\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"   - {test}")
            
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\n🎯 测试成功率: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("🎉 性能测试修复成功！")
    elif success_rate >= 70:
        print("✅ 性能测试大幅改善")
    else:
        print("⚠️  性能测试仍需进一步优化")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_fixed_performance_tests()
    sys.exit(0 if success else 1)
