# 寄存器配置工具重构详细计划

## 目录
- [一、当前架构状态](#一当前架构状态)
- [二、重构目标](#二重构目标)
- [三、重构阶段](#三重构阶段)
- [四、详细任务清单](#四详细任务清单)
- [五、代码规范](#五代码规范)
- [六、测试与质量保证](#六测试与质量保证)
- [七、时间线](#七时间线)

## 一、当前架构状态

### 现有架构
目前项目已开始采用分层架构重构，但处于过渡阶段：

- **核心层（core）**
  - 服务层（services）初步实现
  - 仓库层（repositories）初步实现
  - 模型层（models）初步实现
  - 事件总线（event_bus）初步实现

- **界面层（ui）**
  - 窗口组件（windows）
  - 事件处理器（handlers）
  - 表单组件（forms）

### 存在问题
1. `RegisterMainWindow.py` 代码过于庞大（2200+行）
2. SPI服务接口设计不够清晰
3. 存在大量遗留代码
4. 服务抽象不够完善
5. 数据模型缺乏统一定义
6. 组件间通信方式不统一

## 二、重构目标

1. 实现清晰的分层架构
2. 降低组件间耦合度
3. 提高代码可维护性
4. 实现插件化架构
5. 规范化异常处理
6. 统一事件通信机制
7. 提高代码复用率

## 三、重构阶段

### 第一阶段：基础架构完善（2周）
- 完善服务接口设计
- 规范化数据模型
- 建立统一的事件总线

### 第二阶段：代码拆分与重构（3周）
- 拆分 `RegisterMainWindow`
- 重构服务实现
- 规范化仓库层实现

### 第三阶段：插件系统实现（2周）
- 设计插件接口
- 实现插件加载机制
- 将工具窗口转为插件

### 第四阶段：遗留代码迁移（2周）
- 逐步迁移遗留模块
- 淘汰旧的实现方式

### 第五阶段：测试与优化（1周）
- 编写单元测试
- 进行性能测试
- 修复发现的问题

## 四、详细任务清单

### 第一阶段：基础架构完善

#### 1.1 服务接口设计
- [ ] 重构 `ISPIService` 接口，明确职责边界
```python
# core/services/spi/spi_interface.py
from abc import ABC, abstractmethod
from PyQt5.QtCore import QObject, pyqtSignal

class ISPIService(QObject, ABC):
    """SPI服务接口，定义与硬件通信的核心方法"""
    # 信号定义
    spi_operation_complete = pyqtSignal(str, int, bool)  # (address, value, is_read)
    spi_error_occurred = pyqtSignal(str)
    operation_timeout = pyqtSignal()
    
    @abstractmethod
    def initialize(self) -> bool:
        """初始化SPI服务"""
        pass
    
    @abstractmethod
    def read_register(self, address: str) -> None:
        """读取寄存器，结果通过信号返回"""
        pass
    
    @abstractmethod
    def write_register(self, address: str, value: int) -> None:
        """写入寄存器，结果通过信号返回"""
        pass
    
    @abstractmethod
    def check_spi_available(self, silent: bool = False) -> bool:
        """检查SPI接口是否可用"""
        pass
    
    @abstractmethod
    def cleanup(self) -> None:
        """清理资源"""
        pass
```

- [ ] 设计寄存器服务接口
```python
# core/services/register/register_interface.py
from abc import ABC, abstractmethod
from PyQt5.QtCore import QObject, pyqtSignal
from typing import Dict, List, Optional
from core.models.register import Register, BitField

class IRegisterService(QObject, ABC):
    """寄存器服务接口，处理寄存器业务逻辑"""
    register_updated = pyqtSignal(str, int)  # (address, value)
    
    @abstractmethod
    def get_register(self, address: str) -> Optional[Register]:
        """获取寄存器对象"""
        pass
    
    @abstractmethod
    def get_bit_fields(self, address: str) -> List[BitField]:
        """获取寄存器位域列表"""
        pass
    
    @abstractmethod
    def update_register_value(self, address: str, value: int) -> bool:
        """更新寄存器值"""
        pass
    
    @abstractmethod
    def load_registers_from_file(self, file_path: str) -> bool:
        """从文件加载寄存器配置"""
        pass
    
    @abstractmethod
    def save_registers_to_file(self, file_path: str) -> bool:
        """保存寄存器配置到文件"""
        pass
```

#### 1.2 数据模型规范化
- [ ] 实现 `Register` 数据类
```python
# core/models/register.py
from dataclasses import dataclass
from typing import List, Dict, Optional

@dataclass
class BitField:
    """寄存器位域模型"""
    name: str
    start_bit: int
    end_bit: int
    description: str = ""
    read_only: bool = False
    
    @property
    def width(self) -> int:
        """位域宽度"""
        return self.end_bit - self.start_bit + 1
    
    def get_value(self, register_value: int) -> int:
        """从寄存器值中提取位域值"""
        mask = ((1 << self.width) - 1) << self.start_bit
        return (register_value & mask) >> self.start_bit
    
    def set_value(self, register_value: int, field_value: int) -> int:
        """设置位域值并返回新的寄存器值"""
        # 清除原位域值
        mask = ((1 << self.width) - 1) << self.start_bit
        cleared = register_value & ~mask
        # 设置新位域值
        field_value_aligned = (field_value & ((1 << self.width) - 1)) << self.start_bit
        return cleared | field_value_aligned

@dataclass
class Register:
    """寄存器模型"""
    address: str
    name: str
    value: int = 0
    description: str = ""
    bit_fields: List[BitField] = None
    
    def __post_init__(self):
        if self.bit_fields is None:
            self.bit_fields = []
    
    def get_bit_field_value(self, field_name: str) -> Optional[int]:
        """获取指定位域的值"""
        for field in self.bit_fields:
            if field.name == field_name:
                return field.get_value(self.value)
        return None
    
    def set_bit_field_value(self, field_name: str, field_value: int) -> bool:
        """设置指定位域的值"""
        for field in self.bit_fields:
            if field.name == field_name:
                if field.read_only:
                    return False
                self.value = field.set_value(self.value, field_value)
                return True
        return False
```

#### 1.3 统一事件总线
- [ ] 实现全局事件总线
```python
# core/event_bus/event_bus.py
from PyQt5.QtCore import QObject, pyqtSignal
import functools

class EventBus(QObject):
    """全局事件总线，用于组件间通信"""
    # 寄存器相关事件
    register_updated = pyqtSignal(str, int)  # (address, value)
    register_error = pyqtSignal(str, str)  # (address, error_message)
    
    # 连接相关事件
    connection_changed = pyqtSignal(bool)  # (is_connected)
    simulation_mode_changed = pyqtSignal(bool)  # (is_simulation)
    
    # 操作相关事件
    operation_started = pyqtSignal(str)  # (operation_type)
    operation_completed = pyqtSignal(str)  # (operation_type)
    operation_error = pyqtSignal(str, str)  # (operation_type, error_message)
    
    # 插件相关事件
    plugin_loaded = pyqtSignal(str)  # (plugin_name)
    plugin_unloaded = pyqtSignal(str)  # (plugin_name)
    
    # 单例实现
    _instance = None
    
    @classmethod
    def instance(cls):
        """获取事件总线单例"""
        if cls._instance is None:
            cls._instance = EventBus()
        return cls._instance
```

### 第二阶段：代码拆分与重构

#### 2.1 拆分 RegisterMainWindow
- [ ] 提取 IO 功能到独立类
- [ ] 提取寄存器树功能到独立类
- [ ] 提取寄存器表格功能到独立类
- [ ] 精简主窗口代码

#### 2.2 重构服务实现
- [ ] 重构 SPIServiceImpl
- [ ] 实现寄存器服务 RegisterServiceImpl
- [ ] 实现配置服务 ConfigServiceImpl

#### 2.3 规范化仓库层
- [ ] 重构 RegisterRepository
- [ ] 实现通用的配置仓库 ConfigRepository

### 第三阶段：插件系统实现

#### 3.1 设计插件接口
```python
# plugins/plugin_interface.py
from abc import ABC, abstractmethod
from PyQt5.QtWidgets import QWidget

class IPlugin(ABC):
    """插件接口"""
    @property
    @abstractmethod
    def name(self) -> str:
        """插件名称"""
        pass
    
    @property
    @abstractmethod
    def description(self) -> str:
        """插件描述"""
        pass
    
    @property
    @abstractmethod
    def version(self) -> str:
        """插件版本"""
        pass
    
    @abstractmethod
    def initialize(self) -> bool:
        """初始化插件"""
        pass
    
    @abstractmethod
    def create_widget(self) -> QWidget:
        """创建插件界面"""
        pass
    
    @abstractmethod
    def cleanup(self) -> None:
        """清理插件资源"""
        pass
```

#### 3.2 实现插件加载器
```python
# plugins/plugin_loader.py
import os
import importlib.util
import inspect
from typing import Dict, List
from .plugin_interface import IPlugin

class PluginLoader:
    """插件加载器"""
    def __init__(self, plugin_dir: str = "plugins"):
        self.plugin_dir = plugin_dir
        self.plugins: Dict[str, IPlugin] = {}
    
    def discover_plugins(self) -> List[str]:
        """发现可用插件"""
        plugin_files = []
        for file in os.listdir(self.plugin_dir):
            if file.endswith(".py") and not file.startswith("__"):
                plugin_files.append(file[:-3])  # 去除 .py 后缀
        return plugin_files
    
    def load_plugin(self, plugin_name: str) -> bool:
        """加载指定插件"""
        if plugin_name in self.plugins:
            return True  # 已加载
        
        try:
            # 构建插件模块路径
            plugin_path = os.path.join(self.plugin_dir, f"{plugin_name}.py")
            
            # 动态加载模块
            spec = importlib.util.spec_from_file_location(plugin_name, plugin_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # 查找插件类
            for item_name, item in inspect.getmembers(module):
                if (inspect.isclass(item) and 
                    issubclass(item, IPlugin) and 
                    item.__module__ == module.__name__):
                    # 创建插件实例
                    plugin = item()
                    # 初始化插件
                    if plugin.initialize():
                        self.plugins[plugin_name] = plugin
                        return True
            
            return False
        except Exception as e:
            print(f"加载插件 {plugin_name} 失败: {str(e)}")
            return False
    
    def unload_plugin(self, plugin_name: str) -> bool:
        """卸载指定插件"""
        if plugin_name not in self.plugins:
            return True  # 已卸载
        
        try:
            # 清理插件资源
            self.plugins[plugin_name].cleanup()
            # 移除插件
            del self.plugins[plugin_name]
            return True
        except Exception as e:
            print(f"卸载插件 {plugin_name} 失败: {str(e)}")
            return False
    
    def get_plugin(self, plugin_name: str) -> IPlugin:
        """获取插件实例"""
        return self.plugins.get(plugin_name)
```

#### 3.3 将工具窗口转为插件
- [ ] 转换时钟输出窗口为插件
- [ ] 转换PLL控制窗口为插件
- [ ] 转换其他工具窗口为插件

### 第四阶段：遗留代码迁移

#### 4.1 迁移核心功能
- [ ] 迁移 RegisterManager 到新架构
- [ ] 迁移 RegisterModel 到新架构
- [ ] 迁移 spiPrivacy 到新架构
- [ ] 处理全局依赖和引用

#### 4.2 优化依赖注入
```python
# core/di/service_container.py
from typing import Dict, Type, TypeVar, Generic, Optional, Any

T = TypeVar('T')

class ServiceContainer:
    """服务容器，用于依赖注入"""
    _instance = None
    
    @classmethod
    def instance(cls):
        """获取服务容器单例"""
        if cls._instance is None:
            cls._instance = ServiceContainer()
        return cls._instance
    
    def __init__(self):
        self._services: Dict[Type, Any] = {}
    
    def register(self, interface_type: Type[T], implementation: T) -> None:
        """注册服务实现"""
        self._services[interface_type] = implementation
    
    def resolve(self, interface_type: Type[T]) -> Optional[T]:
        """解析服务实现"""
        return self._services.get(interface_type)
```

#### 4.3 实现新的 main.py
```python
# main.py
import sys
from PyQt5.QtWidgets import QApplication
from ui.windows.main_window import MainWindow
from core.services.spi.spi_service_impl import SPIServiceImpl
from core.services.register.register_service_impl import RegisterServiceImpl
from core.repositories.register_repository import RegisterRepository
from core.di.service_container import ServiceContainer
from core.services.spi.spi_interface import ISPIService
from core.services.register.register_interface import IRegisterService

def main():
    """应用程序入口"""
    # 创建应用程序
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # 设置统一的Fusion风格
    
    # 配置服务容器
    container = ServiceContainer.instance()
    
    # 注册服务实现
    spi_service = SPIServiceImpl()
    container.register(ISPIService, spi_service)
    
    # 初始化SPI服务
    if spi_service.initialize():
        print(f"SPI服务初始化成功，当前模式：{'模拟模式' if spi_service.simulation_mode else '硬件模式'}")
    
    # 创建仓库
    register_repo = RegisterRepository(spi_service)
    
    # 注册寄存器服务
    register_service = RegisterServiceImpl(register_repo)
    container.register(IRegisterService, register_service)
    
    # 创建并显示主窗口
    main_window = MainWindow()
    main_window.show()
    
    # 启动应用程序主循环
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
```

## 五、代码规范

### 5.1 命名规范
- 类名：大驼峰命名（例如：`RegisterService`）
- 方法名：小驼峰命名（例如：`readRegister`）
- 变量名：下划线命名（例如：`register_value`）
- 常量名：全大写下划线命名（例如：`MAX_RETRIES`）
- 私有成员：下划线前缀（例如：`_cache`）

### 5.2 文档规范
- 所有公共方法必须有文档注释
- 复杂逻辑必须有行内注释
- 文档注释使用 Google 风格

### 5.3 代码组织
- 每个文件只定义一个主要类
- 相关功能放在同一模块
- 接口与实现分离
- 测试代码放在 tests 目录

## 六、测试与质量保证

### 6.1 单元测试
- [ ] 为核心服务编写单元测试
- [ ] 为数据模型编写单元测试
- [ ] 为工具类编写单元测试

### 6.2 集成测试
- [ ] 测试服务与仓库集成
- [ ] 测试UI与服务集成
- [ ] 测试插件加载机制

### 6.3 性能测试
- [ ] 测试大量寄存器操作性能
- [ ] 测试UI响应性能
- [ ] 测试内存占用

## 七、时间线

### 第一阶段（2周）
- 第1周：完成服务接口设计和数据模型规范化
- 第2周：完成事件总线实现和初步测试

### 第二阶段（3周）
- 第3周：拆分 RegisterMainWindow
- 第4周：重构服务实现
- 第5周：规范化仓库层实现

### 第三阶段（2周）
- 第6周：设计并实现插件系统
- 第7周：将工具窗口转为插件

### 第四阶段（2周）
- 第8周：迁移核心功能
- 第9周：优化依赖注入和实现新的入口

### 第五阶段（1周）
- 第10周：测试与问题修复
