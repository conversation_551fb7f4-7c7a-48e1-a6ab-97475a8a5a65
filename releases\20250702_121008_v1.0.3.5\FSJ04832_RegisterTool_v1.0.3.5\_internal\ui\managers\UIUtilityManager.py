# -*- coding: utf-8 -*-

"""
UI工具管理器
负责管理各种UI工具功能，如用户手册显示、寄存器转储等
"""

import os
import sys
import traceback
from PyQt5.QtWidgets import (QMessageBox, QTableWidget, QTableWidgetItem, 
                           QHeaderView)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class UIUtilityManager:
    """UI工具管理器类
    
    职责：
    1. 管理用户手册显示
    2. 处理寄存器转储功能
    3. 管理关于对话框
    4. 处理其他UI工具功能
    """
    
    def __init__(self, main_window):
        """初始化UI工具管理器
        
        Args:
            main_window: 主窗口实例
        """
        self.main_window = main_window
        
    def show_user_manual(self):
        """显示用户手册"""
        try:
            # 检查用户手册文件是否存在
            manual_path = self._get_manual_path()
            
            if os.path.exists(manual_path):
                self._open_manual_file(manual_path)
            else:
                self._show_manual_not_found_dialog()
                
        except Exception as e:
            logger.error(f"显示用户手册时出错: {str(e)}\n{traceback.format_exc()}")
            self._show_manual_error_dialog(str(e))
            
    def show_register_dump(self):
        """显示所有寄存器值（批量转储）"""
        try:
            # 如果窗口已存在且可见，先关闭
            if hasattr(self.main_window, 'reg_table') and self.main_window.reg_table.isVisible():
                self.main_window.reg_table.close()
            
            # 获取寄存器数据
            register_values = self._get_register_values()
            
            if not register_values:
                QMessageBox.information(self.main_window, "提示", "没有可显示的寄存器数据")
                return
            
            # 创建并显示转储表格
            self._create_dump_table(register_values)
            
        except Exception as e:
            logger.error(f"显示寄存器转储时出错: {str(e)}\n{traceback.format_exc()}")
            QMessageBox.critical(
                self.main_window, 
                "错误", 
                f"显示寄存器转储时出错: {str(e)}"
            )
            
    def show_about_dialog(self):
        """显示关于对话框"""
        try:
            if hasattr(self.main_window, 'tool_window_manager'):
                self.main_window.tool_window_manager.show_about_dialog()
            else:
                # 后备方案
                self._show_simple_about_dialog()
        except Exception as e:
            logger.error(f"显示关于对话框时出错: {str(e)}")
            
    def _get_manual_path(self):
        """获取用户手册路径
        
        Returns:
            str: 用户手册文件路径
        """
        if hasattr(self.main_window, 'lifecycle_manager'):
            return self.main_window.lifecycle_manager.get_resource_path('docs/user_manual.pdf')
        else:
            # 后备方案
            return os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'docs', 'user_manual.pdf')
            
    def _open_manual_file(self, manual_path):
        """打开用户手册文件
        
        Args:
            manual_path: 用户手册文件路径
        """
        try:
            if sys.platform == 'win32':
                os.startfile(manual_path)  # Windows特有方法
            elif sys.platform == 'darwin':
                import subprocess
                subprocess.call(['open', manual_path])  # macOS
            else:
                import subprocess
                subprocess.call(['xdg-open', manual_path])  # Linux
                
        except Exception as e:
            self._show_manual_open_error(manual_path, str(e))
            
    def _show_manual_not_found_dialog(self):
        """显示用户手册未找到对话框"""
        QMessageBox.information(
            self.main_window, 
            "用户手册", 
            "用户手册文件不存在。\n\n"
            "请联系技术支持获取最新的用户手册。",
            QMessageBox.Ok
        )
        
    def _show_manual_open_error(self, manual_path, error_msg):
        """显示用户手册打开错误对话框
        
        Args:
            manual_path: 用户手册路径
            error_msg: 错误消息
        """
        if hasattr(self.main_window, 'display_manager'):
            self.main_window.display_manager.show_status_message(f"打开用户手册失败: {error_msg}", 5000)
        
        self._safe_show_message(
            "打开失败",
            f"无法打开用户手册: {error_msg}\n\n"
            f"请确保已安装PDF阅读器，并且文件路径正确: {manual_path}",
            QMessageBox.Warning
        )
        
    def _show_manual_error_dialog(self, error_msg):
        """显示用户手册通用错误对话框
        
        Args:
            error_msg: 错误消息
        """
        self._safe_show_message(
            "错误",
            f"显示用户手册时出错: {error_msg}",
            QMessageBox.Critical
        )
        
    def _get_register_values(self):
        """获取寄存器值
        
        Returns:
            dict: 寄存器地址到值的映射
        """
        try:
            if not hasattr(self.main_window, 'register_manager'):
                return {}
                
            register_manager = self.main_window.register_manager
            
            # 尝试使用专门的方法获取所有寄存器值
            if hasattr(register_manager, 'get_all_register_values'):
                return register_manager.get_all_register_values()
            
            # 后备方案：从寄存器对象中提取值
            if hasattr(register_manager, 'register_objects'):
                return {
                    addr: reg.get("current_value", 0) 
                    for addr, reg in register_manager.register_objects.items()
                }
                
            return {}
            
        except Exception as e:
            logger.error(f"获取寄存器值时出错: {str(e)}")
            return {}
            
    def _create_dump_table(self, register_values):
        """创建寄存器转储表格
        
        Args:
            register_values: 寄存器值字典
        """
        reg_count = len(register_values)
        
        # 计算表格行列
        col_count = min(int(reg_count ** 0.5) + 1, 8)
        row_count = (reg_count + col_count - 1) // col_count
        
        # 创建表格
        self.main_window.reg_table = QTableWidget()
        self._setup_dump_table(self.main_window.reg_table, row_count, col_count)
        
        # 填充数据
        self._populate_dump_table(self.main_window.reg_table, register_values, col_count)
        
        # 优化窗口尺寸并显示
        self._optimize_table_size(self.main_window.reg_table, col_count, row_count)
        self.main_window.reg_table.show()
        
    def _setup_dump_table(self, table, row_count, col_count):
        """设置转储表格的基本属性

        Args:
            table: 表格对象
            row_count: 行数
            col_count: 列数
        """
        table.setWindowTitle("Register Dump - Full Window Layout")

        # 设置图标
        try:
            icon_path = self._get_icon_path()
            if os.path.exists(icon_path):
                table.setWindowIcon(QIcon(icon_path))
        except Exception as e:
            logger.warning(f"设置表格图标失败: {e}")

        table.setRowCount(row_count)
        table.setColumnCount(col_count)
        table.setEditTriggers(QTableWidget.NoEditTriggers)

        # 使用拉伸模式，让表格铺满整个窗口
        table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

        table.verticalHeader().setVisible(False)
        table.horizontalHeader().setVisible(False)
        table.setAlternatingRowColors(True)
        table.setVerticalScrollMode(QTableWidget.ScrollPerPixel)
        table.setHorizontalScrollMode(QTableWidget.ScrollPerPixel)

        # 设置表格选择行为
        table.setSelectionBehavior(QTableWidget.SelectItems)
        table.setSelectionMode(QTableWidget.SingleSelection)

        # 去除表格边框和边距，让内容铺满窗口
        table.setContentsMargins(0, 0, 0, 0)
        table.setFrameStyle(0)  # 去除边框
        table.setLineWidth(0)

        # 设置表格样式，去除边距和间隙
        table.setStyleSheet("""
            QTableWidget {
                border: none;
                margin: 0px;
                padding: 0px;
                gridline-color: #E0E0E0;
                background-color: white;
            }
            QTableWidget::item {
                border: 1px solid #E0E0E0;
                padding: 4px;
                margin: 0px;
            }
            QTableWidget::item:selected {
                background-color: #3399FF;
                color: white;
            }
            QTableWidget::item:alternate {
                background-color: #F5F5F5;
            }
        """)
        
    def _populate_dump_table(self, table, register_values, col_count):
        """填充转储表格数据
        
        Args:
            table: 表格对象
            register_values: 寄存器值字典
            col_count: 列数
        """
        items_to_set = []
        
        for index, (addr, value) in enumerate(register_values.items()):
            row = index // col_count
            col = index % col_count
            
            # 转换地址格式
            reg_num = int(addr, 16) if isinstance(addr, str) else addr
            
            # 创建表格项
            item = QTableWidgetItem(f"R{reg_num} = 0x{value:04X}")
            item.setTextAlignment(Qt.AlignCenter)
            items_to_set.append((row, col, item))
        
        # 批量设置表格项
        for row, col, item in items_to_set:
            table.setItem(row, col, item)
            
    def _optimize_table_size(self, table, col_count, row_count):
        """优化表格窗口尺寸，根据实际内容动态调整

        Args:
            table: 表格对象
            col_count: 列数
            row_count: 行数
        """
        try:
            # 等待表格内容完全加载
            from PyQt5.QtCore import QTimer
            QTimer.singleShot(50, lambda: self._calculate_dynamic_table_size(table, col_count, row_count))
        except Exception as e:
            logger.warning(f"动态计算表格大小失败，使用默认大小: {e}")
            # 回退到默认大小
            width = max(600, min(col_count * 120, 1200))
            height = max(400, min(row_count * 40, 800))
            table.resize(width, height)

    def _calculate_dynamic_table_size(self, table, col_count, row_count):
        """动态计算表格大小，铺满整个窗口

        Args:
            table: 表格对象
            col_count: 列数
            row_count: 行数
        """
        try:
            # 获取屏幕信息
            from PyQt5.QtWidgets import QApplication
            screen = QApplication.primaryScreen()
            screen_geometry = screen.availableGeometry()
            max_width = int(screen_geometry.width() * 0.9)  # 增加到90%屏幕宽度
            max_height = int(screen_geometry.height() * 0.9)  # 增加到90%屏幕高度

            # 计算基础内容尺寸（不包含边距）
            # 由于使用Stretch模式，列宽会自动调整，这里计算最小需要的尺寸
            min_col_width = 120  # 每列最小宽度
            min_content_width = col_count * min_col_width

            # 计算实际内容高度（去除边距）
            row_height = table.rowHeight(0) if row_count > 0 else 30
            content_height = row_count * row_height + 30  # 只保留标题栏高度

            # 优化窗口尺寸：让表格内容铺满窗口
            # 宽度：确保能容纳所有列，但不超过屏幕限制
            final_width = min(max(min_content_width, 600), max_width)

            # 高度：根据行数计算，但不超过屏幕限制
            final_height = min(max(content_height, 400), max_height)

            # 应用新尺寸
            table.resize(final_width, final_height)

            # 居中显示
            self._center_table_window(table, screen_geometry)

            logger.info(f"铺满窗口的dump表格大小: {final_width}x{final_height} (列数: {col_count}, 行数: {row_count})")

        except Exception as e:
            logger.error(f"动态计算表格大小失败: {e}")

    def _center_table_window(self, table, screen_geometry):
        """将表格窗口居中显示

        Args:
            table: 表格对象
            screen_geometry: 屏幕几何信息
        """
        try:
            table_geometry = table.geometry()
            x = (screen_geometry.width() - table_geometry.width()) // 2
            y = (screen_geometry.height() - table_geometry.height()) // 2
            table.move(x, y)
        except Exception as e:
            logger.warning(f"居中表格窗口失败: {e}")
        
    def _get_icon_path(self):
        """获取图标路径
        
        Returns:
            str: 图标文件路径
        """
        if hasattr(self.main_window, 'lifecycle_manager'):
            return self.main_window.lifecycle_manager.get_resource_path('images/logo.ico')
        else:
            # 后备方案
            return os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'images', 'logo.ico')
            
    def _show_simple_about_dialog(self):
        """显示简单的关于对话框（后备方案）"""
        try:
            # 尝试获取版本信息
            from core.services.version.VersionService import VersionService
            version_service = VersionService.instance()

            app_name = version_service.get_app_name()
            version = version_service.get_version_string()
            company = version_service.get_company()

            about_text = f"{app_name}\n\n版本: {version}\n开发团队: {company}"

        except Exception:
            # 如果版本服务不可用，使用默认信息
            about_text = "FSJ04832 寄存器配置工具\n\n版本: 1.0.0\n开发团队: FSJ Technology"

        self._safe_show_message("关于", about_text, QMessageBox.Information)

    def _safe_show_message(self, title, message, icon_type=None):
        """安全地显示消息框，避免与主窗口事件过滤器冲突

        Args:
            title: 消息框标题
            message: 消息内容
            icon_type: 图标类型 (QMessageBox.Information, Warning, Critical等)
        """
        try:
            # 临时禁用主窗口的事件过滤器
            main_window_filter_disabled = False
            if hasattr(self.main_window, 'eventFilter'):
                try:
                    self.main_window.removeEventFilter(self.main_window)
                    main_window_filter_disabled = True
                    logger.debug("临时禁用主窗口事件过滤器")
                except Exception as e:
                    logger.debug(f"无法禁用主窗口事件过滤器: {str(e)}")

            # 创建独立的消息框，不使用主窗口作为父窗口
            msg = QMessageBox()
            msg.setWindowTitle(title)
            msg.setText(message)

            # 设置图标
            if icon_type:
                msg.setIcon(icon_type)
            else:
                msg.setIcon(QMessageBox.Information)

            # 设置按钮
            msg.setStandardButtons(QMessageBox.Ok)
            msg.setDefaultButton(QMessageBox.Ok)

            # 设置窗口属性，确保消息框正常显示
            from PyQt5.QtCore import Qt
            msg.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
            msg.setModal(True)

            # 显示消息框
            result = msg.exec_()

            # 恢复主窗口的事件过滤器
            if main_window_filter_disabled:
                try:
                    self.main_window.installEventFilter(self.main_window)
                    logger.debug("恢复主窗口事件过滤器")
                except Exception as e:
                    logger.debug(f"无法恢复主窗口事件过滤器: {str(e)}")

            logger.info(f"安全消息框显示完成: {title}")
            return result

        except Exception as e:
            logger.error(f"显示安全消息框时出错: {str(e)}")
            # 如果安全方式失败，尝试使用状态栏显示消息
            try:
                if hasattr(self.main_window, 'status_bar'):
                    self.main_window.status_bar.showMessage(f"{title}: {message}", 5000)
                    logger.info(f"通过状态栏显示消息: {title}")
            except Exception as e2:
                logger.error(f"状态栏显示消息也失败: {str(e2)}")
            return QMessageBox.Ok
