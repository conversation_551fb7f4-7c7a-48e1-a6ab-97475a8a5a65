#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单验证测试
"""

import sys
import os
import unittest

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'test_suite'))

from test_utils import MockSPIService, TestResult

class SimpleValidationTest(unittest.TestCase):
    """简单验证测试类"""
    
    def test_mock_spi_service(self):
        """测试模拟SPI服务"""
        spi = MockSPIService()
        spi.initialize()
        spi.write_register("0x50", 0x1234)
        value = spi.read_register("0x50")
        self.assertEqual(value, 0x1234)
    
    def test_result_recorder(self):
        """测试结果记录器"""
        result = TestResult("simple_test")
        result.add_detail("key", "value")
        result.set_success(True)
        self.assertTrue(result.success)
        self.assertEqual(result.details["key"], "value")

def run_tests():
    """运行测试"""
    suite = unittest.TestLoader().loadTestsFromTestCase(SimpleValidationTest)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
