import logging
from logging.handlers import RotatingFileHandler
import atexit
import os
from pathlib import Path

class LogManager:
    """集中式日志管理器"""

    # 单例实例
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if self._initialized:
            return

        # 配置日志目录
        self.log_dir = Path("log")
        self.log_dir.mkdir(exist_ok=True)

        # 主日志文件路径
        self.main_log = self.log_dir / "app.log"

        # 配置根记录器
        self.root_logger = logging.getLogger()
        self.root_logger.setLevel(logging.DEBUG)

        # 配置控制台日志
        self._setup_console_logging()

        # 配置文件日志
        self._setup_file_logging()

        self._initialized = True

    def _setup_console_logging(self):
        """配置控制台日志"""
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(formatter)
        self.root_logger.addHandler(console_handler)

    def _setup_file_logging(self):
        """配置文件日志，带轮转功能"""
        file_handler = RotatingFileHandler(
            self.main_log,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        formatter = logging.Formatter(
    '%(asctime)s - %(module)s - [%(filename)s:%(lineno)d] - %(levelname)s - %(message)s'
)
        file_handler.setFormatter(formatter)
        self.root_logger.addHandler(file_handler)

    def get_logger(self, name=None):
        """获取模块专用日志记录器"""
        return logging.getLogger(name)

# 初始化日志系统
log_manager = LogManager()
logger = log_manager.get_logger()

# 确保退出时清理
@atexit.register
def cleanup():
    logging.shutdown()

# 提供便捷访问方法
def get_module_logger(module_name):
    """获取模块专用日志记录器
    Args:
        module_name: 模块名(通常使用__name__)
    Returns:
        配置好的日志记录器，自动包含模块名
    """
    logger = log_manager.get_logger(module_name)

    # 添加模块过滤器
    class ModuleFilter(logging.Filter):
        def filter(self, record):
            record.module = module_name.split('.')[-1]
            return True

    logger.addFilter(ModuleFilter())
    return logger

def get_page_logger(page_name):
    """获取页面专用日志记录器
    Args:
        page_name: 页面名称(如'ClkOutputs')
    Returns:
        配置好的页面日志记录器，自动包含页面名
    """
    logger = log_manager.get_logger(f"page.{page_name}")

    # 添加页面过滤器
    class PageFilter(logging.Filter):
        def filter(self, record):
            record.page = page_name
            return True

    logger.addFilter(PageFilter())
    return logger

# 示例日志记录
# logger.debug('这是一个调试信息')
# logger.info('这是一个信息')
# logger.warning('这是一个警告')
# logger.error('这是一个错误')
# logger.critical('这是一个严重错误')