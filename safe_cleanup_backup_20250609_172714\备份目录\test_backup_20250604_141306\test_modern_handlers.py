#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试现代化处理器的SPI端口显示和搜索功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QHBoxLayout
from PyQt5.QtCore import QTimer
from ui.handlers.ModernRegisterIOHandler import ModernRegisterIOHandler
from ui.handlers.ModernRegisterTableHandler import ModernRegisterTableHandler
from utils.Log import logger


class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("现代化处理器测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QHBoxLayout(central_widget)
        
        try:
            # 创建现代化IO处理器
            self.io_handler = ModernRegisterIOHandler.create_for_testing(self)
            layout.addWidget(self.io_handler, 1)
            
            # 创建现代化表格处理器
            self.table_handler = ModernRegisterTableHandler.create_for_testing(self)
            layout.addWidget(self.table_handler, 1)
            
            # 连接信号
            self._connect_signals()
            
            logger.info("测试窗口初始化完成")
            
        except Exception as e:
            logger.error(f"初始化测试窗口时出错: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def _connect_signals(self):
        """连接处理器之间的信号"""
        try:
            # 连接搜索信号
            if hasattr(self.table_handler, 'bit_field_selected'):
                self.table_handler.bit_field_selected.connect(self._on_bit_field_selected)
            
            # 连接寄存器值变化信号
            if hasattr(self.io_handler, 'write_requested'):
                self.io_handler.write_requested.connect(self._on_write_requested)
                
            logger.info("信号连接完成")
            
        except Exception as e:
            logger.error(f"连接信号时出错: {str(e)}")
    
    def _on_bit_field_selected(self, reg_addr):
        """处理位段选择"""
        try:
            logger.info(f"位段选择: {reg_addr}")
            
            # 更新IO处理器的地址显示
            if hasattr(self.io_handler, 'set_address_display'):
                addr_int = int(reg_addr, 16) if reg_addr.startswith("0x") else int(reg_addr)
                self.io_handler.set_address_display(addr_int)
                
                # 模拟读取寄存器值
                mock_value = 0x1234  # 模拟值
                self.io_handler.set_value_display(mock_value)
                
                # 更新表格显示
                self.table_handler.show_bit_fields(reg_addr, mock_value)
                
        except Exception as e:
            logger.error(f"处理位段选择时出错: {str(e)}")
    
    def _on_write_requested(self, addr, value):
        """处理写入请求"""
        try:
            logger.info(f"写入请求: 地址=0x{addr:02X}, 值=0x{value:04X}")
            
            # 更新表格显示
            reg_addr = f"0x{addr:02X}"
            self.table_handler.show_bit_fields(reg_addr, value)
            
        except Exception as e:
            logger.error(f"处理写入请求时出错: {str(e)}")


def test_spi_port_display():
    """测试SPI端口显示功能"""
    print("=== 测试SPI端口显示功能 ===")
    
    try:
        # 创建IO处理器
        io_handler = ModernRegisterIOHandler.create_for_testing()
        
        # 检查端口控件是否存在
        if hasattr(io_handler, 'port_combo') and io_handler.port_combo:
            print("✓ SPI端口下拉框已创建")
            print(f"  端口数量: {io_handler.port_combo.count()}")
            
            # 检查刷新按钮
            if hasattr(io_handler, 'refresh_btn') and io_handler.refresh_btn:
                print("✓ 刷新按钮已创建")
            else:
                print("✗ 刷新按钮未创建")
        else:
            print("✗ SPI端口下拉框未创建")
            
        # 检查SPI服务连接
        if hasattr(io_handler, 'spi_service') and io_handler.spi_service:
            print("✓ SPI服务已连接")
        else:
            print("✗ SPI服务未连接")
            
    except Exception as e:
        print(f"✗ 测试SPI端口显示功能时出错: {str(e)}")


def test_search_functionality():
    """测试搜索功能"""
    print("\n=== 测试搜索功能 ===")
    
    try:
        # 创建表格处理器
        table_handler = ModernRegisterTableHandler.create_for_testing()
        
        # 检查搜索控件是否存在
        if hasattr(table_handler, 'search_edit') and table_handler.search_edit:
            print("✓ 搜索输入框已创建")
        else:
            print("✗ 搜索输入框未创建")
            
        if hasattr(table_handler, 'search_btn') and table_handler.search_btn:
            print("✓ 搜索按钮已创建")
        else:
            print("✗ 搜索按钮未创建")
            
        if hasattr(table_handler, 'search_results_list') and table_handler.search_results_list:
            print("✓ 搜索结果列表已创建")
        else:
            print("✗ 搜索结果列表未创建")
            
        # 检查搜索信号
        if hasattr(table_handler, 'search_requested'):
            print("✓ 搜索请求信号已定义")
        else:
            print("✗ 搜索请求信号未定义")
            
        if hasattr(table_handler, 'bit_field_selected'):
            print("✓ 位段选择信号已定义")
        else:
            print("✗ 位段选择信号未定义")
            
    except Exception as e:
        print(f"✗ 测试搜索功能时出错: {str(e)}")


def main():
    """主函数"""
    print("开始测试现代化处理器功能...")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 运行功能测试
    test_spi_port_display()
    test_search_functionality()
    
    print("\n=== 启动GUI测试 ===")
    print("请在GUI中测试以下功能：")
    print("1. SPI端口下拉框是否显示可用端口")
    print("2. 刷新按钮是否能刷新端口列表")
    print("3. 搜索框是否能输入文本")
    print("4. 搜索按钮是否能触发搜索")
    print("5. 搜索结果是否能正确显示")
    
    # 创建测试窗口
    window = TestMainWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
