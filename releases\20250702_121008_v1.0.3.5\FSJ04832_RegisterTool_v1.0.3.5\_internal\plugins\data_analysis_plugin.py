#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据分析插件
提供寄存器数据分析和可视化功能
"""

import json
import time
from typing import Dict, List, Optional, Any
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTextEdit, QTableWidget, QTableWidgetItem,
                            QTabWidget, QGroupBox, QGridLayout, QComboBox,
                            QSpinBox, QCheckBox, QFileDialog, QMessageBox)
from PyQt5.QtCore import pyqtSignal, QTimer, Qt
from core.services.plugin.PluginManager import IToolWindowPlugin
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class RegisterDataAnalyzer:
    """寄存器数据分析器"""
    
    def __init__(self):
        self.data_history = []  # 存储历史数据
        self.analysis_results = {}
        
    def add_data_snapshot(self, registers_data: Dict[str, Any]):
        """添加数据快照"""
        snapshot = {
            'timestamp': time.time(),
            'data': registers_data.copy()
        }
        self.data_history.append(snapshot)
        
        # 限制历史数据数量
        if len(self.data_history) > 1000:
            self.data_history.pop(0)
    
    def analyze_register_changes(self, register_addr: str) -> Dict:
        """分析特定寄存器的变化"""
        if not self.data_history:
            return {}
            
        values = []
        timestamps = []
        
        for snapshot in self.data_history:
            if register_addr in snapshot['data']:
                values.append(snapshot['data'][register_addr])
                timestamps.append(snapshot['timestamp'])
        
        if not values:
            return {}
            
        # 计算统计信息
        analysis = {
            'count': len(values),
            'min_value': min(values),
            'max_value': max(values),
            'avg_value': sum(values) / len(values),
            'unique_values': len(set(values)),
            'change_count': sum(1 for i in range(1, len(values)) if values[i] != values[i-1]),
            'first_timestamp': timestamps[0] if timestamps else 0,
            'last_timestamp': timestamps[-1] if timestamps else 0,
            'values': values[-50:],  # 最近50个值
            'timestamps': timestamps[-50:]  # 对应的时间戳
        }
        
        return analysis
    
    def find_correlations(self) -> List[Dict]:
        """查找寄存器之间的相关性"""
        if len(self.data_history) < 2:
            return []
            
        correlations = []
        
        # 获取所有寄存器地址
        all_registers = set()
        for snapshot in self.data_history:
            all_registers.update(snapshot['data'].keys())
        
        all_registers = list(all_registers)
        
        # 计算相关性（简化版本）
        for i, reg1 in enumerate(all_registers):
            for reg2 in all_registers[i+1:]:
                correlation = self._calculate_correlation(reg1, reg2)
                if correlation and abs(correlation['coefficient']) > 0.5:
                    correlations.append(correlation)
        
        return sorted(correlations, key=lambda x: abs(x['coefficient']), reverse=True)
    
    def _calculate_correlation(self, reg1: str, reg2: str) -> Optional[Dict]:
        """计算两个寄存器之间的相关性"""
        values1 = []
        values2 = []
        
        for snapshot in self.data_history:
            if reg1 in snapshot['data'] and reg2 in snapshot['data']:
                values1.append(snapshot['data'][reg1])
                values2.append(snapshot['data'][reg2])
        
        if len(values1) < 3:  # 需要至少3个数据点
            return None
            
        # 简化的相关系数计算
        try:
            n = len(values1)
            sum1 = sum(values1)
            sum2 = sum(values2)
            sum1_sq = sum(x*x for x in values1)
            sum2_sq = sum(x*x for x in values2)
            sum_prod = sum(values1[i] * values2[i] for i in range(n))
            
            numerator = n * sum_prod - sum1 * sum2
            denominator = ((n * sum1_sq - sum1 * sum1) * (n * sum2_sq - sum2 * sum2)) ** 0.5
            
            if denominator == 0:
                return None
                
            coefficient = numerator / denominator
            
            return {
                'register1': reg1,
                'register2': reg2,
                'coefficient': coefficient,
                'sample_count': n,
                'strength': 'strong' if abs(coefficient) > 0.8 else 'moderate' if abs(coefficient) > 0.5 else 'weak'
            }
            
        except Exception as e:
            logger.error(f"计算相关性时出错: {str(e)}")
            return None
    
    def export_analysis_data(self, filename: str):
        """导出分析数据"""
        export_data = {
            'export_time': time.time(),
            'data_count': len(self.data_history),
            'analysis_results': self.analysis_results,
            'data_history': self.data_history[-100:]  # 导出最近100条记录
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)


class DataAnalysisWindow(QWidget):
    """数据分析窗口"""

    window_closed = pyqtSignal()

    def __init__(self, parent=None, register_manager=None, main_window=None):
        super().__init__(parent)
        self.setWindowTitle("数据分析器")
        self.resize(1000, 700)
        self.setMinimumSize(800, 500)

        # 设置窗口属性
        from PyQt5.QtCore import Qt
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint |
                           Qt.WindowMinimizeButtonHint | Qt.WindowMaximizeButtonHint)

        # 保存引用
        self.register_manager = register_manager
        self.main_window = main_window

        # 初始化分析器
        self.analyzer = RegisterDataAnalyzer()

        # 设置UI
        self._setup_ui()

        # 连接寄存器更新总线
        self._connect_register_bus()

        # 设置定时器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self._update_analysis)
        self.update_timer.start(5000)  # 每5秒更新一次

        # 初始化寄存器列表
        self._initialize_register_list()
        
    def _setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 寄存器分析标签页
        self._create_register_analysis_tab()
        
        # 相关性分析标签页
        self._create_correlation_tab()
        
        # 数据导出标签页
        self._create_export_tab()
        
        # 控制按钮
        self._create_control_buttons(layout)

    def _connect_register_bus(self):
        """连接寄存器更新总线"""
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            # 连接寄存器更新信号
            RegisterUpdateBus.instance().register_updated.connect(self._on_register_updated)
            logger.info("数据分析器已连接到寄存器更新总线")
        except Exception as e:
            logger.error(f"连接寄存器更新总线失败: {str(e)}")

    def _initialize_register_list(self):
        """初始化寄存器列表"""
        if self.register_manager:
            try:
                # 获取所有寄存器地址
                register_addresses = list(self.register_manager.register_objects.keys())
                register_addresses.sort()  # 排序

                # 添加到下拉列表
                for addr in register_addresses:
                    self.register_combo.addItem(addr)

                logger.info(f"已加载 {len(register_addresses)} 个寄存器到分析器")
            except Exception as e:
                logger.error(f"初始化寄存器列表失败: {str(e)}")
        else:
            logger.warning("寄存器管理器不可用，无法初始化寄存器列表")

    def _on_register_updated(self, addr, value):
        """处理寄存器更新事件"""
        try:
            # 添加数据快照
            registers_data = {addr: value}
            self.analyzer.add_data_snapshot(registers_data)

            # 如果当前选中的寄存器被更新，刷新分析
            current_addr = self.register_combo.currentText().strip()
            if current_addr == addr:
                self._analyze_selected_register()

            logger.debug(f"数据分析器收到寄存器更新: {addr} = 0x{value:04X}")
        except Exception as e:
            logger.error(f"处理寄存器更新事件失败: {str(e)}")
    
    def _create_register_analysis_tab(self):
        """创建寄存器分析标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 寄存器选择
        selection_group = QGroupBox("寄存器选择")
        selection_layout = QHBoxLayout(selection_group)
        
        selection_layout.addWidget(QLabel("寄存器地址:"))
        self.register_combo = QComboBox()
        self.register_combo.setEditable(True)
        self.register_combo.currentTextChanged.connect(self._on_register_selected)
        selection_layout.addWidget(self.register_combo)
        
        self.analyze_button = QPushButton("分析")
        self.analyze_button.clicked.connect(self._analyze_selected_register)
        selection_layout.addWidget(self.analyze_button)

        self.refresh_button = QPushButton("刷新当前值")
        self.refresh_button.clicked.connect(self._refresh_current_value)
        selection_layout.addWidget(self.refresh_button)
        
        selection_layout.addStretch()
        layout.addWidget(selection_group)
        
        # 分析结果
        results_group = QGroupBox("分析结果")
        results_layout = QGridLayout(results_group)
        
        self.sample_count_label = QLabel("0")
        self.min_value_label = QLabel("0")
        self.max_value_label = QLabel("0")
        self.avg_value_label = QLabel("0")
        self.unique_values_label = QLabel("0")
        self.change_count_label = QLabel("0")
        
        results_layout.addWidget(QLabel("样本数量:"), 0, 0)
        results_layout.addWidget(self.sample_count_label, 0, 1)
        results_layout.addWidget(QLabel("最小值:"), 1, 0)
        results_layout.addWidget(self.min_value_label, 1, 1)
        results_layout.addWidget(QLabel("最大值:"), 2, 0)
        results_layout.addWidget(self.max_value_label, 2, 1)
        results_layout.addWidget(QLabel("平均值:"), 0, 2)
        results_layout.addWidget(self.avg_value_label, 0, 3)
        results_layout.addWidget(QLabel("唯一值数:"), 1, 2)
        results_layout.addWidget(self.unique_values_label, 1, 3)
        results_layout.addWidget(QLabel("变化次数:"), 2, 2)
        results_layout.addWidget(self.change_count_label, 2, 3)
        
        layout.addWidget(results_group)
        
        # 历史数据表格
        history_group = QGroupBox("历史数据")
        history_layout = QVBoxLayout(history_group)
        
        self.history_table = QTableWidget()
        self.history_table.setColumnCount(3)
        self.history_table.setHorizontalHeaderLabels(["时间", "值", "变化"])
        history_layout.addWidget(self.history_table)
        
        layout.addWidget(history_group)
        
        self.tab_widget.addTab(tab, "寄存器分析")
    
    def _create_correlation_tab(self):
        """创建相关性分析标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 控制面板
        control_group = QGroupBox("分析控制")
        control_layout = QHBoxLayout(control_group)
        
        self.correlation_button = QPushButton("查找相关性")
        self.correlation_button.clicked.connect(self._find_correlations)
        control_layout.addWidget(self.correlation_button)
        
        control_layout.addWidget(QLabel("最小相关系数:"))
        self.min_correlation_spin = QSpinBox()
        self.min_correlation_spin.setRange(1, 100)
        self.min_correlation_spin.setValue(50)
        self.min_correlation_spin.setSuffix("%")
        control_layout.addWidget(self.min_correlation_spin)
        
        control_layout.addStretch()
        layout.addWidget(control_group)
        
        # 相关性结果表格
        correlation_group = QGroupBox("相关性分析结果")
        correlation_layout = QVBoxLayout(correlation_group)
        
        self.correlation_table = QTableWidget()
        self.correlation_table.setColumnCount(5)
        self.correlation_table.setHorizontalHeaderLabels(["寄存器1", "寄存器2", "相关系数", "强度", "样本数"])
        correlation_layout.addWidget(self.correlation_table)
        
        layout.addWidget(correlation_group)
        
        self.tab_widget.addTab(tab, "相关性分析")
    
    def _create_export_tab(self):
        """创建数据导出标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 导出选项
        options_group = QGroupBox("导出选项")
        options_layout = QGridLayout(options_group)
        
        self.export_history_check = QCheckBox("包含历史数据")
        self.export_history_check.setChecked(True)
        options_layout.addWidget(self.export_history_check, 0, 0)
        
        self.export_analysis_check = QCheckBox("包含分析结果")
        self.export_analysis_check.setChecked(True)
        options_layout.addWidget(self.export_analysis_check, 0, 1)
        
        options_layout.addWidget(QLabel("历史数据条数:"), 1, 0)
        self.history_count_spin = QSpinBox()
        self.history_count_spin.setRange(10, 1000)
        self.history_count_spin.setValue(100)
        options_layout.addWidget(self.history_count_spin, 1, 1)
        
        layout.addWidget(options_group)
        
        # 导出按钮
        export_group = QGroupBox("导出操作")
        export_layout = QHBoxLayout(export_group)
        
        self.export_json_button = QPushButton("导出为JSON")
        self.export_json_button.clicked.connect(self._export_json)
        export_layout.addWidget(self.export_json_button)
        
        self.export_csv_button = QPushButton("导出为CSV")
        self.export_csv_button.clicked.connect(self._export_csv)
        export_layout.addWidget(self.export_csv_button)
        
        export_layout.addStretch()
        layout.addWidget(export_group)
        
        # 导出状态
        self.export_status_text = QTextEdit()
        self.export_status_text.setMaximumHeight(150)
        self.export_status_text.setReadOnly(True)
        layout.addWidget(self.export_status_text)
        
        self.tab_widget.addTab(tab, "数据导出")
    
    def _create_control_buttons(self, layout):
        """创建控制按钮"""
        button_layout = QHBoxLayout()
        
        self.start_monitoring_button = QPushButton("开始监控")
        self.start_monitoring_button.clicked.connect(self._start_monitoring)
        button_layout.addWidget(self.start_monitoring_button)
        
        self.stop_monitoring_button = QPushButton("停止监控")
        self.stop_monitoring_button.clicked.connect(self._stop_monitoring)
        self.stop_monitoring_button.setEnabled(False)
        button_layout.addWidget(self.stop_monitoring_button)
        
        self.clear_data_button = QPushButton("清除数据")
        self.clear_data_button.clicked.connect(self._clear_data)
        button_layout.addWidget(self.clear_data_button)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)

    def _on_register_selected(self, register_addr: str):
        """寄存器选择变化"""
        if register_addr:
            # 获取当前寄存器值并添加到分析器
            self._add_current_register_value(register_addr)
            self._analyze_selected_register()

    def _add_current_register_value(self, register_addr):
        """添加当前寄存器值到分析器"""
        if self.register_manager and register_addr:
            try:
                current_value = self.register_manager.get_register_value(register_addr)
                registers_data = {register_addr: current_value}
                self.analyzer.add_data_snapshot(registers_data)
                logger.debug(f"添加当前寄存器值到分析器: {register_addr} = 0x{current_value:04X}")
            except Exception as e:
                logger.error(f"获取寄存器值失败: {str(e)}")

    def _analyze_selected_register(self):
        """分析选中的寄存器"""
        register_addr = self.register_combo.currentText().strip()
        if not register_addr:
            return

        analysis = self.analyzer.analyze_register_changes(register_addr)
        if not analysis:
            self._clear_analysis_display()
            self._show_no_data_message(register_addr)
            return

        # 更新分析结果显示
        self.sample_count_label.setText(str(analysis['count']))
        self.min_value_label.setText(f"0x{analysis['min_value']:04X}")
        self.max_value_label.setText(f"0x{analysis['max_value']:04X}")
        self.avg_value_label.setText(f"0x{analysis['avg_value']:.0f}")
        self.unique_values_label.setText(str(analysis['unique_values']))
        self.change_count_label.setText(str(analysis['change_count']))

        # 更新历史数据表格
        self._update_history_table(analysis)

        logger.info(f"分析完成: {register_addr}, 样本数: {analysis['count']}")

    def _show_no_data_message(self, register_addr):
        """显示无数据消息"""
        logger.info(f"寄存器 {register_addr} 暂无历史数据")

    def _refresh_current_value(self):
        """刷新当前选中寄存器的值"""
        register_addr = self.register_combo.currentText().strip()
        if not register_addr:
            QMessageBox.warning(self, "警告", "请先选择一个寄存器地址")
            return

        if not self.register_manager:
            QMessageBox.warning(self, "错误", "寄存器管理器不可用")
            return

        try:
            # 获取当前值并添加到分析器
            current_value = self.register_manager.get_register_value(register_addr)
            registers_data = {register_addr: current_value}
            self.analyzer.add_data_snapshot(registers_data)

            # 重新分析
            self._analyze_selected_register()

            # 显示状态消息
            QMessageBox.information(self, "成功",
                f"已刷新寄存器 {register_addr} 的当前值: 0x{current_value:04X}")
            logger.info(f"手动刷新寄存器值: {register_addr} = 0x{current_value:04X}")

        except Exception as e:
            error_msg = f"刷新寄存器值失败: {str(e)}"
            QMessageBox.critical(self, "错误", error_msg)
            logger.error(error_msg)

    def _clear_analysis_display(self):
        """清除分析显示"""
        self.sample_count_label.setText("0")
        self.min_value_label.setText("无数据")
        self.max_value_label.setText("无数据")
        self.avg_value_label.setText("无数据")
        self.unique_values_label.setText("0")
        self.change_count_label.setText("0")
        self.history_table.setRowCount(0)

    def _update_history_table(self, analysis: Dict):
        """更新历史数据表格"""
        values = analysis.get('values', [])
        timestamps = analysis.get('timestamps', [])

        self.history_table.setRowCount(len(values))

        for i, (value, timestamp) in enumerate(zip(values, timestamps)):
            # 时间
            time_str = time.strftime('%H:%M:%S', time.localtime(timestamp))
            self.history_table.setItem(i, 0, QTableWidgetItem(time_str))

            # 值（显示为十六进制）
            value_str = f"0x{value:04X}"
            self.history_table.setItem(i, 1, QTableWidgetItem(value_str))

            # 变化（与前一个值比较）
            if i > 0:
                prev_value = values[i-1]
                if value != prev_value:
                    change = f"{value - prev_value:+d} (0x{abs(value - prev_value):04X})"
                    self.history_table.setItem(i, 2, QTableWidgetItem(change))
                else:
                    self.history_table.setItem(i, 2, QTableWidgetItem("无变化"))
            else:
                self.history_table.setItem(i, 2, QTableWidgetItem("-"))

        # 滚动到最新记录
        self.history_table.scrollToBottom()

    def _find_correlations(self):
        """查找相关性"""
        correlations = self.analyzer.find_correlations()
        min_correlation = self.min_correlation_spin.value() / 100.0

        # 过滤相关性
        filtered_correlations = [c for c in correlations if abs(c['coefficient']) >= min_correlation]

        # 更新相关性表格
        self.correlation_table.setRowCount(len(filtered_correlations))

        for i, correlation in enumerate(filtered_correlations):
            self.correlation_table.setItem(i, 0, QTableWidgetItem(correlation['register1']))
            self.correlation_table.setItem(i, 1, QTableWidgetItem(correlation['register2']))
            self.correlation_table.setItem(i, 2, QTableWidgetItem(f"{correlation['coefficient']:.3f}"))
            self.correlation_table.setItem(i, 3, QTableWidgetItem(correlation['strength']))
            self.correlation_table.setItem(i, 4, QTableWidgetItem(str(correlation['sample_count'])))

    def _start_monitoring(self):
        """开始监控"""
        self.start_monitoring_button.setEnabled(False)
        self.stop_monitoring_button.setEnabled(True)
        self.update_timer.start(5000)  # 每5秒更新
        self._add_export_log("开始数据监控")

    def _stop_monitoring(self):
        """停止监控"""
        self.start_monitoring_button.setEnabled(True)
        self.stop_monitoring_button.setEnabled(False)
        self.update_timer.stop()
        self._add_export_log("停止数据监控")

    def _clear_data(self):
        """清除数据"""
        reply = QMessageBox.question(self, "确认", "确定要清除所有分析数据吗？",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.analyzer = RegisterDataAnalyzer()
            self._clear_analysis_display()
            self.correlation_table.setRowCount(0)
            self.register_combo.clear()
            # 重新初始化寄存器列表
            self._initialize_register_list()
            self._add_export_log("已清除所有数据")

    def _export_json(self):
        """导出JSON格式"""
        filename, _ = QFileDialog.getSaveFileName(
            self, "导出JSON数据", "analysis_data.json", "JSON文件 (*.json)"
        )

        if filename:
            try:
                self.analyzer.export_analysis_data(filename)
                self._add_export_log(f"成功导出JSON数据到: {filename}")
            except Exception as e:
                self._add_export_log(f"导出JSON失败: {str(e)}")

    def _export_csv(self):
        """导出CSV格式"""
        filename, _ = QFileDialog.getSaveFileName(
            self, "导出CSV数据", "analysis_data.csv", "CSV文件 (*.csv)"
        )

        if filename:
            try:
                self._export_csv_data(filename)
                self._add_export_log(f"成功导出CSV数据到: {filename}")
            except Exception as e:
                self._add_export_log(f"导出CSV失败: {str(e)}")

    def _export_csv_data(self, filename: str):
        """导出CSV数据"""
        import csv

        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)

            # 写入标题
            writer.writerow(['时间戳', '寄存器地址', '值'])

            # 写入数据
            count = self.history_count_spin.value()
            recent_data = self.analyzer.data_history[-count:] if count > 0 else self.analyzer.data_history

            for snapshot in recent_data:
                timestamp = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(snapshot['timestamp']))
                for addr, value in snapshot['data'].items():
                    writer.writerow([timestamp, addr, value])

    def _add_export_log(self, message: str):
        """添加导出日志"""
        timestamp = time.strftime('%H:%M:%S')
        self.export_status_text.append(f"[{timestamp}] {message}")

    def _update_analysis(self):
        """更新分析（定时调用）"""
        if self.register_manager:
            try:
                # 获取所有寄存器的当前值
                registers_data = {}
                for addr in self.register_manager.register_objects.keys():
                    try:
                        value = self.register_manager.get_register_value(addr)
                        registers_data[addr] = value
                    except Exception as e:
                        logger.debug(f"获取寄存器 {addr} 值失败: {str(e)}")

                if registers_data:
                    # 添加数据快照
                    self.analyzer.add_data_snapshot(registers_data)

                    # 如果当前有选中的寄存器，更新分析
                    current_addr = self.register_combo.currentText().strip()
                    if current_addr:
                        self._analyze_selected_register()

                    logger.debug(f"定时更新: 收集了 {len(registers_data)} 个寄存器数据")

            except Exception as e:
                logger.error(f"定时更新分析数据失败: {str(e)}")
        else:
            logger.debug("寄存器管理器不可用，跳过定时更新")

    def add_register_data(self, registers_data: Dict[str, Any]):
        """添加寄存器数据（外部调用）"""
        self.analyzer.add_data_snapshot(registers_data)

        # 更新寄存器下拉列表
        current_registers = set(registers_data.keys())
        combo_registers = set(self.register_combo.itemText(i) for i in range(self.register_combo.count()))

        new_registers = current_registers - combo_registers
        for register in new_registers:
            self.register_combo.addItem(register)

    def closeEvent(self, event):
        """窗口关闭事件"""
        self.update_timer.stop()
        self.window_closed.emit()
        super().closeEvent(event)


class DataAnalysisPlugin(IToolWindowPlugin):
    """数据分析插件"""

    @property
    def name(self) -> str:
        return "数据分析器"

    @property
    def version(self) -> str:
        return "1.0.0"

    @property
    def description(self) -> str:
        return "提供寄存器数据分析和可视化功能"

    @property
    def menu_text(self) -> str:
        return "数据分析器"

    @property
    def icon_path(self) -> Optional[str]:
        return None

    def __init__(self):
        self.context = None
        self.window_instance = None

    def initialize(self, context):
        """初始化插件"""
        self.context = context
        logger.info(f"插件 '{self.name}' 初始化完成")

    def create_window(self, parent=None):
        """创建工具窗口"""
        if self.window_instance is None:
            # 获取寄存器管理器
            register_manager = None
            main_window = None

            if self.context and hasattr(self.context, 'register_manager'):
                register_manager = self.context.register_manager
                main_window = self.context
                logger.info(f"从主窗口获取寄存器管理器: {register_manager}")

            if not register_manager:
                logger.warning(f"无法获取寄存器管理器，插件 '{self.name}' 功能可能受限")

            # 创建窗口实例
            self.window_instance = DataAnalysisWindow(
                parent=parent,
                register_manager=register_manager,
                main_window=main_window
            )
            self.window_instance.window_closed.connect(self._on_window_closed)
            logger.info(f"创建 '{self.name}' 工具窗口")

        return self.window_instance

    def _on_window_closed(self):
        """窗口关闭处理"""
        self.window_instance = None
        logger.info(f"'{self.name}' 工具窗口已关闭")

    def cleanup(self):
        """清理插件资源"""
        if self.window_instance:
            self.window_instance.close()
            self.window_instance = None

        logger.info(f"插件 '{self.name}' 已清理")


# 插件入口点
plugin_class = DataAnalysisPlugin
