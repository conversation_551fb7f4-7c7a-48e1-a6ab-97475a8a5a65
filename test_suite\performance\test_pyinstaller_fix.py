#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试PyInstaller修复方案
验证所有现代化处理器都能被正确导入
"""

import sys
import traceback

def test_static_imports():
    """测试静态导入是否工作"""
    print("=" * 60)
    print("测试静态导入")
    print("=" * 60)
    
    try:
        from ui.factories.ModernToolWindowFactory import ModernToolWindowFactory
        print("✓ ModernToolWindowFactory导入成功")
        
        # 创建工厂实例
        factory = ModernToolWindowFactory(None)
        print("✓ 工厂实例创建成功")
        
        # 测试所有现代化处理器的静态映射
        test_handlers = [
            'ui.handlers.ModernSetModesHandler.ModernSetModesHandler',
            'ui.handlers.ModernClkinControlHandler.ModernClkinControlHandler',
            'ui.handlers.ModernPLLHandler.ModernPLLHandler',
            'ui.handlers.ModernSyncSysRefHandler.ModernSyncSysRefHandler',
            'ui.handlers.ModernClkOutputsHandler.ModernClkOutputsHandler',
            'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler',
            'ui.handlers.ModernUIEventHandler.ModernUIEventHandler',
            'ui.handlers.ModernRegisterIOHandler.ModernRegisterIOHandler',
            'ui.handlers.ModernRegisterTreeHandler.ModernRegisterTreeHandler',
        ]
        
        success_count = 0
        for handler_path in test_handlers:
            try:
                handler_class = factory._import_handler_class(handler_path)
                print(f"✓ {handler_path.split('.')[-1]}: {handler_class.__name__}")
                success_count += 1
            except Exception as e:
                print(f"❌ {handler_path}: {str(e)}")
        
        print(f"\n成功导入: {success_count}/{len(test_handlers)} 个处理器")
        return success_count == len(test_handlers)
        
    except Exception as e:
        print(f"❌ 静态导入测试失败: {str(e)}")
        traceback.print_exc()
        return False

def test_direct_imports():
    """测试直接导入所有处理器"""
    print("\n" + "=" * 60)
    print("测试直接导入")
    print("=" * 60)
    
    handlers = [
        ('ModernSetModesHandler', 'ui.handlers.ModernSetModesHandler'),
        ('ModernClkinControlHandler', 'ui.handlers.ModernClkinControlHandler'),
        ('ModernPLLHandler', 'ui.handlers.ModernPLLHandler'),
        ('ModernSyncSysRefHandler', 'ui.handlers.ModernSyncSysRefHandler'),
        ('ModernClkOutputsHandler', 'ui.handlers.ModernClkOutputsHandler'),
        ('ModernRegisterTableHandler', 'ui.handlers.ModernRegisterTableHandler'),
        ('ModernUIEventHandler', 'ui.handlers.ModernUIEventHandler'),
        ('ModernRegisterIOHandler', 'ui.handlers.ModernRegisterIOHandler'),
        ('ModernRegisterTreeHandler', 'ui.handlers.ModernRegisterTreeHandler'),
    ]
    
    success_count = 0
    for class_name, module_path in handlers:
        try:
            import importlib
            module = importlib.import_module(module_path)
            handler_class = getattr(module, class_name)
            print(f"✓ {class_name}: {handler_class}")
            success_count += 1
        except Exception as e:
            print(f"❌ {class_name}: {str(e)}")
    
    print(f"\n成功导入: {success_count}/{len(handlers)} 个处理器")
    return success_count == len(handlers)

def test_init_file():
    """测试__init__.py文件导入"""
    print("\n" + "=" * 60)
    print("测试__init__.py文件导入")
    print("=" * 60)
    
    try:
        import ui.handlers
        print("✓ ui.handlers模块导入成功")
        
        # 检查__all__中的类
        if hasattr(ui.handlers, '__all__'):
            print(f"✓ __all__包含 {len(ui.handlers.__all__)} 个类")
            
            success_count = 0
            for class_name in ui.handlers.__all__:
                if hasattr(ui.handlers, class_name):
                    handler_class = getattr(ui.handlers, class_name)
                    print(f"✓ {class_name}: {handler_class}")
                    success_count += 1
                else:
                    print(f"❌ {class_name}: 未找到")
            
            print(f"\n成功导入: {success_count}/{len(ui.handlers.__all__)} 个类")
            return success_count == len(ui.handlers.__all__)
        else:
            print("❌ __all__未定义")
            return False
            
    except Exception as e:
        print(f"❌ __init__.py导入测试失败: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("PyInstaller修复方案测试")
    print("测试所有现代化处理器的导入能力")
    
    # 测试1: 静态导入
    test1_result = test_static_imports()
    
    # 测试2: 直接导入
    test2_result = test_direct_imports()
    
    # 测试3: __init__.py导入
    test3_result = test_init_file()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    print(f"静态导入测试: {'✓ 通过' if test1_result else '❌ 失败'}")
    print(f"直接导入测试: {'✓ 通过' if test2_result else '❌ 失败'}")
    print(f"__init__.py导入测试: {'✓ 通过' if test3_result else '❌ 失败'}")
    
    all_passed = test1_result and test2_result and test3_result
    print(f"\n总体结果: {'✅ 所有测试通过' if all_passed else '❌ 部分测试失败'}")
    
    if all_passed:
        print("\n🎉 PyInstaller修复方案验证成功！")
        print("现在可以安全地使用PyInstaller打包应用程序。")
        print("\n建议的打包命令:")
        print("1. 使用spec文件: pyinstaller main.spec")
        print("2. 使用脚本: python build_exe.py")
    else:
        print("\n⚠️  仍有问题需要解决")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
