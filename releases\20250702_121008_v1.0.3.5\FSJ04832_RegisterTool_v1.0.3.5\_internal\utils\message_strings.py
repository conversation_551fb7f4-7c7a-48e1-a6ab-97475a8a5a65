# -*- coding: utf-8 -*-

class MessageStrings:
    """消息字符串类，用于本地化支持"""
    
    # 当前语言，默认为中文
    current_lang = "zh"
    
    # 错误消息映射
    error_messages = {
        "spi_not_available": {
            "zh": "SPI连接不可用",
            "en": "SPI connection not available"
        },
        "spi_operation_failed": {
            "zh": "SPI操作失败",
            "en": "SPI operation failed"
        },
        "invalid_register": {
            "zh": "无效的寄存器地址",
            "en": "Invalid register address"
        },
        "file_not_found": {
            "zh": "找不到文件",
            "en": "File not found"
        },
        "invalid_json": {
            "zh": "无效的JSON格式",
            "en": "Invalid JSON format"
        },
        "operation_timeout": {
            "zh": "操作超时",
            "en": "Operation timeout"
        },
        "value_out_of_range": {
            "zh": "值超出范围",
            "en": "Value out of range"
        },
        # 添加更多错误消息...
    }
    
    # 提示消息映射
    info_messages = {
        "simulation_mode_enabled": {
            "zh": "已启用模拟模式",
            "en": "Simulation mode enabled"
        },
        "simulation_mode_disabled": {
            "zh": "已禁用模拟模式",
            "en": "Simulation mode disabled"
        },
        "operation_complete": {
            "zh": "操作完成",
            "en": "Operation complete"
        },
        "operation_canceled": {
            "zh": "操作已取消",
            "en": "Operation canceled"
        },
        # 添加更多提示消息...
    }
    
    # 建议消息映射
    suggestion_messages = {
        "spi_not_available": {
            "zh": "请检查硬件连接或启用模拟模式",
            "en": "Please check hardware connection or enable simulation mode"
        },
        "invalid_register": {
            "zh": "请确保寄存器地址在有效范围内",
            "en": "Please ensure the register address is within valid range"
        },
        "value_out_of_range": {
            "zh": "请确保输入值在有效范围内",
            "en": "Please ensure the input value is within valid range"
        },
        # 添加更多建议消息...
    }
    
    @classmethod
    def set_language(cls, lang_code):
        """设置当前语言
        
        Args:
            lang_code: 语言代码，如'zh'或'en'
        """
        if lang_code in ["zh", "en"]:
            cls.current_lang = lang_code
            return True
        return False
    
    @classmethod
    def get_error(cls, error_code):
        """获取错误消息
        
        Args:
            error_code: 错误代码
            
        Returns:
            对应语言的错误消息
        """
        if error_code in cls.error_messages:
            return cls.error_messages[error_code].get(cls.current_lang, cls.error_messages[error_code]["zh"])
        return error_code
    
    @classmethod
    def get_info(cls, info_code):
        """获取提示消息
        
        Args:
            info_code: 提示代码
            
        Returns:
            对应语言的提示消息
        """
        if info_code in cls.info_messages:
            return cls.info_messages[info_code].get(cls.current_lang, cls.info_messages[info_code]["zh"])
        return info_code
    
    @classmethod
    def get_suggestion(cls, error_code):
        """获取建议消息
        
        Args:
            error_code: 错误代码
            
        Returns:
            对应语言的建议消息
        """
        if error_code in cls.suggestion_messages:
            return cls.suggestion_messages[error_code].get(cls.current_lang, cls.suggestion_messages[error_code]["zh"])
        return ""