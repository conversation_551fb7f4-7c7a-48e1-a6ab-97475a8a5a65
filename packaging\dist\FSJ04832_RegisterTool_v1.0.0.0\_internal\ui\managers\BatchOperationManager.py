"""
批量操作管理器
负责处理寄存器的批量读写操作
"""

import time
import traceback
from PyQt5.QtCore import QTimer, QObject, pyqtSignal, Qt
from PyQt5.QtWidgets import QProgressDialog, QMessageBox
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class BatchOperationManager(QObject):
    """批量操作管理器，处理寄存器的批量读写操作"""
    
    # 信号定义
    operation_completed = pyqtSignal(str)  # 操作完成信号，参数为操作类型
    operation_cancelled = pyqtSignal(str)  # 操作取消信号
    progress_updated = pyqtSignal(int, int)  # 进度更新信号 (当前, 总数)

    # 性能监控信号
    batch_read_started = pyqtSignal(int)  # 批量读取开始 (总数)
    batch_read_finished = pyqtSignal(bool, float, int)  # 批量读取完成 (成功, 耗时, 完成数)
    batch_read_progress = pyqtSignal(int, int)  # 批量读取进度 (当前, 总数)

    batch_write_started = pyqtSignal(int)  # 批量写入开始 (总数)
    batch_write_finished = pyqtSignal(bool, float, int)  # 批量写入完成 (成功, 耗时, 完成数)
    batch_write_progress = pyqtSignal(int, int)  # 批量写入进度 (当前, 总数)
    
    def __init__(self, main_window):
        """初始化批量操作管理器
        
        Args:
            main_window: 主窗口实例
        """
        super().__init__()
        self.main_window = main_window
        
        # 批量操作状态
        self.is_batch_reading = False
        self.is_batch_writing = False
        self.is_batch_updating = False

        # 进度跟踪
        self.read_all_completed = 0
        self.read_all_total = 0
        self.write_all_completed = 0
        self.write_all_total = 0

        # 批次处理
        self.remaining_batches = None
        self.progress_dialog = None
        self.batch_mode_active = False  # 批量模式标志，用于优化UI更新

        # UI更新控制
        self._ui_updates_suspended = False
        self._original_layout_sizes = None
        self._layout_widgets = None
        
        # 从配置加载常量
        self._load_batch_config()

        # 创建异步批量操作管理器
        self._setup_async_manager()

    def _setup_async_manager(self):
        """设置异步批量操作管理器"""
        # 暂时禁用异步管理器，使用传统方式但优化性能
        self.async_batch_manager = None
        logger.info("使用传统批量操作方式（已优化性能）")

    def _load_batch_config(self):
        """从配置加载批量操作相关配置"""
        try:
            from core.services.config.ConfigurationManager import get_config

            # 加载批量操作配置
            self.BATCH_SIZE = get_config('spi.batch_size', 50)
            self.OPERATION_TIMEOUT = get_config('spi.timeout', 2000)
            self.BATCH_TIMEOUT_MULTIPLIER = get_config('performance.batch_timeout_multiplier', 1.5)
            self.UI_UPDATE_INTERVAL = get_config('performance.ui_update_interval', 10)

            logger.info(f"批量操作配置已加载: BATCH_SIZE={self.BATCH_SIZE}, "
                       f"TIMEOUT={self.OPERATION_TIMEOUT}ms, "
                       f"UI_UPDATE_INTERVAL={self.UI_UPDATE_INTERVAL}")
        except Exception as e:
            logger.error(f"加载批量操作配置失败: {str(e)}")
            # 使用默认值
            self.BATCH_SIZE = 50
            self.OPERATION_TIMEOUT = 2000
            self.BATCH_TIMEOUT_MULTIPLIER = 1.5
            self.UI_UPDATE_INTERVAL = 10

    def _handle_async_read_all(self):
        """使用异步管理器处理批量读取"""
        try:
            # 获取所有寄存器
            all_registers = self._get_all_registers()
            if not all_registers:
                QMessageBox.information(
                    self.main_window,
                    "提示",
                    "没有可读取的寄存器"
                )
                return

            logger.info(f"使用异步管理器开始批量读取: {len(all_registers)}个寄存器")

            # 启动异步批量读取
            self.async_batch_manager.start_batch_read_async(all_registers)

        except Exception as e:
            logger.error(f"启动异步批量读取失败: {str(e)}")
            QMessageBox.critical(
                self.main_window,
                "错误",
                f"启动异步批量读取失败: {str(e)}"
            )

    def _handle_async_write_all(self):
        """使用异步管理器处理批量写入"""
        try:
            # 获取所有寄存器及其值
            all_registers = self._get_all_registers(with_values=True)
            if not all_registers:
                QMessageBox.information(
                    self.main_window,
                    "提示",
                    "没有可写入的寄存器"
                )
                return

            # 转换为字典格式
            register_data = {addr: value for addr, value in all_registers}

            logger.info(f"使用异步管理器开始批量写入: {len(register_data)}个寄存器")

            # 启动异步批量写入
            self.async_batch_manager.start_batch_write_async(register_data)

        except Exception as e:
            logger.error(f"启动异步批量写入失败: {str(e)}")
            QMessageBox.critical(
                self.main_window,
                "错误",
                f"启动异步批量写入失败: {str(e)}"
            )

    def _on_async_batch_read_completed(self, results):
        """处理异步批量读取完成"""
        logger.info(f"异步批量读取完成，读取了 {len(results)} 个寄存器")

        # 更新寄存器管理器中的值
        success_count = 0
        for addr, value in results.items():
            if value is not None:
                try:
                    self.main_window.register_manager.set_register_value(addr, value)
                    success_count += 1
                except Exception as e:
                    logger.error(f"更新寄存器值失败 {addr}: {str(e)}")

        # 刷新UI显示
        self._refresh_ui_after_batch_operation()

        logger.info(f"异步批量读取完成，成功更新 {success_count} 个寄存器")

    def _on_async_batch_write_completed(self, results):
        """处理异步批量写入完成"""
        success_count = len([v for v in results.values() if v])
        logger.info(f"异步批量写入完成，成功写入 {success_count}/{len(results)} 个寄存器")

        # 刷新UI显示
        self._refresh_ui_after_batch_operation()

    def _on_async_operation_progress(self, current, total):
        """处理异步操作进度更新"""
        # 这里可以添加额外的进度处理逻辑
        pass

    def _on_async_operation_cancelled(self):
        """处理异步操作取消"""
        logger.info("异步批量操作被用户取消")

    def _refresh_ui_after_batch_operation(self):
        """批量操作完成后刷新UI"""
        try:
            # 恢复UI更新
            self._resume_ui_updates()

            # 刷新当前选中寄存器的显示
            if hasattr(self.main_window, 'current_register_addr') and self.main_window.current_register_addr:
                current_addr = self.main_window.current_register_addr
                logger.info(f"刷新当前选中寄存器的详细显示: {current_addr}")

                # 获取最新值
                current_value = self.main_window.register_manager.get_register_value(current_addr)

                # 使用现有的刷新方法
                if hasattr(self.main_window, 'refresh_view'):
                    self.main_window.refresh_view()
                elif hasattr(self.main_window, 'display_manager'):
                    self.main_window.display_manager.update_bit_field_display(current_addr, current_value)

            logger.info("批量操作后UI刷新完成")

        except Exception as e:
            logger.error(f"批量操作后刷新UI失败: {str(e)}")

    def _suspend_ui_updates(self):
        """暂停UI更新以提高性能"""
        try:
            if hasattr(self.main_window, 'setUpdatesEnabled'):
                self.main_window.setUpdatesEnabled(False)
                logger.debug("已暂停UI更新")
        except Exception as e:
            logger.debug(f"暂停UI更新失败: {str(e)}")

    def _resume_ui_updates(self):
        """恢复UI更新"""
        try:
            if hasattr(self.main_window, 'setUpdatesEnabled'):
                self.main_window.setUpdatesEnabled(True)
                logger.debug("已恢复UI更新")
        except Exception as e:
            logger.debug(f"恢复UI更新失败: {str(e)}")

    def handle_read_all_requested(self):
        """处理读取所有寄存器请求"""
        if not self._check_batch_operation_status('read'):
            return

        # 优先使用异步批量操作管理器
        if hasattr(self, 'async_batch_manager') and self.async_batch_manager:
            self._handle_async_read_all()
            return

        # 强制清理之前的状态，确保干净的开始
        self._force_cleanup_before_start()

        try:
            # 获取所有寄存器
            all_registers = self._get_all_registers()
            if not all_registers:
                QMessageBox.information(
                    self.main_window,
                    "提示",
                    "没有可读取的寄存器"
                )
                return

            # 设置批量读取状态
            self.is_batch_reading = True
            self.main_window.is_batch_reading = True  # 确保主窗口也设置了状态
            self.read_all_completed = 0
            self.read_all_total = len(all_registers)

            # 记录开始时间
            self.batch_read_start_time = time.time()

            # 在批量操作开始前暂停UI更新，防止布局抖动
            self._suspend_ui_updates()

            # 发出性能监控信号
            self.batch_read_started.emit(self.read_all_total)

            # 添加详细日志
            logger.info(f"🚀 开始批量读取: 总共 {self.read_all_total} 个寄存器")
            logger.debug(f"寄存器列表: {all_registers[:10]}{'...' if len(all_registers) > 10 else ''}")  # 只显示前10个
            logger.debug(f"寄存器地址范围: {all_registers[0]} 到 {all_registers[-1]}")  # 显示地址范围

            # 创建进度对话框
            self.progress_dialog = self._create_progress_dialog(
                "批量读取",
                "正在读取所有寄存器...",
                self.read_all_total
            )
            
            # 分批处理
            batches = [all_registers[i:i + self.BATCH_SIZE]
                      for i in range(0, len(all_registers), self.BATCH_SIZE)]
            self.remaining_batches = iter(batches)

            # 添加批次信息日志
            logger.info(f"📦 创建了 {len(batches)} 个批次，每批最多 {self.BATCH_SIZE} 个寄存器")
            for i, batch in enumerate(batches):
                logger.debug(f"批次 {i+1}: {len(batch)} 个寄存器 - {batch[:3]}{'...' if len(batch) > 3 else ''}")
            
            # 连接SPI信号
            self._connect_read_signals()
            
            # 开始处理第一批
            try:
                first_batch = next(self.remaining_batches)
                self._process_read_batch(first_batch)
            except StopIteration:
                self._finish_read_all()
                
        except Exception as e:
            logger.error(f"启动批量读取时出错: {str(e)}")
            self._finish_read_all()
            QMessageBox.critical(
                self.main_window, 
                "错误", 
                f"启动批量读取失败: {str(e)}"
            )
            traceback.print_exc()
    
    def handle_write_all_requested(self):
        """处理写入所有寄存器请求"""
        if not self._check_batch_operation_status('write'):
            return

        # 优先使用异步批量操作管理器
        if hasattr(self, 'async_batch_manager') and self.async_batch_manager:
            self._handle_async_write_all()
            return

        # 强制清理之前的状态，确保干净的开始
        self._force_cleanup_before_start()

        try:
            # 获取所有寄存器及其值
            all_registers = self._get_all_registers(with_values=True)
            if not all_registers:
                QMessageBox.information(
                    self.main_window,
                    "提示",
                    "没有可写入的寄存器"
                )
                return

            # 设置批量写入状态
            self.is_batch_writing = True
            self.main_window.is_batch_writing = True  # 确保主窗口也设置了状态
            self.write_all_completed = 0
            self.write_all_total = len(all_registers)

            # 记录开始时间
            self.batch_write_start_time = time.time()

            # 在批量操作开始前暂停UI更新，防止布局抖动
            self._suspend_ui_updates()

            # 发出性能监控信号
            self.batch_write_started.emit(self.write_all_total)
            
            # 创建进度对话框
            self.progress_dialog = self._create_progress_dialog(
                "批量写入", 
                "正在写入所有寄存器...", 
                self.write_all_total
            )
            
            # 分批处理 - all_registers已经是(addr, value)元组的列表
            batches = [all_registers[i:i + self.BATCH_SIZE]
                      for i in range(0, len(all_registers), self.BATCH_SIZE)]
            self.remaining_batches = iter(batches)
            
            # 连接SPI信号
            self._connect_write_signals()
            
            # 开始处理第一批
            try:
                first_batch = next(self.remaining_batches)
                self._process_write_batch(first_batch)
            except StopIteration:
                self._finish_write_all()
                
        except Exception as e:
            logger.error(f"启动批量写入时出错: {str(e)}")
            self._finish_write_all()
            QMessageBox.critical(
                self.main_window, 
                "错误", 
                f"启动批量写入失败: {str(e)}"
            )
            traceback.print_exc()
    
    def _check_batch_operation_status(self, operation_type):
        """检查批量操作状态"""
        if operation_type == 'read' and self.is_batch_reading:
            logger.warning("已经在进行批量读取，忽略此请求")
            return False
        elif operation_type == 'write' and self.is_batch_writing:
            logger.warning("已经在进行批量写入，忽略此请求")
            return False
        return True
    
    def _get_all_registers(self, with_values=False):
        """获取所有寄存器信息"""
        return self.main_window.register_service.get_all_registers(with_values)
    
    def _create_progress_dialog(self, title, label, total):
        """创建进度对话框"""
        # 确保文本使用正确的编码
        title_text = str(title) if title else "进度"
        label_text = str(label) if label else "正在处理..."
        cancel_text = "取消"

        progress = QProgressDialog(label_text, cancel_text, 0, total, self.main_window)
        progress.setWindowTitle(title_text)
        # 使用非模态对话框，避免影响主窗口布局
        progress.setWindowModality(Qt.NonModal)
        progress.setMinimumDuration(500)  # 500毫秒后才显示
        progress.setValue(0)

        # 确保对话框使用正确的字体
        if hasattr(self.main_window, 'font') and self.main_window.font():
            progress.setFont(self.main_window.font())

        # 显示对话框
        progress.show()

        # 使用QTimer延迟调整大小，确保对话框已完全显示后再调整
        from PyQt5.QtCore import QTimer
        def adjust_dialog_size():
            if progress and not progress.wasCanceled():
                # 设置对话框的最小宽度，确保标题能完整显示
                progress.setMinimumWidth(600)
                # 强制调整大小
                progress.resize(600, progress.height())
                # 重新设置对话框位置，确保居中显示
                if hasattr(self.main_window, 'geometry'):
                    main_rect = self.main_window.geometry()
                    dialog_rect = progress.geometry()
                    x = main_rect.x() + (main_rect.width() - 600) // 2
                    y = main_rect.y() + (main_rect.height() - dialog_rect.height()) // 2
                    progress.move(x, y)

        # 延迟100毫秒执行，确保对话框完全显示
        QTimer.singleShot(100, adjust_dialog_size)

        return progress
    
    def _connect_read_signals(self):
        """连接读取操作的信号"""
        if hasattr(self.main_window.spi_service, 'spi_operation_complete'):
            # 先断开可能存在的连接，避免重复连接
            try:
                self.main_window.spi_service.spi_operation_complete.disconnect(
                    self.update_read_all_progress
                )
                logger.debug("断开了之前的读取信号连接")
            except Exception:
                pass  # 如果没有连接则忽略异常

            # 重新连接信号
            self.main_window.spi_service.spi_operation_complete.connect(
                self.update_read_all_progress
            )
            logger.info("已连接批量读取操作信号到BatchOperationManager")
    
    def _connect_write_signals(self):
        """连接写入操作的信号"""
        if hasattr(self.main_window.spi_service, 'spi_operation_complete'):
            # 先断开可能存在的连接，避免重复连接
            try:
                self.main_window.spi_service.spi_operation_complete.disconnect(
                    self.update_write_all_progress
                )
                logger.debug("断开了之前的写入信号连接")
            except Exception:
                pass  # 如果没有连接则忽略异常

            # 重新连接信号
            self.main_window.spi_service.spi_operation_complete.connect(
                self.update_write_all_progress
            )
            logger.debug("已连接写入操作信号")
    
    def _disconnect_read_signals(self):
        """断开读取操作的信号"""
        if hasattr(self.main_window.spi_service, 'spi_operation_complete'):
            try:
                self.main_window.spi_service.spi_operation_complete.disconnect(
                    self.update_read_all_progress
                )
                logger.debug("成功断开读取操作信号")
            except Exception as e:
                logger.debug(f"断开读取信号时出现异常（可能未连接）: {str(e)}")
                pass
    
    def _disconnect_write_signals(self):
        """断开写入操作的信号"""
        if hasattr(self.main_window.spi_service, 'spi_operation_complete'):
            try:
                self.main_window.spi_service.spi_operation_complete.disconnect(
                    self.update_write_all_progress
                )
                logger.debug("成功断开写入操作信号")
            except Exception as e:
                logger.debug(f"断开写入信号时出现异常（可能未连接）: {str(e)}")
                pass
    
    def is_in_batch_operation(self):
        """检查是否正在进行批量操作"""
        return (self.is_batch_reading or 
                self.is_batch_writing or 
                self.is_batch_updating)
    
    def force_cancel_all_operations(self):
        """强制取消所有批量操作"""
        try:
            if self.is_batch_reading:
                logger.info("强制取消批量读取操作")
                self._finish_read_all()
                
            if self.is_batch_writing:
                logger.info("强制取消批量写入操作")
                self._finish_write_all()
                
            # 断开所有信号连接
            self._disconnect_read_signals()
            self._disconnect_write_signals()
            
            # 关闭进度对话框
            if self.progress_dialog:
                try:
                    self.progress_dialog.close()
                    self.progress_dialog = None
                except Exception:
                    pass
            
            # 重置状态
            self._reset_all_states()
            
            logger.info("已强制取消所有批量操作")
            
        except Exception as e:
            logger.error(f"强制取消批量操作时出错: {str(e)}")
            self._reset_all_states()
    
    def _reset_all_states(self):
        """重置所有状态"""
        self.is_batch_reading = False
        self.is_batch_writing = False
        self.is_batch_updating = False
        self.read_all_completed = 0
        self.read_all_total = 0
        self.write_all_completed = 0
        self.write_all_total = 0
        self.remaining_batches = None

    def _force_cleanup_before_start(self):
        """在开始新的批量操作前强制清理所有状态"""
        logger.debug("开始强制清理批量操作状态")

        # 断开所有信号连接
        self._disconnect_read_signals()
        self._disconnect_write_signals()

        # 关闭可能存在的进度对话框
        if self.progress_dialog:
            try:
                self.progress_dialog.close()
                self.progress_dialog.deleteLater()
                logger.debug("关闭了之前的进度对话框")
            except Exception as e:
                logger.debug(f"关闭进度对话框时出错: {str(e)}")
            finally:
                self.progress_dialog = None

        # 清空SPI操作队列
        if hasattr(self.main_window, 'spi_service') and self.main_window.spi_service:
            try:
                self.main_window.spi_service.clear_operation_queue()
                logger.debug("清空了SPI操作队列")
            except Exception as e:
                logger.debug(f"清空SPI操作队列时出错: {str(e)}")

        # 重置所有状态
        self._reset_all_states()

        # 确保按钮状态正确
        try:
            self.main_window.io_handler.toggle_buttons(True)
        except Exception as e:
            logger.debug(f"重置按钮状态时出错: {str(e)}")

        logger.debug("强制清理完成")

    def _process_read_batch(self, batch):
        """处理一批要读取的寄存器"""
        # 检查是否取消
        if not self.progress_dialog or self.progress_dialog.wasCanceled():
            logger.info("进度对话框已关闭或取消，结束批量读取")
            self._finish_read_all()
            return

        # 检查是否还在批量读取模式
        if not self.is_batch_reading:
            logger.warning("已不在批量读取模式，忽略批次处理")
            return

        # 检查批次是否有效
        if not batch:
            logger.warning("收到空批次，尝试完成批量读取")
            self._finish_read_all()
            return

        # 模拟模式下直接处理所有数据
        if self.main_window.simulation_mode:
            logger.debug(f"模拟模式下处理批次，共 {len(batch)} 个寄存器")
            for addr in batch:
                # 检查是否取消
                if not self.progress_dialog or self.progress_dialog.wasCanceled():
                    self._finish_read_all()
                    return

                # 读取寄存器值
                value = self.main_window.spi_service.read_register(addr)
                self.main_window.handle_spi_result(addr, value, True)
                # 更新进度计数
                self.read_all_completed += 1
                if self.progress_dialog:
                    self.progress_dialog.setValue(self.read_all_completed)

            # 如果全部完成
            if self.read_all_completed >= self.read_all_total:
                logger.info("模拟模式下，所有寄存器已读取完成")
                self._finish_read_all()
                return

            # 否则处理下一批
            try:
                next_batch = next(self.remaining_batches)
                logger.debug(f"模拟模式下，处理下一批，共 {len(next_batch)} 个寄存器")
                self._process_read_batch(next_batch)
            except StopIteration:
                logger.info("模拟模式下，没有更多批次，完成批量读取")
                self._finish_read_all()
            except Exception as e:
                logger.error(f"模拟模式下，处理下一批读取时出错: {str(e)}")
                self._finish_read_all()
        else:
            # 非模拟模式下，发送批次请求，等待回调处理
            try:
                logger.debug(f"开始处理批次中的 {len(batch)} 个寄存器")
                batch_start_count = self.read_all_completed

                # 设置批次超时 - 给每个操作更多时间
                batch_timeout = len(batch) * self.BATCH_TIMEOUT_MULTIPLIER * (self.OPERATION_TIMEOUT // 1000) + 10
                QTimer.singleShot(
                    batch_timeout * 1000,
                    lambda: self._check_batch_timeout(batch_start_count, len(batch))
                )

                for addr in batch:
                    # 确保仍在批量读取模式
                    if not self.is_batch_reading:
                        logger.warning("处理批次过程中发现已不在批量读取模式，中止处理")
                        return

                    # 检查是否取消
                    if not self.progress_dialog or self.progress_dialog.wasCanceled():
                        logger.info("处理批次过程中发现进度对话框已关闭或取消，中止处理")
                        self._finish_read_all()
                        return

                    # 读取实际值
                    normalized_addr = self.main_window._normalize_register_address(addr)
                    logger.debug(f"发送读取请求: {normalized_addr}")
                    self.main_window.spi_service.read_register(normalized_addr)

                logger.debug("批次中的所有寄存器读取请求已发送，等待回调")

                # 设置进度检查定时器 - 给批次更多时间完成
                initial_check_timeout = self.OPERATION_TIMEOUT * 2  # 首次检查延迟更长
                QTimer.singleShot(
                    initial_check_timeout,
                    lambda: self._check_batch_progress(batch_start_count, len(batch))
                )

            except Exception as e:
                logger.error(f"处理批次过程中出错: {str(e)}")
                self._finish_read_all()

    def _process_write_batch(self, batch):
        """处理一批要写入的寄存器"""
        for addr, value in batch:
            # 检查是否取消
            if not self.progress_dialog or self.progress_dialog.wasCanceled():
                self._finish_write_all()
                return

            # 写入寄存器值
            if self.main_window.simulation_mode:
                self._handle_simulation_write(addr, value)
                # 更新进度计数
                self.write_all_completed += 1
                if self.progress_dialog:
                    self.progress_dialog.setValue(self.write_all_completed)
            else:
                # 写入实际值
                normalized_addr = self.main_window._normalize_register_address(addr)
                self.main_window.spi_service.write_register(normalized_addr, value)

        # 如果是模拟模式，检查是否还有下一批
        if self.main_window.simulation_mode:
            # 如果全部完成
            if self.write_all_completed >= self.write_all_total:
                self._finish_write_all()
                return

            # 否则处理下一批
            try:
                next_batch = next(self.remaining_batches)
                self._process_write_batch(next_batch)
            except StopIteration:
                self._finish_write_all()
            except Exception as e:
                logger.error(f"处理下一批写入时出错: {str(e)}")
                self._finish_write_all()

    def update_read_all_progress(self, addr, value, is_read):
        """更新读取所有寄存器的进度(仅适用于非模拟模式)"""
        try:
            # 如果在模拟模式下不应该被调用
            if self.main_window.simulation_mode:
                logger.warning("在模拟模式下调用了update_read_all_progress方法，这是不应该发生的")
                return

            # 如果已经不在批量读取模式，就忽略此次回调
            if not self.is_batch_reading:
                logger.warning(f"收到SPI回调，但不在批量读取模式，忽略此次回调: {addr}")
                return

            # 检查进度对话框是否还存在且有效
            if not self.progress_dialog:
                logger.warning(f"收到SPI回调，但进度对话框不存在，忽略此次回调: {addr}")
                self._finish_read_all()
                return

            # 只处理读取操作的回调
            if not is_read:
                logger.debug(f"忽略写入操作回调: {addr}")
                return

            # 增加完成计数
            self.read_all_completed += 1

            # 减少日志输出频率，只在关键点记录
            if self.read_all_completed % self.UI_UPDATE_INTERVAL == 0 or self.read_all_completed == self.read_all_total:
                logger.info(f"📊 读取进度更新: {self.read_all_completed}/{self.read_all_total}")

            # 发出性能监控进度信号
            self.batch_read_progress.emit(self.read_all_completed, self.read_all_total)

            # 检查是否超出预期范围
            if self.read_all_completed > self.read_all_total:
                logger.error(f"❌ 读取进度超出预期范围! 完成数: {self.read_all_completed}, 总数: {self.read_all_total}")
                self._finish_read_all()
                return

            # 减少进度条更新频率，提高性能
            if self.progress_dialog and (self.read_all_completed % self.UI_UPDATE_INTERVAL == 0 or self.read_all_completed == self.read_all_total):
                self.progress_dialog.setValue(self.read_all_completed)

                # 如果所有操作已完成，则完成操作
                if self.read_all_completed >= self.read_all_total:
                    logger.info(f"所有读取操作已完成: {self.read_all_completed}/{self.read_all_total}")
                    self._finish_read_all()
                    return

                # 如果进度对话框被取消，则停止处理
                if self.progress_dialog.wasCanceled():
                    logger.info("用户取消了批量读取操作")
                    self._finish_read_all()
                    return

                # 检查是否需要处理下一批数据
                current_batch_start = ((self.read_all_completed - 1) // self.BATCH_SIZE) * self.BATCH_SIZE
                current_batch_end = min(current_batch_start + self.BATCH_SIZE, self.read_all_total)

                logger.debug(f"🔍 批次检查: 已完成={self.read_all_completed}, 当前批次开始={current_batch_start}, 当前批次结束={current_batch_end}")

                # 当完成的操作数等于当前批次的结束索引时，表示当前批次已完成
                if self.read_all_completed == current_batch_end:
                    logger.info(f"✅ 当前批次已完成，尝试处理下一批 (已完成: {self.read_all_completed})")
                    try:
                        # 尝试获取下一批数据
                        next_batch = next(self.remaining_batches)
                        logger.info(f"🔄 获取到下一批数据，包含 {len(next_batch)} 个寄存器")

                        # 先检查是否应该继续
                        if not self.progress_dialog or self.progress_dialog.wasCanceled():
                            logger.info("对话框已关闭或取消，停止批量读取")
                            self._finish_read_all()
                            return

                        # 处理下一批数据
                        logger.debug(f"开始处理下一批，共 {len(next_batch)} 个寄存器")
                        self._process_read_batch(next_batch)
                    except StopIteration:
                        # 如果没有更多批次，强制调用完成方法
                        logger.info(f"🏁 没有更多批次，主动调用完成方法 (总共完成: {self.read_all_completed}/{self.read_all_total})")
                        self._finish_read_all()
                    except Exception as e:
                        logger.error(f"❌ 处理下一批读取时出错: {str(e)}")
                        self._finish_read_all()
                else:
                    logger.debug(f"⏳ 当前批次未完成，继续等待 (已完成: {self.read_all_completed}, 批次结束: {current_batch_end})")
        except Exception as e:
            logger.error(f"更新读取进度时出错: {str(e)}")
            # 出现异常时，确保完成批量读取
            self._finish_read_all()

    def update_write_all_progress(self, addr, value, is_read):
        """更新写入所有寄存器的进度(仅适用于非模拟模式)"""
        try:
            # 如果在模拟模式下不应该被调用
            if self.main_window.simulation_mode:
                logger.warning("在模拟模式下调用了update_write_all_progress方法，这是不应该发生的")
                return

            # 如果已经不在批量写入模式，就忽略此次回调
            if not self.is_batch_writing:
                logger.warning(f"收到SPI回调，但不在批量写入模式，忽略此次回调: {addr}")
                return

            # 检查进度对话框是否还存在且有效
            if not self.progress_dialog:
                logger.warning(f"收到SPI回调，但进度对话框不存在，忽略此次回调: {addr}")
                self._finish_write_all()
                return

            # 增加完成计数
            self.write_all_completed += 1
            logger.debug(f"批量写入进度更新: {self.write_all_completed}/{self.write_all_total} (地址: {addr})")

            # 发出性能监控进度信号
            self.batch_write_progress.emit(self.write_all_completed, self.write_all_total)

            # 更新进度条
            if self.progress_dialog:
                try:
                    self.progress_dialog.setValue(self.write_all_completed)
                except Exception as e:
                    logger.warning(f"更新进度条时出错: {str(e)}")

                # 如果所有操作已完成，则完成操作
                if self.write_all_completed >= self.write_all_total:
                    logger.info("所有写入操作已完成，开始清理")
                    self._finish_write_all()
                    return

                # 如果当前批次所有寄存器都已写入，处理下一批
                if self.write_all_completed % self.BATCH_SIZE == 0:
                    try:
                        next_batch = next(self.remaining_batches)
                        logger.debug(f"开始处理下一批写入，共 {len(next_batch)} 个寄存器")
                        self._process_write_batch(next_batch)
                    except StopIteration:
                        # 如果没有更多批次，等待剩余的操作完成
                        logger.debug("没有更多批次，等待剩余操作完成")
                        pass
                    except Exception as e:
                        logger.error(f"处理下一批写入时出错: {str(e)}")
                        self._finish_write_all()
        except Exception as e:
            logger.error(f"更新写入进度时出错: {str(e)}")
            # 出现异常时，确保完成批量写入
            self._finish_write_all()

    def _check_batch_timeout(self, batch_start_count, expected_progress):
        """检查批次是否超时，如果整个批次超时则强制完成"""
        # 如果已经不在批量读取模式，直接返回
        if not self.is_batch_reading:
            return

        # 检查进度是否有足够变化
        current_progress = self.read_all_completed - batch_start_count
        if current_progress < expected_progress:
            # 如果批次没有完全处理完成，可能是系统卡住了，强制完成操作
            logger.warning(f"批次处理超时：预期处理 {expected_progress} 个操作，实际只完成 {current_progress} 个，强制完成")
            self._finish_read_all()

    def _check_batch_progress(self, batch_start_count, expected_progress, retry_count=0):
        """检查批处理进度，如果没有进展则强制完成"""
        # 如果已经不在批量读取模式，直接返回
        if not self.is_batch_reading:
            return

        current_progress = self.read_all_completed - batch_start_count
        max_retries = 3  # 最大重试次数

        # 检查进度是否有变化
        if current_progress <= 0:
            # 如果进度没有变化
            if retry_count >= max_retries:
                logger.warning("批次处理超时：未收到任何回调响应，强制完成操作")
                self._finish_read_all()
            else:
                logger.warning(f"批次处理暂无进展，重试 {retry_count + 1}/{max_retries}")
                # 等待更长时间再次检查
                QTimer.singleShot(self.OPERATION_TIMEOUT,
                                lambda: self._check_batch_progress(batch_start_count, expected_progress, retry_count + 1))
        elif current_progress < expected_progress:
            # 如果进度有一些变化但未完成，继续等待
            logger.info(f"批次处理进行中：已完成 {current_progress}/{expected_progress}，继续等待")
            # 重置重试计数，因为有进展
            QTimer.singleShot(self.OPERATION_TIMEOUT,
                            lambda: self._check_batch_progress(batch_start_count, expected_progress, 0))
        # 如果 current_progress >= expected_progress，说明批次已完成，不需要做任何事情

    def _finish_read_all(self):
        """完成所有寄存器读取操作"""
        # 立即设置标志为False，防止重入
        if not self.is_batch_reading:
            return

        # 标记为不再进行批量读取
        self.is_batch_reading = False
        self.main_window.is_batch_reading = False  # 清除主窗口状态
        logger.info("开始完成批量读取操作的清理工作")

        # 断开信号连接
        self._disconnect_read_signals()

        # 关闭进度对话框
        if self.progress_dialog:
            try:
                # 确保进度条达到最大值
                self.progress_dialog.setValue(self.progress_dialog.maximum())
                self.progress_dialog.close()
                logger.debug("成功关闭进度对话框")
            except Exception as e:
                logger.warning(f"关闭进度对话框时出错: {str(e)}")
            finally:
                # 确保引用被清除
                progress_dialog_ref = self.progress_dialog
                self.progress_dialog = None
                if progress_dialog_ref:
                    progress_dialog_ref.deleteLater()

        # 恢复UI更新，防止布局抖动
        try:
            self._resume_ui_updates()
        except Exception as e:
            logger.warning(f"恢复UI更新时出错: {str(e)}")

        # 重新启用按钮
        self.main_window.io_handler.toggle_buttons(True)
        logger.info("批量读取操作完成，UI按钮已重新启用")

        # 清空SPI操作队列，确保没有残留操作
        if hasattr(self.main_window, 'spi_service') and self.main_window.spi_service:
            try:
                self.main_window.spi_service.clear_operation_queue()
                logger.debug("批量读取完成后已清空SPI操作队列")
            except Exception as e:
                logger.warning(f"清空SPI操作队列时出错: {str(e)}")

        # 清理迭代器
        self.remaining_batches = iter([])

        # 显示完成消息
        if hasattr(self, 'read_all_completed') and hasattr(self, 'read_all_total'):
            if self.read_all_completed >= self.read_all_total:
                message = f"成功读取所有寄存器：共 {self.read_all_completed} 个"
                self.main_window.show_status_message(message, 3000)
                logger.info(message)
            else:
                message = f"读取操作被取消：已读取 {self.read_all_completed} 个，共 {self.read_all_total} 个"
                self.main_window.show_status_message(message, 3000)
                logger.info(message)
        else:
            self.main_window.show_status_message("读取操作已完成", 3000)
            logger.info("读取操作已完成")

        # 清理变量
        self.read_all_completed = 0
        self.read_all_total = 0
        self.remaining_batches = None

        # 批量操作完成后，确保所有寄存器变量都已更新，然后刷新UI
        self._update_all_registers_and_refresh_ui()

        # 强制进行垃圾回收，释放资源
        import gc
        gc.collect()

        logger.info("批量读取操作清理工作完成")

        # 计算性能数据并发送性能监控完成信号
        if hasattr(self, 'batch_read_start_time'):
            duration = time.time() - self.batch_read_start_time
            success = self.read_all_completed >= self.read_all_total
            self.batch_read_finished.emit(success, duration, self.read_all_completed)
            logger.info(f"批量读取性能数据: 耗时 {duration:.3f}秒, 成功 {success}, 完成数 {self.read_all_completed}")

        # 发送操作完成信号
        self.operation_completed.emit("read")

    def _finish_write_all(self):
        """完成所有寄存器写入操作"""
        # 立即设置标志为False，防止重入
        if not self.is_batch_writing:
            return

        # 标记为不再进行批量写入
        self.is_batch_writing = False
        self.main_window.is_batch_writing = False  # 清除主窗口状态
        logger.info("开始完成批量写入操作的清理工作")

        # 断开信号连接
        self._disconnect_write_signals()

        # 关闭进度对话框
        if self.progress_dialog:
            try:
                # 确保进度条达到最大值
                self.progress_dialog.setValue(self.progress_dialog.maximum())
                self.progress_dialog.close()
                logger.debug("成功关闭进度对话框")
            except Exception as e:
                logger.warning(f"关闭进度对话框时出错: {str(e)}")
            finally:
                # 确保引用被清除
                progress_dialog_ref = self.progress_dialog
                self.progress_dialog = None
                if progress_dialog_ref:
                    progress_dialog_ref.deleteLater()

        # 恢复UI更新，防止布局抖动
        try:
            self._resume_ui_updates()
        except Exception as e:
            logger.warning(f"恢复UI更新时出错: {str(e)}")

        # 重新启用按钮
        try:
            self.main_window.io_handler.toggle_buttons(True)
        except Exception as e:
            logger.warning(f"重新启用按钮时出错: {str(e)}")

        # 清空SPI操作队列，确保没有残留操作
        if hasattr(self.main_window, 'spi_service') and self.main_window.spi_service:
            try:
                self.main_window.spi_service.clear_operation_queue()
                logger.debug("批量写入完成后已清空SPI操作队列")
            except Exception as e:
                logger.warning(f"清空SPI操作队列时出错: {str(e)}")

        # 显示完成消息
        try:
            if hasattr(self, 'write_all_completed') and hasattr(self, 'write_all_total'):
                if self.write_all_completed == self.write_all_total:
                    message = f"成功写入所有寄存器：共 {self.write_all_completed} 个"
                else:
                    message = f"写入操作被取消：已写入 {self.write_all_completed} 个，共 {self.write_all_total} 个"
            else:
                message = "写入操作已完成"

            self.main_window.show_status_message(message, 3000)
            logger.info(message)
        except Exception as e:
            logger.warning(f"显示完成消息时出错: {str(e)}")

        # 批量操作完成后，刷新当前选中的寄存器显示
        try:
            self._refresh_current_register_after_batch()
        except Exception as e:
            logger.warning(f"刷新当前寄存器显示时出错: {str(e)}")

        # 清理变量
        try:
            self.write_all_completed = 0
            self.write_all_total = 0
            self.remaining_batches = None
        except Exception as e:
            logger.warning(f"清理变量时出错: {str(e)}")

        # 强制进行垃圾回收，释放资源
        try:
            import gc
            gc.collect()
            logger.debug("强制垃圾回收完成")
        except Exception as e:
            logger.warning(f"强制垃圾回收时出错: {str(e)}")

        # 计算性能数据并发送性能监控完成信号
        try:
            if hasattr(self, 'batch_write_start_time'):
                duration = time.time() - self.batch_write_start_time
                success = self.write_all_completed >= self.write_all_total
                self.batch_write_finished.emit(success, duration, self.write_all_completed)
                logger.info(f"批量写入性能数据: 耗时 {duration:.3f}秒, 成功 {success}, 完成数 {self.write_all_completed}")
        except Exception as e:
            logger.warning(f"发送性能监控信号时出错: {str(e)}")

        # 发送操作完成信号
        try:
            self.operation_completed.emit("write")
        except Exception as e:
            logger.warning(f"发送操作完成信号时出错: {str(e)}")

        logger.info("批量写入操作清理工作完成")

    def _update_all_registers_and_refresh_ui(self):
        """批量操作完成后，确保所有寄存器变量都已更新，然后刷新UI"""
        try:
            logger.info("批量读取完成，开始更新所有寄存器变量并刷新UI")

            # 1. 确保所有寄存器变量都已更新（这一步在读取过程中已经完成）
            # 寄存器值在每次读取时都会通过 register_manager.set_register_value() 更新
            logger.info("所有寄存器变量已在读取过程中更新完成")

            # 2. 刷新树视图中的所有寄存器显示（通过更新每个寄存器）
            if hasattr(self.main_window, 'tree_handler') and hasattr(self.main_window, 'register_manager'):
                logger.info("开始刷新树视图中的所有寄存器显示")
                try:
                    # 获取所有寄存器的最新值并更新树视图
                    all_register_values = self.main_window.register_manager.get_all_register_values()
                    for addr, value in all_register_values.items():
                        if hasattr(self.main_window.tree_handler, 'update_register_value'):
                            self.main_window.tree_handler.update_register_value(addr, value)
                    logger.info(f"成功刷新了 {len(all_register_values)} 个寄存器的树视图显示")
                except Exception as tree_error:
                    logger.warning(f"刷新树视图时出错: {str(tree_error)}")

            # 3. 如果有当前选中的寄存器，刷新其详细显示
            if hasattr(self.main_window, 'selected_register_addr') and self.main_window.selected_register_addr:
                current_addr = self.main_window.selected_register_addr
                logger.info(f"刷新当前选中寄存器的详细显示: {current_addr}")

                # 获取最新值
                current_value = self.main_window.register_manager.get_register_value(current_addr)  # noqa: F841

                # 使用现有的刷新方法
                if hasattr(self.main_window, 'refresh_view'):
                    self.main_window.refresh_view()
                    logger.info(f"批量读取完成后成功刷新了当前寄存器 {current_addr} 的显示")
                else:
                    logger.warning("主窗口没有refresh_view方法")
            else:
                logger.info("批量读取完成，但没有选中的寄存器需要刷新详细显示")

            logger.info("批量读取完成后UI刷新完成")

        except Exception as e:
            logger.error(f"批量读取完成后刷新UI时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _refresh_current_register_after_batch(self):
        """批量操作完成后刷新当前选中的寄存器显示（保留兼容性）"""
        if hasattr(self.main_window, '_refresh_current_register_after_batch'):
            self.main_window._refresh_current_register_after_batch()

    def _handle_simulation_write(self, addr, value):
        """处理模拟模式下的写入"""
        if hasattr(self.main_window, '_handle_simulation_write'):
            self.main_window._handle_simulation_write(addr, value)

    def _suspend_ui_updates(self):
        """暂停UI更新，防止批量操作期间的布局抖动"""
        try:
            self._ui_updates_suspended = True

            # 完全禁用主窗口的更新，防止布局重新计算
            if hasattr(self.main_window, 'setUpdatesEnabled'):
                self.main_window.setUpdatesEnabled(False)
                logger.debug("已禁用主窗口更新")

            # 禁用中央控件的布局更新
            if hasattr(self.main_window, 'centralWidget'):
                central_widget = self.main_window.centralWidget()
                if central_widget:
                    central_widget.setUpdatesEnabled(False)
                    logger.debug("已禁用中央控件更新")

            logger.debug("UI更新已完全暂停，防止批量操作期间的布局抖动")

        except Exception as e:
            logger.warning(f"暂停UI更新时出错: {str(e)}")

    def _resume_ui_updates(self):
        """恢复UI更新并还原布局"""
        try:
            self._ui_updates_suspended = False

            # 恢复中央控件的更新
            if hasattr(self.main_window, 'centralWidget'):
                central_widget = self.main_window.centralWidget()
                if central_widget:
                    central_widget.setUpdatesEnabled(True)
                    central_widget.update()
                    logger.debug("已恢复中央控件更新")

            # 恢复主窗口的更新
            if hasattr(self.main_window, 'setUpdatesEnabled'):
                self.main_window.setUpdatesEnabled(True)
                self.main_window.update()
                logger.debug("已恢复主窗口更新")

            logger.debug("UI更新已完全恢复")

        except Exception as e:
            logger.warning(f"恢复UI更新时出错: {str(e)}")
        finally:
            self._layout_widgets = None
            self._original_layout_sizes = None
