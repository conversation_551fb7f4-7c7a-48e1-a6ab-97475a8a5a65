# -*- coding: utf-8 -*-

from PyQt5.QtCore import QObject, pyqtSignal
from utils.Log import get_module_logger

logger = get_module_logger(__name__)

class RegisterUpdateBus(QObject):
    """寄存器更新总线，用于在不同组件间传递寄存器更新事件"""
    
    # 单例模式
    _instance = None
    
    # 寄存器更新信号
    register_updated = pyqtSignal(str, int)
    
    # 时钟源选择信号：源名称, 频率值, 分频比
    clock_source_selected = pyqtSignal(str, float, int)
    
    # 模式变化信号：模式名称
    mode_changed = pyqtSignal(str)
    
    # 多寄存器更新信号：寄存器地址列表
    multiple_registers_updated = pyqtSignal(list)
    
    @classmethod
    def instance(cls):
        """获取单例实例"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def __init__(self):
        """确保只有一个实例"""
        super().__init__()
        if RegisterUpdateBus._instance is not None:
            raise RuntimeError("RegisterUpdateBus已经实例化，请使用instance()方法获取实例")
        RegisterUpdateBus._instance = self

        # CLKin_SEL_MANUAL[1:0] (bits 5:4 of 0x57) default is "01" from register.json
        # This corresponds to index 1 in CLOCK_SOURCES array in ClkinControlHandler
        # 0: CLKin0, 1: CLKin1, 2: CLKin2, 3: Holdover
        initial_clkin_sel_manual_value_from_register = 1  # Simulating read from register default "01"

        clock_sources_map = {
            0: "CLKin0",
            1: "CLKin1",
            2: "CLKin2",
            3: "Holdover"
        }
        default_source_raw = clock_sources_map.get(initial_clkin_sel_manual_value_from_register, "CLKin0")

        # Normalize to ClkInX format for internal consistency
        if default_source_raw.startswith("CLK"):
            self._current_clock_source = "ClkIn" + default_source_raw[5:]
        else:
            self._current_clock_source = default_source_raw  # For "Holdover"

        logger.info(f"RegisterUpdateBus: Initial clock source set based on register default (0x57, CLKin_SEL_MANUAL): {self._current_clock_source}")

        # 存储各时钟源的频率和分频比
        self._clock_frequencies = {
            "ClkIn0": 122.88,  # 默认值
            "ClkIn1": 122.88,  # 默认值
            "ClkIn2": 122.88,
            "ClkIn3": 0,
            "Holdover": 0
        }
        
        self._clock_dividers = {
            "ClkIn0": 120,  # 默认值
            "ClkIn1": 120,  # 默认值
            "ClkIn2": 120,
            "ClkIn3": 1,
            "Holdover": 1
        }
        
        # 连接自身信号到日志函数，用于调试
        self.register_updated.connect(self.log_register_update)
        self.clock_source_selected.connect(self.log_clock_source_selection)
        self.multiple_registers_updated.connect(self.log_multiple_registers_update)
        
    def log_register_update(self, reg_addr, reg_value):
        """记录寄存器更新事件"""
        # 确保地址是字符串格式
        if not isinstance(reg_addr, str):
            reg_addr = f"0x{reg_addr:X}"

        # 确保值是整数
        if isinstance(reg_value, str):
            try:
                if reg_value.startswith("0x"):
                    reg_value = int(reg_value, 16)
                else:
                    reg_value = int(reg_value)
            except ValueError:
                logger.warning(f"无法将寄存器值 '{reg_value}' 转换为整数，保持原值")
                # 如果转换失败，直接记录字符串值
                logger.info(f"寄存器总线收到更新: 地址={reg_addr}, 值={reg_value}")
                return

        # 只有当reg_value是整数时才使用X格式
        if isinstance(reg_value, int):
            logger.info(f"寄存器总线收到更新: 地址={reg_addr}, 值=0x{reg_value:X}")
        else:
            logger.info(f"寄存器总线收到更新: 地址={reg_addr}, 值={reg_value}")
    
    def log_clock_source_selection(self, source_name, frequency, divider):
        """记录时钟源选择事件，并更新内部存储的时钟源配置"""
        logger.info(f"时钟源选择: 源={source_name}, 频率={frequency}MHz, 分频比={divider}")
        
        # 规范化时钟源名称
        normalized_source = source_name
        if normalized_source.startswith("CLK"):
            # 将CLKinX规范为ClkInX
            if len(normalized_source) > 5:
                normalized_source = "ClkIn" + normalized_source[5:]
        elif normalized_source.startswith("Clk"):
            # 确保ClkInX格式一致
            pass
        else:
            # 其他类型的源名称保持不变
            pass
            
        # 更新当前选中的时钟源
        self._current_clock_source = normalized_source
        
        # 同时更新内部存储的频率和分频比
        self.set_clock_frequency(normalized_source, frequency)
        self.set_clock_divider(normalized_source, divider)
        
        # 也更新可能的其他格式的名称映射
        if normalized_source.startswith("ClkIn"):
            alt_name = "CLKin" + normalized_source[5:]
            self.set_clock_frequency(alt_name, frequency)
            self.set_clock_divider(alt_name, divider)
            
        logger.info(f"已更新时钟源配置: {normalized_source}, 频率={frequency}MHz, 分频比={divider}")
    
    def get_current_clock_source(self):
        """获取当前选中的时钟源（标准化格式）"""
        # 确保返回的是标准化格式（ClkInX）
        source = self._current_clock_source
        if source and source.startswith("CLK"):
            # 将CLKinX规范为ClkInX
            if len(source) > 5:
                return "ClkIn" + source[5:]
        return source
    
    def normalize_source_name(self, source_name):
        """规范化时钟源名称
        
        Args:
            source_name: 原始时钟源名称
            
        Returns:
            str: 规范化后的时钟源名称（ClkInX格式）
        """
        if not source_name:
            return "ClkIn0"  # 默认值
            
        # 规范化时钟源名称
        if source_name.startswith("CLK"):
            # 将CLKinX规范为ClkInX
            if len(source_name) > 5:
                return "ClkIn" + source_name[5:]
        return source_name
            
    def get_clock_frequency(self, source_name):
        """获取指定时钟源的频率
        
        Args:
            source_name: 时钟源名称
            
        Returns:
            float: 时钟源频率，如果未找到则返回0
        """
        # 尝试直接获取
        freq = self._clock_frequencies.get(source_name, None)
        if freq is not None:
            return freq
            
        # 尝试使用规范化名称获取
        norm_name = self.normalize_source_name(source_name)
        freq = self._clock_frequencies.get(norm_name, None)
        if freq is not None:
            return freq
            
        # 尝试使用替代格式获取
        if norm_name.startswith("ClkIn"):
            alt_name = "CLKin" + norm_name[5:]
            freq = self._clock_frequencies.get(alt_name, None)
            if freq is not None:
                return freq
                
        # 没有找到，返回默认值
        logger.warning(f"未找到时钟源 {source_name} 的频率配置，返回默认值0")
        return 0
        
    def get_clock_divider(self, source_name):
        """获取指定时钟源的分频比
        
        Args:
            source_name: 时钟源名称
            
        Returns:
            int: 时钟源分频比，如果未找到则返回1
        """
        # 尝试直接获取
        divider = self._clock_dividers.get(source_name, None)
        if divider is not None:
            return divider
            
        # 尝试使用规范化名称获取
        norm_name = self.normalize_source_name(source_name)
        divider = self._clock_dividers.get(norm_name, None)
        if divider is not None:
            return divider
            
        # 尝试使用替代格式获取
        if norm_name.startswith("ClkIn"):
            alt_name = "CLKin" + norm_name[5:]
            divider = self._clock_dividers.get(alt_name, None)
            if divider is not None:
                return divider
                
        # 没有找到，返回默认值
        logger.warning(f"未找到时钟源 {source_name} 的分频比配置，返回默认值1")
        return 1
        
    def set_clock_frequency(self, source_name, frequency):
        """设置指定时钟源的频率
        
        Args:
            source_name: 时钟源名称
            frequency: 时钟源频率
            
        Returns:
            bool: 设置是否成功
        """
        # 规范化时钟源名称
        norm_name = self.normalize_source_name(source_name)
        
        try:
            # 设置规范化名称的频率
            self._clock_frequencies[norm_name] = float(frequency)
            
            # 如果是ClkInX格式，同时设置CLKinX格式的频率
            if norm_name.startswith("ClkIn"):
                alt_name = "CLKin" + norm_name[5:]
                self._clock_frequencies[alt_name] = float(frequency)
                
            logger.info(f"设置时钟源 {norm_name} 的频率为 {frequency}MHz")
            return True
        except (ValueError, TypeError):
            logger.warning(f"设置时钟源频率失败: 无效的频率值 {frequency}")
            return False
        
    def set_clock_divider(self, source_name, divider):
        """设置指定时钟源的分频比
        
        Args:
            source_name: 时钟源名称
            divider: 时钟源分频比
            
        Returns:
            bool: 设置是否成功
        """
        # 规范化时钟源名称
        norm_name = self.normalize_source_name(source_name)
        
        try:
            # 设置规范化名称的分频比
            self._clock_dividers[norm_name] = int(divider)
            
            # 如果是ClkInX格式，同时设置CLKinX格式的分频比
            if norm_name.startswith("ClkIn"):
                alt_name = "CLKin" + norm_name[5:]
                self._clock_dividers[alt_name] = int(divider)
                
            logger.info(f"设置时钟源 {norm_name} 的分频比为 {divider}")
            return True
        except (ValueError, TypeError):
            logger.warning(f"设置时钟源分频比失败: 无效的分频比值 {divider}")
            return False
    
    def emit_register_updated(self, reg_addr, reg_value):
        """发送寄存器更新信号的安全方法"""
        # 标准化地址格式
        if isinstance(reg_addr, int):
            reg_addr = f"0x{reg_addr:X}"
        
        # 确保值是整数
        if isinstance(reg_value, str):
            try:
                if reg_value.startswith("0x"):
                    reg_value = int(reg_value, 16)
                else:
                    reg_value = int(reg_value)
            except ValueError:
                logger.warning(f"无法将寄存器值 '{reg_value}' 转换为整数，尝试使用默认值0")
                reg_value = 0
                
        # 发送信号
        logger.info(f"寄存器总线发送更新: 地址={reg_addr}, 值=0x{reg_value:X}")
        self.register_updated.emit(reg_addr, reg_value)
        
    def emit_clock_source_selected(self, source_name, frequency, divider=1):
        """发送时钟源选择信号的安全方法"""
        # 确保频率是浮点数
        try:
            frequency = float(frequency)
        except (ValueError, TypeError):
            logger.warning(f"无法将频率值 '{frequency}' 转换为浮点数，使用默认值0.0")
            frequency = 0.0
            
        # 确保分频比是整数
        try:
            divider = int(divider)
        except (ValueError, TypeError):
            logger.warning(f"无法将分频比 '{divider}' 转换为整数，使用默认值1")
            divider = 1
            
        # 发送信号
        logger.info(f"发送时钟源选择: 源={source_name}, 频率={frequency}MHz, 分频比={divider}")
        self.clock_source_selected.emit(source_name, frequency, divider)
    
    def log_multiple_registers_update(self, reg_addr_list):
        """记录多寄存器更新事件"""
        logger.info(f"寄存器总线收到多寄存器更新通知: {', '.join(reg_addr_list)}")
        
    def emit_multiple_registers_updated(self, reg_addr_list):
        """发送多寄存器更新信号的安全方法"""
        if not isinstance(reg_addr_list, list):
            logger.warning(f"寄存器地址列表必须为list类型，而不是 {type(reg_addr_list)}")
            return
            
        # 标准化地址格式
        formatted_addresses = []
        for addr in reg_addr_list:
            if isinstance(addr, int):
                formatted_addresses.append(f"0x{addr:X}")
            else:
                formatted_addresses.append(str(addr))
                
        # 发送信号
        logger.info(f"寄存器总线发送多寄存器更新: {', '.join(formatted_addresses)}")
        self.multiple_registers_updated.emit(formatted_addresses)