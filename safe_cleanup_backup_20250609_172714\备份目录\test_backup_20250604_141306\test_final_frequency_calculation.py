#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终频率计算测试
验证现代化时钟输出处理器的频率计算功能
"""

import sys
import os
import json
from PyQt5.QtWidgets import QApplication

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ui.handlers.ModernClkOutputsHandler import ModernClkOutputsHandler
from core.services.register.RegisterManager import RegisterManager
from utils.Log import logger

def test_final_frequency_calculation():
    """最终频率计算测试"""
    print("=" * 60)
    print("最终频率计算测试")
    print("=" * 60)
    
    try:
        # 创建QApplication
        app = QApplication(sys.argv)
        
        print("1. 加载寄存器配置...")
        config_path = os.path.join('lib', 'register.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        print("2. 创建RegisterManager...")
        register_manager = RegisterManager(registers_config)
        
        print("3. 创建现代化时钟输出处理器...")
        modern_handler = ModernClkOutputsHandler(None, register_manager)
        
        print("4. 验证初始化后的频率计算...")
        
        # 检查VCO频率
        vco_freq = modern_handler.fvco
        print(f"   VCO频率: {vco_freq} MHz")
        
        # 检查所有输出频率
        expected_frequencies = {
            0: vco_freq / 2,   # 1474.56
            1: vco_freq / 2,   # 1474.56
            2: vco_freq / 4,   # 737.28
            3: vco_freq / 4,   # 737.28
            4: vco_freq / 8,   # 368.64
            5: vco_freq / 8,   # 368.64
            6: vco_freq / 8,   # 368.64
            7: vco_freq / 8,   # 368.64
            8: vco_freq / 8,   # 368.64
            9: vco_freq / 8,   # 368.64
            10: vco_freq / 8,  # 368.64
            11: vco_freq / 8,  # 368.64
            12: vco_freq / 2,  # 1474.56
            13: vco_freq / 2,  # 1474.56
        }
        
        all_correct = True
        for output_num in range(14):
            output_attr = f"lineEditFout{output_num}Output"
            if hasattr(modern_handler.ui, output_attr):
                output_widget = getattr(modern_handler.ui, output_attr)
                displayed_freq = float(output_widget.text())
                expected_freq = expected_frequencies[output_num]
                
                is_correct = abs(displayed_freq - expected_freq) < 0.01
                status = "✅" if is_correct else "❌"
                
                print(f"   输出{output_num:2d}: {displayed_freq:8.2f} MHz (期望: {expected_freq:8.2f}) {status}")
                
                if not is_correct:
                    all_correct = False
        
        print("\n5. 测试VCO频率变化...")
        
        # 测试不同的VCO频率
        test_vco_frequencies = [3000.0, 2500.0, 3500.0]
        
        for test_vco in test_vco_frequencies:
            print(f"\n   设置VCO频率为 {test_vco} MHz...")
            modern_handler.ui.lineEditFvco.setText(str(test_vco))
            app.processEvents()
            
            # 检查前4个输出
            for output_num in range(4):
                output_attr = f"lineEditFout{output_num}Output"
                if hasattr(modern_handler.ui, output_attr):
                    output_widget = getattr(modern_handler.ui, output_attr)
                    displayed_freq = float(output_widget.text())
                    
                    # 计算期望频率
                    divider_value = modern_handler._get_divider_value_for_output(output_num)
                    expected_freq = test_vco / divider_value
                    
                    is_correct = abs(displayed_freq - expected_freq) < 0.01
                    status = "✅" if is_correct else "❌"
                    
                    print(f"     输出{output_num}: {displayed_freq:8.2f} MHz (期望: {expected_freq:8.2f}, 分频: {divider_value}) {status}")
        
        print("\n6. 测试分频器变化...")
        
        # 恢复原始VCO频率
        modern_handler.ui.lineEditFvco.setText("2949.12")
        app.processEvents()
        
        # 修改第一个分频器
        if hasattr(modern_handler.ui, "DCLK0_1DIV"):
            print("   修改DCLK0_1DIV从2到4...")
            modern_handler.ui.DCLK0_1DIV.setValue(4)
            app.processEvents()
            
            # 检查输出0和1的频率
            for output_num in [0, 1]:
                output_attr = f"lineEditFout{output_num}Output"
                if hasattr(modern_handler.ui, output_attr):
                    output_widget = getattr(modern_handler.ui, output_attr)
                    displayed_freq = float(output_widget.text())
                    expected_freq = 2949.12 / 4  # 737.28
                    
                    is_correct = abs(displayed_freq - expected_freq) < 0.01
                    status = "✅" if is_correct else "❌"
                    
                    print(f"     输出{output_num}: {displayed_freq:8.2f} MHz (期望: {expected_freq:8.2f}) {status}")
        
        print("\n7. 获取当前状态...")
        status = modern_handler.get_current_status()
        print(f"   VCO频率: {status.get('vco_frequency', 'N/A')} MHz")
        print(f"   分频器状态: {status.get('divider_values', {})}")
        print(f"   SRCMUX状态: {status.get('srcmux_states', {})}")
        
        print("\n" + "=" * 60)
        if all_correct:
            print("🎉 所有测试通过！频率计算功能正常工作！")
        else:
            print("❌ 部分测试失败，需要进一步检查")
        print("=" * 60)
        
        return all_correct
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_final_frequency_calculation()
