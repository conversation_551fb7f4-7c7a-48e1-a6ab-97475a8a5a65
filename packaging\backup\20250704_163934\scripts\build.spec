# -*- mode: python ; coding: utf-8 -*-
"""
FSJ04832 寄存器配置工具 PyInstaller 配置文件
更新日期: 2025-06-09
包含完整的插件系统和现代化处理器支持
"""

import os
import sys
import json
from pathlib import Path

# PyInstaller 导入
from PyInstaller.utils.hooks import collect_submodules, collect_data_files
from PyInstaller.building.build_main import Analysis, PYZ, EXE, COLLECT

# 读取版本信息
def get_version_info():
    """获取版本信息"""
    import os
    import json
    from pathlib import Path

    try:
        # PyInstaller执行时工作目录是项目根目录
        # 直接使用相对于项目根目录的路径
        version_file = Path('packaging/config/version.json')

        if version_file.exists():
            with open(version_file, 'r', encoding='utf-8') as f:
                version_data = json.load(f)
                v = version_data.get('version', {})
                if isinstance(v, dict):
                    return f"{v.get('major', 1)}.{v.get('minor', 0)}.{v.get('patch', 0)}.{v.get('build', 0)}"
                return str(v)

        # 回退到项目根目录的version.json
        version_file = Path('version.json')
        if version_file.exists():
            with open(version_file, 'r', encoding='utf-8') as f:
                version_data = json.load(f)
                v = version_data.get('version', {})
                if isinstance(v, dict):
                    return f"{v.get('major', 1)}.{v.get('minor', 0)}.{v.get('patch', 0)}.{v.get('build', 0)}"
                return str(v)

    except Exception as e:
        print(f"读取版本信息失败: {e}")

    return '*******'

# 获取当前版本
current_version = get_version_info()
exe_name = f'FSJ04832_RegisterTool_v{current_version}'

# 使用相对路径确保可移植性
# 注意：PyInstaller执行spec文件时，工作目录是spec文件所在目录
import os

# 计算从spec文件位置到项目根目录的相对路径
# spec文件在 packaging/scripts/ 目录下，需要向上两级到达项目根目录
project_root_relative = '../..'

# 优化的数据文件包含配置（使用相对于spec文件的路径）
# 1. 必需的运行时文件
added_files = [
    # 核心配置文件（运行时必需）
    (f'{project_root_relative}/config/default.json', 'config'),  # 只包含默认配置
    (f'{project_root_relative}/lib/register.json', 'lib'),  # 寄存器配置文件（受保护）

    # 应用资源文件
    (f'{project_root_relative}/images', 'images'),  # 图标和图片资源
    (f'{project_root_relative}/gui', 'gui'),  # GUI图片资源

    # 核心模块（Python代码会被编译，这里主要是资源文件）
    (f'{project_root_relative}/plugins', 'plugins'),  # 插件目录
    (f'{project_root_relative}/ui/forms', 'ui/forms'),  # UI表单文件
    (f'{project_root_relative}/ui/resources', 'ui/resources'),  # UI资源文件

    # 版本管理文件
    ('../config/version.json', 'packaging/config'),  # 版本文件
    ('../config/version.json', '.'),  # 根目录版本文件
]

# 2. 排除的文件类型和目录（不打包到exe中）
excluded_files = [
    # Excel文件（开发时使用，客户不需要）
    '*.xlsx', '*.xls', '*.xlsm',
    # 临时文件
    '~$*', '*.tmp', '*.temp',
    # 开发配置文件（客户不需要）
    'app.json.template', 'local.json', 'migration.json',
    # 测试和调试文件
    'test_*', '*_test.py', '*.log', '*.log.*',
    # 文档文件（减小体积）
    '*.md', '*.txt', 'README*',
    # 缓存文件
    '__pycache__', '*.pyc', '*.pyo',
    # 备份文件
    '*.bak', '*.backup', '*.old',
    # 开发工具文件
    '.git*', '.vscode', '.idea',
]

# 完整的隐藏导入列表
hiddenimports = [
    # PyQt5 核心模块
    'PyQt5.QtCore',
    'PyQt5.QtWidgets',
    'PyQt5.QtGui',
    'PyQt5.QtPrintSupport',

    # 串口通信
    'serial',
    'serial.tools.list_ports',

    # 核心服务
    'core.event_bus',
    'core.services.spi.spi_service',
    'core.services.spi.spi_service_impl',
    'core.services.spi.port_manager',
    'core.services.register.RegisterOperationService',
    'core.services.version.VersionService',
    'core.services.DIContainer',
    'core.services.plugin.PluginManager',
    'core.services.plugin.PluginIntegrationService',
    'core.services.plugin.menu.PluginMenuService',
    'core.services.plugin.window.PluginWindowService',
    'core.services.plugin.dock.PluginDockService',
    'core.services.ui.WindowManagementService',

    # 现代化处理器
    'ui.handlers.ModernBaseHandler',
    'ui.handlers.ModernSetModesHandler',
    'ui.handlers.ModernClkinControlHandler',
    'ui.handlers.ModernPLLHandler',
    'ui.handlers.ModernSyncSysRefHandler',
    'ui.handlers.ModernClkOutputsHandler',
    'ui.handlers.ModernRegisterTableHandler',
    'ui.handlers.ModernUIEventHandler',
    'ui.handlers.ModernRegisterIOHandler',
    'ui.handlers.ModernRegisterTreeHandler',

    # 传统处理器（回退支持）
    'ui.handlers.BaseHandler',
    'ui.handlers.SetModesHandler',
    'ui.handlers.ClkinControlHandler',
    'ui.handlers.RegisterTableHandler',
    'ui.handlers.UIEventHandler',
    'ui.handlers.RegisterIOHandler',
    'ui.handlers.RegisterTreeHandler',

    # UI管理器
    'ui.managers.InitializationManager',
    'ui.managers.StatusAndConfigManager',
    'ui.managers.SPISignalManager',
    'ui.managers.RegisterDisplayManager',
    'ui.managers.UIManager',
    'ui.managers.BatchOperationManager',
    'ui.managers.TabWindowManager',

    # UI组件
    'ui.components.MenuManager',
    'ui.components.ProgressBarManager',
    'ui.components.StatusBarManager',

    # UI协调器
    'ui.coordinators.EventCoordinator',
    'ui.coordinators.SPIOperationCoordinator',

    # UI处理器
    'ui.processors.RegisterUpdateProcessor',

    # UI样式
    'ui.styles.ProgressBarStyleManager',

    # UI窗口
    'ui.windows.RegisterMainWindow',

    # 工厂类
    'ui.factories.ModernToolWindowFactory',
    'ui.factories.ToolWindowFactory',

    # 工具和插件
    'ui.tools.PluginManagerGUI',
    'ui.tools.VersionManagerGUI',

    # UI工具管理器
    'ui.managers.UIUtilityManager',
    'ui.managers.ToolWindowManager',
    'ui.managers.ResourceAndUtilityManager',
    'ui.managers.ApplicationLifecycleManager',
    'ui.managers.GlobalEventManager',
    'ui.managers.RegisterOperationManager',

    # UI资源文件
    'ui.resources.PLL1_2_rc',
    'ui.resources.clkinControl_rc',
    'ui.resources.clkoutput_rc',
    'ui.resources.setModes_rc',
    'ui.resources.syncSysref_rc',

    # UI表单文件
    'ui.forms.Ui_ClkOutputs',
    'ui.forms.Ui_PLL1_2',
    'ui.forms.Ui_clkinControl',
    'ui.forms.Ui_setModes',
    'ui.forms.Ui_syncSysref',

    # 工具类
    'utils.Log',
    'utils.configFileHandler',
    'utils.address_utils',
    'utils.error_handler',
    'utils.message_strings',

    # 插件文件
    'plugins.set_modes_plugin',
    'plugins.clkin_control_plugin',
    'plugins.pll_control_plugin',
    'plugins.sync_sysref_plugin',
    'plugins.clk_output_plugin',
    'plugins.selective_register_plugin',
    'plugins.performance_monitor_plugin',
    'plugins.data_analysis_plugin',

    # 插件配置
    'plugins.config.selective_register_config',

    # 确保所有插件模块都被包含
    'plugins',

    # 标准库模块
    'json',
    'pathlib',
    'importlib',
    'importlib.util',
    'inspect',
    'threading',
    'queue',
    'datetime',
    'time',
    'logging',
    'logging.handlers',
    'configparser',
    'collections',
    'functools',
    'weakref',
    'copy',
    'pickle',
    'base64',
    'hashlib',
    'uuid',
    'platform',
    'subprocess',
    'shutil',
    'tempfile',
    'zipfile',
    'tarfile',
    'gzip',
    're',
    'math',
    'statistics',
    'random',
    'itertools',
    'operator',
    'types',
    'typing',
]

a = Analysis(
    [f'{project_root_relative}/main.py'],  # 使用相对于spec文件的路径
    pathex=[project_root_relative],  # 添加项目根目录到Python路径
    binaries=[],
    datas=added_files,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # GUI库（不需要的）
        'tkinter', 'tkinter.ttk', 'tkinter.messagebox',

        # 数据分析和科学计算库（减小体积）
        'matplotlib', 'matplotlib.pyplot', 'matplotlib.backends',
        'numpy', 'numpy.core', 'numpy.lib',
        'pandas', 'pandas.core', 'pandas.io',
        'scipy', 'scipy.sparse', 'scipy.stats',

        # 图像处理库
        'PIL', 'PIL.Image', 'Pillow',
        'cv2', 'opencv-python',

        # Web相关库
        'requests', 'urllib3', 'http.client', 'http.server',
        'flask', 'django', 'tornado',

        # 测试框架
        'pytest', 'unittest', 'nose', 'mock',
        'doctest', 'test',

        # 开发工具
        'pdb', 'pydoc', 'profile', 'cProfile',
        'trace', 'timeit', 'dis',

        # 不常用的标准库模块
        'email', 'smtplib', 'poplib', 'imaplib',
        'ftplib', 'telnetlib', 'xmlrpc',
        'sqlite3', 'dbm', 'shelve',
        'multiprocessing', 'concurrent.futures',

        # Excel处理库（运行时不需要）
        'openpyxl', 'xlrd', 'xlwt', 'xlsxwriter',

        # 其他大型库
        'IPython', 'jupyter', 'notebook',
        'sympy', 'statsmodels', 'sklearn',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    noarchive=False
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 创建可执行文件 - 使用 onefile 模式保护代码
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,  # 包含所有二进制文件
    a.zipfiles,  # 包含所有zip文件
    a.datas,     # 包含所有数据文件
    [],
    name=exe_name,  # 使用动态版本名称
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,   # 启用UPX压缩，进一步保护代码
    upx_exclude=[],
    console=False,  # 窗口模式
    icon=f'{project_root_relative}/images/logo.ico',  # 使用相对于spec文件的路径
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None
)

# 注意：onefile 模式不需要 COLLECT，所有内容都打包在单个exe文件中