#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试SpinBox控件的范围设置
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_spinbox_range():
    """测试SpinBox控件的范围设置"""
    try:
        from PyQt5.QtWidgets import QApplication
        
        # 创建QApplication
        app = QApplication(sys.argv)
        
        logger.info("=== 测试SpinBox控件范围设置 ===")
        
        # 创建测试实例
        from ui.handlers.ModernClkinControlHandler import ModernClkinControlHandler
        handler = ModernClkinControlHandler.create_for_testing()
        
        # 检查分频比控件的范围设置
        divider_controls = ["PLL1R0Div", "PLL1R1Div", "PLL1R2Div"]
        
        for control_name in divider_controls:
            if hasattr(handler.ui, control_name):
                control = getattr(handler.ui, control_name)
                
                logger.info(f"\n--- 控件 {control_name} ---")
                logger.info(f"最小值: {control.minimum()}")
                logger.info(f"最大值: {control.maximum()}")
                logger.info(f"当前值: {control.value()}")
                logger.info(f"单步值: {control.singleStep()}")
                
                # 尝试设置120
                logger.info(f"尝试设置值为120...")
                control.setValue(120)
                new_value = control.value()
                logger.info(f"设置后的值: {new_value}")
                
                if new_value == 120:
                    logger.info(f"✓ {control_name} 可以设置为120")
                else:
                    logger.error(f"✗ {control_name} 无法设置为120！实际值: {new_value}")
                    
                    # 检查是否是范围问题
                    if 120 < control.minimum():
                        logger.error(f"  原因: 120 < 最小值({control.minimum()})")
                    elif 120 > control.maximum():
                        logger.error(f"  原因: 120 > 最大值({control.maximum()})")
                    else:
                        logger.error(f"  原因: 未知")
                
                # 尝试设置150
                logger.info(f"尝试设置值为150...")
                control.setValue(150)
                new_value = control.value()
                logger.info(f"设置后的值: {new_value}")
                
                if new_value == 150:
                    logger.info(f"✓ {control_name} 可以设置为150")
                else:
                    logger.error(f"✗ {control_name} 无法设置为150！实际值: {new_value}")
                    
                    # 检查是否是范围问题
                    if 150 < control.minimum():
                        logger.error(f"  原因: 150 < 最小值({control.minimum()})")
                    elif 150 > control.maximum():
                        logger.error(f"  原因: 150 > 最大值({control.maximum()})")
                    else:
                        logger.error(f"  原因: 未知")
                        
            else:
                logger.warning(f"控件 {control_name} 不存在")
        
        # 检查控件映射表中的范围信息
        logger.info("\n=== 检查控件映射表中的范围信息 ===")
        
        for control_name in divider_controls:
            if control_name in handler.widget_register_map:
                widget_info = handler.widget_register_map[control_name]
                bit_def = widget_info.get("bit_def", {})
                options = bit_def.get("options", "未知")
                logger.info(f"{control_name}: 选项范围 = {options}")
            else:
                logger.warning(f"{control_name} 未在映射表中找到")
        
        logger.info("\n=== 测试完成 ===")
        
    except Exception as e:
        logger.error(f"测试时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_spinbox_range()
