"""
全局事件管理器
负责处理全局寄存器更新、批量操作状态检查等全局事件
"""

from PyQt5.QtWidgets import QApplication
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class GlobalEventManager:
    """全局事件管理器，处理全局范围的事件"""
    
    def __init__(self, main_window):
        """初始化全局事件管理器
        
        Args:
            main_window: 主窗口实例
        """
        self.main_window = main_window
        self._in_global_update = False
    
    def on_global_register_updated(self, reg_addr, reg_value):
        """处理全局寄存器更新事件"""
        normalized_addr = self.main_window._normalize_register_address(reg_addr)
        if self._in_global_update:
            return
        self._in_global_update = True
        try:
            old_value = self.main_window.register_manager.get_register_value(normalized_addr)
            self.main_window.tree_handler.update_register_value(normalized_addr, reg_value)
            reg_num = int(normalized_addr, 16)
            
            # 通知显示管理器处理全局更新（它会处理所有UI更新包括位字段表格）
            if hasattr(self.main_window, 'display_manager'):
                self.main_window.display_manager.handle_global_register_update(normalized_addr, reg_value)
            
            # 只更新RX值显示，其他UI更新由display_manager处理
            if normalized_addr == self.main_window.selected_register_addr:
                self.main_window._update_rx_value_display(reg_num, reg_value)
                # 移除重复的table_handler.show_bit_fields调用，避免与display_manager冲突
                # self.main_window.refresh_view() 也移除，由display_manager统一处理
            # 只有在非批量操作且用户还没有手动选择过寄存器时才自动切换页面
            # 避免在用户手动选择寄存器后自动跳转
            if (not self.is_in_batch_operation() and
                hasattr(self.main_window.tree_handler, '_user_has_selected') and
                not self.main_window.tree_handler._user_has_selected):
                self.main_window.tree_handler.select_register_by_addr(normalized_addr)
            if old_value != reg_value:
                self.main_window.show_status_message(f"寄存器 R{reg_num} (0x{reg_num:02X}) 全局更新: 0x{old_value:04X} → 0x{reg_value:04X}", 5000)
            else:
                self.main_window.show_status_message(f"寄存器 R{reg_num} (0x{reg_num:02X}) = 0x{reg_value:04X} 已更新", 3000)
        finally:
            self._in_global_update = False

    def is_in_batch_operation(self):
        """检查是否正在进行批量操作

        Returns:
            bool: 如果正在进行批量读取、写入或更新操作则返回True
        """
        return self.main_window.batch_manager.is_in_batch_operation()

    def refresh_current_register_after_batch(self):
        """批量操作完成后刷新当前选中的寄存器显示

        这确保用户在批量操作完成后看到的是最新的寄存器值
        注意：这是刷新操作，不是跳转操作
        """
        try:
            if hasattr(self.main_window, 'selected_register_addr') and self.main_window.selected_register_addr:
                current_addr = self.main_window.selected_register_addr
                logger.info(f"批量操作完成，开始刷新当前选中的寄存器 {current_addr} 的显示（这不是跳转）")

                # 获取当前选中寄存器的最新值
                try:
                    current_value = self.main_window.register_manager.get_register_value(current_addr)
                    reg_num = int(current_addr, 16)
                except Exception as e:
                    logger.error(f"获取寄存器 {current_addr} 的值时出错: {str(e)}")
                    return

                # 更新显示（添加异常保护）
                try:
                    self.main_window._update_rx_value_display(reg_num, current_value)
                except Exception as e:
                    logger.warning(f"更新寄存器显示时出错: {str(e)}")

                # 注意：移除了位字段表格的重复更新，避免第三次调用
                # 位字段表格更新现在统一由RegisterDisplayManager处理
                logger.debug(f"GlobalEventManager: 跳过位字段表格更新，避免重复调用 - {current_addr}")

                # 刷新视图（添加异常保护）
                try:
                    self.main_window.refresh_view()
                except Exception as e:
                    logger.warning(f"刷新视图时出错: {str(e)}")

                logger.info(f"批量操作完成后成功刷新了当前寄存器 {current_addr} 的显示")
            else:
                logger.debug("批量操作完成，但没有当前选中的寄存器需要刷新")

        except Exception as e:
            logger.error(f"批量操作完成后刷新当前寄存器显示时出错: {str(e)}")
            # 记录详细的异常信息，但不让异常传播导致软件退出
            import traceback
            logger.error(f"详细异常信息: {traceback.format_exc()}")

    def handle_bit_field_selected(self, addr):
        """处理位段选中事件，在批量操作期间不跳转

        Args:
            addr: 寄存器地址
        """
        # 只有在非批量操作时才允许跳转
        if not self.is_in_batch_operation():
            self.main_window.tree_handler.select_register_by_addr(addr)
        else:
            logger.debug(f"批量操作期间忽略位段选中跳转请求: {addr}")

    def handle_value_changed(self, addr, new_value):
        """处理输入值变化事件"""
        # 标准化地址
        normalized_addr = self.main_window._normalize_register_address(addr)
        
        # 记录当前输入值，但不立即触发写操作
        # 只有当用户点击写入按钮时才执行写操作
        if normalized_addr == self.main_window.selected_register_addr:
            # 记录原值用于显示变化
            old_value = self.main_window.current_register_value
            
            # 仅更新显示，不执行写操作
            self.main_window.current_register_value = new_value
            
            # 仅更新位字段表格显示，不触发写操作
            if hasattr(self.main_window.table_handler, 'show_bit_fields'):
                self.main_window.table_handler.show_bit_fields(normalized_addr, new_value)
                
            # 刷新视图确保显示更新
            self.main_window.refresh_view()
            
            # 在状态栏显示寄存器变化详情
            reg_num = int(normalized_addr, 16) if isinstance(normalized_addr, str) else normalized_addr
            self.main_window.show_status_message(f"寄存器 R{reg_num} (0x{reg_num:02X}) 值已修改: 0x{old_value:04X} → 0x{new_value:04X}", 5000)

    def refresh_ui(self):
        """刷新主界面UI，确保寄存器和表格等控件同步最新值"""
        # 刷新当前选中寄存器的详细信息和表格
        self.main_window.refresh_view()
        # 可以根据需要扩展刷新其他控件

    def refresh_view(self):
        """刷新当前视图显示
        在控件状态改变后调用此方法，确保表格视图同步更新
        """
        if not self.main_window.selected_register_addr:
            return
        # 获取当前寄存器值（强制从寄存器对象获取最新值）
        register_value = self.main_window.register_manager.get_register_value(self.main_window.selected_register_addr)
        old_value = self.main_window.current_register_value
        self.main_window.current_register_value = register_value
        # 更新寄存器值显示
        reg_num = int(self.main_window.selected_register_addr, 16) if isinstance(self.main_window.selected_register_addr, str) else self.main_window.selected_register_addr
        # 更新输入框显示
        self.main_window._update_rx_value_display(reg_num, register_value)
        # 更新位字段表格显示 - 使用强制刷新方法
        if hasattr(self.main_window.table_handler, 'refresh_display'):
            self.main_window.table_handler.show_bit_fields(self.main_window.selected_register_addr, register_value)
        elif hasattr(self.main_window.table_handler, 'show_bit_fields'):
            self.main_window.table_handler.show_bit_fields(self.main_window.selected_register_addr, register_value)
        # 检查是否在批量操作期间，避免触发布局重新计算
        is_batch_operation = (getattr(self.main_window, 'is_batch_reading', False) or
                            getattr(self.main_window, 'is_batch_writing', False) or
                            getattr(self.main_window, 'is_batch_updating', False))

        # 只在非批量操作且非寄存器切换时处理事件，避免布局抖动
        if not is_batch_operation:
            # 使用更温和的更新方式，避免强制处理所有事件
            if hasattr(self.main_window, 'update'):
                self.main_window.update()
            # QApplication.processEvents()  # 移除此调用，避免布局重新计算
        # 如果值有变化，在状态栏显示详细信息
        if old_value != register_value:
            self.main_window.show_status_message(f"寄存器 R{reg_num} (0x{reg_num:02X}) 已刷新: 0x{old_value:04X} → 0x{register_value:04X}", 5000)
        else:
            self.main_window.show_status_message(f"寄存器 R{reg_num} (0x{reg_num:02X}) = 0x{register_value:04X} 视图已刷新", 3000)

    def handle_io_write_request(self, addr, value):
        """处理来自 IO Handler 的直接写入请求 (例如，通过回车确认输入)"""
        logger.info(f"MainWindow: 收到来自 IO Handler 的写入请求, 地址: 0x{addr:02X}, 值: 0x{value:04X}")
        # 1. 更新 RegisterManager 中的值
        success = self.main_window.register_manager.set_register_value(addr, value)
        if success:
            logger.info(f"RegisterManager 更新成功: 地址 0x{addr:02X}, 新值 0x{value:04X}")
            # 2. 更新位域表格显示
            self.main_window.table_handler.show_bit_fields(addr, value)
            # 3. (可选) 确保 IO Handler 中的值显示也同步更新 (格式化)
            self.main_window.io_handler.set_value_display(value)
            # 4. 触发全局更新信号
            self.main_window.register_update_bus.emit_register_updated(addr, value)
        else:
            logger.warning(f"RegisterManager 更新失败: 地址 0x{addr:02X}")
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self.main_window, "错误", f"无法更新寄存器 0x{addr:02X} 的值。")

    def update_registers_from_config(self, loaded_values):
        """根据加载的配置更新寄存器管理器和UI"""
        self.main_window.is_batch_updating = True
        # 同时设置全局批量操作状态
        from core.services.BatchOperationState import BatchOperationState
        BatchOperationState.instance().set_batch_updating(True)
        try:
            # 委托给寄存器操作服务
            success = self.main_window.register_service.update_registers_from_config(loaded_values)

            if success:
                # 刷新UI
                self.refresh_ui()
                if hasattr(self.main_window, 'reg_table') and self.main_window.reg_table.isVisible():
                    self.main_window.resource_utility_manager.populate_dump_table()

            return success
        except Exception as e:
            logger.error(f"从配置更新寄存器时出错: {e}")
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self.main_window, "更新错误", f"应用加载的配置时出错: {e}", QMessageBox.Ok)
            return False
        finally:
            self.main_window.is_batch_updating = False
            # 清除全局批量操作状态
            from core.services.BatchOperationState import BatchOperationState
            BatchOperationState.instance().set_batch_updating(False)

    def cleanup_resources(self):
        """清理资源"""
        try:
            # 重置状态
            self._in_global_update = False
            
            logger.debug("全局事件管理器资源已清理")
        except Exception as e:
            logger.error(f"清理全局事件管理器资源时出错: {str(e)}")
