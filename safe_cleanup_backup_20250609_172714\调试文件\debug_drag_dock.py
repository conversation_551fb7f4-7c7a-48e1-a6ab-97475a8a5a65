#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
拖拽停靠功能调试脚本
专门用于调试和分析拖拽停靠功能的问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QLabel, QPushButton, QTextEdit
from PyQt5.QtCore import Qt, QPoint, QRect
from PyQt5.QtGui import QMouseEvent
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DragDockAnalyzer:
    """拖拽停靠分析器"""
    
    def __init__(self, main_window):
        self.main_window = main_window
        
    def analyze_dock_area_calculation(self, global_pos):
        """分析停靠区域计算"""
        try:
            logger.info("=== 停靠区域计算分析 ===")
            
            # 方法1: 使用frameGeometry()
            frame_geometry = self.main_window.frameGeometry()
            logger.info(f"frameGeometry(): {frame_geometry}")
            
            # 方法2: 使用geometry()
            geometry = self.main_window.geometry()
            logger.info(f"geometry(): {geometry}")
            
            # 方法3: 使用geometry()并转换为全局坐标
            global_geometry = geometry
            main_global_pos = self.main_window.mapToGlobal(geometry.topLeft())
            global_geometry.moveTopLeft(main_global_pos)
            logger.info(f"geometry() + mapToGlobal(): {global_geometry}")
            
            # 分析三种方法的差异
            logger.info("=== 几何信息对比 ===")
            logger.info(f"frameGeometry 与 geometry 的差异:")
            logger.info(f"  位置差异: ({frame_geometry.x() - geometry.x()}, {frame_geometry.y() - geometry.y()})")
            logger.info(f"  尺寸差异: ({frame_geometry.width() - geometry.width()}, {frame_geometry.height() - geometry.height()})")
            
            # 计算停靠区域（使用三种方法）
            self._calculate_dock_area_with_method(frame_geometry, "frameGeometry", global_pos)
            self._calculate_dock_area_with_method(global_geometry, "geometry+mapToGlobal", global_pos)
            
        except Exception as e:
            logger.error(f"分析停靠区域计算失败: {str(e)}")
    
    def _calculate_dock_area_with_method(self, main_geometry, method_name, global_pos):
        """使用指定方法计算停靠区域"""
        try:
            logger.info(f"=== 使用 {method_name} 计算停靠区域 ===")
            
            # 定义停靠区域：主窗口底部30%的区域
            dock_area_height = int(main_geometry.height() * 0.3)
            dock_area_top = main_geometry.top() + main_geometry.height() - dock_area_height
            dock_area_bottom = main_geometry.bottom()
            
            # 添加边距
            margin = 10
            dock_area_left = main_geometry.left() + margin
            dock_area_right = main_geometry.right() - margin
            dock_area_top += margin
            dock_area_bottom -= margin
            
            # 检查鼠标是否在停靠区域内
            is_in_area = (global_pos.x() >= dock_area_left and
                         global_pos.x() <= dock_area_right and
                         global_pos.y() >= dock_area_top and
                         global_pos.y() <= dock_area_bottom)
            
            logger.info(f"主窗口几何: {main_geometry}")
            logger.info(f"停靠区域: X({dock_area_left}-{dock_area_right}), Y({dock_area_top}-{dock_area_bottom})")
            logger.info(f"鼠标位置: {global_pos}")
            logger.info(f"在停靠区域内: {is_in_area}")
            
            return is_in_area
            
        except Exception as e:
            logger.error(f"使用 {method_name} 计算停靠区域失败: {str(e)}")
            return False
    
    def test_drag_distance_calculation(self, start_pos, current_pos):
        """测试拖拽距离计算"""
        try:
            logger.info("=== 拖拽距离计算测试 ===")
            
            distance = (current_pos - start_pos).manhattanLength()
            system_threshold = QApplication.startDragDistance()
            custom_threshold = 5
            
            logger.info(f"起始位置: {start_pos}")
            logger.info(f"当前位置: {current_pos}")
            logger.info(f"曼哈顿距离: {distance}")
            logger.info(f"系统拖拽阈值: {system_threshold}")
            logger.info(f"自定义拖拽阈值: {custom_threshold}")
            logger.info(f"超过系统阈值: {distance >= system_threshold}")
            logger.info(f"超过自定义阈值: {distance >= custom_threshold}")
            
            return distance >= custom_threshold
            
        except Exception as e:
            logger.error(f"测试拖拽距离计算失败: {str(e)}")
            return False


class DebugMainWindow(QMainWindow):
    """调试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("拖拽停靠功能调试 - 主窗口")
        self.setGeometry(200, 200, 800, 600)
        
        # 设置中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        layout.addWidget(QLabel("这是调试主窗口"))
        layout.addWidget(QLabel("底部30%区域是停靠区域"))
        
        # 创建测试按钮
        test_btn = QPushButton("创建测试拖拽窗口")
        test_btn.clicked.connect(self.create_test_window)
        layout.addWidget(test_btn)
        
        analyze_btn = QPushButton("分析当前鼠标位置")
        analyze_btn.clicked.connect(self.analyze_current_mouse_position)
        layout.addWidget(analyze_btn)
        
        # 日志显示区域
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        layout.addWidget(QLabel("调试日志:"))
        layout.addWidget(self.log_text)
        
        central_widget.setLayout(layout)
        
        self.test_windows = []
        self.analyzer = DragDockAnalyzer(self)
    
    def create_test_window(self):
        """创建测试窗口"""
        from test_drag_dock import TestDragWindow
        
        window_count = len(self.test_windows) + 1
        test_window = TestDragWindow(f"调试测试窗口 {window_count}")
        test_window.show()
        self.test_windows.append(test_window)
        
        logger.info(f"创建了调试测试窗口 {window_count}")
        self.log_text.append(f"创建了调试测试窗口 {window_count}")
    
    def analyze_current_mouse_position(self):
        """分析当前鼠标位置"""
        try:
            from PyQt5.QtGui import QCursor
            
            current_pos = QCursor.pos()
            logger.info(f"当前鼠标全局位置: {current_pos}")
            self.log_text.append(f"当前鼠标全局位置: {current_pos}")
            
            # 分析停靠区域计算
            self.analyzer.analyze_dock_area_calculation(current_pos)
            
        except Exception as e:
            logger.error(f"分析当前鼠标位置失败: {str(e)}")
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        logger.info(f"主窗口鼠标按下: {event.globalPos()}")
        super().mousePressEvent(event)
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if event.buttons() == Qt.LeftButton:
            logger.info(f"主窗口鼠标拖拽: {event.globalPos()}")
        super().mouseMoveEvent(event)


def debug_coordinate_systems():
    """调试坐标系统"""
    logger.info("=== 坐标系统调试 ===")
    
    app = QApplication.instance()
    if not app:
        app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = DebugMainWindow()
    main_window.show()
    
    # 等待窗口显示
    app.processEvents()
    
    # 获取窗口几何信息
    frame_geometry = main_window.frameGeometry()
    geometry = main_window.geometry()
    
    logger.info(f"主窗口 frameGeometry: {frame_geometry}")
    logger.info(f"主窗口 geometry: {geometry}")
    
    # 计算全局坐标
    global_top_left = main_window.mapToGlobal(geometry.topLeft())
    logger.info(f"geometry.topLeft() 映射到全局: {global_top_left}")
    
    # 分析差异
    logger.info(f"frameGeometry 与 geometry 的位置差异: ({frame_geometry.x() - geometry.x()}, {frame_geometry.y() - geometry.y()})")
    logger.info(f"frameGeometry 与 geometry 的尺寸差异: ({frame_geometry.width() - geometry.width()}, {frame_geometry.height() - geometry.height()})")
    
    return main_window


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    logger.info("=== 拖拽停靠功能调试启动 ===")
    
    # 调试坐标系统
    main_window = debug_coordinate_systems()
    
    logger.info("调试说明:")
    logger.info("1. 点击'创建测试拖拽窗口'创建可拖拽的测试窗口")
    logger.info("2. 点击'分析当前鼠标位置'分析鼠标位置和停靠区域")
    logger.info("3. 拖拽测试窗口到主窗口底部观察日志输出")
    logger.info("4. 对比不同坐标计算方法的结果")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
