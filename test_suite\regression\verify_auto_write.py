#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
自动写入功能验证脚本
用于验证自动写入功能是否正常工作
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def verify_auto_write_feature():
    """验证自动写入功能"""
    print("=" * 60)
    print("自动写入功能验证")
    print("=" * 60)
    
    try:
        # 1. 验证配置服务
        print("\n1. 验证配置服务...")
        from core.services.config.ConfigurationService import ConfigurationService
        
        # 创建一个临时的配置服务实例
        config_service = ConfigurationService(None)
        
        # 测试保存和加载
        config_service.save_auto_write_mode(True)
        loaded_mode = config_service.load_auto_write_mode()
        
        if loaded_mode == True:
            print("   ✓ 配置服务正常工作")
        else:
            print("   ✗ 配置服务异常")
            return False
            
        # 2. 验证BaseHandler的自动写入方法
        print("\n2. 验证BaseHandler的自动写入方法...")
        from ui.handlers.BaseHandler import BaseClockHandler

        # 检查BaseClockHandler是否有自动写入相关方法
        if hasattr(BaseClockHandler, '_is_auto_write_enabled'):
            print("   ✓ BaseHandler包含自动写入检查方法")
        else:
            print("   ✗ BaseHandler缺少自动写入检查方法")
            return False

        if hasattr(BaseClockHandler, '_auto_write_register_to_chip'):
            print("   ✓ BaseHandler包含自动写入执行方法")
        else:
            print("   ✗ BaseHandler缺少自动写入执行方法")
            return False

        # 检查update_register_value方法是否包含自动写入逻辑
        import inspect
        update_method_source = inspect.getsource(BaseClockHandler.update_register_value)
        if '_is_auto_write_enabled' in update_method_source and '_auto_write_register_to_chip' in update_method_source:
            print("   ✓ update_register_value方法包含自动写入逻辑")
        else:
            print("   ✗ update_register_value方法缺少自动写入逻辑")
            return False
        
        # 3. 验证菜单管理器
        print("\n3. 验证菜单管理器...")
        from ui.components.MenuManager import MenuManager

        # 检查MenuManager是否有自动写入相关的代码
        import inspect
        menu_source = inspect.getsource(MenuManager)
        if 'auto_write_action' in menu_source:
            print("   ✓ 菜单管理器包含自动写入选项")
        else:
            print("   ✗ 菜单管理器缺少自动写入选项")
            return False
            
        # 4. 验证事件协调器
        print("\n4. 验证事件协调器...")
        from ui.coordinators.EventCoordinator import EventCoordinator
        
        # 检查EventCoordinator是否有自动写入处理方法
        if hasattr(EventCoordinator, 'handle_auto_write_mode_toggle'):
            print("   ✓ 事件协调器包含自动写入处理方法")
        else:
            print("   ✗ 事件协调器缺少自动写入处理方法")
            return False
            
        # 5. 验证工具窗口工厂
        print("\n5. 验证工具窗口工厂...")
        from ui.factories.ToolWindowFactory import ToolWindowFactory
        
        # 检查ToolWindowFactory是否设置主窗口引用
        factory_source = inspect.getsource(ToolWindowFactory)
        if 'window.main_window = self.main_window' in factory_source:
            print("   ✓ 工具窗口工厂正确设置主窗口引用")
        else:
            print("   ✗ 工具窗口工厂缺少主窗口引用设置")
            return False
            
        print("\n" + "=" * 60)
        print("✓ 所有验证通过！自动写入功能已正确实现。")
        print("=" * 60)
        
        print("\n使用说明:")
        print("1. 启动应用程序")
        print("2. 在菜单栏选择 '工具' -> '自动写入' 来启用功能")
        print("3. 打开任意工具窗口（如PLL控制、时钟输出等）")
        print("4. 修改控件状态，系统会自动写入到寄存器")
        print("5. 查看控制台输出确认写入操作")
        
        return True
        
    except Exception as e:
        print(f"\n✗ 验证过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = verify_auto_write_feature()
    sys.exit(0 if success else 1)
