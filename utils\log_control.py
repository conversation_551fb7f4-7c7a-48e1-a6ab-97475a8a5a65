#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志级别控制工具

提供运行时动态调整日志级别的功能
"""

import json
import logging
from pathlib import Path
from typing import Optional

class LogLevelController:
    """日志级别控制器"""
    
    LEVELS = {
        'DEBUG': logging.DEBUG,
        'INFO': logging.INFO, 
        'WARNING': logging.WARNING,
        'ERROR': logging.ERROR,
        'CRITICAL': logging.CRITICAL
    }
    
    LEVEL_DESCRIPTIONS = {
        'DEBUG': '调试级别 - 显示所有日志信息（最详细）',
        'INFO': '信息级别 - 显示一般信息和以上级别',
        'WARNING': '警告级别 - 只显示警告、错误和严重错误（推荐）',
        'ERROR': '错误级别 - 只显示错误和严重错误',
        'CRITICAL': '严重级别 - 只显示严重错误（最少）'
    }
    
    def __init__(self):
        self.config_file = Path("config/default.json")
        
    def get_current_level(self) -> str:
        """获取当前日志级别"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('logging', {}).get('level', 'INFO')
        except Exception:
            pass
        return 'INFO'
    
    def set_log_level(self, level: str) -> bool:
        """设置日志级别"""
        level = level.upper()
        if level not in self.LEVELS:
            print(f"无效的日志级别: {level}")
            print(f"可用级别: {', '.join(self.LEVELS.keys())}")
            return False
            
        try:
            # 更新配置文件
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                data.setdefault('logging', {})['level'] = level
                
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
            
            # 动态更新当前运行的日志级别
            try:
                from utils.Log import log_manager
                log_manager.set_log_level(level)
                print(f"日志级别已设置为: {level} - {self.LEVEL_DESCRIPTIONS[level]}")
                return True
            except ImportError:
                print(f"配置文件已更新为: {level}，重启应用后生效")
                return True
                
        except Exception as e:
            print(f"设置日志级别失败: {e}")
            return False
    
    def show_current_status(self):
        """显示当前日志状态"""
        current_level = self.get_current_level()
        print("=" * 60)
        print("日志级别控制")
        print("=" * 60)
        print(f"当前级别: {current_level} - {self.LEVEL_DESCRIPTIONS.get(current_level, '未知')}")
        print()
        print("可用级别:")
        for level, desc in self.LEVEL_DESCRIPTIONS.items():
            marker = " ✓ " if level == current_level else "   "
            print(f"{marker}{level}: {desc}")
        print("=" * 60)
    
    def interactive_set_level(self):
        """交互式设置日志级别"""
        self.show_current_status()
        print()
        
        while True:
            level = input("请输入新的日志级别 (或输入 'q' 退出): ").strip()
            if level.lower() == 'q':
                break
            
            if self.set_log_level(level):
                break
            print()

def main():
    """命令行入口"""
    import sys
    
    controller = LogLevelController()
    
    if len(sys.argv) == 1:
        # 无参数，显示交互式界面
        controller.interactive_set_level()
    elif len(sys.argv) == 2:
        if sys.argv[1].lower() in ['status', 'show', 's']:
            # 显示当前状态
            controller.show_current_status()
        else:
            # 设置日志级别
            level = sys.argv[1]
            controller.set_log_level(level)
    else:
        print("用法:")
        print("  python log_control.py           # 交互式设置")
        print("  python log_control.py status    # 显示当前状态")
        print("  python log_control.py WARNING   # 直接设置级别")

if __name__ == "__main__":
    main()
