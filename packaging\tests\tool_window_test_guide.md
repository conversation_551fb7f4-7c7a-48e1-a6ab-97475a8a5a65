
# 工具窗口测试指南

## 🎯 测试目标
验证安全打包版本中的所有工具窗口是否能正常工作

## 📋 测试步骤

### 1. 启动程序
- 双击 `FSJ04832_RegisterTool_v1.0.3.9_Release.exe`
- 确认主窗口正常显示

### 2. 测试工具窗口
依次测试以下菜单项：

#### 时钟输入控制窗口
- 菜单: 工具 -> 时钟输入控制窗口
- 预期: 打开时钟输入配置界面
- 检查: 界面元素是否完整显示

#### PLL控制窗口  
- 菜单: 工具 -> PLL控制窗口
- 预期: 打开PLL1和PLL2配置界面
- 检查: PLL参数设置是否正常

#### 时钟输出窗口
- 菜单: 工具 -> 时钟输出窗口  
- 预期: 打开时钟输出配置界面
- 检查: 输出通道设置是否正常

#### 同步系统参考窗口
- 菜单: 工具 -> 同步系统参考窗口
- 预期: 打开同步配置界面
- 检查: 同步参数是否可设置

#### 模式设置窗口
- 菜单: 工具 -> 模式设置窗口
- 预期: 打开工作模式配置界面
- 检查: 模式选择是否正常

### 3. 测试结果
- ✅ 所有窗口都能正常打开 = 打包成功
- ❌ 有窗口无法打开 = 需要修复打包配置

## 🔍 常见问题

### 窗口无法打开
- 可能原因: 相关模块未正确打包
- 解决方案: 检查PyInstaller的hiddenimports配置

### 界面显示异常
- 可能原因: UI资源文件缺失
- 解决方案: 确保.ui文件和资源文件已包含

### 功能异常
- 可能原因: 依赖的服务模块缺失
- 解决方案: 检查核心服务模块的导入

## 📞 技术支持
如有问题，请检查:
1. 打包配置文件: packaging/scripts/build.spec
2. 隐藏导入列表: hiddenimports
3. 数据文件列表: added_files
