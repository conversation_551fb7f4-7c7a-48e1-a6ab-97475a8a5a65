from abc import ABC, abstractmethod
from PyQt5.QtCore import QObject, pyqtSignal

class ISPIService(QObject):
    """SPI服务接口定义"""

    # 定义信号
    spi_operation_complete = pyqtSignal(str, int, bool)  # (address, value, is_read)
    spi_error_occurred = pyqtSignal(str)
    operation_timeout = pyqtSignal()
    ports_refreshed = pyqtSignal(list) # 信号：端口列表已刷新
    simulation_mode_changed = pyqtSignal(bool) # 信号：模拟模式状态改变
    connection_status_changed = pyqtSignal(bool) # 信号：连接状态改变

    def __init__(self, parent=None):
        super().__init__(parent)

    def initialize(self):
        """初始化SPI服务"""
        raise NotImplementedError

    def cleanup(self):
        """清理SPI资源"""
        raise NotImplementedError

    def refresh_available_ports(self):
        """刷新并获取可用的SPI端口列表"""
        raise NotImplementedError

    def set_port(self, port_name: str) -> bool:
        """设置要使用的SPI端口"""
        raise NotImplementedError

    def read_register(self, address: str):
        """读取单个寄存器"""
        raise NotImplementedError

    def write_register(self, address: str, value: int):
        """写入单个寄存器"""
        raise NotImplementedError

    def batch_read_registers(self, addresses: list):
        """批量读取寄存器"""
        raise NotImplementedError

    def batch_write_registers(self, operations: list):
        """批量写入寄存器"""
        raise NotImplementedError

    def set_simulation_mode(self, enabled: bool):
        """设置模拟模式"""
        raise NotImplementedError

    @property
    def simulation_mode(self) -> bool:
        """获取当前是否为模拟模式"""
        raise NotImplementedError

    @property
    @abstractmethod
    def is_port_selected(self) -> bool:
        """检查是否已选择端口"""

    @property
    def is_connected(self) -> bool:
        """检查是否已连接到设备"""
        raise NotImplementedError

    def get_connection_status(self) -> dict:
        """获取连接状态信息"""
        raise NotImplementedError