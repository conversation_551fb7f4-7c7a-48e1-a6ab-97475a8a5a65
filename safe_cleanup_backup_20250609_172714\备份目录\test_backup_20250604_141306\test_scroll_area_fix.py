#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查PLL工具窗口状态
验证是否还在使用旧文件，并测试功能是否正常
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication, QMainWindow
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt


def check_pll_window_status():
    """检查PLL工具窗口状态"""
    print("=" * 60)
    print("检查PLL工具窗口状态")
    print("=" * 60)

    try:
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        # 1. 检查文件存在性
        print("1. 检查PLL相关文件...")

        # 检查传统PLL处理器
        legacy_pll_path = os.path.join(project_root, 'ui', 'handlers', 'PLLHandler.py')
        modern_pll_path = os.path.join(project_root, 'ui', 'handlers', 'ModernPLLHandler.py')

        if os.path.exists(legacy_pll_path):
            print("✓ 传统PLL处理器文件存在: PLLHandler.py")
        else:
            print("❌ 传统PLL处理器文件不存在: PLLHandler.py")

        if os.path.exists(modern_pll_path):
            print("✓ 现代化PLL处理器文件存在: ModernPLLHandler.py")
        else:
            print("❌ 现代化PLL处理器文件不存在: ModernPLLHandler.py")

        # 检查UI文件
        ui_file_path = os.path.join(project_root, 'ui', 'forms', 'Ui_PLL1_2.py')
        ui_form_path = os.path.join(project_root, 'ui', 'forms', 'PLL1_2.ui')

        if os.path.exists(ui_file_path):
            print("✓ PLL UI文件存在: Ui_PLL1_2.py")
        else:
            print("❌ PLL UI文件不存在: Ui_PLL1_2.py")

        if os.path.exists(ui_form_path):
            print("✓ PLL UI表单文件存在: PLL1_2.ui")
        else:
            print("❌ PLL UI表单文件不存在: PLL1_2.ui")

        # 2. 检查工厂配置
        print("\n2. 检查工厂配置...")
        from ui.factories.ModernToolWindowFactory import ModernToolWindowFactory

        # 创建寄存器管理器
        from core.services.register.RegisterManager import RegisterManager
        config_path = os.path.join(project_root, 'lib', 'register.json')
        if not os.path.exists(config_path):
            print(f"❌ 寄存器配置文件不存在: {config_path}")
            return False

        import json
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)

        register_manager = RegisterManager(registers_config)

        # 创建模拟主窗口
        class MockMainWindow:
            def __init__(self):
                self.register_manager = register_manager

        mock_main_window = MockMainWindow()
        factory = ModernToolWindowFactory(mock_main_window)

        # 检查PLL配置
        pll_config = factory.HANDLER_CONFIGS.get('pll_control')
        if pll_config:
            print("✓ PLL控制配置存在")
            print(f"  - 标题: {pll_config['title']}")
            print(f"  - 使用现代化版本: {pll_config.get('use_modern', False)}")
            print(f"  - 传统处理器: {pll_config.get('legacy_handler', 'None')}")
            print(f"  - 现代化处理器: {pll_config.get('modern_handler', 'None')}")
        else:
            print("❌ PLL控制配置不存在")
            return False

        # 3. 测试处理器创建
        print("\n3. 测试处理器创建...")

        # 测试现代化处理器
        try:
            from ui.handlers.ModernPLLHandler import ModernPLLHandler
            modern_handler = ModernPLLHandler(None, register_manager)
            print("✓ 现代化PLL处理器创建成功")
            print(f"  - 类型: {type(modern_handler).__name__}")
            print(f"  - 窗口标题: {modern_handler.windowTitle()}")

            # 检查UI
            if hasattr(modern_handler, 'ui'):
                print("✓ 现代化处理器UI对象存在")
                print(f"  - UI类型: {type(modern_handler.ui).__name__}")
            else:
                print("❌ 现代化处理器UI对象不存在")

        except Exception as e:
            print(f"❌ 现代化PLL处理器创建失败: {str(e)}")
            import traceback
            traceback.print_exc()

        # 测试传统处理器
        try:
            # from ui.handlers.PLLHandler import PLLHandler  # 已移除
            # legacy_handler = PLLHandler(None, register_manager.register_objects)  # 已移除
            print("✓ 传统PLL处理器创建成功")
            print(f"  - 类型: {type(legacy_handler).__name__}")
            print(f"  - 窗口标题: {legacy_handler.windowTitle()}")

            # 检查UI
            if hasattr(legacy_handler, 'ui'):
                print("✓ 传统处理器UI对象存在")
                print(f"  - UI类型: {type(legacy_handler.ui).__name__}")
            else:
                print("❌ 传统处理器UI对象不存在")

        except Exception as e:
            print(f"❌ 传统PLL处理器创建失败: {str(e)}")
            import traceback
            traceback.print_exc()

        return True

    except Exception as e:
        print(f"❌ 检查过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_pll_window_in_main_application():
    """在主应用程序中测试PLL窗口"""
    print("\n" + "=" * 60)
    print("在主应用程序中测试PLL窗口")
    print("=" * 60)

    try:
        # 创建主窗口
        from ui.windows.RegisterMainWindow import RegisterMainWindow
        from core.services.spi.spi_service_impl import SPIServiceImpl
        from core.repositories.register_repository import RegisterRepository

        spi_service = SPIServiceImpl()
        spi_service.initialize()
        register_repo = RegisterRepository(spi_service)
        main_window = RegisterMainWindow(register_repo)
        main_window.show()

        # 等待界面完全加载
        QTest.qWait(2000)

        print("✓ 主窗口创建成功")

        # 创建PLL控制窗口
        print("\n创建PLL控制窗口...")
        pll_window = main_window.tool_window_factory.create_window_by_type('pll_control')

        if pll_window:
            print("✓ PLL控制窗口创建成功")
            print(f"✓ 窗口类型: {type(pll_window).__name__}")
            print(f"✓ 窗口标题: {pll_window.windowTitle()}")

            # 检查是否使用现代化版本
            if 'Modern' in type(pll_window).__name__:
                print("✓ 使用现代化PLL处理器")

                # 检查滚动区域（如果有）
                if hasattr(pll_window, 'scroll_area'):
                    print("✓ 现代化处理器有滚动区域")
                    print(f"✓ 滚动区域大小: {pll_window.scroll_area.size()}")
                else:
                    print("ℹ️  现代化处理器无滚动区域（可能不需要）")

                # 检查content_widget
                if hasattr(pll_window, 'content_widget'):
                    print("✓ 现代化处理器有content_widget")
                    print(f"✓ content_widget大小: {pll_window.content_widget.size()}")
                else:
                    print("ℹ️  现代化处理器无content_widget")
            else:
                print("⚠️  使用传统PLL处理器")

            # 检查UI对象
            if hasattr(pll_window, 'ui'):
                print("✓ PLL窗口UI对象存在")
                print(f"✓ UI类型: {type(pll_window.ui).__name__}")

                # 检查关键控件
                key_widgets = ['PLL1PD', 'PLL2PD', 'FreFin', 'OSCinFreq']
                for widget_name in key_widgets:
                    if hasattr(pll_window.ui, widget_name):
                        widget = getattr(pll_window.ui, widget_name)
                        print(f"✓ {widget_name}: 存在且可见={widget.isVisible()}")
                    else:
                        print(f"❌ {widget_name}: 不存在")
            else:
                print("❌ PLL窗口UI对象不存在")

            print("✅ 在主应用程序中的PLL测试完成")

            # 保持窗口打开供用户查看
            print("\n主窗口将保持打开5秒供查看...")
            QTest.qWait(5000)

            return True
        else:
            print("❌ PLL控制窗口创建失败")
            return False

    except Exception as e:
        print(f"❌ 主应用程序PLL测试出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def check_old_files_cleanup():
    """检查是否有需要清理的旧文件"""
    print("\n" + "=" * 60)
    print("检查旧文件清理状态")
    print("=" * 60)

    try:
        # 检查可能存在的旧文件
        old_files_to_check = [
            'ui/handlers/OldPLLHandler.py',
            'ui/handlers/PLLHandler_backup.py',
            'ui/handlers/PLLHandler.bak',
            'ui/forms/PLL1_2_old.ui',
            'ui/forms/Ui_PLL1_2_old.py',
            'ui/forms/PLL1_2.ui.bak',
        ]

        found_old_files = []
        for file_path in old_files_to_check:
            full_path = os.path.join(project_root, file_path)
            if os.path.exists(full_path):
                found_old_files.append(file_path)
                print(f"⚠️  发现旧文件: {file_path}")

        if not found_old_files:
            print("✓ 未发现明显的旧文件")
        else:
            print(f"\n发现 {len(found_old_files)} 个可能的旧文件")
            print("建议检查这些文件是否需要清理")

        # 检查是否有重复的处理器导入
        print("\n检查处理器导入状态...")

        # 检查工厂配置
        from ui.factories.ModernToolWindowFactory import ModernToolWindowFactory

        # 创建模拟主窗口
        class MockMainWindow:
            def __init__(self):
                pass

        mock_main_window = MockMainWindow()
        factory = ModernToolWindowFactory(mock_main_window)

        pll_config = factory.HANDLER_CONFIGS.get('pll_control')
        if pll_config:
            legacy_handler = pll_config.get('legacy_handler')
            modern_handler = pll_config.get('modern_handler')
            use_modern = pll_config.get('use_modern', False)

            print("✓ PLL配置状态:")
            print(f"  - 使用现代化版本: {use_modern}")
            print(f"  - 传统处理器: {legacy_handler}")
            print(f"  - 现代化处理器: {modern_handler}")

            if use_modern and modern_handler:
                print("✅ 配置正确：优先使用现代化处理器")
            elif not use_modern and legacy_handler:
                print("⚠️  配置使用传统处理器")
            else:
                print("❌ 配置有问题")

        return len(found_old_files) == 0

    except Exception as e:
        print(f"❌ 检查旧文件时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    # 检查PLL工具窗口状态
    success1 = check_pll_window_status()

    # 在主应用程序中测试PLL窗口
    success2 = test_pll_window_in_main_application()

    # 检查旧文件清理状态
    success3 = check_old_files_cleanup()

    if success1 and success2 and success3:
        print("\n🎉 所有检查通过！PLL工具窗口状态正常！")
        print("📋 检查总结:")
        print("   - ✅ PLL处理器文件状态正常")
        print("   - ✅ 工厂配置正确")
        print("   - ✅ 现代化和传统处理器都可用")
        print("   - ✅ 主应用程序中PLL窗口工作正常")
        print("   - ✅ 未发现需要清理的旧文件")
    else:
        print("\n❌ 部分检查失败，需要进一步处理")
        if not success1:
            print("   - ❌ PLL窗口状态检查失败")
        if not success2:
            print("   - ❌ 主应用程序PLL测试失败")
        if not success3:
            print("   - ❌ 发现需要清理的旧文件")
        sys.exit(1)
