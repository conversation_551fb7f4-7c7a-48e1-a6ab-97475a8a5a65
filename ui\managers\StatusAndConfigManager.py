"""
状态和配置管理器
负责管理状态栏更新、语言切换、模拟模式切换等功能
"""

from PyQt5.QtWidgets import QMessageBox
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class StatusAndConfigManager:
    """状态和配置管理器，管理状态栏和配置相关功能"""
    
    def __init__(self, main_window):
        """初始化状态和配置管理器
        
        Args:
            main_window: 主窗口实例
        """
        self.main_window = main_window
    
    def update_status_bar(self):
        """更新状态栏的模式和端口信息"""
        logger.info("StatusAndConfigManager.update_status_bar() 被调用")

        if hasattr(self.main_window, 'spi_service') and self.main_window.spi_service:
            try:
                status = self.main_window.spi_service.get_connection_status()
                logger.info(f"获取到SPI服务状态: {status}")

                mode_text = status.get('mode', '未知')
                logger.info(f"原始模式文本: '{mode_text}'")

                # 标准化模式文本显示，支持大小写兼容
                if mode_text.lower() == 'hardware':
                    display_mode = "硬件模式"
                    self.main_window.status_mode_label.setStyleSheet("color: green")
                elif mode_text.lower() == 'simulation':
                    display_mode = "模拟模式"
                    self.main_window.status_mode_label.setStyleSheet("color: red")
                else:
                    display_mode = mode_text
                    self.main_window.status_mode_label.setStyleSheet("color: black")

                self.main_window.status_mode_label.setText(f"通信模式: {display_mode}")

                port_text = "未知"
                if mode_text.lower() == 'hardware':
                    current_port = status.get('port')
                    if current_port:
                        port_text = str(current_port)
                    else:
                        port_text = "未连接"
                elif mode_text.lower() == 'simulation':
                    port_text = "模拟模式"

                self.main_window.status_port_label.setText(f"端口: {port_text}")

                logger.info(f"状态栏已更新: 模式={display_mode}, 端口={port_text}")
            except Exception as e:
                logger.error(f"更新状态栏失败: {e}")
                self.main_window.status_mode_label.setText("通信模式: 获取失败")
                self.main_window.status_port_label.setText("端口: 获取失败")
        else:
            logger.warning("SPI Service 未在RegisterMainWindow中正确初始化，无法更新状态栏。")
            self.main_window.status_mode_label.setText("通信模式: 未知")
            self.main_window.status_port_label.setText("端口: 未知")
    
    def toggle_simulation_mode(self, checked):
        """切换模拟模式"""
        self.main_window.simulation_mode = checked
        if hasattr(self.main_window, 'spi_service'):
            # 调用正确的公共方法来设置模拟模式
            self.main_window.spi_service.set_simulation_mode(checked)
        
        # 更新状态栏
        mode_text = "模拟模式" if checked else "硬件模式"
        self.main_window.show_status_message(f"已切换到{mode_text}", 3000)
        
        # 保存模拟模式状态
        self.main_window.config_service.save_simulation_mode(checked)
    
    def toggle_preload(self, checked):
        """切换是否启用预读取功能
        
        Args:
            checked: 是否选中
        """
        # 这里可以添加预读取功能的逻辑
        # 例如：在选择寄存器时自动读取其值
        logger.info(f"预读取功能已{'启用' if checked else '禁用'}")
        
        # 可以保存这个设置到配置文件
        # self.main_window.config_service.save_preload_setting(checked)
    
    def handle_language_change(self, action):
        """处理语言切换"""
        if action:
            lang_code = action.data()
            # 委托给配置管理服务
            self.main_window.config_service.set_language(lang_code)
    
    def handle_ports_refreshed(self, ports_info: list):
        """处理端口刷新信号

        Args:
            ports_info: 包含端口信息的列表，每个元素是一个字典，包含 device 和 description
        """
        try:
            logger.info("StatusAndConfigManager: 收到端口刷新信号")

            # 防止重复处理相同的端口信息
            if hasattr(self, '_last_ports_info') and self._last_ports_info == ports_info:
                logger.info("StatusAndConfigManager: 端口信息未变化，跳过重复处理")
                return
            self._last_ports_info = ports_info.copy() if ports_info else []

            # 检查是否已经有其他管理器处理了这个信号
            if hasattr(self.main_window, 'spi_signal_manager') and hasattr(self.main_window.spi_signal_manager, '_last_ports_info'):
                if self.main_window.spi_signal_manager._last_ports_info == ports_info:
                    logger.info("StatusAndConfigManager: SPISignalManager已处理此端口信息，跳过")
                    return

            # 如果IO处理器存在且有刷新端口的方法，更新端口列表
            if hasattr(self.main_window.io_handler, 'update_port_list'):
                self.main_window.io_handler.update_port_list(ports_info)

            # 更新状态栏显示
            if ports_info:
                port_count = len(ports_info)
                self.main_window.show_status_message(f"发现 {port_count} 个可用端口", 3000)
            else:
                self.main_window.show_status_message("未发现可用端口", 3000)

        except Exception as e:
            logger.error(f"处理端口刷新信号时出错: {str(e)}")
            self.main_window.show_status_message("更新端口列表时出错", 3000)
    
    def handle_simulation_mode_changed(self, enabled: bool):
        """处理模拟模式变化信号

        Args:
            enabled: 是否启用模拟模式
        """
        try:
            logger.info(f"模拟模式已{'启用' if enabled else '禁用'}")
            # 更新本地模拟模式状态
            self.main_window.simulation_mode = enabled

            # 更新UI状态
            if hasattr(self.main_window, 'io_handler'):
                # 更新IO处理器的模拟模式状态（如果有相关方法）
                if hasattr(self.main_window.io_handler, 'set_simulation_mode'):
                    self.main_window.io_handler.set_simulation_mode(enabled)

            # 更新菜单项的选中状态（如果存在）
            for action in self.main_window.menuBar().actions():
                if action.text() == '工具(&T)':
                    for sub_action in action.menu().actions():
                        if sub_action.text() == '模拟通信(&W)':
                            sub_action.setChecked(enabled)
                            break
                    break

            # 立即更新状态栏以反映模式变化
            self.update_status_bar()

            # 显示切换消息
            mode_text = "模拟模式" if enabled else "硬件模式"
            self.main_window.show_status_message(f"已切换到{mode_text}", 3000)

        except Exception as e:
            logger.error(f"处理模拟模式变化信号时出错: {str(e)}")
    
    def handle_connection_status_changed(self, connected: bool):
        """处理连接状态变化信号
        
        Args:
            connected: 是否已连接
        """
        try:
            logger.info(f"SPI连接状态变化: {'已连接' if connected else '已断开'}")
            
            # 获取详细的连接状态信息
            status_info = self.main_window.spi_service.get_connection_status()
            
            # 更新UI状态
            if hasattr(self.main_window, 'io_handler'):
                # 更新IO处理器的连接状态（如果有相关方法）
                if hasattr(self.main_window.io_handler, 'update_connection_status'):
                    self.main_window.io_handler.update_connection_status(connected)
            
            # 根据连接状态更新按钮状态
            self.main_window.io_handler.toggle_buttons(connected)
            
            # 更新状态栏显示
            if connected:
                port = status_info.get('port', 'Unknown')
                mode = status_info.get('mode', 'Unknown')
                self.main_window.show_status_message(f"已连接到 {port} ({mode} 模式)", 3000)
            else:
                error = status_info.get('last_error', '')
                message = "连接已断开" + (f": {error}" if error else "")
                self.main_window.show_status_message(message, 3000)
                
                # 如果是因为错误断开，且不在模拟模式下，显示错误对话框
                if error and not self.main_window.simulation_mode:
                    QMessageBox.warning(self.main_window, "连接错误", message)
            
        except Exception as e:
            logger.error(f"处理连接状态变化信号时出错: {str(e)}")
    
    def show_status_message(self, message, timeout=3000):
        """统一显示状态栏消息"""
        if hasattr(self.main_window, "status_bar"):
            self.main_window.status_bar.showMessage(message, timeout)
    
    def show_advanced_settings(self):
        """显示高级设置对话框"""
        # 这里可以显示一个更复杂的设置对话框，允许用户配置更多选项
        self._safe_show_message(
            "高级设置",
            "高级设置功能正在开发中。\n"
            "未来版本将支持更多自定义选项。",
            QMessageBox.Information
        )
    
    def save_window_state(self):
        """保存窗口状态"""
        try:
            # 保存窗口几何信息
            self.main_window.settings.setValue("geometry", self.main_window.saveGeometry())
            self.main_window.settings.setValue("windowState", self.main_window.saveState())
            
            # 保存模拟模式状态
            self.main_window.settings.setValue("simulation_mode", self.main_window.simulation_mode)
            
            logger.debug("窗口状态已保存")
        except Exception as e:
            logger.error(f"保存窗口状态时出错: {str(e)}")
    
    def restore_window_state(self):
        """恢复窗口状态"""
        try:
            # 恢复窗口几何信息
            geometry = self.main_window.settings.value("geometry")
            if geometry:
                self.main_window.restoreGeometry(geometry)
            
            window_state = self.main_window.settings.value("windowState")
            if window_state:
                self.main_window.restoreState(window_state)
            
            logger.debug("窗口状态已恢复")
        except Exception as e:
            logger.error(f"恢复窗口状态时出错: {str(e)}")
    
    def get_application_info(self):
        """获取应用程序信息"""
        return {
            'name': 'FSJ04832 寄存器配置工具',
            'version': '1.0.0',
            'description': '用于配置和管理FSJ04832设备寄存器的图形界面工具',
            'copyright': '© 2023 FSJ团队. 保留所有权利.',
            'support_email': '<EMAIL>'
        }
    
    def cleanup_resources(self):
        """清理资源"""
        try:
            # 保存当前状态
            self.save_window_state()
            
            # 清理其他资源
            logger.debug("状态和配置管理器资源已清理")
        except Exception as e:
            logger.error(f"清理状态和配置管理器资源时出错: {str(e)}")

    def _safe_show_message(self, title, message, icon_type=None):
        """安全地显示消息框，避免与主窗口事件过滤器冲突

        Args:
            title: 消息框标题
            message: 消息内容
            icon_type: 图标类型 (QMessageBox.Information, Warning, Critical等)
        """
        try:
            # 临时禁用主窗口的事件过滤器
            main_window_filter_disabled = False
            if hasattr(self.main_window, 'eventFilter'):
                try:
                    self.main_window.removeEventFilter(self.main_window)
                    main_window_filter_disabled = True
                    logger.debug("临时禁用主窗口事件过滤器")
                except Exception as e:
                    logger.debug(f"无法禁用主窗口事件过滤器: {str(e)}")

            # 创建独立的消息框，不使用主窗口作为父窗口
            msg = QMessageBox()
            msg.setWindowTitle(title)
            msg.setText(message)

            # 设置图标
            if icon_type:
                msg.setIcon(icon_type)
            else:
                msg.setIcon(QMessageBox.Information)

            # 设置按钮
            msg.setStandardButtons(QMessageBox.Ok)
            msg.setDefaultButton(QMessageBox.Ok)

            # 设置窗口属性，确保消息框正常显示
            from PyQt5.QtCore import Qt
            msg.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
            msg.setModal(True)

            # 显示消息框
            result = msg.exec_()

            # 恢复主窗口的事件过滤器
            if main_window_filter_disabled:
                try:
                    self.main_window.installEventFilter(self.main_window)
                    logger.debug("恢复主窗口事件过滤器")
                except Exception as e:
                    logger.debug(f"无法恢复主窗口事件过滤器: {str(e)}")

            logger.info(f"安全消息框显示完成: {title}")
            return result

        except Exception as e:
            logger.error(f"显示安全消息框时出错: {str(e)}")
            # 如果安全方式失败，尝试使用状态栏显示消息
            try:
                if hasattr(self.main_window, 'status_bar'):
                    self.main_window.status_bar.showMessage(f"{title}: {message}", 5000)
                    logger.info(f"通过状态栏显示消息: {title}")
            except Exception as e2:
                logger.error(f"状态栏显示消息也失败: {str(e2)}")
            return QMessageBox.Ok
