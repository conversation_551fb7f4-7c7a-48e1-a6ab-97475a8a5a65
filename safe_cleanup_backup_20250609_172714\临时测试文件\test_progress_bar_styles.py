#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
进度条样式测试脚本
测试软件中所有进度条的绿色样式
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QProgressBar, QGroupBox
from PyQt5.QtCore import QTimer
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class ProgressBarTestWindow(QWidget):
    """进度条样式测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.setup_timers()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("进度条样式测试")
        self.setGeometry(100, 100, 800, 600)
        
        layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel("进度条样式测试 - 所有进度条应显示为绿色")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)
        
        # 默认样式进度条
        self.create_default_progress_group(layout)
        
        # 高对比度样式进度条
        self.create_high_contrast_progress_group(layout)
        
        # 紧凑样式进度条
        self.create_compact_progress_group(layout)
        
        # 测试按钮
        self.create_test_buttons(layout)
        
        # 状态标签
        self.status_label = QLabel("准备测试...")
        layout.addWidget(self.status_label)
        
        self.setLayout(layout)
        
    def create_default_progress_group(self, layout):
        """创建默认样式进度条组"""
        group = QGroupBox("默认绿色样式")
        group_layout = QVBoxLayout()
        
        # 进度条1 - 确定进度
        self.progress1 = QProgressBar()
        self.progress1.setRange(0, 100)
        self.progress1.setValue(30)
        self.apply_green_style(self.progress1, "default")
        
        group_layout.addWidget(QLabel("确定进度 (30%):"))
        group_layout.addWidget(self.progress1)
        
        # 进度条2 - 不确定进度
        self.progress2 = QProgressBar()
        self.progress2.setRange(0, 0)  # 不确定进度
        self.apply_green_style(self.progress2, "default")
        
        group_layout.addWidget(QLabel("不确定进度:"))
        group_layout.addWidget(self.progress2)
        
        group.setLayout(group_layout)
        layout.addWidget(group)
        
    def create_high_contrast_progress_group(self, layout):
        """创建高对比度样式进度条组"""
        group = QGroupBox("高对比度绿色样式")
        group_layout = QVBoxLayout()
        
        # 进度条3 - 高对比度
        self.progress3 = QProgressBar()
        self.progress3.setRange(0, 100)
        self.progress3.setValue(60)
        self.apply_green_style(self.progress3, "high_contrast")
        
        group_layout.addWidget(QLabel("高对比度进度 (60%):"))
        group_layout.addWidget(self.progress3)
        
        group.setLayout(group_layout)
        layout.addWidget(group)
        
    def create_compact_progress_group(self, layout):
        """创建紧凑样式进度条组"""
        group = QGroupBox("紧凑绿色样式")
        group_layout = QVBoxLayout()
        
        # 进度条4 - 紧凑样式
        self.progress4 = QProgressBar()
        self.progress4.setRange(0, 100)
        self.progress4.setValue(80)
        self.apply_green_style(self.progress4, "compact")
        
        group_layout.addWidget(QLabel("紧凑进度 (80%):"))
        group_layout.addWidget(self.progress4)
        
        group.setLayout(group_layout)
        layout.addWidget(group)
        
    def create_test_buttons(self, layout):
        """创建测试按钮"""
        button_layout = QHBoxLayout()
        
        # 开始动画按钮
        self.start_btn = QPushButton("开始动画测试")
        self.start_btn.clicked.connect(self.start_animation)
        button_layout.addWidget(self.start_btn)
        
        # 停止动画按钮
        self.stop_btn = QPushButton("停止动画")
        self.stop_btn.clicked.connect(self.stop_animation)
        button_layout.addWidget(self.stop_btn)
        
        # 重置按钮
        self.reset_btn = QPushButton("重置进度")
        self.reset_btn.clicked.connect(self.reset_progress)
        button_layout.addWidget(self.reset_btn)
        
        # 测试插件进度条按钮
        self.test_plugins_btn = QPushButton("测试插件进度条")
        self.test_plugins_btn.clicked.connect(self.test_plugin_progress_bars)
        button_layout.addWidget(self.test_plugins_btn)
        
        layout.addLayout(button_layout)
        
    def apply_green_style(self, progress_bar, style_type="default"):
        """应用绿色样式"""
        try:
            from ui.styles.ProgressBarStyleManager import apply_green_progress_style
            apply_green_progress_style(progress_bar, style_type)
            logger.info(f"已应用 {style_type} 样式")
        except ImportError:
            logger.warning("样式管理器不可用，使用内联样式")
            # 备用内联样式
            progress_bar.setStyleSheet("""
                QProgressBar {
                    border: 2px solid #C0C0C0;
                    border-radius: 5px;
                    background-color: #F0F0F0;
                    text-align: center;
                    font-weight: bold;
                    color: #333333;
                }
                
                QProgressBar::chunk {
                    background-color: qlineargradient(
                        x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #7ED321,
                        stop: 0.5 #5CB85C,
                        stop: 1 #4CAF50
                    );
                    border-radius: 3px;
                    margin: 1px;
                }
            """)
        except Exception as e:
            logger.error(f"应用样式失败: {str(e)}")
            
    def setup_timers(self):
        """设置定时器"""
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self.update_progress)
        self.animation_value = 0
        self.animation_direction = 1
        
    def start_animation(self):
        """开始动画"""
        self.animation_timer.start(50)  # 每50ms更新一次
        self.status_label.setText("动画测试进行中...")
        
    def stop_animation(self):
        """停止动画"""
        self.animation_timer.stop()
        self.status_label.setText("动画已停止")
        
    def update_progress(self):
        """更新进度条动画"""
        # 更新动画值
        self.animation_value += self.animation_direction * 2
        
        # 反向
        if self.animation_value >= 100:
            self.animation_direction = -1
        elif self.animation_value <= 0:
            self.animation_direction = 1
            
        # 更新进度条
        self.progress1.setValue(self.animation_value)
        self.progress3.setValue((self.animation_value + 20) % 100)
        self.progress4.setValue((self.animation_value + 40) % 100)
        
    def reset_progress(self):
        """重置进度条"""
        self.stop_animation()
        self.progress1.setValue(30)
        self.progress3.setValue(60)
        self.progress4.setValue(80)
        self.status_label.setText("进度已重置")
        
    def test_plugin_progress_bars(self):
        """测试插件中的进度条"""
        try:
            self.status_label.setText("正在测试插件进度条...")
            
            # 查找主窗口
            app = QApplication.instance()
            main_window = None
            
            for widget in app.topLevelWidgets():
                if hasattr(widget, 'plugin_integration_service') or hasattr(widget, 'plugin_service'):
                    main_window = widget
                    break
            
            if not main_window:
                self.status_label.setText("未找到主窗口")
                return
            
            # 更新主窗口中的进度条
            from ui.styles.ProgressBarStyleManager import update_all_progress_bars
            count = update_all_progress_bars(main_window)
            
            # 检查插件窗口
            plugin_count = 0
            if hasattr(main_window, 'plugin_integration_service'):
                plugin_service = main_window.plugin_integration_service
                if hasattr(plugin_service, 'window_service'):
                    window_service = plugin_service.window_service
                    for plugin_name, window in window_service.plugin_windows.items():
                        if window and hasattr(window, 'findChildren'):
                            plugin_progress_count = update_all_progress_bars(window)
                            plugin_count += plugin_progress_count
                            logger.info(f"插件 {plugin_name} 更新了 {plugin_progress_count} 个进度条")
            
            total_count = count + plugin_count
            self.status_label.setText(f"已更新 {total_count} 个进度条样式 (主窗口: {count}, 插件: {plugin_count})")
            
        except Exception as e:
            error_msg = f"测试插件进度条失败: {str(e)}"
            self.status_label.setText(error_msg)
            logger.error(error_msg)


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    test_window = ProgressBarTestWindow()
    test_window.show()
    
    print("🎨 进度条样式测试")
    print("功能说明：")
    print("1. 显示不同样式的绿色进度条")
    print("2. 测试动画效果")
    print("3. 验证插件中的进度条样式")
    print("4. 所有进度条应显示为绿色主题")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
