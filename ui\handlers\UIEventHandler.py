"""
UI事件处理器
负责处理主窗口的各种UI事件
"""

import os
import sys
import traceback
from PyQt5.QtWidgets import QMessageBox
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class UIEventHandler:
    """UI事件处理器，管理主窗口的各种UI事件"""
    
    def __init__(self, main_window):
        """初始化UI事件处理器
        
        Args:
            main_window: 主窗口实例
        """
        self.main_window = main_window
    
    def handle_read_button_click(self):
        """处理读取按钮点击事件"""
        # 获取当前选中的寄存器地址
        addr = self._get_current_register_address()
        if addr:
            logger.info(f"UIEventHandler: 读取按钮点击，获取到地址: {addr}")
            self.handle_read_requested(int(addr, 16) if isinstance(addr, str) else addr)
        else:
            logger.warning("UIEventHandler: 读取按钮点击，但无法获取当前寄存器地址")

    def _get_current_register_address(self):
        """获取当前选中的寄存器地址"""
        try:
            # 优先使用主窗口的selected_register_addr属性
            if hasattr(self.main_window, 'selected_register_addr') and self.main_window.selected_register_addr:
                logger.debug(f"UIEventHandler: 从主窗口获取当前寄存器地址: {self.main_window.selected_register_addr}")
                return self.main_window.selected_register_addr

            # 如果没有，尝试从树形控件获取
            addr = self.main_window._get_register_addr_from_tree_item()
            if addr:
                logger.debug(f"UIEventHandler: 从树形控件获取当前寄存器地址: {addr}")
                return addr

            logger.warning("UIEventHandler: 无法获取当前寄存器地址，所有方法都失败")
            return None
        except Exception as e:
            logger.error(f"UIEventHandler: 获取当前寄存器地址时出错: {str(e)}")
            return None
    
    def handle_write_button_click(self):
        """处理写入按钮点击事件"""
        try:
            # 获取当前选中的寄存器地址（修复手动写入地址错误问题）
            addr = self._get_current_register_address()
            if not addr:
                QMessageBox.warning(self.main_window, "错误", "请先选择一个寄存器")
                return

            # 获取用户输入值
            input_value = self.main_window.io_handler.get_current_value()
            logger.info(f"用户输入值: {input_value}")
            if input_value is None:
                QMessageBox.warning(self.main_window, "错误", "请输入有效的值")
                return

            # 更新寄存器值（使用当前选中的寄存器地址）
            addr_int = int(addr, 16) if isinstance(addr, str) else addr
            logger.info(f"手动写入: 地址={addr} (0x{addr_int:02X}), 值=0x{input_value:04X}")
            self.handle_write_requested(addr_int, input_value)
        except Exception as e:
            logger.error(f"写入操作失败: {str(e)}")
            QMessageBox.warning(self.main_window, "错误", f"写入操作失败: {str(e)}")
    
    def handle_read_requested(self, addr):
        """处理读取请求"""
        # 将整数地址转换为标准格式的字符串地址
        addr_str = f"0x{addr:02X}" if isinstance(addr, int) else addr
        
        if self.main_window.simulation_mode:
            # 模拟读取
            value = self.main_window.spi_service.read_register(addr_str)
            self.main_window.handle_spi_result(addr_str, value, True)
        else:
            # 实际读取
            self.main_window._execute_spi_read(addr_str)
    
    def handle_write_requested(self, addr, value):
        """处理写入请求"""
        # 将整数地址转换为标准格式的字符串地址
        addr_str = f"0x{addr:02X}" if isinstance(addr, int) else addr
        self.main_window._update_register_value_and_display(addr_str, value)
    
    def handle_io_write_request(self, addr, value):
        """处理来自 IO Handler 的直接写入请求 (例如，通过回车确认输入)"""
        logger.info(f"MainWindow: 收到来自 IO Handler 的写入请求, 地址: 0x{addr:02X}, 值: 0x{value:04X}")
        # 1. 更新 RegisterManager 中的值
        success = self.main_window.register_manager.set_register_value(addr, value)
        if success:
            # 2. 执行SPI写入操作
            self.main_window._execute_spi_write(addr, value)
        else:
            logger.error(f"更新寄存器管理器中的值失败: 地址 0x{addr:02X}, 值 0x{value:04X}")
    
    def handle_value_changed(self, addr, new_value):
        """处理输入值变化事件"""
        # 标准化地址
        normalized_addr = self.main_window._normalize_register_address(addr)
        
        # 记录当前输入值，但不立即触发写操作
        # 只有当用户点击写入按钮时才执行写操作
        if normalized_addr == self.main_window.selected_register_addr:
            # 记录原值用于显示变化
            old_value = self.main_window.current_register_value
            
            # 仅更新显示，不执行写操作
            self.main_window.current_register_value = new_value
            
            # 仅更新位字段表格显示，不触发写操作
            if hasattr(self.main_window.table_handler, 'show_bit_fields'):
                self.main_window.table_handler.show_bit_fields(normalized_addr, new_value)
                
            # 刷新视图确保显示更新
            self.main_window.refresh_view()
            
            # 在状态栏显示寄存器变化详情
            reg_num = int(normalized_addr, 16) if isinstance(normalized_addr, str) else normalized_addr
            self.main_window.show_status_message(f"寄存器 R{reg_num} (0x{reg_num:02X}) 值已修改: 0x{old_value:04X} → 0x{new_value:04X}", 5000)
    
    def handle_bit_field_selected(self, addr):
        """处理位段选中事件，在批量操作期间不跳转
        
        Args:
            addr: 寄存器地址
        """
        # 只有在非批量操作时才允许跳转
        if not self.main_window._is_in_batch_operation():
            self.main_window.tree_handler.select_register_by_addr(addr)
        else:
            logger.debug(f"批量操作期间忽略位段选中跳转请求: {addr}")
    
    def toggle_simulation_mode(self, checked):
        """切换模拟模式"""
        self.main_window.simulation_mode = checked
        if hasattr(self.main_window, 'spi_service'):
            # 调用正确的公共方法来设置模拟模式
            self.main_window.spi_service.set_simulation_mode(checked)
            
        # 更新状态栏
        mode_text = "模拟模式" if checked else "硬件模式"
        self.main_window.show_status_message(f"已切换到{mode_text}", 3000)
        
        # 保存模拟模式状态
        self.main_window.config_service.save_simulation_mode(checked)
        
        # 连接状态变化信号
        if hasattr(self.main_window.spi_service, 'connection_status_changed'):
            self.main_window.spi_service.connection_status_changed.connect(self.main_window._update_status_bar)
        else:
            logger.warning("SPIService does not have a 'connection_status_changed' signal or it's None.")
    
    def toggle_preload(self, checked):
        """切换是否启用预读取功能
        
        Args:
            checked: 是否选中
        """
        # 这里可以添加预读取功能的逻辑
        # 例如：在选择寄存器时自动读取其值
        logger.info(f"预读取功能已{'启用' if checked else '禁用'}")
        
        # 可以保存这个设置到配置文件
        # self.config_service.save_preload_setting(checked)
    
    def show_advanced_settings(self):
        """显示高级设置对话框"""
        # 这里可以显示一个更复杂的设置对话框，允许用户配置更多选项
        QMessageBox.information(
            self.main_window, 
            "高级设置", 
            "高级设置功能正在开发中。\n"
            "未来版本将支持更多自定义选项。",
            QMessageBox.Ok
        )
    
    def show_user_manual(self):
        """显示用户手册"""
        # 检查用户手册文件是否存在
        manual_path = self.main_window.resource_path('docs/user_manual.pdf')
        if os.path.exists(manual_path):
            # 使用系统默认程序打开PDF文件
            try:
                if sys.platform == 'win32':
                    os.startfile(manual_path)  # Windows特有方法
                elif sys.platform == 'darwin':
                    import subprocess
                    subprocess.call(['open', manual_path])  # macOS
                else:
                    import subprocess
                    subprocess.call(['xdg-open', manual_path])  # Linux
            except Exception as e:
                self.main_window.show_status_message(f"打开用户手册失败: {str(e)}", 5000)
                QMessageBox.warning(
                    self.main_window,
                    "打开失败",
                    f"无法打开用户手册文件:\n{str(e)}\n\n"
                    "请手动打开文件或联系技术支持。",
                    QMessageBox.Ok
                )
        else:
            # 文件不存在，显示提示信息
            QMessageBox.information(
                self.main_window,
                "用户手册",
                "用户手册文件不存在。\n\n"
                "请联系技术支持获取最新的用户手册。",
                QMessageBox.Ok
            )
    
    def show_about_dialog(self):
        """显示关于对话框"""
        try:
            # 获取版本信息
            from core.services.version.VersionService import VersionService
            version_service = VersionService.instance()

            app_name = version_service.get_app_name()
            version = version_service.get_version_string()
            description = version_service.get_app_description()
            company = version_service.get_company()
            copyright_info = version_service.get_copyright()
            build_date = version_service.get_build_date()

            # 构建关于信息
            about_text = f"""
            <h3>{app_name}</h3>
            <p><b>版本:</b> {version}</p>
            <p><b>描述:</b> {description}</p>
            <p><b>开发者:</b> {company}</p>
            <p><b>版权:</b> {copyright_info}</p>
            """

            if build_date:
                about_text += f"<p><b>构建日期:</b> {build_date}</p>"

            about_text += """
            <hr>
            <p>本软件提供寄存器读写、批量操作、配置管理等功能。</p>
            <p>如有问题或建议，请联系技术支持。</p>
            """

        except Exception as e:
            logger.warning(f"无法获取版本信息: {str(e)}")
            # 使用默认信息
            about_text = """
            <h3>FSJ04832 寄存器配置工具</h3>
            <p><b>版本:</b> 1.0.0</p>
            <p><b>描述:</b> 用于配置和管理寄存器的专业工具</p>
            <p><b>开发者:</b> 开发团队</p>
            <p><b>版权:</b> © 2024 公司名称</p>
            <hr>
            <p>本软件提供寄存器读写、批量操作、配置管理等功能。</p>
            <p>如有问题或建议，请联系技术支持。</p>
            """

        QMessageBox.about(self.main_window, "关于", about_text)
    
    def handle_language_change(self, action):
        """处理语言切换"""
        if action:
            lang_code = action.data()
            # 委托给配置管理服务
            self.main_window.config_service.set_language(lang_code)
    
    def global_exception_handler(self, exc_type, exc_value, exc_traceback):
        """全局异常处理器"""
        error_msg = "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        logger.error(f"未捕获的异常: {error_msg}")
        QMessageBox.critical(
            self.main_window, 
            "程序错误", 
            f"发生未捕获的异常:\n{str(exc_value)}\n\n详细信息已记录到日志文件"
        )
