#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试和临时文件清理脚本
清理项目中的调试文件、临时文件和其他不必要的文件
"""

import os
import shutil
from datetime import datetime

# 项目根目录
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
DEBUG_BACKUP_DIR = os.path.join(PROJECT_ROOT, 'debug_backup_' + datetime.now().strftime('%Y%m%d_%H%M%S'))

# 调试文件列表
DEBUG_FILES = [
    'debug_actual_window.py',
    'debug_clk_output_window.py', 
    'debug_divider_method.py',
    'debug_divider_values.py',
    'debug_frequency_update.py',
    'debug_output_initialization.py',
    'debug_settext_calls.py',
    'debug_widget_mapping.py',
    'debug_write_timeout.py'
]

# 演示文件列表
DEMO_FILES = [
    'demo_modern_register_io_handler.py',
    'demo_required_features.py'
]

# 诊断和分析文件
ANALYSIS_FILES = [
    'analyze_pll_removal.py',
    'diagnose_modern_factory_issue.py'
]

# 修复和移除脚本
UTILITY_FILES = [
    'fix_dll_issue.py',
    'remove_legacy_pll.py'
]

# 临时测试文件
TEMP_TEST_FILES = [
    'final_clk_output_test.py',
    'quick_test_validation.py',
    'run_complete_tests.py',
    'simple_test_fix.py'
]

# 备份文件
BACKUP_FILES = [
    'test_pll_functionality.py.backup_20250603_132234',
    'test_scroll_area_fix.py.backup_20250603_132234'
]

def create_debug_backup():
    """创建调试文件备份目录"""
    print(f"创建调试文件备份目录: {DEBUG_BACKUP_DIR}")
    os.makedirs(DEBUG_BACKUP_DIR, exist_ok=True)
    return DEBUG_BACKUP_DIR

def backup_and_remove_category(file_list, category_name):
    """备份并删除指定类别的文件"""
    print(f"\n清理{category_name}:")
    removed_count = 0
    
    for file_name in file_list:
        file_path = os.path.join(PROJECT_ROOT, file_name)
        if os.path.exists(file_path):
            # 备份文件
            backup_path = os.path.join(DEBUG_BACKUP_DIR, file_name)
            try:
                shutil.copy2(file_path, backup_path)
                os.remove(file_path)
                print(f"  ✓ 删除: {file_name}")
                removed_count += 1
            except Exception as e:
                print(f"  ✗ 删除失败: {file_name} - {e}")
        else:
            print(f"  - 文件不存在: {file_name}")
    
    print(f"  删除了 {removed_count} 个{category_name}")
    return removed_count

def organize_useful_scripts():
    """整理有用的脚本到tools目录"""
    tools_dir = os.path.join(PROJECT_ROOT, 'tools')
    os.makedirs(tools_dir, exist_ok=True)
    
    useful_scripts = {
        'cleanup_test_files.py': '测试文件清理脚本',
        'cleanup_debug_files.py': '调试文件清理脚本'
    }
    
    print(f"\n整理有用脚本到tools目录:")
    moved_count = 0
    
    for script_name, description in useful_scripts.items():
        source_path = os.path.join(PROJECT_ROOT, script_name)
        dest_path = os.path.join(tools_dir, script_name)
        
        if os.path.exists(source_path) and script_name != 'cleanup_debug_files.py':  # 不移动当前正在运行的脚本
            try:
                shutil.move(source_path, dest_path)
                print(f"  ✓ 移动: {script_name} ({description})")
                moved_count += 1
            except Exception as e:
                print(f"  ✗ 移动失败: {script_name} - {e}")
    
    print(f"  移动了 {moved_count} 个脚本")
    return moved_count

def clean_log_files():
    """清理旧的日志文件"""
    print(f"\n清理日志文件:")
    cleaned_count = 0
    
    # 清理test_suite/logs中的旧日志
    test_logs_dir = os.path.join(PROJECT_ROOT, 'test_suite', 'logs')
    if os.path.exists(test_logs_dir):
        for log_file in os.listdir(test_logs_dir):
            if log_file.endswith('.log') and '20250603' in log_file:
                log_path = os.path.join(test_logs_dir, log_file)
                try:
                    os.remove(log_path)
                    print(f"  ✓ 删除旧日志: {log_file}")
                    cleaned_count += 1
                except Exception as e:
                    print(f"  ✗ 删除失败: {log_file} - {e}")
    
    # 清理logs目录中的旧日志
    logs_dir = os.path.join(PROJECT_ROOT, 'logs')
    if os.path.exists(logs_dir):
        for log_file in os.listdir(logs_dir):
            if log_file.endswith('.log') and 'register_app_20250424' in log_file:
                log_path = os.path.join(logs_dir, log_file)
                try:
                    os.remove(log_path)
                    print(f"  ✓ 删除旧日志: {log_file}")
                    cleaned_count += 1
                except Exception as e:
                    print(f"  ✗ 删除失败: {log_file} - {e}")
    
    print(f"  清理了 {cleaned_count} 个日志文件")
    return cleaned_count

def generate_final_cleanup_report():
    """生成最终清理报告"""
    report_content = f"""# 项目文件最终清理报告

## 清理时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 清理内容
1. **调试文件**: {len(DEBUG_FILES)} 个
2. **演示文件**: {len(DEMO_FILES)} 个  
3. **分析文件**: {len(ANALYSIS_FILES)} 个
4. **工具脚本**: {len(UTILITY_FILES)} 个
5. **临时测试**: {len(TEMP_TEST_FILES)} 个
6. **备份文件**: {len(BACKUP_FILES)} 个

## 备份位置
- 测试文件备份: test_backup_20250604_141306/
- 调试文件备份: {os.path.basename(DEBUG_BACKUP_DIR)}/

## 项目结构优化
```
项目根目录/
├── core/              # 核心业务逻辑
├── ui/                # 用户界面组件
├── utils/             # 工具函数
├── test_suite/        # 测试套件
│   ├── functional/    # 功能测试
│   ├── integration/   # 集成测试
│   ├── ui/           # UI测试
│   ├── unit/         # 单元测试
│   ├── performance/  # 性能测试
│   └── regression/   # 回归测试
├── tools/            # 开发工具脚本
├── docs/             # 文档
├── config/           # 配置文件
└── main.py           # 主程序入口
```

## 建议
1. 定期运行测试套件确保功能正常
2. 保持项目结构清晰，避免临时文件堆积
3. 重要的调试脚本可以从备份中恢复
4. 使用版本控制忽略临时文件和日志文件
"""
    
    report_path = os.path.join(PROJECT_ROOT, 'FINAL_CLEANUP_REPORT.md')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"\n✓ 生成最终清理报告: FINAL_CLEANUP_REPORT.md")

def main():
    """主函数"""
    print("=" * 60)
    print("调试和临时文件清理工具")
    print("=" * 60)
    
    # 创建备份
    create_debug_backup()
    
    # 清理各类文件
    debug_count = backup_and_remove_category(DEBUG_FILES, "调试文件")
    demo_count = backup_and_remove_category(DEMO_FILES, "演示文件")
    analysis_count = backup_and_remove_category(ANALYSIS_FILES, "分析文件")
    utility_count = backup_and_remove_category(UTILITY_FILES, "工具脚本")
    temp_count = backup_and_remove_category(TEMP_TEST_FILES, "临时测试文件")
    backup_count = backup_and_remove_category(BACKUP_FILES, "备份文件")
    
    # 整理有用脚本
    script_count = organize_useful_scripts()
    
    # 清理日志文件
    log_count = clean_log_files()
    
    # 生成报告
    generate_final_cleanup_report()
    
    # 总结
    total_removed = debug_count + demo_count + analysis_count + utility_count + temp_count + backup_count
    print(f"\n" + "=" * 60)
    print("清理完成!")
    print(f"总计删除文件: {total_removed} 个")
    print(f"整理脚本: {script_count} 个")
    print(f"清理日志: {log_count} 个")
    print(f"备份位置: {DEBUG_BACKUP_DIR}")
    print("=" * 60)

if __name__ == "__main__":
    main()
