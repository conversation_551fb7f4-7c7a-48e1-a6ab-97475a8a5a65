#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
现代化处理器集成测试
验证现代化处理器在主窗口中的集成和信号连接
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt, QTimer

from ui.managers.InitializationManager import InitializationManager
from core.services.register.RegisterManager import RegisterManager


class MockMainWindow:
    """模拟主窗口"""
    def __init__(self):
        self.register_manager = None
        self.tree_handler = None
        self.table_handler = None
        self.io_handler = None
        self.tool_window_factory = None
        
        # 模拟工具窗口工厂
        self.tool_window_factory = Mock()
        self.tool_window_factory.modern_factory = Mock()


class MockSPIService:
    """模拟SPI服务"""
    def __init__(self):
        self.registers = {}
        # 添加必要的信号
        from PyQt5.QtCore import pyqtSignal, QObject

        class SignalContainer(QObject):
            spi_operation_complete = pyqtSignal(str, int, object)

        self.signal_container = SignalContainer()
        self.spi_operation_complete = self.signal_container.spi_operation_complete

    def read_register(self, addr):
        return self.registers.get(addr, 0x1234)

    def write_register(self, addr, value):
        self.registers[addr] = value


class TestModernHandlersIntegration(unittest.TestCase):
    """现代化处理器集成测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """设置测试"""
        # 创建模拟的寄存器配置
        self.mock_registers = {
            "0x00": {
                "bits": [
                    {
                        "bit": "15:0",
                        "name": "DA_DEVICE_VERSION",
                        "default": "0001001100000000",
                        "widget_name": "deviceVersion",
                        "widget_type": "label",
                        "options": None,
                        "description": "Device version"
                    }
                ]
            },
            "0x02": {
                "bits": [
                    {
                        "bit": "0",
                        "name": "POWERDOWN",
                        "default": "0",
                        "widget_name": "powerDown",
                        "widget_type": "checkbox",
                        "options": None,
                        "description": "Power down control"
                    }
                ]
            }
        }
        
        # 创建模拟主窗口
        self.main_window = MockMainWindow()
        
        # 创建RegisterManager
        self.register_manager = RegisterManager(self.mock_registers)
        self.main_window.register_manager = self.register_manager
        
        # 创建模拟SPI服务
        self.mock_spi_service = MockSPIService()

        # 手动设置SPI服务（模拟InitializationManager的行为）
        self.main_window.spi_service = self.mock_spi_service

        # 创建InitializationManager
        self.init_manager = InitializationManager(self.main_window)
    
    def tearDown(self):
        """清理测试"""
        # 清理处理器
        if hasattr(self.main_window, 'tree_handler') and self.main_window.tree_handler:
            self.main_window.tree_handler.close()
        if hasattr(self.main_window, 'table_handler') and self.main_window.table_handler:
            self.main_window.table_handler.close()
        if hasattr(self.main_window, 'io_handler') and self.main_window.io_handler:
            self.main_window.io_handler.close()
    
    def test_modern_handlers_creation(self):
        """测试现代化处理器创建"""
        # 创建处理器
        self.init_manager.create_handlers()
        
        # 验证处理器已创建
        self.assertIsNotNone(self.main_window.tree_handler)
        self.assertIsNotNone(self.main_window.table_handler)
        self.assertIsNotNone(self.main_window.io_handler)
        
        # 验证处理器类型
        self.assertEqual(type(self.main_window.tree_handler).__name__, 'ModernRegisterTreeHandler')
        self.assertEqual(type(self.main_window.table_handler).__name__, 'ModernRegisterTableHandler')
        self.assertEqual(type(self.main_window.io_handler).__name__, 'ModernRegisterIOHandler')
    
    def test_register_manager_integration(self):
        """测试RegisterManager集成"""
        self.init_manager.create_handlers()

        # 验证所有处理器都有RegisterManager引用
        self.assertEqual(self.main_window.tree_handler.register_manager, self.register_manager)
        self.assertEqual(self.main_window.table_handler.register_manager, self.register_manager)
        self.assertEqual(self.main_window.io_handler.register_manager, self.register_manager)

    def test_tree_handler_functionality(self):
        """测试树处理器功能"""
        self.init_manager.create_handlers()
        tree_handler = self.main_window.tree_handler

        # 测试树填充
        tree_handler.populate_tree_widget()

        # 验证树结构
        self.assertEqual(tree_handler.tree_widget.topLevelItemCount(), 1)
        root_item = tree_handler.tree_widget.topLevelItem(0)
        self.assertEqual(root_item.text(0), "Registers")
        self.assertEqual(root_item.childCount(), 2)  # 0x00, 0x02

        # 验证寄存器项
        self.assertEqual(len(tree_handler.register_items), 2)
        self.assertIn("0x00", tree_handler.register_items)
        self.assertIn("0x02", tree_handler.register_items)

    def test_table_handler_functionality(self):
        """测试表格处理器功能"""
        self.init_manager.create_handlers()
        table_handler = self.main_window.table_handler

        # 测试表格更新
        table_handler.update_register_display("0x00", 0x1234)

        # 验证表格有数据
        self.assertGreater(table_handler.table_widget.rowCount(), 0)

    def test_io_handler_functionality(self):
        """测试IO处理器功能"""
        self.init_manager.create_handlers()
        io_handler = self.main_window.io_handler

        # 测试地址和值设置
        io_handler.set_address_display(0x02)
        io_handler.set_value_display(0x1234)

        # 验证显示
        self.assertEqual(io_handler.addr_line_edit.text(), "0x02")
        self.assertEqual(io_handler.value_line_edit.text(), "0x1234")

        # 测试获取当前值
        current_value = io_handler.get_current_value()
        self.assertEqual(current_value, 0x1234)

    def test_signal_connections(self):
        """测试信号连接"""
        self.init_manager.create_handlers()

        tree_handler = self.main_window.tree_handler
        io_handler = self.main_window.io_handler
        
        # 测试树处理器信号
        with patch.object(tree_handler, 'register_selected') as mock_signal:
            tree_handler.populate_tree_widget()
            tree_handler.select_default_register()
            # 验证信号被发出（可能需要等待事件循环）
            QTest.qWait(100)
        
        # 测试IO处理器信号
        with patch.object(io_handler, 'read_requested') as mock_signal:
            io_handler.addr_line_edit.setText("0x02")
            io_handler.read_button_clicked()
            mock_signal.emit.assert_called_once_with(0x02)
    
    def test_cross_handler_communication(self):
        """测试处理器间通信"""
        self.init_manager.create_handlers()

        tree_handler = self.main_window.tree_handler
        table_handler = self.main_window.table_handler
        io_handler = self.main_window.io_handler
        
        # 模拟寄存器选择
        tree_handler.populate_tree_widget()
        
        # 测试全局寄存器更新
        with patch.object(table_handler, 'on_global_register_update') as mock_table_update:
            with patch.object(io_handler, 'on_global_register_update') as mock_io_update:
                # 模拟寄存器值变化
                from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
                RegisterUpdateBus.instance().emit_register_updated("0x02", 0x5678)
                
                # 等待信号处理
                QTest.qWait(100)
                
                # 验证所有处理器都收到了更新
                mock_table_update.assert_called()
                mock_io_update.assert_called()
    
    def test_fallback_mechanism(self):
        """测试回退机制"""
        # 模拟现代化工厂不可用的情况
        self.main_window.tool_window_factory = None
        
        # 应该回退到传统处理器
        self.init_manager.create_handlers()
        
        # 验证处理器仍然被创建（使用传统版本）
        self.assertIsNotNone(self.main_window.tree_handler)
        self.assertIsNotNone(self.main_window.table_handler)
        self.assertIsNotNone(self.main_window.io_handler)
        
        # 验证是传统版本
        self.assertEqual(type(self.main_window.tree_handler).__name__, 'RegisterTreeHandler')
        self.assertEqual(type(self.main_window.table_handler).__name__, 'RegisterTableHandler')
        self.assertEqual(type(self.main_window.io_handler).__name__, 'RegisterIOHandler')
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试无效的RegisterRepository
        with patch('ui.managers.InitializationManager.logger') as mock_logger:
            try:
                self.init_manager.create_handlers(None)
                # 应该记录警告并回退
                mock_logger.warning.assert_called()
            except Exception:
                # 即使出错也不应该崩溃
                pass
    
    def test_memory_management(self):
        """测试内存管理"""
        # 创建处理器
        self.init_manager.create_handlers()
        
        # 获取处理器引用
        tree_handler = self.main_window.tree_handler
        table_handler = self.main_window.table_handler
        io_handler = self.main_window.io_handler
        
        # 验证处理器存在
        self.assertIsNotNone(tree_handler)
        self.assertIsNotNone(table_handler)
        self.assertIsNotNone(io_handler)
        
        # 清理处理器
        tree_handler.close()
        table_handler.close()
        io_handler.close()
        
        # 验证清理成功（不应该有异常）
        self.assertTrue(True)
    
    def test_performance(self):
        """测试性能"""
        import time
        
        # 测试处理器创建时间
        start_time = time.time()
        self.init_manager.create_handlers()
        creation_time = time.time() - start_time
        
        # 创建时间应该在合理范围内（小于2秒）
        self.assertLess(creation_time, 2.0)
        
        # 测试树填充时间
        tree_handler = self.main_window.tree_handler
        start_time = time.time()
        tree_handler.populate_tree_widget()
        populate_time = time.time() - start_time
        
        # 填充时间应该很快（小于0.5秒）
        self.assertLess(populate_time, 0.5)


def run_integration_tests():
    """运行集成测试"""
    unittest.main()


if __name__ == '__main__':
    run_integration_tests()
