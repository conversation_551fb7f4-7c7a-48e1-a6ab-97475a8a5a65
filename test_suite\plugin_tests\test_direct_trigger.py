#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
直接触发插件菜单测试
"""

import sys
import os

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication, QMainWindow
from PyQt5.QtCore import QTimer
from core.services.plugin.PluginManager import plugin_manager
from core.services.plugin.PluginIntegrationService import PluginIntegrationService


class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("直接触发插件测试")
        self.resize(800, 600)
        
        # 创建菜单栏
        self.menuBar()
        
        # 设置插件系统
        self.setup_plugins()
        
        # 设置定时器来测试菜单项
        self.setup_test_timer()
    
    def setup_plugins(self):
        """设置插件系统"""
        # 添加插件目录
        plugin_dir = os.path.join(project_root, "plugins")
        plugin_manager.add_plugin_directory(plugin_dir)
        
        # 扫描并初始化插件
        plugin_manager.scan_plugins()
        plugin_manager.initialize_plugins(self)
        
        # 创建插件集成服务
        self.plugin_service = PluginIntegrationService(self)
        self.plugin_service.initialize_plugins()
        
        print(f"插件系统初始化完成，发现 {len(plugin_manager.get_plugin_list())} 个插件")
        print(f"插件动作数量: {len(self.plugin_service.plugin_actions)}")
        
        # 打印所有插件动作
        for name, action in self.plugin_service.plugin_actions.items():
            print(f"插件: {name}, 动作: {action}, 可检查: {action.isCheckable()}")
    
    def setup_test_timer(self):
        """设置测试定时器"""
        self.test_timer = QTimer()
        self.test_timer.timeout.connect(self.test_plugin_actions)
        self.test_timer.start(3000)  # 3秒后开始测试
    
    def test_plugin_actions(self):
        """测试插件动作"""
        self.test_timer.stop()  # 停止定时器
        
        print("\n开始测试插件动作...")
        
        for name, action in self.plugin_service.plugin_actions.items():
            print(f"\n测试插件: {name}")
            print(f"动作启用状态: {action.isEnabled()}")
            print(f"动作可见状态: {action.isVisible()}")
            print(f"动作检查状态: {action.isChecked()}")
            
            try:
                # 直接触发动作
                print(f"直接触发 {name} 动作...")
                action.trigger()
                print(f"✅ {name} 动作触发成功")
            except Exception as e:
                print(f"❌ {name} 动作触发失败: {str(e)}")
        
        # 设置关闭定时器
        close_timer = QTimer()
        close_timer.timeout.connect(self.close)
        close_timer.start(5000)  # 5秒后关闭


def test_direct_trigger():
    """直接触发测试"""
    app = QApplication([])
    
    # 创建测试窗口
    window = TestMainWindow()
    window.show()
    
    print("开始直接触发测试...")
    
    # 运行应用
    app.exec_()
    
    print("测试完成")


if __name__ == "__main__":
    test_direct_trigger()
