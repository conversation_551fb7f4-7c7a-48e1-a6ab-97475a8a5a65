#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试拖拽停靠功能
验证插件窗口的拖拽停靠是否正常工作
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton, QHBoxLayout
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class DragDockTestWindow(QWidget):
    """拖拽停靠测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.plugin_service = None
        self.test_windows = {}
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("拖拽停靠功能测试")
        self.setGeometry(100, 100, 600, 400)
        
        layout = QVBoxLayout()
        
        # 标题
        title = QLabel("拖拽停靠功能测试")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 说明
        info = QLabel("""
测试步骤：
1. 点击"启用强制悬浮模式"按钮
2. 点击"创建测试插件窗口"按钮
3. 拖拽创建的窗口到主窗口底部30%区域
4. 释放鼠标，观察窗口是否自动停靠到标签页

注意：
- 拖拽时窗口标题会显示"释放鼠标停靠到主界面"
- 鼠标光标会变为手型指针
- 停靠区域为主窗口底部30%的区域
        """)
        info.setWordWrap(True)
        layout.addWidget(info)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.force_floating_btn = QPushButton("启用强制悬浮模式")
        self.force_floating_btn.clicked.connect(self.toggle_force_floating)
        button_layout.addWidget(self.force_floating_btn)
        
        self.create_window_btn = QPushButton("创建测试插件窗口")
        self.create_window_btn.clicked.connect(self.create_test_window)
        button_layout.addWidget(self.create_window_btn)
        
        self.close_windows_btn = QPushButton("关闭所有测试窗口")
        self.close_windows_btn.clicked.connect(self.close_test_windows)
        button_layout.addWidget(self.close_windows_btn)
        
        layout.addLayout(button_layout)
        
        # 状态显示
        self.status_label = QLabel("状态: 准备就绪")
        layout.addWidget(self.status_label)
        
        self.setLayout(layout)
        
    def set_plugin_service(self, plugin_service):
        """设置插件服务"""
        self.plugin_service = plugin_service
        
    def toggle_force_floating(self):
        """切换强制悬浮模式"""
        try:
            from core.services.config.ConfigurationManager import get_config, set_config
            
            current_state = get_config('plugins.force_floating_mode', False)
            new_state = not current_state
            set_config('plugins.force_floating_mode', new_state)
            
            if new_state:
                self.force_floating_btn.setText("禁用强制悬浮模式")
                self.status_label.setText("状态: 强制悬浮模式已启用")
                logger.info("强制悬浮模式已启用")
            else:
                self.force_floating_btn.setText("启用强制悬浮模式")
                self.status_label.setText("状态: 强制悬浮模式已禁用")
                logger.info("强制悬浮模式已禁用")
                
        except Exception as e:
            self.status_label.setText(f"状态: 切换失败 - {str(e)}")
            logger.error(f"切换强制悬浮模式失败: {str(e)}")
            
    def create_test_window(self):
        """创建测试插件窗口"""
        try:
            if not self.plugin_service:
                self.status_label.setText("状态: 插件服务未设置")
                return
                
            # 创建一个简单的测试窗口
            test_window = QWidget()
            test_window.setWindowTitle("测试拖拽停靠窗口")
            test_window.setGeometry(200, 200, 300, 200)
            
            # 添加内容
            layout = QVBoxLayout()
            label = QLabel("这是一个测试窗口\n请拖拽我到主窗口底部进行停靠测试")
            label.setAlignment(Qt.AlignCenter)
            layout.addWidget(label)
            test_window.setLayout(layout)
            
            # 为测试窗口添加拖拽停靠支持
            plugin_name = f"test_window_{len(self.test_windows)}"
            self.plugin_service._add_drag_dock_support(test_window, plugin_name)
            
            # 配置为悬浮窗口
            test_window.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint |
                                     Qt.WindowMinimizeButtonHint | Qt.WindowMaximizeButtonHint)
            
            # 显示窗口
            test_window.show()
            
            # 存储窗口引用
            self.test_windows[plugin_name] = test_window
            self.plugin_service.plugin_windows[plugin_name] = test_window
            
            self.status_label.setText(f"状态: 已创建测试窗口 {plugin_name}")
            logger.info(f"已创建测试窗口: {plugin_name}")
            
        except Exception as e:
            self.status_label.setText(f"状态: 创建窗口失败 - {str(e)}")
            logger.error(f"创建测试窗口失败: {str(e)}")
            
    def close_test_windows(self):
        """关闭所有测试窗口"""
        try:
            for plugin_name, window in self.test_windows.items():
                try:
                    window.close()
                    if plugin_name in self.plugin_service.plugin_windows:
                        del self.plugin_service.plugin_windows[plugin_name]
                except Exception as e:
                    logger.warning(f"关闭测试窗口失败 {plugin_name}: {str(e)}")
                    
            self.test_windows.clear()
            self.status_label.setText("状态: 所有测试窗口已关闭")
            logger.info("所有测试窗口已关闭")
            
        except Exception as e:
            self.status_label.setText(f"状态: 关闭窗口失败 - {str(e)}")
            logger.error(f"关闭测试窗口失败: {str(e)}")


def main():
    """主函数"""
    try:
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建测试窗口
        test_window = DragDockTestWindow()
        
        # 尝试获取主窗口和插件服务
        try:
            from ui.windows.RegisterMainWindow import RegisterMainWindow
            from core.services.plugin.PluginIntegrationService import PluginIntegrationService
            
            # 创建主窗口（用于测试）
            main_window = RegisterMainWindow()
            main_window.show()
            
            # 获取插件服务
            if hasattr(main_window, 'plugin_integration_service'):
                plugin_service = main_window.plugin_integration_service
                test_window.set_plugin_service(plugin_service)
                logger.info("已连接到插件服务")
            else:
                logger.warning("主窗口没有插件服务")
                
        except Exception as e:
            logger.error(f"初始化主窗口失败: {str(e)}")
            
        # 显示测试窗口
        test_window.show()
        
        # 运行应用程序
        sys.exit(app.exec_())
        
    except Exception as e:
        logger.error(f"测试程序运行失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
