# -*- mode: python ; coding: utf-8 -*-
"""
FSJ04832 寄存器配置工具 - 安全打包配置
更新日期: 2025-07-02
专门用于生成客户发布版本，最大化代码保护
"""

import os
import sys
import json
import secrets
import string
from pathlib import Path

# PyInstaller 导入
from PyInstaller.utils.hooks import collect_submodules, collect_data_files
from PyInstaller.building.build_main import Analysis, PYZ, EXE

# 导入PyInstaller加密模块（如果可用）
try:
    from PyInstaller.utils.cliutils import pyi_crypto
    CRYPTO_AVAILABLE = True
except ImportError:
    # 如果没有加密模块，使用None
    pyi_crypto = None
    CRYPTO_AVAILABLE = False

# 读取版本信息
def get_version_info():
    """获取版本信息"""
    try:
        version_file = Path('packaging/config/version.json')
        if version_file.exists():
            with open(version_file, 'r', encoding='utf-8') as f:
                version_data = json.load(f)
                v = version_data.get('version', {})
                if isinstance(v, dict):
                    return f"{v.get('major', 1)}.{v.get('minor', 0)}.{v.get('patch', 0)}.{v.get('build', 0)}"
                return str(v)
        
        version_file = Path('version.json')
        if version_file.exists():
            with open(version_file, 'r', encoding='utf-8') as f:
                version_data = json.load(f)
                v = version_data.get('version', {})
                if isinstance(v, dict):
                    return f"{v.get('major', 1)}.{v.get('minor', 0)}.{v.get('patch', 0)}.{v.get('build', 0)}"
                return str(v)
    except Exception as e:
        print(f"读取版本信息失败: {e}")
    return '1.0.0.0'

# 获取当前版本
current_version = get_version_info()
exe_name = f'FSJ04832_RegisterTool_v{current_version}_Release'

# 项目根目录相对路径
project_root_relative = '../..'

# 只包含必要的数据文件，排除敏感配置
added_files = [
    # 只包含必要的配置文件
    (f'{project_root_relative}/config/default.json', 'config'),
    # 必要的资源文件
    (f'{project_root_relative}/images', 'images'),
    (f'{project_root_relative}/lib/register.json', 'lib'),
    # GUI资源
    (f'{project_root_relative}/gui', 'gui'),
    # 版本信息（客户版本）
    ('../config/version.json', 'packaging/config'),
]

# 完整的隐藏导入列表
hiddenimports = [
    # PyQt5 核心模块
    'PyQt5.QtCore', 'PyQt5.QtWidgets', 'PyQt5.QtGui', 'PyQt5.QtPrintSupport',
    
    # 串口通信
    'serial', 'serial.tools.list_ports',
    
    # 核心服务
    'core.event_bus',
    'core.services.spi.spi_service', 'core.services.spi.spi_service_impl', 'core.services.spi.port_manager',
    'core.services.register.RegisterOperationService', 'core.services.version.VersionService',
    'core.services.DIContainer', 'core.services.plugin.PluginManager',
    'core.services.plugin.PluginIntegrationService', 'core.services.plugin.menu.PluginMenuService',
    'core.services.plugin.window.PluginWindowService',
    
    # 现代化处理器
    'ui.handlers.ModernBaseHandler', 'ui.handlers.ModernSetModesHandler',
    'ui.handlers.ModernClkinControlHandler', 'ui.handlers.ModernPLLHandler',
    'ui.handlers.ModernSyncSysRefHandler', 'ui.handlers.ModernClkOutputsHandler',
    'ui.handlers.ModernRegisterTableHandler', 'ui.handlers.ModernUIEventHandler',
    'ui.handlers.ModernRegisterIOHandler', 'ui.handlers.ModernRegisterTreeHandler',
    
    # UI管理器
    'ui.managers.InitializationManager', 'ui.managers.StatusAndConfigManager',
    'ui.managers.SPISignalManager', 'ui.managers.RegisterDisplayManager',
    'ui.managers.UIManager', 'ui.managers.BatchOperationManager',
    
    # UI组件
    'ui.components.MenuManager', 'ui.components.ProgressBarManager', 'ui.components.StatusBarManager',
    
    # 工厂类
    'ui.factories.ModernToolWindowFactory', 'ui.factories.ToolWindowFactory',
    
    # 工具类
    'utils.Log', 'utils.configFileHandler', 'utils.address_utils',
    'utils.error_handler', 'utils.message_strings',
    
    # 插件文件
    'plugins.set_modes_plugin', 'plugins.clkin_control_plugin', 'plugins.pll_control_plugin',
    'plugins.sync_sysref_plugin', 'plugins.clk_output_plugin', 'plugins.selective_register_plugin',
    'plugins.performance_monitor_plugin', 'plugins.data_analysis_plugin',
    
    # 标准库模块
    'json', 'pathlib', 'importlib', 'importlib.util', 'inspect', 'threading', 'queue',
    'datetime', 'time', 'logging', 'logging.handlers', 'configparser', 'collections',
    'functools', 'weakref', 'copy', 'pickle', 'base64', 'hashlib', 'uuid', 'platform',
    'subprocess', 'shutil', 'tempfile', 'zipfile', 'tarfile', 'gzip', 're', 'math',
    'statistics', 'random', 'itertools', 'operator', 'types', 'typing',
]

a = Analysis(
    [f'{project_root_relative}/main.py'],
    pathex=[project_root_relative],
    binaries=[],
    datas=added_files,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter', 'matplotlib', 'numpy', 'pandas', 'scipy', 'PIL', 'cv2',
        'pytest', 'unittest', 'doctest',  # 排除测试框架
        'pdb', 'pydoc',  # 排除调试工具
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    noarchive=False,
    optimize=2  # 启用最高级别的Python优化
)

# 增强安全保护 - 字节码加密
def generate_cipher_key():
    """生成随机密钥用于字节码加密"""
    return ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(16))

# 启用字节码加密（如果支持）
block_cipher = None
if CRYPTO_AVAILABLE and pyi_crypto:
    try:
        cipher_key = generate_cipher_key()
        block_cipher = pyi_crypto.PyiBlockCipher(key=cipher_key)
        print(f"✅ 启用字节码加密保护")
    except Exception as e:
        print(f"⚠️ 字节码加密不可用: {e}")
        block_cipher = None
else:
    print("⚠️ PyInstaller加密模块不可用，跳过字节码加密")

# 使用密钥加密字节码（增强保护）
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# 创建单文件可执行程序 - 最大化代码保护
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name=exe_name + '_Release',  # 添加Release标识
    debug=False,  # 禁用调试信息
    bootloader_ignore_signals=False,
    strip=True,   # 移除符号信息 - 防止逆向工程
    upx=True,     # 启用UPX压缩 - 代码混淆和压缩
    upx_exclude=[],
    runtime_tmpdir=None,  # 不使用临时目录 - 减少文件暴露
    console=False,  # 窗口模式 - 隐藏控制台
    icon=f'{project_root_relative}/images/logo.ico',
    disable_windowed_traceback=True,  # 禁用窗口模式下的回溯信息 - 隐藏错误细节
    argv_emulation=False,  # 禁用参数模拟 - 减少攻击面
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,

    # 高级安全选项
    manifest=None,  # 不包含清单文件
    uac_admin=False,  # 不要求管理员权限
    uac_uiaccess=False,  # 不要求UI访问权限

    # 版本信息（可选）
    version_file=None,  # 可以添加版本资源文件

    # 额外的安全编译选项
    # 注意：这些选项可能需要特定的PyInstaller版本支持
)
