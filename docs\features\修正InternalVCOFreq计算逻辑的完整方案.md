# 修正InternalVCOFreq计算逻辑的完整方案

## 用户指出的正确计算关系

根据用户提供的数据和要求：
- **PLL2PFD** = 245.76 MHz
- **spinBoxSysrefDIV** = 3  
- **InternalVCOFreq** = PLL2PFD × spinBoxSysrefDIV = 245.76 × 3 = 737.28 MHz
- **InternalVCOFreq** = **VCODistFreq** (两者应该相同并同步)

## 修正前的问题

### 错误的计算逻辑
- ❌ InternalVCOFreq直接从VCODistFreq同步，没有考虑SYSREF分频器
- ❌ 没有建立PLL2PFD与InternalVCOFreq的正确关系
- ❌ SYSREF频率计算基于错误的InternalVCOFreq值

### 数据流混乱
- ❌ VCODistFreq → InternalVCOFreq (错误方向)
- ❌ 缺少PLL2PFD的缓存机制
- ❌ 同步系统参考窗口无法获取PLL2PFD数据

## 实施的修正方案

### 1. 建立正确的计算公式

#### 在ModernSyncSysRefHandler中添加：
```python
def calculate_internal_vco_freq_from_pll2pfd(self):
    """根据PLL2PFD和SYSREF分频器计算InternalVCOFreq
    
    正确的计算公式: InternalVCOFreq = PLL2PFD × spinBoxSysrefDIV
    """
    # 获取PLL2PFD频率（从PLL窗口或缓存）
    pll2_pfd_freq = self._get_pll2_pfd_frequency()
    
    # 获取SYSREF分频器值
    sysref_div = self.ui.spinBoxSysrefDIV.value()
    
    # 计算InternalVCOFreq = PLL2PFD × spinBoxSysrefDIV
    internal_vco_freq = pll2_pfd_freq * sysref_div
    
    # 更新InternalVCOFreq控件
    self.ui.InternalVCOFreq.setText(f"{internal_vco_freq:.5f}")
    
    # 重新计算SYSREF输出频率
    self.calculate_output_frequencies()
```

### 2. 添加PLL2PFD频率缓存机制

#### 在RegisterUpdateBus中扩展缓存：
```python
# 频率值缓存
self._frequency_cache = {
    'vco_dist_freq': None,
    'pll1_pfd_freq': None,
    'pll2_pfd_freq': None,  # ✅ 新增PLL2PFD缓存
    'sysref_freq': None,
    'sysref_div': None
}

def cache_pll2_pfd_freq(self, freq_value):
    """缓存PLL2PFD频率值"""
    self._frequency_cache['pll2_pfd_freq'] = freq_value

def get_cached_pll2_pfd_freq(self):
    """获取缓存的PLL2PFD频率值"""
    return self._frequency_cache.get('pll2_pfd_freq')
```

#### 在ModernPLLHandler中添加缓存调用：
```python
def _calculate_pll2_unified_formula(self, input_freq):
    # 计算PLL2PFD频率
    pll2_pfd_freq = self._calculate_pll2_pfd_frequency(input_freq)
    
    # 设置PLL2PFDFreq显示值
    self.ui.PLL2PFDFreq.setText(f"{pll2_pfd_freq:.3f}")
    
    # 缓存PLL2PFD频率值，供同步系统参考窗口使用 ✅ 新增
    self._cache_pll2_pfd_frequency(pll2_pfd_freq)
```

### 3. 修正数据同步方向

#### 修正前的错误方向：
```
VCODistFreq → InternalVCOFreq (❌ 错误)
```

#### 修正后的正确方向：
```
PLL2PFD × spinBoxSysrefDIV → InternalVCOFreq → VCODistFreq (✅ 正确)
```

### 4. 添加SYSREF分频器变化处理

```python
def _on_sysref_div_changed(self):
    """处理SYSREF分频器值变化
    
    当SYSREF分频器变化时：
    1. 重新计算InternalVCOFreq = PLL2PFD × spinBoxSysrefDIV
    2. 重新计算SYSREF输出频率
    """
    logger.info("SYSREF分频器值发生变化，重新计算InternalVCOFreq")
    
    # 重新计算InternalVCOFreq
    self.calculate_internal_vco_freq_from_pll2pfd()
```

## 正确的数据流设计

### PLL窗口 → 同步系统参考窗口
```
1. PLL窗口计算PLL2PFD频率
2. 缓存PLL2PFD到RegisterUpdateBus
3. 同步系统参考窗口从缓存获取PLL2PFD
4. 计算InternalVCOFreq = PLL2PFD × spinBoxSysrefDIV
5. 计算SYSREF频率 = InternalVCOFreq / spinBoxSysrefDIV
6. 缓存SYSREF频率供PLL2Cin使用
```

### 同步系统参考窗口 → PLL窗口
```
1. InternalVCOFreq同步到VCODistFreq
2. SYSREF频率缓存供PLL2Cin使用
3. 确保两个窗口显示一致的频率值
```

## 测试验证场景

### 场景1：基本计算验证
- **输入**: PLL2PFD = 245.76 MHz, spinBoxSysrefDIV = 3
- **计算**: InternalVCOFreq = 245.76 × 3 = 737.28 MHz
- **期望**: 
  - InternalVCOFreq显示: 737.28 MHz
  - VCODistFreq显示: 737.28 MHz (同步)
  - SyncSysrefFreq1显示: 245.76 MHz
  - PLL2Cin显示: 245.76 MHz

### 场景2：分频器变化测试
- **操作**: 将spinBoxSysrefDIV从3改为1
- **期望**: 
  - InternalVCOFreq自动更新为: 245.76 × 1 = 245.76 MHz
  - SYSREF频率更新为: 245.76 MHz
  - PLL2Cin同步更新为: 245.76 MHz

### 场景3：跨窗口同步测试
- **操作**: 关闭同步系统参考窗口，重新打开PLL窗口
- **期望**: PLL2Cin仍能从缓存获取正确的SYSREF频率

## 关键改进效果

### 1. 计算逻辑正确性
- ✅ **修复前**: InternalVCOFreq = VCODistFreq (错误)
- ✅ **修复后**: InternalVCOFreq = PLL2PFD × spinBoxSysrefDIV (正确)

### 2. 数据同步完整性
- ✅ **PLL2PFD缓存**: 确保同步系统参考窗口能获取PLL2PFD数据
- ✅ **InternalVCOFreq计算**: 基于正确的公式计算
- ✅ **VCODistFreq同步**: 与InternalVCOFreq保持一致

### 3. 用户体验提升
- ✅ **实时响应**: SYSREF分频器变化时自动重新计算
- ✅ **数据一致**: 两个窗口显示的频率值保持同步
- ✅ **逻辑清晰**: 计算关系符合用户的理解和期望

## 预期日志输出

正常工作时应该看到：
```
【PLL窗口】已缓存PLL2PFD频率: 245.760 MHz，供同步系统参考窗口使用
【InternalVCOFreq计算】计算公式: 245.76 × 3 = 737.28000 MHz
【InternalVCOFreq计算】InternalVCOFreq更新: '0.00000' -> '737.28000' MHz
【同步系统参考窗口】已缓存SyncSysrefFreq1值: 245.76000 MHz，供PLL2Cin使用
【PLL2Cin调试】PLL2Cin更新: '0.00000' -> '245.76000' MHz
```

## 使用方法

### 测试步骤
1. **运行程序**，打开PLL窗口
2. **查看PLL2PFD频率值**（如245.76 MHz）
3. **打开同步系统参考窗口**
4. **设置spinBoxSysrefDIV为3**
5. **验证InternalVCOFreq显示737.28 MHz**
6. **验证SyncSysrefFreq1显示245.76 MHz**
7. **回到PLL窗口**，选择FBMUX=SYSREF Divider
8. **验证PLL2Cin显示245.76 MHz**
9. **验证VCODistFreq显示737.28 MHz**

### 验证要点
- ✅ InternalVCOFreq = PLL2PFD × spinBoxSysrefDIV
- ✅ InternalVCOFreq = VCODistFreq (同步)
- ✅ SYSREF频率 = InternalVCOFreq / spinBoxSysrefDIV
- ✅ PLL2Cin = SYSREF频率

## 总结

通过实施正确的InternalVCOFreq计算逻辑，现在系统能够：

1. **正确计算InternalVCOFreq** = PLL2PFD × spinBoxSysrefDIV
2. **保持VCODistFreq同步** = InternalVCOFreq
3. **正确计算SYSREF频率** = InternalVCOFreq / spinBoxSysrefDIV
4. **正确显示PLL2Cin** = SYSREF频率

这确保了用户指出的计算关系得到正确实现，解决了之前计算逻辑错误的问题。
