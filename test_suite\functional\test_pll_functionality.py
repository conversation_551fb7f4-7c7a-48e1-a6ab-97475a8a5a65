#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试PLL工具窗口功能
验证现代化和传统处理器的基本功能
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication
from PyQt5.QtTest import QTest


def test_pll_functionality():
    """测试PLL功能"""
    print("=" * 60)
    print("测试PLL工具窗口功能")
    print("=" * 60)
    
    try:
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建寄存器管理器
        from core.services.register.RegisterManager import RegisterManager
        config_path = os.path.join(project_root, 'lib', 'register.json')
        
        if not os.path.exists(config_path):
            print(f"❌ 寄存器配置文件不存在: {config_path}")
            return False

        import json
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        register_manager = RegisterManager(registers_config)
        
        # 1. 测试现代化PLL处理器
        print("\n1. 测试现代化PLL处理器...")
        try:
            from ui.handlers.ModernPLLHandler import ModernPLLHandler
            modern_handler = ModernPLLHandler(None, register_manager)
            
            print("✓ 现代化处理器创建成功")
            print(f"  - 窗口标题: {modern_handler.windowTitle()}")
            
            # 检查关键功能
            if hasattr(modern_handler, 'calculate_output_frequencies'):
                print("✓ 频率计算功能存在")
                modern_handler.calculate_output_frequencies()
                print("✓ 频率计算执行成功")
            
            if hasattr(modern_handler, 'ui') and hasattr(modern_handler.ui, 'PLL1PD'):
                print("✓ 关键UI控件存在")
                
            # 测试窗口显示
            modern_handler.resize(800, 600)
            modern_handler.show()
            QTest.qWait(1000)
            print("✓ 现代化处理器窗口显示成功")
            modern_handler.close()
            
        except Exception as e:
            print(f"❌ 现代化处理器测试失败: {str(e)}")
            return False
        
        # 2. 测试传统PLL处理器
        print("\n2. 测试传统PLL处理器...")
        try:
            # from ui.handlers.PLLHandler import PLLHandler  # 已移除
            # legacy_handler = PLLHandler(None, register_manager.register_objects)  # 已移除
            
            print("✓ 传统处理器创建成功")
            print(f"  - 窗口标题: {legacy_handler.windowTitle()}")
            
            # 检查关键功能
            if hasattr(legacy_handler, 'calculate_output_frequencies'):
                print("✓ 频率计算功能存在")
                legacy_handler.calculate_output_frequencies()
                print("✓ 频率计算执行成功")
            
            if hasattr(legacy_handler, 'ui') and hasattr(legacy_handler.ui, 'PLL1PD'):
                print("✓ 关键UI控件存在")
                
            # 测试窗口显示
            legacy_handler.resize(800, 600)
            legacy_handler.show()
            QTest.qWait(1000)
            print("✓ 传统处理器窗口显示成功")
            legacy_handler.close()
            
        except Exception as e:
            print(f"❌ 传统处理器测试失败: {str(e)}")
            return False
        
        # 3. 测试工厂创建
        print("\n3. 测试工厂创建...")
        try:
            from ui.factories.ModernToolWindowFactory import ModernToolWindowFactory
            
            class MockMainWindow:
                def __init__(self):
                    self.register_manager = register_manager
            
            mock_main_window = MockMainWindow()
            factory = ModernToolWindowFactory(mock_main_window)
            
            # 测试创建PLL窗口
            pll_window = factory.create_window_by_type('pll_control')
            if pll_window:
                print("✓ 工厂创建PLL窗口成功")
                print(f"  - 窗口类型: {type(pll_window).__name__}")
                
                if 'Modern' in type(pll_window).__name__:
                    print("✓ 工厂使用现代化处理器")
                else:
                    print("⚠️  工厂使用传统处理器")
                
                pll_window.show()
                QTest.qWait(1000)
                print("✓ 工厂创建的窗口显示成功")
                pll_window.close()
            else:
                print("❌ 工厂创建PLL窗口失败")
                return False
                
        except Exception as e:
            print(f"❌ 工厂测试失败: {str(e)}")
            return False
        
        print("\n✅ 所有PLL功能测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_pll_functionality()
    
    if success:
        print("\n🎉 PLL工具窗口功能测试完成！")
        print("📋 测试总结:")
        print("   - ✅ 现代化PLL处理器功能正常")
        print("   - ✅ 传统PLL处理器功能正常")
        print("   - ✅ 工厂创建机制正常")
        print("   - ✅ 窗口显示功能正常")
        print("   - ✅ 频率计算功能正常")
        print("\n📝 结论:")
        print("   PLL工具窗口没有使用过时的文件，")
        print("   现代化和传统处理器都工作正常，")
        print("   配置合理，功能完整。")
    else:
        print("\n❌ PLL功能测试失败")
        sys.exit(1)
