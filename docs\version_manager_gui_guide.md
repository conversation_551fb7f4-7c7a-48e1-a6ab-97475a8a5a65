# 版本管理工具图形界面使用指南

## 🎯 工具概述

版本管理工具图形界面是一个可视化的版本管理和打包工具，让您可以通过友好的界面轻松管理版本号和执行打包操作，无需记忆复杂的命令行参数。

## 🚀 启动方法

### 方法一：双击批处理文件（推荐）
```
双击 "启动版本管理工具.bat" 文件
```

### 方法二：Python命令行
```bash
python version_manager.py
```

### 方法三：直接运行GUI模块
```bash
python ui/tools/VersionManagerGUI.py
```

## 🖥️ 界面介绍

### 1. 当前版本信息区域
显示当前项目的版本信息：
- **当前版本**: 完整的四段式版本号
- **应用名称**: 应用程序名称
- **窗口标题**: 带版本号的窗口标题
- **可执行文件名**: 打包后的文件名
- **构建日期**: 最后构建时间
- **构建类型**: 构建类型（release/debug）

### 2. 版本号管理区域
选择版本号增加类型：

#### 🔧 构建号 (Build)
- **用途**: 日常开发构建，小修复
- **变化**: `1.0.1.0` → `1.0.1.1`
- **适用场景**: 
  - 代码调试
  - 小bug修复
  - 日常开发迭代

#### 🔨 补丁版本 (Patch)
- **用途**: Bug修复，安全更新
- **变化**: `1.0.1.5` → `1.0.2.0`
- **适用场景**:
  - 重要bug修复
  - 安全漏洞修复
  - 小功能改进

#### ⚡ 次版本 (Minor)
- **用途**: 新功能添加
- **变化**: `1.0.5.3` → `1.1.0.0`
- **适用场景**:
  - 新功能发布
  - API扩展
  - 向后兼容的更改

#### 🚀 主版本 (Major)
- **用途**: 重大更新，架构变更
- **变化**: `1.5.3.2` → `2.0.0.0`
- **适用场景**:
  - 重大架构重构
  - 不兼容的API更改
  - 重大功能重写

#### ⏸️ 不增加版本号
- **用途**: 仅重新构建，不改变版本号
- **适用场景**:
  - 重新打包
  - 测试构建配置
  - 修复构建问题

### 3. 构建选项区域
- **Spec文件**: 指定PyInstaller配置文件路径
- **浏览按钮**: 可视化选择spec文件

### 4. 操作按钮区域
- **预览版本变化**: 查看版本更新后的效果，不实际执行
- **开始构建**: 执行版本更新和打包操作
- **停止构建**: 中断正在进行的构建过程

### 5. 构建输出区域
- **进度条**: 显示构建进度
- **输出文本**: 实时显示构建过程的详细信息
- **清除输出**: 清空输出文本区域

## 📋 使用流程

### 标准发布流程

#### 1. 日常开发构建
```
1. 启动版本管理工具
2. 选择 "构建号 (Build)"
3. 点击 "预览版本变化" 确认
4. 点击 "开始构建"
5. 等待构建完成
```

#### 2. Bug修复发布
```
1. 选择 "补丁版本 (Patch)"
2. 预览版本变化
3. 开始构建
4. 验证修复效果
```

#### 3. 新功能发布
```
1. 选择 "次版本 (Minor)"
2. 预览版本变化
3. 开始构建
4. 测试新功能
```

#### 4. 重大版本发布
```
1. 选择 "主版本 (Major)"
2. 预览版本变化
3. 开始构建
4. 全面测试
```

## 🎨 界面特性

### 🔄 实时更新
- 版本信息自动刷新
- 构建输出实时显示
- 进度条动态更新

### 🛡️ 错误处理
- 文件不存在检查
- 构建失败提示
- 异常情况处理

### 🎯 用户友好
- 直观的界面布局
- 详细的操作说明
- 清晰的状态反馈

### 📊 信息丰富
- 完整的版本信息展示
- 详细的构建日志
- 清晰的操作结果

## 🔧 高级功能

### 自定义Spec文件
1. 在"构建选项"区域修改spec文件路径
2. 点击"浏览..."按钮选择文件
3. 支持相对路径和绝对路径

### 预览功能
- 在实际构建前查看版本变化
- 确认新版本号和文件名
- 避免误操作

### 构建控制
- 实时查看构建进度
- 随时停止构建过程
- 详细的错误信息显示

## 📁 输出文件

构建完成后，您将获得：

### 可执行文件
```
dist/FSJConfigTool1.0.1.exe
```

### 版本信息更新
- `version.json` 文件更新
- `build.spec` 文件更新
- 应用程序窗口标题更新

## ⚠️ 注意事项

### 构建环境要求
- Python 3.6+
- PyInstaller 已安装
- 项目依赖已安装

### 文件权限
- 确保对项目目录有写权限
- 某些系统可能需要管理员权限

### 版本控制
- 建议在版本控制系统中提交版本更新
- 保持版本历史记录

## 🐛 故障排除

### 常见问题

#### 1. 启动失败
```
问题: 双击bat文件无反应
解决: 检查Python是否正确安装并添加到PATH
```

#### 2. 构建失败
```
问题: 构建过程中出现错误
解决: 查看输出区域的详细错误信息
```

#### 3. 版本信息不更新
```
问题: 界面显示的版本信息不正确
解决: 点击"刷新版本信息"按钮
```

#### 4. Spec文件找不到
```
问题: 提示spec文件不存在
解决: 检查文件路径，使用"浏览..."按钮重新选择
```

## 🎉 使用技巧

### 1. 快速开发流程
- 日常开发使用"构建号"
- 每次提交前构建一次
- 保持版本号连续性

### 2. 发布管理
- 发布前使用"预览版本变化"
- 重要版本使用"补丁"或"次版本"
- 记录版本更新日志

### 3. 团队协作
- 统一版本号管理规范
- 定期同步版本信息
- 避免版本号冲突

## 📞 技术支持

如果遇到问题或有改进建议，请联系开发团队。

---

**🎯 目标**: 让版本管理变得简单直观，提高开发效率，确保版本一致性！
