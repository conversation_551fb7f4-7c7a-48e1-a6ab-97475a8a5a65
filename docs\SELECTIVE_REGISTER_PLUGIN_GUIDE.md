# 选择性寄存器操作插件使用指南

## 🎯 功能概述

选择性寄存器操作插件允许用户选择特定的寄存器进行批量读写操作，而不需要操作所有寄存器。这个功能特别适用于：

- 只需要配置特定功能模块的寄存器
- 调试特定的寄存器组
- 快速验证某些寄存器的设置
- 减少不必要的读写操作，提高效率

## 🚀 启动插件

1. **通过菜单启动**：
   - 启动主程序
   - 点击菜单栏的"插件(&P)" → "选择性寄存器操作"

2. **插件窗口**：
   - 窗口大小：900x700 像素
   - 可调整大小，最小尺寸：600x500 像素
   - 支持独立移动和操作

## 📋 界面介绍

插件窗口包含三个主要标签页：

### 1. 寄存器选择标签页

**功能区域**：
- **快速选择按钮**：
  - `全选`：选择所有寄存器
  - `全不选`：取消选择所有寄存器
  - `按组选择`：按功能分组选择
  
- **搜索框**：
  - 支持实时搜索寄存器
  - 可搜索寄存器名称、地址、描述

**寄存器树形视图**：
- 按功能分组显示寄存器：
  - 设备信息
  - 电源管理
  - 时钟输入
  - PLL控制
  - 时钟输出0-1 到 时钟输出12-13
  - SYSREF控制
  - 同步控制
  - 其他

- 显示信息：
  - 寄存器名称（如 R0 (0x00)）
  - 地址
  - 当前值
  - 功能描述

**选择统计**：
- 实时显示已选择的寄存器数量

### 2. 模板管理标签页

**预设模板**：
- `PLL控制寄存器`：包含PLL相关的核心寄存器
- `时钟输出0-1`：时钟输出通道0和1的控制寄存器
- `时钟输出2-3`：时钟输出通道2和3的控制寄存器
- `时钟输出4-5`：时钟输出通道4和5的控制寄存器
- `SYSREF控制`：SYSREF相关的控制寄存器
- `电源管理`：电源控制相关寄存器

**模板操作**：
- `加载模板`：将模板中的寄存器自动选中
- `保存为模板`：将当前选择保存为新模板（未来版本）
- `删除模板`：删除自定义模板（未来版本）

**模板内容显示**：
- 显示选中模板包含的所有寄存器
- 包括地址、寄存器名、功能描述

### 3. 操作监控标签页

**操作状态**：
- 进度条：显示读写操作的进度
- 状态标签：显示当前操作状态

**操作日志**：
- 实时显示操作过程和结果
- 包含时间戳的详细日志
- 支持清空和导出日志

## 🔧 使用步骤

### 选择性读取寄存器

1. **选择寄存器**：
   - 在"寄存器选择"标签页中选择需要读取的寄存器
   - 可以手动勾选，或使用模板快速选择
   - 可以使用搜索功能快速定位

2. **执行读取**：
   - 点击底部的"读取选中寄存器"按钮
   - 确认读取操作
   - 系统会自动切换到"操作监控"标签页

3. **监控进度**：
   - 观察进度条和日志信息
   - 读取完成后，寄存器树中的值会自动更新

### 选择性写入寄存器

1. **选择寄存器**：
   - 在"寄存器选择"标签页中选择需要写入的寄存器
   - 确保这些寄存器的值已经在主程序中设置好

2. **执行写入**：
   - 点击底部的"写入选中寄存器"按钮
   - 仔细阅读确认对话框（写入操作会覆盖设备中的值）
   - 确认写入操作

3. **监控进度**：
   - 观察进度条和日志信息
   - 写入完成后会显示成功消息

### 使用模板

1. **加载预设模板**：
   - 切换到"模板管理"标签页
   - 从下拉框选择需要的模板
   - 点击"加载模板"按钮
   - 返回"寄存器选择"标签页查看选择结果

2. **查看模板内容**：
   - 在模板下拉框中选择模板
   - 下方表格会显示模板包含的所有寄存器

## ⚠️ 注意事项

### 安全提醒
- **写入操作不可逆**：写入操作会直接修改设备中的寄存器值
- **确认选择**：执行操作前请仔细确认选择的寄存器
- **备份重要设置**：建议在大量修改前保存当前配置

### 操作限制
- **单次操作**：同时只能进行一个读取或写入操作
- **取消操作**：可以通过"取消操作"按钮中止正在进行的操作
- **SPI连接**：需要确保SPI连接正常

### 性能优化
- **批量操作**：插件使用批量读写API，比逐个操作更高效
- **进度显示**：实时显示操作进度，避免界面假死
- **日志记录**：详细记录操作过程，便于问题排查

## 🔍 故障排除

### 常见问题

1. **插件无法启动**：
   - 检查插件系统是否启用
   - 查看日志中的错误信息

2. **寄存器列表为空**：
   - 确认主程序已正确加载寄存器配置
   - 检查寄存器管理器是否初始化

3. **读写操作失败**：
   - 检查SPI连接状态
   - 确认设备通信正常
   - 查看操作日志中的错误信息

4. **进度条不更新**：
   - 检查SPI服务的信号连接
   - 确认批量操作API正常工作

### 日志信息
插件会在以下位置记录日志：
- 插件内部日志：操作监控标签页
- 系统日志：主程序日志文件
- 错误信息：控制台输出

## 🎉 使用技巧

1. **组合使用**：
   - 先使用模板快速选择基础寄存器
   - 再手动添加或移除特定寄存器

2. **搜索功能**：
   - 使用关键字快速定位寄存器
   - 支持搜索地址、名称、描述

3. **分批操作**：
   - 对于大量寄存器，可以分批进行操作
   - 避免一次性操作过多寄存器

4. **模板管理**：
   - 为常用的寄存器组合创建模板
   - 提高日常操作效率

---

**版本信息**：v1.0.0  
**更新日期**：2025-06-07  
**兼容性**：FSJ04832 寄存器配置工具 v1.0.2.25+
