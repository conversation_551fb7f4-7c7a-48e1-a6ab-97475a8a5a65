#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化的传统PLL处理器移除验证
避免多次创建对象导致的问题
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)


def test_file_removal():
    """测试文件移除"""
    print("=" * 60)
    print("测试传统PLL处理器文件移除")
    print("=" * 60)
    
    # 检查PLLHandler.py是否已移除
    legacy_handler_path = os.path.join(project_root, 'ui', 'handlers', 'PLLHandler.py')
    
    if os.path.exists(legacy_handler_path):
        print("❌ PLLHandler.py文件仍然存在")
        return False
    else:
        print("✓ PLLHandler.py文件已成功移除")
    
    # 检查备份文件是否存在
    backup_files = [f for f in os.listdir(os.path.join(project_root, 'ui', 'handlers')) 
                   if f.startswith('PLLHandler.py.backup_')]
    
    if backup_files:
        print(f"✓ 发现备份文件: {backup_files[0]}")
    else:
        print("⚠️  未发现备份文件")
    
    return True


def test_import_failure():
    """测试导入失败"""
    print("\n测试传统处理器导入...")
    
    try:
        from ui.handlers.PLLHandler import PLLHandler
        print("❌ 仍然可以导入PLLHandler")
        return False
    except ImportError:
        print("✓ PLLHandler导入失败（预期行为）")
        return True
    except Exception as e:
        print(f"✓ PLLHandler导入出错（预期行为）: {str(e)}")
        return True


def test_modern_handler_import():
    """测试现代化处理器导入"""
    print("\n测试现代化处理器导入...")
    
    try:
        from ui.handlers.ModernPLLHandler import ModernPLLHandler
        print("✓ ModernPLLHandler导入成功")
        return True
    except Exception as e:
        print(f"❌ ModernPLLHandler导入失败: {str(e)}")
        return False


def test_factory_config():
    """测试工厂配置"""
    print("\n测试工厂配置...")
    
    try:
        from ui.factories.ModernToolWindowFactory import ModernToolWindowFactory
        
        # 创建模拟主窗口
        class MockMainWindow:
            def __init__(self):
                pass
        
        mock_main_window = MockMainWindow()
        factory = ModernToolWindowFactory(mock_main_window)
        
        # 检查PLL配置
        pll_config = factory.HANDLER_CONFIGS.get('pll_control')
        if pll_config:
            print("✓ PLL控制配置存在")
            
            # 检查是否移除了传统处理器引用
            if 'legacy_handler' in pll_config:
                print("⚠️  配置中仍有传统处理器引用")
                return False
            else:
                print("✓ 配置中已移除传统处理器引用")
            
            # 检查现代化处理器配置
            if pll_config.get('modern_handler') == 'ui.handlers.ModernPLLHandler.ModernPLLHandler':
                print("✓ 现代化处理器配置正确")
            else:
                print("❌ 现代化处理器配置错误")
                return False
            
            # 检查使用现代化版本
            if pll_config.get('use_modern', False):
                print("✓ 配置使用现代化版本")
            else:
                print("❌ 配置未设置使用现代化版本")
                return False
            
            return True
        else:
            print("❌ PLL控制配置不存在")
            return False
            
    except Exception as e:
        print(f"❌ 工厂配置测试失败: {str(e)}")
        return False


def test_manager_imports():
    """测试管理器导入"""
    print("\n测试管理器导入...")
    
    try:
        # 测试TabWindowManager
        from ui.managers.TabWindowManager import TabWindowManager
        print("✓ TabWindowManager导入成功")
        
        # 测试ToolWindowManager
        from ui.managers.ToolWindowManager import ToolWindowManager
        print("✓ ToolWindowManager导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 管理器导入失败: {str(e)}")
        return False


def check_updated_files():
    """检查更新的文件"""
    print("\n检查更新的文件...")
    
    updated_files = [
        'ui/factories/ModernToolWindowFactory.py',
        'ui/factories/ToolWindowFactory.py',
        'ui/managers/TabWindowManager.py',
        'ui/managers/ToolWindowManager.py'
    ]
    
    all_exist = True
    for file_path in updated_files:
        full_path = os.path.join(project_root, file_path)
        if os.path.exists(full_path):
            print(f"✓ {file_path} 存在")
            
            # 检查是否有备份文件
            backup_files = [f for f in os.listdir(os.path.dirname(full_path)) 
                           if f.startswith(os.path.basename(file_path) + '.backup_')]
            if backup_files:
                print(f"  备份: {backup_files[0]}")
        else:
            print(f"❌ {file_path} 不存在")
            all_exist = False
    
    return all_exist


def main():
    """主函数"""
    print("开始简化验证传统PLL处理器移除结果...")
    
    tests = [
        ("文件移除", test_file_removal),
        ("导入失败", test_import_failure),
        ("现代化处理器导入", test_modern_handler_import),
        ("工厂配置", test_factory_config),
        ("管理器导入", test_manager_imports),
        ("更新文件检查", check_updated_files),
    ]
    
    success_count = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                success_count += 1
            else:
                print(f"❌ {test_name}测试失败")
        except Exception as e:
            print(f"❌ {test_name}测试出错: {str(e)}")
    
    print("\n" + "=" * 60)
    print("简化验证结果总结")
    print("=" * 60)
    
    if success_count == len(tests):
        print("🎉 传统PLL处理器移除验证完全成功！")
        print(f"✅ 通过 {success_count}/{len(tests)} 个测试")
        print("\n📋 验证结果:")
        print("   - ✅ PLLHandler.py文件已移除")
        print("   - ✅ PLLHandler无法导入")
        print("   - ✅ ModernPLLHandler可正常导入")
        print("   - ✅ 工厂配置已更新")
        print("   - ✅ 管理器类正常")
        print("   - ✅ 相关文件已更新")
        print("\n🎯 结论:")
        print("   传统PLL处理器已成功移除！")
        print("   现在只使用现代化版本。")
        print("   所有配置和引用都已正确更新。")
        
        print("\n🔧 后续建议:")
        print("   1. 运行主应用程序测试PLL功能")
        print("   2. 确认PLL窗口创建和功能正常")
        print("   3. 如果一切正常，可以删除备份文件")
        
        return True
    else:
        print("❌ 传统PLL处理器移除验证未完全通过")
        print(f"⚠️  通过 {success_count}/{len(tests)} 个测试")
        print("\n🔧 建议操作:")
        print("   1. 检查失败的测试")
        print("   2. 修复发现的问题")
        print("   3. 重新运行验证")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
