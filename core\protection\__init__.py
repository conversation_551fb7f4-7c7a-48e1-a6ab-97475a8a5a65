"""
FSJ04832 安全保护模块
提供运行时安全保护、反调试检测和数据混淆功能
"""

import sys
import ctypes
import inspect

# 导入新的运行时安全模块
from .runtime_security import (
    RuntimeSecurity,
    get_security_instance,
    initialize_security,
    is_security_active
)

# 反调试检测逻辑（保持向后兼容）
def anti_debug():
    """检测常见调试器存在"""
    try:
        # 检测Windows调试器
        if ctypes.windll.kernel32.IsDebuggerPresent():
            return True

        # 检测Python调试器
        for frame in inspect.stack():
            if frame.filename.endswith('pydevd.py'):
                return True

    except:
        pass

    return False

# 二进制混淆工具（保持向后兼容）
def obfuscate_config(data):
    """对配置数据进行简单混淆"""
    return bytes([b ^ 0x55 for b in data.encode()]) if isinstance(data, str) else data

# 导出所有安全功能
__all__ = [
    # 传统功能
    'anti_debug',
    'obfuscate_config',
    # 新的运行时安全功能
    'RuntimeSecurity',
    'get_security_instance',
    'initialize_security',
    'is_security_active'
]