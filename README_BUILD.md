# 自动版本管理构建系统

## 🚀 快速开始

### 基本使用

```bash
# 默认构建（自动增加构建号）
python build_exe.py

# 使用build.spec文件构建
python build_exe.py --spec-file build.spec
```

### 版本管理

```bash
# 增加构建号 (1.0.0.0 → 1.0.0.1)
python build_exe.py --version-type build

# 增加补丁版本 (1.0.0.5 → 1.0.1.0)
python build_exe.py --version-type patch

# 增加次版本 (1.0.5.3 → 1.1.0.0)
python build_exe.py --version-type minor

# 增加主版本 (1.5.3.2 → 2.0.0.0)
python build_exe.py --version-type major

# 不增加版本号
python build_exe.py --no-version-increment
```

## 📋 功能特性

### ✅ 自动版本管理
- 🔢 四段式版本号：`主版本.次版本.补丁版本.构建号`
- 📅 自动记录构建时间
- 💾 版本信息持久化存储

### ✅ 智能文件命名
- 📁 可执行文件自动包含版本号：`FSJConfigTool1.0.0.exe`
- 🔄 自动更新spec文件中的文件名
- 📝 支持自定义命名规则

### ✅ 完整构建流程
- 🧹 自动清理构建目录
- 📦 使用PyInstaller打包
- 📂 自动复制资源文件
- 🛡️ 错误处理和编码支持

## 📊 版本号策略

| 版本类型 | 使用场景 | 示例变化 |
|---------|---------|----------|
| **构建号** | 日常开发、小修复 | `1.0.0.0` → `1.0.0.1` |
| **补丁版本** | Bug修复、安全更新 | `1.0.0.5` → `1.0.1.0` |
| **次版本** | 新功能、API扩展 | `1.0.5.3` → `1.1.0.0` |
| **主版本** | 重大更新、架构变更 | `1.5.3.2` → `2.0.0.0` |

## 📁 文件结构

```
项目根目录/
├── build_exe.py          # 构建脚本
├── build.spec            # PyInstaller配置
├── version.json          # 版本信息文件
├── main.py              # 主程序入口
└── dist/                # 输出目录
    └── FSJConfigTool1.0.0.exe
```

## ⚙️ 配置文件

### version.json
```json
{
  "version": {
    "major": 1,
    "minor": 0,
    "patch": 0,
    "build": 0
  },
  "app_info": {
    "name": "FSJ04832 寄存器配置工具",
    "description": "用于配置和管理FSJ04832寄存器的专业工具",
    "company": "FSJ Technology",
    "copyright": "© 2024 FSJ Technology",
    "author": "开发团队"
  },
  "build_info": {
    "last_build_date": "2025-06-04 15:36:12",
    "build_type": "release",
    "target_platform": "windows"
  }
}
```

## 🔧 命令行选项

| 选项 | 说明 | 默认值 |
|------|------|--------|
| `--version-type` | 版本号增加类型 | `build` |
| `--no-version-increment` | 不增加版本号 | `False` |
| `--spec-file` | 指定spec文件路径 | `build.spec` |
| `--help` | 显示帮助信息 | - |

## 📝 使用示例

### 日常开发
```bash
# 每次构建自动增加构建号
python build_exe.py
```

### 发布流程
```bash
# 修复Bug后发布补丁
python build_exe.py --version-type patch

# 添加新功能后发布次版本
python build_exe.py --version-type minor

# 重大更新后发布主版本
python build_exe.py --version-type major
```

### 特殊情况
```bash
# 重新构建但不改变版本号
python build_exe.py --no-version-increment

# 使用自定义spec文件
python build_exe.py --spec-file custom.spec
```

## 🧪 测试功能

```bash
# 运行版本管理测试
python test_version_management.py

# 查看帮助信息
python build_exe.py --help
```

## 🔍 故障排除

### 常见问题

1. **PyInstaller未安装**
   ```bash
   pip install pyinstaller
   ```

2. **编码错误**
   - 脚本已自动处理UTF-8编码问题
   - 如遇问题，检查系统编码设置

3. **spec文件不存在**
   - 确保`build.spec`文件存在
   - 或使用`--spec-file`指定正确路径

4. **权限问题**
   - 某些系统可能需要管理员权限
   - 确保对项目目录有写权限

## 📈 版本历史

- **v1.0.0**: 初始版本，基本构建功能
- **v1.0.1**: 添加自动版本管理
- **v1.1.0**: 增强错误处理和编码支持
- **v2.0.0**: 完整重构，添加命令行参数支持

## 🤝 贡献指南

1. 修改代码后运行测试：`python test_version_management.py`
2. 确保所有功能正常工作
3. 更新文档和版本信息
4. 提交更改

## 📞 技术支持

如有问题或建议，请联系开发团队。

---

**🎯 目标**: 让每次构建都有明确的版本标识，简化发布流程，提高开发效率！
