# 用户配置管理功能使用指南

## 功能概述

本软件新增了两个重要的配置管理功能：

1. **默认配置功能** - 将当前配置保存为默认配置，并可随时恢复
2. **软件记忆功能** - 自动保存和恢复最近一次的配置

## 功能详情

### 1. 默认配置功能

#### 使用方法
1. 点击菜单栏 **设置** → **默认配置** → **生成默认配置（从register.json）**
   - 系统会自动从register.json文件中读取所有寄存器的默认值
   - 生成包含完整默认配置的配置文件
2. 当需要恢复默认配置时，点击 **设置** → **默认配置** → **恢复默认配置**

#### 配置来源
默认配置直接来源于register.json文件中每个寄存器位段的"default"字段，确保与芯片规格书一致。

#### 保存的内容
- 所有寄存器的默认值（从register.json文件中的"default"字段计算得出）
- 时钟输入控制窗口的默认频率值和分频设置
- PLL控制窗口的默认频率值和分频器设置（包括ExternalVCXOFreq、OSCinFreq等重要频率控件）
- 时钟输出窗口的默认分频值和频率显示
- 其他工具窗口的默认配置

#### 重要频率控件说明
- **ExternalVCXOFreq**：外部VCXO频率控件，位于PLL控制窗口，默认值122.88MHz
- **OSCinFreq**：振荡器输入频率控件，位于PLL控制窗口，默认值122.88MHz
- **三个时钟输入频率**：CLKin0、CLKin1、CLKin2的频率值，位于时钟输入控制窗口

### 2. 软件记忆功能

#### 工作原理
- **自动保存**：每次关闭软件时，系统会自动保存当前的所有配置
- **自动恢复**：下次启动软件时，系统会自动恢复最近一次的配置

#### 无需手动操作
此功能完全自动化，用户无需进行任何手动操作。

## 配置文件位置

配置文件保存在用户目录下的隐藏文件夹中：
```
Windows: C:\Users\<USER>\.fsj_register_tool\
```

包含以下文件：
- `default_config.json` - 默认配置文件
- `last_config.json` - 最近配置文件（软件记忆）

## 配置内容说明

### 寄存器配置
保存所有寄存器的当前值，格式为地址-值对：
```json
{
  "registers": {
    "0x50": 4660,
    "0x51": 22136,
    "0x52": 39612
  }
}
```

### 工具窗口配置
保存各个工具窗口的控件状态：

#### 时钟输入控制窗口
- 各时钟输入的频率值
- 分频器设置
- 时钟源选择

#### PLL控制窗口
- 输入频率设置
- PLL分频器参数
- 计算出的频率值

#### 时钟输出窗口
- 各输出通道的分频值
- 计算出的输出频率

## 使用建议

1. **生成默认配置**：
   - 建议在首次使用软件时生成默认配置
   - 默认配置基于register.json文件，确保与芯片规格一致
   - 这样可以在任何时候快速恢复到芯片的标准配置状态

2. **利用软件记忆**：
   - 软件记忆功能确保您的工作不会丢失
   - 即使意外关闭软件，下次启动时也会恢复到关闭前的状态

3. **配置备份**：
   - 重要的配置可以通过复制配置文件进行备份
   - 配置文件为JSON格式，可以手动编辑（高级用户）

## 注意事项

1. **权限要求**：
   - 软件需要在用户目录下创建配置文件夹的权限
   - 如果遇到权限问题，请以管理员身份运行软件

2. **配置兼容性**：
   - 配置文件与软件版本相关
   - 升级软件版本时，旧的配置文件可能需要重新生成

3. **故障排除**：
   - 如果配置恢复出现问题，可以删除配置文件夹重新开始
   - 配置文件损坏时，软件会自动跳过恢复过程

## 技术实现

### 架构设计
- `UserConfigurationService` - 核心配置管理服务
- 集成到主窗口的初始化和关闭流程
- 通过插件系统获取工具窗口的配置

### 配置收集
- 默认配置：从register.json文件中解析每个寄存器位段的默认值
- 最近配置：自动遍历所有工具窗口，提取控件的当前值
- 生成结构化的配置数据

### 配置应用
- 解析配置文件
- 恢复寄存器值
- 恢复工具窗口的控件状态

## 更新日志

### v1.0.0 (2025-07-04)
- 新增默认配置生成和恢复功能（基于register.json文件）
- 新增软件记忆功能
- 在设置菜单中添加默认配置选项
- 支持寄存器值和工具窗口配置的完整保存和恢复
- 默认配置直接从register.json文件的"default"字段提取，确保与芯片规格一致
