#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化的现代化处理器集成测试
验证现代化处理器的基本集成功能
"""

import sys
import os
import unittest

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt

from ui.handlers.ModernRegisterTreeHandler import ModernRegisterTreeHandler
from ui.handlers.ModernRegisterTableHandler import ModernRegisterTableHandler
from ui.handlers.ModernRegisterIOHandler import ModernRegisterIOHandler
from core.services.register.RegisterManager import RegisterManager


class TestSimpleIntegration(unittest.TestCase):
    """简化的集成测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """设置测试"""
        # 创建模拟的寄存器配置
        self.mock_registers = {
            "0x00": {
                "bits": [
                    {
                        "bit": "15:0",
                        "name": "DA_DEVICE_VERSION",
                        "default": "0001001100000000",
                        "widget_name": "deviceVersion",
                        "widget_type": "label",
                        "options": None,
                        "description": "Device version"
                    }
                ]
            },
            "0x02": {
                "bits": [
                    {
                        "bit": "0",
                        "name": "POWERDOWN",
                        "default": "0",
                        "widget_name": "powerDown",
                        "widget_type": "checkbox",
                        "options": None,
                        "description": "Power down control"
                    }
                ]
            }
        }
        
        # 创建RegisterManager
        self.register_manager = RegisterManager(self.mock_registers)
        
        # 创建真实的QMainWindow作为父窗口
        self.main_window = QMainWindow()
    
    def tearDown(self):
        """清理测试"""
        if hasattr(self, 'main_window'):
            self.main_window.close()
    
    def test_modern_tree_handler_creation(self):
        """测试现代化树处理器创建"""
        tree_handler = ModernRegisterTreeHandler(
            parent=self.main_window,
            register_manager=self.register_manager
        )
        
        # 验证创建成功
        self.assertIsNotNone(tree_handler)
        self.assertEqual(tree_handler.register_manager, self.register_manager)
        self.assertIsNotNone(tree_handler.tree_widget)
        
        # 测试树填充
        tree_handler.populate_tree_widget()
        self.assertEqual(tree_handler.tree_widget.topLevelItemCount(), 1)
        
        # 清理
        tree_handler.close()
    
    def test_modern_table_handler_creation(self):
        """测试现代化表格处理器创建"""
        table_handler = ModernRegisterTableHandler(
            parent=self.main_window,
            register_manager=self.register_manager
        )
        
        # 验证创建成功
        self.assertIsNotNone(table_handler)
        self.assertEqual(table_handler.register_manager, self.register_manager)
        self.assertIsNotNone(table_handler.bit_field_table)

        # 测试表格更新
        table_handler.show_bit_fields("0x00", 0x1234)
        self.assertGreater(table_handler.bit_field_table.rowCount(), 0)
        
        # 清理
        table_handler.close()
    
    def test_modern_io_handler_creation(self):
        """测试现代化IO处理器创建"""
        io_handler = ModernRegisterIOHandler(
            parent=self.main_window,
            register_manager=self.register_manager,
            register_repo=None  # 可以为None
        )
        
        # 验证创建成功
        self.assertIsNotNone(io_handler)
        self.assertEqual(io_handler.register_manager, self.register_manager)
        self.assertIsNotNone(io_handler.addr_line_edit)
        self.assertIsNotNone(io_handler.value_line_edit)
        
        # 测试地址和值设置
        io_handler.set_address_display(0x02)
        io_handler.set_value_display(0x1234)
        
        self.assertEqual(io_handler.addr_line_edit.text(), "0x02")
        self.assertEqual(io_handler.value_line_edit.text(), "0x1234")
        
        # 清理
        io_handler.close()
    
    def test_handlers_signal_compatibility(self):
        """测试处理器信号兼容性"""
        tree_handler = ModernRegisterTreeHandler(
            parent=self.main_window,
            register_manager=self.register_manager
        )
        
        io_handler = ModernRegisterIOHandler(
            parent=self.main_window,
            register_manager=self.register_manager,
            register_repo=None
        )
        
        # 验证信号存在
        self.assertTrue(hasattr(tree_handler, 'register_selected'))
        self.assertTrue(hasattr(io_handler, 'read_requested'))
        self.assertTrue(hasattr(io_handler, 'write_requested'))
        
        # 清理
        tree_handler.close()
        io_handler.close()
    
    def test_register_manager_integration(self):
        """测试RegisterManager集成"""
        tree_handler = ModernRegisterTreeHandler(
            parent=self.main_window,
            register_manager=self.register_manager
        )
        
        table_handler = ModernRegisterTableHandler(
            parent=self.main_window,
            register_manager=self.register_manager
        )
        
        # 验证都使用相同的RegisterManager
        self.assertEqual(tree_handler.register_manager, self.register_manager)
        self.assertEqual(table_handler.register_manager, self.register_manager)
        
        # 测试RegisterManager功能
        registers = self.register_manager.get_all_registers()
        self.assertEqual(len(registers), 2)
        
        # 清理
        tree_handler.close()
        table_handler.close()
    
    def test_global_register_update_handling(self):
        """测试全局寄存器更新处理"""
        table_handler = ModernRegisterTableHandler(
            parent=self.main_window,
            register_manager=self.register_manager
        )
        
        io_handler = ModernRegisterIOHandler(
            parent=self.main_window,
            register_manager=self.register_manager,
            register_repo=None
        )
        
        # 测试全局更新回调
        table_handler.on_global_register_update("0x02", 0x5678)
        io_handler.on_global_register_update("0x02", 0x5678)
        
        # 验证没有异常
        self.assertTrue(True)
        
        # 清理
        table_handler.close()
        io_handler.close()
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试无效的RegisterManager
        try:
            tree_handler = ModernRegisterTreeHandler(
                parent=self.main_window,
                register_manager=None
            )
            # 应该能创建，但功能受限
            self.assertIsNotNone(tree_handler)
            tree_handler.close()
        except Exception as e:
            # 如果出错，应该是可预期的错误
            self.assertIsInstance(e, (TypeError, AttributeError))
    
    def test_memory_cleanup(self):
        """测试内存清理"""
        handlers = []
        
        # 创建多个处理器
        for i in range(3):
            tree_handler = ModernRegisterTreeHandler(
                parent=self.main_window,
                register_manager=self.register_manager
            )
            handlers.append(tree_handler)
        
        # 验证创建成功
        self.assertEqual(len(handlers), 3)
        
        # 清理所有处理器
        for handler in handlers:
            handler.close()
        
        # 验证清理成功（不应该有异常）
        self.assertTrue(True)
    
    def test_performance_basic(self):
        """测试基本性能"""
        import time
        
        # 测试创建时间
        start_time = time.time()
        tree_handler = ModernRegisterTreeHandler(
            parent=self.main_window,
            register_manager=self.register_manager
        )
        creation_time = time.time() - start_time
        
        # 创建时间应该很快（小于1秒）
        self.assertLess(creation_time, 1.0)
        
        # 测试填充时间
        start_time = time.time()
        tree_handler.populate_tree_widget()
        populate_time = time.time() - start_time
        
        # 填充时间应该很快（小于0.5秒）
        self.assertLess(populate_time, 0.5)
        
        # 清理
        tree_handler.close()
    
    def test_ui_components_exist(self):
        """测试UI组件存在"""
        tree_handler = ModernRegisterTreeHandler(
            parent=self.main_window,
            register_manager=self.register_manager
        )
        
        table_handler = ModernRegisterTableHandler(
            parent=self.main_window,
            register_manager=self.register_manager
        )
        
        io_handler = ModernRegisterIOHandler(
            parent=self.main_window,
            register_manager=self.register_manager,
            register_repo=None
        )
        
        # 验证UI组件存在
        self.assertIsNotNone(tree_handler.tree_widget)
        self.assertIsNotNone(table_handler.bit_field_table)
        self.assertIsNotNone(io_handler.addr_line_edit)
        self.assertIsNotNone(io_handler.value_line_edit)
        self.assertIsNotNone(io_handler.read_btn)
        self.assertIsNotNone(io_handler.write_btn)
        
        # 清理
        tree_handler.close()
        table_handler.close()
        io_handler.close()


def run_simple_integration_tests():
    """运行简化集成测试"""
    unittest.main()


if __name__ == '__main__':
    run_simple_integration_tests()
