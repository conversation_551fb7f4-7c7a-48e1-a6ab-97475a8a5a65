#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
模式设置插件
将模式设置工具窗口改造为插件
"""

from PyQt5.QtCore import pyqtSignal
from core.services.plugin.PluginManager import IToolWindowPlugin
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class SetModesPlugin(IToolWindowPlugin):
    """模式设置插件"""
    
    @property
    def name(self) -> str:
        return "模式设置"
    
    @property
    def version(self) -> str:
        return "1.0.0"
    
    @property
    def description(self) -> str:
        return "配置设备的工作模式和基本设置"
    
    @property
    def menu_text(self) -> str:
        return "模式设置(&M)"
    
    @property
    def icon_path(self) -> str:
        return None  # 可以后续添加图标
    
    def __init__(self):
        """初始化插件"""
        self.context = None
        self.window_instance = None
        self.register_manager = None
        
    def initialize(self, context):
        """初始化插件
        
        Args:
            context: 应用程序上下文（通常是主窗口）
        """
        self.context = context
        
        # 获取寄存器管理器
        if hasattr(context, 'register_manager'):
            self.register_manager = context.register_manager
        else:
            logger.warning("无法获取寄存器管理器")
        
        logger.info(f"插件 '{self.name}' 初始化完成")
    
    def create_window(self, parent=None):
        """创建工具窗口

        Args:
            parent: 父窗口

        Returns:
            工具窗口实例
        """
        # 如果窗口实例存在但已无效，清理它
        if self.window_instance is not None:
            if self._is_window_instance_valid():
                # 窗口仍然有效，直接返回
                logger.debug(f"'{self.name}' 窗口实例有效，直接返回")
                return self.window_instance
            else:
                # 窗口已被删除，清理引用
                logger.info(f"检测到 '{self.name}' 窗口已被删除，清理引用")
                self.window_instance = None

        if self.window_instance is None:
            try:
                # 使用现代化处理器创建窗口
                from ui.handlers.ModernSetModesHandler import ModernSetModesHandler

                # 确保有有效的父窗口
                if parent is None and self.context:
                    parent = self.context

                # 获取寄存器管理器 - 优先从主窗口获取
                register_manager = self.register_manager
                if not register_manager and self.context and hasattr(self.context, 'register_manager'):
                    register_manager = self.context.register_manager
                    logger.info(f"从主窗口获取寄存器管理器: {register_manager}")

                if not register_manager:
                    logger.warning(f"无法获取寄存器管理器，插件 '{self.name}' 功能可能受限")

                # 创建现代化处理器实例
                self.window_instance = ModernSetModesHandler(
                    parent=parent,
                    register_manager=register_manager
                )

                # 设置主窗口引用
                if self.context:
                    self.window_instance.main_window = self.context

                # 连接窗口关闭信号
                if hasattr(self.window_instance, 'window_closed'):
                    self.window_instance.window_closed.connect(self._on_window_closed)

                # 连接Qt的destroyed信号来处理对象被删除的情况
                if hasattr(self.window_instance, 'destroyed'):
                    self.window_instance.destroyed.connect(self._on_window_destroyed)

                logger.info(f"创建 '{self.name}' 工具窗口成功")

            except Exception as e:
                logger.error(f"创建 '{self.name}' 工具窗口失败: {str(e)}")
                import traceback
                logger.error(f"详细错误信息: {traceback.format_exc()}")
                return None

        return self.window_instance

    def _is_window_instance_valid(self) -> bool:
        """检查窗口实例是否有效"""
        if self.window_instance is None:
            return False

        try:
            # 检查是否是已删除的C++对象
            if hasattr(self.window_instance, '__class__'):
                _ = self.window_instance.__class__.__name__

            # 尝试访问窗口的基本属性来验证其有效性
            self.window_instance.isVisible()
            self.window_instance.windowTitle()

            return True
        except (RuntimeError, AttributeError) as e:
            # 特别检查"wrapped C/C++ object has been deleted"错误
            error_msg = str(e).lower()
            if "wrapped c/c++ object" in error_msg and "deleted" in error_msg:
                logger.debug(f"检测到已删除的C++对象: {str(e)}")
                return False
            logger.debug(f"窗口实例无效: {str(e)}")
            return False
        except Exception as e:
            logger.debug(f"窗口实例有效性检查时出现异常: {str(e)}")
            return False

    def _on_window_closed(self):
        """窗口关闭处理"""
        self.window_instance = None
        logger.info(f"'{self.name}' 工具窗口已关闭")

    def _on_window_destroyed(self):
        """窗口对象被销毁时的处理"""
        logger.info(f"'{self.name}' 工具窗口对象已被销毁")
        self.window_instance = None
    
    def cleanup(self):
        """清理插件资源"""
        if self.window_instance:
            try:
                self.window_instance.close()
            except:
                pass
            self.window_instance = None
        
        logger.info(f"插件 '{self.name}' 已清理")
    
    def get_shortcut(self) -> str:
        """获取快捷键"""
        return "Ctrl+M"
    
    def is_checkable(self) -> bool:
        """菜单项是否可选中"""
        return True


# 插件入口点
plugin_class = SetModesPlugin
