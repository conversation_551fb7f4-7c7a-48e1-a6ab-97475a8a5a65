#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试分离窗口修复功能
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QLabel, QPushButton, QTextEdit
from PyQt5.QtCore import Qt, QTimer
from utils.Log import get_module_logger

logger = get_module_logger(__name__)

class UndockFixTestWindow(QMainWindow):
    """分离窗口修复功能测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.plugin_service = None
        self.init_ui()
        self.find_plugin_service()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("分离窗口修复功能测试")
        self.setGeometry(300, 300, 700, 600)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("分离窗口修复功能测试工具")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; text-align: center;")
        layout.addWidget(title_label)
        
        # 状态显示
        self.status_label = QLabel("状态: 准备就绪")
        self.status_label.setStyleSheet("padding: 10px; background-color: #f0f0f0; border: 1px solid #ccc;")
        layout.addWidget(self.status_label)
        
        # 说明文本
        instructions = """
修复内容：
1. 分离窗口时保持窗口对象存活，不被意外删除
2. 停止全局鼠标监控定时器，避免访问已删除对象
3. 只清理容器资源，不清理窗口本身
4. 添加窗口有效性检查，防止访问无效对象

测试步骤：
1. 点击"查找插件服务"确保连接成功
2. 点击"打开测试窗口"创建一个测试窗口
3. 点击"分离窗口"测试分离功能
4. 观察窗口是否正常显示且可操作
5. 检查控制台是否还有错误日志

预期结果：
- 分离后窗口正常显示和操作
- 没有"wrapped C/C++ object has been deleted"错误
- 定时器正确停止，不再产生错误日志
        """
        
        instructions_text = QTextEdit()
        instructions_text.setPlainText(instructions)
        instructions_text.setMaximumHeight(200)
        instructions_text.setReadOnly(True)
        layout.addWidget(instructions_text)
        
        # 按钮区域
        button_layout = QVBoxLayout()
        
        find_service_btn = QPushButton("查找插件服务")
        find_service_btn.clicked.connect(self.find_plugin_service)
        button_layout.addWidget(find_service_btn)
        
        open_test_btn = QPushButton("打开测试窗口")
        open_test_btn.clicked.connect(self.open_test_window)
        button_layout.addWidget(open_test_btn)
        
        undock_btn = QPushButton("分离窗口")
        undock_btn.clicked.connect(self.test_undock_window)
        button_layout.addWidget(undock_btn)
        
        dock_btn = QPushButton("停靠窗口")
        dock_btn.clicked.connect(self.test_dock_window)
        button_layout.addWidget(dock_btn)
        
        check_timers_btn = QPushButton("检查定时器状态")
        check_timers_btn.clicked.connect(self.check_timer_status)
        button_layout.addWidget(check_timers_btn)
        
        layout.addLayout(button_layout)
        
        # 日志显示区域
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setPlaceholderText("操作日志将显示在这里...")
        layout.addWidget(self.log_text)
        
    def find_plugin_service(self):
        """查找插件服务"""
        try:
            # 尝试从主窗口获取插件服务
            for widget in QApplication.topLevelWidgets():
                if (isinstance(widget, QMainWindow) and 
                    widget != self and 
                    hasattr(widget, 'plugin_integration_service')):
                    self.plugin_service = widget.plugin_integration_service
                    self.status_label.setText("已找到插件服务")
                    self.log_text.append("✅ 成功连接到插件服务")
                    logger.info("找到插件服务")
                    return
            
            self.status_label.setText("未找到插件服务")
            self.log_text.append("❌ 未找到插件服务，请确保主程序正在运行")
            logger.warning("未找到插件服务")
            
        except Exception as e:
            error_msg = f"查找插件服务时出错: {str(e)}"
            self.status_label.setText(error_msg)
            self.log_text.append(f"❌ {error_msg}")
            logger.error(error_msg)
            
    def open_test_window(self):
        """打开测试窗口"""
        try:
            if not self.plugin_service:
                self.status_label.setText("请先查找插件服务")
                return
            
            # 尝试打开时钟输入控制窗口
            plugin_name = "时钟输入控制"
            
            # 检查是否已经打开
            if plugin_name in self.plugin_service.plugin_windows:
                self.status_label.setText(f"窗口 {plugin_name} 已经打开")
                self.log_text.append(f"ℹ️ 窗口 {plugin_name} 已经打开")
                return
            
            # 尝试通过菜单打开
            if hasattr(self.plugin_service, 'plugin_actions'):
                for name, action in self.plugin_service.plugin_actions.items():
                    if "时钟输入控制" in action.text():
                        action.trigger()
                        self.status_label.setText(f"已触发打开 {plugin_name}")
                        self.log_text.append(f"✅ 已触发打开 {plugin_name}")
                        return
            
            self.status_label.setText("未找到时钟输入控制菜单")
            self.log_text.append("❌ 未找到时钟输入控制菜单")
            
        except Exception as e:
            error_msg = f"打开测试窗口失败: {str(e)}"
            self.status_label.setText(error_msg)
            self.log_text.append(f"❌ {error_msg}")
            logger.error(error_msg)
            
    def test_undock_window(self):
        """测试分离窗口"""
        try:
            if not self.plugin_service:
                self.status_label.setText("请先查找插件服务")
                return
            
            # 查找已打开的窗口
            plugin_windows = self.plugin_service.plugin_windows
            if not plugin_windows:
                self.status_label.setText("没有打开的窗口可以分离")
                self.log_text.append("❌ 没有打开的窗口可以分离")
                return
            
            # 选择第一个窗口进行分离
            plugin_name = list(plugin_windows.keys())[0]
            window = plugin_windows[plugin_name]
            
            self.log_text.append(f"🔄 开始分离窗口: {plugin_name}")
            
            # 检查窗口分离前的状态
            is_valid_before = self.plugin_service._is_window_valid(window)
            has_timer_before = hasattr(window, '_mouse_monitor_timer')
            
            self.log_text.append(f"分离前状态: 窗口有效={is_valid_before}, 有定时器={has_timer_before}")
            
            # 执行分离
            self.plugin_service.undock_window_from_tab(plugin_name)
            
            # 等待一下再检查状态
            QTimer.singleShot(1000, lambda: self.check_undock_result(plugin_name))
            
        except Exception as e:
            error_msg = f"测试分离窗口失败: {str(e)}"
            self.status_label.setText(error_msg)
            self.log_text.append(f"❌ {error_msg}")
            logger.error(error_msg)
            
    def check_undock_result(self, plugin_name):
        """检查分离结果"""
        try:
            if plugin_name in self.plugin_service.plugin_windows:
                window = self.plugin_service.plugin_windows[plugin_name]
                
                # 检查窗口状态
                is_valid_after = self.plugin_service._is_window_valid(window)
                is_visible = window.isVisible() if is_valid_after else False
                has_timer_after = hasattr(window, '_mouse_monitor_timer')
                timer_active = False
                
                if has_timer_after:
                    timer = window._mouse_monitor_timer
                    timer_active = timer.isActive() if timer else False
                
                self.log_text.append(f"分离后状态:")
                self.log_text.append(f"  - 窗口有效: {is_valid_after}")
                self.log_text.append(f"  - 窗口可见: {is_visible}")
                self.log_text.append(f"  - 有定时器: {has_timer_after}")
                self.log_text.append(f"  - 定时器活跃: {timer_active}")
                
                if is_valid_after and is_visible and not timer_active:
                    self.status_label.setText("✅ 分离成功，窗口正常")
                    self.log_text.append("✅ 分离测试通过")
                else:
                    self.status_label.setText("⚠️ 分离有问题")
                    self.log_text.append("⚠️ 分离测试发现问题")
            else:
                self.status_label.setText("❌ 分离后窗口丢失")
                self.log_text.append("❌ 分离后窗口引用丢失")
                
        except Exception as e:
            error_msg = f"检查分离结果失败: {str(e)}"
            self.status_label.setText(error_msg)
            self.log_text.append(f"❌ {error_msg}")
            logger.error(error_msg)
            
    def test_dock_window(self):
        """测试停靠窗口"""
        try:
            if not self.plugin_service:
                self.status_label.setText("请先查找插件服务")
                return
            
            self.log_text.append("🔄 测试停靠所有悬浮窗口...")
            
            # 执行停靠
            self.plugin_service.dock_all_windows()
            
            self.status_label.setText("停靠测试完成")
            self.log_text.append("✅ 停靠测试完成")
            
        except Exception as e:
            error_msg = f"测试停靠窗口失败: {str(e)}"
            self.status_label.setText(error_msg)
            self.log_text.append(f"❌ {error_msg}")
            logger.error(error_msg)
            
    def check_timer_status(self):
        """检查定时器状态"""
        try:
            if not self.plugin_service:
                self.status_label.setText("请先查找插件服务")
                return
            
            self.log_text.append("🔍 检查所有窗口的定时器状态:")
            
            plugin_windows = self.plugin_service.plugin_windows
            if not plugin_windows:
                self.log_text.append("没有打开的窗口")
                return
            
            for plugin_name, window in plugin_windows.items():
                try:
                    is_valid = self.plugin_service._is_window_valid(window)
                    has_timer = hasattr(window, '_mouse_monitor_timer')
                    timer_active = False
                    
                    if has_timer:
                        timer = window._mouse_monitor_timer
                        timer_active = timer.isActive() if timer else False
                    
                    self.log_text.append(f"  {plugin_name}:")
                    self.log_text.append(f"    - 窗口有效: {is_valid}")
                    self.log_text.append(f"    - 有定时器: {has_timer}")
                    self.log_text.append(f"    - 定时器活跃: {timer_active}")
                    
                except Exception as e:
                    self.log_text.append(f"  {plugin_name}: 检查失败 - {str(e)}")
            
            self.status_label.setText("定时器状态检查完成")
            
        except Exception as e:
            error_msg = f"检查定时器状态失败: {str(e)}"
            self.status_label.setText(error_msg)
            self.log_text.append(f"❌ {error_msg}")
            logger.error(error_msg)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    test_window = UndockFixTestWindow()
    test_window.show()
    
    logger.info("分离窗口修复功能测试工具已启动")
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
