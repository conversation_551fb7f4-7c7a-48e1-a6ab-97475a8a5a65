{".class": "MypyFile", "_fullname": "unittest.suite", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "BaseTestSuite": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "unittest.suite.BaseTestSuite", "name": "BaseTestSuite", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "unittest.suite.BaseTestSuite", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "unittest.suite", "mro": ["unittest.suite.BaseTestSuite", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "result"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "unittest.suite.BaseTestSuite.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "result"], "arg_types": ["unittest.suite.BaseTestSuite", "unittest.result.TestResult"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of BaseTestSuite", "ret_type": "unittest.result.TestResult", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "unittest.suite.BaseTestSuite.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["unittest.suite.BaseTestSuite", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of BaseTestSuite", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "unittest.suite.BaseTestSuite.__hash__", "name": "__hash__", "type": {".class": "NoneType"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "tests"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "unittest.suite.BaseTestSuite.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "tests"], "arg_types": ["unittest.suite.BaseTestSuite", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "unittest.suite._TestType"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BaseTestSuite", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "unittest.suite.BaseTestSuite.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["unittest.suite.BaseTestSuite"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of BaseTestSuite", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "unittest.suite._TestType"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_removed_tests": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "unittest.suite.BaseTestSuite._removed_tests", "name": "_removed_tests", "type": "builtins.int"}}, "_tests": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "unittest.suite.BaseTestSuite._tests", "name": "_tests", "type": {".class": "Instance", "args": ["unittest.case.TestCase"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "addTest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "test"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "unittest.suite.BaseTestSuite.addTest", "name": "addTest", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "test"], "arg_types": ["unittest.suite.BaseTestSuite", {".class": "TypeAliasType", "args": [], "type_ref": "unittest.suite._TestType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "addTest of BaseTestSuite", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "addTests": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tests"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "unittest.suite.BaseTestSuite.addTests", "name": "addTests", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tests"], "arg_types": ["unittest.suite.BaseTestSuite", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "unittest.suite._TestType"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "addTests of BaseTestSuite", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "countTestCases": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "unittest.suite.BaseTestSuite.countTestCases", "name": "countTestCases", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["unittest.suite.BaseTestSuite"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "countTestCases of BaseTestSuite", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "debug": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "unittest.suite.BaseTestSuite.debug", "name": "debug", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["unittest.suite.BaseTestSuite"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "debug of BaseTestSuite", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "result"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "unittest.suite.BaseTestSuite.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "result"], "arg_types": ["unittest.suite.BaseTestSuite", "unittest.result.TestResult"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of BaseTestSuite", "ret_type": "unittest.result.TestResult", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "unittest.suite.BaseTestSuite.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "unittest.suite.BaseTestSuite", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TestSuite": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["unittest.suite.BaseTestSuite"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "unittest.suite.TestSuite", "name": "TestSuite", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "unittest.suite.TestSuite", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "unittest.suite", "mro": ["unittest.suite.TestSuite", "unittest.suite.BaseTestSuite", "builtins.object"], "names": {".class": "SymbolTable", "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "result", "debug"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "unittest.suite.TestSuite.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "result", "debug"], "arg_types": ["unittest.suite.TestSuite", "unittest.result.TestResult", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of TestSuite", "ret_type": "unittest.result.TestResult", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "unittest.suite.TestSuite.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "unittest.suite.TestSuite", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_TestType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "unittest.suite._TestType", "line": 7, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["unittest.case.TestCase", "unittest.suite.TestSuite"], "uses_pep604_syntax": true}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unittest.suite.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unittest.suite.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unittest.suite.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unittest.suite.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unittest.suite.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unittest.suite.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "unittest": {".class": "SymbolTableNode", "cross_ref": "unittest", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\unittest\\suite.pyi"}