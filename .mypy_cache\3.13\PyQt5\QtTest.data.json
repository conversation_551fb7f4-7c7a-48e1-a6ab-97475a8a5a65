{".class": "MypyFile", "_fullname": "PyQt5.QtTest", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "PYQT_OPENGL_ARRAY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "PyQt5.QtTest.PYQT_OPENGL_ARRAY", "line": 39, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "PyQt5.sip.Buffer"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "PYQT_OPENGL_BOUND_ARRAY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "PyQt5.QtTest.PYQT_OPENGL_BOUND_ARRAY", "line": 41, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "PyQt5.sip.Buffer"}, "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "PYQT_SIGNAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "PyQt5.QtTest.PYQT_SIGNAL", "line": 35, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["PyQt5.QtCore.pyqtSignal", "PyQt5.QtCore.pyqtBoundSignal"], "uses_pep604_syntax": false}}}, "PYQT_SLOT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "PyQt5.QtTest.PYQT_SLOT", "line": 36, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "PyQt5.QtCore.pyqtBoundSignal"], "uses_pep604_syntax": false}}}, "PyQt5": {".class": "SymbolTableNode", "cross_ref": "PyQt5", "kind": "Gdef", "module_hidden": true, "module_public": false}, "QAbstractItemModelTester": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["PyQt5.QtCore.QObject"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "PyQt5.QtTest.QAbstractItemModelTester", "name": "QAbstractItemModelTester", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QAbstractItemModelTester", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "PyQt5.QtTest", "mro": ["PyQt5.QtTest.QAbstractItemModelTester", "PyQt5.QtCore.QObject", "PyQt5.sip.wrapper", "PyQt5.sip.simplewrapper", "builtins.object"], "names": {".class": "SymbolTable", "FailureReportingMode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.int"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "PyQt5.QtTest.QAbstractItemModelTester.FailureReportingMode", "name": "FailureReportingMode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QAbstractItemModelTester.FailureReportingMode", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "PyQt5.QtTest", "mro": ["PyQt5.QtTest.QAbstractItemModelTester.FailureReportingMode", "builtins.int", "builtins.object"], "names": {".class": "SymbolTable", "Fatal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "PyQt5.QtTest.QAbstractItemModelTester.FailureReportingMode.Fatal", "name": "Fatal", "type": "PyQt5.QtTest.QAbstractItemModelTester.FailureReportingMode"}}, "QtTest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "PyQt5.QtTest.QAbstractItemModelTester.FailureReportingMode.QtTest", "name": "QtTest", "type": "PyQt5.QtTest.QAbstractItemModelTester.FailureReportingMode"}}, "Warning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "PyQt5.QtTest.QAbstractItemModelTester.FailureReportingMode.Warning", "name": "Warning", "type": "PyQt5.QtTest.QAbstractItemModelTester.FailureReportingMode"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "PyQt5.QtTest.QAbstractItemModelTester.FailureReportingMode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "PyQt5.QtTest.QAbstractItemModelTester.FailureReportingMode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QAbstractItemModelTester.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "model", "parent"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QAbstractItemModelTester.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "model", "parent"], "arg_types": ["PyQt5.QtTest.QAbstractItemModelTester", {".class": "UnionType", "items": ["PyQt5.QtCore.QAbstractItemModel", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["PyQt5.QtCore.QObject", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of QAbstractItemModelTester", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QAbstractItemModelTester.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "model", "parent"], "arg_types": ["PyQt5.QtTest.QAbstractItemModelTester", {".class": "UnionType", "items": ["PyQt5.QtCore.QAbstractItemModel", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["PyQt5.QtCore.QObject", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of QAbstractItemModelTester", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "model", "mode", "parent"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QAbstractItemModelTester.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "model", "mode", "parent"], "arg_types": ["PyQt5.QtTest.QAbstractItemModelTester", {".class": "UnionType", "items": ["PyQt5.QtCore.QAbstractItemModel", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtTest.QAbstractItemModelTester.FailureReportingMode", {".class": "UnionType", "items": ["PyQt5.QtCore.QObject", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of QAbstractItemModelTester", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QAbstractItemModelTester.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "model", "mode", "parent"], "arg_types": ["PyQt5.QtTest.QAbstractItemModelTester", {".class": "UnionType", "items": ["PyQt5.QtCore.QAbstractItemModel", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtTest.QAbstractItemModelTester.FailureReportingMode", {".class": "UnionType", "items": ["PyQt5.QtCore.QObject", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of QAbstractItemModelTester", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "model", "parent"], "arg_types": ["PyQt5.QtTest.QAbstractItemModelTester", {".class": "UnionType", "items": ["PyQt5.QtCore.QAbstractItemModel", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["PyQt5.QtCore.QObject", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of QAbstractItemModelTester", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "model", "mode", "parent"], "arg_types": ["PyQt5.QtTest.QAbstractItemModelTester", {".class": "UnionType", "items": ["PyQt5.QtCore.QAbstractItemModel", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtTest.QAbstractItemModelTester.FailureReportingMode", {".class": "UnionType", "items": ["PyQt5.QtCore.QObject", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of QAbstractItemModelTester", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "failureReportingMode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QAbstractItemModelTester.failureReportingMode", "name": "failureReportingMode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PyQt5.QtTest.QAbstractItemModelTester"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "failureReportingMode of QAbstractItemModelTester", "ret_type": "PyQt5.QtTest.QAbstractItemModelTester.FailureReportingMode", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QAbstractItemModelTester.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PyQt5.QtTest.QAbstractItemModelTester"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "model of QAbstractItemModelTester", "ret_type": {".class": "UnionType", "items": ["PyQt5.QtCore.QAbstractItemModel", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "PyQt5.QtTest.QAbstractItemModelTester.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "PyQt5.QtTest.QAbstractItemModelTester", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "QSignalSpy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["PyQt5.QtCore.QObject"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "PyQt5.QtTest.QSignalSpy", "name": "QSignalSpy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QSignalSpy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "PyQt5.QtTest", "mro": ["PyQt5.QtTest.QSignalSpy", "PyQt5.QtCore.QObject", "PyQt5.sip.wrapper", "PyQt5.sip.simplewrapper", "builtins.object"], "names": {".class": "SymbolTable", "__delitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QSignalSpy.__delitem__", "name": "__delitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["PyQt5.QtTest.QSignalSpy", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__delitem__ of QSignalSpy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QSignalSpy.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["PyQt5.QtTest.QSignalSpy", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of QSignalSpy", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QSignalSpy.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "signal"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QSignalSpy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "signal"], "arg_types": ["PyQt5.QtTest.QSignalSpy", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of QSignalSpy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QSignalSpy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "signal"], "arg_types": ["PyQt5.QtTest.QSignalSpy", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of QSignalSpy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "signal"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QSignalSpy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "signal"], "arg_types": ["PyQt5.QtTest.QSignalSpy", {".class": "UnionType", "items": ["PyQt5.QtCore.QObject", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.QMetaMethod"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of QSignalSpy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QSignalSpy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "signal"], "arg_types": ["PyQt5.QtTest.QSignalSpy", {".class": "UnionType", "items": ["PyQt5.QtCore.QObject", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.QMetaMethod"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of QSignalSpy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "signal"], "arg_types": ["PyQt5.QtTest.QSignalSpy", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of QSignalSpy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "signal"], "arg_types": ["PyQt5.QtTest.QSignalSpy", {".class": "UnionType", "items": ["PyQt5.QtCore.QObject", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.QMetaMethod"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of QSignalSpy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QSignalSpy.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["PyQt5.QtTest.QSignalSpy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of QSignalSpy", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__setitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QSignalSpy.__setitem__", "name": "__setitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["PyQt5.QtTest.QSignalSpy", "builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setitem__ of QSignalSpy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isValid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QSignalSpy.isValid", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PyQt5.QtTest.QSignalSpy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isValid of QSignalSpy", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "signal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QSignalSpy.signal", "name": "signal", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PyQt5.QtTest.QSignalSpy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "signal of QSignalSpy", "ret_type": "PyQt5.QtCore.QByteArray", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "wait": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QSignalSpy.wait", "name": "wait", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "timeout"], "arg_types": ["PyQt5.QtTest.QSignalSpy", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait of QSignalSpy", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "PyQt5.QtTest.QSignalSpy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "PyQt5.QtTest.QSignalSpy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "QTest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["PyQt5.sip.simplewrapper"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "PyQt5.QtTest.QTest", "name": "QTest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QTest", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "PyQt5.QtTest", "mro": ["PyQt5.QtTest.QTest", "PyQt5.sip.simplewrapper", "builtins.object"], "names": {".class": "SymbolTable", "KeyAction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.int"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "PyQt5.QtTest.QTest.KeyAction", "name": "KeyAction", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QTest.KeyAction", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "PyQt5.QtTest", "mro": ["PyQt5.QtTest.QTest.KeyAction", "builtins.int", "builtins.object"], "names": {".class": "SymbolTable", "Click": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "PyQt5.QtTest.QTest.KeyAction.Click", "name": "Click", "type": "PyQt5.QtTest.QTest.KeyAction"}}, "Press": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "PyQt5.QtTest.QTest.KeyAction.Press", "name": "Press", "type": "PyQt5.QtTest.QTest.KeyAction"}}, "Release": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "PyQt5.QtTest.QTest.KeyAction.Release", "name": "Release", "type": "PyQt5.QtTest.QTest.KeyAction"}}, "Shortcut": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "PyQt5.QtTest.QTest.KeyAction.Shortcut", "name": "Shortcut", "type": "PyQt5.QtTest.QTest.KeyAction"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "PyQt5.QtTest.QTest.KeyAction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "PyQt5.QtTest.QTest.KeyAction", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "QTouchEventSequence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "PyQt5.QtTest.QTest.QTouchEventSequence", "name": "QTouchEventSequence", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "PyQt5.QtTest.QTest.QTouchEventSequence", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "PyQt5.QtTest", "mro": ["PyQt5.QtTest.QTest.QTouchEventSequence", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "a0"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QTest.QTouchEventSequence.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "a0"], "arg_types": ["PyQt5.QtTest.QTest.QTouchEventSequence", "PyQt5.QtTest.QTest.QTouchEventSequence"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of QTouchEventSequence", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "commit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "processEvents"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QTest.QTouchEventSequence.commit", "name": "commit", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "processEvents"], "arg_types": ["PyQt5.QtTest.QTest.QTouchEventSequence", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "commit of QTouchEventSequence", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "move": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QTest.QTouchEventSequence.move", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "touchId", "pt", "window"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.QTouchEventSequence.move", "name": "move", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "touchId", "pt", "window"], "arg_types": ["PyQt5.QtTest.QTest.QTouchEventSequence", "builtins.int", "PyQt5.QtCore.QPoint", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "move of QTouchEventSequence", "ret_type": "PyQt5.QtTest.QTest.QTouchEventSequence", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.QTouchEventSequence.move", "name": "move", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "touchId", "pt", "window"], "arg_types": ["PyQt5.QtTest.QTest.QTouchEventSequence", "builtins.int", "PyQt5.QtCore.QPoint", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "move of QTouchEventSequence", "ret_type": "PyQt5.QtTest.QTest.QTouchEventSequence", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "touchId", "pt", "widget"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.QTouchEventSequence.move", "name": "move", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "touchId", "pt", "widget"], "arg_types": ["PyQt5.QtTest.QTest.QTouchEventSequence", "builtins.int", "PyQt5.QtCore.QPoint", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "move of QTouchEventSequence", "ret_type": "PyQt5.QtTest.QTest.QTouchEventSequence", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.QTouchEventSequence.move", "name": "move", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "touchId", "pt", "widget"], "arg_types": ["PyQt5.QtTest.QTest.QTouchEventSequence", "builtins.int", "PyQt5.QtCore.QPoint", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "move of QTouchEventSequence", "ret_type": "PyQt5.QtTest.QTest.QTouchEventSequence", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "touchId", "pt", "window"], "arg_types": ["PyQt5.QtTest.QTest.QTouchEventSequence", "builtins.int", "PyQt5.QtCore.QPoint", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "move of QTouchEventSequence", "ret_type": "PyQt5.QtTest.QTest.QTouchEventSequence", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "touchId", "pt", "widget"], "arg_types": ["PyQt5.QtTest.QTest.QTouchEventSequence", "builtins.int", "PyQt5.QtCore.QPoint", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "move of QTouchEventSequence", "ret_type": "PyQt5.QtTest.QTest.QTouchEventSequence", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "press": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QTest.QTouchEventSequence.press", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "touchId", "pt", "window"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.QTouchEventSequence.press", "name": "press", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "touchId", "pt", "window"], "arg_types": ["PyQt5.QtTest.QTest.QTouchEventSequence", "builtins.int", "PyQt5.QtCore.QPoint", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "press of QTouchEventSequence", "ret_type": "PyQt5.QtTest.QTest.QTouchEventSequence", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.QTouchEventSequence.press", "name": "press", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "touchId", "pt", "window"], "arg_types": ["PyQt5.QtTest.QTest.QTouchEventSequence", "builtins.int", "PyQt5.QtCore.QPoint", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "press of QTouchEventSequence", "ret_type": "PyQt5.QtTest.QTest.QTouchEventSequence", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "touchId", "pt", "widget"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.QTouchEventSequence.press", "name": "press", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "touchId", "pt", "widget"], "arg_types": ["PyQt5.QtTest.QTest.QTouchEventSequence", "builtins.int", "PyQt5.QtCore.QPoint", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "press of QTouchEventSequence", "ret_type": "PyQt5.QtTest.QTest.QTouchEventSequence", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.QTouchEventSequence.press", "name": "press", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "touchId", "pt", "widget"], "arg_types": ["PyQt5.QtTest.QTest.QTouchEventSequence", "builtins.int", "PyQt5.QtCore.QPoint", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "press of QTouchEventSequence", "ret_type": "PyQt5.QtTest.QTest.QTouchEventSequence", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "touchId", "pt", "window"], "arg_types": ["PyQt5.QtTest.QTest.QTouchEventSequence", "builtins.int", "PyQt5.QtCore.QPoint", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "press of QTouchEventSequence", "ret_type": "PyQt5.QtTest.QTest.QTouchEventSequence", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "touchId", "pt", "widget"], "arg_types": ["PyQt5.QtTest.QTest.QTouchEventSequence", "builtins.int", "PyQt5.QtCore.QPoint", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "press of QTouchEventSequence", "ret_type": "PyQt5.QtTest.QTest.QTouchEventSequence", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "release": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QTest.QTouchEventSequence.release", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "touchId", "pt", "window"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.QTouchEventSequence.release", "name": "release", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "touchId", "pt", "window"], "arg_types": ["PyQt5.QtTest.QTest.QTouchEventSequence", "builtins.int", "PyQt5.QtCore.QPoint", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "release of QTouchEventSequence", "ret_type": "PyQt5.QtTest.QTest.QTouchEventSequence", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.QTouchEventSequence.release", "name": "release", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "touchId", "pt", "window"], "arg_types": ["PyQt5.QtTest.QTest.QTouchEventSequence", "builtins.int", "PyQt5.QtCore.QPoint", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "release of QTouchEventSequence", "ret_type": "PyQt5.QtTest.QTest.QTouchEventSequence", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "touchId", "pt", "widget"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.QTouchEventSequence.release", "name": "release", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "touchId", "pt", "widget"], "arg_types": ["PyQt5.QtTest.QTest.QTouchEventSequence", "builtins.int", "PyQt5.QtCore.QPoint", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "release of QTouchEventSequence", "ret_type": "PyQt5.QtTest.QTest.QTouchEventSequence", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.QTouchEventSequence.release", "name": "release", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "touchId", "pt", "widget"], "arg_types": ["PyQt5.QtTest.QTest.QTouchEventSequence", "builtins.int", "PyQt5.QtCore.QPoint", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "release of QTouchEventSequence", "ret_type": "PyQt5.QtTest.QTest.QTouchEventSequence", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "touchId", "pt", "window"], "arg_types": ["PyQt5.QtTest.QTest.QTouchEventSequence", "builtins.int", "PyQt5.QtCore.QPoint", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "release of QTouchEventSequence", "ret_type": "PyQt5.QtTest.QTest.QTouchEventSequence", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "touchId", "pt", "widget"], "arg_types": ["PyQt5.QtTest.QTest.QTouchEventSequence", "builtins.int", "PyQt5.QtCore.QPoint", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "release of QTouchEventSequence", "ret_type": "PyQt5.QtTest.QTest.QTouchEventSequence", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "stationary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "touchId"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QTest.QTouchEventSequence.stationary", "name": "stationary", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "touchId"], "arg_types": ["PyQt5.QtTest.QTest.QTouchEventSequence", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stationary of QTouchEventSequence", "ret_type": "PyQt5.QtTest.QTest.QTouchEventSequence", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "PyQt5.QtTest.QTest.QTouchEventSequence.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "PyQt5.QtTest.QTest.QTouchEventSequence", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "keyClick": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QTest.keyClick", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "widget", "key", "modifier", "delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.keyClick", "name": "keyClick", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "widget", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.Key", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyClick of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.keyClick", "name": "keyClick", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "widget", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.Key", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyClick of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "widget", "key", "modifier", "delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.keyClick", "name": "keyClick", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "widget", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyClick of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.keyClick", "name": "keyClick", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "widget", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyClick of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "window", "key", "modifier", "delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.keyClick", "name": "keyClick", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "window", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.Key", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyClick of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.keyClick", "name": "keyClick", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "window", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.Key", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyClick of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "window", "key", "modifier", "delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.keyClick", "name": "keyClick", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "window", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyClick of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.keyClick", "name": "keyClick", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "window", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyClick of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "widget", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.Key", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyClick of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "widget", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyClick of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "window", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.Key", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyClick of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "window", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyClick of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "keyClicks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "widget", "sequence", "modifier", "delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QTest.keyClicks", "name": "keyClicks", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "widget", "sequence", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyClicks of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "keyEvent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QTest.keyEvent", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "action", "widget", "key", "modifier", "delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.keyEvent", "name": "keyEvent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "action", "widget", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", "PyQt5.QtTest.QTest.KeyAction", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.Key", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyEvent of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.keyEvent", "name": "keyEvent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "action", "widget", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", "PyQt5.QtTest.QTest.KeyAction", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.Key", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyEvent of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "action", "widget", "ascii", "modifier", "delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.keyEvent", "name": "keyEvent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "action", "widget", "ascii", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", "PyQt5.QtTest.QTest.KeyAction", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyEvent of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.keyEvent", "name": "keyEvent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "action", "widget", "ascii", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", "PyQt5.QtTest.QTest.KeyAction", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyEvent of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "action", "window", "key", "modifier", "delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.keyEvent", "name": "keyEvent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "action", "window", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", "PyQt5.QtTest.QTest.KeyAction", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.Key", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyEvent of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.keyEvent", "name": "keyEvent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "action", "window", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", "PyQt5.QtTest.QTest.KeyAction", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.Key", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyEvent of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "action", "window", "ascii", "modifier", "delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.keyEvent", "name": "keyEvent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "action", "window", "ascii", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", "PyQt5.QtTest.QTest.KeyAction", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyEvent of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.keyEvent", "name": "keyEvent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "action", "window", "ascii", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", "PyQt5.QtTest.QTest.KeyAction", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyEvent of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "action", "widget", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", "PyQt5.QtTest.QTest.KeyAction", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.Key", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyEvent of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "action", "widget", "ascii", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", "PyQt5.QtTest.QTest.KeyAction", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyEvent of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "action", "window", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", "PyQt5.QtTest.QTest.KeyAction", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.Key", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyEvent of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "action", "window", "ascii", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", "PyQt5.QtTest.QTest.KeyAction", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyEvent of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "keyPress": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QTest.keyPress", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "widget", "key", "modifier", "delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.keyPress", "name": "keyPress", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "widget", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.Key", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyPress of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.keyPress", "name": "keyPress", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "widget", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.Key", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyPress of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "widget", "key", "modifier", "delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.keyPress", "name": "keyPress", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "widget", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyPress of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.keyPress", "name": "keyPress", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "widget", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyPress of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "window", "key", "modifier", "delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.keyPress", "name": "keyPress", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "window", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.Key", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyPress of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.keyPress", "name": "keyPress", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "window", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.Key", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyPress of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "window", "key", "modifier", "delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.keyPress", "name": "keyPress", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "window", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyPress of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.keyPress", "name": "keyPress", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "window", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyPress of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "widget", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.Key", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyPress of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "widget", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyPress of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "window", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.Key", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyPress of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "window", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyPress of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "keyRelease": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QTest.keyRelease", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "widget", "key", "modifier", "delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.keyRelease", "name": "keyRelease", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "widget", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.Key", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyRelease of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.keyRelease", "name": "keyRelease", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "widget", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.Key", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyRelease of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "widget", "key", "modifier", "delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.keyRelease", "name": "keyRelease", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "widget", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyRelease of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.keyRelease", "name": "keyRelease", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "widget", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyRelease of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "window", "key", "modifier", "delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.keyRelease", "name": "keyRelease", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "window", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.Key", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyRelease of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.keyRelease", "name": "keyRelease", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "window", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.Key", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyRelease of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "window", "key", "modifier", "delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.keyRelease", "name": "keyRelease", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "window", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyRelease of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.keyRelease", "name": "keyRelease", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "window", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyRelease of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "widget", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.Key", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyRelease of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "widget", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyRelease of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "window", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.Key", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyRelease of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "window", "key", "modifier", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keyRelease of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "keySequence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QTest.keySequence", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "widget", "keySequence"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.keySequence", "name": "keySequence", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "widget", "keySequence"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["PyQt5.QtGui.QKeySequence", "PyQt5.QtGui.QKeySequence.StandardKey", "builtins.str", {".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keySequence of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.keySequence", "name": "keySequence", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "widget", "keySequence"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["PyQt5.QtGui.QKeySequence", "PyQt5.QtGui.QKeySequence.StandardKey", "builtins.str", {".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keySequence of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "window", "keySequence"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.keySequence", "name": "keySequence", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "window", "keySequence"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["PyQt5.QtGui.QKeySequence", "PyQt5.QtGui.QKeySequence.StandardKey", "builtins.str", {".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keySequence of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.keySequence", "name": "keySequence", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "window", "keySequence"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["PyQt5.QtGui.QKeySequence", "PyQt5.QtGui.QKeySequence.StandardKey", "builtins.str", {".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keySequence of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "widget", "keySequence"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["PyQt5.QtGui.QKeySequence", "PyQt5.QtGui.QKeySequence.StandardKey", "builtins.str", {".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keySequence of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "window", "keySequence"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["PyQt5.QtGui.QKeySequence", "PyQt5.QtGui.QKeySequence.StandardKey", "builtins.str", {".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keySequence of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "mouseClick": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QTest.mouseClick", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "widget", "button", "modifier", "pos", "delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.mouseClick", "name": "mouseClick", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "widget", "button", "modifier", "pos", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.MouseButton", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "PyQt5.QtCore.QPoint", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mouseClick of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.mouseClick", "name": "mouseClick", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "widget", "button", "modifier", "pos", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.MouseButton", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "PyQt5.QtCore.QPoint", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mouseClick of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "window", "button", "modifier", "pos", "delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.mouseClick", "name": "mouseClick", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "window", "button", "modifier", "pos", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.MouseButton", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "PyQt5.QtCore.QPoint", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mouseClick of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.mouseClick", "name": "mouseClick", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "window", "button", "modifier", "pos", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.MouseButton", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "PyQt5.QtCore.QPoint", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mouseClick of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "widget", "button", "modifier", "pos", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.MouseButton", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "PyQt5.QtCore.QPoint", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mouseClick of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "window", "button", "modifier", "pos", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.MouseButton", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "PyQt5.QtCore.QPoint", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mouseClick of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "mouseDClick": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QTest.mouseDClick", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "widget", "button", "modifier", "pos", "delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.mouseDClick", "name": "mouseDClick", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "widget", "button", "modifier", "pos", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.MouseButton", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "PyQt5.QtCore.QPoint", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mouseDClick of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.mouseDClick", "name": "mouseDClick", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "widget", "button", "modifier", "pos", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.MouseButton", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "PyQt5.QtCore.QPoint", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mouseDClick of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "window", "button", "modifier", "pos", "delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.mouseDClick", "name": "mouseDClick", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "window", "button", "modifier", "pos", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.MouseButton", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "PyQt5.QtCore.QPoint", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mouseDClick of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.mouseDClick", "name": "mouseDClick", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "window", "button", "modifier", "pos", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.MouseButton", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "PyQt5.QtCore.QPoint", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mouseDClick of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "widget", "button", "modifier", "pos", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.MouseButton", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "PyQt5.QtCore.QPoint", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mouseDClick of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "window", "button", "modifier", "pos", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.MouseButton", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "PyQt5.QtCore.QPoint", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mouseDClick of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "mouseMove": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QTest.mouseMove", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "widget", "pos", "delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.mouseMove", "name": "mouseMove", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "widget", "pos", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.QPoint", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mouseMove of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.mouseMove", "name": "mouseMove", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "widget", "pos", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.QPoint", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mouseMove of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "window", "pos", "delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.mouseMove", "name": "mouseMove", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "window", "pos", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.QPoint", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mouseMove of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.mouseMove", "name": "mouseMove", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "window", "pos", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.QPoint", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mouseMove of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "widget", "pos", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.QPoint", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mouseMove of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "window", "pos", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.QPoint", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mouseMove of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "mousePress": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QTest.mousePress", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "widget", "button", "modifier", "pos", "delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.mousePress", "name": "mousePress", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "widget", "button", "modifier", "pos", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.MouseButton", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "PyQt5.QtCore.QPoint", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mousePress of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.mousePress", "name": "mousePress", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "widget", "button", "modifier", "pos", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.MouseButton", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "PyQt5.QtCore.QPoint", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mousePress of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "window", "button", "modifier", "pos", "delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.mousePress", "name": "mousePress", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "window", "button", "modifier", "pos", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.MouseButton", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "PyQt5.QtCore.QPoint", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mousePress of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.mousePress", "name": "mousePress", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "window", "button", "modifier", "pos", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.MouseButton", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "PyQt5.QtCore.QPoint", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mousePress of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "widget", "button", "modifier", "pos", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.MouseButton", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "PyQt5.QtCore.QPoint", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mousePress of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "window", "button", "modifier", "pos", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.MouseButton", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "PyQt5.QtCore.QPoint", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mousePress of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "mouseRelease": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QTest.mouseRelease", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "widget", "button", "modifier", "pos", "delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.mouseRelease", "name": "mouseRelease", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "widget", "button", "modifier", "pos", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.MouseButton", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "PyQt5.QtCore.QPoint", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mouseRelease of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.mouseRelease", "name": "mouseRelease", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "widget", "button", "modifier", "pos", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.MouseButton", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "PyQt5.QtCore.QPoint", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mouseRelease of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "window", "button", "modifier", "pos", "delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.mouseRelease", "name": "mouseRelease", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "window", "button", "modifier", "pos", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.MouseButton", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "PyQt5.QtCore.QPoint", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mouseRelease of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.mouseRelease", "name": "mouseRelease", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "window", "button", "modifier", "pos", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.MouseButton", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "PyQt5.QtCore.QPoint", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mouseRelease of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "widget", "button", "modifier", "pos", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.MouseButton", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "PyQt5.QtCore.QPoint", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mouseRelease of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "window", "button", "modifier", "pos", "delay"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "PyQt5.QtCore.Qt.MouseButton", {".class": "UnionType", "items": ["PyQt5.QtCore.Qt.KeyboardModifiers", "PyQt5.QtCore.Qt.KeyboardModifier"], "uses_pep604_syntax": false}, "PyQt5.QtCore.QPoint", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mouseRelease of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "qSleep": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ms"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QTest.qSleep", "name": "qSleep", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ms"], "arg_types": ["PyQt5.QtTest.QTest", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "qSleep of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "qWait": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ms"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QTest.qWait", "name": "qWait", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ms"], "arg_types": ["PyQt5.QtTest.QTest", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "qWait of QTest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "qWaitForWindowActive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QTest.qWaitForWindowActive", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "window", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.qWaitForWindowActive", "name": "qWaitForWindowActive", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "window", "timeout"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "qWaitForWindowActive of QTest", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.qWaitForWindowActive", "name": "qWaitForWindowActive", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "window", "timeout"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "qWaitForWindowActive of QTest", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "widget", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.qWaitForWindowActive", "name": "qWaitForWindowActive", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "widget", "timeout"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "qWaitForWindowActive of QTest", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.qWaitForWindowActive", "name": "qWaitForWindowActive", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "widget", "timeout"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "qWaitForWindowActive of QTest", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "window", "timeout"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "qWaitForWindowActive of QTest", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "widget", "timeout"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "qWaitForWindowActive of QTest", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "qWaitForWindowExposed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QTest.qWaitForWindowExposed", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "window", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.qWaitForWindowExposed", "name": "qWaitForWindowExposed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "window", "timeout"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "qWaitForWindowExposed of QTest", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.qWaitForWindowExposed", "name": "qWaitForWindowExposed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "window", "timeout"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "qWaitForWindowExposed of QTest", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "widget", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.qWaitForWindowExposed", "name": "qWaitForWindowExposed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "widget", "timeout"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "qWaitForWindowExposed of QTest", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.qWaitForWindowExposed", "name": "qWaitForWindowExposed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "widget", "timeout"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "qWaitForWindowExposed of QTest", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "window", "timeout"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "qWaitForWindowExposed of QTest", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "widget", "timeout"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "qWaitForWindowExposed of QTest", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "touchEvent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "PyQt5.QtTest.QTest.touchEvent", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "widget", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.touchEvent", "name": "touchEvent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "widget", "device"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["PyQt5.QtGui.QTouchDevice", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "touchEvent of QTest", "ret_type": "PyQt5.QtTest.QTest.QTouchEventSequence", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.touchEvent", "name": "touchEvent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "widget", "device"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["PyQt5.QtGui.QTouchDevice", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "touchEvent of QTest", "ret_type": "PyQt5.QtTest.QTest.QTouchEventSequence", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "window", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "PyQt5.QtTest.QTest.touchEvent", "name": "touchEvent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "window", "device"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["PyQt5.QtGui.QTouchDevice", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "touchEvent of QTest", "ret_type": "PyQt5.QtTest.QTest.QTouchEventSequence", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "PyQt5.QtTest.QTest.touchEvent", "name": "touchEvent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "window", "device"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["PyQt5.QtGui.QTouchDevice", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "touchEvent of QTest", "ret_type": "PyQt5.QtTest.QTest.QTouchEventSequence", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "widget", "device"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtWidgets.QWidget", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["PyQt5.QtGui.QTouchDevice", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "touchEvent of QTest", "ret_type": "PyQt5.QtTest.QTest.QTouchEventSequence", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "window", "device"], "arg_types": ["PyQt5.QtTest.QTest", {".class": "UnionType", "items": ["PyQt5.QtGui.QWindow", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["PyQt5.QtGui.QTouchDevice", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "touchEvent of QTest", "ret_type": "PyQt5.QtTest.QTest.QTouchEventSequence", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "PyQt5.QtTest.QTest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "PyQt5.QtTest.QTest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "QtCore": {".class": "SymbolTableNode", "cross_ref": "PyQt5.QtCore", "kind": "Gdef", "module_hidden": true, "module_public": false}, "QtGui": {".class": "SymbolTableNode", "cross_ref": "PyQt5.QtGui", "kind": "Gdef", "module_hidden": true, "module_public": false}, "QtWidgets": {".class": "SymbolTableNode", "cross_ref": "PyQt5.QtWidgets", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PyQt5.QtTest.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PyQt5.QtTest.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PyQt5.QtTest.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PyQt5.QtTest.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PyQt5.QtTest.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PyQt5.QtTest.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\QtTest.pyi"}