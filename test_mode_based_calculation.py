#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试基于模式的PLL计算系统
验证ModernSetModesHandler和ModernPLLHandler的协作
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from ui.handlers.ModernPLLHandler import ModernPLLHandler
from ui.handlers.ModernSetModesHandler import ModernSetModesHandler
from core.RegisterManager import RegisterManager
from utils.Log import get_module_logger

logger = get_module_logger(__name__)

class TestModeBasedCalculation:
    """测试基于模式的计算系统"""
    
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.register_manager = None
        self.pll_handler = None
        self.modes_handler = None
        
    def setup(self):
        """设置测试环境"""
        try:
            # 创建RegisterManager实例
            self.register_manager = RegisterManager()
            
            # 创建ModernSetModesHandler实例
            self.modes_handler = ModernSetModesHandler(register_manager=self.register_manager)
            
            # 创建ModernPLLHandler实例
            self.pll_handler = ModernPLLHandler(register_manager=self.register_manager)
            
            # 连接信号
            self.modes_handler.mode_changed.connect(self.pll_handler.on_mode_changed_from_handler)
            
            # 设置测试频率
            if hasattr(self.pll_handler.ui, "FreFin"):
                self.pll_handler.ui.FreFin.setText("122.88")
            if hasattr(self.pll_handler.ui, "OSCinFreq"):
                self.pll_handler.ui.OSCinFreq.setText("122.88")
            
            logger.info("测试环境设置完成")
            return True
            
        except Exception as e:
            logger.error(f"设置测试环境时出错: {str(e)}")
            return False
    
    def test_mode_workflow(self):
        """测试完整的模式工作流程"""
        print("\n" + "="*60)
        print("测试基于模式的PLL计算工作流程")
        print("="*60)
        
        # 测试不同模式
        test_modes = [
            "DualLoop",
            "SingleLoop", 
            "SingleLoop0Dealy",
            "DualLoop0DealyCascaded",
            "DualLoop0DealyNested",
            "DualLoop0DealyNestedandCasc",
            "DistributionFin1"
        ]
        
        for mode_name in test_modes:
            print(f"\n--- 测试模式: {mode_name} ---")
            
            try:
                # 步骤1: 通过ModernSetModesHandler设置模式
                print(f"1. 设置模式: {mode_name}")
                self.modes_handler.set_mode(mode_name)
                
                # 步骤2: 验证寄存器设置
                print("2. 验证寄存器设置")
                self._verify_mode_registers(mode_name)
                
                # 步骤3: 执行频率计算
                print("3. 执行频率计算")
                self.pll_handler.calculate_output_frequencies()
                
                # 步骤4: 显示计算结果
                print("4. 计算结果:")
                self._display_calculation_results()
                
                print(f"✓ 模式 {mode_name} 测试完成")
                
            except Exception as e:
                print(f"✗ 模式 {mode_name} 测试失败: {str(e)}")
                import traceback
                traceback.print_exc()
    
    def _verify_mode_registers(self, mode_name):
        """验证模式寄存器设置"""
        try:
            # 获取模式配置
            mode_settings = self.modes_handler._get_mode_settings(mode_name)
            if not mode_settings:
                print(f"   警告: 未找到模式 {mode_name} 的配置")
                return
            
            # 验证关键寄存器位
            verification_passed = True
            for reg_addr, bit_settings in mode_settings.items():
                for bit_name, expected_value in bit_settings.items():
                    try:
                        actual_value = self.register_manager.get_bit_field_value(reg_addr, bit_name)
                        if actual_value == expected_value:
                            print(f"   ✓ {reg_addr}.{bit_name} = {actual_value} (期望: {expected_value})")
                        else:
                            print(f"   ✗ {reg_addr}.{bit_name} = {actual_value} (期望: {expected_value})")
                            verification_passed = False
                    except Exception as e:
                        print(f"   ✗ 无法读取 {reg_addr}.{bit_name}: {str(e)}")
                        verification_passed = False
            
            if verification_passed:
                print("   寄存器验证通过")
            else:
                print("   寄存器验证失败")
                
        except Exception as e:
            print(f"   验证寄存器时出错: {str(e)}")
    
    def _display_calculation_results(self):
        """显示计算结果"""
        try:
            results = {}
            
            # 收集计算结果
            if hasattr(self.pll_handler.ui, "PLL1PFDFreq"):
                results["PLL1PFDFreq"] = self.pll_handler.ui.PLL1PFDFreq.text()
            if hasattr(self.pll_handler.ui, "PLL2PFDFreq"):
                results["PLL2PFDFreq"] = self.pll_handler.ui.PLL2PFDFreq.text()
            if hasattr(self.pll_handler.ui, "VCODistFreq"):
                results["VCODistFreq"] = self.pll_handler.ui.VCODistFreq.text()
            if hasattr(self.pll_handler.ui, "Fin0Freq"):
                results["Fin0Freq"] = self.pll_handler.ui.Fin0Freq.text()
            
            # 显示结果
            for freq_name, freq_value in results.items():
                print(f"   {freq_name}: {freq_value} MHz")
                
        except Exception as e:
            print(f"   显示计算结果时出错: {str(e)}")
    
    def test_mode_detection(self):
        """测试模式检测功能"""
        print("\n--- 测试模式检测功能 ---")
        
        test_modes = ["DualLoop", "SingleLoop", "DistributionFin1"]
        
        for mode_name in test_modes:
            try:
                # 设置模式
                self.modes_handler.set_mode(mode_name)
                
                # 清除PLL处理器的模式缓存
                self.pll_handler._current_mode_cache = None
                
                # 检测模式
                detected_mode = self.pll_handler._get_current_mode()
                
                if detected_mode == mode_name:
                    print(f"✓ 模式检测正确: {mode_name}")
                else:
                    print(f"✗ 模式检测错误: 期望 {mode_name}, 检测到 {detected_mode}")
                    
            except Exception as e:
                print(f"✗ 测试模式 {mode_name} 检测时出错: {str(e)}")
    
    def run_tests(self):
        """运行所有测试"""
        if not self.setup():
            print("测试环境设置失败，退出测试")
            return
        
        try:
            # 测试模式检测
            self.test_mode_detection()
            
            # 测试完整工作流程
            self.test_mode_workflow()
            
            print("\n" + "="*60)
            print("所有测试完成")
            print("="*60)
            
        except Exception as e:
            logger.error(f"运行测试时出错: {str(e)}")
            import traceback
            traceback.print_exc()
        
        finally:
            # 清理资源
            self.cleanup()
    
    def cleanup(self):
        """清理测试资源"""
        try:
            if self.pll_handler:
                self.pll_handler.close()
            if self.modes_handler:
                self.modes_handler.close()
            self.app.quit()
        except Exception as e:
            logger.error(f"清理资源时出错: {str(e)}")

def print_design_summary():
    """打印设计总结"""
    print("="*60)
    print("基于模式的PLL计算系统设计总结")
    print("="*60)
    print("""
系统架构：

1. ModernSetModesHandler (模式设置处理器)
   - 负责设置不同PLL工作模式的寄存器配置
   - 支持7种模式：DualLoop, SingleLoop, SingleLoop0Dealy等
   - 发送mode_changed信号通知其他组件

2. ModernPLLHandler (PLL计算处理器)  
   - 接收模式变化信号，根据模式调整计算策略
   - 支持模式检测和缓存机制
   - 根据不同模式选择相应的计算方法

工作流程：
1. 用户通过ModernSetModesHandler设置模式
2. ModernSetModesHandler更新相关寄存器
3. 发送mode_changed信号给ModernPLLHandler
4. ModernPLLHandler根据新模式重新计算频率
5. 显示更新后的频率结果

优势：
- 模式和计算逻辑分离，便于维护
- 支持自动模式检测和手动模式设置
- 计算策略可根据模式动态调整
- 保持与现有代码的兼容性
""")
    print("="*60)

if __name__ == "__main__":
    print_design_summary()
    
    # 运行测试
    test_system = TestModeBasedCalculation()
    test_system.run_tests()
