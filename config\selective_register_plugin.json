{"plugin_info": {"name": "选择性寄存器操作", "version": "1.0.1", "description": "允许用户选择特定的寄存器进行批量读写操作，支持分组选择和模板管理"}, "ui_config": {"window": {"title": "🔧 高级寄存器选择工具", "width": 1200, "height": 800, "min_width": 800, "min_height": 600}, "texts": {"buttons": {"select_all": "🔘 全部选择", "select_none": "⭕ 清除选择", "select_group": "📁 按组选择", "load_template": "📥 加载模板", "save_template": "💾 保存模板", "delete_template": "🗑️ 删除模板", "read_selected": "📖 批量读取", "write_selected": "✍️ 批量写入", "cancel_operation": "❌ 取消操作", "clear_log": "🧹 清空日志", "export_log": "📤 导出日志", "close": "🚪 关闭窗口"}, "labels": {"quick_select": "⚡ 快速操作:", "search": "🔍 搜索:", "search_placeholder": "输入关键字搜索寄存器...", "selection_count": "✅ 已选择: {count} 个寄存器", "template_select": "📋 选择模板:", "status_ready": "🟢 系统就绪", "status_reading": "📖 正在读取寄存器...", "status_writing": "✍️ 正在写入寄存器..."}, "tabs": {"register_selection": "寄存器选择", "template_management": "模板管理", "operation_monitor": "操作监控"}, "groups": {"preset_templates": "预设模板", "template_content": "模板内容", "operation_status": "操作状态", "operation_log": "操作日志"}, "table_headers": {"register_tree": ["寄存器", "地址", "当前值", "描述"], "template_table": ["地址", "寄存器名", "描述"]}, "messages": {"no_registers_selected": "请先选择要{operation}的寄存器", "spi_service_not_found": "SPI服务未找到", "register_manager_not_found": "SPI服务或寄存器管理器未找到", "confirm_read": "确定要读取选中的 {count} 个寄存器吗？", "confirm_write": "确定要写入选中的 {count} 个寄存器吗？\\n此操作将会覆盖设备中对应寄存器的值。", "confirm_close": "当前有操作正在进行，确定要关闭窗口吗？", "operation_cancelled": "用户取消操作", "all_selected": "已全选所有寄存器", "none_selected": "已取消选择所有寄存器", "template_loaded": "已加载模板 '{template_name}'，选择了 {count} 个寄存器", "read_start": "开始读取 {count} 个寄存器...", "write_start": "开始写入 {count} 个寄存器...", "read_complete": "读取完成: {addr} = 0x{value:04X}", "write_complete": "写入完成: {addr} = 0x{value:04X}", "all_read_complete": "所有寄存器读取完成", "all_write_complete": "所有寄存器写入完成", "plugin_initialized": "插件初始化完成", "registers_loaded": "已加载 {count} 个寄存器", "register_manager_not_found_warning": "警告: 寄存器管理器未找到"}}}, "register_groups": {"group_definitions": [{"name": "设备信息", "description": "设备版本和基础信息", "rules": [{"type": "exact", "addresses": ["0x00"]}]}, {"name": "电源管理", "description": "电源控制相关寄存器", "rules": [{"type": "exact", "addresses": ["0x02", "0x50", "0x83"]}]}, {"name": "时钟输入", "description": "时钟输入配置寄存器", "rules": [{"type": "range", "start": "0x55", "end": "0x60"}]}, {"name": "PLL控制", "description": "PLL相关控制寄存器", "rules": [{"type": "exact", "addresses": ["0x63", "0x65", "0x67", "0x69", "0x6B", "0x6C", "0x70", "0x72", "0x76", "0x77", "0x79", "0x7A"]}]}, {"name": "时钟输出0-1", "description": "时钟输出通道0和1", "rules": [{"type": "range", "start": "0x10", "end": "0x17"}]}, {"name": "时钟输出2-3", "description": "时钟输出通道2和3", "rules": [{"type": "range", "start": "0x18", "end": "0x1F"}]}, {"name": "时钟输出4-5", "description": "时钟输出通道4和5", "rules": [{"type": "range", "start": "0x20", "end": "0x27"}]}, {"name": "时钟输出6-7", "description": "时钟输出通道6和7", "rules": [{"type": "range", "start": "0x28", "end": "0x2F"}]}, {"name": "时钟输出8-9", "description": "时钟输出通道8和9", "rules": [{"type": "range", "start": "0x30", "end": "0x37"}]}, {"name": "时钟输出10-11", "description": "时钟输出通道10和11", "rules": [{"type": "range", "start": "0x38", "end": "0x3F"}]}, {"name": "时钟输出12-13", "description": "时钟输出通道12和13", "rules": [{"type": "range", "start": "0x40", "end": "0x47"}]}, {"name": "SYSREF控制", "description": "SYSREF相关控制寄存器", "rules": [{"type": "exact", "addresses": ["0x49", "0x4A", "0x4C", "0x4E"]}]}, {"name": "同步控制", "description": "同步功能寄存器", "rules": [{"type": "exact", "addresses": ["0x53", "0x54", "0x55"]}]}, {"name": "调试专用", "description": "用于调试和测试的关键寄存器", "rules": [{"type": "exact", "addresses": ["0x00", "0x02", "0x50", "0x83"]}]}, {"name": "其他", "description": "未分类的寄存器", "rules": []}]}, "templates": {"preset_templates": [{"name": "PLL控制寄存器", "description": "PLL核心配置寄存器", "addresses": ["0x50", "0x63", "0x65", "0x67", "0x69", "0x6B", "0x6C"], "category": "PLL"}, {"name": "时钟输出0-1", "description": "时钟输出通道0和1的控制寄存器", "addresses": ["0x10", "0x11", "0x12", "0x13", "0x14", "0x15", "0x16", "0x17"], "category": "时钟输出"}, {"name": "时钟输出2-3", "description": "时钟输出通道2和3的控制寄存器", "addresses": ["0x18", "0x19", "0x1A", "0x1B", "0x1C", "0x1D", "0x1E", "0x1F"], "category": "时钟输出"}, {"name": "时钟输出4-5", "description": "时钟输出通道4和5的控制寄存器", "addresses": ["0x20", "0x21", "0x22", "0x23", "0x24", "0x25", "0x26", "0x27"], "category": "时钟输出"}, {"name": "SYSREF控制", "description": "SYSREF相关的控制寄存器", "addresses": ["0x49", "0x4A", "0x4C", "0x4E", "0x53", "0x54", "0x55"], "category": "SYSREF"}, {"name": "电源管理", "description": "电源控制相关寄存器", "addresses": ["0x02", "0x50", "0x83"], "category": "电源"}, {"name": "快速调试", "description": "常用的调试寄存器组合", "addresses": ["0x00", "0x02", "0x10", "0x50", "0x63"], "category": "调试"}, {"name": "完整PLL配置", "description": "PLL完整配置所需的所有寄存器", "addresses": ["0x50", "0x63", "0x65", "0x67", "0x69", "0x6B", "0x6C", "0x70", "0x72"], "category": "PLL"}]}, "operation_config": {"confirmation_required": {"read": false, "write": true, "close_during_operation": true}, "auto_switch_to_monitor": true, "log_timestamp_format": "[%Y-%m-%d %H:%M:%S]", "progress_update_interval": 50}}