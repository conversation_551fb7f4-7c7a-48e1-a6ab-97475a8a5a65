#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
现代化的寄存器IO处理器
使用ModernBaseHandler作为基类，重构自原RegisterIOHandler
主要功能：寄存器输入/输出控制、搜索、批量操作
"""

import re
from PyQt5.QtWidgets import (QHBoxLayout, QVBoxLayout, QLabel, QLineEdit,
                            QPushButton, QComboBox, QWidget, QMessageBox,
                            QListWidget, QListWidgetItem, QCompleter)
from PyQt5.QtCore import Qt, pyqtSignal, QSettings, QStringListModel
from ui.handlers.ModernBaseHandler import ModernBaseHandler
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class ModernRegisterIOHandler(ModernBaseHandler):
    """现代化的寄存器IO处理器"""
    
    # 定义信号（保持与原版本兼容）
    read_requested = pyqtSignal(int)  # 请求读取寄存器
    write_requested = pyqtSignal(int, int)  # 请求写入寄存器(地址, 值)
    read_all_requested = pyqtSignal()  # 请求读取所有寄存器
    write_all_requested = pyqtSignal()  # 请求写入所有寄存器
    save_requested = pyqtSignal()  # 请求保存配置
    load_requested = pyqtSignal()  # 请求加载配置
    value_changed = pyqtSignal(str, int)  # 寄存器值已更改(地址, 新值)
    search_requested = pyqtSignal(str)  # 请求搜索位段
    bit_field_selected = pyqtSignal(str)  # 位段被选中 (传递寄存器地址)

    def __init__(self, parent=None, register_manager=None, spi_service=None, **kwargs):
        """初始化现代化寄存器IO处理器

        Args:
            parent: 父窗口
            register_manager: RegisterManager实例
            spi_service: SPI服务实例
            **kwargs: 其他参数（用于向后兼容）
        """
        super().__init__(parent, register_manager, **kwargs)

        # 直接使用传入的SPI服务
        self.spi_service = spi_service

        # 设置窗口标题
        self.setWindowTitle("寄存器IO控制 (现代化版本)")

        # 初始化设置
        self.settings = QSettings("FSJ", "FSJRead")

        # 添加标志位防止程序化更新时触发写操作
        self._updating_display = False

        # 初始化UI组件引用
        self._init_ui_references()

        # 创建UI
        self._create_io_ui()

        # 连接SPI服务的端口刷新信号（UI创建后）
        if self.spi_service:
            self.spi_service.ports_refreshed.connect(self._update_port_combo)
            # 不再自动刷新端口，因为SPI服务在初始化时已经处理了端口扫描和连接
            # 设置标志，让InitializationManager知道端口刷新已经处理
            if hasattr(self.parent, '_port_refreshed'):
                self.parent._port_refreshed = True

        # 手动调用初始化（因为测试环境没有事件循环）
        self._post_init()

        # 确保处理器本身是可见的
        self.show()

        logger.info("现代化寄存器IO处理器初始化完成")

    def _initial_port_refresh(self):
        """初始端口刷新，设置标志避免重复刷新"""
        if hasattr(self.parent, '_port_refreshed'):
            self.parent._port_refreshed = True
        logger.info("ModernRegisterIOHandler: 执行初始端口刷新")
        if self.spi_service:
            self.spi_service.refresh_available_ports()

    def _init_ui_references(self):
        """初始化UI组件引用"""
        # 主要控件引用
        self.addr_line_edit = None
        self.value_line_edit = None
        self.value_label = None
        self.port_combo = None

        # 按钮引用已移除，不再需要这些按钮

        # 搜索相关
        self.search_edit = None
        self.search_btn = None
        self.search_results_list = None
        self.completer = None  # 自动补全器

    def _create_io_ui(self):
        """创建IO控制UI"""
        # 创建主布局（紧凑布局，移除多余边距）
        main_layout = QVBoxLayout(self.content_widget)

        # 创建IO控制区域（已包含搜索功能）
        io_widget = self._create_io_control_widget()
        main_layout.addWidget(io_widget)

        # 搜索功能已集成到IO控制区域中，不需要单独创建

        # 设置紧凑布局间距，与表格更好地融合
        main_layout.setSpacing(0)  # 移除间距
        main_layout.setContentsMargins(0, 0, 0, 0)  # 移除所有边距

    def _create_io_control_widget(self):
        """创建IO控制控件区域"""
        # 创建容器（移除边框，让它与表格更好地融合）
        io_widget = QWidget()
        # 不设置边框样式，让整体看起来更统一

        # 设置固定高度，防止IO控制区域占用过多空间
        io_widget.setFixedHeight(60)  # 固定高度为60像素

        # 设置大小策略
        from PyQt5.QtWidgets import QSizePolicy
        io_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        # 创建布局（紧凑布局，移除多余边距）
        layout = QVBoxLayout(io_widget)
        layout.setContentsMargins(2, 2, 2, 2)  # 极小边距
        layout.setSpacing(2)  # 极小间距

        # 第一行：按原来的顺序排列控件
        input_layout = QHBoxLayout()

        # 添加左侧间隔，让整体右移
        input_layout.addSpacing(20)  # 添加20像素的左侧间隔

        # COM端口选择部分（放在最前面，和原来一样）
        port_label = QLabel("COM端口:")
        self.port_combo = QComboBox()
        self.port_combo.setMinimumWidth(300)  # 适中的最小宽度，能显示完整端口信息但不过宽
        self.port_combo.setMaximumWidth(400)  # 适中的最大宽度，不会覆盖搜索框
        # 设置下拉框的大小策略为固定，避免过度扩展
        from PyQt5.QtWidgets import QSizePolicy
        self.port_combo.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)

        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.setMaximumWidth(60)
        self.refresh_btn.clicked.connect(self.refresh_ports)

        input_layout.addWidget(port_label)
        input_layout.addWidget(self.port_combo)
        input_layout.addWidget(self.refresh_btn)

        # 在刷新按钮和Address标签之间添加更大的间隔
        input_layout.addSpacing(25)  # 增加间隔，从15改为25像素

        # 地址和值部分 - 创建一个居中的子布局
        addr_value_layout = QHBoxLayout()

        # 地址部分
        addr_label = QLabel("Address:")
        # addr_label.setStyleSheet("font-weight: bold; color: #333;")  # 加粗标签
        self.addr_line_edit = QLineEdit()
        self.addr_line_edit.setPlaceholderText("0x00")
        self.addr_line_edit.setReadOnly(True)
        self.addr_line_edit.setAlignment(Qt.AlignCenter)
        self.addr_line_edit.setFixedWidth(140)  # 进一步增加宽度
        # self.addr_line_edit.setStyleSheet("""
        #     QLineEdit {
        #         border: 2px solid #ccc;
        #         border-radius: 5px;
        #         padding: 5px;
        #         font-size: 12px;
        #         font-weight: bold;
        #         background-color: #f9f9f9;
        #     }
        # """)

        addr_value_layout.addWidget(addr_label)
        addr_value_layout.addWidget(self.addr_line_edit)
        addr_value_layout.addSpacing(20)  # 地址和值之间的间隔

        # 值部分
        self.value_label = QLabel("R0 Value:")
        # self.value_label.setStyleSheet("font-weight: bold; color: #333;")  # 加粗标签
        self.value_line_edit = QLineEdit()
        self.value_line_edit.setPlaceholderText("0x0000")
        self.value_line_edit.setFixedWidth(150)  # 进一步增加宽度，从180改为200
        self.value_line_edit.setAlignment(Qt.AlignCenter)
        self.value_line_edit.setReadOnly(False)
        # self.value_line_edit.setStyleSheet("""
        #     QLineEdit {
        #         border: 2px solid #4CAF50;
        #         border-radius: 5px;
        #         padding: 5px;
        #         font-size: 12px;
        #         font-weight: bold;
        #         background-color: white;
        #     }
        #     QLineEdit:focus {
        #         border-color: #45a049;
        #         background-color: #f0fff0;
        #     }
        # """)

        addr_value_layout.addWidget(self.value_label)
        addr_value_layout.addWidget(self.value_line_edit)

        # 将地址值布局添加到主布局
        input_layout.addLayout(addr_value_layout)

        # 添加适量的弹性空间，让控件更居中
        input_layout.addStretch(3)  # 增加弹性空间，让右侧有更多空间

        # 搜索控件（放在最后，和原来一样）
        search_label = QLabel("搜索位段:")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入位段名称关键字")
        self.search_edit.setMinimumWidth(500)  # 调整最小宽度，从400改为300
        self.search_edit.setMaximumWidth(800)  # 增加最大宽度，支持更长的位段名称

        # 添加自动补全功能（参考重构前的实现）
        self.completer = QCompleter()
        self.completer.setCaseSensitivity(Qt.CaseInsensitive)  # 不区分大小写
        self.completer.setFilterMode(Qt.MatchContains)  # 包含匹配而不是前缀匹配
        self.completer.setCompletionMode(QCompleter.PopupCompletion)  # 设置为弹出窗口模式
        self.search_edit.setCompleter(self.completer)



        self.search_btn = QPushButton("搜索")

        input_layout.addWidget(search_label)
        input_layout.addWidget(self.search_edit)
        input_layout.addWidget(self.search_btn)

        # 移除操作按钮，只保留输入控件
        layout.addLayout(input_layout)

        # 添加搜索结果列表（参考重构前的实现）
        self.search_results_list = QListWidget()
        self.search_results_list.setVisible(False)  # 初始隐藏
        self.search_results_list.setMaximumHeight(150)  # 限制最大高度，与重构前一致

        # 设置为弹出窗口样式（关键！与重构前一致）
        self.search_results_list.setWindowFlags(Qt.Popup)

        # 安装事件过滤器以处理键盘事件和焦点事件（与重构前一致）
        self.search_edit.installEventFilter(self)
        self.search_results_list.installEventFilter(self)

        # 注意：不添加到布局中，作为独立的弹出窗口

        # 连接信号
        self._connect_io_signals()
        self._connect_search_signals()

        return io_widget

    # _create_search_widget 方法已移除，搜索功能已集成到IO控制中

    def _connect_io_signals(self):
        """连接IO控制信号"""
        # 只连接输入框信号，按钮已移除
        self.value_line_edit.returnPressed.connect(lambda: self.on_value_changed(self.value_line_edit.text()))
        # 移除按钮相关的信号连接，因为按钮已被移除

    def _connect_search_signals(self):
        """连接搜索相关信号（参考重构前的实现）"""
        # 搜索按钮和回车键
        self.search_btn.clicked.connect(self._handle_search_click)
        self.search_edit.returnPressed.connect(self._handle_search_click)

        # 文本变化事件（用于实时搜索和防抖）
        self.search_edit.textChanged.connect(self._handle_search_click)

        # 自动补全激活事件
        self.completer.activated.connect(self._handle_completer_activated)

        # 搜索结果选择事件
        self.search_results_list.itemClicked.connect(self._handle_result_selection)

    # === 业务逻辑方法 ===
    
    # 按钮点击事件处理方法已移除，因为按钮已被移除

    def get_current_value(self):
        """获取当前输入的值"""
        text = self.value_line_edit.text()
        if not text:
            return None
        try:
            if text.lower().startswith("0x"):
                return int(text, 16)
            else:
                return int(text, 16)
        except ValueError:
            return None

    def on_value_changed(self, text):
        """处理值文本变化事件"""
        # 如果正在程序化更新显示，不触发写操作
        if self._updating_display:
            logger.debug("现代化IOHandler: 程序化更新显示中，跳过写入操作")
            return

        # 检查文本是否有效
        if not text.strip():
            return

        # 验证输入格式
        if not self.validate_hex_input(text):
            # 如果验证失败，恢复旧值或清空
            current_addr = self.addr_line_edit.text()
            if current_addr and self.register_manager:
                try:
                    addr_int = int(current_addr, 16) if current_addr.startswith("0x") else int(current_addr)
                    old_value = self.register_manager.get_register_value(addr_int)
                    self.set_value_display(old_value) # 恢复旧值
                except (ValueError, KeyError):
                    self.value_line_edit.setText("0000") # 或清空
            else:
                self.value_line_edit.setText("0000")
            return

        try:
            # 智能解析数值：根据前缀判断进制
            if text.lower().startswith("0x"):
                # 十六进制格式（带0x/0X前缀）
                value = int(text, 16)
                logger.debug(f"解析十六进制输入: {text} -> {value}")
            elif re.fullmatch(r'^[0-9]+$', text):
                # 十进制格式（纯数字）
                value = int(text, 10)
                logger.debug(f"解析十进制输入: {text} -> {value}")
                # 自动转换显示格式为十六进制
                self._updating_display = True
                try:
                    self.value_line_edit.setText(f"0x{value:04X}")
                finally:
                    self._updating_display = False
            else:
                # 纯十六进制字符（无0x前缀，向后兼容）
                value = int(text, 16)
                logger.debug(f"解析纯十六进制输入: {text} -> {value}")

            # 获取当前寄存器地址
            addr_text = self.addr_line_edit.text()
            if not addr_text:
                return

            addr_int = int(addr_text, 16) if addr_text.startswith("0x") else int(addr_text)

            # 发出写入请求信号
            logger.info(f"现代化IOHandler: 用户输入触发写入请求, 地址: 0x{addr_int:02X}, 值: 0x{value:04X}")
            self.write_requested.emit(addr_int, value)

        except ValueError as e:
            logger.warning(f"值变化处理时出错: {str(e)}")

    def validate_hex_input(self, text):
        """验证输入是否为有效的数字字符串（支持十进制和十六进制）"""
        if not text:
            return True # 空输入允许，可能表示清除

        # 检查是否为十六进制格式（带0x或0X前缀）
        if text.lower().startswith('0x'):
            # 十六进制格式：0x后面跟1到4位十六进制字符
            if re.fullmatch(r'^0[xX][0-9a-fA-F]{1,4}$', text):
                return True
        else:
            # 十进制格式：纯数字
            if re.fullmatch(r'^[0-9]+$', text):
                try:
                    # 检查十进制数值是否在有效范围内（0-65535，即16位）
                    decimal_value = int(text)
                    if 0 <= decimal_value <= 65535:
                        return True
                    else:
                        logger.warning(f"十进制数值超出范围: {text} (应在0-65535之间)")
                        QMessageBox.warning(self.parent, "输入错误", "十进制数值应在0-65535之间")
                        return False
                except ValueError:
                    pass
            # 也允许纯十六进制字符（无0x前缀，为了向后兼容）
            elif re.fullmatch(r'^[0-9a-fA-F]{1,4}$', text):
                return True

        logger.warning(f"无效的数值输入: {text}")
        QMessageBox.warning(self.parent, "输入错误",
                          "请输入有效的数值:\n"
                          "• 十进制: 如 50, 1234\n"
                          "• 十六进制: 如 0x32, 0X1A, 或 1A")
        return False

    def set_address_display(self, addr):
        """更新地址显示"""
        self._updating_display = True
        try:
            self.addr_line_edit.setText(f"0x{addr:02X}")
            self.value_label.setText(f"R{addr:X} Value:") # Dynamically update the value label
        finally:
            self._updating_display = False

    def set_value_display(self, value):
        """设置值显示"""
        if self.value_line_edit:
            self._updating_display = True
            try:
                self.value_line_edit.setText(f"0x{value:04X}")
            finally:
                self._updating_display = False

    def toggle_buttons(self, enable):
        """启用/禁用所有按钮"""
        # 只保留搜索按钮的切换，其他按钮已移除
        if hasattr(self, 'search_btn') and self.search_btn:
            self.search_btn.setEnabled(enable)

    def register_buttons(self, read_btn, write_btn, read_all_btn, write_all_btn, dumpall_btn, save_btn, load_btn):
        """注册按钮引用，以便切换启用/禁用状态（兼容性方法）"""
        # 现代化版本中按钮已经在内部创建，这个方法保持兼容性
        # 参数仅用于兼容性，实际不使用
        pass

    # === 兼容性方法 ===

    def create_io_layout(self):
        """创建IO布局（兼容性方法）"""
        # 现代化版本中UI已经在__init__中创建
        return self.content_widget

    def create_rx_value_layout(self):
        """创建寄存器值读写布局（兼容性方法）"""
        logger.info("ModernRegisterIOHandler: create_rx_value_layout() 兼容性方法被调用")
        # 创建水平布局（COM端口选择、地址和值显示全部在一行）
        top_layout = QHBoxLayout()

        # 添加左侧间隔，让整体右移
        top_layout.addSpacing(20)  # 添加20像素的左侧间隔

        # COM端口选择部分
        port_label = QLabel('COM端口:')
        if not hasattr(self, 'port_combo') or self.port_combo is None:
            self.port_combo = QComboBox()
        self.port_combo.setMinimumWidth(350)  # 适中的最小宽度，能显示完整端口信息但不过宽
        self.port_combo.setMaximumWidth(450)  # 适中的最大宽度，不会覆盖搜索框
        # 设置下拉框的大小策略为固定，避免过度扩展
        from PyQt5.QtWidgets import QSizePolicy
        self.port_combo.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)

        if not hasattr(self, 'refresh_btn'):
            self.refresh_btn = QPushButton('刷新')
            self.refresh_btn.clicked.connect(self.refresh_ports)

        top_layout.addWidget(port_label)
        top_layout.addWidget(self.port_combo)
        top_layout.addWidget(self.refresh_btn)

        # 在刷新按钮和Address标签之间添加更大的间隔
        top_layout.addSpacing(25)  # 增加间隔，从15改为25像素

        # 地址和值部分 - 创建一个居中的子布局
        addr_value_layout = QHBoxLayout()

        # 地址部分
        addr_label = QLabel("Address:")
        addr_label.setStyleSheet("font-weight: bold; color: #333;")  # 加粗标签
        if self.addr_line_edit:
            self.addr_line_edit.setReadOnly(True)
            self.addr_line_edit.setFixedWidth(160)  # 增加宽度，从140改为160
            self.addr_line_edit.setAlignment(Qt.AlignCenter)
            self.addr_line_edit.setStyleSheet("""
                QLineEdit {
                    border: 2px solid #ccc;
                    border-radius: 5px;
                    padding: 5px;
                    font-size: 12px;
                    font-weight: bold;
                    background-color: #f9f9f9;
                }
            """)

        addr_value_layout.addWidget(addr_label)
        addr_value_layout.addWidget(self.addr_line_edit)
        addr_value_layout.addSpacing(20)  # 地址和值之间的间隔

        # 值部分
        if hasattr(self, 'value_label') and self.value_label:
            self.value_label.setStyleSheet("font-weight: bold; color: #333;")  # 加粗标签

        if self.value_line_edit:
            self.value_line_edit.setFixedWidth(200)  # 增加宽度，从180改为200
            self.value_line_edit.setReadOnly(False)
            self.value_line_edit.setAlignment(Qt.AlignCenter)
            self.value_line_edit.setStyleSheet("""
                QLineEdit {
                    border: 2px solid #4CAF50;
                    border-radius: 5px;
                    padding: 5px;
                    font-size: 12px;
                    font-weight: bold;
                    background-color: white;
                }
                QLineEdit:focus {
                    border-color: #45a049;
                    background-color: #f0fff0;
                }
            """)
            # 连接回车事件
            self.value_line_edit.returnPressed.connect(lambda: self.on_value_changed(self.value_line_edit.text()))

        addr_value_layout.addWidget(self.value_label)
        addr_value_layout.addWidget(self.value_line_edit)

        # 将地址值布局添加到主布局
        top_layout.addLayout(addr_value_layout)

        # 添加更多弹性空间，让控件更居中
        top_layout.addStretch(3)  # 增加弹性空间

        # 创建容器控件
        rx_value_widget = QWidget()
        rx_value_widget.setLayout(top_layout)

        return rx_value_widget

    def refresh_ports(self):
        """请求SPI服务刷新可用串口列表"""
        if self.spi_service:
            logger.info("ModernRegisterIOHandler: 请求SPI服务刷新端口")
            self.spi_service.refresh_available_ports()
        else:
            logger.warning("ModernRegisterIOHandler: SPI服务不可用，无法刷新端口")
            self._update_port_combo([])  # 使用空列表更新，以显示无端口状态

    def _update_port_combo(self, port_details):
        """根据SPI服务提供的端口列表更新COM端口下拉框"""
        logger.debug(f"ModernRegisterIOHandler: _update_port_combo 被调用，port_combo: {self.port_combo}, 类型: {type(self.port_combo)}")
        if self.port_combo is None:
            logger.warning("ModernRegisterIOHandler: port_combo 不存在，跳过端口更新")
            return

        # 防止重复更新相同的端口信息
        if hasattr(self, '_last_port_details') and self._last_port_details == port_details:
            logger.info("ModernRegisterIOHandler: 端口信息未变化，跳过重复更新")
            return
        self._last_port_details = port_details.copy() if port_details else []

        # 获取当前SPI服务的连接状态，避免重复连接
        current_spi_port = None
        spi_already_connected = False
        if self.spi_service:
            spi_status = self.spi_service.get_connection_status()
            if spi_status.get('connected', False):
                current_spi_port = spi_status.get('port')
                spi_already_connected = True
                logger.info(f"ModernRegisterIOHandler: SPI服务已连接到端口: {current_spi_port}")

        self.port_combo.clear()
        if port_details:
            for port_info in port_details:
                device = port_info.get('device')
                description = port_info.get('description', 'N/A')
                display_text = f"{device}: {description[:80]}"  # 进一步增加描述长度，从50改为80字符，确保完整显示
                self.port_combo.addItem(display_text, device)
                logger.info(f"ModernRegisterIOHandler: 添加COM端口到下拉框: {device}")

            # 选择上次配置的端口（如果有）或当前已连接的端口
            target_port = current_spi_port if spi_already_connected else self.settings.value("SPI/Port", "")
            if target_port:
                for i in range(self.port_combo.count()):
                    port_data = self.port_combo.itemData(i)
                    if port_data == target_port:
                        self.port_combo.setCurrentIndex(i)
                        logger.info(f"ModernRegisterIOHandler: 选择端口: {target_port}")
                        break

            # 自动选择第一个端口（如果当前没有选择或选择无效）
            if self.port_combo.currentIndex() == -1 and self.port_combo.count() > 0:
                self.port_combo.setCurrentIndex(0)

            # 连接端口选择变化信号
            if not hasattr(self, '_port_signal_connected'):
                self.port_combo.currentIndexChanged.connect(self.port_selection_changed)
                self._port_signal_connected = True

            # 不再自动触发端口选择变化，因为SPI服务在初始化时已经处理了连接
            # 只是更新UI显示当前连接的端口
            if spi_already_connected:
                logger.info("ModernRegisterIOHandler: SPI服务已连接，UI已同步端口显示")
            else:
                logger.info("ModernRegisterIOHandler: 端口列表已更新，等待用户手动选择")

        else:
            self.port_combo.addItem("未检测到COM端口", "")
            logger.info("ModernRegisterIOHandler: 没有检测到COM端口")
            # 确保在没有端口时，SPI服务也被告知
            if self.spi_service and not spi_already_connected:
                self.spi_service.set_port(None)

        # 端口列表更新后，触发状态栏更新
        self._trigger_status_bar_update()

    def _trigger_status_bar_update(self):
        """触发状态栏更新"""
        try:
            if hasattr(self.parent, 'status_config_manager') and self.parent.status_config_manager:
                logger.info("ModernRegisterIOHandler: 触发状态栏更新")
                self.parent.status_config_manager.update_status_bar()
        except Exception as e:
            logger.error(f"ModernRegisterIOHandler: 触发状态栏更新时出错: {str(e)}")

    def port_selection_changed(self, index):
        """端口选择更改事件处理"""
        if index < 0 or not self.port_combo:
            return

        selected_port = self.port_combo.currentData()
        if not selected_port:
            return

        # 检查是否已经连接到相同端口且在硬件模式，避免重复连接
        if self.spi_service:
            spi_status = self.spi_service.get_connection_status()
            current_mode = spi_status.get('mode', 'simulation')
            current_port = spi_status.get('port')
            is_connected = spi_status.get('connected', False)

            logger.info(f"ModernRegisterIOHandler: 当前状态 - 模式: {current_mode}, 端口: {current_port}, 连接: {is_connected}")
            logger.info(f"ModernRegisterIOHandler: 用户选择端口: {selected_port}")

            # 只有在硬件模式且已连接到相同端口时才跳过
            if current_mode == 'hardware' and is_connected and current_port == selected_port:
                logger.info(f"ModernRegisterIOHandler: 端口 {selected_port} 已在硬件模式下连接，跳过重复设置")
                return

        # 保存选择的端口
        self.settings.setValue("SPI/Port", selected_port)

        # 更新SPI端口
        if self.spi_service:
            logger.info(f"ModernRegisterIOHandler: 设置SPI端口为: {selected_port}")
            success = self.spi_service.set_port(selected_port)
            if success:
                logger.info(f"已设置SPI端口: {selected_port}")
                # 端口设置成功后，触发状态栏更新
                self._trigger_status_bar_update()
            else:
                logger.error(f"设置SPI端口失败: {selected_port}")
                # 端口设置失败后，也触发状态栏更新以反映当前状态
                self._trigger_status_bar_update()

    def get_current_bits(self):
        """获取当前寄存器的位数（兼容性方法）"""
        # 默认返回16位
        return 16

    def validate_register_input(self, text):
        """验证寄存器输入（兼容性方法）"""
        return self.validate_hex_input(text)

    # === 测试和工厂方法 ===

    @classmethod
    def create_for_testing(cls, parent=None):
        """创建测试实例"""
        try:
            # 创建模拟的RegisterManager
            from core.services.register.RegisterManager import RegisterManager
            import json
            import os

            # 加载寄存器配置
            config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'lib', 'register.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                registers_config = json.load(f)

            register_manager = RegisterManager(registers_config)

            # 创建模拟的SPI服务
            class MockSPIService:
                def __init__(self):
                    from PyQt5.QtCore import pyqtSignal, QObject
                    # 创建一个临时的QObject来提供信号
                    class SignalProvider(QObject):
                        spi_operation_complete = pyqtSignal(str, int, bool)
                        spi_error_occurred = pyqtSignal(str)
                        ports_refreshed = pyqtSignal(list)

                    self._signal_provider = SignalProvider()
                    self.spi_operation_complete = self._signal_provider.spi_operation_complete
                    self.spi_error_occurred = self._signal_provider.spi_error_occurred
                    self.ports_refreshed = self._signal_provider.ports_refreshed

                def read_register(self, addr):
                    return 0x1234

                def write_register(self, addr, value):
                    pass

                def refresh_available_ports(self):
                    # 模拟端口列表
                    mock_ports = [
                        {'device': 'COM1', 'description': 'Mock Serial Port 1'},
                        {'device': 'COM2', 'description': 'Mock Serial Port 2'}
                    ]
                    self.ports_refreshed.emit(mock_ports)
                    return mock_ports

                def set_port(self, port):
                    return True

                def get_connection_status(self):
                    return {
                        'connected': False,
                        'mode': 'simulation',
                        'port': None
                    }

            mock_spi = MockSPIService()

            # 创建实例
            instance = cls(parent, register_manager, mock_spi)

            logger.info("创建现代化RegisterIOHandler测试实例成功")
            return instance

        except Exception as e:
            logger.error(f"创建测试实例时出错: {str(e)}")
            raise

    def get_io_widget(self):
        """获取IO控件容器（用于集成到主窗口）"""
        logger.info("ModernRegisterIOHandler: get_io_widget() 被调用")
        return self.content_widget

    # === 端口管理 ===

    def update_port_list(self, ports):
        """更新端口列表

        Args:
            ports: 端口信息列表，可以是字符串列表或字典列表
        """
        if not self.port_combo:
            return

        # 检查ports的格式
        if not ports:
            # 空列表，清空下拉框
            self.port_combo.clear()
            self.port_combo.addItem("未检测到COM端口", "")
            return

        if isinstance(ports[0], dict):
            # 新格式：字典列表 [{'device': 'COM3', 'description': '...'}]
            self._update_port_combo(ports)
        else:
            # 旧格式：字符串列表 ['COM3', 'COM4']
            current_port = self.port_combo.currentText()
            self.port_combo.clear()
            self.port_combo.addItems(ports)

            # 尝试恢复之前选择的端口
            if current_port in ports:
                self.port_combo.setCurrentText(current_port)

    def get_selected_port(self):
        """获取当前选择的端口"""
        if self.port_combo:
            return self.port_combo.currentText()
        return ""

    def set_selected_port(self, port):
        """设置选择的端口"""
        if self.port_combo:
            index = self.port_combo.findText(port)
            if index >= 0:
                self.port_combo.setCurrentIndex(index)

    # === 状态管理 ===

    def save_settings(self):
        """保存设置"""
        if self.settings:
            # 保存当前选择的端口
            if self.port_combo:
                self.settings.setValue("selected_port", self.port_combo.currentText())

            # 保存窗口位置和大小
            self.settings.setValue("io_handler_geometry", self.saveGeometry())

    def load_settings(self):
        """加载设置"""
        if self.settings:
            # 恢复选择的端口
            saved_port = self.settings.value("selected_port", "")
            if saved_port and self.port_combo:
                self.set_selected_port(saved_port)

            # 恢复窗口位置和大小
            geometry = self.settings.value("io_handler_geometry")
            if geometry:
                self.restoreGeometry(geometry)

    def closeEvent(self, event):
        """窗口关闭事件"""
        self.save_settings()
        super().closeEvent(event)

    # === 搜索功能（参考重构前的实现） ===

    def _handle_search_click(self, text_or_checked_state=None):
        """处理搜索文本变化或按钮点击事件（改进版：移除字符长度限制）"""
        # 确定搜索文本
        if isinstance(text_or_checked_state, str):
            search_text_raw = text_or_checked_state
        else:  # 来自按钮点击（bool）或None
            search_text_raw = self.search_edit.text()

        search_text = search_text_raw.strip()
        logger.info(f"搜索请求: '{search_text}' (raw: '{search_text_raw}')")

        if not search_text:  # 如果搜索文本为空
            self.search_results_list.hide()
            return

        # 改进：移除字符长度限制，允许用户输入任意长度的搜索文本
        # 对于任何非空输入都立即执行搜索，提升用户体验
        if self.register_manager:
            results = self.register_manager.search_bit_fields(search_text)
            model = QStringListModel([r[0] for r in results] if results else [])
            self.completer.setModel(model)

            self.search_requested.emit(search_text)
            self.update_search_results(results)



    def update_search_results(self, results):
        """更新搜索结果列表（参考重构前的实现）"""
        self.search_results_list.clear()
        if results:
            for bit_name, addr, _ in results:
                item_text = f"{bit_name}"  # 只显示位段名称，与重构前一致
                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, addr)  # 将地址存储在UserRole中
                self.search_results_list.addItem(item)

            # 计算列表位置 - 相对于屏幕（参考重构前的实现）
            search_edit_global_pos = self.search_edit.mapToGlobal(self.search_edit.rect().bottomLeft())
            self.search_results_list.move(search_edit_global_pos)

            self.search_results_list.setFixedWidth(self.search_edit.width())  # 设置宽度与搜索框一致
            if not self.search_results_list.isVisible():
                self.search_results_list.show()
            if self.search_results_list.count() > 0:
                self.search_results_list.setCurrentRow(0)  # 默认选中第一项
        else:
            self.search_results_list.hide()

    def _handle_result_selection(self, item):
        """处理列表项点击或回车选择（参考重构前的实现）"""
        if item:
            selected_addr = item.data(Qt.UserRole)
            selected_text = item.text().split(' (')[0]  # 获取位段名称部分
            logger.info(f"搜索结果选中: {item.text()}, 地址: {selected_addr}")

            # 临时阻止信号以防止递归搜索触发
            self.search_edit.blockSignals(True)
            self.search_edit.setText(selected_text)  # 将选中的位段名称填入搜索框
            self.search_edit.blockSignals(False)

            self.search_results_list.hide()  # 选择后隐藏列表
            self.bit_field_selected.emit(selected_addr)  # 发出信号

    def _handle_completer_activated(self, text):
        """处理自动补全项选择事件（参考重构前的实现）"""
        # 查找匹配的位段地址并触发选择
        results = self.register_manager.search_bit_fields(text)
        if results:
            # 更新搜索框文本并触发选择
            self.search_edit.setText(text)
            self.bit_field_selected.emit(results[0][1])
            # 立即隐藏下拉列表
            if hasattr(self, 'search_results_list') and self.search_results_list.isVisible():
                self.search_results_list.hide()

    def _check_focus_and_hide_list(self):
        """检查焦点是否仍在搜索框或结果列表内，如果不在则隐藏列表"""
        if not self.search_edit.hasFocus() and not self.search_results_list.hasFocus():
            self.search_results_list.hide()

    def eventFilter(self, obj, event):
        """事件过滤器（参考重构前的实现）"""
        from PyQt5.QtCore import QEvent


        # 处理搜索框的键盘事件
        if obj == self.search_edit:
            if event.type() == QEvent.KeyPress:
                key_event = event
                if key_event.key() == Qt.Key_Down:
                    # 下箭头键：移动到搜索结果列表
                    if self.search_results_list.isVisible() and self.search_results_list.count() > 0:
                        self.search_results_list.setFocus()
                        self.search_results_list.setCurrentRow(0)
                        return True
                elif key_event.key() == Qt.Key_Escape:
                    # ESC键：隐藏搜索结果列表
                    self.search_results_list.hide()
                    return True

        # 处理搜索结果列表的键盘事件
        elif obj == self.search_results_list:
            if event.type() == QEvent.KeyPress:
                key_event = event
                if key_event.key() == Qt.Key_Return or key_event.key() == Qt.Key_Enter:
                    # 回车键：选择当前项
                    current_item = self.search_results_list.currentItem()
                    if current_item:
                        self._handle_result_selection(current_item)
                        return True
                elif key_event.key() == Qt.Key_Escape:
                    # ESC键：隐藏列表并返回焦点到搜索框
                    self.search_results_list.hide()
                    self.search_edit.setFocus()
                    return True
                elif key_event.key() == Qt.Key_Up:
                    # 上箭头键：如果在第一项，返回搜索框
                    if self.search_results_list.currentRow() == 0:
                        self.search_edit.setFocus()
                        return True

        # 调用父类的事件过滤器
        return super().eventFilter(obj, event)

    # === 批量操作 ===

    def batch_read_registers(self, addresses):
        """批量读取寄存器实际值"""
        try:
            if not self.spi_service:
                logger.error("SPI服务不可用，无法执行批量读取")
                return {}

            result = {}
            for addr in addresses:
                value = self.spi_service.read_register(addr)
                result[addr] = value
            return result
        except Exception as e:
            QMessageBox.critical(self.parent, "读取错误", f"批量读取失败: {str(e)}")
            return {}

    # === ModernBaseHandler重写方法 ===

    def on_register_value_changed(self, widget_name, reg_addr, reg_value):
        """处理寄存器值变化"""
        logger.info(f"寄存器IO: 寄存器 {reg_addr} 值变化 (控件: {widget_name}) -> 0x{reg_value:04X}")

        # 如果当前显示的是这个寄存器，更新显示
        current_addr_text = self.addr_line_edit.text()
        if current_addr_text:
            try:
                current_addr = int(current_addr_text, 16) if current_addr_text.startswith("0x") else int(current_addr_text)
                # 标准化地址比较
                normalized_reg_addr = self._normalize_register_address(reg_addr)
                normalized_current_addr = self._normalize_register_address(current_addr)
                if normalized_current_addr == normalized_reg_addr:
                    self.set_value_display(reg_value)
            except ValueError:
                pass

    def on_global_register_update(self, reg_addr, reg_value):
        """处理全局寄存器更新"""
        logger.debug(f"寄存器IO: 收到全局更新 {reg_addr} = 0x{reg_value:04X}")

        # 如果当前显示的是这个寄存器，更新显示
        current_addr_text = self.addr_line_edit.text()
        if current_addr_text:
            try:
                current_addr = int(current_addr_text, 16) if current_addr_text.startswith("0x") else int(current_addr_text)
                # 标准化地址比较
                normalized_reg_addr = self._normalize_register_address(reg_addr)
                normalized_current_addr = self._normalize_register_address(current_addr)
                if normalized_current_addr == normalized_reg_addr:
                    self.set_value_display(reg_value)
                    self.set_address_display(current_addr)
            except ValueError:
                pass

    def _normalize_register_address(self, addr):
        """标准化寄存器地址"""
        if isinstance(addr, str):
            if addr.startswith("0x"):
                return addr
            else:
                return f"0x{int(addr, 16):02X}"
        else:
            return f"0x{addr:02X}"
