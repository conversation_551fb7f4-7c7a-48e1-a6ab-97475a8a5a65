#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
现代化的寄存器表格处理器
使用ModernBaseHandler作为基类，重构自原RegisterTableHandler
主要功能：寄存器位域表格显示、编辑、验证
"""

from PyQt5.QtWidgets import (QTableWidget, QTableWidgetItem, QHeaderView,
                            QMessageBox, QVBoxLayout)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QColor
from ui.handlers.ModernBaseHandler import ModernBaseHandler
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class ModernRegisterTableHandler(ModernBaseHandler):
    """现代化的寄存器表格处理器"""

    # 添加表格相关信号
    bit_field_changed = pyqtSignal(str, str, int)  # register_addr, field_name, new_value
    table_updated = pyqtSignal(str, int)  # register_addr, register_value

    # 搜索相关信号已移至IO处理器

    def __init__(self, parent=None, register_manager=None, **kwargs):
        """初始化现代化寄存器表格处理器

        Args:
            parent: 父窗口
            register_manager: RegisterManager实例
            **kwargs: 其他参数（如register_repo等，用于兼容性）
        """
        super().__init__(parent, register_manager, **kwargs)

        # 设置窗口标题
        self.setWindowTitle("寄存器位域表格 (现代化版本)")

        # 初始化表格特定属性
        self._init_table_config()

        # 创建UI
        self._create_table_ui()

        # 手动调用初始化（因为测试环境没有事件循环）
        self._post_init()

        logger.info("现代化寄存器表格处理器初始化完成")

    def create_bit_field_table(self):
        """创建位域表格（兼容性方法）"""
        return self.bit_field_table

    def _init_table_config(self):
        """初始化表格特定配置"""
        try:
            self.bit_field_table = None
            self.current_register_addr = None
            self.current_register_value = None

            # 表格配置
            self.table_config = {
                "columns": ["位域范围", "位域功能", "值", "访问权限"],
                "column_count": 4,
                "row_height": 22,
                "editable_column": 2,  # 值列可编辑
                "colors": {
                    "editable": QColor(230, 230, 255),
                    "readonly": QColor(245, 245, 245),
                    "error": QColor(255, 230, 230)
                }
            }

            logger.info("表格配置初始化完成")

        except Exception as e:
            logger.error(f"初始化表格配置时出错: {str(e)}")

    # 搜索配置已移至IO处理器
    # def _init_search_config(self):
    #     """初始化搜索相关配置 - 已移至IO处理器"""
    #     pass

    def _create_table_ui(self):
        """创建表格UI"""
        try:
            # 创建主布局（紧凑布局，移除多余边距）
            layout = QVBoxLayout()
            layout.setContentsMargins(0, 0, 0, 0)  # 移除所有边距
            layout.setSpacing(0)  # 移除间距

            # 只创建位域表格，不创建搜索区域（搜索功能由IO处理器提供）
            self.bit_field_table = self._create_bit_field_table()
            layout.addWidget(self.bit_field_table)

            # 设置内容控件的布局
            self.content_widget.setLayout(layout)

            logger.info("表格UI创建完成")

        except Exception as e:
            logger.error(f"创建表格UI时出错: {str(e)}")

    # 搜索功能已移至IO处理器，此方法不再需要
    # def _create_search_widget(self):
    #     """创建搜索控件区域 - 已移至IO处理器"""
    #     pass

    # 搜索信号连接已移至IO处理器
    # def _connect_search_signals(self):
    #     """连接搜索相关信号 - 已移至IO处理器"""
    #     pass

    def _create_bit_field_table(self):
        """创建位域表格"""
        try:
            table = QTableWidget()
            table.setColumnCount(self.table_config["column_count"])
            table.setHorizontalHeaderLabels(self.table_config["columns"])
            table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

            # 优化表格性能
            table.setVerticalScrollMode(QTableWidget.ScrollPerPixel)
            table.setHorizontalScrollMode(QTableWidget.ScrollPerPixel)
            table.setAlternatingRowColors(True)

            # 设置表格大小策略，让表格能够扩展占用可用空间
            from PyQt5.QtWidgets import QSizePolicy
            table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

            # 设置最小高度，确保至少能显示几行
            table.setMinimumHeight(100)  # 最小高度100像素，确保基本可用性

            # 移除最大高度限制，让表格能够占用更多空间

            # 连接单元格变更信号
            table.cellChanged.connect(self._on_table_cell_changed)

            logger.info("位域表格创建完成")
            return table

        except Exception as e:
            logger.error(f"创建位域表格时出错: {str(e)}")
            return None

    def on_register_value_changed(self, widget_name, reg_addr, reg_value):
        """处理寄存器值变化"""
        logger.info(f"寄存器表格: 寄存器 {reg_addr} 值变化 (控件: {widget_name}) -> 0x{reg_value:04X}")

        # 如果当前显示的就是这个寄存器，更新表格
        if self.current_register_addr == reg_addr:
            self.show_bit_fields(reg_addr, reg_value)

    def on_global_register_update(self, reg_addr, reg_value):
        """处理全局寄存器更新"""
        logger.debug(f"寄存器表格: 收到全局更新 {reg_addr} = 0x{reg_value:04X}")

        # 如果当前显示的就是这个寄存器，更新表格（标记为全局更新）
        if self.current_register_addr == reg_addr:
            self.show_bit_fields(reg_addr, reg_value, from_global_update=True)

    def refresh_current_register(self, new_value):
        """刷新当前寄存器的显示，不跳转到其他寄存器

        Args:
            new_value: 新的寄存器值
        """
        try:
            if not self.current_register_addr:
                logger.debug("ModernTableHandler: 没有当前寄存器，跳过刷新")
                return

            logger.info(f"ModernTableHandler: 批量操作完成后刷新当前寄存器显示 - 地址: {self.current_register_addr}, 新值: 0x{new_value:04X}（这是刷新，不是跳转）")

            # 更新当前寄存器值
            self.current_register_value = new_value

            # 获取位域信息
            try:
                bit_fields = self.register_manager.get_register_bit_info(self.current_register_addr)
                if not bit_fields:
                    logger.debug(f"ModernTableHandler: 寄存器 {self.current_register_addr} 没有位域信息")
                    return
            except ValueError as e:
                logger.warning(f"ModernTableHandler: 获取位域信息失败: {str(e)}")
                return

            # 断开信号连接，防止在更新表格时触发cellChanged信号
            self._disconnect_table_signals()

            # 只更新表格内容，不改变当前寄存器
            logger.debug("ModernTableHandler: 开始刷新表格内容")
            self._update_table_content(bit_fields, new_value)

            # 重新连接信号
            self._connect_table_signals()

            logger.debug("ModernTableHandler: 成功刷新当前寄存器显示")

        except Exception as e:
            logger.error(f"ModernTableHandler: 刷新当前寄存器显示时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    # === 业务逻辑方法 ===

    def show_bit_fields(self, register_addr, register_value, from_global_update=False):
        """显示指定寄存器的位域信息

        Args:
            register_addr: 寄存器地址
            register_value: 寄存器值
            from_global_update: 是否来自全局更新（如果是，则忽略_widget_triggered_update标志）
        """
        try:
            # 检查是否是表格自己触发的更新，如果是则跳过以避免重复显示
            main_window = self._get_main_window()
            if main_window and getattr(main_window, '_table_initiated_update', False):
                logger.debug(f"ModernTableHandler: 跳过表格自己触发的重复显示 - 地址: {register_addr}")
                return

            # 只有在非全局更新时才检查控件修改标志
            if not from_global_update and main_window and getattr(main_window, '_widget_triggered_update', False):
                # 检查是否是同一个寄存器的重复显示
                last_display_addr = getattr(main_window, '_last_display_addr', None)
                if last_display_addr == register_addr:
                    logger.debug(f"ModernTableHandler: 跳过控件修改触发的重复显示 - 地址: {register_addr}")
                    return

            logger.info(f"ModernTableHandler: 开始显示位域信息 - 地址: {register_addr}, 值: 0x{register_value:04X}")

            # 记录当前显示的寄存器地址
            if main_window:
                setattr(main_window, '_last_display_addr', register_addr)

            if self.bit_field_table is None:
                logger.warning("ModernTableHandler: 位域表格未初始化")
                return

            self.current_register_addr = register_addr
            self.current_register_value = register_value

            logger.info(f"ModernTableHandler: 设置当前寄存器 - 地址: {self.current_register_addr}, 值: 0x{self.current_register_value:04X}")

            # 获取位域信息
            try:
                bit_fields = self.register_manager.get_register_bit_info(register_addr)
                logger.info(f"ModernTableHandler: 获取到 {len(bit_fields) if bit_fields else 0} 个位域")
            except ValueError as e:
                error_message = f"无法获取寄存器 {register_addr} 的位域定义: {str(e)}"
                logger.warning(f"ModernTableHandler: {error_message}")
                self._clear_table_and_show_error(error_message)
                return

            if bit_fields is None or not bit_fields:
                message = f"寄存器 0x{int(register_addr, 16):02X} ({register_addr}) 没有定义位域信息。"
                logger.warning(f"ModernTableHandler: {message}")
                self._clear_table_and_show_error(message)
                return

            # 断开信号连接，防止在更新表格时触发cellChanged信号
            logger.debug("ModernTableHandler: 断开表格信号连接")
            self._disconnect_table_signals()

            # 更新表格内容
            logger.info("ModernTableHandler: 开始更新表格内容")
            self._update_table_content(bit_fields, register_value)

            # 重新连接信号
            logger.debug("ModernTableHandler: 重新连接表格信号")
            self._connect_table_signals()

            # 发送表格更新信号（确保register_addr是字符串类型）
            addr_str = str(register_addr) if not isinstance(register_addr, str) else register_addr
            self.table_updated.emit(addr_str, register_value)

            logger.info(f"ModernTableHandler: 成功显示寄存器 {register_addr} 的 {len(bit_fields)} 个位域")

        except Exception as e:
            logger.error(f"ModernTableHandler: 显示位域信息时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _disconnect_table_signals(self):
        """断开表格信号连接"""
        try:
            self.bit_field_table.cellChanged.disconnect(self._on_table_cell_changed)
        except TypeError:  # PyQt5 5.11+ disconnect 引发 TypeError（如果未连接）
            pass
        except Exception as e:
            logger.warning(f"断开 cellChanged 信号时出错: {e}")

    def _connect_table_signals(self):
        """连接表格信号"""
        try:
            self.bit_field_table.cellChanged.connect(self._on_table_cell_changed)
        except Exception as e:
            logger.error(f"连接表格信号时出错: {str(e)}")

    def _clear_table_and_show_error(self, error_message):
        """清空表格并显示错误信息

        Args:
            error_message: 错误消息
        """
        try:
            if self.bit_field_table is None:
                return

            self.bit_field_table.setRowCount(0)  # 清空表格内容
            self.bit_field_table.setRowCount(1)
            error_item = QTableWidgetItem(error_message)
            error_item.setTextAlignment(Qt.AlignCenter)
            error_item.setFlags(Qt.ItemIsEnabled)  # 只读
            error_item.setBackground(self.table_config["colors"]["error"])
            self.bit_field_table.setItem(0, 0, error_item)

            # 合并单元格以显示完整的错误消息
            if self.bit_field_table.columnCount() > 1:
                self.bit_field_table.setSpan(0, 0, 1, self.bit_field_table.columnCount())

            logger.error(f"RegisterTableHandler: {error_message}")

        except Exception as e:
            logger.error(f"显示错误信息时出错: {str(e)}")

    def _update_table_content(self, bit_fields, register_value):
        """更新表格内容

        Args:
            bit_fields: 位域列表
            register_value: 寄存器值
        """
        try:
            # 检查是否在批量操作期间，如果是则跳过UI更新优化
            main_window = self._get_main_window()
            is_batch_operation = False
            if main_window:
                is_batch_operation = (getattr(main_window, 'is_batch_reading', False) or
                                    getattr(main_window, 'is_batch_writing', False) or
                                    getattr(main_window, 'is_batch_updating', False))

            # 暂时禁用表格更新，防止布局抖动
            if not is_batch_operation:
                self.bit_field_table.setUpdatesEnabled(False)

            self.bit_field_table.setRowCount(len(bit_fields))

            for row, bit_field in enumerate(bit_fields):
                self._update_table_row(row, bit_field, register_value)

            # 调整行高以优化显示
            for row in range(self.bit_field_table.rowCount()):
                self.bit_field_table.setRowHeight(row, self.table_config["row_height"])

            # 恢复表格更新
            if not is_batch_operation:
                self.bit_field_table.setUpdatesEnabled(True)
                # 只在非批量操作时进行重绘和事件处理
                self.bit_field_table.update()  # 使用update()而不是repaint()，更温和

                # 移除QApplication.processEvents()调用，避免触发布局重新计算
                # from PyQt5.QtWidgets import QApplication
                # QApplication.processEvents()

        except Exception as e:
            logger.error(f"更新表格内容时出错: {str(e)}")
            # 确保在出错时也恢复表格更新
            if not is_batch_operation:
                self.bit_field_table.setUpdatesEnabled(True)

    def _update_table_row(self, row, bit_field, register_value):
        """更新表格的一行

        Args:
            row: 行号
            bit_field: 位域信息
            register_value: 寄存器值
        """
        try:
            # 获取位字段信息
            bit_range = bit_field.get("bit", "")
            field_name = bit_field.get("name", "")
            description = bit_field.get("description") or ""
            default_str = bit_field.get("default", "0")

            # 确定访问权限
            access = bit_field.get("access", "R/W")
            if field_name == "NC" or "Read only" in description or "Reserved" in description:
                access = "R/O"

            # 计算位掩码并提取字段值
            bit_mask = self._get_bit_mask(bit_range)
            bit_start = self._get_bit_start(bit_range)
            field_value = (register_value & bit_mask) >> bit_start

            # 计算位字段的位宽
            bit_width = self._get_bit_width(bit_range, default_str)

            # 创建表格项
            self._create_table_row_items(row, bit_range, field_name, field_value,
                                       access, bit_width)

        except Exception as e:
            logger.error(f"更新表格行 {row} 时出错: {str(e)}")

    def _create_table_row_items(self, row, bit_range, field_name, field_value,
                               access, bit_width):
        """创建表格行的所有项

        Args:
            row: 行号
            bit_range: 位范围
            field_name: 字段名称
            field_value: 字段值
            access: 访问权限
            bit_width: 位宽
        """
        try:
            # 1. Field Offset (位范围)
            offset_item = self._create_table_item(bit_range)
            offset_item.setTextAlignment(Qt.AlignCenter)

            # 2. Field Function (字段名称)
            name_item = self._create_table_item(field_name)
            name_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)

            # 3. Value (值) - 显示为二进制格式
            binary_value = format(field_value, f'0{bit_width}b')
            value_item = QTableWidgetItem(binary_value)
            value_item.setTextAlignment(Qt.AlignCenter)

            if access == "R/W":
                value_item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsSelectable | Qt.ItemIsEditable)
                value_item.setBackground(self.table_config["colors"]["editable"])
            else:
                value_item.setFlags(value_item.flags() & ~Qt.ItemIsEditable)
                value_item.setBackground(self.table_config["colors"]["readonly"])

            # 保存位信息作为用户数据
            value_item.setData(Qt.UserRole, {
                "bit_range": bit_range,
                "access": access,
                "bit_width": bit_width,
                "field_name": field_name
            })

            # 4. Access (访问权限)
            access_item = self._create_table_item(access)
            access_item.setTextAlignment(Qt.AlignCenter)

            # 设置单元格项
            self.bit_field_table.setItem(row, 0, offset_item)
            self.bit_field_table.setItem(row, 1, name_item)
            self.bit_field_table.setItem(row, 2, value_item)
            self.bit_field_table.setItem(row, 3, access_item)

        except Exception as e:
            logger.error(f"创建表格行项时出错: {str(e)}")

    def _create_table_item(self, text):
        """创建表格项

        Args:
            text: 文本内容

        Returns:
            QTableWidgetItem: 表格项
        """
        item = QTableWidgetItem(str(text))
        item.setFlags(item.flags() & ~Qt.ItemIsEditable)
        return item

    # === 公共接口方法 ===

    def get_current_status(self):
        """获取当前表格状态

        Returns:
            dict: 当前状态信息
        """
        try:
            status = {}

            if self.bit_field_table:
                status["current_register"] = self.current_register_addr
                status["current_value"] = self.current_register_value
                status["row_count"] = self.bit_field_table.rowCount()
                status["column_count"] = self.bit_field_table.columnCount()

                # 获取可编辑字段数量
                editable_count = 0
                for row in range(self.bit_field_table.rowCount()):
                    item = self.bit_field_table.item(row, self.table_config["editable_column"])
                    if item and (item.flags() & Qt.ItemIsEditable):
                        editable_count += 1

                status["editable_fields"] = editable_count

            return status

        except Exception as e:
            logger.error(f"获取表格状态时出错: {str(e)}")
            return {}

    def _on_table_cell_changed(self, row, column):
        """处理表格单元格值变更

        Args:
            row: 行号
            column: 列号
        """
        try:
            if column != self.table_config["editable_column"]:  # 只处理值列
                return

            if not self.current_register_addr:
                return

            item = self.bit_field_table.item(row, column)
            if not item:
                return

            # 获取位信息
            bit_data = item.data(Qt.UserRole)
            if not bit_data:
                return

            bit_range = bit_data.get("bit_range", "")
            bit_width = bit_data.get("bit_width", 1)
            field_name = bit_data.get("field_name", "")

            # 获取新输入的值 (二进制字符串)
            new_value_str = item.text().strip()

            # 验证输入是否为有效的二进制字符串，且长度与位宽匹配
            if not self._validate_binary_input(new_value_str, bit_width):
                # 恢复旧值
                self._restore_old_value(item, bit_range, bit_width)
                QMessageBox.warning(self.parent, "输入错误",
                                  f"请输入有效的 {bit_width} 位二进制值 (0 或 1)。")
                return

            # 处理值变更
            self._process_value_change(new_value_str, bit_range, field_name)

        except Exception as e:
            logger.error(f"处理单元格变更时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _validate_binary_input(self, text, expected_length):
        """验证输入是否为指定长度的有效二进制字符串

        Args:
            text: 输入文本
            expected_length: 期望长度

        Returns:
            bool: 是否有效
        """
        if not text:
            return False
        if len(text) != expected_length:
            return False
        if not all(c in '01' for c in text):
            return False
        return True

    def _restore_old_value(self, item, bit_range, bit_width):
        """恢复旧值

        Args:
            item: 表格项
            bit_range: 位范围
            bit_width: 位宽
        """
        try:
            bit_mask = self._get_bit_mask(bit_range)
            bit_start = self._get_bit_start(bit_range)
            old_field_value = (self.current_register_value & bit_mask) >> bit_start
            old_binary_value = format(old_field_value, f'0{bit_width}b')

            # 阻止信号再次触发
            self.bit_field_table.blockSignals(True)
            item.setText(old_binary_value)
            self.bit_field_table.blockSignals(False)

        except Exception as e:
            logger.error(f"恢复旧值时出错: {str(e)}")

    def _process_value_change(self, new_value_str, bit_range, field_name):
        """处理值变更 - 执行四个连续动作（防重复调用版本）

        Args:
            new_value_str: 新值字符串
            bit_range: 位范围
            field_name: 字段名称
        """
        try:
            # 防重复调用检查
            main_window = self._get_main_window()
            if main_window and getattr(main_window, '_processing_table_change', False):
                logger.debug("ModernTableHandler: 检测到重复调用，跳过处理")
                return

            # 设置处理标志
            if main_window:
                setattr(main_window, '_processing_table_change', True)

            try:
                # 将二进制字符串转换为整数
                new_field_value = int(new_value_str, 2)

                # 计算新的寄存器值
                bit_mask = self._get_bit_mask(bit_range)
                bit_start = self._get_bit_start(bit_range)

                # 清除旧的位值
                new_register_value = self.current_register_value & (~bit_mask)
                # 设置新的位值
                new_register_value |= (new_field_value << bit_start)

                logger.info(f"位域变更: {field_name} = {new_value_str} (寄存器 {self.current_register_addr} = 0x{new_register_value:04X})")

                # === 第一个动作：更新寄存器值 ===
                self.current_register_value = new_register_value

                # 更新 RegisterManager 中的值
                if self.register_manager:
                    self.register_manager.set_register_value(self.current_register_addr, new_register_value)

                # === 第二个动作：更新rx_value显示控件 ===
                self._update_rx_value_display(new_register_value)

                # === 第三个动作：写入硬件 ===
                # 表格直接修改后必须写入芯片（参考原RegisterTableHandler实现）
                # 与控件修改不同，表格修改应该立即写入，不依赖自动写入设置
                logger.info(f"ModernTableHandler: 表格修改后立即写入寄存器 {self.current_register_addr} = 0x{new_register_value:04X} 到芯片")
                self._write_register_to_chip(self.current_register_addr, new_register_value)

                # === 第四个动作：更新工具窗口（通过全局信号总线） ===
                # 设置标志，防止表格在响应自己发送的信号时重复更新
                if main_window:
                    setattr(main_window, '_table_initiated_update', True)

                try:
                    # 发送寄存器更新信号到全局总线，确保工具窗口能收到更新
                    if hasattr(self, 'register_update_bus'):
                        self.register_update_bus.emit_register_updated(
                            self.current_register_addr, new_register_value)

                    # 发送位域变更信号（确保register_addr是字符串类型）
                    addr_str = str(self.current_register_addr) if not isinstance(self.current_register_addr, str) else self.current_register_addr
                    self.bit_field_changed.emit(addr_str, field_name, new_field_value)

                finally:
                    # 延迟清除标志，确保所有信号处理完成
                    if main_window:
                        from PyQt5.QtCore import QTimer
                        QTimer.singleShot(100, lambda: self._clear_table_update_flag(main_window))

            finally:
                # 清除处理标志
                if main_window and hasattr(main_window, '_processing_table_change'):
                    delattr(main_window, '_processing_table_change')

        except ValueError as e:
            logger.error(f"无法将输入 '{new_value_str}' 转换为二进制整数: {str(e)}")
        except Exception as e:
            logger.error(f"处理值变更时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    # === 显示更新方法 ===

    def _clear_table_update_flag(self, main_window):
        """清除表格更新标志"""
        try:
            if hasattr(main_window, '_table_initiated_update'):
                delattr(main_window, '_table_initiated_update')
                logger.debug("ModernTableHandler: 已清除表格更新标志")
        except Exception as e:
            logger.warning(f"ModernTableHandler: 清除表格更新标志时出错: {str(e)}")

    def _update_rx_value_display(self, new_register_value):
        """更新rx_value显示控件

        Args:
            new_register_value: 新的寄存器值
        """
        try:
            logger.debug(f"ModernTableHandler: 开始更新rx_value显示: 0x{new_register_value:04X}")

            # 获取主窗口实例
            main_window = self._get_main_window()
            if not main_window:
                logger.warning("ModernTableHandler: 无法获取主窗口实例，跳过rx_value显示更新")
                return

            logger.debug(f"ModernTableHandler: 成功获取主窗口实例: {type(main_window)}")
            logger.debug(f"ModernTableHandler: 主窗口属性: display_manager={hasattr(main_window, 'display_manager')}, io_handler={hasattr(main_window, 'io_handler')}")

            # 检查当前寄存器是否是选中的寄存器
            current_selected = getattr(main_window, 'selected_register_addr', None)
            if current_selected != self.current_register_addr:
                logger.debug(f"ModernTableHandler: 当前寄存器 {self.current_register_addr} 不是选中寄存器 {current_selected}，跳过显示更新")
                return

            # 方法1：尝试使用显示管理器更新
            if hasattr(main_window, 'display_manager') and main_window.display_manager:
                try:
                    reg_num = int(self.current_register_addr, 16) if isinstance(self.current_register_addr, str) else self.current_register_addr
                    logger.debug(f"ModernTableHandler: 调用display_manager.update_register_display({reg_num}, 0x{new_register_value:04X})")
                    main_window.display_manager.update_register_display(reg_num, new_register_value)
                    logger.info(f"ModernTableHandler: 通过display_manager成功更新rx_value显示: R{reg_num} = 0x{new_register_value:04X}")
                    return
                except Exception as e:
                    logger.warning(f"ModernTableHandler: 通过display_manager更新显示失败: {str(e)}")
                    import traceback
                    traceback.print_exc()

            # 方法2：直接更新IO处理器的显示
            if hasattr(main_window, 'io_handler') and main_window.io_handler:
                logger.debug(f"ModernTableHandler: io_handler类型: {type(main_window.io_handler)}")
                if hasattr(main_window.io_handler, 'set_value_display'):
                    try:
                        logger.debug(f"ModernTableHandler: 调用io_handler.set_value_display(0x{new_register_value:04X})")
                        main_window.io_handler.set_value_display(new_register_value)
                        logger.info(f"ModernTableHandler: 直接通过io_handler成功更新显示: 0x{new_register_value:04X}")
                        return
                    except Exception as e:
                        logger.warning(f"ModernTableHandler: 直接更新io_handler显示失败: {str(e)}")
                        import traceback
                        traceback.print_exc()
                else:
                    logger.warning("ModernTableHandler: io_handler没有set_value_display方法")

            logger.warning("ModernTableHandler: 未找到可用的rx_value显示更新方法")

        except Exception as e:
            logger.error(f"ModernTableHandler: 更新rx_value显示时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    # === 寄存器写入方法 ===

    def _write_register_to_chip(self, reg_addr, reg_value):
        """将寄存器值写入到芯片（避免重复调用）

        Args:
            reg_addr: 寄存器地址
            reg_value: 寄存器值
        """
        try:
            # 获取主窗口实例（参考原实现）
            main_window = self._get_main_window()
            if not main_window:
                logger.warning("ModernTableHandler: 无法获取主窗口实例，跳过写入操作")
                return

            # 设置标志，表示这是表格触发的写入，避免重复的显示更新
            setattr(main_window, '_table_triggered_write', True)

            try:
                # 优先使用SPI协调器进行直接写入，避免重复的显示更新逻辑
                if hasattr(main_window, 'spi_coordinator'):
                    logger.info(f"ModernTableHandler: 使用spi_coordinator直接写入寄存器 {reg_addr} = 0x{reg_value:04X}")
                    main_window.spi_coordinator.execute_write_operation(reg_addr, reg_value)
                    return

                # 方法2：使用register_service（现代化方式，但只做写入不做显示更新）
                elif hasattr(main_window, 'register_service') and hasattr(main_window.register_service, 'write_register_only'):
                    logger.info(f"ModernTableHandler: 使用register_service仅写入寄存器 {reg_addr} = 0x{reg_value:04X}")
                    main_window.register_service.write_register_only(reg_addr, reg_value)
                    return

                # 方法3：使用register_service的普通写入方法
                elif hasattr(main_window, 'register_service'):
                    logger.info(f"ModernTableHandler: 使用register_service写入寄存器 {reg_addr} = 0x{reg_value:04X}")
                    main_window.register_service.write_register(reg_addr, reg_value)
                    return

                else:
                    logger.warning("ModernTableHandler: 未找到可用的寄存器写入服务，跳过写入操作")
                    logger.debug(f"ModernTableHandler: 主窗口可用属性: {[attr for attr in dir(main_window) if not attr.startswith('_')]}")

            finally:
                # 清除标志
                if hasattr(main_window, '_table_triggered_write'):
                    delattr(main_window, '_table_triggered_write')

        except Exception as e:
            logger.error(f"ModernTableHandler: 写入寄存器时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def _get_main_window(self):
        """获取主窗口实例（参考ModernBaseHandler实现）

        Returns:
            主窗口实例或None
        """
        try:
            # 添加调试信息
            logger.debug("ModernTableHandler: 尝试获取主窗口实例...")
            logger.debug(f"ModernTableHandler: self类型: {type(self)}")
            logger.debug(f"ModernTableHandler: hasattr(self, 'main_window'): {hasattr(self, 'main_window')}")
            logger.debug(f"ModernTableHandler: hasattr(self, 'parent'): {hasattr(self, 'parent')}")

            # 方法1：直接从self获取main_window
            if hasattr(self, 'main_window') and self.main_window:
                logger.debug("ModernTableHandler: 通过self.main_window获取主窗口")
                return self.main_window

            # 方法2：从parent获取main_window
            if hasattr(self, 'parent') and self.parent:
                logger.debug(f"ModernTableHandler: parent类型: {type(self.parent)}")
                if hasattr(self.parent, 'main_window') and self.parent.main_window:
                    logger.debug("ModernTableHandler: 通过self.parent.main_window获取主窗口")
                    return self.parent.main_window

                # 方法3：parent本身就是主窗口
                if hasattr(self.parent, 'display_manager') or hasattr(self.parent, 'io_handler'):
                    logger.debug("ModernTableHandler: parent本身就是主窗口")
                    return self.parent

                # 方法4：从parent的parent获取
                if hasattr(self.parent, 'parent') and self.parent.parent:
                    logger.debug(f"ModernTableHandler: parent.parent类型: {type(self.parent.parent)}")
                    if hasattr(self.parent.parent, 'display_manager') or hasattr(self.parent.parent, 'io_handler'):
                        logger.debug("ModernTableHandler: 通过self.parent.parent获取主窗口")
                        return self.parent.parent

            logger.warning("ModernTableHandler: 无法找到主窗口实例")
            return None

        except Exception as e:
            logger.error(f"ModernTableHandler: 获取主窗口时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return None

    # === 位操作辅助方法 ===

    def _get_bit_start(self, bit_range):
        """获取位范围的起始位

        Args:
            bit_range: 位范围字符串

        Returns:
            int: 起始位
        """
        try:
            if "-" in bit_range or ":" in bit_range:
                bit_range = bit_range.replace(":", "-")
                start = int(bit_range.split("-")[1])
            else:
                start = int(bit_range)
            return start
        except (ValueError, IndexError):
            logger.error(f"无法解析位范围: {bit_range}")
            return 0

    def _get_bit_mask(self, bit_range):
        """根据位范围计算掩码

        Args:
            bit_range: 位范围字符串

        Returns:
            int: 位掩码
        """
        try:
            if "-" in bit_range or ":" in bit_range:
                bit_range = bit_range.replace(":", "-")
                parts = bit_range.split("-")
                high_bit = int(parts[0])
                low_bit = int(parts[1])
                mask = ((1 << (high_bit - low_bit + 1)) - 1) << low_bit
            else:
                bit_pos = int(bit_range)
                mask = 1 << bit_pos
            return mask
        except (ValueError, IndexError):
            logger.error(f"无法解析位范围: {bit_range}")
            return 0

    def _get_bit_width(self, bit_range, default_str):
        """根据位范围或默认值计算位宽

        Args:
            bit_range: 位范围字符串
            default_str: 默认值字符串

        Returns:
            int: 位宽
        """
        try:
            if '-' in bit_range or ':' in bit_range:
                bit_range = bit_range.replace(":", "-")
                parts = bit_range.split('-')
                high_bit = int(parts[0])
                low_bit = int(parts[1])
                return high_bit - low_bit + 1
            elif bit_range.isdigit():
                return 1  # 单个位

            # 如果无法从位范围确定，尝试从默认值长度推断
            if default_str.startswith('0b'):
                return len(default_str) - 2
            elif default_str.startswith('0x'):
                val = int(default_str, 16)
                return val.bit_length() if val > 0 else 1
            else:
                val = int(default_str)
                return val.bit_length() if val > 0 else 1

        except (ValueError, IndexError):
            return 1  # 默认返回1

    # === 搜索功能已移至IO处理器 ===
    # 搜索相关方法不再需要，由IO处理器统一管理

    @classmethod
    def create_for_testing(cls, parent=None):
        """创建测试实例"""
        try:
            # 创建模拟的RegisterManager
            from core.services.register.RegisterManager import RegisterManager
            import json
            import os
            
            # 加载寄存器配置
            config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'lib', 'register.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                registers_config = json.load(f)
            
            register_manager = RegisterManager(registers_config)
            
            # 创建实例
            instance = cls(parent, register_manager)
            
            logger.info("创建现代化RegisterTableHandler测试实例成功")
            return instance
            
        except Exception as e:
            logger.error(f"创建测试实例时出错: {str(e)}")
            raise


if __name__ == "__main__":
    import sys
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    # 创建测试实例
    handler = ModernRegisterTableHandler.create_for_testing()
    handler.show()
    
    sys.exit(app.exec_())
