#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量读取性能专项测试
专门测试批量读取操作在不同条件下的性能表现
"""

import sys
import os
import time
import logging
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer, QEventLoop

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class BatchReadPerformanceTester:
    """批量读取性能测试器"""
    
    def __init__(self):
        self.app = None
        self.main_window = None
        self.test_results = []
        
    def setup(self):
        """设置测试环境"""
        print("🔧 设置批量读取测试环境...")
        
        try:
            # 创建QApplication
            self.app = QApplication(sys.argv)
            
            # 导入并创建主窗口
            from ui.windows.RegisterMainWindow import RegisterMainWindow
            self.main_window = RegisterMainWindow()
            
            # 确保窗口完全初始化
            self.app.processEvents()
            
            print("✅ 测试环境设置成功")
            return True
            
        except Exception as e:
            print(f"❌ 测试环境设置失败: {str(e)}")
            return False
    
    def test_simulation_read_performance(self):
        """测试模拟模式下的批量读取性能"""
        print("\n" + "="*60)
        print("📖 模拟模式批量读取性能测试")
        print("="*60)
        
        # 确保处于模拟模式
        if hasattr(self.main_window, 'spi_service'):
            self.main_window.spi_service.set_simulation_mode(True)
            print("✅ 已切换到模拟模式")
        
        # 测试不同寄存器数量
        test_counts = [10, 50, 100, 200, 500]
        
        for count in test_counts:
            result = self._measure_read_performance(count, "模拟模式")
            self.test_results.append(result)
            
    def test_hardware_read_performance(self):
        """测试硬件模式下的批量读取性能"""
        print("\n" + "="*60)
        print("🔌 硬件模式批量读取性能测试")
        print("="*60)
        
        # 检查硬件是否可用
        if not self._check_hardware_availability():
            print("❌ 硬件不可用，跳过硬件模式测试")
            return
            
        # 测试不同寄存器数量（硬件模式测试较少数量）
        test_counts = [10, 50, 100]
        
        for count in test_counts:
            result = self._measure_read_performance(count, "硬件模式")
            self.test_results.append(result)
    
    def _check_hardware_availability(self):
        """检查硬件是否可用"""
        if not hasattr(self.main_window, 'spi_service'):
            return False
            
        available_ports = self.main_window.spi_service.get_available_ports()
        if not available_ports:
            return False
            
        # 尝试连接第一个可用端口
        success = self.main_window.spi_service.connect_to_port(available_ports[0])
        if success:
            print(f"✅ 已连接到硬件端口: {available_ports[0]}")
            return True
        else:
            print(f"❌ 连接硬件端口失败: {available_ports[0]}")
            return False
    
    def _measure_read_performance(self, register_count, mode_name):
        """测量批量读取性能"""
        print(f"\n📊 测试 {mode_name} 读取 {register_count} 个寄存器...")
        
        if not hasattr(self.main_window, 'batch_operation_manager'):
            print("❌ 批量操作管理器不可用")
            return None
            
        batch_manager = self.main_window.batch_operation_manager
        
        # 记录开始时间和内存
        start_time = time.time()
        start_memory = self._get_memory_usage()
        
        try:
            # 执行批量读取
            success = self._execute_batch_read(batch_manager, register_count)
            
            # 记录结束时间和内存
            end_time = time.time()
            end_memory = self._get_memory_usage()
            
            # 计算性能指标
            duration = end_time - start_time
            memory_delta = end_memory - start_memory
            throughput = register_count / duration if duration > 0 else 0
            
            result = {
                'mode': mode_name,
                'register_count': register_count,
                'duration': duration,
                'memory_delta': memory_delta,
                'throughput': throughput,
                'success': success
            }
            
            # 显示结果
            self._display_performance_result(result)
            
            return result
            
        except Exception as e:
            print(f"❌ 性能测试失败: {str(e)}")
            return None
    
    def _execute_batch_read(self, batch_manager, target_count):
        """执行批量读取操作"""
        print(f"   🚀 开始读取操作...")
        
        # 设置超时
        timeout = 60  # 60秒超时
        start_time = time.time()
        
        try:
            # 触发批量读取
            batch_manager.handle_read_all_requested()
            
            # 等待操作完成
            while batch_manager.is_batch_reading and (time.time() - start_time) < timeout:
                self.app.processEvents()
                time.sleep(0.01)
                
            if batch_manager.is_batch_reading:
                print(f"   ⚠️  操作超时，强制完成")
                batch_manager._finish_read_all()
                return False
            else:
                print(f"   ✅ 读取操作完成")
                return True
                
        except Exception as e:
            print(f"   ❌ 读取操作异常: {str(e)}")
            if batch_manager.is_batch_reading:
                batch_manager._finish_read_all()
            return False
    
    def _display_performance_result(self, result):
        """显示性能测试结果"""
        if not result:
            return
            
        print(f"   📈 性能结果:")
        print(f"      ⏱️  执行时间: {result['duration']:.3f} 秒")
        print(f"      💾 内存变化: {result['memory_delta']:.2f} MB")
        print(f"      🚀 吞吐量: {result['throughput']:.1f} 寄存器/秒")
        print(f"      ✅ 成功状态: {'成功' if result['success'] else '失败'}")
    
    def _get_memory_usage(self):
        """获取当前内存使用量（MB）"""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except ImportError:
            return 0.0
    
    def generate_performance_report(self):
        """生成性能测试报告"""
        print("\n" + "="*80)
        print("📊 批量读取性能测试报告")
        print("="*80)
        
        if not self.test_results:
            print("❌ 没有测试结果可显示")
            return
            
        # 按模式分组显示结果
        simulation_results = [r for r in self.test_results if r and r['mode'] == '模拟模式']
        hardware_results = [r for r in self.test_results if r and r['mode'] == '硬件模式']
        
        if simulation_results:
            print("\n🎯 模拟模式性能总结:")
            self._display_mode_summary(simulation_results)
            
        if hardware_results:
            print("\n🔌 硬件模式性能总结:")
            self._display_mode_summary(hardware_results)
            
        # 性能对比
        if simulation_results and hardware_results:
            print("\n⚖️  模式对比:")
            self._compare_modes(simulation_results, hardware_results)
            
        # 性能建议
        print("\n💡 性能优化建议:")
        self._generate_recommendations()
    
    def _display_mode_summary(self, results):
        """显示特定模式的性能总结"""
        successful_results = [r for r in results if r['success']]
        
        if not successful_results:
            print("   ❌ 该模式下没有成功的测试")
            return
            
        # 计算统计数据
        durations = [r['duration'] for r in successful_results]
        throughputs = [r['throughput'] for r in successful_results]
        
        avg_duration = sum(durations) / len(durations)
        avg_throughput = sum(throughputs) / len(throughputs)
        max_throughput = max(throughputs)
        
        print(f"   📈 平均执行时间: {avg_duration:.3f} 秒")
        print(f"   🚀 平均吞吐量: {avg_throughput:.1f} 寄存器/秒")
        print(f"   🏆 最高吞吐量: {max_throughput:.1f} 寄存器/秒")
        
        # 显示详细结果表格
        print(f"   📋 详细结果:")
        print(f"      {'寄存器数':<8} {'时间(秒)':<10} {'吞吐量(个/秒)':<15} {'状态':<6}")
        print(f"      {'-'*8} {'-'*10} {'-'*15} {'-'*6}")
        
        for result in results:
            status = "✅" if result['success'] else "❌"
            print(f"      {result['register_count']:<8} "
                  f"{result['duration']:<10.3f} "
                  f"{result['throughput']:<15.1f} "
                  f"{status:<6}")
    
    def _compare_modes(self, sim_results, hw_results):
        """对比不同模式的性能"""
        sim_avg_throughput = sum(r['throughput'] for r in sim_results if r['success']) / len([r for r in sim_results if r['success']])
        hw_avg_throughput = sum(r['throughput'] for r in hw_results if r['success']) / len([r for r in hw_results if r['success']])
        
        if sim_avg_throughput > hw_avg_throughput:
            ratio = sim_avg_throughput / hw_avg_throughput
            print(f"   🎯 模拟模式比硬件模式快 {ratio:.1f} 倍")
        else:
            ratio = hw_avg_throughput / sim_avg_throughput
            print(f"   🔌 硬件模式比模拟模式快 {ratio:.1f} 倍")
    
    def _generate_recommendations(self):
        """生成性能优化建议"""
        print("   1. 模拟模式适合功能测试和开发调试")
        print("   2. 硬件模式性能受SPI通信速度限制")
        print("   3. 批次大小可以根据实际需求调整")
        print("   4. 大量寄存器读取时建议分批处理")
        print("   5. UI更新频率影响整体性能表现")


def test_batch_read_performance():
    """主测试函数"""
    print("🚀 批量读取性能专项测试")
    
    tester = BatchReadPerformanceTester()
    
    try:
        # 设置测试环境
        if not tester.setup():
            return
            
        # 测试模拟模式
        tester.test_simulation_read_performance()
        
        # 测试硬件模式
        tester.test_hardware_read_performance()
        
        # 生成报告
        tester.generate_performance_report()
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_batch_read_performance()
