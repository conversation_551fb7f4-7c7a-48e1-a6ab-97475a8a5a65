
=== 拖拽释放检测测试 ===
✓ 获取到插件服务: PluginIntegrationService
✓ 已启用强制浮动模式
✓ 找到插件: 时钟输入控制
✓ 获取到插件窗口: 时钟输入控制 (现代化版本)

--- 拖拽支持检查 ---
窗口类型: ModernClkinControlHandler
窗口标题: 时钟输入控制 (现代化版本)
鼠标跟踪: True
_is_dragging: False
_drag_start_position: None
_original_title: 未找到

--- 模拟拖拽操作 ---
1. 发送鼠标按下事件: PyQt5.QtCore.QPoint(50, 20)
b'\x00\x00\x05\x02' b'\x00\x00\x04W\x00\x00\x18'
b'\x00\x00\x05\x02' b'\x00\x00\x04W\x00\x00\x10'
b'\x00\x00\x05\x02' b'\x00\x00\x04`\x00\x00\x81'
b'\x00\x00\x05\x02' b'\x00\x00\x04H\x00\x00%'
b'\x00\x00\x05\x02' b'\x00\x00\x04W\x00\x00\x00'
b'\x00\x00\x05\x02' b'\x00\x00\x04X\x00\x00\x00'
b'\x00\x00\x05\x02' b'\x00\x00\x04Y\x00\x00@'
b'\x00\x00\x05\x02' b'\x00\x00\x04X\x00\x00\x00'
b'\x00\x00\x05\x02' b'\x00\x00\x04Y\x00\x00@'
b'\x00\x00\x05\x02' b'\x00\x00\x04H\x00\x00 '
b'\x00\x00\x05\x02' b'\x00\x00\x04U\x00\x00\x00'
b'\x00\x00\x05\x02' b'\x00\x00\x04[\x00\x00\x04'
   按下后_is_dragging: False
2. 发送鼠标移动事件: PyQt5.QtCore.QPoint(50, 120)
   移动后_is_dragging: False
3. 发送鼠标释放事件: PyQt5.QtCore.QPoint(50, 120), Window ID before sendEvent: 2181546817136
   Window ID after sendEvent and processEvents: 2181546817136
   释放后_is_dragging: False

--- 测试结果分析 ---
❌ 拖拽状态未正确设置
🔍 可能是拖拽距离不够或事件处理有问题
