#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试PLL2 Calibration控件的初始化
验证FCAL_EN、FCAL_M1、FCAL_M2控件的正确设置
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication


def test_pll2_calibration():
    """测试PLL2 Calibration控件"""
    print("=" * 60)
    print("测试PLL2 Calibration控件初始化")
    print("=" * 60)

    try:
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        # 创建寄存器管理器
        from core.services.register.RegisterManager import RegisterManager
        config_path = os.path.join(project_root, 'lib', 'register.json')
        
        import json
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)

        register_manager = RegisterManager(registers_config)

        # 创建现代化PLL处理器
        from ui.handlers.ModernPLLHandler import ModernPLLHandler
        modern_handler = ModernPLLHandler(None, register_manager)
        
        print("✓ 现代化PLL处理器创建成功")

        # 检查PLL2 Calibration控件
        print("\n1. 检查PLL2 Calibration控件状态...")
        
        # 检查FCAL_EN控件
        if hasattr(modern_handler.ui, "FCALEN"):
            fcal_en_checked = modern_handler.ui.FCALEN.isChecked()
            print(f"  FCAL_EN: {'✓ 选中' if fcal_en_checked else '❌ 未选中'}")
        else:
            print("  ❌ FCAL_EN控件不存在")

        # 检查FCAL_M1控件
        if hasattr(modern_handler.ui, "FCALM1"):
            widget = modern_handler.ui.FCALM1
            if hasattr(widget, 'currentText'):
                current_text = widget.currentText()
                current_index = widget.currentIndex()
                item_count = widget.count()
                print(f"  FCAL_M1: 当前值='{current_text}', 索引={current_index}, 总选项={item_count}")
                
                # 显示所有选项
                print("    可用选项:")
                for i in range(min(10, item_count)):  # 只显示前10个选项
                    item_text = widget.itemText(i)
                    print(f"      [{i}] {item_text}")
                if item_count > 10:
                    print(f"      ... 还有 {item_count - 10} 个选项")
                    
            elif hasattr(widget, 'value'):
                current_value = widget.value()
                print(f"  FCAL_M1: 当前值={current_value}")
            elif hasattr(widget, 'text'):
                current_text = widget.text()
                print(f"  FCAL_M1: 当前文本='{current_text}'")
            else:
                print(f"  FCAL_M1: 未知控件类型 {type(widget).__name__}")
        else:
            print("  ❌ FCAL_M1控件不存在")

        # 检查FCAL_M2控件
        if hasattr(modern_handler.ui, "FCALM2"):
            widget = modern_handler.ui.FCALM2
            if hasattr(widget, 'currentText'):
                current_text = widget.currentText()
                current_index = widget.currentIndex()
                item_count = widget.count()
                print(f"  FCAL_M2: 当前值='{current_text}', 索引={current_index}, 总选项={item_count}")
                
                # 显示所有选项
                print("    可用选项:")
                for i in range(min(10, item_count)):  # 只显示前10个选项
                    item_text = widget.itemText(i)
                    print(f"      [{i}] {item_text}")
                if item_count > 10:
                    print(f"      ... 还有 {item_count - 10} 个选项")
                    
            elif hasattr(widget, 'value'):
                current_value = widget.value()
                print(f"  FCAL_M2: 当前值={current_value}")
            elif hasattr(widget, 'text'):
                current_text = widget.text()
                print(f"  FCAL_M2: 当前文本='{current_text}'")
            else:
                print(f"  FCAL_M2: 未知控件类型 {type(widget).__name__}")
        else:
            print("  ❌ FCAL_M2控件不存在")

        # 检查FSM_DIV控件
        if hasattr(modern_handler.ui, "FSMDIV"):
            widget = modern_handler.ui.FSMDIV
            if hasattr(widget, 'currentText'):
                current_text = widget.currentText()
                current_index = widget.currentIndex()
                item_count = widget.count()
                print(f"  FSM_DIV: 当前值='{current_text}', 索引={current_index}, 总选项={item_count}")
            else:
                print(f"  FSM_DIV: 未知控件类型 {type(widget).__name__}")
        else:
            print("  ❌ FSM_DIV控件不存在")

        # 检查寄存器映射
        print("\n2. 检查寄存器映射...")
        calibration_widgets = ["FCALEN", "FCALM1", "FCALM2"]
        
        for widget_name in calibration_widgets:
            if widget_name in modern_handler.widget_register_map:
                mapping = modern_handler.widget_register_map[widget_name]
                print(f"  ✓ {widget_name}: 寄存器={mapping['register_addr']}, 类型={mapping['widget_type']}")
            else:
                print(f"  ❌ {widget_name}: 未在映射表中找到")

        # 检查寄存器默认值
        print("\n3. 检查寄存器默认值...")
        
        # 检查0xA4寄存器（FCAL_EN和FCAL_M1）
        if register_manager.has_register("0xA4"):
            reg_0xa4 = register_manager.get_register("0xA4")
            if reg_0xa4:
                fcal_en_value = reg_0xa4.get_bit_field_value("RG_PLL2_FCAL_EN")
                fcal_m1_value = reg_0xa4.get_bit_field_value("RG_PLL2_FCAL_M1[7:0]")
                print(f"  寄存器0xA4: FCAL_EN={fcal_en_value}, FCAL_M1={fcal_m1_value}")
            else:
                print("  ❌ 无法获取寄存器0xA4")
        else:
            print("  ❌ 寄存器0xA4不存在")
            
        # 检查0xA5寄存器（FCAL_M2）
        if register_manager.has_register("0xA5"):
            reg_0xa5 = register_manager.get_register("0xA5")
            if reg_0xa5:
                fcal_m2_value = reg_0xa5.get_bit_field_value("RG_PLL2_FCAL_M2[13:0]")
                print(f"  寄存器0xA5: FCAL_M2={fcal_m2_value}")
            else:
                print("  ❌ 无法获取寄存器0xA5")
        else:
            print("  ❌ 寄存器0xA5不存在")

        print("\n" + "=" * 60)
        print("测试完成")
        
        return True

    except Exception as e:
        print(f"❌ 测试过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_pll2_calibration()
    
    if success:
        print("\n🎉 PLL2 Calibration控件测试完成！")
        sys.exit(0)
    else:
        print("\n❌ PLL2 Calibration控件测试失败！")
        sys.exit(1)
