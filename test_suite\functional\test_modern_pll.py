#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试现代化PLL处理器
验证ModernPLLHandler是否正常工作
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_modern_pll():
    """测试现代化PLL处理器"""
    try:
        print("=" * 60)
        print("测试现代化PLL处理器")
        print("=" * 60)
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 导入必要的模块
        from core.services.register.RegisterManager import RegisterManager
        from ui.handlers.ModernPLLHandler import ModernPLLHandler
        import json
        
        print("1. 加载寄存器配置...")
        # 加载寄存器配置
        config_path = os.path.join(os.path.dirname(__file__), 'lib', 'register.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        print(f"   ✓ 加载了 {len(registers_config)} 个寄存器配置")
        
        print("2. 创建RegisterManager...")
        register_manager = RegisterManager(registers_config)
        print(f"   ✓ 创建了RegisterManager，包含 {len(register_manager.register_objects)} 个寄存器对象")
        
        print("3. 创建现代化PLL处理器...")
        try:
            modern_handler = ModernPLLHandler(None, register_manager)
            print("   ✓ 成功创建ModernPLLHandler")
            
            # 等待初始化完成
            import time
            time.sleep(0.2)
            
            # 检查控件映射
            print(f"   ✓ 构建了 {len(modern_handler.widget_register_map)} 个控件映射")
            
            # 显示部分控件映射
            pll_widgets = [name for name in modern_handler.widget_register_map.keys() if "PLL" in name]
            print(f"   PLL相关控件: {pll_widgets[:5]}...")  # 显示前5个
            
        except Exception as e:
            print(f"   ❌ 创建ModernPLLHandler失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
        print("4. 测试PLL状态获取...")
        try:
            initial_status = modern_handler.get_current_status()
            print(f"   初始状态: {initial_status}")
            
            # 验证状态字段
            expected_fields = ["pll1_enabled", "pll2_enabled", "vco_enabled", "current_clock_source"]
            for field in expected_fields:
                if field in initial_status:
                    print(f"   ✓ {field}: {initial_status[field]}")
                else:
                    print(f"   ❌ 缺少状态字段: {field}")
                    
        except Exception as e:
            print(f"   ❌ 状态获取出错: {str(e)}")
        
        print("5. 测试PLL预设功能...")
        try:
            # 测试默认预设
            print("   应用默认预设...")
            modern_handler.set_pll_preset("default")
            
            # 检查PLL状态
            pll1_pd = register_manager.get_bit_field_value("0x50", "PLL1_PD")
            pll2_pd = register_manager.get_bit_field_value("0x83", "PLL2_PD")
            vco_pd = register_manager.get_bit_field_value("0x50", "VCO_PD")
            
            print(f"   默认预设后:")
            print(f"     PLL1_PD: {pll1_pd} (期望: 0)")
            print(f"     PLL2_PD: {pll2_pd} (期望: 0)")
            print(f"     VCO_PD: {vco_pd} (期望: 0)")
            
            if pll1_pd == 0 and pll2_pd == 0 and vco_pd == 0:
                print("   ✓ 默认预设应用成功")
            else:
                print("   ❌ 默认预设应用失败")
            
            # 测试低功耗预设
            print("   应用低功耗预设...")
            modern_handler.set_pll_preset("low_power")
            
            pll1_pd = register_manager.get_bit_field_value("0x50", "PLL1_PD")
            pll2_pd = register_manager.get_bit_field_value("0x83", "PLL2_PD")
            vco_pd = register_manager.get_bit_field_value("0x50", "VCO_PD")
            
            print(f"   低功耗预设后:")
            print(f"     PLL1_PD: {pll1_pd} (期望: 1)")
            print(f"     PLL2_PD: {pll2_pd} (期望: 1)")
            print(f"     VCO_PD: {vco_pd} (期望: 1)")
            
            if pll1_pd == 1 and pll2_pd == 1 and vco_pd == 1:
                print("   ✓ 低功耗预设应用成功")
            else:
                print("   ❌ 低功耗预设应用失败")
                
        except Exception as e:
            print(f"   ❌ PLL预设测试出错: {str(e)}")
        
        print("6. 测试时钟源更新...")
        try:
            # 测试时钟源更新
            modern_handler.update_clock_source("ClkIn1", 245.76, 2)
            
            # 检查更新结果
            current_source = modern_handler.current_clock_source
            current_freq = modern_handler.clkin_frequencies.get("ClkIn1", 0)
            current_div = modern_handler.clkin_divider_values.get("ClkIn1", 0)
            
            print(f"   时钟源更新后:")
            print(f"     当前时钟源: {current_source}")
            print(f"     ClkIn1频率: {current_freq}MHz")
            print(f"     ClkIn1分频: {current_div}")
            
            if current_source == "ClkIn1" and current_freq == 245.76 and current_div == 2:
                print("   ✓ 时钟源更新成功")
            else:
                print("   ❌ 时钟源更新失败")
                
        except Exception as e:
            print(f"   ❌ 时钟源更新测试出错: {str(e)}")
        
        print("7. 测试频率计算...")
        try:
            # 恢复默认预设以确保PLL开启
            modern_handler.set_pll_preset("default")
            
            # 触发频率计算
            modern_handler.calculate_output_frequencies()
            print("   ✓ 频率计算执行完成")
            
            # 检查频率显示控件是否存在
            freq_widgets = ["PLL1PFDFreq", "PLL2PFDFreq", "Fin0Freq"]
            for widget_name in freq_widgets:
                if hasattr(modern_handler.ui, widget_name):
                    widget = getattr(modern_handler.ui, widget_name)
                    if hasattr(widget, 'text'):
                        freq_text = widget.text()
                        print(f"     {widget_name}: {freq_text}")
                    else:
                        print(f"     {widget_name}: 控件类型不支持text()")
                else:
                    print(f"     {widget_name}: 控件不存在")
                    
        except Exception as e:
            print(f"   ❌ 频率计算测试出错: {str(e)}")
        
        print("8. 测试控件值变化...")
        try:
            # 模拟PLL1电源控制变化
            if "PLL1PD" in modern_handler.widget_register_map:
                print("   模拟PLL1PD控件变化...")
                modern_handler._on_widget_changed("PLL1PD", True)
                print("   ✓ PLL1PD控件值变化处理成功")
            else:
                print("   PLL1PD控件未映射，跳过测试")
            
            # 模拟分频器变化
            if "PLL1RDividerSetting" in modern_handler.widget_register_map:
                print("   模拟PLL1RDividerSetting控件变化...")
                modern_handler._on_widget_changed("PLL1RDividerSetting", 2)
                print("   ✓ PLL1RDividerSetting控件值变化处理成功")
            else:
                print("   PLL1RDividerSetting控件未映射，跳过测试")
                
        except Exception as e:
            print(f"   ❌ 控件值变化测试出错: {str(e)}")
        
        print("9. 测试最终状态...")
        try:
            final_status = modern_handler.get_current_status()
            print(f"   最终状态: {final_status}")
            
            # 验证状态一致性
            if final_status.get("pll1_enabled") is not None:
                expected_pll1 = not register_manager.get_bit_field_value("0x50", "PLL1_PD")
                if final_status["pll1_enabled"] == expected_pll1:
                    print("   ✓ PLL1状态一致")
                else:
                    print("   ❌ PLL1状态不一致")
            
        except Exception as e:
            print(f"   ❌ 最终状态测试出错: {str(e)}")
        
        print("\n" + "=" * 60)
        print("🎉 现代化PLL处理器测试完成！")
        print("=" * 60)
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_modern_pll()
    sys.exit(0 if success else 1)
