#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试SpinBox控件的最小值设置
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_spinbox_minimum():
    """测试SpinBox控件的最小值设置"""
    try:
        from PyQt5.QtWidgets import QApplication
        
        # 创建QApplication
        app = QApplication(sys.argv)
        
        logger.info("=== 测试SpinBox控件最小值设置 ===")
        
        # 创建测试实例
        from ui.handlers.ModernClkinControlHandler import ModernClkinControlHandler
        handler = ModernClkinControlHandler.create_for_testing()
        
        # 检查分频比控件的最小值设置
        divider_controls = ["PLL1R0Div", "PLL1R1Div", "PLL1R2Div"]
        
        for control_name in divider_controls:
            if hasattr(handler.ui, control_name):
                control = getattr(handler.ui, control_name)
                
                logger.info(f"\n--- 控件 {control_name} ---")
                logger.info(f"最小值: {control.minimum()}")
                logger.info(f"最大值: {control.maximum()}")
                logger.info(f"当前值: {control.value()}")
                
                # 检查最小值是否合理
                if control.minimum() > 120:
                    logger.error(f"✗ {control_name} 最小值({control.minimum()})大于120，这会阻止设置120")
                elif control.minimum() > 99:
                    logger.warning(f"⚠ {control_name} 最小值({control.minimum()})大于99，这可能是问题原因")
                else:
                    logger.info(f"✓ {control_name} 最小值({control.minimum()})合理")
                
                # 尝试手动设置最小值为1
                logger.info(f"尝试设置最小值为1...")
                control.setMinimum(1)
                logger.info(f"设置后最小值: {control.minimum()}")
                
                # 再次尝试设置120
                logger.info(f"再次尝试设置值为120...")
                control.setValue(120)
                new_value = control.value()
                logger.info(f"设置后的值: {new_value}")
                
                if new_value == 120:
                    logger.info(f"✓ 设置最小值后，{control_name} 可以设置为120")
                else:
                    logger.error(f"✗ 即使设置最小值后，{control_name} 仍无法设置为120！实际值: {new_value}")
                    
            else:
                logger.warning(f"控件 {control_name} 不存在")
        
        logger.info("\n=== 测试完成 ===")
        
    except Exception as e:
        logger.error(f"测试时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_spinbox_minimum()
