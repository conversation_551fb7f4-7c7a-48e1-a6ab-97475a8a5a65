#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
FSJ04832寄存器配置工具 - 集成通信测试
测试模块间通信、数据流、事件传递等集成功能
"""

import sys
import os
import unittest
import time
from unittest.mock import Mock, patch, MagicMock
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from PyQt5.QtTest import QTest

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from test_config import TestConfig
from test_utils import TestUtils

class IntegrationCommunicationTest(unittest.TestCase):
    """集成通信测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        cls.app = TestUtils.get_test_app()
        cls.test_config = TestConfig()
        
    def setUp(self):
        """每个测试方法前的初始化"""
        self.mock_components = {}
        
    def tearDown(self):
        """每个测试方法后的清理"""
        self.mock_components.clear()
        
    def test_01_register_manager_to_event_bus(self):
        """测试寄存器管理器到事件总线的通信"""
        print("\n=== 测试寄存器管理器到事件总线的通信 ===")
        
        try:
            from core.services.register.RegisterManager import RegisterManager
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            
            # 创建测试组件
            mock_registers = {
                "0x40": {"name": "TEST_REG", "value": 0x0000, "bits": {}}
            }
            register_manager = RegisterManager(mock_registers)
            event_bus = RegisterUpdateBus.instance()
            
            # 设置信号接收器
            received_updates = []
            def update_handler(addr, value):
                received_updates.append((addr, value))
                
            event_bus.register_updated.connect(update_handler)
            
            # 通过寄存器管理器更新值
            register_manager.set_register_value("0x40", 0x1234)
            
            # 手动发送事件总线信号（因为RegisterManager不直接发送）
            event_bus.emit_register_updated("0x40", 0x1234)
            
            # 处理事件循环
            QTest.qWait(100)
            
            # 验证通信
            self.assertEqual(len(received_updates), 1)
            self.assertEqual(received_updates[0], ("0x40", 0x1234))
            
            print("✅ 寄存器管理器到事件总线通信测试通过")
            
        except Exception as e:
            print(f"❌ 寄存器管理器到事件总线通信测试失败: {str(e)}")
            raise
            
    def test_02_event_bus_to_ui_handlers(self):
        """测试事件总线到UI处理器的通信"""
        print("\n=== 测试事件总线到UI处理器的通信 ===")
        
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            
            event_bus = RegisterUpdateBus.instance()
            
            # 模拟UI处理器
            class MockUIHandler(QObject):
                def __init__(self):
                    super().__init__()
                    self.received_data = []
                    
                def handle_register_update(self, addr, value):
                    self.received_data.append(('register', addr, value))
                    
                def handle_clock_update(self, source, freq, div):
                    self.received_data.append(('clock', source, freq, div))
                    
            ui_handler = MockUIHandler()
            
            # 连接信号
            event_bus.register_updated.connect(ui_handler.handle_register_update)
            event_bus.clock_source_selected.connect(ui_handler.handle_clock_update)
            
            # 发送测试信号
            event_bus.emit_register_updated("0x50", 0x5678)
            event_bus.emit_clock_source_selected("CLKin1", 100.0, 2)
            
            # 处理事件循环
            QTest.qWait(100)
            
            # 验证接收
            self.assertEqual(len(ui_handler.received_data), 2)
            self.assertEqual(ui_handler.received_data[0], ('register', "0x50", 0x5678))
            self.assertEqual(ui_handler.received_data[1], ('clock', "CLKin1", 100.0, 2))
            
            print("✅ 事件总线到UI处理器通信测试通过")
            
        except Exception as e:
            print(f"❌ 事件总线到UI处理器通信测试失败: {str(e)}")
            raise
            
    def test_03_plugin_to_main_window_communication(self):
        """测试插件到主窗口的通信"""
        print("\n=== 测试插件到主窗口的通信 ===")
        
        try:
            # 模拟插件和主窗口通信
            class MockPlugin(QObject):
                data_ready = pyqtSignal(str, dict)
                
                def send_data(self, plugin_name, data):
                    self.data_ready.emit(plugin_name, data)
                    
            class MockMainWindow(QObject):
                def __init__(self):
                    super().__init__()
                    self.received_data = []
                    
                def handle_plugin_data(self, plugin_name, data):
                    self.received_data.append((plugin_name, data))
                    
            # 创建测试对象
            plugin = MockPlugin()
            main_window = MockMainWindow()
            
            # 连接信号
            plugin.data_ready.connect(main_window.handle_plugin_data)
            
            # 发送测试数据
            test_data = {"frequency": 122.88, "divider": 1}
            plugin.send_data("PLL_Plugin", test_data)
            
            # 处理事件循环
            QTest.qWait(100)
            
            # 验证通信
            self.assertEqual(len(main_window.received_data), 1)
            self.assertEqual(main_window.received_data[0][0], "PLL_Plugin")
            self.assertEqual(main_window.received_data[0][1], test_data)
            
            print("✅ 插件到主窗口通信测试通过")
            
        except Exception as e:
            print(f"❌ 插件到主窗口通信测试失败: {str(e)}")
            raise
            
    def test_04_cross_window_synchronization(self):
        """测试跨窗口同步"""
        print("\n=== 测试跨窗口同步 ===")
        
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            
            event_bus = RegisterUpdateBus.instance()
            
            # 模拟两个工具窗口
            class MockToolWindow(QObject):
                def __init__(self, name):
                    super().__init__()
                    self.name = name
                    self.synchronized_data = {}
                    
                def handle_sync_data(self, key, value):
                    self.synchronized_data[key] = value
                    
            window1 = MockToolWindow("PLL_Window")
            window2 = MockToolWindow("Clock_Window")
            
            # 连接同步信号
            event_bus.pll1_pfd_freq_updated.connect(
                lambda freq: window1.handle_sync_data("pll1_pfd", freq)
            )
            event_bus.pll1_pfd_freq_updated.connect(
                lambda freq: window2.handle_sync_data("pll1_pfd", freq)
            )
            
            # 发送同步数据
            event_bus.pll1_pfd_freq_updated.emit(122.88)
            
            # 处理事件循环
            QTest.qWait(100)
            
            # 验证同步
            self.assertEqual(window1.synchronized_data.get("pll1_pfd"), 122.88)
            self.assertEqual(window2.synchronized_data.get("pll1_pfd"), 122.88)
            
            print("✅ 跨窗口同步测试通过")
            
        except Exception as e:
            print(f"❌ 跨窗口同步测试失败: {str(e)}")
            raise
            
    def test_05_data_flow_validation(self):
        """测试数据流验证"""
        print("\n=== 测试数据流验证 ===")
        
        try:
            # 模拟完整的数据流：输入 -> 处理 -> 输出
            class DataFlowTest:
                def __init__(self):
                    self.input_data = None
                    self.processed_data = None
                    self.output_data = None
                    
                def input_stage(self, data):
                    self.input_data = data
                    return self.process_stage(data)
                    
                def process_stage(self, data):
                    # 模拟数据处理（频率计算）
                    if isinstance(data, dict) and 'frequency' in data:
                        processed = {
                            'input_freq': data['frequency'],
                            'output_freq': data['frequency'] / data.get('divider', 1)
                        }
                        self.processed_data = processed
                        return self.output_stage(processed)
                    return None
                    
                def output_stage(self, data):
                    self.output_data = data
                    return data
                    
            # 执行数据流测试
            flow_test = DataFlowTest()
            input_data = {'frequency': 122.88, 'divider': 2}
            
            result = flow_test.input_stage(input_data)
            
            # 验证数据流
            self.assertEqual(flow_test.input_data, input_data)
            self.assertIsNotNone(flow_test.processed_data)
            self.assertEqual(flow_test.output_data['output_freq'], 61.44)
            
            print("✅ 数据流验证测试通过")
            print(f"   输入: {flow_test.input_data}")
            print(f"   输出: {flow_test.output_data}")
            
        except Exception as e:
            print(f"❌ 数据流验证测试失败: {str(e)}")
            raise
            
    def test_06_error_propagation(self):
        """测试错误传播机制"""
        print("\n=== 测试错误传播机制 ===")
        
        try:
            # 模拟错误传播链
            class ErrorPropagationTest(QObject):
                error_occurred = pyqtSignal(str, str)  # component, error_message
                
                def __init__(self):
                    super().__init__()
                    self.error_log = []
                    
                def trigger_error(self, component, error_msg):
                    self.error_occurred.emit(component, error_msg)
                    
                def handle_error(self, component, error_msg):
                    self.error_log.append((component, error_msg))
                    
            error_test = ErrorPropagationTest()
            error_test.error_occurred.connect(error_test.handle_error)
            
            # 触发测试错误
            error_test.trigger_error("RegisterManager", "Invalid register address")
            error_test.trigger_error("PLLController", "Frequency out of range")
            
            # 处理事件循环
            QTest.qWait(100)
            
            # 验证错误传播
            self.assertEqual(len(error_test.error_log), 2)
            self.assertEqual(error_test.error_log[0][0], "RegisterManager")
            self.assertEqual(error_test.error_log[1][0], "PLLController")
            
            print("✅ 错误传播机制测试通过")
            print(f"   捕获错误数: {len(error_test.error_log)}")
            
        except Exception as e:
            print(f"❌ 错误传播机制测试失败: {str(e)}")
            raise
            
    def test_07_configuration_synchronization(self):
        """测试配置同步"""
        print("\n=== 测试配置同步 ===")
        
        try:
            # 模拟配置同步机制
            class ConfigSyncTest:
                def __init__(self):
                    self.config_cache = {}
                    self.subscribers = []
                    
                def update_config(self, key, value):
                    old_value = self.config_cache.get(key)
                    self.config_cache[key] = value
                    
                    # 通知订阅者
                    for subscriber in self.subscribers:
                        subscriber(key, value, old_value)
                        
                def subscribe(self, callback):
                    self.subscribers.append(callback)
                    
            # 创建配置同步测试
            config_sync = ConfigSyncTest()
            
            # 模拟订阅者
            notifications = []
            def config_subscriber(key, new_value, old_value):
                notifications.append((key, new_value, old_value))
                
            config_sync.subscribe(config_subscriber)
            
            # 更新配置
            config_sync.update_config("simulation_mode", True)
            config_sync.update_config("auto_write", False)
            
            # 验证同步
            self.assertEqual(len(notifications), 2)
            self.assertEqual(notifications[0][0], "simulation_mode")
            self.assertEqual(notifications[0][1], True)
            
            print("✅ 配置同步测试通过")
            print(f"   同步通知数: {len(notifications)}")
            
        except Exception as e:
            print(f"❌ 配置同步测试失败: {str(e)}")
            raise
            
    def test_08_performance_under_load(self):
        """测试负载下的性能"""
        print("\n=== 测试负载下的性能 ===")
        
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            
            event_bus = RegisterUpdateBus.instance()
            
            # 性能测试参数
            num_signals = 1000
            received_count = 0
            
            def signal_handler(addr, value):
                nonlocal received_count
                received_count += 1
                
            event_bus.register_updated.connect(signal_handler)
            
            # 记录开始时间
            start_time = time.time()
            
            # 发送大量信号
            for i in range(num_signals):
                event_bus.emit_register_updated(f"0x{i:02X}", i)
                
            # 处理事件循环
            QTest.qWait(500)
            
            # 记录结束时间
            end_time = time.time()
            duration = end_time - start_time
            
            # 验证性能
            self.assertEqual(received_count, num_signals)
            self.assertLess(duration, 2.0)  # 应该在2秒内完成
            
            print("✅ 负载下的性能测试通过")
            print(f"   处理信号数: {received_count}")
            print(f"   耗时: {duration:.3f}秒")
            print(f"   平均速度: {num_signals/duration:.0f}信号/秒")
            
        except Exception as e:
            print(f"❌ 负载下的性能测试失败: {str(e)}")
            raise

def run_integration_communication_tests():
    """运行集成通信测试"""
    print("🔗 开始FSJ04832寄存器配置工具集成通信测试")
    print("=" * 60)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(IntegrationCommunicationTest)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果摘要
    print("\n" + "=" * 60)
    print("📊 集成通信测试结果摘要:")
    print(f"   总测试数: {result.testsRun}")
    print(f"   成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"   失败: {len(result.failures)}")
    print(f"   错误: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"   - {test}")
            
    if result.errors:
        print("\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"   - {test}")
            
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\n🎯 测试成功率: {success_rate:.1f}%")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_integration_communication_tests()
    sys.exit(0 if success else 1)
