# -*- mode: python ; coding: utf-8 -*-

added_files = [
    ('config', 'config'),
    ('images', 'images'),
    ('lib', 'lib'),
    ('ui/forms', 'ui/forms'),
    ('ui/resources', 'ui/resources'),
]

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtWidgets',
        'PyQt5.QtGui',
        'serial',
        'serial.tools.list_ports',
        'core.event_bus',
        'core.services.spi.spi_service',
        'core.services.spi.spi_service_impl',
        'core.services.spi.port_manager',
        'core.services.register.RegisterOperationService',
        'core.services.version.VersionService',
        'ui.handlers.ModernRegisterIOHandler',
        'ui.handlers.ModernRegisterTableHandler',
        'ui.handlers.ModernRegisterTreeHandler',
        'ui.handlers.ModernBaseHandler',
        'ui.managers.InitializationManager',
        'ui.managers.StatusAndConfigManager',
        'ui.managers.SPISignalManager',
        'ui.managers.RegisterDisplayManager',
        'utils.Log',
        'utils.configFileHandler'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    noarchive=False
)

pyz = PYZ(a.pure, a.zipped_data)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    name='FSJConfigTool1.0.0.2',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    icon='images/logo.ico',
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None
)
