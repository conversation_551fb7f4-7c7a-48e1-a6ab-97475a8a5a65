#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
工具栏工具修复脚本
专门用于修复工具栏中缺失的核心工具问题
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QPushButton, QTextEdit, QLabel
from PyQt5.QtCore import QTimer
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class ToolbarFixWindow(QWidget):
    """工具栏修复窗口"""
    
    def __init__(self):
        super().__init__()
        self.main_window = None
        self.plugin_service = None
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("工具栏工具修复")
        self.setGeometry(100, 100, 800, 600)
        
        layout = QVBoxLayout()
        
        # 状态标签
        self.status_label = QLabel("准备修复工具栏...")
        layout.addWidget(self.status_label)
        
        # 修复按钮
        self.find_main_window_btn = QPushButton("1. 查找主窗口")
        self.find_main_window_btn.clicked.connect(self.find_main_window)
        layout.addWidget(self.find_main_window_btn)
        
        self.check_services_btn = QPushButton("2. 检查服务状态")
        self.check_services_btn.clicked.connect(self.check_services)
        layout.addWidget(self.check_services_btn)
        
        self.check_plugins_btn = QPushButton("3. 检查插件状态")
        self.check_plugins_btn.clicked.connect(self.check_plugins)
        layout.addWidget(self.check_plugins_btn)
        
        self.check_toolbar_btn = QPushButton("4. 检查工具栏状态")
        self.check_toolbar_btn.clicked.connect(self.check_toolbar)
        layout.addWidget(self.check_toolbar_btn)
        
        self.force_integrate_btn = QPushButton("5. 强制集成工具到工具栏")
        self.force_integrate_btn.clicked.connect(self.force_integrate_tools)
        layout.addWidget(self.force_integrate_btn)
        
        self.verify_fix_btn = QPushButton("6. 验证修复结果")
        self.verify_fix_btn.clicked.connect(self.verify_fix)
        layout.addWidget(self.verify_fix_btn)
        
        # 日志文本框
        self.log_text = QTextEdit()
        layout.addWidget(self.log_text)
        
        self.setLayout(layout)
        
    def find_main_window(self):
        """查找主窗口"""
        try:
            self.log_text.append("🔍 查找主窗口...")
            
            app = QApplication.instance()
            for widget in app.topLevelWidgets():
                if hasattr(widget, 'plugin_integration_service') or hasattr(widget, 'plugin_service'):
                    self.main_window = widget
                    break
            
            if self.main_window:
                self.status_label.setText("✅ 找到主窗口")
                self.log_text.append("✅ 主窗口已找到")
                self.log_text.append(f"   - 窗口类型: {type(self.main_window).__name__}")
                self.log_text.append(f"   - 窗口标题: {self.main_window.windowTitle()}")
            else:
                self.status_label.setText("❌ 未找到主窗口")
                self.log_text.append("❌ 未找到主窗口")
                
        except Exception as e:
            error_msg = f"查找主窗口失败: {str(e)}"
            self.status_label.setText(error_msg)
            self.log_text.append(f"❌ {error_msg}")
            
    def check_services(self):
        """检查服务状态"""
        try:
            if not self.main_window:
                self.log_text.append("❌ 主窗口未找到")
                return
            
            self.log_text.append("🔍 检查服务状态...")
            
            # 检查插件集成服务
            if hasattr(self.main_window, 'plugin_integration_service'):
                self.plugin_service = self.main_window.plugin_integration_service
                self.log_text.append("✅ 找到 plugin_integration_service")
            elif hasattr(self.main_window, 'plugin_service'):
                self.plugin_service = self.main_window.plugin_service
                self.log_text.append("✅ 找到 plugin_service")
            else:
                self.log_text.append("❌ 未找到插件服务")
                return
            
            # 检查子服务
            services_status = []
            if hasattr(self.plugin_service, 'window_service'):
                services_status.append("窗口服务: ✅")
            else:
                services_status.append("窗口服务: ❌")
                
            if hasattr(self.plugin_service, 'dock_service'):
                services_status.append("停靠服务: ✅")
            else:
                services_status.append("停靠服务: ❌")
                
            if hasattr(self.plugin_service, 'menu_service'):
                services_status.append("菜单服务: ✅")
            else:
                services_status.append("菜单服务: ❌")
            
            for status in services_status:
                self.log_text.append(f"   - {status}")
            
            # 检查UI管理器
            if hasattr(self.main_window, 'ui_manager'):
                self.log_text.append("✅ 找到 ui_manager")
                if hasattr(self.main_window.ui_manager, 'menu_manager'):
                    self.log_text.append("✅ 找到 menu_manager")
                else:
                    self.log_text.append("❌ 未找到 menu_manager")
            else:
                self.log_text.append("❌ 未找到 ui_manager")
            
        except Exception as e:
            self.log_text.append(f"❌ 检查服务状态失败: {str(e)}")
            
    def check_plugins(self):
        """检查插件状态"""
        try:
            self.log_text.append("🔍 检查插件状态...")
            
            # 获取插件管理器
            from core.services.plugin.PluginManager import plugin_manager
            
            # 获取所有插件
            all_plugins = plugin_manager.get_plugin_list()
            tool_plugins = plugin_manager.get_tool_window_plugins()
            
            self.log_text.append(f"📊 插件统计:")
            self.log_text.append(f"   - 总插件数: {len(all_plugins)}")
            self.log_text.append(f"   - 工具窗口插件数: {len(tool_plugins)}")
            
            # 检查核心工具插件
            core_tool_names = {'模式设置', '时钟输入控制', 'PLL控制', '同步系统参考', '时钟输出'}
            found_core_tools = []
            
            for plugin in tool_plugins:
                if plugin.name in core_tool_names:
                    found_core_tools.append(plugin)
                    self.log_text.append(f"   ✅ {plugin.name}")
            
            missing_tools = core_tool_names - {p.name for p in found_core_tools}
            if missing_tools:
                for tool_name in missing_tools:
                    self.log_text.append(f"   ❌ {tool_name} (插件缺失)")
            
            # 存储找到的核心工具插件
            self.core_tool_plugins = found_core_tools
            
        except Exception as e:
            self.log_text.append(f"❌ 检查插件状态失败: {str(e)}")
            
    def check_toolbar(self):
        """检查工具栏状态"""
        try:
            if not self.main_window:
                self.log_text.append("❌ 主窗口未找到")
                return
            
            self.log_text.append("🔍 检查工具栏状态...")
            
            # 查找工具栏
            from PyQt5.QtWidgets import QToolBar
            toolbars = self.main_window.findChildren(QToolBar)
            
            self.log_text.append(f"📊 工具栏统计:")
            self.log_text.append(f"   - 工具栏数量: {len(toolbars)}")
            
            core_tool_names = {'模式设置', '时钟输入控制', 'PLL控制', '同步系统参考', '时钟输出'}
            found_in_toolbar = set()
            
            for i, toolbar in enumerate(toolbars):
                actions = toolbar.actions()
                self.log_text.append(f"   - 工具栏 {i+1}: {toolbar.windowTitle()}")
                self.log_text.append(f"     动作数量: {len(actions)}")
                
                for action in actions:
                    if not action.isSeparator():
                        text = action.text()
                        if text in core_tool_names:
                            found_in_toolbar.add(text)
                            self.log_text.append(f"     ✅ {text}")
            
            # 总结
            missing_in_toolbar = core_tool_names - found_in_toolbar
            self.log_text.append(f"📊 工具栏中的核心工具:")
            self.log_text.append(f"   - 已存在: {len(found_in_toolbar)}/5")
            self.log_text.append(f"   - 缺失: {len(missing_in_toolbar)}/5")
            
            if missing_in_toolbar:
                for tool_name in missing_in_toolbar:
                    self.log_text.append(f"     ❌ {tool_name}")
            
        except Exception as e:
            self.log_text.append(f"❌ 检查工具栏状态失败: {str(e)}")
            
    def force_integrate_tools(self):
        """强制集成工具到工具栏"""
        try:
            if not self.main_window or not self.plugin_service:
                self.log_text.append("❌ 主窗口或插件服务未找到")
                return
            
            if not hasattr(self, 'core_tool_plugins'):
                self.log_text.append("❌ 请先检查插件状态")
                return
            
            self.log_text.append("🔧 强制集成工具到工具栏...")
            
            # 获取或创建工具菜单
            menubar = self.main_window.menuBar()
            tools_menu = None
            for action in menubar.actions():
                if action.menu() and "工具" in action.text():
                    tools_menu = action.menu()
                    break
            
            if not tools_menu:
                self.log_text.append("❌ 未找到工具菜单")
                return
            
            # 清除现有的核心工具动作
            core_tool_names = {'模式设置', '时钟输入控制', 'PLL控制', '同步系统参考', '时钟输出'}
            actions_to_remove = []
            for action in tools_menu.actions():
                if action.text() in core_tool_names:
                    actions_to_remove.append(action)
            
            for action in actions_to_remove:
                tools_menu.removeAction(action)
                self.log_text.append(f"   移除现有动作: {action.text()}")
            
            # 重新添加核心工具插件
            core_actions = []
            for plugin in self.core_tool_plugins:
                try:
                    # 使用插件服务添加到菜单
                    if hasattr(self.plugin_service, 'menu_service') and self.plugin_service.menu_service:
                        action = self.plugin_service.menu_service._add_plugin_to_menu(plugin, tools_menu)
                    else:
                        action = self.plugin_service._add_plugin_to_menu(plugin, tools_menu)
                    
                    if action:
                        core_actions.append(action)
                        self.log_text.append(f"   ✅ 添加到菜单: {plugin.name}")
                    else:
                        self.log_text.append(f"   ❌ 添加到菜单失败: {plugin.name}")
                        
                except Exception as e:
                    self.log_text.append(f"   ❌ 添加插件失败 {plugin.name}: {str(e)}")
            
            # 添加到工具栏
            if core_actions and hasattr(self.main_window, 'ui_manager'):
                try:
                    menu_manager = self.main_window.ui_manager.menu_manager
                    menu_manager._add_actions_to_toolbar(core_actions)
                    self.log_text.append(f"✅ 已将 {len(core_actions)} 个动作添加到工具栏")
                except Exception as e:
                    self.log_text.append(f"❌ 添加到工具栏失败: {str(e)}")
            
            self.status_label.setText("强制集成完成")
            
        except Exception as e:
            error_msg = f"强制集成失败: {str(e)}"
            self.status_label.setText(error_msg)
            self.log_text.append(f"❌ {error_msg}")
            
    def verify_fix(self):
        """验证修复结果"""
        try:
            self.log_text.append("🔍 验证修复结果...")
            
            # 重新检查工具栏
            self.check_toolbar()
            
            # 延迟检查，确保UI更新完成
            QTimer.singleShot(1000, self.final_verification)
            
        except Exception as e:
            self.log_text.append(f"❌ 验证修复结果失败: {str(e)}")
            
    def final_verification(self):
        """最终验证"""
        try:
            self.log_text.append("📊 最终验证结果:")
            
            # 检查工具栏中的核心工具
            from PyQt5.QtWidgets import QToolBar
            toolbars = self.main_window.findChildren(QToolBar)
            
            core_tool_names = {'模式设置', '时钟输入控制', 'PLL控制', '同步系统参考', '时钟输出'}
            found_in_toolbar = set()
            
            for toolbar in toolbars:
                for action in toolbar.actions():
                    if not action.isSeparator():
                        text = action.text()
                        if text in core_tool_names:
                            found_in_toolbar.add(text)
            
            success_count = len(found_in_toolbar)
            total_count = len(core_tool_names)
            
            if success_count == total_count:
                self.status_label.setText("✅ 修复成功！所有工具都在工具栏中")
                self.log_text.append("🎉 修复成功！所有核心工具都已添加到工具栏")
            else:
                self.status_label.setText(f"⚠️ 部分修复成功 ({success_count}/{total_count})")
                self.log_text.append(f"⚠️ 部分修复成功，{success_count}/{total_count} 个工具在工具栏中")
                
                missing = core_tool_names - found_in_toolbar
                for tool_name in missing:
                    self.log_text.append(f"   仍然缺失: {tool_name}")
            
        except Exception as e:
            self.log_text.append(f"❌ 最终验证失败: {str(e)}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建修复窗口
    fix_window = ToolbarFixWindow()
    fix_window.show()
    
    print("🔧 工具栏工具修复")
    print("请按照界面上的按钮顺序进行修复：")
    print("1. 查找主窗口")
    print("2. 检查服务状态")
    print("3. 检查插件状态")
    print("4. 检查工具栏状态")
    print("5. 强制集成工具到工具栏")
    print("6. 验证修复结果")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
