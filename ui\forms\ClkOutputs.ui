<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ClkOutputs</class>
 <widget class="QWidget" name="ClkOutputs">
  <property name="enabled">
   <bool>true</bool>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1336</width>
    <height>1512</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="autoFillBackground">
   <bool>false</bool>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <widget class="QLabel" name="label">
   <property name="geometry">
    <rect>
     <x>-24</x>
     <y>-18</y>
     <width>1361</width>
     <height>1560</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="autoFillBackground">
    <bool>true</bool>
   </property>
   <property name="styleSheet">
    <string notr="true"/>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="textFormat">
    <enum>Qt::RichText</enum>
   </property>
   <property name="pixmap">
    <pixmap resource="../qrc/clkoutput.qrc">:/clkoutputs/clockOutputs.bmp</pixmap>
   </property>
   <property name="scaledContents">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QLabel" name="labelFvco">
   <property name="geometry">
    <rect>
     <x>100</x>
     <y>75</y>
     <width>51</width>
     <height>20</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
     <pointsize>10</pointsize>
     <weight>50</weight>
     <bold>false</bold>
    </font>
   </property>
   <property name="text">
    <string>Fvco:</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEditFvco">
   <property name="geometry">
    <rect>
     <x>240</x>
     <y>70</y>
     <width>181</width>
     <height>31</height>
    </rect>
   </property>
   <property name="toolTip">
    <string>输入Fvco值用于计算输出频率</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
   <property name="placeholderText">
    <string>输入Fvco</string>
   </property>
  </widget>
  <widget class="QCheckBox" name="SysrefGBLPD">
   <property name="geometry">
    <rect>
     <x>255</x>
     <y>46</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SysrefCLR">
   <property name="geometry">
    <rect>
     <x>96</x>
     <y>46</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="CLKout0_1PD">
   <property name="geometry">
    <rect>
     <x>479</x>
     <y>48</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="CLKout2_3PD">
   <property name="geometry">
    <rect>
     <x>479</x>
     <y>74</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="CLKout4_5PD">
   <property name="geometry">
    <rect>
     <x>658</x>
     <y>48</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="CLKout6_7PD">
   <property name="geometry">
    <rect>
     <x>658</x>
     <y>73</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="CLKout8_9PD">
   <property name="geometry">
    <rect>
     <x>845</x>
     <y>48</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="CLKout10_11PD">
   <property name="geometry">
    <rect>
     <x>845</x>
     <y>73</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="CLKout12_13PD">
   <property name="geometry">
    <rect>
     <x>1041</x>
     <y>48</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK0_1PD">
   <property name="geometry">
    <rect>
     <x>90</x>
     <y>168</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK0_1DDLYPD">
   <property name="geometry">
    <rect>
     <x>250</x>
     <y>168</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DDLYd0EN">
   <property name="geometry">
    <rect>
     <x>446</x>
     <y>168</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="RGDCLK0_1HSEN">
   <property name="geometry">
    <rect>
     <x>610</x>
     <y>166</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK0_1HSTrig">
   <property name="geometry">
    <rect>
     <x>610</x>
     <y>186</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK0_1POL">
   <property name="geometry">
    <rect>
     <x>821</x>
     <y>172</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK0_1BYPASS">
   <property name="geometry">
    <rect>
     <x>821</x>
     <y>191</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SCLK0_1PD">
   <property name="geometry">
    <rect>
     <x>70</x>
     <y>263</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SCLK0_1HSTrig">
   <property name="geometry">
    <rect>
     <x>420</x>
     <y>273</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SYNCDIS0">
   <property name="geometry">
    <rect>
     <x>142</x>
     <y>190</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SCLK0_1ADLYEnb">
   <property name="geometry">
    <rect>
     <x>611</x>
     <y>271</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SCLK0_1POL">
   <property name="geometry">
    <rect>
     <x>790</x>
     <y>280</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="CLKout1_SRCMUX">
   <property name="geometry">
    <rect>
     <x>960</x>
     <y>280</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QSpinBox" name="DCLK0_1DIV">
   <property name="geometry">
    <rect>
     <x>90</x>
     <y>141</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QSpinBox" name="DCLK0_1DDLY">
   <property name="geometry">
    <rect>
     <x>260</x>
     <y>141</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="SCLK0_1DDLY">
   <property name="geometry">
    <rect>
     <x>239</x>
     <y>247</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="CLKout0FMT">
   <property name="geometry">
    <rect>
     <x>1130</x>
     <y>182</y>
     <width>161</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
     <pointsize>7</pointsize>
    </font>
   </property>
  </widget>
  <widget class="QComboBox" name="CLKout1FMT">
   <property name="geometry">
    <rect>
     <x>1130</x>
     <y>273</y>
     <width>161</width>
     <height>25</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
     <pointsize>8</pointsize>
    </font>
   </property>
  </widget>
  <widget class="QSpinBox" name="DCLK2_3DIV">
   <property name="geometry">
    <rect>
     <x>95</x>
     <y>340</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QSpinBox" name="DCLK2_3DDLY">
   <property name="geometry">
    <rect>
     <x>264</x>
     <y>340</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QSpinBox" name="DDLYdStepCNT_1">
   <property name="geometry">
    <rect>
     <x>440</x>
     <y>141</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QSpinBox" name="DDLYdStepCNT_2">
   <property name="geometry">
    <rect>
     <x>447</x>
     <y>341</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QSpinBox" name="DDLYdStepCNT_3">
   <property name="geometry">
    <rect>
     <x>445</x>
     <y>544</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QSpinBox" name="DDLYdStepCNT_4">
   <property name="geometry">
    <rect>
     <x>451</x>
     <y>744</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QSpinBox" name="DDLYdStepCNT_5">
   <property name="geometry">
    <rect>
     <x>450</x>
     <y>946</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QSpinBox" name="DDLYdStepCNT_6">
   <property name="geometry">
    <rect>
     <x>449</x>
     <y>1150</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QSpinBox" name="DDLYdStepCNT_7">
   <property name="geometry">
    <rect>
     <x>453</x>
     <y>1343</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QCheckBox" name="SYNCDIS2">
   <property name="geometry">
    <rect>
     <x>150</x>
     <y>390</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SYNCDIS4">
   <property name="geometry">
    <rect>
     <x>150</x>
     <y>593</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SYNCDIS6">
   <property name="geometry">
    <rect>
     <x>155</x>
     <y>794</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SYNCDIS8">
   <property name="geometry">
    <rect>
     <x>154</x>
     <y>997</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SYNCDIS10">
   <property name="geometry">
    <rect>
     <x>154</x>
     <y>1201</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SYNCDIS12">
   <property name="geometry">
    <rect>
     <x>157</x>
     <y>1393</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK2_3HSTrig">
   <property name="geometry">
    <rect>
     <x>612</x>
     <y>390</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="RGDCLK2_3HSEN">
   <property name="geometry">
    <rect>
     <x>612</x>
     <y>370</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK2_3BYPASS">
   <property name="geometry">
    <rect>
     <x>828</x>
     <y>390</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK2_3POL">
   <property name="geometry">
    <rect>
     <x>828</x>
     <y>371</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="CLKout3_SRCMUX">
   <property name="geometry">
    <rect>
     <x>960</x>
     <y>480</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SCLK2_3POL">
   <property name="geometry">
    <rect>
     <x>798</x>
     <y>480</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SCLK2_3ADLYEnb">
   <property name="geometry">
    <rect>
     <x>620</x>
     <y>470</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SCLK2_3HSTrig">
   <property name="geometry">
    <rect>
     <x>430</x>
     <y>474</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK2_3DDLYPD">
   <property name="geometry">
    <rect>
     <x>260</x>
     <y>368</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK2_3PD">
   <property name="geometry">
    <rect>
     <x>100</x>
     <y>367</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SCLK2_3PD">
   <property name="geometry">
    <rect>
     <x>71</x>
     <y>463</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QComboBox" name="CLKout2FMT">
   <property name="geometry">
    <rect>
     <x>1130</x>
     <y>380</y>
     <width>161</width>
     <height>31</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="CLKout3FMT">
   <property name="geometry">
    <rect>
     <x>1130</x>
     <y>473</y>
     <width>161</width>
     <height>31</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="CLKout4FMT">
   <property name="geometry">
    <rect>
     <x>1130</x>
     <y>586</y>
     <width>161</width>
     <height>31</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="CLKout5FMT">
   <property name="geometry">
    <rect>
     <x>1130</x>
     <y>677</y>
     <width>161</width>
     <height>31</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="CLKout7FMT">
   <property name="geometry">
    <rect>
     <x>1130</x>
     <y>877</y>
     <width>161</width>
     <height>31</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="CLKout6FMT">
   <property name="geometry">
    <rect>
     <x>1130</x>
     <y>784</y>
     <width>161</width>
     <height>31</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="CLKout9FMT">
   <property name="geometry">
    <rect>
     <x>1130</x>
     <y>1080</y>
     <width>161</width>
     <height>31</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="CLKout8FMT">
   <property name="geometry">
    <rect>
     <x>1128</x>
     <y>988</y>
     <width>161</width>
     <height>31</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="CLKout11FMT">
   <property name="geometry">
    <rect>
     <x>1130</x>
     <y>1284</y>
     <width>161</width>
     <height>31</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="CLKout10FMT">
   <property name="geometry">
    <rect>
     <x>1130</x>
     <y>1191</y>
     <width>161</width>
     <height>31</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="CLKout13FMT">
   <property name="geometry">
    <rect>
     <x>1130</x>
     <y>1476</y>
     <width>161</width>
     <height>31</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="CLKout12FMT">
   <property name="geometry">
    <rect>
     <x>1130</x>
     <y>1385</y>
     <width>161</width>
     <height>31</height>
    </rect>
   </property>
  </widget>
  <widget class="QCheckBox" name="DDLYd2EN">
   <property name="geometry">
    <rect>
     <x>450</x>
     <y>368</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QComboBox" name="SCLK2_3DDLY">
   <property name="geometry">
    <rect>
     <x>245</x>
     <y>447</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QCheckBox" name="CLKout5_SRCMUX">
   <property name="geometry">
    <rect>
     <x>960</x>
     <y>682</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK4_5BYPASS">
   <property name="geometry">
    <rect>
     <x>830</x>
     <y>594</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QSpinBox" name="DCLK4_5DIV">
   <property name="geometry">
    <rect>
     <x>93</x>
     <y>544</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QSpinBox" name="DCLK4_5DDLY">
   <property name="geometry">
    <rect>
     <x>264</x>
     <y>544</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK4_5PD">
   <property name="geometry">
    <rect>
     <x>98</x>
     <y>569</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DDLYd4EN">
   <property name="geometry">
    <rect>
     <x>450</x>
     <y>571</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SCLK4_5HSTrig">
   <property name="geometry">
    <rect>
     <x>429</x>
     <y>677</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK4_5DDLYPD">
   <property name="geometry">
    <rect>
     <x>258</x>
     <y>571</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK4_5HSTrig">
   <property name="geometry">
    <rect>
     <x>610</x>
     <y>590</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SCLK4_5ADLYEnb">
   <property name="geometry">
    <rect>
     <x>618</x>
     <y>674</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SCLK4_5PD">
   <property name="geometry">
    <rect>
     <x>70</x>
     <y>667</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="RGDCLK4_5HSEN">
   <property name="geometry">
    <rect>
     <x>610</x>
     <y>570</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK4_5POL">
   <property name="geometry">
    <rect>
     <x>830</x>
     <y>574</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SCLK4_5POL">
   <property name="geometry">
    <rect>
     <x>798</x>
     <y>683</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SCLK6_7PD">
   <property name="geometry">
    <rect>
     <x>80</x>
     <y>868</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK6_7PD">
   <property name="geometry">
    <rect>
     <x>100</x>
     <y>772</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SCLK6_7POL">
   <property name="geometry">
    <rect>
     <x>800</x>
     <y>884</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="RGDCLK6_7HSEN">
   <property name="geometry">
    <rect>
     <x>620</x>
     <y>771</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK6_7HSTrig">
   <property name="geometry">
    <rect>
     <x>620</x>
     <y>791</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK6_7BYPASS">
   <property name="geometry">
    <rect>
     <x>831</x>
     <y>797</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DDLYd6EN">
   <property name="geometry">
    <rect>
     <x>455</x>
     <y>772</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SCLK6_7HSTrig">
   <property name="geometry">
    <rect>
     <x>292</x>
     <y>878</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SCLK6_7ADLYEnb">
   <property name="geometry">
    <rect>
     <x>430</x>
     <y>877</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="CLKout7_SRCMUX">
   <property name="geometry">
    <rect>
     <x>960</x>
     <y>883</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QSpinBox" name="DCLK6_7DIV">
   <property name="geometry">
    <rect>
     <x>99</x>
     <y>744</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK6_7DDLYPD">
   <property name="geometry">
    <rect>
     <x>260</x>
     <y>772</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QSpinBox" name="DCLK6_7DDLY">
   <property name="geometry">
    <rect>
     <x>270</x>
     <y>744</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK6_7POL">
   <property name="geometry">
    <rect>
     <x>831</x>
     <y>777</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="CLKout9_SRCMUX">
   <property name="geometry">
    <rect>
     <x>960</x>
     <y>1086</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK8_9DDLYPD">
   <property name="geometry">
    <rect>
     <x>260</x>
     <y>975</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SCLK8_9POL">
   <property name="geometry">
    <rect>
     <x>800</x>
     <y>1087</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK8_9BYPASS">
   <property name="geometry">
    <rect>
     <x>830</x>
     <y>1001</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK8_9POL">
   <property name="geometry">
    <rect>
     <x>830</x>
     <y>981</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SCLK8_9PD">
   <property name="geometry">
    <rect>
     <x>78</x>
     <y>1071</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="RGDCLK8_9HSEN">
   <property name="geometry">
    <rect>
     <x>617</x>
     <y>974</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QSpinBox" name="DCLK8_9DDLY">
   <property name="geometry">
    <rect>
     <x>269</x>
     <y>947</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QCheckBox" name="DDLYd8EN">
   <property name="geometry">
    <rect>
     <x>452</x>
     <y>976</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QSpinBox" name="DCLK8_9DIV">
   <property name="geometry">
    <rect>
     <x>99</x>
     <y>947</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK8_9PD">
   <property name="geometry">
    <rect>
     <x>100</x>
     <y>974</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK8_9HSTrig">
   <property name="geometry">
    <rect>
     <x>617</x>
     <y>992</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SCLK8_9ADLYEnb">
   <property name="geometry">
    <rect>
     <x>620</x>
     <y>1080</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SCLK8_9HSTrig">
   <property name="geometry">
    <rect>
     <x>430</x>
     <y>1081</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="CLKout11_SRCMUX">
   <property name="geometry">
    <rect>
     <x>965</x>
     <y>1290</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK10_11DDLYPD">
   <property name="geometry">
    <rect>
     <x>257</x>
     <y>1178</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SCLK10_11ADLYEnb">
   <property name="geometry">
    <rect>
     <x>615</x>
     <y>1284</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="RGDCLK10_11HSEN">
   <property name="geometry">
    <rect>
     <x>607</x>
     <y>1178</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QSpinBox" name="DCLK10_11DIV">
   <property name="geometry">
    <rect>
     <x>97</x>
     <y>1151</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK10_11BYPASS">
   <property name="geometry">
    <rect>
     <x>821</x>
     <y>1205</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DDLYd10EN">
   <property name="geometry">
    <rect>
     <x>450</x>
     <y>1177</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SCLK10_11HSTrig">
   <property name="geometry">
    <rect>
     <x>430</x>
     <y>1285</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SCLK10_11POL">
   <property name="geometry">
    <rect>
     <x>798</x>
     <y>1289</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SCLK10_11PD">
   <property name="geometry">
    <rect>
     <x>75</x>
     <y>1275</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK10_11POL">
   <property name="geometry">
    <rect>
     <x>821</x>
     <y>1185</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK10_11HSTrig">
   <property name="geometry">
    <rect>
     <x>607</x>
     <y>1196</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QSpinBox" name="DCLK10_11DDLY">
   <property name="geometry">
    <rect>
     <x>267</x>
     <y>1151</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK10_11PD">
   <property name="geometry">
    <rect>
     <x>98</x>
     <y>1178</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QComboBox" name="SCLK4_5DDLY">
   <property name="geometry">
    <rect>
     <x>244</x>
     <y>650</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="SCLK6_7DDLY">
   <property name="geometry">
    <rect>
     <x>250</x>
     <y>850</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="SCLK8_9DDLY">
   <property name="geometry">
    <rect>
     <x>249</x>
     <y>1052</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="SCLK10_11DDLY">
   <property name="geometry">
    <rect>
     <x>248</x>
     <y>1257</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QCheckBox" name="CLKout13_SRCMUX">
   <property name="geometry">
    <rect>
     <x>960</x>
     <y>1480</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QSpinBox" name="DCLK12_13DIV">
   <property name="geometry">
    <rect>
     <x>102</x>
     <y>1343</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QCheckBox" name="SCLK12_13POL">
   <property name="geometry">
    <rect>
     <x>800</x>
     <y>1481</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK12_13BYPASS">
   <property name="geometry">
    <rect>
     <x>820</x>
     <y>1395</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK12_13DDLYPD">
   <property name="geometry">
    <rect>
     <x>260</x>
     <y>1368</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK12_13HSTrig">
   <property name="geometry">
    <rect>
     <x>611</x>
     <y>1386</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK12_13POL">
   <property name="geometry">
    <rect>
     <x>820</x>
     <y>1375</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SCLK12_13HSTrig">
   <property name="geometry">
    <rect>
     <x>430</x>
     <y>1477</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QSpinBox" name="DCLK12_13DDLY">
   <property name="geometry">
    <rect>
     <x>272</x>
     <y>1343</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QCheckBox" name="SCLK12_13ADLYEnb">
   <property name="geometry">
    <rect>
     <x>620</x>
     <y>1474</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DCLK12_13PD">
   <property name="geometry">
    <rect>
     <x>103</x>
     <y>1368</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="RGDCLK12_13HSEN">
   <property name="geometry">
    <rect>
     <x>611</x>
     <y>1368</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="DDLYd12EN">
   <property name="geometry">
    <rect>
     <x>458</x>
     <y>1369</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QComboBox" name="SCLK12_13DDLY">
   <property name="geometry">
    <rect>
     <x>251</x>
     <y>1449</y>
     <width>131</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QCheckBox" name="SCLK12_13PD">
   <property name="geometry">
    <rect>
     <x>80</x>
     <y>1465</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEditFout0Output">
   <property name="geometry">
    <rect>
     <x>1063</x>
     <y>128</y>
     <width>81</width>
     <height>21</height>
    </rect>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEditFout1Output">
   <property name="geometry">
    <rect>
     <x>1062</x>
     <y>218</y>
     <width>81</width>
     <height>21</height>
    </rect>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEditFout3Output">
   <property name="geometry">
    <rect>
     <x>1058</x>
     <y>419</y>
     <width>91</width>
     <height>20</height>
    </rect>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEditFout2Output">
   <property name="geometry">
    <rect>
     <x>1061</x>
     <y>329</y>
     <width>81</width>
     <height>20</height>
    </rect>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEditFout5Output">
   <property name="geometry">
    <rect>
     <x>1057</x>
     <y>623</y>
     <width>91</width>
     <height>20</height>
    </rect>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEditFout4Output">
   <property name="geometry">
    <rect>
     <x>1058</x>
     <y>533</y>
     <width>91</width>
     <height>20</height>
    </rect>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEditFout7Output">
   <property name="geometry">
    <rect>
     <x>1063</x>
     <y>819</y>
     <width>91</width>
     <height>24</height>
    </rect>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEditFout6Output">
   <property name="geometry">
    <rect>
     <x>1059</x>
     <y>732</y>
     <width>91</width>
     <height>20</height>
    </rect>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEditFout9Output">
   <property name="geometry">
    <rect>
     <x>1062</x>
     <y>1023</y>
     <width>91</width>
     <height>21</height>
    </rect>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEditFout8Output">
   <property name="geometry">
    <rect>
     <x>1058</x>
     <y>931</y>
     <width>91</width>
     <height>24</height>
    </rect>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEditFout11Output">
   <property name="geometry">
    <rect>
     <x>1061</x>
     <y>1227</y>
     <width>91</width>
     <height>21</height>
    </rect>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEditFout10Output">
   <property name="geometry">
    <rect>
     <x>1061</x>
     <y>1138</y>
     <width>91</width>
     <height>21</height>
    </rect>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEditFout13Output">
   <property name="geometry">
    <rect>
     <x>1065</x>
     <y>1421</y>
     <width>91</width>
     <height>21</height>
    </rect>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEditFout12Output">
   <property name="geometry">
    <rect>
     <x>1065</x>
     <y>1332</y>
     <width>91</width>
     <height>20</height>
    </rect>
   </property>
  </widget>
  <widget class="QCheckBox" name="CLKout0_SRCMUX">
   <property name="geometry">
    <rect>
     <x>960</x>
     <y>107</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="CLKout2_SRCMUX">
   <property name="geometry">
    <rect>
     <x>960</x>
     <y>304</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="CLKout4_SRCMUX">
   <property name="geometry">
    <rect>
     <x>960</x>
     <y>506</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="CLKout6_SRCMUX">
   <property name="geometry">
    <rect>
     <x>960</x>
     <y>710</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="CLKout8_SRCMUX">
   <property name="geometry">
    <rect>
     <x>960</x>
     <y>910</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="CLKout10_SRCMUX">
   <property name="geometry">
    <rect>
     <x>961</x>
     <y>1117</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="CLKout12_SRCMUX">
   <property name="geometry">
    <rect>
     <x>965</x>
     <y>1310</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 20px;  /* 设置方框宽度 */
                height: 20px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QSpinBox" name="SCLK0_1ADLY">
   <property name="geometry">
    <rect>
     <x>620</x>
     <y>237</y>
     <width>131</width>
     <height>30</height>
    </rect>
   </property>
  </widget>
  <widget class="QSpinBox" name="SCLK2_3ADLY">
   <property name="geometry">
    <rect>
     <x>626</x>
     <y>436</y>
     <width>131</width>
     <height>30</height>
    </rect>
   </property>
  </widget>
  <widget class="QSpinBox" name="SCLK8_9ADLY">
   <property name="geometry">
    <rect>
     <x>629</x>
     <y>1043</y>
     <width>131</width>
     <height>30</height>
    </rect>
   </property>
  </widget>
  <widget class="QSpinBox" name="SCLK10_11ADLY">
   <property name="geometry">
    <rect>
     <x>628</x>
     <y>1247</y>
     <width>131</width>
     <height>30</height>
    </rect>
   </property>
  </widget>
  <widget class="QSpinBox" name="SCLK12_13ADLY">
   <property name="geometry">
    <rect>
     <x>630</x>
     <y>1440</y>
     <width>141</width>
     <height>31</height>
    </rect>
   </property>
  </widget>
  <widget class="QSpinBox" name="SCLK4_5ADLY">
   <property name="geometry">
    <rect>
     <x>625</x>
     <y>640</y>
     <width>131</width>
     <height>31</height>
    </rect>
   </property>
  </widget>
  <widget class="QSpinBox" name="SCLK6_7ADLY">
   <property name="geometry">
    <rect>
     <x>630</x>
     <y>840</y>
     <width>131</width>
     <height>31</height>
    </rect>
   </property>
  </widget>
 </widget>
 <resources>
  <include location="../qrc/clkoutput.qrc"/>
 </resources>
 <connections/>
</ui>
