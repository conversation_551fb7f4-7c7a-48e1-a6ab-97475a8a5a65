#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试同步系统参考移除修复效果
验证所有引用都已更新为现代化版本
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication
from PyQt5.QtTest import QTest


def test_main_window_sync_sysref():
    """测试主窗口中的同步系统参考功能"""
    print("=" * 60)
    print("测试主窗口中的同步系统参考功能")
    print("=" * 60)
    
    try:
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        from ui.windows.RegisterMainWindow import RegisterMainWindow
        from core.services.spi.spi_service_impl import SPIServiceImpl
        from core.repositories.register_repository import RegisterRepository
        
        spi_service = SPIServiceImpl()
        spi_service.initialize()
        register_repo = RegisterRepository(spi_service)
        main_window = RegisterMainWindow(register_repo)
        
        print("✓ 主窗口创建成功")
        
        # 检查工厂类型
        factory_type = type(main_window.tool_window_factory).__name__
        print(f"✓ 工具窗口工厂类型: {factory_type}")
        
        if factory_type != "ModernToolWindowFactory":
            print("❌ 主窗口应该使用ModernToolWindowFactory")
            return False
        
        # 测试创建同步系统参考窗口
        print("\n测试创建同步系统参考窗口...")
        sync_window = main_window._show_sync_sysref_window()
        
        if sync_window:
            print("✓ 同步系统参考窗口创建成功")
            print(f"✓ 窗口类型: {type(sync_window).__name__}")
            
            # 验证是现代化处理器
            if 'Modern' in type(sync_window).__name__:
                print("✓ 确认使用现代化处理器")
            else:
                print("❌ 未使用现代化处理器")
                return False
            
            # 检查滚动区域
            if hasattr(sync_window, 'scroll_area'):
                print("✓ 滚动区域存在")
            else:
                print("❌ 滚动区域不存在")
                return False
                
            # 检查控件映射
            if hasattr(sync_window, 'widget_register_map'):
                mapping_count = len(sync_window.widget_register_map)
                print(f"✓ 控件映射: {mapping_count} 个控件")
            else:
                print("❌ 控件映射不存在")
                return False
            
            return True
        else:
            print("❌ 同步系统参考窗口创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_traditional_factory_compatibility():
    """测试传统工厂的兼容性"""
    print("\n" + "=" * 60)
    print("测试传统工厂的兼容性")
    print("=" * 60)
    
    try:
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建寄存器管理器
        from core.services.register.RegisterManager import RegisterManager
        
        # 加载寄存器配置
        config_path = os.path.join(project_root, 'lib', 'register.json')
        if not os.path.exists(config_path):
            print(f"❌ 寄存器配置文件不存在: {config_path}")
            return False
            
        import json
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        register_manager = RegisterManager(registers_config)
        
        # 创建一个模拟的主窗口对象
        class MockMainWindow:
            def __init__(self):
                self.register_manager = register_manager
        
        mock_main_window = MockMainWindow()
        
        # 测试传统工厂
        from ui.factories.ToolWindowFactory import ToolWindowFactory
        traditional_factory = ToolWindowFactory(mock_main_window)
        
        print("✓ 传统工厂创建成功")
        
        # 测试创建同步系统参考窗口
        print("\n测试传统工厂创建同步系统参考窗口...")
        try:
            sync_window = traditional_factory.create_sync_sysref_window()
            
            if sync_window:
                print("✓ 传统工厂成功创建同步系统参考窗口")
                print(f"✓ 窗口类型: {type(sync_window).__name__}")
                
                # 验证是现代化处理器
                if 'Modern' in type(sync_window).__name__:
                    print("✓ 传统工厂也使用现代化处理器")
                else:
                    print("❌ 传统工厂未使用现代化处理器")
                    return False
                
                return True
            else:
                print("❌ 传统工厂创建窗口失败")
                return False
                
        except Exception as e:
            print(f"❌ 传统工厂创建窗口时出错: {str(e)}")
            return False
            
    except Exception as e:
        print(f"❌ 传统工厂测试出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_import_verification():
    """测试导入验证"""
    print("\n" + "=" * 60)
    print("测试导入验证")
    print("=" * 60)
    
    try:
        # 1. 验证传统处理器无法导入
        print("1. 验证传统处理器无法导入...")
        try:
            from ui.handlers.SyncSysRefHandler import SyncSysRefHandler
            print("❌ 仍然可以导入传统处理器")
            return False
        except ImportError:
            print("✓ 传统处理器导入失败（预期行为）")
        except Exception as e:
            print(f"✓ 传统处理器导入出错（预期行为）: {str(e)}")
        
        # 2. 验证现代化处理器可以正常导入
        print("\n2. 验证现代化处理器可以正常导入...")
        try:
            from ui.handlers.ModernSyncSysRefHandler import ModernSyncSysRefHandler
            print("✓ 现代化处理器导入成功")
        except Exception as e:
            print(f"❌ 现代化处理器导入失败: {str(e)}")
            return False
        
        # 3. 验证工厂可以正常导入
        print("\n3. 验证工厂可以正常导入...")
        try:
            from ui.factories.ModernToolWindowFactory import ModernToolWindowFactory
            from ui.factories.ToolWindowFactory import ToolWindowFactory
            print("✓ 现代化工厂导入成功")
            print("✓ 传统工厂导入成功")
        except Exception as e:
            print(f"❌ 工厂导入失败: {str(e)}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 导入验证测试出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_file_removal_verification():
    """测试文件移除验证"""
    print("\n" + "=" * 60)
    print("测试文件移除验证")
    print("=" * 60)
    
    try:
        # 检查传统处理器文件是否已移除
        legacy_handler_path = os.path.join(project_root, 'ui', 'handlers', 'SyncSysRefHandler.py')
        
        if os.path.exists(legacy_handler_path):
            print("❌ 传统处理器文件仍然存在")
            return False
        else:
            print("✓ 传统处理器文件已成功移除")
        
        # 检查现代化处理器文件是否存在
        modern_handler_path = os.path.join(project_root, 'ui', 'handlers', 'ModernSyncSysRefHandler.py')
        
        if os.path.exists(modern_handler_path):
            print("✓ 现代化处理器文件存在")
        else:
            print("❌ 现代化处理器文件不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 文件移除验证出错: {str(e)}")
        return False


if __name__ == "__main__":
    # 测试文件移除验证
    success1 = test_file_removal_verification()
    
    # 测试导入验证
    success2 = test_import_verification()
    
    # 测试传统工厂兼容性
    success3 = test_traditional_factory_compatibility()
    
    # 测试主窗口功能
    success4 = test_main_window_sync_sysref()
    
    if success1 and success2 and success3 and success4:
        print("\n🎉 所有测试通过！同步系统参考移除修复成功！")
        print("📋 修复总结:")
        print("   - ✅ 传统处理器文件已移除")
        print("   - ✅ 传统处理器无法导入")
        print("   - ✅ 现代化处理器正常工作")
        print("   - ✅ 主窗口使用现代化工厂")
        print("   - ✅ 传统工厂兼容现代化处理器")
        print("   - ✅ 所有引用都已更新")
        print("\n🔧 修复的文件:")
        print("   - ui/windows/RegisterMainWindow.py")
        print("   - ui/factories/ToolWindowFactory.py")
        print("   - ui/managers/ToolWindowManager.py")
        print("   - ui/managers/TabWindowManager.py")
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
        sys.exit(1)
