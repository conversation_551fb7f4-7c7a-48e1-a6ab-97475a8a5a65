@echo off
echo ============================================================
echo FSJ04832 Package Manager Debug
echo ============================================================
echo.
echo 1. Current directory: %CD%
echo 2. Script directory: %~dp0
echo 3. Target directory: %~dp0..
echo.
echo 4. Checking Python...
python --version
if errorlevel 1 (
    echo ERROR: Python not found!
) else (
    echo Python OK
)
echo.
echo 5. Changing directory...
cd /d "%~dp0.."
echo New directory: %CD%
echo.
echo 6. Checking package.py...
if exist package.py (
    echo package.py exists
) else (
    echo ERROR: package.py not found!
)
echo.
echo 7. Listing files...
dir /b
echo.
echo 8. Testing package.py...
python package.py --help
echo.
pause
