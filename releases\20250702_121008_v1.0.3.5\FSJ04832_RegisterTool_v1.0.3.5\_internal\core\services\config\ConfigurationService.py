"""
配置管理服务
负责处理应用程序的配置管理，包括用户设置、寄存器配置文件的保存和加载
"""

import re
from datetime import datetime
from PyQt5.QtCore import QSettings
from PyQt5.QtWidgets import QMessageBox, QFileDialog
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class ConfigurationService:
    """配置管理服务"""
    
    # 配置文件格式常量
    HEADER_TEMPLATE = """# FSJ04832 Register Configuration
# Format: R<number> = 0X<4-digit HEX>;
# Version: 1.1
# Date: {date}\n\n"""
    
    LINE_FORMAT = "R{reg_num} = 0x{value:04X};"  # 强制四位十六进制
    LINE_PATTERN = re.compile(r'^\s*R(\d+)\s*=\s*0[xX]([0-9A-F]{1,4})\s*;\s*(#.*)?$', re.IGNORECASE)
    
    def __init__(self, main_window):
        """初始化配置服务
        
        Args:
            main_window: 主窗口实例
        """
        self.main_window = main_window
        self.settings = QSettings("FSJ", "FSJRead")
        
    def save_register_config(self, file_path=None):
        """保存寄存器配置到文件
        
        Args:
            file_path: 文件路径，如果为None则弹出对话框选择
            
        Returns:
            bool: 保存是否成功
        """
        try:
            # 如果没有指定文件路径，弹出保存对话框
            if file_path is None:
                file_path = self._safe_get_save_filename()

                if not file_path:
                    return False  # 用户取消了操作
            
            logger.info(f"正在保存配置到文件: {file_path}")
            
            # 获取所有寄存器值
            register_values = self.main_window.register_manager.get_all_register_values()
            
            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                self._write_header(f)
                self._write_register_values(f, register_values)
            
            # 显示成功消息
            self._safe_show_message(
                "保存成功",
                f"配置已保存到: {file_path}",
                QMessageBox.Information
            )
            
            self.main_window.show_status_message(f"配置已保存到: {file_path}", 3000)
            logger.info(f"配置保存成功: {file_path}")
            return True
            
        except Exception as e:
            error_msg = f"保存配置时出错: {str(e)}"
            logger.error(error_msg)
            self._safe_show_message(
                "保存错误",
                error_msg,
                QMessageBox.Critical
            )
            return False
    
    def load_register_config(self, file_path=None):
        """从文件加载寄存器配置
        
        Args:
            file_path: 文件路径，如果为None则弹出对话框选择
            
        Returns:
            bool: 加载是否成功
        """
        try:
            # 如果没有指定文件路径，弹出加载对话框
            if file_path is None:
                file_path = self._safe_get_open_filename()

                if not file_path:
                    return False  # 用户取消了操作
            
            logger.info(f"正在从文件加载配置: {file_path}")
            
            # 从文件加载值
            loaded_values = {}
            error_lines = []
            
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                        
                    addr, value, error = self._parse_line(line_num, line)
                    if error:
                        error_lines.append(error)
                    elif addr:
                        # 标准化地址格式
                        addr_num = int(addr, 16)
                        normalized_addr = f"0x{addr_num:02X}"
                        loaded_values[normalized_addr] = value
            
            # 处理加载结果
            self._handle_load_result(loaded_values, error_lines)
            return True
            
        except Exception as e:
            error_msg = f"加载配置失败: {str(e)}"
            logger.error(error_msg)
            QMessageBox.critical(
                self.main_window, 
                "加载失败",
                error_msg,
                QMessageBox.Ok
            )
            return False
    
    def _write_header(self, file_obj):
        """写入文件头"""
        file_obj.write(self.HEADER_TEMPLATE.format(
            date=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        ))
    
    def _write_register_values(self, file_obj, register_values):
        """写入寄存器值（自动格式化为四位十六进制）"""
        error_count = 0
        
        # 创建一个标准化的地址值字典，确保地址格式一致
        normalized_values = {}
        for addr, value in register_values.items():
            # 标准化地址格式
            addr_num = int(addr, 16)
            normalized_addr = f"0x{addr_num:02X}"
            normalized_values[normalized_addr] = value
        
        # 使用标准化后的字典进行排序和写入
        sorted_regs = sorted(
            normalized_values.items(),
            key=lambda x: int(x[0], 16)
        )
        
        for addr, value in sorted_regs:
            reg_num = int(addr, 16)
            try:
                self._validate_save_entry(reg_num, value)
                # 强制格式化为四位十六进制
                formatted_value = value & 0xFFFF
                file_obj.write(self.LINE_FORMAT.format(
                    reg_num=reg_num, value=formatted_value) + '\n')
            except ValueError as e:
                error_count += 1
                logger.error(f"保存跳过 {addr}: {str(e)}")
        
        if error_count > 0:
            raise ValueError(f"发现{error_count}个无效项未保存")
    
    def _validate_save_entry(self, reg_num, value):
        """验证保存条目有效性"""
        if not 0 <= reg_num <= 255:
            raise ValueError(f"地址越界: R{reg_num}")
        if not 0 <= (value & 0xFFFF) <= 0xFFFF:
            raise ValueError(f"值越界: 0X{value:04X}")
    
    def _parse_line(self, line_num, line):
        """增强版行解析（支持自动补零）"""
        match = self.LINE_PATTERN.match(line)
        if not match:
            return None, None, (line_num, "格式错误")
        
        try:
            reg_num_str, value_str = match.groups()[:2]
            reg_num = int(reg_num_str)
            
            # 处理值部分（自动补零到4位）
            clean_value = value_str.zfill(4).upper()
            if len(clean_value) > 4:
                raise ValueError("十六进制值超过4位")
                
            value = int(clean_value, 16)
            addr = f"0x{reg_num:02X}".lower()
            
            # 验证范围
            if not 0 <= reg_num <= 255:
                raise ValueError(f"地址越界: R{reg_num}")
            if not 0 <= value <= 0xFFFF:
                raise ValueError(f"值越界: 0X{value:04X}")
            
            return addr, value, None
        
        except ValueError as e:
            return None, None, (line_num, str(e))
    
    def _handle_load_result(self, loaded_values, error_lines):
        """处理加载结果"""
        if error_lines:
            self._show_load_errors(error_lines)
            
        if loaded_values:
            # 调用主窗口的方法来处理更新
            update_successful = self.main_window.update_registers_from_config(loaded_values)
            if update_successful:
                self._safe_show_message(
                    "加载完成",
                    f"成功加载并更新{len(loaded_values)}个寄存器\n"
                    f"忽略{len(error_lines)}个错误项",
                    QMessageBox.Information
                )
            else:
                self._safe_show_message(
                    "更新警告",
                    "部分寄存器值未能成功应用。",
                    QMessageBox.Warning
                )
        elif not error_lines:
            self._safe_show_message(
                "加载警告",
                "未在文件中找到有效的配置项。",
                QMessageBox.Warning
            )
            raise ValueError("未找到有效配置项")
    
    def _show_load_errors(self, error_lines):
        """显示加载错误"""
        error_msg = "\n".join(
            f"第{num}行: {err}"
            for num, err in error_lines[:10]
        )
        if len(error_lines) > 10:
            error_msg += f"\n...（共{len(error_lines)}个错误）"

        # 使用安全的消息框显示方法
        full_message = f"配置文件中发现格式错误或无效数据\n\n详细错误:\n{error_msg}"
        self._safe_show_message(
            "部分数据加载失败",
            full_message,
            QMessageBox.Warning
        )
    
    # 用户设置管理方法
    def get_setting(self, key, default_value=None, value_type=None):
        """获取用户设置
        
        Args:
            key: 设置键
            default_value: 默认值
            value_type: 值类型
            
        Returns:
            设置值
        """
        if value_type:
            return self.settings.value(key, default_value, type=value_type)
        else:
            return self.settings.value(key, default_value)
    
    def set_setting(self, key, value):
        """设置用户设置
        
        Args:
            key: 设置键
            value: 设置值
        """
        self.settings.setValue(key, value)
        logger.debug(f"设置已更新: {key} = {value}")
    
    def toggle_preload(self, checked):
        """切换是否启用预读取功能
        
        Args:
            checked: 是否选中
        """
        self.set_setting("preload_registers", checked)
        self.main_window.show_status_message(
            f"预读取功能已{'启用' if checked else '禁用'}", 3000
        )
        logger.info(f"预读取功能已{'启用' if checked else '禁用'}")
    
    def set_preload_count(self, count):
        """设置预读取数量
        
        Args:
            count: 预读取数量
        """
        self.set_setting("preload_count", count)
        self.main_window.show_status_message(
            f"预读取数量设置为 {count} 个寄存器", 3000
        )
        logger.info(f"预读取数量设置为 {count}")
    
    def set_language(self, lang_code):
        """设置语言
        
        Args:
            lang_code: 语言代码
        """
        self.set_setting("language", lang_code)
        
        # 提示用户重启应用以应用新语言
        QMessageBox.information(
            self.main_window, 
            "语言设置", 
            "语言设置已更改。请重启应用程序以应用新的语言设置。",
            QMessageBox.Ok
        )
        logger.info(f"语言设置已更改为: {lang_code}")
    
    def save_simulation_mode(self, simulation_mode):
        """保存模拟模式设置
        
        Args:
            simulation_mode: 是否为模拟模式
        """
        self.set_setting("simulation_mode", simulation_mode)
        logger.info(f"模拟模式设置已保存: {simulation_mode}")
    
    def load_simulation_mode(self):
        """加载模拟模式设置

        Returns:
            bool: 模拟模式设置
        """
        return self.get_setting("simulation_mode", True, bool)

    def save_auto_write_mode(self, auto_write_mode):
        """保存自动写入模式设置

        Args:
            auto_write_mode: 是否启用自动写入模式
        """
        self.set_setting("auto_write_mode", auto_write_mode)
        logger.info(f"自动写入模式设置已保存: {auto_write_mode}")

    def load_auto_write_mode(self):
        """加载自动写入模式设置

        Returns:
            bool: 自动写入模式设置，默认为False（不启用）
        """
        return self.get_setting("auto_write_mode", False, bool)

    def _safe_get_open_filename(self):
        """安全地获取打开文件名，避免对话框闪烁和崩溃问题

        Returns:
            str: 选择的文件路径，如果取消则返回空字符串
        """
        try:
            # 确保在主线程中执行
            from PyQt5.QtCore import QThread
            from PyQt5.QtWidgets import QApplication
            if QThread.currentThread() != self.main_window.thread():
                logger.warning("文件对话框不在主线程中调用，可能导致问题")

            # 确保QApplication实例存在
            if not QApplication.instance():
                logger.error("QApplication实例不存在，无法显示文件对话框")
                raise RuntimeError("QApplication实例不存在")

            # 验证主窗口是否有效
            if not self.main_window or not hasattr(self.main_window, 'isVisible'):
                logger.warning("主窗口无效，使用None作为父窗口")
                parent = None
            else:
                parent = self.main_window

            # 使用更安全的方式创建文件对话框
            dialog = QFileDialog(parent)
            dialog.setWindowTitle("加载配置")
            dialog.setNameFilter("配置文件 (*.cfg *.txt);;所有文件 (*)")
            dialog.setFileMode(QFileDialog.ExistingFile)
            dialog.setAcceptMode(QFileDialog.AcceptOpen)

            # 设置默认目录为当前工作目录
            import os
            dialog.setDirectory(os.getcwd())

            # 设置对话框为模态
            dialog.setModal(True)

            # 显示对话框
            result = dialog.exec_()
            if result == QFileDialog.Accepted:
                selected_files = dialog.selectedFiles()
                if selected_files:
                    file_path = selected_files[0]
                    logger.info(f"用户选择了文件: {file_path}")
                    return file_path

            logger.info("用户取消了文件选择")
            return ""

        except Exception as e:
            logger.error(f"打开文件对话框时出错: {str(e)}")
            # 如果安全方式失败，尝试使用静态方法作为备选
            try:
                file_path, _ = QFileDialog.getOpenFileName(
                    None,  # 使用None作为父窗口
                    "加载配置",
                    "",
                    "配置文件 (*.cfg *.txt);;所有文件 (*)"
                )
                return file_path if file_path else ""
            except Exception as e2:
                logger.error(f"备选文件对话框也失败: {str(e2)}")
                self._safe_show_message(
                    "错误",
                    f"无法打开文件选择对话框: {str(e)}\n请检查系统环境。",
                    QMessageBox.Critical
                )
                return ""

    def _safe_get_save_filename(self):
        """安全地获取保存文件名，避免对话框闪烁和崩溃问题

        Returns:
            str: 选择的文件路径，如果取消则返回空字符串
        """
        try:
            # 确保在主线程中执行
            from PyQt5.QtCore import QThread
            from PyQt5.QtWidgets import QApplication
            if QThread.currentThread() != self.main_window.thread():
                logger.warning("文件对话框不在主线程中调用，可能导致问题")

            # 确保QApplication实例存在
            if not QApplication.instance():
                logger.error("QApplication实例不存在，无法显示文件对话框")
                raise RuntimeError("QApplication实例不存在")

            # 验证主窗口是否有效
            if not self.main_window or not hasattr(self.main_window, 'isVisible'):
                logger.warning("主窗口无效，使用None作为父窗口")
                parent = None
            else:
                parent = self.main_window

            # 使用更安全的方式创建文件对话框
            dialog = QFileDialog(parent)
            dialog.setWindowTitle("保存寄存器配置")
            dialog.setNameFilter("配置文件 (*.cfg);;文本文件 (*.txt);;所有文件 (*)")
            dialog.setFileMode(QFileDialog.AnyFile)
            dialog.setAcceptMode(QFileDialog.AcceptSave)
            dialog.setDefaultSuffix("cfg")

            # 设置默认目录为当前工作目录
            import os
            dialog.setDirectory(os.getcwd())

            # 设置对话框为模态
            dialog.setModal(True)

            # 显示对话框
            result = dialog.exec_()
            if result == QFileDialog.Accepted:
                selected_files = dialog.selectedFiles()
                if selected_files:
                    file_path = selected_files[0]
                    logger.info(f"用户选择了保存文件: {file_path}")
                    return file_path

            logger.info("用户取消了文件保存")
            return ""

        except Exception as e:
            logger.error(f"打开保存文件对话框时出错: {str(e)}")
            # 如果安全方式失败，尝试使用静态方法作为备选
            try:
                file_path, _ = QFileDialog.getSaveFileName(
                    None,  # 使用None作为父窗口
                    "保存寄存器配置",
                    "",
                    "配置文件 (*.cfg);;文本文件 (*.txt);;所有文件 (*)"
                )
                return file_path if file_path else ""
            except Exception as e2:
                logger.error(f"备选保存文件对话框也失败: {str(e2)}")
                self._safe_show_message(
                    "错误",
                    f"无法打开文件保存对话框: {str(e)}\n请检查系统环境。",
                    QMessageBox.Critical
                )
                return ""

    def _safe_show_message(self, title, message, icon_type=None):
        """安全地显示消息框，避免与主窗口事件过滤器冲突

        Args:
            title: 消息框标题
            message: 消息内容
            icon_type: 图标类型 (QMessageBox.Information, Warning, Critical等)
        """
        try:
            # 临时禁用主窗口的事件过滤器
            main_window_filter_disabled = False
            if hasattr(self.main_window, 'eventFilter'):
                # 暂时移除主窗口的事件过滤器
                try:
                    self.main_window.removeEventFilter(self.main_window)
                    main_window_filter_disabled = True
                    logger.debug("临时禁用主窗口事件过滤器")
                except Exception as e:
                    logger.debug(f"无法禁用主窗口事件过滤器: {str(e)}")

            # 创建独立的消息框，不使用主窗口作为父窗口
            msg = QMessageBox()
            msg.setWindowTitle(title)
            msg.setText(message)

            # 设置图标
            if icon_type:
                msg.setIcon(icon_type)
            else:
                msg.setIcon(QMessageBox.Information)

            # 设置按钮
            msg.setStandardButtons(QMessageBox.Ok)
            msg.setDefaultButton(QMessageBox.Ok)

            # 设置窗口属性，确保消息框正常显示
            from PyQt5.QtCore import Qt
            msg.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
            msg.setModal(True)

            # 显示消息框
            result = msg.exec_()

            # 恢复主窗口的事件过滤器
            if main_window_filter_disabled:
                try:
                    self.main_window.installEventFilter(self.main_window)
                    logger.debug("恢复主窗口事件过滤器")
                except Exception as e:
                    logger.debug(f"无法恢复主窗口事件过滤器: {str(e)}")

            logger.info(f"安全消息框显示完成: {title}")
            return result

        except Exception as e:
            logger.error(f"显示安全消息框时出错: {str(e)}")
            # 如果安全方式失败，尝试使用状态栏显示消息
            try:
                if hasattr(self.main_window, 'status_bar'):
                    self.main_window.status_bar.showMessage(f"{title}: {message}", 5000)
                    logger.info(f"通过状态栏显示消息: {title}")
            except Exception as e2:
                logger.error(f"状态栏显示消息也失败: {str(e2)}")
            return QMessageBox.Ok
