#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试UI布局修复
验证批量读取操作期间分割器不会移动
"""

import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from utils.Log import get_module_logger

logger = get_module_logger(__name__)

def test_ui_layout_stability():
    """测试UI布局稳定性"""
    try:
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 导入主窗口
        from ui.windows.RegisterMainWindow import RegisterMainWindow
        
        # 创建主窗口
        main_window = RegisterMainWindow()
        main_window.show()
        
        # 等待界面完全加载
        QTimer.singleShot(2000, lambda: test_register_switch_layout(main_window))
        
        # 运行应用程序
        sys.exit(app.exec_())
        
    except Exception as e:
        logger.error(f"测试UI布局稳定性时出错: {str(e)}")
        import traceback
        traceback.print_exc()

def test_batch_read_layout(main_window):
    """测试批量读取时的布局稳定性"""
    try:
        logger.info("开始测试批量读取时的UI布局稳定性")

        # 获取当前布局控件尺寸
        central_widget = main_window.centralWidget()
        if central_widget and central_widget.layout():
            main_layout = central_widget.layout()

            # 查找顶部的HBoxLayout（树和表格的布局）
            original_sizes = []
            layout_widgets = []

            for i in range(main_layout.count()):
                item = main_layout.itemAt(i)
                if item and hasattr(item, 'layout') and item.layout():
                    layout = item.layout()
                    if hasattr(layout, 'addWidget') and layout.count() == 2:  # 应该有两个控件：树和表格区域
                        for j in range(layout.count()):
                            widget_item = layout.itemAt(j)
                            if widget_item and widget_item.widget():
                                widget = widget_item.widget()
                                layout_widgets.append(widget)
                                original_sizes.append(widget.size())
                        break

            logger.info(f"批量读取前的控件尺寸: {[f'{size.width()}x{size.height()}' for size in original_sizes]}")

            # 启动批量读取
            if hasattr(main_window, 'batch_manager'):
                main_window.batch_manager.handle_read_all_requested()

                # 定时检查控件尺寸
                def check_widget_sizes():
                    current_sizes = [widget.size() for widget in layout_widgets]
                    logger.info(f"批量读取期间的控件尺寸: {[f'{size.width()}x{size.height()}' for size in current_sizes]}")

                    # 检查尺寸是否发生显著变化
                    if original_sizes and current_sizes:
                        for i, (orig, curr) in enumerate(zip(original_sizes, current_sizes)):
                            width_diff = abs(orig.width() - curr.width())
                            height_diff = abs(orig.height() - curr.height())

                            if width_diff > 10 or height_diff > 10:  # 如果变化超过10像素
                                logger.warning(f"控件{i}尺寸发生显著变化: {orig.width()}x{orig.height()} -> {curr.width()}x{curr.height()}")
                            else:
                                logger.info(f"控件{i}尺寸保持稳定")

                # 每秒检查一次控件尺寸
                timer = QTimer()
                timer.timeout.connect(check_widget_sizes)
                timer.start(1000)

                # 10秒后停止检查
                QTimer.singleShot(10000, timer.stop)
            else:
                logger.error("主窗口没有batch_manager属性")
        else:
            logger.error("未找到中央控件或布局")

    except Exception as e:
        logger.error(f"测试批量读取布局时出错: {str(e)}")
        import traceback
        traceback.print_exc()

def test_register_switch_layout(main_window):
    """测试寄存器切换时的布局稳定性"""
    try:
        logger.info("开始测试寄存器切换时的UI布局稳定性")

        # 获取当前布局控件尺寸
        central_widget = main_window.centralWidget()
        if central_widget and central_widget.layout():
            main_layout = central_widget.layout()

            # 查找顶部的HBoxLayout（树和表格的布局）
            original_sizes = []
            layout_widgets = []

            for i in range(main_layout.count()):
                item = main_layout.itemAt(i)
                if item and hasattr(item, 'layout') and item.layout():
                    layout = item.layout()
                    if hasattr(layout, 'addWidget') and layout.count() == 2:  # 应该有两个控件：树和表格区域
                        for j in range(layout.count()):
                            widget_item = layout.itemAt(j)
                            if widget_item and widget_item.widget():
                                widget = widget_item.widget()
                                layout_widgets.append(widget)
                                original_sizes.append(widget.size())
                        break

            logger.info(f"寄存器切换前的控件尺寸: {[f'{size.width()}x{size.height()}' for size in original_sizes]}")

            # 检查TreeWidget的最小宽度设置
            if hasattr(main_window, 'tree_handler') and hasattr(main_window.tree_handler, 'tree_widget'):
                tree_widget = main_window.tree_handler.tree_widget
                if tree_widget:
                    min_width = tree_widget.minimumWidth()
                    current_width = tree_widget.width()
                    logger.info(f"TreeWidget最小宽度: {min_width}px, 当前宽度: {current_width}px")

            # 模拟寄存器切换
            if hasattr(main_window, 'tree_handler'):
                # 获取一些寄存器地址进行切换测试
                test_addresses = ['0x00', '0x01', '0x02', '0x03', '0x04']
                current_index = 0

                def switch_register():
                    nonlocal current_index
                    if current_index < len(test_addresses):
                        addr = test_addresses[current_index]
                        logger.info(f"切换到寄存器: {addr}")

                        # 检查控件尺寸
                        current_sizes = [widget.size() for widget in layout_widgets]
                        logger.info(f"切换后的控件尺寸: {[f'{size.width()}x{size.height()}' for size in current_sizes]}")

                        # 检查尺寸是否发生显著变化
                        if original_sizes and current_sizes:
                            for i, (orig, curr) in enumerate(zip(original_sizes, current_sizes)):
                                width_diff = abs(orig.width() - curr.width())
                                height_diff = abs(orig.height() - curr.height())

                                if width_diff > 5 or height_diff > 5:  # 如果变化超过5像素
                                    logger.warning(f"寄存器切换时控件{i}尺寸发生变化: {orig.width()}x{orig.height()} -> {curr.width()}x{curr.height()}")
                                else:
                                    logger.info(f"寄存器切换时控件{i}尺寸保持稳定")

                        # 执行寄存器切换
                        try:
                            main_window.tree_handler.select_register_by_addr(addr)
                        except Exception as e:
                            logger.warning(f"切换寄存器时出错: {str(e)}")

                        current_index += 1

                        # 继续下一个切换
                        if current_index < len(test_addresses):
                            QTimer.singleShot(1000, switch_register)
                        else:
                            logger.info("寄存器切换测试完成")

                # 开始切换测试
                QTimer.singleShot(500, switch_register)
            else:
                logger.error("主窗口没有tree_handler属性")
        else:
            logger.error("未找到中央控件或布局")

    except Exception as e:
        logger.error(f"测试寄存器切换布局时出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ui_layout_stability()
