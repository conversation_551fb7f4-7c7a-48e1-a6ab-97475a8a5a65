#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试输出控件初始化过程
追踪lineEditFout*Output控件的值变化
"""

import sys
import os
import json
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ui.handlers.ModernClkOutputsHandler import ModernClkOutputsHandler
from core.services.register.RegisterManager import RegisterManager
from utils.Log import logger

def debug_output_initialization():
    """调试输出控件初始化过程"""
    print("=" * 60)
    print("调试输出控件初始化过程")
    print("=" * 60)
    
    try:
        # 创建QApplication
        app = QApplication(sys.argv)
        
        print("1. 加载寄存器配置...")
        config_path = os.path.join('lib', 'register.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        print("2. 创建RegisterManager...")
        register_manager = RegisterManager(registers_config)
        
        print("3. 检查UI加载前的状态...")
        # 直接加载UI文件
        from ui.forms.Ui_ClkOutputs import Ui_ClkOutputs
        from PyQt5.QtWidgets import QWidget
        
        ui_widget = QWidget()
        ui = Ui_ClkOutputs()
        ui.setupUi(ui_widget)
        
        print("4. 检查UI加载后的初始值...")
        for output_num in range(14):
            output_attr = f"lineEditFout{output_num}Output"
            if hasattr(ui, output_attr):
                output_widget = getattr(ui, output_attr)
                initial_text = output_widget.text()
                print(f"   输出{output_num}: '{initial_text}'")
        
        print("\n5. 创建现代化时钟输出处理器...")
        
        # 创建处理器但不传入UI（让它自己创建）
        modern_handler = ModernClkOutputsHandler(None, register_manager)
        
        print("6. 检查处理器创建后的值...")
        for output_num in range(14):
            output_attr = f"lineEditFout{output_num}Output"
            if hasattr(modern_handler.ui, output_attr):
                output_widget = getattr(modern_handler.ui, output_attr)
                after_handler_text = output_widget.text()
                print(f"   输出{output_num}: '{after_handler_text}'")
        
        print("\n7. 检查VCO频率控件...")
        if hasattr(modern_handler.ui, "lineEditFvco"):
            vco_text = modern_handler.ui.lineEditFvco.text()
            print(f"   VCO频率: '{vco_text}'")
        
        print("\n8. 手动设置VCO频率并重新计算...")
        if hasattr(modern_handler.ui, "lineEditFvco"):
            # 清空VCO频率
            modern_handler.ui.lineEditFvco.setText("")
            app.processEvents()
            
            # 检查输出频率是否变化
            print("   清空VCO频率后:")
            for output_num in range(4):
                output_attr = f"lineEditFout{output_num}Output"
                if hasattr(modern_handler.ui, output_attr):
                    output_widget = getattr(modern_handler.ui, output_attr)
                    text = output_widget.text()
                    print(f"     输出{output_num}: '{text}'")
            
            # 设置新的VCO频率
            modern_handler.ui.lineEditFvco.setText("3000.0")
            app.processEvents()
            
            # 手动调用频率计算
            modern_handler.calculate_output_frequencies()
            app.processEvents()
            
            print("   设置VCO频率为3000.0并计算后:")
            for output_num in range(4):
                output_attr = f"lineEditFout{output_num}Output"
                if hasattr(modern_handler.ui, output_attr):
                    output_widget = getattr(modern_handler.ui, output_attr)
                    text = output_widget.text()
                    print(f"     输出{output_num}: '{text}'")
        
        print("\n9. 检查寄存器映射...")
        output_widgets_in_map = []
        for widget_name in modern_handler.widget_register_map.keys():
            if 'lineEditFout' in widget_name and 'Output' in widget_name:
                output_widgets_in_map.append(widget_name)
        
        if output_widgets_in_map:
            print(f"   在寄存器映射中找到的输出控件: {output_widgets_in_map}")
        else:
            print("   ✓ 输出频率控件未在寄存器映射中（正确）")
        
        print("\n10. 检查初始化时机...")
        # 检查初始化方法的调用顺序
        print("   初始化方法调用顺序:")
        print("   1. _init_clk_output_config()")
        print("   2. _init_default_values()")
        print("   3. _init_format_comboboxes()")
        print("   4. _init_output_frequencies()")
        print("   5. calculate_output_frequencies()")
        
        print("\n" + "=" * 60)
        print("调试完成")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_output_initialization()
