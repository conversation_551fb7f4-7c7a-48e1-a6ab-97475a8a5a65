"""
FSJ04832 运行时安全保护模块
提供反调试、反分析和运行时验证功能
"""

import os
import sys
import time
import hashlib
import platform
import threading
from typing import Optional

class RuntimeSecurity:
    """运行时安全保护类"""
    
    def __init__(self):
        self.is_protected = False
        self.debug_detected = False
        self.protection_thread = None
        self._stop_protection = False
        
    def initialize_protection(self) -> bool:
        """初始化安全保护"""
        try:
            # 检查运行环境
            if not self._check_environment():
                return False
                
            # 启动保护监控
            self._start_protection_monitor()
            
            self.is_protected = True
            return True
            
        except Exception as e:
            print(f"安全保护初始化失败: {e}")
            return False
    
    def _check_environment(self) -> bool:
        """检查运行环境安全性"""
        
        # 1. 检查是否在调试器中运行
        if self._detect_debugger():
            print("检测到调试器，程序将退出")
            self.debug_detected = True
            return False
            
        # 2. 检查是否为打包后的可执行文件
        if not self._is_packaged_executable():
            print("程序必须在打包环境中运行")
            return False
            
        # 3. 检查文件完整性
        if not self._verify_integrity():
            print("程序文件完整性验证失败")
            return False
            
        return True
    
    def _detect_debugger(self) -> bool:
        """检测调试器"""
        try:
            # 方法1: 检查调试器相关的环境变量
            debug_vars = [
                'PYTHONBREAKPOINT', 'PYCHARM_HOSTED', 'VSCODE_PID',
                'DEBUGPY_LAUNCHER_PORT', 'PYDEVD_LOAD_VALUES_ASYNC'
            ]
            
            for var in debug_vars:
                if os.environ.get(var):
                    return True
            
            # 方法2: 检查sys.gettrace()
            if sys.gettrace() is not None:
                return True
                
            # 方法3: 检查调试器进程
            if self._check_debugger_processes():
                return True
                
            return False
            
        except Exception:
            # 如果检测过程出错，为安全起见返回True
            return True
    
    def _check_debugger_processes(self) -> bool:
        """检查是否有调试器进程在运行"""
        try:
            if platform.system() == "Windows":
                import psutil
                debugger_names = [
                    'windbg.exe', 'x64dbg.exe', 'x32dbg.exe',
                    'ollydbg.exe', 'ida.exe', 'ida64.exe',
                    'cheatengine.exe', 'processhacker.exe'
                ]
                
                for proc in psutil.process_iter(['name']):
                    try:
                        if proc.info['name'].lower() in [name.lower() for name in debugger_names]:
                            return True
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
                        
        except ImportError:
            # 如果psutil不可用，跳过此检查
            pass
        except Exception:
            # 检测过程出错，为安全起见返回True
            return True
            
        return False
    
    def _is_packaged_executable(self) -> bool:
        """检查是否为打包后的可执行文件"""
        # PyInstaller打包后会设置这些属性
        return (
            hasattr(sys, 'frozen') or  # PyInstaller
            hasattr(sys, '_MEIPASS') or  # PyInstaller临时目录
            'python.exe' not in sys.executable.lower()  # 不是直接的Python解释器
        )
    
    def _verify_integrity(self) -> bool:
        """验证程序文件完整性"""
        try:
            # 获取可执行文件路径
            if hasattr(sys, 'frozen'):
                exe_path = sys.executable
            else:
                # 开发环境下跳过完整性检查
                return True
                
            # 计算文件哈希（如需使用可取消注释）
            # file_hash = self._calculate_file_hash(exe_path)
            
            # 这里可以与预期的哈希值进行比较
            # 为了简化，我们只检查文件是否存在且可读
            return os.path.exists(exe_path) and os.access(exe_path, os.R_OK)
            
        except Exception:
            return False
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """计算文件SHA256哈希值"""
        sha256_hash = hashlib.sha256()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
            return sha256_hash.hexdigest()
        except Exception:
            return ""
    
    def _start_protection_monitor(self):
        """启动保护监控线程"""
        if self.protection_thread is None or not self.protection_thread.is_alive():
            self.protection_thread = threading.Thread(
                target=self._protection_monitor_loop,
                daemon=True
            )
            self.protection_thread.start()
    
    def _protection_monitor_loop(self):
        """保护监控循环"""
        while not self._stop_protection:
            try:
                # 定期检查调试器
                if self._detect_debugger():
                    self.debug_detected = True
                    self._handle_security_violation("调试器检测")
                    break
                
                # 检查内存中的可疑模块
                if self._check_suspicious_modules():
                    self._handle_security_violation("可疑模块检测")
                    break
                
                # 等待一段时间再次检查
                time.sleep(5)
                
            except Exception as e:
                print(f"保护监控异常: {e}")
                break
    
    def _check_suspicious_modules(self) -> bool:
        """检查可疑的已加载模块"""
        try:
            suspicious_modules = [
                'pdb', 'debugpy', 'pydevd', 'winpdb',
                'rpdb', 'pudb', 'ipdb', 'bdb'
            ]
            
            loaded_modules = list(sys.modules.keys())
            for module_name in suspicious_modules:
                if any(module_name in loaded for loaded in loaded_modules):
                    return True
                    
            return False
            
        except Exception:
            return True  # 检测失败时为安全起见返回True
    
    def _handle_security_violation(self, violation_type: str):
        """处理安全违规"""
        print(f"安全违规检测: {violation_type}")
        print("程序将在3秒后退出...")
        
        # 清理敏感数据
        self._cleanup_sensitive_data()
        
        # 延迟退出
        time.sleep(3)
        os._exit(1)
    
    def _cleanup_sensitive_data(self):
        """清理敏感数据"""
        try:
            # 这里可以添加清理敏感数据的代码
            # 例如：清理内存中的密钥、配置等
            pass
        except Exception:
            pass
    
    def stop_protection(self):
        """停止保护监控"""
        self._stop_protection = True
        if self.protection_thread and self.protection_thread.is_alive():
            self.protection_thread.join(timeout=1)

# 全局安全保护实例
_security_instance: Optional[RuntimeSecurity] = None

def get_security_instance() -> RuntimeSecurity:
    """获取安全保护实例"""
    global _security_instance
    if _security_instance is None:
        _security_instance = RuntimeSecurity()
    return _security_instance

def initialize_security() -> bool:
    """初始化安全保护"""
    security = get_security_instance()
    return security.initialize_protection()

def is_security_active() -> bool:
    """检查安全保护是否激活"""
    security = get_security_instance()
    return security.is_protected and not security.debug_detected
