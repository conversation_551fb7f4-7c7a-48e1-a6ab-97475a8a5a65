import re
from datetime import datetime
from PyQt5.QtWidgets import QMessageBox

class ConfigFileHandler:
    """处理寄存器配置文件的加载和保存（强制四位十六进制格式）"""
    
    HEADER_TEMPLATE = """# FSJ04832 Register Configuration
# Format: R<number> = 0X<4-digit HEX>;
# Version: 1.1
# Date: {date}\n\n"""
    
    LINE_FORMAT = "R{reg_num} = 0x{value:04X};"  # 强制四位十六进制
    LINE_PATTERN = re.compile(r'^\s*R(\d+)\s*=\s*0[xX]([0-9A-F]{1,4})\s*;\s*(#.*)?$', re.IGNORECASE)

    def __init__(self, parent_widget):
        self.parent = parent_widget
        self.registers = parent_widget.registers
        self.register_manager = parent_widget.register_manager  # 使用寄存器管理器代替register_values

    def save_config(self, file_path):
        """保存配置到文件（强制四位十六进制格式）"""
        try:
            register_values = self.register_manager.get_all_register_values()

            with open(file_path, 'w', encoding='utf-8') as f:
                self._write_header(f)
                self._write_register_values(f, register_values)
                
            QMessageBox.information(self.parent, "保存成功", 
                                   "配置文件已保存", QMessageBox.Ok)
            return True
            
        except Exception as e:
            QMessageBox.critical(self.parent, "保存失败",
                                f"保存失败：{str(e)}", QMessageBox.Ok)
            return False

    def _write_header(self, file_obj):
        """写入文件头"""
        file_obj.write(self.HEADER_TEMPLATE.format(
            date=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        ))

    def _write_register_values(self, file_obj, register_values):
        """写入寄存器值（自动格式化为四位十六进制）"""
        error_count = 0
        # 创建一个标准化的地址值字典，确保地址格式一致
        normalized_values = {}
        for addr, value in register_values.items():
            # 标准化地址格式
            addr_num = int(addr, 16)
            normalized_addr = f"0x{addr_num:02X}"
            normalized_values[normalized_addr] = value
        
        # 使用标准化后的字典进行排序和写入
        sorted_regs = sorted(
            normalized_values.items(),
            key=lambda x: int(x[0], 16)
        )
        
        for addr, value in sorted_regs:
            reg_num = int(addr, 16)
            try:
                self._validate_save_entry(reg_num, value)
                # 强制格式化为四位十六进制
                formatted_value = value & 0xFFFF
                file_obj.write(self.LINE_FORMAT.format(
                    reg_num=reg_num, value=formatted_value) + '\n')
            except ValueError as e:
                error_count += 1
                self.parent._log_error(f"保存跳过 {addr}: {str(e)}")
        
        if error_count > 0:
            raise ValueError(f"发现{error_count}个无效项未保存")

    def _validate_save_entry(self, reg_num, value):
        """验证保存条目有效性"""
        if not 0 <= reg_num <= 255:
            raise ValueError(f"地址越界: R{reg_num}")
        if not 0 <= (value & 0xFFFF) <= 0xFFFF:
            raise ValueError(f"值越界: 0X{value:04X}")

    def load_config(self, file_path):
        """从文件加载配置（支持1-4位十六进制自动补零）"""
        try:
            # 从文件加载值
            loaded_values = {}
            error_lines = []
            
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                        
                    addr, value, error = self._parse_line(line_num, line)
                    if error:
                        error_lines.append(error)
                    elif addr:
                        # 标准化地址格式
                        addr_num = int(addr, 16)
                        normalized_addr = f"0x{addr_num:02X}"
                        loaded_values[normalized_addr] = value

            self._handle_load_result(loaded_values, error_lines)
            return True
            
        except Exception as e:
            QMessageBox.critical(self.parent, "加载失败",
                                f"加载失败：{str(e)}", QMessageBox.Ok)
            return False

    def _parse_line(self, line_num, line):
        """增强版行解析（支持自动补零）"""
        match = self.LINE_PATTERN.match(line)
        if not match:
            return None, None, (line_num, "格式错误")
        
        try:
            reg_num_str, value_str = match.groups()[:2]
            reg_num = int(reg_num_str)
            
            # 处理值部分（自动补零到4位）
            clean_value = value_str.zfill(4).upper()
            if len(clean_value) > 4:
                raise ValueError("十六进制值超过4位")
                
            value = int(clean_value, 16)
            addr = f"0x{reg_num:02X}".lower()
            
            # 验证范围
            if not 0 <= reg_num <= 255:
                raise ValueError(f"地址越界: R{reg_num}")
            if not 0 <= value <= 0xFFFF:
                raise ValueError(f"值越界: 0X{value:04X}")
            
            return addr, value, None
        
        except ValueError as e:
            return None, None, (line_num, str(e))

    def _handle_load_result(self, loaded_values, error_lines):
        """处理加载结果"""
        if error_lines:
            self._show_load_errors(error_lines)
            
        if loaded_values:
            # 调用父窗口的方法来处理更新，而不是直接修改数据和调用_refresh_ui
            update_successful = self.parent.update_registers_from_config(loaded_values)
            if update_successful:
                QMessageBox.information(
                    self.parent, "加载完成",
                    f"成功加载并更新{len(loaded_values)}个寄存器\n"
                    f"忽略{len(error_lines)}个错误项",
                    QMessageBox.Ok
                )
            else: # 可以选择在这里添加更新失败的消息
                QMessageBox.warning(self.parent, "更新警告", "部分寄存器值未能成功应用。", QMessageBox.Ok)

        elif not error_lines: # 只有在没有加载到任何值且没有错误时才报这个错
            QMessageBox.warning(self.parent, "加载警告", "未在文件中找到有效的配置项。", QMessageBox.Ok)
            raise ValueError("未找到有效配置项")

    def _show_load_errors(self, error_lines):
        """显示加载错误"""
        error_msg = "\n".join(
            f"第{num}行: {err}" 
            for num, err in error_lines[:10]
        )
        if len(error_lines) > 10:
            error_msg += f"\n...（共{len(error_lines)}个错误）"
            
        msg = QMessageBox(self.parent)
        msg.setIcon(QMessageBox.Warning)
        msg.setWindowTitle("部分数据加载失败")
        msg.setText("配置文件中发现格式错误或无效数据")
        msg.setDetailedText(error_msg)
        msg.exec_()