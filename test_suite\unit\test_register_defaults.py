#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试寄存器默认值
"""

import sys
import os
import json
import logging

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_register_defaults():
    """测试寄存器默认值"""
    try:
        logger.info("=== 测试寄存器默认值 ===")
        
        # 加载寄存器配置
        config_path = 'lib/register.json'
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        # 创建RegisterManager
        from core.services.register.RegisterManager import RegisterManager
        register_manager = RegisterManager(registers_config)
        
        # 测试分频比寄存器的默认值
        test_registers = [
            ("0x63", "CLKin0_R[13:0]", "PLL1R0Div"),
            ("0x65", "CLKin1_R[13:0]", "PLL1R1Div"), 
            ("0x67", "CLKin2_R[13:0]", "PLL1R2Div")
        ]
        
        for reg_addr, bit_name, widget_name in test_registers:
            logger.info(f"\n--- 测试寄存器 {reg_addr} ---")
            
            # 从JSON配置获取默认值
            reg_config = registers_config.get(reg_addr, {})
            bits_config = reg_config.get("bits", [])
            
            for bit_config in bits_config:
                if bit_config.get("name") == bit_name:
                    default_binary = bit_config.get("default", "0")
                    default_decimal = int(default_binary, 2)
                    logger.info(f"JSON配置默认值: {default_binary} (二进制) = {default_decimal} (十进制)")
                    break
            
            # 从RegisterManager获取寄存器值
            register = register_manager.get_register(reg_addr)
            if register:
                logger.info(f"寄存器对象值: 0x{register.value:04X} = {register.value} (十进制)")
                
                # 获取位字段值
                bit_value = register_manager.get_bit_field_value(reg_addr, bit_name)
                logger.info(f"位字段 {bit_name} 值: {bit_value}")
                
                # 检查是否匹配
                if bit_value == default_decimal:
                    logger.info(f"✓ 寄存器 {reg_addr} 默认值正确")
                else:
                    logger.error(f"✗ 寄存器 {reg_addr} 默认值错误！期望{default_decimal}，实际{bit_value}")
            else:
                logger.error(f"✗ 无法获取寄存器 {reg_addr}")
        
        # 测试设置值后的读取
        logger.info("\n=== 测试设置值后的读取 ===")
        
        # 设置新值
        test_values = [120, 120, 150]
        bit_names = ["CLKin0_R[13:0]", "CLKin1_R[13:0]", "CLKin2_R[13:0]"]
        
        for i, (reg_addr, bit_name, widget_name) in enumerate(test_registers):
            new_value = test_values[i]
            logger.info(f"\n设置 {reg_addr}.{bit_name} = {new_value}")
            
            # 设置值
            register_manager.set_bit_field_value(reg_addr, bit_name, new_value)
            
            # 读取值
            read_value = register_manager.get_bit_field_value(reg_addr, bit_name)
            logger.info(f"读取值: {read_value}")
            
            if read_value == new_value:
                logger.info(f"✓ 设置和读取一致")
            else:
                logger.error(f"✗ 设置和读取不一致！设置{new_value}，读取{read_value}")
        
        logger.info("\n=== 测试完成 ===")
        
    except Exception as e:
        logger.error(f"测试时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_register_defaults()
