#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ModernRegisterIOHandler演示脚本
展示现代化寄存器IO处理器的基本功能
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import QTimer

from ui.handlers.ModernRegisterIOHandler import ModernRegisterIOHandler
from core.services.register.RegisterManager import RegisterManager
from core.repositories.register_repository import RegisterRepository


class MockSPIService:
    """模拟SPI服务"""
    def __init__(self):
        self.registers = {}
    
    def read_register(self, addr):
        return self.registers.get(addr, 0x1234)
    
    def write_register(self, addr, value):
        self.registers[addr] = value
        print(f"模拟写入: 地址 0x{addr:02X} = 0x{value:04X}")


class DemoWindow(QMainWindow):
    """演示窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("ModernRegisterIOHandler 演示")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建模拟的RegisterManager
        self.mock_registers = {
            "0x00": {
                "bits": [
                    {
                        "bit": "15:0",
                        "name": "DA_DEVICE_VERSION",
                        "default": "0001001100000000",
                        "widget_name": "deviceVersion",
                        "widget_type": "label",
                        "options": None,
                        "description": "Device version"
                    }
                ]
            },
            "0x02": {
                "bits": [
                    {
                        "bit": "0",
                        "name": "POWERDOWN",
                        "default": "0",
                        "widget_name": "powerDown",
                        "widget_type": "checkbox",
                        "options": None,
                        "description": "Power down control"
                    }
                ]
            }
        }
        
        self.register_manager = RegisterManager(self.mock_registers)
        
        # 创建模拟的RegisterRepository
        self.mock_spi_service = MockSPIService()
        self.register_repo = RegisterRepository(self.mock_spi_service)
        
        # 创建ModernRegisterIOHandler实例
        self.io_handler = ModernRegisterIOHandler(
            parent=self,
            register_manager=self.register_manager,
            register_repo=self.register_repo
        )
        
        # 设置中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.addWidget(self.io_handler)
        
        # 连接信号
        self.connect_signals()
        
        # 初始化一些数据
        self.init_demo_data()
        
        print("ModernRegisterIOHandler演示窗口初始化完成")
    
    def connect_signals(self):
        """连接信号"""
        self.io_handler.read_requested.connect(self.on_read_requested)
        self.io_handler.write_requested.connect(self.on_write_requested)
        self.io_handler.read_all_requested.connect(self.on_read_all_requested)
        self.io_handler.write_all_requested.connect(self.on_write_all_requested)
        self.io_handler.search_requested.connect(self.on_search_requested)
        self.io_handler.bit_field_selected.connect(self.on_bit_field_selected)
    
    def init_demo_data(self):
        """初始化演示数据"""
        # 设置默认地址和值
        self.io_handler.set_address_display(0x02)
        self.io_handler.set_value_display(0x0000)
        
        # 更新端口列表
        ports = ["COM1", "COM2", "COM3", "模拟端口"]
        self.io_handler.update_port_list(ports)
        self.io_handler.set_selected_port("模拟端口")
    
    def on_read_requested(self, addr):
        """处理读取请求"""
        print(f"读取请求: 地址 0x{addr:02X}")
        # 模拟读取操作
        value = self.mock_spi_service.read_register(addr)
        self.io_handler.set_value_display(value)
        
        # 更新RegisterManager
        self.register_manager.set_register_value(addr, value)
    
    def on_write_requested(self, addr, value):
        """处理写入请求"""
        print(f"写入请求: 地址 0x{addr:02X}, 值 0x{value:04X}")
        # 模拟写入操作
        self.mock_spi_service.write_register(addr, value)
        
        # 更新RegisterManager
        self.register_manager.set_register_value(addr, value)
    
    def on_read_all_requested(self):
        """处理读取全部请求"""
        print("读取全部寄存器请求")
        # 模拟读取所有寄存器
        for addr_str in self.mock_registers.keys():
            addr = int(addr_str, 16)
            value = self.mock_spi_service.read_register(addr)
            self.register_manager.set_register_value(addr, value)
            print(f"  读取 0x{addr:02X} = 0x{value:04X}")
    
    def on_write_all_requested(self):
        """处理写入全部请求"""
        print("写入全部寄存器请求")
        # 模拟写入所有寄存器
        for addr_str in self.mock_registers.keys():
            addr = int(addr_str, 16)
            value = self.register_manager.get_register_value(addr)
            self.mock_spi_service.write_register(addr, value)
            print(f"  写入 0x{addr:02X} = 0x{value:04X}")
    
    def on_search_requested(self, search_text):
        """处理搜索请求"""
        print(f"搜索请求: '{search_text}'")
        # 搜索功能由RegisterManager处理
        results = self.register_manager.search_bit_fields(search_text)
        print(f"  找到 {len(results)} 个结果")
        for bit_name, reg_addr, bit_range in results:
            print(f"    {bit_name} (寄存器: {reg_addr}, 位: {bit_range})")
    
    def on_bit_field_selected(self, reg_addr):
        """处理位字段选择"""
        print(f"位字段选择: 寄存器 {reg_addr}")
        # 切换到选中的寄存器
        try:
            addr = int(reg_addr, 16) if reg_addr.startswith("0x") else int(reg_addr)
            self.io_handler.set_address_display(addr)
            value = self.register_manager.get_register_value(addr)
            self.io_handler.set_value_display(value)
        except ValueError:
            print(f"无效的寄存器地址: {reg_addr}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建演示窗口
    demo_window = DemoWindow()
    demo_window.show()
    
    # 设置定时器来演示一些功能
    def demo_actions():
        print("\n=== 开始演示 ===")
        
        # 演示搜索功能
        demo_window.io_handler.search_edit.setText("POWERDOWN")
        demo_window.io_handler._perform_search()
        
        # 演示地址和值设置
        QTimer.singleShot(1000, lambda: demo_window.io_handler.set_address_display(0x00))
        QTimer.singleShot(2000, lambda: demo_window.io_handler.set_value_display(0x1234))
        
        print("演示动作已启动")
    
    # 延迟启动演示
    QTimer.singleShot(500, demo_actions)
    
    # 运行应用
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
