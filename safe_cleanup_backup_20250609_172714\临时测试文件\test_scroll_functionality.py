#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试滚动功能的脚本
用于验证工具窗口的滚动条功能是否正常工作
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

try:
    from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton
    from PyQt5.QtCore import Qt
    print("PyQt5 导入成功")
except ImportError as e:
    print(f"PyQt5 导入失败: {e}")
    sys.exit(1)

try:
    from plugins.example_tool_plugin import ExampleToolWindow
    print("示例插件导入成功")
except ImportError as e:
    print(f"示例插件导入失败: {e}")

try:
    from ui.handlers.ModernBaseHandler import ModernBaseHandler
    print("ModernBaseHandler 导入成功")
except ImportError as e:
    print(f"ModernBaseHandler 导入失败: {e}")

try:
    from utils.Log import get_module_logger
    print("日志模块导入成功")
except ImportError as e:
    print(f"日志模块导入失败: {e}")
    # 创建一个简单的logger替代
    import logging
    def get_module_logger(name):
        return logging.getLogger(name)

logger = get_module_logger(__name__)


class ScrollTestWindow(QMainWindow):
    """滚动功能测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("滚动功能测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 添加测试按钮
        self.create_test_buttons(layout)
        
        # 存储测试窗口
        self.test_windows = []
    
    def create_test_buttons(self, layout):
        """创建测试按钮"""
        
        # 测试示例插件窗口
        example_btn = QPushButton("测试示例插件窗口 (自动滚动)")
        example_btn.clicked.connect(self.test_example_plugin)
        layout.addWidget(example_btn)
        
        # 测试手动启用滚动的窗口
        manual_scroll_btn = QPushButton("测试手动启用滚动的窗口")
        manual_scroll_btn.clicked.connect(self.test_manual_scroll)
        
        layout.addWidget(manual_scroll_btn)
        
        # 测试禁用滚动的窗口
        no_scroll_btn = QPushButton("测试无滚动的窗口")
        no_scroll_btn.clicked.connect(self.test_no_scroll)
        layout.addWidget(no_scroll_btn)
        
        # 关闭所有测试窗口
        close_all_btn = QPushButton("关闭所有测试窗口")
        close_all_btn.clicked.connect(self.close_all_test_windows)
        layout.addWidget(close_all_btn)
    
    def test_example_plugin(self):
        """测试示例插件窗口"""
        try:
            window = ExampleToolWindow()
            window.setWindowTitle("示例插件窗口 - 滚动测试")
            window.show()
            self.test_windows.append(window)
            logger.info("创建示例插件窗口")
        except Exception as e:
            logger.error(f"创建示例插件窗口失败: {str(e)}")
    
    def test_manual_scroll(self):
        """测试手动启用滚动的窗口"""
        try:
            # 创建一个基础的ModernBaseHandler窗口
            window = ModernBaseHandler(enable_scroll=False)  # 先禁用滚动
            window.setWindowTitle("手动滚动测试窗口")
            
            # 添加一些内容
            from PyQt5.QtWidgets import QVBoxLayout, QLabel, QPushButton
            layout = QVBoxLayout(window.content_widget)
            
            for i in range(20):
                label = QLabel(f"这是第 {i+1} 行内容，用于测试滚动功能")
                layout.addWidget(label)
            
            # 手动启用滚动
            window.enable_scroll_area(600, 800)
            
            window.show()
            self.test_windows.append(window)
            logger.info("创建手动滚动测试窗口")
        except Exception as e:
            logger.error(f"创建手动滚动测试窗口失败: {str(e)}")
    
    def test_no_scroll(self):
        """测试无滚动的窗口"""
        try:
            # 创建一个不启用滚动的窗口
            window = ModernBaseHandler(enable_scroll=False)
            window.setWindowTitle("无滚动测试窗口")
            
            # 添加少量内容
            from PyQt5.QtWidgets import QVBoxLayout, QLabel
            layout = QVBoxLayout(window.content_widget)
            
            for i in range(5):
                label = QLabel(f"这是第 {i+1} 行内容，无滚动功能")
                layout.addWidget(label)
            
            window.show()
            self.test_windows.append(window)
            logger.info("创建无滚动测试窗口")
        except Exception as e:
            logger.error(f"创建无滚动测试窗口失败: {str(e)}")
    
    def close_all_test_windows(self):
        """关闭所有测试窗口"""
        for window in self.test_windows:
            try:
                window.close()
            except Exception as e:
                logger.error(f"关闭测试窗口失败: {str(e)}")
        
        self.test_windows.clear()
        logger.info("已关闭所有测试窗口")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    test_window = ScrollTestWindow()
    test_window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
