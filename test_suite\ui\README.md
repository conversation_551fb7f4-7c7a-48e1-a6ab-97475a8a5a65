# UI 测试

## 描述
UI测试 - 测试用户界面相关功能

## 测试文件
- `test_scroll_area_fix.py`
- `test_ui_display.py`
- `test_ui_refactor.py`
- `test_final_layout.py`
- `test_search_final.py`
- `test_search_layout.py`
- `test_search_style.py`
- `test_single_search_box.py`
- `test_improved_search.py`
- `test_modern_search.py`
- `test_window_fix.py`
- `test_window_api_fix.py`
- `test_bottom_buttons_restored.py`
- `test_buttons_removed.py`

## 运行测试
```bash
# 运行该分类的所有测试
python test_suite/run_all_tests.py --category ui

# 运行特定测试文件
python test_suite/ui/test_specific_file.py
```

## 注意事项
- 确保测试环境已正确设置
- 某些测试可能需要Qt环境
- 性能测试可能需要较长时间
