# 架构集成使用指南

本文档详细说明如何使用新增的四个架构组件。

## 📋 概述

新增的四个架构组件：
1. **依赖注入容器** (`DIContainer.py`) - 统一管理服务依赖
2. **配置管理系统** (`ConfigurationManager.py`) - 分层配置管理
3. **异步操作管理器** (`AsyncOperationManager.py`) - 异步操作统一管理
4. **插件系统** (`PluginManager.py`) - 动态插件加载和管理

## 🔧 1. 依赖注入容器使用

### 基本用法

```python
from core.services.DIContainer import container

# 注册服务
container.register_singleton('my_service', MyServiceClass, param1='value1')

# 获取服务
service = container.get('my_service')

# 依赖注入（使用@前缀引用其他服务）
container.register_singleton('dependent_service', DependentClass, 
                           dependency='@my_service')
```

### 在现有代码中的集成

主窗口已经集成了依赖注入，现有的管理器创建方式：

**之前：**
```python
self.display_manager = RegisterDisplayManager(self)
```

**现在：**
```python
self.display_manager = container.get('display_manager')
```

### 添加新服务

```python
# 在 _setup_dependency_injection 方法中添加
container.register_singleton('new_service', NewServiceClass, 
                           main_window='@main_window')
```

## ⚙️ 2. 配置管理系统使用

### 配置文件结构

配置文件位于 `config/` 目录：
- `default.json` - 默认配置
- `app.json` - 应用配置
- `local.json` - 本地覆盖配置

### 读取配置

```python
from core.services.config.ConfigurationManager import get_config

# 读取配置值
window_width = get_config('app.window.width', 1840)  # 默认值1840
theme = get_config('ui.theme', 'fusion')
```

### 设置配置

```python
from core.services.config.ConfigurationManager import set_config

# 设置配置值
set_config('ui.theme', 'dark')
set_config('spi.timeout', 10000)
```

### 监听配置变化

```python
from core.services.config.ConfigurationManager import watch_config

def on_theme_changed(key, value):
    print(f"主题已更改为: {value}")

# 监听主题变化
watch_config('ui.theme', on_theme_changed)
```

### 环境变量覆盖

```bash
# 设置环境变量覆盖配置
export FSJ_CONFIG_UI_THEME=dark
export FSJ_CONFIG_SPI_TIMEOUT=10000
```

## 🚀 3. 异步操作管理器使用

### 基本异步操作

```python
from core.services.async.AsyncOperationManager import submit_async_operation

def long_running_task(param1, param2):
    # 执行耗时操作
    time.sleep(5)
    return f"结果: {param1} + {param2}"

# 提交异步操作
operation_id = submit_async_operation(
    long_running_task, 
    "参数1", "参数2",
    operation_name="test_operation"
)
```

### 使用装饰器

```python
from core.services.async.AsyncOperationManager import async_operation

@async_operation("register_read")
def read_register_async(address):
    # 执行寄存器读取
    return spi_service.read_register(address)

# 调用会自动变为异步
operation_id = read_register_async("0x00")
```

### 监听操作完成

```python
from core.services.async.AsyncOperationManager import AsyncOperationManager

async_manager = AsyncOperationManager.instance()

def on_operation_completed(result):
    if result.success:
        print(f"操作成功: {result.result}")
    else:
        print(f"操作失败: {result.error}")

# 连接信号
async_manager.operation_completed.connect(on_operation_completed)
```

### SPI异步操作

```python
from core.services.spi.async_spi_service import AsyncSPIService

async_spi = AsyncSPIService()

# 异步读取寄存器
def on_read_complete(result):
    if result.success:
        print(f"读取成功: {result.result}")

operation_id = async_spi.read_register_async("0x00", callback=on_read_complete)

# 异步批量操作
addresses = ["0x00", "0x01", "0x02"]
batch_id = async_spi.batch_read_async(addresses)
```

## 🔌 4. 插件系统使用

### 创建工具窗口插件

```python
from core.services.plugin.PluginManager import IToolWindowPlugin
from PyQt5.QtWidgets import QWidget

class MyToolWindow(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("我的工具")
        # 设置UI...

class MyToolPlugin(IToolWindowPlugin):
    @property
    def name(self) -> str:
        return "我的工具"
    
    @property
    def version(self) -> str:
        return "1.0.0"
    
    @property
    def description(self) -> str:
        return "这是我的自定义工具"
    
    @property
    def menu_text(self) -> str:
        return "我的工具"
    
    @property
    def icon_path(self) -> str:
        return "icons/my_tool.png"  # 可选
    
    def initialize(self, context):
        self.context = context
    
    def create_window(self, parent=None):
        return MyToolWindow(parent)
    
    def cleanup(self):
        pass
```

### 插件目录结构

```
plugins/
├── my_tool_plugin.py          # 插件文件
├── another_plugin/
│   ├── __init__.py
│   ├── plugin.py              # 插件主文件
│   └── resources/             # 插件资源
└── ...
```

### 插件配置

在 `config/default.json` 中配置插件：

```json
{
  "plugins": {
    "enabled": true,
    "directories": [
      "plugins",
      "ui/tools"
    ],
    "auto_load": true
  }
}
```

## 🔄 5. 现有代码迁移指南

### 迁移管理器创建

**之前：**
```python
class RegisterMainWindow(QMainWindow):
    def __init__(self):
        self.display_manager = RegisterDisplayManager(self)
        self.event_coordinator = EventCoordinator(self)
```

**现在：**
```python
class RegisterMainWindow(QMainWindow):
    def __init__(self):
        self._setup_dependency_injection()
        self.display_manager = container.get('display_manager')
        self.event_coordinator = container.get('event_coordinator')
```

### 迁移配置访问

**之前：**
```python
# 硬编码配置
self.resize(1840, 1100)
self.timeout = 5000
```

**现在：**
```python
# 配置驱动
width = get_config('app.window.width', 1840)
height = get_config('app.window.height', 1100)
self.resize(width, height)
self.timeout = get_config('spi.timeout', 5000)
```

### 迁移同步操作为异步

**之前：**
```python
def read_all_registers(self):
    for addr in addresses:
        value = spi_service.read_register(addr)
        self.update_ui(addr, value)
```

**现在：**
```python
def read_all_registers(self):
    def batch_read():
        results = {}
        for addr in addresses:
            results[addr] = spi_service.read_register(addr)
        return results
    
    operation_id = submit_async_operation(
        batch_read, 
        operation_name="batch_read_all"
    )
```

## 📈 6. 性能优化建议

### 异步操作最佳实践

1. **批量操作异步化**：将耗时的批量操作改为异步
2. **UI更新优化**：异步操作完成后批量更新UI
3. **操作取消**：为长时间运行的操作提供取消功能

### 配置缓存

```python
# 缓存频繁访问的配置
class ConfigCache:
    def __init__(self):
        self.ui_theme = get_config('ui.theme')
        self.spi_timeout = get_config('spi.timeout')
        
        # 监听变化
        watch_config('ui.theme', self._on_theme_changed)
    
    def _on_theme_changed(self, key, value):
        self.ui_theme = value
```

## 🧪 7. 测试指南

### 测试依赖注入

```python
def test_dependency_injection():
    # 创建测试容器
    test_container = DIContainer()
    test_container.register_singleton('test_service', TestService)
    
    # 测试获取服务
    service = test_container.get('test_service')
    assert service is not None
```

### 测试异步操作

```python
def test_async_operation():
    def test_func():
        return "test_result"
    
    operation_id = submit_async_operation(test_func)
    result = AsyncOperationManager.instance().wait_for_operation(operation_id)
    
    assert result.success
    assert result.result == "test_result"
```

## 🚨 8. 注意事项

### 依赖注入
- 避免循环依赖
- 服务名称要唯一
- 及时清理不需要的服务

### 配置管理
- 配置键名要有意义
- 提供合理的默认值
- 敏感配置不要提交到版本控制

### 异步操作
- 避免在UI线程中执行耗时操作
- 正确处理异常
- 及时取消不需要的操作

### 插件系统
- 插件要处理好资源清理
- 避免插件间的直接依赖
- 提供良好的错误处理

## 📚 9. 扩展示例

查看以下示例文件：
- `plugins/example_tool_plugin.py` - 示例插件
- `config/default.json` - 默认配置
- `core/services/spi/async_spi_service.py` - 异步SPI服务

这些新的架构组件让应用程序更加模块化、可配置和可扩展。
