@echo off
chcp 65001 >nul
title FSJ04832 安全打包工具

echo.
echo ================================================================
echo                    FSJ04832 安全打包工具
echo ================================================================
echo.
echo 🔒 此工具用于生成客户发布版本，具有以下安全特性：
echo    ✅ 单文件可执行程序 - 无法查看内部文件
echo    ✅ 代码混淆保护 - 防止反编译  
echo    ✅ UPX压缩 - 进一步保护代码
echo    ✅ 调试信息移除 - 不暴露开发信息
echo.

:MENU
echo 请选择操作：
echo.
echo [1] 安全打包 - 增加构建号 (推荐日常使用)
echo [2] 安全打包 - 增加补丁版本 (bug修复)
echo [3] 安全打包 - 增加次版本 (新功能)
echo [4] 安全打包 - 增加主版本 (重大更新)
echo [5] 查看版本历史
echo [6] 启动图形界面
echo [0] 退出
echo.

set /p choice=请输入选择 (0-6): 

if "%choice%"=="1" (
    echo.
    echo 🔒 开始安全打包 - 增加构建号...
    cd /d "%~dp0.."
    python package.py secure build
    goto END
)

if "%choice%"=="2" (
    echo.
    echo 🔒 开始安全打包 - 增加补丁版本...
    cd /d "%~dp0.."
    python package.py secure patch
    goto END
)

if "%choice%"=="3" (
    echo.
    echo 🔒 开始安全打包 - 增加次版本...
    cd /d "%~dp0.."
    python package.py secure minor
    goto END
)

if "%choice%"=="4" (
    echo.
    echo 🔒 开始安全打包 - 增加主版本...
    cd /d "%~dp0.."
    python package.py secure major
    goto END
)

if "%choice%"=="5" (
    echo.
    echo 📋 查看版本历史...
    cd /d "%~dp0.."
    python package.py list
    goto END
)

if "%choice%"=="6" (
    echo.
    echo 🎨 启动图形界面...
    cd /d "%~dp0.."
    python package.py gui
    goto END
)

if "%choice%"=="0" (
    echo.
    echo 👋 再见！
    goto EXIT
)

echo.
echo ❌ 无效选择，请重新输入
echo.
goto MENU

:END
echo.
echo 按任意键返回菜单...
pause >nul
cls
goto MENU

:EXIT
pause
