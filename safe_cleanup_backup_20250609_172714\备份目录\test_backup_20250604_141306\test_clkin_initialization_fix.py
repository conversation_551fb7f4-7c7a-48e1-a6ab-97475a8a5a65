#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试时钟输入控制初始化修复
验证手动初始化控件不再出现转换错误
"""

import sys
import os
import json
import traceback

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication
from utils.Log import logger
from core.services.register.RegisterManager import RegisterManager
from ui.handlers.ModernClkinControlHandler import ModernClkinControlHandler

def test_clkin_initialization():
    """测试时钟输入控制初始化"""
    print("=== 测试时钟输入控制初始化修复 ===")
    
    try:
        # 创建QApplication
        app = QApplication(sys.argv)
        
        print("1. 加载寄存器配置...")
        config_path = os.path.join('lib', 'register.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        print("2. 创建RegisterManager...")
        register_manager = RegisterManager(registers_config)
        
        print("3. 创建现代化时钟输入控制处理器...")
        try:
            handler = ModernClkinControlHandler(
                parent=None,
                register_manager=register_manager
            )
            print("   ✓ 现代化时钟输入控制处理器创建成功")
            
            # 检查手动初始化的控件是否正确注册
            print("4. 检查手动初始化控件...")
            manual_controls = [
                "lineEditClkin0",
                "lineEditClkin1", 
                "lineEditClkin2Oscout",
                "lineEditClkinSelOut"
            ]
            
            for control_name in manual_controls:
                if hasattr(handler.ui, control_name):
                    control = getattr(handler.ui, control_name)
                    current_text = control.text()
                    print(f"   {control_name}: '{current_text}'")
                    
                    # 检查是否在映射表中
                    if hasattr(handler, 'widget_register_map') and control_name in handler.widget_register_map:
                        widget_info = handler.widget_register_map[control_name]
                        bit_def = widget_info.get("bit_def", {})
                        is_manual = bit_def.get("manually_initialized", False)
                        print(f"     - 在映射表中: ✓, 手动初始化: {'✓' if is_manual else '✗'}")
                    else:
                        print(f"     - 在映射表中: ✗")
                else:
                    print(f"   {control_name}: 控件不存在")
            
            print("5. 检查分频比控件...")
            divider_controls = ["PLL1R0Div", "PLL1R1Div", "PLL1R2Div"]
            for control_name in divider_controls:
                if hasattr(handler.ui, control_name):
                    control = getattr(handler.ui, control_name)
                    current_value = control.value()
                    print(f"   {control_name}: {current_value}")
                else:
                    print(f"   {control_name}: 控件不存在")
            
            print("6. 检查时钟频率控件...")
            freq_controls = [
                ("lineEditClkin0", "122.88"),
                ("lineEditClkin1", "122.88"), 
                ("lineEditClkin2Oscout", "153.6")
            ]
            
            for control_name, expected in freq_controls:
                if hasattr(handler.ui, control_name):
                    control = getattr(handler.ui, control_name)
                    current_text = control.text()
                    print(f"   {control_name}: '{current_text}' (期望: '{expected}')")
                else:
                    print(f"   {control_name}: 控件不存在")
            
            print("\n✓ 测试完成，没有发现初始化错误")
            return True
            
        except Exception as e:
            print(f"✗ 创建处理器时出错: {str(e)}")
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_clkin_initialization()
    sys.exit(0 if success else 1)
