#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试自动写入功能修复
验证两个关键问题的修复：
1. 跳转后自动写入问题：修改控件后跳转到对应寄存器，应该自动将修改的寄存器值写入芯片
2. 手动写入地址错误：手动写入时应该写入当前显示的寄存器地址，而不是0x00地址
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication
from PyQt5.QtTest import QTest


def test_auto_write_after_control_change():
    """测试控件修改后的自动写入功能"""
    print("=" * 80)
    print("测试控件修改后的自动写入功能")
    print("=" * 80)

    try:
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        # 创建主窗口
        from ui.windows.RegisterMainWindow import RegisterMainWindow

        # 现在使用依赖注入，不需要repository参数
        main_window = RegisterMainWindow()
        main_window.show()
        QTest.qWait(1000)

        print("✓ 主窗口创建成功")

        # 创建时钟输出控制窗口
        clk_output_window = main_window.tool_window_factory.create_window_by_type('clk_output')
        if clk_output_window:
            print("✓ 时钟输出控制窗口创建成功")
            
            # 测试控件修改
            test_widget_name = "CLKout0_1PD"
            if hasattr(clk_output_window.ui, test_widget_name):
                widget = getattr(clk_output_window.ui, test_widget_name)
                print(f"✓ 找到测试控件: {test_widget_name}")
                
                # 获取控件对应的寄存器地址
                if test_widget_name in clk_output_window.widget_register_map:
                    widget_info = clk_output_window.widget_register_map[test_widget_name]
                    reg_addr = widget_info["register_addr"]
                    print(f"✓ 控件对应寄存器地址: {reg_addr}")
                    
                    # 记录修改前的状态
                    initial_value = widget.isChecked()
                    print(f"控件初始状态: {initial_value}")
                    
                    # 修改控件状态
                    new_value = not initial_value
                    print(f"修改控件状态为: {new_value}")
                    widget.setChecked(new_value)
                    
                    # 等待处理完成
                    QTest.qWait(2000)
                    
                    # 检查是否触发了自动写入
                    print("✅ 控件修改完成，检查日志确认是否自动写入到芯片")
                    
                else:
                    print(f"❌ 控件 {test_widget_name} 未在映射中找到")
                    return False
            else:
                print(f"❌ 未找到测试控件: {test_widget_name}")
                return False
        else:
            print("❌ 时钟输出控制窗口创建失败")
            return False

        return True

    except Exception as e:
        print(f"❌ 测试过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_manual_write_address_fix():
    """测试手动写入地址修复"""
    print("\n" + "=" * 80)
    print("测试手动写入地址修复")
    print("=" * 80)

    try:
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        # 创建主窗口
        from ui.windows.RegisterMainWindow import RegisterMainWindow

        # 现在使用依赖注入，不需要repository参数
        main_window = RegisterMainWindow()
        main_window.show()
        QTest.qWait(1000)

        print("✓ 主窗口创建成功")

        # 选择一个特定的寄存器
        test_register = "0x12"  # 时钟输出相关寄存器
        print(f"选择测试寄存器: {test_register}")
        
        # 通过主窗口选择寄存器
        main_window.on_register_selected(test_register)
        QTest.qWait(500)
        
        # 检查当前选中的寄存器地址
        if hasattr(main_window, 'selected_register_addr'):
            current_addr = main_window.selected_register_addr
            print(f"当前选中寄存器地址: {current_addr}")
            
            if current_addr == test_register:
                print("✅ 寄存器选择成功")
                
                # 设置一个测试值
                test_value = 0x1234
                if hasattr(main_window, 'io_handler'):
                    # 设置IO处理器的值
                    main_window.io_handler.set_value_display(test_value)
                    print(f"设置测试值: 0x{test_value:04X}")
                    
                    # 模拟点击写入按钮
                    print("模拟点击写入按钮...")
                    main_window._handle_write_button_click()
                    
                    QTest.qWait(1000)
                    
                    print("✅ 手动写入完成，检查日志确认写入的地址是否正确")
                    print(f"期望写入地址: {test_register}")
                    
                else:
                    print("❌ 未找到IO处理器")
                    return False
            else:
                print(f"❌ 寄存器选择失败，期望: {test_register}, 实际: {current_addr}")
                return False
        else:
            print("❌ 主窗口没有 selected_register_addr 属性")
            return False

        return True

    except Exception as e:
        print(f"❌ 测试过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_auto_write_mode_setting():
    """测试自动写入模式设置"""
    print("\n" + "=" * 80)
    print("测试自动写入模式设置")
    print("=" * 80)

    try:
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        # 创建主窗口
        from ui.windows.RegisterMainWindow import RegisterMainWindow

        # 现在使用依赖注入，不需要repository参数
        main_window = RegisterMainWindow()
        main_window.show()
        QTest.qWait(1000)

        print("✓ 主窗口创建成功")

        # 检查自动写入模式设置
        auto_write_mode = getattr(main_window, 'auto_write_mode', None)
        print(f"当前自动写入模式: {auto_write_mode}")
        
        # 设置自动写入模式为True
        main_window.auto_write_mode = True
        print("✓ 设置自动写入模式为True")
        
        # 验证设置
        if main_window.auto_write_mode:
            print("✅ 自动写入模式设置成功")
        else:
            print("❌ 自动写入模式设置失败")
            return False

        return True

    except Exception as e:
        print(f"❌ 测试过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 开始自动写入功能修复测试")
    
    # 创建应用程序（只创建一次）
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    try:
        # 测试1：控件修改后的自动写入功能
        print("\n📋 第一阶段：测试控件修改后的自动写入功能")
        success1 = test_auto_write_after_control_change()
        
        # 测试2：手动写入地址修复
        print("\n📋 第二阶段：测试手动写入地址修复")
        success2 = test_manual_write_address_fix()
        
        # 测试3：自动写入模式设置
        print("\n📋 第三阶段：测试自动写入模式设置")
        success3 = test_auto_write_mode_setting()
        
        # 总结结果
        print("\n" + "=" * 80)
        print("📊 测试结果总结")
        print("=" * 80)
        print(f"控件修改后自动写入: {'✅ 通过' if success1 else '❌ 失败'}")
        print(f"手动写入地址修复: {'✅ 通过' if success2 else '❌ 失败'}")
        print(f"自动写入模式设置: {'✅ 通过' if success3 else '❌ 失败'}")
        
        if success1 and success2 and success3:
            print("\n🎉 所有测试通过！自动写入功能修复成功！")
            print("\n修复内容：")
            print("1. ✅ 控件修改后强制自动写入到芯片，确保寄存器值与芯片同步")
            print("2. ✅ 手动写入使用当前选中的寄存器地址，而不是固定的0x00地址")
            sys.exit(0)
        else:
            print("\n❌ 部分测试失败，需要进一步调试")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 测试过程中发生严重错误: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
