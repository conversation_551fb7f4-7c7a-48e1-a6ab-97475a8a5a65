#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
FSJ04832寄存器配置工具 - 性能和稳定性测试
测试应用启动、响应时间、内存使用、长时间运行稳定性
"""

import sys
import os
import time
import psutil
import unittest
import threading
from unittest.mock import Mock, patch
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from PyQt5.QtTest import QTest

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from test_config import TestConfig
from test_utils import TestUtils

class PerformanceStabilityTest(unittest.TestCase):
    """性能和稳定性测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        cls.app = TestUtils.get_test_app()
        cls.test_config = TestConfig()
        cls.process = psutil.Process()
        
    def setUp(self):
        """每个测试方法前的初始化"""
        self.start_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        self.start_time = time.time()
        
    def tearDown(self):
        """每个测试方法后的清理"""
        # 强制垃圾回收
        import gc
        gc.collect()
        
    def test_01_application_startup_time(self):
        """测试应用程序启动时间"""
        print("\n=== 测试应用程序启动时间 ===")
        
        startup_times = []
        
        for i in range(3):  # 测试3次取平均值
            start_time = time.time()
            
            try:
                from ui.windows.RegisterMainWindow import RegisterMainWindow
                
                # 创建主窗口
                main_window = RegisterMainWindow()
                
                end_time = time.time()
                startup_time = end_time - start_time
                startup_times.append(startup_time)
                
                print(f"   第{i+1}次启动时间: {startup_time:.3f}秒")
                
                # 关闭窗口
                main_window.close()
                QTest.qWait(100)
                
            except Exception as e:
                print(f"   第{i+1}次启动失败: {str(e)}")
                startup_times.append(10.0)  # 失败时记录较大值
                
        avg_startup_time = sum(startup_times) / len(startup_times)
        
        print(f"✅ 平均启动时间: {avg_startup_time:.3f}秒")
        
        # 验证启动时间在合理范围内（5秒内）
        self.assertLess(avg_startup_time, 5.0, "启动时间过长")
        
    def test_02_memory_usage_baseline(self):
        """测试内存使用基线"""
        print("\n=== 测试内存使用基线 ===")
        
        try:
            from ui.windows.RegisterMainWindow import RegisterMainWindow
            
            # 记录启动前内存
            memory_before = self.process.memory_info().rss / 1024 / 1024
            
            # 创建主窗口
            main_window = RegisterMainWindow()
            QTest.qWait(1000)  # 等待完全加载
            
            # 记录启动后内存
            memory_after = self.process.memory_info().rss / 1024 / 1024
            memory_increase = memory_after - memory_before
            
            print(f"   启动前内存: {memory_before:.1f} MB")
            print(f"   启动后内存: {memory_after:.1f} MB")
            print(f"   内存增长: {memory_increase:.1f} MB")
            
            # 验证内存使用在合理范围内（增长不超过200MB）
            self.assertLess(memory_increase, 200.0, "内存使用过多")
            
            main_window.close()
            
            print("✅ 内存使用基线测试通过")
            
        except Exception as e:
            print(f"❌ 内存使用基线测试失败: {str(e)}")
            raise
            
    def test_03_ui_response_time(self):
        """测试UI响应时间"""
        print("\n=== 测试UI响应时间 ===")
        
        try:
            from PyQt5.QtWidgets import QPushButton, QLineEdit
            
            # 创建测试控件
            button = QPushButton("测试按钮")
            line_edit = QLineEdit()
            
            # 测试按钮点击响应时间
            click_times = []
            for i in range(10):
                start_time = time.time()
                QTest.mouseClick(button, 1)  # 左键点击
                end_time = time.time()
                click_times.append(end_time - start_time)
                
            avg_click_time = sum(click_times) / len(click_times)
            
            # 测试文本输入响应时间
            input_times = []
            test_text = "测试文本输入"
            for i in range(10):
                line_edit.clear()
                start_time = time.time()
                line_edit.setText(test_text)
                end_time = time.time()
                input_times.append(end_time - start_time)
                
            avg_input_time = sum(input_times) / len(input_times)
            
            print(f"   平均按钮点击响应时间: {avg_click_time*1000:.2f}毫秒")
            print(f"   平均文本输入响应时间: {avg_input_time*1000:.2f}毫秒")
            
            # 验证响应时间在合理范围内（100毫秒内）
            self.assertLess(avg_click_time, 0.1, "按钮响应时间过长")
            self.assertLess(avg_input_time, 0.1, "文本输入响应时间过长")
            
            print("✅ UI响应时间测试通过")
            
        except Exception as e:
            print(f"❌ UI响应时间测试失败: {str(e)}")
            raise
            
    def test_04_event_bus_performance(self):
        """测试事件总线性能"""
        print("\n=== 测试事件总线性能 ===")
        
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            
            event_bus = RegisterUpdateBus.instance()
            
            # 测试大量信号发送性能
            signal_count = 1000
            received_count = 0
            
            def signal_handler(addr, value):
                nonlocal received_count
                received_count += 1
                
            event_bus.register_updated.connect(signal_handler)
            
            # 测试信号发送性能
            start_time = time.time()
            
            for i in range(signal_count):
                event_bus.emit_register_updated(f"0x{i:02X}", i)
                
            # 等待信号处理完成
            QTest.qWait(500)
            
            end_time = time.time()
            duration = end_time - start_time
            
            signals_per_second = signal_count / duration
            
            print(f"   发送信号数: {signal_count}")
            print(f"   接收信号数: {received_count}")
            print(f"   处理时间: {duration:.3f}秒")
            print(f"   处理速度: {signals_per_second:.0f}信号/秒")
            
            # 验证性能指标
            self.assertEqual(received_count, signal_count, "信号丢失")
            self.assertGreater(signals_per_second, 500, "事件总线性能不足")
            
            print("✅ 事件总线性能测试通过")
            
        except Exception as e:
            print(f"❌ 事件总线性能测试失败: {str(e)}")
            raise
            
    def test_05_memory_leak_detection(self):
        """测试内存泄漏检测"""
        print("\n=== 测试内存泄漏检测 ===")
        
        try:
            from ui.windows.RegisterMainWindow import RegisterMainWindow
            
            initial_memory = self.process.memory_info().rss / 1024 / 1024
            memory_samples = [initial_memory]
            
            # 重复创建和销毁窗口
            for i in range(5):
                main_window = RegisterMainWindow()
                QTest.qWait(200)
                
                current_memory = self.process.memory_info().rss / 1024 / 1024
                memory_samples.append(current_memory)
                
                main_window.close()
                QTest.qWait(200)
                
                # 强制垃圾回收
                import gc
                gc.collect()
                
                final_memory = self.process.memory_info().rss / 1024 / 1024
                memory_samples.append(final_memory)
                
                print(f"   第{i+1}轮 - 创建后: {current_memory:.1f}MB, 销毁后: {final_memory:.1f}MB")
                
            final_memory = memory_samples[-1]
            memory_growth = final_memory - initial_memory
            
            print(f"   初始内存: {initial_memory:.1f}MB")
            print(f"   最终内存: {final_memory:.1f}MB")
            print(f"   内存增长: {memory_growth:.1f}MB")
            
            # 验证内存增长在合理范围内（不超过50MB）
            self.assertLess(memory_growth, 50.0, "可能存在内存泄漏")
            
            print("✅ 内存泄漏检测通过")
            
        except Exception as e:
            print(f"❌ 内存泄漏检测失败: {str(e)}")
            raise
            
    def test_06_concurrent_operations(self):
        """测试并发操作稳定性"""
        print("\n=== 测试并发操作稳定性 ===")
        
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            
            event_bus = RegisterUpdateBus.instance()
            
            # 并发测试参数
            thread_count = 5
            operations_per_thread = 100
            results = []
            
            def worker_thread(thread_id):
                """工作线程函数"""
                try:
                    for i in range(operations_per_thread):
                        # 发送事件总线信号
                        event_bus.emit_register_updated(f"0x{thread_id:X}{i:02X}", i)
                        
                        # 短暂延迟
                        time.sleep(0.001)
                        
                    results.append(f"线程{thread_id}完成")
                    
                except Exception as e:
                    results.append(f"线程{thread_id}失败: {str(e)}")
                    
            # 创建并启动线程
            threads = []
            start_time = time.time()
            
            for i in range(thread_count):
                thread = threading.Thread(target=worker_thread, args=(i,))
                threads.append(thread)
                thread.start()
                
            # 等待所有线程完成
            for thread in threads:
                thread.join()
                
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"   并发线程数: {thread_count}")
            print(f"   每线程操作数: {operations_per_thread}")
            print(f"   总操作数: {thread_count * operations_per_thread}")
            print(f"   执行时间: {duration:.3f}秒")
            print(f"   操作速度: {(thread_count * operations_per_thread) / duration:.0f}操作/秒")
            
            # 验证所有线程都成功完成
            success_count = sum(1 for result in results if "完成" in result)
            self.assertEqual(success_count, thread_count, "并发操作失败")
            
            print("✅ 并发操作稳定性测试通过")
            
        except Exception as e:
            print(f"❌ 并发操作稳定性测试失败: {str(e)}")
            raise
            
    def test_07_long_running_stability(self):
        """测试长时间运行稳定性"""
        print("\n=== 测试长时间运行稳定性 ===")
        
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            
            event_bus = RegisterUpdateBus.instance()
            
            # 长时间运行测试参数
            test_duration = 10  # 秒
            operation_interval = 0.1  # 秒
            
            start_time = time.time()
            operation_count = 0
            errors = []
            
            print(f"   开始长时间运行测试，持续{test_duration}秒...")
            
            while time.time() - start_time < test_duration:
                try:
                    # 执行各种操作
                    event_bus.emit_register_updated(f"0x{operation_count:04X}", operation_count)
                    
                    # 模拟时钟源选择
                    if operation_count % 10 == 0:
                        event_bus.emit_clock_source_selected("OSCin", 122.88, 1)
                        
                    operation_count += 1
                    time.sleep(operation_interval)
                    
                except Exception as e:
                    errors.append(str(e))
                    
            end_time = time.time()
            actual_duration = end_time - start_time
            
            print(f"   实际运行时间: {actual_duration:.1f}秒")
            print(f"   执行操作数: {operation_count}")
            print(f"   错误数: {len(errors)}")
            print(f"   平均操作间隔: {actual_duration/operation_count:.3f}秒")
            
            # 验证稳定性
            self.assertLess(len(errors), operation_count * 0.01, "错误率过高")  # 错误率小于1%
            
            if errors:
                print(f"   错误示例: {errors[:3]}")  # 显示前3个错误
                
            print("✅ 长时间运行稳定性测试通过")
            
        except Exception as e:
            print(f"❌ 长时间运行稳定性测试失败: {str(e)}")
            raise
            
    def test_08_resource_cleanup(self):
        """测试资源清理"""
        print("\n=== 测试资源清理 ===")
        
        try:
            initial_memory = self.process.memory_info().rss / 1024 / 1024
            
            # 创建大量临时对象
            temp_objects = []
            for i in range(1000):
                temp_objects.append({
                    'id': i,
                    'data': f"测试数据{i}" * 100,  # 创建较大的字符串
                    'timestamp': time.time()
                })
                
            peak_memory = self.process.memory_info().rss / 1024 / 1024
            
            # 清理对象
            temp_objects.clear()
            
            # 强制垃圾回收
            import gc
            gc.collect()
            
            # 等待一段时间让系统回收内存
            time.sleep(1)
            
            final_memory = self.process.memory_info().rss / 1024 / 1024
            
            print(f"   初始内存: {initial_memory:.1f}MB")
            print(f"   峰值内存: {peak_memory:.1f}MB")
            print(f"   清理后内存: {final_memory:.1f}MB")
            print(f"   内存回收: {peak_memory - final_memory:.1f}MB")
            
            # 验证内存得到有效回收
            memory_recovered = peak_memory - final_memory
            memory_used = peak_memory - initial_memory
            recovery_rate = memory_recovered / memory_used if memory_used > 0 else 0
            
            print(f"   内存回收率: {recovery_rate*100:.1f}%")
            
            # 验证至少回收了50%的内存
            self.assertGreater(recovery_rate, 0.3, "内存回收不足")
            
            print("✅ 资源清理测试通过")
            
        except Exception as e:
            print(f"❌ 资源清理测试失败: {str(e)}")
            raise

def run_performance_stability_tests():
    """运行性能和稳定性测试"""
    print("⚡ 开始FSJ04832寄存器配置工具性能和稳定性测试")
    print("=" * 60)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(PerformanceStabilityTest)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果摘要
    print("\n" + "=" * 60)
    print("📊 性能和稳定性测试结果摘要:")
    print(f"   总测试数: {result.testsRun}")
    print(f"   成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"   失败: {len(result.failures)}")
    print(f"   错误: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"   - {test}")
            
    if result.errors:
        print("\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"   - {test}")
            
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\n🎯 测试成功率: {success_rate:.1f}%")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_performance_stability_tests()
    sys.exit(0 if success else 1)
