#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试PLL2统一计算公式
验证: OSCin × Doubler / PLL2RDivider = PLL2NDivider输入值 × PLL2NDivider
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from ui.handlers.ModernPLLHandler import ModernPLLHandler
from ui.handlers.ModernClkOutputsHandler import ModernClkOutputsHandler
from core.RegisterManager import RegisterManager
from utils.Log import get_module_logger

logger = get_module_logger(__name__)

def test_pll2_unified_formula():
    """测试PLL2统一计算公式"""
    print("="*60)
    print("测试PLL2统一计算公式")
    print("="*60)
    
    app = QApplication(sys.argv)
    
    try:
        # 创建RegisterManager实例
        register_manager = RegisterManager()
        
        # 创建处理器实例
        pll_handler = ModernPLLHandler(register_manager=register_manager)
        clk_outputs_handler = ModernClkOutputsHandler(register_manager=register_manager)
        
        print("\n--- 测试1: PLL2 Prescaler模式 (PLL2NclkMux = 0) ---")
        test_prescaler_mode(pll_handler, clk_outputs_handler)
        
        print("\n--- 测试2: Feedback Mux模式 (PLL2NclkMux = 1) ---")
        test_feedback_mux_mode(pll_handler, clk_outputs_handler)
        
        print("\n--- 测试3: 公式平衡性验证 ---")
        test_formula_balance_verification(pll_handler, clk_outputs_handler)
        
        print("\n--- 测试4: 参数调整建议 ---")
        test_parameter_adjustment_suggestions(pll_handler, clk_outputs_handler)
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        app.quit()

def test_prescaler_mode(pll_handler, clk_outputs_handler):
    """测试PLL2 Prescaler模式"""
    try:
        print("测试PLL2 Prescaler模式...")
        
        # 设置测试参数
        test_oscin = 122.88
        test_doubler = 1  # 1x
        test_r_divider = 2
        test_n_divider = 35
        test_vco_dist_freq = 2949.12
        test_prescaler_ratio = 2
        
        # 设置控件值
        if hasattr(pll_handler.ui, "OSCinFreq"):
            pll_handler.ui.OSCinFreq.setText(str(test_oscin))
        if hasattr(pll_handler.ui, "Doubler"):
            pll_handler.ui.Doubler.setCurrentIndex(test_doubler - 1)
        if hasattr(pll_handler.ui, "PLL2RDivider"):
            pll_handler.ui.PLL2RDivider.setValue(test_r_divider)
        if hasattr(pll_handler.ui, "PLL2NDivider"):
            pll_handler.ui.PLL2NDivider.setValue(test_n_divider)
        if hasattr(pll_handler.ui, "VCODistFreq"):
            pll_handler.ui.VCODistFreq.setText(str(test_vco_dist_freq))
        
        # 设置为Prescaler模式
        if hasattr(pll_handler.ui, "PLL2NclkMux"):
            pll_handler.ui.PLL2NclkMux.setCurrentIndex(0)  # PLL2 Prescaler
        
        print(f"  设置参数:")
        print(f"    OSCin: {test_oscin} MHz")
        print(f"    Doubler: {test_doubler}x")
        print(f"    PLL2RDivider: {test_r_divider}")
        print(f"    PLL2NDivider: {test_n_divider}")
        print(f"    VCODistFreq: {test_vco_dist_freq} MHz")
        print(f"    Prescaler分频比: {test_prescaler_ratio}")
        
        # 手动计算期望值
        left_side = test_oscin * test_doubler / test_r_divider
        prescaler_output = test_vco_dist_freq / test_prescaler_ratio
        right_side = prescaler_output * test_n_divider
        
        print(f"\n  手动计算:")
        print(f"    公式左边: {test_oscin} × {test_doubler} / {test_r_divider} = {left_side:.6f} MHz")
        print(f"    Prescaler输出: {test_vco_dist_freq} / {test_prescaler_ratio} = {prescaler_output:.6f} MHz")
        print(f"    公式右边: {prescaler_output:.6f} × {test_n_divider} = {right_side:.6f} MHz")
        print(f"    公式平衡性: {abs(left_side - right_side):.6f} MHz 差值")
        
        # 程序计算
        result = pll_handler._calculate_pll2_unified_formula(test_oscin)
        print(f"\n  程序计算:")
        print(f"    PLL2PFDFreq: {result:.6f} MHz")
        
        # 验证结果
        if abs(result - left_side) < 0.001:
            print("  ✓ Prescaler模式计算正确")
        else:
            print(f"  ✗ Prescaler模式计算错误，差值: {abs(result - left_side):.6f}")
        
        print("✓ PLL2 Prescaler模式测试完成")
        
    except Exception as e:
        print(f"✗ PLL2 Prescaler模式测试失败: {str(e)}")

def test_feedback_mux_mode(pll_handler, clk_outputs_handler):
    """测试Feedback Mux模式"""
    try:
        print("测试Feedback Mux模式...")
        
        # 设置测试参数
        test_oscin = 122.88
        test_doubler = 1  # 1x
        test_r_divider = 2
        test_n_divider = 35
        test_vco_dist_freq = 2949.12
        test_dclk6_7div = 8
        
        # 设置控件值
        if hasattr(pll_handler.ui, "OSCinFreq"):
            pll_handler.ui.OSCinFreq.setText(str(test_oscin))
        if hasattr(pll_handler.ui, "Doubler"):
            pll_handler.ui.Doubler.setCurrentIndex(test_doubler - 1)
        if hasattr(pll_handler.ui, "PLL2RDivider"):
            pll_handler.ui.PLL2RDivider.setValue(test_r_divider)
        if hasattr(pll_handler.ui, "PLL2NDivider"):
            pll_handler.ui.PLL2NDivider.setValue(test_n_divider)
        if hasattr(pll_handler.ui, "VCODistFreq"):
            pll_handler.ui.VCODistFreq.setText(str(test_vco_dist_freq))
        if hasattr(clk_outputs_handler.ui, 'DCLK6_7DIV'):
            clk_outputs_handler.ui.DCLK6_7DIV.setValue(test_dclk6_7div)
        
        # 设置为Feedback Mux模式
        if hasattr(pll_handler.ui, "PLL2NclkMux"):
            pll_handler.ui.PLL2NclkMux.setCurrentIndex(1)  # Feedback Mux
        if hasattr(pll_handler.ui, "FBMUX"):
            pll_handler.ui.FBMUX.setCurrentIndex(0)  # CLKout6
        
        print(f"  设置参数:")
        print(f"    OSCin: {test_oscin} MHz")
        print(f"    Doubler: {test_doubler}x")
        print(f"    PLL2RDivider: {test_r_divider}")
        print(f"    PLL2NDivider: {test_n_divider}")
        print(f"    VCODistFreq: {test_vco_dist_freq} MHz")
        print(f"    DCLK6_7DIV: {test_dclk6_7div}")
        
        # 手动计算期望值
        left_side = test_oscin * test_doubler / test_r_divider
        clkout6_freq = test_vco_dist_freq / test_dclk6_7div
        right_side = clkout6_freq * test_n_divider
        
        print(f"\n  手动计算:")
        print(f"    公式左边: {test_oscin} × {test_doubler} / {test_r_divider} = {left_side:.6f} MHz")
        print(f"    CLKout6频率: {test_vco_dist_freq} / {test_dclk6_7div} = {clkout6_freq:.6f} MHz")
        print(f"    公式右边: {clkout6_freq:.6f} × {test_n_divider} = {right_side:.6f} MHz")
        print(f"    公式平衡性: {abs(left_side - right_side):.6f} MHz 差值")
        
        # 计算建议的NDivider值
        suggested_n_divider = left_side / clkout6_freq
        print(f"    建议PLL2NDivider: {suggested_n_divider:.6f} (整数: {round(suggested_n_divider)})")
        
        # 程序计算
        result = pll_handler._calculate_pll2_unified_formula(test_oscin)
        print(f"\n  程序计算:")
        print(f"    PLL2PFDFreq: {result:.6f} MHz")
        
        # 验证结果
        if abs(result - left_side) < 0.001:
            print("  ✓ Feedback Mux模式计算正确")
        else:
            print(f"  ✗ Feedback Mux模式计算错误，差值: {abs(result - left_side):.6f}")
        
        print("✓ Feedback Mux模式测试完成")
        
    except Exception as e:
        print(f"✗ Feedback Mux模式测试失败: {str(e)}")

def test_formula_balance_verification(pll_handler, clk_outputs_handler):
    """测试公式平衡性验证"""
    try:
        print("测试公式平衡性验证...")
        
        # 测试不同的参数组合
        test_cases = [
            {
                "name": "平衡案例",
                "oscin": 100.0,
                "doubler": 1,
                "r_divider": 2,
                "n_divider": 20,
                "vco_dist_freq": 1000.0,
                "dclk6_7div": 10
            },
            {
                "name": "不平衡案例",
                "oscin": 122.88,
                "doubler": 2,
                "r_divider": 3,
                "n_divider": 50,
                "vco_dist_freq": 2400.0,
                "dclk6_7div": 6
            }
        ]
        
        for case in test_cases:
            print(f"\n  {case['name']}:")
            
            # 设置参数
            if hasattr(pll_handler.ui, "OSCinFreq"):
                pll_handler.ui.OSCinFreq.setText(str(case['oscin']))
            if hasattr(pll_handler.ui, "Doubler"):
                pll_handler.ui.Doubler.setCurrentIndex(case['doubler'] - 1)
            if hasattr(pll_handler.ui, "PLL2RDivider"):
                pll_handler.ui.PLL2RDivider.setValue(case['r_divider'])
            if hasattr(pll_handler.ui, "PLL2NDivider"):
                pll_handler.ui.PLL2NDivider.setValue(case['n_divider'])
            if hasattr(pll_handler.ui, "VCODistFreq"):
                pll_handler.ui.VCODistFreq.setText(str(case['vco_dist_freq']))
            if hasattr(clk_outputs_handler.ui, 'DCLK6_7DIV'):
                clk_outputs_handler.ui.DCLK6_7DIV.setValue(case['dclk6_7div'])
            
            # 设置为Feedback Mux模式
            if hasattr(pll_handler.ui, "PLL2NclkMux"):
                pll_handler.ui.PLL2NclkMux.setCurrentIndex(1)
            if hasattr(pll_handler.ui, "FBMUX"):
                pll_handler.ui.FBMUX.setCurrentIndex(0)
            
            # 计算公式两边
            left_side = case['oscin'] * case['doubler'] / case['r_divider']
            clkout6_freq = case['vco_dist_freq'] / case['dclk6_7div']
            right_side = clkout6_freq * case['n_divider']
            
            difference = abs(left_side - right_side)
            relative_error = difference / left_side * 100 if left_side > 0 else 0
            
            print(f"    左边: {left_side:.6f} MHz")
            print(f"    右边: {right_side:.6f} MHz")
            print(f"    差值: {difference:.6f} MHz")
            print(f"    相对误差: {relative_error:.3f}%")
            
            if relative_error < 1.0:
                print(f"    ✓ 公式平衡 (误差 < 1%)")
            else:
                print(f"    ⚠ 公式不平衡 (误差 > 1%)")
                suggested_n_divider = left_side / clkout6_freq
                print(f"    建议PLL2NDivider: {round(suggested_n_divider)} (当前: {case['n_divider']})")
        
        print("✓ 公式平衡性验证测试完成")
        
    except Exception as e:
        print(f"✗ 公式平衡性验证测试失败: {str(e)}")

def test_parameter_adjustment_suggestions(pll_handler, clk_outputs_handler):
    """测试参数调整建议"""
    try:
        print("测试参数调整建议...")
        
        # 设置一个明显不平衡的案例
        test_oscin = 122.88
        test_doubler = 1
        test_r_divider = 2
        test_n_divider = 100  # 故意设置一个不合理的值
        test_vco_dist_freq = 2949.12
        test_dclk6_7div = 8
        
        # 设置控件值
        if hasattr(pll_handler.ui, "OSCinFreq"):
            pll_handler.ui.OSCinFreq.setText(str(test_oscin))
        if hasattr(pll_handler.ui, "Doubler"):
            pll_handler.ui.Doubler.setCurrentIndex(test_doubler - 1)
        if hasattr(pll_handler.ui, "PLL2RDivider"):
            pll_handler.ui.PLL2RDivider.setValue(test_r_divider)
        if hasattr(pll_handler.ui, "PLL2NDivider"):
            pll_handler.ui.PLL2NDivider.setValue(test_n_divider)
        if hasattr(pll_handler.ui, "VCODistFreq"):
            pll_handler.ui.VCODistFreq.setText(str(test_vco_dist_freq))
        if hasattr(clk_outputs_handler.ui, 'DCLK6_7DIV'):
            clk_outputs_handler.ui.DCLK6_7DIV.setValue(test_dclk6_7div)
        
        # 设置为Feedback Mux模式
        if hasattr(pll_handler.ui, "PLL2NclkMux"):
            pll_handler.ui.PLL2NclkMux.setCurrentIndex(1)
        if hasattr(pll_handler.ui, "FBMUX"):
            pll_handler.ui.FBMUX.setCurrentIndex(0)
        
        print(f"  不平衡参数设置:")
        print(f"    PLL2NDivider: {test_n_divider} (故意设置的不合理值)")
        
        # 计算合理的NDivider值
        left_side = test_oscin * test_doubler / test_r_divider
        clkout6_freq = test_vco_dist_freq / test_dclk6_7div
        reasonable_n_divider = left_side / clkout6_freq
        
        print(f"    合理的PLL2NDivider: {reasonable_n_divider:.6f} (整数: {round(reasonable_n_divider)})")
        
        # 执行计算，应该会触发参数调整建议
        result = pll_handler._calculate_pll2_unified_formula(test_oscin)
        
        print(f"  计算结果: {result:.6f} MHz")
        print("  (查看日志输出中的参数调整建议)")
        
        print("✓ 参数调整建议测试完成")
        
    except Exception as e:
        print(f"✗ 参数调整建议测试失败: {str(e)}")

def print_unified_formula_summary():
    """打印统一公式总结"""
    print("\n" + "="*60)
    print("PLL2统一计算公式系统总结")
    print("="*60)
    print("""
统一公式：
OSCin × Doubler / PLL2RDivider = PLL2NDivider输入值 × PLL2NDivider

公式说明：
- 左边：PLL2的PFD频率 (参考频率)
- 右边：PLL2的VCO频率 (通过NDivider倍频)
- 平衡条件：左边 = 右边 (PLL锁定条件)

PLL2NDivider输入值来源 (由PLL2NclkMux控制)：
- PLL2NclkMux = 0: PLL2 Prescaler输出 (VCODistFreq / Prescaler分频比)
- PLL2NclkMux = 1: Feedback Mux输出 (CLKout6, CLKout8等)

计算流程：
1. 计算PLL2PFDFreq = OSCin × Doubler / PLL2RDivider
2. 根据PLL2NclkMux获取NDivider输入频率
3. 验证公式平衡性：PFDFreq ?= NDivider输入 × NDivider
4. 如果不平衡，建议调整PLL2NDivider值

优势：
- 统一的计算框架，适用于所有PLL2模式
- 自动验证参数设置的合理性
- 智能建议最佳的分频比设置
- 确保PLL能够稳定锁定
- 支持复杂的反馈路径配置
""")
    print("="*60)

if __name__ == "__main__":
    print_unified_formula_summary()
    test_pll2_unified_formula()
