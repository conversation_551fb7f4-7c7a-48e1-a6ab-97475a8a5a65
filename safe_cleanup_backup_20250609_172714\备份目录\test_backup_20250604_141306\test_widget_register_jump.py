#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试工具界面控件状态改变时寄存器表格跳转功能
"""

import sys
import os
sys.path.append('.')

# 设置环境变量以避免GUI问题
os.environ['QT_QPA_PLATFORM'] = 'offscreen'

from PyQt5.QtWidgets import QApplication
from ui.windows.RegisterMainWindow import RegisterMainWindow
from core.services.spi.spi_service_impl import SPIServiceImpl
from core.repositories.register_repository import RegisterRepository
from utils.Log import logger

def test_widget_register_jump():
    """测试工具界面控件状态改变时的寄存器表格跳转"""
    
    print("=== 测试工具界面控件状态改变时的寄存器表格跳转 ===")
    
    # 创建应用程序
    app = QApplication([])
    
    try:
        # 创建服务
        spi_service = SPIServiceImpl()
        spi_service.initialize()
        register_repo = RegisterRepository(spi_service)
        
        # 创建主窗口
        main_window = RegisterMainWindow(register_repo)
        print("✓ 主窗口创建成功")
        
        # 检查主窗口的关键组件
        components = {
            'table_handler': '表格处理器',
            'display_manager': '显示管理器', 
            'event_coordinator': '事件协调器',
            'register_service': '寄存器服务',
            'tool_window_factory': '工具窗口工厂'
        }
        
        print("\n--- 检查主窗口组件 ---")
        for component, desc in components.items():
            if hasattr(main_window, component):
                obj = getattr(main_window, component)
                print(f"✓ {desc} ({component}): {type(obj).__name__}")
            else:
                print(f"✗ {desc} ({component}): 不存在")
        
        # 创建时钟输入控制窗口
        print("\n--- 创建时钟输入控制窗口 ---")
        clkin_window = main_window.tool_window_factory.create_window_by_type('clkin_control')
        
        if clkin_window:
            print(f"✓ 时钟输入控制窗口创建成功: {type(clkin_window).__name__}")
            
            # 检查控件映射
            if hasattr(clkin_window, 'widget_register_map'):
                widget_map = clkin_window.widget_register_map
                print(f"✓ 找到 {len(widget_map)} 个控件映射")
                
                # 选择一个控件进行测试
                test_widget_name = None
                test_register_addr = None
                test_widget_info = None
                
                for widget_name, widget_info in widget_map.items():
                    if widget_info.get('widget_type') == 'checkbox':
                        test_widget_name = widget_name
                        test_register_addr = widget_info['register_addr']
                        test_widget_info = widget_info
                        break
                
                if test_widget_name:
                    print(f"\n--- 测试控件 {test_widget_name} (寄存器 {test_register_addr}) ---")
                    
                    # 检查现代化处理器的跳转方法
                    if hasattr(clkin_window, '_trigger_register_table_navigation'):
                        print("✓ 现代化处理器有 _trigger_register_table_navigation 方法")
                        
                        # 测试1: 直接调用跳转方法
                        print("\n=== 测试1: 直接调用跳转方法 ===")
                        try:
                            print(f"调用 _trigger_register_table_navigation('{test_register_addr}')")
                            clkin_window._trigger_register_table_navigation(test_register_addr)
                            print("✓ 跳转方法调用成功")
                            
                            # 检查主窗口状态
                            if hasattr(main_window, 'selected_register_addr'):
                                current_addr = main_window.selected_register_addr
                                print(f"主窗口当前选中寄存器: {current_addr}")
                                if current_addr == test_register_addr:
                                    print("✓ 寄存器表格成功跳转到目标寄存器")
                                else:
                                    print(f"✗ 寄存器表格未跳转到目标寄存器 (期望: {test_register_addr}, 实际: {current_addr})")
                            else:
                                print("✗ 主窗口没有 selected_register_addr 属性")
                                
                        except Exception as e:
                            print(f"✗ 跳转方法调用失败: {e}")
                            import traceback
                            traceback.print_exc()
                        
                        # 测试2: 模拟控件值变化
                        print("\n=== 测试2: 模拟控件值变化 ===")
                        try:
                            print(f"模拟控件 {test_widget_name} 值变化")
                            
                            # 调用控件变化处理方法
                            if hasattr(clkin_window, '_on_widget_changed'):
                                print("调用 _on_widget_changed 方法")
                                clkin_window._on_widget_changed(test_widget_name, True)  # 模拟checkbox选中
                                print("✓ 控件变化处理成功")
                                
                                # 检查是否触发了跳转
                                if hasattr(main_window, 'selected_register_addr'):
                                    current_addr = main_window.selected_register_addr
                                    print(f"控件变化后主窗口当前选中寄存器: {current_addr}")
                                    if current_addr == test_register_addr:
                                        print("✓ 控件变化成功触发寄存器表格跳转")
                                    else:
                                        print(f"✗ 控件变化未触发寄存器表格跳转 (期望: {test_register_addr}, 实际: {current_addr})")
                                        
                            else:
                                print("✗ 现代化处理器没有 _on_widget_changed 方法")
                                
                        except Exception as e:
                            print(f"✗ 控件变化处理失败: {e}")
                            import traceback
                            traceback.print_exc()
                        
                        # 测试3: 检查表格处理器状态
                        print("\n=== 测试3: 检查表格处理器状态 ===")
                        try:
                            if hasattr(main_window, 'table_handler'):
                                table_handler = main_window.table_handler
                                print(f"表格处理器类型: {type(table_handler).__name__}")
                                
                                # 检查当前寄存器地址
                                if hasattr(table_handler, 'current_register_addr'):
                                    current_table_addr = table_handler.current_register_addr
                                    print(f"表格处理器当前寄存器地址: {current_table_addr}")
                                    
                                    if current_table_addr == test_register_addr:
                                        print("✓ 表格处理器已跳转到目标寄存器")
                                    else:
                                        print(f"✗ 表格处理器未跳转到目标寄存器 (期望: {test_register_addr}, 实际: {current_table_addr})")
                                else:
                                    print("✗ 表格处理器没有 current_register_addr 属性")
                                    
                                # 检查表格内容
                                if hasattr(table_handler, 'bit_field_table') and table_handler.bit_field_table:
                                    row_count = table_handler.bit_field_table.rowCount()
                                    print(f"表格当前行数: {row_count}")
                                    if row_count > 0:
                                        print("✓ 表格有内容显示")
                                    else:
                                        print("✗ 表格没有内容")
                                else:
                                    print("✗ 表格处理器没有 bit_field_table 或表格为空")
                            else:
                                print("✗ 主窗口没有 table_handler")
                                
                        except Exception as e:
                            print(f"✗ 检查表格处理器状态失败: {e}")
                            import traceback
                            traceback.print_exc()
                            
                    else:
                        print("✗ 现代化处理器没有 _trigger_register_table_navigation 方法")
                        
                else:
                    print("✗ 没有找到可测试的控件")
            else:
                print("✗ 时钟输入控制窗口没有控件映射")
        else:
            print("✗ 时钟输入控制窗口创建失败")
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_widget_register_jump()
