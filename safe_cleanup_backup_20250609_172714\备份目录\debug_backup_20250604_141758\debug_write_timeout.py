#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
写入超时问题诊断工具

用于诊断寄存器 0x5B 写入超时的问题
"""

import sys
import os
import logging
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# 设置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def diagnose_write_timeout():
    """诊断写入超时问题"""
    print("=" * 60)
    print("寄存器 0x5B 写入超时问题诊断")
    print("=" * 60)
    
    try:
        # 1. 检查SPI服务配置
        print("\n1. 检查SPI服务配置:")
        from core.services.spi.spi_service_impl import SPIServiceImpl
        print(f"   默认超时时间: {SPIServiceImpl.DEFAULT_TIMEOUT} 秒")
        print(f"   最大队列大小: {SPIServiceImpl.MAX_QUEUE_SIZE}")
        
        # 2. 检查寄存器配置
        print("\n2. 检查寄存器 0x5B 配置:")
        import json
        config_path = os.path.join('lib', 'register.json')
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                registers_config = json.load(f)
            
            if "0x5B" in registers_config:
                reg_config = registers_config["0x5B"]
                print(f"   寄存器 0x5B 位字段数量: {len(reg_config.get('bits', []))}")
                for bit in reg_config.get('bits', []):
                    if bit.get('widget_name'):
                        print(f"   - {bit['name']}: 控件={bit['widget_name']}, 类型={bit['widget_type']}")
            else:
                print("   ❌ 寄存器 0x5B 配置未找到")
        else:
            print("   ❌ 寄存器配置文件未找到")
        
        # 3. 检查ModernBaseHandler修复
        print("\n3. 检查ModernBaseHandler修复:")
        from ui.handlers.ModernBaseHandler import ModernBaseHandler
        
        # 检查是否有防循环更新的标志
        handler_source = open('ui/handlers/ModernBaseHandler.py', 'r', encoding='utf-8').read()
        if '_updating_from_widget' in handler_source:
            print("   ✅ 防循环更新标志已添加")
        else:
            print("   ❌ 防循环更新标志缺失")
        
        if 'blockSignals(True)' in handler_source:
            print("   ✅ 信号阻断机制已添加")
        else:
            print("   ❌ 信号阻断机制缺失")
        
        # 4. 检查IO Handler修复
        print("\n4. 检查IO Handler修复:")
        io_handler_source = open('ui/handlers/ModernRegisterIOHandler.py', 'r', encoding='utf-8').read()
        if '_updating_display' in io_handler_source:
            print("   ✅ IO Handler防写入标志已添加")
        else:
            print("   ❌ IO Handler防写入标志缺失")
        
        # 5. 模拟写入操作测试
        print("\n5. 模拟写入操作测试:")
        app = QApplication(sys.argv)
        
        # 创建模拟的RegisterManager
        from core.services.register.RegisterManager import RegisterManager
        register_manager = RegisterManager(registers_config)
        
        # 测试寄存器值更新
        print("   测试寄存器值更新...")
        try:
            # 设置初始值
            register_manager.set_register_value("0x5B", 0x0000)
            initial_value = register_manager.get_register_value("0x5B")
            print(f"   初始值: 0x{initial_value:04X}")
            
            # 更新LOS_EN位
            success = register_manager.set_bit_field_value("0x5B", "LOS_EN", 1)
            if success:
                new_value = register_manager.get_register_value("0x5B")
                print(f"   更新后值: 0x{new_value:04X}")
                print("   ✅ 寄存器值更新正常")
            else:
                print("   ❌ 寄存器值更新失败")
                
        except Exception as e:
            print(f"   ❌ 寄存器测试出错: {str(e)}")
        
        # 6. 检查自动写入配置
        print("\n6. 检查自动写入配置:")
        from core.services.config.ConfigurationService import ConfigurationService
        config_service = ConfigurationService()
        auto_write_enabled = config_service.load_auto_write_mode()
        print(f"   自动写入模式: {'启用' if auto_write_enabled else '禁用'}")
        
        # 7. 建议解决方案
        print("\n7. 建议解决方案:")
        print("   a) 增加SPI超时时间 (已修改为5秒)")
        print("   b) 检查SPI硬件连接")
        print("   c) 确认自动写入功能是否需要启用")
        print("   d) 检查是否有重复的写入操作")
        print("   e) 考虑在硬件模式下禁用自动写入")
        
        print("\n" + "=" * 60)
        print("诊断完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"诊断过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    diagnose_write_timeout()
