# 版本管理系统演示

## 🎯 功能概述

本系统实现了完整的自动版本管理功能，确保应用程序的窗口标题、关于对话框和打包文件名都与版本号保持一致。

## 📋 实现的功能

### ✅ 1. 自动版本号管理
- **四段式版本号**: `主版本.次版本.补丁版本.构建号`
- **自动增加**: 支持不同类型的版本号增加
- **持久化存储**: 版本信息保存在 `version.json` 文件中

### ✅ 2. 窗口标题版本显示
- **动态标题**: 窗口标题自动包含版本号
- **格式**: `FSJ04832 寄存器配置工具 v1.0.1`
- **实时更新**: 版本更新后标题自动更新

### ✅ 3. 关于对话框版本信息
- **完整信息**: 显示版本号、构建日期、公司信息等
- **多处同步**: 所有关于对话框都使用统一的版本服务
- **错误处理**: 版本服务不可用时显示默认信息

### ✅ 4. 打包文件名版本化
- **自动命名**: 可执行文件名包含版本号
- **格式**: `FSJConfigTool1.0.1.exe`
- **spec文件更新**: 自动更新PyInstaller配置

## 🚀 使用演示

### 当前版本状态
```
版本: 1.0.1.0
窗口标题: FSJ04832 寄存器配置工具 v1.0.1
可执行文件名: FSJConfigTool1.0.1
```

### 版本号增加演示

#### 1. 增加构建号
```bash
python build_exe.py --version-type build
# 1.0.1.0 → *******
```

#### 2. 增加补丁版本
```bash
python build_exe.py --version-type patch
# ******* → *******
```

#### 3. 增加次版本
```bash
python build_exe.py --version-type minor
# ******* → *******
```

#### 4. 增加主版本
```bash
python build_exe.py --version-type major
# ******* → *******
```

## 📁 文件变化演示

### 版本配置文件 (version.json)
```json
{
  "version": {
    "major": 1,
    "minor": 0,
    "patch": 1,
    "build": 0
  },
  "app_info": {
    "name": "FSJ04832 寄存器配置工具",
    "description": "用于配置和管理FSJ04832寄存器的专业工具",
    "company": "FSJ Technology",
    "copyright": "© 2024 FSJ Technology",
    "author": "开发团队"
  },
  "build_info": {
    "last_build_date": "2025-06-04 15:47:59",
    "build_type": "release",
    "target_platform": "windows"
  }
}
```

### PyInstaller配置文件 (build.spec)
```python
# 第43行自动更新
name='FSJConfigTool1.0.1',
```

## 🔧 技术实现

### 1. 版本服务 (VersionService)
- **单例模式**: 确保全局唯一的版本管理实例
- **多路径加载**: 支持从多个位置加载版本文件
- **错误处理**: 版本文件不存在时使用默认配置

### 2. 主窗口集成
```python
def _setup_window_title(self):
    """设置带版本号的窗口标题"""
    try:
        from core.services.version.VersionService import VersionService
        version_service = VersionService.instance()
        title = version_service.get_app_title_with_version()
        self.setWindowTitle(title)
    except Exception as e:
        # 错误处理
        self.setWindowTitle('FSJ04832 寄存器配置工具')
```

### 3. 关于对话框集成
```python
def show_about_dialog(self):
    """显示关于对话框"""
    try:
        version_service = VersionService.instance()
        app_name = version_service.get_app_name()
        version = version_service.get_version_string()
        # ... 构建关于信息
    except Exception as e:
        # 使用默认信息
```

### 4. 构建脚本集成
```python
def update_spec_file(version_manager, spec_file):
    """更新spec文件中的版本信息"""
    exe_name = version_manager.get_exe_name()
    # 正则表达式替换文件名
    content = re.sub(r"name='[^']*'", f"name='{exe_name}'", content)
```

## 📊 测试验证

### 版本服务测试
```
✅ 版本服务单例模式正常
✅ 版本信息重新加载正常
✅ 所有版本信息方法正常工作
```

### 集成测试
```
✅ 窗口标题版本信息正确
✅ 关于对话框版本信息获取成功
✅ 版本号格式符合规范
✅ 开发构建检测正常
```

### 构建测试
```
✅ 版本号自动增加
✅ spec文件自动更新
✅ 可执行文件名正确生成
```

## 🎨 用户界面效果

### 窗口标题
```
之前: FSJ04832 寄存器配置工具
现在: FSJ04832 寄存器配置工具 v1.0.1
```

### 关于对话框
```
FSJ04832 寄存器配置工具
版本: 1.0.1.0
描述: 用于配置和管理FSJ04832寄存器的专业工具
开发者: FSJ Technology
版权: © 2024 FSJ Technology
构建日期: 2025-06-04 15:47:59
```

### 可执行文件
```
之前: FSJConfigTool.exe
现在: FSJConfigTool1.0.1.exe
```

## 🔄 版本更新流程

1. **开发阶段**: 使用 `--version-type build` 增加构建号
2. **Bug修复**: 使用 `--version-type patch` 增加补丁版本
3. **新功能**: 使用 `--version-type minor` 增加次版本
4. **重大更新**: 使用 `--version-type major` 增加主版本

## 🎯 优势特点

### ✅ 一致性
- 所有版本显示位置保持同步
- 单一版本源，避免不一致

### ✅ 自动化
- 构建时自动更新版本号
- 无需手动修改多个文件

### ✅ 可靠性
- 完善的错误处理机制
- 版本服务不可用时的后备方案

### ✅ 可扩展性
- 易于添加新的版本信息字段
- 支持自定义版本号格式

## 📝 总结

通过实现完整的版本管理系统，现在每次打包后：

1. **窗口标题** 自动显示版本号
2. **关于对话框** 显示完整版本信息
3. **可执行文件名** 包含版本号
4. **所有信息** 保持一致和同步

这确保了用户始终能够清楚地知道当前使用的软件版本，提高了软件的专业性和可维护性。
