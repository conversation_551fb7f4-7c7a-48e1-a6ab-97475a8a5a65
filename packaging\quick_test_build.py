#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试打包修复效果
"""

import subprocess
import sys
import os
from pathlib import Path

def test_import_modules():
    """测试关键模块是否可以导入"""
    print("🧪 测试关键模块导入...")
    
    critical_modules = [
        'dis', 'os', 'sys', 'io', 'codecs', 'encodings',
        'inspect', 'types', 'typing', 'json', 'pathlib'
    ]
    
    failed_modules = []
    
    for module_name in critical_modules:
        try:
            __import__(module_name)
            print(f"  ✅ {module_name}")
        except ImportError as e:
            print(f"  ❌ {module_name}: {e}")
            failed_modules.append(module_name)
    
    if failed_modules:
        print(f"\n⚠️ 失败的模块: {failed_modules}")
        return False
    else:
        print("\n✅ 所有关键模块导入成功！")
        return True

def quick_build_test():
    """快速构建测试"""
    print("\n🔨 执行快速构建测试...")
    
    # 切换到packaging目录
    packaging_dir = Path(__file__).parent
    os.chdir(packaging_dir)
    
    try:
        # 尝试运行PyInstaller来验证spec文件
        cmd = ['python', '-c', '''
import sys
sys.path.insert(0, "scripts")
try:
    with open("scripts/build_secure.spec", "r", encoding="utf-8") as f:
        spec_content = f.read()
    exec(spec_content)
    print("build_secure.spec syntax OK")
except Exception as e:
    print(f"build_secure.spec error: {e}")
    sys.exit(1)
''']
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ spec文件语法验证通过")
            print(result.stdout)
            return True
        else:
            print("❌ spec文件验证失败")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ 验证超时")
        return False
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        return False

def main():
    """主函数"""
    print("🚀 快速测试打包修复效果")
    print("=" * 40)
    
    # 测试模块导入
    import_ok = test_import_modules()
    
    # 测试spec文件
    spec_ok = quick_build_test()
    
    print("\n" + "=" * 40)
    
    if import_ok and spec_ok:
        print("🎉 测试通过！可以尝试重新打包了")
        print("\n📋 建议的打包命令:")
        print("  cd packaging")
        print("  python package.py secure patch")
        print("\n💡 如果仍有问题，请检查:")
        print("  1. PyInstaller版本是否兼容")
        print("  2. Python环境是否完整")
        print("  3. 是否有其他依赖冲突")
    else:
        print("⚠️ 测试未完全通过，可能需要进一步调试")
        
        if not import_ok:
            print("  - 模块导入有问题，检查Python环境")
        if not spec_ok:
            print("  - spec文件有问题，检查配置语法")

if __name__ == "__main__":
    main()
