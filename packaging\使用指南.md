# FSJ04832 打包管理系统 - 使用指南

## 🚀 快速开始

### 方法一：图形界面（推荐）
```bash
# 进入打包目录
cd packaging

# 启动图形界面
python package.py gui
```

### 方法二：批处理启动器
双击 `packaging/launchers/启动打包工具.bat`

### 方法三：命令行操作
```bash
cd packaging
python package.py build build    # 增加构建号
python package.py list           # 查看版本历史
```

## 📋 主要功能

### 🔧 版本构建
- **构建号增加**: `python package.py build build`
- **补丁版本**: `python package.py build patch`
- **次版本**: `python package.py build minor`
- **主版本**: `python package.py build major`

### 📊 版本管理
- **查看历史**: `python package.py list`
- **清理旧版本**: `python package.py clean`
- **系统测试**: `python package.py test`

### 🎨 图形界面
- **可视化操作**: 直观的版本选择和构建
- **实时进度**: 构建过程实时显示
- **大字体优化**: 清晰易读的界面

## 📁 输出结果

### 版本化文件夹
```
releases/
├── 20250604_170841_v1.0.2.3/     # 时间戳_版本号
│   ├── FSJConfigTool1.0.2.3.exe  # 可执行文件
│   └── version_info.json         # 版本信息
└── latest/                        # 最新版本链接
```

### 版本号格式
```
主版本.次版本.补丁版本.构建号
   1   .  0   .   2   .  3
```

### 文件命名
- **可执行文件**: `FSJConfigTool1.0.2.3.exe`
- **窗口标题**: `FSJ04832 寄存器配置工具 v1.0.2.3`
- **版本目录**: `20250604_170841_v1.0.2.3`

## ✅ 系统验证

### 运行测试
```bash
cd packaging
python package.py test
```

**预期结果:**
```
目录结构                 : ✅ 通过
配置文件                 : ✅ 通过
脚本文件                 : ✅ 通过
工具脚本                 : ✅ 通过
打包入口                 : ✅ 通过
项目集成                 : ✅ 通过

总计: 6/6 个测试通过
```

## 🎯 使用场景

### 日常开发
1. 修改代码后需要测试
2. 使用构建号增加: `python package.py build build`
3. 生成新的测试版本

### 重要修复
1. 修复重要bug
2. 使用补丁版本: `python package.py build patch`
3. 发布修复版本

### 功能发布
1. 添加新功能
2. 使用次版本: `python package.py build minor`
3. 发布功能版本

### 重大更新
1. 架构重构或重大变更
2. 使用主版本: `python package.py build major`
3. 发布重大版本

## 🔧 故障排除

### 常见问题

#### 1. 模块导入错误
```
ModuleNotFoundError: No module named 'xxx'
```
**解决方案:**
- 确保在 `packaging` 目录下运行
- 检查Python路径设置
- 运行系统测试诊断

#### 2. 编码错误
```
UnicodeEncodeError: 'gbk' codec can't encode character
```
**解决方案:**
- 使用编码安全脚本: `scripts/build_safe.py`
- 或通过统一入口: `python package.py build build`

#### 3. 权限问题
```
PermissionError: [Errno 13] Permission denied
```
**解决方案:**
- 以管理员身份运行命令行
- 检查文件和目录权限
- 关闭可能占用文件的程序

#### 4. 构建失败
```
构建失败: xxx
```
**解决方案:**
- 检查PyInstaller是否正确安装
- 查看详细错误日志
- 确保所有依赖模块可用

### 诊断步骤

1. **运行系统测试**
   ```bash
   cd packaging
   python package.py test
   ```

2. **检查环境**
   ```bash
   python --version
   pip list | grep PyQt5
   pip list | grep pyinstaller
   ```

3. **查看日志**
   - 检查构建输出信息
   - 查看错误详细信息
   - 参考文档解决方案

## 📚 参考文档

### 系统文档
- `README.md` - 系统概述
- `docs/packaging_system_summary.md` - 系统总结
- `docs/encoding_fix.md` - 编码问题解决
- `docs/version_sync_fix.md` - 版本同步修复

### 配置文件
- `config/version.json` - 版本配置
- `config/packaging_config.json` - 打包配置

### 测试脚本
- `tests/test_packaging.py` - 系统测试
- `tests/test_version_display.py` - 版本显示测试
- `tests/test_versioned_build.py` - 构建测试

## 💡 最佳实践

### 1. 版本管理策略
- **日常开发**: 使用构建号增加
- **bug修复**: 使用补丁版本增加
- **新功能**: 使用次版本增加
- **重大更新**: 使用主版本增加

### 2. 存储管理
- 定期清理旧版本节省空间
- 保留重要里程碑版本
- 备份关键版本和配置

### 3. 团队协作
- 统一使用打包系统
- 共享版本配置文件
- 建立版本发布流程

### 4. 质量保证
- 构建前运行测试
- 验证版本信息正确
- 测试可执行文件功能

## 🎉 总结

FSJ04832 打包管理系统提供了：

- ✅ **统一的打包入口** - 一个命令搞定所有操作
- ✅ **完整的版本管理** - 自动版本号同步和历史保留
- ✅ **友好的用户界面** - 图形界面和命令行双重支持
- ✅ **可靠的构建系统** - 经过充分测试的构建流程
- ✅ **便捷的管理工具** - 版本查看、清理、测试一应俱全

**开始使用:**
```bash
cd packaging
python package.py gui
```

**获取帮助:**
```bash
python package.py help
```

---

**🎯 目标**: 让版本管理和打包变得简单、可靠、高效！
