#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试端口刷新修复
验证是否解决了过多SPI操作的问题
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PyQt5.QtCore import QTimer

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.Log import get_module_logger

logger = get_module_logger(__name__)
from core.services.spi.spi_service_impl import SPIServiceImpl
from core.repositories.register_repository import RegisterRepository
from ui.handlers.ModernRegisterIOHandler import ModernRegisterIOHandler


class TestPortRefreshWindow(QMainWindow):
    """测试端口刷新修复的窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("测试端口刷新修复")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 添加说明标签
        info_label = QLabel("此测试验证端口刷新修复是否有效\n观察日志输出，应该看到减少的SPI操作")
        layout.addWidget(info_label)
        
        # 添加测试按钮
        test_btn = QPushButton("开始测试端口刷新")
        test_btn.clicked.connect(self.start_test)
        layout.addWidget(test_btn)
        
        # 状态标签
        self.status_label = QLabel("准备测试...")
        layout.addWidget(self.status_label)
        
        # 初始化变量
        self.spi_service = None
        self.io_handler = None
        self.refresh_count = 0
        
    def start_test(self):
        """开始测试"""
        try:
            self.status_label.setText("正在初始化SPI服务...")
            logger.info("=== 开始端口刷新修复测试 ===")
            
            # 创建SPI服务
            self.spi_service = SPIServiceImpl()
            self.spi_service.initialize()
            register_repo = RegisterRepository(self.spi_service)
            
            # 创建IO处理器
            self.status_label.setText("正在创建IO处理器...")
            self.io_handler = ModernRegisterIOHandler.create_for_testing(self)
            self.io_handler.spi_service = self.spi_service
            self.io_handler.register_repo = register_repo
            
            # 连接信号以监控刷新次数
            if hasattr(self.spi_service, 'ports_refreshed'):
                self.spi_service.ports_refreshed.connect(self.on_ports_refreshed)
            
            # 模拟多次快速刷新（这应该被优化掉）
            self.status_label.setText("正在测试多次快速刷新...")
            logger.info("开始测试多次快速端口刷新...")
            
            # 连续刷新5次，看看是否会被去重
            for i in range(5):
                QTimer.singleShot(i * 100, self.trigger_refresh)
            
            # 5秒后显示结果
            QTimer.singleShot(5000, self.show_results)
            
        except Exception as e:
            logger.error(f"测试失败: {str(e)}")
            self.status_label.setText(f"测试失败: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def trigger_refresh(self):
        """触发端口刷新"""
        if self.io_handler:
            logger.info(f"触发第 {self.refresh_count + 1} 次端口刷新")
            self.io_handler.refresh_ports()
    
    def on_ports_refreshed(self, ports_info):
        """端口刷新信号处理"""
        self.refresh_count += 1
        logger.info(f"收到第 {self.refresh_count} 次端口刷新信号，端口数量: {len(ports_info)}")
        self.status_label.setText(f"已收到 {self.refresh_count} 次端口刷新信号")
    
    def show_results(self):
        """显示测试结果"""
        logger.info("=== 端口刷新修复测试完成 ===")
        logger.info(f"总共收到 {self.refresh_count} 次端口刷新信号")
        
        if self.refresh_count <= 2:
            result = "✅ 测试通过：端口刷新次数已优化"
            logger.info("测试结果：端口刷新优化有效")
        else:
            result = "❌ 测试失败：仍有过多的端口刷新"
            logger.warning("测试结果：端口刷新优化可能无效")
        
        self.status_label.setText(f"{result}\n总刷新次数: {self.refresh_count}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestPortRefreshWindow()
    window.show()
    
    # 启动应用
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
