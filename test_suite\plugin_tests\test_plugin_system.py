#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
插件系统测试
验证插件系统的基本功能
"""

import sys
import os
import time

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication
from core.services.plugin.PluginManager import plugin_manager


def test_plugin_discovery():
    """测试插件发现功能"""
    print("🔍 测试插件发现功能...")
    
    # 添加插件目录
    plugin_dir = os.path.join(project_root, "plugins")
    plugin_manager.add_plugin_directory(plugin_dir)
    
    # 扫描插件
    plugin_manager.scan_plugins()
    
    # 获取插件列表
    plugins = plugin_manager.get_plugin_list()
    
    print(f"✅ 发现 {len(plugins)} 个插件:")
    for plugin in plugins:
        print(f"   - {plugin.name} v{plugin.version}")
        print(f"     描述: {plugin.description}")
        print(f"     文件: {plugin.file_path}")
        print()
    
    return len(plugins) > 0


def test_plugin_initialization():
    """测试插件初始化"""
    print("🚀 测试插件初始化...")
    
    # 创建模拟的主窗口上下文
    app = QApplication([])
    
    class MockMainWindow:
        def __init__(self):
            self.batch_manager = None
    
    mock_main_window = MockMainWindow()
    
    # 初始化插件
    plugin_manager.initialize_plugins(mock_main_window)
    
    # 检查插件状态
    plugins = plugin_manager.get_plugin_list()
    initialized_count = sum(1 for p in plugins if p.enabled and p.instance)
    
    print(f"✅ 成功初始化 {initialized_count}/{len(plugins)} 个插件")
    
    for plugin in plugins:
        status = "✅ 已初始化" if plugin.enabled and plugin.instance else "❌ 初始化失败"
        print(f"   - {plugin.name}: {status}")
    
    return initialized_count > 0


def test_tool_window_plugins():
    """测试工具窗口插件"""
    print("🪟 测试工具窗口插件...")
    
    tool_plugins = plugin_manager.get_tool_window_plugins()
    
    print(f"✅ 发现 {len(tool_plugins)} 个工具窗口插件:")
    
    for plugin in tool_plugins:
        print(f"   - {plugin.name}")
        print(f"     菜单文本: {plugin.menu_text}")
        print(f"     图标路径: {plugin.icon_path or '无'}")
        
        # 测试窗口创建
        try:
            window = plugin.create_window()
            if window:
                print(f"     ✅ 窗口创建成功: {type(window).__name__}")
                window.close()  # 立即关闭测试窗口
            else:
                print(f"     ❌ 窗口创建失败")
        except Exception as e:
            print(f"     ❌ 窗口创建异常: {str(e)}")
        print()
    
    return len(tool_plugins) > 0


def test_plugin_enable_disable():
    """测试插件启用/禁用功能"""
    print("🔄 测试插件启用/禁用功能...")
    
    plugins = plugin_manager.get_plugin_list()
    if not plugins:
        print("❌ 没有可用的插件进行测试")
        return False
    
    test_plugin = plugins[0]
    original_state = test_plugin.enabled
    
    print(f"测试插件: {test_plugin.name}")
    print(f"原始状态: {'启用' if original_state else '禁用'}")
    
    try:
        # 测试禁用
        if test_plugin.enabled:
            plugin_manager.disable_plugin(test_plugin.name)
            print(f"✅ 插件禁用成功")
        
        # 测试启用
        plugin_manager.enable_plugin(test_plugin.name)
        print(f"✅ 插件启用成功")
        
        # 恢复原始状态
        if not original_state:
            plugin_manager.disable_plugin(test_plugin.name)
        
        return True
        
    except Exception as e:
        print(f"❌ 插件启用/禁用测试失败: {str(e)}")
        return False


def test_plugin_cleanup():
    """测试插件清理功能"""
    print("🧹 测试插件清理功能...")
    
    try:
        plugin_manager.cleanup_all_plugins()
        print("✅ 插件清理成功")
        return True
    except Exception as e:
        print(f"❌ 插件清理失败: {str(e)}")
        return False


def run_plugin_system_tests():
    """运行插件系统测试"""
    print("🚀 插件系统测试开始")
    print("=" * 60)
    
    test_results = []
    
    # 测试插件发现
    test_results.append(("插件发现", test_plugin_discovery()))
    
    # 测试插件初始化
    test_results.append(("插件初始化", test_plugin_initialization()))
    
    # 测试工具窗口插件
    test_results.append(("工具窗口插件", test_tool_window_plugins()))
    
    # 测试插件启用/禁用
    test_results.append(("插件启用/禁用", test_plugin_enable_disable()))
    
    # 测试插件清理
    test_results.append(("插件清理", test_plugin_cleanup()))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有插件系统测试通过！")
    else:
        print("⚠️  部分测试失败，请检查插件系统配置")
    
    return passed == total


if __name__ == "__main__":
    success = run_plugin_system_tests()
    sys.exit(0 if success else 1)
