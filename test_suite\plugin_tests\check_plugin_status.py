#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
插件化状态检查工具
检查当前插件系统的实现状态和工具窗口的插件化程度
"""

import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from core.services.plugin.PluginManager import plugin_manager
from core.services.plugin.PluginIntegrationService import PluginIntegrationService
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


def check_plugin_discovery():
    """检查插件发现状态"""
    print("🔍 检查插件发现状态...")
    
    # 添加插件目录
    plugin_dir = os.path.join(project_root, "plugins")
    plugin_manager.add_plugin_directory(plugin_dir)
    
    # 扫描插件
    plugin_manager.scan_plugins()
    
    plugins = plugin_manager.get_plugin_list()
    print(f"✅ 发现 {len(plugins)} 个插件:")
    
    for plugin in plugins:
        print(f"   - {plugin.name} v{plugin.version}")
        print(f"     描述: {plugin.description}")
        print(f"     状态: {'启用' if plugin.enabled else '禁用'}")
        print()
    
    return len(plugins) > 0


def check_tool_window_plugins():
    """检查工具窗口插件"""
    print("🪟 检查工具窗口插件...")

    # 先初始化插件（使用模拟上下文）
    class MockContext:
        def __init__(self):
            self.register_manager = None

    mock_context = MockContext()
    plugin_manager.initialize_plugins(mock_context)

    tool_plugins = plugin_manager.get_tool_window_plugins()
    print(f"✅ 发现 {len(tool_plugins)} 个工具窗口插件:")

    for plugin in tool_plugins:
        print(f"   - {plugin.name}")
        print(f"     菜单文本: {plugin.menu_text}")
        print(f"     图标: {plugin.icon_path or '无'}")
        print()

    return len(tool_plugins) > 0


def check_legacy_tool_windows():
    """检查是否还有传统工具窗口"""
    print("🔧 检查传统工具窗口状态...")
    
    # 检查主窗口中的硬编码工具窗口方法
    legacy_methods = [
        '_show_set_modes_window',
        '_show_clkin_control_window', 
        '_show_pll_control_window',
        '_show_sync_sysref_window',
        '_show_clk_output_window'
    ]
    
    try:
        from ui.windows.RegisterMainWindow import RegisterMainWindow
        
        legacy_count = 0
        for method_name in legacy_methods:
            if hasattr(RegisterMainWindow, method_name):
                legacy_count += 1
                print(f"   ⚠️  发现传统方法: {method_name}")
        
        if legacy_count == 0:
            print("   ✅ 没有发现传统工具窗口方法")
            return True
        else:
            print(f"   ❌ 发现 {legacy_count} 个传统工具窗口方法")
            return False
            
    except Exception as e:
        print(f"   ❌ 检查失败: {str(e)}")
        return False


def check_menu_integration():
    """检查菜单集成状态"""
    print("📋 检查菜单集成状态...")
    
    try:
        from ui.components.MenuManager import MenuManager
        
        # 检查菜单管理器中的硬编码工具窗口
        hardcoded_connections = [
            '_show_set_modes_window',
            '_show_clkin_control_window',
            '_show_pll_control_window', 
            '_show_sync_sysref_window',
            '_show_clk_output_window'
        ]
        
        # 读取MenuManager源码
        menu_manager_file = os.path.join(project_root, "ui", "components", "MenuManager.py")
        if os.path.exists(menu_manager_file):
            with open(menu_manager_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            hardcoded_count = 0
            for method in hardcoded_connections:
                if method in content:
                    hardcoded_count += 1
                    print(f"   ⚠️  菜单中发现硬编码连接: {method}")
            
            if hardcoded_count == 0:
                print("   ✅ 菜单已完全插件化")
                return True
            else:
                print(f"   ❌ 菜单中发现 {hardcoded_count} 个硬编码连接")
                return False
        else:
            print("   ❌ 无法找到MenuManager.py文件")
            return False
            
    except Exception as e:
        print(f"   ❌ 检查失败: {str(e)}")
        return False


def check_modern_factory_status():
    """检查现代化工厂状态"""
    print("🏭 检查现代化工厂状态...")
    
    try:
        from ui.factories.ModernToolWindowFactory import ModernToolWindowFactory
        
        # 检查工厂配置
        configs = ModernToolWindowFactory.HANDLER_CONFIGS
        
        print(f"   ✅ 工厂支持 {len(configs)} 种窗口类型:")
        
        modern_count = 0
        legacy_count = 0
        
        for window_type, config in configs.items():
            use_modern = config.get('use_modern', False)
            has_modern = config.get('modern_handler') is not None
            has_legacy = config.get('legacy_handler') is not None
            
            if use_modern and has_modern:
                modern_count += 1
                status = "✅ 现代化"
            elif has_legacy:
                legacy_count += 1
                status = "⚠️  传统"
            else:
                status = "❌ 无处理器"
            
            print(f"     - {window_type}: {status}")
        
        print(f"   📊 统计: {modern_count} 个现代化, {legacy_count} 个传统")
        
        return modern_count > legacy_count
        
    except Exception as e:
        print(f"   ❌ 检查失败: {str(e)}")
        return False


def main():
    """主检查函数"""
    print("🚀 插件化状态检查")
    print("=" * 60)
    
    tests = [
        ("插件发现", check_plugin_discovery),
        ("工具窗口插件", check_tool_window_plugins),
        ("传统工具窗口", check_legacy_tool_windows),
        ("菜单集成", check_menu_integration),
        ("现代化工厂", check_modern_factory_status)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print()
        except Exception as e:
            print(f"❌ {test_name} 检查出错: {str(e)}")
            results.append((test_name, False))
            print()
    
    # 显示总结
    print("=" * 60)
    print("📊 插件化状态总结")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 完成" if result else "❌ 未完成"
        print(f"{test_name:<15} {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项已完成")
    
    # 给出结论
    if passed == total:
        print("\n🎉 插件化改造已完全完成！")
        print("✅ 所有工具窗口都已改造为插件")
        print("✅ 菜单系统已完全插件化")
        print("✅ 现代化工厂正常工作")
    elif passed >= total * 0.8:
        print("\n⚠️  插件化改造基本完成，但仍有部分传统结构")
        print("🔧 建议完成剩余的插件化工作")
    else:
        print("\n❌ 插件化改造尚未完成")
        print("🔧 仍在使用大量传统结构，需要继续插件化工作")
    
    return passed == total


if __name__ == "__main__":
    main()
