# 版本化构建系统说明

## 🎯 功能概述

新的版本化构建系统解决了之前版本覆盖的问题，现在每次构建都会创建独立的时间戳文件夹，保留所有历史版本。

## 📁 目录结构

```
项目根目录/
├── releases/                    # 版本发布目录
│   ├── 20250604_162052_v1.0.1.2/   # 时间戳_版本号文件夹
│   │   ├── FSJConfigTool1.0.1/      # 可执行文件目录
│   │   │   ├── FSJConfigTool1.0.1.exe
│   │   │   ├── config/
│   │   │   ├── images/
│   │   │   └── ...
│   │   └── version_info.json       # 版本信息文件
│   ├── 20250604_163045_v1.0.1.3/   # 下一个版本
│   └── latest/                      # 指向最新版本的链接
├── build_exe.py                    # 构建脚本
├── version_manager.py               # 版本管理GUI
└── list_versions.py                # 版本历史查看工具
```

## 🔧 核心功能

### 1. 时间戳命名
- **格式**: `YYYYMMDD_HHMMSS_vX.X.X.X`
- **示例**: `20250604_162052_v1.0.1.2`
- **优点**: 
  - 按时间排序
  - 包含版本信息
  - 避免名称冲突

### 2. 版本保留
- ✅ **保留所有历史版本**
- ✅ **独立文件夹存储**
- ✅ **不会覆盖旧版本**
- ✅ **支持版本回退**

### 3. 版本信息文件
每个版本目录包含 `version_info.json`：
```json
{
  "version": "1.0.1.2",
  "app_name": "FSJ04832 寄存器配置工具",
  "exe_name": "FSJConfigTool1.0.1",
  "build_date": "2025-06-04 16:20:52",
  "build_info": {...},
  "app_info": {...}
}
```

### 4. 最新版本链接
- **路径**: `releases/latest`
- **功能**: 指向最新构建的版本
- **类型**: 符号链接（如果支持）或目录副本

## 🚀 使用方法

### 方法一：图形界面（推荐）
```bash
python start_gui.py
```
或双击 `启动工具.bat`

### 方法二：命令行构建
```bash
# 构建号增加
python build_exe.py --version-type build

# 补丁版本增加
python build_exe.py --version-type patch

# 次版本增加
python build_exe.py --version-type minor

# 主版本增加
python build_exe.py --version-type major

# 不增加版本号，仅重新构建
python build_exe.py --no-version-increment
```

### 方法三：查看版本历史
```bash
python list_versions.py
```

## 📋 构建流程

### 1. 版本号管理
```
当前版本: 1.0.1.1
↓ (选择构建号增加)
新版本: 1.0.1.2
```

### 2. 目录创建
```
创建: releases/20250604_162052_v1.0.1.2/
```

### 3. 构建执行
```
PyInstaller构建 → 输出到版本目录
```

### 4. 文件整理
```
- 复制配置文件
- 复制图像资源
- 创建版本信息文件
- 更新最新版本链接
```

## 🎨 GUI界面改进

### 构建完成提示
- ✅ 显示版本历史信息
- ✅ 提示版本保存位置
- ✅ 显示最近5个版本
- ✅ 总版本数统计

### 输出信息增强
```
📁 版本历史 (共3个版本):
  1. 20250604_162052_v1.0.1.2 (v1.0.1.2) - 2025-06-04 16:20:52
  2. 20250604_161030_v1.0.1.1 (v1.0.1.1) - 2025-06-04 16:10:30
  3. 20250604_155500_v1.0.1.0 (v1.0.1.0) - 2025-06-04 15:55:00

💡 所有版本保存在 'releases' 目录中
💡 最新版本链接: releases/latest
```

## 🔍 版本管理

### 查看所有版本
```bash
python list_versions.py
```

输出示例：
```
📁 找到 3 个版本:

 1. 版本 1.0.1.2
    📅 构建时间: 2025-06-04 16:20:52
    📂 目录名称: 20250604_162052_v1.0.1.2
    📁 路径: releases\20250604_162052_v1.0.1.2
    💾 大小: 45.2 MB
    📋 应用名称: FSJ04832 寄存器配置工具
    🔧 可执行文件: FSJConfigTool1.0.1.exe

 2. 版本 1.0.1.1
    📅 构建时间: 2025-06-04 16:10:30
    ...
```

### 版本清理
可以安全删除旧版本文件夹：
```bash
# 删除特定版本
rm -rf releases/20250604_155500_v1.0.1.0

# 保留最近3个版本，删除其他
# (需要手动操作或编写清理脚本)
```

## 🛠️ 技术实现

### 核心函数

#### 1. 创建版本化输出目录
```python
def create_version_output_dir(version_manager):
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    version_string = version_manager.get_version_string()
    version_dir_name = f"{timestamp}_v{version_string}"
    # ...
```

#### 2. 版本历史查询
```python
def list_all_versions():
    # 扫描releases目录
    # 解析时间戳和版本号
    # 按时间排序
    # ...
```

#### 3. 最新版本链接
```python
def create_latest_link(output_dir, version_manager):
    # 尝试创建符号链接
    # 失败则复制目录
    # ...
```

## 📊 优势对比

| 特性 | 旧系统 | 新系统 |
|------|--------|--------|
| 版本保留 | ❌ 覆盖旧版本 | ✅ 保留所有版本 |
| 版本回退 | ❌ 不支持 | ✅ 完全支持 |
| 版本历史 | ❌ 无记录 | ✅ 完整记录 |
| 文件组织 | ❌ 混乱 | ✅ 清晰结构 |
| 空间管理 | ✅ 节省空间 | ⚠️ 需要管理 |
| 构建安全 | ❌ 可能丢失 | ✅ 完全安全 |

## 💡 最佳实践

### 1. 版本号策略
- **构建号**: 日常开发，小修复
- **补丁版本**: 重要bug修复
- **次版本**: 新功能发布
- **主版本**: 重大更新

### 2. 空间管理
- 定期清理旧版本
- 保留重要里程碑版本
- 使用外部存储备份

### 3. 团队协作
- 统一版本号规范
- 共享版本历史记录
- 标记重要版本

## 🔧 故障排除

### 常见问题

#### 1. 符号链接创建失败
```
问题: 无法创建符号链接
解决: 系统会自动复制目录作为备选方案
```

#### 2. 磁盘空间不足
```
问题: releases目录占用空间过大
解决: 删除旧版本文件夹，保留重要版本
```

#### 3. 版本历史显示异常
```
问题: 无法正确解析版本目录
解决: 检查目录命名格式是否正确
```

## 🎉 总结

新的版本化构建系统提供了：

- ✅ **完整的版本历史保留**
- ✅ **清晰的文件组织结构**
- ✅ **便捷的版本管理工具**
- ✅ **安全的构建流程**
- ✅ **友好的用户界面**

现在您可以放心地进行多次构建，不用担心覆盖之前的版本！
