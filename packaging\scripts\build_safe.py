#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
编码安全的构建脚本
避免Windows命令行Unicode编码问题
"""

import sys
import os
import io

# 设置标准输出编码为UTF-8
if sys.platform == 'win32':
    # 在Windows上设置控制台编码
    try:
        # 尝试设置控制台代码页为UTF-8
        os.system('chcp 65001 >NUL 2>&1')
        
        # 重新包装stdout和stderr以支持UTF-8
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')
    except:
        # 如果设置失败，使用ASCII安全模式
        pass

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def safe_print(text):
    """安全打印函数，避免编码错误"""
    try:
        print(text)
    except UnicodeEncodeError:
        # 如果出现编码错误，移除特殊字符
        safe_text = text.encode('ascii', errors='ignore').decode('ascii')
        print(safe_text)

def main():
    """主函数"""
    safe_print("=" * 60)
    safe_print("FSJ04832 编码安全构建脚本")
    safe_print("=" * 60)
    
    try:
        # 导入构建模块
        from build_exe import main as build_main
        
        # 执行构建
        success = build_main()
        
        if success:
            safe_print("\n[成功] 构建完成！")
        else:
            safe_print("\n[失败] 构建失败！")
            
        return success
        
    except UnicodeEncodeError as e:
        safe_print(f"\n[错误] 编码错误: {str(e)}")
        safe_print("[提示] 请使用 python build_safe.py 运行构建")
        return False
        
    except Exception as e:
        safe_print(f"\n[错误] 构建异常: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
