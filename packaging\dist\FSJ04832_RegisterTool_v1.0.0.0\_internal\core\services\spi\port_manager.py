#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
COM端口管理器
统一管理COM端口的访问，避免重复打开和权限冲突
"""

import logging
import threading
import time
from typing import Optional, Dict, Set
import serial
import serial.tools.list_ports

logger = logging.getLogger(__name__)


class PortManager:
    """COM端口管理器，单例模式"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self._active_ports: Dict[str, serial.Serial] = {}
        self._port_locks: Dict[str, threading.Lock] = {}
        self._busy_ports: Set[str] = set()
        self._last_scan_time = 0
        self._scan_interval = 5.0  # 5秒扫描间隔
        
    def get_available_ports(self):
        """获取可用的COM端口列表"""
        try:
            ports = []
            for port in serial.tools.list_ports.comports():
                port_info = {
                    'device': port.device,
                    'description': port.description,
                    'available': self._is_port_available(port.device)
                }
                ports.append(port_info)
                logger.debug(f"发现端口: {port.device} - {port.description} "
                           f"({'可用' if port_info['available'] else '占用'})")
            return ports
        except Exception as e:
            logger.error(f"扫描COM端口失败: {str(e)}")
            return []
    
    def _is_port_available(self, port_name: str) -> bool:
        """检查端口是否可用（不被其他程序占用）"""
        # 如果端口已经被当前应用程序打开，认为可用
        if port_name in self._active_ports:
            return True
            
        # 如果端口在忙碌列表中，认为不可用
        if port_name in self._busy_ports:
            return False
            
        # 尝试快速打开和关闭端口来检测可用性
        try:
            test_ser = serial.Serial()
            test_ser.port = port_name
            test_ser.timeout = 0.1
            test_ser.open()
            test_ser.close()
            return True
        except Exception:
            self._busy_ports.add(port_name)
            return False
    
    def open_port(self, port_name: str, baudrate: int = 115200, 
                  exclusive: bool = True) -> Optional[serial.Serial]:
        """安全地打开COM端口"""
        
        # 获取端口锁
        if port_name not in self._port_locks:
            self._port_locks[port_name] = threading.Lock()
        
        with self._port_locks[port_name]:
            # 检查是否已经打开
            if port_name in self._active_ports:
                existing_ser = self._active_ports[port_name]
                if existing_ser.is_open:
                    logger.info(f"端口 {port_name} 已打开，复用现有连接")
                    return existing_ser
                else:
                    # 清理无效连接
                    del self._active_ports[port_name]
            
            # 检查端口是否可用
            if not self._is_port_available(port_name):
                logger.warning(f"端口 {port_name} 不可用或被占用")
                return None
            
            try:
                # 尝试打开端口
                ser = serial.Serial(
                    port=port_name,
                    baudrate=baudrate,
                    bytesize=8,
                    parity='N',
                    stopbits=1,
                    timeout=1,
                    exclusive=exclusive
                )
                
                self._active_ports[port_name] = ser
                # 从忙碌列表中移除
                self._busy_ports.discard(port_name)
                
                logger.info(f"成功打开端口: {port_name}")
                return ser
                
            except serial.SerialException as e:
                if "PermissionError" in str(e) or "拒绝访问" in str(e):
                    logger.warning(f"端口 {port_name} 权限被拒绝，可能被其他程序占用")
                    self._busy_ports.add(port_name)
                else:
                    logger.error(f"打开端口 {port_name} 失败: {str(e)}")
                return None
            except Exception as e:
                logger.error(f"打开端口 {port_name} 时发生未预期错误: {str(e)}")
                return None
    
    def close_port(self, port_name: str):
        """关闭COM端口"""
        if port_name not in self._port_locks:
            return
            
        with self._port_locks[port_name]:
            if port_name in self._active_ports:
                try:
                    ser = self._active_ports[port_name]
                    if ser.is_open:
                        ser.close()
                    del self._active_ports[port_name]
                    logger.info(f"已关闭端口: {port_name}")
                except Exception as e:
                    logger.error(f"关闭端口 {port_name} 时出错: {str(e)}")
    
    def close_all_ports(self):
        """关闭所有端口"""
        for port_name in list(self._active_ports.keys()):
            self.close_port(port_name)
    
    def is_port_open(self, port_name: str) -> bool:
        """检查端口是否已打开"""
        if port_name in self._active_ports:
            ser = self._active_ports[port_name]
            return ser.is_open
        return False
    
    def get_port_connection(self, port_name: str) -> Optional[serial.Serial]:
        """获取端口连接对象"""
        if port_name in self._active_ports:
            ser = self._active_ports[port_name]
            if ser.is_open:
                return ser
        return None
    
    def refresh_port_status(self):
        """刷新端口状态，清理无效的忙碌标记"""
        current_time = time.time()
        if current_time - self._last_scan_time < self._scan_interval:
            return
            
        self._last_scan_time = current_time
        
        # 清理忙碌端口列表
        busy_ports_copy = self._busy_ports.copy()
        for port_name in busy_ports_copy:
            if self._is_port_available(port_name):
                self._busy_ports.discard(port_name)
                logger.debug(f"端口 {port_name} 现在可用")
    
    def get_status(self) -> dict:
        """获取端口管理器状态"""
        return {
            'active_ports': list(self._active_ports.keys()),
            'busy_ports': list(self._busy_ports),
            'total_locks': len(self._port_locks)
        }


# 全局端口管理器实例
port_manager = PortManager()


def get_port_manager() -> PortManager:
    """获取全局端口管理器实例"""
    return port_manager


# 清理函数，在程序退出时调用
def cleanup_ports():
    """清理所有端口连接"""
    try:
        port_manager.close_all_ports()
        logger.info("已清理所有COM端口连接")
    except Exception as e:
        logger.error(f"清理COM端口时出错: {str(e)}")


# 注册清理函数
import atexit
atexit.register(cleanup_ports)


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.DEBUG)
    
    pm = get_port_manager()
    
    # 获取可用端口
    ports = pm.get_available_ports()
    print("可用端口:")
    for port in ports:
        print(f"  {port['device']}: {port['description']} ({'可用' if port['available'] else '占用'})")
    
    # 测试打开端口
    if ports:
        test_port = ports[0]['device']
        print(f"\n测试打开端口: {test_port}")
        
        ser = pm.open_port(test_port)
        if ser:
            print(f"成功打开端口: {test_port}")
            print(f"端口状态: {pm.get_status()}")
            
            # 测试重复打开
            ser2 = pm.open_port(test_port)
            print(f"重复打开结果: {ser2 is ser}")
            
            # 关闭端口
            pm.close_port(test_port)
            print(f"已关闭端口: {test_port}")
        else:
            print(f"无法打开端口: {test_port}")
    
    print(f"\n最终状态: {pm.get_status()}")
