#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试SpinBox控件的setValue操作
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_spinbox_setvalue():
    """测试SpinBox控件的setValue操作"""
    try:
        from PyQt5.QtWidgets import QApplication
        
        # 创建QApplication
        app = QApplication(sys.argv)
        
        logger.info("=== 测试SpinBox控件的setValue操作 ===")
        
        # 创建测试实例
        from ui.handlers.ModernClkinControlHandler import ModernClkinControlHandler
        handler = ModernClkinControlHandler.create_for_testing()
        
        # 检查分频比控件的setValue操作
        divider_controls = ["PLL1R0Div", "PLL1R1Div", "PLL1R2Div"]
        
        for control_name in divider_controls:
            if hasattr(handler.ui, control_name):
                control = getattr(handler.ui, control_name)
                
                logger.info(f"\n--- 控件 {control_name} ---")
                logger.info(f"初始状态:")
                logger.info(f"  最小值: {control.minimum()}")
                logger.info(f"  最大值: {control.maximum()}")
                logger.info(f"  当前值: {control.value()}")
                logger.info(f"  单步值: {control.singleStep()}")
                
                # 测试一系列setValue操作
                test_values = [0, 1, 99, 100, 120, 150, 200, 1023]
                
                for test_value in test_values:
                    logger.info(f"\n尝试设置值为 {test_value}:")
                    
                    # 阻断信号
                    control.blockSignals(True)
                    
                    # 设置值
                    control.setValue(test_value)
                    
                    # 获取实际值
                    actual_value = control.value()
                    
                    # 恢复信号
                    control.blockSignals(False)
                    
                    logger.info(f"  设置后的值: {actual_value}")
                    
                    if actual_value == test_value:
                        logger.info(f"  ✓ 设置成功")
                    else:
                        logger.error(f"  ✗ 设置失败！期望{test_value}，实际{actual_value}")
                        
                        # 分析失败原因
                        if test_value < control.minimum():
                            logger.error(f"    原因: {test_value} < 最小值({control.minimum()})")
                        elif test_value > control.maximum():
                            logger.error(f"    原因: {test_value} > 最大值({control.maximum()})")
                        else:
                            logger.error(f"    原因: 未知")
                
                # 测试特殊情况：先设置范围再设置值
                logger.info(f"\n测试特殊情况：重新设置范围")
                
                # 重新设置范围
                control.setMinimum(1)
                control.setMaximum(16383)
                
                logger.info(f"重新设置范围后:")
                logger.info(f"  最小值: {control.minimum()}")
                logger.info(f"  最大值: {control.maximum()}")
                logger.info(f"  当前值: {control.value()}")
                
                # 再次尝试设置120
                logger.info(f"\n重新设置范围后，尝试设置值为120:")
                control.blockSignals(True)
                control.setValue(120)
                actual_value = control.value()
                control.blockSignals(False)
                
                logger.info(f"  设置后的值: {actual_value}")
                
                if actual_value == 120:
                    logger.info(f"  ✓ 重新设置范围后设置成功")
                else:
                    logger.error(f"  ✗ 重新设置范围后仍然失败！期望120，实际{actual_value}")
                    
            else:
                logger.warning(f"控件 {control_name} 不存在")
        
        logger.info("\n=== 测试完成 ===")
        
    except Exception as e:
        logger.error(f"测试时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_spinbox_setvalue()
