#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
版本显示测试脚本
测试版本信息是否正确显示在软件界面中
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
packaging_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
project_root = os.path.dirname(packaging_root)
sys.path.insert(0, project_root)

def test_version_service():
    """测试版本服务"""
    print("=" * 60)
    print("测试版本服务")
    print("=" * 60)
    
    try:
        from core.services.version.VersionService import VersionService
        
        # 获取版本服务实例
        vs = VersionService.instance()
        
        print("📋 版本信息:")
        print(f"  完整版本: {vs.get_version_string()}")
        print(f"  短版本: {vs.get_short_version_string()}")
        print(f"  应用名称: {vs.get_app_name()}")
        print(f"  窗口标题: {vs.get_app_title_with_version()}")
        print(f"  完整窗口标题: {vs.get_full_app_title_with_version()}")
        print(f"  可执行文件名: {vs.get_exe_name()}")
        print(f"  应用描述: {vs.get_app_description()}")
        print(f"  公司名称: {vs.get_company()}")
        print(f"  版权信息: {vs.get_copyright()}")
        print(f"  构建日期: {vs.get_build_date()}")
        print(f"  构建类型: {vs.get_build_type()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 版本服务测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_main_window_title():
    """测试主窗口标题设置"""
    print("\n" + "=" * 60)
    print("测试主窗口标题设置")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from core.services.version.VersionService import VersionService
        
        # 创建应用程序实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 获取版本服务
        vs = VersionService.instance()
        
        # 模拟主窗口标题设置
        expected_title = vs.get_app_title_with_version()
        print(f"✓ 期望的窗口标题: {expected_title}")
        
        # 测试窗口标题设置逻辑
        try:
            title = vs.get_app_title_with_version()
            print(f"✓ 窗口标题设置成功: {title}")
            
            # 验证标题格式
            if "v" in title and vs.get_short_version_string() in title:
                print("✓ 窗口标题格式正确")
            else:
                print("⚠️  窗口标题格式可能有问题")
                
        except Exception as e:
            print(f"❌ 窗口标题设置失败: {str(e)}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 主窗口标题测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_about_dialog_info():
    """测试关于对话框信息"""
    print("\n" + "=" * 60)
    print("测试关于对话框信息")
    print("=" * 60)
    
    try:
        from core.services.version.VersionService import VersionService
        
        vs = VersionService.instance()
        
        # 模拟关于对话框信息构建
        app_name = vs.get_app_name()
        version = vs.get_version_string()
        description = vs.get_app_description()
        company = vs.get_company()
        copyright_info = vs.get_copyright()
        build_date = vs.get_build_date()
        
        print("📋 关于对话框信息:")
        print(f"  应用名称: {app_name}")
        print(f"  版本: {version}")
        print(f"  描述: {description}")
        print(f"  开发者: {company}")
        print(f"  版权: {copyright_info}")
        print(f"  构建日期: {build_date}")
        
        # 构建关于信息（模拟实际代码）
        about_text = f"""
        <h3>{app_name}</h3>
        <p><b>版本:</b> {version}</p>
        <p><b>描述:</b> {description}</p>
        <p><b>开发者:</b> {company}</p>
        <p><b>版权:</b> {copyright_info}</p>
        """
        
        if build_date:
            about_text += f"<p><b>构建日期:</b> {build_date}</p>"
        
        print("\n📝 生成的关于对话框HTML:")
        print(about_text)
        
        return True
        
    except Exception as e:
        print(f"❌ 关于对话框信息测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_executable_naming():
    """测试可执行文件命名"""
    print("\n" + "=" * 60)
    print("测试可执行文件命名")
    print("=" * 60)
    
    try:
        from core.services.version.VersionService import VersionService
        from build_exe import VersionManager
        
        # 测试版本服务的可执行文件名
        vs = VersionService.instance()
        vs_exe_name = vs.get_exe_name()
        print(f"✓ 版本服务可执行文件名: {vs_exe_name}")
        
        # 测试构建管理器的可执行文件名
        vm = VersionManager()
        vm_exe_name = vm.get_exe_name()
        print(f"✓ 构建管理器可执行文件名: {vm_exe_name}")
        
        # 验证一致性
        if vs_exe_name == vm_exe_name:
            print("✓ 可执行文件名一致")
        else:
            print(f"⚠️  可执行文件名不一致: {vs_exe_name} vs {vm_exe_name}")
        
        # 验证命名格式
        expected_pattern = r"FSJConfigTool\d+\.\d+\.\d+"
        import re
        if re.match(expected_pattern, vs_exe_name):
            print("✓ 可执行文件名格式正确")
        else:
            print(f"⚠️  可执行文件名格式可能有问题: {vs_exe_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 可执行文件命名测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_version_file_consistency():
    """测试版本文件一致性"""
    print("\n" + "=" * 60)
    print("测试版本文件一致性")
    print("=" * 60)
    
    try:
        import json
        
        # 读取version.json文件
        version_file = Path('version.json')
        if not version_file.exists():
            print("⚠️  version.json文件不存在")
            return False
        
        with open(version_file, 'r', encoding='utf-8') as f:
            version_data = json.load(f)
        
        print("📄 version.json内容:")
        print(f"  版本: {version_data.get('version', {})}")
        print(f"  应用信息: {version_data.get('app_info', {})}")
        print(f"  构建信息: {version_data.get('build_info', {})}")
        
        # 验证版本服务读取的一致性
        from core.services.version.VersionService import VersionService
        vs = VersionService.instance()
        
        # 比较版本号
        file_version = version_data['version']
        service_version_parts = vs.get_version_string().split('.')
        
        if (str(file_version['major']) == service_version_parts[0] and
            str(file_version['minor']) == service_version_parts[1] and
            str(file_version['patch']) == service_version_parts[2] and
            str(file_version['build']) == service_version_parts[3]):
            print("✓ 版本文件与服务读取一致")
        else:
            print("⚠️  版本文件与服务读取不一致")
            print(f"  文件版本: {file_version}")
            print(f"  服务版本: {vs.get_version_string()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 版本文件一致性测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 版本显示集成测试")
    print("测试版本信息是否正确显示在软件界面中...")
    
    tests = [
        ("版本服务", test_version_service),
        ("主窗口标题", test_main_window_title),
        ("关于对话框信息", test_about_dialog_info),
        ("可执行文件命名", test_executable_naming),
        ("版本文件一致性", test_version_file_consistency),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 出现异常: {str(e)}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20s} : {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！版本信息已正确集成到软件中。")
        print("\n📋 版本显示功能:")
        print("- ✅ 可执行文件名包含版本号")
        print("- ✅ 窗口标题显示版本号")
        print("- ✅ 关于对话框显示完整版本信息")
        print("- ✅ 版本服务提供一致的版本信息")
        print("- ✅ 版本文件与服务读取一致")
    else:
        print(f"\n💥 {total - passed} 个测试失败！请检查版本集成。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
