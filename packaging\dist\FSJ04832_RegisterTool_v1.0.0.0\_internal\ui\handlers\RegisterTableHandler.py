from PyQt5.QtWidgets import (QTableWidget, QTableWidgetItem, QHeaderView, 
                            QMessageBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QColor
from utils.Log import get_module_logger

logger = get_module_logger(__name__)

class RegisterTableHandler:
    """处理寄存器表格相关的所有操作"""
    
    def __init__(self, parent=None, register_manager=None):
        self.parent = parent
        self.register_manager = register_manager
        self.bit_field_table = None
        self.current_register_addr = None
        self.current_register_value = None
        
    def create_bit_field_table(self):
        """创建位域表格"""
        self.bit_field_table = QTableWidget()
        self.bit_field_table.setColumnCount(4)
        self.bit_field_table.setHorizontalHeaderLabels([
            "位域范围", "位域功能", "值", "访问权限"
        ])
        self.bit_field_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        
        # 优化表格性能
        self.bit_field_table.setVerticalScrollMode(QTableWidget.ScrollPerPixel)
        self.bit_field_table.setHorizontalScrollMode(QTableWidget.ScrollPerPixel)
        self.bit_field_table.setAlternatingRowColors(True)
        
        # 连接单元格变更信号
        self.bit_field_table.cellChanged.connect(self.on_table_cell_changed)
        
        return self.bit_field_table
    
    def show_bit_fields(self, register_addr, register_value):
        """显示指定寄存器的位域信息"""
        if self.bit_field_table is None:
            return
            
        self.current_register_addr = register_addr
        self.current_register_value = register_value
        
        # 获取位域信息
        try:
            bit_fields = self.register_manager.get_register_bit_info(register_addr)
        except ValueError as e:  # 处理寄存器地址无效或获取位域定义失败的情况
            error_message = f"无法获取寄存器 {register_addr} 的位域定义: {str(e)}"
            logger.warning(error_message)
            self.clear_table_and_show_error(error_message)
            return

        if bit_fields is None or not bit_fields:  # 如果 bit_fields 是 None 或空列表
            message = f"寄存器 0x{int(register_addr, 16):02X} ({register_addr}) 没有定义位域信息。"
            logger.warning(message)
            self.clear_table_and_show_error(message)
            return
            
        # 断开信号连接，防止在更新表格时触发cellChanged信号
        try:
            self.bit_field_table.cellChanged.disconnect(self.on_table_cell_changed)
        except TypeError:  # PyQt5 5.11+ disconnect 引发 TypeError（如果未连接）
            pass
        except Exception as e:
            logger.warning(f"断开 cellChanged 信号时出错: {e}")
            pass  # 如果没有连接或发生其他错误，忽略
        
        # 检查是否在批量操作期间，如果是则跳过UI更新优化
        is_batch_operation = False
        if hasattr(self.parent, 'is_batch_reading') and self.parent.is_batch_reading:
            is_batch_operation = True
        elif hasattr(self.parent, 'is_batch_writing') and self.parent.is_batch_writing:
            is_batch_operation = True
        elif hasattr(self.parent, 'is_batch_updating') and self.parent.is_batch_updating:
            is_batch_operation = True

        # 暂时禁用表格更新，防止布局抖动
        if not is_batch_operation:
            self.bit_field_table.setUpdatesEnabled(False)

        # bit_fields 已经是位字段列表了
        self.bit_field_table.setRowCount(len(bit_fields))

        for row, bit_field in enumerate(bit_fields):
            self._update_table_row(row, bit_field, register_value)

        # 重新连接信号
        self.bit_field_table.cellChanged.connect(self.on_table_cell_changed)

        # 调整行高以优化显示
        for row in range(self.bit_field_table.rowCount()):
            self.bit_field_table.setRowHeight(row, 22)  # 设置统一的行高

        # 恢复表格更新
        if not is_batch_operation:
            self.bit_field_table.setUpdatesEnabled(True)
            # 只在非批量操作时进行重绘和事件处理
            self.bit_field_table.update()  # 使用update()而不是repaint()，更温和

            # 移除QApplication.processEvents()调用，避免触发布局重新计算
            # from PyQt5.QtWidgets import QApplication
            # QApplication.processEvents()

    def clear_table_and_show_error(self, error_message):
        """清空表格并显示错误信息"""
        if self.bit_field_table is None:
            return
        
        self.bit_field_table.setRowCount(0) # 清空表格内容
        # 可以在表格中显示一条错误信息，或者通过状态栏/对话框显示
        # 例如，在表格中添加一行显示错误
        self.bit_field_table.setRowCount(1)
        error_item = QTableWidgetItem(error_message)
        error_item.setTextAlignment(Qt.AlignCenter)
        error_item.setFlags(Qt.ItemIsEnabled) # 只读
        self.bit_field_table.setItem(0, 0, error_item)
        # 合并单元格以显示完整的错误消息
        if self.bit_field_table.columnCount() > 1:
            self.bit_field_table.setSpan(0, 0, 1, self.bit_field_table.columnCount())
        logger.error(f"RegisterTableHandler: {error_message}")
    
    def _update_table_row(self, row, bit_field, register_value):
        """更新表格的一行"""
        # 获取位字段信息
        bit_range = bit_field.get("bit", "")
        field_name = bit_field.get("name", "")
        description = bit_field.get("description") or ""
        default_str = bit_field.get("default", "0")  # 获取默认值字符串，用于确定位宽
        
        # 确定访问权限
        access = bit_field.get("access", "R/W")
        if field_name == "NC" or "Read only" in description or "Reserved" in description:
            access = "R/O"
        
        # 计算位掩码并提取字段值
        bit_mask = self.get_bit_mask(bit_range)
        bit_start = self.get_bit_start(bit_range)
        field_value = (register_value & bit_mask) >> bit_start
        
        # 计算位字段的位宽
        bit_width = self._get_bit_width(bit_range, default_str)
        
        # 创建表格项
        # 1. Field Offset (位范围)
        offset_item = self._create_table_item(bit_range)
        offset_item.setTextAlignment(Qt.AlignCenter)
        
        # 2. Field Function (字段名称)
        name_item = self._create_table_item(field_name)
        name_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        
        # 3. Value (值) - 显示为二进制格式，位数与default值一致
        binary_value = format(field_value, f'0{bit_width}b')  # 转换为二进制字符串，前导0补齐到bit_width位
        value_item = QTableWidgetItem(binary_value)
        value_item.setTextAlignment(Qt.AlignCenter)
        if access == "R/W":
            value_item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsSelectable | Qt.ItemIsEditable)
            value_item.setBackground(QColor(230, 230, 255))  # 可写字段颜色标记
        else:
            value_item.setFlags(value_item.flags() & ~Qt.ItemIsEditable)
        
        # 保存位信息作为用户数据
        value_item.setData(Qt.UserRole, {"bit_range": bit_range, "access": access, "bit_width": bit_width})
        
        # 4. Access (访问权限)
        access_item = self._create_table_item(access)
        access_item.setTextAlignment(Qt.AlignCenter)
        
        # 设置单元格项
        self.bit_field_table.setItem(row, 0, offset_item)
        self.bit_field_table.setItem(row, 1, name_item)
        self.bit_field_table.setItem(row, 2, value_item)
        self.bit_field_table.setItem(row, 3, access_item)
    
    def _create_table_item(self, text):
        """创建表格项"""
        item = QTableWidgetItem(str(text))
        item.setFlags(item.flags() & ~Qt.ItemIsEditable)
        return item
    
    def get_bit_start(self, bit_range):
        """获取位范围的起始位"""
        if "-" in bit_range or ":" in bit_range:
            # 替换冒号为连字符以统一处理
            bit_range = bit_range.replace(":", "-")
            start = int(bit_range.split("-")[1])
        else:
            try:
                start = int(bit_range)
            except ValueError:
                # 如果无法解析为数字，记录错误并返回0
                print(f"无法解析位范围: {bit_range}")
                start = 0
        return start
    
    def get_bit_mask(self, bit_range):
        """根据位范围计算掩码"""
        if "-" in bit_range or ":" in bit_range:
            # 替换冒号为连字符以统一处理
            bit_range = bit_range.replace(":", "-")
            parts = bit_range.split("-")
            high_bit = int(parts[0])
            low_bit = int(parts[1])
            mask = ((1 << (high_bit - low_bit + 1)) - 1) << low_bit
        else:
            # 单个位
            try:
                bit_pos = int(bit_range)
                mask = 1 << bit_pos
            except ValueError:
                # 如果无法解析为数字，记录错误并返回0
                print(f"无法解析位范围: {bit_range}")
                mask = 0
        return mask
    
    def on_table_cell_changed(self, row, column):
        """处理表格单元格值变更"""
        if column != 2:  # 只处理值列
            return
            
        if not self.current_register_addr:
            return
            
        item = self.bit_field_table.item(row, column)
        if not item:
            return
            
        # 获取位信息
        bit_data = item.data(Qt.UserRole)
        if not bit_data:
            return
            
        bit_range = bit_data.get("bit_range", "")
        bit_width = bit_data.get("bit_width", 1)
        
        # 获取字段名称
        field_name = ""
        if self.bit_field_table.item(row, 1):
            field_name = self.bit_field_table.item(row, 1).text()
        
        # 获取新输入的值 (二进制字符串)
        new_value_str = item.text().strip()
        
        # 验证输入是否为有效的二进制字符串，且长度与位宽匹配
        if not self._validate_binary_input(new_value_str, bit_width):
            # 恢复旧值
            bit_mask = self.get_bit_mask(bit_range)
            bit_start = self.get_bit_start(bit_range)
            old_field_value = (self.current_register_value & bit_mask) >> bit_start
            old_binary_value = format(old_field_value, f'0{bit_width}b')
            
            # 阻止信号再次触发
            self.bit_field_table.blockSignals(True)
            item.setText(old_binary_value)
            self.bit_field_table.blockSignals(False)
            
            QMessageBox.warning(self.parent, "输入错误", f"请输入有效的 {bit_width} 位二进制值 (0 或 1)。")
            return
            
        try:
            # 防重复调用检查
            if getattr(self.parent, '_processing_table_change', False):
                print("RegisterTableHandler: 检测到重复调用，跳过处理")
                return

            # 设置处理标志
            setattr(self.parent, '_processing_table_change', True)

            try:
                # 将二进制字符串转换为整数
                new_field_value = int(new_value_str, 2)

                # 计算新的寄存器值
                bit_mask = self.get_bit_mask(bit_range)
                bit_start = self.get_bit_start(bit_range)

                # 清除旧的位值
                new_register_value = self.current_register_value & (~bit_mask)
                # 设置新的位值
                new_register_value |= (new_field_value << bit_start)

                print(f"单元格 ({row}, {column}) 变更: 字段='{field_name}', 新二进制值='{new_value_str}', 新寄存器值=0x{new_register_value:04X}")

                # === 第一个动作：更新寄存器值 ===
                self.current_register_value = new_register_value

                # 更新 RegisterManager 中的值
                if self.register_manager:
                    self.register_manager.set_register_value(self.current_register_addr, new_register_value)

                # === 第二个动作：更新rx_value显示控件 ===
                if hasattr(self.parent, 'io_handler'):
                    self.parent.io_handler.set_value_display(new_register_value)

                # === 第三个动作：写入硬件 ===
                if hasattr(self.parent, 'register_repo'):
                    self.parent.register_repo.write_register(self.current_register_addr, new_register_value)

                # === 第四个动作：更新工具窗口（通过全局信号总线） ===
                if hasattr(self.parent, 'register_update_bus'):
                    self.parent.register_update_bus.emit_register_updated(self.current_register_addr, new_register_value)

            finally:
                # 清除处理标志
                if hasattr(self.parent, '_processing_table_change'):
                    delattr(self.parent, '_processing_table_change')
            
        except ValueError:
            print(f"无法将输入 '{new_value_str}' 转换为二进制整数")
            # 可以选择恢复旧值或显示错误
            pass
        except Exception as e:
            print(f"处理单元格变更时发生错误: {e}")
            import traceback
            traceback.print_exc()

    def _validate_binary_input(self, text, expected_length):
        """验证输入是否为指定长度的有效二进制字符串"""
        if not text:
            return False
        if len(text) != expected_length:
            return False
        if not all(c in '01' for c in text):
            return False
        return True

    def _get_bit_width(self, bit_range, default_str):
        """根据位范围或默认值计算位宽"""
        if '-' in bit_range or ':' in bit_range:
            bit_range = bit_range.replace(":", "-")
            parts = bit_range.split('-')
            try:
                high_bit = int(parts[0])
                low_bit = int(parts[1])
                return high_bit - low_bit + 1
            except (ValueError, IndexError):
                pass # 如果解析失败，尝试使用默认值
        elif bit_range.isdigit():
            return 1 # 单个位
        
        # 如果无法从位范围确定，尝试从默认值长度推断
        if default_str.startswith('0b'):
            return len(default_str) - 2
        elif default_str.startswith('0x'):
            try:
                val = int(default_str, 16)
                return val.bit_length() if val > 0 else 1
            except ValueError:
                return 1 # 默认返回1
        else:
            try:
                val = int(default_str)
                return val.bit_length() if val > 0 else 1
            except ValueError:
                return 1 # 默认返回1

    # 移除不再需要的过滤方法
    # def filter_bit_fields_by_name(self, keyword):
    #     """根据位段名称关键字过滤表格行 (此方法不再由搜索框直接调用)"""
    #     if not self.bit_field_table:
    #         return
    # 
    #     keyword_lower = keyword.lower().strip()
    # 
    #     for row in range(self.bit_field_table.rowCount()):
    #         item = self.bit_field_table.item(row, 1)  # Column 1 is '位域功能' (Field Function/Name)
    #         if item:
    #             field_name = item.text().lower()
    #             # If keyword is empty or field name contains the keyword (case-insensitive), show the row
    #             if not keyword_lower or keyword_lower in field_name:
    #                 self.bit_field_table.setRowHidden(row, False)
    #             else:
    #                 self.bit_field_table.setRowHidden(row, True)
    #         else:
    #             # 如果没有名称项，默认隐藏？或者根据需求处理
    #             self.bit_field_table.setRowHidden(row, bool(keyword_lower))