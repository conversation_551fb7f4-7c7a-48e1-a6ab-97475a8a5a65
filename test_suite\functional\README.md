# FUNCTIONAL 测试

## 描述
功能测试 - 测试具体的业务功能

## 测试文件
- `test_modern_pll.py`
- `test_modern_pll_fix.py`
- `test_modern_pll_fixed.py`
- `test_modern_clk_outputs.py`
- `test_modern_sync_sysref.py`
- `test_auto_write.py`
- `test_auto_write_fix.py`
- `test_table_auto_write.py`
- `test_pll_functionality.py`
- `test_pll_controls_only.py`
- `test_clk_output_ui.py`
- `test_sync_sysref_final.py`
- `test_sync_sysref_fix.py`
- `test_enhanced_sync_initialization.py`
- `test_final_frequency_calculation.py`
- `test_pll_and_clkout_calculations.py`

## 运行测试
```bash
# 运行该分类的所有测试
python test_suite/run_all_tests.py --category functional

# 运行特定测试文件
python test_suite/functional/test_specific_file.py
```

## 注意事项
- 确保测试环境已正确设置
- 某些测试可能需要Qt环境
- 性能测试可能需要较长时间
