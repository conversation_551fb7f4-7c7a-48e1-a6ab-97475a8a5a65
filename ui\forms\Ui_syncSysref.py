# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file '.\syncSysref.ui'
#
# Created by: PyQt5 UI code generator 5.15.10
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_sync_sysref(object):
    def setupUi(self, sync_sysref):
        sync_sysref.setObjectName("sync_sysref")
        sync_sysref.resize(1733, 915)
        self.label = QtWidgets.QLabel(sync_sysref)
        self.label.setGeometry(QtCore.QRect(-26, -24, 1791, 978))
        font = QtGui.QFont()
        font.setFamily("微软雅黑")
        self.label.setFont(font)
        self.label.setText("")
        self.label.setPixmap(QtGui.QPixmap(":/syncSysref/SYNC&SYSREF.bmp"))
        self.label.setScaledContents(True)
        self.label.setObjectName("label")
        self.SyncEn = QtWidgets.QCheckBox(sync_sysref)
        self.SyncEn.setGeometry(QtCore.QRect(1186, 70, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SyncEn.sizePolicy().hasHeightForWidth())
        self.SyncEn.setSizePolicy(sizePolicy)
        self.SyncEn.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 27px;  /* 设置方框宽度 */\n"
"                height: 27px; /* 设置方框高度 */\n"
"            }")
        self.SyncEn.setText("")
        self.SyncEn.setObjectName("SyncEn")
        self.SysrefCLR = QtWidgets.QCheckBox(sync_sysref)
        self.SysrefCLR.setGeometry(QtCore.QRect(1186, 100, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SysrefCLR.sizePolicy().hasHeightForWidth())
        self.SysrefCLR.setSizePolicy(sizePolicy)
        self.SysrefCLR.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 27px;  /* 设置方框宽度 */\n"
"                height: 27px; /* 设置方框高度 */\n"
"            }")
        self.SysrefCLR.setText("")
        self.SysrefCLR.setObjectName("SysrefCLR")
        self.pBtAllOn = QtWidgets.QPushButton(sync_sysref)
        self.pBtAllOn.setGeometry(QtCore.QRect(1463, 61, 87, 31))
        self.pBtAllOn.setObjectName("pBtAllOn")
        self.pBtAllOff = QtWidgets.QPushButton(sync_sysref)
        self.pBtAllOff.setGeometry(QtCore.QRect(1555, 62, 86, 31))
        self.pBtAllOff.setObjectName("pBtAllOff")
        self.SYNCDIS0 = QtWidgets.QCheckBox(sync_sysref)
        self.SYNCDIS0.setGeometry(QtCore.QRect(1467, 91, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SYNCDIS0.sizePolicy().hasHeightForWidth())
        self.SYNCDIS0.setSizePolicy(sizePolicy)
        self.SYNCDIS0.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 27px;  /* 设置方框宽度 */\n"
"                height: 27px; /* 设置方框高度 */\n"
"            }")
        self.SYNCDIS0.setText("")
        self.SYNCDIS0.setObjectName("SYNCDIS0")
        self.SYNCDIS2 = QtWidgets.QCheckBox(sync_sysref)
        self.SYNCDIS2.setGeometry(QtCore.QRect(1467, 115, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SYNCDIS2.sizePolicy().hasHeightForWidth())
        self.SYNCDIS2.setSizePolicy(sizePolicy)
        self.SYNCDIS2.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 27px;  /* 设置方框宽度 */\n"
"                height: 27px; /* 设置方框高度 */\n"
"            }")
        self.SYNCDIS2.setText("")
        self.SYNCDIS2.setObjectName("SYNCDIS2")
        self.SYNCDIS4 = QtWidgets.QCheckBox(sync_sysref)
        self.SYNCDIS4.setGeometry(QtCore.QRect(1467, 140, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SYNCDIS4.sizePolicy().hasHeightForWidth())
        self.SYNCDIS4.setSizePolicy(sizePolicy)
        self.SYNCDIS4.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 27px;  /* 设置方框宽度 */\n"
"                height: 27px; /* 设置方框高度 */\n"
"            }")
        self.SYNCDIS4.setText("")
        self.SYNCDIS4.setObjectName("SYNCDIS4")
        self.SYNCDIS6 = QtWidgets.QCheckBox(sync_sysref)
        self.SYNCDIS6.setGeometry(QtCore.QRect(1467, 165, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SYNCDIS6.sizePolicy().hasHeightForWidth())
        self.SYNCDIS6.setSizePolicy(sizePolicy)
        self.SYNCDIS6.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 27px;  /* 设置方框宽度 */\n"
"                height: 27px; /* 设置方框高度 */\n"
"            }")
        self.SYNCDIS6.setText("")
        self.SYNCDIS6.setObjectName("SYNCDIS6")
        self.SYNCDIS8 = QtWidgets.QCheckBox(sync_sysref)
        self.SYNCDIS8.setGeometry(QtCore.QRect(1467, 191, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SYNCDIS8.sizePolicy().hasHeightForWidth())
        self.SYNCDIS8.setSizePolicy(sizePolicy)
        self.SYNCDIS8.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 27px;  /* 设置方框宽度 */\n"
"                height: 27px; /* 设置方框高度 */\n"
"            }")
        self.SYNCDIS8.setText("")
        self.SYNCDIS8.setObjectName("SYNCDIS8")
        self.SYNCDIS10 = QtWidgets.QCheckBox(sync_sysref)
        self.SYNCDIS10.setGeometry(QtCore.QRect(1467, 216, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SYNCDIS10.sizePolicy().hasHeightForWidth())
        self.SYNCDIS10.setSizePolicy(sizePolicy)
        self.SYNCDIS10.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 27px;  /* 设置方框宽度 */\n"
"                height: 27px; /* 设置方框高度 */\n"
"            }")
        self.SYNCDIS10.setText("")
        self.SYNCDIS10.setObjectName("SYNCDIS10")
        self.SYNCDIS12 = QtWidgets.QCheckBox(sync_sysref)
        self.SYNCDIS12.setGeometry(QtCore.QRect(1468, 243, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SYNCDIS12.sizePolicy().hasHeightForWidth())
        self.SYNCDIS12.setSizePolicy(sizePolicy)
        self.SYNCDIS12.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 27px;  /* 设置方框宽度 */\n"
"                height: 27px; /* 设置方框高度 */\n"
"            }")
        self.SYNCDIS12.setText("")
        self.SYNCDIS12.setObjectName("SYNCDIS12")
        self.pBtSYNCDividers = QtWidgets.QPushButton(sync_sysref)
        self.pBtSYNCDividers.setGeometry(QtCore.QRect(202, 72, 152, 104))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(8)
        self.pBtSYNCDividers.setFont(font)
        self.pBtSYNCDividers.setObjectName("pBtSYNCDividers")
        self.pBtNormal = QtWidgets.QPushButton(sync_sysref)
        self.pBtNormal.setGeometry(QtCore.QRect(370, 72, 121, 41))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(8)
        self.pBtNormal.setFont(font)
        self.pBtNormal.setObjectName("pBtNormal")
        self.pBtReClocked = QtWidgets.QPushButton(sync_sysref)
        self.pBtReClocked.setGeometry(QtCore.QRect(515, 72, 121, 41))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(8)
        self.pBtReClocked.setFont(font)
        self.pBtReClocked.setObjectName("pBtReClocked")
        self.pBtPulser = QtWidgets.QPushButton(sync_sysref)
        self.pBtPulser.setGeometry(QtCore.QRect(658, 72, 117, 41))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(8)
        self.pBtPulser.setFont(font)
        self.pBtPulser.setObjectName("pBtPulser")
        self.pBtContinuous = QtWidgets.QPushButton(sync_sysref)
        self.pBtContinuous.setGeometry(QtCore.QRect(800, 72, 121, 41))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(8)
        self.pBtContinuous.setFont(font)
        self.pBtContinuous.setObjectName("pBtContinuous")
        self.pBtSYSREFRequest = QtWidgets.QPushButton(sync_sysref)
        self.pBtSYSREFRequest.setGeometry(QtCore.QRect(940, 72, 121, 41))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(8)
        self.pBtSYSREFRequest.setFont(font)
        self.pBtSYSREFRequest.setObjectName("pBtSYSREFRequest")
        self.pBtNone = QtWidgets.QPushButton(sync_sysref)
        self.pBtNone.setGeometry(QtCore.QRect(410, 136, 151, 40))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(8)
        self.pBtNone.setFont(font)
        self.pBtNone.setObjectName("pBtNone")
        self.pBtSYNCPin = QtWidgets.QPushButton(sync_sysref)
        self.pBtSYNCPin.setGeometry(QtCore.QRect(590, 136, 152, 40))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(8)
        self.pBtSYNCPin.setFont(font)
        self.pBtSYNCPin.setObjectName("pBtSYNCPin")
        self.pBtCLKin0 = QtWidgets.QPushButton(sync_sysref)
        self.pBtCLKin0.setGeometry(QtCore.QRect(777, 136, 147, 40))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(8)
        self.pBtCLKin0.setFont(font)
        self.pBtCLKin0.setObjectName("pBtCLKin0")
        self.SyncPLL1DLD = QtWidgets.QCheckBox(sync_sysref)
        self.SyncPLL1DLD.setGeometry(QtCore.QRect(157, 396, 38, 33))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SyncPLL1DLD.sizePolicy().hasHeightForWidth())
        self.SyncPLL1DLD.setSizePolicy(sizePolicy)
        self.SyncPLL1DLD.setMinimumSize(QtCore.QSize(0, 0))
        self.SyncPLL1DLD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 27px;  /* 设置方框宽度 */\n"
"                height: 27px; /* 设置方框高度 */\n"
"            }")
        self.SyncPLL1DLD.setText("")
        self.SyncPLL1DLD.setObjectName("SyncPLL1DLD")
        self.SyncPLL2DLD = QtWidgets.QCheckBox(sync_sysref)
        self.SyncPLL2DLD.setGeometry(QtCore.QRect(157, 426, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SyncPLL2DLD.sizePolicy().hasHeightForWidth())
        self.SyncPLL2DLD.setSizePolicy(sizePolicy)
        self.SyncPLL2DLD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 27px;  /* 设置方框宽度 */\n"
"                height: 27px; /* 设置方框高度 */\n"
"            }")
        self.SyncPLL2DLD.setText("")
        self.SyncPLL2DLD.setObjectName("SyncPLL2DLD")
        self.SyncPOL = QtWidgets.QCheckBox(sync_sysref)
        self.SyncPOL.setGeometry(QtCore.QRect(208, 552, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SyncPOL.sizePolicy().hasHeightForWidth())
        self.SyncPOL.setSizePolicy(sizePolicy)
        self.SyncPOL.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 27px;  /* 设置方框宽度 */\n"
"                height: 27px; /* 设置方框高度 */\n"
"            }")
        self.SyncPOL.setText("")
        self.SyncPOL.setObjectName("SyncPOL")
        self.CLKin0Demux = QtWidgets.QComboBox(sync_sysref)
        self.CLKin0Demux.setGeometry(QtCore.QRect(460, 235, 111, 27))
        self.CLKin0Demux.setObjectName("CLKin0Demux")
        self.comboSyncMode = QtWidgets.QComboBox(sync_sysref)
        self.comboSyncMode.setGeometry(QtCore.QRect(694, 450, 63, 25))
        self.comboSyncMode.setObjectName("comboSyncMode")
        self.comboSysrefPulseCnt = QtWidgets.QComboBox(sync_sysref)
        self.comboSysrefPulseCnt.setGeometry(QtCore.QRect(872, 665, 121, 27))
        self.comboSysrefPulseCnt.setObjectName("comboSysrefPulseCnt")
        self.comboSysrefMux = QtWidgets.QComboBox(sync_sysref)
        self.comboSysrefMux.setGeometry(QtCore.QRect(1230, 672, 121, 31))
        self.comboSysrefMux.setObjectName("comboSysrefMux")
        self.SyncSysrefFreq1 = QtWidgets.QLineEdit(sync_sysref)
        self.SyncSysrefFreq1.setGeometry(QtCore.QRect(1558, 469, 106, 27))
        self.SyncSysrefFreq1.setObjectName("SyncSysrefFreq1")
        self.InternalVCOFreq = QtWidgets.QLineEdit(sync_sysref)
        self.InternalVCOFreq.setGeometry(QtCore.QRect(29, 804, 121, 33))
        self.InternalVCOFreq.setObjectName("InternalVCOFreq")
        self.comboDDLYdSysrefStep = QtWidgets.QComboBox(sync_sysref)
        self.comboDDLYdSysrefStep.setGeometry(QtCore.QRect(650, 780, 111, 27))
        self.comboDDLYdSysrefStep.setObjectName("comboDDLYdSysrefStep")
        self.spinBoxSysrefDIV = QtWidgets.QSpinBox(sync_sysref)
        self.spinBoxSysrefDIV.setGeometry(QtCore.QRect(305, 721, 115, 33))
        self.spinBoxSysrefDIV.setObjectName("spinBoxSysrefDIV")
        self.spinBoxSysrefDDLY = QtWidgets.QSpinBox(sync_sysref)
        self.spinBoxSysrefDDLY.setGeometry(QtCore.QRect(476, 721, 116, 34))
        self.spinBoxSysrefDDLY.setObjectName("spinBoxSysrefDDLY")
        self.DDLYdStepCNT_1 = QtWidgets.QSpinBox(sync_sysref)
        self.DDLYdStepCNT_1.setGeometry(QtCore.QRect(649, 720, 116, 34))
        self.DDLYdStepCNT_1.setObjectName("DDLYdStepCNT_1")
        self.comboVcoMode = QtWidgets.QComboBox(sync_sysref)
        self.comboVcoMode.setGeometry(QtCore.QRect(52, 685, 111, 31))
        self.comboVcoMode.setObjectName("comboVcoMode")
        self.SendPulse = QtWidgets.QPushButton(sync_sysref)
        self.SendPulse.setGeometry(QtCore.QRect(447, 540, 101, 33))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        self.SendPulse.setFont(font)
        self.SendPulse.setObjectName("SendPulse")
        self.sysrefPD = QtWidgets.QCheckBox(sync_sysref)
        self.sysrefPD.setGeometry(QtCore.QRect(289, 626, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.sysrefPD.sizePolicy().hasHeightForWidth())
        self.sysrefPD.setSizePolicy(sizePolicy)
        self.sysrefPD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 27px;  /* 设置方框宽度 */\n"
"                height: 27px; /* 设置方框高度 */\n"
"            }")
        self.sysrefPD.setText("")
        self.sysrefPD.setObjectName("sysrefPD")
        self.SysrefReqEn = QtWidgets.QCheckBox(sync_sysref)
        self.SysrefReqEn.setGeometry(QtCore.QRect(952, 331, 38, 33))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SysrefReqEn.sizePolicy().hasHeightForWidth())
        self.SysrefReqEn.setSizePolicy(sizePolicy)
        self.SysrefReqEn.setMinimumSize(QtCore.QSize(0, 0))
        self.SysrefReqEn.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 27px;  /* 设置方框宽度 */\n"
"                height: 27px; /* 设置方框高度 */\n"
"            }")
        self.SysrefReqEn.setText("")
        self.SysrefReqEn.setObjectName("SysrefReqEn")
        self.SysrefPlsrPd = QtWidgets.QCheckBox(sync_sysref)
        self.SysrefPlsrPd.setGeometry(QtCore.QRect(849, 609, 38, 33))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SysrefPlsrPd.sizePolicy().hasHeightForWidth())
        self.SysrefPlsrPd.setSizePolicy(sizePolicy)
        self.SysrefPlsrPd.setMinimumSize(QtCore.QSize(0, 0))
        self.SysrefPlsrPd.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 27px;  /* 设置方框宽度 */\n"
"                height: 27px; /* 设置方框高度 */\n"
"            }")
        self.SysrefPlsrPd.setText("")
        self.SysrefPlsrPd.setObjectName("SysrefPlsrPd")
        self.sysrefDissysref = QtWidgets.QCheckBox(sync_sysref)
        self.sysrefDissysref.setGeometry(QtCore.QRect(204, 871, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.sysrefDissysref.sizePolicy().hasHeightForWidth())
        self.sysrefDissysref.setSizePolicy(sizePolicy)
        self.sysrefDissysref.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 27px;  /* 设置方框宽度 */\n"
"                height: 27px; /* 设置方框高度 */\n"
"            }")
        self.sysrefDissysref.setText("")
        self.sysrefDissysref.setObjectName("sysrefDissysref")
        self.sysrefDDLYPD = QtWidgets.QCheckBox(sync_sysref)
        self.sysrefDDLYPD.setGeometry(QtCore.QRect(439, 753, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.sysrefDDLYPD.sizePolicy().hasHeightForWidth())
        self.sysrefDDLYPD.setSizePolicy(sizePolicy)
        self.sysrefDDLYPD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 27px;  /* 设置方框宽度 */\n"
"                height: 27px; /* 设置方框高度 */\n"
"            }")
        self.sysrefDDLYPD.setText("")
        self.sysrefDDLYPD.setObjectName("sysrefDDLYPD")
        self.sysrefDDLYdEn = QtWidgets.QCheckBox(sync_sysref)
        self.sysrefDDLYdEn.setGeometry(QtCore.QRect(648, 812, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.sysrefDDLYdEn.sizePolicy().hasHeightForWidth())
        self.sysrefDDLYdEn.setSizePolicy(sizePolicy)
        self.sysrefDDLYdEn.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 27px;  /* 设置方框宽度 */\n"
"                height: 27px; /* 设置方框高度 */\n"
"            }")
        self.sysrefDDLYdEn.setText("")
        self.sysrefDDLYdEn.setObjectName("sysrefDDLYdEn")
        self.Sync1SHOTEn = QtWidgets.QCheckBox(sync_sysref)
        self.Sync1SHOTEn.setGeometry(QtCore.QRect(1421, 545, 38, 33))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.Sync1SHOTEn.sizePolicy().hasHeightForWidth())
        self.Sync1SHOTEn.setSizePolicy(sizePolicy)
        self.Sync1SHOTEn.setMinimumSize(QtCore.QSize(0, 0))
        self.Sync1SHOTEn.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 29px;  /* 设置方框宽度 */\n"
"                height: 29px; /* 设置方框高度 */\n"
"            }")
        self.Sync1SHOTEn.setText("")
        self.Sync1SHOTEn.setObjectName("Sync1SHOTEn")

        self.retranslateUi(sync_sysref)
        QtCore.QMetaObject.connectSlotsByName(sync_sysref)

    def retranslateUi(self, sync_sysref):
        _translate = QtCore.QCoreApplication.translate
        sync_sysref.setWindowTitle(_translate("sync_sysref", "Form"))
        self.pBtAllOn.setText(_translate("sync_sysref", "All On"))
        self.pBtAllOff.setText(_translate("sync_sysref", "All Off"))
        self.pBtSYNCDividers.setText(_translate("sync_sysref", "SYNC Dividers"))
        self.pBtNormal.setText(_translate("sync_sysref", "Normal"))
        self.pBtReClocked.setText(_translate("sync_sysref", "Re-Clocked"))
        self.pBtPulser.setText(_translate("sync_sysref", "Pulser"))
        self.pBtContinuous.setText(_translate("sync_sysref", "Continuous"))
        self.pBtSYSREFRequest.setText(_translate("sync_sysref", "SYSREF-Request"))
        self.pBtNone.setText(_translate("sync_sysref", "None"))
        self.pBtSYNCPin.setText(_translate("sync_sysref", "SYNC Pin"))
        self.pBtCLKin0.setText(_translate("sync_sysref", "CLKin0"))
        self.SendPulse.setText(_translate("sync_sysref", "Send Pulse"))
from ..resources import syncSysref_rc
