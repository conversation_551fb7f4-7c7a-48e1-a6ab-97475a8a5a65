# REGRESSION 测试

## 描述
回归测试 - 测试功能回归和兼容性

## 测试文件
- `test_pll_removal_verification.py`
- `test_pll_removal_simple.py`
- `test_clk_output_removal.py`
- `test_sync_sysref_removal.py`
- `test_initialization_fix.py`
- `test_main_window_init.py`
- `test_port_refresh_fix.py`
- `test_port_display.py`
- `verify_fixes.py`
- `verify_refactor.py`
- `verify_auto_write.py`
- `final_verification.py`

## 运行测试
```bash
# 运行该分类的所有测试
python test_suite/run_all_tests.py --category regression

# 运行特定测试文件
python test_suite/regression/test_specific_file.py
```

## 注意事项
- 确保测试环境已正确设置
- 某些测试可能需要Qt环境
- 性能测试可能需要较长时间
