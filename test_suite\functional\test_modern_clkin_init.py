#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
专门测试现代化时钟输入控制窗口的初始化
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.Log import get_module_logger

logger = get_module_logger(__name__)


def test_modern_clkin_initialization():
    """测试现代化时钟输入控制窗口的初始化"""
    print("=" * 60)
    print("测试现代化时钟输入控制窗口初始化")
    print("=" * 60)
    
    try:
        from ui.handlers.ModernClkinControlHandler import ModernClkinControlHandler
        
        print("\n1. 创建现代化版本...")
        modern_handler = ModernClkinControlHandler.create_for_testing()
        print("   ✓ 现代化版本创建成功")
        
        # 等待一段时间让延迟初始化完成
        print("\n2. 等待延迟初始化完成...")
        
        def check_initialization():
            print("\n3. 检查初始化状态...")
            
            # 检查关键控件是否初始化
            key_controls = [
                'lineEditClkin0', 'lineEditClkin1', 'lineEditClkin2Oscout',
                'PLL1R0Div', 'PLL1R1Div', 'PLL1R2Div',
                'CLKinSelManual', 'lineEditClkinSelOut'
            ]
            
            for control_name in key_controls:
                if hasattr(modern_handler.ui, control_name):
                    control = getattr(modern_handler.ui, control_name)
                    if hasattr(control, 'text'):
                        value = control.text()
                        print(f"   ✓ {control_name}: '{value}'")
                    elif hasattr(control, 'value'):
                        value = control.value()
                        print(f"   ✓ {control_name}: {value}")
                    elif hasattr(control, 'currentText'):
                        value = control.currentText()
                        print(f"   ✓ {control_name}: '{value}'")
                    else:
                        print(f"   ✓ {control_name}: 存在")
                else:
                    print(f"   ❌ {control_name}: 不存在")
            
            # 检查ComboBox选项
            print("\n4. 检查ComboBox选项...")
            combo_controls = [
                'CLKinSelManual', 'CLKin0Demux', 'CLKin1Demux', 
                'OSCoutMux', 'OSCoutClockFormat'
            ]
            
            for control_name in combo_controls:
                if hasattr(modern_handler.ui, control_name):
                    combo = getattr(modern_handler.ui, control_name)
                    count = combo.count()
                    print(f"   ✓ {control_name}: {count}项")
                    
                    # 显示前几个选项
                    for i in range(min(count, 3)):
                        text = combo.itemText(i)
                        print(f"     - 第{i}项: '{text}'")
                else:
                    print(f"   ❌ {control_name}: 不存在")
            
            # 检查实例变量
            print("\n5. 检查实例变量...")
            vars_to_check = [
                'clkin0', 'clkin1', 'clkin2',
                'clkin0_divider', 'clkin1_divider', 'clkin2_divider'
            ]
            
            for var_name in vars_to_check:
                if hasattr(modern_handler, var_name):
                    value = getattr(modern_handler, var_name)
                    print(f"   ✓ {var_name}: {value}")
                else:
                    print(f"   ❌ {var_name}: 不存在")
            
            # 显示窗口
            print("\n6. 显示窗口...")
            modern_handler.show()
            modern_handler.raise_()
            modern_handler.activateWindow()
            print("   ✓ 窗口已显示")
        
        # 延迟检查，等待初始化完成
        QTimer.singleShot(1000, check_initialization)
        
        return modern_handler
        
    except Exception as e:
        print(f"   ❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


def main():
    """主函数"""
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    
    # 运行测试
    modern_handler = test_modern_clkin_initialization()
    
    if modern_handler:
        # 运行应用程序
        sys.exit(app.exec_())
    else:
        print("测试失败，无法启动应用程序")
        sys.exit(1)


if __name__ == "__main__":
    main()
