"""
增强版本管理工具
用于管理应用程序版本号、构建信息和发布流程
"""

import json
import os
import sys
import subprocess
import platform
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple

class EnhancedVersionManager:
    """增强版本管理器"""
    
    def __init__(self, version_file_path: str = None):
        if version_file_path is None:
            # 默认版本文件路径
            self.version_file = Path(__file__).parent.parent / 'config' / 'version.json'
        else:
            self.version_file = Path(version_file_path)
        
        self.version_data = self._load_version_data()
        self._ensure_schema_compatibility()
    
    def _load_version_data(self) -> Dict[str, Any]:
        """加载版本数据"""
        if not self.version_file.exists():
            # 创建默认版本数据
            default_data = self._create_default_version_data()
            self._save_version_data(default_data)
            return default_data
        
        try:
            with open(self.version_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载版本文件失败: {e}")
            return self._create_default_version_data()
    
    def _create_default_version_data(self) -> Dict[str, Any]:
        """创建默认版本数据"""
        return {
            "version": {
                "major": 1,
                "minor": 0,
                "patch": 0,
                "build": 0,
                "pre_release": None,
                "build_metadata": None
            },
            "app_info": {
                "name": "FSJ04832 寄存器配置工具",
                "display_name": "FSJ04832 Register Configuration Tool",
                "description": "用于配置和管理FSJ04832寄存器的专业工具",
                "company": "FSJ Technology",
                "copyright": "© 2024 FSJ Technology",
                "author": "开发团队",
                "product_id": "FSJ04832-RegTool",
                "internal_name": "FSJRegisterTool"
            },
            "build_info": {
                "last_build_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "build_type": "development",
                "target_platform": "windows",
                "build_environment": "development",
                "git_commit": self._get_git_commit(),
                "git_branch": self._get_git_branch(),
                "build_number": 0,
                "build_machine": platform.node(),
                "compiler_version": f"Python {sys.version.split()[0]}"
            },
            "release_info": {
                "release_date": None,
                "release_notes": [],
                "breaking_changes": [],
                "new_features": [],
                "bug_fixes": [],
                "known_issues": []
            },
            "compatibility": {
                "min_windows_version": "Windows 7",
                "recommended_windows_version": "Windows 10",
                "architecture": "x64",
                "dependencies": {
                    "python_version": "3.8+",
                    "pyqt5_version": "5.15+",
                    "pyserial_version": "3.5+"
                }
            },
            "packaging": {
                "package_types": ["onefile", "onedir", "secure", "optimized"],
                "default_package_type": "onefile",
                "compression_enabled": True,
                "upx_enabled": True,
                "debug_info_stripped": True
            },
            "metadata": {
                "schema_version": "2.0",
                "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "update_history": []
            }
        }
    
    def _get_git_commit(self) -> Optional[str]:
        """获取当前Git提交哈希"""
        try:
            result = subprocess.run(['git', 'rev-parse', 'HEAD'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                return result.stdout.strip()[:8]  # 短哈希
        except:
            pass
        return None
    
    def _get_git_branch(self) -> Optional[str]:
        """获取当前Git分支"""
        try:
            result = subprocess.run(['git', 'branch', '--show-current'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                return result.stdout.strip()
        except:
            pass
        return None
    
    def _ensure_schema_compatibility(self):
        """确保版本数据架构兼容性"""
        default_data = self._create_default_version_data()
        updated = False
        
        # 递归合并默认数据，确保所有字段都存在
        def merge_dict(target: dict, source: dict) -> bool:
            changed = False
            for key, value in source.items():
                if key not in target:
                    target[key] = value
                    changed = True
                elif isinstance(value, dict) and isinstance(target[key], dict):
                    if merge_dict(target[key], value):
                        changed = True
            return changed
        
        if merge_dict(self.version_data, default_data):
            updated = True
        
        # 更新架构版本
        current_schema = self.version_data.get('metadata', {}).get('schema_version', '1.0')
        if current_schema != '2.0':
            self.version_data['metadata']['schema_version'] = '2.0'
            updated = True
        
        if updated:
            self._save_version_data()
    
    def _save_version_data(self, data: Dict[str, Any] = None):
        """保存版本数据"""
        if data is None:
            data = self.version_data
        
        # 更新最后修改时间
        data['metadata']['last_updated'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 确保目录存在
        self.version_file.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(self.version_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存版本文件失败: {e}")
    
    def get_version_string(self, include_pre_release: bool = False, include_metadata: bool = False) -> str:
        """获取版本字符串（支持语义化版本）"""
        v = self.version_data.get('version', {})
        version_str = f"{v.get('major', 1)}.{v.get('minor', 0)}.{v.get('patch', 0)}.{v.get('build', 0)}"
        
        if include_pre_release and v.get('pre_release'):
            version_str += f"-{v['pre_release']}"
        
        if include_metadata and v.get('build_metadata'):
            version_str += f"+{v['build_metadata']}"
        
        return version_str
    
    def get_semantic_version(self) -> str:
        """获取语义化版本字符串（不包含build号）"""
        v = self.version_data.get('version', {})
        version_str = f"{v.get('major', 1)}.{v.get('minor', 0)}.{v.get('patch', 0)}"
        
        if v.get('pre_release'):
            version_str += f"-{v['pre_release']}"
        
        if v.get('build_metadata'):
            version_str += f"+{v['build_metadata']}"
        
        return version_str
    
    def increment_version(self, version_type: str = 'build', pre_release: str = None) -> str:
        """增加版本号（支持预发布版本）"""
        version = self.version_data.get('version', {})
        
        if version_type == 'major':
            version['major'] = version.get('major', 1) + 1
            version['minor'] = 0
            version['patch'] = 0
            version['build'] = 0
        elif version_type == 'minor':
            version['minor'] = version.get('minor', 0) + 1
            version['patch'] = 0
            version['build'] = 0
        elif version_type == 'patch':
            version['patch'] = version.get('patch', 0) + 1
            version['build'] = 0
        else:  # build
            version['build'] = version.get('build', 0) + 1
        
        # 设置预发布标识
        version['pre_release'] = pre_release
        
        # 更新构建信息
        build_info = self.version_data.get('build_info', {})
        build_info['last_build_date'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        build_info['build_number'] = build_info.get('build_number', 0) + 1
        build_info['git_commit'] = self._get_git_commit()
        build_info['git_branch'] = self._get_git_branch()
        
        self.version_data['version'] = version
        self.version_data['build_info'] = build_info
        
        # 记录版本更新历史
        self._add_to_update_history(version_type, self.get_version_string())
        
        self._save_version_data()
        
        return self.get_version_string()
    
    def _add_to_update_history(self, update_type: str, new_version: str):
        """添加到更新历史"""
        history_entry = {
            "date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "type": update_type,
            "version": new_version,
            "git_commit": self._get_git_commit(),
            "git_branch": self._get_git_branch()
        }
        
        update_history = self.version_data.get('metadata', {}).get('update_history', [])
        update_history.append(history_entry)
        
        # 只保留最近50次更新记录
        if len(update_history) > 50:
            update_history = update_history[-50:]
        
        self.version_data['metadata']['update_history'] = update_history

    def set_build_type(self, build_type: str):
        """设置构建类型"""
        if 'build_info' not in self.version_data:
            self.version_data['build_info'] = {}

        self.version_data['build_info']['build_type'] = build_type
        self._save_version_data()

    def get_build_type(self) -> str:
        """获取构建类型"""
        return self.version_data.get('build_info', {}).get('build_type', 'development')

    def get_app_name(self) -> str:
        """获取应用名称"""
        return self.version_data.get('app_info', {}).get('name', 'FSJ04832 寄存器配置工具')

    def get_build_date(self) -> str:
        """获取构建日期"""
        return self.version_data.get('build_info', {}).get('last_build_date', '')

    def get_exe_name(self, package_type: str = 'onefile', suffix: str = '') -> str:
        """获取可执行文件名（支持不同打包类型）"""
        version = self.get_version_string()
        build_type = self.get_build_type()

        base_name = "FSJ04832_RegisterTool"

        # 根据打包类型添加后缀
        type_suffix = {
            'onefile': '',
            'onedir': '_Portable',
            'secure': '_Release',
            'optimized': '_Compact'
        }.get(package_type, '')

        if build_type != 'development':
            name = f"{base_name}_v{version}_{build_type.title()}{type_suffix}"
        else:
            name = f"{base_name}_v{version}{type_suffix}"

        if suffix:
            name += f"_{suffix}"

        return name

    def create_release_notes(self, version_type: str, notes: List[str] = None,
                           features: List[str] = None, fixes: List[str] = None,
                           breaking_changes: List[str] = None) -> bool:
        """创建发布说明"""
        try:
            release_info = self.version_data.get('release_info', {})

            if notes:
                release_info['release_notes'] = notes
            if features:
                release_info['new_features'] = features
            if fixes:
                release_info['bug_fixes'] = fixes
            if breaking_changes:
                release_info['breaking_changes'] = breaking_changes

            release_info['release_date'] = datetime.now().strftime("%Y-%m-%d")

            self.version_data['release_info'] = release_info
            self._save_version_data()

            return True
        except Exception as e:
            print(f"创建发布说明失败: {e}")
            return False

    def generate_build_report(self) -> Dict[str, Any]:
        """生成构建报告"""
        return {
            'version': self.get_version_string(),
            'semantic_version': self.get_semantic_version(),
            'build_info': self.version_data.get('build_info', {}),
            'app_info': self.version_data.get('app_info', {}),
            'packaging_config': self.version_data.get('packaging', {}),
            'compatibility': self.version_data.get('compatibility', {}),
            'release_info': self.version_data.get('release_info', {}),
            'update_history': self.version_data.get('metadata', {}).get('update_history', [])[-10:]  # 最近10次更新
        }

    def export_version_info(self, output_path: str, format: str = 'json') -> bool:
        """导出版本信息到文件"""
        try:
            report = self.generate_build_report()

            if format.lower() == 'json':
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(report, f, indent=2, ensure_ascii=False)
            elif format.lower() == 'txt':
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(f"FSJ04832 寄存器配置工具 - 版本信息\n")
                    f.write("=" * 50 + "\n\n")
                    f.write(f"版本号: {report['version']}\n")
                    f.write(f"语义化版本: {report['semantic_version']}\n")
                    f.write(f"构建日期: {report['build_info'].get('last_build_date', 'N/A')}\n")
                    f.write(f"构建类型: {report['build_info'].get('build_type', 'N/A')}\n")
                    f.write(f"Git提交: {report['build_info'].get('git_commit', 'N/A')}\n")
                    f.write(f"Git分支: {report['build_info'].get('git_branch', 'N/A')}\n")
                    f.write(f"构建机器: {report['build_info'].get('build_machine', 'N/A')}\n")

                    if report['release_info'].get('new_features'):
                        f.write(f"\n新功能:\n")
                        for feature in report['release_info']['new_features']:
                            f.write(f"  - {feature}\n")

                    if report['release_info'].get('bug_fixes'):
                        f.write(f"\n错误修复:\n")
                        for fix in report['release_info']['bug_fixes']:
                            f.write(f"  - {fix}\n")

            return True
        except Exception as e:
            print(f"导出版本信息失败: {e}")
            return False

    def validate_version_data(self) -> Tuple[bool, List[str]]:
        """验证版本数据的完整性"""
        errors = []

        # 检查必需字段
        required_fields = [
            ('version.major', int),
            ('version.minor', int),
            ('version.patch', int),
            ('version.build', int),
            ('app_info.name', str),
            ('build_info.build_type', str),
            ('metadata.schema_version', str)
        ]

        for field_path, expected_type in required_fields:
            try:
                keys = field_path.split('.')
                value = self.version_data
                for key in keys:
                    value = value[key]

                if not isinstance(value, expected_type):
                    errors.append(f"字段 {field_path} 类型错误，期望 {expected_type.__name__}")

            except KeyError:
                errors.append(f"缺少必需字段: {field_path}")

        # 检查版本号逻辑
        version = self.version_data.get('version', {})
        if any(v < 0 for v in [version.get('major', 0), version.get('minor', 0),
                              version.get('patch', 0), version.get('build', 0)]):
            errors.append("版本号不能为负数")

        return len(errors) == 0, errors

# 兼容性别名，保持向后兼容
VersionManager = EnhancedVersionManager
