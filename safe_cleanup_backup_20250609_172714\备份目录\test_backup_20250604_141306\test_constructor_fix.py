#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试构造函数修复
验证现代化处理器构造函数参数问题是否已解决
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_constructor_fix():
    """测试构造函数修复"""
    print("=" * 60)
    print("🔧 测试现代化处理器构造函数修复")
    print("=" * 60)
    
    test_results = []
    
    try:
        # 1. 测试InitializationManager的_create_modern_handlers方法
        print("\n1. 测试InitializationManager的_create_modern_handlers方法...")
        
        # 创建模拟的RegisterManager
        from core.services.register.RegisterManager import RegisterManager
        import json
        
        # 加载寄存器配置
        config_path = os.path.join('lib', 'register.json')
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                registers_config = json.load(f)
        else:
            # 创建最小的模拟配置
            registers_config = {
                "0x00": {"name": "Test Register", "bits": []},
                "0x01": {"name": "Test Register 2", "bits": []}
            }
        
        register_manager = RegisterManager(registers_config)
        
        # 创建模拟的RegisterRepository
        class MockSPIService:
            def __init__(self):
                from PyQt5.QtCore import pyqtSignal, QObject
                class SignalEmitter(QObject):
                    ports_refreshed = pyqtSignal(list)
                self._emitter = SignalEmitter()
                self.ports_refreshed = self._emitter.ports_refreshed
                
            def refresh_available_ports(self):
                pass
                
            def set_port(self, port):
                return True
        
        class MockRegisterRepository:
            def __init__(self):
                self.spi_service = MockSPIService()
        
        register_repo = MockRegisterRepository()
        
        # 创建模拟主窗口
        from PyQt5.QtWidgets import QApplication, QMainWindow
        app = QApplication(sys.argv)
        
        class MockMainWindow(QMainWindow):
            def __init__(self):
                super().__init__()
                self.register_manager = register_manager
                self.auto_write_mode = False
        
        mock_main_window = MockMainWindow()
        
        print("   ✓ 模拟环境创建成功")
        test_results.append("模拟环境创建成功")
        
        # 2. 测试工具窗口工厂初始化
        print("\n2. 测试工具窗口工厂初始化...")
        try:
            from ui.factories.ToolWindowFactory import ToolWindowFactory
            mock_main_window.tool_window_factory = ToolWindowFactory(mock_main_window)
            print("   ✅ 工具窗口工厂初始化成功")
            test_results.append("工具窗口工厂初始化成功")
        except Exception as e:
            print(f"   ❌ 工具窗口工厂初始化失败: {str(e)}")
            test_results.append(f"工具窗口工厂初始化失败: {str(e)}")
            return False
        
        # 3. 测试InitializationManager
        print("\n3. 测试InitializationManager...")
        try:
            from ui.managers.InitializationManager import InitializationManager
            init_manager = InitializationManager(mock_main_window)
            print("   ✅ InitializationManager创建成功")
            test_results.append("InitializationManager创建成功")
        except Exception as e:
            print(f"   ❌ InitializationManager创建失败: {str(e)}")
            test_results.append(f"InitializationManager创建失败: {str(e)}")
            return False
        
        # 4. 测试现代化处理器创建（关键测试）
        print("\n4. 测试现代化处理器创建...")
        try:
            # 这是关键测试 - 之前会因为构造函数参数错误而失败
            init_manager._create_modern_handlers(register_repo)
            print("   ✅ 现代化处理器创建成功")
            test_results.append("现代化处理器创建成功")
            
            # 检查处理器是否正确创建
            if hasattr(mock_main_window, 'table_handler'):
                table_type = type(mock_main_window.table_handler).__name__
                print(f"   ✓ Table处理器: {table_type}")
                if 'Modern' in table_type:
                    test_results.append("Table处理器使用现代化版本")
                else:
                    test_results.append("Table处理器使用传统版本")
            
            if hasattr(mock_main_window, 'io_handler'):
                io_type = type(mock_main_window.io_handler).__name__
                print(f"   ✓ IO处理器: {io_type}")
                if 'Modern' in io_type:
                    test_results.append("IO处理器使用现代化版本")
                else:
                    test_results.append("IO处理器使用传统版本")
            
            if hasattr(mock_main_window, 'tree_handler'):
                tree_type = type(mock_main_window.tree_handler).__name__
                print(f"   ✓ Tree处理器: {tree_type}")
                if 'Modern' in tree_type:
                    test_results.append("Tree处理器使用现代化版本")
                else:
                    test_results.append("Tree处理器使用传统版本")
                    
        except Exception as e:
            print(f"   ❌ 现代化处理器创建失败: {str(e)}")
            test_results.append(f"现代化处理器创建失败: {str(e)}")
            
            # 检查是否是构造函数参数错误
            if "unexpected keyword argument" in str(e):
                print("   ⚠️  这是构造函数参数错误，说明修复未完成")
                test_results.append("构造函数参数错误未修复")
            else:
                print("   ℹ️  这是其他类型的错误，构造函数参数问题可能已修复")
                test_results.append("构造函数参数问题可能已修复")
        
        # 5. 总结测试结果
        print("\n" + "=" * 60)
        print("📊 测试结果总结")
        print("=" * 60)
        
        success_count = 0
        total_count = len(test_results)
        
        for i, result in enumerate(test_results, 1):
            if "失败" in result or "错误" in result or "未修复" in result:
                print(f"{i}. ❌ {result}")
            else:
                print(f"{i}. ✅ {result}")
                success_count += 1
        
        print(f"\n成功率: {success_count}/{total_count} ({success_count/total_count:.1%})")
        
        # 检查关键指标
        modern_handlers_created = any("现代化处理器创建成功" in result for result in test_results)
        no_constructor_errors = not any("unexpected keyword argument" in result for result in test_results)
        
        if modern_handlers_created and no_constructor_errors:
            print("\n🎉 构造函数修复成功！")
            print("现在应该不会再出现'unexpected keyword argument'错误了。")
            return True
        elif no_constructor_errors:
            print("\n✅ 构造函数参数问题已修复！")
            print("虽然可能有其他问题，但构造函数参数错误已解决。")
            return True
        else:
            print(f"\n⚠️  构造函数修复可能不完整。")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_constructor_fix()
    sys.exit(0 if success else 1)
