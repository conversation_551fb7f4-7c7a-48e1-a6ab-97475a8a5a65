#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量操作性能测试运行器
提供简单的命令行界面来运行各种性能测试
"""

import sys
import os
import argparse
import time

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)


def run_batch_read_test():
    """运行批量读取性能测试"""
    print("🚀 启动批量读取性能测试...")
    try:
        from test_suite.performance.test_batch_read_performance import test_batch_read_performance
        test_batch_read_performance()
    except Exception as e:
        print(f"❌ 批量读取测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


def run_batch_write_test():
    """运行批量写入性能测试"""
    print("🚀 启动批量写入性能测试...")
    try:
        from test_suite.performance.test_batch_write import test_batch_performance
        test_batch_performance()
    except Exception as e:
        print(f"❌ 批量写入测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


def run_quick_performance_test():
    """运行快速性能测试"""
    print("⚡ 启动快速性能测试...")
    print("这个测试将快速评估当前系统的批量操作性能")
    
    try:
        # 设置环境变量以避免GUI问题
        os.environ['QT_QPA_PLATFORM'] = 'offscreen'
        
        from PyQt5.QtWidgets import QApplication
        from ui.windows.RegisterMainWindow import RegisterMainWindow
        
        # 创建应用程序
        app = QApplication([])
        
        # 创建主窗口
        main_window = RegisterMainWindow()
        print("✅ 主窗口创建成功")
        
        # 确保处于模拟模式
        if hasattr(main_window, 'spi_service'):
            main_window.spi_service.set_simulation_mode(True)
            print("✅ 已切换到模拟模式")
        
        # 快速测试批量读取
        if hasattr(main_window, 'batch_manager'):
            batch_manager = main_window.batch_manager

            print("\n📖 测试批量读取所有寄存器...")
            start_time = time.time()

            # 触发批量读取
            batch_manager.handle_read_all_requested()

            # 等待完成
            timeout = 15  # 15秒超时
            while batch_manager.is_batch_reading and (time.time() - start_time) < timeout:
                app.processEvents()
                time.sleep(0.01)

            duration = time.time() - start_time

            if batch_manager.is_batch_reading:
                print("⚠️  批量读取超时")
                batch_manager._finish_read_all()
            else:
                completed = getattr(batch_manager, 'read_all_completed', 0)
                total = getattr(batch_manager, 'read_all_total', 0)
                throughput = completed / duration if duration > 0 else 0
                print(f"✅ 批量读取完成")
                print(f"   📊 读取了 {completed}/{total} 个寄存器")
                print(f"   ⏱️  耗时: {duration:.3f} 秒")
                print(f"   🚀 吞吐量: {throughput:.1f} 寄存器/秒")

            # 等待一下再进行下一个测试
            time.sleep(0.5)

            # 快速测试批量写入
            print("\n📝 测试批量写入所有寄存器...")
            start_time = time.time()

            # 触发批量写入
            batch_manager.handle_write_all_requested()

            # 等待完成
            while batch_manager.is_batch_writing and (time.time() - start_time) < timeout:
                app.processEvents()
                time.sleep(0.01)

            duration = time.time() - start_time

            if batch_manager.is_batch_writing:
                print("⚠️  批量写入超时")
                batch_manager._finish_write_all()
            else:
                completed = getattr(batch_manager, 'write_all_completed', 0)
                total = getattr(batch_manager, 'write_all_total', 0)
                throughput = completed / duration if duration > 0 else 0
                print(f"✅ 批量写入完成")
                print(f"   📊 写入了 {completed}/{total} 个寄存器")
                print(f"   ⏱️  耗时: {duration:.3f} 秒")
                print(f"   🚀 吞吐量: {throughput:.1f} 寄存器/秒")
        else:
            print("❌ 主窗口没有批量操作管理器")
            print(f"   可用属性: {[attr for attr in dir(main_window) if 'batch' in attr.lower()]}")
        
        print("\n✅ 快速性能测试完成")
        
    except Exception as e:
        print(f"❌ 快速测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


def run_all_tests():
    """运行所有性能测试"""
    print("🎯 运行所有性能测试...")
    
    print("\n" + "="*60)
    print("1. 批量读取性能测试")
    print("="*60)
    run_batch_read_test()
    
    print("\n" + "="*60)
    print("2. 批量写入性能测试")
    print("="*60)
    run_batch_write_test()
    
    print("\n" + "="*60)
    print("✅ 所有性能测试完成")
    print("="*60)


def show_system_info():
    """显示系统信息"""
    print("💻 系统信息:")
    
    try:
        import platform
        print(f"   操作系统: {platform.system()} {platform.release()}")
        print(f"   Python版本: {platform.python_version()}")
        print(f"   处理器: {platform.processor()}")
        
        try:
            import psutil
            print(f"   内存总量: {psutil.virtual_memory().total / 1024 / 1024 / 1024:.1f} GB")
            print(f"   可用内存: {psutil.virtual_memory().available / 1024 / 1024 / 1024:.1f} GB")
            print(f"   CPU核心数: {psutil.cpu_count()}")
        except ImportError:
            print("   (psutil未安装，无法显示详细内存和CPU信息)")
            
        try:
            from PyQt5.QtCore import QT_VERSION_STR
            print(f"   Qt版本: {QT_VERSION_STR}")
        except ImportError:
            print("   (PyQt5未安装)")
            
    except Exception as e:
        print(f"   获取系统信息失败: {str(e)}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='批量操作性能测试工具')
    parser.add_argument('--test', choices=['read', 'write', 'quick', 'all'], 
                       default='quick', help='选择要运行的测试类型')
    parser.add_argument('--info', action='store_true', help='显示系统信息')
    
    args = parser.parse_args()
    
    print("🚀 批量操作性能测试工具")
    print("="*50)
    
    if args.info:
        show_system_info()
        print()
    
    if args.test == 'read':
        run_batch_read_test()
    elif args.test == 'write':
        run_batch_write_test()
    elif args.test == 'quick':
        run_quick_performance_test()
    elif args.test == 'all':
        run_all_tests()
    
    print("\n💡 使用提示:")
    print("   python run_performance_tests.py --test quick    # 快速测试")
    print("   python run_performance_tests.py --test read     # 批量读取测试")
    print("   python run_performance_tests.py --test write    # 批量写入测试")
    print("   python run_performance_tests.py --test all      # 所有测试")
    print("   python run_performance_tests.py --info          # 显示系统信息")


if __name__ == "__main__":
    main()
