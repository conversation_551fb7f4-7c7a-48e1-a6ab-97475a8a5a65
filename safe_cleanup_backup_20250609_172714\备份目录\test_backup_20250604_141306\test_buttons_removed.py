#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试按钮是否已被移除
验证修复后的界面没有读取、写入等操作按钮
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QPushButton
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt

def test_buttons_removed():
    """测试操作按钮是否已被移除"""
    app = QApplication(sys.argv)
    
    try:
        # 创建主窗口
        from ui.windows.RegisterMainWindow import RegisterMainWindow
        from core.services.spi.spi_service_impl import SPIServiceImpl
        from core.repositories.register_repository import RegisterRepository
        
        spi_service = SPIServiceImpl()
        spi_service.initialize()
        register_repo = RegisterRepository(spi_service)
        main_window = RegisterMainWindow(register_repo)
        main_window.show()
        
        # 等待界面完全加载
        QTest.qWait(2000)
        
        # 查找所有按钮
        all_buttons = main_window.findChildren(QPushButton)
        
        # 需要被移除的按钮文本
        removed_buttons = ["读取", "写入", "读取全部", "写入全部", "导出全部", "保存", "加载"]
        
        # 统计按钮文本
        found_buttons = []
        for button in all_buttons:
            text = button.text()
            if text in removed_buttons:
                found_buttons.append(text)
        
        print("所有找到的按钮:")
        for button in all_buttons:
            text = button.text()
            if text:  # 只显示有文本的按钮
                print(f"  {text}")
        
        if found_buttons:
            print(f"\n❌ 发现应该被移除的按钮:")
            for text in found_buttons:
                print(f"  {text}")
            return False
        else:
            print(f"\n✅ 成功！所有操作按钮都已被移除")
            print("只保留了必要的按钮（如刷新、搜索等）")
            return True
            
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        app.quit()

if __name__ == "__main__":
    success = test_buttons_removed()
    sys.exit(0 if success else 1)
