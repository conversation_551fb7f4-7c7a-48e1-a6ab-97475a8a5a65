import sys
import os
from PyQt5.QtWidgets import QMainWindow
from PyQt5 import sip

# 修正导入路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.Log import get_module_logger
from core.services.DIContainer import container, configure_services, configure_ui_managers, configure_additional_managers
from core.services.config.ConfigurationManager import config_manager

logger = get_module_logger(__name__)


class RegisterMainWindow(QMainWindow):
    """寄存器配置工具主窗口类，集成三个处理器"""

    # 配置驱动的常量（将在初始化时从配置文件读取）
    OPERATION_TIMEOUT = None  # 将从配置读取
    BATCH_SIZE = None  # 将从配置读取
    
    def __init__(self, parent=None):
        """初始化主窗口"""
        super(RegisterMainWindow, self).__init__(parent)

        # 加载配置
        self._load_configuration()

        # 初始化主窗口配置
        self._setup_window_title()
        self._setup_window_from_config()

        # 配置依赖注入容器
        self._setup_dependency_injection()

        # 创建管理器实例（使用依赖注入）
        self._setup_managers_with_dependency_injection()

        # 执行完整的初始化流程
        self.initialization_manager.initialize_window()

        # 设置主窗口的点击置顶功能
        self._setup_main_window_activation()

        # 初始化插件系统（在寄存器管理器创建之后）
        self._setup_plugin_system()

        # 插件系统已经自动集成了核心工具插件到菜单和工具栏
        # 不需要额外的集成调用

        # # 选择默认寄存器
        # self.tree_handler.select_default_register()

    def _setup_dependency_injection(self):
        """设置依赖注入容器"""
        try:
            # 注册主窗口实例
            container.register_singleton('main_window', type(self), instance=self)

            # 配置核心服务
            configure_services()

            # 配置UI管理器
            configure_ui_managers()

            # 配置额外管理器
            configure_additional_managers()

            logger.info("依赖注入容器设置完成")
        except Exception as e:
            logger.error(f"依赖注入设置失败: {str(e)}")
            # 继续执行，使用传统方式创建管理器

    def _setup_managers_with_dependency_injection(self):
        """使用依赖注入设置管理器"""
        try:
            # 通过依赖注入获取管理器实例
            self.initialization_manager = container.get('initialization_manager')
            self.register_operation_manager = container.get('register_operation_manager')
            self.display_manager = container.get('display_manager')
            self.event_coordinator = container.get('event_coordinator')
            # tool_window_factory 已移除，现在通过插件系统管理工具窗口
            self.register_update_processor = container.get('register_update_processor')
            self.lifecycle_manager = container.get('lifecycle_manager')
            self.ui_utility_manager = container.get('ui_utility_manager')

            logger.info("管理器依赖注入设置完成")
        except Exception as e:
            logger.error(f"管理器依赖注入设置失败: {str(e)}")
            # 回退到传统方式创建管理器
            self._setup_managers_traditional_way()

    def _setup_managers_traditional_way(self):
        """传统方式创建管理器（回退方案）"""
        try:
            from ui.managers.InitializationManager import InitializationManager
            from ui.managers.RegisterOperationManager import RegisterOperationManager
            from ui.managers.RegisterDisplayManager import RegisterDisplayManager
            from ui.coordinators.EventCoordinator import EventCoordinator
            # ModernToolWindowFactory 已移除，现在通过插件系统管理工具窗口
            from ui.processors.RegisterUpdateProcessor import RegisterUpdateProcessor
            from ui.managers.ApplicationLifecycleManager import ApplicationLifecycleManager
            from ui.managers.UIUtilityManager import UIUtilityManager

            self.initialization_manager = InitializationManager(self)
            self.register_operation_manager = RegisterOperationManager(self)
            self.display_manager = RegisterDisplayManager(self)
            self.event_coordinator = EventCoordinator(self)
            # tool_window_factory 已移除，现在通过插件系统管理工具窗口
            self.register_update_processor = RegisterUpdateProcessor(self)
            self.lifecycle_manager = ApplicationLifecycleManager(self)
            self.ui_utility_manager = UIUtilityManager(self)

            logger.info("传统方式管理器创建完成")
        except Exception as e:
            logger.error(f"传统方式管理器创建失败: {str(e)}")
            raise

    def _setup_plugin_system(self):
        """设置插件系统"""
        try:
            from core.services.plugin.PluginIntegrationService import PluginIntegrationService
            self.plugin_integration_service = PluginIntegrationService(self)

            # 为了向后兼容，也保留plugin_service引用
            self.plugin_service = self.plugin_integration_service

            # 注册到依赖注入容器
            container.register_singleton('plugin_service', PluginIntegrationService, instance=self.plugin_integration_service)

            # 初始化插件
            self.plugin_integration_service.initialize_plugins()

            logger.info("插件系统设置完成")
        except Exception as e:
            logger.error(f"插件系统设置失败: {str(e)}")

    def _integrate_core_tools_to_menu_and_toolbar(self):
        """将核心工具插件集成到菜单和工具栏"""
        try:
            if hasattr(self, 'plugin_integration_service') and hasattr(self, 'ui_manager'):
                # 获取工具菜单
                menu_manager = self.ui_manager.menu_manager
                if hasattr(menu_manager, '_integrate_tool_window_plugins_to_menu'):
                    # 找到工具菜单
                    menubar = self.menuBar()
                    tools_menu = None
                    for action in menubar.actions():
                        if action.menu() and "工具" in action.text():
                            tools_menu = action.menu()
                            break

                    if tools_menu:
                        # 调用菜单管理器的集成方法
                        menu_manager._integrate_tool_window_plugins_to_menu(tools_menu)
                        logger.info("核心工具插件已集成到菜单和工具栏")
                    else:
                        logger.warning("未找到工具菜单，无法集成核心工具插件")
                else:
                    logger.warning("菜单管理器不支持插件集成")
            else:
                logger.warning("插件服务或UI管理器不可用，无法集成核心工具插件")
        except Exception as e:
            logger.error(f"集成核心工具插件失败: {str(e)}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")

    def _load_configuration(self):
        """加载应用程序配置"""
        try:
            config_manager.load_default_configs()
            logger.info("配置加载完成")
        except Exception as e:
            logger.error(f"配置加载失败: {str(e)}")

    def _setup_window_from_config(self):
        """根据配置设置窗口"""
        try:
            # 设置窗口大小
            width = config_manager.get('app.window.width', 1840)
            height = config_manager.get('app.window.height', 1100)
            self.resize(width, height)
            logger.info(f"窗口大小设置为: {width}x{height}")

            # 加载操作常量
            self._load_operation_constants()

        except Exception as e:
            logger.error(f"设置窗口配置失败: {str(e)}")
            self.resize(1840, 1100)  # 使用默认值
            self._load_operation_constants()  # 确保常量被加载

    def _load_operation_constants(self):
        """从配置加载操作常量"""
        try:
            # 从配置读取操作常量
            RegisterMainWindow.OPERATION_TIMEOUT = config_manager.get('spi.timeout', 5000)
            RegisterMainWindow.BATCH_SIZE = config_manager.get('spi.batch_size', 20)

            logger.info(f"操作常量已加载: TIMEOUT={self.OPERATION_TIMEOUT}ms, BATCH_SIZE={self.BATCH_SIZE}")
        except Exception as e:
            logger.error(f"加载操作常量失败: {str(e)}")
            # 使用默认值
            RegisterMainWindow.OPERATION_TIMEOUT = 5000
            RegisterMainWindow.BATCH_SIZE = 20

    def _setup_window_title(self):
        """设置带版本号的窗口标题"""
        try:
            from core.services.version.VersionService import VersionService
            version_service = VersionService.instance()
            title = version_service.get_app_title_with_version()
            self.setWindowTitle(title)
            logger.info(f"窗口标题已设置为: {title}")
        except Exception as e:
            # 如果版本服务不可用，使用默认标题
            default_title = 'FSJ04832 寄存器配置工具'
            self.setWindowTitle(default_title)
            logger.warning(f"无法获取版本信息，使用默认标题: {default_title}, 错误: {str(e)}")

    def _setup_main_window_activation(self):
        """设置主窗口的点击置顶功能"""
        try:
            from PyQt5.QtCore import Qt, QTimer

            # 设置窗口属性，使其能够接收焦点
            self.setFocusPolicy(Qt.ClickFocus)

            # 设置窗口标志，确保窗口可以被激活
            current_flags = self.windowFlags()
            self.setWindowFlags(current_flags | Qt.WindowStaysOnTopHint)
            self.setWindowFlags(current_flags)  # 立即移除置顶标志，只是为了触发窗口系统更新

            # 确保窗口可以接收输入
            self.setAttribute(Qt.WA_AcceptTouchEvents, True)
            self.setAttribute(Qt.WA_InputMethodEnabled, True)

            # 安装事件过滤器来处理鼠标点击
            self.installEventFilter(self)

            # 不再为应用程序安装全局事件过滤器，避免干扰对话框
            # QApplication.instance().installEventFilter(self)  # 注释掉，避免干扰QMessageBox等对话框

            # 延迟设置寄存器组件的事件过滤器（等待UI创建完成）
            QTimer.singleShot(1000, self._setup_register_components_activation)

            logger.info("主窗口点击置顶功能已设置")

        except Exception as e:
            logger.error(f"设置主窗口点击置顶功能时出错: {str(e)}")

    def _setup_register_components_activation(self):
        """为寄存器组件设置点击激活功能"""
        try:
            from PyQt5.QtCore import QEvent, QObject

            # 创建专门的事件过滤器类
            class RegisterAreaClickFilter(QObject):
                def __init__(self, main_window):
                    super().__init__()
                    self.main_window = main_window

                def eventFilter(self, obj, event):
                    try:
                        if event.type() == QEvent.MouseButtonPress:
                            logger.info(f"寄存器区域点击事件 - 对象: {obj.__class__.__name__}")
                            # 直接调用隐藏工具标签页的方法
                            self.main_window._bring_register_area_to_front()
                            return False  # 不拦截事件，让其继续传播
                        return False
                    except Exception as e:
                        logger.error(f"处理寄存器区域点击事件时出错: {str(e)}")
                        return False

            # 创建过滤器实例
            self._register_click_filter = RegisterAreaClickFilter(self)

            # 为寄存器树安装事件过滤器
            if hasattr(self, 'tree_handler') and hasattr(self.tree_handler, 'tree_widget'):
                self.tree_handler.tree_widget.installEventFilter(self._register_click_filter)
                logger.info("已为寄存器树安装点击事件过滤器")

            # 为寄存器表格安装事件过滤器
            if hasattr(self, 'table_handler'):
                self.table_handler.installEventFilter(self._register_click_filter)
                logger.info("已为寄存器表格安装点击事件过滤器")

        except Exception as e:
            logger.error(f"设置寄存器组件激活功能时出错: {str(e)}")

    def eventFilter(self, obj, event):
        """事件过滤器，处理主窗口激活"""
        try:
            from PyQt5.QtCore import QEvent, QTimer

            # 只处理主窗口的鼠标事件，不处理全局应用程序事件
            if obj == self:
                if event.type() in [QEvent.MouseButtonPress, QEvent.MouseButtonDblClick]:
                    logger.info(f"主窗口鼠标点击事件 - 位置: {event.pos()}")
                    # 检查点击位置是否在上半部分（寄存器区域）
                    if self._is_click_in_register_area(event):
                        # 点击在寄存器区域，隐藏工具标签页让寄存器区域获得焦点
                        QTimer.singleShot(10, self._bring_register_area_to_front)
                        logger.info("主窗口寄存器区域将获得焦点")
                    else:
                        # 点击在其他区域，执行常规置顶
                        QTimer.singleShot(10, self._force_window_activation)
                        logger.info("主窗口将通过点击置顶")
                elif event.type() == QEvent.FocusIn:
                    self._force_window_activation()
                    logger.debug("主窗口已通过焦点置顶")
                elif event.type() == QEvent.WindowActivate:
                    # 窗口激活时也确保置顶
                    self._force_window_activation()
                    logger.debug("主窗口已通过激活事件置顶")

            return super().eventFilter(obj, event)

        except Exception as e:
            logger.error(f"处理主窗口事件时出错: {str(e)}")
            return False

    def _force_window_activation(self):
        """强制激活窗口"""
        try:
            from PyQt5.QtCore import Qt
            from PyQt5.QtWidgets import QApplication

            # 如果窗口被最小化，先恢复
            if self.isMinimized():
                self.showNormal()

            # 确保窗口可见
            if not self.isVisible():
                self.show()

            # 在Windows系统上，使用最强力的激活方法
            import sys
            if sys.platform.startswith('win'):
                try:
                    import ctypes

                    # 获取窗口句柄
                    hwnd = int(self.winId())

                    # 定义Windows API常量
                    HWND_TOP = 0
                    HWND_TOPMOST = -1
                    SWP_NOMOVE = 0x0002
                    SWP_NOSIZE = 0x0001
                    SWP_SHOWWINDOW = 0x0040

                    # 步骤1: 临时设置为最顶层窗口
                    ctypes.windll.user32.SetWindowPos(
                        hwnd, HWND_TOPMOST, 0, 0, 0, 0,
                        SWP_NOMOVE | SWP_NOSIZE | SWP_SHOWWINDOW
                    )

                    # 步骤2: 立即取消最顶层状态，但保持在顶部
                    ctypes.windll.user32.SetWindowPos(
                        hwnd, HWND_TOP, 0, 0, 0, 0,
                        SWP_NOMOVE | SWP_NOSIZE | SWP_SHOWWINDOW
                    )

                    # 步骤3: 强制设置前台窗口
                    ctypes.windll.user32.SetForegroundWindow(hwnd)

                    # 步骤4: 确保窗口在顶部
                    ctypes.windll.user32.BringWindowToTop(hwnd)

                    # 步骤5: 强制刷新窗口
                    ctypes.windll.user32.UpdateWindow(hwnd)

                    # 步骤6: 使用AttachThreadInput技巧强制激活
                    try:
                        current_thread = ctypes.windll.kernel32.GetCurrentThreadId()
                        foreground_window = ctypes.windll.user32.GetForegroundWindow()
                        if foreground_window != hwnd:
                            foreground_thread = ctypes.windll.user32.GetWindowThreadProcessId(foreground_window, None)
                            if foreground_thread != current_thread:
                                ctypes.windll.user32.AttachThreadInput(foreground_thread, current_thread, True)
                                ctypes.windll.user32.SetForegroundWindow(hwnd)
                                ctypes.windll.user32.AttachThreadInput(foreground_thread, current_thread, False)
                    except Exception as thread_error:
                        logger.debug(f"线程附加方法失败: {str(thread_error)}")

                    logger.debug("使用增强Windows API强制激活主窗口")

                except Exception as api_error:
                    logger.debug(f"Windows API激活失败，使用Qt方法: {str(api_error)}")

            # Qt方法作为备用
            self.raise_()
            self.activateWindow()
            self.setFocus(Qt.MouseFocusReason)

            # 强制应用程序处理事件
            QApplication.processEvents()

            # 额外的Qt方法确保窗口置顶
            self.repaint()

            # 调整与工具窗口的层级关系
            self._bring_main_window_to_front_of_tools()

            logger.debug("主窗口强制置顶完成")

        except Exception as e:
            logger.error(f"强制激活窗口时出错: {str(e)}")

    def _bring_main_window_to_front_of_tools(self):
        """将主窗口置于所有工具窗口之前"""
        try:
            # 获取所有打开的工具窗口
            tool_windows = []

            # 检查各种工具窗口
            tool_window_attrs = [
                'reg_table', 'set_modes_window', 'clkin_control_window',
                'pll_control_window', 'sync_sysref_window', 'clk_output_window'
            ]

            for window_attr in tool_window_attrs:
                if hasattr(self, window_attr):
                    window = getattr(self, window_attr)
                    if window and hasattr(window, 'isVisible') and window.isVisible():
                        tool_windows.append(window)

            # 如果有工具窗口打开，确保主窗口在它们之前
            if tool_windows:
                # 先将主窗口置顶
                self._force_window_activation()

                # 然后将工具窗口置于主窗口之下
                for tool_window in tool_windows:
                    try:
                        # 使用Windows API设置窗口层级关系
                        import sys
                        if sys.platform.startswith('win'):
                            import ctypes
                            main_hwnd = int(self.winId())
                            tool_hwnd = int(tool_window.winId())

                            # 将工具窗口设置为主窗口的下一层
                            SWP_NOMOVE = 0x0002
                            SWP_NOSIZE = 0x0001
                            SWP_NOACTIVATE = 0x0010

                            ctypes.windll.user32.SetWindowPos(
                                tool_hwnd, main_hwnd, 0, 0, 0, 0,
                                SWP_NOMOVE | SWP_NOSIZE | SWP_NOACTIVATE
                            )
                    except Exception as tool_error:
                        logger.debug(f"调整工具窗口层级时出错: {str(tool_error)}")

                logger.debug(f"已调整 {len(tool_windows)} 个工具窗口的层级关系")

        except Exception as e:
            logger.error(f"调整窗口层级关系时出错: {str(e)}")

    def _is_click_in_register_area(self, event):
        """检查鼠标点击是否在寄存器区域（上半部分）"""
        try:
            # 获取点击位置
            click_pos = event.pos()
            logger.info(f"点击位置: {click_pos}")

            # 获取工具标签页的位置和大小
            if hasattr(self, 'tools_tab_widget') and self.tools_tab_widget.isVisible():
                tab_widget_geometry = self.tools_tab_widget.geometry()
                logger.info(f"工具标签页几何信息: {tab_widget_geometry}")

                # 如果点击位置在工具标签页上方，则认为是在寄存器区域
                if click_pos.y() < tab_widget_geometry.y():
                    logger.info("点击在寄存器区域")
                    return True
                else:
                    logger.info("点击在工具标签页区域")
                    return False
            else:
                # 如果工具标签页不可见，则认为点击在寄存器区域
                logger.info("工具标签页不可见，认为点击在寄存器区域")
                return True

        except Exception as e:
            logger.error(f"检查点击位置时出错: {str(e)}")
            return False

    def _bring_register_area_to_front(self):
        """将寄存器区域置于前台（通过隐藏或最小化工具标签页）"""
        try:
            # 如果工具标签页可见，则隐藏它
            if hasattr(self, 'tools_tab_widget') and self.tools_tab_widget.isVisible():
                # 记录当前标签页状态，以便后续恢复
                if not hasattr(self, '_tab_widget_was_visible'):
                    self._tab_widget_was_visible = True

                # 隐藏工具标签页
                self.tools_tab_widget.setVisible(False)

                # 给寄存器区域设置焦点
                if hasattr(self, 'tree_handler') and hasattr(self.tree_handler, 'tree_widget'):
                    self.tree_handler.tree_widget.setFocus()
                elif hasattr(self, 'table_handler'):
                    self.table_handler.setFocus()

                logger.info("工具标签页已隐藏，寄存器区域获得焦点")

                # 显示提示信息
                if hasattr(self, 'status_bar'):
                    self.status_bar.showMessage("工具窗口已隐藏，点击菜单重新打开", 3000)
            else:
                # 如果工具标签页已经隐藏，则执行常规的窗口置顶
                self._force_window_activation()

        except Exception as e:
            logger.error(f"将寄存器区域置于前台时出错: {str(e)}")

    def _update_status_bar(self):
        """更新状态栏的模式和端口信息"""
        # 委托给状态和配置管理器
        self.status_config_manager.update_status_bar()


    def _handle_io_write_request(self, addr, value):
        """处理来自 IO Handler 的直接写入请求 (例如，通过回车确认输入)"""
        # 委托给全局事件管理器
        self.global_event_manager.handle_io_write_request(addr, value)
    
    def _create_ui(self):
        """创建UI组件"""
        # 使用UI组件管理器创建界面
        from ui.components.MainWindowUI import MainWindowUI
        self.ui_manager = MainWindowUI(self)
        self.ui_manager.setup_ui()

        # 填充寄存器树
        self.tree_handler.populate_tree_widget()
    
    def _toggle_simulation_mode(self, checked):
        """切换模拟模式"""
        # 委托给事件协调器
        self.event_coordinator.handle_simulation_mode_toggle(checked)

    def _toggle_auto_write_mode(self, checked):
        """切换自动写入模式"""
        # 委托给事件协调器
        self.event_coordinator.handle_auto_write_mode_toggle(checked)

    def _connect_signals(self):
        """连接处理器信号"""
        # 委托给事件协调器
        self.event_coordinator.connect_all_signals()

    
    def _handle_read_requested(self, addr):
        """处理读取请求"""
        # 委托给事件协调器
        self.event_coordinator.handle_read_requested(addr)

    def _handle_write_requested(self, addr, value):
        """处理写入请求"""
        # 委托给事件协调器
        self.event_coordinator.handle_write_requested(addr, value)
    
    def on_register_selected(self, reg_addr):
        """处理寄存器被选中事件

        Args:
            reg_addr: 寄存器地址
        """
        # 委托给事件协调器
        self.event_coordinator.handle_register_selection(reg_addr)
    
    def _update_rx_value_display(self, reg_num, value):
        """更新寄存器值显示"""
        # 委托给显示管理器
        self.display_manager.update_register_display(reg_num, value)
    
    def update_register_from_input(self):
        """从输入框更新寄存器值"""
        # 委托给寄存器更新处理器
        self.register_update_processor.update_register_from_input()

    def update_register_from_table(self, addr, new_value):
        """从表格更新寄存器值"""
        # 委托给寄存器更新处理器
        self.register_update_processor.update_register_from_table(addr, new_value)
    
    def _update_register_value_and_display(self, addr, new_value):
        """更新寄存器值并显示

        Args:
            addr: 寄存器地址
            new_value: 新的寄存器值

        Returns:
            bool: 是否成功更新
        """
        # 委托给寄存器更新处理器
        return self.register_update_processor.update_register_value_and_display(addr, new_value)
    
    def _handle_simulation_write(self, addr, new_value):
        """模拟模式下处理写入操作

        Args:
            addr: 寄存器地址
            new_value: 新值
        """
        # 委托给寄存器更新处理器
        self.register_update_processor.handle_simulation_write(addr, new_value)
    
    def _execute_spi_write(self, addr, new_value):
        """通过SPI执行寄存器写入

        Args:
            addr: 寄存器地址
            new_value: 新值
        """
        # 委托给SPI操作协调器
        self.spi_coordinator.execute_write_operation(addr, new_value)
    
    def _execute_spi_read(self, addr):
        """执行SPI读取操作"""
        # 委托给SPI操作协调器
        self.spi_coordinator.execute_read_operation(addr)
    
    def _check_spi_availability(self):
        """检查SPI可用性"""
        # 委托给寄存器更新处理器
        return self.register_update_processor.check_spi_availability()
    
    def _handle_operation_timeout(self):
        """处理操作超时"""
        # 委托给SPI操作协调器
        self.spi_coordinator.handle_operation_timeout()
    
    def handle_spi_result(self, addr, value, is_read):
        """处理SPI操作结果"""
        # 委托给SPI操作协调器
        self.spi_coordinator.handle_spi_result(addr, value, is_read)
    
    def show_spi_error(self, error_msg):
        """显示SPI错误"""
        # 委托给SPI操作协调器
        self.spi_coordinator.show_error(error_msg)
    
    def _update_bit_field_display(self, addr, value):
        """更新位字段显示，不触发写操作"""
        # 委托给显示管理器
        self.display_manager.update_bit_field_display(addr, value)
    
    # 注意：_is_modifying_readonly_bits 和 _restore_original_value 方法
    # 已经移动到 RegisterOperationService 中
    
    def _normalize_register_address(self, addr):
        """标准化寄存器地址格式"""
        return self.register_manager._normalize_register_address(addr)
    
    def _handle_read_button_click(self):
        """处理读取按钮点击事件"""
        # 委托给事件协调器
        self.event_coordinator.handle_read_button_click()

    def _handle_write_button_click(self):
        """处理写入按钮点击事件"""
        # 委托给事件协调器
        self.event_coordinator.handle_write_button_click()
    


    def _handle_read_all_requested(self):
        """处理读取所有寄存器请求"""
        # 委托给事件协调器
        self.event_coordinator.handle_read_all_requested()

    def update_read_all_progress(self, addr, value, is_read):
        """更新读取所有寄存器的进度(仅适用于非模拟模式)"""
        # 委托给批量操作管理器
        self.batch_manager.update_read_all_progress(addr, value, is_read)

    def _handle_write_all_requested(self):
        """处理写入所有寄存器请求"""
        # 委托给事件协调器
        self.event_coordinator.handle_write_all_requested()

    def update_write_all_progress(self, addr, value, is_read):
        """更新写入所有寄存器的进度(仅适用于非模拟模式)"""
        # 委托给批量操作管理器
        self.batch_manager.update_write_all_progress(addr, value, is_read)

    def _handle_save_requested(self):
        """处理保存配置请求"""
        # 委托给事件协调器
        self.event_coordinator.handle_save_requested()

    def _handle_load_requested(self):
        """处理加载配置请求"""
        # 委托给事件协调器
        self.event_coordinator.handle_load_requested()

    def update_registers_from_config(self, loaded_values):
        """根据加载的配置更新寄存器管理器和UI"""
        # 委托给生命周期管理器
        return self.lifecycle_manager.update_registers_from_config(loaded_values)

    def _get_register_addr_from_tree_item(self):
        """从树形控件中获取当前选中的寄存器地址"""
        # 委托给生命周期管理器
        return self.lifecycle_manager.get_register_addr_from_tree_item()

    def close_tool_tab(self, index):
        """关闭工具标签页"""
        # 委托给生命周期管理器
        self.lifecycle_manager.close_tool_tab(index)

    def force_hide_tab_widget(self):
        """强制隐藏标签页容器"""
        try:
            if hasattr(self, 'tools_tab_widget'):
                self.tools_tab_widget.setVisible(False)
                # 强制刷新UI以确保布局更新
                self.tools_tab_widget.update()
                if hasattr(self, 'centralWidget'):
                    self.centralWidget().update()
                logger.info("已强制隐藏标签页容器")
            else:
                logger.warning("主窗口没有 tools_tab_widget 属性")
        except Exception as e:
            logger.error(f"强制隐藏标签页容器时出错: {str(e)}")

    def _uncheck_action_by_tab_text(self, tab_text):
        """根据标签页文本取消选中关联的QAction（后备方法）"""
        # 将标签页标题的已知前缀映射到其对应的QAction属性名称
        # 注意：这里的键是菜单项的文本，可能需要与工具栏按钮的文本或标签页的实际标题匹配
        action_map = {
            # 这些key应该与触发QAction的文本或tab标题的主要部分匹配
            "模式设置": self.set_modes_action if hasattr(self, 'set_modes_action') else None,
            "时钟输入": self.clkin_control_action if hasattr(self, 'clkin_control_action') else None, # 假设tab标题以“时钟输入”开头
            "PLL1 & PLL2 控制": self.pll_control_action if hasattr(self, 'pll_control_action') else None,
            "同步系统参考": self.sync_sysref_action if hasattr(self, 'sync_sysref_action') else None,
            "时钟输出": self.clk_output_action if hasattr(self, 'clk_output_action') else None,
        }

        action_to_uncheck = None
        matched_prefix_key = None

        # 尝试基于tab_text的前缀匹配
        for prefix_key, action_instance in action_map.items():
            if action_instance and tab_text.startswith(prefix_key):
                action_to_uncheck = action_instance
                matched_prefix_key = prefix_key
                break
        
        if action_to_uncheck:
            if hasattr(action_to_uncheck, 'setChecked') and callable(action_to_uncheck.setChecked):
                if not sip.isdeleted(action_to_uncheck):
                    action_to_uncheck.setChecked(False)
                    logger.info(f"后备方案: 取消选中菜单项 '{action_to_uncheck.text()}' (for tab '{tab_text}', matched prefix '{matched_prefix_key}')")
                else:
                    logger.warning(f"后备方案: QAction '{action_to_uncheck.text()}' (for tab '{tab_text}') 已被删除.")
            else:
                logger.warning(f"后备方案: 为标签页 '{tab_text}' 找到的Action对象无效或没有setChecked方法.")
        else:
            logger.warning(f"后备方案: 未能为标签页文本 '{tab_text}' 匹配到任何已知的QAction. 已知前缀: {list(action_map.keys())}")
    
    def on_global_register_updated(self, reg_addr, reg_value):
        """处理全局寄存器更新事件"""
        # 委托给显示管理器
        self.display_manager.handle_global_register_update(reg_addr, reg_value)

    def _is_in_batch_operation(self):
        """检查是否正在进行批量操作

        Returns:
            bool: 如果正在进行批量读取、写入或更新操作则返回True
        """
        # 委托给生命周期管理器
        return self.lifecycle_manager.is_in_batch_operation()

    def _refresh_current_register_after_batch(self):
        """批量操作完成后刷新当前选中的寄存器显示

        这确保用户在批量操作完成后看到的是最新的寄存器值
        """
        # 委托给生命周期管理器
        self.lifecycle_manager.refresh_current_register_after_batch()

    def _handle_bit_field_selected(self, addr):
        """处理位段选中事件，在批量操作期间不跳转

        Args:
            addr: 寄存器地址
        """
        # 委托给生命周期管理器
        self.lifecycle_manager.handle_bit_field_selected(addr)

    def _global_exception_handler(self, exc_type, exc_value, exc_traceback):
        """全局异常处理器"""
        # 委托给生命周期管理器
        self.lifecycle_manager._global_exception_handler(exc_type, exc_value, exc_traceback)

    def resource_path(self, relative_path):
        """获取资源文件路径"""
        # 委托给生命周期管理器
        return self.lifecycle_manager.get_resource_path(relative_path)

    def force_cancel_batch_operations(self):
        """强制取消所有批量操作，用于程序退出或其他紧急情况"""
        # 委托给生命周期管理器
        self.lifecycle_manager.force_cancel_batch_operations()
    
    def closeEvent(self, event):
        """关闭窗口事件处理"""
        # 委托给生命周期管理器
        self.lifecycle_manager.handle_close_event(event)

    def _force_close(self, event):
        """超时强制关闭处理"""
        # 委托给生命周期管理器
        self.lifecycle_manager._force_close(event)

    def _handle_value_changed(self, addr, new_value):
        """处理输入值变化事件"""
        # 委托给事件协调器
        self.event_coordinator.handle_value_changed(addr, new_value)
    

    
    def _toggle_preload(self, checked):
        """切换是否启用预读取功能

        Args:
            checked: 是否选中
        """
        # 委托给事件协调器
        self.event_coordinator.handle_preload_toggle(checked)

    def _set_preload_count(self):
        """设置预读取数量"""
        action = self.sender()
        # 委托给事件协调器
        self.event_coordinator.handle_preload_count_set(action)

    def _set_language(self):
        """设置语言"""
        action = self.sender()
        # 委托给事件协调器
        self.event_coordinator.handle_language_set(action)
    
    def _show_advanced_settings(self):
        """显示高级设置对话框"""
        # 委托给事件协调器
        self.event_coordinator.handle_advanced_settings_show()
    
    def _show_user_manual(self):
        """显示用户手册"""
        # 委托给UI工具管理器
        self.ui_utility_manager.show_user_manual()

    def _show_about_dialog(self):
        """显示关于对话框"""
        # 委托给UI工具管理器
        self.ui_utility_manager.show_about_dialog()
    
    def _create_window_in_tab(self, title, window_attr, window_class, action_attr=None, post_init_callback=None, *args, **kwargs):
        """通用方法：在标签页中创建窗口

        Args:
            title: 标签页标题
            window_attr: 窗口对象的属性名
            window_class: 窗口类
            action_attr: 菜单动作的属性名(可选)
            post_init_callback: 窗口初始化后的回调(可选)
            *args, **kwargs: 传递给窗口构造函数的参数

        Returns:
            创建的窗口对象
        """
        # 委托给窗口管理服务
        return self.window_service.create_window_in_tab(
            title, window_attr, window_class, action_attr, post_init_callback, *args, **kwargs
        )
            
    def _handle_window_closed(self, window_name, action_name, window_attr_name, log_prefix="工具窗口"):
        """通用窗口关闭处理方法

        Args:
            window_name: 窗口在日志中显示的名称
            action_name: 关联菜单动作的属性名
            window_attr_name: 窗口对象的属性名
            log_prefix: 日志前缀
        """
        # 委托给窗口管理服务
        self.window_service.handle_window_closed(window_name, action_name, window_attr_name, log_prefix)
            
    # 传统工具窗口方法已移除，现在通过插件系统管理
    # 所有工具窗口都通过插件集成服务动态创建和管理
    
    def refresh_view(self):
        """刷新当前视图显示
        在控件状态改变后调用此方法，确保表格视图同步更新
        """
        # 委托给显示管理器
        self.display_manager.refresh_current_view()

    def show_status_message(self, message, timeout=3000):
        """统一显示状态栏消息"""
        # 委托给显示管理器
        self.display_manager.show_status_message(message, timeout)

    def dumpall_button_clicked(self):
        """显示所有寄存器值（批量转储）"""
        # 委托给UI工具管理器
        self.ui_utility_manager.show_register_dump()

