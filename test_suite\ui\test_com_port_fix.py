#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试COM端口权限问题修复
验证端口管理器和改进的SPI服务是否能解决权限冲突
"""

import sys
import time
import logging
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def test_port_manager():
    """测试端口管理器"""
    print("=" * 60)
    print("测试端口管理器")
    print("=" * 60)
    
    try:
        from core.services.spi.port_manager import get_port_manager
        
        pm = get_port_manager()
        
        # 获取可用端口
        ports = pm.get_available_ports()
        print(f"发现 {len(ports)} 个端口:")
        for port in ports:
            status = "可用" if port['available'] else "占用"
            print(f"  {port['device']}: {port['description']} ({status})")
        
        if not ports:
            print("未发现任何COM端口")
            return False
        
        # 测试打开第一个端口
        test_port = ports[0]['device']
        print(f"\n测试打开端口: {test_port}")
        
        # 第一次打开
        ser1 = pm.open_port(test_port)
        if ser1:
            print(f"✓ 第一次打开成功: {test_port}")
            
            # 第二次打开同一端口（应该复用）
            ser2 = pm.open_port(test_port)
            if ser2:
                print(f"✓ 第二次打开成功（复用）: {test_port}")
                print(f"  连接对象相同: {ser1 is ser2}")
            else:
                print(f"❌ 第二次打开失败: {test_port}")
            
            # 关闭端口
            pm.close_port(test_port)
            print(f"✓ 端口已关闭: {test_port}")
            
            return True
        else:
            print(f"❌ 无法打开端口: {test_port}")
            return False
            
    except Exception as e:
        print(f"❌ 端口管理器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_spi_manager():
    """测试改进的SPI管理器"""
    print("\n" + "=" * 60)
    print("测试改进的SPI管理器")
    print("=" * 60)
    
    try:
        from core.services.spi.spiPrivacy import SPIManager
        
        # 创建第一个SPI管理器实例
        print("创建第一个SPI管理器实例...")
        spi1 = SPIManager()
        
        if spi1.ser and spi1.ser.is_open:
            print(f"✓ SPI1 成功连接到: {spi1.port_name}")
            
            # 创建第二个SPI管理器实例（应该复用连接）
            print("创建第二个SPI管理器实例...")
            spi2 = SPIManager(port=spi1.port_name)
            
            if spi2.ser and spi2.ser.is_open:
                print(f"✓ SPI2 成功连接到: {spi2.port_name}")
                print(f"  连接对象相同: {spi1.ser is spi2.ser}")
            else:
                print("❌ SPI2 连接失败")
            
            # 关闭连接
            spi1.close()
            spi2.close()
            print("✓ SPI连接已关闭")
            
            return True
        else:
            print("❌ SPI1 连接失败")
            return False
            
    except Exception as e:
        print(f"❌ SPI管理器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_spi_service():
    """测试SPI服务"""
    print("\n" + "=" * 60)
    print("测试SPI服务")
    print("=" * 60)
    
    try:
        from core.services.spi.spi_service_impl import SPIServiceImpl
        
        # 创建SPI服务实例
        spi_service = SPIServiceImpl()
        
        # 初始化服务
        if spi_service.initialize():
            print("✓ SPI服务初始化成功")
            
            # 获取可用端口
            ports = spi_service.refresh_available_ports()
            if ports:
                test_port = ports[0]['device']
                print(f"测试设置端口: {test_port}")
                
                # 第一次设置端口
                success1 = spi_service.set_port(test_port)
                print(f"第一次设置端口: {'成功' if success1 else '失败'}")
                
                # 第二次设置相同端口（应该跳过重复设置）
                success2 = spi_service.set_port(test_port)
                print(f"第二次设置端口: {'成功' if success2 else '失败'}")
                
                return success1 and success2
            else:
                print("未发现可用端口")
                return False
        else:
            print("❌ SPI服务初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ SPI服务测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_main_application():
    """测试主应用程序启动"""
    print("\n" + "=" * 60)
    print("测试主应用程序启动")
    print("=" * 60)
    
    try:
        # 创建QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 导入主窗口
        from ui.windows.RegisterMainWindow import RegisterMainWindow

        # 创建主窗口 (现在使用依赖注入，不需要repository参数)
        main_window = RegisterMainWindow()
        print("✓ 主窗口创建成功")

        # 显示窗口
        main_window.show()
        print("✓ 主窗口显示成功")

        # 设置定时器在3秒后关闭
        def close_app():
            print("✓ 应用程序测试完成，正在关闭...")
            main_window.close()
            app.quit()

        timer = QTimer()
        timer.singleShot(3000, close_app)

        # 运行事件循环
        app.exec_()

        return True
            
    except Exception as e:
        print(f"❌ 主应用程序测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("COM端口权限问题修复验证")
    print("测试端口管理器和改进的SPI服务")
    
    results = []
    
    # 测试1: 端口管理器
    results.append(("端口管理器", test_port_manager()))
    
    # 测试2: SPI管理器
    results.append(("SPI管理器", test_spi_manager()))
    
    # 测试3: SPI服务
    results.append(("SPI服务", test_spi_service()))
    
    # 测试4: 主应用程序
    results.append(("主应用程序", test_main_application()))
    
    # 总结结果
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "❌ 失败"
        print(f"{test_name:<15}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！COM端口权限问题已解决。")
        print("现在可以正常运行应用程序，不会再出现权限冲突。")
    else:
        print(f"\n⚠️  {total - passed} 个测试失败，仍需要进一步调试。")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
