"""
资源和工具管理器
负责管理资源路径、异常处理、应用程序关闭等功能
"""

import os
import sys
import traceback
from PyQt5.QtWidgets import QMessageBox, QTableWidget, QTableWidgetItem, QHeaderView
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QIcon
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class ResourceAndUtilityManager:
    """资源和工具管理器，管理资源路径和工具功能"""
    
    def __init__(self, main_window):
        """初始化资源和工具管理器
        
        Args:
            main_window: 主窗口实例
        """
        self.main_window = main_window
    
    def resource_path(self, relative_path):
        """获取资源文件路径"""
        # 尝试获取运行目录
        if getattr(sys, 'frozen', False):
            # 打包后的路径
            try:
                base_path = sys._MEIPASS
            except AttributeError:
                base_path = os.path.dirname(sys.executable)
        else:
            # 开发环境路径
            base_path = os.path.abspath(".")
            
        # 构建完整路径
        full_path = os.path.join(base_path, relative_path)
        
        # 检查文件是否存在，如果不存在则尝试替代路径
        if not os.path.exists(full_path):
            # 尝试当前目录
            current_dir_path = os.path.join(os.path.dirname(__file__), relative_path)
            if os.path.exists(current_dir_path):
                return current_dir_path
                
            # 尝试上级目录
            parent_dir_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), relative_path)
            if os.path.exists(parent_dir_path):
                return parent_dir_path
            
        return full_path
    
    def global_exception_handler(self, exc_type, exc_value, exc_traceback):
        """全局异常处理器"""
        error_msg = "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        logger.error(f"未捕获的异常: {error_msg}")
        QMessageBox.critical(
            self.main_window, 
            "程序错误", 
            f"发生未捕获的异常:\n{str(exc_value)}\n\n详细信息已记录到日志文件"
        )
    
    def create_dump_table(self):
        """创建寄存器转储表格"""
        # 委托给UI工具管理器处理，保持功能统一
        if hasattr(self.main_window, 'ui_utility_manager'):
            self.main_window.ui_utility_manager.show_register_dump()
        else:
            # 后备方案：使用旧的实现但改为动态布局
            self._create_legacy_dump_table()

    def _create_legacy_dump_table(self):
        """创建寄存器转储表格（后备方案，使用动态布局）"""
        # 如果窗口已存在且可见，先关闭
        if hasattr(self.main_window, 'reg_table') and self.main_window.reg_table.isVisible():
            self.main_window.reg_table.close()

        # 获取寄存器数量
        reg_count = len(self.main_window.register_manager.register_objects)
        if reg_count == 0:
            QMessageBox.information(self.main_window, "提示", "没有可显示的寄存器数据")
            return

        # 计算表格行列
        col_count = min(int(reg_count ** 0.5) + 1, 8)
        row_count = (reg_count + col_count - 1) // col_count
        self.main_window.reg_table = QTableWidget()
        self.main_window.reg_table.setWindowTitle("Register Dump - Dynamic Layout")
        self.main_window.reg_table.setWindowIcon(QIcon(self.resource_path('images/logo.ico')))
        self.main_window.reg_table.setRowCount(row_count)
        self.main_window.reg_table.setColumnCount(col_count)
        self.main_window.reg_table.setEditTriggers(QTableWidget.NoEditTriggers)

        # 使用拉伸模式，让表格铺满整个窗口
        self.main_window.reg_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

        self.main_window.reg_table.verticalHeader().setVisible(False)
        self.main_window.reg_table.horizontalHeader().setVisible(False)
        self.main_window.reg_table.setAlternatingRowColors(True)
        self.main_window.reg_table.setVerticalScrollMode(QTableWidget.ScrollPerPixel)
        self.main_window.reg_table.setHorizontalScrollMode(QTableWidget.ScrollPerPixel)

        # 设置表格选择行为
        self.main_window.reg_table.setSelectionBehavior(QTableWidget.SelectItems)
        self.main_window.reg_table.setSelectionMode(QTableWidget.SingleSelection)

        # 去除表格边框和边距，让内容铺满窗口
        self.main_window.reg_table.setContentsMargins(0, 0, 0, 0)
        self.main_window.reg_table.setFrameStyle(0)  # 去除边框
        self.main_window.reg_table.setLineWidth(0)

        # 设置表格样式，去除边距和间隙
        self.main_window.reg_table.setStyleSheet("""
            QTableWidget {
                border: none;
                margin: 0px;
                padding: 0px;
                gridline-color: #E0E0E0;
                background-color: white;
            }
            QTableWidget::item {
                border: 1px solid #E0E0E0;
                padding: 4px;
                margin: 0px;
            }
            QTableWidget::item:selected {
                background-color: #3399FF;
                color: white;
            }
            QTableWidget::item:alternate {
                background-color: #F5F5F5;
            }
        """)

        # 填充数据
        items_to_set = []
        register_values = self.main_window.register_manager.get_all_register_values() if hasattr(self.main_window.register_manager, 'get_all_register_values') else {
            addr: reg.get("current_value", 0) for addr, reg in self.main_window.register_manager.register_objects.items()
        }
        for index, (addr, value) in enumerate(register_values.items()):
            row = index // col_count
            col = index % col_count
            reg_num = int(addr, 16) if isinstance(addr, str) else addr
            item = QTableWidgetItem(f"R{reg_num} = 0x{value:04X}")
            item.setTextAlignment(Qt.AlignCenter)
            items_to_set.append((row, col, item))
        for row, col, item in items_to_set:
            self.main_window.reg_table.setItem(row, col, item)

        # 使用动态尺寸计算
        self._optimize_legacy_table_size(col_count, row_count)
        self.main_window.reg_table.show()

    def _optimize_legacy_table_size(self, col_count, row_count):
        """优化后备方案表格的窗口尺寸"""
        try:
            # 等待表格内容完全加载后计算尺寸
            from PyQt5.QtCore import QTimer
            QTimer.singleShot(50, lambda: self._calculate_legacy_table_size(col_count, row_count))
        except Exception as e:
            logger.warning(f"动态计算后备表格大小失败，使用默认大小: {e}")
            # 回退到默认大小
            width = max(600, min(col_count * 120, 1200))
            height = max(400, min(row_count * 40, 800))
            self.main_window.reg_table.resize(width, height)

    def _calculate_legacy_table_size(self, col_count, row_count):
        """计算后备方案表格的动态大小，铺满窗口"""
        try:
            table = self.main_window.reg_table

            # 获取屏幕信息
            from PyQt5.QtWidgets import QApplication
            screen = QApplication.primaryScreen()
            screen_geometry = screen.availableGeometry()
            max_width = int(screen_geometry.width() * 0.9)  # 增加到90%屏幕宽度
            max_height = int(screen_geometry.height() * 0.9)  # 增加到90%屏幕高度

            # 计算基础内容尺寸（不包含边距）
            min_col_width = 120  # 每列最小宽度
            min_content_width = col_count * min_col_width

            # 计算实际内容高度（去除边距）
            row_height = table.rowHeight(0) if row_count > 0 else 30
            content_height = row_count * row_height + 30  # 只保留标题栏高度

            # 优化窗口尺寸：让表格内容铺满窗口
            final_width = min(max(min_content_width, 600), max_width)
            final_height = min(max(content_height, 400), max_height)

            # 应用新尺寸并居中
            table.resize(final_width, final_height)
            x = (screen_geometry.width() - final_width) // 2
            y = (screen_geometry.height() - final_height) // 2
            table.move(x, y)

            logger.info(f"后备方案铺满窗口的dump表格大小: {final_width}x{final_height} (列数: {col_count}, 行数: {row_count})")

        except Exception as e:
            logger.error(f"计算后备方案表格大小失败: {e}")
    
    def force_cancel_batch_operations(self):
        """强制取消所有批量操作，用于程序退出或其他紧急情况"""
        try:
            # 委托给批量操作管理器
            self.main_window.batch_manager.force_cancel_all_operations()

            # 停止所有定时器
            if hasattr(self.main_window, 'operation_timer'):
                self.main_window.operation_timer.stop()

            # 确保按钮被重新启用
            self.main_window.io_handler.toggle_buttons(True)

            logger.info("已强制取消所有批量操作并清理资源")

        except Exception as e:
            logger.error(f"强制取消批量操作时出错: {str(e)}")
            # 确保按钮被重新启用
            self.main_window.io_handler.toggle_buttons(True)
    
    def force_close(self, event):
        """超时强制关闭处理"""
        logger.warning("关闭操作超时，强制关闭应用程序")
        event.accept()
    
    def handle_close_event(self, event):
        """处理窗口关闭事件"""
        logger.info("应用程序正在关闭...")
        
        # 禁用窗口输入，避免多次点击
        self.main_window.setEnabled(False)
        
        try:
            # 强制取消所有批量操作
            self.force_cancel_batch_operations()

            # 关闭所有管理的窗口
            if hasattr(self.main_window, 'window_service'):
                self.main_window.window_service.close_all_windows()

            # 创建计时器用于超时检测
            close_timer = QTimer()
            close_timer.setSingleShot(True)
            close_timer.timeout.connect(lambda: self.force_close(event))
            close_timer.start(5000)  # 5秒超时

            # 保存用户设置
            self.main_window.config_service.save_simulation_mode(self.main_window.simulation_mode)

            # 清理SPI资源
            if hasattr(self.main_window, 'spi_service') and self.main_window.spi_service:
                try:
                    self.main_window.spi_service.cleanup()
                except Exception as e:
                    logger.error(f"清理SPI资源时出错: {e}")

            # 停止超时计时器，关闭正常
            close_timer.stop()
            logger.info("应用程序关闭完成")
                
        except Exception as e:
            logger.error(f"关闭时清理资源失败: {e}")
        finally:
            # 确保事件被接受
            event.accept()
    
    def refresh_ui(self):
        """刷新主界面UI，确保寄存器和表格等控件同步最新值"""
        # 刷新当前选中寄存器的详细信息和表格
        self.main_window.refresh_view()
        # 可以根据需要扩展刷新其他控件
    
    def refresh_current_register_after_batch(self):
        """批量操作完成后刷新当前选中的寄存器显示

        这确保用户在批量操作完成后看到的是最新的寄存器值
        """
        try:
            if hasattr(self.main_window, 'selected_register_addr') and self.main_window.selected_register_addr:
                # 获取当前选中寄存器的最新值
                current_value = self.main_window.register_manager.get_register_value(self.main_window.selected_register_addr)
                reg_num = int(self.main_window.selected_register_addr, 16)

                # 更新显示
                self.main_window._update_rx_value_display(reg_num, current_value)

                # 注意：移除了位字段表格的重复更新，避免第三次调用
                # 位字段表格更新现在统一由RegisterDisplayManager处理
                logger.debug("ResourceAndUtilityManager: 跳过位字段表格更新，避免重复调用")

                # 刷新视图
                self.main_window.refresh_view()

                logger.debug(f"批量操作完成后刷新了当前寄存器 {self.main_window.selected_register_addr} 的显示")

        except Exception as e:
            logger.warning(f"批量操作完成后刷新当前寄存器显示时出错: {str(e)}")
    
    def populate_dump_table(self):
        """填充转储表格数据"""
        if hasattr(self.main_window, 'reg_table') and self.main_window.reg_table:
            self.create_dump_table()
    
    def cleanup_resources(self):
        """清理所有资源"""
        try:
            # 关闭转储表格
            if hasattr(self.main_window, 'reg_table') and self.main_window.reg_table:
                try:
                    self.main_window.reg_table.close()
                except Exception as e:
                    logger.warning(f"关闭转储表格时出错: {str(e)}")
            
            # 清理其他资源
            logger.debug("资源和工具管理器资源已清理")
        except Exception as e:
            logger.error(f"清理资源和工具管理器资源时出错: {str(e)}")
    
    def get_open_tool_windows_info(self):
        """获取当前打开的工具窗口信息
        
        Returns:
            dict: 包含打开窗口信息的字典
        """
        info = {
            'total_windows': 0,
            'window_list': []
        }
        
        # 检查各种工具窗口
        tool_windows = [
            ('reg_table', '寄存器转储表格'),
            ('set_modes_window', '模式设置窗口'),
            ('clkin_control_window', '时钟输入控制窗口'),
            ('pll_control_window', 'PLL控制窗口'),
            ('sync_sysref_window', '同步系统参考窗口'),
            ('clk_output_window', '时钟输出窗口')
        ]
        
        for window_attr, window_name in tool_windows:
            if hasattr(self.main_window, window_attr):
                window = getattr(self.main_window, window_attr)
                if window and hasattr(window, 'isVisible') and window.isVisible():
                    info['window_list'].append(window_name)
                    info['total_windows'] += 1
        
        return info
