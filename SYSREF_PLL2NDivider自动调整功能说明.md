# SYSREF频率缓存和PLL2NDivider自动调整功能说明

## 概述

根据用户需求，实现了当FBMUX选择为SYSREF DIVIDER且PLL2NclkMux为feedback mux模式时，PLL2NDivider的自动调整功能。同时实现了SYSREF频率的缓存机制，确保PLL页面启动时能获取到正确的PLL2Cin值。

## 功能需求

### 1. 核心需求
- **自动调整条件**：当`FBMuxEn`选中 && `FBMUX=SYSREF DIVIDER` && `PLL2NclkMux=feedback mux`时
- **调整公式**：`PLL2Cin × PLL2NDivider = PLL2PFDFreq`
- **自动调整**：当PLL2Cin值变化时，PLL2NDivider值自动调整以保证等式成立
- **缓存机制**：SYSREF频率值在事件总线中缓存，确保跨窗口同步

### 2. 技术要求
- PLL2Cin值来自系统参考输入页面
- 需要实时同步机制
- PLL页面启动时应获取到准确的当前数值而不是0

## 实现方案

### 1. 事件总线扩展 (`core/event_bus/RegisterUpdateBus.py`)

#### 新增信号和缓存
```python
# 新增SYSREF频率更新信号
sysref_freq_updated = pyqtSignal(float)

# 扩展频率缓存
self._frequency_cache = {
    'vco_dist_freq': None,
    'pll1_pfd_freq': None,
    'sysref_freq': None     # 新增SYSREF频率缓存
}
```

#### 新增缓存方法
```python
def cache_sysref_freq(self, freq_value):
    """缓存SYSREF频率值并发送更新信号"""
    
def get_cached_sysref_freq(self):
    """获取缓存的SYSREF频率值"""
```

### 2. 同步系统参考处理器修改 (`ui/handlers/ModernSyncSysRefHandler.py`)

#### 自动缓存SYSREF频率
```python
def calculate_output_frequencies(self):
    """计算同步系统参考频率"""
    # 原有计算逻辑...
    sysref_freq = fvco / sysref_div
    
    # 新增：将SYSREF频率发送到事件总线缓存
    self._cache_sysref_frequency(sysref_freq)

def _cache_sysref_frequency(self, sysref_freq):
    """将SYSREF频率缓存到事件总线"""
    bus = RegisterUpdateBus.instance()
    if bus:
        bus.cache_sysref_freq(sysref_freq)
```

### 3. PLL处理器增强 (`ui/handlers/ModernPLLHandler.py`)

#### 优化SYSREF频率获取
```python
def _get_sysref_frequency(self):
    """优先从缓存获取SYSREF频率，然后从窗口获取"""
    # 1. 首先尝试从事件总线缓存获取
    bus = RegisterUpdateBus.instance()
    cached_freq = bus.get_cached_sysref_freq()
    if cached_freq is not None:
        return cached_freq
    
    # 2. 如果缓存中没有，从同步系统参考窗口获取
    # 3. 将获取到的频率缓存到事件总线
```

#### 连接SYSREF频率更新信号
```python
def _connect_special_signals(self):
    """连接特殊信号"""
    # 原有信号连接...
    
    # 新增：连接SYSREF频率更新信号
    RegisterUpdateBus.instance().sysref_freq_updated.connect(self._on_sysref_freq_updated)
```

#### 实现自动调整逻辑
```python
def _on_sysref_freq_updated(self, sysref_freq):
    """处理SYSREF频率更新信号"""
    if self._should_auto_adjust_pll2_ndivider():
        self._update_pll2cin_value()  # 更新PLL2Cin显示
        self._auto_adjust_pll2_ndivider_for_sysref(sysref_freq)  # 自动调整
        self.calculate_output_frequencies()  # 重新计算

def _should_auto_adjust_pll2_ndivider(self):
    """检查自动调整条件"""
    return (self.ui.FBMuxEn.isChecked() and 
            self.ui.FBMUX.currentIndex() == 2 and  # SYSREF DIVIDER
            self.ui.PLL2NclkMux.currentIndex() == 1)  # feedback mux

def _auto_adjust_pll2_ndivider_for_sysref(self, sysref_freq):
    """根据公式自动调整PLL2NDivider"""
    pll2_pfd_freq = float(self.ui.PLL2PFDFreq.text())
    new_n_divider = pll2_pfd_freq / sysref_freq
    rounded_n_divider = max(1, round(new_n_divider))
    self.ui.PLL2NDivider.setValue(rounded_n_divider)
```

#### 初始化时从缓存获取值
```python
def _init_pll2cin_value(self):
    """初始化PLL2Cin控件的值"""
    # 如果FBMUX设置为SYSREF DIVIDER，尝试从缓存获取
    if fb_mux_value == 2:  # SYSREF DIVIDER
        cached_sysref_freq = bus.get_cached_sysref_freq()
        if cached_sysref_freq is not None:
            self.ui.PLL2Cin.setText(f"{cached_sysref_freq:.5f}")
            return
    
    # 否则调用常规更新方法
    self._update_pll2cin_value()
```

## 工作流程

### 1. 系统启动流程
1. **同步系统参考窗口**计算SYSREF频率并缓存到事件总线
2. **PLL窗口**启动时从缓存获取SYSREF频率初始化PLL2Cin
3. 如果满足自动调整条件，根据当前PLL2PFDFreq自动调整PLL2NDivider

### 2. 运行时更新流程
1. **用户修改**同步系统参考窗口的VCO频率或分频比
2. **重新计算**SYSREF频率并更新缓存
3. **事件总线**发送SYSREF频率更新信号
4. **PLL窗口**接收信号，更新PLL2Cin显示
5. **检查条件**，如果满足自动调整条件则调整PLL2NDivider
6. **重新计算**PLL相关频率

## 测试验证

### 基本功能测试
```
测试参数:
  PLL2PFDFreq: 100.0 MHz
  SYSREF频率: 0.73728 MHz
  
计算结果:
  计算的PLL2NDivider: 135.633681
  调整后的PLL2NDivider: 136
  验证: 0.73728 × 136 = 100.270 MHz (接近100.0)
```

### 自动调整条件
- ✅ FBMuxEn选中
- ✅ FBMUX = SYSREF DIVIDER (索引2)
- ✅ PLL2NclkMux = feedback mux (索引1)

## 优势特点

### 1. 实时同步
- 跨窗口实时同步SYSREF频率值
- 无需手动打开系统参考输入页面即可获取最新值

### 2. 自动调整
- 满足条件时自动调整PLL2NDivider
- 确保公式 `PLL2Cin × PLL2NDivider = PLL2PFDFreq` 始终成立

### 3. 缓存机制
- 事件总线缓存机制确保数据持久性
- 窗口关闭重开时仍能获取到正确值

### 4. 防循环调用
- 实现了防循环调用机制，避免无限递归
- 智能检测调整条件，只在必要时执行

## 使用场景

### 适用情况
- FBMUX选择为SYSREF DIVIDER
- PLL2NclkMux设置为feedback mux模式
- 需要PLL2Cin和PLL2NDivider保持特定关系

### 用户体验
- **透明化**：用户无需关心复杂的同步机制
- **自动化**：满足条件时自动调整，减少手动操作
- **实时性**：频率变化立即反映到相关控件

## 总结

该功能成功实现了SYSREF频率的缓存和PLL2NDivider的自动调整，解决了用户提出的跨窗口同步问题。通过事件总线的缓存机制和信号系统，确保了数据的实时性和一致性，提升了用户操作的便利性和系统的智能化程度。
