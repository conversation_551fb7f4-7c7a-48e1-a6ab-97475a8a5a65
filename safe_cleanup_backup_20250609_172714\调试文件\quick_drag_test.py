#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速测试拖拽停靠功能
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QLabel
from PyQt5.QtCore import Qt, QPoint
from PyQt5.QtGui import QMouseEvent
from utils.Log import get_module_logger

logger = get_module_logger(__name__)

class QuickDragTestWindow(QMainWindow):
    """快速拖拽测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.main_window = None
        self._drag_start_position = None
        self._is_dragging = False
        self._drag_confirmed = False
        self.init_ui()
        self.find_main_window()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("快速拖拽测试")
        self.setGeometry(300, 300, 400, 200)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        title_label = QLabel("快速拖拽测试窗口")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; text-align: center;")
        layout.addWidget(title_label)
        
        self.status_label = QLabel("状态: 准备就绪")
        layout.addWidget(self.status_label)
        
        instruction_label = QLabel("拖拽此窗口到主窗口底部50%区域进行测试")
        layout.addWidget(instruction_label)
        
        # 启用鼠标跟踪
        self.setMouseTracking(True)
        
    def find_main_window(self):
        """查找主窗口"""
        try:
            for widget in QApplication.topLevelWidgets():
                if (isinstance(widget, QMainWindow) and 
                    widget != self and 
                    hasattr(widget, 'menuBar')):
                    self.main_window = widget
                    logger.info(f"找到主窗口: {widget.windowTitle()}")
                    break
        except Exception as e:
            logger.error(f"查找主窗口失败: {str(e)}")
            
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self._drag_start_position = event.globalPos()
            self._is_dragging = False
            self._drag_confirmed = False
            self.status_label.setText("鼠标按下")
            logger.info(f"🖱️ 鼠标按下 - 位置: {event.globalPos()}")
        super().mousePressEvent(event)
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        # 检查按钮状态
        buttons_pressed = event.buttons()
        left_button_pressed = bool(buttons_pressed & Qt.LeftButton)
        
        logger.info(f"🖱️ 鼠标移动 - 位置: {event.globalPos()}, 左键: {left_button_pressed}")
        
        if (self._drag_start_position and left_button_pressed):
            
            if not self._is_dragging:
                # 检查拖拽距离
                distance = (event.globalPos() - self._drag_start_position).manhattanLength()
                drag_threshold = 2
                logger.info(f"🖱️ 拖拽距离: {distance}, 阈值: {drag_threshold}")
                
                if distance >= drag_threshold:
                    self._is_dragging = True
                    self._drag_confirmed = True
                    self.status_label.setText(f"开始拖拽 (距离: {distance})")
                    logger.info(f"🎯 开始拖拽 - 距离: {distance}")
            
            if self._is_dragging:
                # 检查停靠区域
                in_dock_area = self._is_in_dock_area(event.globalPos())
                logger.info(f"🎯 在停靠区域: {in_dock_area}")
                
                if in_dock_area:
                    self.setWindowTitle("快速拖拽测试 - 释放鼠标停靠到主界面")
                    self.status_label.setText("在停靠区域 - 可以释放鼠标")
                else:
                    self.setWindowTitle("快速拖拽测试")
                    self.status_label.setText("拖拽中...")
        
        super().mouseMoveEvent(event)
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton and self._drag_confirmed:
            logger.info(f"🖱️ 鼠标释放 - 位置: {event.globalPos()}")
            
            # 检查是否在停靠区域释放
            in_dock_area = self._is_in_dock_area(event.globalPos())
            
            if in_dock_area:
                self.status_label.setText("在停靠区域释放 - 应该停靠")
                logger.info("✅ 在停靠区域释放，应该触发停靠")
            else:
                self.status_label.setText("不在停靠区域 - 保持悬浮")
                logger.info("❌ 不在停靠区域，保持悬浮状态")
        
        # 重置状态
        self._drag_start_position = None
        self._is_dragging = False
        self._drag_confirmed = False
        self.setWindowTitle("快速拖拽测试")
        
        super().mouseReleaseEvent(event)
        
    def _is_in_dock_area(self, global_pos):
        """判断是否在停靠区域内"""
        try:
            if not self.main_window:
                return False
                
            # 获取主窗口几何信息
            main_geometry = None
            
            try:
                main_geometry = self.main_window.frameGeometry()
                if main_geometry.isEmpty() or main_geometry.width() <= 0 or main_geometry.height() <= 0:
                    main_geometry = None
            except Exception:
                main_geometry = None

            if main_geometry is None:
                try:
                    main_geometry = self.main_window.geometry()
                    main_global_pos = self.main_window.mapToGlobal(QPoint(0, 0))
                    main_geometry.moveTopLeft(main_global_pos)
                except Exception:
                    return False

            if (main_geometry.isEmpty() or 
                main_geometry.width() <= 0 or 
                main_geometry.height() <= 0):
                return False

            # 计算停靠区域（底部50%）
            dock_area_height = int(main_geometry.height() * 0.5)
            dock_area_top = main_geometry.top() + main_geometry.height() - dock_area_height
            dock_area_bottom = main_geometry.bottom()

            # 边距
            margin = 5
            dock_area_left = main_geometry.left() + margin
            dock_area_right = main_geometry.right() - margin
            dock_area_top += margin
            dock_area_bottom -= margin

            # 检查是否在区域内
            is_in_area = (global_pos.x() >= dock_area_left and
                         global_pos.x() <= dock_area_right and
                         global_pos.y() >= dock_area_top and
                         global_pos.y() <= dock_area_bottom)

            # 详细调试信息
            logger.info(f"🎯 停靠区域检测:")
            logger.info(f"  鼠标位置: ({global_pos.x()}, {global_pos.y()})")
            logger.info(f"  主窗口几何: {main_geometry}")
            logger.info(f"  停靠区域: X({dock_area_left}-{dock_area_right}), Y({dock_area_top}-{dock_area_bottom})")
            logger.info(f"  在停靠区域: {is_in_area}")

            return is_in_area

        except Exception as e:
            logger.error(f"停靠区域检测失败: {str(e)}")
            return False

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    test_window = QuickDragTestWindow()
    test_window.show()
    
    logger.info("快速拖拽测试窗口已启动")
    logger.info("请拖拽窗口到主窗口底部50%区域进行测试")
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
