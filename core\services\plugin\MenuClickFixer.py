# -*- coding: utf-8 -*-

"""
菜单点击修复工具
专门解决菜单点击无响应的问题
"""

from PyQt5.QtWidgets import QAction, QMenu
from PyQt5.QtCore import QTimer, QObject, pyqtSignal
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class MenuClickFixer(QObject):
    """菜单点击修复器
    
    解决菜单点击无响应或需要滑动才能触发的问题
    """
    
    # 信号
    action_clicked = pyqtSignal(QAction, bool)  # 动作点击信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.fixed_actions = {}  # 存储已修复的动作
        
    def fix_action(self, action: QAction, callback=None):
        """修复动作的点击响应
        
        Args:
            action: 要修复的QAction
            callback: 回调函数，接收(action, checked)参数
        """
        if not action:
            return
            
        action_id = id(action)
        if action_id in self.fixed_actions:
            logger.debug(f"动作已经修复过: {action.text()}")
            return
            
        logger.info(f"开始修复动作点击响应: {action.text()}")
        
        # 断开所有现有连接（如果需要）
        # action.triggered.disconnect()  # 谨慎使用，可能断开其他重要连接
        
        # 创建增强的事件处理器
        def enhanced_handler(checked=False):
            """增强的事件处理器"""
            try:
                logger.info(f"🎯 增强处理器触发: {action.text()}, checked={checked}")
                
                # 发送信号
                self.action_clicked.emit(action, checked)
                
                # 调用回调函数
                if callback:
                    QTimer.singleShot(0, lambda: callback(action, checked))
                    
            except Exception as e:
                logger.error(f"增强处理器失败: {e}")
        
        # 连接增强的处理器
        action.triggered.connect(enhanced_handler)
        
        # 添加额外的事件监听
        self._add_extra_event_listeners(action, enhanced_handler)
        
        # 确保动作属性正确
        self._ensure_action_properties(action)
        
        # 记录已修复的动作
        self.fixed_actions[action_id] = {
            'action': action,
            'handler': enhanced_handler,
            'callback': callback
        }
        
        logger.info(f"动作点击响应修复完成: {action.text()}")
    
    def _add_extra_event_listeners(self, action: QAction, handler):
        """添加额外的事件监听器"""
        try:
            # 监听悬停事件
            action.hovered.connect(
                lambda: logger.debug(f"鼠标悬停: {action.text()}")
            )
            
            # 监听切换事件（对于可选中的动作）
            if action.isCheckable():
                action.toggled.connect(
                    lambda checked: logger.debug(f"动作切换: {action.text()}, checked={checked}")
                )
            
        except Exception as e:
            logger.warning(f"添加额外事件监听器失败: {e}")
    
    def _ensure_action_properties(self, action: QAction):
        """确保动作属性正确设置"""
        try:
            # 确保动作可见和可用
            action.setVisible(True)
            action.setEnabled(True)
            
            # 设置工具提示（如果没有的话）
            if not action.toolTip():
                action.setToolTip(f"点击{action.text()}")
            
            # 设置状态提示（如果没有的话）
            if not action.statusTip():
                action.setStatusTip(f"执行{action.text()}")
                
        except Exception as e:
            logger.warning(f"设置动作属性失败: {e}")
    
    def fix_menu(self, menu: QMenu, action_callback=None):
        """修复整个菜单的点击响应
        
        Args:
            menu: 要修复的QMenu
            action_callback: 动作回调函数
        """
        if not menu:
            return
            
        logger.info(f"开始修复菜单点击响应: {menu.title()}")
        
        # 修复菜单中的所有动作
        for action in menu.actions():
            if not action.isSeparator():
                self.fix_action(action, action_callback)
        
        # 确保菜单属性正确
        self._ensure_menu_properties(menu)
        
        logger.info(f"菜单点击响应修复完成: {menu.title()}")
    
    def _ensure_menu_properties(self, menu: QMenu):
        """确保菜单属性正确设置"""
        try:
            # 确保菜单可见和可用
            menu.setEnabled(True)
            menu.setVisible(True)
            
            # 强制更新菜单
            menu.update()
            
            # 添加菜单显示事件监听
            menu.aboutToShow.connect(
                lambda: logger.debug(f"菜单即将显示: {menu.title()}")
            )
            
            menu.aboutToHide.connect(
                lambda: logger.debug(f"菜单即将隐藏: {menu.title()}")
            )
            
        except Exception as e:
            logger.warning(f"设置菜单属性失败: {e}")
    
    def create_robust_action(self, text: str, parent=None, callback=None, checkable=False):
        """创建一个具有强健点击响应的动作
        
        Args:
            text: 动作文本
            parent: 父对象
            callback: 回调函数
            checkable: 是否可选中
            
        Returns:
            QAction: 创建的动作
        """
        action = QAction(text, parent)
        action.setCheckable(checkable)
        
        # 立即修复这个动作
        self.fix_action(action, callback)
        
        logger.info(f"创建强健动作: {text}")
        return action
    
    def get_fixed_actions_count(self):
        """获取已修复的动作数量"""
        return len(self.fixed_actions)
    
    def cleanup(self):
        """清理资源"""
        logger.info(f"清理菜单修复器，已修复 {len(self.fixed_actions)} 个动作")
        self.fixed_actions.clear()


# 全局菜单修复器实例
menu_click_fixer = MenuClickFixer()
