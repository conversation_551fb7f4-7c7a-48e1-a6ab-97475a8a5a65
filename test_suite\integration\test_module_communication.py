#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
模块间通信集成测试
测试不同模块之间的交互和通信
"""

import sys
import os
import unittest

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from test_suite.test_utils import TestLogger, TestResult, qt_application, MockRegisterManager, create_mock_main_window
from test_suite.test_config import get_test_config

class TestModuleCommunication(unittest.TestCase):
    """模块间通信测试类"""
    
    def setUp(self):
        """测试设置"""
        self.logger = TestLogger("module_communication_test")
        self.test_results = []
    
    def tearDown(self):
        """测试清理"""
        pass
    
    def test_main_window_to_tool_window_communication(self):
        """测试主窗口与工具窗口的通信"""
        result = TestResult("main_window_to_tool_window_communication")
        
        try:
            with qt_application():
                from ui.windows.RegisterMainWindow import RegisterMainWindow
                from core.services.spi.spi_service_impl import SPIServiceImpl
                from core.repositories.register_repository import RegisterRepository
                
                # 创建主窗口
                spi_service = SPIServiceImpl()
                spi_service.initialize()
                register_repo = RegisterRepository(spi_service)
                main_window = RegisterMainWindow(register_repo)
                
                # 创建工具窗口
                pll_window = main_window.tool_window_factory.create_window_by_type('pll_control')
                
                if pll_window:
                    # 验证工具窗口与主窗口的连接
                    self.assertIsNotNone(pll_window)
                    
                    # 测试工具窗口是否能访问主窗口的服务
                    if hasattr(pll_window, 'register_manager'):
                        self.assertIsNotNone(pll_window.register_manager)
                    
                    result.add_detail('pll_window_created', True)
                    result.add_detail('window_type', type(pll_window).__name__)
                    result.set_success(True)
                else:
                    result.set_error("PLL工具窗口创建失败")
                
        except Exception as e:
            result.set_error(str(e))
            self.logger.error(f"主窗口与工具窗口通信测试失败: {e}")
        
        self.test_results.append(result)
        self.assertTrue(result.success)
    
    def test_register_manager_to_handlers_communication(self):
        """测试寄存器管理器与处理器的通信"""
        result = TestResult("register_manager_to_handlers_communication")
        
        try:
            with qt_application():
                from ui.handlers.ModernRegisterTreeHandler import ModernRegisterTreeHandler
                from ui.handlers.ModernRegisterTableHandler import ModernRegisterTableHandler
                from ui.handlers.ModernRegisterIOHandler import ModernRegisterIOHandler
                
                # 创建模拟环境
                mock_main_window = create_mock_main_window()
                register_manager = MockRegisterManager()
                
                # 创建处理器
                tree_handler = ModernRegisterTreeHandler(
                    parent=mock_main_window,
                    register_manager=register_manager
                )
                
                table_handler = ModernRegisterTableHandler(
                    parent=mock_main_window,
                    register_manager=register_manager
                )
                
                io_handler = ModernRegisterIOHandler(
                    parent=mock_main_window,
                    register_manager=register_manager
                )
                
                # 验证所有处理器都使用相同的寄存器管理器
                self.assertEqual(tree_handler.register_manager, register_manager)
                self.assertEqual(table_handler.register_manager, register_manager)
                self.assertEqual(io_handler.register_manager, register_manager)
                
                # 测试寄存器值变化的传播
                register_manager.set_register_value("0x50", 0x1234)
                
                # 验证处理器能够获取更新的值
                value = tree_handler.register_manager.get_register_value("0x50")
                self.assertEqual(value, 0x1234)
                
                result.add_detail('handlers_created', 3)
                result.add_detail('register_value_propagation', True)
                result.set_success(True)
                
        except Exception as e:
            result.set_error(str(e))
            self.logger.error(f"寄存器管理器与处理器通信测试失败: {e}")
        
        self.test_results.append(result)
        self.assertTrue(result.success)
    
    def test_event_bus_communication(self):
        """测试事件总线通信"""
        result = TestResult("event_bus_communication")
        
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            
            # 获取事件总线实例
            event_bus = RegisterUpdateBus.instance()
            
            # 测试事件发送和接收
            received_events = []
            
            def event_handler(address, value):
                received_events.append((address, value))
            
            # 连接事件处理器
            event_bus.register_updated.connect(event_handler)
            
            # 发送测试事件
            test_address = "0x50"
            test_value = 0x1234
            event_bus.emit_register_updated(test_address, test_value)
            
            # 验证事件被接收
            self.assertEqual(len(received_events), 1)
            self.assertEqual(received_events[0], (test_address, test_value))
            
            result.add_detail('events_sent', 1)
            result.add_detail('events_received', len(received_events))
            result.set_success(True)
            
        except Exception as e:
            result.set_error(str(e))
            self.logger.error(f"事件总线通信测试失败: {e}")
        
        self.test_results.append(result)
        self.assertTrue(result.success)
    
    def test_service_layer_communication(self):
        """测试服务层通信"""
        result = TestResult("service_layer_communication")
        
        try:
            with qt_application():
                from core.services.config.ConfigurationService import ConfigurationService
                from core.services.ui.WindowManagementService import WindowManagementService
                from core.services.register.RegisterOperationService import RegisterOperationService
                
                # 创建模拟主窗口
                mock_main_window = create_mock_main_window()
                
                # 创建服务
                config_service = ConfigurationService(mock_main_window)
                window_service = WindowManagementService(mock_main_window)
                register_service = RegisterOperationService(mock_main_window)
                
                # 测试服务间的协作
                # 例如：配置服务保存设置，窗口服务使用这些设置
                config_service.save_auto_write_mode(True)
                auto_write_mode = config_service.load_auto_write_mode()
                
                self.assertTrue(auto_write_mode)
                
                result.add_detail('services_created', 3)
                result.add_detail('config_service_test', 'passed')
                result.set_success(True)
                
        except Exception as e:
            result.set_error(str(e))
            self.logger.error(f"服务层通信测试失败: {e}")
        
        self.test_results.append(result)
        self.assertTrue(result.success)
    
    def test_cross_handler_data_flow(self):
        """测试跨处理器数据流"""
        result = TestResult("cross_handler_data_flow")
        
        try:
            with qt_application():
                from ui.handlers.ModernPLLHandler import ModernPLLHandler
                from ui.handlers.ModernClkOutputsHandler import ModernClkOutputsHandler
                
                # 创建模拟环境
                register_manager = MockRegisterManager()
                
                # 创建处理器
                pll_handler = ModernPLLHandler(None, register_manager)
                clk_handler = ModernClkOutputsHandler(None, register_manager)
                
                # 测试PLL设置对时钟输出的影响
                # 设置PLL预设
                pll_handler.set_pll_preset("default")
                
                # 验证寄存器值被正确设置
                pll1_pd = register_manager.get_bit_field_value("0x50", "PLL1_PD")
                self.assertEqual(pll1_pd, 0)
                
                # 测试时钟输出处理器能否读取PLL状态
                clk_status = clk_handler.get_current_status()
                self.assertIsInstance(clk_status, dict)
                
                result.add_detail('pll_preset_applied', True)
                result.add_detail('clk_status_retrieved', True)
                result.set_success(True)
                
        except Exception as e:
            result.set_error(str(e))
            self.logger.error(f"跨处理器数据流测试失败: {e}")
        
        self.test_results.append(result)
        self.assertTrue(result.success)
    
    def test_factory_pattern_integration(self):
        """测试工厂模式集成"""
        result = TestResult("factory_pattern_integration")
        
        try:
            with qt_application():
                from ui.factories.ModernToolWindowFactory import ModernToolWindowFactory
                
                # 创建模拟主窗口
                mock_main_window = create_mock_main_window()
                
                # 创建工厂
                factory = ModernToolWindowFactory(mock_main_window)
                
                # 测试不同类型窗口的创建
                window_types = ['pll_control', 'clk_outputs', 'sync_sysref']
                created_windows = []
                
                for window_type in window_types:
                    try:
                        window = factory.create_window_by_type(window_type)
                        if window:
                            created_windows.append(window_type)
                    except Exception as e:
                        self.logger.warning(f"创建窗口 {window_type} 失败: {e}")
                
                # 验证至少创建了一些窗口
                self.assertGreater(len(created_windows), 0)
                
                result.add_detail('window_types_tested', len(window_types))
                result.add_detail('windows_created', len(created_windows))
                result.add_detail('created_window_types', created_windows)
                result.set_success(True)
                
        except Exception as e:
            result.set_error(str(e))
            self.logger.error(f"工厂模式集成测试失败: {e}")
        
        self.test_results.append(result)
        self.assertTrue(result.success)

def run_tests():
    """运行模块间通信测试"""
    suite = unittest.TestLoader().loadTestsFromTestCase(TestModuleCommunication)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
