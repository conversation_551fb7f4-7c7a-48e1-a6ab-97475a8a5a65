"""
工具窗口管理器
负责管理各种工具窗口的创建、显示和关闭
"""

import os
import sys
from PyQt5.QtWidgets import QMessageBox
from utils.Log import logger


class ToolWindowManager:
    """工具窗口管理器，管理各种工具窗口"""
    
    def __init__(self, main_window):
        """初始化工具窗口管理器
        
        Args:
            main_window: 主窗口实例
        """
        self.main_window = main_window
    
    def show_set_modes_window(self):
        """显示模式设置窗口"""
        from ui.handlers.SetModesHandler import SetModesHandler
        
        def post_init(window):
            """窗口创建后的初始化回调"""
            # 可以在这里添加一些初始化逻辑
            pass
        
        # 委托给窗口管理服务
        self.main_window.window_service.show_window(
            "模式设置窗口",
            SetModesHandler,
            "set_modes_action",
            "set_modes_window",
            post_init_callback=post_init
        )
    
    def show_clkin_control_window(self):
        """显示时钟输入控制窗口"""
        from ui.handlers.ClkinControlHandler import ClkinControlHandler
        
        def post_init(window):
            """窗口创建后的初始化回调"""
            # 可以在这里添加一些初始化逻辑
            pass
        
        # 委托给窗口管理服务
        self.main_window.window_service.show_window(
            "时钟输入控制窗口",
            ClkinControlHandler,
            "clkin_control_action",
            "clkin_control_window",
            post_init_callback=post_init
        )
    
    def show_pll_control_window(self):
        """显示PLL控制窗口"""
        from ui.handlers.PLLHandler import PLLHandler
        
        def post_init(window):
            """窗口创建后的初始化回调"""
            # 可以在这里添加一些初始化逻辑
            pass
        
        # 委托给窗口管理服务
        self.main_window.window_service.show_window(
            "PLL控制窗口",
            PLLHandler,
            "pll_control_action",
            "pll_control_window",
            post_init_callback=post_init
        )
    
    def show_sync_sysref_window(self):
        """显示同步系统参考窗口"""
        # 传统处理器已移除，使用现代化版本
        from ui.handlers.ModernSyncSysRefHandler import ModernSyncSysRefHandler
        
        def post_init(window):
            """窗口创建后的初始化回调"""
            # 可以在这里添加一些初始化逻辑
            pass
        
        # 委托给窗口管理服务
        self.main_window.window_service.show_window(
            "同步系统参考窗口",
            ModernSyncSysRefHandler,
            "sync_sysref_action",
            "sync_sysref_window",
            post_init_callback=post_init
        )
    
    def show_clk_output_window(self):
        """显示时钟输出窗口"""
        # 委托给工具窗口工厂，它会处理现代化和传统处理器的选择
        return self.main_window.tool_window_factory.create_clk_output_window()
    
    def show_about_dialog(self):
        """显示关于对话框"""
        # 获取应用版本信息
        version = "1.0.0"  # 可以从配置文件或其他地方获取
        
        # 构建关于信息
        about_text = f"""
        <h3>寄存器配置工具</h3>
        <p><b>版本:</b> {version}</p>
        <p><b>描述:</b> 用于配置和管理寄存器的专业工具</p>
        <p><b>开发者:</b> 开发团队</p>
        <p><b>版权:</b> © 2024 公司名称</p>
        <hr>
        <p>本软件提供寄存器读写、批量操作、配置管理等功能。</p>
        <p>如有问题或建议，请联系技术支持。</p>
        """
        
        QMessageBox.about(self.main_window, "关于", about_text)
    
    def show_user_manual(self):
        """显示用户手册"""
        # 检查用户手册文件是否存在
        manual_path = self.main_window.resource_path('docs/user_manual.pdf')
        if os.path.exists(manual_path):
            # 使用系统默认程序打开PDF文件
            try:
                if sys.platform == 'win32':
                    os.startfile(manual_path)  # Windows特有方法
                elif sys.platform == 'darwin':
                    import subprocess
                    subprocess.call(['open', manual_path])  # macOS
                else:
                    import subprocess
                    subprocess.call(['xdg-open', manual_path])  # Linux
            except Exception as e:
                self.main_window.show_status_message(f"打开用户手册失败: {str(e)}", 5000)
                QMessageBox.warning(
                    self.main_window,
                    "打开失败",
                    f"无法打开用户手册文件:\n{str(e)}\n\n"
                    "请手动打开文件或联系技术支持。",
                    QMessageBox.Ok
                )
        else:
            # 文件不存在，显示提示信息
            QMessageBox.information(
                self.main_window,
                "用户手册",
                "用户手册文件不存在。\n\n"
                "请联系技术支持获取最新的用户手册。",
                QMessageBox.Ok
            )
    
    def show_advanced_settings(self):
        """显示高级设置对话框"""
        # 这里可以显示一个更复杂的设置对话框，允许用户配置更多选项
        QMessageBox.information(
            self.main_window, 
            "高级设置", 
            "高级设置功能正在开发中。\n"
            "未来版本将支持更多自定义选项。",
            QMessageBox.Ok
        )
    
    def handle_window_closed(self, window_name, action_name, window_attr_name, log_prefix=None):
        """处理工具窗口关闭事件
        
        Args:
            window_name: 窗口名称
            action_name: 对应的动作名称
            window_attr_name: 窗口属性名称
            log_prefix: 日志前缀
        """
        # 委托给窗口管理服务
        self.main_window.window_service.handle_window_closed(
            window_name, action_name, window_attr_name, log_prefix
        )
    
    def handle_set_modes_window_closed(self):
        """处理模式设置窗口关闭事件"""
        self.handle_window_closed("模式设置窗口", "set_modes_action", "set_modes_window")
    
    def handle_clkin_control_window_closed(self):
        """处理时钟输入控制窗口关闭事件"""
        self.handle_window_closed("时钟输入控制窗口", "clkin_control_action", "clkin_control_window")
    
    def handle_pll_control_window_closed(self):
        """处理PLL控制窗口关闭事件"""
        self.handle_window_closed("PLL控制窗口", "pll_control_action", "pll_control_window")
    
    def handle_sync_sysref_window_closed(self):
        """处理同步系统参考窗口关闭事件"""
        self.handle_window_closed("同步系统参考窗口", "sync_sysref_action", "sync_sysref_window")
    
    def handle_clk_output_window_closed(self):
        """处理时钟输出窗口关闭事件"""
        self.handle_window_closed("时钟输出窗口", "clk_output_action", "clk_output_window")
    
    def close_all_tool_windows(self):
        """关闭所有工具窗口"""
        tool_windows = [
            'set_modes_window',
            'clkin_control_window', 
            'pll_control_window',
            'sync_sysref_window',
            'clk_output_window'
        ]
        
        for window_attr in tool_windows:
            if hasattr(self.main_window, window_attr):
                window = getattr(self.main_window, window_attr)
                if window and hasattr(window, 'close'):
                    try:
                        window.close()
                        logger.debug(f"已关闭工具窗口: {window_attr}")
                    except Exception as e:
                        logger.warning(f"关闭工具窗口 {window_attr} 时出错: {str(e)}")
                        
                # 清除引用
                setattr(self.main_window, window_attr, None)
    
    def get_open_tool_windows(self):
        """获取当前打开的工具窗口列表
        
        Returns:
            list: 打开的工具窗口名称列表
        """
        open_windows = []
        tool_windows = {
            'set_modes_window': '模式设置窗口',
            'clkin_control_window': '时钟输入控制窗口',
            'pll_control_window': 'PLL控制窗口',
            'sync_sysref_window': '同步系统参考窗口',
            'clk_output_window': '时钟输出窗口'
        }
        
        for window_attr, window_name in tool_windows.items():
            if hasattr(self.main_window, window_attr):
                window = getattr(self.main_window, window_attr)
                if window and hasattr(window, 'isVisible') and window.isVisible():
                    open_windows.append(window_name)
        
        return open_windows
