"""
核心算法保护模块
包含反调试检测和二进制混淆功能
"""

import sys
import ctypes
import inspect

# 反调试检测逻辑
def anti_debug():
    """检测常见调试器存在"""
    try:
        # 检测Windows调试器
        if ctypes.windll.kernel32.IsDebuggerPresent():
            return True
            
        # 检测Python调试器
        for frame in inspect.stack():
            if frame.filename.endswith('pydevd.py'):
                return True
                
    except:
        pass
    
    return False

# 二进制混淆工具
def obfuscate_config(data):
    """对配置数据进行简单混淆"""
    return bytes([b ^ 0x55 for b in data.encode()]) if isinstance(data, str) else data