#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试批量写入功能，确保不会触发表格跳转
"""

import sys
import os
import logging
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_batch_write():
    """测试批量写入功能"""
    print("=" * 60)
    print("测试批量写入功能")
    print("=" * 60)
    
    try:
        # 创建QApplication
        app = QApplication(sys.argv)
        
        # 导入必要的模块
        from ui.windows.RegisterMainWindow import RegisterMainWindow
        from core.services.spi.spi_service_impl import SPIServiceImpl
        from core.repositories.register_repository import RegisterRepository
        from core.services.BatchOperationState import BatchOperationState
        
        print("✅ 导入成功")
        
        # 初始化服务
        spi_service = SPIServiceImpl()
        spi_service.initialize()
        register_repo = RegisterRepository(spi_service)
        
        # 创建主窗口
        main_window = RegisterMainWindow(register_repo)
        print("✅ 主窗口创建成功")
        
        # 检查批量操作状态管理器
        batch_state = BatchOperationState.instance()
        print(f"✅ 批量操作状态管理器: {type(batch_state)}")
        print(f"初始批量操作状态: {batch_state.is_in_batch_operation()}")
        
        # 测试批量操作状态设置
        print("\n开始测试批量操作状态...")
        
        # 设置批量写入状态
        batch_state.set_batch_writing(True)
        print(f"设置批量写入状态后: {batch_state.is_in_batch_operation()}")
        print(f"批量写入状态: {batch_state.is_batch_writing()}")
        
        # 测试现代化处理器的批量操作检查
        print("\n测试现代化处理器的批量操作检查...")
        
        # 创建时钟输入控制窗口
        clkin_window = main_window._show_clkin_control_window()
        if clkin_window:
            print("✅ 时钟输入控制窗口创建成功")
            
            # 检查是否有批量操作检查方法
            if hasattr(clkin_window, '_is_in_batch_operation'):
                batch_check = clkin_window._is_in_batch_operation()
                print(f"现代化处理器批量操作检查: {batch_check}")
                
                if batch_check:
                    print("✅ 现代化处理器正确检测到批量操作状态")
                else:
                    print("❌ 现代化处理器未检测到批量操作状态")
            else:
                print("❌ 现代化处理器没有 _is_in_batch_operation 方法")
            
            # 模拟在批量操作期间修改控件
            print("\n模拟在批量操作期间修改控件...")
            if hasattr(clkin_window, 'widget_register_map') and clkin_window.widget_register_map:
                first_widget = list(clkin_window.widget_register_map.keys())[0]
                widget_info = clkin_window.widget_register_map[first_widget]
                target_addr = widget_info['register_addr']
                
                print(f"测试控件: {first_widget} -> 寄存器: {target_addr}")
                
                # 获取修改前的表格状态
                if hasattr(main_window, 'table_handler'):
                    before_status = main_window.table_handler.get_current_status()
                    print(f"修改前表格状态: {before_status}")
                
                # 模拟控件修改（在批量操作期间）
                if hasattr(clkin_window, '_on_widget_changed'):
                    print(f"在批量操作期间调用 _on_widget_changed('{first_widget}', 1)")
                    clkin_window._on_widget_changed(first_widget, 1)
                    
                    # 检查修改后的表格状态
                    if hasattr(main_window, 'table_handler'):
                        after_status = main_window.table_handler.get_current_status()
                        print(f"修改后表格状态: {after_status}")
                        
                        # 检查表格是否跳转
                        if before_status['current_register'] == after_status['current_register']:
                            print(f"✅ 批量操作期间表格未跳转，保持在 {after_status['current_register']}")
                        else:
                            print(f"❌ 批量操作期间表格发生了跳转: {before_status['current_register']} -> {after_status['current_register']}")
        
        # 清除批量操作状态
        print("\n清除批量操作状态...")
        batch_state.set_batch_writing(False)
        print(f"清除后批量操作状态: {batch_state.is_in_batch_operation()}")
        
        # 测试非批量操作时的表格跳转
        print("\n测试非批量操作时的表格跳转...")
        if clkin_window and hasattr(clkin_window, 'widget_register_map') and clkin_window.widget_register_map:
            # 选择另一个控件进行测试
            widget_names = list(clkin_window.widget_register_map.keys())
            if len(widget_names) > 1:
                second_widget = widget_names[1]
                widget_info = clkin_window.widget_register_map[second_widget]
                target_addr = widget_info['register_addr']
                
                print(f"测试控件: {second_widget} -> 寄存器: {target_addr}")
                
                # 获取修改前的表格状态
                if hasattr(main_window, 'table_handler'):
                    before_status = main_window.table_handler.get_current_status()
                    print(f"修改前表格状态: {before_status}")
                
                # 模拟控件修改（非批量操作期间）
                if hasattr(clkin_window, '_on_widget_changed'):
                    print(f"在非批量操作期间调用 _on_widget_changed('{second_widget}', 1)")
                    clkin_window._on_widget_changed(second_widget, 1)
                    
                    # 检查修改后的表格状态
                    if hasattr(main_window, 'table_handler'):
                        after_status = main_window.table_handler.get_current_status()
                        print(f"修改后表格状态: {after_status}")
                        
                        # 检查表格是否跳转
                        if after_status['current_register'] == target_addr:
                            print(f"✅ 非批量操作期间表格正确跳转到 {target_addr}")
                        else:
                            print(f"❌ 非批量操作期间表格未跳转到目标寄存器: 期望 {target_addr}, 实际 {after_status['current_register']}")
        
        print("\n" + "=" * 60)
        print("测试完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_batch_write()
