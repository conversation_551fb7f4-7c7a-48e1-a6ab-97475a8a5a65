# -*- mode: python ; coding: utf-8 -*-
"""
FSJ04832 寄存器配置工具 - 文件大小优化打包配置
专门用于生成最小体积的可执行文件
"""

import os
import sys
import json
from pathlib import Path

# PyInstaller 导入
from PyInstaller.utils.hooks import collect_submodules, collect_data_files
from PyInstaller.building.build_main import Analysis, PYZ, EXE

# 读取版本信息
def get_version_info():
    """获取版本信息"""
    try:
        # 首先尝试从packaging目录的version.json读取
        version_file = Path('../config/version.json')
        if version_file.exists():
            with open(version_file, 'r', encoding='utf-8') as f:
                version_data = json.load(f)
                v = version_data.get('version', {})
                if isinstance(v, dict):
                    return f"{v.get('major', 1)}.{v.get('minor', 0)}.{v.get('patch', 0)}.{v.get('build', 0)}"
                return str(v)

        # 回退到项目根目录的version.json
        version_file = Path('version.json')
        if version_file.exists():
            with open(version_file, 'r', encoding='utf-8') as f:
                version_data = json.load(f)
                v = version_data.get('version', {})
                if isinstance(v, dict):
                    return f"{v.get('major', 1)}.{v.get('minor', 0)}.{v.get('patch', 0)}.{v.get('build', 0)}"
                return str(v)

    except Exception as e:
        print(f"读取版本信息失败: {e}")

    return '1.0.0.0'

# 获取当前版本
current_version = get_version_info()
exe_name = f'FSJ04832_RegisterTool_v{current_version}_Compact'

# 使用相对路径确保可移植性
project_root_relative = '../..'

# 最小化的数据文件包含配置（只包含绝对必需的文件）
added_files = [
    # 核心配置文件（最小集合）
    (f'{project_root_relative}/config/default.json', 'config'),
    (f'{project_root_relative}/lib/register.json', 'lib'),
    
    # 最小图标资源
    (f'{project_root_relative}/images/logo.ico', 'images'),

    # 版本文件
    ('../config/version.json', '.'),

    # 日志控制工具
    (f'{project_root_relative}/set_log_level.bat', '.')]

# 最小化的隐藏导入列表（只包含绝对必需的模块）
hiddenimports = [
    # PyQt5 核心模块（最小集合）
    'PyQt5.QtCore',
    'PyQt5.QtWidgets', 
    'PyQt5.QtGui',
    
    # 串口通信（必需）
    'serial',
    'serial.tools.list_ports',
    
    # 核心服务（最小集合）
    'core.event_bus',
    'core.services.spi.spi_service',
    'core.services.register.RegisterOperationService',
    'core.services.DIContainer',

    # 工具类（必需）
    'utils.Log',
    'utils.log_control',
    
    # 基本标准库模块
    'threading',
    'logging',
    'configparser',
    'functools',
    'collections',

    # PyInstaller运行时必需的模块
    'dis', 'email', 'email.mime', 'email.mime.text', 'email.mime.multipart',
    'os', 'sys', 'io', 'codecs', 'encodings', 'encodings.utf_8', 'encodings.cp1252']

# 大幅扩展的排除列表（最大化减小体积）
excludes = [
    # GUI库
    'tkinter', 'tkinter.ttk', 'tkinter.messagebox', 'tkinter.filedialog',
    
    # 数据分析和科学计算库
    'matplotlib', 'matplotlib.pyplot', 'matplotlib.backends', 'matplotlib.figure',
    'numpy', 'numpy.core', 'numpy.lib', 'numpy.random', 'numpy.linalg',
    'pandas', 'pandas.core', 'pandas.io', 'pandas.plotting',
    'scipy', 'scipy.sparse', 'scipy.stats', 'scipy.optimize',
    
    # 图像处理库
    'PIL', 'PIL.Image', 'PIL.ImageTk', 'Pillow',
    'cv2', 'opencv-python', 'skimage',
    
    # Web相关库
    'requests', 'urllib3', 'http.client', 'http.server', 'http.cookies',
    'flask', 'django', 'tornado', 'fastapi', 'aiohttp',
    'websocket', 'socketio',
    
    # 测试框架
    'pytest', 'unittest', 'nose', 'mock', 'doctest', 'test',
    'coverage', 'tox', 'hypothesis',
    
    # 开发工具
    'pdb', 'pydoc', 'profile', 'cProfile', 'trace', 'timeit',
    'py_compile', 'compileall', 'ast',
    
    # 网络和通信库
    'smtplib', 'poplib', 'imaplib', 'ftplib', 'telnetlib',
    'xmlrpc', 'socket', 'ssl', 'hashlib', 'hmac',
    
    # 数据库相关
    'sqlite3', 'dbm', 'shelve', 'pickle', 'dill',
    'sqlalchemy', 'pymongo', 'redis',
    
    # 并发和异步
    'multiprocessing', 'concurrent.futures', 'asyncio', 'threading',
    'queue', 'subprocess',
    
    # Excel和文档处理
    'openpyxl', 'xlrd', 'xlwt', 'xlsxwriter', 'xlutils',
    'docx', 'python-docx', 'reportlab',
    
    # 压缩和归档
    'zipfile', 'tarfile', 'gzip', 'bz2', 'lzma', 'zlib',
    
    # 加密和安全
    'cryptography', 'pycrypto', 'bcrypt', 'passlib',
    
    # 日期时间处理
    'dateutil', 'pytz', 'calendar',
    
    # 系统和平台相关
    'platform', 'ctypes', 'winreg',
    'psutil', 'wmi', 'win32api', 'win32com',
    
    # 大型开发框架
    'IPython', 'jupyter', 'notebook', 'qtconsole',
    'sympy', 'statsmodels', 'sklearn', 'tensorflow', 'torch',
    
    # 其他大型库
    'babel', 'jinja2', 'markupsafe', 'werkzeug',
    'click', 'colorama', 'tqdm', 'progressbar',
    'lxml', 'beautifulsoup4', 'html5lib',
    'pyqt5-tools', 'designer']

a = Analysis(
    [f'{project_root_relative}/main.py'],
    pathex=[project_root_relative],
    binaries=[],
    datas=added_files,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    noarchive=False,
    optimize=2  # 启用最高级别的Python优化
)

# 过滤掉不必要的文件
a.datas = [x for x in a.datas if not any(
    exclude in x[0].lower() for exclude in [
        '.md', '.txt', 'readme', 'license', 'changelog',
        '.pyc', '.pyo', '__pycache__',
        'test_', '_test.py', '.log',
        '.xlsx', '.xls', '~$'
    ]
)]

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 创建优化的可执行文件
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name=exe_name,
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,  # 移除符号信息
    upx=True,    # 启用UPX压缩
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    icon=f'{project_root_relative}/images/logo.ico',
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None
)
