#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单测试工具窗口工厂修复
验证现代化工厂初始化问题是否已解决
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_factory_fix_simple():
    """简单测试工具窗口工厂修复"""
    print("=" * 60)
    print("🔧 简单测试工具窗口工厂修复")
    print("=" * 60)
    
    try:
        # 1. 测试ToolWindowFactory的现代化工厂初始化
        print("\n1. 测试ToolWindowFactory的现代化工厂初始化...")
        
        # 创建模拟主窗口
        class MockMainWindow:
            def __init__(self):
                self.register_manager = None
                self.auto_write_mode = False
        
        mock_main_window = MockMainWindow()
        
        # 创建ToolWindowFactory
        from ui.factories.ToolWindowFactory import ToolWindowFactory
        tool_factory = ToolWindowFactory(mock_main_window)
        
        # 检查现代化工厂是否正确初始化
        if hasattr(tool_factory, 'modern_factory'):
            if tool_factory.modern_factory:
                print("   ✅ 现代化工厂实例存在")
                print(f"   ✓ 现代化工厂类型: {type(tool_factory.modern_factory).__name__}")
            else:
                print("   ❌ 现代化工厂实例为None")
                return False
        else:
            print("   ❌ ToolWindowFactory缺少modern_factory属性")
            return False
        
        # 检查use_modern_factory标志
        if hasattr(tool_factory, 'use_modern_factory'):
            if tool_factory.use_modern_factory:
                print("   ✅ 现代化工厂已启用")
            else:
                print("   ❌ 现代化工厂未启用")
                return False
        else:
            print("   ❌ ToolWindowFactory缺少use_modern_factory属性")
            return False
        
        # 2. 测试InitializationManager访问模式
        print("\n2. 测试InitializationManager访问模式...")
        
        # 模拟InitializationManager的访问方式
        if hasattr(mock_main_window, 'tool_window_factory') and hasattr(mock_main_window.tool_window_factory, 'modern_factory'):
            # 这是错误的访问方式，因为我们直接设置了tool_window_factory
            pass
        
        # 正确的访问方式
        mock_main_window.tool_window_factory = tool_factory
        
        if hasattr(mock_main_window, 'tool_window_factory') and hasattr(mock_main_window.tool_window_factory, 'modern_factory'):
            modern_factory = mock_main_window.tool_window_factory.modern_factory
            if modern_factory:
                print("   ✅ InitializationManager可以正确访问现代化工厂")
                print(f"   ✓ 访问到的现代化工厂类型: {type(modern_factory).__name__}")
            else:
                print("   ❌ InitializationManager访问的现代化工厂为None")
                return False
        else:
            print("   ❌ InitializationManager无法访问现代化工厂")
            return False
        
        # 3. 测试工厂配置
        print("\n3. 测试工厂配置...")
        
        # 检查现代化工厂的配置
        modern_configs = tool_factory.modern_factory.HANDLER_CONFIGS
        
        modern_count = 0
        total_count = len(modern_configs)
        
        for window_type, config in modern_configs.items():
            use_modern = config.get('use_modern', False)
            if use_modern:
                modern_count += 1
                print(f"   ✓ {window_type}: 使用现代化版本")
            else:
                print(f"   ⚠️  {window_type}: 使用传统版本")
        
        print(f"\n   现代化配置: {modern_count}/{total_count} ({modern_count/total_count:.1%})")
        
        if modern_count == total_count:
            print("   ✅ 所有工具窗口都配置为使用现代化版本")
        else:
            print(f"   ⚠️  还有 {total_count - modern_count} 个工具窗口使用传统版本")
        
        # 4. 测试迁移状态
        print("\n4. 测试迁移状态...")
        
        migration_status = tool_factory.get_migration_status()
        if migration_status:
            total_handlers = migration_status.get('total_handlers', 0)
            configured_modern = migration_status.get('configured_modern', 0)
            migration_progress = migration_status.get('migration_progress', 0.0)
            
            print(f"   总处理器数量: {total_handlers}")
            print(f"   现代化处理器数量: {configured_modern}")
            print(f"   迁移进度: {migration_progress:.1%}")
            
            if migration_progress >= 1.0:
                print("   ✅ 迁移完成度: 100%")
            else:
                print(f"   ⚠️  迁移完成度: {migration_progress:.1%}")
        
        # 5. 总结
        print("\n" + "=" * 60)
        print("📊 测试结果总结")
        print("=" * 60)
        print("✅ ToolWindowFactory现代化工厂初始化正常")
        print("✅ InitializationManager可以正确访问现代化工厂")
        print("✅ 工厂配置正确")
        print("✅ 迁移状态正常")
        
        print("\n🎉 工具窗口工厂修复成功！")
        print("\n📝 修复说明：")
        print("   - RegisterMainWindow现在使用ToolWindowFactory而不是直接使用ModernToolWindowFactory")
        print("   - ToolWindowFactory会自动初始化ModernToolWindowFactory作为其modern_factory属性")
        print("   - InitializationManager现在可以通过main_window.tool_window_factory.modern_factory访问现代化工厂")
        print("   - 这解决了'现代化工厂不可用'的警告问题")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_factory_fix_simple()
    sys.exit(0 if success else 1)
