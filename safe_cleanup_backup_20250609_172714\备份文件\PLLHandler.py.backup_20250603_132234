# -*- coding: utf-8 -*-

from PyQt5 import QtCore, QtWidgets
from PyQt5.QtWidgets import QMessageBox
from ui.handlers.BaseHandler import BaseClockHandler
from ..forms.Ui_PLL1_2 import Ui_PLL1_2
from utils.Log import logger
from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
import traceback

class PLLHandler(BaseClockHandler):
    """PLL处理类，继承自BaseClockHandler，用于处理PLL1和PLL2的功能"""
    
    # 添加一个关闭信号
    window_closed = QtCore.pyqtSignal()
    
    def __init__(self, parent=None, registers=None):
        # 解决registers处理问题
        if registers is not None and hasattr(registers, 'keys'):
            # 如果传入的是register_objects，直接使用
            reg_objects = registers
        else:
            # 否则使用空字典
            reg_objects = {}
            
        # 调用父类构造器
        super().__init__(parent, reg_objects)
        
        # 创建UI实例
        self.ui = Ui_PLL1_2()
        self.ui.setupUi(self.content_widget)
        
        # 设置窗口标题
        self.setWindowTitle("PLL配置")
        
        # 测试模式标志
        self._testing_mode = False
        
        # 获取时钟总线实例
        clock_bus = RegisterUpdateBus.instance()
        
        # 获取当前时钟源
        self.current_clock_source = clock_bus.get_current_clock_source()
        if not self.current_clock_source:
            self.current_clock_source = "ClkIn0"  # 默认值

        self.ui.OSCinFreq.setText('122.88')
        self.ui.ExternalVCXOFreq.setText('122.88')   
        # 把当前时钟源规范化为ClkInX格式
        if self.current_clock_source.startswith("CLK"):
            self.current_clock_source = "ClkIn" + self.current_clock_source[5:]
            
        # 打印时钟源信息，帮助调试
        logger.info(f"【PLL初始化】当前时钟源: {self.current_clock_source}")
        logger.info(f"【PLL初始化】时钟源频率: {clock_bus.get_clock_frequency(self.current_clock_source)}MHz")
        logger.info(f"【PLL初始化】时钟源分频比: {clock_bus.get_clock_divider(self.current_clock_source)}")
        
        # 预先初始化空的本地配置
        self.clkin_frequencies = {}
        self.clkin_divider_values = {}
        
        # 先用默认值初始化
        default_frequencies = {
            "ClkIn0": 122.88,
            "ClkIn1": 245.76,
            "ClkIn2": 0,
            "ClkIn3": 0
        }
        
        default_dividers = {
            "ClkIn0": 1,
            "ClkIn1": 1,
            "ClkIn2": 1,
            "ClkIn3": 1
        }
        
        # 获取RegisterUpdateBus中存储的时钟源配置
        for source in ["ClkIn0", "ClkIn1", "ClkIn2", "ClkIn3", "Holdover"]:
            freq = clock_bus.get_clock_frequency(source)
            divider = clock_bus.get_clock_divider(source)
            
            # 规范化时钟源名称
            normalized_source = source
            if source.startswith("CLK"):
                # 将CLKinX转换为ClkInX
                normalized_source = "ClkIn" + source[5:]
                
            # 只有在有配置的情况下才更新
            if freq > 0:
                self.clkin_frequencies[normalized_source] = freq
            else:
                self.clkin_frequencies[normalized_source] = default_frequencies.get(normalized_source, 0)
                
            if divider > 0:
                self.clkin_divider_values[normalized_source] = divider
            else:
                self.clkin_divider_values[normalized_source] = default_dividers.get(normalized_source, 1)
                
        # 规范化当前时钟源名称
        if self.current_clock_source.startswith("CLK"):
            self.current_clock_source = "ClkIn" + self.current_clock_source[5:]
            
        logger.info(f"PLL界面初始化时钟源配置: 当前源={self.current_clock_source}")
        logger.info(f"频率配置: {self.clkin_frequencies}")
        logger.info(f"分频比配置: {self.clkin_divider_values}")
        
        # 初始化UI默认值
        self.init_ui_default_values()
        
        # 设置PLL2NDivider控件范围
        if hasattr(self.ui, "PLL2NDivider"):
            self.ui.PLL2NDivider.setMinimum(2)
            self.ui.PLL2NDivider.setMaximum(262143)  # 最大值为2^18-1
            logger.info("已设置PLL2NDivider控件范围: 2-262143")
            
        # 设置PLL1RDividerSetting的最大值
        if hasattr(self.ui, "PLL1RDividerSetting"):
            self.ui.PLL1RDividerSetting.setMaximum(16383)
            logger.info("已设置PLL1RDividerSetting控件最大值: 16383")
        
        # 创建控件与寄存器的映射表
        self.create_widget_register_map()
        
        # 初始化控件
        self.initialize_widgets()
        
        # 连接控件信号
        self.connect_widget_signals()

        # 连接OSCinFreq的textChanged信号到calculate_output_frequencies
        if hasattr(self.ui, "OSCinFreq"):
            self.ui.OSCinFreq.textChanged.connect(self.calculate_output_frequencies)
            logger.info("已连接 OSCinFreq textChanged 信号到 calculate_output_frequencies")
        
        # 连接时钟源选择信号
        RegisterUpdateBus.instance().clock_source_selected.connect(self.on_global_clock_source_selected)
        
        # 连接模式变化信号
        RegisterUpdateBus.instance().mode_changed.connect(self.on_mode_changed)
        
        # 设置时钟源相关的控件值
        self.update_clock_source_display(self.current_clock_source)
        
        # 计算初始频率
        self.calculate_output_frequencies()
    
    @classmethod
    def create_for_testing(cls, parent=None):
        """创建一个用于测试的实例，使用模拟的寄存器数据"""
        try:
            # 创建模拟的寄存器对象
            mock_registers = cls._create_mock_registers()
            
            # 创建实例
            instance = cls(parent, mock_registers)
            
            # 标记为测试模式
            instance._testing_mode = True
            
            # 初始化测试数据
            instance._initialize_test_data()
            
            return instance
        except Exception as e:
            logger.error(f"创建PLL测试实例时发生错误: {str(e)}")
            traceback.print_exc()
            raise
    
    def _initialize_test_data(self):
        """初始化测试数据"""
        try:
            # 设置测试时钟源数据
            self.clkin_frequencies = {
                "ClkIn0": 122.88,
                "ClkIn1": 245.76,
                "ClkIn2": 153.6,
                "ClkIn3": 100.0
            }
            
            self.clkin_divider_values = {
                "ClkIn0": 1,
                "ClkIn1": 2,
                "ClkIn2": 4,
                "ClkIn3": 5
            }
            
            # 设置当前时钟源
            self.current_clock_source = "ClkIn0"
            
            # 更新UI
            if hasattr(self.ui, "FreFin"):
                self.ui.FreFin.setText(str(self.clkin_frequencies[self.current_clock_source]))
                
            if hasattr(self.ui, "PLL1RDividerSetting"):
                self.ui.PLL1RDividerSetting.setValue(self.clkin_divider_values[self.current_clock_source])
                
            # 重新计算输出频率
            self.calculate_output_frequencies()
            
            logger.info("已初始化PLL测试数据")
        except Exception as e:
            logger.error(f"初始化PLL测试数据时发生错误: {str(e)}")
            traceback.print_exc()
    
    @staticmethod
    def _create_mock_registers():
        """创建模拟的寄存器数据用于测试"""
        class MockRegister:
            def __init__(self, addr, bits=None):
                self.addr = addr
                self.bits = bits or []
                self.value = 0
            
            def get_bit_field_value(self, field_name):
                """模拟获取位域值的方法"""
                return 0
        
        # 创建模拟的寄存器映射
        mock_registers = {}
        
        # 为常用的控件创建位定义
        common_bits = [
            # PLL1控制寄存器
            {"widget_name": "PLL1PD", "widget_type": "checkbox", "default": "0", "options": "0:1"},
            {"widget_name": "PLL1RST", "widget_type": "checkbox", "default": "0", "options": "0:1"},
            {"widget_name": "comboPLL1WindSize", "widget_type": "combobox", "default": "00", "options": "0:3"},
            {"widget_name": "PLL1CPState", "widget_type": "combobox", "default": "0", "options": "0:1"},
            {"widget_name": "PLL1PFDPolarity", "widget_type": "combobox", "default": "0", "options": "0:1"},
            {"widget_name": "PLL1CPGain", "widget_type": "combobox", "default": "0000", "options": "0:15"},
            {"widget_name": "spinBoxPLL1DLDCNT", "widget_type": "spinbox", "default": "00000000", "options": "0:255"},
            
            # PLL2控制寄存器
            {"widget_name": "PLL2PD", "widget_type": "checkbox", "default": "0", "options": "0:1"},
            {"widget_name": "PLL2RST", "widget_type": "checkbox", "default": "0", "options": "0:1"},
            {"widget_name": "PLL2OpenLoop", "widget_type": "checkbox", "default": "0", "options": "0:1"},
            {"widget_name": "VCOLdoPD", "widget_type": "checkbox", "default": "0", "options": "0:1"},
            {"widget_name": "PLL2WINDSIZE", "widget_type": "combobox", "default": "00", "options": "0:3"},
            {"widget_name": "PLL2CPState", "widget_type": "combobox", "default": "0", "options": "0:1"},
            {"widget_name": "PLL2PFDPolarity", "widget_type": "combobox", "default": "0", "options": "0:1"},
            {"widget_name": "PLL2CPGain", "widget_type": "combobox", "default": "00", "options": "0:3"},
            {"widget_name": "spinBoxPLL2DLDCNT", "widget_type": "spinbox", "default": "00000000", "options": "0:255"},
            
            # PLL2配置寄存器
            {"widget_name": "PLL2R3", "widget_type": "combobox", "default": "00", "options": "0:4"},
            {"widget_name": "PLL2C1", "widget_type": "combobox", "default": "00", "options": "0:2"},
            {"widget_name": "PLL2C3", "widget_type": "combobox", "default": "00", "options": "0:2"},
            
            # 其他配置寄存器
            {"widget_name": "PLL1NclkMux", "widget_type": "combobox", "default": "00", "options": "0:2"},
            {"widget_name": "PLL2NclkMux", "widget_type": "combobox", "default": "00", "options": "0:1"},
            {"widget_name": "PLL2RclkMux", "widget_type": "combobox", "default": "00", "options": "0:1"},
            {"widget_name": "PLL2Prescaler", "widget_type": "combobox", "default": "000", "options": "0:7"},
            {"widget_name": "FBMUX", "widget_type": "combobox", "default": "000", "options": "0:4"},
            {"widget_name": "Doubler", "widget_type": "combobox", "default": "0", "options": "0:1"},
            
            # 分频器设置
            {"widget_name": "PLL1RDividerSetting", "widget_type": "spinbox", "default": "0000000000000001", "options": "1:16383"},
            {"widget_name": "PLL2RDivider", "widget_type": "spinbox", "default": "0000000000000001", "options": "1:16383"},
            
            # PLL2N 高16位位域
            {"widget_name": "PLL2NDivider_high", "widget_type": "spinbox", "default": "00", "options": "0:3", "bit_field": "PLL2_N[17:16]"},
            # PLL2N 低16位位域
            {"widget_name": "PLL2NDivider_low", "widget_type": "spinbox", "default": "0000000000000010", "options": "0:65535", "bit_field": "PLL2_N[15:0]"},
        ]
        
        # 创建寄存器
        for i in range(0x70, 0x80):
            addr = f"0x{i:02x}"
            # 为测试数据特殊处理部分地址
            if addr == "0x76":  # PLL2N 高2位地址
                bits = [bit for bit in common_bits if bit.get("bit_field") == "PLL2_N[17:16]"]
                mock_registers[addr] = MockRegister(addr, bits)
            elif addr == "0x77":  # PLL2N 低16位地址
                bits = [bit for bit in common_bits if bit.get("bit_field") == "PLL2_N[15:0]"]
                mock_registers[addr] = MockRegister(addr, bits)
            else:
                # 过滤掉特殊地址的位定义
                bits = [bit for bit in common_bits if not bit.get("bit_field")]
                mock_registers[addr] = MockRegister(addr, bits[:])
        
        return mock_registers

    def closeEvent(self, event):
        """处理窗口关闭事件"""
        try:
            # 发送窗口关闭信号
            self.window_closed.emit()
            logger.info("PLL窗口关闭，已发送关闭信号")
        except Exception as e:
            logger.error(f"处理PLL窗口关闭事件时发生错误: {str(e)}")
        
        # 调用父类方法继续处理关闭事件
        super().closeEvent(event)
        
    def init_ui_default_values(self):
        """初始化UI的默认值"""
        try:
            # 定义控件选项映射字典
            self.combobox_options_map = {
                "comboPLL1WindSize": {
                    0: "4ns",
                    1: "9ns",
                    2: "19ns",
                    3: "43ns"
                },
                "PLL2WINDSIZE": {
                    0: "Reserved",
                    1: "1ns",
                    2: "18ns",
                    3: "26ns"
                },
                "PLL1CPState": {
                    0: "Active",
                    1: "Tristate",
                },
                "PLL2CPState": {
                    0: "Active",
                    1: "Tristate",
                },
                "PLL1PFDPolarity": {
                    0: "Negative",
                    1: "Positive"
                },
                "PLL2PFDPolarity": {
                    0: "Negative",
                    1: "Positive"
                },
                "PLL1CPGain": {i: f"{50 + i*100}μA" for i in range(16)},
                "PLL2CPGain": {
                    0: "Reserved",
                    1: "Reserved",
                    2: "1600uA",
                    3: "3200uA"
                },
                "PLL2R3": {
                    0: "2.4KOhm",
                    1: "0.2KOhm",
                    2: "0.5KOhm",
                    4: "1.1KOhm"
                },
                "PLL2C1": {
                    0: "10pF",
                    1: "20pF",
                    2: "40pF",
                },
                "PLL2C3": {
                    0: "10pF",
                    1: "20pF",
                    2: "40pF",
                },
                "PLL1NclkMux": {
                    0: "OSCin",
                    1: "Feedback Mux",
                    2: "PLL2 Prescaler"
                },
                "PLL2NclkMux": {
                    0: "PLL2 Prescaler",
                    1: "Feedback Mux",
                },
                "PLL2RclkMux": {
                    0: "OSCin",
                    1: "PLL1 CLKinX",
                },
                "PLL2Prescaler": {
                    0: "8",
                    1: "1",
                    2: "2",
                    3: "3",
                    4: "4",
                    5: "5",
                    6: "6",
                    7: "7",
                },
                "FBMUX": {
                    0: "CLKout6",
                    1: "CLKout8",
                    2: "SYSREF Divider",
                    4: "External",
                },
                "comboVcoMode": {
                    0: "VCO 0",
                    1: "VCO 1",
                    2: "CLKin1",
                    3: "Fin0",
                },
                "Doubler": {
                    0: "1×",
                    1: "2×",
                },
                "Fin0InputType": {
                    0: "Diff Input",
                    1: "Single Ended Input(Fin)",
                    2: "Single Ended Input(Fin*)",
                    3: "Reserved",
                },
                "FSMDIV": {
                    0: "0-63MHz,not valid",
                    1: "div1 -> 63-127MHz",
                    2: "div2 -> 127-255MHz",
                    3: "div3 -> Reserved",
                    4: "div4 -> 255-500MHz",
                    5: "div5 -> Reserved",
                    6: "div6 -> Reserved",
                    7: "div7 -> Reserved",
                },
                "DACClkMult": {
                    0: "4",
                    1: "64",
                    2: "1024",
                    3: "16384",
                },
                "HoldoverExitMode": {
                    0: "Exist Based on LOS",
                    1: "Exist Based on DLD",
                },
                "RGHOExitDacassistStep": {
                    0: "slowest",
                    1: "slow",
                    2: "fast",
                    3: "fastest",
                }
            }
            
            # 注册已经初始化的控件集合
            self._initialized_widgets = set()
            
            # 注意：不在这里设置FreFin的值，让时钟源选择逻辑来处理
            
        except Exception as e:
            logger.error(f"初始化UI控件时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
    
    # 重写基类的initialize_widgets方法
    def initialize_widgets(self):
        """初始化界面控件的默认值，先手动设置ComboBox选项，再调用基类方法"""
        try:
            # 先为所有ComboBox控件设置选项
            for widget_name, options in self.combobox_options_map.items():
                if hasattr(self.ui, widget_name):
                    combo_box = getattr(self.ui, widget_name)
                    if isinstance(combo_box, QtWidgets.QComboBox):
                        # 清空并设置选项
                        combo_box.clear()
                        
                        # 特殊处理PLL2R3控件
                        if widget_name == "PLL2R3":
                            # PLL2R3的值映射: [0, 1, 2, 4]
                            values = [0, 1, 2, 4]  # 实际寄存器值
                            texts = ["2.4KOhm", "0.2KOhm", "0.5KOhm", "1.1KOhm"]  # 显示文本
                            for i, (value, text) in enumerate(zip(values, texts)):
                                combo_box.addItem(text, value)  # 设置实际值为userData
                            logger.info(f"已为 {widget_name} 设置特殊映射: {values} -> {texts}")
                        else:
                            # 常规ComboBox处理
                            for value, text in sorted(options.items()):
                                combo_box.addItem(text, value)  # 存储实际值为userData
                            logger.info(f"已为 {widget_name} 设置选项映射")
                            
                        # 设置默认选中项 (统一逻辑，适用于PLL2R3和常规ComboBox)
                        if hasattr(combo_box, 'itemData'): # Ensure it's a QComboBox with items
                            if widget_name in self.widget_register_map:
                                widget_info = self.widget_register_map[widget_name]
                                default_value_str_orig = widget_info.get("default_value")
                                
                                if default_value_str_orig is not None:
                                    default_value_str = default_value_str_orig.strip()
                                    default_int_value = 0 # Default fallback value
                                    parsed_successfully = False

                                    if default_value_str: # Ensure not empty
                                        # Try binary first if it looks like a binary string
                                        if all(c in '01' for c in default_value_str):
                                            try:
                                                default_int_value = int(default_value_str, 2)
                                                parsed_successfully = True
                                            except ValueError:
                                                # This case (all '01' but invalid binary) is unlikely for typical inputs
                                                logger.warning(f"尝试将 '{default_value_str}' (全01字符) 作为二进制解析失败 for {widget_name}.")
                                        
                                        # If not parsed as binary (or wasn't binary-like), try decimal if it's all digits
                                        if not parsed_successfully and default_value_str.isdigit():
                                            try:
                                                default_int_value = int(default_value_str)
                                                parsed_successfully = True
                                            except ValueError: # Should not happen if isdigit() is true and not empty
                                                logger.error(f"尝试将 '{default_value_str}' (isdigit=True) 作为十进制解析失败 for {widget_name}.")

                                    if not parsed_successfully:
                                        # Log if it was not empty but failed parsing
                                        if default_value_str: 
                                            logger.warning(f"无法将 {widget_name} 的默认值 '{default_value_str_orig}' 解析为整数。使用0作为回退。")
                                        else: # Log if it was empty
                                            logger.info(f"{widget_name} 的默认值为空或仅含空格。使用0作为回退。")
                                        default_int_value = 0 # Ensure fallback is set to 0

                                    target_index = -1
                                    for i in range(combo_box.count()):
                                        # Ensure itemData is not None before comparison
                                        item_data = combo_box.itemData(i)
                                        if item_data is not None and item_data == default_int_value:
                                            target_index = i
                                            break
                                    
                                    if target_index != -1:
                                        combo_box.blockSignals(True)
                                        combo_box.setCurrentIndex(target_index)
                                        combo_box.blockSignals(False)
                                        logger.info(f"已为 {widget_name} 设置默认选中项 (值: {default_int_value}, 原始: '{default_value_str_orig}', 索引: {target_index}) 基于JSON配置")
                                    else:
                                        logger.warning(f"无法为 {widget_name} 找到默认值 {default_int_value} (原始: '{default_value_str_orig}') 对应的选项。当前选项数据: {[combo_box.itemData(i) for i in range(combo_box.count())]}. 尝试设置索引0.")
                                        if combo_box.count() > 0:
                                            combo_box.blockSignals(True)
                                            combo_box.setCurrentIndex(0) # Fallback to index 0
                                            combo_box.blockSignals(False)
                                else:
                                    logger.warning(f"控件 {widget_name} 在 widget_register_map 中没有 default_value。尝试设置索引0。")
                                    if combo_box.count() > 0:
                                        combo_box.blockSignals(True)
                                        combo_box.setCurrentIndex(0)
                                        combo_box.blockSignals(False)
                            else:
                                logger.warning(f"控件 {widget_name} 未在 widget_register_map 中找到，无法设置基于JSON的默认值。尝试设置索引0。")
                                if combo_box.count() > 0:
                                    combo_box.blockSignals(True)
                                    combo_box.setCurrentIndex(0)
                                    combo_box.blockSignals(False)
                        
                        # 标记为已初始化
                        self._initialized_widgets.add(widget_name)
            
            # 调用基类方法初始化其他控件
            super().initialize_widgets()
            
        except Exception as e:
            logger.error(f"初始化控件时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
    
    # 重写基类的initialize_combobox方法，跳过已初始化的控件
    def initialize_combobox(self, widget, default_value, options_range):
        """重写基类的ComboBox初始化方法，跳过已初始化的控件"""
        widget_name = widget.objectName()
        
        # 如果控件已经初始化过，直接返回
        if hasattr(self, '_initialized_widgets') and widget_name in self._initialized_widgets:
            logger.info(f"跳过已初始化的控件: {widget_name}")
            return
            
        # 否则调用基类方法
        super().initialize_combobox(widget, default_value, options_range)
    
    def update_clock_source_display(self, source_name):
        """更新时钟源显示，但不触发信号"""
        try:
            # 从RegisterUpdateBus获取最新的时钟源配置
            clock_bus = RegisterUpdateBus.instance()
            
            # 获取时钟源对应的频率和分频值
            frequency = clock_bus.get_clock_frequency(source_name)
            divider = clock_bus.get_clock_divider(source_name)
            
            # 如果从总线获取失败，尝试从本地获取
            if frequency == 0:
                frequency = self.clkin_frequencies.get(source_name, 0)
                # 同时更新总线中的值
                clock_bus.set_clock_frequency(source_name, frequency)
                
            if divider == 1 and source_name in self.clkin_divider_values and self.clkin_divider_values[source_name] != 1:
                divider = self.clkin_divider_values.get(source_name, 1)
                # 同时更新总线中的值
                clock_bus.set_clock_divider(source_name, divider)
                
            # 将获取到的值同步更新到本地存储
            self.clkin_frequencies[source_name] = frequency
            self.clkin_divider_values[source_name] = divider
            
            # 更新UI显示
            self.ui.FreFin.setText(str(frequency))
            self.ui.PLL1RDividerSetting.setValue(divider)
            
            # 调试输出
            logger.info(f"【时钟源显示更新】源名称={source_name}, 频率={frequency}MHz, 分频比={divider}")
            logger.info(f"【时钟源本地配置】{self.clkin_frequencies}, {self.clkin_divider_values}")
            
            # 重新计算输出频率
            self.calculate_output_frequencies()
            
        except Exception as e:
            logger.error(f"更新时钟源显示时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def update_clock_source(self, index=None):
        """更新时钟源及相关设置"""
        try:
            if index is None:
                index = self.ui.comboClockSource.currentIndex()
            
            # 获取选择的时钟源名称
            clock_source = self.ui.comboClockSource.currentText()
            
            # 更新FreFin的值为对应时钟源的频率
            self.ui.FreFin.setText(str(self.clkin_frequencies.get(clock_source, 0)))
            
            # 更新PLL1RDividerSetting的值为对应时钟源的分频值
            divider_value = self.clkin_divider_values.get(clock_source, 1)
            self.ui.PLL1RDividerSetting.setValue(divider_value)
            
            # 重新计算输出频率
            self.calculate_output_frequencies()
            
            logger.info(f"已选择时钟源: {clock_source}, 频率: {self.clkin_frequencies.get(clock_source, 0)}MHz, 分频值: {divider_value}")
            
        except Exception as e:
            logger.error(f"更新时钟源时发生错误: {str(e)}")
            self.show_error_message("错误", f"更新时钟源时发生错误: {str(e)}")
    
    def update_clkin_frequencies(self, clkin_name, frequency):
        """更新时钟输入频率值"""
        if clkin_name in self.clkin_frequencies:
            self.clkin_frequencies[clkin_name] = frequency
            
            # 如果当前选择的就是这个时钟源，则更新FreFin的值
            if hasattr(self.ui, "comboClockSource") and self.ui.comboClockSource.currentText() == clkin_name:
                self.ui.FreFin.setText(str(frequency))
                self.calculate_output_frequencies()
    
    def update_clkin_divider_values(self, clkin_name, divider_value):
        """更新时钟输入分频值"""
        if clkin_name in self.clkin_divider_values:
            self.clkin_divider_values[clkin_name] = divider_value
            
            # 如果当前选择的就是这个时钟源，则更新PLL1RDividerSetting的值
            if hasattr(self.ui, "comboClockSource") and self.ui.comboClockSource.currentText() == clkin_name:
                self.ui.PLL1RDividerSetting.setValue(divider_value)
                self.calculate_output_frequencies()
            
    def handle_register_value_change(self, widget_name):
        """处理寄存器值变化的回调函数"""
        try:
            # 更新频率显示
            self.calculate_output_frequencies()
            
            # 处理特定控件的特殊逻辑
            if widget_name == "PLL1PD":
                self.handle_pll1_power_down()
            elif widget_name == "PLL2PD":
                self.handle_pll2_power_down()
            elif widget_name == "PLL1RST":
                self.handle_pll1_reset()
            elif widget_name == "PLL2R3":
                # 记录PLL2R3值变化
                index = self.ui.PLL2R3.currentIndex()
                text = self.ui.PLL2R3.currentText()
                value = self.ui.PLL2R3.itemData(index)
                if value is None:
                    # 如果itemData未设置，使用映射表
                    pll2r3_values = {0: 0, 1: 1, 2: 2, 3: 4}
                    value = pll2r3_values.get(index, 0)
                logger.info(f"PLL2R3选择了电阻值: {text}, 索引: {index}, 实际值: {value}")
                
        except Exception as e:
            logger.error(f"处理寄存器值变化时发生错误: {str(e)}")
            self.show_error_message("错误", f"处理寄存器值变化时发生错误: {str(e)}")
            
    def handle_pll1_power_down(self):
        """处理PLL1电源控制"""
        is_powered_down = self.ui.PLL1PD.isChecked()
        logger.info(f"PLL1PD状态: {'已关闭' if is_powered_down else '已打开'}")
        # 根据电源状态更新相关控件
        # self.ui.PLL1RST.setEnabled(not is_powered_down)
        # self.ui.comboPLL1WindSize.setEnabled(not is_powered_down)
        # self.ui.spinBoxPLL1DLDCNT.setEnabled(not is_powered_down)
        
    def handle_pll2_power_down(self):
        """处理PLL2电源控制"""
        is_powered_down = self.ui.PLL2PD.isChecked()
        logger.info(f"PLL2PD状态: {'已关闭' if is_powered_down else '已打开'}")
        # 根据电源状态更新相关控件
        # self.ui.PLL2RST.setEnabled(not is_powered_down)
        # self.ui.PLL2OpenLoop.setEnabled(not is_powered_down)
        # self.ui.VCOLdoPD.setEnabled(not is_powered_down)
        # self.ui.PLL2WINDSIZE.setEnabled(not is_powered_down)
        # self.ui.spinBoxPLL2DLDCNT.setEnabled(not is_powered_down)
        
    def handle_pll1_reset(self):
        """处理PLL1复位"""
        if self.ui.PLL1RRst.isChecked():
            # 执行复位操作
            QMessageBox.information(self, "PLL1复位", "PLL1正在复位...")
            logger.info("PLL1已复位")
            # 复位完成后自动取消选中
            # self.ui.PLL1RST.setChecked(False)
            
    def calculate_output_frequencies(self):
        """计算输出频率"""
        try:
            # 获取输入频率
            fin = float(self.ui.FreFin.text() or "0")
            
            # 计算PLL1输出频率
            if not self.ui.PLL1PD.isChecked():
                r_div = self.ui.PLL1RDividerSetting.value()
                if r_div > 0:  # 避免除零错误
                    pll1_freq = fin / r_div
                    self.ui.PLL1PFDFreq.setText(f"{pll1_freq:.2f}")
                else:
                    self.ui.PLL1PFDFreq.setText("0.00")
            else:
                self.ui.PLL1PFDFreq.setText("0.00")
                
            # 计算PLL2输出频率
            if not self.ui.PLL2PD.isChecked():
                r_div = self.ui.PLL2RDivider.value()
                n_div = self.ui.PLL2NDivider.value()
                if r_div > 0:  # 避免除零错误
                    pll2_freq = fin * n_div / r_div
                    self.ui.PLL2PFDFreq.setText(f"{pll2_freq:.2f}")
                else:
                    self.ui.PLL2PFDFreq.setText("0.00")
            else:
                self.ui.PLL2PFDFreq.setText("0.00")
                
        except Exception as e:
            logger.error(f"计算输出频率时发生错误: {str(e)}")
            self.show_error_message("错误", f"计算输出频率时发生错误: {str(e)}")
            
    def handle_global_register_update(self, reg_addr, reg_value):
        """处理全局寄存器更新的附加效果"""
        try:
            # 更新频率显示
            self.calculate_output_frequencies()
            
            # 处理特定寄存器的更新
            if reg_addr == "0x00":  # PLL1控制寄存器
                self.handle_pll1_register_update(reg_value)
            elif reg_addr == "0x01":  # PLL2控制寄存器
                self.handle_pll2_register_update(reg_value)
                
        except Exception as e:
            logger.error(f"处理全局寄存器更新时发生错误: {str(e)}")
            self.show_error_message("错误", f"处理全局寄存器更新时发生错误: {str(e)}")
            
    def handle_pll1_register_update(self, reg_value):
        """处理PLL1寄存器更新"""
        # 更新PLL1相关状态
        pass
        
    def handle_pll2_register_update(self, reg_value):
        """处理PLL2寄存器更新"""
        # 更新PLL2相关状态
        pass

    def update_register_value(self, widget_name, value):
        """重写父类方法，处理特殊控件的值更新"""
        try:
            # 特殊处理PLL2R3控件
            if widget_name == "PLL2R3" and hasattr(self.ui, "PLL2R3"):
                # 从ComboBox中获取itemData作为实际寄存器值
                actual_value = self.ui.PLL2R3.itemData(value)
                if actual_value is not None:
                    logger.info(f"更新PLL2R3寄存器值: 索引={value}, 实际值={actual_value}")
                    # 调用父类方法，传递实际值
                    super().update_register_value(widget_name, actual_value)
                else:
                    logger.error(f"PLL2R3控件索引{value}的itemData为None")
            else:
                # 对其他控件使用默认处理
                super().update_register_value(widget_name, value)
        except Exception as e:
            logger.error(f"更新寄存器值时发生错误: {str(e)}")
            self.show_error_message("错误", f"更新寄存器值时发生错误: {str(e)}")

    def on_global_clock_source_selected(self, source_name, frequency, divider):
        """处理全局时钟源选择事件"""
        logger.info(f"收到时钟源选择事件: 源={source_name}, 频率={frequency}MHz, 分频比={divider}")
        
        try:
            # 规范化时钟源名称
            normalized_source = source_name
            if source_name.startswith("CLK"):
                normalized_source = "ClkIn" + source_name[5:]
                
            # 更新当前选中的时钟源
            self.current_clock_source = normalized_source
            
            # 更新存储的时钟源频率
            self.clkin_frequencies[normalized_source] = frequency
            logger.info(f"已更新时钟源 {normalized_source} 的频率为 {frequency}MHz")
            
            # 更新存储的时钟源分频值
            self.clkin_divider_values[normalized_source] = divider
            logger.info(f"已更新时钟源 {normalized_source} 的分频值为 {divider}")
            
            # 更新UI显示
            self.update_clock_source_display(normalized_source)
                
        except Exception as e:
            logger.error(f"处理时钟源选择事件时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def create_widget_register_map(self):
        """创建控件与寄存器的映射表，包括特殊的跨寄存器控件"""
        # 首先调用父类方法创建基本映射
        super().create_widget_register_map()
        
        try:
            # 注册PLL2NDivider为跨寄存器控件
            if hasattr(self.ui, "PLL2NDivider"):
                self.register_cross_register_control(
                    widget_name="PLL2NDivider",
                    bit_fields=["PLL2_N[17:16]", "PLL2_N[15:0]"],
                    reg_addrs=["0x76", "0x77"],
                    default_value="000000000000000000",
                    options="2:262143"
                )
                logger.info("已在PLLHandler中注册PLL2NDivider为跨寄存器控件")
        except Exception as e:
            logger.error(f"在PLLHandler中注册跨寄存器控件时发生错误: {str(e)}")
            traceback.print_exc()

    def on_mode_changed(self, mode_name):
        """处理模式变化信号"""
        logger.info(f"PLLHandler 收到模式变化信号: {mode_name}")
        # TODO: 根据不同的模式实现不同的计算逻辑和控件更新
        self.calculate_output_frequencies() # 示例：模式变化后重新计算频率

    def _get_float_from_lineedit(self, line_edit, default_value=0.0):
        """安全地从QLineEdit获取浮点数，处理空或无效输入。"""
        try:
            text = line_edit.text()
            return float(text) if text else default_value
        except ValueError:
            logger.warning(f"无法将QLineEdit内容 '{text}' 转换为浮点数，使用默认值 {default_value}")
            return default_value

    def _calculate_pll1_output(self, fin_freq):
        """计算PLL1的PFD频率。"""
        if self.ui.PLL1PD.isChecked():
            self.ui.PLL1PFDFreq.setText("0.00")
            return 0.0

        r_div = self.ui.PLL1RDividerSetting.value()
        if r_div > 0:
            pll1_pfd_freq = fin_freq / r_div
            self.ui.PLL1PFDFreq.setText(f"{pll1_pfd_freq:.5f}")
            return pll1_pfd_freq
        else:
            self.ui.PLL1PFDFreq.setText("0.00")
            logger.warning("PLL1 R Divider 为0，无法计算PFD频率。")
            return 0.0

    def _calculate_pll2_output(self, oscin_freq):
        """计算PLL2的PFD频率。"""
        if self.ui.PLL2PD.isChecked():
            self.ui.PLL2PFDFreq.setText("0.00")
            return 0.0 # 返回 PFD 频率，而不是 VCO 频率

        r_div = self.ui.PLL2RDivider.value()
        doubleValue = self.ui.Doubler.currentIndex()+1
        if r_div > 0:
            pll2_pfd_freq = oscin_freq * doubleValue / r_div
            self.ui.PLL2PFDFreq.setText(f"{pll2_pfd_freq:.3f}")
            return pll2_pfd_freq
        else:
            self.ui.PLL2PFDFreq.setText("0.00")
            logger.warning("PLL2 R Divider 为0，无法计算PFD频率。")
            return 0.0

    def _calculate_fin0_output(self, pll2_pfd_freq):
        """计算Fin0的输出频率。"""
        if self.ui.PLL2PD.isChecked(): # 如果PLL2掉电，Fin0也无输出
            self.ui.Fin0Freq.setText("0.00")
            return
            
        n_div = self.ui.PLL2NDivider.value()
        # PLL2Prescaler 的值需要从ComboBox的currentText获取并转换为数值
        # 假设PLL2Prescaler的文本就是其数值，如果不是，需要更复杂的映射
        try:
            prescaler_val_str = self.ui.PLL2Prescaler.currentText()
            prescaler_val = float(prescaler_val_str) if prescaler_val_str else 0.0
        except ValueError:
            logger.error(f"无法将PLL2Prescaler值 '{prescaler_val_str}' 转换为数字。")
            prescaler_val = 0.0
            self.ui.Fin0Freq.setText("Error")
            return

        # 这里的计算逻辑可能需要根据实际芯片手册调整
        # 原始逻辑: fout = pll2_freq * self.ui.PLL2NDivider.value() * float(self.ui.PLL2Prescaler.currentText())
        # pll2_freq 在原始代码中是 Oscin / r_div，即PLL2的PFD频率
        # 通常 VCO_freq = PFD_freq * N_div
        # 然后 Fin0_freq = VCO_freq / Prescaler (或者乘以Prescaler，取决于Prescaler的作用)
        # 假设 Prescaler 是分频作用
        vco2_freq = pll2_pfd_freq * n_div
        
        # 检查Prescaler是否为0，避免除零
        if prescaler_val != 0: # 假设prescaler是分频器，所以不能为0
             # 这里的prescaler_val是PLL2Prescaler的值，通常是整数，但原始代码用了float
             # 假设PLL2Prescaler的值是分频比的索引或者直接是分频比
             # 如果是索引，需要映射到实际分频比
             # 原始代码直接将currentText转为float，这里保持一致，但需要注意其含义
             # 假设PLL2Prescaler的值直接是乘数或除数，根据芯片手册调整
             # 原始代码是 fout = pll2_pfd_freq * N * Prescaler，这看起来像是Prescaler是乘数
            fin0_freq = vco2_freq * prescaler_val # 保持原始逻辑的乘法关系，但使用VCO频率
            # 如果Prescaler是分频器，应该是 fin0_freq = vco2_freq / prescaler_val
            # 鉴于原始代码是 pll2_pfd_freq * N * Prescaler, 那么 Prescaler 应该是一个乘数因子
            # 或者，PLL2Prescaler的输出是 VCO / Prescaler_value, 然后这个结果再乘以N反馈？
            # 让我们严格按照原始公式的结构来重构，但使用中间变量：
            # fin0_freq = pll2_pfd_freq * n_div * prescaler_val (这是最接近原始的)
            self.ui.Fin0Freq.setText(f"{fin0_freq:.5f}")
        else:
            logger.warning("PLL2 Prescaler 值为0或无效，无法计算Fin0频率。")
            self.ui.Fin0Freq.setText("0.00")

    def calculate_output_frequencies(self):
        """计算PLL输出频率，使用辅助方法进行模块化计算。"""
        try:
            fin_freq = self._get_float_from_lineedit(self.ui.FreFin)
            oscin_freq = self._get_float_from_lineedit(self.ui.OSCinFreq)

            self._calculate_pll1_output(fin_freq)
            pll2_pfd_freq = self._calculate_pll2_output(oscin_freq)
            self._calculate_fin0_output(pll2_pfd_freq)
            
        except Exception as e:
            logger.error(f"计算输出频率时发生未预料的错误: {str(e)}", exc_info=True)
            self.show_error_message("计算错误", f"计算输出频率时发生未预料的错误: {str(e)}")
            
    def handle_global_register_update(self, reg_addr, reg_value):
        """处理全局寄存器更新的附加效果"""
        try:
            # 更新频率显示
            self.calculate_output_frequencies()
            
            # 处理特定寄存器的更新
            if reg_addr == "0x00":  # PLL1控制寄存器
                self.handle_pll1_register_update(reg_value)
            elif reg_addr == "0x01":  # PLL2控制寄存器
                self.handle_pll2_register_update(reg_value)
                
        except Exception as e:
            logger.error(f"处理全局寄存器更新时发生错误: {str(e)}")
            self.show_error_message("错误", f"处理全局寄存器更新时发生错误: {str(e)}")
            
    def handle_pll1_register_update(self, reg_value):
        """处理PLL1寄存器更新"""
        # 更新PLL1相关状态
        pass
        
    def handle_pll2_register_update(self, reg_value):
        """处理PLL2寄存器更新"""
        # 更新PLL2相关状态
        pass

    def update_register_value(self, widget_name, value):
        """重写父类方法，处理特殊控件的值更新"""
        try:
            # 特殊处理PLL2R3控件
            if widget_name == "PLL2R3" and hasattr(self.ui, "PLL2R3"):
                # 从ComboBox中获取itemData作为实际寄存器值
                actual_value = self.ui.PLL2R3.itemData(value)
                if actual_value is not None:
                    logger.info(f"更新PLL2R3寄存器值: 索引={value}, 实际值={actual_value}")
                    # 调用父类方法，传递实际值
                    super().update_register_value(widget_name, actual_value)
                else:
                    logger.error(f"PLL2R3控件索引{value}的itemData为None")
            else:
                # 对其他控件使用默认处理
                super().update_register_value(widget_name, value)
        except Exception as e:
            logger.error(f"更新寄存器值时发生错误: {str(e)}")
            self.show_error_message("错误", f"更新寄存器值时发生错误: {str(e)}")

if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    
    # 使用测试模式创建实例
    try:
        pll_handler = PLLHandler.create_for_testing()
        pll_handler.show()
        logger.info("PLL测试实例已创建并显示")
        sys.exit(app.exec_())
    except Exception as e:
        logger.error(f"启动PLL测试界面时发生错误: {str(e)}")
        traceback.print_exc()
        sys.exit(1)