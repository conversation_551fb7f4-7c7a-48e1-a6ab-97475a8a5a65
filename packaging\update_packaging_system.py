#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FSJ04832 打包系统全面优化更新脚本
整合所有优化功能：文件包含/排除、安全保护、文件大小优化、版本管理策略
"""

import os
import sys
import json
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List

class PackagingSystemUpdater:
    """打包系统更新器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.packaging_root = Path(__file__).parent
        self.backup_dir = self.packaging_root / 'backup' / datetime.now().strftime("%Y%m%d_%H%M%S")
        self.update_report = {
            'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            'updates': [],
            'errors': [],
            'warnings': []
        }
    
    def run_full_update(self) -> bool:
        """运行完整的打包系统更新"""
        print("🚀 开始FSJ04832打包系统全面优化...")
        print("=" * 60)
        
        try:
            # 1. 创建备份
            self._create_backup()
            
            # 2. 更新打包配置
            self._update_packaging_config()
            
            # 3. 更新版本管理
            self._update_version_management()
            
            # 4. 创建新的启动器
            self._create_optimized_launchers()
            
            # 5. 生成更新报告
            self._generate_update_report()
            
            print("\n✅ 打包系统优化完成！")
            print(f"📁 备份位置: {self.backup_dir}")
            print(f"📊 更新报告: {self.packaging_root / 'update_report.json'}")
            
            return True
            
        except Exception as e:
            print(f"\n❌ 更新失败: {e}")
            self.update_report['errors'].append(str(e))
            return False
    
    def _create_backup(self):
        """创建配置文件备份"""
        print("📦 创建配置备份...")
        
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # 备份关键配置文件
        backup_files = [
            'config/packaging_config.json',
            'config/version.json',
            'scripts/build.spec',
            'scripts/build_secure.spec'
        ]
        
        for file_path in backup_files:
            source = self.packaging_root / file_path
            if source.exists():
                target = self.backup_dir / file_path
                target.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(source, target)
                print(f"  ✓ 备份: {file_path}")
        
        self.update_report['updates'].append("创建配置文件备份")
    
    def _update_packaging_config(self):
        """更新打包配置"""
        print("⚙️ 更新打包配置...")
        
        config_file = self.packaging_root / 'config' / 'packaging_config.json'
        
        # 加载现有配置
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
        else:
            config = {}
        
        # 更新配置
        updated_config = {
            "packaging_info": {
                "name": "FSJ04832 寄存器配置工具打包系统",
                "description": "优化的版本管理和打包解决方案",
                "version": "2.0.0",
                "author": "开发团队",
                "created_date": "2025-06-04",
                "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            },
            "build_config": {
                "default_version_type": "build",
                "output_base_dir": "../releases",
                "spec_files": {
                    "standard": "scripts/build.spec",
                    "secure": "scripts/build_secure.spec",
                    "optimized": "scripts/build_optimized.spec"
                },
                "main_script": "../main.py",
                "app_name": "FSJ04832 寄存器配置工具",
                "exe_base_name": "FSJConfigTool"
            },
            "optimization": {
                "file_exclusions": {
                    "excel_files": ["*.xlsx", "*.xls", "*.xlsm"],
                    "temp_files": ["~$*", "*.tmp", "*.temp"],
                    "dev_files": ["app.json.template", "local.json", "migration.json"],
                    "test_files": ["test_*", "*_test.py", "*.log", "*.log.*"],
                    "docs": ["*.md", "*.txt", "README*"],
                    "cache": ["__pycache__", "*.pyc", "*.pyo"],
                    "backup": ["*.bak", "*.backup", "*.old"]
                },
                "resource_optimization": {
                    "enabled": True,
                    "image_compression": True,
                    "config_minification": True
                },
                "size_optimization": {
                    "exclude_large_modules": True,
                    "strip_debug_info": True,
                    "upx_compression": True
                }
            },
            "security": {
                "code_protection": {
                    "bytecode_encryption": True,
                    "anti_debug": True,
                    "runtime_verification": True
                },
                "file_protection": {
                    "single_file_mode": True,
                    "config_obfuscation": True,
                    "strip_symbols": True
                }
            },
            "version_management": {
                "enhanced_versioning": True,
                "semantic_versioning": True,
                "build_tracking": True,
                "git_integration": True,
                "release_notes": True
            }
        }
        
        # 合并现有配置
        def merge_configs(base: dict, update: dict) -> dict:
            for key, value in update.items():
                if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                    base[key] = merge_configs(base[key], value)
                else:
                    base[key] = value
            return base
        
        final_config = merge_configs(config, updated_config)
        
        # 保存更新的配置
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(final_config, f, indent=2, ensure_ascii=False)
        
        print("  ✓ 打包配置已更新")
        self.update_report['updates'].append("更新打包配置文件")
    
    def _update_version_management(self):
        """更新版本管理"""
        print("📋 更新版本管理...")
        
        # 导入增强版本管理器
        sys.path.insert(0, str(self.packaging_root / 'tools'))
        from enhanced_version_manager import EnhancedVersionManager
        
        # 初始化版本管理器
        version_manager = EnhancedVersionManager()
        
        # 验证版本数据
        is_valid, errors = version_manager.validate_version_data()
        if not is_valid:
            for error in errors:
                self.update_report['warnings'].append(f"版本数据验证: {error}")
        
        # 导出版本信息
        version_info_file = self.packaging_root / 'version_info.json'
        if version_manager.export_version_info(str(version_info_file)):
            print("  ✓ 版本信息已导出")
            self.update_report['updates'].append("导出版本信息文件")
        
        print("  ✓ 版本管理系统已更新")
        self.update_report['updates'].append("更新版本管理系统")
    
    def _create_optimized_launchers(self):
        """创建优化的启动器"""
        print("🚀 创建优化启动器...")
        
        launchers_dir = self.packaging_root / 'launchers'
        
        # 创建优化打包启动器
        optimized_launcher = launchers_dir / '优化打包工具.bat'
        launcher_content = '''@echo off
chcp 65001 >nul
title FSJ04832 优化打包工具

echo.
echo ================================================================
echo                    FSJ04832 优化打包工具 v2.0
echo ================================================================
echo.
echo 🎯 此工具提供四种优化的打包方式：
echo    ✅ 标准打包 - 平衡功能和大小
echo    ✅ 安全打包 - 最大化代码保护
echo    ✅ 紧凑打包 - 最小化文件大小
echo    ✅ 便携打包 - 目录模式，便于调试
echo.

:MENU
echo 请选择打包方式：
echo.
echo [1] 标准打包 (推荐)
echo [2] 安全打包 (客户发布)
echo [3] 紧凑打包 (最小体积)
echo [4] 便携打包 (开发调试)
echo [5] 查看版本信息
echo [0] 退出
echo.

set /p choice=请输入选择 (0-5): 

if "%choice%"=="1" (
    echo.
    echo 🔨 开始标准打包...
    cd /d "%~dp0.."
    python package.py build patch
    goto END
)

if "%choice%"=="2" (
    echo.
    echo 🔒 开始安全打包...
    cd /d "%~dp0.."
    python package.py secure patch
    goto END
)

if "%choice%"=="3" (
    echo.
    echo 📦 开始紧凑打包...
    cd /d "%~dp0.."
    python package.py optimized patch
    goto END
)

if "%choice%"=="4" (
    echo.
    echo 🛠️ 开始便携打包...
    cd /d "%~dp0.."
    python package.py portable patch
    goto END
)

if "%choice%"=="5" (
    echo.
    echo 📋 版本信息...
    cd /d "%~dp0.."
    python -c "from tools.enhanced_version_manager import EnhancedVersionManager; vm=EnhancedVersionManager(); print(f'版本: {vm.get_version_string()}'); print(f'构建: {vm.get_build_date()}'); print(f'类型: {vm.get_build_type()}')"
    goto END
)

if "%choice%"=="0" (
    echo.
    echo 👋 再见！
    goto EXIT
)

echo.
echo ❌ 无效选择，请重新输入
echo.
goto MENU

:END
echo.
echo 按任意键返回菜单...
pause >nul
cls
goto MENU

:EXIT
pause
'''
        
        with open(optimized_launcher, 'w', encoding='utf-8') as f:
            f.write(launcher_content)
        
        print("  ✓ 优化启动器已创建")
        self.update_report['updates'].append("创建优化启动器")
    
    def _generate_update_report(self):
        """生成更新报告"""
        print("📊 生成更新报告...")
        
        report_file = self.packaging_root / 'update_report.json'
        
        # 添加总结信息
        self.update_report['summary'] = {
            'total_updates': len(self.update_report['updates']),
            'total_errors': len(self.update_report['errors']),
            'total_warnings': len(self.update_report['warnings']),
            'success': len(self.update_report['errors']) == 0
        }
        
        # 保存报告
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.update_report, f, indent=2, ensure_ascii=False)
        
        print("  ✓ 更新报告已生成")

def main():
    """主函数"""
    updater = PackagingSystemUpdater()
    success = updater.run_full_update()
    
    if success:
        print("\n🎉 FSJ04832打包系统优化完成！")
        print("\n📋 优化内容:")
        print("  ✅ 文件包含/排除列表优化")
        print("  ✅ 安全保护措施增强")
        print("  ✅ 文件大小优化")
        print("  ✅ 版本管理策略改进")
        print("\n🚀 现在可以使用新的优化启动器进行打包！")
    else:
        print("\n❌ 优化过程中出现错误，请检查更新报告")
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
