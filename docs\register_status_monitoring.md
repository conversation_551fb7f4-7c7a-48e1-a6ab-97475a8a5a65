# 寄存器状态监控功能实现方案

## 概述

本文档描述了寄存器状态监控功能的实现方案，该功能可以每5秒自动读取指定的状态寄存器并更新UI控件状态，确保用户界面实时反映硬件状态。

## 功能需求

### 监控的寄存器控件

需要监控以下寄存器控件的状态：

- `RB_PLL1_LD_LOST` - PLL1锁定丢失状态
- `RB_PLL1_LD` - PLL1锁定状态  
- `RB_PLL2_LD_LOST` - PLL2锁定丢失状态
- `RB_PLL2_LD` - PLL2锁定状态
- `RB_CLKin2_SEL` - 时钟输入2选择状态
- `RB_CLKin1_SEL` - 时钟输入1选择状态
- `RB_CLKin0_SEL` - 时钟输入0选择状态
- `RB_CLKin2_LOS` - 时钟输入2信号丢失状态
- `RB_CLKin1_LOS` - 时钟输入1信号丢失状态
- `RB_CLKin0_LOS` - 时钟输入0信号丢失状态
- `RB_DAC_VALUE[9:0]` - DAC值
- `RB_HOLDOVER` - 保持状态
- `RB_DAC_RAIL` - DAC轨道状态
- `RB_DAC_HIGH` - DAC高电平状态
- `RB_DAC_LOW` - DAC低电平状态
- `RB_DAC_LOCKED` - DAC锁定状态

### 功能要求

1. **定时读取**：每5秒读取一次状态寄存器
2. **硬件模式限制**：只在硬件模式下启动监控，模拟模式下停止
3. **UI同步**：读取到的状态要同步更新到寄存器表格和相关控件
4. **自动启停**：根据模式切换自动启动或停止监控

## 技术实现方案

### 1. 寄存器地址映射

根据register.json配置文件，状态寄存器分布如下：

```python
STATUS_REGISTERS = {
    0x93: [
        "RB_PLL1_LD_LOST",
        "RB_PLL1_LD", 
        "RB_PLL2_LD_LOST",
        "RB_PLL2_LD"
    ],
    0x94: [
        "RB_CLKin2_SEL",
        "RB_CLKin1_SEL", 
        "RB_CLKin0_SEL",
        "RB_CLKin2_LOS",
        "RB_CLKin1_LOS",
        "RB_CLKin0_LOS"
    ],
    0x95: [
        "RB_DAC_VALUE[9:0]"
    ],
    0x98: [
        "RB_HOLDOVER",
        "RB_DAC_RAIL",
        "RB_DAC_HIGH", 
        "RB_DAC_LOW",
        "RB_DAC_LOCKED"
    ]
}
```

### 2. 核心组件设计

#### RegisterStatusMonitor类

创建专门的状态监控器类，负责定时读取和状态更新：

**主要功能：**
- 使用QTimer实现5秒间隔的定时读取
- 检查硬件/模拟模式状态
- 调用现有的寄存器读取功能
- 通过RegisterUpdateBus发送更新信号

**关键方法：**
- `start_monitoring()` - 启动监控
- `stop_monitoring()` - 停止监控  
- `_read_status_registers()` - 读取所有状态寄存器
- `update_widget_states()` - 更新控件状态

### 3. 系统集成点

#### 3.1 RegisterOperationService集成

在`RegisterOperationService`中集成状态监控器：

```python
class RegisterOperationService:
    def __init__(self, main_window):
        # ... 现有初始化代码 ...
        
        # 创建状态监控器
        self.status_monitor = RegisterStatusMonitor(self)
        
    def start_status_monitoring(self):
        """启动状态监控"""
        self.status_monitor.start_monitoring()
    
    def stop_status_monitoring(self):
        """停止状态监控"""
        self.status_monitor.stop_monitoring()
```

#### 3.2 主窗口初始化集成

在主窗口初始化完成后启动状态监控：

```python
def _setup_status_monitoring(self):
    """设置状态监控"""
    try:
        if hasattr(self, 'register_service') and not self.simulation_mode:
            # 只在硬件模式下启动状态监控
            self.register_service.start_status_monitoring()
            logger.info("已启动寄存器状态监控")
        else:
            logger.info("模拟模式下不启动状态监控")
    except Exception as e:
        logger.error(f"设置状态监控时出错: {str(e)}")
```

#### 3.3 模式切换处理

在EventCoordinator中处理模式切换时的监控启停：

```python
def handle_simulation_mode_toggle(self, checked):
    """处理模拟模式切换"""
    try:
        self.main_window.simulation_mode = checked
        # ... 现有模式切换代码 ...
        
        # 根据模式切换状态监控
        if hasattr(self.main_window, 'register_service'):
            if checked:
                # 切换到模拟模式，停止状态监控
                self.main_window.register_service.stop_status_monitoring()
                logger.info("模拟模式：停止状态监控")
            else:
                # 切换到硬件模式，启动状态监控
                self.main_window.register_service.start_status_monitoring()
                logger.info("硬件模式：启动状态监控")
                
    except Exception as e:
        logger.error(f"切换模拟模式时出错: {str(e)}")
```

### 4. 数据流程

#### 4.1 监控启动流程

```mermaid
graph TD
    A[主窗口初始化] --> B[检查硬件模式]
    B --> C{是硬件模式?}
    C -->|是| D[启动状态监控]
    C -->|否| E[跳过监控启动]
    D --> F[创建5秒定时器]
    F --> G[开始定时读取]
```

#### 4.2 状态读取和更新流程

```mermaid
graph TD
    A[定时器触发] --> B[检查硬件模式]
    B --> C{仍在硬件模式?}
    C -->|否| D[停止监控]
    C -->|是| E[读取状态寄存器0x93-0x98]
    E --> F[更新RegisterManager]
    F --> G[发送RegisterUpdateBus信号]
    G --> H[各工具窗口接收信号]
    H --> I[更新UI控件状态]
    I --> J[等待下次定时器触发]
    J --> A
```

## 实现优势

### 1. 架构优势
- **无侵入性**：利用现有的寄存器读取和事件总线机制
- **模块化设计**：独立的监控器类，易于维护和扩展
- **统一管理**：通过RegisterOperationService统一管理

### 2. 功能优势
- **实时性**：5秒间隔确保状态及时更新
- **智能启停**：根据硬件/模拟模式自动控制
- **全面覆盖**：涵盖所有重要状态寄存器

### 3. 维护优势
- **配置化**：寄存器映射表易于修改和扩展
- **日志完整**：详细的日志记录便于调试
- **错误处理**：完善的异常处理机制

## 配置和扩展

### 修改监控间隔

```python
# 在RegisterStatusMonitor.__init__()中修改
self.monitor_interval = 3000  # 改为3秒
```

### 添加新的监控寄存器

```python
# 在STATUS_REGISTERS中添加新寄存器
STATUS_REGISTERS = {
    # ... 现有寄存器 ...
    0x99: [
        "NEW_STATUS_REGISTER_1",
        "NEW_STATUS_REGISTER_2"
    ]
}
```

### 自定义状态处理

可以在`update_widget_states()`方法中添加特定寄存器的自定义处理逻辑。

## 测试验证

### 功能测试要点

1. **启动测试**：验证硬件模式下监控正常启动
2. **读取测试**：验证5秒间隔的寄存器读取
3. **更新测试**：验证UI控件状态正确更新
4. **模式切换测试**：验证模式切换时监控的启停
5. **异常处理测试**：验证各种异常情况的处理

### 测试脚本

项目中提供了`test_status_monitoring.py`测试脚本，可以验证：
- 状态监控器初始化
- 手动寄存器读取
- 模式切换功能
- 监控状态检查

## 已知问题和解决方案

### 问题：状态监控读取时仍然触发UI跳转

**问题描述：**
在实现状态监控功能后，发现每次状态监控读取寄存器时，寄存器表格仍然会跳转到被读取的寄存器，影响用户体验。

**问题原因：**
1. 状态监控读取仍然会触发`RegisterUpdateBus.emit_register_updated`信号
2. `RegisterDisplayManager.handle_global_register_update`接收到信号后调用`select_register_by_addr`
3. 导致界面跳转到被读取的寄存器

**当前解决方案：**
在`RegisterOperationService._handle_read_result`中添加了状态监控标志检查，但效果有限。

**完整解决方案（待实现）：**
1. 修改`RegisterUpdateBus`，添加信号类型参数，区分状态监控和用户操作
2. 在`RegisterDisplayManager`中根据信号类型决定是否触发UI跳转
3. 或者为状态监控创建专用的更新通道，避免使用全局更新事件

### 建议的改进方案

```python
# 方案1：修改RegisterUpdateBus支持信号类型
class RegisterUpdateBus:
    def emit_register_updated(self, addr, value, update_type="user"):
        """发送寄存器更新信号
        Args:
            update_type: "user" | "status_monitor" | "batch"
        """
        self.register_updated.emit(addr, value, update_type)

# 方案2：在RegisterDisplayManager中过滤状态监控更新
def handle_global_register_update(self, addr, value, update_type="user"):
    if update_type == "status_monitor":
        # 只更新数据，不触发UI跳转
        return
    # 正常处理用户操作的更新
```

## 总结

本实现方案通过创建专门的RegisterStatusMonitor类，与现有的RegisterOperationService和事件总线系统集成，实现了自动化的寄存器状态监控功能。虽然基本功能已实现，但仍存在UI跳转问题需要进一步优化。该方案具有良好的扩展性和维护性，为实时监控硬件状态提供了基础框架。
