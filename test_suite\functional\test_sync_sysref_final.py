#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终的同步系统参考修复验证测试
专注于核心功能验证
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication


def test_core_functionality():
    """测试核心功能"""
    print("=" * 60)
    print("最终的同步系统参考修复验证")
    print("=" * 60)
    
    try:
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 1. 验证文件移除
        print("1. 验证文件移除...")
        legacy_handler_path = os.path.join(project_root, 'ui', 'handlers', 'SyncSysRefHandler.py')
        
        if os.path.exists(legacy_handler_path):
            print("❌ 传统处理器文件仍然存在")
            return False
        else:
            print("✓ 传统处理器文件已成功移除")
        
        # 2. 验证导入
        print("\n2. 验证导入...")
        try:
            from ui.handlers.SyncSysRefHandler import SyncSysRefHandler
            print("❌ 仍然可以导入传统处理器")
            return False
        except ImportError:
            print("✓ 传统处理器导入失败（预期行为）")
        
        try:
            from ui.handlers.ModernSyncSysRefHandler import ModernSyncSysRefHandler
            print("✓ 现代化处理器导入成功")
        except Exception as e:
            print(f"❌ 现代化处理器导入失败: {str(e)}")
            return False
        
        # 3. 验证现代化处理器功能
        print("\n3. 验证现代化处理器功能...")
        
        # 创建寄存器管理器
        from core.services.register.RegisterManager import RegisterManager
        
        # 加载寄存器配置
        config_path = os.path.join(project_root, 'lib', 'register.json')
        if not os.path.exists(config_path):
            print(f"❌ 寄存器配置文件不存在: {config_path}")
            return False
            
        import json
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        register_manager = RegisterManager(registers_config)
        
        # 创建现代化处理器
        modern_handler = ModernSyncSysRefHandler(None, register_manager)
        print("✓ 现代化处理器创建成功")
        
        # 检查关键功能
        if hasattr(modern_handler, 'scroll_area'):
            print("✓ 滚动区域存在")
        else:
            print("❌ 滚动区域不存在")
            return False
        
        if hasattr(modern_handler, 'widget_register_map'):
            mapping_count = len(modern_handler.widget_register_map)
            print(f"✓ 控件映射: {mapping_count} 个控件")
        else:
            print("❌ 控件映射不存在")
            return False
        
        # 4. 验证现代化工厂
        print("\n4. 验证现代化工厂...")
        
        # 创建一个模拟的主窗口对象
        class MockMainWindow:
            def __init__(self):
                self.register_manager = register_manager
        
        mock_main_window = MockMainWindow()
        
        from ui.factories.ModernToolWindowFactory import ModernToolWindowFactory
        modern_factory = ModernToolWindowFactory(mock_main_window)
        
        # 检查配置
        sync_config = modern_factory.HANDLER_CONFIGS.get('sync_sysref')
        if not sync_config:
            print("❌ 同步系统参考配置不存在")
            return False
        
        print("✓ 现代化工厂配置正确")
        
        if sync_config.get('legacy_handler') is not None:
            print("❌ 传统处理器配置应该为None")
            return False
        else:
            print("✓ 传统处理器配置已正确设置为None")
        
        if not sync_config.get('modern_handler'):
            print("❌ 现代化处理器配置不能为空")
            return False
        else:
            print("✓ 现代化处理器配置正确")
        
        # 5. 测试工厂创建窗口
        print("\n5. 测试工厂创建窗口...")
        try:
            sync_window = modern_factory.create_window_by_type('sync_sysref')
            if sync_window:
                print("✓ 现代化工厂成功创建同步系统参考窗口")
                print(f"✓ 创建的窗口类型: {type(sync_window).__name__}")
                
                # 验证是现代化处理器
                if 'Modern' in type(sync_window).__name__:
                    print("✓ 确认使用现代化处理器")
                else:
                    print("❌ 未使用现代化处理器")
                    return False
            else:
                print("❌ 现代化工厂创建窗口失败")
                return False
        except Exception as e:
            print(f"❌ 现代化工厂创建窗口时出错: {str(e)}")
            return False
        
        print("\n✅ 所有核心功能测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_core_functionality()
    
    if success:
        print("\n🎉 同步系统参考移除修复完全成功！")
        print("📋 修复总结:")
        print("   - ✅ 传统处理器文件已移除")
        print("   - ✅ 传统处理器无法导入")
        print("   - ✅ 现代化处理器正常工作")
        print("   - ✅ 滚动区域功能正常")
        print("   - ✅ 控件映射构建成功")
        print("   - ✅ 现代化工厂配置正确")
        print("   - ✅ 工厂创建窗口正常")
        print("\n🔧 修复的文件:")
        print("   - ui/windows/RegisterMainWindow.py - 使用现代化工厂")
        print("   - ui/factories/ToolWindowFactory.py - 使用现代化处理器")
        print("   - ui/managers/ToolWindowManager.py - 使用现代化处理器")
        print("   - ui/managers/TabWindowManager.py - 使用现代化处理器")
        print("\n🚀 现在可以正常运行软件，不会再出现SyncSysRefHandler导入错误！")
    else:
        print("\n❌ 测试失败，需要进一步检查")
        sys.exit(1)
