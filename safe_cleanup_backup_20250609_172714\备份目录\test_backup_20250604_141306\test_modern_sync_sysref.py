#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试现代化同步系统参考处理器
验证ModernSyncSysRefHandler是否正常工作
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_modern_sync_sysref():
    """测试现代化同步系统参考处理器"""
    try:
        print("=" * 60)
        print("测试现代化同步系统参考处理器")
        print("=" * 60)
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 导入必要的模块
        from core.services.register.RegisterManager import RegisterManager
        from ui.handlers.ModernSyncSysRefHandler import ModernSyncSysRefHandler
        import json
        
        print("1. 加载寄存器配置...")
        # 加载寄存器配置
        config_path = os.path.join(os.path.dirname(__file__), 'lib', 'register.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        print(f"   ✓ 加载了 {len(registers_config)} 个寄存器配置")
        
        print("2. 创建RegisterManager...")
        register_manager = RegisterManager(registers_config)
        print(f"   ✓ 创建了RegisterManager，包含 {len(register_manager.register_objects)} 个寄存器对象")
        
        print("3. 创建现代化同步系统参考处理器...")
        try:
            modern_handler = ModernSyncSysRefHandler(None, register_manager)
            print("   ✓ 成功创建ModernSyncSysRefHandler")
            
            # 等待初始化完成
            import time
            time.sleep(0.2)
            
            # 检查控件映射
            print(f"   ✓ 构建了 {len(modern_handler.widget_register_map)} 个控件映射")
            
            # 显示部分控件映射
            sync_widgets = [name for name in modern_handler.widget_register_map.keys() if "SYNC" in name]
            print(f"   SYNC相关控件: {sync_widgets[:5]}...")  # 显示前5个
            
        except Exception as e:
            print(f"   ❌ 创建ModernSyncSysRefHandler失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
        print("4. 测试同步系统状态获取...")
        try:
            initial_status = modern_handler.get_current_status()
            print(f"   初始状态: {initial_status}")
            
            # 验证状态字段
            expected_fields = ["sysref_enabled", "sync_dis_status", "vco_frequency", "sysref_divider"]
            for field in expected_fields:
                if field in initial_status:
                    print(f"   ✓ {field}: {initial_status[field]}")
                else:
                    print(f"   ❌ 缺少状态字段: {field}")
                    
        except Exception as e:
            print(f"   ❌ 状态获取出错: {str(e)}")
        
        print("5. 测试频率计算功能...")
        try:
            # 设置VCO频率
            if hasattr(modern_handler.ui, "InternalVCOFreq"):
                modern_handler.ui.InternalVCOFreq.setText("2949.12")
                print("   设置VCO频率: 2949.12 MHz")
            
            # 设置分频比
            if hasattr(modern_handler.ui, "spinBoxSysrefDIV"):
                modern_handler.ui.spinBoxSysrefDIV.setValue(32)
                print("   设置分频比: 32")
            
            # 触发频率计算
            modern_handler.calculate_output_frequencies()
            print("   ✓ 频率计算执行完成")
            
            # 检查计算结果
            expected_freq = 2949.12 / 32  # 约92.16 MHz
            
            # 检查频率显示控件
            freq_widgets = ["SysrefFreq", "SyncSysrefFreq1", "SyncSysrefFreq2"]
            for widget_name in freq_widgets:
                if hasattr(modern_handler.ui, widget_name):
                    widget = getattr(modern_handler.ui, widget_name)
                    if hasattr(widget, 'text'):
                        freq_text = widget.text()
                        try:
                            freq_value = float(freq_text)
                            if abs(freq_value - expected_freq) < 0.01:
                                print(f"     ✓ {widget_name}: {freq_text} MHz (正确)")
                            else:
                                print(f"     ❌ {widget_name}: {freq_text} MHz (期望: {expected_freq:.2f})")
                        except ValueError:
                            print(f"     ❌ {widget_name}: 无效频率值 '{freq_text}'")
                    else:
                        print(f"     {widget_name}: 控件类型不支持text()")
                else:
                    print(f"     {widget_name}: 控件不存在")
                    
        except Exception as e:
            print(f"   ❌ 频率计算测试出错: {str(e)}")
        
        print("6. 测试同步系统预设功能...")
        try:
            # 测试默认预设
            print("   应用默认预设...")
            modern_handler.set_sync_preset("default")
            
            # 检查VCO频率
            if hasattr(modern_handler.ui, "InternalVCOFreq"):
                vco_freq = modern_handler.ui.InternalVCOFreq.text()
                print(f"   默认预设后VCO频率: {vco_freq} MHz")
                if vco_freq == "2949.12":
                    print("   ✓ 默认预设VCO频率正确")
                else:
                    print("   ❌ 默认预设VCO频率不正确")
            
            # 检查分频比
            if hasattr(modern_handler.ui, "spinBoxSysrefDIV"):
                div_value = modern_handler.ui.spinBoxSysrefDIV.value()
                print(f"   默认预设后分频比: {div_value}")
                if div_value == 32:
                    print("   ✓ 默认预设分频比正确")
                else:
                    print("   ❌ 默认预设分频比不正确")
            
            # 测试高频预设
            print("   应用高频预设...")
            modern_handler.set_sync_preset("high_frequency")
            
            if hasattr(modern_handler.ui, "InternalVCOFreq"):
                vco_freq = modern_handler.ui.InternalVCOFreq.text()
                if vco_freq == "3000.0":
                    print("   ✓ 高频预设应用成功")
                else:
                    print("   ❌ 高频预设应用失败")
                    
        except Exception as e:
            print(f"   ❌ 同步系统预设测试出错: {str(e)}")
        
        print("7. 测试SYNC DIS批量控制...")
        try:
            # 获取SYNC DIS状态
            sync_dis_count = modern_handler.get_sync_dis_count()
            print(f"   SYNC DIS控件状态: {sync_dis_count}")
            
            if sync_dis_count["total_count"] > 0:
                # 测试全部启用
                print("   测试全部启用SYNC DIS...")
                modern_handler.set_all_sync_dis(True)
                
                # 检查结果
                new_count = modern_handler.get_sync_dis_count()
                if new_count["enabled_count"] == new_count["total_count"]:
                    print("   ✓ 全部启用SYNC DIS成功")
                else:
                    print(f"   ❌ 全部启用SYNC DIS失败 ({new_count['enabled_count']}/{new_count['total_count']})")
                
                # 测试全部禁用
                print("   测试全部禁用SYNC DIS...")
                modern_handler.set_all_sync_dis(False)
                
                # 检查结果
                final_count = modern_handler.get_sync_dis_count()
                if final_count["enabled_count"] == 0:
                    print("   ✓ 全部禁用SYNC DIS成功")
                else:
                    print(f"   ❌ 全部禁用SYNC DIS失败 ({final_count['enabled_count']}/{final_count['total_count']})")
            else:
                print("   未找到SYNC DIS控件，跳过批量控制测试")
                
        except Exception as e:
            print(f"   ❌ SYNC DIS批量控制测试出错: {str(e)}")
        
        print("8. 测试控件值变化...")
        try:
            # 模拟VCO频率变化
            if hasattr(modern_handler.ui, "InternalVCOFreq"):
                print("   模拟VCO频率变化...")
                modern_handler.ui.InternalVCOFreq.setText("3000.0")
                modern_handler.calculate_output_frequencies()
                print("   ✓ VCO频率变化处理成功")
            
            # 模拟分频比变化
            if hasattr(modern_handler.ui, "spinBoxSysrefDIV"):
                print("   模拟分频比变化...")
                modern_handler.ui.spinBoxSysrefDIV.setValue(64)
                modern_handler.calculate_output_frequencies()
                print("   ✓ 分频比变化处理成功")
                
        except Exception as e:
            print(f"   ❌ 控件值变化测试出错: {str(e)}")
        
        print("9. 测试最终状态...")
        try:
            final_status = modern_handler.get_current_status()
            print(f"   最终状态: {final_status}")
            
            # 验证状态一致性
            if final_status.get("vco_frequency") == 3000.0:
                print("   ✓ VCO频率状态一致")
            else:
                print("   ❌ VCO频率状态不一致")
            
            if final_status.get("sysref_divider") == 64:
                print("   ✓ 分频比状态一致")
            else:
                print("   ❌ 分频比状态不一致")
            
        except Exception as e:
            print(f"   ❌ 最终状态测试出错: {str(e)}")
        
        print("\n" + "=" * 60)
        print("🎉 现代化同步系统参考处理器测试完成！")
        print("=" * 60)
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_modern_sync_sysref()
    sys.exit(0 if success else 1)
