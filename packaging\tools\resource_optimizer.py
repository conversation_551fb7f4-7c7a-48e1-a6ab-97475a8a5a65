"""
资源文件优化工具
用于在打包前优化图片、配置文件等资源，减小最终exe文件大小
"""

import os
import json
import shutil
from pathlib import Path
from typing import List, Dict, Any

class ResourceOptimizer:
    """资源文件优化器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.optimized_dir = self.project_root / 'packaging' / 'optimized_resources'
        self.optimization_report = {}
        
    def optimize_all_resources(self) -> Dict[str, Any]:
        """优化所有资源文件"""
        print("🔧 开始资源优化...")
        
        # 创建优化目录
        self.optimized_dir.mkdir(exist_ok=True)
        
        # 优化配置文件
        config_savings = self._optimize_config_files()
        
        # 优化图片资源
        image_savings = self._optimize_images()
        
        # 清理不必要的文件
        cleanup_savings = self._cleanup_unnecessary_files()
        
        # 生成优化报告
        self.optimization_report = {
            'config_files': config_savings,
            'images': image_savings,
            'cleanup': cleanup_savings,
            'total_savings': config_savings + image_savings + cleanup_savings
        }
        
        print(f"✅ 资源优化完成，节省空间: {self.optimization_report['total_savings']:.2f} KB")
        return self.optimization_report
    
    def _optimize_config_files(self) -> float:
        """优化配置文件"""
        print("  📄 优化配置文件...")
        savings = 0.0
        
        config_dir = self.project_root / 'config'
        optimized_config_dir = self.optimized_dir / 'config'
        optimized_config_dir.mkdir(exist_ok=True)
        
        for config_file in config_dir.glob('*.json'):
            if config_file.name in ['default.json']:  # 只保留必需的配置文件
                original_size = config_file.stat().st_size
                
                # 读取并压缩JSON
                with open(config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 移除注释和不必要的字段
                optimized_data = self._clean_config_data(data)
                
                # 保存压缩的JSON
                optimized_file = optimized_config_dir / config_file.name
                with open(optimized_file, 'w', encoding='utf-8') as f:
                    json.dump(optimized_data, f, separators=(',', ':'), ensure_ascii=False)
                
                new_size = optimized_file.stat().st_size
                file_savings = (original_size - new_size) / 1024
                savings += file_savings
                
                print(f"    ✓ {config_file.name}: {file_savings:.2f} KB")
        
        return savings
    
    def _clean_config_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """清理配置数据，移除不必要的字段"""
        if not isinstance(data, dict):
            return data
            
        cleaned = {}
        
        # 保留的关键字段
        essential_keys = {
            'app', 'ui', 'spi', 'register', 'logging',
            'name', 'version', 'window', 'theme', 'font',
            'timeout', 'retry_count', 'auto_write'
        }
        
        for key, value in data.items():
            # 跳过开发相关的配置
            if key.startswith('dev_') or key.startswith('debug_'):
                continue
                
            # 递归清理嵌套字典
            if isinstance(value, dict):
                cleaned_value = self._clean_config_data(value)
                if cleaned_value:  # 只保留非空的字典
                    cleaned[key] = cleaned_value
            elif key in essential_keys or not key.startswith('_'):
                cleaned[key] = value
        
        return cleaned
    
    def _optimize_images(self) -> float:
        """优化图片资源"""
        print("  🖼️ 优化图片资源...")
        savings = 0.0
        
        # 检查是否有PIL可用于图片优化
        try:
            from PIL import Image
            pil_available = True
        except ImportError:
            pil_available = False
            print("    ⚠️ PIL不可用，跳过图片压缩")
        
        images_dir = self.project_root / 'images'
        gui_dir = self.project_root / 'gui'
        optimized_images_dir = self.optimized_dir / 'images'
        optimized_gui_dir = self.optimized_dir / 'gui'
        
        optimized_images_dir.mkdir(exist_ok=True)
        optimized_gui_dir.mkdir(exist_ok=True)
        
        # 优化images目录
        if images_dir.exists():
            savings += self._optimize_image_directory(images_dir, optimized_images_dir, pil_available)
        
        # 优化gui目录
        if gui_dir.exists():
            savings += self._optimize_image_directory(gui_dir, optimized_gui_dir, pil_available)
        
        return savings
    
    def _optimize_image_directory(self, source_dir: Path, target_dir: Path, pil_available: bool) -> float:
        """优化指定目录中的图片"""
        savings = 0.0
        
        for image_file in source_dir.glob('*'):
            if image_file.suffix.lower() in ['.png', '.jpg', '.jpeg', '.bmp', '.gif']:
                original_size = image_file.stat().st_size
                target_file = target_dir / image_file.name
                
                if pil_available and image_file.suffix.lower() != '.ico':
                    # 使用PIL优化图片
                    try:
                        from PIL import Image
                        with Image.open(image_file) as img:
                            # 转换为RGB模式（如果需要）
                            if img.mode in ('RGBA', 'LA', 'P'):
                                img = img.convert('RGB')
                            
                            # 保存优化后的图片
                            img.save(target_file, optimize=True, quality=85)
                    except Exception as e:
                        print(f"    ⚠️ 优化 {image_file.name} 失败: {e}")
                        shutil.copy2(image_file, target_file)
                else:
                    # 直接复制
                    shutil.copy2(image_file, target_file)
                
                new_size = target_file.stat().st_size
                file_savings = (original_size - new_size) / 1024
                savings += file_savings
                
                if file_savings > 0:
                    print(f"    ✓ {image_file.name}: {file_savings:.2f} KB")
            elif image_file.suffix.lower() == '.ico':
                # ICO文件直接复制
                shutil.copy2(image_file, target_dir / image_file.name)
        
        return savings
    
    def _cleanup_unnecessary_files(self) -> float:
        """清理不必要的文件"""
        print("  🧹 清理不必要的文件...")
        savings = 0.0
        
        # 要清理的文件模式
        cleanup_patterns = [
            '**/*.log', '**/*.log.*',  # 日志文件
            '**/*.tmp', '**/*.temp',   # 临时文件
            '**/*~', '**/*.bak',       # 备份文件
            '**/*.pyc', '**/*.pyo',    # Python缓存文件
            '**/__pycache__',          # Python缓存目录
            '**/*.xlsx', '**/*.xls',   # Excel文件
            '**/*.md', '**/*.txt',     # 文档文件（除了必需的）
            '**/test_*', '**/*_test.py', # 测试文件
        ]
        
        for pattern in cleanup_patterns:
            for file_path in self.project_root.glob(pattern):
                if file_path.is_file():
                    file_size = file_path.stat().st_size / 1024
                    savings += file_size
                    print(f"    🗑️ 清理: {file_path.relative_to(self.project_root)} ({file_size:.2f} KB)")
        
        return savings
    
    def get_optimized_paths(self) -> Dict[str, str]:
        """获取优化后的资源路径映射"""
        return {
            'config': str(self.optimized_dir / 'config'),
            'images': str(self.optimized_dir / 'images'),
            'gui': str(self.optimized_dir / 'gui'),
        }
    
    def cleanup_optimized_resources(self):
        """清理优化后的资源目录"""
        if self.optimized_dir.exists():
            shutil.rmtree(self.optimized_dir)
            print("🧹 已清理优化资源目录")

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1:
        project_root = sys.argv[1]
    else:
        project_root = Path(__file__).parent.parent.parent
    
    optimizer = ResourceOptimizer(project_root)
    report = optimizer.optimize_all_resources()
    
    print("\n📊 优化报告:")
    print(f"  配置文件优化: {report['config_files']:.2f} KB")
    print(f"  图片优化: {report['images']:.2f} KB") 
    print(f"  文件清理: {report['cleanup']:.2f} KB")
    print(f"  总计节省: {report['total_savings']:.2f} KB")

if __name__ == '__main__':
    main()
