"""
信息面板插件 - 显示PLL状态、调试日志和控件信息
"""

import os
import time
import codecs
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QTextEdit, QTabWidget, QGroupBox,
                            QGridLayout, QComboBox,
                            QCheckBox)
from PyQt5.QtCore import QTimer, QObject
from PyQt5.QtGui import QFont, QTextCursor

from core.services.plugin.PluginManager import IToolWindowPlugin
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class InfoPanelPlugin(IToolWindowPlugin):
    """信息面板插件"""
    
    def __init__(self, main_window=None):
        super().__init__()
        self.main_window = main_window
        self.window = None
        self.start_time = time.time()
        self._is_closing = False
        
        # PLL锁存状态缓存
        self._pll1_lock_data = {}
        self._pll2_lock_data = {}
    
    @property
    def name(self) -> str:
        """插件名称"""
        return "信息面板"

    @property
    def version(self) -> str:
        """插件版本"""
        return "1.0.0"

    @property
    def description(self) -> str:
        """插件描述"""
        return "显示PLL状态、调试日志和控件信息"

    def initialize(self, context=None):
        """初始化插件"""
        try:
            logger.info("初始化信息面板插件")
            if context:
                self.main_window = context
            return True
        except Exception as e:
            logger.error(f"初始化信息面板插件失败: {str(e)}")
            return False

    def cleanup(self):
        """清理插件资源"""
        try:
            if hasattr(self, 'window') and self.window:
                self.window.close()
            logger.info("信息面板插件资源已清理")
        except Exception as e:
            logger.error(f"清理信息面板插件资源失败: {str(e)}")

    @property
    def menu_text(self) -> str:
        """菜单文本"""
        return "信息面板"

    @property
    def icon_path(self) -> str:
        """图标路径"""
        return ""  # 暂时不使用图标

    def create_window(self, parent=None):
        """创建窗口"""
        try:
            if self.window is None:
                self._create_window(parent)
            return self.window
        except Exception as e:
            logger.error(f"创建窗口失败: {str(e)}")
            return None

    def show_window(self):
        """显示信息面板窗口"""
        try:
            if self.window is None:
                self._create_window()

            if self.window:
                self.window.show()
                self.window.raise_()
                self.window.activateWindow()
                logger.info("信息面板窗口已显示")
            else:
                logger.error("窗口创建失败，无法显示")

        except Exception as e:
            logger.error(f"显示信息面板窗口失败: {str(e)}")

    def _create_window(self, parent=None):
        """创建信息面板窗口"""
        try:
            self.window = QWidget(parent)
            self.window.setWindowTitle("信息面板")
            self.window.setGeometry(100, 100, 800, 600)

            # 设置窗口关闭事件
            self.window.closeEvent = self._on_window_close

            # 创建主布局
            layout = QVBoxLayout(self.window)

            # 创建标签页控件
            self.tab_widget = QTabWidget()
            layout.addWidget(self.tab_widget)

            # 创建各个标签页
            self._create_debug_log_tab()
            self._create_pll1_status_tab()
            self._create_pll2_status_tab()
            self._create_widget_info_tab()

            # 设置定时器更新信息
            self.update_timer = QTimer()
            self.update_timer.timeout.connect(self._update_all_info)
            self.update_timer.start(2000)  # 每2秒更新一次

            # 设置控件信息监听器
            self._setup_widget_info_listener()

            # 连接PLL锁存状态信号
            self._connect_pll_lock_signals()

            # 连接PLL控件变化信号
            self._connect_pll_widget_signals()

            # 初始化信息
            self._update_all_info()

            logger.info("信息面板窗口创建完成")
            return self.window

        except Exception as e:
            logger.error(f"创建信息面板窗口失败: {str(e)}")
            return None
    
    def _create_debug_log_tab(self):
        """创建调试日志标签页"""
        try:
            tab = QWidget()
            layout = QVBoxLayout(tab)

            # 日志控制区域
            control_group = QGroupBox("日志控制")
            control_layout = QHBoxLayout(control_group)

            # 日志级别选择
            level_label = QLabel("日志级别:")
            control_layout.addWidget(level_label)

            self.log_level_combo = QComboBox()
            self.log_level_combo.addItem("🔴 ERROR - 只显示错误", "ERROR")
            self.log_level_combo.addItem("⚠️ WARNING - 显示警告和错误", "WARNING")
            self.log_level_combo.addItem("ℹ️ INFO - 显示信息、警告和错误", "INFO")
            self.log_level_combo.addItem("🔍 DEBUG - 显示所有日志", "DEBUG")

            # 设置默认选择为WARNING
            self.log_level_combo.setCurrentIndex(1)
            # 使用多个信号确保能捕获到变化
            self.log_level_combo.currentIndexChanged.connect(self._on_log_level_changed)
            self.log_level_combo.currentTextChanged.connect(self._on_log_level_changed)
            control_layout.addWidget(self.log_level_combo)

            # 实时更新开关
            self.auto_refresh_checkbox = QCheckBox("实时更新")
            self.auto_refresh_checkbox.setChecked(True)
            self.auto_refresh_checkbox.toggled.connect(self._on_auto_refresh_toggled)
            control_layout.addWidget(self.auto_refresh_checkbox)

            control_layout.addStretch()
            layout.addWidget(control_group)

            # 日志显示区域
            log_group = QGroupBox("调试日志")
            log_layout = QVBoxLayout(log_group)

            self.log_text = QTextEdit()
            self.log_text.setReadOnly(True)
            self.log_text.setFont(QFont("Consolas", 9))
            log_layout.addWidget(self.log_text)

            layout.addWidget(log_group)

            # 控制按钮
            button_layout = QHBoxLayout()

            self.clear_log_btn = QPushButton("清空日志")
            self.clear_log_btn.clicked.connect(self._clear_log)
            button_layout.addWidget(self.clear_log_btn)

            self.refresh_log_btn = QPushButton("刷新日志")
            self.refresh_log_btn.clicked.connect(self._update_debug_log)
            button_layout.addWidget(self.refresh_log_btn)

            # 应用日志级别按钮
            self.apply_level_btn = QPushButton("应用级别到系统")
            self.apply_level_btn.setToolTip("将选择的日志级别应用到整个系统")
            self.apply_level_btn.clicked.connect(self._apply_log_level_to_system)
            button_layout.addWidget(self.apply_level_btn)

            # 测试过滤按钮
            self.test_filter_btn = QPushButton("测试过滤")
            self.test_filter_btn.setToolTip("测试当前选择的日志级别过滤效果")
            self.test_filter_btn.clicked.connect(self._test_log_filter)
            button_layout.addWidget(self.test_filter_btn)

            button_layout.addStretch()
            layout.addLayout(button_layout)

            self.tab_widget.addTab(tab, "调试日志")

            # 初始化完成后立即更新一次日志显示
            QTimer.singleShot(100, self._update_debug_log)

        except Exception as e:
            logger.error(f"创建调试日志标签页失败: {str(e)}")
    
    def _create_pll1_status_tab(self):
        """创建PLL1状态标签页"""
        try:
            tab = QWidget()
            layout = QVBoxLayout(tab)
            
            # PLL1状态信息
            pll1_group = QGroupBox("PLL1状态信息")
            pll1_layout = QGridLayout(pll1_group)
            
            self.pll1_lock_status_label = QLabel("未知")
            self.pll1_pfd_freq_label = QLabel("0.00 MHz")
            self.pll1_n_divider_label = QLabel("未知")
            self.pll1_r_divider_label = QLabel("未知")
            self.pll1_power_status_label = QLabel("未知")
            
            pll1_layout.addWidget(QLabel("锁存状态:"), 0, 0)
            pll1_layout.addWidget(self.pll1_lock_status_label, 0, 1)
            pll1_layout.addWidget(QLabel("PFD频率:"), 1, 0)
            pll1_layout.addWidget(self.pll1_pfd_freq_label, 1, 1)
            pll1_layout.addWidget(QLabel("N Divider:"), 2, 0)
            pll1_layout.addWidget(self.pll1_n_divider_label, 2, 1)
            pll1_layout.addWidget(QLabel("R Divider:"), 3, 0)
            pll1_layout.addWidget(self.pll1_r_divider_label, 3, 1)
            pll1_layout.addWidget(QLabel("电源状态:"), 4, 0)
            pll1_layout.addWidget(self.pll1_power_status_label, 4, 1)
            
            layout.addWidget(pll1_group)
            
            # PLL1状态消息
            pll1_msg_group = QGroupBox("PLL1状态消息")
            pll1_msg_layout = QVBoxLayout(pll1_msg_group)
            
            self.pll1_status_text = QTextEdit()
            self.pll1_status_text.setReadOnly(True)
            self.pll1_status_text.setMaximumHeight(150)
            self.pll1_status_text.setStyleSheet("""
                QTextEdit {
                    background-color: #f0f0f0;
                    border: 1px solid #ccc;
                    font-family: 'Consolas', 'Monaco', monospace;
                    font-size: 10pt;
                }
            """)
            pll1_msg_layout.addWidget(self.pll1_status_text)
            
            layout.addWidget(pll1_msg_group)
            
            layout.addStretch()
            self.tab_widget.addTab(tab, "PLL1状态")
            
        except Exception as e:
            logger.error(f"创建PLL1状态标签页失败: {str(e)}")
    
    def _create_pll2_status_tab(self):
        """创建PLL2状态标签页"""
        try:
            tab = QWidget()
            layout = QVBoxLayout(tab)
            
            # PLL2状态信息
            pll2_group = QGroupBox("PLL2状态信息")
            pll2_layout = QGridLayout(pll2_group)
            
            self.pll2_lock_status_label = QLabel("未知")
            self.pll2_pfd_freq_label = QLabel("0.00 MHz")
            self.pll2_n_divider_label = QLabel("未知")
            self.pll2_r_divider_label = QLabel("未知")
            self.pll2_power_status_label = QLabel("未知")
            
            pll2_layout.addWidget(QLabel("锁存状态:"), 0, 0)
            pll2_layout.addWidget(self.pll2_lock_status_label, 0, 1)
            pll2_layout.addWidget(QLabel("PFD频率:"), 1, 0)
            pll2_layout.addWidget(self.pll2_pfd_freq_label, 1, 1)
            pll2_layout.addWidget(QLabel("N Divider:"), 2, 0)
            pll2_layout.addWidget(self.pll2_n_divider_label, 2, 1)
            pll2_layout.addWidget(QLabel("R Divider:"), 3, 0)
            pll2_layout.addWidget(self.pll2_r_divider_label, 3, 1)
            pll2_layout.addWidget(QLabel("电源状态:"), 4, 0)
            pll2_layout.addWidget(self.pll2_power_status_label, 4, 1)
            
            layout.addWidget(pll2_group)
            
            # PLL2状态消息
            pll2_msg_group = QGroupBox("PLL2状态消息")
            pll2_msg_layout = QVBoxLayout(pll2_msg_group)
            
            self.pll2_status_text = QTextEdit()
            self.pll2_status_text.setReadOnly(True)
            self.pll2_status_text.setMaximumHeight(150)
            self.pll2_status_text.setStyleSheet("""
                QTextEdit {
                    background-color: #f0f0f0;
                    border: 1px solid #ccc;
                    font-family: 'Consolas', 'Monaco', monospace;
                    font-size: 10pt;
                }
            """)
            pll2_msg_layout.addWidget(self.pll2_status_text)
            
            layout.addWidget(pll2_msg_group)
            
            layout.addStretch()
            self.tab_widget.addTab(tab, "PLL2状态")
            
        except Exception as e:
            logger.error(f"创建PLL2状态标签页失败: {str(e)}")
    
    def _create_widget_info_tab(self):
        """创建控件信息标签页"""
        try:
            tab = QWidget()
            layout = QVBoxLayout(tab)
            
            # 说明文字
            info_label = QLabel("将鼠标悬停在应用程序中的控件上，这里会显示对应的寄存器信息")
            info_label.setStyleSheet("color: #666; font-style: italic; padding: 10px;")
            layout.addWidget(info_label)
            
            # 控件信息显示区域
            widget_group = QGroupBox("控件寄存器信息")
            widget_layout = QVBoxLayout(widget_group)
            
            self.widget_info_text = QTextEdit()
            self.widget_info_text.setReadOnly(True)
            self.widget_info_text.setFont(QFont("Consolas", 10))
            self.widget_info_text.setPlainText("等待鼠标悬停在控件上...")
            widget_layout.addWidget(self.widget_info_text)
            
            layout.addWidget(widget_group)
            
            # 控制按钮
            button_layout = QHBoxLayout()

            # 重新加载手动说明配置按钮
            reload_btn = QPushButton("重新加载控件说明")
            reload_btn.setToolTip("重新加载手动控件说明配置文件")
            reload_btn.clicked.connect(self.reload_manual_widget_descriptions)
            button_layout.addWidget(reload_btn)
            
            self.clear_widget_info_btn = QPushButton("清空信息")
            self.clear_widget_info_btn.clicked.connect(self._clear_widget_info)
            button_layout.addWidget(self.clear_widget_info_btn)
            
            button_layout.addStretch()
            layout.addLayout(button_layout)
            
            self.tab_widget.addTab(tab, "控件信息")
            
        except Exception as e:
            logger.error(f"创建控件信息标签页失败: {str(e)}")


    def _connect_pll_lock_signals(self):
        """连接PLL锁存状态信号"""
        try:
            # 如果已经连接过锁存状态信号，避免重复连接
            if hasattr(self, '_pll_lock_signals_connected') and self._pll_lock_signals_connected:
                return

            pll_handler = self._get_pll_handler()
            if pll_handler:
                # 连接PLL1锁存状态信号
                pll_handler.pll1_lock_status_changed.connect(self._on_pll1_lock_status_changed)
                # 连接PLL2锁存状态信号
                pll_handler.pll2_lock_status_changed.connect(self._on_pll2_lock_status_changed)

                # 标记锁存状态信号已连接
                self._pll_lock_signals_connected = True
                logger.debug("已连接PLL锁存状态信号")

                # 连接事件总线信号
                self._connect_event_bus_signals()
            else:
                logger.debug("无法获取PLL处理器，无法连接锁存状态信号")
        except Exception as e:
            logger.debug(f"连接PLL锁存状态信号失败: {str(e)}")

    def _connect_event_bus_signals(self):
        """连接事件总线信号，监听PLL状态变化"""
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            bus = RegisterUpdateBus.instance()

            # 连接PLL状态更新信号
            if hasattr(bus, 'pll_status_updated'):
                bus.pll_status_updated.connect(self._on_pll_status_updated)
                logger.debug("已连接事件总线PLL状态更新信号")

            # 连接寄存器更新信号
            if hasattr(bus, 'register_updated'):
                bus.register_updated.connect(self._on_register_updated)
                logger.debug("已连接事件总线寄存器更新信号")

        except Exception as e:
            logger.debug(f"连接事件总线信号失败: {str(e)}")

    def _on_pll_status_updated(self, pll_name, status_data):
        """处理事件总线的PLL状态更新"""
        try:
            logger.debug(f"收到事件总线PLL状态更新: {pll_name} = {status_data}")

            if pll_name == 'PLL1':
                self._update_pll1_display_from_cache(status_data)
            elif pll_name == 'PLL2':
                self._update_pll2_display_from_cache(status_data)

        except Exception as e:
            logger.debug(f"处理事件总线PLL状态更新失败: {str(e)}")

    def _on_register_updated(self, register_addr, value):
        """处理寄存器更新事件

        Args:
            register_addr (str): 寄存器地址，如 '0x4C'
            value (int): 寄存器值
        """
        try:
            # 监听PLL相关寄存器的变化
            pll_registers = [
                # PLL1 R Divider (动态映射，根据时钟源不同)
                '0x63',  # CLKin0_R[13:0]
                '0x65',  # CLKin1_R[13:0]
                '0x67',  # CLKin2_R[13:0]
                # PLL1 N Divider
                '0x69',  # PLL1_N[13:0]
                # PLL1 Power Down
                '0x50',  # PLL1_PD
                # PLL2相关寄存器
                '0x75',  # PLL2_R
                '0x76',  # PLL2_N_17_16
                '0x77',  # PLL2_N_15_0
                '0x83'   # PLL2_PD
            ]

            if register_addr in pll_registers:
                logger.debug(f"检测到PLL相关寄存器更新: {register_addr} = 0x{value:04X}")

                # 延迟更新状态，避免频繁更新
                if not hasattr(self, '_update_timer_active'):
                    self._update_timer_active = True
                    QTimer.singleShot(500, self._delayed_update_pll_status)

        except Exception as e:
            logger.debug(f"处理寄存器更新事件失败: {str(e)}")

    def _delayed_update_pll_status(self):
        """延迟更新PLL状态"""
        try:
            self._update_timer_active = False
            self._trigger_pll_lock_calculation()
            logger.debug("已执行延迟PLL状态更新")
        except Exception as e:
            logger.debug(f"延迟更新PLL状态失败: {str(e)}")

    def _on_pll1_lock_status_changed(self, is_locked, r_divider_output, n_divider_output):
        """处理PLL1锁存状态变化信号"""
        try:
            # 更新锁存状态显示
            lock_status = "已锁存" if is_locked else "未锁存"
            self.pll1_lock_status_label.setText(lock_status)
            self.pll1_lock_status_label.setStyleSheet(
                "color: green; font-weight: bold;" if is_locked else "color: red; font-weight: bold;"
            )

            # 缓存计算结果用于状态消息生成
            self._pll1_lock_data = {
                'is_locked': is_locked,
                'r_divider_output': r_divider_output,
                'n_divider_output': n_divider_output
            }

            # 生成PLL1状态消息
            self._generate_pll1_status_message()

            logger.debug(f"PLL1锁存状态更新: {'锁存' if is_locked else '未锁存'}")

        except Exception as e:
            logger.debug(f"处理PLL1锁存状态变化失败: {str(e)}")

    def _on_pll2_lock_status_changed(self, is_locked, r_divider_output, n_divider_output):
        """处理PLL2锁存状态变化信号"""
        try:
            # 更新锁存状态显示
            lock_status = "已锁存" if is_locked else "未锁存"
            self.pll2_lock_status_label.setText(lock_status)
            self.pll2_lock_status_label.setStyleSheet(
                "color: green; font-weight: bold;" if is_locked else "color: red; font-weight: bold;"
            )

            # 缓存计算结果用于状态消息生成
            self._pll2_lock_data = {
                'is_locked': is_locked,
                'r_divider_output': r_divider_output,
                'n_divider_output': n_divider_output
            }

            # 生成PLL2状态消息
            self._generate_pll2_status_message()

            logger.debug(f"PLL2锁存状态更新: {'锁存' if is_locked else '未锁存'}")

        except Exception as e:
            logger.debug(f"处理PLL2锁存状态变化失败: {str(e)}")

    def _connect_pll_widget_signals(self):
        """连接PLL控件变化信号，实现实时状态更新"""
        try:
            # 如果已经连接过信号，避免重复连接
            if hasattr(self, '_pll_signals_connected') and self._pll_signals_connected:
                logger.debug("PLL控件信号已连接，跳过重复连接")
                return

            pll_handler = self._get_pll_handler()
            if not pll_handler:
                logger.debug("无法获取PLL处理器，无法连接控件信号")
                return

            if not hasattr(pll_handler, 'ui'):
                logger.debug("PLL处理器没有UI属性，无法连接控件信号")
                return

            ui = pll_handler.ui
            logger.debug(f"获取到PLL处理器UI: {type(ui).__name__}")

            connected_signals = 0

            # 连接PLL1相关控件信号
            if hasattr(ui, 'PLL1PFDFreq'):
                try:
                    # PLL1PFDFreq是QLineEdit，连接textChanged信号
                    ui.PLL1PFDFreq.textChanged.connect(self._on_pll1_pfd_freq_changed)
                    connected_signals += 1
                    logger.debug("✅ 已连接PLL1PFDFreq信号")
                except Exception as e:
                    logger.debug(f"❌ 连接PLL1PFDFreq信号失败: {str(e)}")
            else:
                logger.debug("❌ PLL1PFDFreq控件不存在")

            if hasattr(ui, 'PLL1RDividerSetting'):
                try:
                    # PLL1RDividerSetting是QSpinBox，连接valueChanged信号
                    ui.PLL1RDividerSetting.valueChanged.connect(self._on_pll1_r_divider_changed)
                    connected_signals += 1
                    logger.debug("✅ 已连接PLL1RDividerSetting信号")
                except Exception as e:
                    logger.debug(f"❌ 连接PLL1RDividerSetting信号失败: {str(e)}")
            else:
                logger.debug("❌ PLL1RDividerSetting控件不存在")

            if hasattr(ui, 'PLL1NDivider'):
                try:
                    # PLL1NDivider是QSpinBox，连接valueChanged信号
                    ui.PLL1NDivider.valueChanged.connect(self._on_pll1_n_divider_changed)
                    connected_signals += 1
                    logger.debug("✅ 已连接PLL1NDivider信号")
                except Exception as e:
                    logger.debug(f"❌ 连接PLL1NDivider信号失败: {str(e)}")
            else:
                logger.debug("❌ PLL1NDivider控件不存在")

            if hasattr(ui, 'PLL1PD'):
                try:
                    # PLL1PD是QCheckBox，连接stateChanged信号
                    ui.PLL1PD.stateChanged.connect(self._on_pll1_power_changed)
                    connected_signals += 1
                    logger.debug("✅ 已连接PLL1PD信号")
                except Exception as e:
                    logger.debug(f"❌ 连接PLL1PD信号失败: {str(e)}")
            else:
                logger.debug("❌ PLL1PD控件不存在")

            # 连接PLL2相关控件信号
            if hasattr(ui, 'PLL2PFDFreq'):
                try:
                    ui.PLL2PFDFreq.textChanged.connect(self._on_pll2_pfd_freq_changed)
                    connected_signals += 1
                    logger.debug("✅ 已连接PLL2PFDFreq信号")
                except Exception as e:
                    logger.debug(f"❌ 连接PLL2PFDFreq信号失败: {str(e)}")
            else:
                logger.debug("❌ PLL2PFDFreq控件不存在")

            if hasattr(ui, 'PLL2RDivider'):
                try:
                    ui.PLL2RDivider.valueChanged.connect(self._on_pll2_r_divider_changed)
                    connected_signals += 1
                    logger.debug("✅ 已连接PLL2RDivider信号")
                except Exception as e:
                    logger.debug(f"❌ 连接PLL2RDivider信号失败: {str(e)}")
            else:
                logger.debug("❌ PLL2RDivider控件不存在")

            if hasattr(ui, 'PLL2NDivider'):
                try:
                    ui.PLL2NDivider.valueChanged.connect(self._on_pll2_n_divider_changed)
                    connected_signals += 1
                    logger.debug("✅ 已连接PLL2NDivider信号")
                except Exception as e:
                    logger.debug(f"❌ 连接PLL2NDivider信号失败: {str(e)}")
            else:
                logger.debug("❌ PLL2NDivider控件不存在")

            if hasattr(ui, 'PLL2PD'):
                try:
                    ui.PLL2PD.stateChanged.connect(self._on_pll2_power_changed)
                    connected_signals += 1
                    logger.debug("✅ 已连接PLL2PD信号")
                except Exception as e:
                    logger.debug(f"❌ 连接PLL2PD信号失败: {str(e)}")
            else:
                logger.debug("❌ PLL2PD控件不存在")

            # 标记信号已连接
            if connected_signals > 0:
                self._pll_signals_connected = True
                logger.debug(f"🎉 PLL控件信号连接完成，共连接 {connected_signals} 个信号")
            else:
                logger.debug("⚠️ 没有成功连接任何PLL控件信号")

        except Exception as e:
            logger.debug(f"连接PLL控件信号失败: {str(e)}")

    def _on_pll1_pfd_freq_changed(self, text):
        """PLL1 PFD频率变化处理"""
        try:
            self.pll1_pfd_freq_label.setText(f"{text} MHz" if text else "0.00 MHz")
            logger.debug(f"PLL1 PFD频率更新: {text}")
        except Exception as e:
            logger.debug(f"处理PLL1 PFD频率变化失败: {str(e)}")

    def _on_pll1_r_divider_changed(self, value):
        """PLL1 R Divider变化处理"""
        try:
            self.pll1_r_divider_label.setText(str(value))
            logger.debug(f"PLL1 R Divider更新: {value}")
        except Exception as e:
            logger.debug(f"处理PLL1 R Divider变化失败: {str(e)}")

    def _on_pll1_n_divider_changed(self, value):
        """PLL1 N Divider变化处理"""
        try:
            self.pll1_n_divider_label.setText(str(value))
            logger.debug(f"PLL1 N Divider更新: {value}")
        except Exception as e:
            logger.debug(f"处理PLL1 N Divider变化失败: {str(e)}")

    def _on_pll1_power_changed(self, state):
        """PLL1电源状态变化处理"""
        try:
            power_status = "关闭" if state else "开启"
            self.pll1_power_status_label.setText(power_status)
            self.pll1_power_status_label.setStyleSheet(
                "color: red;" if state else "color: green;"
            )
            logger.debug(f"PLL1电源状态更新: {power_status}")
        except Exception as e:
            logger.debug(f"处理PLL1电源状态变化失败: {str(e)}")

    def _on_pll2_pfd_freq_changed(self, text):
        """PLL2 PFD频率变化处理"""
        try:
            self.pll2_pfd_freq_label.setText(f"{text} MHz" if text else "0.00 MHz")
            logger.debug(f"PLL2 PFD频率更新: {text}")
        except Exception as e:
            logger.debug(f"处理PLL2 PFD频率变化失败: {str(e)}")

    def _on_pll2_r_divider_changed(self, value):
        """PLL2 R Divider变化处理"""
        try:
            self.pll2_r_divider_label.setText(str(value))
            logger.debug(f"PLL2 R Divider更新: {value}")
        except Exception as e:
            logger.debug(f"处理PLL2 R Divider变化失败: {str(e)}")

    def _on_pll2_n_divider_changed(self, value):
        """PLL2 N Divider变化处理"""
        try:
            self.pll2_n_divider_label.setText(str(value))
            logger.debug(f"PLL2 N Divider更新: {value}")
        except Exception as e:
            logger.debug(f"处理PLL2 N Divider变化失败: {str(e)}")

    def _on_pll2_power_changed(self, state):
        """PLL2电源状态变化处理"""
        try:
            power_status = "关闭" if state else "开启"
            self.pll2_power_status_label.setText(power_status)
            self.pll2_power_status_label.setStyleSheet(
                "color: red;" if state else "color: green;"
            )
            logger.debug(f"PLL2电源状态更新: {power_status}")
        except Exception as e:
            logger.debug(f"处理PLL2电源状态变化失败: {str(e)}")

    def _generate_pll1_status_message(self):
        """生成PLL1状态消息"""
        try:
            # 使用缓存的锁存数据
            lock_data = getattr(self, '_pll1_lock_data', {})
            is_locked = lock_data.get('is_locked', False)
            r_output = lock_data.get('r_divider_output', 0.0)
            n_output = lock_data.get('n_divider_output', 0.0)

            if is_locked:
                message = "PLL1 locked."
                self.pll1_status_text.setStyleSheet("""
                    QTextEdit {
                        background-color: #e8f5e8;
                        border: 1px solid #4caf50;
                        color: #2e7d32;
                        font-family: 'Consolas', 'Monaco', monospace;
                        font-size: 10pt;
                    }
                """)
            else:
                # 显示详细的计算信息
                if r_output > 0 and n_output > 0:
                    message = f"Output of R-divider = {r_output:.3f} MHz; Output of N-divider = {n_output:.3f} MHz; PLL1 not locked.\\nR-divider and N-divider output are different frequencies."
                else:
                    message = "PLL1 not locked.\\nR-divider and N-divider output are different frequencies."

                self.pll1_status_text.setStyleSheet("""
                    QTextEdit {
                        background-color: #ffeaea;
                        border: 1px solid #f44336;
                        color: #c62828;
                        font-family: 'Consolas', 'Monaco', monospace;
                        font-size: 10pt;
                    }
                """)

            self.pll1_status_text.setPlainText(message)

        except Exception as e:
            logger.debug(f"生成PLL1状态消息失败: {str(e)}")
            self.pll1_status_text.setPlainText("状态消息生成失败")

    def _generate_pll2_status_message(self):
        """生成PLL2状态消息"""
        try:
            # 使用缓存的锁存数据
            lock_data = getattr(self, '_pll2_lock_data', {})
            is_locked = lock_data.get('is_locked', False)
            r_output = lock_data.get('r_divider_output', 0.0)
            n_output = lock_data.get('n_divider_output', 0.0)

            if is_locked:
                message = "PLL2 locked."
                self.pll2_status_text.setStyleSheet("""
                    QTextEdit {
                        background-color: #e8f5e8;
                        border: 1px solid #4caf50;
                        color: #2e7d32;
                        font-family: 'Consolas', 'Monaco', monospace;
                        font-size: 10pt;
                    }
                """)
            else:
                # 显示详细的计算信息
                if r_output > 0 and n_output > 0:
                    message = f"Output of R-divider = {r_output:.3f} MHz; Output of N-divider = {n_output:.3f} MHz; PLL2 not locked.\\nR-divider and N-divider output are different frequencies."
                else:
                    message = "PLL2 not locked.\\nR-divider and N-divider output are different frequencies."

                self.pll2_status_text.setStyleSheet("""
                    QTextEdit {
                        background-color: #ffeaea;
                        border: 1px solid #f44336;
                        color: #c62828;
                        font-family: 'Consolas', 'Monaco', monospace;
                        font-size: 10pt;
                    }
                """)

            self.pll2_status_text.setPlainText(message)

        except Exception as e:
            logger.debug(f"生成PLL2状态消息失败: {str(e)}")
            self.pll2_status_text.setPlainText("状态消息生成失败")

    def _update_all_info(self):
        """更新所有信息"""
        if self._is_closing:
            return

        try:
            # 尝试连接PLL锁存状态信号（如果还没有连接）
            self._connect_pll_lock_signals()

            # 尝试连接PLL控件信号（如果还没有连接）
            self._connect_pll_widget_signals()

            # 尝试从缓存获取PLL状态
            self._update_pll_status_from_cache()

            # 强制刷新PLL状态显示
            self._force_refresh_pll_status()

            self._update_pll1_status()
            self._update_pll2_status()

            # 只有在启用实时更新时才更新调试日志
            if hasattr(self, 'auto_refresh_checkbox') and self.auto_refresh_checkbox.isChecked():
                self._update_debug_log()

            # 尝试触发PLL锁存状态计算
            self._trigger_pll_lock_calculation()

        except Exception as e:
            logger.error(f"更新信息时出错: {str(e)}")

    def _force_refresh_pll_status(self):
        """强制刷新PLL状态显示"""
        try:
            pll_handler = self._get_pll_handler()
            if pll_handler and hasattr(pll_handler, 'ui'):
                ui = pll_handler.ui

                # 强制读取并更新PLL1状态
                if hasattr(ui, 'PLL1PFDFreq'):
                    pfd_text = ui.PLL1PFDFreq.text()
                    self.pll1_pfd_freq_label.setText(f"{pfd_text} MHz" if pfd_text else "0.00 MHz")

                if hasattr(ui, 'PLL1RDividerSetting'):
                    r_div = ui.PLL1RDividerSetting.value()
                    self.pll1_r_divider_label.setText(str(r_div))

                if hasattr(ui, 'PLL1NDivider'):
                    n_div = ui.PLL1NDivider.value()
                    self.pll1_n_divider_label.setText(str(n_div))

                # 强制读取并更新PLL2状态
                if hasattr(ui, 'PLL2PFDFreq'):
                    pfd_text = ui.PLL2PFDFreq.text()
                    self.pll2_pfd_freq_label.setText(f"{pfd_text} MHz" if pfd_text else "0.00 MHz")

                if hasattr(ui, 'PLL2RDivider'):
                    r_div = ui.PLL2RDivider.value()
                    self.pll2_r_divider_label.setText(str(r_div))

                if hasattr(ui, 'PLL2NDivider'):
                    n_div = ui.PLL2NDivider.value()
                    self.pll2_n_divider_label.setText(str(n_div))

                logger.debug("已强制刷新PLL状态显示")
            else:
                logger.debug("无法获取PLL处理器，跳过强制刷新")

        except Exception as e:
            logger.debug(f"强制刷新PLL状态失败: {str(e)}")

    def _update_pll_status_from_cache(self):
        """从事件总线缓存更新PLL状态"""
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            bus = RegisterUpdateBus.instance()

            # 获取缓存的PLL1状态
            if hasattr(bus, 'get_cached_pll1_status'):
                pll1_status = bus.get_cached_pll1_status()
                if pll1_status:
                    logger.debug(f"从缓存获取PLL1状态: {pll1_status}")
                    self._update_pll1_display_from_cache(pll1_status)

            # 获取缓存的PLL2状态
            if hasattr(bus, 'get_cached_pll2_status'):
                pll2_status = bus.get_cached_pll2_status()
                if pll2_status:
                    logger.debug(f"从缓存获取PLL2状态: {pll2_status}")
                    self._update_pll2_display_from_cache(pll2_status)

        except Exception as e:
            logger.debug(f"从缓存更新PLL状态失败: {str(e)}")

    def _update_pll1_display_from_cache(self, pll1_status):
        """从缓存数据更新PLL1显示"""
        try:
            if 'pfd_freq' in pll1_status:
                self.pll1_pfd_freq_label.setText(f"{pll1_status['pfd_freq']:.3f} MHz")

            if 'r_divider' in pll1_status:
                self.pll1_r_divider_label.setText(str(pll1_status['r_divider']))

            if 'n_divider' in pll1_status:
                self.pll1_n_divider_label.setText(str(pll1_status['n_divider']))

            if 'power_status' in pll1_status:
                power_on = not pll1_status['power_status']  # PLL1_PD=0表示开启
                power_text = "开启" if power_on else "关闭"
                self.pll1_power_status_label.setText(power_text)
                self.pll1_power_status_label.setStyleSheet(
                    "color: green;" if power_on else "color: red;"
                )

            logger.debug("已从缓存更新PLL1显示")

        except Exception as e:
            logger.debug(f"从缓存更新PLL1显示失败: {str(e)}")

    def _update_pll2_display_from_cache(self, pll2_status):
        """从缓存数据更新PLL2显示"""
        try:
            if 'pfd_freq' in pll2_status:
                self.pll2_pfd_freq_label.setText(f"{pll2_status['pfd_freq']:.3f} MHz")

            if 'r_divider' in pll2_status:
                self.pll2_r_divider_label.setText(str(pll2_status['r_divider']))

            if 'n_divider' in pll2_status:
                self.pll2_n_divider_label.setText(str(pll2_status['n_divider']))

            if 'power_status' in pll2_status:
                power_on = not pll2_status['power_status']  # PLL2_PD=0表示开启
                power_text = "开启" if power_on else "关闭"
                self.pll2_power_status_label.setText(power_text)
                self.pll2_power_status_label.setStyleSheet(
                    "color: green;" if power_on else "color: red;"
                )

            logger.debug("已从缓存更新PLL2显示")

        except Exception as e:
            logger.debug(f"从缓存更新PLL2显示失败: {str(e)}")

    def _trigger_pll_lock_calculation(self):
        """触发PLL锁存状态计算"""
        try:
            pll_handler = self._get_pll_handler()
            if pll_handler:
                # 如果PLL处理器有计算方法，主动调用
                if hasattr(pll_handler, '_check_and_emit_pll_lock_status'):
                    pll_handler._check_and_emit_pll_lock_status()
                    logger.debug("已触发PLL锁存状态计算")
                elif hasattr(pll_handler, 'calculate_output_frequencies'):
                    # 计算输出频率通常会触发锁存状态检查
                    pll_handler.calculate_output_frequencies()
                    logger.debug("已触发PLL频率计算")
            else:
                # 如果无法获取PLL处理器，手动计算并显示状态
                self._manual_calculate_pll_status()

        except Exception as e:
            logger.debug(f"触发PLL锁存状态计算失败: {str(e)}")

    def _manual_calculate_pll_status(self):
        """手动计算PLL状态（当无法获取PLL处理器时）"""
        try:
            register_manager = self._get_register_manager()
            if not register_manager:
                return

            # 手动计算PLL1锁存状态
            pll1_locked, pll1_r_output, pll1_n_output = self._manual_calculate_pll1_lock()
            self._on_pll1_lock_status_changed(pll1_locked, pll1_r_output, pll1_n_output)

            # 手动计算PLL2锁存状态
            pll2_locked, pll2_r_output, pll2_n_output = self._manual_calculate_pll2_lock()
            self._on_pll2_lock_status_changed(pll2_locked, pll2_r_output, pll2_n_output)

            logger.debug("已完成手动PLL状态计算")

        except Exception as e:
            logger.debug(f"手动计算PLL状态失败: {str(e)}")

    def _manual_calculate_pll1_lock(self):
        """手动计算PLL1锁存状态"""
        try:
            register_manager = self._get_register_manager()
            if not register_manager:
                return False, 0.0, 0.0

            # 获取PLL1相关寄存器值
            # FreFin频率 - 从寄存器或默认值获取
            fre_fin_freq = 122.88  # 默认值

            # PLL1 R Divider - 根据当前时钟源从对应寄存器获取
            pll1_r_divider = 1  # 默认值
            try:
                # 尝试从不同的时钟源寄存器获取PLL1 R Divider值
                clkin_registers = [
                    ("0x63", "CLKin0_R[13:0]"),  # CLKin0
                    ("0x65", "CLKin1_R[13:0]"),  # CLKin1
                    ("0x67", "CLKin2_R[13:0]"),  # CLKin2
                ]

                for reg_addr, bit_field in clkin_registers:
                    try:
                        value = register_manager.get_bit_field_value(reg_addr, bit_field)
                        if value > 0:  # 找到非零值，使用它
                            pll1_r_divider = value
                            break
                    except Exception:
                        continue

                if pll1_r_divider == 0:
                    pll1_r_divider = 1  # 避免除零
            except Exception:
                pll1_r_divider = 1

            # PLL1 N Divider - 从寄存器0x69获取
            try:
                pll1_n_divider = register_manager.get_bit_field_value("0x69", "PLL1_N[13:0]")
                if pll1_n_divider == 0:
                    pll1_n_divider = 1  # 避免除零
            except Exception:
                pll1_n_divider = 1

            # 计算R DIVIDER输出
            r_divider_output = fre_fin_freq / pll1_r_divider

            # 计算N DIVIDER输出 (简化计算，假设使用OSCin作为输入)
            oscin_freq = 122.88  # 默认OSCin频率
            n_divider_output = oscin_freq / pll1_n_divider

            # 判断是否锁存（允许0.1%的误差）
            if r_divider_output > 0 and n_divider_output > 0:
                relative_error = abs(r_divider_output - n_divider_output) / max(r_divider_output, n_divider_output)
                is_locked = relative_error < 0.001  # 0.1%误差内认为锁存
            else:
                is_locked = False

            logger.debug(f"手动计算PLL1锁存状态: R={r_divider_output:.3f}, N={n_divider_output:.3f}, 锁存={is_locked}")
            return is_locked, r_divider_output, n_divider_output

        except Exception as e:
            logger.debug(f"手动计算PLL1锁存状态失败: {str(e)}")
            return False, 0.0, 0.0

    def _manual_calculate_pll2_lock(self):
        """手动计算PLL2锁存状态"""
        try:
            register_manager = self._get_register_manager()
            if not register_manager:
                return False, 0.0, 0.0

            # 获取PLL2相关寄存器值
            # PLL2 R Divider - 从寄存器0x75获取
            try:
                pll2_r_divider = register_manager.get_bit_field_value("0x75", "PLL2_R")
                if pll2_r_divider == 0:
                    pll2_r_divider = 1  # 避免除零
            except Exception:
                pll2_r_divider = 1

            # PLL2 N Divider - 从寄存器0x76和0x77组合获取
            try:
                pll2_n_high = register_manager.get_bit_field_value("0x76", "PLL2_N_17_16") or 0
                pll2_n_low = register_manager.get_bit_field_value("0x77", "PLL2_N_15_0") or 0
                pll2_n_divider = (pll2_n_high << 16) + pll2_n_low
                if pll2_n_divider == 0:
                    pll2_n_divider = 1  # 避免除零
            except Exception:
                pll2_n_divider = 1

            # 简化计算 - 假设使用OSCin作为输入
            oscin_freq = 122.88  # 默认OSCin频率

            # 计算R DIVIDER输出
            r_divider_output = oscin_freq / pll2_r_divider

            # 计算N DIVIDER输出
            n_divider_output = oscin_freq / pll2_n_divider

            # 判断是否锁存（允许0.1%的误差）
            if r_divider_output > 0 and n_divider_output > 0:
                relative_error = abs(r_divider_output - n_divider_output) / max(r_divider_output, n_divider_output)
                is_locked = relative_error < 0.001  # 0.1%误差内认为锁存
            else:
                is_locked = False

            logger.debug(f"手动计算PLL2锁存状态: R={r_divider_output:.3f}, N={n_divider_output:.3f}, 锁存={is_locked}")
            return is_locked, r_divider_output, n_divider_output

        except Exception as e:
            logger.debug(f"手动计算PLL2锁存状态失败: {str(e)}")
            return False, 0.0, 0.0

    def _update_pll1_status(self):
        """更新PLL1状态"""
        try:
            # 获取寄存器管理器
            register_manager = self._get_register_manager()
            if not register_manager:
                self._set_pll1_status_unknown()
                return

            # 读取PLL1电源状态 (0x50寄存器，PLL1_PD位)
            try:
                pll1_pd = register_manager.get_bit_field_value("0x50", "PLL1_PD")
                power_status = "关闭" if pll1_pd else "开启"
                self.pll1_power_status_label.setText(power_status)
                self.pll1_power_status_label.setStyleSheet(
                    "color: red;" if pll1_pd else "color: green;"
                )
            except Exception as e:
                logger.debug(f"读取PLL1电源状态失败: {str(e)}")
                self.pll1_power_status_label.setText("读取失败")
                self.pll1_power_status_label.setStyleSheet("color: gray;")

            # 获取PLL1频率信息
            self._update_pll1_frequencies()

        except Exception as e:
            logger.warning(f"更新PLL1状态失败: {str(e)}")
            self._set_pll1_status_unknown()

    def _update_pll2_status(self):
        """更新PLL2状态"""
        try:
            # 获取寄存器管理器
            register_manager = self._get_register_manager()
            if not register_manager:
                self._set_pll2_status_unknown()
                return

            # 读取PLL2电源状态 (0x83寄存器，PLL2_PD位)
            try:
                pll2_pd = register_manager.get_bit_field_value("0x83", "PLL2_PD")
                power_status = "关闭" if pll2_pd else "开启"
                self.pll2_power_status_label.setText(power_status)
                self.pll2_power_status_label.setStyleSheet(
                    "color: red;" if pll2_pd else "color: green;"
                )
            except Exception as e:
                logger.debug(f"读取PLL2电源状态失败: {str(e)}")
                self.pll2_power_status_label.setText("读取失败")
                self.pll2_power_status_label.setStyleSheet("color: gray;")

            # 获取PLL2频率信息
            self._update_pll2_frequencies()

        except Exception as e:
            logger.warning(f"更新PLL2状态失败: {str(e)}")
            self._set_pll2_status_unknown()

    def _get_register_manager(self):
        """获取寄存器管理器"""
        try:
            if self.main_window and hasattr(self.main_window, 'register_manager'):
                return self.main_window.register_manager
            return None
        except Exception as e:
            logger.debug(f"获取寄存器管理器失败: {str(e)}")
            return None

    def _get_pll_handler(self):
        """获取PLL处理器"""
        try:
            if not self.main_window:
                logger.debug("主窗口不存在")
                return None

            # 尝试多种可能的PLL窗口属性名
            pll_window_attrs = [
                'pll_control_window',  # 最可能的属性名
                'pll_window',
                'pll_handler',
                'modern_pll_handler'
            ]

            for attr_name in pll_window_attrs:
                if hasattr(self.main_window, attr_name):
                    pll_window = getattr(self.main_window, attr_name)
                    logger.debug(f"找到PLL窗口属性: {attr_name}, 值: {pll_window}")

                    if pll_window:
                        # 如果窗口本身就是处理器（ModernPLLHandler）
                        if hasattr(pll_window, 'pll1_lock_status_changed'):
                            logger.debug(f"找到PLL处理器: {type(pll_window).__name__}")
                            return pll_window

                        # 如果窗口有handler属性
                        if hasattr(pll_window, 'handler'):
                            handler = pll_window.handler
                            if handler and hasattr(handler, 'pll1_lock_status_changed'):
                                logger.debug(f"找到PLL处理器(通过handler): {type(handler).__name__}")
                                return handler

            # 如果直接属性查找失败，尝试通过子对象查找
            all_children = self.main_window.findChildren(object)
            for child in all_children:
                if hasattr(child, 'pll1_lock_status_changed') and hasattr(child, 'pll2_lock_status_changed'):
                    logger.debug(f"通过子对象找到PLL处理器: {type(child).__name__}")
                    return child

            logger.debug("未找到PLL处理器")
            return None

        except Exception as e:
            logger.debug(f"获取PLL处理器失败: {str(e)}")
            return None

    def _update_pll1_frequencies(self):
        """更新PLL1频率信息"""
        try:
            pll_handler = self._get_pll_handler()
            if pll_handler and hasattr(pll_handler, 'ui'):
                # 获取PLL1 PFD频率
                if hasattr(pll_handler.ui, 'PLL1PFDFreq'):
                    pfd_text = pll_handler.ui.PLL1PFDFreq.text()
                    self.pll1_pfd_freq_label.setText(f"{pfd_text} MHz" if pfd_text else "0.00 MHz")

                # 获取PLL1 R Divider
                if hasattr(pll_handler.ui, 'PLL1RDividerSetting'):
                    r_div = pll_handler.ui.PLL1RDividerSetting.value()
                    self.pll1_r_divider_label.setText(str(r_div))

                # 获取PLL1 N Divider
                if hasattr(pll_handler.ui, 'PLL1NDivider'):
                    n_div = pll_handler.ui.PLL1NDivider.value()
                    self.pll1_n_divider_label.setText(str(n_div))
            else:
                self.pll1_pfd_freq_label.setText("PLL窗口未打开")
                self.pll1_r_divider_label.setText("未知")
                self.pll1_n_divider_label.setText("未知")
        except Exception as e:
            logger.debug(f"更新PLL1频率信息失败: {str(e)}")
            self.pll1_pfd_freq_label.setText("获取失败")
            self.pll1_r_divider_label.setText("获取失败")
            self.pll1_n_divider_label.setText("获取失败")

    def _update_pll2_frequencies(self):
        """更新PLL2频率信息"""
        try:
            pll_handler = self._get_pll_handler()
            if pll_handler and hasattr(pll_handler, 'ui'):
                # 获取PLL2 PFD频率
                if hasattr(pll_handler.ui, 'PLL2PFDFreq'):
                    pfd_text = pll_handler.ui.PLL2PFDFreq.text()
                    self.pll2_pfd_freq_label.setText(f"{pfd_text} MHz" if pfd_text else "0.00 MHz")

                # 获取PLL2 R Divider - 正确的控件名称是PLL2RDivider
                if hasattr(pll_handler.ui, 'PLL2RDivider'):
                    r_div = pll_handler.ui.PLL2RDivider.value()
                    self.pll2_r_divider_label.setText(str(r_div))

                # 获取PLL2 N Divider - 使用统一的PLL2NDivider控件
                if hasattr(pll_handler.ui, 'PLL2NDivider'):
                    n_div = pll_handler.ui.PLL2NDivider.value()
                    self.pll2_n_divider_label.setText(str(n_div))
            else:
                self.pll2_pfd_freq_label.setText("PLL窗口未打开")
                self.pll2_r_divider_label.setText("未知")
                self.pll2_n_divider_label.setText("未知")
        except Exception as e:
            logger.debug(f"更新PLL2频率信息失败: {str(e)}")
            self.pll2_pfd_freq_label.setText("获取失败")
            self.pll2_r_divider_label.setText("获取失败")
            self.pll2_n_divider_label.setText("获取失败")

    def _set_pll1_status_unknown(self):
        """设置PLL1状态为未知"""
        self.pll1_lock_status_label.setText("未知")
        self.pll1_lock_status_label.setStyleSheet("color: gray;")
        self.pll1_pfd_freq_label.setText("未知")
        self.pll1_n_divider_label.setText("未知")
        self.pll1_r_divider_label.setText("未知")
        self.pll1_power_status_label.setText("未知")
        self.pll1_power_status_label.setStyleSheet("color: gray;")
        self.pll1_status_text.setPlainText("无法获取PLL1状态信息")
        self.pll1_status_text.setStyleSheet("""
            QTextEdit {
                background-color: #f5f5f5;
                border: 1px solid #ccc;
                color: #666;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10pt;
            }
        """)

    def _set_pll2_status_unknown(self):
        """设置PLL2状态为未知"""
        self.pll2_lock_status_label.setText("未知")
        self.pll2_lock_status_label.setStyleSheet("color: gray;")
        self.pll2_pfd_freq_label.setText("未知")
        self.pll2_n_divider_label.setText("未知")
        self.pll2_r_divider_label.setText("未知")
        self.pll2_power_status_label.setText("未知")
        self.pll2_power_status_label.setStyleSheet("color: gray;")
        self.pll2_status_text.setPlainText("无法获取PLL2状态信息")
        self.pll2_status_text.setStyleSheet("""
            QTextEdit {
                background-color: #f5f5f5;
                border: 1px solid #ccc;
                color: #666;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10pt;
            }
        """)

    def _update_debug_log(self):
        """更新调试日志"""
        try:
            log_file_path = "log/app.log"
            if os.path.exists(log_file_path):
                with codecs.open(log_file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                    # 根据选择的日志级别过滤日志
                    filtered_lines = self._filter_log_lines_by_level(lines)

                    # 只显示最后100行
                    recent_lines = filtered_lines[-100:] if len(filtered_lines) > 100 else filtered_lines
                    log_content = ''.join(recent_lines)

                    self.log_text.setPlainText(log_content)

                    # 滚动到底部
                    cursor = self.log_text.textCursor()
                    cursor.movePosition(QTextCursor.End)
                    self.log_text.setTextCursor(cursor)
            else:
                self.log_text.setPlainText("日志文件不存在")
        except Exception as e:
            logger.debug(f"更新调试日志失败: {str(e)}")
            self.log_text.setPlainText(f"读取日志失败: {str(e)}")

    def _filter_log_lines_by_level(self, lines):
        """根据选择的日志级别过滤日志行"""
        try:
            if not hasattr(self, 'log_level_combo'):
                logger.debug("信息面板: log_level_combo 不存在，返回原始日志")
                return lines

            selected_level = self.log_level_combo.currentData()
            if not selected_level:
                logger.debug("信息面板: 未选择日志级别，返回原始日志")
                return lines

            logger.debug(f"信息面板: 开始过滤日志，选择级别: {selected_level}, 原始行数: {len(lines)}")

            # 定义日志级别优先级
            level_priority = {
                'DEBUG': 0,
                'INFO': 1,
                'WARNING': 2,
                'ERROR': 3,
                'CRITICAL': 4
            }

            selected_priority = level_priority.get(selected_level, 0)
            filtered_lines = []

            for line in lines:
                # 检查行中是否包含日志级别
                line_priority = -1
                for level, priority in level_priority.items():
                    if f" - {level} - " in line:
                        line_priority = priority
                        break

                # 如果找到了日志级别且优先级足够高，或者没有找到日志级别（保留非标准格式的行）
                if line_priority == -1 or line_priority >= selected_priority:
                    filtered_lines.append(line)

            logger.debug(f"信息面板: 过滤完成，过滤后行数: {len(filtered_lines)}")
            return filtered_lines

        except Exception as e:
            logger.debug(f"过滤日志行失败: {str(e)}")
            return lines

    def _clear_log(self):
        """清空日志显示"""
        self.log_text.clear()
        logger.info("日志显示已清空")

    def _on_log_level_changed(self):
        """日志级别选择变化时的处理"""
        try:
            selected_level = self.log_level_combo.currentData()
            selected_text = self.log_level_combo.currentText()
            logger.info(f"信息面板日志级别已切换到: {selected_level} ({selected_text})")
            # 立即刷新日志显示
            self._update_debug_log()
        except Exception as e:
            logger.error(f"处理日志级别变化失败: {str(e)}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")

    def _on_auto_refresh_toggled(self, checked):
        """实时更新开关切换时的处理"""
        try:
            if checked:
                logger.info("信息面板日志实时更新已启用")
            else:
                logger.info("信息面板日志实时更新已禁用")
        except Exception as e:
            logger.error(f"处理实时更新开关失败: {str(e)}")

    def _apply_log_level_to_system(self):
        """将选择的日志级别应用到整个系统"""
        try:
            selected_level = self.log_level_combo.currentData()
            if not selected_level:
                logger.warning("未选择有效的日志级别")
                return

            # 使用日志控制工具应用级别
            try:
                from utils.Log import log_manager
                if hasattr(log_manager, 'set_log_level'):
                    log_manager.set_log_level(selected_level)
                    logger.info(f"✅ 系统日志级别已应用: {selected_level}")

                    # 更新配置文件
                    self._update_log_config_file(selected_level)

                    # 显示成功提示
                    self._show_level_applied_message(selected_level)
                else:
                    logger.error("日志管理器不支持动态设置级别")
            except ImportError:
                logger.error("无法导入日志管理器")

        except Exception as e:
            logger.error(f"应用日志级别到系统失败: {str(e)}")

    def _update_log_config_file(self, level):
        """更新配置文件中的日志级别"""
        try:
            import json
            from pathlib import Path

            config_file = Path("config/default.json")
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                data.setdefault('logging', {})['level'] = level

                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)

                logger.info(f"配置文件已更新: {level}")

        except Exception as e:
            logger.error(f"更新配置文件失败: {str(e)}")

    def _show_level_applied_message(self, level):
        """显示级别应用成功的消息"""
        try:
            level_descriptions = {
                'DEBUG': '🔍 DEBUG - 显示所有日志（最详细）',
                'INFO': 'ℹ️ INFO - 显示信息、警告和错误',
                'WARNING': '⚠️ WARNING - 显示警告和错误（推荐）',
                'ERROR': '🔴 ERROR - 只显示错误（最少）'
            }

            description = level_descriptions.get(level, level)
            logger.info(f"🎯 系统日志级别已设置为: {description}")

        except Exception as e:
            logger.debug(f"显示级别应用消息失败: {str(e)}")

    def _test_log_filter(self):
        """测试日志过滤功能"""
        try:
            selected_level = self.log_level_combo.currentData()
            selected_text = self.log_level_combo.currentText()

            logger.info(f"🧪 开始测试日志过滤功能，当前级别: {selected_level}")

            # 生成测试日志
            from utils.Log import get_module_logger
            test_logger = get_module_logger("InfoPanelTest")

            test_logger.debug("🔍 测试DEBUG级别日志")
            test_logger.info("ℹ️ 测试INFO级别日志")
            test_logger.warning("⚠️ 测试WARNING级别日志")
            test_logger.error("🔴 测试ERROR级别日志")

            # 强制刷新日志显示
            self._update_debug_log()

            logger.info(f"✅ 日志过滤测试完成，当前显示级别: {selected_text}")

        except Exception as e:
            logger.error(f"测试日志过滤失败: {str(e)}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")

    def _clear_widget_info(self):
        """清空控件信息"""
        self.widget_info_text.setPlainText("等待鼠标悬停在控件上...")
        logger.info("控件信息已清空")

    def _setup_widget_info_listener(self):
        """设置控件信息监听器"""
        try:
            # 安装全局事件过滤器来监听鼠标悬停事件
            from PyQt5.QtWidgets import QApplication

            # 创建事件过滤器
            self.event_filter = WidgetInfoEventFilter(self)
            QApplication.instance().installEventFilter(self.event_filter)

            logger.debug("控件信息监听器已设置")

        except Exception as e:
            logger.warning(f"设置控件信息监听器失败: {str(e)}")

    def show_widget_info(self, widget):
        """显示控件的寄存器信息"""
        try:
            if not widget:
                logger.debug("控件为空，跳过显示")
                return

            widget_name = widget.objectName()
            if not widget_name:
                logger.debug(f"控件无名称，类型: {type(widget).__name__}")
                return

            logger.debug(f"处理控件: {widget_name} (类型: {type(widget).__name__})")

            # 获取寄存器管理器
            register_manager = self._get_register_manager()
            if not register_manager:
                logger.debug("无法获取寄存器管理器")
                return

            # 查找控件对应的寄存器信息
            widget_info = self._find_widget_register_info(widget_name, register_manager)

            if widget_info:
                info_text = self._format_widget_info(widget_name, widget_info)
                self.widget_info_text.setPlainText(info_text)
                logger.debug(f"显示控件 {widget_name} 的寄存器信息")
            else:
                # 如果没有找到寄存器信息，不显示任何信息（避免显示无用信息）
                logger.debug(f"控件 {widget_name} 未找到寄存器映射，不显示信息")

        except Exception as e:
            logger.warning(f"显示控件信息失败: {str(e)}")
            # 发生错误时也不显示错误信息给用户，只记录日志

    def clear_widget_info(self):
        """清空控件信息显示"""
        try:
            self.widget_info_text.clear()
            logger.debug("已清空控件信息显示")
        except Exception as e:
            logger.debug(f"清空控件信息显示失败: {str(e)}")

    def _find_widget_register_info(self, widget_name, register_manager):
        """查找控件对应的寄存器信息"""
        try:
            # 首先尝试从手动说明文件中查找
            manual_info = self._find_manual_widget_info(widget_name)
            if manual_info:
                return manual_info

            # 如果手动说明文件中没有，再从寄存器映射中查找
            for addr in register_manager.register_objects.keys():
                widget_map = register_manager.get_widget_register_mapping(addr)
                if widget_name in widget_map:
                    return widget_map[widget_name]
            return None
        except Exception as e:
            logger.debug(f"查找控件寄存器信息失败: {str(e)}")
            return None

    def _format_widget_info(self, widget_name, widget_info):
        """格式化控件信息"""
        try:
            # 检查是否为手动说明信息
            if widget_info.get('is_manual_info', False):
                return self._format_manual_widget_info(widget_name, widget_info)

            # 原有的寄存器信息格式化逻辑
            register_addr = widget_info.get('register_addr', '未知')
            bit_def_name = widget_info.get('bit_def_name', '未知')
            description = widget_info.get('description', '无描述')

            # 获取位字段信息
            bit_def = widget_info.get('bit_def', {})
            bit_range = bit_def.get('bit', '未知')

            # 解析位范围
            start_bit, stop_bit, length = self._parse_bit_range(bit_range)

            # 转换寄存器地址为寄存器编号
            register_number = self._convert_addr_to_register_number(register_addr)

            info_text = f"""Field Name: {bit_def_name}

Register Address: {register_addr}
Register Number: {register_number}
Start Bit: {start_bit}
Stop Bit : {stop_bit}
Length   : {length}

Description: {description}"""

            return info_text

        except Exception as e:
            logger.debug(f"格式化控件信息失败: {str(e)}")
            return f"控件: {widget_name}\\n格式化信息时出错: {str(e)}"

    def _convert_addr_to_register_number(self, register_addr):
        """将寄存器地址转换为寄存器编号"""
        try:
            if register_addr == '未知':
                return '未知'

            # 解析十六进制地址
            if isinstance(register_addr, str) and register_addr.startswith('0x'):
                addr_num = int(register_addr, 16)
                return f"R{addr_num}"
            elif isinstance(register_addr, str):
                # 尝试直接解析为十六进制
                addr_num = int(register_addr, 16)
                return f"R{addr_num}"
            elif isinstance(register_addr, int):
                return f"R{register_addr}"
            else:
                return str(register_addr)

        except Exception as e:
            logger.debug(f"转换寄存器地址失败: {register_addr}, 错误: {str(e)}")
            return str(register_addr)

    def _find_manual_widget_info(self, widget_name):
        """从手动说明文件中查找控件信息"""
        try:
            manual_info = self._load_manual_widget_descriptions()
            return manual_info.get(widget_name)
        except Exception as e:
            logger.debug(f"查找手动控件信息失败: {str(e)}")
            return None

    def _load_manual_widget_descriptions(self):
        """加载手动控件说明配置文件"""
        try:
            # 如果已经加载过，直接返回缓存的数据
            if hasattr(self, '_manual_widget_cache'):
                return self._manual_widget_cache

            # 获取配置文件路径
            config_path = self._get_manual_widget_config_path()
            if not config_path or not os.path.exists(config_path):
                logger.debug("手动控件说明配置文件不存在")
                self._manual_widget_cache = {}
                return self._manual_widget_cache

            # 读取配置文件
            import json
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            # 缓存数据
            self._manual_widget_cache = config_data.get('widgets', {})
            logger.info(f"成功加载手动控件说明配置，包含 {len(self._manual_widget_cache)} 个控件")
            return self._manual_widget_cache

        except Exception as e:
            logger.warning(f"加载手动控件说明配置失败: {str(e)}")
            self._manual_widget_cache = {}
            return self._manual_widget_cache

    def _get_manual_widget_config_path(self):
        """获取手动控件说明配置文件路径"""
        try:
            # 尝试多个可能的路径
            possible_paths = [
                'lib/widget_descriptions.json',
                os.path.join(os.path.dirname(__file__), '../lib/widget_descriptions.json'),
                os.path.join(os.path.dirname(__file__), '../../lib/widget_descriptions.json')
            ]

            for path in possible_paths:
                if os.path.exists(path):
                    return path

            return None
        except Exception as e:
            logger.debug(f"获取手动控件说明配置文件路径失败: {str(e)}")
            return None

    def _format_manual_widget_info(self, widget_name, widget_info):
        """格式化手动控件信息"""
        try:
            widget_type = widget_info.get('type', '未知')
            source = widget_info.get('source', '未知')
            description = widget_info.get('description', '无描述')
            calculation = widget_info.get('calculation', '')
            dependencies = widget_info.get('dependencies', [])

            info_text = f"""Widget Name: {widget_name}

Widget Type: {widget_type}
Data Source: {source}

Description: {description}"""

            if calculation:
                info_text += f"\n\nCalculation: {calculation}"

            if dependencies:
                deps_str = ', '.join(dependencies)
                info_text += f"\n\nDependencies: {deps_str}"

            info_text += "\n\n[手动配置的控件说明]"

            return info_text

        except Exception as e:
            logger.debug(f"格式化手动控件信息失败: {str(e)}")
            return f"控件: {widget_name}\\n格式化手动信息时出错: {str(e)}"

    def reload_manual_widget_descriptions(self):
        """重新加载手动控件说明配置（清除缓存后重新加载）"""
        try:
            if hasattr(self, '_manual_widget_cache'):
                delattr(self, '_manual_widget_cache')
            self._load_manual_widget_descriptions()
            logger.info("手动控件说明配置已重新加载")
        except Exception as e:
            logger.warning(f"重新加载手动控件说明配置失败: {str(e)}")

    def _parse_bit_range(self, bit_range):
        """解析位范围字符串"""
        try:
            if ':' in bit_range:
                # 范围格式，如 "15:8"
                parts = bit_range.split(':')
                start_bit = int(parts[0])
                stop_bit = int(parts[1])
                length = abs(start_bit - stop_bit) + 1
            else:
                # 单个位，如 "7"
                start_bit = stop_bit = int(bit_range)
                length = 1

            return start_bit, stop_bit, length

        except Exception as e:
            logger.debug(f"解析位范围失败: {str(e)}")
            return "未知", "未知", "未知"

    def _on_window_close(self, event):
        """窗口关闭事件处理"""
        try:
            self._is_closing = True

            # 停止定时器
            if hasattr(self, 'update_timer') and self.update_timer:
                self.update_timer.stop()
                logger.debug("信息面板更新定时器已停止")

            # 移除事件过滤器
            if hasattr(self, 'event_filter') and self.event_filter:
                try:
                    from PyQt5.QtWidgets import QApplication
                    QApplication.instance().removeEventFilter(self.event_filter)
                    logger.debug("信息面板事件过滤器已移除")
                except Exception as e:
                    logger.debug(f"移除事件过滤器失败: {str(e)}")

            event.accept()
            logger.info("信息面板窗口已关闭")

        except Exception as e:
            logger.error(f"关闭信息面板窗口时出错: {str(e)}")
            event.accept()


class WidgetInfoEventFilter(QObject):
    """控件信息事件过滤器"""

    def __init__(self, info_panel):
        super().__init__()
        self.info_panel = info_panel
        self.last_widget = None
        self.clear_timer = None  # 用于管理清空定时器

    def _is_useful_widget(self, widget):
        """判断是否是有用的控件（需要显示信息的控件）"""
        from PyQt5.QtWidgets import QWidget

        if not widget or not isinstance(widget, QWidget):
            return False

        widget_name = widget.objectName()
        if not widget_name:
            return False

        # 检查是否是我们关心的控件类型
        from PyQt5.QtWidgets import QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox, QCheckBox

        # 只对特定类型的控件显示信息
        useful_types = (QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox, QCheckBox)
        if not isinstance(widget, useful_types):
            return False

        # 可以添加更多过滤条件，比如控件名称模式
        return True

    def eventFilter(self, obj, event):
        """事件过滤器"""
        try:
            from PyQt5.QtWidgets import QWidget
            from PyQt5.QtCore import QEvent, QTimer

            # 处理鼠标进入事件
            if event.type() == QEvent.Enter and isinstance(obj, QWidget):
                # 避免重复处理同一个控件
                if obj != self.last_widget:
                    if self._is_useful_widget(obj):  # 有用的控件，显示信息
                        # 取消任何待执行的清空定时器
                        if self.clear_timer:
                            self.clear_timer.stop()
                            self.clear_timer = None

                        self.last_widget = obj
                        widget_name = obj.objectName()
                        logger.debug(f"鼠标进入有用控件: {widget_name}")
                        # 延迟显示信息，避免频繁更新
                        QTimer.singleShot(100, lambda: self.info_panel.show_widget_info(obj))
                    else:
                        # 鼠标进入无用控件（如空白区域、按钮等），清空信息显示
                        logger.debug("鼠标进入无用控件，清空信息显示")
                        # 取消任何待执行的清空定时器
                        if self.clear_timer:
                            self.clear_timer.stop()
                            self.clear_timer = None
                        QTimer.singleShot(50, self.info_panel.clear_widget_info)
                        self.last_widget = None

            # 处理鼠标离开事件
            elif event.type() == QEvent.Leave and isinstance(obj, QWidget):
                # 只有当离开的是有用控件时才延迟清空
                if obj == self.last_widget and self._is_useful_widget(obj):
                    logger.debug(f"鼠标离开有用控件: {obj.objectName()}")
                    # 创建新的清空定时器
                    self.clear_timer = QTimer()
                    self.clear_timer.setSingleShot(True)
                    self.clear_timer.timeout.connect(self.info_panel.clear_widget_info)
                    self.clear_timer.start(1000)  # 1秒后清空

            # 处理鼠标点击事件（点击任何区域）
            elif event.type() == QEvent.MouseButtonPress:
                # 如果点击的不是有用控件，清空信息显示
                if isinstance(obj, QWidget) and not self._is_useful_widget(obj):
                    logger.debug("点击无用区域，清空信息显示")
                    # 取消任何待执行的清空定时器
                    if self.clear_timer:
                        self.clear_timer.stop()
                        self.clear_timer = None
                    self.info_panel.clear_widget_info()
                    self.last_widget = None

            return False  # 不拦截事件，让其继续传播

        except Exception as e:
            # 记录异常但不影响正常事件处理
            logger.debug(f"事件过滤器异常: {str(e)}")
            return False


# 插件入口点
plugin_class = InfoPanelPlugin
