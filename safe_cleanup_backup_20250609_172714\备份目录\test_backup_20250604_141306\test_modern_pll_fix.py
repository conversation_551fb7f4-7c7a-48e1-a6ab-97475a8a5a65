#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试现代化PLL处理器的修复
验证控件初始化和跨寄存器控件功能
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication, QMainWindow
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt


def test_modern_pll_handler():
    """测试现代化PLL处理器"""
    print("=" * 60)
    print("测试现代化PLL处理器修复")
    print("=" * 60)

    try:
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        # 1. 测试现代化处理器创建
        print("1. 创建现代化PLL处理器...")
        
        # 创建寄存器管理器
        from core.services.register.RegisterManager import RegisterManager
        config_path = os.path.join(project_root, 'lib', 'register.json')
        if not os.path.exists(config_path):
            print(f"❌ 寄存器配置文件不存在: {config_path}")
            return False

        import json
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)

        register_manager = RegisterManager(registers_config)

        # 创建现代化PLL处理器
        from ui.handlers.ModernPLLHandler import ModernPLLHandler
        modern_handler = ModernPLLHandler(None, register_manager)
        
        print("✓ 现代化PLL处理器创建成功")
        print(f"  - 类型: {type(modern_handler).__name__}")
        print(f"  - 窗口标题: {modern_handler.windowTitle()}")

        # 2. 检查控件映射
        print("\n2. 检查控件映射...")
        widget_count = len(modern_handler.widget_register_map)
        print(f"✓ 控件映射数量: {widget_count}")
        
        # 检查关键控件是否已映射
        key_widgets = [
            'PLL1PD', 'PLL2PD', 'PLL2NDivider', 'PLL1RDividerSetting', 
            'PLL2RDivider', 'PLL2R3', 'comboPLL1WindSize', 'PLL1CPGain'
        ]
        
        mapped_widgets = []
        unmapped_widgets = []
        
        for widget_name in key_widgets:
            if widget_name in modern_handler.widget_register_map:
                mapped_widgets.append(widget_name)
                widget_info = modern_handler.widget_register_map[widget_name]
                print(f"  ✓ {widget_name}: 寄存器={widget_info['register_addr']}, 类型={widget_info['widget_type']}")
            else:
                unmapped_widgets.append(widget_name)
                print(f"  ❌ {widget_name}: 未映射")

        print(f"\n映射统计: {len(mapped_widgets)}/{len(key_widgets)} 个关键控件已映射")

        # 3. 检查跨寄存器控件
        print("\n3. 检查跨寄存器控件...")
        if 'PLL2NDivider' in modern_handler.widget_register_map:
            pll2n_info = modern_handler.widget_register_map['PLL2NDivider']
            bit_def = pll2n_info.get('bit_def', {})
            if bit_def.get('is_cross_register'):
                print("✓ PLL2NDivider已注册为跨寄存器控件")
                print(f"  - 关联寄存器: {bit_def.get('registers', [])}")
                print(f"  - 位字段: {bit_def.get('bits', [])}")
                print(f"  - 范围: {bit_def.get('options', 'N/A')}")
            else:
                print("❌ PLL2NDivider未正确注册为跨寄存器控件")
        else:
            print("❌ PLL2NDivider未在控件映射中找到")

        # 4. 检查控件范围设置
        print("\n4. 检查控件范围设置...")
        if hasattr(modern_handler.ui, "PLL2NDivider"):
            min_val = modern_handler.ui.PLL2NDivider.minimum()
            max_val = modern_handler.ui.PLL2NDivider.maximum()
            current_val = modern_handler.ui.PLL2NDivider.value()
            print(f"✓ PLL2NDivider范围: {min_val} - {max_val}, 当前值: {current_val}")
        else:
            print("❌ PLL2NDivider控件不存在")

        if hasattr(modern_handler.ui, "PLL1RDividerSetting"):
            min_val = modern_handler.ui.PLL1RDividerSetting.minimum()
            max_val = modern_handler.ui.PLL1RDividerSetting.maximum()
            current_val = modern_handler.ui.PLL1RDividerSetting.value()
            print(f"✓ PLL1RDividerSetting范围: {min_val} - {max_val}, 当前值: {current_val}")
        else:
            print("❌ PLL1RDividerSetting控件不存在")

        # 5. 检查ComboBox控件初始化
        print("\n5. 检查ComboBox控件初始化...")
        combo_widgets = ['PLL2R3', 'comboPLL1WindSize', 'PLL1CPGain', 'PLL2CPGain']
        
        for widget_name in combo_widgets:
            if hasattr(modern_handler.ui, widget_name):
                widget = getattr(modern_handler.ui, widget_name)
                if hasattr(widget, 'count'):
                    item_count = widget.count()
                    current_index = widget.currentIndex()
                    current_text = widget.currentText()
                    print(f"✓ {widget_name}: {item_count}个选项, 当前选择: {current_index} ({current_text})")
                else:
                    print(f"❌ {widget_name}: 不是ComboBox控件")
            else:
                print(f"❌ {widget_name}: 控件不存在")

        # 6. 测试频率计算
        print("\n6. 测试频率计算...")
        try:
            modern_handler.calculate_output_frequencies()
            print("✓ 频率计算执行成功")

            # 检查频率显示控件
            freq_widgets = ['PLL1PFDFreq', 'PLL2PFDFreq', 'Fin0Freq']
            all_freq_ok = True

            for widget_name in freq_widgets:
                if hasattr(modern_handler.ui, widget_name):
                    widget = getattr(modern_handler.ui, widget_name)
                    if hasattr(widget, 'text'):
                        freq_text = widget.text()
                        if freq_text and freq_text != "0.00" and freq_text != "Error":
                            print(f"  ✓ {widget_name}: {freq_text}")
                        else:
                            print(f"  ❌ {widget_name}: {freq_text} (无效值)")
                            all_freq_ok = False
                    else:
                        print(f"  ❌ {widget_name}: 不是文本控件")
                        all_freq_ok = False
                else:
                    print(f"  ❌ {widget_name}: 控件不存在")
                    all_freq_ok = False

            # 检查其他计算控件
            print("\n  检查其他计算控件:")
            other_calc_widgets = ['OSCinFreq', 'FreFin', 'ExternalVCXOFreq']
            for widget_name in other_calc_widgets:
                if hasattr(modern_handler.ui, widget_name):
                    widget = getattr(modern_handler.ui, widget_name)
                    if hasattr(widget, 'text'):
                        text_value = widget.text()
                        if text_value:
                            print(f"  ✓ {widget_name}: {text_value}")
                        else:
                            print(f"  ⚠️  {widget_name}: 空值")
                    else:
                        print(f"  ❌ {widget_name}: 不是文本控件")
                else:
                    print(f"  ❌ {widget_name}: 控件不存在")

            if all_freq_ok:
                print("  🎉 所有频率计算控件都有正确的值！")
            else:
                print("  ⚠️  部分频率计算控件值异常")

        except Exception as e:
            print(f"❌ 频率计算失败: {str(e)}")

        # 7. 测试PLL2NDivider更新
        print("\n7. 测试PLL2NDivider更新...")
        try:
            if hasattr(modern_handler.ui, "PLL2NDivider"):
                # 测试设置一个新值
                test_value = 100
                modern_handler.ui.PLL2NDivider.setValue(test_value)
                print(f"✓ 设置PLL2NDivider值为: {test_value}")
                
                # 测试跨寄存器更新
                modern_handler._update_pll2n_divider(test_value)
                print("✓ PLL2NDivider跨寄存器更新测试完成")
            else:
                print("❌ PLL2NDivider控件不存在，无法测试")
        except Exception as e:
            print(f"❌ PLL2NDivider更新测试失败: {str(e)}")

        print("\n" + "=" * 60)
        print("测试总结:")
        print(f"✓ 控件映射: {len(mapped_widgets)}/{len(key_widgets)} 个关键控件")
        print(f"✓ 总映射数量: {widget_count} 个控件")
        
        if len(unmapped_widgets) == 0:
            print("🎉 所有关键控件都已正确映射！")
            return True
        else:
            print(f"⚠️  还有 {len(unmapped_widgets)} 个控件未映射: {', '.join(unmapped_widgets)}")
            return False

    except Exception as e:
        print(f"❌ 测试过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_modern_pll_handler()
    
    if success:
        print("\n🎉 现代化PLL处理器修复测试通过！")
        sys.exit(0)
    else:
        print("\n❌ 现代化PLL处理器修复测试失败！")
        sys.exit(1)
