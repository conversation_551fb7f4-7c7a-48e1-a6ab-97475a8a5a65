#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试基于时钟源选择的PLL计算系统
验证不同MUX设置下的时钟源选择和计算
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from ui.handlers.ModernPLLHandler import ModernPLLHandler
from ui.handlers.ModernSetModesHandler import ModernSetModesHandler
from core.RegisterManager import RegisterManager
from utils.Log import get_module_logger

logger = get_module_logger(__name__)

def test_clock_source_selection():
    """测试时钟源选择功能"""
    print("="*60)
    print("测试基于时钟源选择的PLL计算系统")
    print("="*60)
    
    app = QApplication(sys.argv)
    
    try:
        # 创建RegisterManager实例
        register_manager = RegisterManager()
        
        # 创建处理器实例
        modes_handler = ModernSetModesHandler(register_manager=register_manager)
        pll_handler = ModernPLLHandler(register_manager=register_manager)
        
        # 连接信号
        modes_handler.mode_changed.connect(pll_handler.on_mode_changed_from_handler)
        
        # 设置测试频率
        if hasattr(pll_handler.ui, "FreFin"):
            pll_handler.ui.FreFin.setText("122.88")
        if hasattr(pll_handler.ui, "OSCinFreq"):
            pll_handler.ui.OSCinFreq.setText("122.88")
        
        print("\n--- 测试1: 默认时钟源选择 ---")
        test_default_clock_sources(pll_handler)
        
        print("\n--- 测试2: 不同模式下的时钟源选择 ---")
        test_mode_based_clock_sources(modes_handler, pll_handler)
        
        print("\n--- 测试3: MUX控件值对时钟源的影响 ---")
        test_mux_control_effects(register_manager, pll_handler)
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        app.quit()

def test_default_clock_sources(pll_handler):
    """测试默认时钟源选择"""
    try:
        print("测试默认时钟源选择...")
        
        # 获取PLL1时钟源
        pll1_source, pll1_freq = pll_handler._get_pll1_clock_source()
        print(f"PLL1时钟源: {pll1_source}, 频率: {pll1_freq} MHz")
        
        # 获取PLL2时钟源
        pll2_source, pll2_freq = pll_handler._get_pll2_clock_source(0.0)
        print(f"PLL2时钟源: {pll2_source}, 频率: {pll2_freq} MHz")
        
        # 执行计算
        pll_handler.calculate_output_frequencies()
        
        # 显示结果
        print("计算结果:")
        display_calculation_results(pll_handler)
        
        print("✓ 默认时钟源选择测试完成")
        
    except Exception as e:
        print(f"✗ 默认时钟源选择测试失败: {str(e)}")

def test_mode_based_clock_sources(modes_handler, pll_handler):
    """测试不同模式下的时钟源选择"""
    test_modes = [
        ("DualLoop", "双环路模式"),
        ("SingleLoop", "单环路模式"),
        ("DualLoop0DealyNested", "双环路嵌套模式")
    ]
    
    for mode_name, mode_desc in test_modes:
        try:
            print(f"\n测试 {mode_desc} ({mode_name})...")
            
            # 设置模式
            modes_handler.set_mode(mode_name)
            
            # 获取时钟源
            pll1_source, pll1_freq = pll_handler._get_pll1_clock_source()
            pll2_source, pll2_freq = pll_handler._get_pll2_clock_source(0.0)
            
            print(f"  PLL1时钟源: {pll1_source}, 频率: {pll1_freq} MHz")
            print(f"  PLL2时钟源: {pll2_source}, 频率: {pll2_freq} MHz")
            
            # 执行计算
            pll_handler.calculate_output_frequencies()
            
            # 显示结果
            print("  计算结果:")
            display_calculation_results(pll_handler, indent="    ")
            
            print(f"✓ {mode_desc}测试完成")
            
        except Exception as e:
            print(f"✗ {mode_desc}测试失败: {str(e)}")

def test_mux_control_effects(register_manager, pll_handler):
    """测试MUX控件值对时钟源的影响"""
    
    # 测试PLL1_NCLK_MUX的不同值
    print("测试PLL1_NCLK_MUX的影响...")
    
    pll1_mux_tests = [
        (0, "OSCin"),
        (1, "Feedback Mux"),
        (2, "PLL2 Prescaler")
    ]
    
    for mux_value, expected_source in pll1_mux_tests:
        try:
            # 设置PLL1_NCLK_MUX值
            register_manager.set_bit_field_value("0x4F", "PLL1_NCLK_MUX[1:0]", mux_value)
            
            # 获取时钟源
            pll1_source, pll1_freq = pll_handler._get_pll1_clock_source()
            
            print(f"  PLL1_NCLK_MUX={mux_value} -> {pll1_source} (期望: {expected_source})")
            
            if expected_source in pll1_source:
                print(f"    ✓ 时钟源选择正确")
            else:
                print(f"    ✗ 时钟源选择错误")
                
        except Exception as e:
            print(f"    ✗ 测试PLL1_NCLK_MUX={mux_value}时出错: {str(e)}")
    
    # 测试PLL2_RCLK_MUX的不同值
    print("\n测试PLL2_RCLK_MUX的影响...")
    
    pll2_mux_tests = [
        (0, "OSCin"),
        (1, "Currently selected CLKin")
    ]
    
    for mux_value, expected_source in pll2_mux_tests:
        try:
            # 设置PLL2_RCLK_MUX值
            register_manager.set_bit_field_value("0x4F", "PLL2_RCLK_MUX", mux_value)
            
            # 获取时钟源
            pll2_source, pll2_freq = pll_handler._get_pll2_clock_source(0.0)
            
            print(f"  PLL2_RCLK_MUX={mux_value} -> {pll2_source} (期望: {expected_source})")
            
            if expected_source in pll2_source:
                print(f"    ✓ 时钟源选择正确")
            else:
                print(f"    ✗ 时钟源选择错误")
                
        except Exception as e:
            print(f"    ✗ 测试PLL2_RCLK_MUX={mux_value}时出错: {str(e)}")

def display_calculation_results(pll_handler, indent="  "):
    """显示计算结果"""
    try:
        results = {}
        
        if hasattr(pll_handler.ui, "PLL1PFDFreq"):
            results["PLL1PFDFreq"] = pll_handler.ui.PLL1PFDFreq.text()
        if hasattr(pll_handler.ui, "PLL2PFDFreq"):
            results["PLL2PFDFreq"] = pll_handler.ui.PLL2PFDFreq.text()
        if hasattr(pll_handler.ui, "VCODistFreq"):
            results["VCODistFreq"] = pll_handler.ui.VCODistFreq.text()
        if hasattr(pll_handler.ui, "Fin0Freq"):
            results["Fin0Freq"] = pll_handler.ui.Fin0Freq.text()
        
        for freq_name, freq_value in results.items():
            print(f"{indent}{freq_name}: {freq_value} MHz")
            
    except Exception as e:
        print(f"{indent}显示计算结果时出错: {str(e)}")

def print_design_summary():
    """打印设计总结"""
    print("\n" + "="*60)
    print("基于时钟源选择的PLL计算系统设计总结")
    print("="*60)
    print("""
核心理念：
- 计算公式不变，变化的是时钟源的取值
- 通过MUX控件的值动态选择时钟源
- 支持复杂的时钟路径和反馈配置

关键控件：
1. PLL1NclkMux (PLL1_NCLK_MUX[1:0])
   - 0: OSCin
   - 1: Feedback Mux  
   - 2: PLL2 Prescaler

2. PLL2RclkMux (PLL2_RCLK_MUX)
   - 0: OSCin
   - 1: Currently selected CLKin

3. PLL2NclkMux (PLL2_NCLK_MUX)
   - 0: PLL2 Prescaler
   - 1: Feedback Mux

4. FBMUX (FB_MUX[1:0])
   - 0: CLKout6
   - 1: CLKout8
   - 2: SYSREF Divider
   - 4: External

工作流程：
1. 根据MUX控件值确定时钟源
2. 获取对应时钟源的频率值
3. 使用相同的计算公式进行计算
4. 输出计算结果

优势：
- 灵活的时钟源选择
- 支持复杂的时钟路径
- 保持计算逻辑的一致性
- 易于扩展和维护
""")
    print("="*60)

if __name__ == "__main__":
    print_design_summary()
    test_clock_source_selection()
