#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
MD文件整理脚本
将根目录下的MD文件按类型分类整理到docs目录中
"""

import os
import shutil
from datetime import datetime

# 项目根目录
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
DOCS_DIR = os.path.join(PROJECT_ROOT, 'docs')
MD_BACKUP_DIR = os.path.join(PROJECT_ROOT, 'md_backup_' + datetime.now().strftime('%Y%m%d_%H%M%S'))

# MD文件分类映射
MD_FILE_CATEGORIES = {
    # 修复和问题解决相关
    'fixes': [
        'AUTO_WRITE_FEATURE_README.md',
        'CLK_OUTPUT_WINDOW_FIX_SUMMARY.md',
        'COM_PORT_FIX_GUIDE.md',
        'FACTORY_FIX_REPORT.md',
        'FINAL_FIX_REPORT.md',
        'INITIALIZATION_FIX_REPORT.md',
        'SPI_PORT_REFRESH_FIX.md',
        'SYNC_SYSREF_INITIALIZATION_ENHANCEMENT.md',
        'WINDOW_API_FIX_SUMMARY.md'
    ],
    
    # 重构相关
    'refactoring': [
        'FINAL_REFACTORING_REPORT.md',
        'MIGRATION_COMPLETION_REPORT.md',
        'PLL_MODERNIZATION_FIX_SUMMARY.md',
        'PLL_REMOVAL_SUMMARY.md',
        'REFACTORING_COMPLETION_REPORT.md',
        'REFACTORING_SUMMARY.md',
        'REFACTOR_REPORT.md',
        'SYNC_SYSREF_REMOVAL_SUMMARY.md'
    ],
    
    # 测试相关
    'testing': [
        'FEATURE_TEST_SUMMARY.md',
        'TESTING_GUIDE.md',
        'TEST_CLEANUP_REPORT.md',
        'TEST_FILES_ORGANIZATION_SUMMARY.md',
        'TEST_SUITE_SUMMARY.md'
    ],
    
    # 构建和部署
    'build': [
        'PYINSTALLER_GUIDE.md',
        'PYINSTALLER_SUCCESS_GUIDE.md'
    ],
    
    # 回滚和确认
    'rollback': [
        'ROLLBACK_CONFIRMATION.md',
        'ROLLBACK_SUCCESS_CONFIRMATION.md'
    ],
    
    # 清理相关
    'cleanup': [
        'FINAL_CLEANUP_REPORT.md'
    ],
    
    # 功能总结（中文）
    'features': [
        '寄存器表格自动写入修复总结.md',
        '搜索功能修改总结.md'
    ]
}

# 保留在根目录的重要文件
KEEP_IN_ROOT = [
    'README.md'
]

def create_md_backup():
    """创建MD文件备份目录"""
    print(f"创建MD文件备份目录: {MD_BACKUP_DIR}")
    os.makedirs(MD_BACKUP_DIR, exist_ok=True)
    return MD_BACKUP_DIR

def create_docs_structure():
    """创建docs目录结构"""
    print(f"创建docs目录结构...")
    
    # 确保docs目录存在
    os.makedirs(DOCS_DIR, exist_ok=True)
    
    # 为每个分类创建子目录
    for category in MD_FILE_CATEGORIES.keys():
        category_dir = os.path.join(DOCS_DIR, category)
        os.makedirs(category_dir, exist_ok=True)
        print(f"  ✓ 创建目录: docs/{category}/")

def organize_md_files():
    """整理MD文件到对应分类"""
    print(f"\n整理MD文件:")
    organized_count = 0
    
    for category, files in MD_FILE_CATEGORIES.items():
        category_dir = os.path.join(DOCS_DIR, category)
        
        print(f"\n  {category.upper()}:")
        for file_name in files:
            source_path = os.path.join(PROJECT_ROOT, file_name)
            dest_path = os.path.join(category_dir, file_name)
            backup_path = os.path.join(MD_BACKUP_DIR, file_name)
            
            if os.path.exists(source_path):
                try:
                    # 备份文件
                    shutil.copy2(source_path, backup_path)
                    # 移动文件
                    shutil.move(source_path, dest_path)
                    print(f"    ✓ 移动: {file_name}")
                    organized_count += 1
                except Exception as e:
                    print(f"    ✗ 移动失败: {file_name} - {e}")
            else:
                print(f"    - 文件不存在: {file_name}")
    
    print(f"\n  总计整理了 {organized_count} 个MD文件")
    return organized_count

def create_category_index_files():
    """为每个分类创建索引文件"""
    print(f"\n创建分类索引文件:")
    
    category_descriptions = {
        'fixes': '修复和问题解决相关文档',
        'refactoring': '重构相关文档',
        'testing': '测试相关文档',
        'build': '构建和部署相关文档',
        'rollback': '回滚和确认相关文档',
        'cleanup': '清理相关文档',
        'features': '功能总结文档'
    }
    
    for category, description in category_descriptions.items():
        category_dir = os.path.join(DOCS_DIR, category)
        index_path = os.path.join(category_dir, 'README.md')
        
        # 获取该分类下的文件列表
        files_in_category = []
        if os.path.exists(category_dir):
            for file_name in os.listdir(category_dir):
                if file_name.endswith('.md') and file_name != 'README.md':
                    files_in_category.append(file_name)
        
        index_content = f"""# {category.upper()} - {description}

## 文档列表

"""
        
        for file_name in sorted(files_in_category):
            # 生成文档标题（去掉.md后缀，替换下划线为空格）
            title = file_name.replace('.md', '').replace('_', ' ')
            index_content += f"- [{title}](./{file_name})\n"
        
        index_content += f"""
## 说明
{description}

---
*最后更新: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        with open(index_path, 'w', encoding='utf-8') as f:
            f.write(index_content)
        
        print(f"  ✓ 创建索引: docs/{category}/README.md")

def create_main_docs_index():
    """创建主文档索引"""
    print(f"\n创建主文档索引:")
    
    main_index_path = os.path.join(DOCS_DIR, 'README.md')
    
    index_content = f"""# 项目文档索引

## 文档分类

### 🔧 [修复和问题解决](./fixes/)
包含各种功能修复、问题解决和增强的相关文档。

### 🔄 [重构相关](./refactoring/)
包含项目重构、迁移、现代化改造的相关文档。

### 🧪 [测试相关](./testing/)
包含测试指南、测试报告、测试套件相关文档。

### 🏗️ [构建和部署](./build/)
包含项目构建、打包、部署相关的指南和文档。

### ↩️ [回滚和确认](./rollback/)
包含回滚操作和确认相关的文档。

### 🧹 [清理相关](./cleanup/)
包含项目清理、整理相关的报告和文档。

### ⭐ [功能总结](./features/)
包含各种功能的总结和说明文档。

## 文档统计
"""
    
    total_docs = 0
    for category, files in MD_FILE_CATEGORIES.items():
        existing_files = [f for f in files if os.path.exists(os.path.join(DOCS_DIR, category, f))]
        total_docs += len(existing_files)
        index_content += f"- {category}: {len(existing_files)} 个文档\n"
    
    index_content += f"""
**总计: {total_docs} 个文档**

## 使用说明
1. 点击上方分类链接浏览对应类型的文档
2. 每个分类目录都有自己的README.md索引文件
3. 文档按功能和用途进行分类，便于查找和维护

---
*文档索引生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
    
    with open(main_index_path, 'w', encoding='utf-8') as f:
        f.write(index_content)
    
    print(f"  ✓ 创建主索引: docs/README.md")

def generate_md_organization_report():
    """生成MD文件整理报告"""
    report_content = f"""# MD文件整理报告

## 整理时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 整理统计
"""
    
    total_organized = 0
    for category, files in MD_FILE_CATEGORIES.items():
        existing_files = [f for f in files if os.path.exists(os.path.join(DOCS_DIR, category, f))]
        total_organized += len(existing_files)
        report_content += f"- {category}: {len(existing_files)} 个文档\n"
    
    report_content += f"""
**总计整理: {total_organized} 个MD文件**

## 文档结构
```
docs/
├── README.md              # 主文档索引
├── fixes/                 # 修复相关文档
├── refactoring/           # 重构相关文档
├── testing/               # 测试相关文档
├── build/                 # 构建部署文档
├── rollback/              # 回滚确认文档
├── cleanup/               # 清理相关文档
└── features/              # 功能总结文档
```

## 备份位置
{MD_BACKUP_DIR}

## 保留文件
以下文件保留在根目录:
"""
    
    for file_name in KEEP_IN_ROOT:
        if os.path.exists(os.path.join(PROJECT_ROOT, file_name)):
            report_content += f"- {file_name}\n"
    
    report_content += f"""
## 建议
1. 使用docs/README.md作为文档导航入口
2. 新文档按功能分类放入对应目录
3. 定期更新索引文件
4. 重要文档可从备份中恢复
"""
    
    report_path = os.path.join(PROJECT_ROOT, 'MD_ORGANIZATION_REPORT.md')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"\n✓ 生成整理报告: MD_ORGANIZATION_REPORT.md")

def main():
    """主函数"""
    print("=" * 60)
    print("MD文件整理工具")
    print("=" * 60)
    
    # 创建备份
    create_md_backup()
    
    # 创建docs目录结构
    create_docs_structure()
    
    # 整理MD文件
    organized_count = organize_md_files()
    
    # 创建分类索引
    create_category_index_files()
    
    # 创建主索引
    create_main_docs_index()
    
    # 生成报告
    generate_md_organization_report()
    
    # 总结
    print(f"\n" + "=" * 60)
    print("MD文件整理完成!")
    print(f"总计整理文件: {organized_count} 个")
    print(f"备份位置: {MD_BACKUP_DIR}")
    print(f"文档目录: {DOCS_DIR}")
    print("=" * 60)

if __name__ == "__main__":
    main()
