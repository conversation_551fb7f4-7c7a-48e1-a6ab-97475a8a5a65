#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试中文编码修复
验证读取/写入所有寄存器对话框的中文显示是否正常
"""

import sys
import os
import json
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from ui.windows.RegisterMainWindow import RegisterMainWindow
from core.services.spi.spi_service_impl import SPIServiceImpl
from core.repositories.register_repository import RegisterRepository
from utils.Log import get_module_logger

logger = get_module_logger(__name__)

def test_chinese_encoding_fix():
    """测试中文编码修复"""
    print("=" * 60)
    print("测试中文编码修复")
    print("=" * 60)
    
    try:
        # 创建QApplication（包含中文支持设置）
        app = QApplication(sys.argv)
        
        print("1. 创建应用程序...")
        
        # 初始化SPI服务
        spi_service = SPIServiceImpl()
        if spi_service.initialize():
            print("✓ SPI服务初始化成功")
        
        # 创建寄存器仓库
        register_repo = RegisterRepository(spi_service)
        print("✓ 寄存器仓库创建成功")
        
        # 创建主窗口
        main_window = RegisterMainWindow(register_repo)
        main_window.show()
        print("✓ 主窗口创建成功")
        
        # 等待窗口完全加载
        app.processEvents()
        time.sleep(1)
        
        print("\n2. 测试批量读取对话框...")
        
        # 检查批量操作管理器
        if hasattr(main_window, 'batch_manager'):
            batch_manager = main_window.batch_manager
            print("✓ 批量操作管理器可用")
            
            # 模拟批量读取请求
            print("   模拟批量读取请求...")
            
            # 创建测试用的进度对话框
            test_title = "批量读取"
            test_label = "正在读取所有寄存器..."

            batch_manager._create_progress_dialog(test_title, test_label, 100)
            
            if batch_manager.progress_dialog:
                print("✓ 读取进度对话框创建成功")

                # 检查对话框文本
                dialog_title = batch_manager.progress_dialog.windowTitle()
                dialog_label = batch_manager.progress_dialog.labelText()

                print(f"   对话框标题: '{dialog_title}'")
                print(f"   对话框标签: '{dialog_label}'")

                # 检查是否包含中文字符
                if "读取" in dialog_title and "寄存器" in dialog_label:
                    print("✅ 读取对话框中文显示正常")
                else:
                    print("❌ 读取对话框中文显示异常")

                # 关闭对话框
                batch_manager.progress_dialog.close()
                batch_manager.progress_dialog = None
            else:
                print("❌ 读取进度对话框创建失败")
        else:
            print("❌ 批量操作管理器不可用")

        print("\n3. 测试批量写入对话框...")

        # 测试写入对话框
        if hasattr(main_window, 'batch_manager'):
            batch_manager = main_window.batch_manager
            
            # 创建测试用的进度对话框
            test_title = "批量写入"
            test_label = "正在写入所有寄存器..."

            batch_manager._create_progress_dialog(test_title, test_label, 100)

            if batch_manager.progress_dialog:
                print("✓ 写入进度对话框创建成功")

                # 检查对话框文本
                dialog_title = batch_manager.progress_dialog.windowTitle()
                dialog_label = batch_manager.progress_dialog.labelText()

                print(f"   对话框标题: '{dialog_title}'")
                print(f"   对话框标签: '{dialog_label}'")

                # 检查是否包含中文字符
                if "写入" in dialog_title and "寄存器" in dialog_label:
                    print("✅ 写入对话框中文显示正常")
                else:
                    print("❌ 写入对话框中文显示异常")

                # 关闭对话框
                batch_manager.progress_dialog.close()
                batch_manager.progress_dialog = None
            else:
                print("❌ 写入进度对话框创建失败")
        
        print("\n4. 测试字体设置...")
        
        # 检查应用程序字体
        app_font = app.font()
        if app_font:
            print(f"✓ 应用程序字体: {app_font.family()}, 大小: {app_font.pointSize()}")
            
            # 检查是否为中文字体
            chinese_fonts = ["Microsoft YaHei", "SimHei", "SimSun", "PingFang SC", "Hiragino Sans GB", "WenQuanYi Micro Hei", "Noto Sans CJK SC"]
            if any(font_name in app_font.family() for font_name in chinese_fonts):
                print("✅ 使用了支持中文的字体")
            else:
                print(f"⚠️  当前字体可能不完全支持中文: {app_font.family()}")
        else:
            print("❌ 无法获取应用程序字体")
        
        print("\n5. 测试完成")
        print("=" * 60)
        print("测试结果总结:")
        print("- 如果看到 ✅ 标记，说明该项测试通过")
        print("- 如果看到 ❌ 标记，说明该项测试失败")
        print("- 如果看到 ⚠️  标记，说明该项需要注意")
        print("=" * 60)
        
        # 关闭应用程序
        main_window.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_chinese_encoding_fix()
    if success:
        print("\n🎉 中文编码测试完成")
    else:
        print("\n💥 中文编码测试失败")
    
    sys.exit(0 if success else 1)
