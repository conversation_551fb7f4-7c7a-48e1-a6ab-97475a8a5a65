#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试现代化工厂类
验证新旧处理器的并行使用功能
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_modern_factory():
    """测试现代化工厂类"""
    try:
        print("=" * 60)
        print("测试现代化工厂类")
        print("=" * 60)
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 导入必要的模块
        from core.services.register.RegisterManager import RegisterManager
        from ui.factories.ToolWindowFactory import ToolWindowFactory
        from ui.factories.ModernToolWindowFactory import ModernToolWindowFactory
        import json
        
        print("1. 创建模拟主窗口...")
        # 创建模拟主窗口
        class MockMainWindow:
            def __init__(self):
                # 加载寄存器配置
                config_path = os.path.join(os.path.dirname(__file__), 'lib', 'register.json')
                with open(config_path, 'r', encoding='utf-8') as f:
                    registers_config = json.load(f)
                
                self.register_manager = RegisterManager(registers_config)
                self.auto_write_mode = False
                
                # 模拟窗口服务
                self.window_service = MockWindowService()
        
        class MockWindowService:
            def add_window_to_tab(self, window, title, window_attr, action_attr=None):
                print(f"   模拟添加窗口到标签页: {title} ({window_attr})")
                return True
        
        main_window = MockMainWindow()
        print(f"   ✓ 创建了模拟主窗口，包含 {len(main_window.register_manager.register_objects)} 个寄存器对象")
        
        print("2. 测试现代化工厂初始化...")
        try:
            modern_factory = ModernToolWindowFactory(main_window)
            print("   ✓ 现代化工厂初始化成功")
            
            # 检查配置
            configs = modern_factory.HANDLER_CONFIGS
            print(f"   ✓ 加载了 {len(configs)} 个处理器配置")
            
            for handler_type, config in configs.items():
                use_modern = config.get('use_modern', False)
                status = "现代化" if use_modern else "传统"
                print(f"     {handler_type}: {status}")
                
        except Exception as e:
            print(f"   ❌ 现代化工厂初始化失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
        print("3. 测试传统工厂集成...")
        try:
            tool_factory = ToolWindowFactory(main_window)
            print("   ✓ 传统工厂初始化成功")
            
            # 检查是否使用现代化工厂
            is_using_modern = tool_factory.is_using_modern_factory()
            print(f"   ✓ 是否使用现代化工厂: {is_using_modern}")
            
            # 获取支持的窗口类型
            supported_types = tool_factory.get_supported_window_types()
            print(f"   ✓ 支持的窗口类型: {supported_types}")
            
        except Exception as e:
            print(f"   ❌ 传统工厂集成失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
        print("4. 测试迁移状态报告...")
        try:
            migration_status = tool_factory.get_migration_status()
            print(f"   迁移状态报告:")
            print(f"     总处理器数: {migration_status['total_handlers']}")
            print(f"     配置为现代化: {migration_status['configured_modern']}")
            print(f"     配置为传统: {migration_status['configured_legacy']}")
            print(f"     迁移进度: {migration_status['migration_progress']:.1f}%")
            print(f"     活跃窗口: {migration_status['active_windows']}")
            
            if migration_status['migration_progress'] == 100.0:
                print("   ✓ 所有处理器已配置为现代化版本")
            else:
                print(f"   ⚠️ 迁移进度: {migration_status['migration_progress']:.1f}%")
                
        except Exception as e:
            print(f"   ❌ 迁移状态报告失败: {str(e)}")
        
        print("5. 测试现代化处理器创建...")
        test_handlers = ['set_modes', 'clkin_control', 'pll_control']
        
        for handler_type in test_handlers:
            try:
                print(f"   创建 {handler_type} 处理器...")
                window = tool_factory.create_window_by_type(handler_type)
                
                if window:
                    print(f"     ✓ {handler_type} 创建成功")
                    print(f"     窗口类型: {type(window).__name__}")
                    print(f"     窗口标题: {window.windowTitle()}")
                    
                    # 检查是否是现代化版本
                    is_modern = "Modern" in type(window).__name__
                    print(f"     是否现代化: {is_modern}")
                    
                    # 检查控件映射
                    if hasattr(window, 'widget_register_map'):
                        mapping_count = len(window.widget_register_map)
                        print(f"     控件映射数: {mapping_count}")
                    
                    # 关闭窗口
                    window.close()
                    
                else:
                    print(f"     ❌ {handler_type} 创建失败")
                    
            except Exception as e:
                print(f"     ❌ {handler_type} 创建出错: {str(e)}")
        
        print("6. 测试处理器类型切换...")
        try:
            # 测试切换到传统版本
            print("   切换 set_modes 到传统版本...")
            success = tool_factory.switch_handler_type('set_modes', use_modern=False)
            
            if success:
                print("   ✓ 切换成功")
                
                # 获取更新后的状态
                updated_status = tool_factory.get_migration_status()
                print(f"   更新后迁移进度: {updated_status['migration_progress']:.1f}%")
                
                # 切换回现代化版本
                print("   切换回现代化版本...")
                tool_factory.switch_handler_type('set_modes', use_modern=True)
                print("   ✓ 切换回现代化版本成功")
                
            else:
                print("   ❌ 切换失败")
                
        except Exception as e:
            print(f"   ❌ 处理器类型切换测试出错: {str(e)}")
        
        print("7. 测试跨处理器交互...")
        try:
            # 创建需要交互的处理器
            print("   创建时钟输出处理器...")
            clk_output_window = tool_factory.create_window_by_type('clk_output')
            
            print("   创建同步系统参考处理器...")
            sync_sysref_window = tool_factory.create_window_by_type('sync_sysref')
            
            if clk_output_window and sync_sysref_window:
                print("   ✓ 跨处理器交互测试窗口创建成功")
                
                # 检查交互设置
                if hasattr(clk_output_window, 'sync_sysref_handler'):
                    if clk_output_window.sync_sysref_handler:
                        print("   ✓ 时钟输出处理器已连接同步系统参考处理器")
                    else:
                        print("   ⚠️ 时钟输出处理器未连接同步系统参考处理器")
                
                # 关闭窗口
                clk_output_window.close()
                sync_sysref_window.close()
                
            else:
                print("   ❌ 跨处理器交互测试窗口创建失败")
                
        except Exception as e:
            print(f"   ❌ 跨处理器交互测试出错: {str(e)}")
        
        print("8. 测试错误处理和回退机制...")
        try:
            # 测试不存在的处理器类型
            print("   测试不存在的处理器类型...")
            invalid_window = tool_factory.create_window_by_type('invalid_type')
            
            if invalid_window is None:
                print("   ✓ 正确处理了无效的处理器类型")
            else:
                print("   ❌ 未正确处理无效的处理器类型")
                
        except Exception as e:
            print(f"   ❌ 错误处理测试出错: {str(e)}")
        
        print("9. 生成最终报告...")
        try:
            final_status = tool_factory.get_migration_status()
            
            print(f"   最终迁移报告:")
            print(f"     总处理器: {final_status['total_handlers']}")
            print(f"     现代化配置: {final_status['configured_modern']}")
            print(f"     传统配置: {final_status['configured_legacy']}")
            print(f"     迁移完成度: {final_status['migration_progress']:.1f}%")
            
            if final_status['migration_progress'] == 100.0:
                print("   🎉 所有处理器已成功迁移到现代化版本！")
            else:
                print(f"   📊 迁移进度: {final_status['migration_progress']:.1f}%")
                
        except Exception as e:
            print(f"   ❌ 生成最终报告时出错: {str(e)}")
        
        print("\n" + "=" * 60)
        print("🎉 现代化工厂类测试完成！")
        print("=" * 60)
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_modern_factory()
    sys.exit(0 if success else 1)
