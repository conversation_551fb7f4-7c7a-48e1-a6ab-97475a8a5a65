
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FSJ04832寄存器配置工具 - 可视化测试报告</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .metric-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #007bff;
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        .metric-label {
            color: #666;
            margin-top: 5px;
        }
        .charts-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        .chart-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .test-details {
            margin-top: 30px;
        }
        .test-category {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .test-category h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .test-stats {
            display: flex;
            gap: 15px;
        }
        .stat {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 0.9em;
        }
        .stat.passed { background: #d4edda; color: #155724; }
        .stat.failed { background: #f8d7da; color: #721c24; }
        .stat.errors { background: #fff3cd; color: #856404; }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin-top: 10px;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 FSJ04832寄存器配置工具</h1>
            <h2>可视化测试报告</h2>
            <p>生成时间: 2025年08月04日 15:42:41</p>
        </div>
        
        <div class="summary">
            <div class="metric-card">
                <div class="metric-value">50</div>
                <div class="metric-label">总测试数</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">39</div>
                <div class="metric-label">通过测试</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">3</div>
                <div class="metric-label">失败测试</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">78.0%</div>
                <div class="metric-label">成功率</div>
            </div>
        </div>
        
        <div class="charts-container">
            <div class="chart-container">
                <h3>测试结果分布</h3>
                <canvas id="resultChart" width="400" height="300"></canvas>
            </div>
            <div class="chart-container">
                <h3>各模块测试成功率</h3>
                <canvas id="moduleChart" width="400" height="300"></canvas>
            </div>
        </div>
        
        <div class="test-details">
            <h2>📊 详细测试结果</h2>

            <div class="test-category">
                <h3>核心功能测试</h3>
                <div class="test-stats">
                    <span class="stat passed">通过: 5</span>
                    <span class="stat failed">失败: 1</span>
                    <span class="stat errors">错误: 2</span>
                    <span>成功率: 62.5%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 62.5%"></div>
                </div>
            </div>

            <div class="test-category">
                <h3>架构组件测试</h3>
                <div class="test-stats">
                    <span class="stat passed">通过: 7</span>
                    <span class="stat failed">失败: 1</span>
                    <span class="stat errors">错误: 1</span>
                    <span>成功率: 77.8%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 77.77777777777779%"></div>
                </div>
            </div>

            <div class="test-category">
                <h3>UI界面测试</h3>
                <div class="test-stats">
                    <span class="stat passed">通过: 9</span>
                    <span class="stat failed">失败: 0</span>
                    <span class="stat errors">错误: 0</span>
                    <span>成功率: 100.0%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 100.0%"></div>
                </div>
            </div>

            <div class="test-category">
                <h3>集成通信测试</h3>
                <div class="test-stats">
                    <span class="stat passed">通过: 8</span>
                    <span class="stat failed">失败: 0</span>
                    <span class="stat errors">错误: 0</span>
                    <span>成功率: 100.0%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 100.0%"></div>
                </div>
            </div>

            <div class="test-category">
                <h3>性能稳定性测试</h3>
                <div class="test-stats">
                    <span class="stat passed">通过: 3</span>
                    <span class="stat failed">失败: 0</span>
                    <span class="stat errors">错误: 5</span>
                    <span>成功率: 37.5%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 37.5%"></div>
                </div>
            </div>

            <div class="test-category">
                <h3>打包部署测试</h3>
                <div class="test-stats">
                    <span class="stat passed">通过: 7</span>
                    <span class="stat failed">失败: 1</span>
                    <span class="stat errors">错误: 0</span>
                    <span>成功率: 87.5%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 87.5%"></div>
                </div>
            </div>

        </div>
    </div>
    
    <script>
        // 测试结果分布饼图
        const resultCtx = document.getElementById('resultChart').getContext('2d');
        new Chart(resultCtx, {
            type: 'doughnut',
            data: {
                labels: ['通过', '失败', '错误'],
                datasets: [{
                    data: [39, 3, 8],
                    backgroundColor: ['#28a745', '#dc3545', '#ffc107'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
        
        // 各模块成功率柱状图
        const moduleCtx = document.getElementById('moduleChart').getContext('2d');
        new Chart(moduleCtx, {
            type: 'bar',
            data: {
                labels: ['核心功能测试', '架构组件测试', 'UI界面测试', '集成通信测试', '性能稳定性测试', '打包部署测试'],
                datasets: [{
                    label: '成功率 (%)',
                    data: [62.5, 77.77777777777779, 100.0, 100.0, 37.5, 87.5],
                    backgroundColor: 'rgba(54, 162, 235, 0.8)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    </script>
</body>
</html>
