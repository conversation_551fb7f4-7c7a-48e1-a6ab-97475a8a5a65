#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
现代化的同步系统参考处理器
使用ModernBaseHandler作为基类，重构自原SyncSysRefHandler
主要功能：同步系统参考配置、频率计算、批量控制
"""

from PyQt5 import QtCore, QtWidgets
from ui.handlers.ModernBaseHandler import ModernBaseHandler
from ui.forms.Ui_syncSysref import Ui_sync_sysref
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class ModernSyncSysRefHandler(ModernBaseHandler):
    """现代化的同步系统参考处理器"""

    # 添加窗口关闭信号
    window_closed = QtCore.pyqtSignal()

    def __init__(self, parent=None, register_manager=None, **kwargs):
        """初始化现代化同步系统参考处理器

        Args:
            parent: 父窗口
            register_manager: RegisterManager实例
            **kwargs: 其他参数（如register_repo等，用于兼容性）
        """
        super().__init__(parent, register_manager, **kwargs)

        # 设置窗口标题
        self.setWindowTitle("同步系统参考配置 (现代化版本)")

        # 创建UI实例
        self.ui = Ui_sync_sysref()
        self.ui.setupUi(self.content_widget)

        # 初始化同步系统特定配置
        self._init_sync_config()

        logger.info("现代化同步系统参考处理器初始化完成")

    def _init_sync_config(self):
        """初始化同步系统特定配置"""
        try:
            # 设置默认VCO频率
            self._init_default_values()

            # 连接特殊信号
            self._connect_special_signals()

            # 连接批量控制按钮
            self._connect_batch_control_buttons()

            logger.info("同步系统特定配置初始化完成")

        except Exception as e:
            logger.error(f"初始化同步系统配置时出错: {str(e)}")

    def _init_default_values(self):
        """初始化默认值"""
        try:
            # 设置默认VCO频率
            if hasattr(self.ui, "InternalVCOFreq"):
                self.ui.InternalVCOFreq.setText("2949.12")

            # 设置SYSREF分频器的正确范围和默认值
            if hasattr(self.ui, "spinBoxSysrefDIV"):
                # 根据register.json，SYSREF_DIV[12:0]的范围是0:8191
                self.ui.spinBoxSysrefDIV.setMinimum(0)
                self.ui.spinBoxSysrefDIV.setMaximum(8191)

                # 默认值是0110000000000（二进制）= 3072（十进制）
                self.ui.spinBoxSysrefDIV.setValue(3072)
                logger.info("已设置SYSREF分频器范围(0-8191)和默认值(3072)")

            # 设置SYSREF延迟的正确范围和默认值
            if hasattr(self.ui, "spinBoxSysrefDDLY"):
                # 根据register.json，SYSREF_DDLY[12:0]的范围是2:8191
                self.ui.spinBoxSysrefDDLY.setMinimum(2)
                self.ui.spinBoxSysrefDDLY.setMaximum(8191)

                # 默认值是0000000001000（二进制）= 8（十进制）
                self.ui.spinBoxSysrefDDLY.setValue(8)
                logger.info("已设置SYSREF延迟范围(2-8191)和默认值(8)")

            # 设置DDLYd步进计数器的范围和默认值
            if hasattr(self.ui, "DDLYdStepCNT_1"):
                # 根据register.json，DDLYd_STEP_CNT[7:0]的范围是0:255
                self.ui.DDLYdStepCNT_1.setMinimum(0)
                self.ui.DDLYdStepCNT_1.setMaximum(255)
                self.ui.DDLYdStepCNT_1.setValue(0)  # 默认值是00000000
                logger.info("已设置DDLYd步进计数器范围(0-255)和默认值(0)")

            # 初始化ComboBox控件
            self._init_combobox_controls()

            # 初始化频率显示控件
            self._init_frequency_displays()

            # 初始化电源管理和控制状态
            self._init_power_and_control_states()

            # 计算并显示初始频率
            self.calculate_output_frequencies()

        except Exception as e:
            logger.error(f"初始化默认值时出错: {str(e)}")

    def _connect_special_signals(self):
        """连接特殊信号"""
        try:
            # 连接VCO频率输入信号
            if hasattr(self.ui, "InternalVCOFreq"):
                self.ui.InternalVCOFreq.returnPressed.connect(self.calculate_output_frequencies)
                self.ui.InternalVCOFreq.textChanged.connect(self.calculate_output_frequencies)
                logger.info("已连接 InternalVCOFreq 信号")

            # 连接分频器值变化信号
            if hasattr(self.ui, "spinBoxSysrefDIV"):
                self.ui.spinBoxSysrefDIV.valueChanged.connect(self.calculate_output_frequencies)
                logger.info("已连接 spinBoxSysrefDIV 信号")
            if hasattr(self.ui, "SendPulse"):
                self.ui.SendPulse.clicked.connect(self.send_pulse)
                logger.info("已连接 SendPulse 按钮信号")

        except Exception as e:
            logger.error(f"连接特殊信号时出错: {str(e)}")

    def _init_combobox_controls(self):
        """初始化ComboBox控件的选项和默认值"""
        try:
            # 初始化SYNC模式ComboBox
            if hasattr(self.ui, "comboSyncMode"):
                self.ui.comboSyncMode.clear()
                self.ui.comboSyncMode.addItems(["0: off", "1: SYNC Pin", "2: Pin(pulser)", "3: SPI(pulser)"])
                self.ui.comboSyncMode.setCurrentIndex(1)  # 默认值是01
                logger.info("已初始化SYNC模式ComboBox")

            # 初始化SYSREF脉冲计数ComboBox
            if hasattr(self.ui, "comboSysrefPulseCnt"):
                self.ui.comboSysrefPulseCnt.clear()
                self.ui.comboSysrefPulseCnt.addItems(["0", "1", "2", "3"])
                self.ui.comboSysrefPulseCnt.setCurrentIndex(3)  # 默认值是11
                logger.info("已初始化SYSREF脉冲计数ComboBox")

            # 初始化SYSREF MUX ComboBox
            if hasattr(self.ui, "comboSysrefMux"):
                self.ui.comboSysrefMux.clear()
                self.ui.comboSysrefMux.addItems(["0: Normal", "1: Reclocked", "2: Pulser", "3: Continuous"])
                self.ui.comboSysrefMux.setCurrentIndex(0)  # 默认值是00
                logger.info("已初始化SYSREF MUX ComboBox")

            # 初始化DDLYd SYSREF步进ComboBox
            if hasattr(self.ui, "comboDDLYdSysrefStep"):
                self.ui.comboDDLYdSysrefStep.clear()
                for i in range(16):
                    self.ui.comboDDLYdSysrefStep.addItem(str(i))
                self.ui.comboDDLYdSysrefStep.setCurrentIndex(1)  # 默认值是0001
                logger.info("已初始化DDLYd SYSREF步进ComboBox")

            # 初始化CLKin0 Demux ComboBox
            if hasattr(self.ui, "CLKin0Demux"):
                self.ui.CLKin0Demux.clear()
                self.ui.CLKin0Demux.addItems(["Fin", "Feedback Mux (0-delay mode)", "PLL1", "Off"])
                self.ui.CLKin0Demux.setCurrentIndex(0)  # 默认选择CLKin0
                logger.info("已初始化CLKin0 Demux ComboBox")

            # 初始化VCO模式ComboBox
            if hasattr(self.ui, "comboVcoMode"):
                self.ui.comboVcoMode.clear()
                self.ui.comboVcoMode.addItems(["Internal", "External"])
                self.ui.comboVcoMode.setCurrentIndex(0)  # 默认选择Internal
                logger.info("已初始化VCO模式ComboBox")

            # 应用之前延迟设置的ComboBox值
            if hasattr(self, 'apply_pending_combobox_values'):
                self.apply_pending_combobox_values()

        except Exception as e:
            logger.error(f"初始化ComboBox控件时出错: {str(e)}")

    def _init_frequency_displays(self):
        """初始化频率显示控件"""
        try:
            # 初始化SYSREF频率显示控件
            frequency_controls = ["SyncSysrefFreq1", "SyncSysrefFreq2"]

            for control_name in frequency_controls:
                if hasattr(self.ui, control_name):
                    control = getattr(self.ui, control_name)
                    control.setText("0.96")  # 默认显示计算后的频率 2949.12/3072 ≈ 0.96
                    control.setReadOnly(True)  # 设置为只读
                    logger.info(f"已初始化{control_name}频率显示")

        except Exception as e:
            logger.error(f"初始化频率显示控件时出错: {str(e)}")

    def _init_power_and_control_states(self):
        """初始化电源管理和控制状态"""
        try:
            # 根据register.json的默认值设置关键控制位

            # SYNC_EN默认为1（启用）
            if hasattr(self.ui, "SyncEn"):
                self.ui.SyncEn.setChecked(True)
                logger.info("已设置SYNC_EN为启用状态")

            # SYSREF相关的电源控制位默认为1（关闭），需要根据实际需要调整
            power_down_controls = {
                "SysrefGBLPD": True,    # SYSREF_GBL_PD默认为1
                "sysrefPD": True,       # SYSREF_PD默认为1
                "sysrefDDLYPD": True,   # SYSREF_DDLY_PD默认为1
                "SysrefPlsrPd": True    # SYSREF_PLSR_PD默认为1
            }

            for control_name, default_state in power_down_controls.items():
                if hasattr(self.ui, control_name):
                    control = getattr(self.ui, control_name)
                    control.setChecked(default_state)
                    logger.info(f"已设置{control_name}为{'关闭' if default_state else '启用'}状态")

            # 设置其他重要的默认状态
            control_defaults = {
                "SyncPOL": False,       # SYNC_POL默认为0（正常）
                "Sync1SHOTEn": False,   # SYNC_1SHOT_EN默认为0（电平敏感）
                "SysrefCLR": False,     # SYSREF_CLR默认为0
                "SysrefReqEn": False,   # SYSREF_REQ_EN默认为0
                "sysrefDissysref": False, # SYNC_DISSYSREF默认为0
                "sysrefDDLYdEn": False  # DDLYd_SYSREF_EN默认为0
            }

            for control_name, default_state in control_defaults.items():
                if hasattr(self.ui, control_name):
                    control = getattr(self.ui, control_name)
                    control.setChecked(default_state)
                    logger.info(f"已设置{control_name}默认状态")

            # 设置SYNC DIS控件的默认状态（默认为0，即不禁用）
            sync_dis_controls = [
                "SYNCDIS12", "SYNCDIS10", "SYNCDIS8", "SYNCDIS6",
                "SYNCDIS4", "SYNCDIS2", "SYNCDIS0"
            ]

            for control_name in sync_dis_controls:
                if hasattr(self.ui, control_name):
                    control = getattr(self.ui, control_name)
                    control.setChecked(False)  # 默认不禁用同步
                    logger.debug(f"已设置{control_name}为不禁用状态")

            logger.info("电源管理和控制状态初始化完成")

        except Exception as e:
            logger.error(f"初始化电源管理和控制状态时出错: {str(e)}")

    def _connect_batch_control_buttons(self):
        """连接批量控制按钮"""
        try:
            # 连接All On按钮
            if hasattr(self.ui, "pBtAllOn"):
                self.ui.pBtAllOn.clicked.connect(lambda: self.set_all_sync_dis(True))
                logger.info("已连接 pBtAllOn 按钮")

            # 连接All Off按钮
            if hasattr(self.ui, "pBtAllOff"):
                self.ui.pBtAllOff.clicked.connect(lambda: self.set_all_sync_dis(False))
                logger.info("已连接 pBtAllOff 按钮")

        except Exception as e:
            logger.error(f"连接批量控制按钮时出错: {str(e)}")

    def on_register_value_changed(self, widget_name, reg_addr, reg_value):
        """处理寄存器值变化"""
        logger.info(f"同步系统: 寄存器 {reg_addr} 值变化 (控件: {widget_name}) -> 0x{reg_value:04X}")

        # 重新计算频率
        self.calculate_output_frequencies()

        # 处理特定控件的业务逻辑
        if widget_name.startswith("SYNCDIS"):
            self._handle_sync_dis_change(widget_name)
        elif widget_name in ["SYSREFMUX", "InternalVCO", "CLKDEMUX"]:
            self._handle_mux_change(widget_name)

    def on_global_register_update(self, reg_addr, reg_value):
        """处理全局寄存器更新"""
        logger.debug(f"同步系统: 收到全局更新 {reg_addr} = 0x{reg_value:04X}")

        # 重新计算频率
        self.calculate_output_frequencies()

    # === 业务逻辑方法 ===

    def calculate_output_frequencies(self):
        """计算同步系统参考频率"""
        try:
            # 获取VCO频率
            if not hasattr(self.ui, "InternalVCOFreq"):
                logger.warning("未找到InternalVCOFreq控件，无法计算频率")
                return

            fvco_text = self.ui.InternalVCOFreq.text()
            if not fvco_text:
                self._update_output_frequency_display(0.0)
                return

            # 转换为浮点数
            try:
                fvco = float(fvco_text)
            except ValueError:
                logger.warning(f"无效的VCO频率输入: {fvco_text}")
                self._update_output_frequency_display(0.0)
                return

            # 获取分频比
            if not hasattr(self.ui, "spinBoxSysrefDIV"):
                logger.warning("未找到spinBoxSysrefDIV控件，无法计算频率")
                self._update_output_frequency_display(0.0)
                return

            sysref_div = self.ui.spinBoxSysrefDIV.value()
            if sysref_div <= 0:
                logger.warning(f"无效的分频比: {sysref_div}")
                self._update_output_frequency_display(0.0)
                return

            # 计算SYSREF频率
            sysref_freq = fvco / sysref_div
            logger.debug(f"计算完成，系统参考频率: {sysref_freq:.2f} MHz")

            # 更新SYSREF频率显示
            self._update_output_frequency_display(sysref_freq)

        except Exception as e:
            logger.error(f"计算输出频率时发生错误: {str(e)}")

    def _update_output_frequency_display(self, frequency):
        """更新频率显示到所有输出频率控件

        Args:
            frequency: 计算得到的频率值
        """
        try:
            # 格式化频率显示（保留2位小数）
            freq_text = f"{frequency:.2f}"

            # 处理SysrefFreq控件
            if hasattr(self.ui, "SysrefFreq"):
                self.ui.SysrefFreq.setText(freq_text)
                logger.debug(f"SysrefFreq输出频率: {freq_text} MHz")

            # 处理SyncSysrefFreq1控件
            if hasattr(self.ui, "SyncSysrefFreq1"):
                self.ui.SyncSysrefFreq1.setText(freq_text)
                logger.debug(f"SyncSysrefFreq1输出频率: {freq_text} MHz")

            # 处理SyncSysrefFreq2控件
            if hasattr(self.ui, "SyncSysrefFreq2"):
                self.ui.SyncSysrefFreq2.setText(freq_text)
                logger.debug(f"SyncSysrefFreq2输出频率: {freq_text} MHz")

            # 更新状态信息
            logger.info(f"SYSREF频率已更新: {freq_text} MHz")

        except Exception as e:
            logger.error(f"更新频率显示时出错: {str(e)}")

    def set_all_sync_dis(self, checked):
        """设置所有SYNC DIS复选框的状态并更新寄存器值

        Args:
            checked: 布尔值，True表示选中，False表示取消选中
        """
        try:
            # 获取所有SYNC DIS复选框
            sync_dis_checkboxes = []
            for attr_name in dir(self.ui):
                if attr_name.startswith('SYNCDIS') and hasattr(self.ui, attr_name):
                    attr_value = getattr(self.ui, attr_name)
                    if isinstance(attr_value, QtWidgets.QCheckBox):
                        sync_dis_checkboxes.append(attr_value)

            if not sync_dis_checkboxes:
                logger.warning("未找到任何SYNCDIS复选框控件")
                return

            logger.info(f"找到 {len(sync_dis_checkboxes)} 个SYNCDIS复选框控件")

            # 设置所有复选框的状态并更新寄存器值
            success_count = 0
            for checkbox in sync_dis_checkboxes:
                try:
                    widget_name = checkbox.objectName()

                    # 通过RegisterManager更新寄存器值
                    if widget_name in self.widget_register_map:
                        widget_info = self.widget_register_map[widget_name]
                        reg_addr = widget_info["register_addr"]
                        bit_def = widget_info["bit_def"]
                        bit_name = bit_def.get("name", "")

                        if bit_name and self.register_manager:
                            # 更新寄存器值
                            success = self.register_manager.set_bit_field_value(reg_addr, bit_name, 1 if checked else 0)
                            if success:
                                success_count += 1
                                logger.debug(f"已更新控件 {widget_name} 的状态为 {checked}")
                            else:
                                logger.warning(f"更新控件 {widget_name} 的寄存器值失败")
                    else:
                        logger.warning(f"控件 {widget_name} 未在映射表中找到")

                except Exception as e:
                    logger.error(f"更新控件 {checkbox.objectName()} 状态时发生错误: {str(e)}")

            logger.info(f"已{('启用' if checked else '禁用')}所有SYNC DIS控件 ({success_count}/{len(sync_dis_checkboxes)})")

        except Exception as e:
            logger.error(f"设置所有SYNC DIS控件时发生错误: {str(e)}")

    def _handle_sync_dis_change(self, widget_name):
        """处理SYNC DIS控件变化"""
        try:
            logger.info(f"SYNC DIS控件 {widget_name} 状态变化")
            # 可以在这里添加特定的业务逻辑
        except Exception as e:
            logger.error(f"处理SYNC DIS控件变化时出错: {str(e)}")

    def _handle_mux_change(self, widget_name):
        """处理MUX控件变化"""
        try:
            logger.info(f"MUX控件 {widget_name} 状态变化")
            # 重新计算频率
            self.calculate_output_frequencies()
        except Exception as e:
            logger.error(f"处理MUX控件变化时出错: {str(e)}")

    # === 公共接口方法 ===

    def get_current_status(self):
        """获取当前同步系统状态

        Returns:
            dict: 当前状态信息
        """
        try:
            status = {}

            if self.register_manager:
                # 获取SYSREF配置状态
                status["sysref_enabled"] = True  # 默认启用

                # 获取SYNC DIS状态
                sync_dis_status = {}
                for widget_name in self.widget_register_map:
                    if widget_name.startswith("SYNCDIS"):
                        widget_info = self.widget_register_map[widget_name]
                        reg_addr = widget_info["register_addr"]
                        bit_def = widget_info["bit_def"]
                        bit_name = bit_def.get("name", "")

                        if bit_name:
                            bit_value = self.register_manager.get_bit_field_value(reg_addr, bit_name)
                            sync_dis_status[widget_name] = bool(bit_value)

                status["sync_dis_status"] = sync_dis_status

                # 获取频率信息
                if hasattr(self.ui, "InternalVCOFreq"):
                    try:
                        vco_freq = float(self.ui.InternalVCOFreq.text() or "0")
                        status["vco_frequency"] = vco_freq
                    except ValueError:
                        status["vco_frequency"] = 0.0

                if hasattr(self.ui, "spinBoxSysrefDIV"):
                    status["sysref_divider"] = self.ui.spinBoxSysrefDIV.value()

            return status

        except Exception as e:
            logger.error(f"获取同步系统状态时出错: {str(e)}")
            return {}

    def set_sync_preset(self, preset_name):
        """设置同步系统预设配置

        Args:
            preset_name: 预设名称
        """
        try:
            logger.info(f"应用同步系统预设: {preset_name}")

            # 定义预设配置
            presets = {
                "default": {
                    "vco_frequency": 2949.12,
                    "sysref_divider": 32,
                    "all_sync_dis": False
                },
                "high_frequency": {
                    "vco_frequency": 3000.0,
                    "sysref_divider": 16,
                    "all_sync_dis": False
                },
                "low_frequency": {
                    "vco_frequency": 2400.0,
                    "sysref_divider": 64,
                    "all_sync_dis": False
                },
                "all_disabled": {
                    "vco_frequency": 2949.12,
                    "sysref_divider": 32,
                    "all_sync_dis": True
                }
            }

            if preset_name not in presets:
                logger.warning(f"未知的预设: {preset_name}")
                return

            preset = presets[preset_name]

            # 应用预设
            if hasattr(self.ui, "InternalVCOFreq"):
                self.ui.InternalVCOFreq.setText(str(preset["vco_frequency"]))

            if hasattr(self.ui, "spinBoxSysrefDIV"):
                self.ui.spinBoxSysrefDIV.setValue(preset["sysref_divider"])

            # 设置所有SYNC DIS状态
            self.set_all_sync_dis(preset["all_sync_dis"])

            # 重新计算频率
            self.calculate_output_frequencies()

            logger.info(f"同步系统预设 {preset_name} 应用完成")

        except Exception as e:
            logger.error(f"设置同步系统预设时出错: {str(e)}")

    def get_sync_dis_count(self):
        """获取SYNC DIS控件的数量和状态

        Returns:
            dict: 包含总数和启用数的字典
        """
        try:
            total_count = 0
            enabled_count = 0

            for widget_name in self.widget_register_map:
                if widget_name.startswith("SYNCDIS"):
                    total_count += 1

                    widget_info = self.widget_register_map[widget_name]
                    reg_addr = widget_info["register_addr"]
                    bit_def = widget_info["bit_def"]
                    bit_name = bit_def.get("name", "")

                    if bit_name and self.register_manager:
                        bit_value = self.register_manager.get_bit_field_value(reg_addr, bit_name)
                        if bit_value:
                            enabled_count += 1

            return {
                "total_count": total_count,
                "enabled_count": enabled_count,
                "disabled_count": total_count - enabled_count
            }

        except Exception as e:
            logger.error(f"获取SYNC DIS状态时出错: {str(e)}")
            return {"total_count": 0, "enabled_count": 0, "disabled_count": 0}

    def send_pulse(self):
        """发送脉冲 - 对寄存器0x4E的bit7写入1"""
        try:
            reg_addr = 0x4E  # 寄存器地址
            
            logger.info(f"SendPulse按钮被点击，准备对寄存器0x{reg_addr:02X}的bit7写入1")
            
            # 检查RegisterManager是否可用
            if not self.register_manager:
                logger.error("RegisterManager不可用，无法执行脉冲发送")
                return
            
            # 获取当前寄存器值
            current_value = self.register_manager.get_register_value(reg_addr)
            logger.debug(f"当前寄存器0x{reg_addr:02X}的值: 0x{current_value:04X}")
            
            # 设置bit7为1 (bit7的掩码是0x80)
            new_value = current_value | 0x80
            logger.info(f"设置bit7后的新值: 0x{new_value:04X}")
            
            # 写入寄存器值到RegisterManager
            success = self.register_manager.set_register_value(reg_addr, new_value)
            
            if success:
                logger.info(f"成功更新寄存器0x{reg_addr:02X}的值为0x{new_value:04X}")
                
                # 发送寄存器更新信号到全局总线
                try:
                    from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
                    bus_instance = RegisterUpdateBus.instance()
                    if bus_instance:
                        bus_instance.register_updated.emit(reg_addr, new_value)
                        logger.debug("已发送寄存器更新信号到全局总线")
                    else:
                        logger.warning("RegisterUpdateBus实例为None，跳过信号发送")
                except Exception as e:
                    logger.warning(f"发送RegisterUpdateBus信号时出错: {str(e)}")
                
                # 自动写入到芯片
                self._auto_write_register_to_chip(reg_addr, new_value)
                
                # 触发寄存器表格跳转
                if not self._is_in_batch_operation():
                    logger.info(f"SendPulse触发寄存器表格跳转到0x{reg_addr:02X}")
                    self._trigger_register_table_navigation(reg_addr)
                
                logger.info("脉冲发送操作完成")
            else:
                logger.error(f"更新寄存器0x{reg_addr:02X}失败")
                
        except Exception as e:
            logger.error(f"发送脉冲时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    @classmethod
    def create_for_testing(cls, parent=None):
        """创建测试实例"""
        try:
            # 创建模拟的RegisterManager
            from core.services.register.RegisterManager import RegisterManager
            import json
            import os
            
            # 加载寄存器配置
            config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'lib', 'register.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                registers_config = json.load(f)
            
            register_manager = RegisterManager(registers_config)
            
            # 创建实例
            instance = cls(parent, register_manager)
            
            logger.info("创建现代化SyncSysRefHandler测试实例成功")
            return instance
            
        except Exception as e:
            logger.error(f"创建测试实例时出错: {str(e)}")
            raise


if __name__ == "__main__":
    import sys
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    # 创建测试实例
    handler = ModernSyncSysRefHandler.create_for_testing()
    handler.show()
    
    sys.exit(app.exec_())
