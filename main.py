#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTextCodec, QLocale
from PyQt5.QtGui import QFont
from ui.windows.RegisterMainWindow import RegisterMainWindow
from utils.Log import logger

def setup_chinese_support(app):
    """设置中文支持"""
    try:
        # 设置UTF-8编码
        if hasattr(QTextCodec, 'setCodecForLocale'):
            utf8_codec = QTextCodec.codecForName("UTF-8")
            if utf8_codec:
                QTextCodec.setCodecForLocale(utf8_codec)

        # 设置中文本地化
        QLocale.setDefault(QLocale(QLocale.Chinese, QLocale.China))

        # 设置支持中文的字体
        font = QFont()

        # 根据操作系统选择合适的中文字体
        if sys.platform.startswith('win'):
            # Windows系统
            font.setFamily("Microsoft YaHei")  # 微软雅黑
            if not font.exactMatch():
                font.setFamily("SimHei")  # 黑体
                if not font.exactMatch():
                    font.setFamily("SimSun")  # 宋体
        elif sys.platform.startswith('darwin'):
            # macOS系统
            font.setFamily("PingFang SC")
            if not font.exactMatch():
                font.setFamily("Hiragino Sans GB")
        else:
            # Linux系统
            font.setFamily("WenQuanYi Micro Hei")
            if not font.exactMatch():
                font.setFamily("Noto Sans CJK SC")
                if not font.exactMatch():
                    font.setFamily("DejaVu Sans")

        font.setPointSize(9)
        app.setFont(font)

        logger.info(f"中文支持设置完成，使用字体: {font.family()}")

    except Exception as e:
        logger.warning(f"设置中文支持时出现警告: {str(e)}")

def main():
    """主程序入口"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # 设置统一的Fusion风格

    # 设置中文支持
    setup_chinese_support(app)

    # 创建并显示主窗口
    main_window = RegisterMainWindow()
    main_window.show()

    # 启动应用程序主循环
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()