#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试同步系统参考初始化
验证SYSREF DIV控件的正确初始化
"""

import sys
import os
import json
from PyQt5.QtWidgets import QApplication

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ui.handlers.ModernSyncSysRefHandler import ModernSyncSysRefHandler
from core.services.register.RegisterManager import RegisterManager
from utils.Log import get_module_logger

logger = get_module_logger(__name__)

def test_sync_sysref_initialization():
    """测试同步系统参考初始化"""
    print("=" * 60)
    print("测试同步系统参考初始化")
    print("=" * 60)
    
    try:
        # 创建QApplication
        app = QApplication(sys.argv)
        
        print("1. 加载寄存器配置...")
        config_path = os.path.join('lib', 'register.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        print("2. 创建RegisterManager...")
        register_manager = RegisterManager(registers_config)
        
        print("3. 创建现代化同步系统参考处理器...")
        sync_handler = ModernSyncSysRefHandler(None, register_manager)
        
        print("4. 验证SYSREF DIV控件初始化...")
        
        if hasattr(sync_handler.ui, "spinBoxSysrefDIV"):
            sysref_div = sync_handler.ui.spinBoxSysrefDIV
            
            # 检查范围设置
            min_value = sysref_div.minimum()
            max_value = sysref_div.maximum()
            current_value = sysref_div.value()
            
            print(f"   SYSREF DIV控件:")
            print(f"     最小值: {min_value}")
            print(f"     最大值: {max_value}")
            print(f"     当前值: {current_value}")
            
            # 验证范围是否正确
            if min_value == 0 and max_value == 8191:
                print("   ✅ 范围设置正确 (0-8191)")
            else:
                print(f"   ❌ 范围设置错误，期望(0-8191)，实际({min_value}-{max_value})")
                return False
            
            # 验证默认值是否正确
            if current_value == 3072:
                print("   ✅ 默认值正确 (3072)")
            else:
                print(f"   ❌ 默认值错误，期望3072，实际{current_value}")
                return False
        else:
            print("   ❌ 未找到spinBoxSysrefDIV控件")
            return False
        
        print("\n5. 验证SYSREF DDLY控件初始化...")
        
        if hasattr(sync_handler.ui, "spinBoxSysrefDDLY"):
            sysref_ddly = sync_handler.ui.spinBoxSysrefDDLY
            
            # 检查范围设置
            min_value = sysref_ddly.minimum()
            max_value = sysref_ddly.maximum()
            current_value = sysref_ddly.value()
            
            print(f"   SYSREF DDLY控件:")
            print(f"     最小值: {min_value}")
            print(f"     最大值: {max_value}")
            print(f"     当前值: {current_value}")
            
            # 验证范围是否正确
            if min_value == 2 and max_value == 8191:
                print("   ✅ 范围设置正确 (2-8191)")
            else:
                print(f"   ❌ 范围设置错误，期望(2-8191)，实际({min_value}-{max_value})")
                return False
            
            # 验证默认值是否正确
            if current_value == 8:
                print("   ✅ 默认值正确 (8)")
            else:
                print(f"   ❌ 默认值错误，期望8，实际{current_value}")
                return False
        else:
            print("   ❌ 未找到spinBoxSysrefDDLY控件")
            return False
        
        print("\n6. 验证VCO频率初始化...")
        
        if hasattr(sync_handler.ui, "InternalVCOFreq"):
            vco_freq = sync_handler.ui.InternalVCOFreq.text()
            print(f"   VCO频率: {vco_freq}")
            
            if vco_freq == "2949.12":
                print("   ✅ VCO频率默认值正确")
            else:
                print(f"   ❌ VCO频率默认值错误，期望2949.12，实际{vco_freq}")
                return False
        else:
            print("   ❌ 未找到InternalVCOFreq控件")
            return False
        
        print("\n7. 测试频率计算...")
        
        # 手动调用频率计算
        sync_handler.calculate_output_frequencies()
        
        # 检查计算结果
        expected_freq = 2949.12 / 3072  # VCO频率 / SYSREF分频器
        print(f"   期望频率: {expected_freq:.4f} MHz")
        
        # 检查频率显示控件
        freq_widgets = ["SysrefFreq", "SyncSysrefFreq1", "SyncSysrefFreq2"]
        for widget_name in freq_widgets:
            if hasattr(sync_handler.ui, widget_name):
                widget = getattr(sync_handler.ui, widget_name)
                displayed_freq = widget.text()
                print(f"   {widget_name}: {displayed_freq} MHz")
                
                try:
                    freq_value = float(displayed_freq)
                    if abs(freq_value - expected_freq) < 0.01:
                        print(f"   ✅ {widget_name} 频率计算正确")
                    else:
                        print(f"   ❌ {widget_name} 频率计算错误")
                        return False
                except ValueError:
                    print(f"   ❌ {widget_name} 频率值无效: {displayed_freq}")
                    return False
            else:
                print(f"   ⚠️ 未找到{widget_name}控件")
        
        print("\n8. 测试分频器值变化...")
        
        # 修改分频器值
        test_divider = 1024
        sync_handler.ui.spinBoxSysrefDIV.setValue(test_divider)
        app.processEvents()
        
        # 重新计算频率
        sync_handler.calculate_output_frequencies()
        
        # 检查新的计算结果
        new_expected_freq = 2949.12 / test_divider
        print(f"   新分频器值: {test_divider}")
        print(f"   新期望频率: {new_expected_freq:.4f} MHz")
        
        # 检查第一个频率显示控件
        if hasattr(sync_handler.ui, "SysrefFreq"):
            new_displayed_freq = sync_handler.ui.SysrefFreq.text()
            print(f"   新显示频率: {new_displayed_freq} MHz")
            
            try:
                new_freq_value = float(new_displayed_freq)
                if abs(new_freq_value - new_expected_freq) < 0.01:
                    print("   ✅ 分频器变化后频率计算正确")
                else:
                    print("   ❌ 分频器变化后频率计算错误")
                    return False
            except ValueError:
                print(f"   ❌ 新频率值无效: {new_displayed_freq}")
                return False
        
        print("\n9. 验证寄存器映射...")
        
        # 检查SYSREF相关控件的寄存器映射
        sysref_widgets = ["spinBoxSysrefDIV", "spinBoxSysrefDDLY"]
        for widget_name in sysref_widgets:
            if widget_name in sync_handler.widget_register_map:
                widget_info = sync_handler.widget_register_map[widget_name]
                reg_addr = widget_info["register_addr"]
                bit_def = widget_info["bit_def"]
                # 处理寄存器地址格式
                if isinstance(reg_addr, str):
                    print(f"   {widget_name}: 映射到寄存器 {reg_addr}")
                else:
                    print(f"   {widget_name}: 映射到寄存器 0x{reg_addr:02X}")
                print(f"     位定义: {bit_def.get('name', 'N/A')}")
                print(f"   ✅ 寄存器映射正确")
            else:
                print(f"   ❌ {widget_name} 未在寄存器映射中找到")
                return False
        
        print("\n" + "=" * 60)
        print("🎉 所有测试通过！同步系统参考初始化正确！")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_sync_sysref_initialization()
