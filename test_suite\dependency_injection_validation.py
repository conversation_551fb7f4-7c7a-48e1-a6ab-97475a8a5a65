#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
依赖注入系统验证测试
验证依赖注入系统的完成情况和功能
"""

import sys
import os

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

from utils.Log import get_module_logger

logger = get_module_logger(__name__)


def test_di_container_basic_functionality():
    """测试依赖注入容器基本功能"""
    print("=" * 60)
    print("测试依赖注入容器基本功能")
    print("=" * 60)
    
    try:
        from core.services.DIContainer import DIContainer
        
        # 创建测试容器
        test_container = DIContainer()
        
        # 测试单例注册
        class TestService:
            def __init__(self, name="test"):
                self.name = name
        
        test_container.register_singleton('test_service', TestService, name='test_singleton')
        service1 = test_container.get('test_service')
        service2 = test_container.get('test_service')
        
        assert service1 is service2, "单例服务应该返回同一个实例"
        assert service1.name == 'test_singleton', "单例服务参数应该正确传递"
        
        print("✅ 单例注册和获取测试通过")
        
        # 测试瞬态注册
        test_container.register_transient('transient_service', TestService, name='test_transient')
        transient1 = test_container.get('transient_service')
        transient2 = test_container.get('transient_service')
        
        assert transient1 is not transient2, "瞬态服务应该返回不同实例"
        assert transient1.name == 'test_transient', "瞬态服务参数应该正确传递"
        
        print("✅ 瞬态注册和获取测试通过")
        
        # 测试依赖注入
        class DependentService:
            def __init__(self, dependency):
                self.dependency = dependency
        
        test_container.register_singleton('dependent_service', DependentService, dependency='@test_service')
        dependent = test_container.get('dependent_service')
        
        assert dependent.dependency is service1, "依赖注入应该正确解析"
        
        print("✅ 依赖注入测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 依赖注入容器基本功能测试失败: {str(e)}")
        return False


def test_core_services_registration():
    """测试核心服务注册"""
    print("\n" + "=" * 60)
    print("测试核心服务注册")
    print("=" * 60)
    
    try:
        from core.services.DIContainer import container, configure_services
        
        # 清空容器
        container.clear()
        
        # 配置核心服务
        configure_services()
        
        # 检查核心服务是否已注册
        core_services = ['spi_service', 'register_manager', 'event_bus']
        
        for service_name in core_services:
            if service_name in container._configurations:
                print(f"✅ {service_name} 已注册")
            else:
                print(f"❌ {service_name} 未注册")
                return False
        
        print("✅ 所有核心服务注册测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 核心服务注册测试失败: {str(e)}")
        return False


def test_ui_managers_registration():
    """测试UI管理器注册"""
    print("\n" + "=" * 60)
    print("测试UI管理器注册")
    print("=" * 60)
    
    try:
        from core.services.DIContainer import container, configure_ui_managers
        
        # 模拟主窗口注册
        class MockMainWindow:
            pass
        
        mock_window = MockMainWindow()
        container.register_singleton('main_window', MockMainWindow, instance=mock_window)
        
        # 配置UI管理器
        configure_ui_managers()
        
        # 检查UI管理器是否已注册
        ui_managers = [
            'initialization_manager',
            'register_operation_manager', 
            'display_manager',
            'event_coordinator',
            'tool_window_factory',
            'register_update_processor',
            'lifecycle_manager',
            'ui_utility_manager'
        ]
        
        for manager_name in ui_managers:
            if manager_name in container._configurations:
                print(f"✅ {manager_name} 已注册")
            else:
                print(f"❌ {manager_name} 未注册")
                return False
        
        print("✅ 所有UI管理器注册测试通过")
        return True
        
    except Exception as e:
        print(f"❌ UI管理器注册测试失败: {str(e)}")
        return False


def test_additional_managers_registration():
    """测试额外管理器注册"""
    print("\n" + "=" * 60)
    print("测试额外管理器注册")
    print("=" * 60)
    
    try:
        from core.services.DIContainer import container, configure_additional_managers
        
        # 配置额外管理器
        configure_additional_managers()
        
        # 检查额外管理器是否已注册
        additional_managers = [
            'batch_manager',
            'batch_operation_controller',
            'spi_coordinator',
            'ui_event_handler',
            'tool_window_manager',
            'status_config_manager',
            'resource_utility_manager',
            'tab_window_manager'
        ]
        
        for manager_name in additional_managers:
            if manager_name in container._configurations:
                print(f"✅ {manager_name} 已注册")
            else:
                print(f"❌ {manager_name} 未注册")
                return False
        
        print("✅ 所有额外管理器注册测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 额外管理器注册测试失败: {str(e)}")
        return False


def test_dependency_injection_decorator():
    """测试依赖注入装饰器"""
    print("\n" + "=" * 60)
    print("测试依赖注入装饰器")
    print("=" * 60)
    
    try:
        from core.services.DIContainer import inject, container
        
        # 注册测试服务
        class TestService:
            def get_data(self):
                return "test_data"
        
        container.register_singleton('decorator_test_service', TestService)
        
        # 使用装饰器
        @inject('decorator_test_service')
        def test_function(service, param):
            return service.get_data() + "_" + param
        
        result = test_function("param")
        expected = "test_data_param"
        
        assert result == expected, f"装饰器结果不正确: 期望 {expected}, 实际 {result}"
        
        print("✅ 依赖注入装饰器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 依赖注入装饰器测试失败: {str(e)}")
        return False


def generate_dependency_injection_report():
    """生成依赖注入系统完成情况报告"""
    print("\n" + "=" * 60)
    print("依赖注入系统完成情况报告")
    print("=" * 60)
    
    # 运行所有测试
    tests = [
        ("依赖注入容器基本功能", test_di_container_basic_functionality),
        ("核心服务注册", test_core_services_registration),
        ("UI管理器注册", test_ui_managers_registration),
        ("额外管理器注册", test_additional_managers_registration),
        ("依赖注入装饰器", test_dependency_injection_decorator)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试执行失败: {str(e)}")
            results.append((test_name, False))
    
    # 生成报告
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<30} {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 依赖注入系统优化已完成！")
        print("✅ 已创建 DIContainer.py")
        print("✅ 统一管理服务生命周期")
        print("✅ 支持依赖注入和工厂模式")
        print("✅ 减少硬编码依赖")
    else:
        print(f"\n⚠️  依赖注入系统还有 {total - passed} 个问题需要解决")
    
    return passed == total


if __name__ == "__main__":
    success = generate_dependency_injection_report()
    sys.exit(0 if success else 1)
