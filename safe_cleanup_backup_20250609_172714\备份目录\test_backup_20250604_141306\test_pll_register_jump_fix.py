#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试PLL工具窗口寄存器表格跳转功能修复
验证工具窗口界面修改控件后，寄存器表格能否正确跳转到对应的寄存器
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication, QMainWindow
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt
from utils.Log import logger


def test_pll_register_jump_functionality():
    """测试PLL工具窗口的寄存器跳转功能"""
    print("=" * 80)
    print("测试PLL工具窗口寄存器表格跳转功能修复 - 避免读取操作引起的跳转混乱")
    print("=" * 80)

    try:
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        # 创建主窗口
        print("1. 创建主窗口...")
        from ui.windows.RegisterMainWindow import RegisterMainWindow
        from core.services.spi.spi_service_impl import SPIServiceImpl
        from core.repositories.register_repository import RegisterRepository

        spi_service = SPIServiceImpl()
        spi_service.initialize()
        register_repo = RegisterRepository(spi_service)
        main_window = RegisterMainWindow(register_repo)
        main_window.show()

        # 等待界面完全加载
        QTest.qWait(1000)
        print("✓ 主窗口创建成功")

        # 创建PLL控制窗口
        print("\n2. 创建PLL控制窗口...")
        pll_window = main_window.tool_window_factory.create_window_by_type('pll_control')

        if not pll_window:
            print("❌ PLL控制窗口创建失败")
            return False

        print(f"✓ PLL控制窗口创建成功: {type(pll_window).__name__}")
        pll_window.show()
        QTest.qWait(1000)

        # 检查主窗口的寄存器表格相关组件
        print("\n3. 检查主窗口寄存器表格组件...")
        
        components_found = []
        if hasattr(main_window, 'table_handler'):
            components_found.append(f"table_handler: {type(main_window.table_handler).__name__}")
        if hasattr(main_window, 'on_register_selected'):
            components_found.append("on_register_selected方法")
        if hasattr(main_window, 'event_coordinator'):
            components_found.append(f"event_coordinator: {type(main_window.event_coordinator).__name__}")
        if hasattr(main_window, 'display_manager'):
            components_found.append(f"display_manager: {type(main_window.display_manager).__name__}")

        if components_found:
            print("✓ 找到寄存器表格相关组件:")
            for component in components_found:
                print(f"  - {component}")
        else:
            print("❌ 未找到寄存器表格相关组件")

        # 测试PLL控件修改和寄存器跳转
        print("\n4. 测试PLL控件修改和寄存器跳转...")
        
        # 测试用例：修改PLL1PD控件
        test_cases = [
            {
                "widget_name": "PLL1PD",
                "expected_register": "0x50",
                "description": "PLL1 Power Down控件"
            },
            {
                "widget_name": "PLL2PD", 
                "expected_register": "0x83",
                "description": "PLL2 Power Down控件"
            },
            {
                "widget_name": "PLL1RDividerSetting",
                "expected_register": "0x63",  # 默认ClkIn0对应的寄存器
                "description": "PLL1 R Divider设置控件"
            }
        ]

        success_count = 0
        for i, test_case in enumerate(test_cases, 1):
            widget_name = test_case["widget_name"]
            expected_reg = test_case["expected_register"]
            description = test_case["description"]
            
            print(f"\n  测试 {i}: {description}")
            
            if not hasattr(pll_window.ui, widget_name):
                print(f"    ❌ 控件 {widget_name} 不存在")
                continue
                
            widget = getattr(pll_window.ui, widget_name)
            print(f"    ✓ 找到控件: {widget_name} ({type(widget).__name__})")
            
            # 记录初始状态
            initial_table_register = None
            if hasattr(main_window, 'table_handler') and hasattr(main_window.table_handler, 'current_register_addr'):
                initial_table_register = main_window.table_handler.current_register_addr
                print(f"    初始表格寄存器: {initial_table_register}")
            
            # 模拟控件值变化
            try:
                print(f"    尝试修改控件 {widget_name}...")
                
                if hasattr(widget, 'setChecked'):  # CheckBox
                    current_state = widget.isChecked()
                    widget.setChecked(not current_state)
                    print(f"    ✓ CheckBox状态: {current_state} -> {not current_state}")
                elif hasattr(widget, 'setValue'):  # SpinBox
                    current_value = widget.value()
                    new_value = current_value + 1 if current_value < widget.maximum() else current_value - 1
                    widget.setValue(new_value)
                    print(f"    ✓ SpinBox值: {current_value} -> {new_value}")
                elif hasattr(widget, 'setCurrentIndex'):  # ComboBox
                    current_index = widget.currentIndex()
                    new_index = (current_index + 1) % widget.count()
                    widget.setCurrentIndex(new_index)
                    print(f"    ✓ ComboBox索引: {current_index} -> {new_index}")
                
                # 等待处理完成
                QTest.qWait(500)
                
                # 检查寄存器表格是否跳转
                final_table_register = None
                if hasattr(main_window, 'table_handler') and hasattr(main_window.table_handler, 'current_register_addr'):
                    final_table_register = main_window.table_handler.current_register_addr
                    print(f"    最终表格寄存器: {final_table_register}")
                    
                    if final_table_register == expected_reg:
                        print(f"    ✅ 寄存器表格成功跳转到 {expected_reg}")
                        success_count += 1
                    elif final_table_register != initial_table_register:
                        print(f"    ⚠️  寄存器表格跳转到 {final_table_register}，期望 {expected_reg}")
                    else:
                        print(f"    ❌ 寄存器表格未跳转，仍为 {final_table_register}")
                else:
                    print(f"    ❌ 无法检查寄存器表格状态")
                    
            except Exception as e:
                print(f"    ❌ 修改控件时出错: {str(e)}")
                import traceback
                traceback.print_exc()

        # 测试读取操作不会触发跳转
        print(f"\n  测试读取操作不触发跳转:")
        initial_register = None
        if hasattr(main_window, 'table_handler') and hasattr(main_window.table_handler, 'current_register_addr'):
            initial_register = main_window.table_handler.current_register_addr
            print(f"    当前表格寄存器: {initial_register}")

        # 执行读取操作
        test_read_register = "0x83"  # PLL2相关寄存器
        print(f"    执行读取操作: {test_read_register}")

        if hasattr(main_window, 'register_service'):
            main_window.register_service.read_register(test_read_register)
            QTest.qWait(1000)  # 等待读取完成

            # 检查表格是否跳转
            final_register = None
            if hasattr(main_window, 'table_handler') and hasattr(main_window.table_handler, 'current_register_addr'):
                final_register = main_window.table_handler.current_register_addr
                print(f"    读取后表格寄存器: {final_register}")

                if final_register == initial_register:
                    print(f"    ✅ 读取操作未触发跳转，表格保持在 {final_register}")
                else:
                    print(f"    ❌ 读取操作错误触发跳转: {initial_register} -> {final_register}")
            else:
                print(f"    ❌ 无法检查表格状态")
        else:
            print(f"    ❌ 无法执行读取操作")

        # 总结测试结果
        print(f"\n5. 测试结果总结:")
        print(f"   - 总测试用例: {len(test_cases)}")
        print(f"   - 成功跳转: {success_count}")
        print(f"   - 成功率: {success_count/len(test_cases)*100:.1f}%")

        if success_count == len(test_cases):
            print("🎉 所有测试用例通过！寄存器表格跳转功能正常工作！")
            result = True
        elif success_count > 0:
            print("⚠️  部分测试用例通过，寄存器表格跳转功能部分正常")
            result = True
        else:
            print("❌ 所有测试用例失败，寄存器表格跳转功能仍有问题")
            result = False

        # 保持窗口打开供用户查看
        print(f"\n窗口将保持打开3秒供查看...")
        QTest.qWait(3000)

        # 返回结果和主窗口实例供后续测试使用
        return result, main_window

    except Exception as e:
        print(f"❌ 测试过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, None


def test_register_navigation_methods(existing_main_window=None):
    """测试各种寄存器导航方法

    Args:
        existing_main_window: 可选的现有主窗口实例，避免重复创建
    """
    print("\n" + "=" * 80)
    print("测试寄存器导航方法")
    print("=" * 80)

    try:
        # 使用现有主窗口或创建新的
        if existing_main_window:
            main_window = existing_main_window
            print("✓ 使用现有主窗口实例")
        else:
            # 创建应用程序
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)

            # 创建主窗口
            from ui.windows.RegisterMainWindow import RegisterMainWindow
            from core.services.spi.spi_service_impl import SPIServiceImpl
            from core.repositories.register_repository import RegisterRepository

            spi_service = SPIServiceImpl()
            spi_service.initialize()
            register_repo = RegisterRepository(spi_service)
            main_window = RegisterMainWindow(register_repo)
            main_window.show()
            QTest.qWait(1000)
            print("✓ 创建新主窗口实例")

        test_register = "0x50"  # PLL1相关寄存器

        print(f"测试寄存器: {test_register}")

        # 检查RegisterUpdateBus状态
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            bus_instance = RegisterUpdateBus.instance()
            if bus_instance:
                print("✓ RegisterUpdateBus实例正常")
            else:
                print("❌ RegisterUpdateBus实例为None")
                return False
        except Exception as e:
            print(f"❌ RegisterUpdateBus检查失败: {str(e)}")
            return False

        # 方法1：on_register_selected
        if hasattr(main_window, 'on_register_selected'):
            print("✓ 测试 on_register_selected 方法")
            try:
                main_window.on_register_selected(test_register)
                print("  ✓ 调用成功")
            except Exception as e:
                print(f"  ❌ 调用失败: {e}")
        else:
            print("❌ 主窗口没有 on_register_selected 方法")

        # 方法2：直接调用table_handler
        if hasattr(main_window, 'table_handler'):
            print("✓ 测试直接调用 table_handler")
            try:
                if hasattr(main_window, 'register_manager'):
                    reg_value = main_window.register_manager.get_register_value(test_register)
                    main_window.table_handler.show_bit_fields(test_register, reg_value)
                    print("  ✓ 调用成功")
                else:
                    print("  ❌ 主窗口没有 register_manager")
            except Exception as e:
                print(f"  ❌ 调用失败: {e}")
        else:
            print("❌ 主窗口没有 table_handler")

        # 方法3：事件协调器
        if hasattr(main_window, 'event_coordinator'):
            print("✓ 测试事件协调器")
            try:
                if hasattr(main_window.event_coordinator, 'handle_register_selection'):
                    main_window.event_coordinator.handle_register_selection(test_register)
                    print("  ✓ 调用成功")
                else:
                    print("  ❌ 事件协调器没有 handle_register_selection 方法")
            except Exception as e:
                print(f"  ❌ 调用失败: {e}")
        else:
            print("❌ 主窗口没有 event_coordinator")

        return True

    except Exception as e:
        print(f"❌ 测试导航方法时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 开始PLL工具窗口寄存器表格跳转功能测试")

    # 创建应用程序（只创建一次）
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)

    main_window = None
    success1 = False
    success2 = False

    try:
        # 测试PLL寄存器跳转功能
        print("\n📋 第一阶段：测试PLL寄存器跳转功能")
        result = test_pll_register_jump_functionality()
        if isinstance(result, tuple):
            success1, main_window = result
        else:
            success1 = result

        # 测试寄存器导航方法（重用主窗口）
        if main_window:
            print("\n📋 第二阶段：测试寄存器导航方法（重用主窗口）")
            success2 = test_register_navigation_methods(main_window)
        else:
            print("\n📋 第二阶段：测试寄存器导航方法（创建新主窗口）")
            success2 = test_register_navigation_methods()

        # 总结结果
        print("\n" + "=" * 80)
        print("📊 测试结果总结")
        print("=" * 80)
        print(f"第一阶段 - PLL寄存器跳转功能: {'✅ 通过' if success1 else '❌ 失败'}")
        print(f"第二阶段 - 寄存器导航方法: {'✅ 通过' if success2 else '❌ 失败'}")

        if success1 and success2:
            print("\n🎉 所有测试通过！PLL工具窗口寄存器表格跳转功能修复成功！")
            sys.exit(0)
        else:
            print("\n❌ 部分测试失败，需要进一步调试")
            sys.exit(1)

    except Exception as e:
        print(f"\n💥 测试过程中发生严重错误: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

    finally:
        # 清理资源
        if main_window:
            try:
                main_window.close()
            except:
                pass
