#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试布局修改效果的脚本
验证：
1. IO控制区域与表格的紧密布局
2. 按钮大小和可见性
3. 整体布局的紧凑性
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.Log import logger


def test_layout_modifications():
    """测试布局修改效果"""
    logger.info("开始测试布局修改效果...")

    try:
        # 直接运行main.py来启动应用程序
        import subprocess

        logger.info("启动应用程序进行布局测试...")
        result = subprocess.run([sys.executable, "main.py"],
                              timeout=15,
                              capture_output=True,
                              text=True)

        logger.info("应用程序已运行完成")
        logger.info("从日志输出中检查布局修改效果:")

        # 分析输出中的关键信息
        output_lines = result.stderr.split('\n') if result.stderr else []

        # 查找按钮可见性相关的日志
        button_visibility_found = False
        layout_info_found = False

        for line in output_lines:
            if "按钮" in line and "可见" in line:
                logger.info(f"按钮状态: {line.strip()}")
                button_visibility_found = True
            elif "布局" in line and ("边距" in line or "间距" in line):
                logger.info(f"布局信息: {line.strip()}")
                layout_info_found = True
            elif "延迟设置后按钮控件可见性" in line:
                logger.info(f"按钮容器状态: {line.strip()}")

        if button_visibility_found:
            logger.info("✓ 按钮可见性修改已生效")
        else:
            logger.warning("✗ 未找到按钮可见性信息")

        logger.info("✓ 布局修改测试完成 - 请手动检查应用程序界面")

    except subprocess.TimeoutExpired:
        logger.info("应用程序运行超时，这是正常的（应用程序仍在运行）")
    except Exception as e:
        logger.error(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()

    logger.info("布局修改测试完成")


def test_layout_properties(main_window):
    """测试布局属性"""
    logger.info("=== 开始检查布局属性 ===")
    
    try:
        # 检查IO处理器
        if hasattr(main_window, 'io_handler'):
            io_widget = main_window.io_handler.get_io_widget()
            logger.info(f"IO控件大小: {io_widget.size()}")
            logger.info(f"IO控件可见性: {io_widget.isVisible()}")
            
            # 检查IO控件的布局边距
            layout = io_widget.layout()
            if layout:
                margins = layout.contentsMargins()
                spacing = layout.spacing()
                logger.info(f"IO布局边距: 左{margins.left()}, 上{margins.top()}, 右{margins.right()}, 下{margins.bottom()}")
                logger.info(f"IO布局间距: {spacing}")
        
        # 检查表格处理器
        if hasattr(main_window, 'table_handler'):
            table_widget = main_window.table_handler
            logger.info(f"表格控件大小: {table_widget.size()}")
            logger.info(f"表格控件可见性: {table_widget.isVisible()}")
            
            # 检查表格的布局边距
            layout = table_widget.layout()
            if layout:
                margins = layout.contentsMargins()
                spacing = layout.spacing()
                logger.info(f"表格布局边距: 左{margins.left()}, 上{margins.top()}, 右{margins.right()}, 下{margins.bottom()}")
                logger.info(f"表格布局间距: {spacing}")
        
        # 检查按钮
        button_names = ['read_btn', 'write_btn', 'read_all_btn', 'write_all_btn', 
                       'dumpall_btn', 'save_btn', 'load_btn']
        
        logger.info("=== 按钮属性检查 ===")
        for btn_name in button_names:
            if hasattr(main_window, btn_name):
                button = getattr(main_window, btn_name)
                logger.info(f"{btn_name}: 大小{button.size()}, 可见{button.isVisible()}, 启用{button.isEnabled()}")
        
        # 检查右侧区域的布局
        central_widget = main_window.centralWidget()
        if central_widget:
            main_layout = central_widget.layout()
            if main_layout and main_layout.count() > 0:
                # 获取顶部布局（包含左右分割）
                top_layout_item = main_layout.itemAt(0)
                if top_layout_item and hasattr(top_layout_item, 'layout'):
                    top_layout = top_layout_item.layout()
                    if top_layout and top_layout.count() > 1:
                        # 获取右侧控件
                        right_widget_item = top_layout.itemAt(1)
                        if right_widget_item and right_widget_item.widget():
                            right_widget = right_widget_item.widget()
                            right_layout = right_widget.layout()
                            if right_layout:
                                logger.info(f"右侧布局控件数量: {right_layout.count()}")
                                margins = right_layout.contentsMargins()
                                spacing = right_layout.spacing()
                                logger.info(f"右侧布局边距: 左{margins.left()}, 上{margins.top()}, 右{margins.right()}, 下{margins.bottom()}")
                                logger.info(f"右侧布局间距: {spacing}")
        
        logger.info("=== 布局属性检查完成 ===")
        
    except Exception as e:
        logger.error(f"检查布局属性时出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_layout_modifications()
