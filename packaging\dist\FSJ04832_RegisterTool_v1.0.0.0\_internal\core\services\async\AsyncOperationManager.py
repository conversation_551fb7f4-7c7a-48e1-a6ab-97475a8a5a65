#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
异步操作管理器
统一管理应用程序中的异步操作
"""

import asyncio
import threading
from concurrent.futures import ThreadPoolExecutor, Future
from typing import Callable, Any, Optional, Dict
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class AsyncOperationResult:
    """异步操作结果"""
    
    def __init__(self, operation_id: str, success: bool, result: Any = None, error: Exception = None):
        self.operation_id = operation_id
        self.success = success
        self.result = result
        self.error = error


class AsyncOperationManager(QObject):
    """异步操作管理器"""
    
    # 信号定义
    operation_started = pyqtSignal(str)  # 操作开始
    operation_completed = pyqtSignal(AsyncOperationResult)  # 操作完成
    operation_progress = pyqtSignal(str, int)  # 操作进度 (operation_id, progress)
    
    _instance = None
    
    def __init__(self):
        """初始化异步操作管理器"""
        super().__init__()
        self._executor = ThreadPoolExecutor(max_workers=4)
        self._operations: Dict[str, Future] = {}
        self._operation_counter = 0
        self._cleanup_timer = QTimer()
        self._cleanup_timer.timeout.connect(self._cleanup_completed_operations)
        self._cleanup_timer.start(5000)  # 每5秒清理一次完成的操作
        
    @classmethod
    def instance(cls):
        """获取单例实例"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def submit_operation(self, operation_func: Callable, *args, 
                        operation_name: str = None, **kwargs) -> str:
        """提交异步操作
        
        Args:
            operation_func: 要执行的函数
            *args: 函数参数
            operation_name: 操作名称
            **kwargs: 函数关键字参数
            
        Returns:
            操作ID
        """
        self._operation_counter += 1
        operation_id = f"{operation_name or 'operation'}_{self._operation_counter}"
        
        # 包装操作函数以处理结果
        def wrapped_operation():
            try:
                result = operation_func(*args, **kwargs)
                return AsyncOperationResult(operation_id, True, result)
            except Exception as e:
                logger.error(f"异步操作失败 {operation_id}: {str(e)}")
                return AsyncOperationResult(operation_id, False, error=e)
        
        # 提交到线程池
        future = self._executor.submit(wrapped_operation)
        self._operations[operation_id] = future
        
        # 添加完成回调
        future.add_done_callback(lambda f: self._on_operation_completed(operation_id, f))
        
        # 发送开始信号
        self.operation_started.emit(operation_id)
        logger.info(f"异步操作已提交: {operation_id}")
        
        return operation_id
    
    def _on_operation_completed(self, operation_id: str, future: Future):
        """操作完成回调"""
        try:
            result = future.result()
            self.operation_completed.emit(result)
            logger.info(f"异步操作完成: {operation_id}, 成功: {result.success}")
        except Exception as e:
            error_result = AsyncOperationResult(operation_id, False, error=e)
            self.operation_completed.emit(error_result)
            logger.error(f"异步操作异常: {operation_id}, 错误: {str(e)}")
    
    def cancel_operation(self, operation_id: str) -> bool:
        """取消异步操作
        
        Args:
            operation_id: 操作ID
            
        Returns:
            是否成功取消
        """
        if operation_id in self._operations:
            future = self._operations[operation_id]
            if future.cancel():
                logger.info(f"异步操作已取消: {operation_id}")
                return True
            else:
                logger.warning(f"异步操作无法取消: {operation_id}")
                return False
        else:
            logger.warning(f"未找到异步操作: {operation_id}")
            return False
    
    def is_operation_running(self, operation_id: str) -> bool:
        """检查操作是否正在运行
        
        Args:
            operation_id: 操作ID
            
        Returns:
            是否正在运行
        """
        if operation_id in self._operations:
            future = self._operations[operation_id]
            return not future.done()
        return False
    
    def get_running_operations(self) -> list:
        """获取所有正在运行的操作ID"""
        running_ops = []
        for op_id, future in self._operations.items():
            if not future.done():
                running_ops.append(op_id)
        return running_ops
    
    def wait_for_operation(self, operation_id: str, timeout: Optional[float] = None) -> AsyncOperationResult:
        """等待操作完成
        
        Args:
            operation_id: 操作ID
            timeout: 超时时间（秒）
            
        Returns:
            操作结果
        """
        if operation_id in self._operations:
            future = self._operations[operation_id]
            try:
                result = future.result(timeout=timeout)
                return result
            except Exception as e:
                return AsyncOperationResult(operation_id, False, error=e)
        else:
            return AsyncOperationResult(operation_id, False, 
                                      error=Exception(f"操作不存在: {operation_id}"))
    
    def _cleanup_completed_operations(self):
        """清理已完成的操作"""
        completed_ops = []
        for op_id, future in self._operations.items():
            if future.done():
                completed_ops.append(op_id)
        
        for op_id in completed_ops:
            del self._operations[op_id]
        
        if completed_ops:
            logger.debug(f"清理了 {len(completed_ops)} 个已完成的操作")
    
    def cancel_all_operations(self):
        """取消所有操作"""
        cancelled_count = 0
        for op_id, future in self._operations.items():
            if future.cancel():
                cancelled_count += 1
        
        logger.info(f"取消了 {cancelled_count} 个操作")
    
    def shutdown(self):
        """关闭异步操作管理器"""
        self._cleanup_timer.stop()
        self.cancel_all_operations()
        self._executor.shutdown(wait=True)
        logger.info("异步操作管理器已关闭")


# 便捷函数
def submit_async_operation(operation_func: Callable, *args, 
                          operation_name: str = None, **kwargs) -> str:
    """提交异步操作的便捷函数"""
    return AsyncOperationManager.instance().submit_operation(
        operation_func, *args, operation_name=operation_name, **kwargs
    )


def cancel_async_operation(operation_id: str) -> bool:
    """取消异步操作的便捷函数"""
    return AsyncOperationManager.instance().cancel_operation(operation_id)


# 装饰器
def async_operation(operation_name: str = None):
    """异步操作装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            return submit_async_operation(func, *args, operation_name=operation_name, **kwargs)
        return wrapper
    return decorator


# 使用示例：
# @async_operation("register_read")
# def read_register(address):
#     # 执行寄存器读取操作
#     return spi_service.read_register(address)
#
# # 或者直接使用
# operation_id = submit_async_operation(read_register, "0x00", operation_name="read_reg_0x00")
