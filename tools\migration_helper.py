#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
重构迁移助手工具
帮助分析现有处理器并生成现代化版本
"""

import os
import sys
import json
import argparse
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

class MigrationHelper:
    """重构迁移助手"""
    
    def __init__(self):
        self.project_root = project_root
        self.handlers_dir = self.project_root / "ui" / "handlers"
        self.forms_dir = self.project_root / "ui" / "forms"
        
    def analyze_handlers(self):
        """分析现有的处理器"""
        print("=" * 60)
        print("分析现有处理器")
        print("=" * 60)
        
        handlers = []
        
        # 扫描处理器目录
        for handler_file in self.handlers_dir.glob("*Handler.py"):
            if handler_file.name.startswith("Modern"):
                continue  # 跳过现代化版本
                
            handler_name = handler_file.stem
            
            # 分析处理器
            analysis = self._analyze_handler(handler_file)
            if analysis:
                analysis["name"] = handler_name
                analysis["file"] = str(handler_file)
                handlers.append(analysis)
        
        # 按复杂度排序
        handlers.sort(key=lambda x: x["complexity_score"])
        
        print(f"\n发现 {len(handlers)} 个处理器:")
        print("-" * 40)
        
        for i, handler in enumerate(handlers, 1):
            print(f"{i:2d}. {handler['name']:<25} "
                  f"复杂度: {handler['complexity_score']:2d} "
                  f"控件: {handler['widget_count']:2d} "
                  f"UI: {handler['ui_class'] or 'N/A'}")
        
        return handlers
    
    def _analyze_handler(self, handler_file):
        """分析单个处理器文件"""
        try:
            with open(handler_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            analysis = {
                "ui_class": None,
                "widget_count": 0,
                "has_register_logic": False,
                "has_frequency_calc": False,
                "complexity_score": 0,
                "imports": [],
                "methods": []
            }
            
            lines = content.split('\n')
            
            for line in lines:
                line = line.strip()
                
                # 检查UI类导入
                if "from ui.forms." in line and "import" in line:
                    ui_class = line.split("import")[-1].strip()
                    analysis["ui_class"] = ui_class
                
                # 检查导入
                if line.startswith("from ") or line.startswith("import "):
                    analysis["imports"].append(line)
                
                # 检查方法定义
                if line.startswith("def "):
                    method_name = line.split("(")[0].replace("def ", "")
                    analysis["methods"].append(method_name)
                
                # 检查寄存器相关逻辑
                if any(keyword in line.lower() for keyword in [
                    "register", "widget_register_map", "update_register_value"
                ]):
                    analysis["has_register_logic"] = True
                
                # 检查频率计算逻辑
                if any(keyword in line.lower() for keyword in [
                    "frequency", "calculate", "freq"
                ]):
                    analysis["has_frequency_calc"] = True
                
                # 统计控件相关代码
                if any(keyword in line for keyword in [
                    "getattr(self.ui,", "self.ui.", "widget"
                ]):
                    analysis["widget_count"] += 1
            
            # 计算复杂度分数
            score = 0
            score += len(analysis["methods"]) * 2
            score += analysis["widget_count"]
            score += 10 if analysis["has_register_logic"] else 0
            score += 5 if analysis["has_frequency_calc"] else 0
            
            analysis["complexity_score"] = score
            
            return analysis
            
        except Exception as e:
            print(f"分析 {handler_file} 时出错: {str(e)}")
            return None
    
    def generate_modern_handler(self, handler_name):
        """生成现代化处理器"""
        print(f"\n生成现代化处理器: {handler_name}")
        print("-" * 40)
        
        # 分析原处理器
        original_file = self.handlers_dir / f"{handler_name}.py"
        if not original_file.exists():
            print(f"错误: 找不到原处理器文件 {original_file}")
            return False
        
        analysis = self._analyze_handler(original_file)
        if not analysis:
            print(f"错误: 无法分析处理器 {handler_name}")
            return False
        
        # 生成现代化版本
        modern_name = f"Modern{handler_name}"
        modern_file = self.handlers_dir / f"{modern_name}.py"
        
        template = self._generate_handler_template(handler_name, analysis)
        
        try:
            with open(modern_file, 'w', encoding='utf-8') as f:
                f.write(template)
            
            print(f"✓ 生成现代化处理器: {modern_file}")
            return True
            
        except Exception as e:
            print(f"错误: 生成处理器时出错: {str(e)}")
            return False
    
    def _generate_handler_template(self, handler_name, analysis):
        """生成处理器模板"""
        ui_class = analysis.get("ui_class", "Ui_Form")
        
        # 推断UI文件路径
        ui_import = f"from ui.forms.{ui_class} import {ui_class}"
        if not ui_class:
            ui_import = "# TODO: 添加正确的UI导入"
            ui_class = "Ui_Form"
        
        template = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
现代化的{handler_name}
使用ModernBaseHandler作为基类，重构自原{handler_name}
"""

from ui.handlers.ModernBaseHandler import ModernBaseHandler
{ui_import}
from utils.Log import logger


class Modern{handler_name}(ModernBaseHandler):
    """现代化的{handler_name}"""
    
    def __init__(self, parent=None, register_manager=None):
        """初始化现代化{handler_name}
        
        Args:
            parent: 父窗口
            register_manager: RegisterManager实例
        """
        super().__init__(parent, register_manager)
        
        # 设置窗口标题
        self.setWindowTitle("{handler_name} (现代化版本)")
        
        # 创建UI实例
        self.ui = {ui_class}()
        self.ui.setupUi(self.content_widget)
        
        # 手动调用初始化（因为测试环境没有事件循环）
        self._post_init()
        
        logger.info("现代化{handler_name}初始化完成")
    
    def on_register_value_changed(self, widget_name, reg_addr, reg_value):
        """处理寄存器值变化"""
        logger.info(f"{handler_name}: 寄存器 {{reg_addr}} 值变化 (控件: {{widget_name}}) -> 0x{{reg_value:04X}}")
        
        # TODO: 添加特定的业务逻辑
        # 例如：
        # if widget_name == "someWidget":
        #     self._handle_some_change(reg_value)
    
    def on_global_register_update(self, reg_addr, reg_value):
        """处理全局寄存器更新"""
        logger.debug(f"{handler_name}: 收到全局更新 {{reg_addr}} = 0x{{reg_value:04X}}")
        
        # TODO: 添加全局更新的特殊处理逻辑
    
    # === 业务逻辑方法 ===
    # TODO: 从原{handler_name}迁移业务逻辑方法
    
    # === 公共接口方法 ===
    
    def get_current_status(self):
        """获取当前状态
        
        Returns:
            dict: 当前状态信息
        """
        try:
            status = {{}}
            
            if self.register_manager:
                # TODO: 添加状态获取逻辑
                pass
                
            return status
            
        except Exception as e:
            logger.error(f"获取状态时出错: {{str(e)}}")
            return {{}}
    
    @classmethod
    def create_for_testing(cls, parent=None):
        """创建测试实例"""
        try:
            # 创建模拟的RegisterManager
            from core.services.register.RegisterManager import RegisterManager
            import json
            import os
            
            # 加载寄存器配置
            config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'lib', 'register.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                registers_config = json.load(f)
            
            register_manager = RegisterManager(registers_config)
            
            # 创建实例
            instance = cls(parent, register_manager)
            
            logger.info("创建现代化{handler_name}测试实例成功")
            return instance
            
        except Exception as e:
            logger.error(f"创建测试实例时出错: {{str(e)}}")
            raise


if __name__ == "__main__":
    import sys
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    # 创建测试实例
    handler = Modern{handler_name}.create_for_testing()
    handler.show()
    
    sys.exit(app.exec_())
'''
        
        return template
    
    def create_migration_plan(self, handlers):
        """创建迁移计划"""
        print("\n" + "=" * 60)
        print("创建迁移计划")
        print("=" * 60)
        
        # 按复杂度分组
        simple = [h for h in handlers if h["complexity_score"] < 20]
        medium = [h for h in handlers if 20 <= h["complexity_score"] < 50]
        complex_handlers = [h for h in handlers if h["complexity_score"] >= 50]
        
        plan = {
            "phase1_simple": simple[:3],  # 前3个简单的
            "phase2_medium": medium[:3],  # 前3个中等的
            "phase3_complex": complex_handlers[:2]  # 前2个复杂的
        }
        
        print("阶段1 - 简单处理器 (复杂度 < 20):")
        for i, handler in enumerate(plan["phase1_simple"], 1):
            print(f"  {i}. {handler['name']} (复杂度: {handler['complexity_score']})")
        
        print("\n阶段2 - 中等处理器 (复杂度 20-50):")
        for i, handler in enumerate(plan["phase2_medium"], 1):
            print(f"  {i}. {handler['name']} (复杂度: {handler['complexity_score']})")
        
        print("\n阶段3 - 复杂处理器 (复杂度 >= 50):")
        for i, handler in enumerate(plan["phase3_complex"], 1):
            print(f"  {i}. {handler['name']} (复杂度: {handler['complexity_score']})")
        
        return plan


def main():
    parser = argparse.ArgumentParser(description="重构迁移助手")
    parser.add_argument("--analyze", action="store_true", help="分析现有处理器")
    parser.add_argument("--migrate", type=str, help="迁移指定处理器")
    parser.add_argument("--plan", action="store_true", help="创建迁移计划")
    
    args = parser.parse_args()
    
    helper = MigrationHelper()
    
    if args.analyze or args.plan:
        handlers = helper.analyze_handlers()
        
        if args.plan:
            helper.create_migration_plan(handlers)
    
    if args.migrate:
        helper.generate_modern_handler(args.migrate)
    
    if not any([args.analyze, args.migrate, args.plan]):
        # 默认行为：分析并创建计划
        handlers = helper.analyze_handlers()
        helper.create_migration_plan(handlers)


if __name__ == "__main__":
    main()
