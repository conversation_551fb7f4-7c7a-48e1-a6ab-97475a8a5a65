#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试PLL模式计算功能
验证不同模式下的PLL计算逻辑是否正确
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from ui.handlers.ModernPLLHandler import ModernPLLHandler
from core.RegisterManager import RegisterManager
from utils.Log import get_module_logger

logger = get_module_logger(__name__)

def test_mode_detection():
    """测试模式检测功能"""
    print("=== 测试PLL模式检测功能 ===")
    
    app = QApplication(sys.argv)
    
    try:
        # 创建RegisterManager实例
        register_manager = RegisterManager()
        
        # 创建PLL处理器实例
        pll_handler = ModernPLLHandler(register_manager=register_manager)
        
        # 测试不同模式的检测
        test_modes = [
            {
                "name": "DualLoop",
                "registers": {
                    "0x4F": {"PLL1_NCLK_MUX[1:0]": 0, "PLL2_NCLK_MUX": 0, "FB_MUX_EN": 0},
                    "0x50": {"PLL1_PD": 0, "OSCin_PD": 0, "VCO_PD": 0},
                    "0x83": {"PLL2_PD": 0}
                }
            },
            {
                "name": "SingleLoop",
                "registers": {
                    "0x4F": {"PLL1_NCLK_MUX[1:0]": 0, "PLL2_NCLK_MUX": 0, "FB_MUX_EN": 0},
                    "0x50": {"PLL1_PD": 1, "OSCin_PD": 0, "VCO_PD": 0},
                    "0x83": {"PLL2_PD": 0}
                }
            },
            {
                "name": "DistributionFin1",
                "registers": {
                    "0x4F": {"PLL1_NCLK_MUX[1:0]": 0, "PLL2_NCLK_MUX": 0, "FB_MUX_EN": 0},
                    "0x50": {"PLL1_PD": 1, "OSCin_PD": 1, "VCO_PD": 1},
                    "0x83": {"PLL2_PD": 1}
                }
            }
        ]
        
        for test_case in test_modes:
            print(f"\n--- 测试模式: {test_case['name']} ---")
            
            # 设置寄存器值
            for reg_addr, bit_values in test_case['registers'].items():
                for bit_name, bit_value in bit_values.items():
                    try:
                        register_manager.set_bit_field_value(reg_addr, bit_name, bit_value)
                        print(f"设置 {reg_addr}[{bit_name}] = {bit_value}")
                    except Exception as e:
                        print(f"设置寄存器失败: {reg_addr}[{bit_name}] = {bit_value}, 错误: {e}")
            
            # 清除模式缓存以强制重新检测
            pll_handler._current_mode_cache = None
            
            # 检测模式
            detected_mode = pll_handler._get_current_mode()
            print(f"检测到的模式: {detected_mode}")
            
            # 验证结果
            if detected_mode == test_case['name']:
                print("✓ 模式检测正确")
            else:
                print(f"✗ 模式检测错误，期望: {test_case['name']}, 实际: {detected_mode}")
        
        print("\n=== 测试频率计算功能 ===")
        
        # 测试频率计算
        test_frequency_calculation(pll_handler)
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        app.quit()

def test_frequency_calculation(pll_handler):
    """测试频率计算功能"""
    
    # 设置测试频率
    test_fin_freq = 122.88
    test_oscin_freq = 122.88
    
    # 模拟设置UI控件值
    if hasattr(pll_handler.ui, "FreFin"):
        pll_handler.ui.FreFin.setText(str(test_fin_freq))
    if hasattr(pll_handler.ui, "OSCinFreq"):
        pll_handler.ui.OSCinFreq.setText(str(test_oscin_freq))
    
    # 测试不同模式下的计算
    test_modes = ["DualLoop", "SingleLoop", "DistributionFin1"]
    
    for mode in test_modes:
        print(f"\n--- 测试 {mode} 模式下的频率计算 ---")
        
        # 设置模式缓存
        pll_handler._current_mode_cache = mode
        
        try:
            # 执行频率计算
            pll_handler.calculate_output_frequencies()
            
            # 读取计算结果
            results = {}
            if hasattr(pll_handler.ui, "PLL1PFDFreq"):
                results["PLL1PFDFreq"] = pll_handler.ui.PLL1PFDFreq.text()
            if hasattr(pll_handler.ui, "PLL2PFDFreq"):
                results["PLL2PFDFreq"] = pll_handler.ui.PLL2PFDFreq.text()
            if hasattr(pll_handler.ui, "VCODistFreq"):
                results["VCODistFreq"] = pll_handler.ui.VCODistFreq.text()
            if hasattr(pll_handler.ui, "Fin0Freq"):
                results["Fin0Freq"] = pll_handler.ui.Fin0Freq.text()
            
            # 显示结果
            for freq_name, freq_value in results.items():
                print(f"{freq_name}: {freq_value} MHz")
            
            print(f"✓ {mode} 模式计算完成")
            
        except Exception as e:
            print(f"✗ {mode} 模式计算失败: {str(e)}")

def print_design_summary():
    """打印设计总结"""
    print("\n" + "="*60)
    print("PLL模式化计算设计总结")
    print("="*60)
    print("""
设计要点：

1. 模式检测机制：
   - 通过读取关键寄存器位（0x4F, 0x50, 0x83）来自动检测当前模式
   - 支持模式缓存，避免重复检测
   - 支持手动模式设置（通过mode_changed信号）

2. 计算策略分离：
   - DualLoop模式：PLL1和PLL2独立工作
   - SingleLoop模式：只有PLL2工作，PLL1关闭
   - DistributionFin1模式：所有PLL关闭，直接分配时钟

3. 输入源动态选择：
   - 根据模式自动选择PLL的输入时钟源
   - 支持嵌套模式下的级联输入
   - 支持零延迟模式的反馈路径

4. 扩展性设计：
   - 易于添加新的模式支持
   - 计算方法模块化，便于维护
   - 保持与现有代码的兼容性

使用方法：
1. 当模式变化时，系统会自动重新计算频率
2. 可以通过寄存器设置来切换模式
3. 频率计算会根据当前模式自动选择合适的算法
""")
    print("="*60)

if __name__ == "__main__":
    print_design_summary()
    test_mode_detection()
