#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试实时计算功能
验证当MUX控件值改变时能够实时重新计算频率
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from ui.handlers.ModernPLLHandler import ModernPLLHandler
from core.RegisterManager import RegisterManager
from utils.Log import get_module_logger

logger = get_module_logger(__name__)

def test_realtime_calculation():
    """测试实时计算功能"""
    print("="*60)
    print("测试基于UI控件的实时PLL计算")
    print("="*60)
    
    app = QApplication(sys.argv)
    
    try:
        # 创建RegisterManager实例
        register_manager = RegisterManager()
        
        # 创建PLL处理器实例
        pll_handler = ModernPLLHandler(register_manager=register_manager)
        
        # 设置测试频率
        if hasattr(pll_handler.ui, "FreFin"):
            pll_handler.ui.FreFin.setText("122.88")
        if hasattr(pll_handler.ui, "OSCinFreq"):
            pll_handler.ui.OSCinFreq.setText("122.88")
        
        print("\n--- 测试1: 验证从UI控件获取MUX值 ---")
        test_ui_mux_values(pll_handler)
        
        print("\n--- 测试2: 模拟MUX控件值变化 ---")
        test_mux_value_changes(pll_handler)
        
        print("\n--- 测试3: 验证信号连接 ---")
        test_signal_connections(pll_handler)
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        app.quit()

def test_ui_mux_values(pll_handler):
    """测试从UI控件获取MUX值"""
    try:
        print("测试从UI控件获取MUX值...")
        
        # 测试PLL1NclkMux控件
        if hasattr(pll_handler.ui, "PLL1NclkMux"):
            current_index = pll_handler.ui.PLL1NclkMux.currentIndex()
            current_data = None
            if hasattr(pll_handler.ui.PLL1NclkMux, 'currentData'):
                current_data = pll_handler.ui.PLL1NclkMux.currentData()
            print(f"  PLL1NclkMux: index={current_index}, data={current_data}")
        else:
            print("  PLL1NclkMux控件不存在")
        
        # 测试PLL2RclkMux控件
        if hasattr(pll_handler.ui, "PLL2RclkMux"):
            current_index = pll_handler.ui.PLL2RclkMux.currentIndex()
            current_data = None
            if hasattr(pll_handler.ui.PLL2RclkMux, 'currentData'):
                current_data = pll_handler.ui.PLL2RclkMux.currentData()
            print(f"  PLL2RclkMux: index={current_index}, data={current_data}")
        else:
            print("  PLL2RclkMux控件不存在")
        
        # 测试FBMUX控件
        if hasattr(pll_handler.ui, "FBMUX"):
            current_index = pll_handler.ui.FBMUX.currentIndex()
            current_data = None
            if hasattr(pll_handler.ui.FBMUX, 'currentData'):
                current_data = pll_handler.ui.FBMUX.currentData()
            print(f"  FBMUX: index={current_index}, data={current_data}")
        else:
            print("  FBMUX控件不存在")
        
        # 获取时钟源
        pll1_source, pll1_freq = pll_handler._get_pll1_clock_source()
        pll2_source, pll2_freq = pll_handler._get_pll2_clock_source(0.0)
        
        print(f"  计算得到的时钟源:")
        print(f"    PLL1: {pll1_source}, 频率: {pll1_freq} MHz")
        print(f"    PLL2: {pll2_source}, 频率: {pll2_freq} MHz")
        
        print("✓ UI控件MUX值获取测试完成")
        
    except Exception as e:
        print(f"✗ UI控件MUX值获取测试失败: {str(e)}")

def test_mux_value_changes(pll_handler):
    """测试MUX控件值变化"""
    try:
        print("测试MUX控件值变化...")
        
        # 测试PLL1NclkMux值变化
        if hasattr(pll_handler.ui, "PLL1NclkMux"):
            print("  测试PLL1NclkMux值变化:")
            
            for test_value in [0, 1, 2]:
                try:
                    # 设置控件值
                    pll_handler.ui.PLL1NclkMux.setCurrentIndex(test_value)
                    
                    # 获取时钟源
                    pll1_source, pll1_freq = pll_handler._get_pll1_clock_source()
                    
                    print(f"    设置为{test_value} -> {pll1_source}, 频率: {pll1_freq} MHz")
                    
                except Exception as e:
                    print(f"    设置为{test_value}时出错: {str(e)}")
        else:
            print("  PLL1NclkMux控件不存在，跳过测试")
        
        # 测试PLL2RclkMux值变化
        if hasattr(pll_handler.ui, "PLL2RclkMux"):
            print("  测试PLL2RclkMux值变化:")
            
            for test_value in [0, 1]:
                try:
                    # 设置控件值
                    pll_handler.ui.PLL2RclkMux.setCurrentIndex(test_value)
                    
                    # 获取时钟源
                    pll2_source, pll2_freq = pll_handler._get_pll2_clock_source(0.0)
                    
                    print(f"    设置为{test_value} -> {pll2_source}, 频率: {pll2_freq} MHz")
                    
                except Exception as e:
                    print(f"    设置为{test_value}时出错: {str(e)}")
        else:
            print("  PLL2RclkMux控件不存在，跳过测试")
        
        print("✓ MUX控件值变化测试完成")
        
    except Exception as e:
        print(f"✗ MUX控件值变化测试失败: {str(e)}")

def test_signal_connections(pll_handler):
    """测试信号连接"""
    try:
        print("测试信号连接...")
        
        # 检查MUX控件是否存在并连接了信号
        mux_widgets = [
            ("PLL1NclkMux", "PLL1时钟源选择"),
            ("PLL2RclkMux", "PLL2参考时钟选择"),
            ("PLL2NclkMux", "PLL2反馈时钟选择"),
            ("FBMUX", "反馈多路复用器"),
            ("FBMuxEn", "反馈多路复用器使能")
        ]
        
        for widget_name, description in mux_widgets:
            if hasattr(pll_handler.ui, widget_name):
                widget = getattr(pll_handler.ui, widget_name)
                print(f"  ✓ {description} ({widget_name}) 控件存在")
                
                # 检查信号连接（这里只是检查控件存在，实际信号连接在初始化时已完成）
                if hasattr(widget, 'currentIndexChanged'):
                    print(f"    - 支持currentIndexChanged信号")
                elif hasattr(widget, 'stateChanged'):
                    print(f"    - 支持stateChanged信号")
                else:
                    print(f"    - 未知的信号类型")
            else:
                print(f"  ✗ {description} ({widget_name}) 控件不存在")
        
        print("✓ 信号连接检查完成")
        
    except Exception as e:
        print(f"✗ 信号连接检查失败: {str(e)}")

def print_realtime_summary():
    """打印实时计算总结"""
    print("\n" + "="*60)
    print("实时计算系统设计总结")
    print("="*60)
    print("""
实时响应机制：

1. UI控件值获取：
   - 直接从ComboBox控件获取currentIndex()或currentData()
   - 避免从寄存器读取，确保获取最新的用户设置
   - 支持控件不存在时的容错处理

2. 信号连接：
   - PLL1NclkMux.currentIndexChanged -> _on_mux_changed()
   - PLL2RclkMux.currentIndexChanged -> _on_mux_changed()
   - PLL2NclkMux.currentIndexChanged -> _on_mux_changed()
   - FBMUX.currentIndexChanged -> _on_mux_changed()
   - FBMuxEn.stateChanged -> _on_mux_changed()

3. 计算触发：
   - 用户改变MUX控件值时自动触发重新计算
   - 使用QTimer.singleShot(50ms)避免频繁计算
   - 保持计算结果的实时性

4. 时钟源选择：
   - PLL1: OSCin / Feedback Mux / PLL2 Prescaler
   - PLL2: OSCin / Currently selected CLKin
   - 反馈: CLKout6 / CLKout8 / SYSREF / External

优势：
- 用户操作即时响应
- 避免寄存器读取延迟
- 支持复杂的时钟路径配置
- 良好的容错和调试能力
""")
    print("="*60)

if __name__ == "__main__":
    print_realtime_summary()
    test_realtime_calculation()
