2025-06-23 14:25:26,290 - main - [main.py:50] - INFO - 中文支持设置完成，使用字体: Microsoft YaHei
2025-06-23 14:25:26,290 - ConfigurationManager - [ConfigurationManager.py:59] - INFO - 已加载配置文件: E:\FSJ04832\FSJReadOutput\version3\anotherCore3\config\default.json
2025-06-23 14:25:26,291 - ConfigurationManager - [ConfigurationManager.py:85] - DEBUG - 跳过可选配置文件: app.json
2025-06-23 14:25:26,291 - ConfigurationManager - [ConfigurationManager.py:59] - INFO - 已加载配置文件: E:\FSJ04832\FSJReadOutput\version3\anotherCore3\config\local.json
2025-06-23 14:25:26,291 - ConfigurationManager - [ConfigurationManager.py:87] - INFO - 配置加载完成，已加载文件: default.json, local.json
2025-06-23 14:25:26,291 - RegisterMainWindow - [RegisterMainWindow.py:169] - INFO - 配置加载完成
2025-06-23 14:25:26,292 - VersionService - [VersionService.py:65] - WARNING - 未找到版本文件，使用默认版本信息
2025-06-23 14:25:26,293 - RegisterMainWindow - [RegisterMainWindow.py:211] - INFO - 窗口标题已设置为: FSJ04832 寄存器配置工具 v1.0.0.0
2025-06-23 14:25:26,294 - RegisterMainWindow - [RegisterMainWindow.py:180] - INFO - 窗口大小设置为: 1840x1100
2025-06-23 14:25:26,294 - RegisterMainWindow - [RegisterMainWindow.py:197] - INFO - 操作常量已加载: TIMEOUT=5000ms, BATCH_SIZE=50
2025-06-23 14:25:26,294 - DIContainer - [DIContainer.py:46] - INFO - 注册单例实例: main_window
2025-06-23 14:25:26,307 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: spi_service
2025-06-23 14:25:26,307 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: register_manager
2025-06-23 14:25:26,307 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: event_bus
2025-06-23 14:25:26,307 - DIContainer - [DIContainer.py:181] - INFO - 核心服务配置完成
2025-06-23 14:25:26,333 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: initialization_manager
2025-06-23 14:25:26,333 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: register_operation_manager
2025-06-23 14:25:26,333 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: display_manager
2025-06-23 14:25:26,333 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: event_coordinator
2025-06-23 14:25:26,333 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: tool_window_factory
2025-06-23 14:25:26,333 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: register_update_processor
2025-06-23 14:25:26,333 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: lifecycle_manager
2025-06-23 14:25:26,334 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: ui_utility_manager
2025-06-23 14:25:26,334 - DIContainer - [DIContainer.py:210] - INFO - UI管理器服务配置完成
2025-06-23 14:25:26,336 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: batch_manager
2025-06-23 14:25:26,337 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: spi_coordinator
2025-06-23 14:25:26,337 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: ui_event_handler
2025-06-23 14:25:26,337 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: tool_window_manager
2025-06-23 14:25:26,337 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: status_config_manager
2025-06-23 14:25:26,337 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: resource_utility_manager
2025-06-23 14:25:26,337 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: tab_window_manager
2025-06-23 14:25:26,337 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: global_event_manager
2025-06-23 14:25:26,337 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: spi_signal_manager
2025-06-23 14:25:26,337 - DIContainer - [DIContainer.py:243] - INFO - 额外管理器服务配置完成
2025-06-23 14:25:26,338 - RegisterMainWindow - [RegisterMainWindow.py:66] - INFO - 依赖注入容器设置完成
2025-06-23 14:25:26,338 - RegisterMainWindow - [RegisterMainWindow.py:84] - INFO - 管理器依赖注入设置完成
2025-06-23 14:25:26,338 - InitializationManager - [InitializationManager.py:31] - INFO - 正在初始化寄存器配置工具...
2025-06-23 14:25:26,340 - InitializationManager - [InitializationManager.py:40] - INFO - 成功加载寄存器配置文件，包含 125 个寄存器
2025-06-23 14:25:26,343 - spi_service - [spi_service.py:67] - INFO - SPI配置已加载: TIMEOUT=5000ms, RETRY_COUNT=3, RETRY_DELAY=1000ms
2025-06-23 14:25:26,343 - spi_service - [spi_service.py:54] - INFO - SPIService initialized
2025-06-23 14:25:26,343 - spi_service - [spi_service.py:84] - INFO - SPIService initializing...
2025-06-23 14:25:26,344 - spi_service - [spi_service.py:421] - DEBUG - Cleaning up SPI thread...
2025-06-23 14:25:26,344 - spi_service - [spi_service.py:455] - DEBUG - SPI thread resources cleaned.
2025-06-23 14:25:26,344 - spi_service_impl - [spi_service_impl.py:79] - INFO - 已启用模拟模式
2025-06-23 14:25:26,344 - spi_service - [spi_service.py:387] - DEBUG - SPI thread components initialized and signals connected.
2025-06-23 14:25:26,345 - spi_service - [spi_service.py:395] - INFO - Starting SPI thread...
2025-06-23 14:25:26,345 - spi_service - [spi_service.py:111] - INFO - Scanning for available COM ports during initialization...
2025-06-23 14:25:26,345 - spi_service_impl - [spi_service_impl.py:411] - INFO - SPI操作处理线程已启动
2025-06-23 14:25:26,351 - spi_service - [spi_service.py:121] - DEBUG - No COM ports detected during initialization.
2025-06-23 14:25:26,351 - spi_service - [spi_service.py:144] - INFO - No usable hardware ports found. Enabling simulation mode.
2025-06-23 14:25:26,352 - spi_service - [spi_service.py:96] - INFO - SPIService initialization complete.
2025-06-23 14:25:26,352 - InitializationManager - [InitializationManager.py:92] - INFO - SPI服务初始化成功，当前模式：模拟模式
2025-06-23 14:25:26,353 - RegisterUpdateBus - [RegisterUpdateBus.py:59] - INFO - RegisterUpdateBus: Initial clock source set based on register default (0x57, CLKin_SEL_MANUAL): ClkIn1
2025-06-23 14:25:26,354 - ModernBaseHandler - [ModernBaseHandler.py:79] - DEBUG - 成功连接到RegisterUpdateBus
2025-06-23 14:25:26,354 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:77] - INFO - 表格配置初始化完成
2025-06-23 14:25:26,722 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:142] - INFO - 位域表格创建完成
2025-06-23 14:25:26,723 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:102] - INFO - 表格UI创建完成
2025-06-23 14:25:26,723 - ModernBaseHandler - [ModernBaseHandler.py:352] - WARNING - _post_init方法已废弃，请使用新的初始化流程
2025-06-23 14:25:26,723 - ModernBaseHandler - [ModernBaseHandler.py:97] - INFO - ModernBaseHandler: 开始完成初始化
2025-06-23 14:25:26,723 - ModernBaseHandler - [ModernBaseHandler.py:112] - DEBUG - 此处理器不需要UI映射，跳过控件映射构建
2025-06-23 14:25:26,723 - ModernBaseHandler - [ModernBaseHandler.py:118] - INFO - ModernBaseHandler: 初始化完成
2025-06-23 14:25:26,724 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:51] - INFO - 现代化寄存器表格处理器初始化完成
2025-06-23 14:25:26,724 - ModernBaseHandler - [ModernBaseHandler.py:79] - DEBUG - 成功连接到RegisterUpdateBus
2025-06-23 14:25:26,725 - ModernBaseHandler - [ModernBaseHandler.py:352] - WARNING - _post_init方法已废弃，请使用新的初始化流程
2025-06-23 14:25:26,725 - ModernBaseHandler - [ModernBaseHandler.py:97] - INFO - ModernBaseHandler: 开始完成初始化
2025-06-23 14:25:26,725 - ModernBaseHandler - [ModernBaseHandler.py:112] - DEBUG - 此处理器不需要UI映射，跳过控件映射构建
2025-06-23 14:25:26,725 - ModernBaseHandler - [ModernBaseHandler.py:118] - INFO - ModernBaseHandler: 初始化完成
2025-06-23 14:25:26,728 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:78] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-23 14:25:26,728 - ModernBaseHandler - [ModernBaseHandler.py:79] - DEBUG - 成功连接到RegisterUpdateBus
2025-06-23 14:25:26,729 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:81] - DEBUG - 现代化TreeWidget设置最小宽度: 180px，防止布局抖动
2025-06-23 14:25:26,729 - ModernBaseHandler - [ModernBaseHandler.py:352] - WARNING - _post_init方法已废弃，请使用新的初始化流程
2025-06-23 14:25:26,729 - ModernBaseHandler - [ModernBaseHandler.py:97] - INFO - ModernBaseHandler: 开始完成初始化
2025-06-23 14:25:26,729 - ModernBaseHandler - [ModernBaseHandler.py:112] - DEBUG - 此处理器不需要UI映射，跳过控件映射构建
2025-06-23 14:25:26,730 - ModernBaseHandler - [ModernBaseHandler.py:118] - INFO - ModernBaseHandler: 初始化完成
2025-06-23 14:25:26,730 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:51] - INFO - 现代化寄存器树处理器初始化完成
2025-06-23 14:25:26,730 - InitializationManager - [InitializationManager.py:145] - INFO - 现代化处理器创建成功
2025-06-23 14:25:26,730 - InitializationManager - [InitializationManager.py:113] - INFO - 使用现代化处理器创建核心处理器
2025-06-23 14:25:26,730 - BatchOperationManager - [BatchOperationManager.py:85] - INFO - 批量操作配置已加载: BATCH_SIZE=50, TIMEOUT=5000ms, UI_UPDATE_INTERVAL=10
2025-06-23 14:25:26,730 - BatchOperationManager - [BatchOperationManager.py:72] - INFO - 使用传统批量操作方式（已优化性能）
2025-06-23 14:25:26,730 - SPIOperationCoordinator - [SPIOperationCoordinator.py:283] - DEBUG - 已连接SPI操作完成信号
2025-06-23 14:25:26,730 - InitializationManager - [InitializationManager.py:182] - INFO - 使用依赖注入创建管理器完成
2025-06-23 14:25:26,737 - MainWindowUI - [MainWindowUI.py:137] - INFO - MainWindowUI: 检查现代化处理器 - IO: True, Table: True
2025-06-23 14:25:26,737 - MainWindowUI - [MainWindowUI.py:138] - INFO - MainWindowUI: IO处理器类型: <class 'ui.handlers.ModernRegisterIOHandler.ModernRegisterIOHandler'>
2025-06-23 14:25:26,737 - MainWindowUI - [MainWindowUI.py:139] - INFO - MainWindowUI: Table处理器类型: <class 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler'>
2025-06-23 14:25:26,737 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:705] - INFO - ModernRegisterIOHandler: get_io_widget() 被调用
2025-06-23 14:25:26,738 - MainWindowUI - [MainWindowUI.py:69] - INFO - MainWindowUI: 使用QSplitter创建稳定的TreeWidget和TableWidget分割布局
2025-06-23 14:25:26,740 - ProgressBarStyleManager - [ProgressBarStyleManager.py:135] - DEBUG - 已为进度条应用绿色样式: default
2025-06-23 14:25:26,741 - MenuManager - [MenuManager.py:330] - INFO - 插件服务尚未可用，跳过工具插件集成（将在插件系统设置完成后执行）
2025-06-23 14:25:26,742 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:128] - INFO - 填充了 125 个寄存器到树视图
2025-06-23 14:25:26,743 - SPISignalManager - [SPISignalManager.py:41] - DEBUG - SPI信号连接完成
2025-06-23 14:25:26,743 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:164] - INFO - 现代化TreeHandler: 选择默认寄存器: 'R0 (0x00)'
2025-06-23 14:25:26,743 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:181] - INFO - 现代化TreeHandler: 树项点击 - 项: 'R0 (0x00)', 地址: 0x00, 列: 0
2025-06-23 14:25:26,743 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:190] - INFO - 现代化TreeHandler: 选择寄存器地址: 0x00 从项 'R0 (0x00)'
2025-06-23 14:25:26,743 - EventCoordinator - [EventCoordinator.py:169] - DEBUG - EventCoordinator: 开始处理寄存器选择 0x00
2025-06-23 14:25:26,744 - EventCoordinator - [EventCoordinator.py:172] - DEBUG - EventCoordinator: 找到显示管理器，调用 handle_register_selection(0x00)
2025-06-23 14:25:26,744 - RegisterDisplayManager - [RegisterDisplayManager.py:219] - DEBUG - DisplayManager: 开始处理寄存器选择 0x00
2025-06-23 14:25:26,744 - RegisterOperationService - [RegisterOperationService.py:420] - INFO - RegisterOperationService: 处理寄存器选择, addr='0x00'
2025-06-23 14:25:26,744 - RegisterOperationService - [RegisterOperationService.py:234] - INFO - RegisterOperationService: 更新UI显示 - 地址: 0x00, 值: 0x1300
2025-06-23 14:25:26,745 - RegisterOperationService - [RegisterOperationService.py:242] - DEBUG - RegisterOperationService: 跳过表格更新，避免重复调用 - 0x00
2025-06-23 14:25:26,745 - RegisterDisplayManager - [RegisterDisplayManager.py:223] - DEBUG - DisplayManager: 寄存器操作服务返回: success=True, reg_num=0, value=0x1300
2025-06-23 14:25:26,745 - RegisterDisplayManager - [RegisterDisplayManager.py:228] - INFO - DisplayManager: 已更新主窗口选中寄存器地址为: 0x00
2025-06-23 14:25:26,745 - RegisterDisplayManager - [RegisterDisplayManager.py:71] - INFO - DisplayManager: 开始更新位字段显示 - 地址: 0x00, 值: 0x1300
2025-06-23 14:25:26,745 - RegisterDisplayManager - [RegisterDisplayManager.py:75] - INFO - DisplayManager: 找到 table_handler: <class 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler'>
2025-06-23 14:25:26,745 - RegisterDisplayManager - [RegisterDisplayManager.py:76] - INFO - DisplayManager: 调用 table_handler.show_bit_fields(0x00, 4864)
2025-06-23 14:25:26,746 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:808] - DEBUG - ModernTableHandler: 尝试获取主窗口实例...
2025-06-23 14:25:26,746 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:809] - DEBUG - ModernTableHandler: self类型: <class 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler'>
2025-06-23 14:25:26,746 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:810] - DEBUG - ModernTableHandler: hasattr(self, 'main_window'): False
2025-06-23 14:25:26,746 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:811] - DEBUG - ModernTableHandler: hasattr(self, 'parent'): True
2025-06-23 14:25:26,746 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:820] - DEBUG - ModernTableHandler: parent类型: <class 'ui.windows.RegisterMainWindow.RegisterMainWindow'>
2025-06-23 14:25:26,746 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:827] - DEBUG - ModernTableHandler: parent本身就是主窗口
2025-06-23 14:25:26,746 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:232] - INFO - ModernTableHandler: 开始显示位域信息 - 地址: 0x00, 值: 0x1300
2025-06-23 14:25:26,746 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:245] - INFO - ModernTableHandler: 设置当前寄存器 - 地址: 0x00, 值: 0x1300
2025-06-23 14:25:26,746 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:250] - INFO - ModernTableHandler: 获取到 1 个位域
2025-06-23 14:25:26,746 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:264] - DEBUG - ModernTableHandler: 断开表格信号连接
2025-06-23 14:25:26,746 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:268] - INFO - ModernTableHandler: 开始更新表格内容
2025-06-23 14:25:26,746 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:808] - DEBUG - ModernTableHandler: 尝试获取主窗口实例...
2025-06-23 14:25:26,746 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:809] - DEBUG - ModernTableHandler: self类型: <class 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler'>
2025-06-23 14:25:26,746 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:810] - DEBUG - ModernTableHandler: hasattr(self, 'main_window'): False
2025-06-23 14:25:26,746 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:811] - DEBUG - ModernTableHandler: hasattr(self, 'parent'): True
2025-06-23 14:25:26,747 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:820] - DEBUG - ModernTableHandler: parent类型: <class 'ui.windows.RegisterMainWindow.RegisterMainWindow'>
2025-06-23 14:25:26,747 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:827] - DEBUG - ModernTableHandler: parent本身就是主窗口
2025-06-23 14:25:26,747 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:272] - DEBUG - ModernTableHandler: 重新连接表格信号
2025-06-23 14:25:26,747 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:279] - INFO - ModernTableHandler: 成功显示寄存器 0x00 的 1 个位域
2025-06-23 14:25:26,747 - RegisterDisplayManager - [RegisterDisplayManager.py:78] - INFO - DisplayManager: 位字段显示更新完成
2025-06-23 14:25:26,747 - RegisterDisplayManager - [RegisterDisplayManager.py:234] - DEBUG - DisplayManager: handle_register_selection跳过refresh_current_view调用，避免重复更新位字段
2025-06-23 14:25:26,747 - EventCoordinator - [EventCoordinator.py:174] - DEBUG - EventCoordinator: 显示管理器返回结果: True
2025-06-23 14:25:26,747 - InitializationManager - [InitializationManager.py:334] - INFO - 主窗口初始化完成
2025-06-23 14:25:26,753 - DIContainer - [DIContainer.py:46] - INFO - 注册单例实例: plugin_service
2025-06-23 14:25:26,754 - PluginManager - [PluginManager.py:132] - DEBUG - 跳过不存在的插件目录: plugins
2025-06-23 14:25:26,754 - PluginManager - [PluginManager.py:132] - DEBUG - 跳过不存在的插件目录: _internal/plugins
2025-06-23 14:25:26,754 - PluginManager - [PluginManager.py:132] - DEBUG - 跳过不存在的插件目录: ui/tools
2025-06-23 14:25:26,754 - PluginManager - [PluginManager.py:132] - DEBUG - 跳过不存在的插件目录: _internal/ui/tools
2025-06-23 14:25:26,754 - PluginManager - [PluginManager.py:331] - INFO - 找到 0 个工具窗口插件
2025-06-23 14:25:26,754 - PluginMenuService - [PluginMenuService.py:38] - INFO - 没有发现工具窗口插件
2025-06-23 14:25:26,754 - PluginIntegrationService - [PluginIntegrationService.py:86] - INFO - 插件系统初始化完成
2025-06-23 14:25:26,754 - RegisterMainWindow - [RegisterMainWindow.py:131] - INFO - 插件系统设置完成
2025-06-23 14:25:26,944 - InitializationManager - [InitializationManager.py:274] - INFO - InitializationManager: 执行延迟端口刷新，确保UI同步端口状态
2025-06-23 14:25:26,944 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:501] - INFO - ModernRegisterIOHandler: 请求SPI服务刷新端口
2025-06-23 14:25:26,945 - spi_service - [spi_service.py:179] - INFO - Refreshing available COM ports...
2025-06-23 14:25:26,949 - spi_service - [spi_service.py:188] - DEBUG - No COM ports detected.
2025-06-23 14:25:26,949 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:509] - DEBUG - ModernRegisterIOHandler: _update_port_combo 被调用，port_combo: <PyQt5.QtWidgets.QComboBox object at 0x000001CB878035E0>, 类型: <class 'PyQt5.QtWidgets.QComboBox'>
2025-06-23 14:25:26,949 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:567] - INFO - ModernRegisterIOHandler: 没有检测到COM端口
2025-06-23 14:25:26,949 - spi_service - [spi_service.py:203] - INFO - Attempting to set SPI port to: None
2025-06-23 14:25:26,953 - spiPrivacy - [spiPrivacy.py:61] - WARNING - 未检测到可用的串口
2025-06-23 14:25:26,953 - spi_service_impl - [spi_service_impl.py:353] - WARNING - 无法连接到SPI端口: None
2025-06-23 14:25:26,953 - spi_service - [spi_service.py:230] - ERROR - Failed to set SPI port to None. Enabling simulation mode.
2025-06-23 14:25:26,954 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:579] - INFO - ModernRegisterIOHandler: 触发状态栏更新
2025-06-23 14:25:26,954 - StatusAndConfigManager - [StatusAndConfigManager.py:25] - INFO - StatusAndConfigManager.update_status_bar() 被调用
2025-06-23 14:25:26,954 - StatusAndConfigManager - [StatusAndConfigManager.py:30] - INFO - 获取到SPI服务状态: {'connected': False, 'port': None, 'mode': 'simulation', 'last_error': '无法连接到端口 None', 'retry_count': 0}
2025-06-23 14:25:26,954 - StatusAndConfigManager - [StatusAndConfigManager.py:33] - INFO - 原始模式文本: 'simulation'
2025-06-23 14:25:26,954 - StatusAndConfigManager - [StatusAndConfigManager.py:60] - INFO - 状态栏已更新: 模式=模拟模式, 端口=模拟模式
2025-06-23 14:25:26,954 - InitializationManager - [InitializationManager.py:277] - INFO - InitializationManager: 端口刷新完成
2025-06-23 14:25:27,244 - InitializationManager - [InitializationManager.py:289] - INFO - InitializationManager: 执行延迟状态栏更新
2025-06-23 14:25:27,244 - StatusAndConfigManager - [StatusAndConfigManager.py:25] - INFO - StatusAndConfigManager.update_status_bar() 被调用
2025-06-23 14:25:27,244 - StatusAndConfigManager - [StatusAndConfigManager.py:30] - INFO - 获取到SPI服务状态: {'connected': False, 'port': None, 'mode': 'simulation', 'last_error': '无法连接到端口 None', 'retry_count': 0}
2025-06-23 14:25:27,244 - StatusAndConfigManager - [StatusAndConfigManager.py:33] - INFO - 原始模式文本: 'simulation'
2025-06-23 14:25:27,245 - StatusAndConfigManager - [StatusAndConfigManager.py:60] - INFO - 状态栏已更新: 模式=模拟模式, 端口=模拟模式
2025-06-23 14:25:36,056 - ApplicationLifecycleManager - [ApplicationLifecycleManager.py:40] - INFO - 应用程序正在关闭...
2025-06-23 14:25:36,058 - BatchOperationManager - [BatchOperationManager.py:483] - DEBUG - 断开读取信号时出现异常（可能未连接）: 'method' object is not connected
2025-06-23 14:25:36,058 - BatchOperationManager - [BatchOperationManager.py:495] - DEBUG - 断开写入信号时出现异常（可能未连接）: 'method' object is not connected
2025-06-23 14:25:36,058 - BatchOperationManager - [BatchOperationManager.py:530] - INFO - 已强制取消所有批量操作
2025-06-23 14:25:36,058 - ApplicationLifecycleManager - [ApplicationLifecycleManager.py:68] - INFO - 已强制取消所有批量操作并清理资源
2025-06-23 14:25:36,059 - PluginWindowService - [PluginWindowService.py:510] - INFO - 所有插件窗口已关闭
2025-06-23 14:25:36,059 - WindowManagementService - [WindowManagementService.py:199] - INFO - 已通过插件服务关闭所有插件窗口
2025-06-23 14:25:36,060 - WindowManagementService - [WindowManagementService.py:246] - INFO - 已清空所有标签页并隐藏标签页容器
2025-06-23 14:25:36,060 - WindowManagementService - [WindowManagementService.py:223] - INFO - 所有管理的窗口已关闭
2025-06-23 14:25:36,060 - ConfigurationService - [ConfigurationService.py:297] - DEBUG - 设置已更新: simulation_mode = False
2025-06-23 14:25:36,060 - ConfigurationService - [ConfigurationService.py:347] - INFO - 模拟模式设置已保存: False
2025-06-23 14:25:36,060 - spi_service - [spi_service.py:156] - INFO - SPIService cleaning up...
2025-06-23 14:25:36,061 - spi_service - [spi_service.py:421] - DEBUG - Cleaning up SPI thread...
2025-06-23 14:25:36,061 - spi_service - [spi_service.py:428] - DEBUG - Worker stop requested
2025-06-23 14:25:36,061 - spi_service - [spi_service.py:437] - INFO - Requesting SPI thread termination...
2025-06-23 14:25:36,070 - spi_service - [spi_service.py:443] - INFO - SPI thread stopped.
2025-06-23 14:25:36,071 - spi_service - [spi_service.py:455] - DEBUG - SPI thread resources cleaned.
2025-06-23 14:25:36,071 - spi_service - [spi_service.py:166] - INFO - SPIService cleanup finished.
2025-06-23 14:25:36,071 - ApplicationLifecycleManager - [ApplicationLifecycleManager.py:99] - INFO - 应用程序关闭完成
2025-06-23 14:25:36,169 - port_manager - [port_manager.py:211] - INFO - 已清理所有COM端口连接
