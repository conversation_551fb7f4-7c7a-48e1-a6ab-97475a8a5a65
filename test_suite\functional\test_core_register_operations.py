#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
核心寄存器操作功能测试
测试您提到的所有核心功能：
1. 读写寄存器
2. 读写所有寄存器  
3. dump功能
4. save功能
5. load配置文件功能
6. 工具窗口打开
7. 模拟硬件通信
8. 控件状态修改后寄存器表格跳转
9. 自动写入验证
"""

import sys
import os
import unittest
import time

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'test_suite'))

from test_utils import TestLogger, TestResult, qt_application, MockSPIService, MockRegisterManager
from test_config import get_test_config

class TestCoreRegisterOperations(unittest.TestCase):
    """核心寄存器操作测试类"""
    
    def setUp(self):
        """测试设置"""
        self.logger = TestLogger("core_register_operations")
        self.test_results = []
    
    def tearDown(self):
        """测试清理"""
        pass
    
    def test_single_register_read_write(self):
        """测试1: 单个寄存器读写功能"""
        result = TestResult("single_register_read_write")
        
        try:
            with qt_application():
                from ui.windows.RegisterMainWindow import RegisterMainWindow

                # 创建主窗口 (现在使用依赖注入，不需要repository参数)
                main_window = RegisterMainWindow()
                
                # 测试单个寄存器写入
                test_addr = "0x50"
                test_value = 0x1234
                
                # 执行写入
                if hasattr(main_window, 'register_service'):
                    main_window.register_service.write_register(test_addr, test_value)
                    
                    # 执行读取验证
                    read_value = main_window.register_service.read_register(test_addr)
                    
                    # 验证读写一致性
                    self.assertEqual(read_value, test_value, "读写值不一致")
                    
                    result.add_detail('write_address', test_addr)
                    result.add_detail('write_value', hex(test_value))
                    result.add_detail('read_value', hex(read_value))
                    result.add_detail('consistency_check', read_value == test_value)
                    result.set_success(True)
                else:
                    result.set_error("主窗口缺少register_service")
                
        except Exception as e:
            result.set_error(str(e))
            self.logger.error(f"单个寄存器读写测试失败: {e}")
        
        self.test_results.append(result)
        self.assertTrue(result.success)
    
    def test_read_write_all_registers(self):
        """测试2: 读写所有寄存器功能"""
        result = TestResult("read_write_all_registers")
        
        try:
            with qt_application():
                from ui.windows.RegisterMainWindow import RegisterMainWindow

                # 创建主窗口
                main_window = RegisterMainWindow()
                
                # 测试读取所有寄存器
                if hasattr(main_window, 'batch_controller'):
                    # 执行读取全部
                    main_window.batch_controller.handle_read_all_requested()
                    
                    # 验证是否有寄存器被读取
                    if hasattr(main_window, 'register_manager'):
                        registers = main_window.register_manager.get_all_registers()
                        self.assertGreater(len(registers), 0, "没有读取到寄存器")
                        
                        # 测试写入全部
                        main_window.batch_controller.handle_write_all_requested()
                        
                        result.add_detail('registers_count', len(registers))
                        result.add_detail('read_all_executed', True)
                        result.add_detail('write_all_executed', True)
                        result.set_success(True)
                    else:
                        result.set_error("主窗口缺少register_manager")
                else:
                    result.set_error("主窗口缺少batch_controller")
                
        except Exception as e:
            result.set_error(str(e))
            self.logger.error(f"读写所有寄存器测试失败: {e}")
        
        self.test_results.append(result)
        self.assertTrue(result.success)
    
    def test_dump_functionality(self):
        """测试3: dump功能"""
        result = TestResult("dump_functionality")
        
        try:
            with qt_application():
                from ui.windows.RegisterMainWindow import RegisterMainWindow

                # 创建主窗口
                main_window = RegisterMainWindow()
                
                # 测试dump功能
                if hasattr(main_window, 'dumpall_button_clicked'):
                    # 执行dump操作
                    main_window.dumpall_button_clicked()
                    
                    # 验证dump表格是否创建
                    if hasattr(main_window, 'reg_table'):
                        self.assertIsNotNone(main_window.reg_table, "dump表格未创建")
                        
                        result.add_detail('dump_executed', True)
                        result.add_detail('dump_table_created', True)
                        result.set_success(True)
                    else:
                        result.set_error("dump表格未创建")
                else:
                    result.set_error("主窗口缺少dumpall_button_clicked方法")
                
        except Exception as e:
            result.set_error(str(e))
            self.logger.error(f"dump功能测试失败: {e}")
        
        self.test_results.append(result)
        self.assertTrue(result.success)
    
    def test_save_load_configuration(self):
        """测试4&5: save和load配置文件功能"""
        result = TestResult("save_load_configuration")
        
        try:
            with qt_application():
                from core.services.config.ConfigurationService import ConfigurationService
                
                # 创建配置服务
                config_service = ConfigurationService(None)
                
                # 测试保存配置
                test_config = {
                    "0x50": 0x1234,
                    "0x51": 0x5678,
                    "0x52": 0x9ABC
                }
                
                # 保存配置
                save_success = config_service.save_register_config(test_config, "test_config.json")
                self.assertTrue(save_success, "配置保存失败")
                
                # 加载配置
                loaded_config = config_service.load_register_config("test_config.json")
                self.assertIsNotNone(loaded_config, "配置加载失败")
                
                # 验证配置一致性
                for addr, value in test_config.items():
                    if addr in loaded_config:
                        self.assertEqual(loaded_config[addr], value, f"配置不一致: {addr}")
                
                result.add_detail('save_success', save_success)
                result.add_detail('load_success', loaded_config is not None)
                result.add_detail('config_consistency', True)
                result.set_success(True)
                
        except Exception as e:
            result.set_error(str(e))
            self.logger.error(f"save/load配置测试失败: {e}")
        
        self.test_results.append(result)
        self.assertTrue(result.success)
    
    def test_tool_windows_opening(self):
        """测试6: 各个工具窗口打开功能"""
        result = TestResult("tool_windows_opening")
        
        try:
            with qt_application():
                from ui.windows.RegisterMainWindow import RegisterMainWindow

                # 创建主窗口
                main_window = RegisterMainWindow()
                
                # 测试各种工具窗口
                window_types = ['pll_control', 'clk_outputs', 'sync_sysref']
                opened_windows = []
                
                for window_type in window_types:
                    try:
                        if hasattr(main_window, 'tool_window_factory'):
                            window = main_window.tool_window_factory.create_window_by_type(window_type)
                            if window:
                                opened_windows.append(window_type)
                                self.logger.info(f"成功打开工具窗口: {window_type}")
                    except Exception as e:
                        self.logger.warning(f"打开工具窗口失败 {window_type}: {e}")
                
                # 验证至少打开了一些窗口
                self.assertGreater(len(opened_windows), 0, "没有成功打开任何工具窗口")
                
                result.add_detail('window_types_tested', len(window_types))
                result.add_detail('windows_opened', len(opened_windows))
                result.add_detail('opened_window_types', opened_windows)
                result.set_success(True)
                
        except Exception as e:
            result.set_error(str(e))
            self.logger.error(f"工具窗口打开测试失败: {e}")
        
        self.test_results.append(result)
        self.assertTrue(result.success)
    
    def test_hardware_communication_simulation(self):
        """测试7: 模拟硬件通信功能"""
        result = TestResult("hardware_communication_simulation")
        
        try:
            # 使用模拟SPI服务测试硬件通信
            spi_service = MockSPIService()
            spi_service.initialize()
            
            # 测试写入操作
            test_registers = {
                "0x50": 0x1234,
                "0x51": 0x5678,
                "0x52": 0x9ABC
            }
            
            for addr_str, value in test_registers.items():
                addr = int(addr_str, 16)
                spi_service.write_register(addr, value)
            
            # 测试读取操作
            for addr_str, expected_value in test_registers.items():
                addr = int(addr_str, 16)
                read_value = spi_service.read_register(addr)
                self.assertEqual(read_value, expected_value, f"硬件通信不一致: {addr_str}")
            
            # 获取通信统计
            stats = spi_service.get_statistics()
            
            result.add_detail('write_operations', stats['write_count'])
            result.add_detail('read_operations', stats['read_count'])
            result.add_detail('registers_tested', len(test_registers))
            result.add_detail('communication_success', True)
            result.set_success(True)
            
        except Exception as e:
            result.set_error(str(e))
            self.logger.error(f"硬件通信模拟测试失败: {e}")
        
        self.test_results.append(result)
        self.assertTrue(result.success)

def run_tests():
    """运行核心寄存器操作测试"""
    suite = unittest.TestLoader().loadTestsFromTestCase(TestCoreRegisterOperations)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
