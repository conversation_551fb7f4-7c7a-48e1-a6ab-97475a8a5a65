# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['main.py'],
    pathex=['D:\\Program Files\\Python38\\python.exe/../DLLs', 'D:\\Program Files\\Python38\\python.exe/..'],
    binaries=[],
    datas=[('config', 'config'), ('images', 'images'), ('ui/forms', 'ui/forms')],
    hiddenimports=['ui.handlers.ModernSetModesHandler', 'ui.handlers.ModernClkinControlHandler', 'ui.handlers.ModernPLLHandler', 'ui.handlers.ModernSyncSysRefHandler', 'ui.handlers.ModernClkOutputsHandler', 'ui.handlers.ModernRegisterTableHandler', 'ui.handlers.ModernUIEventHandler', 'ui.handlers.ModernRegisterIOHandler', 'ui.handlers.ModernRegisterTreeHandler', 'ui.handlers.ModernBaseHandler', 'PyQt5.QtCore', 'PyQt5.QtWidgets', 'PyQt5.QtGui', 'serial', 'serial.tools.list_ports'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=True,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [('v', None, 'OPTION')],
    exclude_binaries=True,
    name='FSJ04832_RegisterTool',
    debug=True,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=False,
    upx_exclude=[],
    name='FSJ04832_RegisterTool',
)
