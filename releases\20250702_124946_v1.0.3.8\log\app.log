2025-07-02 12:50:48,431 - main - [main.py:50] - INFO - 中文支持设置完成，使用字体: Microsoft YaHei
2025-07-02 12:50:48,431 - ConfigurationManager - [ConfigurationManager.py:59] - INFO - 已加载配置文件: C:\Users\<USER>\AppData\Local\Temp\_MEI284682\config\default.json
2025-07-02 12:50:48,431 - ConfigurationManager - [ConfigurationManager.py:85] - DEBUG - 跳过可选配置文件: app.json
2025-07-02 12:50:48,431 - ConfigurationManager - [ConfigurationManager.py:59] - INFO - 已加载配置文件: C:\Users\<USER>\AppData\Local\Temp\_MEI284682\config\local.json
2025-07-02 12:50:48,431 - ConfigurationManager - [ConfigurationManager.py:87] - INFO - 配置加载完成，已加载文件: default.json, local.json
2025-07-02 12:50:48,431 - RegisterMainWindow - [RegisterMainWindow.py:172] - INFO - 配置加载完成
2025-07-02 12:50:48,431 - VersionService - [VersionService.py:65] - WARNING - 未找到版本文件，使用默认版本信息
2025-07-02 12:50:48,431 - RegisterMainWindow - [RegisterMainWindow.py:214] - INFO - 窗口标题已设置为: FSJ04832 寄存器配置工具 v1.0.0.0
2025-07-02 12:50:48,431 - RegisterMainWindow - [RegisterMainWindow.py:183] - INFO - 窗口大小设置为: 1840x1100
2025-07-02 12:50:48,431 - RegisterMainWindow - [RegisterMainWindow.py:200] - INFO - 操作常量已加载: TIMEOUT=5000ms, BATCH_SIZE=50
2025-07-02 12:50:48,431 - DIContainer - [DIContainer.py:46] - INFO - 注册单例实例: main_window
2025-07-02 12:50:48,444 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: spi_service
2025-07-02 12:50:48,444 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: register_manager
2025-07-02 12:50:48,444 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: event_bus
2025-07-02 12:50:48,444 - DIContainer - [DIContainer.py:181] - INFO - 核心服务配置完成
2025-07-02 12:50:48,460 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: initialization_manager
2025-07-02 12:50:48,460 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: register_operation_manager
2025-07-02 12:50:48,460 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: display_manager
2025-07-02 12:50:48,460 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: event_coordinator
2025-07-02 12:50:48,460 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: tool_window_factory
2025-07-02 12:50:48,460 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: register_update_processor
2025-07-02 12:50:48,460 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: lifecycle_manager
2025-07-02 12:50:48,460 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: ui_utility_manager
2025-07-02 12:50:48,460 - DIContainer - [DIContainer.py:210] - INFO - UI管理器服务配置完成
2025-07-02 12:50:48,462 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: batch_manager
2025-07-02 12:50:48,462 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: spi_coordinator
2025-07-02 12:50:48,462 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: ui_event_handler
2025-07-02 12:50:48,462 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: tool_window_manager
2025-07-02 12:50:48,462 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: status_config_manager
2025-07-02 12:50:48,462 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: resource_utility_manager
2025-07-02 12:50:48,462 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: tab_window_manager
2025-07-02 12:50:48,462 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: global_event_manager
2025-07-02 12:50:48,462 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: spi_signal_manager
2025-07-02 12:50:48,462 - DIContainer - [DIContainer.py:243] - INFO - 额外管理器服务配置完成
2025-07-02 12:50:48,462 - RegisterMainWindow - [RegisterMainWindow.py:69] - INFO - 依赖注入容器设置完成
2025-07-02 12:50:48,462 - RegisterMainWindow - [RegisterMainWindow.py:87] - INFO - 管理器依赖注入设置完成
2025-07-02 12:50:48,463 - InitializationManager - [InitializationManager.py:31] - INFO - 正在初始化寄存器配置工具...
2025-07-02 12:50:48,465 - InitializationManager - [InitializationManager.py:40] - INFO - 成功加载寄存器配置文件，包含 125 个寄存器
2025-07-02 12:50:48,466 - spi_service - [spi_service.py:67] - INFO - SPI配置已加载: TIMEOUT=5000ms, RETRY_COUNT=3, RETRY_DELAY=1000ms
2025-07-02 12:50:48,466 - spi_service - [spi_service.py:54] - INFO - SPIService initialized
2025-07-02 12:50:48,466 - spi_service - [spi_service.py:84] - INFO - SPIService initializing...
2025-07-02 12:50:48,466 - spi_service - [spi_service.py:421] - DEBUG - Cleaning up SPI thread...
2025-07-02 12:50:48,466 - spi_service - [spi_service.py:455] - DEBUG - SPI thread resources cleaned.
2025-07-02 12:50:48,466 - spi_service_impl - [spi_service_impl.py:79] - INFO - 已启用模拟模式
2025-07-02 12:50:48,466 - spi_service - [spi_service.py:387] - DEBUG - SPI thread components initialized and signals connected.
2025-07-02 12:50:48,466 - spi_service - [spi_service.py:395] - INFO - Starting SPI thread...
2025-07-02 12:50:48,466 - spi_service - [spi_service.py:111] - INFO - Scanning for available COM ports during initialization...
2025-07-02 12:50:48,466 - spi_service_impl - [spi_service_impl.py:411] - INFO - SPI操作处理线程已启动
2025-07-02 12:50:48,471 - spi_service - [spi_service.py:119] - DEBUG - Found port during init: COM3 - USB 串行设备 (COM3)
2025-07-02 12:50:48,471 - spi_service - [spi_service.py:128] - INFO - Attempting to connect to first available port: COM3
2025-07-02 12:50:48,479 - port_manager - [port_manager.py:123] - INFO - 成功打开端口: COM3
2025-07-02 12:50:48,479 - spiPrivacy - [spiPrivacy.py:75] - INFO - 通过端口管理器成功打开串口: COM3
2025-07-02 12:50:48,479 - spi_service_impl - [spi_service_impl.py:347] - INFO - 成功连接到SPI端口: COM3
2025-07-02 12:50:48,479 - spi_service - [spi_service.py:134] - INFO - Successfully connected to COM3 during initialization. Using hardware mode.
2025-07-02 12:50:48,479 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-02 12:50:48,479 - spi_service_impl - [spi_service_impl.py:81] - INFO - 已禁用模拟模式，使用实际SPI硬件
2025-07-02 12:50:48,479 - spi_service - [spi_service.py:318] - INFO - Simulation mode disabled.
2025-07-02 12:50:48,479 - spi_service - [spi_service.py:357] - INFO - SPI连接状态已更新: 已连接
2025-07-02 12:50:48,479 - spi_service - [spi_service.py:96] - INFO - SPIService initialization complete.
2025-07-02 12:50:48,479 - InitializationManager - [InitializationManager.py:92] - INFO - SPI服务初始化成功，当前模式：硬件模式
2025-07-02 12:50:48,481 - RegisterUpdateBus - [RegisterUpdateBus.py:62] - INFO - RegisterUpdateBus: Initial clock source set based on register default (0x57, CLKin_SEL_MANUAL): ClkIn1
2025-07-02 12:50:48,481 - ModernBaseHandler - [ModernBaseHandler.py:79] - DEBUG - 成功连接到RegisterUpdateBus
2025-07-02 12:50:48,481 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:77] - INFO - 表格配置初始化完成
2025-07-02 12:50:48,943 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:142] - INFO - 位域表格创建完成
2025-07-02 12:50:48,943 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:102] - INFO - 表格UI创建完成
2025-07-02 12:50:48,943 - ModernBaseHandler - [ModernBaseHandler.py:452] - WARNING - _post_init方法已废弃，请使用新的初始化流程
2025-07-02 12:50:48,943 - ModernBaseHandler - [ModernBaseHandler.py:97] - INFO - ModernBaseHandler: 开始完成初始化
2025-07-02 12:50:48,943 - ModernBaseHandler - [ModernBaseHandler.py:112] - DEBUG - 此处理器不需要UI映射，跳过控件映射构建
2025-07-02 12:50:48,945 - ModernBaseHandler - [ModernBaseHandler.py:273] - INFO - ModernRegisterTableHandler: 窗口已居中显示在 (585, 190)，窗口尺寸: 750x700
2025-07-02 12:50:48,945 - ModernBaseHandler - [ModernBaseHandler.py:289] - DEBUG - ModernRegisterTableHandler: 已设置窗口激活功能
2025-07-02 12:50:48,945 - ModernBaseHandler - [ModernBaseHandler.py:242] - INFO - ModernRegisterTableHandler: 设置窗口默认尺寸为 750 x 700，已居中显示
2025-07-02 12:50:48,945 - ModernBaseHandler - [ModernBaseHandler.py:121] - INFO - ModernBaseHandler: 初始化完成
2025-07-02 12:50:48,945 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:51] - INFO - 现代化寄存器表格处理器初始化完成
2025-07-02 12:50:48,945 - ModernBaseHandler - [ModernBaseHandler.py:79] - DEBUG - 成功连接到RegisterUpdateBus
2025-07-02 12:50:48,945 - ModernBaseHandler - [ModernBaseHandler.py:452] - WARNING - _post_init方法已废弃，请使用新的初始化流程
2025-07-02 12:50:48,945 - ModernBaseHandler - [ModernBaseHandler.py:97] - INFO - ModernBaseHandler: 开始完成初始化
2025-07-02 12:50:48,945 - ModernBaseHandler - [ModernBaseHandler.py:112] - DEBUG - 此处理器不需要UI映射，跳过控件映射构建
2025-07-02 12:50:48,947 - ModernBaseHandler - [ModernBaseHandler.py:273] - INFO - ModernRegisterIOHandler: 窗口已居中显示在 (585, 190)，窗口尺寸: 750x700
2025-07-02 12:50:48,947 - ModernBaseHandler - [ModernBaseHandler.py:289] - DEBUG - ModernRegisterIOHandler: 已设置窗口激活功能
2025-07-02 12:50:48,947 - ModernBaseHandler - [ModernBaseHandler.py:242] - INFO - ModernRegisterIOHandler: 设置窗口默认尺寸为 750 x 700，已居中显示
2025-07-02 12:50:48,947 - ModernBaseHandler - [ModernBaseHandler.py:121] - INFO - ModernBaseHandler: 初始化完成
2025-07-02 12:50:48,947 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:78] - INFO - 现代化寄存器IO处理器初始化完成
2025-07-02 12:50:48,947 - ModernBaseHandler - [ModernBaseHandler.py:79] - DEBUG - 成功连接到RegisterUpdateBus
2025-07-02 12:50:48,947 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:81] - DEBUG - 现代化TreeWidget设置最小宽度: 180px，防止布局抖动
2025-07-02 12:50:48,947 - ModernBaseHandler - [ModernBaseHandler.py:452] - WARNING - _post_init方法已废弃，请使用新的初始化流程
2025-07-02 12:50:48,947 - ModernBaseHandler - [ModernBaseHandler.py:97] - INFO - ModernBaseHandler: 开始完成初始化
2025-07-02 12:50:48,947 - ModernBaseHandler - [ModernBaseHandler.py:112] - DEBUG - 此处理器不需要UI映射，跳过控件映射构建
2025-07-02 12:50:48,947 - ModernBaseHandler - [ModernBaseHandler.py:273] - INFO - ModernRegisterTreeHandler: 窗口已居中显示在 (585, 190)，窗口尺寸: 750x700
2025-07-02 12:50:48,947 - ModernBaseHandler - [ModernBaseHandler.py:289] - DEBUG - ModernRegisterTreeHandler: 已设置窗口激活功能
2025-07-02 12:50:48,947 - ModernBaseHandler - [ModernBaseHandler.py:242] - INFO - ModernRegisterTreeHandler: 设置窗口默认尺寸为 750 x 700，已居中显示
2025-07-02 12:50:48,947 - ModernBaseHandler - [ModernBaseHandler.py:121] - INFO - ModernBaseHandler: 初始化完成
2025-07-02 12:50:48,947 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:51] - INFO - 现代化寄存器树处理器初始化完成
2025-07-02 12:50:48,947 - InitializationManager - [InitializationManager.py:145] - INFO - 现代化处理器创建成功
2025-07-02 12:50:48,947 - InitializationManager - [InitializationManager.py:113] - INFO - 使用现代化处理器创建核心处理器
2025-07-02 12:50:48,947 - BatchOperationManager - [BatchOperationManager.py:85] - INFO - 批量操作配置已加载: BATCH_SIZE=50, TIMEOUT=5000ms, UI_UPDATE_INTERVAL=10
2025-07-02 12:50:48,947 - BatchOperationManager - [BatchOperationManager.py:72] - INFO - 使用传统批量操作方式（已优化性能）
2025-07-02 12:50:48,947 - SPIOperationCoordinator - [SPIOperationCoordinator.py:283] - DEBUG - 已连接SPI操作完成信号
2025-07-02 12:50:48,947 - InitializationManager - [InitializationManager.py:182] - INFO - 使用依赖注入创建管理器完成
2025-07-02 12:50:49,171 - MainWindowUI - [MainWindowUI.py:137] - INFO - MainWindowUI: 检查现代化处理器 - IO: True, Table: True
2025-07-02 12:50:49,171 - MainWindowUI - [MainWindowUI.py:138] - INFO - MainWindowUI: IO处理器类型: <class 'ui.handlers.ModernRegisterIOHandler.ModernRegisterIOHandler'>
2025-07-02 12:50:49,171 - MainWindowUI - [MainWindowUI.py:139] - INFO - MainWindowUI: Table处理器类型: <class 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler'>
2025-07-02 12:50:49,173 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:705] - INFO - ModernRegisterIOHandler: get_io_widget() 被调用
2025-07-02 12:50:49,173 - MainWindowUI - [MainWindowUI.py:69] - INFO - MainWindowUI: 使用QSplitter创建稳定的TreeWidget和TableWidget分割布局
2025-07-02 12:50:49,177 - ProgressBarStyleManager - [ProgressBarStyleManager.py:135] - DEBUG - 已为进度条应用绿色样式: default
2025-07-02 12:50:49,180 - MenuManager - [MenuManager.py:330] - INFO - 插件服务尚未可用，跳过工具插件集成（将在插件系统设置完成后执行）
2025-07-02 12:50:49,181 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:128] - INFO - 填充了 125 个寄存器到树视图
2025-07-02 12:50:49,181 - SPISignalManager - [SPISignalManager.py:41] - DEBUG - SPI信号连接完成
2025-07-02 12:50:49,184 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:164] - INFO - 现代化TreeHandler: 选择默认寄存器: 'R0 (0x00)'
2025-07-02 12:50:49,184 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:176] - INFO - 现代化TreeHandler: 树项点击 - 项: 'R0 (0x00)', 地址: 0x00, 列: 0
2025-07-02 12:50:49,184 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:185] - INFO - 现代化TreeHandler: 选择寄存器地址: 0x00 从项 'R0 (0x00)'
2025-07-02 12:50:49,185 - EventCoordinator - [EventCoordinator.py:169] - DEBUG - EventCoordinator: 开始处理寄存器选择 0x00
2025-07-02 12:50:49,185 - EventCoordinator - [EventCoordinator.py:172] - DEBUG - EventCoordinator: 找到显示管理器，调用 handle_register_selection(0x00)
2025-07-02 12:50:49,185 - RegisterDisplayManager - [RegisterDisplayManager.py:209] - DEBUG - DisplayManager: 开始处理寄存器选择 0x00
2025-07-02 12:50:49,185 - RegisterOperationService - [RegisterOperationService.py:420] - INFO - RegisterOperationService: 处理寄存器选择, addr='0x00'
2025-07-02 12:50:49,185 - RegisterOperationService - [RegisterOperationService.py:234] - INFO - RegisterOperationService: 更新UI显示 - 地址: 0x00, 值: 0x1300
2025-07-02 12:50:49,185 - RegisterOperationService - [RegisterOperationService.py:242] - DEBUG - RegisterOperationService: 跳过表格更新，避免重复调用 - 0x00
2025-07-02 12:50:49,185 - RegisterDisplayManager - [RegisterDisplayManager.py:213] - DEBUG - DisplayManager: 寄存器操作服务返回: success=True, reg_num=0, value=0x1300
2025-07-02 12:50:49,186 - RegisterDisplayManager - [RegisterDisplayManager.py:218] - INFO - DisplayManager: 已更新主窗口选中寄存器地址为: 0x00
2025-07-02 12:50:49,186 - RegisterDisplayManager - [RegisterDisplayManager.py:71] - INFO - DisplayManager: 开始更新位字段显示 - 地址: 0x00, 值: 0x1300
2025-07-02 12:50:49,186 - RegisterDisplayManager - [RegisterDisplayManager.py:75] - INFO - DisplayManager: 找到 table_handler: <class 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler'>
2025-07-02 12:50:49,186 - RegisterDisplayManager - [RegisterDisplayManager.py:76] - INFO - DisplayManager: 调用 table_handler.show_bit_fields(0x00, 4864, from_global_update=True)
2025-07-02 12:50:49,186 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:809] - DEBUG - ModernTableHandler: 尝试获取主窗口实例...
2025-07-02 12:50:49,186 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:810] - DEBUG - ModernTableHandler: self类型: <class 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler'>
2025-07-02 12:50:49,186 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:811] - DEBUG - ModernTableHandler: hasattr(self, 'main_window'): False
2025-07-02 12:50:49,186 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:812] - DEBUG - ModernTableHandler: hasattr(self, 'parent'): True
2025-07-02 12:50:49,186 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:821] - DEBUG - ModernTableHandler: parent类型: <class 'ui.windows.RegisterMainWindow.RegisterMainWindow'>
2025-07-02 12:50:49,187 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:828] - DEBUG - ModernTableHandler: parent本身就是主窗口
2025-07-02 12:50:49,187 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:233] - INFO - ModernTableHandler: 开始显示位域信息 - 地址: 0x00, 值: 0x1300
2025-07-02 12:50:49,187 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:246] - INFO - ModernTableHandler: 设置当前寄存器 - 地址: 0x00, 值: 0x1300
2025-07-02 12:50:49,187 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:251] - INFO - ModernTableHandler: 获取到 1 个位域
2025-07-02 12:50:49,187 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:265] - DEBUG - ModernTableHandler: 断开表格信号连接
2025-07-02 12:50:49,187 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:269] - INFO - ModernTableHandler: 开始更新表格内容
2025-07-02 12:50:49,187 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:809] - DEBUG - ModernTableHandler: 尝试获取主窗口实例...
2025-07-02 12:50:49,187 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:810] - DEBUG - ModernTableHandler: self类型: <class 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler'>
2025-07-02 12:50:49,187 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:811] - DEBUG - ModernTableHandler: hasattr(self, 'main_window'): False
2025-07-02 12:50:49,187 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:812] - DEBUG - ModernTableHandler: hasattr(self, 'parent'): True
2025-07-02 12:50:49,187 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:821] - DEBUG - ModernTableHandler: parent类型: <class 'ui.windows.RegisterMainWindow.RegisterMainWindow'>
2025-07-02 12:50:49,187 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:828] - DEBUG - ModernTableHandler: parent本身就是主窗口
2025-07-02 12:50:49,188 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:273] - DEBUG - ModernTableHandler: 重新连接表格信号
2025-07-02 12:50:49,188 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:280] - INFO - ModernTableHandler: 成功显示寄存器 0x00 的 1 个位域
2025-07-02 12:50:49,188 - RegisterDisplayManager - [RegisterDisplayManager.py:78] - INFO - DisplayManager: 位字段显示更新完成
2025-07-02 12:50:49,188 - EventCoordinator - [EventCoordinator.py:174] - DEBUG - EventCoordinator: 显示管理器返回结果: True
2025-07-02 12:50:49,188 - InitializationManager - [InitializationManager.py:334] - INFO - 主窗口初始化完成
2025-07-02 12:50:49,189 - RegisterMainWindow - [RegisterMainWindow.py:247] - INFO - 主窗口点击置顶功能已设置
2025-07-02 12:50:49,193 - DIContainer - [DIContainer.py:46] - INFO - 注册单例实例: plugin_service
2025-07-02 12:50:49,193 - PluginManager - [PluginManager.py:132] - DEBUG - 跳过不存在的插件目录: plugins
2025-07-02 12:50:49,193 - PluginManager - [PluginManager.py:132] - DEBUG - 跳过不存在的插件目录: _internal/plugins
2025-07-02 12:50:49,193 - PluginManager - [PluginManager.py:132] - DEBUG - 跳过不存在的插件目录: ui/tools
2025-07-02 12:50:49,194 - PluginManager - [PluginManager.py:132] - DEBUG - 跳过不存在的插件目录: _internal/ui/tools
2025-07-02 12:50:49,194 - PluginManager - [PluginManager.py:331] - INFO - 找到 0 个工具窗口插件
2025-07-02 12:50:49,194 - PluginMenuService - [PluginMenuService.py:38] - INFO - 没有发现工具窗口插件
2025-07-02 12:50:49,194 - PluginIntegrationService - [PluginIntegrationService.py:86] - INFO - 插件系统初始化完成
2025-07-02 12:50:49,194 - RegisterMainWindow - [RegisterMainWindow.py:134] - INFO - 插件系统设置完成
2025-07-02 12:50:49,327 - RegisterMainWindow - [RegisterMainWindow.py:387] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-02 12:50:49,328 - RegisterMainWindow - [RegisterMainWindow.py:387] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-02 12:50:49,336 - RegisterMainWindow - [RegisterMainWindow.py:406] - DEBUG - 主窗口强制置顶完成
2025-07-02 12:50:49,336 - RegisterMainWindow - [RegisterMainWindow.py:311] - DEBUG - 主窗口已通过焦点置顶
2025-07-02 12:50:49,345 - RegisterMainWindow - [RegisterMainWindow.py:406] - DEBUG - 主窗口强制置顶完成
2025-07-02 12:50:49,346 - RegisterMainWindow - [RegisterMainWindow.py:315] - DEBUG - 主窗口已通过激活事件置顶
2025-07-02 12:50:49,381 - InitializationManager - [InitializationManager.py:274] - INFO - InitializationManager: 执行延迟端口刷新，确保UI同步端口状态
2025-07-02 12:50:49,381 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:501] - INFO - ModernRegisterIOHandler: 请求SPI服务刷新端口
2025-07-02 12:50:49,381 - spi_service - [spi_service.py:179] - INFO - Refreshing available COM ports...
2025-07-02 12:50:49,387 - spi_service - [spi_service.py:186] - DEBUG - Found port: COM3 - USB 串行设备 (COM3)
2025-07-02 12:50:49,387 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:509] - DEBUG - ModernRegisterIOHandler: _update_port_combo 被调用，port_combo: <PyQt5.QtWidgets.QComboBox object at 0x000002466771E0D0>, 类型: <class 'PyQt5.QtWidgets.QComboBox'>
2025-07-02 12:50:49,387 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:528] - INFO - ModernRegisterIOHandler: SPI服务已连接到端口: COM3
2025-07-02 12:50:49,388 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:537] - INFO - ModernRegisterIOHandler: 添加COM端口到下拉框: COM3
2025-07-02 12:50:49,388 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:546] - INFO - ModernRegisterIOHandler: 选择端口: COM3
2025-07-02 12:50:49,388 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:561] - INFO - ModernRegisterIOHandler: SPI服务已连接，UI已同步端口显示
2025-07-02 12:50:49,388 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:579] - INFO - ModernRegisterIOHandler: 触发状态栏更新
2025-07-02 12:50:49,388 - StatusAndConfigManager - [StatusAndConfigManager.py:25] - INFO - StatusAndConfigManager.update_status_bar() 被调用
2025-07-02 12:50:49,388 - StatusAndConfigManager - [StatusAndConfigManager.py:30] - INFO - 获取到SPI服务状态: {'connected': True, 'port': 'COM3', 'mode': 'hardware', 'last_error': None, 'retry_count': 0}
2025-07-02 12:50:49,388 - StatusAndConfigManager - [StatusAndConfigManager.py:33] - INFO - 原始模式文本: 'hardware'
2025-07-02 12:50:49,388 - StatusAndConfigManager - [StatusAndConfigManager.py:60] - INFO - 状态栏已更新: 模式=硬件模式, 端口=COM3
2025-07-02 12:50:49,388 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:509] - DEBUG - ModernRegisterIOHandler: _update_port_combo 被调用，port_combo: <PyQt5.QtWidgets.QComboBox object at 0x000002466771E0D0>, 类型: <class 'PyQt5.QtWidgets.QComboBox'>
2025-07-02 12:50:49,388 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:516] - INFO - ModernRegisterIOHandler: 端口信息未变化，跳过重复更新
2025-07-02 12:50:49,388 - InitializationManager - [InitializationManager.py:277] - INFO - InitializationManager: 端口刷新完成
2025-07-02 12:50:49,684 - InitializationManager - [InitializationManager.py:289] - INFO - InitializationManager: 执行延迟状态栏更新
2025-07-02 12:50:49,684 - StatusAndConfigManager - [StatusAndConfigManager.py:25] - INFO - StatusAndConfigManager.update_status_bar() 被调用
2025-07-02 12:50:49,684 - StatusAndConfigManager - [StatusAndConfigManager.py:30] - INFO - 获取到SPI服务状态: {'connected': True, 'port': 'COM3', 'mode': 'hardware', 'last_error': None, 'retry_count': 0}
2025-07-02 12:50:49,684 - StatusAndConfigManager - [StatusAndConfigManager.py:33] - INFO - 原始模式文本: 'hardware'
2025-07-02 12:50:49,684 - StatusAndConfigManager - [StatusAndConfigManager.py:60] - INFO - 状态栏已更新: 模式=硬件模式, 端口=COM3
2025-07-02 12:50:50,188 - RegisterMainWindow - [RegisterMainWindow.py:281] - INFO - 已为寄存器树安装点击事件过滤器
2025-07-02 12:50:50,188 - RegisterMainWindow - [RegisterMainWindow.py:286] - INFO - 已为寄存器表格安装点击事件过滤器
2025-07-02 12:50:56,389 - RegisterMainWindow - [RegisterMainWindow.py:387] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-02 12:50:56,394 - RegisterMainWindow - [RegisterMainWindow.py:406] - DEBUG - 主窗口强制置顶完成
2025-07-02 12:50:56,395 - RegisterMainWindow - [RegisterMainWindow.py:311] - DEBUG - 主窗口已通过焦点置顶
2025-07-02 12:51:03,981 - RegisterMainWindow - [RegisterMainWindow.py:387] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-02 12:51:03,996 - RegisterMainWindow - [RegisterMainWindow.py:387] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-02 12:51:04,007 - RegisterMainWindow - [RegisterMainWindow.py:406] - DEBUG - 主窗口强制置顶完成
2025-07-02 12:51:04,007 - RegisterMainWindow - [RegisterMainWindow.py:311] - DEBUG - 主窗口已通过焦点置顶
2025-07-02 12:51:04,033 - RegisterMainWindow - [RegisterMainWindow.py:406] - DEBUG - 主窗口强制置顶完成
2025-07-02 12:51:04,033 - RegisterMainWindow - [RegisterMainWindow.py:315] - DEBUG - 主窗口已通过激活事件置顶
2025-07-02 12:51:05,214 - RegisterMainWindow - [RegisterMainWindow.py:387] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-02 12:51:05,214 - RegisterMainWindow - [RegisterMainWindow.py:387] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-02 12:51:05,214 - ApplicationLifecycleManager - [ApplicationLifecycleManager.py:41] - INFO - 应用程序正在关闭...
2025-07-02 12:51:05,214 - BatchOperationManager - [BatchOperationManager.py:483] - DEBUG - 断开读取信号时出现异常（可能未连接）: 'method' object is not connected
2025-07-02 12:51:05,214 - BatchOperationManager - [BatchOperationManager.py:495] - DEBUG - 断开写入信号时出现异常（可能未连接）: 'method' object is not connected
2025-07-02 12:51:05,214 - BatchOperationManager - [BatchOperationManager.py:533] - INFO - 已强制取消所有批量操作
2025-07-02 12:51:05,219 - ApplicationLifecycleManager - [ApplicationLifecycleManager.py:69] - INFO - 已强制取消所有批量操作并清理资源
2025-07-02 12:51:05,219 - PluginWindowService - [PluginWindowService.py:513] - INFO - 所有插件窗口已关闭
2025-07-02 12:51:05,219 - WindowManagementService - [WindowManagementService.py:200] - INFO - 已通过插件服务关闭所有插件窗口
2025-07-02 12:51:05,219 - WindowManagementService - [WindowManagementService.py:250] - INFO - 已清空所有标签页并隐藏标签页容器
2025-07-02 12:51:05,219 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-02 12:51:05,219 - WindowManagementService - [WindowManagementService.py:227] - INFO - 所有管理的窗口已关闭
2025-07-02 12:51:05,219 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-02 12:51:05,219 - ConfigurationService - [ConfigurationService.py:283] - DEBUG - 设置已更新: simulation_mode = False
2025-07-02 12:51:05,219 - ConfigurationService - [ConfigurationService.py:333] - INFO - 模拟模式设置已保存: False
2025-07-02 12:51:05,219 - spi_service - [spi_service.py:156] - INFO - SPIService cleaning up...
2025-07-02 12:51:05,219 - spi_service - [spi_service.py:421] - DEBUG - Cleaning up SPI thread...
2025-07-02 12:51:05,219 - port_manager - [port_manager.py:149] - INFO - 已关闭端口: COM3
2025-07-02 12:51:05,219 - spiPrivacy - [spiPrivacy.py:466] - INFO - 通过端口管理器关闭串口: COM3
2025-07-02 12:51:05,219 - spi_service - [spi_service.py:428] - DEBUG - Worker stop requested
2025-07-02 12:51:05,219 - spi_service - [spi_service.py:437] - INFO - Requesting SPI thread termination...
2025-07-02 12:51:05,234 - spi_service - [spi_service.py:443] - INFO - SPI thread stopped.
2025-07-02 12:51:05,234 - spi_service - [spi_service.py:455] - DEBUG - SPI thread resources cleaned.
2025-07-02 12:51:05,234 - spi_service - [spi_service.py:166] - INFO - SPIService cleanup finished.
2025-07-02 12:51:05,234 - ApplicationLifecycleManager - [ApplicationLifecycleManager.py:103] - INFO - 应用程序关闭完成
2025-07-02 12:51:05,266 - RegisterMainWindow - [RegisterMainWindow.py:406] - DEBUG - 主窗口强制置顶完成
2025-07-02 12:51:05,266 - RegisterMainWindow - [RegisterMainWindow.py:311] - DEBUG - 主窗口已通过焦点置顶
2025-07-02 12:51:05,266 - RegisterMainWindow - [RegisterMainWindow.py:406] - DEBUG - 主窗口强制置顶完成
2025-07-02 12:51:05,266 - RegisterMainWindow - [RegisterMainWindow.py:315] - DEBUG - 主窗口已通过激活事件置顶
2025-07-02 12:51:05,276 - port_manager - [port_manager.py:211] - INFO - 已清理所有COM端口连接
