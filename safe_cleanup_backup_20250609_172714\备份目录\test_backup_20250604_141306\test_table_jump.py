#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试寄存器表格跳转功能
"""

import sys
import os
import logging
from PyQt5.QtWidgets import QApplication

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_table_jump():
    """测试寄存器表格跳转功能"""
    print("=" * 60)
    print("测试寄存器表格跳转功能")
    print("=" * 60)
    
    try:
        # 创建QApplication
        app = QApplication(sys.argv)
        
        # 导入必要的模块
        from ui.windows.RegisterMainWindow import RegisterMainWindow
        from core.services.spi.spi_service_impl import SPIServiceImpl
        from core.repositories.register_repository import RegisterRepository
        
        print("✅ 导入成功")
        
        # 初始化服务
        spi_service = SPIServiceImpl()
        spi_service.initialize()
        register_repo = RegisterRepository(spi_service)
        
        # 创建主窗口
        main_window = RegisterMainWindow(register_repo)
        print("✅ 主窗口创建成功")
        
        # 检查组件
        components = [
            ('table_handler', 'ModernRegisterTableHandler'),
            ('display_manager', 'RegisterDisplayManager'),
            ('event_coordinator', 'EventCoordinator'),
            ('register_service', 'RegisterOperationService')
        ]
        
        for attr_name, expected_type in components:
            if hasattr(main_window, attr_name):
                component = getattr(main_window, attr_name)
                print(f"✅ {attr_name}: {type(component).__name__}")
            else:
                print(f"❌ 缺少 {attr_name}")
        
        # 测试寄存器选择
        test_addr = "0x5B"
        print(f"\n开始测试寄存器选择: {test_addr}")
        
        # 获取初始状态
        if hasattr(main_window, 'table_handler'):
            initial_status = main_window.table_handler.get_current_status()
            print(f"初始表格状态: {initial_status}")
        
        # 调用寄存器选择
        print(f"调用 main_window.on_register_selected('{test_addr}')")
        main_window.on_register_selected(test_addr)
        
        # 检查结果
        if hasattr(main_window, 'table_handler'):
            final_status = main_window.table_handler.get_current_status()
            print(f"最终表格状态: {final_status}")
            
            current_addr = final_status.get('current_register')
            if current_addr == test_addr:
                print(f"✅ 表格成功跳转到寄存器 {test_addr}")
            else:
                print(f"❌ 表格未跳转，当前寄存器: {current_addr}")
        
        # 测试工具窗口控件修改
        print(f"\n开始测试工具窗口控件修改...")
        
        # 创建时钟输入控制窗口
        try:
            clkin_window = main_window._show_clkin_control_window()
            if clkin_window:
                print("✅ 时钟输入控制窗口创建成功")
                
                # 检查控件映射
                if hasattr(clkin_window, 'widget_register_map'):
                    widget_map = clkin_window.widget_register_map
                    print(f"找到 {len(widget_map)} 个控件映射")
                    
                    # 找到第一个控件进行测试
                    if widget_map:
                        first_widget = list(widget_map.keys())[0]
                        widget_info = widget_map[first_widget]
                        target_addr = widget_info['register_addr']
                        
                        print(f"测试控件: {first_widget} -> 寄存器: {target_addr}")
                        
                        # 获取修改前的表格状态
                        if hasattr(main_window, 'table_handler'):
                            before_status = main_window.table_handler.get_current_status()
                            print(f"修改前表格状态: {before_status}")
                        
                        # 模拟控件修改
                        if hasattr(clkin_window, '_on_widget_changed'):
                            print(f"调用 clkin_window._on_widget_changed('{first_widget}', 1)")
                            clkin_window._on_widget_changed(first_widget, 1)
                            
                            # 检查修改后的表格状态
                            if hasattr(main_window, 'table_handler'):
                                after_status = main_window.table_handler.get_current_status()
                                print(f"修改后表格状态: {after_status}")
                                
                                current_addr = after_status.get('current_register')
                                if current_addr == target_addr:
                                    print(f"✅ 控件修改成功触发表格跳转到 {target_addr}")
                                else:
                                    print(f"❌ 控件修改未触发表格跳转，当前寄存器: {current_addr}")
                        else:
                            print("❌ 时钟输入控制窗口没有 _on_widget_changed 方法")
                else:
                    print("❌ 时钟输入控制窗口没有 widget_register_map")
            else:
                print("❌ 时钟输入控制窗口创建失败")
        except Exception as e:
            print(f"❌ 创建时钟输入控制窗口时出错: {str(e)}")
            import traceback
            traceback.print_exc()
        
        print("\n" + "=" * 60)
        print("测试完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_table_jump()
