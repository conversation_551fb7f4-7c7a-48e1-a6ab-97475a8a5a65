#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试实际窗口显示问题
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow
from PyQt5.QtCore import QTimer

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.Log import logger


def debug_actual_window_display():
    """调试实际窗口显示"""
    print("=" * 60)
    print("调试实际窗口显示问题")
    print("=" * 60)
    
    # 创建应用程序
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    
    try:
        # 1. 直接创建现代化处理器测试
        print("\n1. 直接创建现代化处理器...")
        from ui.handlers.ModernClkOutputsHandler import ModernClkOutputsHandler
        from core.services.register.RegisterManager import RegisterManager
        import json

        # 加载寄存器配置
        with open('config/registers.json', 'r', encoding='utf-8') as f:
            registers_config = json.load(f)

        register_manager = RegisterManager(registers_config)
        
        # 直接创建处理器
        handler = ModernClkOutputsHandler(None, register_manager)
        print(f"✓ 处理器创建成功: {type(handler).__name__}")
        
        # 检查UI
        if hasattr(handler, 'ui') and handler.ui:
            print("✓ UI对象存在")
            
            # 检查content_widget
            if hasattr(handler, 'content_widget'):
                print(f"✓ content_widget存在: {type(handler.content_widget).__name__}")
                print(f"  大小: {handler.content_widget.size()}")
                print(f"  可见: {handler.content_widget.isVisible()}")
                
                # 检查子控件
                children = handler.content_widget.findChildren(object)
                print(f"  子控件数量: {len(children)}")
                
                # 检查布局
                layout = handler.content_widget.layout()
                if layout:
                    print(f"  布局: {type(layout).__name__}")
                    print(f"  布局项数量: {layout.count()}")
                else:
                    print("  ❌ 没有布局")
                    
            else:
                print("❌ content_widget不存在")
        else:
            print("❌ UI对象不存在")
        
        # 2. 测试窗口显示
        print("\n2. 测试窗口显示...")
        handler.show()
        print("✓ 调用了show()方法")
        
        # 检查窗口状态
        print(f"  窗口可见: {handler.isVisible()}")
        print(f"  窗口大小: {handler.size()}")
        print(f"  窗口位置: {handler.pos()}")
        print(f"  窗口标题: {handler.windowTitle()}")
        
        # 3. 检查UI文件内容
        print("\n3. 检查UI文件...")
        from ui.forms.Ui_ClkOutputs import Ui_ClkOutputs
        
        # 创建一个测试UI
        test_ui = Ui_ClkOutputs()
        test_widget = QMainWindow()
        test_ui.setupUi(test_widget)
        
        print("✓ UI文件可以正常设置")
        
        # 检查UI中的关键控件
        key_widgets = ['lineEditFvco', 'lineEditFout0Output', 'DCLK0_1DIV']
        for widget_name in key_widgets:
            if hasattr(test_ui, widget_name):
                widget = getattr(test_ui, widget_name)
                print(f"  ✓ {widget_name}: {type(widget).__name__}")
            else:
                print(f"  ❌ {widget_name}: 不存在")
        
        # 4. 检查ModernBaseHandler的问题
        print("\n4. 检查ModernBaseHandler...")
        from ui.handlers.ModernBaseHandler import ModernBaseHandler
        
        # 检查基类的content_widget设置
        if hasattr(handler, 'content_widget') and handler.content_widget:
            print("✓ ModernBaseHandler的content_widget正常")
            
            # 检查UI是否正确设置到content_widget
            if handler.ui and hasattr(handler.ui, 'setupUi'):
                print("✓ UI的setupUi方法存在")
                
                # 重新设置UI试试
                print("  尝试重新设置UI...")
                handler.ui.setupUi(handler.content_widget)
                print("  ✓ UI重新设置完成")
                
                # 检查设置后的状态
                children_after = handler.content_widget.findChildren(object)
                print(f"  重新设置后子控件数量: {len(children_after)}")
                
        # 5. 强制刷新和更新
        print("\n5. 强制刷新...")
        handler.update()
        handler.repaint()
        if hasattr(handler, 'content_widget'):
            handler.content_widget.update()
            handler.content_widget.repaint()
        
        print("✓ 强制刷新完成")
        
        # 保持窗口显示一段时间
        print("\n6. 保持窗口显示...")
        handler.raise_()
        handler.activateWindow()
        
        # 使用定时器关闭窗口
        def close_window():
            print("关闭测试窗口")
            handler.close()
            app.quit()
        
        QTimer.singleShot(3000, close_window)  # 3秒后关闭
        
        print("窗口将显示3秒...")
        app.exec_()
        
        return True
        
    except Exception as e:
        print(f"❌ 调试过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    success = debug_actual_window_display()
    
    if success:
        print("\n✅ 调试完成")
    else:
        print("\n❌ 调试失败")


if __name__ == '__main__':
    main()
