#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
综合菜单修复测试
测试所有菜单点击修复功能
"""

import sys
import os
from functools import partial

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import (QApplication, QMainWindow, QMenuBar, QAction, 
                            QWidget, QVBoxLayout, QLabel, QPushButton, QTextEdit, QHBoxLayout)
from PyQt5.QtCore import QTimer, pyqtSignal
from core.services.plugin.MenuClickFixer import menu_click_fixer


class ComprehensiveTestWindow(QMainWindow):
    """综合测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("综合菜单点击修复测试")
        self.resize(1000, 700)
        
        # 创建中央窗口部件
        self.setup_central_widget()
        
        # 创建测试菜单
        self.create_test_menus()
        
        # 设置自动关闭定时器
        self.close_timer = QTimer()
        self.close_timer.timeout.connect(self.close)
        self.close_timer.start(120000)  # 120秒后自动关闭
    
    def setup_central_widget(self):
        """设置中央窗口部件"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # 添加说明标签
        info_label = QLabel("""
🔧 综合菜单点击修复测试

本测试验证以下修复功能：
1. 基础菜单点击响应
2. 菜单修复器增强的点击响应
3. 可选中菜单项的状态管理
4. 事件处理的稳定性

测试菜单：
- "普通菜单" - 标准菜单项
- "修复菜单" - 使用菜单修复器的菜单项
- "模拟插件" - 模拟插件菜单的行为

请依次测试各个菜单项，观察：
✅ 是否能直接点击触发
✅ 是否不需要滑动操作
✅ 日志输出是否正常
✅ 可选中状态是否正确

程序将在2分钟后自动关闭
        """)
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # 添加按钮区域
        button_layout = QHBoxLayout()
        layout.addLayout(button_layout)
        
        # 添加测试按钮
        self.test_button = QPushButton("运行自动测试")
        self.test_button.clicked.connect(self.run_auto_test)
        button_layout.addWidget(self.test_button)
        
        # 添加清除日志按钮
        clear_button = QPushButton("清除日志")
        clear_button.clicked.connect(self.clear_log)
        button_layout.addWidget(clear_button)
        
        # 添加状态显示
        self.status_label = QLabel("状态: 等待测试...")
        layout.addWidget(self.status_label)
        
        # 添加日志显示
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(300)
        layout.addWidget(self.log_text)
    
    def create_test_menus(self):
        """创建测试菜单"""
        menu_bar = self.menuBar()
        
        # 1. 创建普通菜单（不使用修复器）
        normal_menu = menu_bar.addMenu("普通菜单(&N)")
        
        normal_action1 = QAction("普通点击1", self)
        normal_action1.triggered.connect(lambda: self.log_event("普通点击1", "普通菜单"))
        normal_menu.addAction(normal_action1)
        
        normal_action2 = QAction("普通可选中", self)
        normal_action2.setCheckable(True)
        normal_action2.triggered.connect(lambda checked: self.log_event(f"普通可选中 (checked={checked})", "普通菜单"))
        normal_menu.addAction(normal_action2)
        
        # 2. 创建修复菜单（使用菜单修复器）
        fixed_menu = menu_bar.addMenu("修复菜单(&F)")
        
        # 使用菜单修复器修复整个菜单
        menu_click_fixer.fix_menu(fixed_menu, self.handle_fixed_menu_action)
        
        fixed_action1 = menu_click_fixer.create_robust_action(
            "修复点击1", self, 
            lambda action, checked: self.log_event("修复点击1", "修复菜单")
        )
        fixed_menu.addAction(fixed_action1)
        
        fixed_action2 = menu_click_fixer.create_robust_action(
            "修复可选中", self, 
            lambda action, checked: self.log_event(f"修复可选中 (checked={checked})", "修复菜单"),
            checkable=True
        )
        fixed_menu.addAction(fixed_action2)
        
        # 3. 创建模拟插件菜单
        plugin_menu = menu_bar.addMenu("模拟插件(&P)")
        
        # 模拟选择性寄存器插件
        plugin_action1 = QAction("选择性寄存器操作", self)
        plugin_action1.setCheckable(True)
        plugin_action1.setStatusTip("打开/关闭 选择性寄存器操作")
        plugin_action1.triggered.connect(partial(self.handle_plugin_action, "选择性寄存器操作"))
        
        # 应用菜单修复器
        menu_click_fixer.fix_action(plugin_action1, 
            lambda action, checked: self.handle_plugin_callback("选择性寄存器操作", action, checked))
        
        plugin_menu.addAction(plugin_action1)
        
        # 模拟示例工具插件
        plugin_action2 = QAction("示例工具插件", self)
        plugin_action2.setCheckable(True)
        plugin_action2.setStatusTip("打开/关闭 示例工具插件")
        plugin_action2.triggered.connect(partial(self.handle_plugin_action, "示例工具插件"))
        
        # 应用菜单修复器
        menu_click_fixer.fix_action(plugin_action2,
            lambda action, checked: self.handle_plugin_callback("示例工具插件", action, checked))
        
        plugin_menu.addAction(plugin_action2)
        
        # 添加分隔符
        plugin_menu.addSeparator()
        
        # 插件管理器
        manager_action = QAction("插件管理器", self)
        manager_action.triggered.connect(lambda: self.log_event("插件管理器", "模拟插件"))
        plugin_menu.addAction(manager_action)
        
        # 强制更新所有菜单
        for menu in [normal_menu, fixed_menu, plugin_menu]:
            menu.update()
        menu_bar.update()
        
        self.log_event("菜单创建完成", "系统")
        self.status_label.setText("状态: 菜单创建完成，可以开始测试")
    
    def log_event(self, event_name, source):
        """记录事件"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S.%f")[:-3]
        message = f"[{timestamp}] 📍 {source}: {event_name}"
        print(message)
        self.log_text.append(message)
    
    def handle_fixed_menu_action(self, action, checked):
        """处理修复菜单动作"""
        self.log_event(f"修复器回调: {action.text()}, checked={checked}", "修复菜单")
    
    def handle_plugin_action(self, plugin_name, checked):
        """处理插件动作"""
        self.log_event(f"插件动作: {plugin_name}, checked={checked}", "模拟插件")
        
        # 模拟插件窗口操作
        if checked:
            self.log_event(f"模拟打开插件窗口: {plugin_name}", "模拟插件")
        else:
            self.log_event(f"模拟关闭插件窗口: {plugin_name}", "模拟插件")
    
    def handle_plugin_callback(self, plugin_name, action, checked):
        """处理插件修复器回调"""
        self.log_event(f"插件修复器回调: {plugin_name}, action={action.text()}, checked={checked}", "修复器")
    
    def run_auto_test(self):
        """运行自动测试"""
        self.log_event("开始自动测试", "测试系统")
        self.status_label.setText("状态: 正在运行自动测试...")
        
        # 检查菜单修复器状态
        fixed_count = menu_click_fixer.get_fixed_actions_count()
        self.log_event(f"菜单修复器已修复 {fixed_count} 个动作", "测试系统")
        
        # 检查菜单栏
        menu_bar = self.menuBar()
        menu_count = len(menu_bar.actions())
        self.log_event(f"菜单栏包含 {menu_count} 个菜单", "测试系统")
        
        for action in menu_bar.actions():
            if action.menu():
                menu = action.menu()
                action_count = len([a for a in menu.actions() if not a.isSeparator()])
                self.log_event(f"菜单 '{menu.title()}' 包含 {action_count} 个动作", "测试系统")
        
        self.log_event("自动测试完成", "测试系统")
        self.status_label.setText("状态: 自动测试完成，请手动测试菜单点击")
    
    def clear_log(self):
        """清除日志"""
        self.log_text.clear()
        self.log_event("日志已清除", "系统")


def main():
    """主函数"""
    app = QApplication([])
    
    # 创建测试窗口
    window = ComprehensiveTestWindow()
    window.show()
    
    print("🚀 综合菜单点击修复测试开始")
    print("请测试以下功能：")
    print("1. 普通菜单 - 标准菜单项")
    print("2. 修复菜单 - 使用菜单修复器的菜单项")
    print("3. 模拟插件 - 模拟插件菜单的行为")
    print("4. 观察是否需要滑动操作才能触发")
    print("5. 检查日志输出的详细信息")
    print("程序将在2分钟后自动关闭")
    
    # 运行应用
    app.exec_()
    
    print("✅ 综合测试完成")


if __name__ == "__main__":
    main()
