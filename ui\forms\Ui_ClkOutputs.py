# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file '.\ClkOutputs.ui'
#
# Created by: PyQt5 UI code generator 5.15.10
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_ClkOutputs(object):
    def setupUi(self, ClkOutputs):
        ClkOutputs.setObjectName("ClkOutputs")
        ClkOutputs.setEnabled(True)
        ClkOutputs.resize(1336, 1512)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(ClkOutputs.sizePolicy().hasHeightForWidth())
        ClkOutputs.setSizePolicy(sizePolicy)
        ClkOutputs.setAutoFillBackground(False)
        ClkOutputs.setStyleSheet("")
        self.label = QtWidgets.QLabel(ClkOutputs)
        self.label.setGeometry(QtCore.QRect(-24, -18, 1361, 1560))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label.sizePolicy().hasHeightForWidth())
        self.label.setSizePolicy(sizePolicy)
        self.label.setAutoFillBackground(True)
        self.label.setStyleSheet("")
        self.label.setText("")
        self.label.setTextFormat(QtCore.Qt.RichText)
        self.label.setPixmap(QtGui.QPixmap(":/clkoutputs/clockOutputs.bmp"))
        self.label.setScaledContents(True)
        self.label.setObjectName("label")
        self.labelFvco = QtWidgets.QLabel(ClkOutputs)
        self.labelFvco.setGeometry(QtCore.QRect(100, 75, 51, 20))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(10)
        font.setBold(False)
        font.setWeight(50)
        self.labelFvco.setFont(font)
        self.labelFvco.setObjectName("labelFvco")
        self.lineEditFvco = QtWidgets.QLineEdit(ClkOutputs)
        self.lineEditFvco.setGeometry(QtCore.QRect(240, 70, 181, 31))
        self.lineEditFvco.setAlignment(QtCore.Qt.AlignCenter)
        self.lineEditFvco.setObjectName("lineEditFvco")
        self.SysrefGBLPD = QtWidgets.QCheckBox(ClkOutputs)
        self.SysrefGBLPD.setGeometry(QtCore.QRect(255, 46, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SysrefGBLPD.sizePolicy().hasHeightForWidth())
        self.SysrefGBLPD.setSizePolicy(sizePolicy)
        self.SysrefGBLPD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SysrefGBLPD.setText("")
        self.SysrefGBLPD.setObjectName("SysrefGBLPD")
        self.SysrefCLR = QtWidgets.QCheckBox(ClkOutputs)
        self.SysrefCLR.setGeometry(QtCore.QRect(96, 46, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SysrefCLR.sizePolicy().hasHeightForWidth())
        self.SysrefCLR.setSizePolicy(sizePolicy)
        self.SysrefCLR.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SysrefCLR.setText("")
        self.SysrefCLR.setObjectName("SysrefCLR")
        self.CLKout0_1PD = QtWidgets.QCheckBox(ClkOutputs)
        self.CLKout0_1PD.setGeometry(QtCore.QRect(479, 48, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.CLKout0_1PD.sizePolicy().hasHeightForWidth())
        self.CLKout0_1PD.setSizePolicy(sizePolicy)
        self.CLKout0_1PD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.CLKout0_1PD.setText("")
        self.CLKout0_1PD.setObjectName("CLKout0_1PD")
        self.CLKout2_3PD = QtWidgets.QCheckBox(ClkOutputs)
        self.CLKout2_3PD.setGeometry(QtCore.QRect(479, 74, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.CLKout2_3PD.sizePolicy().hasHeightForWidth())
        self.CLKout2_3PD.setSizePolicy(sizePolicy)
        self.CLKout2_3PD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.CLKout2_3PD.setText("")
        self.CLKout2_3PD.setObjectName("CLKout2_3PD")
        self.CLKout4_5PD = QtWidgets.QCheckBox(ClkOutputs)
        self.CLKout4_5PD.setGeometry(QtCore.QRect(658, 48, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.CLKout4_5PD.sizePolicy().hasHeightForWidth())
        self.CLKout4_5PD.setSizePolicy(sizePolicy)
        self.CLKout4_5PD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.CLKout4_5PD.setText("")
        self.CLKout4_5PD.setObjectName("CLKout4_5PD")
        self.CLKout6_7PD = QtWidgets.QCheckBox(ClkOutputs)
        self.CLKout6_7PD.setGeometry(QtCore.QRect(658, 73, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.CLKout6_7PD.sizePolicy().hasHeightForWidth())
        self.CLKout6_7PD.setSizePolicy(sizePolicy)
        self.CLKout6_7PD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.CLKout6_7PD.setText("")
        self.CLKout6_7PD.setObjectName("CLKout6_7PD")
        self.CLKout8_9PD = QtWidgets.QCheckBox(ClkOutputs)
        self.CLKout8_9PD.setGeometry(QtCore.QRect(845, 48, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.CLKout8_9PD.sizePolicy().hasHeightForWidth())
        self.CLKout8_9PD.setSizePolicy(sizePolicy)
        self.CLKout8_9PD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.CLKout8_9PD.setText("")
        self.CLKout8_9PD.setObjectName("CLKout8_9PD")
        self.CLKout10_11PD = QtWidgets.QCheckBox(ClkOutputs)
        self.CLKout10_11PD.setGeometry(QtCore.QRect(845, 73, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.CLKout10_11PD.sizePolicy().hasHeightForWidth())
        self.CLKout10_11PD.setSizePolicy(sizePolicy)
        self.CLKout10_11PD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.CLKout10_11PD.setText("")
        self.CLKout10_11PD.setObjectName("CLKout10_11PD")
        self.CLKout12_13PD = QtWidgets.QCheckBox(ClkOutputs)
        self.CLKout12_13PD.setGeometry(QtCore.QRect(1041, 48, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.CLKout12_13PD.sizePolicy().hasHeightForWidth())
        self.CLKout12_13PD.setSizePolicy(sizePolicy)
        self.CLKout12_13PD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.CLKout12_13PD.setText("")
        self.CLKout12_13PD.setObjectName("CLKout12_13PD")
        self.DCLK0_1PD = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK0_1PD.setGeometry(QtCore.QRect(90, 168, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK0_1PD.sizePolicy().hasHeightForWidth())
        self.DCLK0_1PD.setSizePolicy(sizePolicy)
        self.DCLK0_1PD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK0_1PD.setText("")
        self.DCLK0_1PD.setObjectName("DCLK0_1PD")
        self.DCLK0_1DDLYPD = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK0_1DDLYPD.setGeometry(QtCore.QRect(250, 168, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK0_1DDLYPD.sizePolicy().hasHeightForWidth())
        self.DCLK0_1DDLYPD.setSizePolicy(sizePolicy)
        self.DCLK0_1DDLYPD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK0_1DDLYPD.setText("")
        self.DCLK0_1DDLYPD.setObjectName("DCLK0_1DDLYPD")
        self.DDLYd0EN = QtWidgets.QCheckBox(ClkOutputs)
        self.DDLYd0EN.setGeometry(QtCore.QRect(446, 168, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DDLYd0EN.sizePolicy().hasHeightForWidth())
        self.DDLYd0EN.setSizePolicy(sizePolicy)
        self.DDLYd0EN.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DDLYd0EN.setText("")
        self.DDLYd0EN.setObjectName("DDLYd0EN")
        self.RGDCLK0_1HSEN = QtWidgets.QCheckBox(ClkOutputs)
        self.RGDCLK0_1HSEN.setGeometry(QtCore.QRect(610, 166, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.RGDCLK0_1HSEN.sizePolicy().hasHeightForWidth())
        self.RGDCLK0_1HSEN.setSizePolicy(sizePolicy)
        self.RGDCLK0_1HSEN.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.RGDCLK0_1HSEN.setText("")
        self.RGDCLK0_1HSEN.setObjectName("RGDCLK0_1HSEN")
        self.DCLK0_1HSTrig = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK0_1HSTrig.setGeometry(QtCore.QRect(610, 186, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK0_1HSTrig.sizePolicy().hasHeightForWidth())
        self.DCLK0_1HSTrig.setSizePolicy(sizePolicy)
        self.DCLK0_1HSTrig.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK0_1HSTrig.setText("")
        self.DCLK0_1HSTrig.setObjectName("DCLK0_1HSTrig")
        self.DCLK0_1POL = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK0_1POL.setGeometry(QtCore.QRect(821, 172, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK0_1POL.sizePolicy().hasHeightForWidth())
        self.DCLK0_1POL.setSizePolicy(sizePolicy)
        self.DCLK0_1POL.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK0_1POL.setText("")
        self.DCLK0_1POL.setObjectName("DCLK0_1POL")
        self.DCLK0_1BYPASS = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK0_1BYPASS.setGeometry(QtCore.QRect(821, 191, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK0_1BYPASS.sizePolicy().hasHeightForWidth())
        self.DCLK0_1BYPASS.setSizePolicy(sizePolicy)
        self.DCLK0_1BYPASS.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK0_1BYPASS.setText("")
        self.DCLK0_1BYPASS.setObjectName("DCLK0_1BYPASS")
        self.SCLK0_1PD = QtWidgets.QCheckBox(ClkOutputs)
        self.SCLK0_1PD.setGeometry(QtCore.QRect(70, 263, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SCLK0_1PD.sizePolicy().hasHeightForWidth())
        self.SCLK0_1PD.setSizePolicy(sizePolicy)
        self.SCLK0_1PD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SCLK0_1PD.setText("")
        self.SCLK0_1PD.setObjectName("SCLK0_1PD")
        self.SCLK0_1HSTrig = QtWidgets.QCheckBox(ClkOutputs)
        self.SCLK0_1HSTrig.setGeometry(QtCore.QRect(420, 273, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SCLK0_1HSTrig.sizePolicy().hasHeightForWidth())
        self.SCLK0_1HSTrig.setSizePolicy(sizePolicy)
        self.SCLK0_1HSTrig.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SCLK0_1HSTrig.setText("")
        self.SCLK0_1HSTrig.setObjectName("SCLK0_1HSTrig")
        self.SYNCDIS0 = QtWidgets.QCheckBox(ClkOutputs)
        self.SYNCDIS0.setGeometry(QtCore.QRect(142, 190, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SYNCDIS0.sizePolicy().hasHeightForWidth())
        self.SYNCDIS0.setSizePolicy(sizePolicy)
        self.SYNCDIS0.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SYNCDIS0.setText("")
        self.SYNCDIS0.setObjectName("SYNCDIS0")
        self.SCLK0_1ADLYEnb = QtWidgets.QCheckBox(ClkOutputs)
        self.SCLK0_1ADLYEnb.setGeometry(QtCore.QRect(611, 271, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SCLK0_1ADLYEnb.sizePolicy().hasHeightForWidth())
        self.SCLK0_1ADLYEnb.setSizePolicy(sizePolicy)
        self.SCLK0_1ADLYEnb.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SCLK0_1ADLYEnb.setText("")
        self.SCLK0_1ADLYEnb.setObjectName("SCLK0_1ADLYEnb")
        self.SCLK0_1POL = QtWidgets.QCheckBox(ClkOutputs)
        self.SCLK0_1POL.setGeometry(QtCore.QRect(790, 280, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SCLK0_1POL.sizePolicy().hasHeightForWidth())
        self.SCLK0_1POL.setSizePolicy(sizePolicy)
        self.SCLK0_1POL.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SCLK0_1POL.setText("")
        self.SCLK0_1POL.setObjectName("SCLK0_1POL")
        self.CLKout1_SRCMUX = QtWidgets.QCheckBox(ClkOutputs)
        self.CLKout1_SRCMUX.setGeometry(QtCore.QRect(960, 280, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.CLKout1_SRCMUX.sizePolicy().hasHeightForWidth())
        self.CLKout1_SRCMUX.setSizePolicy(sizePolicy)
        self.CLKout1_SRCMUX.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.CLKout1_SRCMUX.setText("")
        self.CLKout1_SRCMUX.setObjectName("CLKout1_SRCMUX")
        self.DCLK0_1DIV = QtWidgets.QSpinBox(ClkOutputs)
        self.DCLK0_1DIV.setGeometry(QtCore.QRect(90, 141, 131, 25))
        self.DCLK0_1DIV.setObjectName("DCLK0_1DIV")
        self.DCLK0_1DDLY = QtWidgets.QSpinBox(ClkOutputs)
        self.DCLK0_1DDLY.setGeometry(QtCore.QRect(260, 141, 131, 25))
        self.DCLK0_1DDLY.setObjectName("DCLK0_1DDLY")
        self.SCLK0_1DDLY = QtWidgets.QComboBox(ClkOutputs)
        self.SCLK0_1DDLY.setGeometry(QtCore.QRect(239, 247, 131, 25))
        self.SCLK0_1DDLY.setObjectName("SCLK0_1DDLY")
        self.CLKout0FMT = QtWidgets.QComboBox(ClkOutputs)
        self.CLKout0FMT.setGeometry(QtCore.QRect(1130, 182, 161, 31))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(7)
        self.CLKout0FMT.setFont(font)
        self.CLKout0FMT.setObjectName("CLKout0FMT")
        self.CLKout1FMT = QtWidgets.QComboBox(ClkOutputs)
        self.CLKout1FMT.setGeometry(QtCore.QRect(1130, 273, 161, 25))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(8)
        self.CLKout1FMT.setFont(font)
        self.CLKout1FMT.setObjectName("CLKout1FMT")
        self.DCLK2_3DIV = QtWidgets.QSpinBox(ClkOutputs)
        self.DCLK2_3DIV.setGeometry(QtCore.QRect(95, 340, 131, 25))
        self.DCLK2_3DIV.setObjectName("DCLK2_3DIV")
        self.DCLK2_3DDLY = QtWidgets.QSpinBox(ClkOutputs)
        self.DCLK2_3DDLY.setGeometry(QtCore.QRect(264, 340, 131, 25))
        self.DCLK2_3DDLY.setObjectName("DCLK2_3DDLY")
        self.DDLYdStepCNT_1 = QtWidgets.QSpinBox(ClkOutputs)
        self.DDLYdStepCNT_1.setGeometry(QtCore.QRect(440, 141, 131, 25))
        self.DDLYdStepCNT_1.setObjectName("DDLYdStepCNT_1")
        self.DDLYdStepCNT_2 = QtWidgets.QSpinBox(ClkOutputs)
        self.DDLYdStepCNT_2.setGeometry(QtCore.QRect(447, 341, 131, 25))
        self.DDLYdStepCNT_2.setObjectName("DDLYdStepCNT_2")
        self.DDLYdStepCNT_3 = QtWidgets.QSpinBox(ClkOutputs)
        self.DDLYdStepCNT_3.setGeometry(QtCore.QRect(445, 544, 131, 25))
        self.DDLYdStepCNT_3.setObjectName("DDLYdStepCNT_3")
        self.DDLYdStepCNT_4 = QtWidgets.QSpinBox(ClkOutputs)
        self.DDLYdStepCNT_4.setGeometry(QtCore.QRect(451, 744, 131, 25))
        self.DDLYdStepCNT_4.setObjectName("DDLYdStepCNT_4")
        self.DDLYdStepCNT_5 = QtWidgets.QSpinBox(ClkOutputs)
        self.DDLYdStepCNT_5.setGeometry(QtCore.QRect(450, 946, 131, 25))
        self.DDLYdStepCNT_5.setObjectName("DDLYdStepCNT_5")
        self.DDLYdStepCNT_6 = QtWidgets.QSpinBox(ClkOutputs)
        self.DDLYdStepCNT_6.setGeometry(QtCore.QRect(449, 1150, 131, 25))
        self.DDLYdStepCNT_6.setObjectName("DDLYdStepCNT_6")
        self.DDLYdStepCNT_7 = QtWidgets.QSpinBox(ClkOutputs)
        self.DDLYdStepCNT_7.setGeometry(QtCore.QRect(453, 1343, 131, 25))
        self.DDLYdStepCNT_7.setObjectName("DDLYdStepCNT_7")
        self.SYNCDIS2 = QtWidgets.QCheckBox(ClkOutputs)
        self.SYNCDIS2.setGeometry(QtCore.QRect(150, 390, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SYNCDIS2.sizePolicy().hasHeightForWidth())
        self.SYNCDIS2.setSizePolicy(sizePolicy)
        self.SYNCDIS2.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SYNCDIS2.setText("")
        self.SYNCDIS2.setObjectName("SYNCDIS2")
        self.SYNCDIS4 = QtWidgets.QCheckBox(ClkOutputs)
        self.SYNCDIS4.setGeometry(QtCore.QRect(150, 593, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SYNCDIS4.sizePolicy().hasHeightForWidth())
        self.SYNCDIS4.setSizePolicy(sizePolicy)
        self.SYNCDIS4.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SYNCDIS4.setText("")
        self.SYNCDIS4.setObjectName("SYNCDIS4")
        self.SYNCDIS6 = QtWidgets.QCheckBox(ClkOutputs)
        self.SYNCDIS6.setGeometry(QtCore.QRect(155, 794, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SYNCDIS6.sizePolicy().hasHeightForWidth())
        self.SYNCDIS6.setSizePolicy(sizePolicy)
        self.SYNCDIS6.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SYNCDIS6.setText("")
        self.SYNCDIS6.setObjectName("SYNCDIS6")
        self.SYNCDIS8 = QtWidgets.QCheckBox(ClkOutputs)
        self.SYNCDIS8.setGeometry(QtCore.QRect(154, 997, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SYNCDIS8.sizePolicy().hasHeightForWidth())
        self.SYNCDIS8.setSizePolicy(sizePolicy)
        self.SYNCDIS8.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SYNCDIS8.setText("")
        self.SYNCDIS8.setObjectName("SYNCDIS8")
        self.SYNCDIS10 = QtWidgets.QCheckBox(ClkOutputs)
        self.SYNCDIS10.setGeometry(QtCore.QRect(154, 1201, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SYNCDIS10.sizePolicy().hasHeightForWidth())
        self.SYNCDIS10.setSizePolicy(sizePolicy)
        self.SYNCDIS10.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SYNCDIS10.setText("")
        self.SYNCDIS10.setObjectName("SYNCDIS10")
        self.SYNCDIS12 = QtWidgets.QCheckBox(ClkOutputs)
        self.SYNCDIS12.setGeometry(QtCore.QRect(157, 1393, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SYNCDIS12.sizePolicy().hasHeightForWidth())
        self.SYNCDIS12.setSizePolicy(sizePolicy)
        self.SYNCDIS12.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SYNCDIS12.setText("")
        self.SYNCDIS12.setObjectName("SYNCDIS12")
        self.DCLK2_3HSTrig = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK2_3HSTrig.setGeometry(QtCore.QRect(612, 390, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK2_3HSTrig.sizePolicy().hasHeightForWidth())
        self.DCLK2_3HSTrig.setSizePolicy(sizePolicy)
        self.DCLK2_3HSTrig.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK2_3HSTrig.setText("")
        self.DCLK2_3HSTrig.setObjectName("DCLK2_3HSTrig")
        self.RGDCLK2_3HSEN = QtWidgets.QCheckBox(ClkOutputs)
        self.RGDCLK2_3HSEN.setGeometry(QtCore.QRect(612, 370, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.RGDCLK2_3HSEN.sizePolicy().hasHeightForWidth())
        self.RGDCLK2_3HSEN.setSizePolicy(sizePolicy)
        self.RGDCLK2_3HSEN.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.RGDCLK2_3HSEN.setText("")
        self.RGDCLK2_3HSEN.setObjectName("RGDCLK2_3HSEN")
        self.DCLK2_3BYPASS = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK2_3BYPASS.setGeometry(QtCore.QRect(828, 390, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK2_3BYPASS.sizePolicy().hasHeightForWidth())
        self.DCLK2_3BYPASS.setSizePolicy(sizePolicy)
        self.DCLK2_3BYPASS.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK2_3BYPASS.setText("")
        self.DCLK2_3BYPASS.setObjectName("DCLK2_3BYPASS")
        self.DCLK2_3POL = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK2_3POL.setGeometry(QtCore.QRect(828, 371, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK2_3POL.sizePolicy().hasHeightForWidth())
        self.DCLK2_3POL.setSizePolicy(sizePolicy)
        self.DCLK2_3POL.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK2_3POL.setText("")
        self.DCLK2_3POL.setObjectName("DCLK2_3POL")
        self.CLKout3_SRCMUX = QtWidgets.QCheckBox(ClkOutputs)
        self.CLKout3_SRCMUX.setGeometry(QtCore.QRect(960, 480, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.CLKout3_SRCMUX.sizePolicy().hasHeightForWidth())
        self.CLKout3_SRCMUX.setSizePolicy(sizePolicy)
        self.CLKout3_SRCMUX.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.CLKout3_SRCMUX.setText("")
        self.CLKout3_SRCMUX.setObjectName("CLKout3_SRCMUX")
        self.SCLK2_3POL = QtWidgets.QCheckBox(ClkOutputs)
        self.SCLK2_3POL.setGeometry(QtCore.QRect(798, 480, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SCLK2_3POL.sizePolicy().hasHeightForWidth())
        self.SCLK2_3POL.setSizePolicy(sizePolicy)
        self.SCLK2_3POL.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SCLK2_3POL.setText("")
        self.SCLK2_3POL.setObjectName("SCLK2_3POL")
        self.SCLK2_3ADLYEnb = QtWidgets.QCheckBox(ClkOutputs)
        self.SCLK2_3ADLYEnb.setGeometry(QtCore.QRect(620, 470, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SCLK2_3ADLYEnb.sizePolicy().hasHeightForWidth())
        self.SCLK2_3ADLYEnb.setSizePolicy(sizePolicy)
        self.SCLK2_3ADLYEnb.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SCLK2_3ADLYEnb.setText("")
        self.SCLK2_3ADLYEnb.setObjectName("SCLK2_3ADLYEnb")
        self.SCLK2_3HSTrig = QtWidgets.QCheckBox(ClkOutputs)
        self.SCLK2_3HSTrig.setGeometry(QtCore.QRect(430, 474, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SCLK2_3HSTrig.sizePolicy().hasHeightForWidth())
        self.SCLK2_3HSTrig.setSizePolicy(sizePolicy)
        self.SCLK2_3HSTrig.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SCLK2_3HSTrig.setText("")
        self.SCLK2_3HSTrig.setObjectName("SCLK2_3HSTrig")
        self.DCLK2_3DDLYPD = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK2_3DDLYPD.setGeometry(QtCore.QRect(260, 368, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK2_3DDLYPD.sizePolicy().hasHeightForWidth())
        self.DCLK2_3DDLYPD.setSizePolicy(sizePolicy)
        self.DCLK2_3DDLYPD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK2_3DDLYPD.setText("")
        self.DCLK2_3DDLYPD.setObjectName("DCLK2_3DDLYPD")
        self.DCLK2_3PD = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK2_3PD.setGeometry(QtCore.QRect(100, 367, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK2_3PD.sizePolicy().hasHeightForWidth())
        self.DCLK2_3PD.setSizePolicy(sizePolicy)
        self.DCLK2_3PD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK2_3PD.setText("")
        self.DCLK2_3PD.setObjectName("DCLK2_3PD")
        self.SCLK2_3PD = QtWidgets.QCheckBox(ClkOutputs)
        self.SCLK2_3PD.setGeometry(QtCore.QRect(71, 463, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SCLK2_3PD.sizePolicy().hasHeightForWidth())
        self.SCLK2_3PD.setSizePolicy(sizePolicy)
        self.SCLK2_3PD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SCLK2_3PD.setText("")
        self.SCLK2_3PD.setObjectName("SCLK2_3PD")
        self.CLKout2FMT = QtWidgets.QComboBox(ClkOutputs)
        self.CLKout2FMT.setGeometry(QtCore.QRect(1130, 380, 161, 31))
        self.CLKout2FMT.setObjectName("CLKout2FMT")
        self.CLKout3FMT = QtWidgets.QComboBox(ClkOutputs)
        self.CLKout3FMT.setGeometry(QtCore.QRect(1130, 473, 161, 31))
        self.CLKout3FMT.setObjectName("CLKout3FMT")
        self.CLKout4FMT = QtWidgets.QComboBox(ClkOutputs)
        self.CLKout4FMT.setGeometry(QtCore.QRect(1130, 586, 161, 31))
        self.CLKout4FMT.setObjectName("CLKout4FMT")
        self.CLKout5FMT = QtWidgets.QComboBox(ClkOutputs)
        self.CLKout5FMT.setGeometry(QtCore.QRect(1130, 677, 161, 31))
        self.CLKout5FMT.setObjectName("CLKout5FMT")
        self.CLKout7FMT = QtWidgets.QComboBox(ClkOutputs)
        self.CLKout7FMT.setGeometry(QtCore.QRect(1130, 877, 161, 31))
        self.CLKout7FMT.setObjectName("CLKout7FMT")
        self.CLKout6FMT = QtWidgets.QComboBox(ClkOutputs)
        self.CLKout6FMT.setGeometry(QtCore.QRect(1130, 784, 161, 31))
        self.CLKout6FMT.setObjectName("CLKout6FMT")
        self.CLKout9FMT = QtWidgets.QComboBox(ClkOutputs)
        self.CLKout9FMT.setGeometry(QtCore.QRect(1130, 1080, 161, 31))
        self.CLKout9FMT.setObjectName("CLKout9FMT")
        self.CLKout8FMT = QtWidgets.QComboBox(ClkOutputs)
        self.CLKout8FMT.setGeometry(QtCore.QRect(1128, 988, 161, 31))
        self.CLKout8FMT.setObjectName("CLKout8FMT")
        self.CLKout11FMT = QtWidgets.QComboBox(ClkOutputs)
        self.CLKout11FMT.setGeometry(QtCore.QRect(1130, 1284, 161, 31))
        self.CLKout11FMT.setObjectName("CLKout11FMT")
        self.CLKout10FMT = QtWidgets.QComboBox(ClkOutputs)
        self.CLKout10FMT.setGeometry(QtCore.QRect(1130, 1191, 161, 31))
        self.CLKout10FMT.setObjectName("CLKout10FMT")
        self.CLKout13FMT = QtWidgets.QComboBox(ClkOutputs)
        self.CLKout13FMT.setGeometry(QtCore.QRect(1130, 1476, 161, 31))
        self.CLKout13FMT.setObjectName("CLKout13FMT")
        self.CLKout12FMT = QtWidgets.QComboBox(ClkOutputs)
        self.CLKout12FMT.setGeometry(QtCore.QRect(1130, 1385, 161, 31))
        self.CLKout12FMT.setObjectName("CLKout12FMT")
        self.DDLYd2EN = QtWidgets.QCheckBox(ClkOutputs)
        self.DDLYd2EN.setGeometry(QtCore.QRect(450, 368, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DDLYd2EN.sizePolicy().hasHeightForWidth())
        self.DDLYd2EN.setSizePolicy(sizePolicy)
        self.DDLYd2EN.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DDLYd2EN.setText("")
        self.DDLYd2EN.setObjectName("DDLYd2EN")
        self.SCLK2_3DDLY = QtWidgets.QComboBox(ClkOutputs)
        self.SCLK2_3DDLY.setGeometry(QtCore.QRect(245, 447, 131, 25))
        self.SCLK2_3DDLY.setObjectName("SCLK2_3DDLY")
        self.CLKout5_SRCMUX = QtWidgets.QCheckBox(ClkOutputs)
        self.CLKout5_SRCMUX.setGeometry(QtCore.QRect(960, 682, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.CLKout5_SRCMUX.sizePolicy().hasHeightForWidth())
        self.CLKout5_SRCMUX.setSizePolicy(sizePolicy)
        self.CLKout5_SRCMUX.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.CLKout5_SRCMUX.setText("")
        self.CLKout5_SRCMUX.setObjectName("CLKout5_SRCMUX")
        self.DCLK4_5BYPASS = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK4_5BYPASS.setGeometry(QtCore.QRect(830, 594, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK4_5BYPASS.sizePolicy().hasHeightForWidth())
        self.DCLK4_5BYPASS.setSizePolicy(sizePolicy)
        self.DCLK4_5BYPASS.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK4_5BYPASS.setText("")
        self.DCLK4_5BYPASS.setObjectName("DCLK4_5BYPASS")
        self.DCLK4_5DIV = QtWidgets.QSpinBox(ClkOutputs)
        self.DCLK4_5DIV.setGeometry(QtCore.QRect(93, 544, 131, 25))
        self.DCLK4_5DIV.setObjectName("DCLK4_5DIV")
        self.DCLK4_5DDLY = QtWidgets.QSpinBox(ClkOutputs)
        self.DCLK4_5DDLY.setGeometry(QtCore.QRect(264, 544, 131, 25))
        self.DCLK4_5DDLY.setObjectName("DCLK4_5DDLY")
        self.DCLK4_5PD = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK4_5PD.setGeometry(QtCore.QRect(98, 569, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK4_5PD.sizePolicy().hasHeightForWidth())
        self.DCLK4_5PD.setSizePolicy(sizePolicy)
        self.DCLK4_5PD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK4_5PD.setText("")
        self.DCLK4_5PD.setObjectName("DCLK4_5PD")
        self.DDLYd4EN = QtWidgets.QCheckBox(ClkOutputs)
        self.DDLYd4EN.setGeometry(QtCore.QRect(450, 571, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DDLYd4EN.sizePolicy().hasHeightForWidth())
        self.DDLYd4EN.setSizePolicy(sizePolicy)
        self.DDLYd4EN.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DDLYd4EN.setText("")
        self.DDLYd4EN.setObjectName("DDLYd4EN")
        self.SCLK4_5HSTrig = QtWidgets.QCheckBox(ClkOutputs)
        self.SCLK4_5HSTrig.setGeometry(QtCore.QRect(429, 677, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SCLK4_5HSTrig.sizePolicy().hasHeightForWidth())
        self.SCLK4_5HSTrig.setSizePolicy(sizePolicy)
        self.SCLK4_5HSTrig.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SCLK4_5HSTrig.setText("")
        self.SCLK4_5HSTrig.setObjectName("SCLK4_5HSTrig")
        self.DCLK4_5DDLYPD = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK4_5DDLYPD.setGeometry(QtCore.QRect(258, 571, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK4_5DDLYPD.sizePolicy().hasHeightForWidth())
        self.DCLK4_5DDLYPD.setSizePolicy(sizePolicy)
        self.DCLK4_5DDLYPD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK4_5DDLYPD.setText("")
        self.DCLK4_5DDLYPD.setObjectName("DCLK4_5DDLYPD")
        self.DCLK4_5HSTrig = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK4_5HSTrig.setGeometry(QtCore.QRect(610, 590, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK4_5HSTrig.sizePolicy().hasHeightForWidth())
        self.DCLK4_5HSTrig.setSizePolicy(sizePolicy)
        self.DCLK4_5HSTrig.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK4_5HSTrig.setText("")
        self.DCLK4_5HSTrig.setObjectName("DCLK4_5HSTrig")
        self.SCLK4_5ADLYEnb = QtWidgets.QCheckBox(ClkOutputs)
        self.SCLK4_5ADLYEnb.setGeometry(QtCore.QRect(618, 674, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SCLK4_5ADLYEnb.sizePolicy().hasHeightForWidth())
        self.SCLK4_5ADLYEnb.setSizePolicy(sizePolicy)
        self.SCLK4_5ADLYEnb.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SCLK4_5ADLYEnb.setText("")
        self.SCLK4_5ADLYEnb.setObjectName("SCLK4_5ADLYEnb")
        self.SCLK4_5PD = QtWidgets.QCheckBox(ClkOutputs)
        self.SCLK4_5PD.setGeometry(QtCore.QRect(70, 667, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SCLK4_5PD.sizePolicy().hasHeightForWidth())
        self.SCLK4_5PD.setSizePolicy(sizePolicy)
        self.SCLK4_5PD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SCLK4_5PD.setText("")
        self.SCLK4_5PD.setObjectName("SCLK4_5PD")
        self.RGDCLK4_5HSEN = QtWidgets.QCheckBox(ClkOutputs)
        self.RGDCLK4_5HSEN.setGeometry(QtCore.QRect(610, 570, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.RGDCLK4_5HSEN.sizePolicy().hasHeightForWidth())
        self.RGDCLK4_5HSEN.setSizePolicy(sizePolicy)
        self.RGDCLK4_5HSEN.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.RGDCLK4_5HSEN.setText("")
        self.RGDCLK4_5HSEN.setObjectName("RGDCLK4_5HSEN")
        self.DCLK4_5POL = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK4_5POL.setGeometry(QtCore.QRect(830, 574, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK4_5POL.sizePolicy().hasHeightForWidth())
        self.DCLK4_5POL.setSizePolicy(sizePolicy)
        self.DCLK4_5POL.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK4_5POL.setText("")
        self.DCLK4_5POL.setObjectName("DCLK4_5POL")
        self.SCLK4_5POL = QtWidgets.QCheckBox(ClkOutputs)
        self.SCLK4_5POL.setGeometry(QtCore.QRect(798, 683, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SCLK4_5POL.sizePolicy().hasHeightForWidth())
        self.SCLK4_5POL.setSizePolicy(sizePolicy)
        self.SCLK4_5POL.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SCLK4_5POL.setText("")
        self.SCLK4_5POL.setObjectName("SCLK4_5POL")
        self.SCLK6_7PD = QtWidgets.QCheckBox(ClkOutputs)
        self.SCLK6_7PD.setGeometry(QtCore.QRect(80, 868, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SCLK6_7PD.sizePolicy().hasHeightForWidth())
        self.SCLK6_7PD.setSizePolicy(sizePolicy)
        self.SCLK6_7PD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SCLK6_7PD.setText("")
        self.SCLK6_7PD.setObjectName("SCLK6_7PD")
        self.DCLK6_7PD = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK6_7PD.setGeometry(QtCore.QRect(100, 772, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK6_7PD.sizePolicy().hasHeightForWidth())
        self.DCLK6_7PD.setSizePolicy(sizePolicy)
        self.DCLK6_7PD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK6_7PD.setText("")
        self.DCLK6_7PD.setObjectName("DCLK6_7PD")
        self.SCLK6_7POL = QtWidgets.QCheckBox(ClkOutputs)
        self.SCLK6_7POL.setGeometry(QtCore.QRect(800, 884, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SCLK6_7POL.sizePolicy().hasHeightForWidth())
        self.SCLK6_7POL.setSizePolicy(sizePolicy)
        self.SCLK6_7POL.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SCLK6_7POL.setText("")
        self.SCLK6_7POL.setObjectName("SCLK6_7POL")
        self.RGDCLK6_7HSEN = QtWidgets.QCheckBox(ClkOutputs)
        self.RGDCLK6_7HSEN.setGeometry(QtCore.QRect(620, 771, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.RGDCLK6_7HSEN.sizePolicy().hasHeightForWidth())
        self.RGDCLK6_7HSEN.setSizePolicy(sizePolicy)
        self.RGDCLK6_7HSEN.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.RGDCLK6_7HSEN.setText("")
        self.RGDCLK6_7HSEN.setObjectName("RGDCLK6_7HSEN")
        self.DCLK6_7HSTrig = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK6_7HSTrig.setGeometry(QtCore.QRect(620, 791, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK6_7HSTrig.sizePolicy().hasHeightForWidth())
        self.DCLK6_7HSTrig.setSizePolicy(sizePolicy)
        self.DCLK6_7HSTrig.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK6_7HSTrig.setText("")
        self.DCLK6_7HSTrig.setObjectName("DCLK6_7HSTrig")
        self.DCLK6_7BYPASS = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK6_7BYPASS.setGeometry(QtCore.QRect(831, 797, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK6_7BYPASS.sizePolicy().hasHeightForWidth())
        self.DCLK6_7BYPASS.setSizePolicy(sizePolicy)
        self.DCLK6_7BYPASS.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK6_7BYPASS.setText("")
        self.DCLK6_7BYPASS.setObjectName("DCLK6_7BYPASS")
        self.DDLYd6EN = QtWidgets.QCheckBox(ClkOutputs)
        self.DDLYd6EN.setGeometry(QtCore.QRect(455, 772, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DDLYd6EN.sizePolicy().hasHeightForWidth())
        self.DDLYd6EN.setSizePolicy(sizePolicy)
        self.DDLYd6EN.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DDLYd6EN.setText("")
        self.DDLYd6EN.setObjectName("DDLYd6EN")
        self.SCLK6_7HSTrig = QtWidgets.QCheckBox(ClkOutputs)
        self.SCLK6_7HSTrig.setGeometry(QtCore.QRect(292, 878, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SCLK6_7HSTrig.sizePolicy().hasHeightForWidth())
        self.SCLK6_7HSTrig.setSizePolicy(sizePolicy)
        self.SCLK6_7HSTrig.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SCLK6_7HSTrig.setText("")
        self.SCLK6_7HSTrig.setObjectName("SCLK6_7HSTrig")
        self.SCLK6_7ADLYEnb = QtWidgets.QCheckBox(ClkOutputs)
        self.SCLK6_7ADLYEnb.setGeometry(QtCore.QRect(430, 877, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SCLK6_7ADLYEnb.sizePolicy().hasHeightForWidth())
        self.SCLK6_7ADLYEnb.setSizePolicy(sizePolicy)
        self.SCLK6_7ADLYEnb.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SCLK6_7ADLYEnb.setText("")
        self.SCLK6_7ADLYEnb.setObjectName("SCLK6_7ADLYEnb")
        self.CLKout7_SRCMUX = QtWidgets.QCheckBox(ClkOutputs)
        self.CLKout7_SRCMUX.setGeometry(QtCore.QRect(960, 883, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.CLKout7_SRCMUX.sizePolicy().hasHeightForWidth())
        self.CLKout7_SRCMUX.setSizePolicy(sizePolicy)
        self.CLKout7_SRCMUX.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.CLKout7_SRCMUX.setText("")
        self.CLKout7_SRCMUX.setObjectName("CLKout7_SRCMUX")
        self.DCLK6_7DIV = QtWidgets.QSpinBox(ClkOutputs)
        self.DCLK6_7DIV.setGeometry(QtCore.QRect(99, 744, 131, 25))
        self.DCLK6_7DIV.setObjectName("DCLK6_7DIV")
        self.DCLK6_7DDLYPD = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK6_7DDLYPD.setGeometry(QtCore.QRect(260, 772, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK6_7DDLYPD.sizePolicy().hasHeightForWidth())
        self.DCLK6_7DDLYPD.setSizePolicy(sizePolicy)
        self.DCLK6_7DDLYPD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK6_7DDLYPD.setText("")
        self.DCLK6_7DDLYPD.setObjectName("DCLK6_7DDLYPD")
        self.DCLK6_7DDLY = QtWidgets.QSpinBox(ClkOutputs)
        self.DCLK6_7DDLY.setGeometry(QtCore.QRect(270, 744, 131, 25))
        self.DCLK6_7DDLY.setObjectName("DCLK6_7DDLY")
        self.DCLK6_7POL = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK6_7POL.setGeometry(QtCore.QRect(831, 777, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK6_7POL.sizePolicy().hasHeightForWidth())
        self.DCLK6_7POL.setSizePolicy(sizePolicy)
        self.DCLK6_7POL.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK6_7POL.setText("")
        self.DCLK6_7POL.setObjectName("DCLK6_7POL")
        self.CLKout9_SRCMUX = QtWidgets.QCheckBox(ClkOutputs)
        self.CLKout9_SRCMUX.setGeometry(QtCore.QRect(960, 1086, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.CLKout9_SRCMUX.sizePolicy().hasHeightForWidth())
        self.CLKout9_SRCMUX.setSizePolicy(sizePolicy)
        self.CLKout9_SRCMUX.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.CLKout9_SRCMUX.setText("")
        self.CLKout9_SRCMUX.setObjectName("CLKout9_SRCMUX")
        self.DCLK8_9DDLYPD = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK8_9DDLYPD.setGeometry(QtCore.QRect(260, 975, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK8_9DDLYPD.sizePolicy().hasHeightForWidth())
        self.DCLK8_9DDLYPD.setSizePolicy(sizePolicy)
        self.DCLK8_9DDLYPD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK8_9DDLYPD.setText("")
        self.DCLK8_9DDLYPD.setObjectName("DCLK8_9DDLYPD")
        self.SCLK8_9POL = QtWidgets.QCheckBox(ClkOutputs)
        self.SCLK8_9POL.setGeometry(QtCore.QRect(800, 1087, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SCLK8_9POL.sizePolicy().hasHeightForWidth())
        self.SCLK8_9POL.setSizePolicy(sizePolicy)
        self.SCLK8_9POL.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SCLK8_9POL.setText("")
        self.SCLK8_9POL.setObjectName("SCLK8_9POL")
        self.DCLK8_9BYPASS = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK8_9BYPASS.setGeometry(QtCore.QRect(830, 1001, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK8_9BYPASS.sizePolicy().hasHeightForWidth())
        self.DCLK8_9BYPASS.setSizePolicy(sizePolicy)
        self.DCLK8_9BYPASS.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK8_9BYPASS.setText("")
        self.DCLK8_9BYPASS.setObjectName("DCLK8_9BYPASS")
        self.DCLK8_9POL = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK8_9POL.setGeometry(QtCore.QRect(830, 981, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK8_9POL.sizePolicy().hasHeightForWidth())
        self.DCLK8_9POL.setSizePolicy(sizePolicy)
        self.DCLK8_9POL.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK8_9POL.setText("")
        self.DCLK8_9POL.setObjectName("DCLK8_9POL")
        self.SCLK8_9PD = QtWidgets.QCheckBox(ClkOutputs)
        self.SCLK8_9PD.setGeometry(QtCore.QRect(78, 1071, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SCLK8_9PD.sizePolicy().hasHeightForWidth())
        self.SCLK8_9PD.setSizePolicy(sizePolicy)
        self.SCLK8_9PD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SCLK8_9PD.setText("")
        self.SCLK8_9PD.setObjectName("SCLK8_9PD")
        self.RGDCLK8_9HSEN = QtWidgets.QCheckBox(ClkOutputs)
        self.RGDCLK8_9HSEN.setGeometry(QtCore.QRect(617, 974, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.RGDCLK8_9HSEN.sizePolicy().hasHeightForWidth())
        self.RGDCLK8_9HSEN.setSizePolicy(sizePolicy)
        self.RGDCLK8_9HSEN.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.RGDCLK8_9HSEN.setText("")
        self.RGDCLK8_9HSEN.setObjectName("RGDCLK8_9HSEN")
        self.DCLK8_9DDLY = QtWidgets.QSpinBox(ClkOutputs)
        self.DCLK8_9DDLY.setGeometry(QtCore.QRect(269, 947, 131, 25))
        self.DCLK8_9DDLY.setObjectName("DCLK8_9DDLY")
        self.DDLYd8EN = QtWidgets.QCheckBox(ClkOutputs)
        self.DDLYd8EN.setGeometry(QtCore.QRect(452, 976, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DDLYd8EN.sizePolicy().hasHeightForWidth())
        self.DDLYd8EN.setSizePolicy(sizePolicy)
        self.DDLYd8EN.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DDLYd8EN.setText("")
        self.DDLYd8EN.setObjectName("DDLYd8EN")
        self.DCLK8_9DIV = QtWidgets.QSpinBox(ClkOutputs)
        self.DCLK8_9DIV.setGeometry(QtCore.QRect(99, 947, 131, 25))
        self.DCLK8_9DIV.setObjectName("DCLK8_9DIV")
        self.DCLK8_9PD = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK8_9PD.setGeometry(QtCore.QRect(100, 974, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK8_9PD.sizePolicy().hasHeightForWidth())
        self.DCLK8_9PD.setSizePolicy(sizePolicy)
        self.DCLK8_9PD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK8_9PD.setText("")
        self.DCLK8_9PD.setObjectName("DCLK8_9PD")
        self.DCLK8_9HSTrig = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK8_9HSTrig.setGeometry(QtCore.QRect(617, 992, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK8_9HSTrig.sizePolicy().hasHeightForWidth())
        self.DCLK8_9HSTrig.setSizePolicy(sizePolicy)
        self.DCLK8_9HSTrig.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK8_9HSTrig.setText("")
        self.DCLK8_9HSTrig.setObjectName("DCLK8_9HSTrig")
        self.SCLK8_9ADLYEnb = QtWidgets.QCheckBox(ClkOutputs)
        self.SCLK8_9ADLYEnb.setGeometry(QtCore.QRect(620, 1080, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SCLK8_9ADLYEnb.sizePolicy().hasHeightForWidth())
        self.SCLK8_9ADLYEnb.setSizePolicy(sizePolicy)
        self.SCLK8_9ADLYEnb.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SCLK8_9ADLYEnb.setText("")
        self.SCLK8_9ADLYEnb.setObjectName("SCLK8_9ADLYEnb")
        self.SCLK8_9HSTrig = QtWidgets.QCheckBox(ClkOutputs)
        self.SCLK8_9HSTrig.setGeometry(QtCore.QRect(430, 1081, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SCLK8_9HSTrig.sizePolicy().hasHeightForWidth())
        self.SCLK8_9HSTrig.setSizePolicy(sizePolicy)
        self.SCLK8_9HSTrig.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SCLK8_9HSTrig.setText("")
        self.SCLK8_9HSTrig.setObjectName("SCLK8_9HSTrig")
        self.CLKout11_SRCMUX = QtWidgets.QCheckBox(ClkOutputs)
        self.CLKout11_SRCMUX.setGeometry(QtCore.QRect(965, 1290, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.CLKout11_SRCMUX.sizePolicy().hasHeightForWidth())
        self.CLKout11_SRCMUX.setSizePolicy(sizePolicy)
        self.CLKout11_SRCMUX.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.CLKout11_SRCMUX.setText("")
        self.CLKout11_SRCMUX.setObjectName("CLKout11_SRCMUX")
        self.DCLK10_11DDLYPD = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK10_11DDLYPD.setGeometry(QtCore.QRect(257, 1178, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK10_11DDLYPD.sizePolicy().hasHeightForWidth())
        self.DCLK10_11DDLYPD.setSizePolicy(sizePolicy)
        self.DCLK10_11DDLYPD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK10_11DDLYPD.setText("")
        self.DCLK10_11DDLYPD.setObjectName("DCLK10_11DDLYPD")
        self.SCLK10_11ADLYEnb = QtWidgets.QCheckBox(ClkOutputs)
        self.SCLK10_11ADLYEnb.setGeometry(QtCore.QRect(615, 1284, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SCLK10_11ADLYEnb.sizePolicy().hasHeightForWidth())
        self.SCLK10_11ADLYEnb.setSizePolicy(sizePolicy)
        self.SCLK10_11ADLYEnb.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SCLK10_11ADLYEnb.setText("")
        self.SCLK10_11ADLYEnb.setObjectName("SCLK10_11ADLYEnb")
        self.RGDCLK10_11HSEN = QtWidgets.QCheckBox(ClkOutputs)
        self.RGDCLK10_11HSEN.setGeometry(QtCore.QRect(607, 1178, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.RGDCLK10_11HSEN.sizePolicy().hasHeightForWidth())
        self.RGDCLK10_11HSEN.setSizePolicy(sizePolicy)
        self.RGDCLK10_11HSEN.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.RGDCLK10_11HSEN.setText("")
        self.RGDCLK10_11HSEN.setObjectName("RGDCLK10_11HSEN")
        self.DCLK10_11DIV = QtWidgets.QSpinBox(ClkOutputs)
        self.DCLK10_11DIV.setGeometry(QtCore.QRect(97, 1151, 131, 25))
        self.DCLK10_11DIV.setObjectName("DCLK10_11DIV")
        self.DCLK10_11BYPASS = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK10_11BYPASS.setGeometry(QtCore.QRect(821, 1205, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK10_11BYPASS.sizePolicy().hasHeightForWidth())
        self.DCLK10_11BYPASS.setSizePolicy(sizePolicy)
        self.DCLK10_11BYPASS.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK10_11BYPASS.setText("")
        self.DCLK10_11BYPASS.setObjectName("DCLK10_11BYPASS")
        self.DDLYd10EN = QtWidgets.QCheckBox(ClkOutputs)
        self.DDLYd10EN.setGeometry(QtCore.QRect(450, 1177, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DDLYd10EN.sizePolicy().hasHeightForWidth())
        self.DDLYd10EN.setSizePolicy(sizePolicy)
        self.DDLYd10EN.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DDLYd10EN.setText("")
        self.DDLYd10EN.setObjectName("DDLYd10EN")
        self.SCLK10_11HSTrig = QtWidgets.QCheckBox(ClkOutputs)
        self.SCLK10_11HSTrig.setGeometry(QtCore.QRect(430, 1285, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SCLK10_11HSTrig.sizePolicy().hasHeightForWidth())
        self.SCLK10_11HSTrig.setSizePolicy(sizePolicy)
        self.SCLK10_11HSTrig.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SCLK10_11HSTrig.setText("")
        self.SCLK10_11HSTrig.setObjectName("SCLK10_11HSTrig")
        self.SCLK10_11POL = QtWidgets.QCheckBox(ClkOutputs)
        self.SCLK10_11POL.setGeometry(QtCore.QRect(798, 1289, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SCLK10_11POL.sizePolicy().hasHeightForWidth())
        self.SCLK10_11POL.setSizePolicy(sizePolicy)
        self.SCLK10_11POL.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SCLK10_11POL.setText("")
        self.SCLK10_11POL.setObjectName("SCLK10_11POL")
        self.SCLK10_11PD = QtWidgets.QCheckBox(ClkOutputs)
        self.SCLK10_11PD.setGeometry(QtCore.QRect(75, 1275, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SCLK10_11PD.sizePolicy().hasHeightForWidth())
        self.SCLK10_11PD.setSizePolicy(sizePolicy)
        self.SCLK10_11PD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SCLK10_11PD.setText("")
        self.SCLK10_11PD.setObjectName("SCLK10_11PD")
        self.DCLK10_11POL = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK10_11POL.setGeometry(QtCore.QRect(821, 1185, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK10_11POL.sizePolicy().hasHeightForWidth())
        self.DCLK10_11POL.setSizePolicy(sizePolicy)
        self.DCLK10_11POL.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK10_11POL.setText("")
        self.DCLK10_11POL.setObjectName("DCLK10_11POL")
        self.DCLK10_11HSTrig = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK10_11HSTrig.setGeometry(QtCore.QRect(607, 1196, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK10_11HSTrig.sizePolicy().hasHeightForWidth())
        self.DCLK10_11HSTrig.setSizePolicy(sizePolicy)
        self.DCLK10_11HSTrig.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK10_11HSTrig.setText("")
        self.DCLK10_11HSTrig.setObjectName("DCLK10_11HSTrig")
        self.DCLK10_11DDLY = QtWidgets.QSpinBox(ClkOutputs)
        self.DCLK10_11DDLY.setGeometry(QtCore.QRect(267, 1151, 131, 25))
        self.DCLK10_11DDLY.setObjectName("DCLK10_11DDLY")
        self.DCLK10_11PD = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK10_11PD.setGeometry(QtCore.QRect(98, 1178, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK10_11PD.sizePolicy().hasHeightForWidth())
        self.DCLK10_11PD.setSizePolicy(sizePolicy)
        self.DCLK10_11PD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK10_11PD.setText("")
        self.DCLK10_11PD.setObjectName("DCLK10_11PD")
        self.SCLK4_5DDLY = QtWidgets.QComboBox(ClkOutputs)
        self.SCLK4_5DDLY.setGeometry(QtCore.QRect(244, 650, 131, 25))
        self.SCLK4_5DDLY.setObjectName("SCLK4_5DDLY")
        self.SCLK6_7DDLY = QtWidgets.QComboBox(ClkOutputs)
        self.SCLK6_7DDLY.setGeometry(QtCore.QRect(250, 850, 131, 25))
        self.SCLK6_7DDLY.setObjectName("SCLK6_7DDLY")
        self.SCLK8_9DDLY = QtWidgets.QComboBox(ClkOutputs)
        self.SCLK8_9DDLY.setGeometry(QtCore.QRect(249, 1052, 131, 25))
        self.SCLK8_9DDLY.setObjectName("SCLK8_9DDLY")
        self.SCLK10_11DDLY = QtWidgets.QComboBox(ClkOutputs)
        self.SCLK10_11DDLY.setGeometry(QtCore.QRect(248, 1257, 131, 25))
        self.SCLK10_11DDLY.setObjectName("SCLK10_11DDLY")
        self.CLKout13_SRCMUX = QtWidgets.QCheckBox(ClkOutputs)
        self.CLKout13_SRCMUX.setGeometry(QtCore.QRect(960, 1480, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.CLKout13_SRCMUX.sizePolicy().hasHeightForWidth())
        self.CLKout13_SRCMUX.setSizePolicy(sizePolicy)
        self.CLKout13_SRCMUX.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.CLKout13_SRCMUX.setText("")
        self.CLKout13_SRCMUX.setObjectName("CLKout13_SRCMUX")
        self.DCLK12_13DIV = QtWidgets.QSpinBox(ClkOutputs)
        self.DCLK12_13DIV.setGeometry(QtCore.QRect(102, 1343, 131, 25))
        self.DCLK12_13DIV.setObjectName("DCLK12_13DIV")
        self.SCLK12_13POL = QtWidgets.QCheckBox(ClkOutputs)
        self.SCLK12_13POL.setGeometry(QtCore.QRect(800, 1481, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SCLK12_13POL.sizePolicy().hasHeightForWidth())
        self.SCLK12_13POL.setSizePolicy(sizePolicy)
        self.SCLK12_13POL.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SCLK12_13POL.setText("")
        self.SCLK12_13POL.setObjectName("SCLK12_13POL")
        self.DCLK12_13BYPASS = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK12_13BYPASS.setGeometry(QtCore.QRect(820, 1395, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK12_13BYPASS.sizePolicy().hasHeightForWidth())
        self.DCLK12_13BYPASS.setSizePolicy(sizePolicy)
        self.DCLK12_13BYPASS.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK12_13BYPASS.setText("")
        self.DCLK12_13BYPASS.setObjectName("DCLK12_13BYPASS")
        self.DCLK12_13DDLYPD = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK12_13DDLYPD.setGeometry(QtCore.QRect(260, 1368, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK12_13DDLYPD.sizePolicy().hasHeightForWidth())
        self.DCLK12_13DDLYPD.setSizePolicy(sizePolicy)
        self.DCLK12_13DDLYPD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK12_13DDLYPD.setText("")
        self.DCLK12_13DDLYPD.setObjectName("DCLK12_13DDLYPD")
        self.DCLK12_13HSTrig = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK12_13HSTrig.setGeometry(QtCore.QRect(611, 1386, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK12_13HSTrig.sizePolicy().hasHeightForWidth())
        self.DCLK12_13HSTrig.setSizePolicy(sizePolicy)
        self.DCLK12_13HSTrig.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK12_13HSTrig.setText("")
        self.DCLK12_13HSTrig.setObjectName("DCLK12_13HSTrig")
        self.DCLK12_13POL = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK12_13POL.setGeometry(QtCore.QRect(820, 1375, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK12_13POL.sizePolicy().hasHeightForWidth())
        self.DCLK12_13POL.setSizePolicy(sizePolicy)
        self.DCLK12_13POL.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK12_13POL.setText("")
        self.DCLK12_13POL.setObjectName("DCLK12_13POL")
        self.SCLK12_13HSTrig = QtWidgets.QCheckBox(ClkOutputs)
        self.SCLK12_13HSTrig.setGeometry(QtCore.QRect(430, 1477, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SCLK12_13HSTrig.sizePolicy().hasHeightForWidth())
        self.SCLK12_13HSTrig.setSizePolicy(sizePolicy)
        self.SCLK12_13HSTrig.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SCLK12_13HSTrig.setText("")
        self.SCLK12_13HSTrig.setObjectName("SCLK12_13HSTrig")
        self.DCLK12_13DDLY = QtWidgets.QSpinBox(ClkOutputs)
        self.DCLK12_13DDLY.setGeometry(QtCore.QRect(272, 1343, 131, 25))
        self.DCLK12_13DDLY.setObjectName("DCLK12_13DDLY")
        self.SCLK12_13ADLYEnb = QtWidgets.QCheckBox(ClkOutputs)
        self.SCLK12_13ADLYEnb.setGeometry(QtCore.QRect(620, 1474, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SCLK12_13ADLYEnb.sizePolicy().hasHeightForWidth())
        self.SCLK12_13ADLYEnb.setSizePolicy(sizePolicy)
        self.SCLK12_13ADLYEnb.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SCLK12_13ADLYEnb.setText("")
        self.SCLK12_13ADLYEnb.setObjectName("SCLK12_13ADLYEnb")
        self.DCLK12_13PD = QtWidgets.QCheckBox(ClkOutputs)
        self.DCLK12_13PD.setGeometry(QtCore.QRect(103, 1368, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DCLK12_13PD.sizePolicy().hasHeightForWidth())
        self.DCLK12_13PD.setSizePolicy(sizePolicy)
        self.DCLK12_13PD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DCLK12_13PD.setText("")
        self.DCLK12_13PD.setObjectName("DCLK12_13PD")
        self.RGDCLK12_13HSEN = QtWidgets.QCheckBox(ClkOutputs)
        self.RGDCLK12_13HSEN.setGeometry(QtCore.QRect(611, 1368, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.RGDCLK12_13HSEN.sizePolicy().hasHeightForWidth())
        self.RGDCLK12_13HSEN.setSizePolicy(sizePolicy)
        self.RGDCLK12_13HSEN.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.RGDCLK12_13HSEN.setText("")
        self.RGDCLK12_13HSEN.setObjectName("RGDCLK12_13HSEN")
        self.DDLYd12EN = QtWidgets.QCheckBox(ClkOutputs)
        self.DDLYd12EN.setGeometry(QtCore.QRect(458, 1369, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.DDLYd12EN.sizePolicy().hasHeightForWidth())
        self.DDLYd12EN.setSizePolicy(sizePolicy)
        self.DDLYd12EN.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.DDLYd12EN.setText("")
        self.DDLYd12EN.setObjectName("DDLYd12EN")
        self.SCLK12_13DDLY = QtWidgets.QComboBox(ClkOutputs)
        self.SCLK12_13DDLY.setGeometry(QtCore.QRect(251, 1449, 131, 25))
        self.SCLK12_13DDLY.setObjectName("SCLK12_13DDLY")
        self.SCLK12_13PD = QtWidgets.QCheckBox(ClkOutputs)
        self.SCLK12_13PD.setGeometry(QtCore.QRect(80, 1465, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SCLK12_13PD.sizePolicy().hasHeightForWidth())
        self.SCLK12_13PD.setSizePolicy(sizePolicy)
        self.SCLK12_13PD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.SCLK12_13PD.setText("")
        self.SCLK12_13PD.setObjectName("SCLK12_13PD")
        self.lineEditFout0Output = QtWidgets.QLineEdit(ClkOutputs)
        self.lineEditFout0Output.setGeometry(QtCore.QRect(1063, 128, 81, 21))
        self.lineEditFout0Output.setObjectName("lineEditFout0Output")
        self.lineEditFout1Output = QtWidgets.QLineEdit(ClkOutputs)
        self.lineEditFout1Output.setGeometry(QtCore.QRect(1062, 218, 81, 21))
        self.lineEditFout1Output.setObjectName("lineEditFout1Output")
        self.lineEditFout3Output = QtWidgets.QLineEdit(ClkOutputs)
        self.lineEditFout3Output.setGeometry(QtCore.QRect(1058, 419, 91, 20))
        self.lineEditFout3Output.setObjectName("lineEditFout3Output")
        self.lineEditFout2Output = QtWidgets.QLineEdit(ClkOutputs)
        self.lineEditFout2Output.setGeometry(QtCore.QRect(1061, 329, 81, 20))
        self.lineEditFout2Output.setObjectName("lineEditFout2Output")
        self.lineEditFout5Output = QtWidgets.QLineEdit(ClkOutputs)
        self.lineEditFout5Output.setGeometry(QtCore.QRect(1057, 623, 91, 20))
        self.lineEditFout5Output.setObjectName("lineEditFout5Output")
        self.lineEditFout4Output = QtWidgets.QLineEdit(ClkOutputs)
        self.lineEditFout4Output.setGeometry(QtCore.QRect(1058, 533, 91, 20))
        self.lineEditFout4Output.setObjectName("lineEditFout4Output")
        self.lineEditFout7Output = QtWidgets.QLineEdit(ClkOutputs)
        self.lineEditFout7Output.setGeometry(QtCore.QRect(1063, 819, 91, 24))
        self.lineEditFout7Output.setObjectName("lineEditFout7Output")
        self.lineEditFout6Output = QtWidgets.QLineEdit(ClkOutputs)
        self.lineEditFout6Output.setGeometry(QtCore.QRect(1059, 732, 91, 20))
        self.lineEditFout6Output.setObjectName("lineEditFout6Output")
        self.lineEditFout9Output = QtWidgets.QLineEdit(ClkOutputs)
        self.lineEditFout9Output.setGeometry(QtCore.QRect(1062, 1023, 91, 21))
        self.lineEditFout9Output.setObjectName("lineEditFout9Output")
        self.lineEditFout8Output = QtWidgets.QLineEdit(ClkOutputs)
        self.lineEditFout8Output.setGeometry(QtCore.QRect(1058, 931, 91, 24))
        self.lineEditFout8Output.setObjectName("lineEditFout8Output")
        self.lineEditFout11Output = QtWidgets.QLineEdit(ClkOutputs)
        self.lineEditFout11Output.setGeometry(QtCore.QRect(1061, 1227, 91, 21))
        self.lineEditFout11Output.setObjectName("lineEditFout11Output")
        self.lineEditFout10Output = QtWidgets.QLineEdit(ClkOutputs)
        self.lineEditFout10Output.setGeometry(QtCore.QRect(1061, 1138, 91, 21))
        self.lineEditFout10Output.setObjectName("lineEditFout10Output")
        self.lineEditFout13Output = QtWidgets.QLineEdit(ClkOutputs)
        self.lineEditFout13Output.setGeometry(QtCore.QRect(1065, 1421, 91, 21))
        self.lineEditFout13Output.setObjectName("lineEditFout13Output")
        self.lineEditFout12Output = QtWidgets.QLineEdit(ClkOutputs)
        self.lineEditFout12Output.setGeometry(QtCore.QRect(1065, 1332, 91, 20))
        self.lineEditFout12Output.setObjectName("lineEditFout12Output")
        self.CLKout0_SRCMUX = QtWidgets.QCheckBox(ClkOutputs)
        self.CLKout0_SRCMUX.setGeometry(QtCore.QRect(960, 107, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.CLKout0_SRCMUX.sizePolicy().hasHeightForWidth())
        self.CLKout0_SRCMUX.setSizePolicy(sizePolicy)
        self.CLKout0_SRCMUX.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.CLKout0_SRCMUX.setText("")
        self.CLKout0_SRCMUX.setObjectName("CLKout0_SRCMUX")
        self.CLKout2_SRCMUX = QtWidgets.QCheckBox(ClkOutputs)
        self.CLKout2_SRCMUX.setGeometry(QtCore.QRect(960, 304, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.CLKout2_SRCMUX.sizePolicy().hasHeightForWidth())
        self.CLKout2_SRCMUX.setSizePolicy(sizePolicy)
        self.CLKout2_SRCMUX.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.CLKout2_SRCMUX.setText("")
        self.CLKout2_SRCMUX.setObjectName("CLKout2_SRCMUX")
        self.CLKout4_SRCMUX = QtWidgets.QCheckBox(ClkOutputs)
        self.CLKout4_SRCMUX.setGeometry(QtCore.QRect(960, 506, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.CLKout4_SRCMUX.sizePolicy().hasHeightForWidth())
        self.CLKout4_SRCMUX.setSizePolicy(sizePolicy)
        self.CLKout4_SRCMUX.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.CLKout4_SRCMUX.setText("")
        self.CLKout4_SRCMUX.setObjectName("CLKout4_SRCMUX")
        self.CLKout6_SRCMUX = QtWidgets.QCheckBox(ClkOutputs)
        self.CLKout6_SRCMUX.setGeometry(QtCore.QRect(960, 710, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.CLKout6_SRCMUX.sizePolicy().hasHeightForWidth())
        self.CLKout6_SRCMUX.setSizePolicy(sizePolicy)
        self.CLKout6_SRCMUX.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.CLKout6_SRCMUX.setText("")
        self.CLKout6_SRCMUX.setObjectName("CLKout6_SRCMUX")
        self.CLKout8_SRCMUX = QtWidgets.QCheckBox(ClkOutputs)
        self.CLKout8_SRCMUX.setGeometry(QtCore.QRect(960, 910, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.CLKout8_SRCMUX.sizePolicy().hasHeightForWidth())
        self.CLKout8_SRCMUX.setSizePolicy(sizePolicy)
        self.CLKout8_SRCMUX.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.CLKout8_SRCMUX.setText("")
        self.CLKout8_SRCMUX.setObjectName("CLKout8_SRCMUX")
        self.CLKout10_SRCMUX = QtWidgets.QCheckBox(ClkOutputs)
        self.CLKout10_SRCMUX.setGeometry(QtCore.QRect(961, 1117, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.CLKout10_SRCMUX.sizePolicy().hasHeightForWidth())
        self.CLKout10_SRCMUX.setSizePolicy(sizePolicy)
        self.CLKout10_SRCMUX.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.CLKout10_SRCMUX.setText("")
        self.CLKout10_SRCMUX.setObjectName("CLKout10_SRCMUX")
        self.CLKout12_SRCMUX = QtWidgets.QCheckBox(ClkOutputs)
        self.CLKout12_SRCMUX.setGeometry(QtCore.QRect(965, 1310, 20, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.CLKout12_SRCMUX.sizePolicy().hasHeightForWidth())
        self.CLKout12_SRCMUX.setSizePolicy(sizePolicy)
        self.CLKout12_SRCMUX.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 20px;  /* 设置方框宽度 */\n"
"                height: 20px; /* 设置方框高度 */\n"
"            }")
        self.CLKout12_SRCMUX.setText("")
        self.CLKout12_SRCMUX.setObjectName("CLKout12_SRCMUX")
        self.SCLK0_1ADLY = QtWidgets.QSpinBox(ClkOutputs)
        self.SCLK0_1ADLY.setGeometry(QtCore.QRect(620, 237, 131, 30))
        self.SCLK0_1ADLY.setObjectName("SCLK0_1ADLY")
        self.SCLK2_3ADLY = QtWidgets.QSpinBox(ClkOutputs)
        self.SCLK2_3ADLY.setGeometry(QtCore.QRect(626, 436, 131, 30))
        self.SCLK2_3ADLY.setObjectName("SCLK2_3ADLY")
        self.SCLK8_9ADLY = QtWidgets.QSpinBox(ClkOutputs)
        self.SCLK8_9ADLY.setGeometry(QtCore.QRect(629, 1043, 131, 30))
        self.SCLK8_9ADLY.setObjectName("SCLK8_9ADLY")
        self.SCLK10_11ADLY = QtWidgets.QSpinBox(ClkOutputs)
        self.SCLK10_11ADLY.setGeometry(QtCore.QRect(628, 1247, 131, 30))
        self.SCLK10_11ADLY.setObjectName("SCLK10_11ADLY")
        self.SCLK12_13ADLY = QtWidgets.QSpinBox(ClkOutputs)
        self.SCLK12_13ADLY.setGeometry(QtCore.QRect(630, 1440, 141, 31))
        self.SCLK12_13ADLY.setObjectName("SCLK12_13ADLY")
        self.SCLK4_5ADLY = QtWidgets.QSpinBox(ClkOutputs)
        self.SCLK4_5ADLY.setGeometry(QtCore.QRect(625, 640, 131, 31))
        self.SCLK4_5ADLY.setObjectName("SCLK4_5ADLY")
        self.SCLK6_7ADLY = QtWidgets.QSpinBox(ClkOutputs)
        self.SCLK6_7ADLY.setGeometry(QtCore.QRect(630, 840, 131, 31))
        self.SCLK6_7ADLY.setObjectName("SCLK6_7ADLY")

        self.retranslateUi(ClkOutputs)
        QtCore.QMetaObject.connectSlotsByName(ClkOutputs)

    def retranslateUi(self, ClkOutputs):
        _translate = QtCore.QCoreApplication.translate
        ClkOutputs.setWindowTitle(_translate("ClkOutputs", "Form"))
        self.labelFvco.setText(_translate("ClkOutputs", "Fvco:"))
        self.lineEditFvco.setToolTip(_translate("ClkOutputs", "输入Fvco值用于计算输出频率"))
        self.lineEditFvco.setPlaceholderText(_translate("ClkOutputs", "输入Fvco"))
from ..resources import clkoutput_rc
