"""
菜单管理器
负责创建和管理主窗口的菜单和工具栏
"""

from PyQt5.QtWidgets import QAction, QMenu, QActionGroup
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class MenuManager:
    """菜单管理器"""
    
    def __init__(self, main_window):
        """初始化菜单管理器
        
        Args:
            main_window: RegisterMainWindow实例
        """
        self.main_window = main_window
        
    def create_menu_and_toolbar(self):
        """创建菜单和工具栏"""
        # 创建菜单栏
        menubar = self.main_window.menuBar()
        
        # 创建各个菜单
        self._create_file_menu(menubar)
        self._create_tools_menu(menubar)
        self._create_settings_menu(menubar)
        self._create_help_menu(menubar)
        
        # 创建工具栏
        self._create_toolbar()
        
    def _create_file_menu(self, menubar):
        """创建文件菜单"""
        file_menu = menubar.addMenu('文件(&F)')
        
        # 添加保存和加载操作
        save_action = QAction('保存配置(&S)', self.main_window)
        save_action.setShortcut("Ctrl+S")
        save_action.triggered.connect(self.main_window._handle_save_requested)
        file_menu.addAction(save_action)
        
        load_action = QAction('加载配置(&L)', self.main_window)
        load_action.setShortcut("Ctrl+L")
        load_action.triggered.connect(self.main_window._handle_load_requested)
        file_menu.addAction(load_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('退出(&X)', self.main_window)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.main_window.close)
        file_menu.addAction(exit_action)
        
    def _create_tools_menu(self, menubar):
        """创建工具菜单"""
        tools_menu = menubar.addMenu('工具(&T)')
        
        # 添加工具菜单项
        self.mode_comm_action = QAction('模拟通信(&W)', self.main_window)
        self.mode_comm_action.setCheckable(True)
        self.mode_comm_action.setChecked(self.main_window.simulation_mode)
        self.mode_comm_action.triggered.connect(self.main_window._toggle_simulation_mode)
        tools_menu.addAction(self.mode_comm_action)

        # 添加自动写入选项
        self.auto_write_action = QAction('自动写入(&A)', self.main_window)
        self.auto_write_action.setCheckable(True)
        self.auto_write_action.setChecked(getattr(self.main_window, 'auto_write_mode', False))
        self.auto_write_action.triggered.connect(self.main_window._toggle_auto_write_mode)
        tools_menu.addAction(self.auto_write_action)
        
        tools_menu.addSeparator()

        # 工具窗口菜单项现在通过插件系统动态创建
        # 插件集成服务会自动将工具窗口插件添加到菜单中
        # 这样可以支持插件的动态启用/禁用

        # 在菜单创建完成后，通知插件系统集成工具窗口插件
        self._integrate_tool_window_plugins_to_menu(tools_menu)
        
    def _create_settings_menu(self, menubar):
        """创建设置菜单"""
        settings_menu = menubar.addMenu('设置(&S)')
        
        # 预读取设置子菜单
        self._create_preload_menu(settings_menu)
        
        settings_menu.addSeparator()
        
        # 默认配置子菜单
        self._create_default_config_menu(settings_menu)

        settings_menu.addSeparator()

        # 语言设置子菜单
        self._create_language_menu(settings_menu)

        # 高级设置
        settings_menu.addSeparator()
        advanced_action = QAction("高级设置", self.main_window)
        advanced_action.triggered.connect(self.main_window._show_advanced_settings)
        settings_menu.addAction(advanced_action)
        
    def _create_preload_menu(self, settings_menu):
        """创建预读取设置子菜单"""
        preload_menu = QMenu("寄存器预读取", self.main_window)
        settings_menu.addMenu(preload_menu)
        
        # 启用预读取选项
        preload_action = QAction("启用预读取", self.main_window)
        preload_action.setCheckable(True)
        preload_action.setChecked(self.main_window.settings.value("preload_registers", True, type=bool))
        preload_action.triggered.connect(self.main_window._toggle_preload)
        preload_menu.addAction(preload_action)
        
        # 预读取数量子菜单
        preload_count_menu = QMenu("预读取数量", self.main_window)
        preload_menu.addMenu(preload_count_menu)
        
        # 预读取数量选项组
        preload_count_group = QActionGroup(self.main_window)
        preload_count_group.setExclusive(True)
        
        preload_count_values = [5, 10, 20, 50]
        current_count = self.main_window.settings.value("preload_count", 10, type=int)
        
        for count in preload_count_values:
            action = QAction(f"{count}个寄存器", self.main_window)
            action.setCheckable(True)
            action.setChecked(count == current_count)
            action.setData(count)
            action.triggered.connect(self.main_window._set_preload_count)
            preload_count_group.addAction(action)
            preload_count_menu.addAction(action)
            
    def _create_language_menu(self, settings_menu):
        """创建语言设置子菜单"""
        language_menu = QMenu("语言", self.main_window)
        settings_menu.addMenu(language_menu)
        
        # 语言选项组
        language_group = QActionGroup(self.main_window)
        language_group.setExclusive(True)
        
        # 获取当前语言，默认为中文
        current_lang = self.main_window.settings.value("language", "zh", type=str)
        
        # 添加语言选项
        zh_action = QAction("中文", self.main_window)
        zh_action.setCheckable(True)
        zh_action.setChecked(current_lang == "zh")
        zh_action.setData("zh")
        zh_action.triggered.connect(self.main_window._set_language)
        language_group.addAction(zh_action)
        language_menu.addAction(zh_action)
        
        en_action = QAction("English", self.main_window)
        en_action.setCheckable(True)
        en_action.setChecked(current_lang == "en")
        en_action.setData("en")
        en_action.triggered.connect(self.main_window._set_language)
        language_group.addAction(en_action)
        language_menu.addAction(en_action)

    def _create_default_config_menu(self, settings_menu):
        """创建默认配置子菜单"""
        default_config_menu = QMenu("默认配置", self.main_window)
        settings_menu.addMenu(default_config_menu)

        # 生成默认配置（从register.json）
        save_default_action = QAction("生成默认配置（从register.json）", self.main_window)
        save_default_action.triggered.connect(self.main_window._save_default_configuration)
        default_config_menu.addAction(save_default_action)

        # 恢复默认配置
        restore_default_action = QAction("恢复默认配置", self.main_window)
        restore_default_action.triggered.connect(self.main_window._restore_default_configuration)
        default_config_menu.addAction(restore_default_action)

    def _create_help_menu(self, menubar):
        """创建帮助菜单"""
        help_menu = menubar.addMenu('帮助(&H)')
        
        # 添加用户手册项
        user_manual_action = QAction('用户手册(&M)', self.main_window)
        user_manual_action.setShortcut("F1")
        user_manual_action.triggered.connect(self.main_window._show_user_manual)
        help_menu.addAction(user_manual_action)
        
        # 添加关于项
        about_action = QAction('关于(&A)', self.main_window)
        about_action.triggered.connect(self.main_window._show_about_dialog)
        help_menu.addAction(about_action)
        
    def _create_toolbar(self):
        """创建工具栏"""
        toolbar = self.main_window.addToolBar('主工具栏')
        toolbar.setMovable(False)
        toolbar.setFloatable(False)

        # 添加工具按钮到工具栏
        toolbar.addAction(self.mode_comm_action)
        toolbar.addSeparator()

        # 添加窗口管理按钮
        self._add_window_management_actions(toolbar)
        toolbar.addSeparator()

        # 工具窗口按钮现在通过插件系统动态添加
        # 插件集成服务会自动将核心工具插件的action添加到工具栏

    def _add_window_management_actions(self, toolbar):
        """添加窗口管理相关的动作到工具栏"""
        try:
            # 停靠所有悬浮窗口
            dock_all_action = QAction('停靠所有窗口', self.main_window)
            dock_all_action.setToolTip('将所有悬浮窗口停靠到主界面标签页中')
            dock_all_action.triggered.connect(self._dock_all_floating_windows)
            toolbar.addAction(dock_all_action)

            # 分离所有窗口为悬浮状态
            undock_all_action = QAction('分离所有窗口', self.main_window)
            undock_all_action.setToolTip('将所有标签页窗口分离为悬浮窗口')
            undock_all_action.triggered.connect(self._undock_all_windows)
            toolbar.addAction(undock_all_action)

            # 关闭所有工具窗口
            close_all_action = QAction('关闭所有工具窗口', self.main_window)
            close_all_action.setToolTip('关闭所有打开的工具窗口')
            close_all_action.triggered.connect(self._close_all_tool_windows)
            toolbar.addAction(close_all_action)

        except Exception as e:
            logger.error(f"添加窗口管理动作失败: {str(e)}")

    def _dock_all_floating_windows(self):
        """停靠所有悬浮窗口"""
        try:
            if hasattr(self.main_window, 'plugin_integration_service'):
                plugin_service = self.main_window.plugin_integration_service

                # 获取所有插件窗口（从window_service中获取）
                if hasattr(plugin_service, 'window_service') and hasattr(plugin_service.window_service, 'plugin_windows'):
                    for plugin_name in plugin_service.window_service.plugin_windows.keys():
                        plugin_service.dock_floating_window(plugin_name)

                    self.main_window.show_status_message("所有悬浮窗口已停靠到主界面", 3000)
                    logger.info("所有悬浮窗口已停靠")
                else:
                    self.main_window.show_status_message("没有找到悬浮窗口", 3000)
            else:
                self.main_window.show_status_message("插件服务不可用", 3000)

        except Exception as e:
            logger.error(f"停靠所有悬浮窗口失败: {str(e)}")
            self.main_window.show_status_message("停靠窗口失败", 3000)

    def _undock_all_windows(self):
        """分离所有窗口为悬浮状态"""
        try:
            if hasattr(self.main_window, 'plugin_integration_service'):
                plugin_service = self.main_window.plugin_integration_service

                # 获取所有插件窗口（从window_service中获取）
                if hasattr(plugin_service, 'window_service') and hasattr(plugin_service.window_service, 'plugin_windows'):
                    for plugin_name in plugin_service.window_service.plugin_windows.keys():
                        plugin_service.undock_window_from_tab(plugin_name)

                    self.main_window.show_status_message("所有窗口已分离为悬浮状态", 3000)
                    logger.info("所有窗口已分离为悬浮状态")
                else:
                    self.main_window.show_status_message("没有找到窗口", 3000)
            else:
                self.main_window.show_status_message("插件服务不可用", 3000)

        except Exception as e:
            logger.error(f"分离所有窗口失败: {str(e)}")
            self.main_window.show_status_message("分离窗口失败", 3000)

    def _close_all_tool_windows(self):
        """关闭所有工具窗口"""
        try:
            if hasattr(self.main_window, 'plugin_integration_service'):
                self.main_window.plugin_integration_service.close_all_plugin_windows()
                self.main_window.show_status_message("所有工具窗口已关闭", 3000)
                logger.info("所有工具窗口已关闭")
            else:
                self.main_window.show_status_message("插件服务不可用", 3000)

        except Exception as e:
            logger.error(f"关闭所有工具窗口失败: {str(e)}")
            self.main_window.show_status_message("关闭窗口失败", 3000)

    def _integrate_tool_window_plugins_to_menu(self, tools_menu):
        """将工具窗口插件集成到工具菜单"""
        try:
            # 检查主窗口是否有插件集成服务
            if hasattr(self.main_window, 'plugin_integration_service'):
                plugin_service = self.main_window.plugin_integration_service

                # 获取插件管理器
                from core.services.plugin.PluginManager import plugin_manager

                # 获取核心工具窗口插件并按指定顺序排列
                tool_plugins = plugin_manager.get_tool_window_plugins()

                # 定义期望的工具顺序
                desired_order = ['模式设置', '时钟输入控制', '时钟输出', '同步系统参考', 'PLL控制']

                # 创建插件名称到插件对象的映射
                plugin_map = {plugin.name: plugin for plugin in tool_plugins}

                # 按指定顺序收集核心工具插件
                core_tool_plugins = []
                for tool_name in desired_order:
                    if tool_name in plugin_map:
                        core_tool_plugins.append(plugin_map[tool_name])

                # 将核心工具插件添加到工具菜单
                if core_tool_plugins:
                    core_actions = []
                    for plugin in core_tool_plugins:
                        try:
                            # 检查menu_service是否存在
                            if not hasattr(plugin_service, 'menu_service') or plugin_service.menu_service is None:
                                logger.error(f"插件服务没有menu_service属性或为None")
                                # 使用向后兼容的方法
                                action = plugin_service._add_plugin_to_menu(plugin, tools_menu)
                            else:
                                # 使用menu_service的方法
                                action = plugin_service.menu_service._add_plugin_to_menu(plugin, tools_menu)

                            if action:
                                core_actions.append(action)
                        except Exception as e:
                            logger.error(f"添加插件 {plugin.name} 到菜单失败: {str(e)}")
                            continue

                    # 将核心工具插件的action也添加到工具栏
                    if core_actions:
                        self._add_actions_to_toolbar(core_actions)

                    logger.info(f"已将 {len(core_tool_plugins)} 个核心工具插件添加到工具菜单和工具栏")
            else:
                logger.info("插件服务尚未可用，跳过工具插件集成（将在插件系统设置完成后执行）")

        except Exception as e:
            logger.error(f"集成工具窗口插件到菜单失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def _add_actions_to_toolbar(self, actions):
        """将动作添加到工具栏"""
        try:
            # 获取主工具栏
            toolbar = None
            from PyQt5.QtWidgets import QToolBar

            # 查找现有的主工具栏
            for toolbar_obj in self.main_window.findChildren(QToolBar):
                if toolbar_obj.windowTitle() == '主工具栏':
                    toolbar = toolbar_obj
                    break

            if not toolbar:
                # 如果找不到主工具栏，获取第一个工具栏或创建新的
                toolbars = self.main_window.findChildren(QToolBar)
                if toolbars:
                    toolbar = toolbars[0]
                    logger.info(f"使用现有工具栏: {toolbar.windowTitle()}")
                else:
                    # 创建新的工具栏
                    toolbar = self.main_window.addToolBar('主工具栏')
                    toolbar.setMovable(False)
                    toolbar.setFloatable(False)
                    logger.info("创建了新的主工具栏")

            # 添加动作到工具栏
            for action in actions:
                if action:
                    toolbar.addAction(action)
                    logger.debug(f"已将动作 '{action.text()}' 添加到工具栏")

            logger.info(f"已将 {len(actions)} 个动作添加到工具栏")

        except Exception as e:
            logger.error(f"添加动作到工具栏失败: {str(e)}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
