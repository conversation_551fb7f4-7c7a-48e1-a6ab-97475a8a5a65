{"0x00": {"bits": [{"bit": "15:0", "name": "DA_DEVICE_VERSION[15:0]", "default": "0001001100000000", "widget_name": null, "widget_type": null, "options": null, "description": "FSJ04832 device version .Read only"}]}, "0x02": {"bits": [{"bit": "15:1", "name": "NC", "default": "000000000000000", "widget_name": null, "widget_type": null, "options": null, "description": "Read only"}, {"bit": "0", "name": "POWERDOWN", "default": "0", "widget_name": "powerDown", "widget_type": "checkbox", "options": null, "description": "0: Normal operation; 1: Power down device."}]}, "0x03": {"bits": [{"bit": "15:0", "name": "ID_DEVICE_TYPE[15:0]", "default": "0000000000000110", "widget_name": null, "widget_type": null, "options": null, "description": "PLL product device type. Read Only"}]}, "0x04": {"bits": [{"bit": "15:0", "name": "ID_PROD[15:0]", "default": "1101000101100011", "widget_name": null, "widget_type": null, "options": null, "description": "the product identifier.Read Only"}]}, "0x05": {"bits": [{"bit": "15:12", "name": "HOLDOVERSTATE[3:0]", "default": "0000", "widget_name": null, "widget_type": null, "options": null, "description": "Read only"}, {"bit": "11:8", "name": "HOLDOVER_NXT_STATE[3:0]", "default": "0001", "widget_name": null, "widget_type": null, "options": null, "description": "Read only"}, {"bit": "7:4", "name": "CN_STATE[3:0]", "default": "0000", "widget_name": null, "widget_type": null, "options": null, "description": "Read only"}, {"bit": "3:0", "name": "CN_NXT_STATE[3:0]", "default": "0000", "widget_name": null, "widget_type": null, "options": null, "description": "Read only"}]}, "0x06": {"bits": [{"bit": "15:8", "name": "NC", "default": "00000000", "widget_name": null, "widget_type": null, "options": null, "description": "Read only"}, {"bit": "7:0", "name": "ID_MASKREV[7:0]", "default": "01110000", "widget_name": null, "widget_type": null, "options": null, "description": "IC version identifier for LMK04832. Read Only"}]}, "0x0C": {"bits": [{"bit": "15:0", "name": "ID_VNDR[15:0]", "default": "0101000100000100", "widget_name": null, "widget_type": null, "options": null, "description": "the vendor identifier. Read Only"}]}, "0x10": {"bits": [{"bit": "15:10", "name": "NC", "default": "000000", "widget_name": null, "widget_type": null, "options": null, "description": "Read only"}, {"bit": "9:0", "name": "DCLK0_1_DIV[9:0]", "default": "0000000010", "widget_name": "DCLK0_1DIV", "widget_type": "spinbox", "options": "1:1023", "description": "divide value for the clock output, the divide may be even or odd. Both even or odd divides output a 50% duty cycle clock ."}]}, "0x11": {"bits": [{"bit": "15:10", "name": "NC", "default": "000000", "widget_name": null, "widget_type": null, "options": null, "description": "Read only"}, {"bit": "9:0", "name": "DCLK0_1_DDLY[9:0]", "default": "0000001010", "widget_name": "DCLK0_1DDLY", "widget_type": "spinbox", "options": "2:1023", "description": "static digital delay which takes effect after a SYNC"}]}, "0x12": {"bits": [{"bit": "15:8", "name": "NC", "default": "00000000", "widget_name": null, "widget_type": null, "options": null, "description": "Read only"}, {"bit": "7", "name": "CLKout0_1_PD", "default": "0", "widget_name": "CLKout0_1PD", "widget_type": "checkbox", "options": null, "description": "Power down the clock group defined by X and Y.\n0: Enabled; 1: Power down entire clock group including both CLKoutX and CLKoutY."}, {"bit": "6:5", "name": "NC", "default": "00", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "4", "name": "DCLK0_1_DDLY_PD", "default": "0", "widget_name": "DCLK0_1DDLYPD", "widget_type": "checkbox", "options": null, "description": "Powerdown the device clock digital delay circuitry. \n0: Enabled; 1: Power down static digital delay for device clock divider."}, {"bit": "3:0", "name": "NC", "default": "0000", "widget_name": null, "widget_type": null, "options": null, "description": null}]}, "0x13": {"bits": [{"bit": "15:6", "name": "NC", "default": "0000000000", "widget_name": null, "widget_type": null, "options": null, "description": "Read only"}, {"bit": "5", "name": "CLKout0_SRC_MUX", "default": "0", "widget_name": "CLKout0_SRCMUX", "widget_type": "checkbox", "options": null, "description": "Select CLKoutX clock source. Source must also be powered up. 0: <PERSON>ce Clock; 1: SYSREF"}, {"bit": "4", "name": "DCLK0_1_PD", "default": "0", "widget_name": "DCLK0_1PD", "widget_type": "checkbox", "options": null, "description": "Power down the clock group defined by X and Y. 0: Enabled; 1: Power down enter clock group X_Y."}, {"bit": "3", "name": "DCLK0_1_BYP", "default": "0", "widget_name": "DCLK0_1BYPASS", "widget_type": "checkbox", "options": null, "description": "Enable high performance bypass path for even clock outputs.\n0: CLKoutX not in high performance bypass mode. CML is not valid for CLKoutX_FMT.\n1: CLKoutX in high performance bypass mode. Only CML clock format is valid."}, {"bit": "2", "name": "NC", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "Read only"}, {"bit": "1", "name": "DCLK0_1_POL", "default": "0", "widget_name": "DCLK0_1POL", "widget_type": "checkbox", "options": null, "description": "Invert polarity of device clock output. This also applies to CLKoutX in high performance bypass mode. Polarity invert is a method to get a half-step phase adjustment in high performance bypass mode or /1 divide value.\n0: Normal polarity; 1: Invert polarity"}, {"bit": "0", "name": "DCLK0_1_HS", "default": "0", "widget_name": "DCLK0_1HSTrig", "widget_type": "checkbox", "options": null, "description": "Sets the device clock half step value. Must be set to zero (0) for a divide of 1. \n0: No phase adjustment; 1: Adjust device clock phase -0.5 clock distribution path cycles."}]}, "0x14": {"bits": [{"bit": "15:6", "name": "NC", "default": "0000000000", "widget_name": null, "widget_type": null, "options": null, "description": "Read only"}, {"bit": "5", "name": "CLKout1_SRC_MUX", "default": "0", "widget_name": "CLKout1_SRCMUX", "widget_type": "checkbox", "options": null, "description": "Select CLKoutX clock source. Source must also be powered up. 0: <PERSON>ce Clock; 1: SYSREF"}, {"bit": "4", "name": "SCLK0_1_PD", "default": "1", "widget_name": "SCLK0_1PD", "widget_type": "checkbox", "options": null, "description": "Power down the SYSREF clock output circuitry. 0: SYSREF enabled; 1: Power down SYSREF path for clock pair."}, {"bit": "3:2", "name": "SCLK0_1_DIS_MODE[1:0]", "default": "00", "widget_name": null, "widget_type": null, "options": null, "description": "Set disable mode for clock outputs controlled by SYSREF. Some cases will assert when SYSREF_GBL_PD = 1."}, {"bit": "1", "name": "SCLK0_1_POL", "default": "0", "widget_name": "SCLK0_1POL", "widget_type": "checkbox", "options": null, "description": "Sets the polarity of clock on SCLKX_Y when SYSREF clock output is selected with CLKoutX_MUX or CLKoutY_MUX.\n0: Normal; 1: Inverted"}, {"bit": "0", "name": "SCLK0_1_HS", "default": "0", "widget_name": "SCLK0_1HSTrig", "widget_type": "checkbox", "options": null, "description": "Sets the local SYSREF clock half step value.\n0: No phase adjustment; 1: Adjust device SYSREF phase -0.5 clock distribution path cycles."}]}, "0x15": {"bits": [{"bit": "15:9", "name": "NC", "default": "0000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved.Read only"}, {"bit": "8", "name": "SCLK0_1_ADLY_Enb", "default": "0", "widget_name": "SCLK0_1ADLYEnb", "widget_type": "checkbox", "options": null, "description": "Enables analog delay for the SYSREF output. 1: Disabled; 0: Enabled"}, {"bit": "7:0", "name": "SCLK0_1_ADLY[7:0]", "default": "00000000", "widget_name": "SCLK0_1ADLY", "widget_type": "spinbox", "options": "0:255", "description": "SYSREF analog delay in approximately 10ps steps. Selecting analog delay adds an additional 125 ps in propagation delay. Range is 125 ps to 2675 ps."}]}, "0x16": {"bits": [{"bit": "15:4", "name": "NC", "default": "000000000000", "widget_name": null, "widget_type": null, "options": null, "description": "Read only"}, {"bit": "3:0", "name": "SCLK0_1_DDLY[3:0]", "default": "0001", "widget_name": "SCLK0_1DDLY", "widget_type": "combobox", "options": "0:10", "description": "Set the number of VCO cycles to delay SCLKout by"}]}, "0x17": {"bits": [{"bit": "15:8", "name": "NC", "default": "00000000", "widget_name": null, "widget_type": null, "options": null, "description": "Read only"}, {"bit": "7:4", "name": "CLKout1_FMT[3:0]", "default": "0101", "widget_name": "CLKout1FMT", "widget_type": "combobox", "options": "0:15", "description": "Set CLKoutX/Y clock format"}, {"bit": "3:0", "name": "CLKout0_FMT[3:0]", "default": "0101", "widget_name": "CLKout0FMT", "widget_type": "combobox", "options": "0:15", "description": "Set CLKoutX/Y clock format"}]}, "0x18": {"bits": [{"bit": "15:10", "name": "NC", "default": "000000", "widget_name": null, "widget_type": null, "options": null, "description": "Read only"}, {"bit": "9:0", "name": "DCLK2_3_DIV[9:0]", "default": "0000000100", "widget_name": "DCLK2_3DIV", "widget_type": "spinbox", "options": "1:1023", "description": "divide value for the clock output, the divide may be even or odd. Both even or odd divides output a 50% duty cycle clock ."}]}, "0x19": {"bits": [{"bit": "15:10", "name": "NC", "default": "000000", "widget_name": null, "widget_type": null, "options": null, "description": "Read only"}, {"bit": "9:0", "name": "DCLK2_3_DDLY[9:0]", "default": "0000001010", "widget_name": "DCLK2_3DDLY", "widget_type": "spinbox", "options": "2:1023", "description": "Static digital delay which takes effect after a SYNC."}]}, "0x1A": {"bits": [{"bit": "15:8", "name": "NC", "default": "00000000", "widget_name": null, "widget_type": null, "options": null, "description": "Read only"}, {"bit": "7", "name": "CLKout2_3_PD", "default": "0", "widget_name": "CLKout2_3PD", "widget_type": "checkbox", "options": null, "description": "Power down the clock group defined by X and Y.\n0: Enabled; 1: Power down entire clock group including both CLKoutX and CLKoutY."}, {"bit": "6:5", "name": "NC", "default": "00", "widget_name": null, "widget_type": null, "options": null, "description": "Read only"}, {"bit": "4", "name": "DCLK2_3_DDLY_PD", "default": "0", "widget_name": "DCLK2_3DDLYPD", "widget_type": "checkbox", "options": null, "description": "Powerdown the device clock digital delay circuitry. \n0: Enabled; 1: Power down static digital delay for device clock divider."}, {"bit": "3:0", "name": "NC", "default": "0000", "widget_name": null, "widget_type": null, "options": null, "description": null}]}, "0x1B": {"bits": [{"bit": "15:6", "name": "NC", "default": "0000000000", "widget_name": null, "widget_type": null, "options": null, "description": "Read only"}, {"bit": "5", "name": "CLKout2_SRC_MUX", "default": "0", "widget_name": "CLKout2_SRCMUX", "widget_type": "checkbox", "options": null, "description": "Select CLKoutX clock source. Source must also be powered up. 0: <PERSON>ce Clock; 1: SYSREF"}, {"bit": "4", "name": "DCLK2_3_PD", "default": "0", "widget_name": "DCLK2_3PD", "widget_type": "checkbox", "options": null, "description": "Power down the clock group defined by X and Y. 0: Enabled; 1: Power down enter clock group X_Y."}, {"bit": "3", "name": "DCLK2_3_BYP", "default": "0", "widget_name": "DCLK2_3BYPASS", "widget_type": "checkbox", "options": null, "description": "Enable high performance bypass path for even clock outputs.\n0: CLKoutX not in high performance bypass mode. CML is not valid for CLKoutX_FMT.\n1: CLKoutX in high performance bypass mode. Only CML clock format is valid."}, {"bit": "2", "name": "NC", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "Read only"}, {"bit": "1", "name": "DCLK2_3_POL", "default": "0", "widget_name": "DCLK2_3POL", "widget_type": "checkbox", "options": null, "description": "Invert polarity of device clock output. This also applies to CLKoutX in high performance bypass mode. Polarity invert is a method to get a half-step phase adjustment in high performance bypass mode or /1 divide value.\n0: Normal polarity; 1: Invert polarity"}, {"bit": "0", "name": "DCLK2_3_HS", "default": "0", "widget_name": "DCLK2_3HSTrig", "widget_type": "checkbox", "options": null, "description": "Sets the device clock half step value. Must be set to zero (0) for a divide of 1. \n0: No phase adjustment; 1: Adjust device clock phase -0.5 clock distribution path cycles."}]}, "0x1C": {"bits": [{"bit": "15:6", "name": "NC", "default": "0000000000", "widget_name": null, "widget_type": null, "options": null, "description": "Read only"}, {"bit": "5", "name": "CLKout3_SRC_MUX", "default": "0", "widget_name": "CLKout3_SRCMUX", "widget_type": "checkbox", "options": null, "description": "Select CLKoutX clock source. Source must also be powered up. 0: <PERSON>ce Clock; 1: SYSREF"}, {"bit": "4", "name": "SCLK2_3_PD", "default": "1", "widget_name": "SCLK2_3PD", "widget_type": "checkbox", "options": null, "description": "Power down the SYSREF clock output circuitry. 0: SYSREF enabled; 1: Power down SYSREF path for clock pair."}, {"bit": "3:2", "name": "SCLK2_3_DIS_MODE[1:0]", "default": "00", "widget_name": null, "widget_type": null, "options": null, "description": "Set disable mode for clock outputs controlled by SYSREF. Some cases will assert when SYSREF_GBL_PD = 1."}, {"bit": "1", "name": "SCLK2_3_POL", "default": "0", "widget_name": "SCLK2_3POL", "widget_type": "checkbox", "options": null, "description": "Sets the polarity of clock on SCLKX_Y when SYSREF clock output is selected with CLKoutX_MUX or CLKoutY_MUX.\n0: Normal; 1: Inverted"}, {"bit": "0", "name": "SCLK2_3_HS", "default": "0", "widget_name": "SCLK2_3HSTrig", "widget_type": "checkbox", "options": null, "description": "Sets the local SYSREF clock half step value.\n0: No phase adjustment; 1: Adjust device SYSREF phase -0.5 clock distribution path cycles."}]}, "0x1D": {"bits": [{"bit": "15:9", "name": "NC", "default": "0000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved.Read only"}, {"bit": "8", "name": "SCLK2_3_ADLY_Enb", "default": "0", "widget_name": "SCLK2_3ADLYEnb", "widget_type": "checkbox", "options": null, "description": "Enables analog delay for the SYSREF output. 0: Disabled; 1: Enabled"}, {"bit": "7:0", "name": "SCLK2_3_ADLY[7:0]", "default": "00000000", "widget_name": "SCLK2_3ADLY", "widget_type": "spinbox", "options": "0:255", "description": "SYSREF analog delay in approximately 10ps steps. Selecting analog delay adds an additional 125 ps in propagation delay. Range is 125 ps to 2675 ps."}]}, "0x1E": {"bits": [{"bit": "15:4", "name": "NC", "default": "000000000000", "widget_name": null, "widget_type": null, "options": null, "description": "Read only"}, {"bit": "3:0", "name": "SCLK2_3_DDLY[3:0]", "default": "0001", "widget_name": "SCLK2_3DDLY", "widget_type": "combobox", "options": "0:10", "description": "Set the number of VCO cycles to delay SCLKout by"}]}, "0x1F": {"bits": [{"bit": "15:8", "name": "NC", "default": "00000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "7:4", "name": "CLKout3_FMT[3:0]", "default": "0000", "widget_name": "CLKout3FMT", "widget_type": "combobox", "options": "0:15", "description": "Set CLKoutX/Y clock format"}, {"bit": "3:0", "name": "CLKout2_FMT[3:0]", "default": "0101", "widget_name": "CLKout2FMT", "widget_type": "combobox", "options": "0:15", "description": "Set CLKoutX/Y clock format"}]}, "0x20": {"bits": [{"bit": "15:10", "name": "NC", "default": "000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "9:0", "name": "DCLK4_5_DIV[9:0]", "default": "0000001000", "widget_name": "DCLK4_5DIV", "widget_type": "spinbox", "options": "1:1023", "description": "divide value for the clock output, the divide may be even or odd. Both even or odd divides output a 50% duty cycle clock ."}]}, "0x21": {"bits": [{"bit": "15:10", "name": "NC", "default": "000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "9:0", "name": "DCLK4_5_DDLY[9:0]", "default": "0000001010", "widget_name": "DCLK4_5DDLY", "widget_type": "spinbox", "options": "2:1023", "description": "Static digital delay which takes effect after a SYNC."}]}, "0x22": {"bits": [{"bit": "15:8", "name": "NC", "default": "00000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "7", "name": "CLKout4_5_PD", "default": "1", "widget_name": "CLKout4_5PD", "widget_type": "checkbox", "options": null, "description": "Power down the clock group defined by X and Y.\n0: Enabled; 1: Power down entire clock group including both CLKoutX and CLKoutY."}, {"bit": "6:5", "name": "NC", "default": "00", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "4", "name": "DCLK4_5_DDLY_PD", "default": "0", "widget_name": "DCLK4_5DDLYPD", "widget_type": "checkbox", "options": null, "description": "Powerdown the device clock digital delay circuitry. \n0: Enabled; 1: Power down static digital delay for device clock divider."}, {"bit": "3:0", "name": "NC", "default": "0000", "widget_name": null, "widget_type": null, "options": null, "description": null}]}, "0x23": {"bits": [{"bit": "15:6", "name": "NC", "default": "0000000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "5", "name": "CLKout4_SRC_MUX", "default": "0", "widget_name": "CLKout4_SRCMUX", "widget_type": "checkbox", "options": null, "description": "Select CLKoutX clock source. Source must also be powered up. 0: <PERSON>ce Clock; 1: SYSREF"}, {"bit": "4", "name": "DCLK4_5_PD", "default": "0", "widget_name": "DCLK4_5PD", "widget_type": "checkbox", "options": null, "description": "Power down the clock group defined by X and Y. 0: Enabled; 1: Power down enter clock group X_Y."}, {"bit": "3", "name": "DCLK4_5_BYP", "default": "0", "widget_name": "DCLK4_5BYPASS", "widget_type": "checkbox", "options": null, "description": "Enable high performance bypass path for even clock outputs.\n0: CLKoutX not in high performance bypass mode. CML is not valid for CLKoutX_FMT.\n1: CLKoutX in high performance bypass mode. Only CML clock format is valid."}, {"bit": "2", "name": "NC", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "1", "name": "DCLK4_5_POL", "default": "0", "widget_name": "DCLK4_5POL", "widget_type": "checkbox", "options": null, "description": "Invert polarity of device clock output. This also applies to CLKoutX in high performance bypass mode. Polarity invert is a method to get a half-step phase adjustment in high performance bypass mode or /1 divide value.\n0: Normal polarity; 1: Invert polarity"}, {"bit": "0", "name": "DCLK4_5_HS", "default": "0", "widget_name": "DCLK4_5HSTrig", "widget_type": "checkbox", "options": null, "description": "Sets the device clock half step value. Must be set to zero (0) for a divide of 1. \n0: No phase adjustment; 1: Adjust device clock phase -0.5 clock distribution path cycles."}]}, "0x24": {"bits": [{"bit": "15:6", "name": "NC", "default": "0000000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "5", "name": "CLKout5_SRC_MUX", "default": "0", "widget_name": "CLKout5_SRCMUX", "widget_type": "checkbox", "options": null, "description": "Select CLKoutX clock source. Source must also be powered up. 0: <PERSON>ce Clock; 1: SYSREF"}, {"bit": "4", "name": "SCLK4_5_PD", "default": "1", "widget_name": "SCLK4_5PD", "widget_type": "checkbox", "options": null, "description": "Power down the SYSREF clock output circuitry. 0: SYSREF enabled; 1: Power down SYSREF path for clock pair."}, {"bit": "3:2", "name": "SCLK4_5_DIS_MODE[1:0]", "default": "00", "widget_name": null, "widget_type": null, "options": null, "description": "Set disable mode for clock outputs controlled by SYSREF. Some cases will assert when SYSREF_GBL_PD = 1."}, {"bit": "1", "name": "SCLK4_5_POL", "default": "0", "widget_name": "SCLK4_5POL", "widget_type": "checkbox", "options": null, "description": "Sets the polarity of clock on SCLKX_Y when SYSREF clock output is selected with CLKoutX_MUX or CLKoutY_MUX.\n0: Normal; 1: Inverted"}, {"bit": "0", "name": "SCLK4_5_HS", "default": "0", "widget_name": "SCLK4_5HSTrig", "widget_type": "checkbox", "options": null, "description": "Sets the local SYSREF clock half step value.\n0: No phase adjustment; 1: Adjust device SYSREF phase -0.5 clock distribution path cycles."}]}, "0x25": {"bits": [{"bit": "15:9", "name": "NC", "default": "0000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "8", "name": "SCLK4_5_ADLY_Enb", "default": "0", "widget_name": "SCLK4_5ADLYEnb", "widget_type": "checkbox", "options": null, "description": "Enables analog delay for the SYSREF output. 0: Disabled; 1: Enabled"}, {"bit": "7:0", "name": "SCLK4_5_ADLY[7:0]", "default": "00000000", "widget_name": "SCLK4_5ADLY", "widget_type": "spinbox", "options": "0:255", "description": "SYSREF analog delay in approximately 10ps steps. Selecting analog delay adds an additional 125 ps in propagation delay. Range is 125 ps to 2675 ps."}]}, "0x26": {"bits": [{"bit": "15:4", "name": "NC", "default": "000000000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "3:0", "name": "SCLK4_5_DDLY[3:0]", "default": "0001", "widget_name": "SCLK4_5DDLY", "widget_type": "combobox", "options": "0:10", "description": "Set the number of VCO cycles to delay SCLKout by"}]}, "0x27": {"bits": [{"bit": "15:8", "name": "NC", "default": "00000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "7:4", "name": "CLKout5_FMT[3:0]", "default": "0000", "widget_name": "CLKout5FMT", "widget_type": "combobox", "options": "0:15", "description": "Set CLKoutX/Y clock format"}, {"bit": "3:0", "name": "CLKout4_FMT[3:0]", "default": "0000", "widget_name": "CLKout4FMT", "widget_type": "combobox", "options": "0:15", "description": "Set CLKoutX/Y clock format"}]}, "0x28": {"bits": [{"bit": "15:10", "name": "NC", "default": "000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "9:0", "name": "DCLK6_7_DIV[9:0]", "default": "0000001000", "widget_name": "DCLK6_7DIV", "widget_type": "spinbox", "options": "1:1023", "description": "divide value for the clock output, the divide may be even or odd. Both even or odd divides output a 50% duty cycle clock ."}]}, "0x29": {"bits": [{"bit": "15:10", "name": "NC", "default": "000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "9:0", "name": "DCLK6_7_DDLY[9:0]", "default": "0000001010", "widget_name": "DCLK6_7DDLY", "widget_type": "spinbox", "options": "2:1023", "description": "Static digital delay which takes effect after a SYNC."}]}, "0x2A": {"bits": [{"bit": "15:8", "name": "NC", "default": "00000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "7", "name": "CLKout6_7_PD", "default": "0", "widget_name": "CLKout6_7PD", "widget_type": "checkbox", "options": null, "description": "Power down the clock group defined by X and Y.\n0: Enabled; 1: Power down entire clock group including both CLKoutX and CLKoutY."}, {"bit": "6:5", "name": "NC", "default": "00", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "4", "name": "DCLK6_7_DDLY_PD", "default": "0", "widget_name": "DCLK6_7DDLYPD", "widget_type": "checkbox", "options": null, "description": "Powerdown the device clock digital delay circuitry. \n0: Enabled; 1: Power down static digital delay for device clock divider."}, {"bit": "3:0", "name": "NC", "default": "0000", "widget_name": null, "widget_type": null, "options": null, "description": null}]}, "0x2B": {"bits": [{"bit": "15:6", "name": "NC", "default": "0000000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "5", "name": "CLKout6_SRC_MUX", "default": "0", "widget_name": "CLKout6_SRCMUX", "widget_type": "checkbox", "options": null, "description": "Select CLKoutX clock source. Source must also be powered up. 0: <PERSON>ce Clock; 1: SYSREF"}, {"bit": "4", "name": "DCLK6_7_PD", "default": "0", "widget_name": "DCLK6_7PD", "widget_type": "checkbox", "options": null, "description": "Power down the clock group defined by X and Y. 0: Enabled; 1: Power down enter clock group X_Y."}, {"bit": "3", "name": "DCLK6_7_BYP", "default": "0", "widget_name": "DCLK6_7BYPASS", "widget_type": "checkbox", "options": null, "description": "Enable high performance bypass path for even clock outputs.\n0: CLKoutX not in high performance bypass mode. CML is not valid for CLKoutX_FMT.\n1: CLKoutX in high performance bypass mode. Only CML clock format is valid."}, {"bit": "2", "name": "NC", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "1", "name": "DCLK6_7_POL", "default": "0", "widget_name": "DCLK6_7POL", "widget_type": "checkbox", "options": null, "description": "Invert polarity of device clock output. This also applies to CLKoutX in high performance bypass mode. Polarity invert is a method to get a half-step phase adjustment in high performance bypass mode or /1 divide value.\n0: Normal polarity; 1: Invert polarity"}, {"bit": "0", "name": "DCLK6_7_HS", "default": "0", "widget_name": "DCLK6_7HSTrig", "widget_type": "checkbox", "options": null, "description": "Sets the device clock half step value. Must be set to zero (0) for a divide of 1. \n0: No phase adjustment; 1: Adjust device clock phase -0.5 clock distribution path cycles."}]}, "0x2C": {"bits": [{"bit": "15:6", "name": "NC", "default": "0000000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "5", "name": "CLKout7_SRC_MUX", "default": "0", "widget_name": "CLKout7_SRCMUX", "widget_type": "checkbox", "options": null, "description": "Select CLKoutX clock source. Source must also be powered up. 0: <PERSON>ce Clock; 1: SYSREF"}, {"bit": "4", "name": "SCLK6_7_PD", "default": "1", "widget_name": "SCLK6_7PD", "widget_type": "checkbox", "options": null, "description": "Power down the SYSREF clock output circuitry. 0: SYSREF enabled; 1: Power down SYSREF path for clock pair."}, {"bit": "3:2", "name": "SCLK6_7_DIS_MODE[1:0]", "default": "00", "widget_name": null, "widget_type": null, "options": null, "description": "Set disable mode for clock outputs controlled by SYSREF. Some cases will assert when SYSREF_GBL_PD = 1."}, {"bit": "1", "name": "SCLK6_7_POL", "default": "0", "widget_name": "SCLK6_7POL", "widget_type": "checkbox", "options": null, "description": "Sets the polarity of clock on SCLKX_Y when SYSREF clock output is selected with CLKoutX_MUX or CLKoutY_MUX.\n0: Normal; 1: Inverted"}, {"bit": "0", "name": "SCLK6_7_HS", "default": "0", "widget_name": "SCLK6_7HSTrig", "widget_type": "checkbox", "options": null, "description": "Sets the local SYSREF clock half step value.\n0: No phase adjustment; 1: Adjust device SYSREF phase -0.5 clock distribution path cycles."}]}, "0x2D": {"bits": [{"bit": "15:9", "name": "NC", "default": "0000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "8", "name": "SCLK6_7_ADLY_Enb", "default": "0", "widget_name": "SCLK6_7ADLYEnb", "widget_type": "checkbox", "options": null, "description": "Enables analog delay for the SYSREF output. 1: Disabled; 0: Enabled"}, {"bit": "7:0", "name": "SCLK6_7_ADLY[7:0]", "default": "00000000", "widget_name": "SCLK6_7ADLY", "widget_type": "spinbox", "options": "0:255", "description": "SYSREF analog delay in approximately 10ps steps. Selecting analog delay adds an additional 125 ps in propagation delay. Range is 125 ps to 2675 ps."}]}, "0x2E": {"bits": [{"bit": "15:4", "name": "NC", "default": "000000000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "3:0", "name": "SCLK6_7_DDLY[3:0]", "default": "0001", "widget_name": "SCLK6_7DDLY", "widget_type": "combobox", "options": "0:10", "description": "Set the number of VCO cycles to delay SCLKout by"}]}, "0x2F": {"bits": [{"bit": "15:8", "name": "NC", "default": "00000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "7:4", "name": "CLKout7_FMT[3:0]", "default": "0011", "widget_name": "CLKout7FMT", "widget_type": "combobox", "options": "0:15", "description": "Set CLKoutX/Y clock format"}, {"bit": "3:0", "name": "CLKout6_FMT[3:0]", "default": "0011", "widget_name": "CLKout6FMT", "widget_type": "combobox", "options": "0:15", "description": "Set CLKoutX/Y clock format"}]}, "0x30": {"bits": [{"bit": "15:10", "name": "NC", "default": "000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "9:0", "name": "DCLK8_9_DIV[9:0]", "default": "0000001000", "widget_name": "DCLK8_9DIV", "widget_type": "spinbox", "options": "1:1023", "description": "DCLKX_Y_DIV sets the divide value for the clock output, the divide may be even or odd. Both even or odd divides output a 50% duty cycle clock."}]}, "0x31": {"bits": [{"bit": "15:10", "name": "NC", "default": "000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "9:0", "name": "DCLK8_9_DDLY[9:0]", "default": "0000001010", "widget_name": "DCLK8_9DDLY", "widget_type": "spinbox", "options": "2:1023", "description": "Static digital delay which takes effect after a SYNC."}]}, "0x32": {"bits": [{"bit": "15:8", "name": "NC", "default": "00000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "7", "name": "CLKout8_9_PD", "default": "1", "widget_name": "CLKout8_9PD", "widget_type": "checkbox", "options": null, "description": "Power down the clock group defined by X and Y.\n0: Enabled; 1: Power down entire clock group including both CLKoutX and CLKoutY."}, {"bit": "6:5", "name": "NC", "default": "00", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "4", "name": "DCLK8_9_DDLY_PD", "default": "0", "widget_name": "DCLK8_9DDLYPD", "widget_type": "checkbox", "options": null, "description": "Powerdown the device clock digital delay circuitry. \n0: Enabled; 1: Power down static digital delay for device clock divider."}, {"bit": "3:0", "name": "NC", "default": "0000", "widget_name": null, "widget_type": null, "options": null, "description": null}]}, "0x33": {"bits": [{"bit": "15:6", "name": "NC", "default": "0000000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "5", "name": "CLKout8_SRC_MUX", "default": "0", "widget_name": "CLKout8_SRCMUX", "widget_type": "checkbox", "options": null, "description": "Select CLKoutX clock source. Source must also be powered up. 0: <PERSON>ce Clock; 1: SYSREF"}, {"bit": "4", "name": "DCLK8_9_PD", "default": "0", "widget_name": "DCLK8_9PD", "widget_type": "checkbox", "options": null, "description": "Power down the clock group defined by X and Y. 0: Enabled; 1: Power down enter clock group X_Y."}, {"bit": "3", "name": "DCLK8_9_BYP", "default": "0", "widget_name": "DCLK8_9BYPASS", "widget_type": "checkbox", "options": null, "description": "Enable high performance bypass path for even clock outputs.\n0: CLKoutX not in high performance bypass mode. CML is not valid for CLKoutX_FMT.\n1: CLKoutX in high performance bypass mode. Only CML clock format is valid."}, {"bit": "2", "name": "NC", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "1", "name": "DCLK8_9_POL", "default": "0", "widget_name": "DCLK8_9POL", "widget_type": "checkbox", "options": null, "description": "Invert polarity of device clock output. This also applies to CLKoutX in high performance bypass mode. Polarity invert is a method to get a half-step phase adjustment in high performance bypass mode or /1 divide value.\n0: Normal polarity; 1: Invert polarity"}, {"bit": "0", "name": "DCLK8_9_HS", "default": "0", "widget_name": "DCLK8_9HSTrig", "widget_type": "checkbox", "options": null, "description": "Sets the device clock half step value. Must be set to zero (0) for a divide of 1. \n0: No phase adjustment; 1: Adjust device clock phase -0.5 clock distribution path cycles."}]}, "0x34": {"bits": [{"bit": "15:6", "name": "NC", "default": "0000000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "5", "name": "CLKout9_SRC_MUX", "default": "0", "widget_name": "CLKout9_SRCMUX", "widget_type": "checkbox", "options": null, "description": "Select CLKoutX clock source. Source must also be powered up. 0: <PERSON>ce Clock; 1: SYSREF"}, {"bit": "4", "name": "SCLK8_9_PD", "default": "1", "widget_name": "SCLK8_9PD", "widget_type": "checkbox", "options": null, "description": "Power down the SYSREF clock output circuitry. 0: SYSREF enabled; 1: Power down SYSREF path for clock pair."}, {"bit": "3:2", "name": "SCLK8_9_DIS_MODE[1:0]", "default": "00", "widget_name": null, "widget_type": null, "options": null, "description": "Set disable mode for clock outputs controlled by SYSREF. Some cases will assert when SYSREF_GBL_PD = 1."}, {"bit": "1", "name": "SCLK8_9_POL", "default": "0", "widget_name": "SCLK8_9POL", "widget_type": "checkbox", "options": null, "description": "Sets the polarity of clock on SCLKX_Y when SYSREF clock output is selected with CLKoutX_MUX or CLKoutY_MUX.\n0: Normal; 1: Inverted"}, {"bit": "0", "name": "SCLK8_9_HS", "default": "0", "widget_name": "SCLK8_9HSTrig", "widget_type": "checkbox", "options": null, "description": "Sets the local SYSREF clock half step value.\n0: No phase adjustment; 1: Adjust device SYSREF phase -0.5 clock distribution path cycles."}]}, "0x35": {"bits": [{"bit": "15:9", "name": "NC", "default": "0000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "8", "name": "SCLK8_9_ADLY_Enb", "default": "0", "widget_name": "SCLK8_9ADLYEnb", "widget_type": "checkbox", "options": null, "description": "Enables analog delay for the SYSREF output. 1: Disabled; 0: Enabled"}, {"bit": "7:0", "name": "SCLK8_9_ADLY[7:0]", "default": "00000000", "widget_name": "SCLK8_9ADLY", "widget_type": "spinbox", "options": "0:255", "description": "SYSREF analog delay in approximately 10ps steps. Selecting analog delay adds an additional 125 ps in propagation delay. Range is 125 ps to 2675 ps."}]}, "0x36": {"bits": [{"bit": "15:4", "name": "NC", "default": "000000000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "3:0", "name": "SCLK8_9_DDLY[3:0]", "default": "0001", "widget_name": "SCLK8_9DDLY", "widget_type": "combobox", "options": "0:10", "description": "Set the number of VCO cycles to delay SCLKout by"}]}, "0x37": {"bits": [{"bit": "15:8", "name": "NC", "default": "00000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "7:4", "name": "CLKout9_FMT[3:0]", "default": "0000", "widget_name": "CLKout9FMT", "widget_type": "combobox", "options": "0:15", "description": "Set CLKoutX/Y clock format"}, {"bit": "3:0", "name": "CLKout8_FMT[3:0]", "default": "0000", "widget_name": "CLKout8FMT", "widget_type": "combobox", "options": "0:15", "description": "Set CLKoutX/Y clock format"}]}, "0x38": {"bits": [{"bit": "15:10", "name": "NC", "default": "000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "9:0", "name": "DCLK10_11_DIV[9:0]", "default": "0000001000", "widget_name": "DCLK10_11DIV", "widget_type": "spinbox", "options": "1:1023", "description": "DCLKX_Y_DIV sets the divide value for the clock output, the divide may be even or odd. Both even or odd divides output a 50% duty cycle clock."}]}, "0x39": {"bits": [{"bit": "15:10", "name": "NC", "default": "000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "9:0", "name": "DCLK10_11_DDLY[9:0]", "default": "0000001010", "widget_name": "DCLK10_11DDLY", "widget_type": "spinbox", "options": "2:1023", "description": "Static digital delay which takes effect after a SYNC."}]}, "0x3A": {"bits": [{"bit": "15:8", "name": "NC", "default": "00000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "7", "name": "CLKout10_11_PD", "default": "1", "widget_name": "CLKout10_11PD", "widget_type": "checkbox", "options": null, "description": "Power down the clock group defined by X and Y.\n0: Enabled; 1: Power down entire clock group including both CLKoutX and CLKoutY."}, {"bit": "6:5", "name": "NC", "default": "00", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "4", "name": "DCLK10_11_DDLY_PD", "default": "0", "widget_name": "DCLK10_11DDLYPD", "widget_type": "checkbox", "options": null, "description": "Powerdown the device clock digital delay circuitry. \n0: Enabled; 1: Power down static digital delay for device clock divider."}, {"bit": "3:0", "name": "NC", "default": "0000", "widget_name": null, "widget_type": null, "options": null, "description": null}]}, "0x3B": {"bits": [{"bit": "15:6", "name": "NC", "default": "0000000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "5", "name": "CLKout10_SRC_MUX", "default": "0", "widget_name": "CLKout10_SRCMUX", "widget_type": "checkbox", "options": null, "description": "Select CLKoutX clock source. Source must also be powered up. 0: <PERSON>ce Clock; 1: SYSREF"}, {"bit": "4", "name": "DCLK10_11_PD", "default": "0", "widget_name": "DCLK10_11PD", "widget_type": "checkbox", "options": null, "description": "Power down the clock group defined by X and Y. 0: Enabled; 1: Power down enter clock group X_Y."}, {"bit": "3", "name": "DCLK10_11_BYP", "default": "0", "widget_name": "DCLK10_11BYPASS", "widget_type": "checkbox", "options": null, "description": "Enable high performance bypass path for even clock outputs.\n0: CLKoutX not in high performance bypass mode. CML is not valid for CLKoutX_FMT.\n1: CLKoutX in high performance bypass mode. Only CML clock format is valid."}, {"bit": "2", "name": "NC", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "1", "name": "DCLK10_11_POL", "default": "0", "widget_name": "DCLK10_11POL", "widget_type": "checkbox", "options": null, "description": "Invert polarity of device clock output. This also applies to CLKoutX in high performance bypass mode. Polarity invert is a method to get a half-step phase adjustment in high performance bypass mode or /1 divide value.\n0: Normal polarity; 1: Invert polarity"}, {"bit": "0", "name": "DCLK10_11_HS", "default": "0", "widget_name": "DCLK10_11HSTrig", "widget_type": "checkbox", "options": null, "description": "Sets the device clock half step value. Must be set to zero (0) for a divide of 1. \n0: No phase adjustment; 1: Adjust device clock phase -0.5 clock distribution path cycles."}]}, "0x3C": {"bits": [{"bit": "15:6", "name": "NC", "default": "0000000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "5", "name": "CLKout11_SRC_MUX", "default": "0", "widget_name": "CLKout11_SRCMUX", "widget_type": "checkbox", "options": null, "description": "Select CLKoutX clock source. Source must also be powered up. 0: <PERSON>ce Clock; 1: SYSREF"}, {"bit": "4", "name": "SCLK10_11_PD", "default": "1", "widget_name": "SCLK10_11PD", "widget_type": "checkbox", "options": null, "description": "Power down the SYSREF clock output circuitry. 0: SYSREF enabled; 1: Power down SYSREF path for clock pair."}, {"bit": "3:2", "name": "SCLK10_11_DIS_MODE[1:0]", "default": "00", "widget_name": null, "widget_type": null, "options": null, "description": "Set disable mode for clock outputs controlled by SYSREF. Some cases will assert when SYSREF_GBL_PD = 1."}, {"bit": "1", "name": "SCLK10_11_POL", "default": "0", "widget_name": "SCLK10_11POL", "widget_type": "checkbox", "options": null, "description": "Sets the polarity of clock on SCLKX_Y when SYSREF clock output is selected with CLKoutX_MUX or CLKoutY_MUX.\n0: Normal; 1: Inverted"}, {"bit": "0", "name": "SCLK10_11_HS", "default": "0", "widget_name": "SCLK10_11HSTrig", "widget_type": "checkbox", "options": null, "description": "Sets the local SYSREF clock half step value.\n0: No phase adjustment; 1: Adjust device SYSREF phase -0.5 clock distribution path cycles."}]}, "0x3D": {"bits": [{"bit": "15:9", "name": "NC", "default": "0000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "8", "name": "SCLK10_11_ADLY_Enb", "default": "0", "widget_name": "SCLK10_11ADLYEnb", "widget_type": "checkbox", "options": null, "description": "Enables analog delay for the SYSREF output. 1: Disabled; 0: Enabled"}, {"bit": "7:0", "name": "SCLK10_11_ADLY[7:0]", "default": "00000000", "widget_name": "SCLK10_11ADLY", "widget_type": "spinbox", "options": "0:255", "description": "SYSREF analog delay in approximately 10ps steps. Selecting analog delay adds an additional 125 ps in propagation delay. Range is 125 ps to 2675 ps."}]}, "0x3E": {"bits": [{"bit": "15:4", "name": "NC", "default": "000000000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "3:0", "name": "SCLK10_11_DDLY[3:0]", "default": "0001", "widget_name": "SCLK10_11DDLY", "widget_type": "combobox", "options": "0:10", "description": "Set the number of VCO cycles to delay SCLKout by"}]}, "0x3F": {"bits": [{"bit": "15:8", "name": "NC", "default": "00000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "7:4", "name": "CLKout11_FMT[3:0]", "default": "0000", "widget_name": "CLKout11FMT", "widget_type": "combobox", "options": "0:15", "description": "Set CLKoutX/Y clock format"}, {"bit": "3:0", "name": "CLKout10_FMT[3:0]", "default": "0000", "widget_name": "CLKout10FMT", "widget_type": "combobox", "options": "0:15", "description": "Set CLKoutX/Y clock format"}]}, "0x40": {"bits": [{"bit": "15:10", "name": "NC", "default": "000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "9:0", "name": "DCLK12_13_DIV[9:0]", "default": "0000000010", "widget_name": "DCLK12_13DIV", "widget_type": "spinbox", "options": "1:1023", "description": "DCLKX_Y_DIV sets the divide value for the clock output, the divide may be even or odd. Both even or odd divides output a 50% duty cycle clock."}]}, "0x41": {"bits": [{"bit": "15:10", "name": "NC", "default": "000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "9:0", "name": "DCLK12_13_DDLY[9:0]", "default": "0000001010", "widget_name": "DCLK12_13DDLY", "widget_type": "spinbox", "options": "2:1023", "description": "Static digital delay which takes effect after a SYNC."}]}, "0x42": {"bits": [{"bit": "15:8", "name": "NC", "default": "00000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "7", "name": "CLKout12_13_PD", "default": "0", "widget_name": "CLKout12_13PD", "widget_type": "checkbox", "options": null, "description": "Power down the clock group defined by X and Y.\n0: Enabled; 1: Power down entire clock group including both CLKoutX and CLKoutY."}, {"bit": "6:5", "name": "NC", "default": "00", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "4", "name": "DCLK12_13_DDLY_PD", "default": "0", "widget_name": "DCLK12_13DDLYPD", "widget_type": "checkbox", "options": null, "description": "Powerdown the device clock digital delay circuitry. \n0: Enabled; 1: Power down static digital delay for device clock divider."}, {"bit": "3:0", "name": "NC", "default": "0000", "widget_name": null, "widget_type": null, "options": null, "description": null}]}, "0x43": {"bits": [{"bit": "15:6", "name": "NC", "default": "0000000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "5", "name": "CLKout12_SRC_MUX", "default": "0", "widget_name": "CLKout12_SRCMUX", "widget_type": "checkbox", "options": null, "description": "Select CLKoutX clock source. Source must also be powered up. 0: <PERSON>ce Clock; 1: SYSREF"}, {"bit": "4", "name": "DCLK12_13_PD", "default": "0", "widget_name": "DCLK12_13PD", "widget_type": "checkbox", "options": null, "description": "Power down the clock group defined by X and Y. 0: Enabled; 1: Power down enter clock group X_Y."}, {"bit": "3", "name": "DCLK12_13_BYP", "default": "0", "widget_name": "DCLK12_13BYPASS", "widget_type": "checkbox", "options": null, "description": "Enable high performance bypass path for even clock outputs.\n0: CLKoutX not in high performance bypass mode. CML is not valid for CLKoutX_FMT.\n1: CLKoutX in high performance bypass mode. Only CML clock format is valid."}, {"bit": "2", "name": "NC", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "1", "name": "DCLK12_13_POL", "default": "0", "widget_name": "DCLK12_13POL", "widget_type": "checkbox", "options": null, "description": "Invert polarity of device clock output. This also applies to CLKoutX in high performance bypass mode. Polarity invert is a method to get a half-step phase adjustment in high performance bypass mode or /1 divide value.\n0: Normal polarity; 1: Invert polarity"}, {"bit": "0", "name": "DCLK12_13_HS", "default": "0", "widget_name": "DCLK12_13HSTrig", "widget_type": "checkbox", "options": null, "description": "Sets the device clock half step value. Must be set to zero (0) for a divide of 1. \n0: No phase adjustment; 1: Adjust device clock phase -0.5 clock distribution path cycles."}]}, "0x44": {"bits": [{"bit": "15:6", "name": "NC", "default": "0000000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "5", "name": "CLKout13_SRC_MUX", "default": "0", "widget_name": "CLKout13_SRCMUX", "widget_type": "checkbox", "options": null, "description": "Select CLKoutX clock source. Source must also be powered up. 0: <PERSON>ce Clock; 1: SYSREF"}, {"bit": "4", "name": "SCLK12_13_PD", "default": "1", "widget_name": "SCLK12_13PD", "widget_type": "checkbox", "options": null, "description": "Power down the SYSREF clock output circuitry. 0: SYSREF enabled; 1: Power down SYSREF path for clock pair."}, {"bit": "3:2", "name": "SCLK12_13_DIS_MODE[1:0]", "default": "00", "widget_name": null, "widget_type": null, "options": null, "description": "Set disable mode for clock outputs controlled by SYSREF. Some cases will assert when SYSREF_GBL_PD = 1."}, {"bit": "1", "name": "SCLK12_13_POL", "default": "0", "widget_name": "SCLK12_13POL", "widget_type": "checkbox", "options": null, "description": "Sets the polarity of clock on SCLKX_Y when SYSREF clock output is selected with CLKoutX_MUX or CLKoutY_MUX.\n0: Normal; 1: Inverted"}, {"bit": "0", "name": "SCLK12_13_HS", "default": "0", "widget_name": "SCLK12_13HSTrig", "widget_type": "checkbox", "options": null, "description": "Sets the local SYSREF clock half step value.\n0: No phase adjustment; 1: Adjust device SYSREF phase -0.5 clock distribution path cycles."}]}, "0x45": {"bits": [{"bit": "15:9", "name": "NC", "default": "0000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "8", "name": "SCLK12_13_ADLY_Enb", "default": "0", "widget_name": "SCLK12_13ADLYEnb", "widget_type": "checkbox", "options": null, "description": "Enables analog delay for the SYSREF output. 1: Disabled; 0: Enabled"}, {"bit": "7:0", "name": "SCLK12_13_ADLY[7:0]", "default": "00000000", "widget_name": "SCLK12_13ADLY", "widget_type": "spinbox", "options": "0:255", "description": "SYSREF analog delay in approximately 10ps steps. Selecting analog delay adds an additional 125 ps in propagation delay. Range is 125 ps to 2675 ps."}]}, "0x46": {"bits": [{"bit": "15:4", "name": "NC", "default": "000000000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "3:0", "name": "SCLK12_13_DDLY[3:0]", "default": "0001", "widget_name": "SCLK12_13DDLY", "widget_type": "combobox", "options": "0:10", "description": "Set the number of VCO cycles to delay SCLKout by"}]}, "0x47": {"bits": [{"bit": "15:8", "name": "NC", "default": "00000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "7:4", "name": "CLKout13_FMT[3:0]", "default": "0011", "widget_name": "CLKout13FMT", "widget_type": "combobox", "options": "0:15", "description": "Set CLKoutX/Y clock format"}, {"bit": "3:0", "name": "CLKout12_FMT[3:0]", "default": "0011", "widget_name": "CLKout12FMT", "widget_type": "combobox", "options": "0:15", "description": "Set CLKoutX/Y clock format"}]}, "0x48": {"bits": [{"bit": "15:7", "name": "NC", "default": "000000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "6:5", "name": "VCO_MUX[1:0]", "default": "01", "widget_name": "comboVcoMode", "widget_type": "combobox", "options": "0:3", "description": "Selects clock distribution path source from VCO0, VCO1, or CLKin (external VCO)"}, {"bit": "4", "name": "OSCout_MUX", "default": "0", "widget_name": "OSCoutMux", "widget_type": "combobox", "options": "0:1", "description": "Select the source for OSCout: 0: Buffered OSCin; 1: Feedback Mux"}, {"bit": "3:0", "name": "OSCout_FMT[3:0]", "default": "0101", "widget_name": "OSCoutClockFormat", "widget_type": "combobox", "options": "0:14", "description": "Selects the output format of OSCout. When powered down, these pins may beused as CLKin2."}]}, "0x49": {"bits": [{"bit": "15:5", "name": "NC", "default": "00000000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "4", "name": "SYSREF_REQ_EN", "default": "0", "widget_name": "SysrefReqEn", "widget_type": "checkbox", "options": null, "description": "Enables the SYNC/SYSREF_REQ pin to force the SYSREF_MUX = 3 for continuous pulses. When using this feature enable pulser and set SYSREF_MUX = 2 (Pulser)."}, {"bit": "3", "name": "SYNC_BYPASS", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "Bypass SYNC polarity invert and other circuitry. 0: Normal; 1: SYNC signal is bypassed"}, {"bit": "2", "name": "NC", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "1:0", "name": "SYSREF_MUX[1:0]", "default": "00", "widget_name": "comboSysrefMux", "widget_type": "combobox", "options": "0:3", "description": "Selects the SYSREF source."}]}, "0x4A": {"bits": [{"bit": "15:13", "name": "NC", "default": "000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": " 12:0", "name": "SYSREF_DIV[12:0]", "default": "0110000000000", "widget_name": "spinBoxSysrefDIV", "widget_type": "spinbox", "options": "0:8191", "description": "Divide value for the SYSREF outputs."}]}, "0x4C": {"bits": [{"bit": "15:13", "name": "NC", "default": "000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": " 12:0", "name": "SYSREF_DDLY[12:0]", "default": "0000000001000", "widget_name": "spinBoxSysrefDDLY", "widget_type": "spinbox", "options": "2:8191", "description": "Sets the value of the SYSREF digital delay."}]}, "0x4E": {"bits": [{"bit": "15:8", "name": "NC", "default": "00000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "7", "name": "RG_SYSREF_PULSE_TRIG", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "write 1'b1 automatically cleared to 1'b0 at next spi_clk, default 1'b0"}, {"bit": " 6", "name": "RG_SYSREF_PULSE_TRIG_AUTO_EN", "default": "1", "widget_name": null, "widget_type": null, "options": null, "description": "automatically trig when write 0x4E register when this bit is 1'b1"}, {"bit": " 5:2", "name": "NC", "default": "0000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "1:0", "name": "SYSREF_PULSE_CNT[1:0]", "default": "11", "widget_name": "comboSysrefPulseCnt", "widget_type": "combobox", "options": "0:3", "description": "Sets the number of SYSREF pulses generated when not in continuous mode. See SYSREF_REQ_EN, SYNC_BYPASS, SYSREF_MUX for more information on SYSREF modes."}]}, "0x4F": {"bits": [{"bit": "15:8", "name": "NC", "default": "00000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "7", "name": "PLL2_RCLK_MUX", "default": "0", "widget_name": "PLL2RclkMux", "widget_type": "combobox", "options": "0:1", "description": "Selects the source for PLL2 reference. 0: OSCin; 1: Currently selected CLKin."}, {"bit": "6", "name": "NC", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "5", "name": "PLL2_NCLK_MUX", "default": "0", "widget_name": "PLL2NclkMux", "widget_type": "combobox", "options": "0:1", "description": "Selects the input to the PLL2 N Divider. 0: PLL2 Prescaler; 1: Feedback Mux"}, {"bit": "4:3", "name": "PLL1_NCLK_MUX[1:0]", "default": "00", "widget_name": "PLL1NclkMux", "widget_type": "combobox", "options": "0:2", "description": "Selects the input to the PLL1 N Divider. 0: OSCin; 1: Feedback Mux; 2: PLL2 Prescaler"}, {"bit": "2:1", "name": "FB_MUX[1:0]", "default": "00", "widget_name": "FBMUX", "widget_type": "combobox", "options": "0:3", "description": "When in 0-delay mode, the feedback mux selects the clock output to be fed back into the PLL1 N Divider."}, {"bit": "0", "name": "FB_MUX_EN", "default": "0", "widget_name": "FBMuxEn", "widget_type": "checkbox", "options": null, "description": "When using 0-delay, FB_MUX_EN must be set to 1 power up the feedback mux.\n0: Feedback mux powered down; 1: Feedback mux enabled"}]}, "0x50": {"bits": [{"bit": "15:8", "name": "NC", "default": "00000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "7", "name": "PLL1_PD", "default": "0", "widget_name": "PLL1PD", "widget_type": "checkbox", "options": null, "description": "Power down PLL1. 0: Normal operation; 1: Power down"}, {"bit": "6", "name": "VCO_LDO_PD", "default": "0", "widget_name": "VCOLdoPD", "widget_type": "checkbox", "options": null, "description": "Power down VCO_LDO. 0: Normal operation; 1: Power down"}, {"bit": "5", "name": "VCO_PD", "default": "0", "widget_name": "VCOPD", "widget_type": "checkbox", "options": null, "description": "Power down VCO. 0: Normal operation; 1: Power down"}, {"bit": "4", "name": "OSCin_PD", "default": "0", "widget_name": "OSCinPD", "widget_type": "checkbox", "options": null, "description": "Power down the OSCin port. 0: Normal operation; 1: Power down"}, {"bit": "3", "name": "SYSREF_GBL_PD", "default": "1", "widget_name": "SysrefGBLPD", "widget_type": "checkbox", "options": null, "description": "Power down individual SYSREF outputs depending on the setting of SCLKX_Y_DIS_MODE for each SYSREF output. SYSREF_GBL_PD allows many SYSREF outputs to be controlled through a single bit.\n0: Normal operation; 1: Activate Power down Mode"}, {"bit": "2", "name": "SYSREF_PD", "default": "1", "widget_name": "sysrefPD", "widget_type": "checkbox", "options": null, "description": "Power down the SYSREF circuitry and divider. If powered down, SYSREF output mode cannot be used. SYNC cannot be provided either. 0: SYSREF can be used as programmed by individual SYSREF output registers. 1: Power down"}, {"bit": "1", "name": "SYSREF_DDLY_PD", "default": "1", "widget_name": "sysrefDDLYPD", "widget_type": "checkbox", "options": null, "description": "Power down the SYSREF digital delay circuitry. 0: Normal operation, SYSREF digital delay may be used. Must be powered up during SYNC for deterministic phase relationship with other clocks. 1: Power down"}, {"bit": "0", "name": "SYSREF_PLSR_PD", "default": "1", "widget_name": "SysrefPlsrPd", "widget_type": "checkbox", "options": null, "description": "Powerdown the SYSREF pulse generator. 0: Normal operation; 1: Powerdown"}]}, "0x51": {"bits": [{"bit": "15:8", "name": "NC", "default": "00000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "7", "name": "DDLYd_SYSREF_EN", "default": "0", "widget_name": "sysrefDDLYdEn", "widget_type": "checkbox", "options": null, "description": "Enables dynamic digital delay on SYSREF outputs"}, {"bit": "6", "name": "DDLYd12_EN", "default": "0", "widget_name": "DDLYd12EN", "widget_type": "checkbox", "options": null, "description": "Enables dynamic digital delay on DCLKout12"}, {"bit": "5", "name": "DDLYd10_EN", "default": "0", "widget_name": "DDLYd10EN", "widget_type": "checkbox", "options": null, "description": "Enables dynamic digital delay on DCLKout10"}, {"bit": "4", "name": "DDLYd8_EN", "default": "0", "widget_name": "DDLYd8EN", "widget_type": "checkbox", "options": null, "description": "Enables dynamic digital delay on DCLKout8"}, {"bit": "3", "name": "DDLYd6_EN", "default": "0", "widget_name": "DDLYd6EN", "widget_type": "checkbox", "options": null, "description": "Enables dynamic digital delay on DCLKout6"}, {"bit": "2", "name": "DDLYd4_EN", "default": "0", "widget_name": "DDLYd4EN", "widget_type": "checkbox", "options": null, "description": "Enables dynamic digital delay on DCLKout4"}, {"bit": "1", "name": "DDLYd2_EN", "default": "0", "widget_name": "DDLYd2EN", "widget_type": "checkbox", "options": null, "description": "Enables dynamic digital delay on DCLKout2"}, {"bit": "0", "name": "DDLYd0_EN", "default": "0", "widget_name": "DDLYd0EN", "widget_type": "checkbox", "options": null, "description": "Enables dynamic digital delay on DCLKout0"}]}, "0x52": {"bits": [{"bit": "15:12", "name": "NC", "default": "0000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "11:8", "name": "RG_DDLYd_STEP_SYSREF[3:0]", "default": "0001", "widget_name": "comboDDLYdSysrefStep", "widget_type": "combobox", "options": "0:15", "description": "cotrol SYSREF CLK dynamic delay step, total dynamic delay=CNT[7:0]xSTEP_SYSREF[3:0]"}, {"bit": "7:0", "name": "DDLYd_STEP_CNT[7:0]", "default": "00000000", "widget_name": "DDLYdStepCNT_1", "widget_type": "spinbox", "options": "0:255", "description": "Sets the number of dynamic digital delay adjustments that will occur."}]}, "0x53": {"bits": [{"bit": "15:9", "name": "NC", "default": "0000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "8", "name": "RG_SYNC_DLD_INV", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "invert PLL1_DLD and PLL2_DLD for SYNC(controlled by SYNC_PLL1_DLD and SYNC_PLL2_DLD) based on LMK04832"}, {"bit": "7", "name": "SYSREF_CLR", "default": "0", "widget_name": "SysrefCLR", "widget_type": "checkbox", "options": null, "description": "Except during SYSREF Setup Procedure (see SYNC/SYSREF), this bit should always be programmed to 0. While this bit is set, extra current is used."}, {"bit": "6", "name": "SYNC_1SHOT_EN", "default": "0", "widget_name": "Sync1SHOTEn", "widget_type": "checkbox", "options": null, "description": "SYNC one shot enables edge sensitive SYNC.\n0: SYNC is level sensitive and outputs will be held in SYNC as long as SYNC  is asserted.\n1: SYNC is edge sensitive, outputs will be SYNCed on rising edge of SYNC. This results in the clock being held in SYNC for a minimum amount of time."}, {"bit": "5", "name": "SYNC_POL", "default": "0", "widget_name": "SyncPOL", "widget_type": "checkbox", "options": null, "description": "Sets the polarity of the SYNC pin. 0: Normal; 1: Inverted"}, {"bit": "4", "name": "SYNC_EN", "default": "1", "widget_name": "SyncEn", "widget_type": "checkbox", "options": null, "description": "Enables the SYNC functionality. 0: Disabled; 1: Enabled"}, {"bit": "3", "name": "SYNC_PLL2_DLD", "default": "0", "widget_name": "SyncPLL2DLD", "widget_type": "checkbox", "options": null, "description": "0: Off; 1: Assert SYNC until PLL2 DLD = 1"}, {"bit": "2", "name": "SYNC_PLL1_DLD", "default": "0", "widget_name": "SyncPLL1DLD", "widget_type": "checkbox", "options": null, "description": "0: Off; 1: Assert SYNC until PLL1 DLD = 1"}, {"bit": "1:0", "name": "SYNC_MODE[1:0]", "default": "01", "widget_name": "comboSyncMode", "widget_type": "combobox", "options": "0:3", "description": "Sets the method of generating a SYNC event."}]}, "0x54": {"bits": [{"bit": "15:8", "name": "NC", "default": "00000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "7", "name": "SYNC_DISSYSREF", "default": "0", "widget_name": "sysrefDissysref", "widget_type": "checkbox", "options": null, "description": "Prevent the SYSREF clocks from becoming synchronized during a SYNC event. If SYNC_DISSYSREF is enabled it will continue to operate normally during a SYNC event."}, {"bit": "6", "name": "SYNC_DIS12", "default": "0", "widget_name": "SYNCDIS12", "widget_type": "checkbox", "options": null, "description": "Prevent the device clock output from becoming synchronized during a SYNC event or SYSREF clock. If SYNC_DIS bit for a particular output is enabled then it will continue to operate normally during a SYNC event or SYSREF clock."}, {"bit": "5", "name": "SYNC_DIS10", "default": "0", "widget_name": "SYNCDIS10", "widget_type": "checkbox", "options": null, "description": null}, {"bit": "4", "name": "SYNC_DIS8", "default": "0", "widget_name": "SYNCDIS8", "widget_type": "checkbox", "options": null, "description": null}, {"bit": "3", "name": "SYNC_DIS6", "default": "0", "widget_name": "SYNCDIS6", "widget_type": "checkbox", "options": null, "description": null}, {"bit": "2", "name": "SYNC_DIS4", "default": "0", "widget_name": "SYNCDIS4", "widget_type": "checkbox", "options": null, "description": null}, {"bit": "1", "name": "SYNC_DIS2", "default": "0", "widget_name": "SYNCDIS2", "widget_type": "checkbox", "options": null, "description": null}, {"bit": "0", "name": "SYNC_DIS0", "default": "0", "widget_name": "SYNCDIS0", "widget_type": "checkbox", "options": null, "description": null}]}, "0x55": {"bits": [{"bit": "15:7", "name": "NC", "default": "000000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "6", "name": "PLL1R_SYNC_EN", "default": "0", "widget_name": "PLL1RSyncEn", "widget_type": "checkbox", "options": null, "description": "Enable synchronization for PLL1 R divider. 0: Not enabled; 1: Enabled"}, {"bit": "5:4", "name": "PLL1R_SYNC_SRC[1:0]", "default": "00", "widget_name": "syncSource", "widget_type": "combobox", "options": "0:3", "description": "Select the source for PLL1 R divider synchronization"}, {"bit": "3", "name": "PLL2R_SYNC_EN", "default": "0", "widget_name": "PLL2SyncEn", "widget_type": "checkbox", "options": null, "description": "Enable synchronization for PLL2 R divider. Synchronization for PLL2 R always comes from the SYNC pin.\n0: Not enabled; 1: Enabled"}, {"bit": "2", "name": "FIN0_DIV2_EN", "default": "0", "widget_name": "Div2", "widget_type": "checkbox", "options": null, "description": "Sets the input path to use or bypass  the divide-by-2; 0: Bypassed(/1); 1: Divided(/2)"}, {"bit": "1:0", "name": "FIN0_INPUT_TYPE[1:0]", "default": "00", "widget_name": "Fin0InputType", "widget_type": "combobox", "options": "0:3", "description": "Program input type to hardware interface used."}]}, "0x56": {"bits": [{"bit": "15:8", "name": "NC", "default": "00000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "7", "name": "CL<PERSON>in_SEL_PIN_EN", "default": "0", "widget_name": "clkinSelPinEn", "widget_type": "checkbox", "options": null, "description": "Enables pin control according to Figure 12."}, {"bit": "6", "name": "CL<PERSON>in_SEL_PIN_POL", "default": "0", "widget_name": "clkinSelPinPol", "widget_type": "checkbox", "options": null, "description": "Inverts the CLKin polarity for use in pin select mode. 0: Active High; 1: Active Low"}, {"bit": "5", "name": "CLKin2_EN", "default": "0", "widget_name": "clkin2En", "widget_type": "checkbox", "options": null, "description": "Enable CLKin2 to be used during auto-switching.\n0: Not enabled for auto mode; 1: Enabled for auto clock switching mode"}, {"bit": "4", "name": "CLKin1_EN", "default": "1", "widget_name": "clkin1En", "widget_type": "checkbox", "options": null, "description": "Enable CLKin1 to be used during auto-switching.\n0: Not enabled for auto mode; 1: Enabled for auto clock switching mode"}, {"bit": "3", "name": "CLKin0_EN", "default": "1", "widget_name": "clkin0En", "widget_type": "checkbox", "options": null, "description": "Enable CLKin0 to be used during auto-switching.\n0: Not enabled for auto mode; 1: Enabled for auto clock switching mode"}, {"bit": "2:0", "name": "NC", "default": "000", "widget_name": null, "widget_type": null, "options": null, "description": null}]}, "0x57": {"bits": [{"bit": "15:8", "name": "NC", "default": "00000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "7", "name": "CLKin_SEL_AUTO_REVERT_EN", "default": "0", "widget_name": "clkinSelAutoRevertEn", "widget_type": "checkbox", "options": null, "description": "When in auto clock switching mode. If active clock is detected on higher priority clock, the clock input is immediately switched. Highest priority input is lowest numbered active clock input."}, {"bit": "6", "name": "CLKin_SEL_AUTO_EN", "default": "0", "widget_name": "clkinSelAutoEn", "widget_type": "checkbox", "options": null, "description": "Enables pin control according to Figure 12."}, {"bit": "5:4", "name": "CLKin_SEL_MANUAL[1:0]", "default": "01", "widget_name": "CLKinSelManual", "widget_type": "combobox", "options": "0:3", "description": "Selects the clock input when in manual mode according to Figure 12."}, {"bit": "3:2", "name": "CLKin1_DEMUX[1:0]", "default": "10", "widget_name": "CLKin1Demux", "widget_type": "combobox", "options": "0:3", "description": "Selects where the output of the CLKin1 buffer is directed."}, {"bit": "1:0", "name": "CLKin0_DEMUX[1:0]", "default": "10", "widget_name": "CLKin0Demux", "widget_type": "combobox", "options": "0:3", "description": "Selects where the output of the CLKin0 buffer is directed."}]}, "0x58": {"bits": [{"bit": "15:6", "name": "NC", "default": "0000000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "5:3", "name": "CLKin_SEL0_MUX[2:0]", "default": "000", "widget_name": "CLKinSel0Mux", "widget_type": "combobox", "options": "0:7", "description": "This set the output value of the CLKin_SEL0 pin. This register only applies if CLKin_SEL0_TYPE is set to an output mode"}, {"bit": "2:0", "name": "CLKin_SEL0_TYPE[2:0]", "default": "010", "widget_name": "CLKinSel0Type", "widget_type": "combobox", "options": "0:6", "description": "This sets the IO type of the CLKin_SEL0 pin."}]}, "0x59": {"bits": [{"bit": "15:7", "name": "NC", "default": "000000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "6", "name": "SDIO_RDBK_TYPE（FSJ04832 delete)", "default": "1", "widget_name": "SDIO_RDBK_TYPE", "widget_type": "lineedit", "options": null, "description": "Sets the SDIO pin to open drain when during SPI readback in 3 wire mode. 0: Output, push-pull; 1: Output, open drain."}, {"bit": "5:3", "name": "CLKin_SEL1_MUX[2:0]", "default": "000", "widget_name": "CLKinSel1Mux", "widget_type": "combobox", "options": "0:7", "description": "This set the output value of the CLKin_SEL1 pin. This register only applies if CLKin_SEL1_TYPE is set to an output mode."}, {"bit": "2:0", "name": "CLKin_SEL1_TYPE[2:0]", "default": "010", "widget_name": "CLKinSel1Type", "widget_type": "combobox", "options": "0:6", "description": "This sets the IO type of the CLKin_SEL1 pin."}]}, "0x5A": {"bits": [{"bit": "15:6", "name": "NC", "default": "0000000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "5:3", "name": "RESET_MUX[2:0]", "default": "011", "widget_name": "ResetMux", "widget_type": "combobox", "options": "0:6", "description": "This sets the output value of the RESET pin. This register only applies if RESET_TYPE is set to an output mode."}, {"bit": "2:0", "name": "RESET_TYPE[2:0]", "default": "000", "widget_name": "ResetType", "widget_type": "combobox", "options": "0:6", "description": "This sets the IO type of the RESET pin."}]}, "0x5B": {"bits": [{"bit": "15:9", "name": "NC", "default": "0000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "8:6", "name": "LOS_TIMEOUT[2:0]", "default": "000", "widget_name": "LOSTimeout", "widget_type": "combobox", "options": "0:7", "description": "This controls the amount of time in which no activity on a CLKin forces a clock switch event."}, {"bit": "5", "name": "LOS_EN", "default": "0", "widget_name": "losEn", "widget_type": "checkbox", "options": null, "description": "Enables the LOS (Loss-of-Signal) timeout control. Valid for MOS clock inputs. 0: Disabled; 1: Enabled"}, {"bit": "4", "name": "TRACK_EN", "default": "0", "widget_name": "TrackEn", "widget_type": "checkbox", "options": null, "description": "Enable the DAC to track the PLL1 tuning voltage, optionally for use in holdover mode. After device reset, tracking starts at DAC code = 512. Tracking can be used to monitor PLL1 voltage in any mode.\n0: Disabled; 1: Enabled, will only track when PLL1 is locked."}, {"bit": "3", "name": "HOLDOVER_FORCE", "default": "0", "widget_name": "HOLDOVER_FORCE", "widget_type": "checkbox", "options": null, "description": "This bit forces holdover mode. When holdover mode is forced, if MAN_DAC_EN = 1, then the DAC will set the programmed MAN_DAC value. Otherwise the tracked DAC value will set the DAC voltage.\n0: Disabled; 1: Enabled."}, {"bit": "2", "name": "MAN_DAC_EN", "default": "1", "widget_name": "ManDacEN", "widget_type": "checkbox", "options": null, "description": "This bit enables the manual DAC mode. 0: Automatic; 1: Manual"}, {"bit": "1:0", "name": "NC", "default": "00", "widget_name": null, "widget_type": null, "options": null, "description": null}]}, "0x5C": {"bits": [{"bit": "15:10", "name": "NC", "default": "000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "9:0", "name": "MAN_DAC[9:0]", "default": "1000000000", "widget_name": "MANDAC", "widget_type": "spinbox", "options": "0:1023", "description": "Sets the value of the manual DAC when in manual DAC mode."}]}, "0x5D": {"bits": [{"bit": "15:7", "name": "NC", "default": "000000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "6", "name": "RG_VTUNEDET_RELATIVE_EN", "default": "0", "widget_name": "RGVtunedetRelativeEn", "widget_type": "checkbox", "options": null, "description": "VTUNEDET enter HO relative mode enable"}, {"bit": "5:0", "name": "DAC_TRIP_LOW[5:0]", "default": "000000", "widget_name": "DACLowTrip", "widget_type": "spinbox", "options": "0:63", "description": "Voltage from GND at which holdover is entered if HOLDOVER_VTUNE_DET is enabled."}]}, "0x5E": {"bits": [{"bit": "15:8", "name": "NC", "default": "00000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "7:6", "name": "DAC_CLK_MULT[1:0]", "default": "11", "widget_name": "DACClkMult", "widget_type": "combobox", "options": "0:3", "description": "This is the multiplier for the DAC_CLK_CNTR which sets the rate at which the DAC value is tracked."}, {"bit": "5:0", "name": "DAC_TRIP_HIGH[5:0]", "default": "000000", "widget_name": "DACHighTrip", "widget_type": "spinbox", "options": "0:63", "description": "Voltage from Vcc at which holdover is entered if HOLDOVER_VTUNE_DET is enabled."}]}, "0x5F": {"bits": [{"bit": "15:8", "name": "NC", "default": "00000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "7:0", "name": "DAC_CLK_CNTR[7:0]", "default": "01111111", "widget_name": "DACClkCntr", "widget_type": "combobox", "options": "0:255", "description": "This with DAC_CLK_MULT set the rate at which the DAC is updated. The update rate is = DAC_CLK_MULT * DAC_CLK_CNTR / PLL1 PDF"}]}, "0x60": {"bits": [{"bit": "15:13", "name": "NC", "default": "000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "12", "name": "RG_HO_FAST_ENTER_EN", "default": "0", "widget_name": "RGHoFastEnterEn", "widget_type": "checkbox", "options": null, "description": "HO fast enter use PHE circuit, like HMC7044"}, {"bit": "11:10", "name": "RG_HO_EXIT_DACASSIST_STEP[1:0]", "default": "00", "widget_name": "RGHOExitDacassistStep", "widget_type": "combobox", "options": "0:3", "description": "DAC assist exit HO speed control; 00: the slowest; 11/10: for simulation."}, {"bit": "9", "name": "RG_HO_EXIT_DACASSIST", "default": "0", "widget_name": "RGHOExitDacassist", "widget_type": "checkbox", "options": null, "description": "0 : disable; 1: VTR assist reduce Vtune variation when exit holdover"}, {"bit": "8", "name": "RG_CYCLE_SLIP_EN", "default": "0", "widget_name": "RGCycleslipEn", "widget_type": "checkbox", "options": null, "description": "0: disable Exit Holdover on cycle slip rise edge; 1: enable Exit Holdover on cycle slip rise edge"}, {"bit": "7", "name": "RG_ZPS_EN", "default": "1", "widget_name": "RGZpsEn", "widget_type": "checkbox", "options": null, "description": "1: Enable ZPS to align the rise edge of refrence clock and div clock"}, {"bit": "6", "name": "CLKin_OVERRIDE", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "When manual clock select is enabled, then <PERSON><PERSON><PERSON><PERSON>_SEL_MANUAL = 0/1/2 selects a manual clock input. CLKin_OVERRIDE = 1 will force that clock input. CLKin_OVERRIDE = 1 is used with clock distribution mode for best performance.\n0: Normal, no override.\n1: Force select of only CLKin0/1/2 as specified by CLKin_SEL_MANUAL in manual mode. Dynamic digital delay will not operate."}, {"bit": "5", "name": "HOLDOVER_EXIT_MODE", "default": "0", "widget_name": "HoldoverExitMode", "widget_type": "checkbox", "options": null, "description": "0: Exit based on LOS status. If clock is active by LOS, then begin exit.\n1: Exit based on PLL1 DLD. When the PLL1 phase detector confirming valid clock."}, {"bit": "4", "name": "HOLDOVER_PLL1_DET", "default": "0", "widget_name": "HoldOverPLL1Det", "widget_type": "checkbox", "options": null, "description": "This enables the HOLDOVER when PLL1 lock detect signal transitions from high to low.\n0: PLL1 DLD does not cause a clock switch event; 1: PLL1 DLD causes a clock switch event"}, {"bit": "3", "name": "LOS_EXTERNAL_INPUT", "default": "0", "widget_name": "LosExternalInput", "widget_type": "checkbox", "options": null, "description": "Use external signals for LOS status instead of internal LOS circuitry. CLKin_SEL0 pin is used for CLKin0 LOS, CLKin_SEL1 pin is used for CLKin1 LOS, and Status_LD1 is used for CLKin2 LOS. For any of these pins to be valid, the corresponding _TYPE register must be programmed as an input. 0: Disabled; 1: Enabled"}, {"bit": "2", "name": "RG_CLKin1_FIN1_HiFreq_BUF_EN", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "Enables the DAC Vtune rail detector. When the DAC achieves a specified Vtune, if this bit is enabled, the current clock input is considered invalid and an input clock switch event is generated. 0: Disabled; 1: Enabled"}, {"bit": "1", "name": "CLKin_SWITCH_CP_TRI", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "Enable clock switching with tri-stated charge pump. 0: Not enabled; 1: PLL1 charge pump tri-states during clock switching."}, {"bit": "0", "name": "HOLDOVER_EN", "default": "1", "widget_name": "HoldOverEn", "widget_type": "checkbox", "options": null, "description": "Sets whether holdover mode is active or not. 0: Disabled; 1: Enabled"}]}, "0x61": {"bits": [{"bit": "15:14", "name": "NC", "default": "00", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "13:0", "name": "HOLDOVER_DLD_CNT[13:0]", "default": "00001000000000", "widget_name": null, "widget_type": null, "options": null, "description": "The number of valid clocks of PLL1 PDF before holdover mode is exited."}]}, "0x63": {"bits": [{"bit": "15:14", "name": "NC", "default": "00", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "13:0", "name": "CLKin0_R[13:0]", "default": "00000001111000", "widget_name": "PLL1R0Div", "widget_type": "spinbox", "options": "0:16383", "description": "The value of PLL1 N counter when CLKin0 is selected."}]}, "0x65": {"bits": [{"bit": "15:14", "name": "NC", "default": "00", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "13:0", "name": "CLKin1_R[13:0]", "default": "00000001111000", "widget_name": "PLL1R1Div", "widget_type": "spinbox", "options": "0:16383", "description": "The value of PLL1 N counter when CLKin1 is selected."}]}, "0x67": {"bits": [{"bit": "15:14", "name": "NC", "default": "00", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "13:0", "name": "CLKin2_R[13:0]", "default": "00000010010110", "widget_name": "PLL1R2Div", "widget_type": "spinbox", "options": "0:16383", "description": "The value of PLL1 N counter when CLKin2 is selected."}]}, "0x69": {"bits": [{"bit": "15:14", "name": "NC", "default": "00", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "13:0", "name": "PLL1_N[13:0]", "default": "00000001111000", "widget_name": "PLL1NDivider", "widget_type": "spinbox", "options": "0:4095", "description": "The value of PLL1 N counter."}]}, "0x6B": {"bits": [{"bit": "15:8", "name": "NC", "default": "00000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "7:6", "name": "PLL1_WND_SIZE[1:0]", "default": "11", "widget_name": "comboPLL1WindSize", "widget_type": "combobox", "options": "0:3", "description": "PLL1_WND_SIZE sets the window size used for digital lock detect for PLL1. If the phase error between the reference and feedback of PLL1 is less than specified time, then the PLL1 lock counter increments."}, {"bit": "5", "name": "PLL1_CP_TRI", "default": "0", "widget_name": "PLL1CPState", "widget_type": "combobox", "options": "0:1", "description": "This bit allows for the PLL1 charge pump output pin, CPout1, to be placed into TRI-STATE.\n0: PLL1 CPout1 is active; 1: PLL1 CPout1 is at TRI-STATE"}, {"bit": "4", "name": "PLL1_CP_POL", "default": "1", "widget_name": "PLL1PFDPolarity", "widget_type": "combobox", "options": "0:1", "description": "PLL1_CP_POL sets the charge pump polarity for PLL1. Many VCXOs use positive slope. A positive slope VCXO increases output frequency with increasing voltage. A negative slope VCXO decreases output frequency with increasing voltage.\n0: Negative Slope VCO/VCXO; 1: Positive Slope VCO/VCXO"}, {"bit": "3:0", "name": "PLL1_CP_GAIN[3:0]", "default": "0100", "widget_name": "PLL1CPGain", "widget_type": "combobox", "options": "0:15", "description": "This bit programs the PLL1 charge pump output current level."}]}, "0x6C": {"bits": [{"bit": "15:14", "name": "NC", "default": "00", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "13:0", "name": "PLL1_DLD_CNT[13:0]", "default": "10000000000000", "widget_name": "spinBoxPLL1DLDCNT", "widget_type": "spinbox", "options": "0:16383", "description": "The value of PLL1 N counter when CLKin2 is selected."}]}, "0x6F": {"bits": [{"bit": "15:8", "name": "NC", "default": "00000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "7:3", "name": "PLL1_LD_MUX[4:0]", "default": "00001", "widget_name": "PLL1_LD_MUX", "widget_type": "combobox", "options": "0:15", "description": "This sets the output value of the Status_LD1 pin."}, {"bit": "2:0", "name": "PLL1_LD_TYPE[2:0]", "default": "011", "widget_name": "PLL1_LD_TYPE", "widget_type": "combobox", "options": "0:6", "description": "Sets the IO type of the Status_LD1 pin."}]}, "0x70": {"bits": [{"bit": "15:12", "name": "NC", "default": "0000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "11:0", "name": "PLL2_R[11:0]", "default": "000000000001", "widget_name": "PLL2RDivider", "widget_type": "spinbox", "options": "0:4095", "description": "Valid values for the PLL2 R divider."}]}, "0x72": {"bits": [{"bit": "15:8", "name": "NC", "default": "00000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "7:5", "name": "PLL2_P[2:0]", "default": "011", "widget_name": "PLL2Prescaler", "widget_type": "combobox", "options": "0:7", "description": "Field Value"}, {"bit": "4:2", "name": "OSCin_FREQ[2:0]", "default": "101", "widget_name": "OSCin_FREQ", "widget_type": "combobox", "options": "0:7", "description": "The frequency of the PLL2 reference input to the PLL2 Phase Detector (OSCin/OSCin* port) must be programmed in order  to support proper operation of the frequency calibration routine which locks the internal VCO to the target frequency."}, {"bit": "1", "name": "NC", "default": "1", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "0", "name": "PLL2_REF_2X_EN", "default": "0", "widget_name": "Doubler", "widget_type": "combobox", "options": "0:1", "description": "Enabling the PLL2 reference frequency doubler allows for higher phase detector frequencies on PLL2 than would normally be allowed with the given VCXO frequency. Higher phase detector frequencies reduces the PLL2 N values which makes the design of wider loop bandwidth filters possible. 0: Doubler Disabled; 1: Doubler Enabled"}]}, "0x76": {"bits": [{"bit": "15:2", "name": "NC", "default": "00000000000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "1:0", "name": "PLL2_N[17:16]", "default": "00", "widget_name": null, "widget_type": null, "options": null, "description": "This register disables frequency calibration and sets the PLL2 N divider value. Programming register 0x78 starts a VCO calibration routine if PLL2_FCAL_DIS = 0."}]}, "0x77": {"bits": [{"bit": "15:0", "name": "PLL2_N[15:0]", "default": "0000000000001100", "widget_name": null, "widget_type": null, "options": null, "description": "2(0x2)"}]}, "0x79": {"bits": [{"bit": "15:7", "name": "NC", "default": "000000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "6:5", "name": "PLL2_WND_SIZE[1:0]", "default": "10", "widget_name": "PLL2WINDSIZE", "widget_type": "combobox", "options": "0:3", "description": "PLL2_WND_SIZE sets the window size used for digital lock detect for PLL2. If the phase error between the reference and feedback of PLL2 is less than specified time, then the PLL2 lock counter increments."}, {"bit": "4:3", "name": "PLL2_CP_GAIN[1:0]", "default": "11", "widget_name": "PLL2CPGain", "widget_type": "combobox", "options": "0:3", "description": "This bit programs the PLL2 charge pump output current level. The table below also illustrates the impact of the PLL2 TRISTATE bit in conjunction with PLL2_CP_GAIN."}, {"bit": "2", "name": "PLL2_CP_POL", "default": "0", "widget_name": "PLL2PFDPolarity", "widget_type": "combobox", "options": "0:1", "description": "PLL2_CP_POL sets the charge pump polarity for PLL2. The internal VCO requires the negative charge pump polarity to be selected. Many VCOs use positive slope. A positive slope VCO increases output frequency with increasing voltage. A negative slope VCO decreases output frequency with increasing voltage."}, {"bit": "1", "name": "PLL2_CP_TRI", "default": "0", "widget_name": "PLL2CPState", "widget_type": "combobox", "options": "0:1", "description": "PLL2_CP_TRI TRI-STATEs the output of the PLL2 charge pump. 0: Disabled; 1: TRI-STATE"}, {"bit": "0", "name": "PLL2_DLD_EN", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "PLL2 DLD circuitry is enabled when the PLL2 DLD is used to provide an output to a lock detect status pin. PLL2_DLD_EN allows enabling the PLL2 DLD circuitry without needing to provide PLL2 DLD to a status pin. This enables PLL2 DLD status to be read back using SPI while allowing the Status pins to be used for other purposes.\n0: PLL2 DLD circuitry is on only of PLL2 DLD or PLL1 + PLL2 DLD signal is output from a Status_LD_MUX.\n1: PLL2 DLD circuitry is forced on."}]}, "0x7A": {"bits": [{"bit": "15:14", "name": "NC", "default": "00", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "13:0", "name": "PLL2_DLD_CNT[13:0]", "default": "01000000000000", "widget_name": "spinBoxPLL2DLDCNT", "widget_type": "spinbox", "options": "0:16383", "description": "The reference and feedback of PLL2 must be within the window of phase error as specified by PLL2_WND_SIZE for\nPLL2_DLD_CNT cycles before PLL2 digital lock detect is asserted."}]}, "0x7E": {"bits": [{"bit": "15:8", "name": "NC", "default": "00000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "7:3", "name": "PLL2_LD_MUX[4:0]", "default": "00000", "widget_name": "PLL2_LD_MUX", "widget_type": "combobox", "options": "0:15", "description": "This sets the output value of the Status_LD2 pin."}, {"bit": "2:0", "name": "PLL2_LD_TYPE[2:0]", "default": "011", "widget_name": "PLL2_LD_TYPE", "widget_type": "combobox", "options": "0:6", "description": "Sets the IO type of the Status_LD1 pin."}]}, "0x83": {"bits": [{"bit": "15:7", "name": "NC", "default": "000000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "6", "name": "PLL2_PRE_PD", "default": "0", "widget_name": "PLL2PrePD", "widget_type": "checkbox", "options": null, "description": "Powerdown PLL2 prescaler. 0: Normal Operation; 1: Powerdown"}, {"bit": "5", "name": "PLL2_PD", "default": "0", "widget_name": "PLL2PD", "widget_type": "checkbox", "options": null, "description": "Powerdown PLL2. 0: Normal Operation; 1: Powerdown"}, {"bit": "4", "name": "FIN0_PD", "default": "1", "widget_name": "Fin0PD", "widget_type": "checkbox", "options": null, "description": "Powerdown FIN0 buffer. 0: Normal Operation; 1: Powerdown"}, {"bit": "3:0", "name": "NC", "default": "0000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}]}, "0x87": {"bits": [{"bit": "15:6", "name": "NC", "default": "0000000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "5", "name": "PLL1R_RST", "default": "0", "widget_name": "PLL1RRst", "widget_type": "checkbox", "options": null, "description": "When set, PLL1 R divider will be held in reset. PLL1 will never lock with PLL1R_RST = 1. This bit is used in when synchronizing the PLL1 R divider. 0: PLL1 R divider normal operation. 1: PLL1 R divider held in reset."}, {"bit": "4:0", "name": "NC", "default": "00000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}]}, "0x92": {"bits": [{"bit": "15:2", "name": "NC", "default": "00000000000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "1", "name": "CLR_PLL1_LD_LOST", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "To reset RB_PLL1_LD_LOST, write CLR_PLL1_LD_LOST with 1 and then 0.\n0: RB_PLL1_LD_LOST will be set on next falling PLL1 DLD edge.\n1: RB_PLL1_LD_LOST is held clear (0). User must clear this bit to allow RB_PLL1_LD_LOST to become set again."}, {"bit": "0", "name": "CLR_PLL2_LD_LOST", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "To reset RB_PLL2_LD_LOST, write CLR_PLL2_LD_LOST with 1 and then 0.\n0: RB_PLL2_LD_LOST will be set on next falling PLL2 DLD edge.\n1: RB_PLL2_LD_LOST is held clear (0). User must clear this bit to allow RB_PLL2_LD_LOST to become set again."}]}, "0x93": {"bits": [{"bit": "15:4", "name": "NA", "default": "000000000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "3", "name": "RB_PLL1_LD_LOST", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "This is set when PLL1 DLD edge falls. Does not set if cleared while PLL1 DLD is low."}, {"bit": "2", "name": "RB_PLL1_LD", "default": "0", "widget_name": "RBPLL1DLD", "widget_type": "checkbox", "options": null, "description": "Read back 0: PLL1 DLD is low. Read back 1: PLL1 DLD is high."}, {"bit": "1", "name": "RB_PLL2_LD_LOST", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "This is set when PLL2 DLD edge falls. Does not set if cleared while PLL2 DLD is low."}, {"bit": "0", "name": "RB_PLL2_LD", "default": "0", "widget_name": "RBPLL2DLD", "widget_type": "checkbox", "options": null, "description": "PLL1_LD_MUX or PLL2_LD_MUX must select setting 2 (PLL2 DLD) for valid reading of this bit.\nRead back 0: PLL2 DLD is low. Read back 1: PLL2 DLD is high."}]}, "0x94": {"bits": [{"bit": "15:6", "name": "NC", "default": "0000000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "5", "name": "RB_CLKin2_SEL", "default": "0", "widget_name": "clkin2SEL", "widget_type": "checkbox", "options": null, "description": "Read back 0: CLKin2 is not selected for input to PLL1. Read back 1: CLKin2 is selected for input to PLL1."}, {"bit": "4", "name": "RB_CLKin1_SEL", "default": "0", "widget_name": "clkin1SEL", "widget_type": "checkbox", "options": null, "description": "Read back 0: CLKin1 is not selected for input to PLL1. Read back 1: CLKin1 is selected for input to PLL1."}, {"bit": "3", "name": "RB_CLKin0_SEL", "default": "0", "widget_name": "clkin0SEL", "widget_type": "checkbox", "options": null, "description": "Read back 0: CLKin0 is not selected for input to PLL1. Read back 1: CLKin0 is selected for input to PLL1."}, {"bit": "2", "name": "RB_CLKin2_LOS", "default": "0", "widget_name": "clkin2LOS", "widget_type": "checkbox", "options": null, "description": "Read back 1: CLKin2 LOS is active. Read back 0: CLKin2 LOS is not active."}, {"bit": "1", "name": "RB_CLKin1_LOS", "default": "0", "widget_name": "clkin1LOS", "widget_type": "checkbox", "options": null, "description": "Read back 1: CLKin1 LOS is active. Read back 0: CLKin1 LOS is not active."}, {"bit": "0", "name": "RB_CLKin0_LOS", "default": "0", "widget_name": "clkin0LOS", "widget_type": "checkbox", "options": null, "description": "Read back 1: CLKin0 LOS is active. Read back 0: CLKin0 LOS is not active."}]}, "0x95": {"bits": [{"bit": "15:10", "name": "NC", "default": "000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "9:0", "name": "RB_DAC_VALUE[9:0]", "default": "0000000000", "widget_name": null, "widget_type": null, "options": null, "description": "DAC value is 512 on power on reset, if PLL1 locks upon power-up the DAC value will change."}]}, "0x98": {"bits": [{"bit": "15:5", "name": "NC", "default": "00000000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "4", "name": "RB_HOLDOVER", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "Read back 0: Not in HOLDOVER. Read back 1: In HOLDOVER."}, {"bit": "3", "name": "RB_DAC_RAIL", "default": "0", "widget_name": "RB_DAC_RAIL", "widget_type": "checkbox", "options": null, "description": "Read Only"}, {"bit": "2", "name": "RB_DAC_HIGH", "default": "0", "widget_name": "RB_DAC_HIGH", "widget_type": "checkbox", "options": null, "description": "Read Only"}, {"bit": "1", "name": "RB_DAC_LOW", "default": "0", "widget_name": "RB_DAC_LOW", "widget_type": "checkbox", "options": null, "description": "Read Only"}, {"bit": "0", "name": "RB_DAC_LOCKED", "default": "0", "widget_name": "RB_DAC_LOCKED", "widget_type": "checkbox", "options": null, "description": "Read Only"}]}, "0xA0": {"bits": [{"bit": "15", "name": "NC", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "14", "name": "RG_PLL1_SWITCHOVER_RST", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "PLL1 switchover digital parts reset"}, {"bit": "13", "name": "RG_PLL1_HOLDOVER_RST", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "PLL1 holdover digital parts reset"}, {"bit": "12", "name": "RG_R1DIV_EN", "default": "1", "widget_name": null, "widget_type": null, "options": null, "description": "PLL1 R1 divider enable is controlled both by PLL1_PD and RG_R1DIV_EN; if PLL1_PD=0, this register is used for disable/enable R1DIV"}, {"bit": "11", "name": "RG_R1DIV_BYP_EN", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "bypass R1DIV(2~16383), equals to R1 divratio=1"}, {"bit": "10", "name": "RG_R1DIV_DCC_EN", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "used to increase R1DIV output duty cycle(~5CLKinX); to use it, need PLL1 R1 divratio>6."}, {"bit": "9", "name": "RG_N1DIV_EN", "default": "1", "widget_name": null, "widget_type": null, "options": null, "description": "PLL1 N1 divider enable is controlled both by PLL1_PD and RG_N1DIV_EN; if PLL1_PD=0, this register is used for disable/enable N1DIV"}, {"bit": "8", "name": "RG_N1DIV_DCC_EN", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "used to increase N1DIV output duty cycle(~5Tvcxo); to use it, need PLL1 N1 divratio>6."}, {"bit": "7:0", "name": "RG_CLKIN_LOS_REC[7:0]", "default": "10000000", "widget_name": null, "widget_type": null, "options": null, "description": "when CLKinX recover, LOS need wait LOS_REC[7:0] to change from 1 to 0"}]}, "0xA1": {"bits": [{"bit": "15", "name": "RG_PLL2_REF_2X_INV", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "debug used"}, {"bit": "14:13", "name": "RG_PLL2_REF_2X_TD[1:0]", "default": "10", "widget_name": null, "widget_type": null, "options": null, "description": "PLL2 reference delay time"}, {"bit": "12:10", "name": "RG_PLL2_LPF_C1[2:0]", "default": "100", "widget_name": "PLL2C1", "widget_type": "combobox", "options": "0:2", "description": "internal C1: 30pF fixed; C1[2:0] control 10/20/40pF"}, {"bit": "9:7", "name": "RG_PLL2_LPF_C3[2:0]", "default": "100", "widget_name": "PLL2C3", "widget_type": "combobox", "options": "0:2", "description": "internal C1: 30pF fixed; C1[2:0] control 10/20/40pF"}, {"bit": "6:4", "name": "RG_PLL2_LPF_R3[2:0]", "default": "100", "widget_name": "PLL2R3", "widget_type": "combobox", "options": "0,1,2,4", "description": "000: R3=2.4KOhm, 001: R3=0.2KOhm, 010: R3=0.5KOhm, 100: R3=1.1KOhm,"}, {"bit": "3:1", "name": "RG_PLL2_VTUNEBIAS_VOUT[2:0]", "default": "100", "widget_name": null, "widget_type": null, "options": null, "description": "used to set LPF initial voltage for PLL2 VCO calibration"}, {"bit": "0", "name": "RG_PLL2_ZPS_EN", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "PLL2 zero phase start enable, to align PFD two input(fref and fmmd)"}]}, "0xA2": {"bits": [{"bit": "15", "name": "NC", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "14", "name": "RG_PLL2_VTUNEBIAS_MILLER_EN", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "for external LPF, MILLER_EN=0; no need open to customs"}, {"bit": "13:12", "name": "RG_PLL2_VCO_BIAS[1:0]", "default": "10", "widget_name": null, "widget_type": null, "options": null, "description": "VCO Bias setting; no need open to customs"}, {"bit": "11", "name": "RG_PLL2_VTUNE_ADC_EN", "default": "1", "widget_name": null, "widget_type": null, "options": null, "description": "Vtune ADC enable"}, {"bit": "10:8", "name": "RG_PLL2_VTUNEBIAS_IOP[2:0]", "default": "100", "widget_name": null, "widget_type": null, "options": null, "description": "the larger the IOP[2:0], the faster the VCO Caliration LPF settling"}, {"bit": "7:4", "name": "AD_PLL2_VTUNEADC<3:0>", "default": "1000", "widget_name": "RBPLL2VtuneADC", "widget_type": "lineedit", "options": null, "description": "Vtune ADC ReadBack code; 0001: 0.2V, 0010: 0.4V…, 1111:3.0V"}, {"bit": "3:0", "name": "AD_TSENSOR_OUT<3:0>", "default": "1000", "widget_name": "AD_TSENSOR_OUT", "widget_type": "lineedit", "options": null, "description": "Temperature Sensor Readback code; relative resolution is about 15degree/code"}]}, "0xA3": {"bits": [{"bit": "15:8", "name": "RG_PLL2_FCAL_T2[7:0]", "default": "10000000", "widget_name": null, "widget_type": null, "options": null, "description": "PLL2 VCO calibration capacitance array waiting time"}, {"bit": "7:0", "name": "RG_PLL2_FCAL_T1[7:0]", "default": "10000000", "widget_name": null, "widget_type": null, "options": null, "description": "PLL2 VCO calibration LPF settle waiting time"}]}, "0xA4": {"bits": [{"bit": "15:13", "name": "NC", "default": "000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "12", "name": "RG_PLL2_OPEN", "default": "0", "widget_name": "PLL2OpenLoop", "widget_type": "checkbox", "options": null, "description": "PLL2 open enable, used for PLL2 open loop VCO phase noise test"}, {"bit": "11", "name": "RG_PLL2_TCL_EN", "default": "1", "widget_name": null, "widget_type": null, "options": null, "description": "PLL2 temperature compensation enable"}, {"bit": "10", "name": "RG_PLL2_TCL_SEL", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "if TCL_EN=1, TCL_SEL=x do not affect PLL2 loop; if TCL_EN=0, TCL_SEL=0, Kvco'=Kvco; TCL_SEL=1, Kvco'=Kvco+Ktco"}, {"bit": "9", "name": "RG_PLL2_FCAL_RSTB", "default": "1", "widget_name": null, "widget_type": null, "options": null, "description": "for resetb PLL2 VCO calibration digital parts"}, {"bit": "8", "name": "RG_PLL2_FCAL_EN", "default": "0", "widget_name": "FCALEN", "widget_type": "checkbox", "options": null, "description": "PLL2 VCO calibration trigger signal; everytime after POWERDOWN, PLL2_PD or M2[13:0] changed, need set this register from 0 to 1(keep);   for external VCO, no need."}, {"bit": "7:0", "name": "RG_PLL2_FCAL_M1[7:0]", "default": "10100000", "widget_name": "FCALM1", "widget_type": "combobox", "options": "0:255", "description": "for , 61.44MHz, the time window corresponding to 2.6us"}]}, "0xA5": {"bits": [{"bit": "15:14", "name": "NC", "default": "00", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "13:0", "name": "RG_PLL2_FCAL_M2[13:0]", "default": "00011110000000", "widget_name": "FCALM2", "widget_type": "combobox", "options": "0:16383", "description": "for 61.44MHz and M1=160, M2=1920 corresonding to 2946.12MHz"}]}, "0xA6": {"bits": [{"bit": "15:10", "name": "NC", "default": "000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "9", "name": "DA_PLL2_FCAL_DONE", "default": "1", "widget_name": null, "widget_type": null, "options": null, "description": "readonly; PLL2 auto VCO calibration done signal"}, {"bit": "8", "name": "DA_PLL2_FCAL_FAIL", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "readonly; PLL2 auto VCO calibration fail signal"}, {"bit": "7:0", "name": "DA_PLL2_FCAL_SCA[7:0]", "default": "10000000", "widget_name": "RBFcalCAPMODE", "widget_type": "lineedit", "options": null, "description": "readonly; PLL2 auto VCO calibration capacitance code"}]}, "0xA7": {"bits": [{"bit": "15:9", "name": "NC", "default": "0000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "8", "name": "RG_PLL2_FCAL_MANU_EN", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "PLL2 manual VCO capacitrance array search enable; debug used"}, {"bit": "7:0", "name": "RG_PLL2_FCAL_SCA_MANU[7:0]", "default": "10000000", "widget_name": null, "widget_type": null, "options": null, "description": "PLL2 manual VCO capacitrance array code; debug used"}]}, "0xA8": {"bits": [{"bit": "15:6", "name": "NC", "default": "0000000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "5", "name": "RG_N2DIV_EN", "default": "1", "widget_name": null, "widget_type": null, "options": null, "description": "PLL2 N2 divider enable is controlled both by PLL2_PD and RG_N2DIV_EN; if PLL2_PD=0, this register is used for disable/enable N2DIV"}, {"bit": "4", "name": "RG_N2DIV_DCC_EN", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "used to increase N2DIV output duty cycle(~5Tvco); to use it, need PLL2 N2 divratio>6."}, {"bit": "3", "name": "RG_N2DIV_RETIME_INV", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "back up register; do not use by default."}, {"bit": "2", "name": "RG_R2DIV_EN", "default": "1", "widget_name": null, "widget_type": null, "options": null, "description": "PLL2 R2 divider enable is controlled both by PLL2_PD and RG_R2DIV_EN; if PLL2_PD=0, this register is used for disable/enable R2DIV"}, {"bit": "1", "name": "RG_R2DIV_BYP_EN", "default": "1", "widget_name": null, "widget_type": null, "options": null, "description": "bypass R2DIV(2~4095), equals to R2 divratio=1"}, {"bit": "0", "name": "RG_R2DIV_DCC_EN", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "used to increase R2DIV output duty cycle(~5Tvcxo); to use it, need PLL2 R2 divratio>6."}]}, "0xA9": {"bits": [{"bit": "15:7", "name": "NC", "default": "000000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "6", "name": "RG_DCLK12_13_HS_EN", "default": "0", "widget_name": "RGDCLK12_13HSEN", "widget_type": "checkbox", "options": null, "description": "added based on TI LMK04832. If DCLK divide ration is odd number, Half step will change output duty cycle(one Tvco)"}, {"bit": "5", "name": "RG_DCLK10_11_HS_EN", "default": "0", "widget_name": "RGDCLK10_11HSEN", "widget_type": "checkbox", "options": null, "description": "added based on TI LMK04832. If DCLK divide ration is odd number, Half step will change output duty cycle(one Tvco)"}, {"bit": "4", "name": "RG_DCLK8_9_HS_EN", "default": "0", "widget_name": "RGDCLK8_9HSEN", "widget_type": "checkbox", "options": null, "description": "added based on TI LMK04832. If DCLK divide ration is odd number, Half step will change output duty cycle(one Tvco)"}, {"bit": "3", "name": "RG_DCLK6_7_HS_EN", "default": "0", "widget_name": "RGDCLK6_7HSEN", "widget_type": "checkbox", "options": null, "description": "added based on TI LMK04832. If DCLK divide ration is odd number, Half step will change output duty cycle(one Tvco)"}, {"bit": "2", "name": "RG_DCLK4_5_HS_EN", "default": "0", "widget_name": "RGDCLK4_5HSEN", "widget_type": "checkbox", "options": null, "description": "added based on TI LMK04832. If DCLK divide ration is odd number, Half step will change output duty cycle(one Tvco)"}, {"bit": "1", "name": "RG_DCLK2_3_HS_EN", "default": "0", "widget_name": "RGDCLK2_3HSEN", "widget_type": "checkbox", "options": null, "description": "added based on TI LMK04832. If DCLK divide ration is odd number, Half step will change output duty cycle(one Tvco)"}, {"bit": "0", "name": "RG_DCLK0_1_HS_EN", "default": "0", "widget_name": "RGDCLK0_1HSEN", "widget_type": "checkbox", "options": null, "description": "added based on TI LMK04832. If DCLK divide ration is odd number, Half step will change output duty cycle(one Tvco)"}]}, "0xAA": {"bits": [{"bit": "15:13", "name": "NC", "default": "000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "12:9", "name": "RG_LDO12_VOUT_PLL1[3:0]", "default": "0001", "widget_name": null, "widget_type": null, "options": null, "description": "LDO output setting; no need open to customs"}, {"bit": "8:6", "name": "RG_LDO25_VOUT_PLL1[2:0]", "default": "110", "widget_name": null, "widget_type": null, "options": null, "description": "LDO output setting; no need open to customs"}, {"bit": "5:3", "name": "RG_LDO25_VOUT_OSCOUT[2:0]", "default": "001", "widget_name": null, "widget_type": null, "options": null, "description": "LDO output setting; no need open to customs"}, {"bit": "2:0", "name": "RG_LDO25_VOUT_OSCIN[2:0]", "default": "000", "widget_name": null, "widget_type": null, "options": null, "description": "LDO output setting; no need open to customs"}]}, "0xAB": {"bits": [{"bit": "15:13", "name": "NC", "default": "000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "12:9", "name": "RG_LDO12_VOUT_PLL2[3:0]", "default": "0001", "widget_name": null, "widget_type": null, "options": null, "description": "LDO output setting; no need open to customs"}, {"bit": "8:6", "name": "RG_LDO25_VOUT_PLL2_CP[2:0]", "default": "110", "widget_name": null, "widget_type": null, "options": null, "description": "LDO output setting; no need open to customs"}, {"bit": "5:3", "name": "RG_LDO25_VOUT_PLL2_PFD[2:0]", "default": "001", "widget_name": null, "widget_type": null, "options": null, "description": "LDO output setting; no need open to customs"}, {"bit": "2:0", "name": "RG_LDO25_VOUT_PLL2_VCO[2:0]", "default": "000", "widget_name": null, "widget_type": null, "options": null, "description": "LDO output setting; no need open to customs"}]}, "0xAC": {"bits": [{"bit": "15:14", "name": "NC", "default": "00", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "13:10", "name": "RG_LDO12_VOUT_CG0[3:0]", "default": "0000", "widget_name": null, "widget_type": null, "options": null, "description": "LDO output setting; no need open to customs"}, {"bit": "9:7", "name": "RG_LDO25_VOUT_CG0[2:0]", "default": "111", "widget_name": null, "widget_type": null, "options": null, "description": "LDO output setting; no need open to customs"}, {"bit": "6:3", "name": "RG_LDO12_VOUT_CG1[3:0]", "default": "0001", "widget_name": null, "widget_type": null, "options": null, "description": "LDO output setting; no need open to customs"}, {"bit": "2:0", "name": "RG_LDO25_VOUT_CG1[2:0]", "default": "000", "widget_name": null, "widget_type": null, "options": null, "description": "LDO output setting; no need open to customs"}]}, "0xAD": {"bits": [{"bit": "15:14", "name": "NC", "default": "00", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "13:10", "name": "RG_LDO12_VOUT_CG2[3:0]", "default": "0000", "widget_name": null, "widget_type": null, "options": null, "description": "LDO output setting; no need open to customs"}, {"bit": "9:7", "name": "RG_LDO25_VOUT_CG2[2:0]", "default": "111", "widget_name": null, "widget_type": null, "options": null, "description": "LDO output setting; no need open to customs"}, {"bit": "6:3", "name": "RG_LDO12_VOUT_CG3[3:0]", "default": "0001", "widget_name": null, "widget_type": null, "options": null, "description": "LDO output setting; no need open to customs"}, {"bit": "2:0", "name": "RG_LDO25_VOUT_CG3[2:0]", "default": "000", "widget_name": null, "widget_type": null, "options": null, "description": "LDO output setting; no need open to customs"}]}, "0xAE": {"bits": [{"bit": "15:8", "name": "NC", "default": "00000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "7:4", "name": "RG_LDO12_VOUT_SYSREF[3:0]", "default": "0100", "widget_name": null, "widget_type": null, "options": null, "description": "LDO output setting; no need open to customs"}, {"bit": "3", "name": "RG_FC1_EN", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "PLL1 fast charge and reset"}, {"bit": "2", "name": "RG_FC2_EN", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "PLL2 fast charge and reset"}, {"bit": "1", "name": "RG_FC3_EN", "default": "1", "widget_name": null, "widget_type": null, "options": null, "description": "SYSREF fast charge and reset"}, {"bit": "0", "name": "RG_FC4_EN", "default": "1", "widget_name": null, "widget_type": null, "options": null, "description": "14 output paths fast charge and reset"}]}, "0xD8": {"bits": [{"bit": "15:8", "name": "NC", "default": "00000000", "widget_name": null, "widget_type": null, "options": null, "description": "Reserved"}, {"bit": "7", "name": "RG_SPI_LE_DEBUR_EN", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": "SPI LE Deburiing Enable : 1: enable; 0: disable"}, {"bit": "6", "name": "NC", "default": "0", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "5:4", "name": "RG_LE_TYPE[1:0]", "default": "10", "widget_name": null, "widget_type": null, "options": null, "description": "define SPI Leb pin input type: 00: pushpull; 01:pll up; 10: pull down; 11:pushpull"}, {"bit": "3", "name": "SPl_3WIRE_DIS", "default": "0", "widget_name": "SPI3WrireDis", "widget_type": "checkbox", "options": null, "description": "Disable 3-wire SPl mode. 0: 3 Wire Mode enabled; 1: 3 Wire Mode disabled"}, {"bit": "2", "name": "RG_UPD_EDGE", "default": "1", "widget_name": null, "widget_type": null, "options": null, "description": "0: device update data on spi_SDIO or spi_MISO on rising edge of spi_clk\n1: device update data on spi_SDIO or spi_MISO on falling edge of spi_clk"}, {"bit": "1:0", "name": "RG_SPI_LOCK[1:0]", "default": "00", "widget_name": null, "widget_type": null, "options": null, "description": "0: Registers unlocked; 1 to 3: Registers locked."}]}, "0xDA": {"bits": [{"bit": "15:8", "name": "RG_RESET_CLR_CNT", "default": "10000000", "widget_name": null, "widget_type": null, "options": null, "description": "after RESET_CLR_CNT cycles , RESET will deassert even no spi_clk rise comes"}, {"bit": "7:1", "name": "NC", "default": "0000000", "widget_name": null, "widget_type": null, "options": null, "description": null}, {"bit": "0", "name": "RESET", "default": "0", "widget_name": "spiReset", "widget_type": "checkbox", "options": null, "description": "0:Normal operation; 1:Reset (automatically cleared whe the spi_clk rise comes)"}]}}