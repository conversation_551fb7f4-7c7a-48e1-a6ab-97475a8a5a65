"""
窗口管理服务
负责管理子窗口的创建、显示、关闭和状态管理
"""

from PyQt5.QtWidgets import QWidget, QVBoxLayout, QApplication, QMessageBox
from PyQt5.QtCore import QTimer
from utils.Log import get_module_logger
from utils.CursorUtils import force_restore_cursor

logger = get_module_logger(__name__)
import traceback


class WindowManagementService:
    """窗口管理服务"""
    
    def __init__(self, main_window):
        """初始化窗口管理服务
        
        Args:
            main_window: 主窗口实例
        """
        self.main_window = main_window
        self.managed_windows = {}  # 存储管理的窗口
        
    def create_window_in_tab(self, title, window_attr, window_class, action_attr=None, 
                           post_init_callback=None, *args, **kwargs):
        """在标签页中创建窗口
        
        Args:
            title: 标签页标题
            window_attr: 窗口对象的属性名
            window_class: 窗口类
            action_attr: 菜单动作的属性名(可选)
            post_init_callback: 窗口初始化后的回调(可选)
            *args, **kwargs: 传递给窗口构造函数的参数
            
        Returns:
            创建的窗口对象
        """
        try:
            # 显示进度条
            self.main_window.loading_progress.setVisible(True)
            
            # 如果窗口已经存在，直接激活它
            if hasattr(self.main_window, window_attr) and getattr(self.main_window, window_attr) is not None:
                return self._activate_existing_window(title, window_attr)
            
            # 创建新窗口
            window = self._create_new_window(window_class, args, kwargs)
            
            # 连接窗口关闭信号
            self._connect_window_signals(window, window_attr)
            
            # 执行窗口后初始化回调
            if post_init_callback:
                post_init_callback(window)
            
            # 添加到标签页
            self._add_window_to_tab(window, title, window_attr, action_attr)
            
            # 隐藏进度条
            self.main_window.loading_progress.setVisible(False)
            
            # 激活窗口
            self._activate_window()
            
            logger.info(f"{title}界面加载完成")
            return window
            
        except Exception as e:
            return self._handle_window_creation_error(title, e)
    
    def _activate_existing_window(self, title, window_attr):
        """激活已存在的窗口"""
        # 查找该窗口在哪个标签页
        for i in range(self.main_window.tools_tab_widget.count()):
            if self.main_window.tools_tab_widget.tabText(i) == title:
                self.main_window.tools_tab_widget.setCurrentIndex(i)
                # 确保标签页容器可见
                self.main_window.tools_tab_widget.setVisible(True)
                break
        
        return getattr(self.main_window, window_attr)
    
    def _create_new_window(self, window_class, args, kwargs):
        """创建新窗口实例"""
        # 使用适配后的寄存器对象替换原始寄存器对象
        if 'registers' in kwargs and hasattr(self.main_window, 'register_manager'):
            if kwargs['registers'] == self.main_window.register_manager.register_objects:
                # 替换为适配后的寄存器对象
                logger.info("使用适配后的寄存器对象创建窗口")
                kwargs['registers'] = self.main_window.register_manager.get_adapted_register_objects()
        
        # 创建窗口 - 设置parent为None以避免显示问题
        return window_class(*args, **kwargs)
    
    def _connect_window_signals(self, window, window_attr):
        """连接窗口信号"""
        if hasattr(window, 'window_closed'):
            handler_name = f"handle_{window_attr.replace('window', '')}_closed"
            if hasattr(self.main_window, handler_name):
                window.window_closed.connect(getattr(self.main_window, handler_name))
    
    def _add_window_to_tab(self, window, title, window_attr, action_attr):
        """将窗口添加到标签页"""
        # 创建容器窗口部件作为标签页内容
        container = QWidget()
        container_layout = QVBoxLayout(container)
        container_layout.addWidget(window)
        container_layout.setContentsMargins(0, 0, 0, 0)

        # 在容器上存储窗口信息，用于后续的拖拽停靠功能
        container.tool_window = window
        container.window_attr = window_attr
        container.title = title

        # 添加到标签页
        tab_index = self.main_window.tools_tab_widget.addTab(container, title)

        # 确保标签页容器可见并切换到新标签页
        self.main_window.tools_tab_widget.setVisible(True)
        self.main_window.tools_tab_widget.setCurrentIndex(tab_index)

        # 保存窗口引用
        setattr(self.main_window, window_attr, window)
        self.managed_windows[window_attr] = {
            'window': window,
            'title': title,
            'action_attr': action_attr,
            'tab_index': tab_index
        }

        # 确保菜单项被选中
        if action_attr and hasattr(self.main_window, action_attr):
            getattr(self.main_window, action_attr).setChecked(True)
    
    def _activate_window(self):
        """激活主窗口"""
        # 立即处理窗口事件，确保标签页显示
        QApplication.processEvents()
        
        # 激活窗口确保前置显示
        self.main_window.activateWindow()
        self.main_window.raise_()
    
    def _handle_window_creation_error(self, title, error):
        """处理窗口创建错误"""
        logger.error(f"创建{title}界面时出错: {str(error)}")
        traceback.print_exc()
        
        # 隐藏进度条
        self.main_window.loading_progress.setVisible(False)
        
        QMessageBox.critical(
            self.main_window, 
            "错误", 
            f"创建{title}界面时出错:\n{str(error)}"
        )
        return None
    
    def handle_window_closed(self, window_name, action_name, window_attr_name, log_prefix="工具窗口"):
        """通用窗口关闭处理方法
        
        Args:
            window_name: 窗口在日志中显示的名称
            action_name: 关联菜单动作的属性名
            window_attr_name: 窗口对象的属性名
            log_prefix: 日志前缀
        """
        try:
            # 取消菜单项的选中状态
            if hasattr(self.main_window, action_name):
                getattr(self.main_window, action_name).setChecked(False)
            
            # 清除窗口引用
            setattr(self.main_window, window_attr_name, None)
            
            # 从管理的窗口中移除
            if window_attr_name in self.managed_windows:
                del self.managed_windows[window_attr_name]

            # 检查是否需要隐藏标签页容器
            self._check_and_hide_tab_widget_if_empty()

            logger.info(f"{window_name}已关闭，已取消菜单项选中状态")
            
        except Exception as e:
            logger.error(f"处理{window_name}关闭事件时发生错误: {str(e)}")
            traceback.print_exc()
    
    def close_all_windows(self):
        """关闭所有管理的窗口"""
        try:
            # 首先关闭所有插件窗口（如果有插件服务）
            if hasattr(self.main_window, 'plugin_integration_service'):
                try:
                    self.main_window.plugin_integration_service.close_all_plugin_windows()
                    logger.info("已通过插件服务关闭所有插件窗口")
                except Exception as e:
                    logger.error(f"通过插件服务关闭窗口时出错: {str(e)}")

            # 然后关闭其他管理的窗口
            for window_attr, window_info in list(self.managed_windows.items()):
                try:
                    window = window_info['window']
                    if window and hasattr(window, 'close'):
                        window.close()

                    # 清除主窗口中的引用
                    if hasattr(self.main_window, window_attr):
                        setattr(self.main_window, window_attr, None)

                except Exception as e:
                    logger.error(f"关闭窗口 {window_attr} 时出错: {str(e)}")

            # 清空管理的窗口字典
            self.managed_windows.clear()

            # 清空所有标签页并隐藏容器
            self._clear_and_hide_tab_widget()
            
            # 强制恢复光标状态
            force_restore_cursor()

            logger.info("所有管理的窗口已关闭")

        except Exception as e:
            logger.error(f"关闭所有窗口时出错: {str(e)}")

    def _clear_and_hide_tab_widget(self):
        """清空所有标签页并隐藏标签页容器"""
        try:
            if hasattr(self.main_window, 'tools_tab_widget'):
                tab_widget = self.main_window.tools_tab_widget

                # 清空所有标签页
                while tab_widget.count() > 0:
                    tab_widget.removeTab(0)

                # 隐藏标签页容器
                tab_widget.setVisible(False)

                # 强制刷新UI以确保布局更新
                tab_widget.update()
                if hasattr(self.main_window, 'centralWidget'):
                    self.main_window.centralWidget().update()

                logger.info("已清空所有标签页并隐藏标签页容器")
            else:
                logger.warning("主窗口没有 tools_tab_widget 属性")
        except Exception as e:
            logger.error(f"清空和隐藏标签页容器时出错: {str(e)}")

    def _check_and_hide_tab_widget_if_empty(self):
        """检查标签页容器，如果为空则隐藏"""
        try:
            if hasattr(self.main_window, 'tools_tab_widget'):
                tab_widget = self.main_window.tools_tab_widget
                tab_count = tab_widget.count()

                logger.debug(f"当前标签页数量: {tab_count}")

                if tab_count == 0:
                    tab_widget.setVisible(False)
                    logger.info("所有标签页已关闭，隐藏标签页容器")

                    # 强制刷新UI以确保布局更新
                    tab_widget.update()
                    if hasattr(self.main_window, 'centralWidget'):
                        self.main_window.centralWidget().update()
                else:
                    logger.debug(f"仍有 {tab_count} 个标签页，保持容器可见")
            else:
                logger.warning("主窗口没有 tools_tab_widget 属性")
        except Exception as e:
            logger.error(f"检查和隐藏标签页容器时出错: {str(e)}")

    def get_window_count(self):
        """获取当前管理的窗口数量
        
        Returns:
            int: 窗口数量
        """
        return len(self.managed_windows)
    
    def is_window_open(self, window_attr):
        """检查指定窗口是否打开
        
        Args:
            window_attr: 窗口属性名
            
        Returns:
            bool: 窗口是否打开
        """
        return (window_attr in self.managed_windows and 
                hasattr(self.main_window, window_attr) and 
                getattr(self.main_window, window_attr) is not None)
    
    def get_window_info(self, window_attr):
        """获取窗口信息
        
        Args:
            window_attr: 窗口属性名
            
        Returns:
            dict: 窗口信息字典，如果窗口不存在则返回None
        """
        return self.managed_windows.get(window_attr)
    
    def refresh_all_windows(self):
        """刷新所有管理的窗口"""
        try:
            for window_attr, window_info in self.managed_windows.items():
                window = window_info['window']
                if window and hasattr(window, 'refresh'):
                    try:
                        window.refresh()
                        logger.debug(f"已刷新窗口: {window_info['title']}")
                    except Exception as e:
                        logger.warning(f"刷新窗口 {window_info['title']} 时出错: {str(e)}")
            
            logger.info("所有窗口刷新完成")
            
        except Exception as e:
            logger.error(f"刷新所有窗口时出错: {str(e)}")

    def undock_tool_window(self, window_attr):
        """将工具窗口从标签页中分离为悬浮窗口

        Args:
            window_attr: 窗口属性名
        """
        try:
            if window_attr not in self.managed_windows:
                logger.warning(f"未找到工具窗口: {window_attr}")
                return

            window_info = self.managed_windows[window_attr]
            window = window_info['window']
            title = window_info['title']

            # 查找并移除标签页
            tab_widget = self.main_window.tools_tab_widget
            for i in range(tab_widget.count()):
                if tab_widget.tabText(i) == title:
                    # 获取容器
                    container = tab_widget.widget(i)
                    if container and container.layout():
                        # 从容器中移除窗口
                        container.layout().removeWidget(window)

                        # 移除标签页
                        tab_widget.removeTab(i)

                        # 配置为悬浮窗口
                        self._configure_floating_window(window, window_attr)
                        window.show()

                        logger.info(f"工具窗口 {title} 已分离为悬浮窗口")
                        break

            # 检查是否需要隐藏标签页容器
            self._check_and_hide_tab_widget_if_empty()

        except Exception as e:
            logger.error(f"分离工具窗口失败 {window_attr}: {str(e)}")

    def dock_tool_window(self, window_attr):
        """将悬浮的工具窗口停靠到标签页

        Args:
            window_attr: 窗口属性名
        """
        try:
            if window_attr not in self.managed_windows:
                logger.warning(f"未找到工具窗口: {window_attr}")
                return

            window_info = self.managed_windows[window_attr]
            window = window_info['window']
            title = window_info['title']

            # 将窗口重新添加到标签页
            self._add_window_to_tab(window, title, window_attr, window_info.get('action_attr'))

            logger.info(f"工具窗口 {title} 已停靠到主界面")

        except Exception as e:
            logger.error(f"停靠工具窗口失败 {window_attr}: {str(e)}")

    def _configure_floating_window(self, window, window_attr):
        """配置悬浮窗口

        Args:
            window: 窗口实例
            window_attr: 窗口属性名
        """
        try:
            from PyQt5.QtCore import Qt
            from PyQt5.QtWidgets import QApplication

            # 设置窗口标志，使其可以独立移动和调整大小
            window.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint |
                                Qt.WindowMinimizeButtonHint | Qt.WindowMaximizeButtonHint)

            # 设置窗口为非模态
            if hasattr(window, 'setModal'):
                window.setModal(False)

            # 添加拖拽停靠功能
            self._add_drag_dock_support_to_tool_window(window, window_attr)

            # 设置窗口位置（主窗口右侧）
            main_window = self.main_window
            if main_window:
                main_geometry = main_window.geometry()
                plugin_x = main_geometry.x() + main_geometry.width() + 10
                plugin_y = main_geometry.y()

                # 获取屏幕尺寸
                screen = QApplication.primaryScreen()
                screen_geometry = screen.availableGeometry()

                # 确保窗口不会超出屏幕边界
                if plugin_x + window.width() > screen_geometry.right():
                    plugin_x = main_geometry.x() - window.width() - 10
                    if plugin_x < 0:
                        plugin_x = 50

                if plugin_y + window.height() > screen_geometry.bottom():
                    plugin_y = screen_geometry.bottom() - window.height() - 50

                window.move(plugin_x, plugin_y)

            logger.info(f"已配置悬浮工具窗口: {window_attr}")

        except Exception as e:
            logger.error(f"配置悬浮工具窗口失败 {window_attr}: {str(e)}")

    def show_window_status(self):
        """显示窗口状态信息"""
        if not self.managed_windows:
            self.main_window.show_status_message("当前没有打开的工具窗口", 3000)
        else:
            window_names = [info['title'] for info in self.managed_windows.values()]
            status_msg = f"当前打开的工具窗口: {', '.join(window_names)}"
            self.main_window.show_status_message(status_msg, 5000)

    def _add_drag_dock_support_to_tool_window(self, window, window_attr):
        """为工具窗口添加拖拽停靠支持

        Args:
            window: 工具窗口实例
            window_attr: 窗口属性名
        """
        try:
            from PyQt5.QtCore import Qt
            from PyQt5.QtWidgets import QApplication

            # 存储拖拽相关的状态
            window._drag_start_position = None
            window._is_dragging = False
            window._window_attr = window_attr
            window._window_service = self

            # 重写鼠标事件处理方法
            original_mouse_press = window.mousePressEvent
            original_mouse_move = window.mouseMoveEvent
            original_mouse_release = window.mouseReleaseEvent

            def mouse_press_event(event):
                """处理鼠标按下事件"""
                logger.debug(f"[工具窗口拖拽] 鼠标按下事件 - 窗口: {window_attr}, 按钮: {event.button()}")
                if event.button() == Qt.LeftButton:
                    window._drag_start_position = event.globalPos()
                    window._is_dragging = False
                    logger.debug(f"[工具窗口拖拽] 开始跟踪拖拽 - 起始位置: {window._drag_start_position}")
                # 调用原始事件处理
                if original_mouse_press:
                    original_mouse_press(event)

            def mouse_move_event(event):
                """处理鼠标移动事件"""
                if (event.buttons() == Qt.LeftButton and
                    window._drag_start_position is not None):

                    # 计算拖拽距离
                    distance = (event.globalPos() - window._drag_start_position).manhattanLength()

                    # 如果拖拽距离足够大，开始拖拽 - 使用更小的阈值让拖拽更容易触发
                    drag_threshold = 5  # 降低拖拽阈值从默认的10到5像素，与插件服务保持一致
                    if distance >= drag_threshold:
                        if not window._is_dragging:
                            logger.info(f"[工具窗口拖拽] 开始拖拽模式 - 窗口: {window_attr}, 距离: {distance}, 阈值: {drag_threshold}")
                        window._is_dragging = True

                        # 检查是否拖拽到停靠区域
                        self._check_tool_window_dock_area(window, event.globalPos())

                # 调用原始事件处理
                if original_mouse_move:
                    original_mouse_move(event)

            def mouse_release_event(event):
                """处理鼠标释放事件"""
                try:
                    logger.debug(f"[工具窗口拖拽] 鼠标释放事件 - 窗口: {window_attr}, 拖拽中: {window._is_dragging}")

                    if (event.button() == Qt.LeftButton and
                        window._is_dragging):

                        # 检查是否在停靠区域释放
                        in_dock_area = self._is_tool_window_in_dock_area(event.globalPos())
                        logger.debug(f"[工具窗口拖拽] 释放位置检查 - 在停靠区域: {in_dock_area}")

                        if in_dock_area:
                            logger.info(f"[工具窗口拖拽] 尝试停靠工具窗口: {window_attr}")
                            self.dock_tool_window(window_attr)

                    # 恢复窗口状态
                    self._restore_tool_window_state(window)

                    # 重置拖拽状态
                    window._drag_start_position = None
                    window._is_dragging = False

                    # 调用原始事件处理
                    if original_mouse_release:
                        original_mouse_release(event)

                except Exception as e:
                    logger.error(f"处理工具窗口鼠标释放事件时出错: {str(e)}")

            # 替换事件处理方法
            window.mousePressEvent = mouse_press_event
            window.mouseMoveEvent = mouse_move_event
            window.mouseReleaseEvent = mouse_release_event

            logger.info(f"为工具窗口 {window_attr} 添加拖拽停靠支持")

        except Exception as e:
            logger.error(f"添加工具窗口拖拽停靠支持失败 {window_attr}: {str(e)}")

    def _check_tool_window_dock_area(self, window, global_pos):
        """检查工具窗口拖拽位置并提供视觉反馈"""
        try:
            if self._is_tool_window_in_dock_area(global_pos):
                # 提供视觉反馈
                if not hasattr(window, '_original_title'):
                    window._original_title = window.windowTitle()
                window.setWindowTitle(f"{window._original_title} - 释放鼠标停靠到主界面")

                # 改变鼠标光标
                from PyQt5.QtCore import Qt
                from PyQt5.QtWidgets import QApplication
                QApplication.setOverrideCursor(Qt.PointingHandCursor)
            else:
                # 恢复原始状态
                if hasattr(window, '_original_title'):
                    window.setWindowTitle(window._original_title)
                from PyQt5.QtWidgets import QApplication
                from PyQt5.QtCore import Qt
                # 强制恢复光标状态
                force_restore_cursor()
        except Exception as e:
            logger.error(f"检查工具窗口停靠区域时出错: {str(e)}")

    def _is_tool_window_in_dock_area(self, global_pos):
        """判断鼠标位置是否在工具窗口停靠区域内"""
        try:
            # 获取主窗口的全局几何信息 - 使用frameGeometry()获取包含标题栏的完整窗口区域
            main_geometry = self.main_window.frameGeometry()

            # 如果frameGeometry()不可用，使用geometry()并转换为全局坐标
            if main_geometry.isEmpty():
                main_geometry = self.main_window.geometry()
                # 将窗口坐标转换为全局坐标 - 使用QPoint(0,0)来获取窗口左上角的全局坐标
                from PyQt5.QtCore import QPoint
                main_global_pos = self.main_window.mapToGlobal(QPoint(0, 0))
                main_geometry.moveTopLeft(main_global_pos)

            # 定义停靠区域：主窗口底部30%的区域
            dock_area_height = int(main_geometry.height() * 0.3)
            dock_area_top = main_geometry.top() + main_geometry.height() - dock_area_height
            dock_area_bottom = main_geometry.bottom()

            # 添加边距，避免边界误触发
            margin = 10
            dock_area_left = main_geometry.left() + margin
            dock_area_right = main_geometry.right() - margin
            dock_area_top += margin
            dock_area_bottom -= margin

            # 检查鼠标是否在停靠区域内
            is_in_area = (global_pos.x() >= dock_area_left and
                         global_pos.x() <= dock_area_right and
                         global_pos.y() >= dock_area_top and
                         global_pos.y() <= dock_area_bottom)

            # 只在状态变化时输出调试信息
            if not hasattr(self, '_last_tool_dock_state') or self._last_tool_dock_state != is_in_area:
                logger.info(f"🎯 [工具窗口停靠区域] 鼠标位置: {global_pos}, 在停靠区域: {is_in_area}")
                logger.info(f"🎯 [工具窗口停靠区域] 停靠区域范围 - X: {dock_area_left}-{dock_area_right}, Y: {dock_area_top}-{dock_area_bottom}")
                logger.info(f"🎯 [工具窗口停靠区域] 主窗口几何: {main_geometry}")
                self._last_tool_dock_state = is_in_area

            return is_in_area

        except Exception as e:
            logger.error(f"判断工具窗口停靠区域时出错: {str(e)}")
            return False

    def _restore_tool_window_state(self, window):
        """恢复工具窗口状态"""
        try:
            # 恢复窗口标题
            if hasattr(window, '_original_title'):
                window.setWindowTitle(window._original_title)
                delattr(window, '_original_title')

            # 强制恢复鼠标光标 - 清空整个光标栈
            from PyQt5.QtWidgets import QApplication
            from PyQt5.QtCore import Qt
            try:
                # 多次调用restoreOverrideCursor确保清空光标栈
                for _ in range(10):  # 最多尝试10次
                    QApplication.restoreOverrideCursor()
                # 最后设置为默认光标
                QApplication.setOverrideCursor(Qt.ArrowCursor)
                QApplication.restoreOverrideCursor()
            except Exception as cursor_e:
                logger.debug(f"恢复光标状态时出错: {str(cursor_e)}")

        except RuntimeError as e:
            # 处理Qt对象已删除的情况
            if "wrapped C/C++ object" in str(e):
                logger.debug("恢复窗口状态时检测到Qt对象已删除")
            else:
                logger.error(f"恢复窗口状态失败: {str(e)}")
        except Exception as e:
            logger.error(f"恢复工具窗口状态时出错: {str(e)}")
