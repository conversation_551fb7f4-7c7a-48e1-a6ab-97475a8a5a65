#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
综合测试初始化修复
验证所有手动初始化控件和工具窗口功能正常
"""

import sys
import os
import json
import traceback
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from utils.Log import get_module_logger

logger = get_module_logger(__name__)
from core.services.register.RegisterManager import RegisterManager
from ui.handlers.ModernClkinControlHandler import ModernClkinControlHandler
from ui.handlers.ModernPLLHandler import ModernPLLHandler
from ui.handlers.ModernSyncSysRefHandler import ModernSyncSysRefHandler

def test_comprehensive_initialization():
    """综合测试初始化修复"""
    print("=== 综合测试初始化修复 ===")
    
    try:
        # 创建QApplication
        app = QApplication(sys.argv)
        
        print("1. 加载寄存器配置...")
        config_path = os.path.join('lib', 'register.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        print("2. 创建RegisterManager...")
        register_manager = RegisterManager(registers_config)
        
        # 测试所有现代化处理器
        handlers = []
        
        print("3. 测试现代化时钟输入控制处理器...")
        try:
            clkin_handler = ModernClkinControlHandler(
                parent=None,
                register_manager=register_manager
            )
            handlers.append(("ClkinControl", clkin_handler))
            print("   ✓ 现代化时钟输入控制处理器创建成功")
        except Exception as e:
            print(f"   ✗ 创建时钟输入控制处理器失败: {str(e)}")
            traceback.print_exc()
        
        print("4. 测试现代化PLL控制处理器...")
        try:
            pll_handler = ModernPLLHandler(
                parent=None,
                register_manager=register_manager
            )
            handlers.append(("PLLControl", pll_handler))
            print("   ✓ 现代化PLL控制处理器创建成功")
        except Exception as e:
            print(f"   ✗ 创建PLL控制处理器失败: {str(e)}")
            traceback.print_exc()

        print("5. 测试现代化同步系统参考处理器...")
        try:
            sync_handler = ModernSyncSysRefHandler(
                parent=None,
                register_manager=register_manager
            )
            handlers.append(("SyncSysref", sync_handler))
            print("   ✓ 现代化同步系统参考处理器创建成功")
        except Exception as e:
            print(f"   ✗ 创建同步系统参考处理器失败: {str(e)}")
            traceback.print_exc()
        
        print("6. 检查手动初始化控件...")
        for handler_name, handler in handlers:
            print(f"   检查 {handler_name} 处理器:")
            
            # 检查手动初始化的控件
            manual_controls = [
                "lineEditClkin0",
                "lineEditClkin1", 
                "lineEditClkin2Oscout",
                "lineEditClkinSelOut"
            ]
            
            for control_name in manual_controls:
                if hasattr(handler.ui, control_name):
                    control = getattr(handler.ui, control_name)
                    current_text = control.text()
                    print(f"     {control_name}: '{current_text}'")
                    
                    # 检查是否在映射表中
                    if hasattr(handler, 'widget_register_map') and control_name in handler.widget_register_map:
                        widget_info = handler.widget_register_map[control_name]
                        bit_def = widget_info.get("bit_def", {})
                        is_manual = bit_def.get("manually_initialized", False)
                        print(f"       - 在映射表中: ✓, 手动初始化: {'✓' if is_manual else '✗'}")
                    else:
                        print(f"       - 在映射表中: ✗")
        
        print("7. 检查分频比控件...")
        for handler_name, handler in handlers:
            if handler_name == "ClkinControl":
                print(f"   检查 {handler_name} 分频比控件:")
                divider_controls = ["PLL1R0Div", "PLL1R1Div", "PLL1R2Div"]
                for control_name in divider_controls:
                    if hasattr(handler.ui, control_name):
                        control = getattr(handler.ui, control_name)
                        current_value = control.value()
                        print(f"     {control_name}: {current_value}")
                    else:
                        print(f"     {control_name}: 控件不存在")
        
        print("8. 检查控件映射表...")
        for handler_name, handler in handlers:
            if hasattr(handler, 'widget_register_map'):
                mapping_count = len(handler.widget_register_map)
                print(f"   {handler_name}: {mapping_count} 个控件映射")
            else:
                print(f"   {handler_name}: 无控件映射表")
        
        print("9. 测试控件值变化...")
        for handler_name, handler in handlers:
            if handler_name == "ClkinControl":
                print(f"   测试 {handler_name} 控件值变化:")
                try:
                    # 测试checkbox控件
                    if hasattr(handler.ui, 'powerDown'):
                        checkbox = handler.ui.powerDown
                        original_state = checkbox.isChecked()
                        checkbox.setChecked(not original_state)
                        time.sleep(0.1)  # 等待信号处理
                        checkbox.setChecked(original_state)
                        print("     ✓ Checkbox控件测试成功")
                    
                    # 测试spinbox控件
                    if hasattr(handler.ui, 'PLL1R0Div'):
                        spinbox = handler.ui.PLL1R0Div
                        original_value = spinbox.value()
                        spinbox.setValue(100)
                        time.sleep(0.1)  # 等待信号处理
                        spinbox.setValue(original_value)
                        print("     ✓ SpinBox控件测试成功")
                        
                except Exception as e:
                    print(f"     ✗ 控件值变化测试失败: {str(e)}")
        
        print("\n✓ 综合测试完成，所有处理器创建成功，没有发现初始化错误")
        
        # 清理
        for handler_name, handler in handlers:
            try:
                if hasattr(handler, 'cleanup'):
                    handler.cleanup()
            except:
                pass
        
        return True
        
    except Exception as e:
        print(f"✗ 综合测试失败: {str(e)}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_comprehensive_initialization()
    sys.exit(0 if success else 1)
