#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
自动写入功能测试脚本
用于验证控件状态变化后是否能自动写入到芯片
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QTimer

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_auto_write_functionality():
    """测试自动写入功能"""
    try:
        # 导入必要的模块
        from core.services.config.ConfigurationService import ConfigurationService
        from PyQt5.QtCore import QSettings

        # 创建应用程序
        app = QApplication(sys.argv)

        # 创建一个简单的测试窗口来测试配置功能
        from PyQt5.QtWidgets import QMainWindow, QLabel, QVBoxLayout, QWidget, QPushButton

        class TestWindow(QMainWindow):
            def __init__(self):
                super().__init__()
                self.setWindowTitle("自动写入功能测试")
                self.resize(400, 300)

                # 创建配置服务
                self.config_service = ConfigurationService(self)

                # 创建UI
                central_widget = QWidget()
                self.setCentralWidget(central_widget)
                layout = QVBoxLayout(central_widget)

                # 显示当前设置
                self.status_label = QLabel()
                layout.addWidget(self.status_label)

                # 测试按钮
                test_btn = QPushButton("测试保存/加载自动写入设置")
                test_btn.clicked.connect(self.test_config)
                layout.addWidget(test_btn)

                self.update_status()

            def test_config(self):
                # 测试保存和加载
                self.config_service.save_auto_write_mode(True)
                loaded = self.config_service.load_auto_write_mode()
                self.status_label.setText(f"测试结果: 保存True，加载得到{loaded}")

            def update_status(self):
                current = self.config_service.load_auto_write_mode()
                self.status_label.setText(f"当前自动写入模式: {current}")

        test_window = TestWindow()

        # 显示测试窗口
        test_window.show()

        print("自动写入功能配置测试启动成功！")
        
        # 创建一个定时器来自动关闭程序（用于测试）
        def close_app():
            print("测试完成，关闭应用程序")
            test_window.close()
            app.quit()

        timer = QTimer()
        timer.timeout.connect(close_app)
        timer.start(10000)  # 10秒后关闭

        # 测试自动写入功能是否正确集成
        try:
            # 测试BaseHandler的自动写入检查
            from ui.handlers.BaseHandler import BaseClockHandler

            # 创建一个简单的测试处理器
            class TestHandler(BaseClockHandler):
                def __init__(self):
                    super().__init__()
                    # 模拟主窗口
                    self.main_window = type('MockMainWindow', (), {
                        'auto_write_mode': True,
                        'register_service': type('MockRegisterService', (), {
                            'write_register': lambda addr, value: print(f"模拟写入: {addr} = 0x{value:04X}")
                        })()
                    })()

                def test_auto_write_check(self):
                    return self._is_auto_write_enabled()

                def test_auto_write_execution(self, addr, value):
                    self._auto_write_register_to_chip(addr, value)

            test_handler = TestHandler()
            auto_write_enabled = test_handler.test_auto_write_check()
            print(f"自动写入功能检查结果: {auto_write_enabled}")

            if auto_write_enabled:
                test_handler.test_auto_write_execution("0x50", 0x1234)

        except Exception as e:
            print(f"测试自动写入功能时出错: {str(e)}")
            import traceback
            traceback.print_exc()

        # 显示测试信息
        QMessageBox.information(
            test_window,
            "自动写入功能测试",
            f"自动写入功能配置测试启动成功！\n\n"
            f"点击按钮测试配置的保存和加载功能。\n"
            f"这验证了自动写入功能的配置管理是否正常工作。\n\n"
            f"请查看控制台输出了解详细的测试结果。",
            QMessageBox.Ok
        )
        
        return app.exec_()
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(test_auto_write_functionality())
