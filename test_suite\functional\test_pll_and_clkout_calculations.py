#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试PLL和时钟输出的计算功能
验证频率计算是否正常工作
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication
from PyQt5.QtTest import QTest


def test_pll_calculations():
    """测试PLL频率计算"""
    print("=" * 60)
    print("测试PLL频率计算功能")
    print("=" * 60)
    
    try:
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建寄存器管理器
        from core.services.register.RegisterManager import RegisterManager
        config_path = os.path.join(project_root, 'lib', 'register.json')
        
        if not os.path.exists(config_path):
            print("❌ 寄存器配置文件不存在")
            return False

        import json
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        register_manager = RegisterManager(registers_config)
        
        print("1. 创建现代化PLL处理器...")
        from ui.handlers.ModernPLLHandler import ModernPLLHandler
        pll_handler = ModernPLLHandler(None, register_manager)
        
        print("✓ PLL处理器创建成功")
        
        print("\n2. 设置输入频率...")
        # 设置输入频率
        if hasattr(pll_handler.ui, "FreFin"):
            pll_handler.ui.FreFin.setText("122.88")
            print("  设置FreFin: 122.88 MHz")
        
        if hasattr(pll_handler.ui, "OSCinFreq"):
            pll_handler.ui.OSCinFreq.setText("122.88")
            print("  设置OSCinFreq: 122.88 MHz")
        
        print("\n3. 设置分频器值...")
        # 设置分频器值
        if hasattr(pll_handler.ui, "PLL1RDividerSetting"):
            pll_handler.ui.PLL1RDividerSetting.setValue(1)
            print("  设置PLL1 R分频器: 1")
        
        if hasattr(pll_handler.ui, "PLL2RDivider"):
            pll_handler.ui.PLL2RDivider.setValue(1)
            print("  设置PLL2 R分频器: 1")
        
        if hasattr(pll_handler.ui, "PLL2NDivider"):
            pll_handler.ui.PLL2NDivider.setValue(24)
            print("  设置PLL2 N分频器: 24")
        
        print("\n4. 执行频率计算...")
        pll_handler.calculate_output_frequencies()
        
        print("\n5. 检查计算结果...")
        # 检查PLL1 PFD频率
        if hasattr(pll_handler.ui, "PLL1PFDFreq"):
            pll1_freq = pll_handler.ui.PLL1PFDFreq.text()
            print(f"  PLL1 PFD频率: {pll1_freq} MHz")
            
            # 验证计算是否正确 (122.88 / 1 = 122.88)
            try:
                freq_val = float(pll1_freq)
                if abs(freq_val - 122.88) < 0.01:
                    print("  ✓ PLL1频率计算正确")
                else:
                    print(f"  ❌ PLL1频率计算错误，期望: 122.88")
            except ValueError:
                print(f"  ❌ PLL1频率格式错误: {pll1_freq}")
        
        # 检查PLL2 PFD频率
        if hasattr(pll_handler.ui, "PLL2PFDFreq"):
            pll2_freq = pll_handler.ui.PLL2PFDFreq.text()
            print(f"  PLL2 PFD频率: {pll2_freq} MHz")
            
            # 验证计算是否正确 (122.88 * 1 / 1 = 122.88)
            try:
                freq_val = float(pll2_freq)
                if abs(freq_val - 122.88) < 0.01:
                    print("  ✓ PLL2频率计算正确")
                else:
                    print(f"  ❌ PLL2频率计算错误，期望: 122.88")
            except ValueError:
                print(f"  ❌ PLL2频率格式错误: {pll2_freq}")
        
        # 检查Fin0频率
        if hasattr(pll_handler.ui, "Fin0Freq"):
            fin0_freq = pll_handler.ui.Fin0Freq.text()
            print(f"  Fin0频率: {fin0_freq} MHz")
            
            # 验证计算是否正确 (122.88 * 24 * 1 = 2949.12)
            try:
                freq_val = float(fin0_freq)
                expected = 122.88 * 24 * 1  # PFD * N * Prescaler
                if abs(freq_val - expected) < 1.0:
                    print("  ✓ Fin0频率计算正确")
                else:
                    print(f"  ❌ Fin0频率计算错误，期望约: {expected}")
            except ValueError:
                print(f"  ❌ Fin0频率格式错误: {fin0_freq}")
        
        return True
        
    except Exception as e:
        print(f"❌ PLL计算测试出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_clkout_calculations():
    """测试时钟输出计算"""
    print("\n" + "=" * 60)
    print("测试时钟输出频率计算功能")
    print("=" * 60)
    
    try:
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建寄存器管理器
        from core.services.register.RegisterManager import RegisterManager
        config_path = os.path.join(project_root, 'lib', 'register.json')
        
        import json
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        register_manager = RegisterManager(registers_config)
        
        print("1. 创建现代化时钟输出处理器...")
        from ui.handlers.ModernClkOutputsHandler import ModernClkOutputsHandler
        clkout_handler = ModernClkOutputsHandler(None, register_manager)
        
        print("✓ 时钟输出处理器创建成功")
        
        print("\n2. 设置VCO频率...")
        if hasattr(clkout_handler.ui, "lineEditFvco"):
            clkout_handler.ui.lineEditFvco.setText("2949.12")
            print("  设置VCO频率: 2949.12 MHz")
        
        print("\n3. 设置分频器值...")
        # 设置一些分频器值
        dividers = {
            "DCLK0_1DIV": 1,
            "DCLK2_3DIV": 2,
            "DCLK4_5DIV": 4,
            "DCLK6_7DIV": 8
        }
        
        for div_name, div_value in dividers.items():
            if hasattr(clkout_handler.ui, div_name):
                widget = getattr(clkout_handler.ui, div_name)
                if hasattr(widget, 'setValue'):
                    widget.setValue(div_value)
                    print(f"  设置{div_name}: {div_value}")
        
        print("\n4. 执行频率计算...")
        clkout_handler.calculate_output_frequencies()
        
        print("\n5. 检查计算结果...")
        # 检查前几个输出的频率
        expected_freqs = {
            0: 2949.12 / 1,  # CLKout0
            1: 2949.12 / 1,  # CLKout1
            2: 2949.12 / 2,  # CLKout2
            3: 2949.12 / 2,  # CLKout3
            4: 2949.12 / 4,  # CLKout4
            5: 2949.12 / 4,  # CLKout5
        }
        
        all_correct = True
        for output_num, expected_freq in expected_freqs.items():
            output_attr = f"lineEditFout{output_num}Output"
            if hasattr(clkout_handler.ui, output_attr):
                output_widget = getattr(clkout_handler.ui, output_attr)
                actual_freq_text = output_widget.text()
                print(f"  CLKout{output_num}频率: {actual_freq_text} MHz")
                
                try:
                    actual_freq = float(actual_freq_text)
                    if abs(actual_freq - expected_freq) < 0.1:
                        print(f"    ✓ 计算正确 (期望: {expected_freq:.2f})")
                    else:
                        print(f"    ❌ 计算错误 (期望: {expected_freq:.2f})")
                        all_correct = False
                except ValueError:
                    print(f"    ❌ 频率格式错误: {actual_freq_text}")
                    all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ 时钟输出计算测试出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("开始测试PLL和时钟输出计算功能...")
    
    # 测试PLL计算
    pll_success = test_pll_calculations()
    
    # 测试时钟输出计算
    clkout_success = test_clkout_calculations()
    
    print("\n" + "=" * 60)
    print("计算功能测试结果总结")
    print("=" * 60)
    
    if pll_success and clkout_success:
        print("🎉 所有计算功能测试通过！")
        print("\n📋 测试结果:")
        print("   - ✅ PLL频率计算正常")
        print("   - ✅ 时钟输出频率计算正常")
        print("   - ✅ 控件初始化完整")
        print("   - ✅ 数值显示正确")
        print("\n🎯 结论:")
        print("   现代化PLL和时钟输出处理器的")
        print("   计算功能已完全修复！")
        print("   用户界面应该能正常显示")
        print("   所有频率计算结果。")
        return True
    else:
        print("❌ 部分计算功能测试失败")
        if not pll_success:
            print("   - ❌ PLL频率计算有问题")
        if not clkout_success:
            print("   - ❌ 时钟输出频率计算有问题")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
