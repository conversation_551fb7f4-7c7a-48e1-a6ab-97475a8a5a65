# INTEGRATION 测试

## 描述
集成测试 - 测试模块间的集成

## 测试文件
- `test_modern_architecture.py`
- `test_modern_handlers.py`
- `test_modern_factory.py`
- `test_stage3_refactor.py`
- `test_final_refactoring.py`
- `final_refactor_verification.py`
- `complete_refactor_verification.py`
- `test_app_after_removal.py`

## 运行测试
```bash
# 运行该分类的所有测试
python test_suite/run_all_tests.py --category integration

# 运行特定测试文件
python test_suite/integration/test_specific_file.py
```

## 注意事项
- 确保测试环境已正确设置
- 某些测试可能需要Qt环境
- 性能测试可能需要较长时间
