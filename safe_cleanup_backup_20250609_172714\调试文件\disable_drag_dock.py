#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
禁用拖拽停靠功能的脚本

这个脚本会恢复默认配置，让插件窗口以标签页集成模式显示。
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from core.services.config.ConfigurationManager import set_config, get_config, config_manager

def main():
    """禁用拖拽停靠功能，恢复默认模式"""
    print("🔄 禁用拖拽停靠功能")
    print("=" * 50)
    
    try:
        # 检查当前状态
        current_status = get_config('plugins.force_floating_mode', False)
        print(f"\n📊 当前状态：")
        print(f"   拖拽停靠功能: {'✅ 已启用' if current_status else '❌ 已禁用'}")
        print(f"   插件显示模式: {'悬浮窗口' if current_status else '标签页集成'}")
        
        if not current_status:
            print("\n⚠️  拖拽停靠功能已经禁用，无需重复操作")
            return
        
        print("\n🔧 正在禁用拖拽停靠功能...")
        
        # 禁用强制悬浮模式
        set_config('plugins.force_floating_mode', False)
        print("   ✅ 已禁用强制悬浮模式")
        
        # 保存配置到本地配置文件
        try:
            config_manager.save_to_file('config/local.json')
            print("   ✅ 配置已保存到 config/local.json")
        except Exception as save_error:
            print(f"   ⚠️  保存配置文件失败: {str(save_error)}")
            print("   ℹ️  配置仍在内存中生效，重启应用后需要重新设置")
        
        print("\n🎉 已恢复默认模式！")
        print("\n📋 当前配置：")
        print("   - 插件窗口将以标签页形式集成到主界面")
        print("   - 不支持拖拽停靠功能")
        print("   - 这是应用程序的默认行为")
        
        print("\n🔄 如需重新启用拖拽停靠功能，请运行：")
        print("   python auto_enable_drag_dock.py")
        
    except Exception as e:
        print(f"\n❌ 禁用失败: {str(e)}")
        print("\n🔍 可能的解决方案：")
        print("   1. 确保配置文件权限正确")
        print("   2. 检查 config 目录是否存在")
        print("   3. 尝试以管理员权限运行")
        return False
    
    return True

if __name__ == "__main__":
    main()