#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
现代化的PLL处理器
使用ModernBaseHandler作为基类，重构自原PLLHandler
主要功能：PLL1和PLL2的配置管理、频率计算、时钟源管理
"""

from PyQt5 import QtCore
from ui.handlers.ModernBaseHandler import ModernBaseHandler
from ui.forms.Ui_PLL1_2 import Ui_PLL1_2
from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class ModernPLLHandler(ModernBaseHandler):
    """现代化的PLL处理器"""

    # 添加窗口关闭信号
    window_closed = QtCore.pyqtSignal()

    def __init__(self, parent=None, register_manager=None, main_window=None, **kwargs):
        """初始化现代化PLL处理器

        Args:
            parent: 父窗口
            register_manager: RegisterManager实例
            main_window: 主窗口引用（用于避免控件变化时的引用问题）
            **kwargs: 其他参数（如register_repo等，用于兼容性）
        """
        super().__init__(parent, register_manager, **kwargs)

        # 立即设置主窗口引用（在任何可能触发控件变化的操作之前）
        if main_window:
            self.main_window = main_window
            logger.info(f"ModernPLLHandler: 已在构造函数中设置主窗口引用: {type(main_window)}")

        # 设置窗口标题
        self.setWindowTitle("PLL配置 (现代化版本)")

        # 创建UI实例
        self.ui = Ui_PLL1_2()
        self.ui.setupUi(self.content_widget)

        # 初始化PLL特定配置
        self._init_pll_config()

        # 手动调用初始化（因为测试环境没有事件循环）
        self._post_init()

        # 在所有初始化完成后，进行初始频率计算
        self._perform_initial_calculations()

        # 确保内容完全可见
        QtCore.QTimer.singleShot(200, self.ensure_content_fully_visible)

        logger.info("现代化PLL处理器初始化完成")



    def _init_pll_config(self):
        """初始化PLL特定配置"""
        try:
            # 初始化时钟源配置（参考传统PLL处理器）
            self._init_clock_source_config()

            # 设置当前时钟源
            self.current_clock_source = "ClkIn0"

            # 初始化UI默认值
            self._init_ui_defaults()

            # 设置控件范围
            self._setup_widget_ranges()

            # 注册跨寄存器控件（参考传统PLL处理器）
            self._register_cross_register_controls()

            # 连接特殊信号
            self._connect_special_signals()

            logger.info("PLL特定配置初始化完成")

        except Exception as e:
            logger.error(f"初始化PLL配置时出错: {str(e)}")

    def _init_clock_source_config(self):
        """初始化时钟源配置（参考传统PLL处理器）"""
        try:
            # 预先初始化空的本地配置
            self.clkin_frequencies = {}
            self.clkin_divider_values = {}

            # 先用默认值初始化
            default_frequencies = {
                "ClkIn0": 122.88,
                "ClkIn1": 245.76,
                "ClkIn2": 0,
                "ClkIn3": 0
            }

            default_dividers = {
                "ClkIn0": 120,  # 参考传统实现的默认值
                "ClkIn1": 120,
                "ClkIn2": 120,
                "ClkIn3": 120
            }

            # 尝试从RegisterUpdateBus获取时钟源配置
            try:
                from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
                clock_bus = RegisterUpdateBus.instance()

                # 获取RegisterUpdateBus中存储的时钟源配置
                for source in ["ClkIn0", "ClkIn1", "ClkIn2", "ClkIn3"]:
                    freq = clock_bus.get_clock_frequency(source)
                    divider = clock_bus.get_clock_divider(source)

                    # 只有在有配置的情况下才更新
                    if freq > 0:
                        self.clkin_frequencies[source] = freq
                    else:
                        self.clkin_frequencies[source] = default_frequencies.get(source, 0)

                    if divider > 0:
                        self.clkin_divider_values[source] = divider
                    else:
                        self.clkin_divider_values[source] = default_dividers.get(source, 120)

                # 获取当前时钟源
                current_source = clock_bus.get_current_clock_source()
                if current_source:
                    # 规范化时钟源名称
                    if current_source.startswith("CLK"):
                        self.current_clock_source = "ClkIn" + current_source[5:]
                    else:
                        self.current_clock_source = current_source

                logger.info(f"从RegisterUpdateBus获取时钟源配置: {self.current_clock_source}")

            except Exception as e:
                logger.warning(f"无法从RegisterUpdateBus获取时钟源配置，使用默认值: {str(e)}")
                # 使用默认配置
                self.clkin_frequencies = default_frequencies.copy()
                self.clkin_divider_values = default_dividers.copy()

            logger.info(f"时钟源配置初始化完成: 频率={self.clkin_frequencies}, 分频={self.clkin_divider_values}")

        except Exception as e:
            logger.error(f"初始化时钟源配置时出错: {str(e)}")
            # 使用最基本的默认配置
            self.clkin_frequencies = {"ClkIn0": 122.88, "ClkIn1": 245.76, "ClkIn2": 0, "ClkIn3": 0}
            self.clkin_divider_values = {"ClkIn0": 120, "ClkIn1": 120, "ClkIn2": 120, "ClkIn3": 120}

    def _init_ui_defaults(self):
        """初始化UI默认值"""
        try:
            # 定义控件选项映射字典
            self.combobox_options_map = {
                "comboPLL1WindSize": {
                    0: "4ns",
                    1: "9ns",
                    2: "19ns",
                    3: "43ns"
                },
                "PLL2WINDSIZE": {
                    0: "Reserved",
                    1: "1ns",
                    2: "18ns",
                    3: "26ns"
                },
                "PLL1CPState": {
                    0: "Active",
                    1: "Tristate",
                },
                "PLL2CPState": {
                    0: "Active",
                    1: "Tristate",
                },
                "PLL1PFDPolarity": {
                    0: "Negative",
                    1: "Positive"
                },
                "PLL2PFDPolarity": {
                    0: "Negative",
                    1: "Positive"
                },
                "PLL1CPGain": {i: f"{50 + i*100}μA" for i in range(16)},
                "PLL2CPGain": {
                    0: "Reserved",
                    1: "Reserved",
                    2: "1600uA",
                    3: "3200uA"
                },
                "PLL2R3": {
                    0: "2.4KOhm",
                    1: "0.2KOhm",
                    2: "0.5KOhm",
                    4: "1.1KOhm"
                },
                "PLL2C1": {
                    0: "10pF",
                    1: "20pF",
                    2: "40pF",
                },
                "PLL2C3": {
                    0: "10pF",
                    1: "20pF",
                    2: "40pF",
                },
                "PLL1NclkMux": {
                    0: "OSCin",
                    1: "Feedback Mux",
                    2: "PLL2 Prescaler"
                },
                "PLL2NclkMux": {
                    0: "PLL2 Prescaler",
                    1: "Feedback Mux",
                },
                "PLL2RclkMux": {
                    0: "OSCin",
                    1: "PLL1 CLKinX",
                },
                "PLL2Prescaler": {
                    0: "8",
                    1: "1",
                    2: "2",
                    3: "3",
                    4: "4",
                    5: "5",
                    6: "6",
                    7: "7",
                },
                "FBMUX": {
                    0: "CLKout6",
                    1: "CLKout8",
                    2: "SYSREF Divider",
                    4: "External",
                },
                "comboVcoMode": {
                    0: "VCO 0",
                    1: "VCO 1",
                    2: "CLKin1",
                    3: "Fin0",
                },
                "Doubler": {
                    0: "1×",
                    1: "2×",
                },
                "Fin0InputType": {
                    0: "Diff Input",
                    1: "Single Ended Input(Fin)",
                    2: "Single Ended Input(Fin*)",
                    3: "Reserved",
                },
                "OSCin_FREQ": {
                    0: "0-63MHz,not valid",
                    1: "div1 -> 63-127MHz",
                    2: "div2 -> 127-255MHz",
                    3: "div3 -> Reserved",
                    4: "div4 -> 255-500MHz",
                    5: "div5 -> Reserved",
                    6: "div6 -> Reserved",
                    7: "div7 -> Reserved",
                },
                "DACClkMult": {
                    0: "4",
                    1: "64",
                    2: "1024",
                    3: "16384",
                },
                "RGHOExitDacassistStep": {
                    0: "slowest",
                    1: "slow",
                    2: "fast",
                    3: "fastest",
                },
                "FCALM1": {i: str(i) for i in range(256)},
                "FCALM2": {i: str(i) for i in range(16384)}
            }

            # 初始化ComboBox控件
            self._init_combobox_controls()

            # 设置默认频率值
            if hasattr(self.ui, "OSCinFreq"):
                self.ui.OSCinFreq.setText('122.88')
            if hasattr(self.ui, "ExternalVCXOFreq"):
                self.ui.ExternalVCXOFreq.setText('122.88')
            if hasattr(self.ui, "FreFin"):
                self.ui.FreFin.setText(str(self.clkin_frequencies[self.current_clock_source]))

            logger.info("UI默认值初始化完成")

        except Exception as e:
            logger.error(f"初始化UI默认值时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _init_combobox_controls(self):
        """初始化ComboBox控件"""
        try:
            from PyQt5.QtWidgets import QComboBox

            # 为所有ComboBox控件设置选项
            for widget_name, options in self.combobox_options_map.items():
                if hasattr(self.ui, widget_name):
                    combo_box = getattr(self.ui, widget_name)
                    if isinstance(combo_box, QComboBox):
                        # 清空并设置选项
                        combo_box.clear()

                        # 特殊处理PLL2R3控件
                        if widget_name == "PLL2R3":
                            # PLL2R3的值映射: [0, 1, 2, 4]
                            values = [0, 1, 2, 4]  # 实际寄存器值
                            texts = ["2.4KOhm", "0.2KOhm", "0.5KOhm", "1.1KOhm"]  # 显示文本
                            for i, (value, text) in enumerate(zip(values, texts)):
                                combo_box.addItem(text, value)  # 设置实际值为userData
                            logger.info(f"已为 {widget_name} 设置特殊映射: {values} -> {texts}")
                        else:
                            # 常规ComboBox处理
                            for value, text in sorted(options.items()):
                                combo_box.addItem(text, value)  # 存储实际值为userData
                            logger.info(f"已为 {widget_name} 设置选项映射")

                        # 设置默认选中项
                        self._set_combobox_default_value(combo_box, widget_name)

            logger.info("ComboBox控件初始化完成")

            # 应用之前延迟设置的ComboBox值
            if hasattr(self, 'apply_pending_combobox_values'):
                self.apply_pending_combobox_values()

        except Exception as e:
            logger.error(f"初始化ComboBox控件时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _set_combobox_default_value(self, combo_box, widget_name):
        """设置ComboBox的默认值，优先从RegisterManager获取。"""
        try:
            default_index = 0  # 默认索引为0
            initialized_from_reg_manager = False
            widget_info = self.widget_register_map.get(widget_name)

            # 1. 尝试从RegisterManager获取值
            if widget_info and self.register_manager:
                reg_addr = widget_info.get("register_addr")
                bit_def_name = widget_info.get("bit_def_name") # 假设bit_def_name存储了位字段的名称

                if reg_addr and bit_def_name:
                    try:
                        reg_val = self.register_manager.get_bit_field_value(reg_addr, bit_def_name)
                        if reg_val is not None:
                            found_index = -1
                            for i in range(combo_box.count()):
                                item_data = combo_box.itemData(i)
                                if item_data == reg_val: # 直接比较，假设itemData和reg_val类型一致
                                    found_index = i
                                    break
                            
                            if found_index != -1:
                                default_index = found_index
                                initialized_from_reg_manager = True
                                logger.info(f"为 {widget_name} 从RegisterManager设置索引: {default_index} (reg_addr={reg_addr}, bit='{bit_def_name}', val={reg_val})")
                            else:
                                logger.warning(f"RegisterManager中的值 {reg_val} (来自 {reg_addr} [{bit_def_name}]) 在ComboBox {widget_name} 中未找到对应项。将尝试其他默认值。")
                        else:
                            logger.info(f"RegisterManager中未找到 {widget_name} ({reg_addr} [{bit_def_name}]) 的值。将尝试其他默认值。")
                    except Exception as e_reg:
                        logger.warning(f"从RegisterManager获取 {widget_name} ({reg_addr} [{bit_def_name}]) 值失败: {e_reg}。将尝试其他默认值。")
                else:
                    logger.debug(f"{widget_name} 在 widget_register_map 中缺少 register_addr 或 bit_def_name，无法从RegisterManager获取。")
            else:
                logger.debug(f"RegisterManager未初始化或 {widget_name} 未在 widget_register_map 中，无法从RegisterManager获取。")

            # 2. 如果未从RegisterManager初始化，则使用其他默认逻辑
            if not initialized_from_reg_manager:
                # 特殊处理PLL2C1和PLL2C3控件 (仅当未从寄存器管理器初始化时)
                if widget_name in ["PLL2C1", "PLL2C3"]:
                    default_index = 2  # 选择40pF选项
                    logger.info(f"为 {widget_name} 设置特殊默认值: 索引2 (40pF) 因为未从RegisterManager初始化")
                else:
                    # 定义已知控件的默认值映射（基于寄存器配置文件）
                    known_defaults = {
                        "comboPLL1WindSize": 3,      # 寄存器0x6B位7:6，默认值"11"=3
                        "PLL2WINDSIZE": 2,           # 寄存器0x79位6:5，默认值"10"=2
                        "PLL1CPState": 0,            # 寄存器0x6B位5，默认值"0"=0
                        "PLL2CPState": 0,            # 假设默认值为0
                        "PLL1PFDPolarity": 1,        # 寄存器0x6B位4，默认值"1"=1
                        "PLL2PFDPolarity": 0,        # 假设默认值为0
                        "PLL1CPGain": 0,             # 假设默认值为0
                        "PLL2CPGain": 2,             # 假设默认值为2
                        "PLL1NclkMux": 0,            # 假设默认值为0
                        "PLL2NclkMux": 0,            # 假设默认值为0
                        "PLL2RclkMux": 0,            # 假设默认值为0
                        "PLL2Prescaler": 0,          # 假设默认值为0
                        "FBMUX": 0,                  # 假设默认值为0
                        "comboVcoMode": 0,           # 假设默认值为0
                        "Doubler": 0,                # 假设默认值为0
                        "Fin0InputType": 0,          # 假设默认值为0
                        "OSCinFreq": 0,                 # 假设默认值为0
                        "DACClkMult": 0,             # 假设默认值为0
                        "RGHOExitDacassistStep": 0,  # 假设默认值为0
                    }

                    default_value_to_find = None
                    source_of_default = ""

                    if widget_info and widget_info.get("default_value") is not None:
                        default_value_str_orig = widget_info.get("default_value")
                        default_value_str = default_value_str_orig.strip()
                        parsed_successfully = False
                        temp_val = 0
                        if default_value_str:
                            if all(c in '01' for c in default_value_str):
                                try:
                                    temp_val = int(default_value_str, 2)
                                    parsed_successfully = True
                                except ValueError:
                                    pass # Fall through
                            if not parsed_successfully and default_value_str.isdigit():
                                try:
                                    temp_val = int(default_value_str)
                                    parsed_successfully = True
                                except ValueError:
                                    pass # Fall through
                        if parsed_successfully:
                            default_value_to_find = temp_val
                            source_of_default = "widget_register_map"
                        else:
                            if default_value_str:
                                logger.warning(f"无法将 {widget_name} 的 widget_map default_value '{default_value_str_orig}' 解析为整数。尝试known_defaults。")
                            # Fall through to known_defaults if parsing failed or no default_value in map
                    
                    if default_value_to_find is None and widget_name in known_defaults:
                        default_value_to_find = known_defaults[widget_name]
                        source_of_default = "known_defaults"
                    
                    if default_value_to_find is not None:
                        found_idx = -1
                        for i in range(combo_box.count()):
                            item_data = combo_box.itemData(i)
                            if item_data == default_value_to_find:
                                found_idx = i
                                break
                        if found_idx != -1:
                            default_index = found_idx
                            logger.info(f"为 {widget_name} 使用来自 {source_of_default} 的值 {default_value_to_find} 设置默认索引: {default_index}")
                        else:
                            logger.warning(f"ComboBox {widget_name} 中未找到来自 {source_of_default} 的数据: {default_value_to_find}，将使用索引0")
                            default_index = 0 # Fallback
                    else:
                        logger.info(f"ComboBox {widget_name} 未在widget_map或known_defaults中指定有效默认值，将使用索引0")
                        default_index = 0 # Default to 0

            combo_box.blockSignals(True)
            if 0 <= default_index < combo_box.count():
                combo_box.setCurrentIndex(default_index)
            elif combo_box.count() > 0:
                logger.warning(f"计算出的 default_index {default_index} 超出 {widget_name} 的范围 [0, {combo_box.count()-1}]，强制设为0")
                combo_box.setCurrentIndex(0)
            else:
                logger.warning(f"{widget_name} 为空，无法设置索引。")
            combo_box.blockSignals(False)

            # 更新内部跟踪的值
            if hasattr(self, 'widget_values') and combo_box.count() > 0:
                current_data = combo_box.itemData(combo_box.currentIndex()) # 使用实际设置的currentIndex
                self.widget_values[widget_name] = current_data
                logger.debug(f"{widget_name} 初始值设置为: {current_data} (索引: {combo_box.currentIndex()})")

        except Exception as e:
            logger.error(f"设置 {widget_name} 默认值时出错: {str(e)}")
            if combo_box.count() > 0:
                combo_box.blockSignals(True)
                combo_box.setCurrentIndex(0)
                combo_box.blockSignals(False)

    def _setup_widget_ranges(self):
        """设置控件范围"""
        try:
            # 设置PLL2NDivider控件范围
            if hasattr(self.ui, "PLL2NDivider"):
                self.ui.PLL2NDivider.setMinimum(2)
                self.ui.PLL2NDivider.setMaximum(262143)  # 最大值为2^18-1
                logger.info("已设置PLL2NDivider控件范围: 2-262143")

            # 设置PLL1RDividerSetting的最大值
            if hasattr(self.ui, "PLL1RDividerSetting"):
                self.ui.PLL1RDividerSetting.setMaximum(16383)
                logger.info("已设置PLL1RDividerSetting控件最大值: 16383")

            if hasattr(self.ui, "spinBoxPLL1DLDCNT"):
                self.ui.spinBoxPLL1DLDCNT.setMinimum(0)
                self.ui.spinBoxPLL1DLDCNT.setMaximum(16383)
                logger.info("已设置spinBoxPLL1DLDCNT控件范围: 0-16383")

            if hasattr(self.ui, "spinBoxPLL2DLDCNT"):
                self.ui.spinBoxPLL2DLDCNT.setMinimum(0)
                self.ui.spinBoxPLL2DLDCNT.setMaximum(16383)
                logger.info("已设置spinBoxPLL2DLDCNT控件范围: 0-16383")

        except Exception as e:
            logger.error(f"设置控件范围时出错: {str(e)}")

    def _connect_special_signals(self):
        """连接特殊信号"""
        try:
            # 连接OSCinFreq的textChanged信号到频率计算
            if hasattr(self.ui, "OSCinFreq"):
                self.ui.OSCinFreq.textChanged.connect(self.calculate_output_frequencies)
                logger.info("已连接 OSCinFreq textChanged 信号")

            # 连接时钟源选择信号
            RegisterUpdateBus.instance().clock_source_selected.connect(self.on_global_clock_source_selected)
            logger.info("已连接时钟源选择信号")

            # 连接模式变化信号
            RegisterUpdateBus.instance().mode_changed.connect(self.on_mode_changed)
            logger.info("已连接模式变化信号")

        except Exception as e:
            logger.error(f"连接特殊信号时出错: {str(e)}")

    def _perform_initial_calculations(self):
        """执行初始计算，确保所有频率显示控件都有正确的初始值，优先从RegisterManager获取。"""
        try:
            logger.info("开始执行初始频率计算...")

            # 1. 初始化 FreFin (基于当前时钟源，这个逻辑不变，但确保在后续控件初始化后仍然正确)
            if hasattr(self.ui, "FreFin"):
                current_fin_freq = self.clkin_frequencies.get(self.current_clock_source, 122.88) # 默认值
                self.ui.FreFin.setText(str(current_fin_freq))
                logger.info(f"设置FreFin为: {current_fin_freq} (基于当前时钟源: {self.current_clock_source})")

            # 2. 初始化 PLL1RDividerSetting
            # PLL1RDividerSetting 的值依赖于当前时钟源，其寄存器地址也动态变化
            # on_global_clock_source_selected 会处理其值的更新和寄存器映射的更新
            # 这里我们尝试根据当前已知的映射（如果存在）从RegisterManager读取一次
            # 如果读取不到，on_global_clock_source_selected 稍后会根据选择的时钟源设置一个默认值
            pll1r_widget_name = "PLL1RDividerSetting"
            if hasattr(self.ui, pll1r_widget_name) and self.register_manager:
                widget_info = self.widget_register_map.get(pll1r_widget_name)
                default_pll1r_divider = self.clkin_divider_values.get(self.current_clock_source, 120)
                final_pll1r_value = default_pll1r_divider
                initialized_from_reg = False

                if widget_info:
                    reg_addr = widget_info.get("register_addr") # 当前映射的寄存器地址
                    bit_def_name = widget_info.get("bit_def_name") # 通常是组合的名称
                    # 对于PLL1RDividerSetting，bit_def_name可能需要从其 "bits" 字段获取第一个
                    if "bit_def" in widget_info and "bits" in widget_info["bit_def"] and widget_info["bit_def"]["bits"]:
                        bit_def_name = widget_info["bit_def"]["bits"][0]
                    
                    if reg_addr and bit_def_name:
                        try:
                            val = self.register_manager.get_bit_field_value(reg_addr, bit_def_name)
                            if val is not None:
                                final_pll1r_value = int(val)
                                initialized_from_reg = True
                                logger.info(f"{pll1r_widget_name} 从RegisterManager获取值: {final_pll1r_value} (reg={reg_addr}, bit='{bit_def_name}')")
                            else:
                                logger.info(f"RegisterManager中未找到 {pll1r_widget_name} ({reg_addr} [{bit_def_name}]) 的值，将使用时钟源默认值: {default_pll1r_divider}")
                        except Exception as e_reg:
                            logger.warning(f"从RegisterManager获取 {pll1r_widget_name} 值失败: {e_reg}，将使用时钟源默认值: {default_pll1r_divider}")
                    else:
                        logger.info(f"{pll1r_widget_name} 在 widget_register_map 中缺少 reg_addr 或 bit_def_name，将使用时钟源默认值: {default_pll1r_divider}")
                else:
                    logger.info(f"{pll1r_widget_name} 未在 widget_register_map 中定义，将使用时钟源默认值: {default_pll1r_divider}")
                
                # 确保值在控件范围内
                min_val = self.ui.PLL1RDividerSetting.minimum()
                max_val = self.ui.PLL1RDividerSetting.maximum()
                if not (min_val <= final_pll1r_value <= max_val):
                    logger.warning(f"{pll1r_widget_name} 值 {final_pll1r_value} 超出范围 [{min_val}-{max_val}]，修正为默认值 {default_pll1r_divider}")
                    final_pll1r_value = default_pll1r_divider # 或修正到最近的有效值
                    if final_pll1r_value < min_val: final_pll1r_value = min_val
                    if final_pll1r_value > max_val: final_pll1r_value = max_val
                    initialized_from_reg = False # 因为值被修正了

                self.ui.PLL1RDividerSetting.setValue(final_pll1r_value)
                logger.info(f"设置 {pll1r_widget_name} 为: {final_pll1r_value}{' (来自寄存器)' if initialized_from_reg else ' (默认/修正值)'}")
            elif hasattr(self.ui, pll1r_widget_name):
                # Fallback if register_manager is not available or widget not in map yet
                current_divider = self.clkin_divider_values.get(self.current_clock_source, 120)
                self.ui.PLL1RDividerSetting.setValue(current_divider)
                logger.info(f"设置 {pll1r_widget_name} 为时钟源默认值: {current_divider}")

            # 3. 初始化 PLL2NDivider (已由 _initialize_pll2n_divider 处理，这里可以移除或保留一个简单的范围检查)
            # _initialize_pll2n_divider 应该在 _register_cross_register_controls 中被调用，那里会进行初始化
            # 此处保留一个最小值检查，以防 _initialize_pll2n_divider 未能正确设置
            if hasattr(self.ui, "PLL2NDivider"):
                if self.ui.PLL2NDivider.value() < self.ui.PLL2NDivider.minimum(): # 假设最小值是2
                    logger.warning(f"PLL2NDivider 值 {self.ui.PLL2NDivider.value()} 小于其最小值，可能未正确初始化。尝试设置为最小值。")
                    # self.ui.PLL2NDivider.setValue(self.ui.PLL2NDivider.minimum()) # 或者一个合理的默认值
                    # _initialize_pll2n_divider 应该已经处理了这个问题
                    pass # 依赖 _initialize_pll2n_divider

            # 4. 初始化 PLL2RDivider
            pll2r_widget_name = "PLL2RDivider"
            if hasattr(self.ui, pll2r_widget_name):
                default_pll2r_val = 1 # 默认值
                min_val_pll2r, max_val_pll2r = 1, 127 # 假设范围
                final_pll2r_value = default_pll2r_val
                initialized_pll2r_from_reg = False

                if self.widget_register_map and pll2r_widget_name in self.widget_register_map:
                    widget_info = self.widget_register_map[pll2r_widget_name]
                    if "default_value" in widget_info:
                        try: default_pll2r_val = int(widget_info["default_value"],2) # 假设二进制
                        except: pass
                    if "bit_def" in widget_info and "options" in widget_info["bit_def"]:
                        try:
                            min_s, max_s = widget_info["bit_def"]["options"].split(':')
                            min_val_pll2r, max_val_pll2r = int(min_s), int(max_s)
                        except: pass
                    final_pll2r_value = default_pll2r_val
                
                if self.register_manager and self.widget_register_map.get(pll2r_widget_name):
                    reg_addr = self.widget_register_map[pll2r_widget_name].get("register_addr")
                    bit_def_name = self.widget_register_map[pll2r_widget_name].get("bit_def_name")
                    if reg_addr and bit_def_name:
                        try:
                            val = self.register_manager.get_bit_field_value(reg_addr, bit_def_name)
                            if val is not None:
                                final_pll2r_value = int(val)
                                initialized_pll2r_from_reg = True
                                logger.info(f"{pll2r_widget_name} 从RegisterManager获取值: {final_pll2r_value}")
                            else:
                                logger.info(f"RegisterManager中未找到 {pll2r_widget_name}，使用默认值: {default_pll2r_val}")
                                final_pll2r_value = default_pll2r_val
                        except Exception as e_reg_pll2r:
                            logger.warning(f"从RegisterManager获取 {pll2r_widget_name} 失败: {e_reg_pll2r}，使用默认值: {default_pll2r_val}")
                            final_pll2r_value = default_pll2r_val
                
                if not (min_val_pll2r <= final_pll2r_value <= max_val_pll2r):
                    logger.warning(f"{pll2r_widget_name} 值 {final_pll2r_value} 超出范围 [{min_val_pll2r}-{max_val_pll2r}]，修正为 {default_pll2r_val}")
                    final_pll2r_value = default_pll2r_val
                    if final_pll2r_value < min_val_pll2r: final_pll2r_value = min_val_pll2r
                    if final_pll2r_value > max_val_pll2r: final_pll2r_value = max_val_pll2r
                    initialized_pll2r_from_reg = False

                self.ui.PLL2RDivider.setValue(final_pll2r_value)
                logger.info(f"设置 {pll2r_widget_name} 为: {final_pll2r_value}{' (来自寄存器)' if initialized_pll2r_from_reg else ' (默认/修正值)'}")

            # 5. 初始化PLL2 Calibration控件的默认值 (这个方法内部已经处理了从RegisterManager读取)
            # self._init_pll2_calibration_defaults()

            # 6. 执行频率计算 (这个会基于当前UI控件的值来计算)
            self.calculate_output_frequencies()

            logger.info("初始频率计算完成")

        except Exception as e:
            logger.error(f"执行初始计算时出错: {str(e)}")
            import traceback
            traceback.print_exc()



    def _register_cross_register_controls(self):
        """注册跨寄存器控件（参考传统PLL处理器的实现）"""
        try:
            # 注册PLL2NDivider为跨寄存器控件
            if hasattr(self.ui, "PLL2NDivider"):
                success = self._register_cross_register_control(
                    widget_name="PLL2NDivider",
                    bit_fields=["PLL2_N[17:16]", "PLL2_N[15:0]"],
                    reg_addrs=["0x76", "0x77"],
                    default_value="000000000000000000",
                    widget_type="spinbox",
                    options="2:262143"
                )
                if success:
                    logger.info("已注册PLL2NDivider为跨寄存器控件")
                    # 初始化PLL2NDivider控件值
                    self._initialize_pll2n_divider()
                else:
                    logger.error("注册PLL2NDivider跨寄存器控件失败")

            # 注册PLL1RDividerSetting控件（动态映射控件）
            if hasattr(self.ui, "PLL1RDividerSetting"):
                # PLL1RDividerSetting是一个动态控件，根据当前时钟源映射到不同寄存器
                # 默认映射到ClkIn0对应的寄存器0x63
                success = self._register_cross_register_control(
                    widget_name="PLL1RDividerSetting",
                    bit_fields=["CLKin0_R[13:0]"],
                    reg_addrs=["0x63"],
                    default_value="00000001111000",
                    widget_type="spinbox",
                    options="1:16383"
                )
                if success:
                    logger.info("已注册PLL1RDividerSetting为动态映射控件")
                    # 设置控件范围
                    self.ui.PLL1RDividerSetting.setMinimum(1)
                    self.ui.PLL1RDividerSetting.setMaximum(16383)
                    logger.info("已设置PLL1RDividerSetting控件范围")
                else:
                    logger.error("注册PLL1RDividerSetting控件失败")

            logger.info("跨寄存器控件注册完成")

        except Exception as e:
            logger.error(f"注册跨寄存器控件时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _register_cross_register_control(self, widget_name, bit_fields, reg_addrs, default_value, widget_type="spinbox", options="0:65535"):
        """注册跨寄存器控件（参考BaseHandler的实现）

        Args:
            widget_name: 控件名称
            bit_fields: 关联的位字段列表，从高位到低位排序
            reg_addrs: 关联的寄存器地址列表，从高位到低位排序
            default_value: 默认值
            widget_type: 控件类型
            options: 选项范围
        """
        try:
            if not hasattr(self.ui, widget_name):
                logger.warning(f"跨寄存器控件 {widget_name} 不存在")
                return False

            # 如果控件已在映射表中，则跳过
            if widget_name in self.widget_register_map:
                logger.info(f"跨寄存器控件 {widget_name} 已在映射表中")
                return True

            # 确保bit_fields和reg_addrs长度一致
            if len(bit_fields) != len(reg_addrs):
                logger.error("跨寄存器控件错误: bit_fields和reg_addrs长度不一致")
                return False

            # 创建映射条目
            self.widget_register_map[widget_name] = {
                "register_addr": reg_addrs[0],  # 使用第一个寄存器地址作为主地址
                "widget_type": widget_type,
                "default_value": default_value,
                "bit_def": {
                    "options": options,
                    "default": default_value,
                    "name": "_".join(bit_fields),  # 组合名称
                    "is_cross_register": True,     # 特殊标记，表示跨寄存器字段
                    "bits": bit_fields,            # 存储相关联的寄存器位字段
                    "registers": reg_addrs         # 存储相关联的寄存器地址
                }
            }

            logger.info(f"已注册跨寄存器控件 {widget_name}, 关联寄存器: {', '.join(reg_addrs)}")
            return True

        except Exception as e:
            logger.error(f"注册跨寄存器控件 {widget_name} 时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def _initialize_pll2n_divider(self):
        """特殊初始化PLL2NDivider控件，优先从RegisterManager获取值"""
        try:
            widget_name = "PLL2NDivider"
            if not hasattr(self.ui, widget_name):
                logger.warning(f"控件 {widget_name} 不存在，跳过初始化")
                return

            # 默认值，参考widget_register_map或控件自身定义
            # 假设PLL2NDivider的默认值在widget_register_map中定义，或者有一个标准默认值
            default_combined_value = 12 # 一个合理的默认值，如果无法从其他地方获取
            min_val, max_val = 2, 262143 # PLL2NDivider的有效范围
            initialized_from_reg = False
            final_value = default_combined_value

            if self.widget_register_map and widget_name in self.widget_register_map:
                widget_info = self.widget_register_map[widget_name]
                if "default_value" in widget_info:
                    try:
                        default_combined_value = int(widget_info["default_value"], 2) # 假设默认值是二进制字符串
                    except ValueError:
                        logger.warning(f"{widget_name} 的默认值 '{widget_info['default_value']}' 不是有效的二进制字符串，使用预设默认值 {default_combined_value}")
                if "bit_def" in widget_info and "options" in widget_info["bit_def"]:
                    try:
                        min_str, max_str = widget_info["bit_def"]["options"].split(':')
                        min_val = int(min_str)
                        max_val = int(max_str)
                    except Exception as e_range:
                        logger.warning(f"解析 {widget_name} 范围失败: {e_range}, 使用预设范围 {min_val}-{max_val}")
                final_value = default_combined_value

            if self.register_manager:
                high_bits = None
                low_bits = None
                # 获取0x76寄存器的1:0位值 (高2位)
                try:
                    val = self.register_manager.get_bit_field_value("0x76", "PLL2_N[17:16]")
                    if val is not None:
                        high_bits = int(val)
                        logger.info(f"{widget_name} (高位) 从RegisterManager获取值: {high_bits} (reg=0x76, bit='PLL2_N[17:16]')")
                    else:
                        logger.info(f"RegisterManager中未找到 {widget_name} (高位) (0x76 [PLL2_N[17:16]]) 的值")
                except Exception as e_high:
                    logger.warning(f"从RegisterManager获取 {widget_name} (高位) 值失败: {e_high}")

                # 获取0x77寄存器的值 (低16位)
                try:
                    val = self.register_manager.get_bit_field_value("0x77", "PLL2_N[15:0]")
                    if val is not None:
                        low_bits = int(val)
                        logger.info(f"{widget_name} (低位) 从RegisterManager获取值: {low_bits} (reg=0x77, bit='PLL2_N[15:0]')")
                    else:
                        logger.info(f"RegisterManager中未找到 {widget_name} (低位) (0x77 [PLL2_N[15:0]]) 的值")
                except Exception as e_low:
                    logger.warning(f"从RegisterManager获取 {widget_name} (低位) 值失败: {e_low}")

                if high_bits is not None and low_bits is not None:
                    combined_value_from_reg = (high_bits << 16) | low_bits
                    # 确保值在有效范围内
                    if min_val <= combined_value_from_reg <= max_val:
                        final_value = combined_value_from_reg
                        initialized_from_reg = True
                        logger.info(f"{widget_name} 成功从RegisterManager组合值: {final_value}")
                    else:
                        logger.warning(f"{widget_name} 从RegisterManager组合的值 {combined_value_from_reg} 超出范围 [{min_val}-{max_val}]，将使用默认值 {default_combined_value}")
                        final_value = default_combined_value # 如果超出范围，则回退到默认值
                else:
                    logger.info(f"{widget_name} 未能从RegisterManager获取完整值，使用默认值: {default_combined_value}")
                    final_value = default_combined_value
            else:
                logger.info(f"RegisterManager未设置，{widget_name} 使用默认值: {default_combined_value}")
                final_value = default_combined_value

            # 再次确保最终值在有效范围内
            if final_value < min_val:
                logger.warning(f"{widget_name} 最终值 {final_value} 小于最小值 {min_val}，修正为 {min_val}")
                final_value = min_val
            elif final_value > max_val:
                logger.warning(f"{widget_name} 最终值 {final_value} 大于最大值 {max_val}，修正为 {max_val}")
                final_value = max_val

            # 设置控件值
            if hasattr(self.ui, widget_name):
                control = getattr(self.ui, widget_name)
                if hasattr(control, 'setValue'): # SpinBox
                    control.setValue(final_value)
                    logger.info(f"设置 {widget_name} 控件值为: {final_value}{' (来自寄存器)' if initialized_from_reg else ' (默认值)'}")
                else:
                    logger.warning(f"{widget_name} 控件不支持 setValue")
            else:
                logger.warning(f"UI中找不到控件 {widget_name}")

        except Exception as e:
            logger.error(f"初始化 {widget_name} 控件时出错: {str(e)}")
            import traceback
            traceback.print_exc()

            # 设置控件值，添加空值检查
            widget = self.ui.PLL2NDivider
            if widget and hasattr(widget, 'setValue'):
                widget.blockSignals(True)
                try:
                    widget.setValue(int(combined_value))  # 确保是整数
                    logger.info(f"已初始化PLL2NDivider控件，值为 {combined_value} (高2位={high_bits}, 低16位={low_bits})")
                except Exception as set_error:
                    logger.error(f"设置PLL2NDivider控件值时出错: {str(set_error)}")
                    # 尝试设置一个安全的默认值
                    try:
                        widget.setValue(12)
                        logger.info("已设置PLL2NDivider控件为安全默认值: 12")
                    except Exception as fallback_error:
                        logger.error(f"设置PLL2NDivider安全默认值也失败: {str(fallback_error)}")
                finally:
                    widget.blockSignals(False)
            else:
                logger.warning("PLL2NDivider控件不存在或没有setValue方法")

        except Exception as e:
            logger.error(f"初始化PLL2NDivider控件时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def on_register_value_changed(self, widget_name, reg_addr, reg_value):
        """处理寄存器值变化"""
        logger.info(f"PLL: 寄存器 {reg_addr} 值变化 (控件: {widget_name}) -> 0x{reg_value:04X}")

        # 重新计算频率
        self.calculate_output_frequencies()

        # 处理特定控件的业务逻辑
        if widget_name == "PLL1PD":
            self._handle_pll1_power_down()
        elif widget_name == "PLL2PD":
            self._handle_pll2_power_down()
        elif widget_name == "PLL1RST":
            self._handle_pll1_reset()
        elif widget_name == "PLL2R3":
            self._handle_pll2r3_change()
        elif widget_name in ["PLL1RDividerSetting", "PLL2RDivider", "PLL2NDivider"]:
            self._handle_divider_change(widget_name)

        # 注意：寄存器表格跳转功能已在基类ModernBaseHandler中处理
        # 不需要在这里重复调用，避免跳转逻辑混乱

    def update_register_value(self, widget_name, value):
        """重写父类方法，处理特殊控件的值更新"""
        try:
            # 特殊处理PLL2NDivider控件（跨寄存器控件）
            if widget_name == "PLL2NDivider":
                self._update_pll2n_divider(value)
                return

            # 特殊处理PLL2R3控件
            elif widget_name == "PLL2R3" and hasattr(self.ui, "PLL2R3"):
                # 从ComboBox中获取itemData作为实际寄存器值
                actual_value = self.ui.PLL2R3.itemData(value)
                if actual_value is not None:
                    logger.info(f"更新PLL2R3寄存器值: 索引={value}, 实际值={actual_value}")
                    # 调用父类方法，传递实际值
                    super().update_register_value(widget_name, actual_value)
                else:
                    logger.error(f"PLL2R3控件索引{value}的itemData为None")
            else:
                # 对其他控件使用默认处理
                super().update_register_value(widget_name, value)
        except Exception as e:
            logger.error(f"更新寄存器值时发生错误: {str(e)}")

    def _update_pll2n_divider(self, value):
        """更新PLL2NDivider跨寄存器控件（参考传统PLL处理器的实现）"""
        try:
            if not self.register_manager:
                logger.warning("RegisterManager未设置，无法更新PLL2NDivider")
                return

            logger.info(f"更新PLL2NDivider值: {value}")

            # 将18位值分解为高2位和低16位
            high_bits = (value >> 16) & 0x3  # 取高2位
            low_bits = value & 0xFFFF        # 取低16位

            logger.info(f"PLL2NDivider分解: 总值={value}, 高2位={high_bits}, 低16位={low_bits}")

            # 更新0x76寄存器的PLL2_N[17:16]位字段
            success1 = self.register_manager.set_bit_field_value("0x76", "PLL2_N[17:16]", high_bits)
            if success1:
                logger.info("成功更新寄存器0x76的PLL2_N[17:16]位字段: {high_bits}")
            else:
                logger.error("更新寄存器0x76的PLL2_N[17:16]位字段失败")

            # 更新0x77寄存器的PLL2_N[15:0]位字段
            success2 = self.register_manager.set_bit_field_value("0x77", "PLL2_N[15:0]", low_bits)
            if success2:
                logger.info("成功更新寄存器0x77的PLL2_N[15:0]位字段: {low_bits}")
            else:
                logger.error("更新寄存器0x77的PLL2_N[15:0]位字段失败")

            if success1 and success2:
                logger.info(f"PLL2NDivider跨寄存器更新成功: {value}")
                # 触发频率重新计算
                self.calculate_output_frequencies()
            else:
                logger.error("PLL2NDivider跨寄存器更新失败")

        except Exception as e:
            logger.error(f"更新PLL2NDivider时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def _handle_pll2r3_change(self):
        """处理PLL2R3控件变化"""
        try:
            if hasattr(self.ui, "PLL2R3"):
                index = self.ui.PLL2R3.currentIndex()
                text = self.ui.PLL2R3.currentText()
                value = self.ui.PLL2R3.itemData(index)
                if value is None:
                    # 如果itemData未设置，使用映射表
                    pll2r3_values = {0: 0, 1: 1, 2: 2, 3: 4}
                    value = pll2r3_values.get(index, 0)
                logger.info(f"PLL2R3选择了电阻值: {text}, 索引: {index}, 实际值: {value}")
        except Exception as e:
            logger.error(f"处理PLL2R3变化时出错: {str(e)}")

    def on_global_register_update(self, reg_addr, reg_value):
        """处理全局寄存器更新"""
        logger.debug(f"PLL: 收到全局更新 {reg_addr} = 0x{reg_value:04X}")

        # 重新计算频率
        self.calculate_output_frequencies()

    # === 业务逻辑方法 ===

    def calculate_output_frequencies(self):
        """计算PLL输出频率"""
        try:
            # 获取输入频率
            fin_freq = self._get_float_from_lineedit(self.ui.FreFin) if hasattr(self.ui, "FreFin") else 0.0
            oscin_freq = self._get_float_from_lineedit(self.ui.OSCinFreq) if hasattr(self.ui, "OSCinFreq") else 0.0

            # 计算PLL1输出频率
            self._calculate_pll1_output(fin_freq)

            # 计算PLL2输出频率
            pll2_pfd_freq = self._calculate_pll2_output(oscin_freq)

            # 计算Fin0输出频率
            self._calculate_fin0_output(pll2_pfd_freq)

            logger.debug(f"频率计算完成: Fin={fin_freq}, OSCin={oscin_freq}")

        except Exception as e:
            logger.error(f"计算输出频率时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def _calculate_pll1_output(self, fin_freq):
        """计算PLL1的PFD频率"""
        try:
            if not hasattr(self.ui, "PLL1PFDFreq"):
                return 0.0

            # 检查PLL1是否掉电
            if hasattr(self.ui, "PLL1PD") and self.ui.PLL1PD.isChecked():
                self.ui.PLL1PFDFreq.setText("0.00")
                return 0.0

            # 获取R分频器值
            r_div = 1
            if hasattr(self.ui, "PLL1RDividerSetting"):
                r_div = max(1, self.ui.PLL1RDividerSetting.value())

            # 计算PFD频率
            if r_div > 0:
                pll1_pfd_freq = fin_freq / r_div
                self.ui.PLL1PFDFreq.setText(f"{pll1_pfd_freq:.5f}")
                logger.debug(f"PLL1 PFD频率: {pll1_pfd_freq:.5f} MHz (Fin: {fin_freq}, R: {r_div})")
                return pll1_pfd_freq
            else:
                self.ui.PLL1PFDFreq.setText("0.00")
                logger.warning("PLL1 R Divider 为0，无法计算PFD频率")
                return 0.0

        except Exception as e:
            logger.error(f"计算PLL1输出时出错: {str(e)}")
            if hasattr(self.ui, "PLL1PFDFreq"):
                self.ui.PLL1PFDFreq.setText("Error")
            return 0.0

    def _calculate_pll2_output(self, oscin_freq):
        """计算PLL2的PFD频率"""
        try:
            if not hasattr(self.ui, "PLL2PFDFreq"):
                return 0.0

            # 检查PLL2是否掉电
            if hasattr(self.ui, "PLL2PD") and self.ui.PLL2PD.isChecked():
                self.ui.PLL2PFDFreq.setText("0.00")
                return 0.0

            # 获取R分频器值
            r_div = 1
            if hasattr(self.ui, "PLL2RDivider"):
                r_div = max(1, self.ui.PLL2RDivider.value())

            # 获取倍频器值
            doubler_value = 1
            if hasattr(self.ui, "Doubler"):
                doubler_value = self.ui.Doubler.currentIndex() + 1  # 0->1, 1->2

            # 计算PFD频率
            if r_div > 0:
                pll2_pfd_freq = oscin_freq * doubler_value / r_div
                self.ui.PLL2PFDFreq.setText(f"{pll2_pfd_freq:.3f}")
                logger.debug(f"PLL2 PFD频率: {pll2_pfd_freq:.3f} MHz (OSCin: {oscin_freq}, Doubler: {doubler_value}, R: {r_div})")
                return pll2_pfd_freq
            else:
                self.ui.PLL2PFDFreq.setText("0.00")
                logger.warning("PLL2 R Divider 为0，无法计算PFD频率")
                return 0.0

        except Exception as e:
            logger.error(f"计算PLL2输出时出错: {str(e)}")
            if hasattr(self.ui, "PLL2PFDFreq"):
                self.ui.PLL2PFDFreq.setText("Error")
            return 0.0

    def _calculate_fin0_output(self, pll2_pfd_freq):
        """计算Fin0的输出频率"""
        try:
            if not hasattr(self.ui, "Fin0Freq"):
                return

            # 检查PLL2是否掉电
            if hasattr(self.ui, "PLL2PD") and self.ui.PLL2PD.isChecked():
                self.ui.Fin0Freq.setText("0.00")
                return

            # 获取N分频器值
            n_div = 1
            if hasattr(self.ui, "PLL2NDivider"):
                n_div = max(1, self.ui.PLL2NDivider.value())

            # 获取预分频器值
            prescaler_val = 1.0
            if hasattr(self.ui, "PLL2Prescaler"):
                try:
                    prescaler_text = self.ui.PLL2Prescaler.currentText()
                    prescaler_val = float(prescaler_text) if prescaler_text else 1.0
                except ValueError:
                    logger.error(f"无法将PLL2Prescaler值 '{prescaler_text}' 转换为数字")
                    prescaler_val = 1.0

            # 计算VCO频率和Fin0频率
            if prescaler_val > 0:
                vco2_freq = pll2_pfd_freq * n_div
                fin0_freq = vco2_freq * prescaler_val
                self.ui.Fin0Freq.setText(f"{fin0_freq:.5f}")
                logger.debug(f"Fin0频率: {fin0_freq:.5f} MHz (PFD: {pll2_pfd_freq}, N: {n_div}, Prescaler: {prescaler_val})")
            else:
                logger.warning("PLL2 Prescaler 值为0或无效，无法计算Fin0频率")
                self.ui.Fin0Freq.setText("0.00")

        except Exception as e:
            logger.error(f"计算Fin0输出时出错: {str(e)}")
            if hasattr(self.ui, "Fin0Freq"):
                self.ui.Fin0Freq.setText("Error")

    def _get_float_from_lineedit(self, line_edit, default_value=0.0):
        """安全地从QLineEdit获取浮点数"""
        try:
            text = line_edit.text()
            return float(text) if text else default_value
        except (ValueError, AttributeError):
            return default_value



    def _handle_pll1_power_down(self):
        """处理PLL1电源控制"""
        try:
            pll1_pd = self.register_manager.get_bit_field_value("0x50", "PLL1_PD") if self.register_manager else 0
            logger.info(f"PLL1电源状态: {'关闭' if pll1_pd else '开启'}")
            # 重新计算频率
            self.calculate_output_frequencies()
        except Exception as e:
            logger.error(f"处理PLL1电源控制时出错: {str(e)}")

    def _handle_pll2_power_down(self):
        """处理PLL2电源控制"""
        try:
            pll2_pd = self.register_manager.get_bit_field_value("0x83", "PLL2_PD") if self.register_manager else 0
            logger.info(f"PLL2电源状态: {'关闭' if pll2_pd else '开启'}")
            # 重新计算频率
            self.calculate_output_frequencies()
        except Exception as e:
            logger.error(f"处理PLL2电源控制时出错: {str(e)}")

    def _handle_pll1_reset(self):
        """处理PLL1复位"""
        try:
            logger.info("PLL1复位操作")
            # 可以在这里添加复位后的特殊处理
        except Exception as e:
            logger.error(f"处理PLL1复位时出错: {str(e)}")

    def _handle_divider_change(self, widget_name):
        """处理分频器值变化"""
        try:
            logger.info(f"分频器 {widget_name} 值变化")
            # 重新计算频率
            self.calculate_output_frequencies()
        except Exception as e:
            logger.error(f"处理分频器变化时出错: {str(e)}")

    # === 公共接口方法 ===

    def get_current_status(self):
        """获取当前PLL状态

        Returns:
            dict: 当前状态信息
        """
        try:
            status = {}

            if self.register_manager:
                # 获取PLL状态
                status["pll1_enabled"] = not self.register_manager.get_bit_field_value("0x50", "PLL1_PD")
                status["pll2_enabled"] = not self.register_manager.get_bit_field_value("0x83", "PLL2_PD")
                status["vco_enabled"] = not self.register_manager.get_bit_field_value("0x50", "VCO_PD")

                # 获取频率信息
                status["current_clock_source"] = self.current_clock_source
                status["clkin_frequencies"] = self.clkin_frequencies.copy()

            return status

        except Exception as e:
            logger.error(f"获取PLL状态时出错: {str(e)}")
            return {}

    def update_clock_source(self, source_name, frequency, divider):
        """更新时钟源配置

        Args:
            source_name: 时钟源名称
            frequency: 频率值
            divider: 分频值
        """
        try:
            # 规范化时钟源名称
            normalized_source = source_name
            if source_name.startswith("CLK"):
                normalized_source = "ClkIn" + source_name[5:]

            # 更新当前时钟源
            self.current_clock_source = normalized_source

            # 更新频率和分频值
            self.clkin_frequencies[normalized_source] = frequency
            self.clkin_divider_values[normalized_source] = divider

            # 更新UI显示
            if hasattr(self.ui, "FreFin"):
                self.ui.FreFin.setText(str(frequency))
            if hasattr(self.ui, "PLL1RDividerSetting"):
                self.ui.PLL1RDividerSetting.setValue(divider)

            # 重新计算频率
            self.calculate_output_frequencies()

            logger.info(f"已更新时钟源: {normalized_source}, 频率: {frequency}MHz, 分频: {divider}")

        except Exception as e:
            logger.error(f"更新时钟源时出错: {str(e)}")

    def on_global_clock_source_selected(self, source_name, frequency, divider):
        """处理全局时钟源选择事件"""
        logger.info(f"【PLL工具窗口】收到时钟源选择事件: 源={source_name}, 频率={frequency}MHz, 分频比={divider}")

        try:
            # 规范化时钟源名称
            normalized_source = source_name
            if source_name.startswith("CLK"):
                normalized_source = "ClkIn" + source_name[5:]

            logger.info(f"【PLL工具窗口】规范化时钟源名称: {source_name} -> {normalized_source}")

            # 更新当前选中的时钟源
            self.current_clock_source = normalized_source

            # 更新存储的时钟源频率
            self.clkin_frequencies[normalized_source] = frequency
            logger.info(f"【PLL工具窗口】已更新时钟源 {normalized_source} 的频率为 {frequency}MHz")

            # 更新存储的时钟源分频值
            self.clkin_divider_values[normalized_source] = divider
            logger.info(f"【PLL工具窗口】已更新时钟源 {normalized_source} 的分频值为 {divider}")

            # 更新UI显示
            if hasattr(self.ui, "FreFin"):
                old_value = self.ui.FreFin.text()
                self.ui.FreFin.setText(str(frequency))
                logger.info(f"【PLL工具窗口】FreFin更新: '{old_value}' -> '{frequency}'")
            else:
                logger.warning("【PLL工具窗口】FreFin控件不存在!")

            if hasattr(self.ui, "PLL1RDividerSetting"):
                old_value = self.ui.PLL1RDividerSetting.value()
                # 优先从RegisterManager获取与当前时钟源关联的PLL1RDivider值
                reg_value = None
                if self.register_manager and normalized_source in self.widget_register_map.get("PLL1RDividerSetting", {}):
                    reg_addr, bit_field = self.widget_register_map["PLL1RDividerSetting"][normalized_source]
                    try:
                        reg_value = self.register_manager.get_bit_field_value(reg_addr, bit_field)
                        logger.info(f"【PLL工具窗口】从RegisterManager获取 {normalized_source} 的 PLL1RDivider ({reg_addr}[{bit_field}]): {reg_value}")
                    except Exception as e:
                        logger.warning(f"【PLL工具窗口】从RegisterManager获取 {normalized_source} 的 PLL1RDivider 值失败: {e}")
                        reg_value = None

                if reg_value is not None and 0 < reg_value <= 127: # 假设有效范围
                    self.ui.PLL1RDividerSetting.setValue(reg_value)
                    logger.info(f"【PLL工具窗口】PLL1RDividerSetting更新 (来自RM): {old_value} -> {reg_value}")
                else:
                    self.ui.PLL1RDividerSetting.setValue(divider) # 使用事件传递的值
                    logger.info(f"【PLL工具窗口】PLL1RDividerSetting更新 (来自事件): {old_value} -> {divider}")
            else:
                logger.warning("【PLL工具窗口】PLL1RDividerSetting控件不存在!")

            # 重新计算输出频率
            self.calculate_output_frequencies()
            logger.info("【PLL工具窗口】已重新计算输出频率")

        except Exception as e:
            logger.error(f"【PLL工具窗口】处理时钟源选择事件时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def on_mode_changed(self, mode_name):
        """处理模式变化信号"""
        logger.info(f"ModernPLLHandler 收到模式变化信号: {mode_name}")
        # 模式变化后重新计算频率
        self.calculate_output_frequencies()

    def set_pll_preset(self, preset_name):
        """设置PLL预设配置

        Args:
            preset_name: 预设名称
        """
        try:
            logger.info(f"应用PLL预设: {preset_name}")

            # 定义预设配置
            presets = {
                "default": {
                    "PLL1_PD": 0,
                    "PLL2_PD": 0,
                    "VCO_PD": 0,
                    "PLL1RDividerSetting": 1,
                    "PLL2RDivider": 1,
                    "PLL2NDivider": 100
                },
                "low_power": {
                    "PLL1_PD": 1,
                    "PLL2_PD": 1,
                    "VCO_PD": 1
                },
                "high_performance": {
                    "PLL1_PD": 0,
                    "PLL2_PD": 0,
                    "VCO_PD": 0,
                    "PLL2NDivider": 200
                }
            }

            if preset_name not in presets:
                logger.warning(f"未知的预设: {preset_name}")
                return

            preset = presets[preset_name]

            # 应用预设
            for param_name, value in preset.items():
                if param_name.endswith("_PD"):
                    # 电源控制位
                    bit_name = param_name
                    reg_addr = "0x50" if param_name.startswith("PLL1") or param_name.startswith("VCO") else "0x83"
                    if self.register_manager:
                        self.register_manager.set_bit_field_value(reg_addr, bit_name, value)
                elif hasattr(self.ui, param_name):
                    # UI控件值
                    widget = getattr(self.ui, param_name)
                    if hasattr(widget, 'setValue'):
                        widget.setValue(value)

            # 重新计算频率
            self.calculate_output_frequencies()

        except Exception as e:
            logger.error(f"设置PLL预设时出错: {str(e)}")



    @classmethod
    def create_for_testing(cls, parent=None):
        """创建测试实例"""
        try:
            # 创建模拟的RegisterManager
            from core.services.register.RegisterManager import RegisterManager
            import json
            import os

            # 加载寄存器配置
            config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'lib', 'register.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                registers_config = json.load(f)

            register_manager = RegisterManager(registers_config)

            # 创建实例
            instance = cls(parent, register_manager)

            # 创建模拟主窗口用于测试
            class MockMainWindow:
                def __init__(self):
                    self.auto_write_mode = False
                    self.register_manager = register_manager
                    # 模拟寄存器服务
                    self.register_service = None

                def on_register_selected(self, reg_addr):
                    """模拟寄存器选择方法"""
                    logger.debug(f"模拟主窗口: 寄存器选择 {reg_addr}")

            # 设置模拟主窗口引用
            instance.main_window = MockMainWindow()
            logger.info("已为测试实例设置模拟主窗口引用")

            logger.info("创建现代化PLLHandler测试实例成功")
            return instance

        except Exception as e:
            logger.error(f"创建测试实例时出错: {str(e)}")
            raise


if __name__ == "__main__":
    import sys
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    # 创建测试实例
    handler = ModernPLLHandler.create_for_testing()
    handler.show()
    
    sys.exit(app.exec_())
