#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试标签页清理功能
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QLabel, QPushButton, QTextEdit
from PyQt5.QtCore import Qt, QTimer
from utils.Log import get_module_logger

logger = get_module_logger(__name__)

class TabCleanupTestWindow(QMainWindow):
    """标签页清理功能测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.plugin_service = None
        self.init_ui()
        self.find_plugin_service()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("标签页清理功能测试")
        self.setGeometry(200, 200, 600, 500)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("标签页清理功能测试工具")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; text-align: center;")
        layout.addWidget(title_label)
        
        # 状态显示
        self.status_label = QLabel("状态: 准备就绪")
        self.status_label.setStyleSheet("padding: 10px; background-color: #f0f0f0; border: 1px solid #ccc;")
        layout.addWidget(self.status_label)
        
        # 说明文本
        instructions = """
测试说明：
1. 此工具用于测试标签页清理功能
2. 可以查看当前标签页状态
3. 可以强制清理空白标签页
4. 可以测试分离和停靠功能

测试步骤：
1. 点击"查找插件服务"确保连接成功
2. 点击"查看标签页状态"查看当前状态
3. 打开一些工具窗口进行测试
4. 使用分离功能测试资源清理
5. 使用清理功能清除空白标签页
        """
        
        instructions_text = QTextEdit()
        instructions_text.setPlainText(instructions)
        instructions_text.setMaximumHeight(150)
        instructions_text.setReadOnly(True)
        layout.addWidget(instructions_text)
        
        # 按钮区域
        button_layout = QVBoxLayout()
        
        find_service_btn = QPushButton("查找插件服务")
        find_service_btn.clicked.connect(self.find_plugin_service)
        button_layout.addWidget(find_service_btn)
        
        status_btn = QPushButton("查看标签页状态")
        status_btn.clicked.connect(self.show_tab_status)
        button_layout.addWidget(status_btn)
        
        cleanup_btn = QPushButton("强制清理空白标签页")
        cleanup_btn.clicked.connect(self.force_cleanup_tabs)
        button_layout.addWidget(cleanup_btn)
        
        test_undock_btn = QPushButton("测试分离功能")
        test_undock_btn.clicked.connect(self.test_undock_function)
        button_layout.addWidget(test_undock_btn)
        
        test_dock_btn = QPushButton("测试停靠功能")
        test_dock_btn.clicked.connect(self.test_dock_function)
        button_layout.addWidget(test_dock_btn)
        
        layout.addLayout(button_layout)
        
        # 日志显示区域
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setPlaceholderText("操作日志将显示在这里...")
        layout.addWidget(self.log_text)
        
    def find_plugin_service(self):
        """查找插件服务"""
        try:
            # 尝试从主窗口获取插件服务
            for widget in QApplication.topLevelWidgets():
                if (isinstance(widget, QMainWindow) and 
                    widget != self and 
                    hasattr(widget, 'plugin_integration_service')):
                    self.plugin_service = widget.plugin_integration_service
                    self.status_label.setText("已找到插件服务")
                    self.log_text.append("✅ 成功连接到插件服务")
                    logger.info("找到插件服务")
                    return
            
            self.status_label.setText("未找到插件服务")
            self.log_text.append("❌ 未找到插件服务，请确保主程序正在运行")
            logger.warning("未找到插件服务")
            
        except Exception as e:
            error_msg = f"查找插件服务时出错: {str(e)}"
            self.status_label.setText(error_msg)
            self.log_text.append(f"❌ {error_msg}")
            logger.error(error_msg)
            
    def show_tab_status(self):
        """显示标签页状态"""
        try:
            if not self.plugin_service:
                self.status_label.setText("请先查找插件服务")
                return
            
            status_info = self.plugin_service.get_tab_status_info()
            self.status_label.setText("标签页状态已获取")
            self.log_text.append("📊 标签页状态信息:")
            self.log_text.append(status_info)
            logger.info("获取标签页状态信息成功")
            
        except Exception as e:
            error_msg = f"获取标签页状态失败: {str(e)}"
            self.status_label.setText(error_msg)
            self.log_text.append(f"❌ {error_msg}")
            logger.error(error_msg)
            
    def force_cleanup_tabs(self):
        """强制清理空白标签页"""
        try:
            if not self.plugin_service:
                self.status_label.setText("请先查找插件服务")
                return
            
            # 获取清理前状态
            before_status = self.plugin_service.get_tab_status_info()
            self.log_text.append("🧹 开始清理空白标签页...")
            self.log_text.append(f"清理前状态: {before_status}")
            
            # 执行清理
            self.plugin_service.force_cleanup_empty_tabs()
            
            # 获取清理后状态
            after_status = self.plugin_service.get_tab_status_info()
            self.status_label.setText("空白标签页清理完成")
            self.log_text.append("✅ 清理完成")
            self.log_text.append(f"清理后状态: {after_status}")
            logger.info("空白标签页清理完成")
            
        except Exception as e:
            error_msg = f"清理空白标签页失败: {str(e)}"
            self.status_label.setText(error_msg)
            self.log_text.append(f"❌ {error_msg}")
            logger.error(error_msg)
            
    def test_undock_function(self):
        """测试分离功能"""
        try:
            if not self.plugin_service:
                self.status_label.setText("请先查找插件服务")
                return
            
            # 获取当前打开的插件窗口
            plugin_windows = self.plugin_service.plugin_windows
            if not plugin_windows:
                self.status_label.setText("没有打开的插件窗口")
                self.log_text.append("❌ 没有可分离的插件窗口")
                return
            
            # 选择第一个插件进行测试
            plugin_name = list(plugin_windows.keys())[0]
            self.log_text.append(f"🔄 测试分离插件: {plugin_name}")
            
            # 执行分离
            self.plugin_service.undock_window_from_tab(plugin_name)
            
            self.status_label.setText(f"已分离插件: {plugin_name}")
            self.log_text.append(f"✅ 插件 {plugin_name} 分离完成")
            
            # 显示分离后的状态
            QTimer.singleShot(1000, self.show_tab_status)
            
        except Exception as e:
            error_msg = f"测试分离功能失败: {str(e)}"
            self.status_label.setText(error_msg)
            self.log_text.append(f"❌ {error_msg}")
            logger.error(error_msg)
            
    def test_dock_function(self):
        """测试停靠功能"""
        try:
            if not self.plugin_service:
                self.status_label.setText("请先查找插件服务")
                return
            
            self.log_text.append("🔄 测试停靠所有悬浮窗口...")
            
            # 执行停靠所有窗口
            self.plugin_service.dock_all_windows()
            
            self.status_label.setText("停靠功能测试完成")
            self.log_text.append("✅ 停靠所有窗口完成")
            
            # 显示停靠后的状态
            QTimer.singleShot(1000, self.show_tab_status)
            
        except Exception as e:
            error_msg = f"测试停靠功能失败: {str(e)}"
            self.status_label.setText(error_msg)
            self.log_text.append(f"❌ {error_msg}")
            logger.error(error_msg)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    test_window = TabCleanupTestWindow()
    test_window.show()
    
    logger.info("标签页清理功能测试工具已启动")
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
