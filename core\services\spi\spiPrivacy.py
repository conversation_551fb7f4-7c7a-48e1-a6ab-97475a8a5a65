import serial
from typing import Optional, List, Union
import logging
from enum import Enum

# 使用统一的日志管理器
try:
    from utils.Log import get_module_logger
    logger = get_module_logger(__name__)
except ImportError:
    # 备用日志配置
    logging.basicConfig(
        level=logging.WARNING,  # 降低默认日志级别
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler()]
    )
    logger = logging.getLogger(__name__)

# 检查serial库的可用性
has_serial_tools = True
try:
    import serial.tools.list_ports
except (ImportError, AttributeError) as e:
    has_serial_tools = False
    logging.error(f"无法导入serial.tools.list_ports: {str(e)}")

# 导入端口管理器
try:
    from .port_manager import get_port_manager
    has_port_manager = True
except ImportError:
    has_port_manager = False
    logging.warning("端口管理器不可用，使用传统方式")

# 在文件顶部添加枚举定义
class SPIReadWrite(Enum):
    WRITE = 0
    READ = 1


class SPIManager:
    # 类级别的端口锁，防止多个实例同时访问同一端口
    _port_locks = {}
    _active_connections = {}

    def __init__(self, port: Optional[str] = None, baudrate: int = 115200):
        """
        初始化SPI管理器，自动检测或指定串口
        :param port: 指定串口设备名，若为None则自动检测
        :param baudrate: 串口波特率（文档建议设为最大值）
        """
        self.ser = None
        self.port_name = None
        self.baudrate = baudrate

        try:
            # 如果没有指定端口，尝试自动检测
            if not port:
                port = self._auto_detect_port()

            # 如果有可用端口，尝试打开
            if port:
                self._open_port_safely(port, baudrate)
            else:
                logging.warning("未检测到可用的串口")
        except Exception as e:
            logging.error(f"初始化SPI管理器失败: {str(e)}")
            self.ser = None

    def _open_port_safely(self, port: str, baudrate: int):
        """安全地打开串口，避免重复打开和权限冲突"""
        try:
            # 优先使用端口管理器
            if has_port_manager:
                port_manager = get_port_manager()
                self.ser = port_manager.open_port(port, baudrate, exclusive=True)
                if self.ser:
                    self.port_name = port
                    logging.info(f"通过端口管理器成功打开串口: {port}")
                    return
                else:
                    logging.warning(f"端口管理器无法打开串口: {port}")
                    return

            # 回退到传统方式
            # 检查是否已有活跃连接
            if port in self._active_connections:
                existing_ser = self._active_connections[port]
                if existing_ser and existing_ser.is_open:
                    logging.warning(f"串口 {port} 已被当前应用程序占用，复用现有连接")
                    self.ser = existing_ser
                    self.port_name = port
                    return
                else:
                    # 清理无效连接
                    del self._active_connections[port]

            # 检查端口是否被外部程序占用
            if self._is_port_busy(port):
                logging.warning(f"串口 {port} 被外部程序占用，跳过")
                return

            logging.debug(f"尝试打开串口: {port}")

            # 尝试打开串口
            self.ser = serial.Serial(
                port,
                baudrate,
                bytesize=8,
                parity='N',
                stopbits=1,
                timeout=1,
                exclusive=True  # 独占模式，防止其他程序同时访问
            )

            self.port_name = port
            self._active_connections[port] = self.ser
            logging.info(f"成功打开串口: {port}")

        except serial.SerialException as e:
            if "PermissionError" in str(e) or "拒绝访问" in str(e):
                logging.warning(f"串口 {port} 权限被拒绝，可能被其他程序占用")
            else:
                logging.error(f"打开串口 {port} 失败: {str(e)}")
            self.ser = None
        except Exception as e:
            logging.error(f"打开串口 {port} 时发生未预期错误: {str(e)}")
            self.ser = None

    def _is_port_busy(self, port: str) -> bool:
        """检查端口是否被其他程序占用"""
        try:
            # 尝试快速打开和关闭端口来检测是否可用
            test_ser = serial.Serial()
            test_ser.port = port
            test_ser.timeout = 0.1
            test_ser.open()
            test_ser.close()
            return False
        except Exception:
            return True

    def _auto_detect_port(self) -> Optional[str]:
        """自动检测串口设备"""
        try:
            # 尝试导入并使用serial.tools.list_ports
            try:
                ports = list(serial.tools.list_ports.comports())
                
                # 如果检测到串口
                if ports:
                    # 寻找匹配的串口
                    for port in ports:
                        logging.info(f"发现串口: {port.device} - {port.description}")
                        if "COM" in port.device or "USB" in port.description:
                            return port.device
                    
                    # 如果没有找到特定匹配的串口，使用第一个可用的
                    logging.info(f"使用第一个可用串口: {ports[0].device}")
                    return ports[0].device
            except Exception as e:
                logging.error(f"自动检测串口失败: {str(e)}")
            
            # 如果无法检测到串口，返回None
            return None
        except Exception as e:
            logging.error(f"未预期的错误: {str(e)}")
            return None

    def initialize_spi(
            self,
            clock_rate: int = 0x07,
            mode: int = 0x01,
            default_level: int = 0x00,
            sample_edge: int = 0x00
    ) -> bool:
        """
        SPI初始化
        :param clock_rate: 时钟速率（0x00=42MHz, 0x04=2.625MHz等）
        :param mode: 工作模式（0x00=全双工，0x01=半双工）
        :param default_level: 默认电平（0=低电平）
        :param sample_edge: 采样边沿（0=第一沿）
        :return: 初始化是否成功
        """
        ini_buf = bytes([
            0x00, 0x00, 0x05, 0x00, 0x00, 0x00, 0x0B,
            mode, default_level, sample_edge, clock_rate
        ])
        logging.debug(f'Initializing SPI: {ini_buf.hex()}')
        self.ser.write(ini_buf)
        response = self.ser.read()
        return response == b'\x11'

    def generate_register_address(self, register_number, read_write_flag: SPIReadWrite, address_extention):
        """
        生成符合位域要求的控制字（2字节）
        字节结构：
        [地址高8位 | 控制低8位]
        控制低8位结构（从高位到低位）：
        AE3-AE0(4bit) | Step/Cycle(1bit) | R/W(1bit) | 4/8 Cycle(1bit) | Z(1bit)
        """
        # 参数验证更新
        if read_write_flag not in SPIReadWrite:
            raise ValueError("读写标志位必须使用SPIReadWrite枚举值")
        if not (0 <= register_number <= 0xFF):
            raise ValueError("寄存器编号必须为8位值 (0x00-0xFF)")
        if not (0 <= address_extention <= 0xF):
            raise ValueError("地址标志必须为4位值 (0x0-0xF)")

        # 构建控制字
        # 高字节：直接使用寄存器编号
        addr_high = register_number & 0xFF
        
        # 低字节：组合各个控制位
        control_low = (
            (address_extention & 0xF) << 4 |
            (0x00 << 3) |
            (read_write_flag.value << 2) |  # 使用枚举值
            (0x00 << 1) |
            (0x00)
        ) & 0xFF

        control_bytes = [addr_high, control_low]
        hex_str = f"{addr_high:02X}{control_low:02X}"
        
        logging.info(f"生成控制字: {hex_str} "
                     f"(AE={address_extention:01X} R/W={read_write_flag.value} "
                     f"Step={0x00} Cycle={0x00} Z={0x00})")
        return control_bytes

    def write_spi(
        self,
        register_address: Union[int, List[int]],
        data: Union[int, List[List[int]]],
        mode: int = 0,
        address_flag: int = 0,
        read_write_flag: SPIReadWrite = SPIReadWrite.WRITE  # 修改默认值为枚举
    ) -> bool:
        """
        优化后的SPI写操作
        """
        # 参数预处理和验证
        self._validate_write_parameters(register_address, data, mode, address_flag, read_write_flag)
        processed_address = register_address
        processed_data = data

        try:
            # 改进后的模式选择逻辑
            build_methods = {
                0: self._build_single_packet,
                1: self._build_multi_packet,
                2: self._build_autoinc_packet
            }
            
            # 更安全的模式检查
            if mode not in build_methods:
                raise ValueError(f"无效模式: {mode} (支持模式: {list(build_methods.keys())})")
                
            # 更明确的参数传递
            builder = build_methods[mode]
            # 更安全的参数解包方式
            required_params = {
                'address': processed_address,
                'data': processed_data,
                'address_flag': address_flag,
                'read_write_flag': read_write_flag
            }
            
            # 添加参数验证
            try:
                header, payload = builder(**required_params)
                print(header, payload)
            except TypeError as e:
                error_msg = f"参数不匹配: {e}，请检查构建方法参数定义"
                logging.error(error_msg)
                raise ValueError(error_msg) from e
            
            return self._send_packet(header + payload)

        except KeyError as e:
            # 更详细的错误日志
            logging.error(f"配置错误: 缺少模式处理器 {str(e)}")
            raise RuntimeError("内部配置错误，请联系开发者") from e

    def _validate_write_parameters(self, addresses, data, mode, address_flag, rw_flag):
        """参数验证逻辑"""
        if mode not in [0, 1, 2]:
            raise ValueError("模式必须为0（单组）、1（多组）或2（地址自增）")
        if not 0 <= address_flag <= 0xF:
            raise ValueError("地址标志必须为4位值 (0x0-0xF)")
        if rw_flag not in SPIReadWrite:
            raise ValueError("读写标志必须使用SPIReadWrite枚举值")

    def _build_single_packet(self, address, data, address_flag, read_write_flag):
        """构建单组模式数据包"""
        control = self.generate_register_address(address, read_write_flag, address_flag)
        logging.info(f'data: {data.to_bytes(2, "big").hex().upper()}')
        return (
            bytes([0x00, 0x00, 0x05, 0x02]),
            bytes([0x00, 0x00, 0x04]) + bytes(control) + data.to_bytes(2, 'big')
        )

    def _build_multi_packet(self, addresses, data_groups, address_flag, read_write_flag):
        """构建多组模式数据包"""
        if len(addresses) != len(data_groups):
            raise ValueError("地址与数据组数量不匹配")
        if len(data_groups) > 13:
            raise ValueError("最多支持13组数据")

        payload = bytes([len(addresses), 0x04])
        for addr, data in zip(addresses, data_groups):
            control = self.generate_register_address(addr, read_write_flag, address_flag)
            payload += bytes(control) + bytes(data)
        
        return (
            bytes([0x00, 0x00, 0x05, 0x02, 0x01]),
            payload
        )

    def _build_autoinc_packet(self, start_address, data_groups, address_flag, read_write_flag):
        """构建地址自增模式数据包"""
        control = self.generate_register_address(start_address[0], read_write_flag, address_flag)
        flat_data = [byte for group in data_groups for byte in group]
        total_bytes = len(control) + len(flat_data)
        
        return (
            bytes([0x00, 0x00, 0x05, 0x02, 0x02]),
            bytes([0x02, len(data_groups), total_bytes]) + bytes(control) + bytes(flat_data)
        )

    def _send_packet(self, packet):
        """发送数据包并处理响应"""
        self.ser.write(packet)
        logging.info(f"发送数据包: {packet.hex().upper()}")
        
        response = self.ser.read(1)
        success = response == b'\x13'
        logging.info(f"操作{'成功' if success else '失败'}")
        return success

    def read_spi(
        self,
        register_address: int,
        address_extention: int = 0,
        max_retries: int = 2
    ):
        """
        SPI读操作（优化版）
        :param register_address: 寄存器地址
        :param address_extention: 地址标志位（4位）
        :param max_retries: 最大重试次数
        :return: 读取到的2字节有效数据，失败返回None
        """
        for attempt in range(max_retries + 1):
            if attempt > 0:
                logging.info(f"重试读取地址 0x{register_address:02X} (第 {attempt} 次重试)")
                import time
                time.sleep(0.02)  # 重试前额外延迟

            result = self._perform_single_read(register_address, address_extention)
            if result[0]:  # 如果读取成功
                return result

            if attempt < max_retries:
                logging.warning(f"地址 0x{register_address:02X} 读取失败，准备重试")

        logging.error(f"地址 0x{register_address:02X} 读取失败，已达到最大重试次数")
        return False, 0

    def _perform_single_read(self, register_address: int, address_extention: int = 0):
        """
        执行单次SPI读取操作
        :param register_address: 寄存器地址
        :param address_extention: 地址标志位（4位）
        :return: (success, value) 元组
        """
        # 修改读操作的调用
        addr_ctrl = self.generate_register_address(
            register_number=register_address,
            read_write_flag=SPIReadWrite.READ,  # 使用枚举值
            address_extention=address_extention
        )

        # 根据地址+控制位+预计读回数据的总长度确定WR_Buf[6]的值
        # 地址+控制位为2字节，预计读回数据为1或2字节
        total_length = 2 + (1 if address_extention & 0x8 else 2)  # 根据AE3位判断数据长度
        ctrl_byte_for_payload_length = 0x03 if total_length == 3 else 0x04
        payload_bytes = bytes(addr_ctrl)

        # 构建写缓冲区
        wr_buf = bytes([
            0x00, 0x00, 0x05,  # 固定头
            0x0C,              # 命令字 (read)
            0x00, 0x00,        # 保留位
            ctrl_byte_for_payload_length # 0x03或0x04，根据总长度决定
        ]) + payload_bytes
        print(f"发送数据包: {wr_buf.hex().upper()}")
        try:
            # 清空输入缓冲区，避免读取到旧数据
            if self.ser.in_waiting > 0:
                old_data = self.ser.read(self.ser.in_waiting)
                logging.debug(f"清空缓冲区中的旧数据: {old_data.hex().upper()}")

            # 发送并接收数据
            self.ser.write(wr_buf)

            # 添加短暂延迟，确保硬件有足够时间处理请求
            import time
            time.sleep(0.01)  # 10ms延迟

            response = self.ser.read(8)
            print("完整数据包解析:")
            print(f"字节0: {response[0]:02X} (头1)")
            print(f"字节1: {response[1]:02X} (头2)")
            print(f"字节2: {response[2]:02X} (数据高字节), type: {type(response[3])}")
            print(f"字节3: {response[3]:02X} (数据低字节)")
            print(f"字节4: {response[4]:02X} (预期0x0C)")
            print(f"字节5: {response[5]:02X}")
            print(f"字节6: {response[6]:02X} (预期0xAA)")
            print(f"字节7: {response[7]:02X} (预期0x55)")

            # 校验响应
            if (len(response) == 8 and
                response[4] == 0x0C and
                response[6] == 0xAA and
                response[7] == 0x55
                ):
                # 转换有效数据为整数（大端序）
                valid_bytes = response[2:4]
                valid_int = int.from_bytes(valid_bytes, byteorder='big')

                logging.info(f"读取成功: 地址 0x{register_address:02X} → 原始字节 {valid_bytes.hex().upper()} → 整数值 {valid_int} (0x{valid_int:04X})")
                return True, valid_int
            else:
                logging.warning(f"响应校验失败: 地址 0x{register_address:02X}, 响应长度: {len(response)}, 响应内容: {response.hex().upper()}")

                # 如果响应格式不正确，尝试重新读取一次
                if len(response) < 8:
                    logging.info("响应数据不完整，尝试读取剩余数据")
                    remaining = self.ser.read(8 - len(response))
                    if remaining:
                        response += remaining
                        logging.info(f"补充读取数据: {remaining.hex().upper()}")

                        # 重新校验
                        if (len(response) == 8 and
                            response[4] == 0x0C and
                            response[6] == 0xAA and
                            response[7] == 0x55
                            ):
                            valid_bytes = response[2:4]
                            valid_int = int.from_bytes(valid_bytes, byteorder='big')
                            logging.info(f"补充读取成功: 地址 0x{register_address:02X} → {valid_int} (0x{valid_int:04X})")
                            return True, valid_int

                return False, 0

        except Exception as e:
            logging.error(f"读操作异常: {str(e)}")
            return None

    def close(self):
        """关闭串口连接并清理资源"""
        try:
            if self.port_name:
                # 优先使用端口管理器关闭
                if has_port_manager:
                    port_manager = get_port_manager()
                    port_manager.close_port(self.port_name)
                    logging.info(f"通过端口管理器关闭串口: {self.port_name}")
                else:
                    # 传统方式关闭
                    if self.ser and self.ser.is_open:
                        self.ser.close()
                        logging.info(f"已关闭串口: {self.port_name}")

                    # 清理活跃连接记录
                    if self.port_name in self._active_connections:
                        del self._active_connections[self.port_name]

        except Exception as e:
            logging.error(f"关闭串口时出错: {str(e)}")
        finally:
            self.ser = None
            self.port_name = None

    @classmethod
    def cleanup_all_connections(cls):
        """清理所有活跃连接（类方法）"""
        for port, ser in list(cls._active_connections.items()):
            try:
                if ser and ser.is_open:
                    ser.close()
                    logging.info(f"已清理连接: {port}")
            except Exception as e:
                logging.error(f"清理连接 {port} 时出错: {str(e)}")
        cls._active_connections.clear()


# 示例用法
if __name__ == "__main__":
    # 基本功能测试
    def test_spi_basic():
        """SPI基本功能测试"""
        spi = SPIManager()
        try:
            # if spi.ser and spi.initialize_spi():
            if spi.ser:
                # logging.info("\n=== 测试1：单寄存器写入 ===")
                # # 测试单组写入（地址0x01，数据[0xA1, 0xB2]）
                # success = spi.write_spi(
                #     register_address=0x01,
                #     data=18,
                #     mode=0,
                #     address_flag=0x0
                # )
                # logging.info(f"写入结果: {'成功' if success else '失败'}")
                success = False
                logging.info("\n=== 测试2：寄存器读取 ===")
                success, read_data = spi.read_spi(0)
                logging.info(f"读取数据: {success},{read_data},{read_data if read_data else '失败'}")
                logging.info("\n=== 测试3：寄存器写 ===")
                # spi.write_spi(0, 4869)

                success, read_data = spi.read_spi(0)
                logging.info(f"读取数据2: {success},{read_data},{read_data if read_data else '失败'}")
                # logging.info("\n=== 测试3：错误参数验证 ===")
                # try:
                #     spi.write_spi(register_address=0x100, data=[0x00])
                # except ValueError as e:
                #     logging.error(f"参数验证正常捕获错误: {str(e)}")

        finally:
            spi.close()
            logging.info("\n=== 测试完成 ===")

    # 运行测试
    test_spi_basic()