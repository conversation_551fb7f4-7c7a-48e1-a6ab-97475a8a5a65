#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试频率更新问题
检查为什么setText没有生效
"""

import sys
import os
import json
from PyQt5.QtWidgets import QApplication

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ui.handlers.ModernClkOutputsHandler import ModernClkOutputsHandler
from core.services.register.RegisterManager import RegisterManager
from utils.Log import logger

def debug_frequency_update():
    """调试频率更新问题"""
    print("=" * 60)
    print("调试频率更新问题")
    print("=" * 60)
    
    try:
        # 创建QApplication
        app = QApplication(sys.argv)
        
        print("1. 加载寄存器配置...")
        config_path = os.path.join('lib', 'register.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        print("2. 创建RegisterManager...")
        register_manager = RegisterManager(registers_config)
        
        print("3. 创建现代化时钟输出处理器...")
        modern_handler = ModernClkOutputsHandler(None, register_manager)
        
        print("4. 调试单个输出频率更新...")
        
        # 测试输出0
        output_num = 0
        output_attr = f"lineEditFout{output_num}Output"
        
        if hasattr(modern_handler.ui, output_attr):
            output_widget = getattr(modern_handler.ui, output_attr)
            
            print(f"\n--- 调试输出{output_num} ---")
            print(f"输出控件: {output_widget}")
            print(f"控件类型: {type(output_widget)}")
            
            # 检查初始值
            initial_text = output_widget.text()
            print(f"初始显示值: '{initial_text}'")
            
            # 获取分频器值
            divider_value = modern_handler._get_divider_value_for_output(output_num)
            print(f"分频器值: {divider_value}")
            
            # 获取VCO频率
            vco_freq = modern_handler.fvco
            print(f"VCO频率: {vco_freq}")
            
            # 计算期望频率
            expected_freq = vco_freq / divider_value if divider_value > 0 else 0
            print(f"期望频率: {expected_freq:.2f}")
            
            # 检查SRCMUX状态
            srcmux_state = modern_handler.srcmux_states.get(output_num, False)
            print(f"SRCMUX状态: {srcmux_state}")
            
            # 手动调用更新方法
            print("\n--- 手动调用更新方法 ---")
            try:
                modern_handler._update_single_output_frequency(output_num, output_widget)
                print("✓ _update_single_output_frequency 调用成功")
            except Exception as e:
                print(f"❌ _update_single_output_frequency 调用失败: {e}")
                import traceback
                traceback.print_exc()
            
            # 检查更新后的值
            updated_text = output_widget.text()
            print(f"更新后显示值: '{updated_text}'")
            
            # 直接测试setText
            print("\n--- 直接测试setText ---")
            test_value = "1234.56"
            print(f"设置测试值: {test_value}")
            output_widget.setText(test_value)
            
            # 立即检查
            immediate_text = output_widget.text()
            print(f"立即读取值: '{immediate_text}'")
            
            # 处理事件循环
            app.processEvents()
            
            # 再次检查
            after_events_text = output_widget.text()
            print(f"处理事件后值: '{after_events_text}'")
            
            # 测试其他输出
            print("\n--- 测试其他输出 ---")
            for test_output in [1, 2, 3]:
                test_attr = f"lineEditFout{test_output}Output"
                if hasattr(modern_handler.ui, test_attr):
                    test_widget = getattr(modern_handler.ui, test_attr)
                    test_initial = test_widget.text()
                    
                    # 设置测试值
                    test_widget.setText(f"TEST{test_output}")
                    app.processEvents()
                    test_after = test_widget.text()
                    
                    print(f"输出{test_output}: '{test_initial}' -> '{test_after}'")
        
        print("\n5. 测试完整的频率计算...")
        
        # 修改VCO频率并测试
        if hasattr(modern_handler.ui, "lineEditFvco"):
            print("设置VCO频率为 3000.0")
            modern_handler.ui.lineEditFvco.setText("3000.0")
            app.processEvents()
            
            print("调用 calculate_output_frequencies")
            modern_handler.calculate_output_frequencies()
            app.processEvents()
            
            # 检查前4个输出
            for check_output in range(4):
                check_attr = f"lineEditFout{check_output}Output"
                if hasattr(modern_handler.ui, check_attr):
                    check_widget = getattr(modern_handler.ui, check_attr)
                    check_text = check_widget.text()
                    print(f"输出{check_output}最终值: '{check_text}'")
        
        print("\n" + "=" * 60)
        print("调试完成")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_frequency_update()
