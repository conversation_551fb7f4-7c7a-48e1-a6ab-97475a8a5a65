# -*- coding: utf-8 -*-

from PyQt5 import QtCore, QtWidgets
from PyQt5.QtWidgets import QMessageBox, QScrollArea
from PyQt5.QtCore import QTimer
import json
import os
from utils.Log import get_module_logger

logger = get_module_logger(__name__)
from core.event_bus.RegisterUpdateBus import RegisterUpdateBus

class BaseClockHandler(QtWidgets.QWidget):
    """时钟处理基类，提供共享的功能和方法"""
    
    def __init__(self, parent=None, registers=None):
        super(BaseClockHandler, self).__init__(parent)
        # 连接全局寄存器更新信号
        RegisterUpdateBus.instance().register_updated.connect(self.on_global_register_updated)
        
        # 创建主布局
        main_layout = QtWidgets.QVBoxLayout(self)
        
        # 创建一个QScrollArea
        self.scroll_area = QScrollArea(self)
        self.scroll_area.setWidgetResizable(True)
        
        # 创建内容widget
        self.content_widget = QtWidgets.QWidget()
        self.content_widget.setMinimumSize(1400, 1600)  # 设置内容widget的最小尺寸
        
        # 将内容widget设置到滚动区域
        self.scroll_area.setWidget(self.content_widget)
        
        # 将滚动区域添加到主布局
        main_layout.addWidget(self.scroll_area)
        
        # 设置窗口基本属性
        self.resize(1400, 1600)
        
        # 定义格式名称映射字典
        self.combobox_options_map = {}
        
        # 使用传入的寄存器配置或加载寄存器配置
        self.registers = registers or {}
        if not self.registers:
            self.load_registers()
        
        # 创建控件与寄存器的映射表
        self.widget_register_map = {}
        
        # 生成完整的spinbox选项映射
        self.spinbox_options_map = self.generate_spinbox_options_map()

    def load_registers(self):
        """加载寄存器配置（仅在未传入寄存器配置时使用）"""
        try:
            # 从当前目录读取 registersNew.json 文件
            json_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config', 'registersNew.json')
            with open(json_path, 'r', encoding='utf-8') as file:
                self.registers = json.load(file)
        except Exception as e:
            self.show_error_message("错误", f"加载寄存器配置失败: {str(e)}")

    def show_error_message(self, title, error_detail, user_message=None):
        """显示错误消息的统一方法"""
        logger.info(error_detail)
        if user_message is None:
            user_message = error_detail
        QMessageBox.critical(self, title, user_message)
        
    def generate_spinbox_options_map(self):
        """生成spinbox选项映射"""
        spinbox_options_map = {}
        base_ps = 125
        increment_ps = 10

        for i in range(256):
            current_ps = base_ps + i * increment_ps
            spinbox_options_map[i] = f"--{current_ps} ps"
        # print(f"生成的spinbox选项映射: {spinbox_options_map}")
        return spinbox_options_map

    def create_widget_register_map(self):
        """创建控件与寄存器的映射表"""            
        try:
            for reg_addr, register in self.registers.items():
                for bit in register.bits:
                    widget_name = bit.get('widget_name')
                    widget_type = bit.get('widget_type')
                    if not widget_name or not widget_type:
                        continue
                    try:
                        widget = getattr(self.ui, widget_name)
                        self.map_widget_to_register(widget_name, reg_addr, widget_type, bit.get("default"), bit)
                    except AttributeError:
                        print(f"控件 {widget_name} 不存在，跳过映射")
                        continue
            for attr_name in dir(self.ui):
                if attr_name.startswith('_') or callable(getattr(self.ui, attr_name)):
                    continue
                attr_value = getattr(self.ui, attr_name)
                if isinstance(attr_value, QtWidgets.QSpinBox) and "DDLYdStepCNT" in attr_name:
                    if "DDLYdStepCNT_1" in self.widget_register_map:
                        widget_info = self.widget_register_map["DDLYdStepCNT_1"]
                        self.map_widget_to_register(attr_name, widget_info["register_addr"], widget_info["widget_type"], widget_info["default_value"], widget_info["bit_def"])
            print(f"控件与寄存器映射表创建完成，共映射 {len(self.widget_register_map)} 个控件")
            print("\n已映射的控件列表:")
            for widget_name, info in sorted(self.widget_register_map.items()):
                print(f"  - {widget_name}: 类型={info['widget_type']}, 寄存器={info['register_addr']}, 默认值={info['default_value']}")
        except Exception as e:
            print(f"创建控件与寄存器映射表时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def map_widget_to_register(self, widget_name, reg_addr, widget_type, default_value, bit_def):
        """将控件映射到寄存器"""
        self.widget_register_map[widget_name] = {
            "register_addr": reg_addr,
            "widget_type": widget_type,
            "default_value": default_value,
            "bit_def": bit_def
        }
        print(f"映射控件: {widget_name}, 类型: {widget_type}, 默认值: {default_value}, {bit_def.get('options')}")

    def get_widget_info(self, widget_name):
        """获取控件的映射信息
        
        Args:
            widget_name: 控件名称
            
        Returns:
            dict: 控件的映射信息，如果未找到则返回None
        """
        if widget_name in self.widget_register_map:
            return self.widget_register_map[widget_name]
        logger.warning(f"未找到控件 {widget_name} 的映射信息")
        return None

    def _initialize_pll2n_divider(self):
        """特殊初始化PLL2NDivider控件"""
        try:
            # 从两个寄存器获取值
            high_bits = 0
            low_bits = 0
            
            # 获取0x76寄存器的1:0位值 (高2位)
            if "0x76" in self.registers:
                reg76 = self.registers["0x76"]
                high_bits = reg76.get_bit_field_value("PLL2_N[17:16]")
                
            # 获取0x77寄存器的值 (低16位)
            if "0x77" in self.registers:
                reg77 = self.registers["0x77"]
                low_bits = reg77.get_bit_field_value("PLL2_N[15:0]")
            
            # 组合成完整的PLL2NDivider值
            combined_value = (high_bits << 16) | low_bits
            
            # 设置控件值
            widget = self.ui.PLL2NDivider
            widget.blockSignals(True)
            widget.setValue(combined_value)
            widget.blockSignals(False)
            
            print(f"已初始化PLL2NDivider控件，值为 {combined_value} (高2位={high_bits}, 低16位={low_bits})")
            
        except Exception as e:
            print(f"初始化PLL2NDivider控件时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def initialize_widgets(self):
        """初始化界面控件的默认值"""
        try:
            # 特殊处理PLL2NDivider控件 - 确保控件总是在映射表中
            if hasattr(self.ui, "PLL2NDivider"):
                # 注册PLL2NDivider为跨寄存器控件
                self.register_cross_register_control(
                    widget_name="PLL2NDivider",
                    bit_fields=["PLL2_N[17:16]", "PLL2_N[15:0]"],
                    reg_addrs=["0x76", "0x77"],
                    default_value="000000000000000000",
                    options="2:262143"
                )
                
                # 设置控件范围
                self.ui.PLL2NDivider.setMinimum(2)
                self.ui.PLL2NDivider.setMaximum(262143)  # 2^18-1
                
                # 初始化PLL2NDivider控件
                self._initialize_pll2n_divider()
            
            # 继续处理其他控件
            for widget_name, widget_info in self.widget_register_map.items():
                # 跳过已初始化的PLL2NDivider相关控件
                if widget_name == "PLL2NDivider" or widget_name == "PLL2NDivider_high" or widget_name == "PLL2NDivider_low":
                    continue
                    
                widget_type = widget_info["widget_type"]
                default_value = widget_info["default_value"]
                bit_def = widget_info["bit_def"]
                reg_addr = widget_info["register_addr"]
                
                # 确保控件存在
                if not hasattr(self.ui, widget_name):
                    print(f"控件 {widget_name} 不存在，跳过初始化")
                    continue
                    
                widget = getattr(self.ui, widget_name)
                
                # 从寄存器获取当前值（如果可能）
                current_value = None
                try:
                    if reg_addr in self.registers:
                        register = self.registers[reg_addr]
                        field_name = bit_def.get("name", "")
                        if field_name:
                            # 获取寄存器中的实际值
                            current_value = register.get_bit_field_value(field_name)
                            print(f"从寄存器 {reg_addr} 获取控件 {widget_name} 的当前值: {current_value}")
                except Exception as e:
                    print(f"获取控件 {widget_name} 的寄存器值时出错: {str(e)}")
                    current_value = None
                
                # 如果未能从寄存器获取，则使用默认值
                if current_value is None:
                    # 检查是否是手动初始化的控件
                    bit_def = widget_info.get("bit_def", {})
                    is_manually_initialized = bit_def.get("manually_initialized", False)

                    if is_manually_initialized:
                        # 对于手动初始化的控件，直接使用默认值，不进行转换
                        current_value = default_value
                        logger.debug(f"控件 {widget_name} 是手动初始化的控件，使用原始默认值: {current_value}")
                    elif widget_type == "checkbox":
                        # 转换字符串二进制为整数
                        try:
                            current_value = int(default_value, 2)
                        except ValueError:
                            logger.error(f"初始化控件 {widget_name} 值时出错: invalid literal for int() with base 2: '{default_value}'")
                            current_value = 0  # 使用安全的默认值
                    elif widget_type in ["spinbox", "combobox"]:
                        # 转换字符串二进制为整数
                        try:
                            current_value = int(default_value, 2)
                        except ValueError:
                            logger.error(f"初始化控件 {widget_name} 值时出错: invalid literal for int() with base 2: '{default_value}'")
                            current_value = 0  # 使用安全的默认值
                    else:
                        current_value = default_value
                
                # 根据控件类型设置值
                if widget_type == "checkbox":
                    # 检查控件名称是否以Enb结尾，应用反向逻辑
                    if widget_name.endswith("Enb"):
                        widget.setChecked(current_value == 0)
                    else:
                        widget.setChecked(current_value == 1)
                elif widget_type == "spinbox":
                    options = bit_def.get("options", "0:255")
                    self.initialize_spinbox(widget, current_value, options)
                elif widget_type == "combobox":
                    # 初始化 ComboBox
                    options = bit_def.get("options", "0:15")
                    self.initialize_combobox(widget, current_value, options)
                elif widget_type == "lineedit":
                    widget.setText(str(current_value))
            print("控件初始化完成")
            
            # 延迟检查未初始化的控件，确保在所有后续计算完成后运行
            QTimer.singleShot(1000, self.delayed_check_uninitialized_widgets)
            
        except Exception as e:
            self.show_error_message("错误", f"初始化控件时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def check_uninitialized_widgets(self):
        """检查未初始化的控件，如果需要立即检查，请直接调用delayed_check_uninitialized_widgets"""
        # 此方法保留用于向后兼容
        QTimer.singleShot(500, self.delayed_check_uninitialized_widgets)
        logger.info("已安排延迟检查未初始化控件，将在所有控件和频率初始化完成后执行")
            
    def delayed_check_uninitialized_widgets(self):
        """延迟检查未初始化的控件，确保所有控件和频率都已初始化"""
        try:
            # 获取所有UI控件
            ui_widgets = {}
            for attr_name in dir(self.ui):
                # 跳过私有属性和方法
                if attr_name.startswith('_') or callable(getattr(self.ui, attr_name)):
                    continue
                    
                # 获取属性值
                attr_value = getattr(self.ui, attr_name)
                
                # 检查是否是控件
                if isinstance(attr_value, (QtWidgets.QCheckBox, QtWidgets.QSpinBox, 
                                          QtWidgets.QComboBox, QtWidgets.QLineEdit)):
                    ui_widgets[attr_name] = attr_value
            
            # 获取已初始化的控件
            initialized_widgets = set(self.widget_register_map.keys())
            
            # 找出未初始化的控件
            uninitialized_widgets = set(ui_widgets.keys()) - initialized_widgets
            
            # 排除不需要初始化的控件
            exclude_widgets = set()
            # 添加用户指定的不需要提示的控件
            user_excluded_widgets = {"DACUpdateRate", "FreFin", "PLL1RDividerSetting"}
            exclude_widgets.update(user_excluded_widgets)

            for widget_name in uninitialized_widgets:
                # 排除频率输出显示控件
                if (widget_name.startswith('lineEditFout') and widget_name.endswith('Output')) or \
                   widget_name.startswith('SyncSysref') or widget_name.startswith('Internal'):
                    exclude_widgets.add(widget_name)
                # 排除Fvco输入框和VCO频率显示
                if any(term in widget_name for term in ['Fvco', 'VCO', 'Freq']):
                    exclude_widgets.add(widget_name)
                # 排除标签控件
                if widget_name.startswith('label') or 'label' in widget_name.lower():
                    exclude_widgets.add(widget_name)
                # 排除按钮控件
                if 'button' in widget_name.lower() or 'btn' in widget_name.lower():
                    exclude_widgets.add(widget_name)
                # 排除GroupBox和Frame控件名称
                if 'group' in widget_name.lower() or 'frame' in widget_name.lower():
                    exclude_widgets.add(widget_name)
                # 排除带有Output或Display的控件名称
                if 'output' in widget_name.lower() or 'display' in widget_name.lower():
                    exclude_widgets.add(widget_name)
                # 排除不需要处理的其他控件
                if any(term in widget_name.lower() for term in ['tab', 'scroll', 'layout', 'panel', 'container']):
                    exclude_widgets.add(widget_name)
            
            # 最终未初始化的控件
            final_uninitialized = uninitialized_widgets - exclude_widgets
            
            # 只有在确实有未初始化控件时才显示警告
            if final_uninitialized:
                # 输出结果
                logger.info("\n===== 控件初始化状态检查 =====")
                logger.info(f"UI中共有 {len(ui_widgets)} 个控件")
                logger.info(f"已初始化 {len(initialized_widgets)} 个控件")
                logger.info(f"未初始化 {len(final_uninitialized)} 个控件（排除了不需要初始化的控件）")
                
                print("\n未初始化的控件列表:")
                for widget_name in sorted(final_uninitialized):
                    widget_type = type(ui_widgets[widget_name]).__name__
                    print(f"  - {widget_name} ({widget_type})")
                    
                # 显示提示消息
                QMessageBox.warning(self, "控件初始化检查", 
                                  f"发现 {len(final_uninitialized)} 个控件未初始化，详情请查看控制台输出。")
            else:
                # 所有控件都已初始化，只在调试模式下输出信息
                logger.debug("所有需要初始化的控件都已正确初始化！")
                
        except Exception as e:
            print(f"检查未初始化控件时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def _get_combobox_type(self, widget_name):
        """根据控件名称获取ComboBox类型"""
        if widget_name.startswith("CLK") and widget_name.endswith("FMT"):
            return "CLKXFMT"
        elif widget_name.startswith("SCLK") and widget_name.endswith("ADLY"):
            return "SCLK_ADLY"
        elif widget_name.startswith("DDLYdStepCNT"):
            return "DDLY_STEP"
        elif widget_name.endswith("refMux"):
            return "SYSREFMUX"
        elif widget_name.endswith("VcoMode"):
            return "InternalVCO"
        elif widget_name.endswith("Demux"):
            return "CLKDEMUX"
        return None

    def initialize_combobox(self, widget, default_value, options_range):
        """初始化ComboBox控件
        
        Args:
            widget: 控件对象
            default_value: 默认值，可以是字符串或整数
            options_range: 可选范围，如"0:3"或"0,1,2,3"
        """
        # 从全局combobox_options_map获取选项映射
        widget_name = widget.objectName()
        combobox_type = self._get_combobox_type(widget_name)
        options_map = self.combobox_options_map.get(combobox_type, None)
        
        # 清空当前项
        widget.clear()
        
        # 转换字符串值为整数
        if isinstance(default_value, str):
            if default_value.isdigit():
                value = int(default_value)
            else:
                # 尝试解析二进制字符串
                try:
                    value = int(default_value, 2)
                except ValueError:
                    # 无法解析，使用0
                    print(f"无法解析默认值 {default_value}，使用0")
                    value = 0
        else:
            # 已经是整数
            value = default_value
        
        try:
            # 如果存在映射，使用映射添加项
            if options_map and isinstance(options_map, dict):
                # 将映射项添加到ComboBox
                for i, text in sorted(options_map.items()):
                    widget.addItem(text, i)
                    
                # 找到默认值对应的索引
                default_index = -1
                for i in range(widget.count()):
                    if widget.itemData(i) == value:
                        default_index = i
                        break
                
                if default_index >= 0:
                    widget.blockSignals(True)
                    widget.setCurrentIndex(default_index)
                    widget.blockSignals(False)
                else:
                    # 没有找到匹配的索引，尝试直接设置索引
                    if 0 <= value < widget.count():
                        widget.blockSignals(True)
                        widget.setCurrentIndex(value)
                        widget.blockSignals(False)
                    else:
                        # 默认使用第一项
                        widget.blockSignals(True)
                        widget.setCurrentIndex(0)
                        widget.blockSignals(False)
            # 否则根据options_range添加项
            elif options_range:
                # 解析范围格式，可以是"0:3"或"0,1,2,3"
                if ':' in options_range:
                    # 范围格式，如"0:3"
                    start, end = map(int, options_range.split(':'))
                    for i in range(start, end + 1):
                        widget.addItem(str(i), i)
                elif ',' in options_range:
                    # 列表格式，如"0,1,2,3"
                    options = [int(opt.strip()) for opt in options_range.split(',')]
                    for i in options:
                        widget.addItem(str(i), i)
                else:
                    # 单个值
                    try:
                        single_option = int(options_range)
                        widget.addItem(str(single_option), single_option)
                    except ValueError:
                        # 非整数，作为文本添加
                        widget.addItem(options_range, 0)
                
                # 设置默认值
                if 0 <= value < widget.count():
                    widget.blockSignals(True)
                    widget.setCurrentIndex(value)
                    widget.blockSignals(False)
                else:
                    # 默认使用第一项
                    widget.blockSignals(True)
                    widget.setCurrentIndex(0)
                    widget.blockSignals(False)
            else:
                # 没有任何选项配置，添加一些默认选项
                widget.addItem("Option 0", 0)
                widget.addItem("Option 1", 1)
                
                # 设置默认值
                if value in [0, 1]:
                    widget.blockSignals(True)
                    widget.setCurrentIndex(value)
                    widget.blockSignals(False)
                else:
                    widget.blockSignals(True)
                    widget.setCurrentIndex(0)
                    widget.blockSignals(False)
                
        except Exception as e:
            print(f"初始化ComboBox {widget_name} 时发生错误: {str(e)}")
            
            # 错误恢复，添加默认选项
            widget.clear()
            widget.addItem("Error", 0)
            widget.blockSignals(True)
            widget.setCurrentIndex(0)
            widget.blockSignals(False)

    def initialize_spinbox(self, widget, default_value, options_range=None):
        """初始化spinbox控件
        
        Args:
            widget: 控件对象
            default_value: 默认值，可以是字符串或整数
            options_range: 可选范围，如"0:255"或"1:1000"
        """
        # 转换字符串值为整数
        if isinstance(default_value, str):
            if default_value.isdigit():
                value = int(default_value)
            else:
                # 尝试解析二进制字符串
                try:
                    value = int(default_value, 2)
                except ValueError:
                    # 无法解析，使用0
                    print(f"无法解析默认值 {default_value}，使用0")
                    value = 0
        else:
            # 已经是整数
            value = default_value
            
        # 设置范围
        if options_range:
            try:
                # 解析范围格式，如"0:255"或"1:1000"
                if isinstance(options_range, str) and ':' in options_range:
                    min_val, max_val = map(int, options_range.split(':'))
                    widget.setMinimum(min_val)
                    widget.setMaximum(max_val)
                # 应用值
                try:
                    widget.blockSignals(True)
                    widget.setValue(value)
                    widget.blockSignals(False)
                except Exception as e:
                    print(f"设置SpinBox值时出错: {e}")
            except Exception as e:
                print(f"解析SpinBox选项范围时出错: {e}")
                # 使用默认范围
                widget.setMinimum(0)
                widget.setMaximum(65535)
                
                # 确保值在有效范围内
                value = max(0, min(value, 65535))
                widget.blockSignals(True)
                widget.setValue(value)
                widget.blockSignals(False)

    def connect_widget_signals(self):
        """连接所有控件的信号到对应的处理函数"""
        try:
            # 特殊处理PLL2NDivider控件的信号连接
            if hasattr(self.ui, "PLL2NDivider"):
                # 断开任何现有连接，避免重复
                try:
                    self.ui.PLL2NDivider.valueChanged.disconnect()
                except Exception:
                    pass
                # 连接PLL2NDivider控件的信号
                self.ui.PLL2NDivider.valueChanged.connect(
                    lambda value: self.update_register_value("PLL2NDivider", value)
                )
                print("已特殊连接PLL2NDivider控件的信号")
            
            # 连接其他控件的信号
            for widget_name, widget_info in self.widget_register_map.items():
                # 跳过已连接的PLL2NDivider
                if widget_name == "PLL2NDivider":
                    continue
                    
                try:
                    widget = getattr(self.ui, widget_name)
                    widget_type = widget_info["widget_type"]
                    
                    # 根据控件类型连接不同的信号
                    if widget_type == "checkbox" and isinstance(widget, QtWidgets.QCheckBox):
                        # 检查控件名称是否以Enb结尾，应用反向逻辑
                        if widget_name.endswith("Enb"):
                            widget.stateChanged.connect(lambda state, w=widget_name: self.update_register_value(w, 0 if state == QtCore.Qt.Checked else 1))
                        else:
                            widget.stateChanged.connect(lambda state, w=widget_name: self.update_register_value(w, 1 if state == QtCore.Qt.Checked else 0))
                    elif widget_type == "spinbox" and isinstance(widget, QtWidgets.QSpinBox):
                        widget.valueChanged.connect(lambda value, w=widget_name: self.update_register_value(w, value))
                    elif widget_type == "combobox" and isinstance(widget, QtWidgets.QComboBox):
                        widget.currentIndexChanged.connect(lambda index, w=widget_name: self.update_register_value(w, index))
                    elif widget_type == "lineedit" and isinstance(widget, QtWidgets.QLineEdit):
                        widget.textChanged.connect(lambda text, w=widget_name: self.update_register_value(w, text))
                        
                    print(f"已连接控件 {widget_name} 的信号")
                except AttributeError:
                    print(f"控件 {widget_name} 不存在，无法连接信号")
                except Exception as e:
                    print(f"连接控件 {widget_name} 的信号时发生错误: {str(e)}")
            
            print("所有控件信号连接完成")
        except Exception as e:
            print(f"连接控件信号时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            
    def _update_pll2n_divider(self, value):
        """特殊处理PLL2NDivider值更新"""
        try:
            print(f"更新PLL2NDivider值: {value}")
            
            # 获取高2位和低16位
            high_bits = (value >> 16) & 0x3  # 仅保留高2位
            low_bits = value & 0xFFFF       # 仅保留低16位
            
            updated_registers = []
            
            # 更新0x76寄存器
            if "0x76" in self.registers:
                reg76 = self.registers["0x76"]
                # 获取更新前的值用于比较
                old_value = reg76.value
                
                # 更新PLL2_N[17:16]位
                reg76.set_bit_field_value("PLL2_N[17:16]", high_bits)
                
                # 检查值是否有变化
                if old_value != reg76.value:
                    # 发送寄存器更新信号到全局总线
                    RegisterUpdateBus.instance().register_updated.emit("0x76", reg76.value)
                    # 删除本地信号发送
                    # self.register_updated.emit("0x76", reg76.value)
                    updated_registers.append("0x76")
                    print(f"【PLL2NDivider特殊处理】已更新寄存器0x76的PLL2_N[17:16]位为 {high_bits}, 寄存器值从 {old_value} 变为 {reg76.value}")
                else:
                    print(f"【PLL2NDivider特殊处理】寄存器0x76的值未变化: {old_value}")
            else:
                print("【PLL2NDivider特殊处理】错误: 寄存器0x76不存在")
            
            # 更新0x77寄存器
            if "0x77" in self.registers:
                reg77 = self.registers["0x77"]
                # 获取更新前的值用于比较
                old_value = reg77.value
                
                # 更新PLL2_N[15:0]位
                reg77.set_bit_field_value("PLL2_N[15:0]", low_bits)
                
                # 检查值是否有变化
                if old_value != reg77.value:
                    # 发送寄存器更新信号到全局总线和主界面
                    RegisterUpdateBus.instance().register_updated.emit("0x77", reg77.value)
                    # 删除本地信号发送
                    # self.register_updated.emit("0x77", reg77.value)
                    updated_registers.append("0x77")
                    print(f"【PLL2NDivider特殊处理】已更新寄存器0x77的PLL2_N[15:0]位为 {low_bits}, 寄存器值从 {old_value} 变为 {reg77.value}")
                else:
                    print(f"【PLL2NDivider特殊处理】寄存器0x77的值未变化: {old_value}")
            else:
                print("【PLL2NDivider特殊处理】错误: 寄存器0x77不存在")
            
            # 更新映射表中的默认值
            if "PLL2NDivider" in self.widget_register_map:
                # 获取位宽
                bit_def = self.widget_register_map["PLL2NDivider"]["bit_def"]
                bit_width = len(bit_def.get("default", ""))
                # 更新默认值
                binary_value = format(value, f'0{bit_width}b')
                self.widget_register_map["PLL2NDivider"]["default_value"] = binary_value
                print(f"【PLL2NDivider特殊处理】已更新PLL2NDivider的默认值为 {binary_value}")
            else:
                print("【PLL2NDivider特殊处理】警告: PLL2NDivider未在widget_register_map中找到")
            
            # 额外通知主程序寄存器已更新
            if updated_registers:
                # 发送特殊通知给主程序
                try:
                    # 使用额外的信号通知寄存器更新
                    print(f"【PLL2NDivider特殊处理】通知已更新寄存器: {', '.join(updated_registers)}")
                    RegisterUpdateBus.instance().emit_multiple_registers_updated(updated_registers)
                except Exception as e:
                    print(f"【PLL2NDivider特殊处理】发送多寄存器更新通知时出错: {str(e)}")
            else:
                print("【PLL2NDivider特殊处理】没有寄存器值被更新")
            
            # 触发频率计算
            self.handle_register_value_change("PLL2NDivider")
            
        except Exception as e:
            print(f"【PLL2NDivider特殊处理】更新PLL2NDivider值时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def update_register_value(self, widget_name, value):
        """更新寄存器值并触发频率计算"""
        try:
            print(f"🔥 BaseHandler.update_register_value 被调用: widget_name={widget_name}, value={value}")
            print(f"更新控件 {widget_name} 的值: {value}")
            # 验证输入
            if widget_name not in self.widget_register_map:
                print(f"错误: 控件 {widget_name} 未在映射表中找到")
                return
            
            widget_info = self.widget_register_map[widget_name]
            reg_addr = widget_info["register_addr"]
            
            print(f"寄存器地址: {reg_addr}")
            
            # 处理特殊情况: PLL2NDivider
            if widget_name == "PLL2NDivider":
                self._update_pll2n_divider(value)
                return
            
            # 一般情况下根据控件类型处理
            widget_type = widget_info["widget_type"]
            bit_def = widget_info["bit_def"]
            
            # 获取位值的二进制表示
            if widget_type == "combobox":
                # 对于combobox，传入的value是选项索引
                binary_value = format(int(value), f'0{len(bit_def.get("default", ""))}b')
            elif widget_type == "checkable_combobox" and isinstance(value, bool):
                # 对于可选中的combobox，传入的value是bool类型
                binary_value = "1" if value else "0"
            elif widget_type == "spinbox":
                # 检查位宽
                bit_width = len(bit_def.get("default", ""))
                binary_value = format(int(value), f'0{bit_width}b')
            else:
                # 其他情况
                binary_value = format(int(value), f'0{len(bit_def.get("default", ""))}b')
            
            print(f"二进制值: {binary_value}")
            
            # 直接查找指定寄存器地址的寄存器对象
            register = self.registers.get(reg_addr)
            if not register:
                print(f"错误: 找不到寄存器 {reg_addr}")
                return

            # 查找对应的位字段
            bit_field_found = False
            for bit in register.bits:
                if bit.get("widget_name") == widget_name:
                    bit["default"] = binary_value
                    print(f"已更新寄存器 {reg_addr} 的位 {bit['name']} 的值: {binary_value}")

                    # 获取更新前的寄存器值
                    old_value = register.value
                    print(f"更新前寄存器 {reg_addr} 的值: 0x{old_value:04X}")

                    # 调试：检查寄存器对象类型
                    print(f"寄存器对象类型: {type(register)}")
                    print(f"寄存器对象是否有set_bit_field_value方法: {hasattr(register, 'set_bit_field_value')}")

                    # 设置位字段值
                    bit_name = bit["name"]
                    bit_value = int(binary_value, 2)
                    print(f"调用set_bit_field_value: bit_name={bit_name}, value={bit_value}")

                    try:
                        # 检查寄存器对象的方法
                        if hasattr(register, 'set_bit_field_value'):
                            print(f"调用register.set_bit_field_value({bit_name}, {bit_value})")
                            result = register.set_bit_field_value(bit_name, bit_value)
                            print(f"set_bit_field_value返回值: {result}")
                        else:
                            print(f"错误: 寄存器对象没有set_bit_field_value方法")
                            print(f"寄存器对象可用方法: {[method for method in dir(register) if not method.startswith('_')]}")

                    except Exception as set_error:
                        print(f"调用set_bit_field_value时发生错误: {str(set_error)}")
                        import traceback
                        traceback.print_exc()

                    # 获取更新后的寄存器值
                    new_value = register.value
                    print(f"更新后寄存器 {reg_addr} 的值: 0x{new_value:04X}")

                    # 验证位字段值是否正确设置
                    bit_field_value = register.get_bit_field_value(bit["name"])
                    print(f"验证位字段 {bit['name']} 的值: {bit_field_value}")

                    # 设置一个标志，防止接收自己发送的更新信号
                    self._updating_from_widget = True

                    # 发送寄存器更新信号到全局总线
                    RegisterUpdateBus.instance().register_updated.emit(reg_addr, register.value)
                    bit_field_found = True
                    break

            if not bit_field_found:
                print(f"错误: 在寄存器 {reg_addr} 中找不到控件 {widget_name} 对应的位字段")
            
            print(f"已更新控件 {widget_name} 的值: {binary_value}")

            # 检查是否启用自动写入功能
            if self._is_auto_write_enabled():
                # 再次确认寄存器值已正确更新
                final_register_value = register.value
                print(f"自动写入前最终确认寄存器 {reg_addr} 的值: 0x{final_register_value:04X}")

                # 如果寄存器值没有正确更新，使用新计算的值作为备选方案
                if bit_field_found and final_register_value != new_value:
                    print(f"警告: 寄存器值不一致，使用新计算的值: 0x{new_value:04X}")
                    final_register_value = new_value

                # 自动写入寄存器到芯片
                self._auto_write_register_to_chip(reg_addr, final_register_value)

            # 子类需要覆盖此方法以处理频率计算等特定功能
            self.handle_register_value_change(widget_name)
        except Exception as e:
            print(f"更新寄存器值时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def _is_auto_write_enabled(self):
        """检查是否启用自动写入功能

        Returns:
            bool: 如果启用自动写入功能则返回True
        """
        try:
            # 获取主窗口实例
            main_window = None
            if hasattr(self, 'main_window'):
                main_window = self.main_window
            elif hasattr(self, 'parent') and hasattr(self.parent, 'main_window'):
                main_window = self.parent.main_window
            elif hasattr(self, 'parent') and hasattr(self.parent, 'parent'):
                main_window = self.parent.parent

            if not main_window:
                return False

            # 检查是否有自动写入模式设置
            return getattr(main_window, 'auto_write_mode', False)

        except Exception as e:
            print(f"检查自动写入设置时发生错误: {str(e)}")
            return False

    def _auto_write_register_to_chip(self, reg_addr, reg_value):
        """自动将寄存器值写入到芯片

        Args:
            reg_addr: 寄存器地址
            reg_value: 寄存器值
        """
        try:
            # 获取主窗口实例
            main_window = None
            if hasattr(self, 'main_window'):
                main_window = self.main_window
            elif hasattr(self, 'parent') and hasattr(self.parent, 'main_window'):
                main_window = self.parent.main_window
            elif hasattr(self, 'parent') and hasattr(self.parent, 'parent'):
                main_window = self.parent.parent

            if not main_window:
                print("无法获取主窗口实例，跳过自动写入")
                return

            # 检查是否有寄存器操作服务
            if not hasattr(main_window, 'register_service'):
                print("寄存器操作服务不可用，跳过自动写入")
                return

            # 执行写入操作
            print(f"自动写入寄存器 {reg_addr} = 0x{reg_value:04X} 到芯片")
            main_window.register_service.write_register(reg_addr, reg_value)

        except Exception as e:
            print(f"自动写入寄存器时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def handle_register_value_change(self, widget_name):
        """处理寄存器值变化的回调函数，子类应覆盖此方法以实现特定功能"""
        pass

    def on_global_register_updated(self, reg_addr, reg_value):
        """处理从全局总线发送的寄存器更新信号
        
        Args:
            reg_addr: 寄存器地址
            reg_value: 寄存器新值
        """
        try:
            print(f'接收到全局寄存器更新信号: 寄存器={reg_addr}, 值={reg_value}')

            # 检查是否是自己发送的更新信号
            if hasattr(self, '_updating_from_widget') and self._updating_from_widget:
                print(f"忽略自己发送的更新信号: {reg_addr}")
                self._updating_from_widget = False  # 重置标志
                return

            # 首先同步工具窗口中的寄存器对象值
            if reg_addr in self.registers:
                old_value = self.registers[reg_addr].value
                self.registers[reg_addr].value = reg_value
                print(f"同步工具窗口寄存器 {reg_addr} 值: 0x{old_value:04X} → 0x{reg_value:04X}")

            # 查找所有对应该寄存器的控件
            affected_widgets = []
            for widget_name, widget_info in self.widget_register_map.items():
                if widget_info["register_addr"] == reg_addr:
                    affected_widgets.append(widget_name)

            if not affected_widgets:
                print(f"没有控件对应寄存器 {reg_addr}")
                return

            print(f"需要更新的控件: {affected_widgets}")
            
            # 更新所有受影响的控件
            for widget_name in affected_widgets:
                # 获取控件对应的位字段名称
                bit_field_name = None
                for bit in self.registers[reg_addr].bits:
                    if bit.get("widget_name") == widget_name:
                        bit_field_name = bit["name"]
                        break
                
                if bit_field_name:
                    # 更新控件状态
                    self.update_all_widgets_for_register_bit(reg_addr, bit_field_name)
            
            # 处理全局更新的附加效果
            self.handle_global_register_update(reg_addr, reg_value)
                    
        except Exception as e:
            print(f"处理全局寄存器更新信号时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def handle_global_register_update(self, reg_addr, reg_value):
        """处理全局寄存器更新的附加效果，子类应覆盖此方法以实现特定功能"""
        pass

    def update_all_widgets_for_register_bit(self, reg_addr, bit_field_name):
        """更新所有对应同一寄存器位的控件
        
        Args:
            reg_addr: 寄存器地址
            bit_field_name: 位字段名称
        """
        try:
            # 获取寄存器对象
            register = None
            for addr, reg in self.registers.items():
                if addr == reg_addr:
                    register = reg
                    break
            
            if not register:
                print(f"找不到寄存器: {reg_addr}")
                return
                
            # 获取位字段值
            bit_value = register.get_bit_field_value(bit_field_name)
            print(f"寄存器 {reg_addr} 的位字段 {bit_field_name} 值为 {bit_value}")
            
            # 查找所有对应该寄存器位的控件
            for widget_name, widget_info in self.widget_register_map.items():
                if widget_info["register_addr"] == reg_addr:
                    bit_def = widget_info["bit_def"]
                    if bit_def.get("name") == bit_field_name:
                        # 获取控件
                        try:
                            widget = getattr(self.ui, widget_name)
                            widget_type = widget_info["widget_type"]
                            
                            # 阻断信号以避免循环更新
                            widget.blockSignals(True)
                            
                            # 根据控件类型更新UI
                            if widget_type == "checkbox" and isinstance(widget, QtWidgets.QCheckBox):
                                # 检查控件名称是否以Enb结尾，应用反向逻辑
                                if widget_name.endswith("Enb"):
                                    widget.setChecked(bit_value == 0)
                                else:
                                    widget.setChecked(bit_value == 1)
                                    
                            elif widget_type == "spinbox" and isinstance(widget, QtWidgets.QSpinBox):
                                widget.setValue(bit_value)
                                
                            elif widget_type == "combobox" and isinstance(widget, QtWidgets.QComboBox):
                                if bit_value < widget.count():
                                    widget.setCurrentIndex(bit_value)
                                    
                            elif widget_type == "lineedit" and isinstance(widget, QtWidgets.QLineEdit):
                                widget.setText(str(bit_value))
                                
                            # 恢复信号连接
                            widget.blockSignals(False)
                            
                            # 更新映射表中的默认值
                            bit_width = len(widget_info["default_value"])
                            if widget_type in ["checkbox", "spinbox", "combobox"]:
                                binary_value = format(bit_value, f'0{bit_width}b')
                                self.widget_register_map[widget_name]["default_value"] = binary_value
                            
                            print(f"已更新控件 {widget_name} 的UI状态为 {bit_value}")
                            
                        except AttributeError:
                            print(f"控件 {widget_name} 不存在")
                        except Exception as e:
                            print(f"更新控件 {widget_name} 时发生错误: {str(e)}")
        except Exception as e:
            print(f"更新寄存器控件时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            
    # 子类需要实现的方法
    def calculate_output_frequencies(self):
        """计算输出频率，子类必须实现此方法"""
        raise NotImplementedError("子类必须实现calculate_output_frequencies方法")

    def register_cross_register_control(self, widget_name, bit_fields, reg_addrs, default_value="0", widget_type="spinbox", options="0:65535"):
        """注册跨寄存器控件
        
        Args:
            widget_name: 控件名称
            bit_fields: 关联的位字段列表，从高位到低位排序
            reg_addrs: 关联的寄存器地址列表，从高位到低位排序
            default_value: 默认值
            widget_type: 控件类型
            options: 选项范围
        """
        try:
            if not hasattr(self.ui, widget_name):
                print(f"【跨寄存器控件】警告: 控件 {widget_name} 不存在")
                return False
                
            # 如果控件已在映射表中，则跳过
            if widget_name in self.widget_register_map:
                print(f"【跨寄存器控件】控件 {widget_name} 已在映射表中")
                return True
                
            # 确保bit_fields和reg_addrs长度一致
            if len(bit_fields) != len(reg_addrs):
                print("【跨寄存器控件】错误: bit_fields和reg_addrs长度不一致")
                return False
                
            # 创建映射条目
            self.widget_register_map[widget_name] = {
                "register_addr": reg_addrs[0],  # 使用第一个寄存器地址作为主地址
                "widget_type": widget_type,
                "default_value": default_value,
                "bit_def": {
                    "options": options,
                    "default": default_value,
                    "name": "_".join(bit_fields),  # 组合名称
                    "is_cross_register": True,     # 特殊标记，表示跨寄存器字段
                    "bits": bit_fields,            # 存储相关联的寄存器位字段
                    "registers": reg_addrs         # 存储相关联的寄存器地址
                }
            }
            
            print(f"【跨寄存器控件】已注册控件 {widget_name}, 关联寄存器: {', '.join(reg_addrs)}")
            return True
            
        except Exception as e:
            print(f"【跨寄存器控件】注册跨寄存器控件 {widget_name} 时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return False