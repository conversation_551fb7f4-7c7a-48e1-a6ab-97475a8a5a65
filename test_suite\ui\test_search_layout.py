#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试搜索框布局
验证搜索框是否与其他控件在同一行显示
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QLineEdit, QLabel
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt

def test_search_layout():
    """测试搜索框布局是否正确"""
    app = QApplication(sys.argv)
    
    try:
        # 创建主窗口
        from ui.windows.RegisterMainWindow import RegisterMainWindow

        # 现在使用依赖注入，不需要repository参数
        main_window = RegisterMainWindow()
        main_window.show()
        
        # 等待界面完全加载
        QTest.qWait(2000)
        
        # 查找搜索相关控件
        search_edit = None
        search_label = None
        
        # 查找所有QLineEdit
        line_edits = main_window.findChildren(QLineEdit)
        for edit in line_edits:
            placeholder = edit.placeholderText()
            if "位段" in placeholder or "搜索" in placeholder:
                search_edit = edit
                break
        
        # 查找所有QLabel
        labels = main_window.findChildren(QLabel)
        for label in labels:
            text = label.text()
            if "搜索位段" in text:
                search_label = label
                break
        
        print("搜索控件检查:")
        if search_edit:
            print(f"✅ 找到搜索输入框: {search_edit.placeholderText()}")
            print(f"   位置: ({search_edit.x()}, {search_edit.y()})")
            print(f"   大小: {search_edit.width()} x {search_edit.height()}")
        else:
            print("❌ 未找到搜索输入框")
            
        if search_label:
            print(f"✅ 找到搜索标签: {search_label.text()}")
            print(f"   位置: ({search_label.x()}, {search_label.y()})")
        else:
            print("❌ 未找到搜索标签")
        
        # 检查其他控件
        print("\n其他控件检查:")
        addr_edits = []
        value_edits = []

        for edit in line_edits:
            placeholder = edit.placeholderText()
            print(f"   输入框占位符: '{placeholder}' 位置({edit.x()}, {edit.y()})")
            if placeholder == "0x00":
                addr_edits.append(edit)
                print(f"✅ 地址输入框: 位置({edit.x()}, {edit.y()})")
            elif placeholder == "0x0000":
                value_edits.append(edit)
                print(f"✅ 值输入框: 位置({edit.x()}, {edit.y()})")

        # 检查布局是否在同一行
        if search_edit and addr_edits and value_edits:
            # 取第一个地址和值输入框
            addr_edit = addr_edits[0]
            value_edit = value_edits[0]

            addr_y = addr_edit.y()
            value_y = value_edit.y()
            search_y = search_edit.y()

            print(f"\n布局检查:")
            print(f"地址输入框 Y 坐标: {addr_y}")
            print(f"值输入框 Y 坐标: {value_y}")
            print(f"搜索输入框 Y 坐标: {search_y}")

            # 允许一些像素的误差
            tolerance = 10
            if abs(addr_y - search_y) <= tolerance and abs(value_y - search_y) <= tolerance:
                print("✅ 成功！所有控件都在同一行")
                return True
            else:
                print("❌ 控件不在同一行")
                return False
        else:
            print("❌ 无法找到所有必要的控件")
            return False
            
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        app.quit()

if __name__ == "__main__":
    success = test_search_layout()
    sys.exit(0 if success else 1)
