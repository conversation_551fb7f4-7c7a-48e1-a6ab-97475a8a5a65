#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试时钟窗口打开时的警告问题
验证原始的"控件 X 的bit_value超出范围"警告是否已解决
"""

import sys
import os
import json
import logging
import io
from contextlib import redirect_stderr

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from utils.Log import get_module_logger

logger = get_module_logger(__name__)
from core.services.register.RegisterManager import RegisterManager
from ui.handlers.ModernClkinControlHandler import ModernClkinControlHandler

def capture_warnings():
    """捕获警告信息"""
    # 创建一个StringIO对象来捕获日志输出
    log_capture = io.StringIO()
    
    # 创建一个处理器来捕获日志
    handler = logging.StreamHandler(log_capture)
    handler.setLevel(logging.WARNING)
    
    # 添加处理器到logger
    logger.addHandler(handler)
    
    try:
        print("=" * 60)
        print("测试时钟窗口打开时的警告问题")
        print("=" * 60)
        
        # 1. 创建QApplication
        print("\n1. 创建QApplication...")
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        print("   ✓ QApplication创建成功")
        
        # 2. 加载寄存器配置
        print("\n2. 加载寄存器配置...")
        config_path = os.path.join('lib', 'register.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        print(f"   ✓ 已加载 {len(registers_config)} 个寄存器配置")
        
        # 3. 创建RegisterManager
        print("\n3. 创建RegisterManager...")
        register_manager = RegisterManager(registers_config)
        print("   ✓ RegisterManager创建成功")
        
        # 4. 模拟一些寄存器值（这些值会导致原始的警告）
        print("\n4. 设置可能导致警告的寄存器值...")
        
        # 设置一些可能超出ComboBox范围的值
        test_values = {
            "0x48": 0x05,  # OSCoutClockFormat = 5, OSCoutMux = 0
            "0x55": 0x00,  # syncSource = 0
            "0x57": 0x12,  # CLKinSelManual = 1, CLKin1Demux = 2, CLKin0Demux = 2
            "0x58": 0x20,  # CLKinSel0Mux = 0, CLKinSel0Type = 2
            "0x59": 0x20,  # CLKinSel1Mux = 0, CLKinSel1Type = 2
            "0x5B": 0x00,  # LOSTimeout = 0
            "0x60": 0x00,  # CLKin1Type = 0
        }
        
        for addr, value in test_values.items():
            register_manager.set_register_value(addr, value)
            print(f"   设置寄存器 {addr} = 0x{value:04X}")
        
        print("   ✓ 寄存器值设置完成")
        
        # 5. 创建现代化时钟输入控制处理器（这里应该不会有警告了）
        print("\n5. 创建现代化时钟输入控制处理器...")
        modern_handler = ModernClkinControlHandler(None, register_manager)
        
        # 等待定时器执行
        print("   等待初始化完成...")
        import time
        time.sleep(0.3)  # 等待300ms让定时器执行
        app.processEvents()  # 处理事件
        
        print("   ✓ 处理器创建成功")
        
        # 6. 检查所有相关的ComboBox控件
        print("\n6. 检查ComboBox控件状态...")
        
        # 这些是在原始警告中提到的控件
        warning_controls = [
            "OSCoutMux", "OSCoutClockFormat", "syncSource", 
            "CLKinSelManual", "CLKin1Demux", "CLKin0Demux",
            "CLKinSel0Mux", "CLKinSel0Type", "CLKinSel1Mux", 
            "CLKinSel1Type", "LOSTimeout", "CLKin1Type"
        ]
        
        success_count = 0
        total_count = len(warning_controls)
        
        for control_name in warning_controls:
            if hasattr(modern_handler.ui, control_name):
                control = getattr(modern_handler.ui, control_name)
                if hasattr(control, 'count'):
                    item_count = control.count()
                    current_index = control.currentIndex()
                    
                    if item_count > 0 and 0 <= current_index < item_count:
                        print(f"   ✓ {control_name}: {item_count}项, 当前索引={current_index} (正常)")
                        success_count += 1
                    elif item_count > 0:
                        print(f"   ⚠ {control_name}: {item_count}项, 当前索引={current_index} (索引可能有问题)")
                        success_count += 1  # 至少有选项了
                    else:
                        print(f"   ❌ {control_name}: 没有选项!")
                else:
                    print(f"   ⚠ {control_name}: 不是ComboBox控件")
            else:
                print(f"   ❌ {control_name}: 控件不存在")
        
        print(f"\n   总结: {success_count}/{total_count} 个控件状态正常")
        
        # 7. 检查是否有待设置的ComboBox值
        print("\n7. 检查待设置的ComboBox值...")
        if hasattr(modern_handler, '_pending_combobox_values'):
            pending_count = len(modern_handler._pending_combobox_values)
            if pending_count > 0:
                print(f"   ⚠ 仍有 {pending_count} 个待设置的ComboBox值:")
                for widget_name, value in modern_handler._pending_combobox_values.items():
                    print(f"     - {widget_name}: {value}")
            else:
                print("   ✓ 没有待设置的ComboBox值")
        else:
            print("   ✓ 没有待设置的ComboBox值（属性不存在）")
        
        # 8. 检查捕获的警告
        print("\n8. 检查捕获的警告...")
        log_output = log_capture.getvalue()
        
        # 查找特定的警告模式
        warning_lines = [line for line in log_output.split('\n') if '的bit_value超出范围' in line]
        
        if warning_lines:
            print(f"   ❌ 发现 {len(warning_lines)} 个bit_value超出范围的警告:")
            for line in warning_lines:
                print(f"     {line.strip()}")
            return False
        else:
            print("   ✓ 没有发现bit_value超出范围的警告")
        
        print("\n" + "=" * 60)
        print("测试完成")
        print("=" * 60)
        
        return success_count == total_count and len(warning_lines) == 0
        
    finally:
        # 移除处理器
        logger.removeHandler(handler)
        log_capture.close()

if __name__ == "__main__":
    success = capture_warnings()
    if success:
        print("\n🎉 测试成功！ComboBox初始化警告问题已解决！")
    else:
        print("\n❌ 测试失败，仍有问题需要解决。")
    
    sys.exit(0 if success else 1)
