#!/usr/bin/env python3
"""
测试平衡的COM端口布局
验证COM端口下拉框不会覆盖搜索框
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QLabel
from PyQt5.QtCore import Qt

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.Log import get_module_logger

logger = get_module_logger(__name__)


def test_balanced_layout():
    """测试平衡的布局"""
    try:
        print("🎯 测试平衡的COM端口布局")
        print("=" * 50)
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("平衡布局测试")
        main_window.resize(1200, 200)  # 设置合适的窗口大小
        
        # 创建中央控件
        central_widget = QWidget()
        main_window.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 添加说明标签
        title_label = QLabel("平衡布局测试 - COM端口不应覆盖搜索框")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2E86AB;
                padding: 10px;
                background-color: #F0F8FF;
                border: 2px solid #E0E0E0;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # 创建现代化RegisterIOHandler
        print("创建现代化RegisterIOHandler...")
        from ui.handlers.ModernRegisterIOHandler import ModernRegisterIOHandler
        io_handler = ModernRegisterIOHandler.create_for_testing(main_window)
        
        # 模拟真实的端口数据
        demo_ports = [
            {'device': 'COM3', 'description': 'USB Serial Port (COM3) - Silicon Labs CP210x USB to UART Bridge'},
            {'device': 'COM4', 'description': 'Bluetooth Serial Port (COM4) - Standard Serial over Bluetooth link'},
            {'device': 'COM5', 'description': 'Virtual Serial Port (COM5) - USB-SERIAL CH340 (COM5)'},
            {'device': 'COM6', 'description': 'Arduino Uno (COM6) - USB Serial Device'}
        ]
        
        # 更新端口列表
        io_handler._update_port_combo(demo_ports)
        
        # 设置一些示例数据
        io_handler.set_address_display(0x57)
        io_handler.set_value_display(0x1300)
        
        # 获取IO控件并添加到布局
        io_widget = io_handler.get_io_widget()
        layout.addWidget(io_widget)
        
        # 检查控件尺寸
        if hasattr(io_handler, 'port_combo') and io_handler.port_combo:
            combo = io_handler.port_combo
            print(f"✅ COM端口下拉框尺寸:")
            print(f"   最小宽度: {combo.minimumWidth()}px")
            print(f"   最大宽度: {combo.maximumWidth()}px")
            print(f"   当前宽度: {combo.width()}px")
            
        if hasattr(io_handler, 'search_edit') and io_handler.search_edit:
            search = io_handler.search_edit
            print(f"✅ 搜索框尺寸:")
            print(f"   最小宽度: {search.minimumWidth()}px")
            print(f"   当前宽度: {search.width()}px")
        
        # 添加改进说明
        improvements_label = QLabel("""
        <h3>🎯 平衡布局改进：</h3>
        <ul>
        <li><b>COM端口下拉框</b>：宽度调整为350-450px，既能显示端口信息又不过宽</li>
        <li><b>大小策略</b>：设置为Fixed，避免过度扩展覆盖其他控件</li>
        <li><b>搜索框保护</b>：确保COM端口下拉框不会覆盖搜索框区域</li>
        <li><b>布局平衡</b>：各控件之间有合理的间隔和比例</li>
        </ul>
        """)
        improvements_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                padding: 15px;
                background-color: #F9F9F9;
                border: 1px solid #DDD;
                border-radius: 5px;
                margin-top: 10px;
            }
        """)
        layout.addWidget(improvements_label)
        
        print("✅ 平衡布局测试窗口创建完成")
        print("\n请观察以下改进效果：")
        print("1. COM端口下拉框现在是适中的宽度（350-450px）")
        print("2. 不会覆盖搜索框或其他控件")
        print("3. 仍然能够显示完整的端口描述信息")
        print("4. 整体布局更加平衡和协调")
        
        # 显示窗口
        main_window.show()
        
        return app, main_window
        
    except Exception as e:
        logger.error(f"创建平衡布局测试窗口时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None


if __name__ == "__main__":
    app, window = test_balanced_layout()
    
    if app and window:
        print("\n🚀 平衡布局测试窗口已打开")
        print("请检查COM端口下拉框是否不再覆盖搜索框")
        print("按 Ctrl+C 或关闭窗口退出测试")
        
        try:
            sys.exit(app.exec_())
        except KeyboardInterrupt:
            print("\n测试结束")
    else:
        print("❌ 平衡布局测试窗口创建失败")
