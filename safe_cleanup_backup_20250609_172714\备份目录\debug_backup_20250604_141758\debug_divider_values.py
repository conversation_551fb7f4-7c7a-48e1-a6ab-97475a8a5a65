#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试分频器值获取
检查分频器值是否正确获取
"""

import sys
import os
import json
from PyQt5.QtWidgets import QApplication

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ui.handlers.ModernClkOutputsHandler import ModernClkOutputsHandler
from core.services.register.RegisterManager import RegisterManager
from utils.Log import logger

def debug_divider_values():
    """调试分频器值获取"""
    print("=" * 60)
    print("调试分频器值获取")
    print("=" * 60)
    
    try:
        # 创建QApplication
        app = QApplication(sys.argv)
        
        print("1. 加载寄存器配置...")
        config_path = os.path.join('lib', 'register.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        print("2. 创建RegisterManager...")
        register_manager = RegisterManager(registers_config)
        
        print("3. 创建现代化时钟输出处理器...")
        modern_handler = ModernClkOutputsHandler(None, register_manager)
        
        print("4. 检查分频器值...")
        
        # 检查UI控件中的分频器值
        print("\n--- UI控件分频器值 ---")
        divider_names = ["DCLK0_1DIV", "DCLK2_3DIV", "DCLK4_5DIV", "DCLK6_7DIV", 
                        "DCLK8_9DIV", "DCLK10_11DIV", "DCLK12_13DIV"]
        
        for div_name in divider_names:
            if hasattr(modern_handler.ui, div_name):
                widget = getattr(modern_handler.ui, div_name)
                ui_value = widget.value() if hasattr(widget, 'value') else "N/A"
                print(f"   {div_name}: UI值 = {ui_value}")
            else:
                print(f"   {div_name}: UI控件不存在")
        
        # 检查寄存器中的分频器值
        print("\n--- 寄存器分频器值 ---")
        for div_name in divider_names:
            if div_name in modern_handler.widget_register_map:
                widget_info = modern_handler.widget_register_map[div_name]
                reg_addr = widget_info["register_addr"]
                bit_def = widget_info["bit_def"]
                bit_name = bit_def.get("name", "")
                
                if bit_name:
                    reg_value = register_manager.get_bit_field_value(reg_addr, bit_name)
                    # 确保reg_addr是整数
                    addr_int = int(reg_addr, 16) if isinstance(reg_addr, str) else reg_addr
                    print(f"   {div_name}: 寄存器值 = {reg_value} (地址: 0x{addr_int:02X}, 位: {bit_name})")
                else:
                    print(f"   {div_name}: 位名称未找到")
            else:
                print(f"   {div_name}: 寄存器映射不存在")
        
        # 检查通过方法获取的分频器值
        print("\n--- 方法获取的分频器值 ---")
        for output_num in range(14):
            divider_name = modern_handler._get_divider_name_for_output(output_num)
            divider_value = modern_handler._get_divider_value_for_output(output_num)
            print(f"   输出{output_num}: {divider_name} = {divider_value}")
        
        # 检查频率计算
        print("\n--- 频率计算检查 ---")
        vco_freq = modern_handler.fvco
        print(f"   VCO频率: {vco_freq} MHz")
        
        for output_num in range(4):  # 只检查前4个
            output_attr = f"lineEditFout{output_num}Output"
            if hasattr(modern_handler.ui, output_attr):
                output_widget = getattr(modern_handler.ui, output_attr)
                displayed_freq = output_widget.text()
                
                divider_value = modern_handler._get_divider_value_for_output(output_num)
                calculated_freq = vco_freq / divider_value if divider_value > 0 else 0
                
                print(f"   输出{output_num}:")
                print(f"     显示频率: {displayed_freq} MHz")
                print(f"     计算频率: {calculated_freq:.2f} MHz (VCO: {vco_freq} / 分频: {divider_value})")
                print(f"     匹配: {'✓' if abs(float(displayed_freq) - calculated_freq) < 0.01 else '❌'}")
        
        print("\n" + "=" * 60)
        print("调试完成")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_divider_values()
