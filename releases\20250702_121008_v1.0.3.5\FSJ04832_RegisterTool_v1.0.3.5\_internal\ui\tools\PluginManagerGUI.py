#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
插件管理器GUI - 现代化界面
提供插件的可视化管理界面
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QTextEdit, QMessageBox, QSplitter, QFrame,
                            QCheckBox, QPlainTextEdit, QScrollArea)
from PyQt5.QtCore import pyqtSignal, Qt
from PyQt5.QtGui import QFont
from core.services.plugin.PluginManager import plugin_manager, PluginInfo
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class PluginCard(QFrame):
    """插件卡片组件 - 简洁风格"""

    plugin_toggled = pyqtSignal(str, bool)  # 插件名称, 启用状态
    plugin_selected = pyqtSignal(str)  # 插件名称

    def __init__(self, plugin_info: PluginInfo, parent=None):
        super().__init__(parent)
        self.plugin_info = plugin_info
        self.selected = False
        self._setup_ui()

    def _setup_ui(self):
        """设置卡片UI - 简洁风格"""
        self.setFrameStyle(QFrame.Box)
        self.setLineWidth(1)
        self.setFixedHeight(95)  # 增加高度以适应更大字体
        self.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #E0E0E0;
                border-radius: 5px;
                margin: 1px;
            }
            QFrame:hover {
                background-color: #F0F8FF;
                border-color: #2E86AB;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 6, 10, 6)
        layout.setSpacing(3)

        # 顶部：名称和启用开关
        top_layout = QHBoxLayout()

        # 插件名称
        self.name_label = QLabel(self.plugin_info.name)
        name_font = QFont()
        name_font.setBold(True)
        name_font.setPointSize(10)  # 与主软件字体大小一致
        self.name_label.setFont(name_font)
        self.name_label.setStyleSheet("color: #2E86AB;")  # 使用主软件的蓝色
        top_layout.addWidget(self.name_label)

        top_layout.addStretch()

        # 启用开关
        self.enable_checkbox = QCheckBox()
        self.enable_checkbox.setChecked(self.plugin_info.enabled)
        self.enable_checkbox.stateChanged.connect(self._on_toggle)
        top_layout.addWidget(self.enable_checkbox)

        layout.addLayout(top_layout)

        # 版本和状态行
        info_layout = QHBoxLayout()

        self.version_label = QLabel(f"版本: {self.plugin_info.version}")
        self.version_label.setStyleSheet("color: #666666; font-size: 10pt;")  # 增大字体
        info_layout.addWidget(self.version_label)

        info_layout.addStretch()

        # 状态指示器
        status = "已启用" if self.plugin_info.enabled else "已禁用"
        if self.plugin_info.enabled and self.plugin_info.instance is None:
            status = "启用失败"

        self.status_label = QLabel(status)
        if status == "已启用":
            self.status_label.setStyleSheet("color: #28a745; font-size: 10pt; font-weight: bold;")  # 增大字体
        elif status == "启用失败":
            self.status_label.setStyleSheet("color: #dc3545; font-size: 10pt; font-weight: bold;")  # 增大字体
        else:
            self.status_label.setStyleSheet("color: #666666; font-size: 10pt;")  # 增大字体

        info_layout.addWidget(self.status_label)

        layout.addLayout(info_layout)

        # 简短描述
        desc_text = self.plugin_info.description[:50] + "..." if len(self.plugin_info.description) > 50 else self.plugin_info.description
        self.desc_label = QLabel(desc_text)
        self.desc_label.setWordWrap(True)
        self.desc_label.setStyleSheet("color: #666666; font-size: 9pt;")  # 增大字体
        self.desc_label.setMaximumHeight(25)  # 增加高度
        layout.addWidget(self.desc_label)

    def _on_toggle(self, state):
        """处理启用状态切换"""
        enabled = state == Qt.Checked
        self.plugin_toggled.emit(self.plugin_info.name, enabled)

    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.LeftButton:
            self.plugin_selected.emit(self.plugin_info.name)
            self.set_selected(True)
        super().mousePressEvent(event)

    def set_selected(self, selected):
        """设置选中状态"""
        self.selected = selected
        if selected:
            self.setStyleSheet("""
                QFrame {
                    background-color: #E8F4FD;
                    border: 2px solid #2E86AB;
                    border-radius: 5px;
                    margin: 1px;
                }
            """)
        else:
            self.setStyleSheet("""
                QFrame {
                    background-color: #ffffff;
                    border: 1px solid #E0E0E0;
                    border-radius: 5px;
                    margin: 1px;
                }
                QFrame:hover {
                    background-color: #F0F8FF;
                    border-color: #2E86AB;
                }
            """)

    def update_status(self):
        """更新状态显示"""
        self.enable_checkbox.setChecked(self.plugin_info.enabled)

        status = "已启用" if self.plugin_info.enabled else "已禁用"
        if self.plugin_info.enabled and self.plugin_info.instance is None:
            status = "启用失败"

        self.status_label.setText(status)
        if status == "已启用":
            self.status_label.setStyleSheet("color: #28a745; font-size: 10pt; font-weight: bold;")
        elif status == "启用失败":
            self.status_label.setStyleSheet("color: #dc3545; font-size: 10pt; font-weight: bold;")
        else:
            self.status_label.setStyleSheet("color: #6c757d; font-size: 10pt;")


class PluginManagerWindow(QWidget):
    """插件管理器窗口 - 与主软件风格统一"""

    window_closed = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("插件管理器")
        self.resize(900, 600)  # 调整为更合适的尺寸
        self.plugin_cards = {}
        self.selected_plugin = None

        # 设置窗口属性 - 使其可移动和独立
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint | Qt.WindowMinimizeButtonHint)

        # 设置应用程序字体（与主软件一致）
        app_font = QFont()
        app_font.setFamily("Microsoft YaHei")
        app_font.setPointSize(9)  # 与主软件配置一致
        self.setFont(app_font)

        # 设置窗口样式（与主软件风格一致）
        self.setStyleSheet("""
            QWidget {
                background-color: #ffffff;
                font-family: 'Microsoft YaHei';
                font-size: 9pt;
            }
            QGroupBox {
                font-size: 13px;
                font-weight: bold;
                color: #2E86AB;
                border: 2px solid #E0E0E0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: white;
            }
        """)

        # 设置UI
        self._setup_ui()

        # 刷新插件列表
        self._refresh_plugin_list()
        
    def _setup_ui(self):
        """设置UI - 与主软件风格一致"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # 主要内容区域
        content_splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(content_splitter)

        # 左侧：插件列表
        self._create_plugin_list_panel(content_splitter)

        # 右侧：插件详情
        self._create_plugin_details_panel(content_splitter)

        # 设置分割器比例
        content_splitter.setSizes([500, 400])

        # 底部控制按钮
        self._create_control_buttons(main_layout)

    def _create_plugin_list_panel(self, parent):
        """创建插件列表面板 - 使用GroupBox风格"""
        from PyQt5.QtWidgets import QGroupBox, QLineEdit

        # 主容器
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(10)

        # 插件列表组
        plugin_group = QGroupBox("已安装的插件")
        plugin_layout = QVBoxLayout(plugin_group)

        # 搜索栏
        search_layout = QHBoxLayout()
        search_label = QLabel("搜索:")
        search_label.setStyleSheet("font-weight: normal; color: #333333; font-size: 10pt;")  # 增大字体
        search_layout.addWidget(search_label)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入插件名称进行搜索...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #E0E0E0;
                border-radius: 3px;
                padding: 4px 8px;
                font-size: 10pt;
            }
            QLineEdit:focus {
                border-color: #2E86AB;
            }
        """)
        self.search_input.textChanged.connect(self._filter_plugins)
        search_layout.addWidget(self.search_input)

        plugin_layout.addLayout(search_layout)

        # 统计信息
        self.stats_label = QLabel("加载中...")
        self.stats_label.setStyleSheet("color: #666666; font-size: 10pt; font-weight: normal;")  # 增大字体
        plugin_layout.addWidget(self.stats_label)

        # 插件卡片滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: 1px solid #E0E0E0;
                border-radius: 3px;
                background-color: #ffffff;
            }
            QScrollBar:vertical {
                background-color: #F5F5F5;
                width: 16px;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background-color: #C0C0C0;
                border: 1px solid #A0A0A0;
                border-radius: 6px;
                min-height: 30px;
                margin: 2px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #A0A0A0;
                border-color: #808080;
            }
            QScrollBar::handle:vertical:pressed {
                background-color: #808080;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                background: none;
                border: none;
                height: 0px;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: none;
            }
        """)

        # 卡片容器
        self.cards_widget = QWidget()
        self.cards_layout = QVBoxLayout(self.cards_widget)
        self.cards_layout.setContentsMargins(3, 3, 3, 3)
        self.cards_layout.setSpacing(3)
        self.cards_layout.addStretch()  # 底部弹簧

        scroll_area.setWidget(self.cards_widget)
        plugin_layout.addWidget(scroll_area)

        main_layout.addWidget(plugin_group)
        parent.addWidget(main_widget)
        
    def _create_plugin_details_panel(self, parent):
        """创建插件详情面板 - 使用GroupBox风格"""
        from PyQt5.QtWidgets import QGroupBox

        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(10)

        # 插件信息组
        info_group = QGroupBox("插件信息")
        info_layout = QVBoxLayout(info_group)

        # 插件名称
        self.plugin_name_label = QLabel("请选择一个插件")
        name_font = QFont()
        name_font.setBold(True)
        name_font.setPointSize(11)
        self.plugin_name_label.setFont(name_font)
        self.plugin_name_label.setStyleSheet("color: #2E86AB; font-weight: bold;")
        info_layout.addWidget(self.plugin_name_label)

        # 版本和状态
        status_layout = QHBoxLayout()
        self.plugin_version_label = QLabel("版本: -")
        self.plugin_version_label.setStyleSheet("color: #666666; font-size: 10pt; font-weight: normal;")  # 增大字体
        status_layout.addWidget(self.plugin_version_label)

        status_layout.addStretch()

        self.plugin_status_label = QLabel("状态: -")
        self.plugin_status_label.setStyleSheet("color: #666666; font-size: 10pt; font-weight: normal;")  # 增大字体
        status_layout.addWidget(self.plugin_status_label)

        info_layout.addLayout(status_layout)

        # 文件路径
        self.plugin_file_label = QLabel("文件路径: -")
        self.plugin_file_label.setStyleSheet("color: #666666; font-size: 9pt; font-weight: normal;")  # 增大字体
        self.plugin_file_label.setWordWrap(True)
        info_layout.addWidget(self.plugin_file_label)

        main_layout.addWidget(info_group)

        # 插件描述组
        desc_group = QGroupBox("插件描述")
        desc_layout = QVBoxLayout(desc_group)

        self.plugin_description_text = QTextEdit()
        self.plugin_description_text.setReadOnly(True)
        self.plugin_description_text.setMaximumHeight(100)
        self.plugin_description_text.setStyleSheet("""
            QTextEdit {
                background-color: #ffffff;
                border: 1px solid #E0E0E0;
                border-radius: 3px;
                padding: 5px;
                font-size: 10pt;
                color: #333333;
            }
        """)
        desc_layout.addWidget(self.plugin_description_text)

        main_layout.addWidget(desc_group)

        # 插件操作组
        actions_group = QGroupBox("插件操作")
        actions_layout = QVBoxLayout(actions_group)

        # 操作按钮
        buttons_layout = QHBoxLayout()

        self.enable_button = QPushButton("启用插件")
        self.enable_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 6px 12px;
                font-size: 9pt;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.enable_button.clicked.connect(self._enable_plugin)
        self.enable_button.setEnabled(False)
        buttons_layout.addWidget(self.enable_button)

        self.disable_button = QPushButton("禁用插件")
        self.disable_button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 6px 12px;
                font-size: 9pt;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.disable_button.clicked.connect(self._disable_plugin)
        self.disable_button.setEnabled(False)
        buttons_layout.addWidget(self.disable_button)

        buttons_layout.addStretch()
        actions_layout.addLayout(buttons_layout)

        main_layout.addWidget(actions_group)

        # 操作日志组
        log_group = QGroupBox("操作日志")
        log_layout = QVBoxLayout(log_group)

        self.plugin_log_text = QPlainTextEdit()
        self.plugin_log_text.setReadOnly(True)
        self.plugin_log_text.setMaximumBlockCount(1000)
        self.plugin_log_text.setStyleSheet("""
            QPlainTextEdit {
                background-color: #ffffff;
                border: 1px solid #E0E0E0;
                border-radius: 3px;
                padding: 5px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 9pt;
                color: #333333;
            }
        """)
        log_layout.addWidget(self.plugin_log_text)

        main_layout.addWidget(log_group)

        main_layout.addStretch()
        parent.addWidget(main_widget)
        
    def _create_control_buttons(self, layout):
        """创建控制按钮 - 简洁风格"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(8)

        # 刷新按钮
        self.refresh_button = QPushButton("刷新列表")
        self.refresh_button.clicked.connect(self._refresh_plugin_list)
        button_layout.addWidget(self.refresh_button)

        # 重新扫描按钮
        self.rescan_button = QPushButton("重新扫描")
        self.rescan_button.clicked.connect(self._rescan_plugins)
        button_layout.addWidget(self.rescan_button)

        # 分隔符
        button_layout.addSpacing(15)

        # 全部启用按钮
        self.enable_all_button = QPushButton("启用所有")
        self.enable_all_button.clicked.connect(self._enable_all_plugins)
        button_layout.addWidget(self.enable_all_button)

        # 全部禁用按钮
        self.disable_all_button = QPushButton("禁用所有")
        self.disable_all_button.clicked.connect(self._disable_all_plugins)
        button_layout.addWidget(self.disable_all_button)

        button_layout.addStretch()

        # 关闭按钮
        self.close_button = QPushButton("关闭")
        self.close_button.clicked.connect(self.close)
        button_layout.addWidget(self.close_button)

        layout.addLayout(button_layout)
        
    def _refresh_plugin_list(self):
        """刷新插件卡片列表"""
        plugins = plugin_manager.get_plugin_list()

        # 清除现有卡片
        for card in self.plugin_cards.values():
            card.setParent(None)
            card.deleteLater()
        self.plugin_cards.clear()

        # 创建新的插件卡片
        for plugin_info in plugins:
            card = PluginCard(plugin_info)
            card.plugin_toggled.connect(self._on_plugin_toggled)
            card.plugin_selected.connect(self._on_plugin_selected)

            self.plugin_cards[plugin_info.name] = card
            # 插入到倒数第二个位置（最后一个是弹簧）
            self.cards_layout.insertWidget(self.cards_layout.count() - 1, card)

        # 更新统计信息
        enabled_count = sum(1 for p in plugins if p.enabled)
        self.stats_label.setText(f"总计: {len(plugins)} 个插件 (已启用: {enabled_count})")

        self._add_log(f"刷新插件列表完成，发现 {len(plugins)} 个插件")

    def _filter_plugins(self, text):
        """根据搜索文本过滤插件"""
        text = text.lower().strip()

        for plugin_name, card in self.plugin_cards.items():
            if not text or text in plugin_name.lower():
                card.setVisible(True)
            else:
                card.setVisible(False)

    def _on_plugin_toggled(self, plugin_name: str, enabled: bool):
        """处理插件启用状态切换"""
        if enabled:
            self._enable_plugin_by_name(plugin_name)
        else:
            self._disable_plugin_by_name(plugin_name)

    def _on_plugin_selected(self, plugin_name: str):
        """处理插件选择"""
        # 取消其他卡片的选中状态
        for name, card in self.plugin_cards.items():
            card.set_selected(name == plugin_name)

        # 显示插件详情
        plugins = plugin_manager.get_plugin_list()
        for plugin_info in plugins:
            if plugin_info.name == plugin_name:
                self._show_plugin_details(plugin_info)
                self.selected_plugin = plugin_name
                break
        

        
    def _show_plugin_details(self, plugin_info: PluginInfo):
        """显示插件详情"""
        self.plugin_name_label.setText(plugin_info.name)
        self.plugin_version_label.setText(f"版本: {plugin_info.version}")

        status = "已启用" if plugin_info.enabled else "已禁用"
        status_color = "#28a745"  # 绿色
        if plugin_info.enabled and plugin_info.instance is None:
            status = "启用失败"
            status_color = "#dc3545"  # 红色
        elif not plugin_info.enabled:
            status_color = "#6c757d"  # 灰色

        self.plugin_status_label.setText(f"状态: {status}")
        self.plugin_status_label.setStyleSheet(f"color: {status_color}; font-size: 10pt; font-weight: bold; background: transparent; border: none;")

        self.plugin_description_text.setPlainText(plugin_info.description)
        self.plugin_file_label.setText(f"文件路径: {plugin_info.file_path}")

        # 更新按钮状态
        self.enable_button.setEnabled(not plugin_info.enabled)
        self.disable_button.setEnabled(plugin_info.enabled)
        
    def _clear_plugin_details(self):
        """清除插件详情"""
        self.plugin_name_label.setText("请选择一个插件")
        self.plugin_version_label.setText("版本: -")
        self.plugin_status_label.setText("状态: -")
        self.plugin_status_label.setStyleSheet("color: #6c757d; font-size: 10pt; background: transparent; border: none;")
        self.plugin_description_text.clear()
        self.plugin_file_label.setText("文件路径: -")

        self.enable_button.setEnabled(False)
        self.disable_button.setEnabled(False)
        self.selected_plugin = None
            
    def _enable_plugin(self):
        """启用选中的插件"""
        if self.selected_plugin:
            self._enable_plugin_by_name(self.selected_plugin)

    def _disable_plugin(self):
        """禁用选中的插件"""
        if self.selected_plugin:
            self._disable_plugin_by_name(self.selected_plugin)
        
    def _enable_plugin_by_name(self, plugin_name: str):
        """根据名称启用插件"""
        try:
            plugin_manager.enable_plugin(plugin_name)
            self._add_log(f"✅ 已启用插件: {plugin_name}")

            # 更新对应的卡片状态
            if plugin_name in self.plugin_cards:
                plugins = plugin_manager.get_plugin_list()
                for plugin_info in plugins:
                    if plugin_info.name == plugin_name:
                        self.plugin_cards[plugin_name].plugin_info = plugin_info
                        self.plugin_cards[plugin_name].update_status()
                        if self.selected_plugin == plugin_name:
                            self._show_plugin_details(plugin_info)
                        break

            # 更新统计信息
            plugins = plugin_manager.get_plugin_list()
            enabled_count = sum(1 for p in plugins if p.enabled)
            self.stats_label.setText(f"总计: {len(plugins)} 个插件 (已启用: {enabled_count})")

        except Exception as e:
            self._add_log(f"❌ 启用插件失败 {plugin_name}: {str(e)}")
            QMessageBox.warning(self, "错误", f"启用插件失败: {str(e)}")

    def _disable_plugin_by_name(self, plugin_name: str):
        """根据名称禁用插件"""
        try:
            plugin_manager.disable_plugin(plugin_name)
            self._add_log(f"❌ 已禁用插件: {plugin_name}")

            # 更新对应的卡片状态
            if plugin_name in self.plugin_cards:
                plugins = plugin_manager.get_plugin_list()
                for plugin_info in plugins:
                    if plugin_info.name == plugin_name:
                        self.plugin_cards[plugin_name].plugin_info = plugin_info
                        self.plugin_cards[plugin_name].update_status()
                        if self.selected_plugin == plugin_name:
                            self._show_plugin_details(plugin_info)
                        break

            # 更新统计信息
            plugins = plugin_manager.get_plugin_list()
            enabled_count = sum(1 for p in plugins if p.enabled)
            self.stats_label.setText(f"总计: {len(plugins)} 个插件 (已启用: {enabled_count})")

        except Exception as e:
            self._add_log(f"❌ 禁用插件失败 {plugin_name}: {str(e)}")
            QMessageBox.warning(self, "错误", f"禁用插件失败: {str(e)}")
            
    def _rescan_plugins(self):
        """重新扫描插件"""
        try:
            self._add_log("🔍 开始重新扫描插件...")

            # 清理现有插件
            plugin_manager._plugins.clear()

            # 重新扫描
            plugin_manager.scan_plugins()

            # 刷新界面
            self._refresh_plugin_list()
            self._clear_plugin_details()

            self._add_log("✅ 插件扫描完成")
        except Exception as e:
            self._add_log(f"❌ 插件扫描失败: {str(e)}")
            QMessageBox.warning(self, "错误", f"插件扫描失败: {str(e)}")
            
    def _enable_all_plugins(self):
        """启用所有插件"""
        reply = QMessageBox.question(self, "确认操作",
                                   "确定要启用所有插件吗？\n\n这将启用当前所有已禁用的插件。",
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)
        if reply == QMessageBox.Yes:
            self._add_log("🚀 开始批量启用所有插件...")
            plugins = plugin_manager.get_plugin_list()
            enabled_count = 0

            for plugin_info in plugins:
                if not plugin_info.enabled:
                    try:
                        plugin_manager.enable_plugin(plugin_info.name)
                        enabled_count += 1
                        self._add_log(f"  ✅ 已启用: {plugin_info.name}")
                    except Exception as e:
                        self._add_log(f"  ❌ 启用失败: {plugin_info.name} - {str(e)}")

            # 刷新界面
            self._refresh_plugin_list()
            if self.selected_plugin:
                self._on_plugin_selected(self.selected_plugin)

            self._add_log(f"✅ 批量启用完成，成功启用 {enabled_count} 个插件")

    def _disable_all_plugins(self):
        """禁用所有插件"""
        reply = QMessageBox.question(self, "确认操作",
                                   "确定要禁用所有插件吗？\n\n这将禁用当前所有已启用的插件，可能会影响应用程序功能。",
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)
        if reply == QMessageBox.Yes:
            self._add_log("🛑 开始批量禁用所有插件...")
            plugins = plugin_manager.get_plugin_list()
            disabled_count = 0

            for plugin_info in plugins:
                if plugin_info.enabled:
                    try:
                        plugin_manager.disable_plugin(plugin_info.name)
                        disabled_count += 1
                        self._add_log(f"  ❌ 已禁用: {plugin_info.name}")
                    except Exception as e:
                        self._add_log(f"  ❌ 禁用失败: {plugin_info.name} - {str(e)}")

            # 刷新界面
            self._refresh_plugin_list()
            if self.selected_plugin:
                self._on_plugin_selected(self.selected_plugin)

            self._add_log(f"✅ 批量禁用完成，成功禁用 {disabled_count} 个插件")
                    
    def _add_log(self, message: str):
        """添加日志消息"""
        import time
        timestamp = time.strftime('%H:%M:%S')
        self.plugin_log_text.appendPlainText(f"[{timestamp}] {message}")
        logger.info(f"插件管理器: {message}")
        
    def closeEvent(self, event):
        """窗口关闭事件"""
        self.window_closed.emit()
        super().closeEvent(event)

    def show_and_raise(self):
        """显示窗口并置于前台"""
        self.show()
        self.raise_()
        self.activateWindow()
