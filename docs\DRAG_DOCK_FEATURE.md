# 插件窗口拖拽停靠功能

## 功能概述

为所有插件窗口添加了拖拽停靠功能，用户可以通过拖拽单个插件窗口到主窗口底部区域来自动停靠到标签页中，类似现有的"停靠所有窗口"功能，但针对单个窗口操作。

## 功能特性

### 1. 自动拖拽检测
- 当用户开始拖拽插件窗口时，系统会自动检测拖拽行为
- 拖拽距离超过系统设定阈值时开始拖拽模式

### 2. 停靠区域检测
- 停靠区域定义为主窗口底部30%的区域
- 当窗口拖拽到停靠区域时提供视觉反馈

### 3. 视觉反馈
- **窗口标题变化**：拖拽到停靠区域时，窗口标题会显示"释放鼠标停靠到主界面"
- **鼠标光标变化**：在停靠区域内鼠标光标变为手型指针
- **自动恢复**：离开停靠区域或释放鼠标后自动恢复原始状态

### 4. 自动停靠
- 在停靠区域内释放鼠标时，窗口自动停靠到主界面标签页中
- 停靠后窗口从悬浮状态转换为标签页状态

## 技术实现

### 核心文件
- `core/services/plugin/PluginIntegrationService.py`

### 主要方法

#### 1. `_add_drag_dock_support(window, plugin_name)`
为插件窗口添加拖拽停靠支持的核心方法：
- 重写窗口的鼠标事件处理方法
- 添加拖拽状态跟踪
- 集成停靠区域检测

#### 2. `_check_dock_area(window, global_pos)`
检查拖拽位置并提供视觉反馈：
- 检测鼠标是否在停靠区域内
- 提供窗口标题和鼠标光标的视觉反馈

#### 3. `_is_in_dock_area(window, global_pos)`
判断鼠标位置是否在停靠区域内：
- 计算主窗口底部30%区域
- 返回布尔值表示是否在停靠区域

#### 4. `_restore_window_state(window)`
恢复窗口状态：
- 恢复原始窗口标题
- 恢复鼠标光标

### 事件处理流程

1. **鼠标按下** (`mousePressEvent`)
   - 记录拖拽起始位置
   - 初始化拖拽状态

2. **鼠标移动** (`mouseMoveEvent`)
   - 计算拖拽距离
   - 开始拖拽模式
   - 检查停靠区域并提供视觉反馈

3. **鼠标释放** (`mouseReleaseEvent`)
   - 检查是否在停靠区域释放
   - 执行自动停靠操作
   - 恢复窗口状态
   - 重置拖拽状态

## 适用范围

### 支持拖拽停靠的插件
所有**悬浮窗口**类型的插件都支持拖拽停靠功能，包括：
- 示例工具 (example_tool_plugin)
- 性能监控 (performance_monitor_plugin)
- 选择性寄存器操作 (selective_register_plugin)
- 数据分析 (data_analysis_plugin)

### 不支持拖拽停靠的插件
以下插件默认集成到标签页中，不需要拖拽停靠：
- 时钟输入控制 (clkin_control_plugin)
- PLL控制 (pll_control_plugin)
- 同步系统参考 (sync_sysref_plugin)
- 时钟输出 (clk_output_plugin)
- 模式设置 (set_modes_plugin)

## 使用方法

### 用户操作步骤
1. 打开任意支持拖拽停靠的插件窗口
2. 用鼠标左键点击并拖拽窗口标题栏或窗口内容区域
3. 将窗口拖拽到主窗口底部30%的区域
4. 观察窗口标题变化和鼠标光标变化（视觉反馈）
5. 在停靠区域内释放鼠标左键
6. 窗口自动停靠到主界面标签页中

### 注意事项
- 只有在停靠区域内释放鼠标才会触发自动停靠
- 拖拽过程中可以随时取消，只需在停靠区域外释放鼠标
- 停靠后的窗口可以通过右键菜单或工具栏按钮重新分离为悬浮窗口

## 配置选项

### 停靠区域大小
当前停靠区域设置为主窗口底部30%的区域，可以通过修改 `_is_in_dock_area` 方法中的比例来调整：

```python
# 定义停靠区域：主窗口底部30%的区域
dock_area_height = int(main_geometry.height() * 0.3)
```

### 拖拽距离阈值
使用Qt系统默认的拖拽距离阈值：

```python
if distance >= QApplication.startDragDistance():
```

## 兼容性

### 现有功能兼容
- 与现有的"停靠所有窗口"功能完全兼容
- 与现有的"分离所有窗口"功能完全兼容
- 与现有的右键菜单停靠功能完全兼容

### 插件系统兼容
- 自动集成到现有的插件管理系统中
- 不影响插件的正常创建和销毁流程
- 保持插件窗口的所有原有功能

## 测试

### 测试文件
- `test_drag_dock_feature.py` - 基础拖拽停靠功能测试
- `test_real_plugin_drag_dock.py` - 真实插件拖拽停靠测试

### 测试覆盖
- 拖拽检测功能
- 停靠区域检测
- 视觉反馈功能
- 自动停靠功能
- 状态恢复功能
- 与现有功能的兼容性

## 日志记录

系统会记录以下拖拽停靠相关的日志：
- 拖拽停靠支持添加成功
- 拖拽自动停靠操作
- 停靠区域检测（调试级别）
- 各种错误和异常情况

## 未来改进

### 可能的增强功能
1. **多区域停靠**：支持拖拽到左侧、右侧、顶部等不同区域
2. **停靠预览**：拖拽时显示停靠位置预览
3. **自定义停靠区域**：允许用户自定义停靠区域大小和位置
4. **拖拽动画**：添加平滑的拖拽和停靠动画效果
5. **磁性停靠**：当窗口接近停靠区域时自动吸附
