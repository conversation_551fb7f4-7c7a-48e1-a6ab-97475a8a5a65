#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试setText调用
追踪所有对lineEditFout*Output控件的setText调用
"""

import sys
import os
import json
from PyQt5.QtWidgets import QApplication, QLineEdit

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 猴子补丁QLineEdit.setText方法来追踪调用
original_setText = QLineEdit.setText

def traced_setText(self, text):
    """追踪setText调用的包装方法"""
    if hasattr(self, 'objectName') and 'lineEditFout' in self.objectName() and 'Output' in self.objectName():
        import traceback
        stack = traceback.format_stack()
        print(f"\n🔍 setText调用追踪:")
        print(f"   控件: {self.objectName()}")
        print(f"   设置值: '{text}'")
        print(f"   调用栈:")
        for line in stack[-3:-1]:  # 显示最近的几个调用
            print(f"     {line.strip()}")
        print()
    
    return original_setText(self, text)

# 应用猴子补丁
QLineEdit.setText = traced_setText

from ui.handlers.ModernClkOutputsHandler import ModernClkOutputsHandler
from core.services.register.RegisterManager import RegisterManager
from utils.Log import logger

def debug_settext_calls():
    """调试setText调用"""
    print("=" * 60)
    print("调试setText调用追踪")
    print("=" * 60)
    
    try:
        # 创建QApplication
        app = QApplication(sys.argv)
        
        print("1. 加载寄存器配置...")
        config_path = os.path.join('lib', 'register.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        print("2. 创建RegisterManager...")
        register_manager = RegisterManager(registers_config)
        
        print("3. 创建现代化时钟输出处理器（追踪setText调用）...")
        modern_handler = ModernClkOutputsHandler(None, register_manager)
        
        print("4. 检查初始化后的值...")
        for output_num in range(4):  # 只检查前4个
            output_attr = f"lineEditFout{output_num}Output"
            if hasattr(modern_handler.ui, output_attr):
                output_widget = getattr(modern_handler.ui, output_attr)
                text = output_widget.text()
                print(f"   输出{output_num}: '{text}'")
        
        print("\n5. 手动调用频率计算（追踪setText调用）...")
        modern_handler.calculate_output_frequencies()
        
        print("6. 检查计算后的值...")
        for output_num in range(4):  # 只检查前4个
            output_attr = f"lineEditFout{output_num}Output"
            if hasattr(modern_handler.ui, output_attr):
                output_widget = getattr(modern_handler.ui, output_attr)
                text = output_widget.text()
                print(f"   输出{output_num}: '{text}'")
        
        print("\n" + "=" * 60)
        print("调试完成")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_settext_calls()
