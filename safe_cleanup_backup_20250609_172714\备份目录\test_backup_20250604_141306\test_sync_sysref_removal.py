#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试同步系统参考传统处理器移除效果
验证只使用现代化处理器的情况下是否正常工作
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication, QMainWindow
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt


def test_legacy_handler_removal():
    """测试传统处理器移除"""
    print("=" * 60)
    print("测试同步系统参考传统处理器移除")
    print("=" * 60)
    
    try:
        # 1. 测试传统处理器文件是否已移除
        print("1. 检查传统处理器文件是否已移除...")
        legacy_handler_path = os.path.join(project_root, 'ui', 'handlers', 'SyncSysRefHandler.py')
        
        if os.path.exists(legacy_handler_path):
            print("❌ 传统处理器文件仍然存在")
            return False
        else:
            print("✓ 传统处理器文件已成功移除")
        
        # 2. 测试导入传统处理器是否失败
        print("\n2. 测试导入传统处理器...")
        try:
            from ui.handlers.SyncSysRefHandler import SyncSysRefHandler
            print("❌ 仍然可以导入传统处理器")
            return False
        except ImportError:
            print("✓ 传统处理器导入失败（预期行为）")
        except Exception as e:
            print(f"✓ 传统处理器导入出错（预期行为）: {str(e)}")
        
        # 3. 测试现代化处理器是否正常工作
        print("\n3. 测试现代化处理器...")
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建寄存器管理器
        from core.services.register.RegisterManager import RegisterManager
        
        # 加载寄存器配置
        config_path = os.path.join(project_root, 'lib', 'register.json')
        if not os.path.exists(config_path):
            print(f"❌ 寄存器配置文件不存在: {config_path}")
            return False
            
        import json
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        register_manager = RegisterManager(registers_config)
        
        # 创建现代化同步系统参考处理器
        from ui.handlers.ModernSyncSysRefHandler import ModernSyncSysRefHandler
        
        modern_handler = ModernSyncSysRefHandler(None, register_manager)
        print("✓ 现代化同步系统参考处理器创建成功")
        print(f"✓ 处理器类型: {type(modern_handler).__name__}")
        
        # 检查滚动区域（应该继承自ModernBaseHandler）
        if hasattr(modern_handler, 'scroll_area'):
            print("✓ 现代化处理器包含滚动区域")
        else:
            print("❌ 现代化处理器缺少滚动区域")
            return False
        
        # 检查控件映射
        if hasattr(modern_handler, 'widget_register_map'):
            mapping_count = len(modern_handler.widget_register_map)
            print(f"✓ 控件映射构建成功，包含 {mapping_count} 个控件")
        else:
            print("❌ 控件映射构建失败")
            return False
        
        # 检查UI控件
        if hasattr(modern_handler, 'ui'):
            print("✓ UI对象存在")
            
            # 检查关键控件
            key_widgets = ['InternalVCOFreq', 'spinBoxSysrefDIV', 'SYNCDIS0']
            for widget_name in key_widgets:
                if hasattr(modern_handler.ui, widget_name):
                    print(f"✓ {widget_name}: 存在")
                else:
                    print(f"❌ {widget_name}: 不存在")
        else:
            print("❌ UI对象不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_factory_configuration():
    """测试工厂配置"""
    print("\n" + "=" * 60)
    print("测试工厂配置")
    print("=" * 60)
    
    try:
        from ui.factories.ModernToolWindowFactory import ModernToolWindowFactory
        
        # 检查同步系统参考配置
        sync_config = ModernToolWindowFactory.HANDLER_CONFIGS.get('sync_sysref')
        
        if not sync_config:
            print("❌ 同步系统参考配置不存在")
            return False
        
        print("✓ 同步系统参考配置存在")
        
        # 检查配置内容
        print(f"✓ 标题: {sync_config.get('title')}")
        print(f"✓ 窗口属性: {sync_config.get('window_attr')}")
        print(f"✓ 动作属性: {sync_config.get('action_attr')}")
        print(f"✓ 传统处理器: {sync_config.get('legacy_handler')}")
        print(f"✓ 现代化处理器: {sync_config.get('modern_handler')}")
        print(f"✓ 使用现代化: {sync_config.get('use_modern')}")
        
        # 验证配置正确性
        if sync_config.get('legacy_handler') is not None:
            print("❌ 传统处理器配置应该为None")
            return False
        else:
            print("✓ 传统处理器配置已正确设置为None")
        
        if sync_config.get('use_modern') is not True:
            print("❌ 应该强制使用现代化处理器")
            return False
        else:
            print("✓ 已配置为强制使用现代化处理器")
        
        if not sync_config.get('modern_handler'):
            print("❌ 现代化处理器配置不能为空")
            return False
        else:
            print("✓ 现代化处理器配置正确")
        
        return True
        
    except Exception as e:
        print(f"❌ 工厂配置测试出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_main_window_integration():
    """测试主窗口集成"""
    print("\n" + "=" * 60)
    print("测试主窗口集成")
    print("=" * 60)
    
    try:
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        from ui.windows.RegisterMainWindow import RegisterMainWindow
        from core.services.spi.spi_service_impl import SPIServiceImpl
        from core.repositories.register_repository import RegisterRepository
        
        spi_service = SPIServiceImpl()
        spi_service.initialize()
        register_repo = RegisterRepository(spi_service)
        main_window = RegisterMainWindow(register_repo)
        main_window.show()
        
        # 等待界面完全加载
        QTest.qWait(2000)
        
        print("✓ 主窗口创建成功")
        
        # 创建同步系统参考窗口
        print("\n创建同步系统参考窗口...")
        sync_window = main_window.tool_window_factory.create_window_by_type('sync_sysref')
        
        if sync_window:
            print("✓ 同步系统参考窗口创建成功")
            print(f"✓ 窗口类型: {type(sync_window).__name__}")
            
            # 验证是现代化处理器
            if 'Modern' in type(sync_window).__name__:
                print("✓ 确认使用现代化处理器")
            else:
                print("❌ 未使用现代化处理器")
                return False
            
            # 检查滚动区域
            if hasattr(sync_window, 'scroll_area'):
                print("✓ 滚动区域存在")
            else:
                print("❌ 滚动区域不存在")
                return False
                
            # 检查控件映射
            if hasattr(sync_window, 'widget_register_map'):
                mapping_count = len(sync_window.widget_register_map)
                print(f"✓ 控件映射: {mapping_count} 个控件")
            else:
                print("❌ 控件映射不存在")
                return False
            
            print("✅ 主窗口集成测试完成")
            
            # 保持窗口打开一段时间供用户查看
            print("\n主窗口将保持打开3秒供查看...")
            QTest.qWait(3000)
            
            return True
        else:
            print("❌ 同步系统参考窗口创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 主窗口集成测试出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    # 测试传统处理器移除
    success1 = test_legacy_handler_removal()
    
    # 测试工厂配置
    success2 = test_factory_configuration()
    
    # 测试主窗口集成
    success3 = test_main_window_integration()
    
    if success1 and success2 and success3:
        print("\n🎉 所有测试通过！同步系统参考传统处理器移除成功！")
        print("📋 移除总结:")
        print("   - ✅ 传统处理器文件已移除")
        print("   - ✅ 工厂配置已更新")
        print("   - ✅ 现代化处理器正常工作")
        print("   - ✅ 主窗口集成正常")
        print("   - ✅ 滚动区域功能正常")
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
        sys.exit(1)
