#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的拖拽释放检测测试
用于验证鼠标释放事件是否被正确处理
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer, QPoint, Qt
from PyQt5.QtGui import QMouseEvent
import time

def test_drag_release():
    """测试拖拽释放检测"""
    print("\n=== 简化拖拽释放检测测试 ===")
    
    try:
        # 获取应用实例
        app = QApplication.instance()
        if not app:
            print("❌ 无法获取QApplication实例")
            return False
            
        # 获取主窗口
        main_window = None
        for widget in app.topLevelWidgets():
            if hasattr(widget, 'plugin_service'):
                main_window = widget
                break
                
        if not main_window:
            print("❌ 无法找到主窗口")
            return False
            
        print(f"✅ 找到主窗口: {type(main_window).__name__}")
        
        # 获取插件服务
        plugin_service = main_window.plugin_service
        print(f"✅ 获取插件服务: {type(plugin_service).__name__}")
        
        # 检查当前打开的插件窗口
        open_windows = []
        for plugin_name, window in plugin_service._plugin_windows.items():
            if window and window.isVisible():
                open_windows.append((plugin_name, window))
                
        if not open_windows:
            print("❌ 没有找到打开的插件窗口")
            return False
            
        # 选择第一个打开的窗口进行测试
        plugin_name, test_window = open_windows[0]
        print(f"✅ 选择测试窗口: {plugin_name}")
        
        # 检查窗口的拖拽状态
        print(f"\n🔍 窗口拖拽状态检查:")
        print(f"   _is_dragging: {getattr(test_window, '_is_dragging', '未设置')}")
        print(f"   _drag_start_position: {getattr(test_window, '_drag_start_position', '未设置')}")
        print(f"   窗口位置: {test_window.pos()}")
        print(f"   窗口大小: {test_window.size()}")
        
        # 模拟鼠标按下
        print(f"\n🖱️ 步骤1: 模拟鼠标按下")
        press_pos = QPoint(100, 20)  # 窗口内的位置
        press_event = QMouseEvent(
            QMouseEvent.MouseButtonPress,
            press_pos,
            test_window.mapToGlobal(press_pos),
            Qt.LeftButton,
            Qt.LeftButton,
            Qt.NoModifier
        )
        
        # 发送鼠标按下事件
        app.sendEvent(test_window, press_event)
        app.processEvents()
        time.sleep(0.1)
        
        print(f"   按下后 _is_dragging: {getattr(test_window, '_is_dragging', '未设置')}")
        print(f"   按下后 _drag_start_position: {getattr(test_window, '_drag_start_position', '未设置')}")
        
        # 模拟鼠标移动（足够的距离触发拖拽）
        print(f"\n🖱️ 步骤2: 模拟鼠标移动")
        move_pos = QPoint(150, 70)  # 移动50像素
        move_event = QMouseEvent(
            QMouseEvent.MouseMove,
            move_pos,
            test_window.mapToGlobal(move_pos),
            Qt.LeftButton,
            Qt.LeftButton,
            Qt.NoModifier
        )
        
        # 发送鼠标移动事件
        app.sendEvent(test_window, move_event)
        app.processEvents()
        time.sleep(0.1)
        
        print(f"   移动后 _is_dragging: {getattr(test_window, '_is_dragging', '未设置')}")
        
        # 模拟鼠标释放
        print(f"\n🖱️ 步骤3: 模拟鼠标释放")
        release_pos = move_pos
        release_event = QMouseEvent(
            QMouseEvent.MouseButtonRelease,
            release_pos,
            test_window.mapToGlobal(release_pos),
            Qt.LeftButton,
            Qt.NoButton,
            Qt.NoModifier
        )
        
        # 发送鼠标释放事件
        app.sendEvent(test_window, release_event)
        app.processEvents()
        time.sleep(0.1)
        
        print(f"   释放后 _is_dragging: {getattr(test_window, '_is_dragging', '未设置')}")
        
        # 检查事件过滤器的日志输出
        print(f"\n📋 测试结果分析:")
        print(f"   如果在日志中看到以下信息，说明事件处理正常:")
        print(f"   - 🖱️ [拖拽调试] 事件过滤器捕获鼠标按下")
        print(f"   - 🖱️ [拖拽调试] 事件过滤器捕获鼠标移动")
        print(f"   - 🖱️ [拖拽调试] 事件过滤器捕获鼠标释放")
        print(f"   - 🎯 [拖拽调试] 处理拖拽释放")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # 延迟执行，确保应用完全启动
    def run_test():
        success = test_drag_release()
        if success:
            print("\n✅ 简化拖拽释放检测测试完成")
        else:
            print("\n❌ 简化拖拽释放检测测试失败")
    
    # 使用QTimer延迟执行
    app = QApplication.instance()
    if app:
        timer = QTimer()
        timer.timeout.connect(run_test)
        timer.setSingleShot(True)
        timer.start(2000)  # 2秒后执行
    else:
        run_test()