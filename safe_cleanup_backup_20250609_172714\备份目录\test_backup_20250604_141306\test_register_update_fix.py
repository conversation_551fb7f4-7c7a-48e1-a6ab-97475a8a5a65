#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试寄存器更新修复
验证控件状态变化后寄存器值是否正确更新
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_register_update_fix():
    """测试寄存器更新修复"""
    try:
        # 创建应用程序
        app = QApplication(sys.argv)
        
        print("=" * 60)
        print("测试寄存器更新修复")
        print("=" * 60)
        
        # 导入必要的模块
        from core.models.RegisterModel import Register
        from ui.handlers.BaseHandler import BaseClockHandler
        from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
        
        # 创建一个模拟的寄存器（模拟0x5B寄存器）
        register_bits = [
            {
                "bit": "5",
                "name": "LOS_EN",
                "default": "0",
                "widget_name": "losEn",
                "widget_type": "checkbox",
                "options": None,
                "description": "Enables the LOS (Loss-of-Signal) timeout control"
            },
            {
                "bit": "2",
                "name": "SOME_OTHER_BIT",
                "default": "1",
                "widget_name": None,
                "widget_type": None,
                "options": None,
                "description": "Some other bit"
            }
        ]
        
        # 创建寄存器对象，初始值为0x4（第2位为1）
        register = Register("0x5B", register_bits)
        register.value = 0x4  # 设置初始值
        
        print(f"初始寄存器值: 0x{register.value:04X}")
        print(f"初始LOS_EN位值: {register.get_bit_field_value('LOS_EN')}")
        
        # 创建一个测试处理器
        class TestHandler(BaseClockHandler):
            def __init__(self):
                # 不调用super().__init__()以避免Qt相关问题
                self.registers = {"0x5B": register}
                self.widget_register_map = {
                    "losEn": {
                        "register_addr": "0x5B",
                        "widget_type": "checkbox",
                        "default_value": "0",
                        "bit_def": {
                            "name": "LOS_EN",
                            "default": "0"
                        }
                    }
                }
                # 模拟主窗口
                self.main_window = type('MockMainWindow', (), {
                    'auto_write_mode': True,
                    'register_service': type('MockRegisterService', (), {
                        'write_register': lambda addr, value: print(f"      ✓ 模拟写入: {addr} = 0x{value:04X}")
                    })()
                })()
                
            def calculate_output_frequencies(self):
                pass  # 空实现
                
            def handle_register_value_change(self, widget_name):
                pass  # 空实现
        
        # 创建测试处理器实例
        test_handler = TestHandler()
        
        # 设置一个信号监听器来捕获寄存器更新信号
        update_signals = []
        def capture_signal(addr, value):
            update_signals.append((addr, value))
            print(f"      📡 捕获到寄存器更新信号: {addr} = 0x{value:04X}")
        
        RegisterUpdateBus.instance().register_updated.connect(capture_signal)
        
        print("\n测试1: 设置LOS_EN位为1")
        print("-" * 40)
        
        # 模拟控件状态变化：设置losEn为True（值为1）
        test_handler.update_register_value("losEn", 1)
        
        # 检查结果
        final_value = register.value
        los_en_value = register.get_bit_field_value('LOS_EN')
        
        print(f"\n结果检查:")
        print(f"- 最终寄存器值: 0x{final_value:04X}")
        print(f"- LOS_EN位值: {los_en_value}")
        print(f"- 期望寄存器值: 0x{0x4 | (1 << 5):04X} (0x24)")
        print(f"- 捕获到的信号数量: {len(update_signals)}")
        
        if len(update_signals) > 0:
            signal_addr, signal_value = update_signals[-1]
            print(f"- 最后一个信号值: {signal_addr} = 0x{signal_value:04X}")
        
        # 验证结果
        expected_value = 0x4 | (1 << 5)  # 0x24
        if final_value == expected_value and los_en_value == 1:
            print("\n✅ 测试通过！寄存器值正确更新")
            success = True
        else:
            print("\n❌ 测试失败！寄存器值未正确更新")
            success = False
            
        print("\n测试2: 设置LOS_EN位为0")
        print("-" * 40)
        
        # 清空信号列表
        update_signals.clear()
        
        # 模拟控件状态变化：设置losEn为False（值为0）
        test_handler.update_register_value("losEn", 0)
        
        # 检查结果
        final_value2 = register.value
        los_en_value2 = register.get_bit_field_value('LOS_EN')
        
        print(f"\n结果检查:")
        print(f"- 最终寄存器值: 0x{final_value2:04X}")
        print(f"- LOS_EN位值: {los_en_value2}")
        print(f"- 期望寄存器值: 0x{0x4:04X} (0x04)")
        print(f"- 捕获到的信号数量: {len(update_signals)}")
        
        # 验证结果
        expected_value2 = 0x4  # 回到原始值
        if final_value2 == expected_value2 and los_en_value2 == 0:
            print("\n✅ 测试2通过！寄存器值正确更新")
            success = success and True
        else:
            print("\n❌ 测试2失败！寄存器值未正确更新")
            success = False
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 所有测试通过！寄存器更新修复成功！")
        else:
            print("💥 测试失败！需要进一步检查问题。")
        print("=" * 60)
        
        app.quit()
        return success
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_register_update_fix()
    sys.exit(0 if success else 1)
