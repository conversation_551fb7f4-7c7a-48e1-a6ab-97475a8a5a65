#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试配置文件
定义测试相关的配置参数和常量
"""

import os
import sys

# 项目根目录
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# 测试配置
TEST_CONFIG = {
    # 测试超时时间（秒）
    'timeout': {
        'unit': 30,
        'integration': 60,
        'functional': 120,
        'ui': 180,
        'performance': 300,
        'regression': 600
    },
    
    # 测试数据路径
    'test_data': {
        'register_config': os.path.join(PROJECT_ROOT, 'lib', 'register.json'),
        'mock_config': os.path.join(PROJECT_ROOT, 'test_suite', 'data', 'mock_register.json'),
        'test_logs': os.path.join(PROJECT_ROOT, 'test_suite', 'logs')
    },
    
    # 测试环境设置
    'environment': {
        'use_mock_spi': True,
        'enable_logging': True,
        'log_level': 'DEBUG',
        'qt_app_timeout': 10000  # Qt应用程序超时时间（毫秒）
    },
    
    # 测试分类
    'categories': {
        'unit': {
            'description': '单元测试',
            'timeout': 30,
            'parallel': True
        },
        'integration': {
            'description': '集成测试',
            'timeout': 60,
            'parallel': False
        },
        'functional': {
            'description': '功能测试',
            'timeout': 120,
            'parallel': False
        },
        'ui': {
            'description': 'UI测试',
            'timeout': 180,
            'parallel': False
        },
        'performance': {
            'description': '性能测试',
            'timeout': 300,
            'parallel': False
        },
        'regression': {
            'description': '回归测试',
            'timeout': 600,
            'parallel': False
        }
    },
    
    # 测试报告配置
    'reporting': {
        'output_dir': os.path.join(PROJECT_ROOT, 'test_suite', 'reports'),
        'formats': ['html', 'xml', 'json'],
        'include_coverage': True,
        'include_performance': True
    },
    
    # 性能测试基准
    'performance_benchmarks': {
        'startup_time': 5.0,  # 秒
        'window_creation_time': 2.0,  # 秒
        'register_read_time': 0.1,  # 秒
        'register_write_time': 0.1,  # 秒
        'ui_response_time': 0.5,  # 秒
        'memory_usage_mb': 100  # MB
    },
    
    # 测试数据生成配置
    'data_generation': {
        'register_count': 100,
        'bit_field_count': 8,
        'test_values': [0x0000, 0x5555, 0xAAAA, 0xFFFF],
        'frequency_ranges': {
            'vco': (2000.0, 3000.0),
            'output': (1.0, 1000.0),
            'input': (10.0, 500.0)
        }
    }
}

# 模拟寄存器配置
MOCK_REGISTER_CONFIG = {
    "0x00": {
        "name": "TEST_REG_0",
        "description": "测试寄存器0",
        "bit_fields": {
            "TEST_FIELD_0": {"bit_range": [0, 3], "description": "测试字段0"},
            "TEST_FIELD_1": {"bit_range": [4, 7], "description": "测试字段1"}
        }
    },
    "0x01": {
        "name": "TEST_REG_1", 
        "description": "测试寄存器1",
        "bit_fields": {
            "TEST_FIELD_2": {"bit_range": [0, 7], "description": "测试字段2"}
        }
    },
    "0x50": {
        "name": "PLL_CONTROL",
        "description": "PLL控制寄存器",
        "bit_fields": {
            "PLL1_PD": {"bit_range": [0, 0], "description": "PLL1电源控制"},
            "PLL2_PD": {"bit_range": [1, 1], "description": "PLL2电源控制"},
            "VCO_PD": {"bit_range": [2, 2], "description": "VCO电源控制"}
        }
    },
    "0x83": {
        "name": "PLL2_CONTROL",
        "description": "PLL2控制寄存器",
        "bit_fields": {
            "PLL2_PD": {"bit_range": [0, 0], "description": "PLL2电源控制"}
        }
    }
}

# 测试工具函数
def get_test_config(category=None):
    """获取测试配置"""
    if category:
        return TEST_CONFIG.get('categories', {}).get(category, {})
    return TEST_CONFIG

def get_mock_register_config():
    """获取模拟寄存器配置"""
    return MOCK_REGISTER_CONFIG

def setup_test_environment():
    """设置测试环境"""
    # 添加项目根目录到Python路径
    if PROJECT_ROOT not in sys.path:
        sys.path.insert(0, PROJECT_ROOT)
    
    # 创建测试日志目录
    log_dir = TEST_CONFIG['test_data']['test_logs']
    os.makedirs(log_dir, exist_ok=True)
    
    # 创建测试报告目录
    report_dir = TEST_CONFIG['reporting']['output_dir']
    os.makedirs(report_dir, exist_ok=True)
    
    return True

def cleanup_test_environment():
    """清理测试环境"""
    # 这里可以添加测试后的清理逻辑
    pass

# 测试装饰器
def test_timeout(seconds=None):
    """测试超时装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            import signal
            
            def timeout_handler(signum, frame):
                raise TimeoutError(f"测试 {func.__name__} 超时")
            
            timeout_seconds = seconds or TEST_CONFIG['timeout']['unit']
            signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(timeout_seconds)
            
            try:
                result = func(*args, **kwargs)
                signal.alarm(0)  # 取消超时
                return result
            except Exception as e:
                signal.alarm(0)  # 取消超时
                raise e
        
        return wrapper
    return decorator

def requires_qt():
    """需要Qt环境的测试装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                from PyQt5.QtWidgets import QApplication
                app = QApplication.instance()
                if app is None:
                    app = QApplication(sys.argv)
                return func(*args, **kwargs)
            except ImportError:
                print(f"跳过测试 {func.__name__}: 需要PyQt5环境")
                return None
        return wrapper
    return decorator

# 测试配置类
class TestConfig:
    """测试配置类"""

    def __init__(self):
        self.config = TEST_CONFIG
        self.mock_registers = MOCK_REGISTER_CONFIG

    def get(self, key, default=None):
        """获取配置值"""
        keys = key.split('.')
        value = self.config
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        return value

    def get_timeout(self, category='unit'):
        """获取超时时间"""
        return self.config['timeout'].get(category, 30)

    def get_mock_registers(self):
        """获取模拟寄存器配置"""
        return self.mock_registers

# 测试数据生成器
class TestDataGenerator:
    """测试数据生成器"""

    @staticmethod
    def generate_register_values(count=10):
        """生成测试寄存器值"""
        import random
        values = []
        for _ in range(count):
            values.append(random.randint(0, 0xFFFF))
        return values
    
    @staticmethod
    def generate_frequency_values(freq_type='vco', count=10):
        """生成测试频率值"""
        import random
        ranges = TEST_CONFIG['data_generation']['frequency_ranges']
        freq_range = ranges.get(freq_type, (1.0, 1000.0))
        
        values = []
        for _ in range(count):
            freq = random.uniform(freq_range[0], freq_range[1])
            values.append(round(freq, 2))
        return values
    
    @staticmethod
    def generate_bit_field_values(bit_width=8, count=10):
        """生成测试位字段值"""
        import random
        max_value = (1 << bit_width) - 1
        values = []
        for _ in range(count):
            values.append(random.randint(0, max_value))
        return values

if __name__ == "__main__":
    # 测试配置文件
    print("测试配置验证:")
    print(f"项目根目录: {PROJECT_ROOT}")
    print(f"测试超时配置: {TEST_CONFIG['timeout']}")
    print(f"测试分类: {list(TEST_CONFIG['categories'].keys())}")
    
    # 设置测试环境
    if setup_test_environment():
        print("✓ 测试环境设置成功")
    else:
        print("✗ 测试环境设置失败")
