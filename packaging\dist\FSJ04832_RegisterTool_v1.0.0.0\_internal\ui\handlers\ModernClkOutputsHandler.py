#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
现代化的时钟输出处理器
使用ModernBaseHandler作为基类，重构自原ClkOutputsHandler
主要功能：时钟输出配置、频率计算、SRCMUX控制、格式设置
"""

from PyQt5 import QtCore, QtWidgets
from ui.handlers.ModernBaseHandler import ModernBaseHandler
from ui.forms.Ui_ClkOutputs import Ui_ClkOutputs
from functools import partial
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class ModernClkOutputsHandler(ModernBaseHandler):
    """现代化的时钟输出处理器"""

    # 添加窗口关闭信号
    window_closed = QtCore.pyqtSignal()

    def __init__(self, parent=None, register_manager=None, **kwargs):
        """初始化现代化时钟输出处理器

        Args:
            parent: 父窗口
            register_manager: RegisterManager实例
            **kwargs: 其他参数（如register_repo等，用于兼容性）
        """
        super().__init__(parent, register_manager, **kwargs)

        # 设置窗口标题
        self.setWindowTitle("时钟输出配置 (现代化版本)")

        # 创建UI实例
        self.ui = Ui_ClkOutputs()
        self.ui.setupUi(self.content_widget)

        # 确保content_widget和UI控件都可见
        self.content_widget.setVisible(True)
        self.content_widget.show()

        # 强制更新布局
        self.content_widget.updateGeometry()
        self.updateGeometry()

        # 初始化时钟输出特定配置
        self._init_clk_output_config()

        # 手动调用初始化（因为测试环境没有事件循环）
        self._post_init()

        # 确保内容完全可见
        QtCore.QTimer.singleShot(200, self.ensure_content_fully_visible)

        logger.info("现代化时钟输出处理器初始化完成")



    def show(self):
        """重写show方法确保content_widget正确显示"""
        # 先调用父类的show方法
        super().show()

        # 确保content_widget可见
        if hasattr(self, 'content_widget') and self.content_widget:
            self.content_widget.setVisible(True)
            self.content_widget.show()

            # 强制刷新
            self.content_widget.update()
            self.content_widget.repaint()

            logger.info(f"ModernClkOutputsHandler.show(): content_widget可见性: {self.content_widget.isVisible()}")
            logger.info(f"ModernClkOutputsHandler.show(): content_widget大小: {self.content_widget.size()}")

        # 强制刷新整个窗口
        self.update()
        self.repaint()

    def _init_clk_output_config(self):
        """初始化时钟输出特定配置"""
        try:
            # 初始化VCO频率
            self.fvco = 2949.12

            # 初始化SRCMUX状态字典，记录每个输出的SRCMUX复选框状态
            self.srcmux_states = {i: False for i in range(14)}

            # 存储对SyncSysRefHandler的引用
            self.sync_sysref_handler = None

            # 定义格式名称映射字典
            self.combobox_options_map = {
                "CLKXFMT": {
                    0: "Powerdown",
                    1: "LVDS",
                    2: "HSDS 6 mA",
                    3: "HSDS 8 mA",
                    4: "LVPECL 1600 mV",
                    5: "LVPECL 2000 mV",
                    6: "LCPECL",
                    7: "CML 16 mA",
                    8: "CML 24 mA",
                    9: "CML 32 mA",
                    10: "CMOS (Off/Inv)",
                    11: "CMOS (Norm/Off)",
                    12: "CMOS (Inv/Inv)",
                    13: "CMOS (Inv/Norm)",
                    14: "CMOS (Norm/Inv)",
                    15: "CMOS (Norm/Norm)"
                }
            }

            # 设置默认值
            self._init_default_values()

            # 连接特殊信号
            self._connect_special_signals()

            logger.info("时钟输出特定配置初始化完成")

        except Exception as e:
            logger.error(f"初始化时钟输出配置时出错: {str(e)}")

    def _init_default_values(self):
        """初始化默认值"""
        try:
            # 设置Fvco默认值
            if hasattr(self.ui, "lineEditFvco"):
                self.ui.lineEditFvco.setText(str(self.fvco))

            # 初始化格式下拉框
            self._init_format_comboboxes()

            # 初始化分频器控件
            self._init_divider_controls()

            # 初始化电源和控制状态
            self._init_power_and_control_states()

            # 初始化SYSREF延迟控件
            self._init_sysref_delay_controls()

            # 初始化输出频率显示
            self._init_output_frequencies()

        except Exception as e:
            logger.error(f"初始化默认值时出错: {str(e)}")

    def _init_format_comboboxes(self):
        """初始化所有格式下拉框"""
        try:
            from PyQt5.QtWidgets import QComboBox

            # 格式选项
            format_options = self.combobox_options_map["CLKXFMT"]

            # 为每个CLKout格式下拉框设置选项
            for output_num in range(14):
                fmt_widget_name = f"CLKout{output_num}FMT"
                if hasattr(self.ui, fmt_widget_name):
                    fmt_widget = getattr(self.ui, fmt_widget_name)
                    if isinstance(fmt_widget, QComboBox):
                        # 清空并设置选项
                        fmt_widget.clear()
                        for value, text in sorted(format_options.items()):
                            fmt_widget.addItem(text, value)

                        # 设置默认选择（LVDS）
                        fmt_widget.setCurrentIndex(1)
                        logger.debug(f"已初始化 {fmt_widget_name} 格式选项")

            logger.info("所有格式下拉框初始化完成")

            # 应用之前延迟设置的ComboBox值
            if hasattr(self, 'apply_pending_combobox_values'):
                self.apply_pending_combobox_values()

        except Exception as e:
            logger.error(f"初始化格式下拉框时出错: {str(e)}")

    def _init_divider_controls(self):
        """初始化分频器控件的范围和默认值"""
        try:
            # 初始化DCLK分频器控件
            dclk_dividers = [
                "DCLK0_1DIV", "DCLK2_3DIV", "DCLK4_5DIV", "DCLK6_7DIV",
                "DCLK8_9DIV", "DCLK10_11DIV", "DCLK12_13DIV"
            ]

            # 根据register.json设置正确的默认值
            dclk_default_values = {
                "DCLK0_1DIV": 2,    # 0000000010 = 2
                "DCLK2_3DIV": 4,    # 0000000100 = 4
                "DCLK4_5DIV": 8,    # 0000001000 = 8
                "DCLK6_7DIV": 8,    # 0000001000 = 8
                "DCLK8_9DIV": 8,    # 0000001000 = 8
                "DCLK10_11DIV": 8,  # 0000001000 = 8
                "DCLK12_13DIV": 2   # 0000000010 = 2
            }

            for div_name in dclk_dividers:
                if hasattr(self.ui, div_name):
                    widget = getattr(self.ui, div_name)
                    if hasattr(widget, 'setMinimum') and hasattr(widget, 'setMaximum'):
                        widget.setMinimum(1)
                        widget.setMaximum(1023)  # 根据register.json，范围是1:1023
                        default_value = dclk_default_values.get(div_name, 2)
                        widget.setValue(default_value)
                        logger.debug(f"已初始化{div_name}: 范围(1-1023), 默认值({default_value})")

            # 初始化DCLK延迟控件
            dclk_delays = [
                "DCLK0_1DDLY", "DCLK2_3DDLY", "DCLK4_5DDLY", "DCLK6_7DDLY",
                "DCLK8_9DDLY", "DCLK10_11DDLY", "DCLK12_13DDLY"
            ]

            for delay_name in dclk_delays:
                if hasattr(self.ui, delay_name):
                    widget = getattr(self.ui, delay_name)
                    if hasattr(widget, 'setMinimum') and hasattr(widget, 'setMaximum'):
                        widget.setMinimum(2)
                        widget.setMaximum(1023)  # 根据register.json，范围是2:1023
                        widget.setValue(10)  # 默认值设为10
                        logger.debug(f"已初始化{delay_name}: 范围(2-1023), 默认值(10)")

            # 初始化DDLYd步进计数器
            if hasattr(self.ui, "DDLYdStepCNT_1"):
                self.ui.DDLYdStepCNT_1.setMinimum(0)
                self.ui.DDLYdStepCNT_1.setMaximum(255)
                self.ui.DDLYdStepCNT_1.setValue(0)
                logger.debug("已初始化DDLYdStepCNT_1: 范围(0-255), 默认值(0)")

            logger.info("分频器控件初始化完成")

        except Exception as e:
            logger.error(f"初始化分频器控件时出错: {str(e)}")

    def _init_power_and_control_states(self):
        """初始化电源管理和控制状态"""
        try:
            # 初始化DCLK电源控制位（默认为0，即启用）
            dclk_power_controls = [
                "DCLK0_1PD", "DCLK2_3PD", "DCLK4_5PD", "DCLK6_7PD",
                "DCLK8_9PD", "DCLK10_11PD", "DCLK12_13PD"
            ]

            for control_name in dclk_power_controls:
                if hasattr(self.ui, control_name):
                    control = getattr(self.ui, control_name)
                    control.setChecked(False)  # 默认启用（0表示启用）
                    logger.debug(f"已设置{control_name}为启用状态")

            # 初始化DCLK延迟电源控制位（根据register.json默认为0，即启用）
            dclk_delay_power_controls = [
                "DCLK0_1DDLYPD", "DCLK2_3DDLYPD", "DCLK4_5DDLYPD", "DCLK6_7DDLYPD",
                "DCLK8_9DDLYPD", "DCLK10_11DDLYPD", "DCLK12_13DDLYPD"
            ]

            for control_name in dclk_delay_power_controls:
                if hasattr(self.ui, control_name):
                    control = getattr(self.ui, control_name)
                    control.setChecked(False)  # 根据register.json默认值0，启用延迟功能
                    logger.debug(f"已设置{control_name}为启用状态")

            # 初始化DDLYd使能控件（默认为0，即禁用）
            ddlyd_enable_controls = [
                "DDLYd0EN", "DDLYd2EN", "DDLYd4EN", "DDLYd6EN",
                "DDLYd8EN", "DDLYd10EN", "DDLYd12EN"
            ]

            for control_name in ddlyd_enable_controls:
                if hasattr(self.ui, control_name):
                    control = getattr(self.ui, control_name)
                    control.setChecked(False)  # 默认禁用动态延迟
                    logger.debug(f"已设置{control_name}为禁用状态")

            # 初始化SCLK电源控制位（根据register.json默认为1，即关闭）
            sclk_power_controls = [
                "SCLK0_1PD", "SCLK2_3PD", "SCLK4_5PD", "SCLK6_7PD",
                "SCLK8_9PD", "SCLK10_11PD", "SCLK12_13PD"
            ]

            for control_name in sclk_power_controls:
                if hasattr(self.ui, control_name):
                    control = getattr(self.ui, control_name)
                    control.setChecked(True)  # 根据register.json默认值1，关闭SYSREF输出
                    logger.debug(f"已设置{control_name}为关闭状态")

            # 初始化SCLK半步触发控件（默认为0）
            sclk_hstrig_controls = [
                "SCLK0_1HSTrig", "SCLK2_3HSTrig", "SCLK4_5HSTrig", "SCLK6_7HSTrig",
                "SCLK8_9HSTrig", "SCLK10_11HSTrig", "SCLK12_13HSTrig"
            ]

            for control_name in sclk_hstrig_controls:
                if hasattr(self.ui, control_name):
                    control = getattr(self.ui, control_name)
                    control.setChecked(False)  # 默认无相位调整
                    logger.debug(f"已设置{control_name}为无相位调整")

            # 初始化SYNC禁用控件（默认为0，即不禁用）
            sync_dis_controls = [
                "SYNCDIS0", "SYNCDIS2", "SYNCDIS4", "SYNCDIS6",
                "SYNCDIS8", "SYNCDIS10", "SYNCDIS12"
            ]

            for control_name in sync_dis_controls:
                if hasattr(self.ui, control_name):
                    control = getattr(self.ui, control_name)
                    control.setChecked(False)  # 默认不禁用同步
                    logger.debug(f"已设置{control_name}为不禁用同步")

            logger.info("电源管理和控制状态初始化完成")

        except Exception as e:
            logger.error(f"初始化电源管理和控制状态时出错: {str(e)}")

    def _init_sysref_delay_controls(self):
        """初始化SYSREF延迟控件"""
        try:
            # 初始化SCLK延迟ComboBox控件
            sclk_delay_controls = [
                "SCLK0_1DDLY", "SCLK2_3DDLY", "SCLK4_5DDLY", "SCLK6_7DDLY",
                "SCLK8_9DDLY", "SCLK10_11DDLY", "SCLK12_13DDLY"
            ]

            # SYSREF延迟选项（根据register.json的SCLKX_Y_DDLY位域）
            delay_options = ["0: 0.0ns", "1: 0.5ns", "2: 1.0ns", "3: 1.5ns"]

            for control_name in sclk_delay_controls:
                if hasattr(self.ui, control_name):
                    control = getattr(self.ui, control_name)
                    if hasattr(control, 'clear') and hasattr(control, 'addItems'):
                        control.clear()
                        control.addItems(delay_options)
                        control.setCurrentIndex(1)  # 根据register.json默认值0001，选择索引1（0.5ns）
                        logger.debug(f"已初始化{control_name}: 默认0.5ns延迟")

            logger.info("SYSREF延迟控件初始化完成")

        except Exception as e:
            logger.error(f"初始化SYSREF延迟控件时出错: {str(e)}")

    def _init_output_frequencies(self):
        """初始化输出频率显示"""
        try:
            # 确保分频器值正确初始化
            self._ensure_divider_values_initialized()

            # 同步UI控件值到寄存器
            self._sync_divider_values_to_registers()

            # 延迟计算频率，确保寄存器映射已建立
            # 在_post_init中会重新计算
            logger.info("输出频率初始化完成（延迟计算）")

        except Exception as e:
            logger.error(f"初始化输出频率时出错: {str(e)}")

    def _ensure_divider_values_initialized(self):
        """确保分频器值正确初始化"""
        try:
            # 检查并设置分频器的默认值
            divider_names = ["DCLK0_1DIV", "DCLK2_3DIV", "DCLK4_5DIV", "DCLK6_7DIV",
                            "DCLK8_9DIV", "DCLK10_11DIV", "DCLK12_13DIV"]

            for div_name in divider_names:
                if hasattr(self.ui, div_name):
                    widget = getattr(self.ui, div_name)
                    if hasattr(widget, 'value') and hasattr(widget, 'setValue'):
                        current_value = widget.value()
                        # 确保分频器值为默认值2（如果当前值不合理）
                        if current_value < 1 or current_value == 0:
                            widget.setValue(2)
                            logger.debug(f"修正 {div_name} 值从 {current_value} 到 2")
                        elif current_value != 2:
                            # 如果值不是我们设置的默认值2，保持当前值但记录
                            logger.debug(f"{div_name} 当前值为 {current_value}，保持不变")

            logger.debug("分频器值初始化检查完成")

        except Exception as e:
            logger.error(f"确保分频器值初始化时出错: {str(e)}")

    def _sync_divider_values_to_registers(self):
        """同步UI控件的分频器值到寄存器"""
        try:
            divider_names = ["DCLK0_1DIV", "DCLK2_3DIV", "DCLK4_5DIV", "DCLK6_7DIV",
                            "DCLK8_9DIV", "DCLK10_11DIV", "DCLK12_13DIV"]

            for div_name in divider_names:
                if hasattr(self.ui, div_name):
                    widget = getattr(self.ui, div_name)
                    if hasattr(widget, 'value'):
                        ui_value = widget.value()

                        # 同步到寄存器
                        if self.register_manager and div_name in self.widget_register_map:
                            widget_info = self.widget_register_map[div_name]
                            reg_addr = widget_info["register_addr"]
                            bit_def = widget_info["bit_def"]
                            bit_name = bit_def.get("name", "")

                            if bit_name:
                                self.register_manager.set_bit_field_value(reg_addr, bit_name, ui_value)
                                logger.debug(f"同步 {div_name} UI值 {ui_value} 到寄存器")

            logger.debug("分频器值同步完成")

        except Exception as e:
            logger.error(f"同步分频器值时出错: {str(e)}")

    def _connect_special_signals(self):
        """连接特殊信号"""
        try:
            # 连接VCO频率输入信号
            if hasattr(self.ui, "lineEditFvco"):
                self.ui.lineEditFvco.returnPressed.connect(self.calculate_output_frequencies)
                self.ui.lineEditFvco.textChanged.connect(self.calculate_output_frequencies)
                logger.info("已连接 lineEditFvco 信号")

            # 连接所有SRCMUX复选框信号
            self._connect_srcmux_signals()

        except Exception as e:
            logger.error(f"连接特殊信号时出错: {str(e)}")

    def _connect_srcmux_signals(self):
        """连接所有奇数输出的SRCMUX复选框信号"""
        try:
            # 为每个输出编号单独创建连接
            for output_num in range(14):
                srcmux_attr = f"CLKout{output_num}_SRCMUX"
                if hasattr(self.ui, srcmux_attr):
                    srcmux_checkbox = getattr(self.ui, srcmux_attr)
                    # 使用functools.partial确保每个连接使用正确的output_num值
                    srcmux_checkbox.stateChanged.connect(
                        partial(self.update_srcmux_output, output_num)
                    )
                    logger.debug(f"已连接 {srcmux_attr} 信号")
            logger.info("已连接所有时钟输出的SRCMUX复选框信号")
        except Exception as e:
            logger.error(f"连接SRCMUX信号时出错: {str(e)}")

    def _post_init(self):
        """重写基类的_post_init方法，在寄存器映射建立后计算频率"""
        try:
            # 调用基类的_post_init方法
            super()._post_init()

            # 现在寄存器映射已经建立，可以安全地计算频率
            logger.info("寄存器映射已建立，开始计算输出频率")
            self.calculate_output_frequencies()

        except Exception as e:
            logger.error(f"后初始化时出错: {str(e)}")

    def on_register_value_changed(self, widget_name, reg_addr, reg_value):
        """处理寄存器值变化"""
        logger.info(f"时钟输出: 寄存器 {reg_addr} 值变化 (控件: {widget_name}) -> 0x{reg_value:04X}")

        # 重新计算频率
        self.calculate_output_frequencies()

        # 处理特定控件的业务逻辑
        if "DIV" in widget_name:
            self._handle_divider_change(widget_name)
        elif "FMT" in widget_name:
            self._handle_format_change(widget_name)
        elif "SRCMUX" in widget_name:
            self._handle_srcmux_change(widget_name)

    def on_global_register_update(self, reg_addr, reg_value):
        """处理全局寄存器更新"""
        logger.debug(f"时钟输出: 收到全局更新 {reg_addr} = 0x{reg_value:04X}")

        # 重新计算频率
        self.calculate_output_frequencies()

    # === 业务逻辑方法 ===

    def calculate_output_frequencies(self):
        """根据Fvco值计算所有输出频率，尊重SRCMUX状态"""
        try:
            # 获取Fvco输入值
            if not hasattr(self.ui, "lineEditFvco"):
                logger.warning("未找到lineEditFvco控件，无法计算频率")
                return

            fvco_text = self.ui.lineEditFvco.text()
            if not fvco_text:
                return

            # 转换为浮点数
            try:
                self.fvco = float(fvco_text)
            except ValueError:
                logger.warning(f"无效的VCO频率输入: {fvco_text}")
                return

            # 获取所有输出控件并计算频率
            for output_num in range(14):
                output_attr = f"lineEditFout{output_num}Output"
                if hasattr(self.ui, output_attr):
                    output_widget = getattr(self.ui, output_attr)
                    self._update_single_output_frequency(output_num, output_widget)

        except Exception as e:
            logger.error(f"计算输出频率时发生错误: {str(e)}")

    def _update_single_output_frequency(self, output_num, output_widget):
        """更新单个输出的频率显示 - 参考传统ClkOutputsHandler的处理方式"""
        try:
            # 检查该输出是否使用SRCMUX - 从UI控件读取实际状态
            is_srcmux_enabled = self._get_srcmux_state_from_ui(output_num)

            if is_srcmux_enabled:
                # 使用系统参考频率 - 参考传统ClkOutputsHandler的处理方式
                if self.sync_sysref_handler:
                    # 获取系统参考频率信息 - 参考传统版本的完全相同逻辑
                    sysref_fvco_text = self.sync_sysref_handler.ui.InternalVCOFreq.text()
                    if sysref_fvco_text:
                        try:
                            sysref_fvco = float(sysref_fvco_text)
                            sysref_div = self.sync_sysref_handler.ui.spinBoxSysrefDIV.value()
                            if sysref_div > 0:
                                sysref_freq = sysref_fvco / sysref_div
                                output_widget.setText(f"{sysref_freq:.2f}")
                                logger.debug(f"输出{output_num}使用SRCMUX频率: {sysref_freq:.2f} MHz (系统参考FVCO={sysref_fvco} / SysrefDIV={sysref_div})")
                                return
                            else:
                                logger.debug(f"输出{output_num}使用SRCMUX但系统参考分频比无效: {sysref_div}")
                        except ValueError:
                            logger.debug(f"输出{output_num}使用SRCMUX但系统参考VCO频率无效: {sysref_fvco_text}")
                    else:
                        logger.debug(f"输出{output_num}使用SRCMUX但无法获取系统参考VCO频率")

                    # 如果无法获取有效的系统参考频率，显示0.00
                    output_widget.setText("0.00")
                    return
                else:
                    # 参考传统版本的简单处理方式
                    logger.debug(f"输出{output_num}使用SRCMUX但未连接到系统参考处理器")
                    output_widget.setText("0.00")
                    return

            # 使用正常计算方式 - 参考传统版本：使用时钟输出页面的VCO和分频比
            divider_value = self._get_divider_value_for_output(output_num)
            if divider_value > 0 and self.fvco > 0:
                output_freq = self.fvco / divider_value
                output_widget.setText(f"{output_freq:.2f}")
                logger.debug(f"输出{output_num}频率: {output_freq:.2f} MHz (时钟输出VCO: {self.fvco}, 分频: {divider_value})")
            else:
                output_widget.setText("0.00")

        except Exception as e:
            logger.error(f"更新输出{output_num}频率时出错: {str(e)}")

    def _get_srcmux_state_from_ui(self, output_num):
        """从UI控件读取SRCMUX的实际状态

        Args:
            output_num: 输出编号

        Returns:
            bool: True如果SRCMUX被选中，False否则
        """
        try:
            # 获取对应的SRCMUX复选框
            srcmux_attr = f"CLKout{output_num}_SRCMUX"
            if hasattr(self.ui, srcmux_attr):
                srcmux_checkbox = getattr(self.ui, srcmux_attr)
                is_checked = srcmux_checkbox.isChecked()

                # 同时更新内存中的状态
                self.srcmux_states[output_num] = is_checked

                logger.debug(f"输出{output_num} SRCMUX状态: {is_checked}")
                return is_checked
            else:
                logger.debug(f"未找到输出{output_num}的SRCMUX控件: {srcmux_attr}")
                return False

        except Exception as e:
            logger.error(f"读取输出{output_num} SRCMUX状态时出错: {str(e)}")
            return False

    def _is_sysref_properly_configured(self):
        """检查系统参考是否正确配置"""
        try:
            if not self.sync_sysref_handler:
                return False

            # 检查是否有有效的VCO频率
            if not hasattr(self.sync_sysref_handler.ui, "InternalVCOFreq"):
                return False

            fvco_text = self.sync_sysref_handler.ui.InternalVCOFreq.text()
            if not fvco_text:
                return False

            try:
                fvco = float(fvco_text)
                if fvco <= 0:
                    return False
            except ValueError:
                return False

            # 检查是否有有效的分频比
            if not hasattr(self.sync_sysref_handler.ui, "spinBoxSysrefDIV"):
                return False

            sysref_div = self.sync_sysref_handler.ui.spinBoxSysrefDIV.value()
            if sysref_div <= 0:
                return False

            return True

        except Exception as e:
            logger.error(f"检查系统参考配置时出错: {str(e)}")
            return False

    def _get_divider_value_for_output(self, output_num):
        """获取指定输出的分频值"""
        try:
            # 确定分频器名称
            divider_name = self._get_divider_name_for_output(output_num)

            # 从RegisterManager获取分频值
            if self.register_manager and divider_name in self.widget_register_map:
                widget_info = self.widget_register_map[divider_name]
                reg_addr = widget_info["register_addr"]
                bit_def = widget_info["bit_def"]
                bit_name = bit_def.get("name", "")

                if bit_name:
                    bit_value = self.register_manager.get_bit_field_value(reg_addr, bit_name)
                    divider_value = max(1, bit_value)  # 确保分频值至少为1
                    logger.debug(f"从寄存器获取输出{output_num}分频值: {divider_name} = {divider_value}")
                    return divider_value

            # 如果无法从寄存器获取，尝试从UI控件获取
            if hasattr(self.ui, divider_name):
                widget = getattr(self.ui, divider_name)
                if hasattr(widget, 'value'):
                    ui_value = max(1, widget.value())
                    logger.debug(f"从UI控件获取输出{output_num}分频值: {divider_name} = {ui_value}")
                    return ui_value

            # 默认返回1
            logger.warning(f"无法获取输出{output_num}分频值，使用默认值1")
            return 1

        except Exception as e:
            logger.error(f"获取输出{output_num}分频值时出错: {str(e)}")
            import traceback
            logger.debug(traceback.format_exc())
            return 1

    def _get_divider_name_for_output(self, output_num):
        """根据输出编号获取对应的分频器名称"""
        if output_num in [0, 1]:
            return "DCLK0_1DIV"
        elif output_num in [2, 3]:
            return "DCLK2_3DIV"
        elif output_num in [4, 5]:
            return "DCLK4_5DIV"
        elif output_num in [6, 7]:
            return "DCLK6_7DIV"
        elif output_num in [8, 9]:
            return "DCLK8_9DIV"
        elif output_num in [10, 11]:
            return "DCLK10_11DIV"
        elif output_num in [12, 13]:
            return "DCLK12_13DIV"
        else:
            return "DCLK0_1DIV"  # 默认返回第一个分频器

    def _get_sysref_frequency(self):
        """获取系统参考频率"""
        try:
            if not self.sync_sysref_handler:
                return 0.0

            # 获取系统参考频率信息
            if hasattr(self.sync_sysref_handler.ui, "InternalVCOFreq"):
                fvco_text = self.sync_sysref_handler.ui.InternalVCOFreq.text()
                if fvco_text:
                    fvco = float(fvco_text)

                    # 获取系统参考分频比
                    if hasattr(self.sync_sysref_handler.ui, "spinBoxSysrefDIV"):
                        sysref_div = self.sync_sysref_handler.ui.spinBoxSysrefDIV.value()
                        if sysref_div > 0:
                            return fvco / sysref_div

            return 0.0

        except Exception as e:
            logger.error(f"获取系统参考频率时出错: {str(e)}")
            return 0.0

    def update_srcmux_output(self, output_num, state):
        """更新SRCMUX控制的输出频率 - 参考传统ClkOutputsHandler的处理方式"""
        try:
            # 保存当前复选框状态
            self.srcmux_states[output_num] = (state == QtCore.Qt.Checked)

            # 获取对应的输出控件
            output_widget = getattr(self.ui, f"lineEditFout{output_num}Output")

            if state == QtCore.Qt.Checked:
                # 使用系统参考时钟 - 参考传统版本的完全相同逻辑
                if self.sync_sysref_handler:
                    # 获取系统参考频率信息 - 使用与传统版本相同的变量名
                    sysref_fvco_text = self.sync_sysref_handler.ui.InternalVCOFreq.text()
                    if sysref_fvco_text:
                        try:
                            sysref_fvco = float(sysref_fvco_text)
                            sysref_div = self.sync_sysref_handler.ui.spinBoxSysrefDIV.value()
                            if sysref_div > 0:
                                sysref_freq = sysref_fvco / sysref_div
                                output_widget.setText(f"{sysref_freq:.2f}")
                                logger.info(f"输出{output_num}已设置为SRCMUX模式，频率: {sysref_freq:.2f} MHz (系统参考FVCO={sysref_fvco} / SysrefDIV={sysref_div})")
                                return
                            else:
                                logger.debug(f"输出{output_num}启用SRCMUX但系统参考分频比无效: {sysref_div}")
                        except ValueError:
                            logger.debug(f"输出{output_num}启用SRCMUX但系统参考VCO频率格式无效: {sysref_fvco_text}")
                    else:
                        logger.debug(f"输出{output_num}启用SRCMUX但无法获取系统参考VCO频率")

                    # 如果无法获取有效的系统参考频率，显示0.00
                    output_widget.setText("0.00")
                else:
                    logger.debug(f"输出{output_num}启用SRCMUX但未连接到系统参考处理器")
                    output_widget.setText("0.00")
            else:
                # 如果取消选中，恢复原来的计算方式 - 参考传统版本
                self._update_single_output_frequency(output_num, output_widget)
                logger.info(f"输出{output_num}已恢复为默认计算方式")

        except Exception as e:
            logger.error(f"更新输出{output_num} SRCMUX时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _handle_divider_change(self, widget_name):
        """处理分频器值变化"""
        try:
            logger.info(f"分频器 {widget_name} 值变化")
            # 重新计算相关输出的频率
            self.calculate_output_frequencies()
        except Exception as e:
            logger.error(f"处理分频器变化时出错: {str(e)}")

    def _handle_format_change(self, widget_name):
        """处理格式变化"""
        try:
            logger.info(f"格式控件 {widget_name} 值变化")
            # 格式变化不影响频率计算，但可以记录状态
        except Exception as e:
            logger.error(f"处理格式变化时出错: {str(e)}")

    def _handle_srcmux_change(self, widget_name):
        """处理SRCMUX变化"""
        try:
            logger.info(f"SRCMUX控件 {widget_name} 值变化")
            # SRCMUX变化会在update_srcmux_output中处理
        except Exception as e:
            logger.error(f"处理SRCMUX变化时出错: {str(e)}")

    # === 公共接口方法 ===

    def get_current_status(self):
        """获取当前时钟输出状态

        Returns:
            dict: 当前状态信息
        """
        try:
            status = {}

            # 获取VCO频率
            status["vco_frequency"] = self.fvco

            # 获取SRCMUX状态
            status["srcmux_states"] = self.srcmux_states.copy()

            # 获取所有输出频率
            output_frequencies = {}
            for output_num in range(14):
                output_attr = f"lineEditFout{output_num}Output"
                if hasattr(self.ui, output_attr):
                    output_widget = getattr(self.ui, output_attr)
                    try:
                        freq_text = output_widget.text()
                        output_frequencies[f"output_{output_num}"] = float(freq_text) if freq_text else 0.0
                    except ValueError:
                        output_frequencies[f"output_{output_num}"] = 0.0

            status["output_frequencies"] = output_frequencies

            # 获取分频器状态
            divider_values = {}
            for output_num in range(14):
                divider_name = self._get_divider_name_for_output(output_num)
                if divider_name not in divider_values:
                    divider_values[divider_name] = self._get_divider_value_for_output(output_num)

            status["divider_values"] = divider_values

            return status

        except Exception as e:
            logger.error(f"获取时钟输出状态时出错: {str(e)}")
            return {}

    def set_sync_sysref_handler(self, handler):
        """设置系统参考处理器的引用

        Args:
            handler: SyncSysRefHandler实例
        """
        try:
            self.sync_sysref_handler = handler
            logger.info("已设置系统参考处理器引用")

            # 连接系统参考处理器的分频比变化信号
            if self.sync_sysref_handler and hasattr(self.sync_sysref_handler.ui, "spinBoxSysrefDIV"):
                self.sync_sysref_handler.ui.spinBoxSysrefDIV.valueChanged.connect(
                    self.update_srcmux_outputs
                )
                logger.info("已连接系统参考分频比变化信号")

                # 连接VCO频率变化信号
                if hasattr(self.sync_sysref_handler.ui, "InternalVCOFreq"):
                    self.sync_sysref_handler.ui.InternalVCOFreq.textChanged.connect(
                        self.update_srcmux_outputs
                    )
                    logger.info("已连接系统参考VCO频率变化信号")

            # 立即更新所有使用SRCMUX的输出
            self.update_srcmux_outputs()

        except Exception as e:
            logger.error(f"设置系统参考处理器引用时出错: {str(e)}")

    def update_srcmux_outputs(self):
        """更新所有使用SRCMUX的输出频率 - 参考传统版本的简洁处理"""
        try:
            # 更新所有输出，让_update_single_output_frequency从UI读取实际状态
            updated_count = 0
            for output_num in range(14):
                output_attr = f"lineEditFout{output_num}Output"
                if hasattr(self.ui, output_attr):
                    # 检查该输出是否使用SRCMUX（从UI读取）
                    if self._get_srcmux_state_from_ui(output_num):
                        output_widget = getattr(self.ui, output_attr)
                        self._update_single_output_frequency(output_num, output_widget)
                        updated_count += 1

            if updated_count > 0:
                logger.debug(f"已更新{updated_count}个SRCMUX输出频率")

        except Exception as e:
            logger.error(f"更新SRCMUX输出时出错: {str(e)}")

    def set_output_preset(self, preset_name):
        """设置时钟输出预设配置

        Args:
            preset_name: 预设名称
        """
        try:
            logger.info(f"应用时钟输出预设: {preset_name}")

            # 定义预设配置
            presets = {
                "default": {
                    "vco_frequency": 2949.12,
                    "dividers": {
                        "DCLK0_1DIV": 1,
                        "DCLK2_3DIV": 1,
                        "DCLK4_5DIV": 1,
                        "DCLK6_7DIV": 1,
                        "DCLK8_9DIV": 1,
                        "DCLK10_11DIV": 1,
                        "DCLK12_13DIV": 1
                    },
                    "all_srcmux": False
                },
                "low_frequency": {
                    "vco_frequency": 2949.12,
                    "dividers": {
                        "DCLK0_1DIV": 4,
                        "DCLK2_3DIV": 4,
                        "DCLK4_5DIV": 4,
                        "DCLK6_7DIV": 4,
                        "DCLK8_9DIV": 4,
                        "DCLK10_11DIV": 4,
                        "DCLK12_13DIV": 4
                    },
                    "all_srcmux": False
                },
                "high_frequency": {
                    "vco_frequency": 3000.0,
                    "dividers": {
                        "DCLK0_1DIV": 1,
                        "DCLK2_3DIV": 1,
                        "DCLK4_5DIV": 1,
                        "DCLK6_7DIV": 1,
                        "DCLK8_9DIV": 1,
                        "DCLK10_11DIV": 1,
                        "DCLK12_13DIV": 1
                    },
                    "all_srcmux": False
                },
                "all_sysref": {
                    "vco_frequency": 2949.12,
                    "dividers": {},  # 分频器值不重要，因为都使用SRCMUX
                    "all_srcmux": True
                }
            }

            if preset_name not in presets:
                logger.warning(f"未知的预设: {preset_name}")
                return

            preset = presets[preset_name]

            # 应用VCO频率
            if hasattr(self.ui, "lineEditFvco"):
                self.ui.lineEditFvco.setText(str(preset["vco_frequency"]))
                self.fvco = preset["vco_frequency"]

            # 应用分频器值
            for divider_name, divider_value in preset.get("dividers", {}).items():
                if hasattr(self.ui, divider_name):
                    widget = getattr(self.ui, divider_name)
                    if isinstance(widget, QtWidgets.QSpinBox):
                        widget.setValue(divider_value)

                        # 同时更新寄存器值
                        if self.register_manager and divider_name in self.widget_register_map:
                            widget_info = self.widget_register_map[divider_name]
                            reg_addr = widget_info["register_addr"]
                            bit_def = widget_info["bit_def"]
                            bit_name = bit_def.get("name", "")

                            if bit_name:
                                self.register_manager.set_bit_field_value(reg_addr, bit_name, divider_value)

            # 应用SRCMUX设置
            all_srcmux = preset.get("all_srcmux", False)
            for output_num in range(14):
                srcmux_attr = f"CLKout{output_num}_SRCMUX"
                if hasattr(self.ui, srcmux_attr):
                    srcmux_checkbox = getattr(self.ui, srcmux_attr)
                    srcmux_checkbox.setChecked(all_srcmux)
                    self.srcmux_states[output_num] = all_srcmux

            # 重新计算频率
            self.calculate_output_frequencies()

            logger.info(f"时钟输出预设 {preset_name} 应用完成")

        except Exception as e:
            logger.error(f"设置时钟输出预设时出错: {str(e)}")

    def get_output_count(self):
        """获取输出数量和状态统计

        Returns:
            dict: 包含输出统计信息的字典
        """
        try:
            total_outputs = 14
            active_outputs = 0
            srcmux_outputs = 0

            for output_num in range(14):
                output_attr = f"lineEditFout{output_num}Output"
                if hasattr(self.ui, output_attr):
                    output_widget = getattr(self.ui, output_attr)
                    try:
                        freq_text = output_widget.text()
                        freq_value = float(freq_text) if freq_text else 0.0
                        if freq_value > 0:
                            active_outputs += 1
                    except ValueError:
                        pass

                if self.srcmux_states.get(output_num, False):
                    srcmux_outputs += 1

            return {
                "total_outputs": total_outputs,
                "active_outputs": active_outputs,
                "srcmux_outputs": srcmux_outputs,
                "normal_outputs": active_outputs - srcmux_outputs
            }

        except Exception as e:
            logger.error(f"获取输出统计时出错: {str(e)}")
            return {"total_outputs": 14, "active_outputs": 0, "srcmux_outputs": 0, "normal_outputs": 0}



    @classmethod
    def create_for_testing(cls, parent=None):
        """创建测试实例"""
        try:
            # 创建模拟的RegisterManager
            from core.services.register.RegisterManager import RegisterManager
            import json
            import os
            
            # 加载寄存器配置
            config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'lib', 'register.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                registers_config = json.load(f)
            
            register_manager = RegisterManager(registers_config)
            
            # 创建实例
            instance = cls(parent, register_manager)
            
            logger.info("创建现代化ClkOutputsHandler测试实例成功")
            return instance
            
        except Exception as e:
            logger.error(f"创建测试实例时出错: {str(e)}")
            raise


if __name__ == "__main__":
    import sys
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    # 创建测试实例
    handler = ModernClkOutputsHandler.create_for_testing()
    handler.show()
    
    sys.exit(app.exec_())
