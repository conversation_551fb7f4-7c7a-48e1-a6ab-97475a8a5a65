"""
标签页窗口管理器
负责管理工具窗口的标签页创建、关闭和状态管理
"""

from PyQt5 import sip
from utils.Log import logger


class TabWindowManager:
    """标签页窗口管理器，专门处理标签页相关的逻辑"""
    
    def __init__(self, main_window):
        """初始化标签页窗口管理器
        
        Args:
            main_window: 主窗口实例
        """
        self.main_window = main_window
    
    def close_tool_tab(self, index):
        """关闭工具标签页"""
        widget = self.main_window.tools_tab_widget.widget(index)  # 获取与标签页关联的控件
        tab_text = self.main_window.tools_tab_widget.tabText(index)

        self.main_window.tools_tab_widget.removeTab(index)  # 移除标签页

        action_unchecked_successfully = False
        if widget:
            # 尝试通过直接存储在widget上的属性来获取关联的QAction
            associated_action = getattr(widget, 'associated_action', None)
            if associated_action and hasattr(associated_action, 'setChecked') and callable(associated_action.setChecked):
                if not sip.isdeleted(associated_action):
                    associated_action.setChecked(False)
                    logger.info(f"通过直接属性取消选中菜单项: '{associated_action.text()}' (for tab '{tab_text}')")
                    action_unchecked_successfully = True
                else:
                    logger.warning(f"QAction '{associated_action.text()}' (for tab '{tab_text}') 已被删除，无法取消选中.")
            else:
                logger.debug(f"在控件上未找到有效的 'associated_action' (for tab '{tab_text}'). 尝试后备方案.")
        else:
            logger.warning(f"未找到标签页 '{tab_text}' (index {index}) 的关联控件. 尝试后备方案取消选中.")

        if not action_unchecked_successfully:
            self._uncheck_action_by_tab_text(tab_text)  # 后备方案

        if self.main_window.tools_tab_widget.count() == 0:
            self.main_window.tools_tab_widget.setVisible(False)
        
        logger.info(f"关闭了工具标签页: {tab_text}")

    def _uncheck_action_by_tab_text(self, tab_text):
        """根据标签页文本取消选中关联的QAction（后备方法）"""
        # 将标签页标题的已知前缀映射到其对应的QAction属性名称
        # 注意：这里的键是菜单项的文本，可能需要与工具栏按钮的文本或标签页的实际标题匹配
        action_map = {
            # 这些key应该与触发QAction的文本或tab标题的主要部分匹配
            "模式设置": self.main_window.set_modes_action if hasattr(self.main_window, 'set_modes_action') else None,
            "时钟输入": self.main_window.clkin_control_action if hasattr(self.main_window, 'clkin_control_action') else None, # 假设tab标题以"时钟输入"开头
            "PLL1 & PLL2 控制": self.main_window.pll_control_action if hasattr(self.main_window, 'pll_control_action') else None,
            "同步系统参考": self.main_window.sync_sysref_action if hasattr(self.main_window, 'sync_sysref_action') else None,
            "时钟输出": self.main_window.clk_output_action if hasattr(self.main_window, 'clk_output_action') else None,
        }

        action_to_uncheck = None
        matched_prefix_key = None

        # 尝试基于tab_text的前缀匹配
        for prefix_key, action_instance in action_map.items():
            if action_instance and tab_text.startswith(prefix_key):
                action_to_uncheck = action_instance
                matched_prefix_key = prefix_key
                break
        
        if action_to_uncheck:
            if hasattr(action_to_uncheck, 'setChecked') and callable(action_to_uncheck.setChecked):
                if not sip.isdeleted(action_to_uncheck):
                    action_to_uncheck.setChecked(False)
                    logger.info(f"后备方案: 取消选中菜单项 '{action_to_uncheck.text()}' (for tab '{tab_text}', matched prefix '{matched_prefix_key}')")
                else:
                    logger.warning(f"后备方案: QAction '{action_to_uncheck.text()}' (for tab '{tab_text}') 已被删除.")
            else:
                logger.warning(f"后备方案: 为标签页 '{tab_text}' 找到的Action对象无效或没有setChecked方法.")
        else:
            logger.warning(f"后备方案: 未能为标签页文本 '{tab_text}' 匹配到任何已知的QAction. 已知前缀: {list(action_map.keys())}")
    
    def show_set_modes_window(self):
        """显示模式设置窗口"""
        from ui.handlers.SetModesHandler import SetModesHandler
        
        def post_init(window):
            """窗口创建后的初始化回调"""
            # 可以在这里添加一些初始化逻辑
            pass
            
        # 使用通用窗口创建方法
        return self.main_window._create_window_in_tab(
            title="模式设置",
            window_attr="set_modes_window",
            window_class=SetModesHandler,
            action_attr="set_modes_action",
            post_init_callback=post_init,
            parent=None,  # 确保窗口可以独立使用
            registers=self.main_window.register_manager.register_objects
        )
    
    def show_clkin_control_window(self):
        """显示时钟输入控制窗口"""
        from ui.handlers.ClkinControlHandler import ClkinControlHandler
        
        def post_init(window):
            """窗口创建后的初始化回调"""
            # 可以在这里添加一些初始化逻辑
            pass
            
        # 使用通用窗口创建方法
        return self.main_window._create_window_in_tab(
            title="时钟输入控制",
            window_attr="clkin_control_window",
            window_class=ClkinControlHandler,
            action_attr="clkin_control_action",
            post_init_callback=post_init,
            parent=None,  # 确保窗口可以独立使用
            registers=self.main_window.register_manager.register_objects
        )
    
    def show_pll_control_window(self):
        """显示PLL控制窗口"""
        from ui.handlers.PLLHandler import PLLHandler
        
        def post_init(window):
            """窗口创建后的初始化回调"""
            # 可以在这里添加一些初始化逻辑
            pass
            
        # 使用通用窗口创建方法
        return self.main_window._create_window_in_tab(
            title="PLL1 & PLL2 控制",
            window_attr="pll_control_window",
            window_class=PLLHandler,
            action_attr="pll_control_action",
            post_init_callback=post_init,
            parent=None,  # 确保窗口可以独立使用
            registers=self.main_window.register_manager.register_objects
        )
    
    def show_sync_sysref_window(self):
        """显示同步系统参考窗口"""
        # 传统处理器已移除，使用现代化版本
        from ui.handlers.ModernSyncSysRefHandler import ModernSyncSysRefHandler
        
        def post_init(window):
            """窗口创建后的初始化回调"""
            # 如果时钟输出窗口已存在，连接两者
            if hasattr(self.main_window, 'clk_outputs_window') and self.main_window.clk_outputs_window:
                logger.info("设置时钟输出窗口与同步系统参考窗口的连接")
                self.main_window.clk_outputs_window.set_sync_sysref_handler(window)
            
        # 使用通用窗口创建方法
        return self.main_window._create_window_in_tab(
            title="同步系统参考",
            window_attr="sync_sysref_window",
            window_class=ModernSyncSysRefHandler,
            action_attr="sync_sysref_action",
            post_init_callback=post_init,
            parent=None,  # 确保窗口可以独立使用
            register_manager=self.main_window.register_manager  # 现代化处理器需要RegisterManager
        )
    
    def show_clk_output_window(self):
        """显示时钟输出窗口"""
        from ui.handlers.ClkOutputsHandler import ClkOutputsHandler
        
        def post_init(window):
            """窗口创建后的初始化回调"""
            # 如果同步系统参考窗口已存在，连接两者
            if hasattr(self.main_window, 'sync_sysref_window') and self.main_window.sync_sysref_window:
                logger.info("设置时钟输出窗口与同步系统参考窗口的连接")
                window.set_sync_sysref_handler(self.main_window.sync_sysref_window)
            
        # 使用通用窗口创建方法
        return self.main_window._create_window_in_tab(
            title="时钟输出配置",
            window_attr="clk_output_window",
            window_class=ClkOutputsHandler,
            action_attr="clk_output_action",
            post_init_callback=post_init,
            parent=None,  # 确保窗口可以独立使用
            registers=self.main_window.register_manager.register_objects
        )
    
    def get_tab_count(self):
        """获取当前标签页数量"""
        if hasattr(self.main_window, 'tools_tab_widget'):
            return self.main_window.tools_tab_widget.count()
        return 0
    
    def is_tab_visible(self):
        """检查标签页控件是否可见"""
        if hasattr(self.main_window, 'tools_tab_widget'):
            return self.main_window.tools_tab_widget.isVisible()
        return False
    
    def set_tab_visibility(self, visible):
        """设置标签页控件的可见性"""
        if hasattr(self.main_window, 'tools_tab_widget'):
            self.main_window.tools_tab_widget.setVisible(visible)
