#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
启用拖拽停靠功能的脚本

这个脚本会修改配置，让插件窗口默认以悬浮模式显示，从而支持拖拽停靠功能。
"""

import sys
import os
import json

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from core.services.config.ConfigurationManager import set_config, get_config, config_manager

def enable_drag_dock_feature():
    """启用拖拽停靠功能"""
    print("🔧 正在启用拖拽停靠功能...")
    
    try:
        # 启用强制悬浮模式
        set_config('plugins.force_floating_mode', True)
        print("   ✅ 已启用强制悬浮模式")
        
        # 保存配置到本地配置文件
        config_manager.save_to_file('config/local.json')
        print("   ✅ 配置已保存")
        
        print("\n🎉 拖拽停靠功能已启用！")
        print("\n📋 使用说明：")
        print("   1. 重新启动应用程序")
        print("   2. 打开任意工具窗口（如时钟输入控制、PLL控制等）")
        print("   3. 拖拽窗口标题栏到主窗口底部30%区域")
        print("   4. 看到蓝色高亮提示时释放鼠标")
        print("   5. 窗口将自动停靠到主界面标签页中")
        
        print("\n💡 提示：")
        print("   - 拖拽时会显示蓝色边框提示停靠区域")
        print("   - 停靠后的窗口可以通过标签页切换")
        print("   - 可以通过右键菜单重新分离窗口")
        
    except Exception as e:
        print(f"   ❌ 启用失败: {str(e)}")
        return False
    
    return True

def disable_drag_dock_feature():
    """禁用拖拽停靠功能（恢复默认模式）"""
    print("🔧 正在禁用拖拽停靠功能...")
    
    try:
        # 禁用强制悬浮模式
        set_config('plugins.force_floating_mode', False)
        print("   ✅ 已禁用强制悬浮模式")
        
        # 保存配置到本地配置文件
        config_manager.save_to_file('config/local.json')
        print("   ✅ 配置已保存")
        
        print("\n🎉 已恢复默认模式！")
        print("\n📋 说明：")
        print("   - 插件窗口将直接集成到主界面标签页中")
        print("   - 不再支持拖拽停靠功能")
        print("   - 重新启动应用程序以应用更改")
        
    except Exception as e:
        print(f"   ❌ 禁用失败: {str(e)}")
        return False
    
    return True

def check_current_status():
    """检查当前拖拽停靠功能状态"""
    try:
        force_floating = get_config('plugins.force_floating_mode', False)
        print(f"\n📊 当前状态：")
        print(f"   拖拽停靠功能: {'✅ 已启用' if force_floating else '❌ 已禁用'}")
        print(f"   插件显示模式: {'悬浮窗口' if force_floating else '标签页集成'}")
        return force_floating
    except Exception as e:
        print(f"   ❌ 检查状态失败: {str(e)}")
        return None

def main():
    """主函数"""
    print("🚀 拖拽停靠功能配置工具")
    print("=" * 50)
    
    # 检查当前状态
    current_status = check_current_status()
    
    if current_status is None:
        print("\n❌ 无法检查当前状态，请检查配置文件")
        return
    
    print("\n🔧 可用操作：")
    print("   1. 启用拖拽停靠功能")
    print("   2. 禁用拖拽停靠功能（恢复默认）")
    print("   3. 退出")
    
    while True:
        try:
            choice = input("\n请选择操作 (1-3): ").strip()
            
            if choice == '1':
                if current_status:
                    print("\n⚠️  拖拽停靠功能已经启用")
                else:
                    enable_drag_dock_feature()
                break
            elif choice == '2':
                if not current_status:
                    print("\n⚠️  拖拽停靠功能已经禁用")
                else:
                    disable_drag_dock_feature()
                break
            elif choice == '3':
                print("\n👋 再见！")
                break
            else:
                print("\n❌ 无效选择，请输入 1、2 或 3")
        except KeyboardInterrupt:
            print("\n\n👋 再见！")
            break
        except Exception as e:
            print(f"\n❌ 操作失败: {str(e)}")

if __name__ == "__main__":
    main()