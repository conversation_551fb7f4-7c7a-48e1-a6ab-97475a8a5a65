#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
时钟输出功能测试
测试时钟输出相关的所有功能
"""

import sys
import os
import unittest

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from test_suite.test_utils import TestLogger, TestResult, qt_application, MockRegisterManager
from test_suite.test_config import get_test_config

class TestClkOutputs(unittest.TestCase):
    """时钟输出测试类"""
    
    def setUp(self):
        """测试设置"""
        self.logger = TestLogger("clk_outputs_test")
        self.register_manager = MockRegisterManager()
        self.test_results = []
    
    def tearDown(self):
        """测试清理"""
        pass
    
    def test_modern_clk_outputs_handler_creation(self):
        """测试现代化时钟输出处理器创建"""
        result = TestResult("modern_clk_outputs_handler_creation")
        
        try:
            with qt_application():
                from ui.handlers.ModernClkOutputsHandler import ModernClkOutputsHandler
                
                # 创建处理器
                handler = ModernClkOutputsHandler(None, self.register_manager)
                
                # 验证创建成功
                self.assertIsNotNone(handler)
                self.assertEqual(type(handler).__name__, 'ModernClkOutputsHandler')
                
                # 验证UI对象
                self.assertTrue(hasattr(handler, 'ui'))
                
                # 验证控件映射
                self.assertTrue(hasattr(handler, 'widget_register_map'))
                self.assertGreater(len(handler.widget_register_map), 0)
                
                result.add_detail('handler_type', type(handler).__name__)
                result.add_detail('widget_count', len(handler.widget_register_map))
                result.set_success(True)
                
        except Exception as e:
            result.set_error(str(e))
            self.logger.error(f"现代化时钟输出处理器创建失败: {e}")
        
        self.test_results.append(result)
        self.assertTrue(result.success)
    
    def test_output_status_operations(self):
        """测试输出状态操作"""
        result = TestResult("output_status_operations")
        
        try:
            with qt_application():
                from ui.handlers.ModernClkOutputsHandler import ModernClkOutputsHandler
                
                handler = ModernClkOutputsHandler(None, self.register_manager)
                
                # 测试获取初始状态
                initial_status = handler.get_current_status()
                self.assertIsInstance(initial_status, dict)
                
                # 验证状态字段
                expected_fields = ["vco_frequency", "srcmux_states", "output_frequencies", "divider_values"]
                for field in expected_fields:
                    self.assertIn(field, initial_status)
                
                result.add_detail('initial_status', initial_status)
                result.set_success(True)
                
        except Exception as e:
            result.set_error(str(e))
            self.logger.error(f"输出状态操作测试失败: {e}")
        
        self.test_results.append(result)
        self.assertTrue(result.success)
    
    def test_frequency_calculation(self):
        """测试频率计算功能"""
        result = TestResult("frequency_calculation")
        
        try:
            with qt_application():
                from ui.handlers.ModernClkOutputsHandler import ModernClkOutputsHandler
                
                handler = ModernClkOutputsHandler(None, self.register_manager)
                
                # 设置VCO频率
                test_vco_freq = 2949.12
                if hasattr(handler.ui, "lineEditFvco"):
                    handler.ui.lineEditFvco.setText(str(test_vco_freq))
                
                # 执行频率计算
                handler.calculate_output_frequencies()
                
                # 验证计算完成
                status = handler.get_current_status()
                output_freqs = status.get("output_frequencies", {})
                
                # 检查是否有输出频率
                self.assertGreater(len(output_freqs), 0)
                
                result.add_detail('vco_frequency', test_vco_freq)
                result.add_detail('output_count', len(output_freqs))
                result.set_success(True)
                
        except Exception as e:
            result.set_error(str(e))
            self.logger.error(f"频率计算测试失败: {e}")
        
        self.test_results.append(result)
        self.assertTrue(result.success)
    
    def test_output_preset_functionality(self):
        """测试输出预设功能"""
        result = TestResult("output_preset_functionality")
        
        try:
            with qt_application():
                from ui.handlers.ModernClkOutputsHandler import ModernClkOutputsHandler
                
                handler = ModernClkOutputsHandler(None, self.register_manager)
                
                # 测试默认预设
                handler.set_output_preset("default")
                
                # 验证VCO频率设置
                if hasattr(handler.ui, "lineEditFvco"):
                    vco_freq = handler.ui.lineEditFvco.text()
                    self.assertEqual(vco_freq, "2949.12")
                
                # 测试低频预设
                handler.set_output_preset("low_frequency")
                
                # 验证分频器设置
                divider_status = handler.get_current_status().get("divider_values", {})
                
                result.add_detail('default_preset_test', 'passed')
                result.add_detail('low_frequency_preset_test', 'passed')
                result.add_detail('divider_status', divider_status)
                result.set_success(True)
                
        except Exception as e:
            result.set_error(str(e))
            self.logger.error(f"输出预设功能测试失败: {e}")
        
        self.test_results.append(result)
        self.assertTrue(result.success)
    
    def test_srcmux_functionality(self):
        """测试SRCMUX功能"""
        result = TestResult("srcmux_functionality")
        
        try:
            with qt_application():
                from ui.handlers.ModernClkOutputsHandler import ModernClkOutputsHandler
                
                handler = ModernClkOutputsHandler(None, self.register_manager)
                
                # 测试全部SRCMUX预设
                handler.set_output_preset("all_sysref")
                
                # 验证SRCMUX状态
                srcmux_status = handler.get_current_status().get("srcmux_states", {})
                srcmux_count = sum(1 for state in srcmux_status.values() if state)
                
                # 测试单个SRCMUX切换
                handler.update_srcmux_output(0, 2)  # Qt.Checked = 2
                
                result.add_detail('srcmux_count', srcmux_count)
                result.add_detail('single_srcmux_test', 'passed')
                result.set_success(True)
                
        except Exception as e:
            result.set_error(str(e))
            self.logger.error(f"SRCMUX功能测试失败: {e}")
        
        self.test_results.append(result)
        self.assertTrue(result.success)
    
    def test_output_statistics(self):
        """测试输出统计功能"""
        result = TestResult("output_statistics")
        
        try:
            with qt_application():
                from ui.handlers.ModernClkOutputsHandler import ModernClkOutputsHandler
                
                handler = ModernClkOutputsHandler(None, self.register_manager)
                
                # 获取输出统计
                output_count = handler.get_output_count()
                
                # 验证统计字段
                expected_fields = ["total_outputs", "active_outputs", "srcmux_outputs", "normal_outputs"]
                for field in expected_fields:
                    self.assertIn(field, output_count)
                
                # 验证总输出数
                self.assertEqual(output_count.get("total_outputs"), 14)
                
                result.add_detail('output_statistics', output_count)
                result.set_success(True)
                
        except Exception as e:
            result.set_error(str(e))
            self.logger.error(f"输出统计测试失败: {e}")
        
        self.test_results.append(result)
        self.assertTrue(result.success)
    
    def test_widget_value_changes(self):
        """测试控件值变化处理"""
        result = TestResult("widget_value_changes")
        
        try:
            with qt_application():
                from ui.handlers.ModernClkOutputsHandler import ModernClkOutputsHandler
                
                handler = ModernClkOutputsHandler(None, self.register_manager)
                
                # 测试分频器变化
                if "DCLK0_1DIV" in handler.widget_register_map:
                    handler._on_widget_changed("DCLK0_1DIV", 2)
                    result.add_detail('divider_change', 'success')
                
                # 测试VCO频率变化
                if hasattr(handler.ui, "lineEditFvco"):
                    handler.ui.lineEditFvco.setText("3000.0")
                    handler.calculate_output_frequencies()
                    result.add_detail('vco_frequency_change', 'success')
                
                result.set_success(True)
                
        except Exception as e:
            result.set_error(str(e))
            self.logger.error(f"控件值变化测试失败: {e}")
        
        self.test_results.append(result)
        self.assertTrue(result.success)

def run_tests():
    """运行时钟输出测试"""
    suite = unittest.TestLoader().loadTestsFromTestCase(TestClkOutputs)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
