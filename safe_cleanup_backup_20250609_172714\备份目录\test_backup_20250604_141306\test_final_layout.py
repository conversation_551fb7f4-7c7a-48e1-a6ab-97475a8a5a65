#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终布局测试
验证修复后的布局是否与原来的布局一致
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QLineEdit, QLabel, QComboBox, QPushButton
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt

def test_final_layout():
    """测试最终布局是否正确"""
    app = QApplication(sys.argv)
    
    try:
        # 创建主窗口
        from ui.windows.RegisterMainWindow import RegisterMainWindow
        from core.services.spi.spi_service_impl import SPIServiceImpl
        from core.repositories.register_repository import RegisterRepository
        
        spi_service = SPIServiceImpl()
        spi_service.initialize()
        register_repo = RegisterRepository(spi_service)
        main_window = RegisterMainWindow(register_repo)
        main_window.show()
        
        # 等待界面完全加载
        QTest.qWait(2000)
        
        print("=== 最终布局验证 ===")
        
        # 查找所有控件
        line_edits = main_window.findChildren(QLineEdit)
        labels = main_window.findChildren(QLabel)
        combos = main_window.findChildren(QComboBox)
        buttons = main_window.findChildren(QPushButton)
        
        # 按X坐标排序，验证控件顺序
        controls = []
        
        # 添加COM端口相关控件
        for label in labels:
            if label.text() == "COM端口:":
                controls.append(("COM端口标签", label.x(), label.text()))

        # 查找COM端口下拉框（可能还没有内容）
        for combo in combos:
            # 检查下拉框的位置，应该在COM端口标签附近
            com_label_x = None
            for label in labels:
                if label.text() == "COM端口:":
                    com_label_x = label.x()
                    break

            if com_label_x and abs(combo.x() - com_label_x) < 300:  # 在COM端口标签300像素范围内
                controls.append(("COM端口下拉框", combo.x(), f"下拉框({combo.count()}项)"))
                break
                
        for button in buttons:
            if button.text() == "刷新":
                controls.append(("刷新按钮", button.x(), button.text()))
                break
        
        # 添加地址和值控件
        for label in labels:
            if label.text() == "Address:":
                controls.append(("地址标签", label.x(), label.text()))
                
        for edit in line_edits:
            if edit.placeholderText() == "0x00":
                controls.append(("地址输入框", edit.x(), f"占位符: {edit.placeholderText()}"))
                
        for label in labels:
            if "Value:" in label.text():
                controls.append(("值标签", label.x(), label.text()))
                
        for edit in line_edits:
            if edit.placeholderText() == "0x0000":
                controls.append(("值输入框", edit.x(), f"占位符: {edit.placeholderText()}"))
        
        # 添加搜索控件
        for label in labels:
            if label.text() == "搜索位段:":
                controls.append(("搜索标签", label.x(), label.text()))
                
        for edit in line_edits:
            if "位段名称关键字" in edit.placeholderText():
                controls.append(("搜索输入框", edit.x(), f"占位符: {edit.placeholderText()}, 宽度: {edit.width()}"))
                
        for button in buttons:
            if button.text() == "搜索":
                controls.append(("搜索按钮", button.x(), button.text()))
        
        # 按X坐标排序
        controls.sort(key=lambda x: x[1])
        
        print("\n控件顺序（从左到右）:")
        for i, (name, x, info) in enumerate(controls, 1):
            print(f"{i:2d}. {name:12s} (X={x:3d}) - {info}")
        
        # 验证期望的顺序
        expected_order = [
            "COM端口标签", "COM端口下拉框", "刷新按钮",
            "地址标签", "地址输入框", "值标签", "值输入框",
            "搜索标签", "搜索输入框", "搜索按钮"
        ]
        
        actual_order = [control[0] for control in controls]
        
        print(f"\n期望顺序: {expected_order}")
        print(f"实际顺序: {actual_order}")
        
        # 检查搜索框宽度
        search_width = None
        for control in controls:
            if control[0] == "搜索输入框" and "宽度:" in control[2]:
                width_str = control[2].split("宽度: ")[1]
                search_width = int(width_str)
                break
        
        print(f"\n搜索框宽度: {search_width}像素 (期望: 400像素)")
        
        # 验证结果
        order_correct = actual_order == expected_order
        width_correct = search_width == 400
        
        if order_correct and width_correct:
            print("\n✅ 成功！布局已完全恢复到原来的样子")
            print("   - 控件顺序正确")
            print("   - 搜索框宽度正确")
            return True
        else:
            print("\n❌ 布局还有问题:")
            if not order_correct:
                print("   - 控件顺序不正确")
            if not width_correct:
                print(f"   - 搜索框宽度不正确 (实际: {search_width}, 期望: 400)")
            return False
            
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        app.quit()

if __name__ == "__main__":
    success = test_final_layout()
    sys.exit(0 if success else 1)
