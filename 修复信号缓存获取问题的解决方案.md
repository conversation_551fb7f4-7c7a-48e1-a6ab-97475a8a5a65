# 修复信号缓存获取问题的解决方案

## 问题分析

根据用户提供的日志，发现了以下问题：

### 1. 格式化错误
```
WARNING - 从寄存器获取FBMuxEn值失败: Unknown format code 'X' for object of type 'str'
WARNING - 搜索FB_MUX_EN位字段失败: Unknown format code 'X' for object of type 'str'
```

**原因**: `register_addr`可能是字符串类型，但代码中使用了`{register_addr:02X}`格式符

### 2. SYSREF频率缓存为空
```
INFO - 【SYSREF频率获取】缓存中的SYSREF频率(来自SyncSysrefFreq1): None
WARNING - 【SYSREF频率获取】同步系统参考窗口未打开或不存在
WARNING - 【SYSREF频率获取】❌ 无法获取SYSREF频率，使用默认值: 245.76 MHz
```

**原因**: 
- 同步系统参考窗口没有打开，所以没有计算和缓存SYSREF频率
- PLL窗口只能使用默认值，而不是从缓存获取正确的计算值

## 实施的修复方案

### 1. 修复格式化错误

#### 问题代码：
```python
logger.debug(f"从寄存器0x{register_addr:02X}[{bit_field_name}]获取FBMuxEn值: {bit_value}")
```

#### 修复后：
```python
# 安全的地址格式化
addr_str = f"0x{register_addr:02X}" if isinstance(register_addr, int) else str(register_addr)
logger.debug(f"从寄存器{addr_str}[{bit_field_name}]获取FBMuxEn值: {bit_value}")
```

**改进点**:
- ✅ 检查`register_addr`的类型
- ✅ 只有当它是整数时才使用`X`格式符
- ✅ 否则直接转换为字符串

### 2. 改进SYSREF频率缓存机制

#### 添加主动计算功能：
```python
def _calculate_sysref_frequency_from_pll2pfd(self):
    """从PLL2PFD主动计算SYSREF频率"""
    # 获取PLL2PFD频率
    if hasattr(self.ui, 'PLL2PFDFreq'):
        pll2_pfd_text = self.ui.PLL2PFDFreq.text()
        if pll2_pfd_text and pll2_pfd_text.strip():
            pll2_pfd_freq = float(pll2_pfd_text)
            
            # 根据用户说明：SYSREF频率 = PLL2PFD频率
            # 因为 InternalVCO = PLL2PFD × SYSREF_DIV
            # 而 SYSREF频率 = InternalVCO / SYSREF_DIV = PLL2PFD
            sysref_freq = pll2_pfd_freq
            
            return sysref_freq
    return 0.0
```

#### 改进缓存获取逻辑：
```python
def _get_sysref_frequency(self):
    """获取SYSREF频率（优先从缓存获取，然后主动计算）"""
    # 1. 首先尝试从缓存获取
    cached_freq = bus.get_cached_sysref_freq()
    if cached_freq is not None:
        return cached_freq
    
    # 2. 如果缓存为空，主动计算并缓存 ✅ 新增
    calculated_freq = self._calculate_sysref_frequency_from_pll2pfd()
    if calculated_freq > 0:
        bus.cache_sysref_freq(calculated_freq)
        return calculated_freq
    
    # 3. 尝试从同步系统参考窗口获取
    # ... 原有逻辑
    
    # 4. 使用默认值
    return 245.76
```

## 修复效果

### 1. 消除格式化错误
- ✅ **修复前**: `Unknown format code 'X' for object of type 'str'`
- ✅ **修复后**: 安全的类型检查和格式化

### 2. 改善SYSREF频率获取
- ✅ **修复前**: 只能使用默认值245.76 MHz
- ✅ **修复后**: 能够从PLL2PFD主动计算正确的SYSREF频率

### 3. 完善缓存机制
- ✅ **修复前**: 缓存为空时直接使用默认值
- ✅ **修复后**: 缓存为空时主动计算并缓存

## 正确的数据流

### 修复后的流程：
```
1. PLL窗口打开 → 计算PLL2PFD = 245.76 MHz
2. PLL2Cin需要SYSREF频率 → 检查缓存
3. 缓存为空 → 主动从PLL2PFD计算SYSREF频率
4. 计算: SYSREF频率 = PLL2PFD = 245.76 MHz ✅
5. 缓存SYSREF频率 → 供后续使用
6. PLL2Cin显示正确值 = 245.76 MHz ✅
```

### 关键改进：
- 🎯 **主动计算**: 不再被动等待同步系统参考窗口
- 🔄 **智能缓存**: 计算后立即缓存，避免重复计算
- 📊 **正确逻辑**: SYSREF频率 = PLL2PFD频率（符合用户说明）

## 预期的日志输出

修复后应该看到：
```
【SYSREF频率获取】开始获取SYSREF频率...
【SYSREF频率获取】缓存中没有SYSREF频率值
【主动计算SYSREF】开始从PLL2PFD计算SYSREF频率...
【主动计算SYSREF】PLL2PFD频率: 245.76000 MHz
【主动计算SYSREF】✅ 计算得到SYSREF频率: 245.76000 MHz
【主动计算SYSREF】计算逻辑: SYSREF频率 = PLL2PFD频率 = 245.76000 MHz
【SYSREF频率获取】✅ 主动计算得到SYSREF频率: 245.76000 MHz
RegisterUpdateBus: 已缓存SYSREF频率值(用于PLL2Cin): 245.76000 MHz
【PLL2Cin调试】使用SYSREF频率: 245.76000 MHz
【PLL2Cin调试】PLL2Cin更新: '0.00000' -> '245.76000' MHz
```

## 测试验证

### 测试场景1：PLL窗口单独打开
1. **操作**: 只打开PLL窗口
2. **期望**: 
   - 不再有格式化错误
   - PLL2Cin能显示正确的SYSREF频率
   - 日志显示主动计算过程

### 测试场景2：后续打开同步系统参考窗口
1. **操作**: 先打开PLL窗口，再打开同步系统参考窗口
2. **期望**: 
   - 同步系统参考窗口能获取到缓存的SYSREF频率
   - 两个窗口显示一致的频率值

### 测试场景3：修改SYSREF分频器
1. **操作**: 在同步系统参考窗口修改SYSREF分频器
2. **期望**: 
   - InternalVCO正确计算为PLL2PFD × SYSREF_DIV
   - SYSREF频率重新计算并缓存
   - PLL2Cin实时更新

## 总结

通过实施以下修复：

1. **修复格式化错误**: 安全的类型检查和字符串格式化
2. **添加主动计算**: 从PLL2PFD主动计算SYSREF频率
3. **完善缓存机制**: 计算后立即缓存，避免重复计算

现在系统能够：
- ✅ **消除错误**: 不再有格式化错误和警告
- ✅ **正确计算**: 基于PLL2PFD主动计算SYSREF频率
- ✅ **智能缓存**: 从信号缓存中获取已保存的值
- ✅ **实时同步**: 确保各窗口显示一致的频率值

这样就解决了用户指出的"应该从信号缓存中获取已经保存的值"的问题！
