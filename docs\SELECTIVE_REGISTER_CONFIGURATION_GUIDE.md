# 选择性寄存器插件配置化指南

## 🎯 配置化概述

根据之前讨论的第二步"扩展配置化：将更多硬编码值改为配置驱动"，我们已经成功实现了选择性寄存器插件的全面配置化。

## ✅ 已配置化的内容

### 1. 插件基本信息
- **插件名称**：可通过配置文件修改
- **插件版本**：支持版本管理
- **插件描述**：可自定义描述信息

### 2. 窗口配置
- **窗口标题**：可自定义窗口标题
- **窗口尺寸**：宽度、高度可配置
- **最小尺寸**：最小宽度、高度可配置

### 3. UI文本配置
- **按钮文本**：所有按钮的显示文本
- **标签文本**：所有标签的显示文本
- **消息文本**：提示消息、确认对话框文本
- **标签页标题**：三个标签页的标题
- **分组标题**：各个分组框的标题
- **表格标题**：表格列标题

### 4. 寄存器分组规则
- **分组定义**：支持灵活的分组规则配置
- **地址匹配**：支持精确地址和地址范围匹配
- **分组描述**：每个分组的描述信息

### 5. 预设模板
- **模板定义**：预设的寄存器组合
- **模板分类**：按功能分类的模板
- **模板描述**：每个模板的详细描述

### 6. 操作配置
- **确认设置**：读写操作是否需要确认
- **界面行为**：是否自动切换到监控标签页
- **日志格式**：时间戳格式配置

## 📁 配置文件结构

### 主配置文件
```
config/selective_register_plugin.json
```

### 配置管理器
```
plugins/config/selective_register_config.py
```

## 🔧 配置文件详解

### 1. 插件信息配置
```json
{
  "plugin_info": {
    "name": "选择性寄存器操作",
    "version": "1.0.1",
    "description": "允许用户选择特定的寄存器进行批量读写操作，支持分组选择和模板管理"
  }
}
```

### 2. 窗口配置
```json
{
  "ui_config": {
    "window": {
      "title": "选择性寄存器操作",
      "width": 900,
      "height": 700,
      "min_width": 600,
      "min_height": 500
    }
  }
}
```

### 3. 文本配置
```json
{
  "ui_config": {
    "texts": {
      "buttons": {
        "select_all": "全选",
        "select_none": "全不选",
        "read_selected": "读取选中寄存器",
        "write_selected": "写入选中寄存器"
      },
      "labels": {
        "selection_count": "已选择: {count} 个寄存器"
      },
      "messages": {
        "confirm_read": "确定要读取选中的 {count} 个寄存器吗？"
      }
    }
  }
}
```

### 4. 寄存器分组配置
```json
{
  "register_groups": {
    "group_definitions": [
      {
        "name": "PLL控制",
        "description": "PLL相关控制寄存器",
        "rules": [
          {
            "type": "exact",
            "addresses": ["0x63", "0x65", "0x67"]
          }
        ]
      },
      {
        "name": "时钟输出0-1",
        "description": "时钟输出通道0和1",
        "rules": [
          {
            "type": "range",
            "start": "0x10",
            "end": "0x17"
          }
        ]
      }
    ]
  }
}
```

### 5. 模板配置
```json
{
  "templates": {
    "preset_templates": [
      {
        "name": "PLL控制寄存器",
        "description": "PLL核心配置寄存器",
        "addresses": ["0x50", "0x63", "0x65", "0x67"],
        "category": "PLL"
      }
    ]
  }
}
```

## 🛠️ 配置管理器功能

### SelectiveRegisterConfig 类
- **配置加载**：自动加载JSON配置文件
- **默认配置**：配置文件不存在时使用默认值
- **文本格式化**：支持参数化的文本模板
- **配置更新**：支持运行时配置更新

### RegisterGroupMatcher 类
- **规则匹配**：支持精确地址和地址范围匹配
- **动态分组**：根据配置规则动态创建分组
- **灵活扩展**：易于添加新的匹配规则类型

## 🎨 自定义配置示例

### 1. 修改窗口尺寸
```json
{
  "ui_config": {
    "window": {
      "width": 1200,
      "height": 800,
      "min_width": 800,
      "min_height": 600
    }
  }
}
```

### 2. 自定义按钮文本
```json
{
  "ui_config": {
    "texts": {
      "buttons": {
        "read_selected": "批量读取",
        "write_selected": "批量写入",
        "select_all": "选择全部",
        "select_none": "清除选择"
      }
    }
  }
}
```

### 3. 添加新的寄存器分组
```json
{
  "register_groups": {
    "group_definitions": [
      {
        "name": "自定义分组",
        "description": "用户自定义的寄存器分组",
        "rules": [
          {
            "type": "exact",
            "addresses": ["0x80", "0x81", "0x82"]
          },
          {
            "type": "range",
            "start": "0x90",
            "end": "0x9F"
          }
        ]
      }
    ]
  }
}
```

### 4. 创建新的预设模板
```json
{
  "templates": {
    "preset_templates": [
      {
        "name": "调试专用",
        "description": "用于调试的寄存器组合",
        "addresses": ["0x00", "0x02", "0x50", "0x83"],
        "category": "调试"
      }
    ]
  }
}
```

## 🔄 配置热重载

### 重新加载配置
```python
from plugins.config.selective_register_config import reload_config
config = reload_config()
```

### 获取全局配置
```python
from plugins.config.selective_register_config import get_config
config = get_config()
```

## 📊 配置化效果对比

| 配置项 | 配置化前 | 配置化后 |
|--------|----------|----------|
| 插件信息 | 硬编码在类中 | 配置文件管理 |
| 窗口尺寸 | 固定900x700 | 可配置任意尺寸 |
| UI文本 | 硬编码中文 | 支持多语言配置 |
| 分组规则 | 硬编码地址范围 | 灵活的规则引擎 |
| 预设模板 | 固定6个模板 | 可配置任意模板 |
| 操作行为 | 固定行为 | 可配置操作选项 |

## 🎯 配置化优势

### 1. 灵活性提升
- **易于定制**：无需修改代码即可调整界面和行为
- **多环境支持**：不同环境可使用不同配置
- **用户友好**：用户可根据需要自定义界面

### 2. 维护性改善
- **集中管理**：所有配置集中在JSON文件中
- **版本控制**：配置文件可独立进行版本管理
- **错误隔离**：配置错误不影响代码逻辑

### 3. 扩展性增强
- **新功能添加**：添加新功能时只需更新配置
- **规则扩展**：分组规则可轻松扩展新类型
- **模板管理**：模板可动态添加和修改

## 🚀 未来扩展

### 1. 配置界面
- 图形化配置编辑器
- 实时配置预览
- 配置验证和错误提示

### 2. 高级功能
- 配置文件加密
- 远程配置管理
- 配置变更历史

### 3. 多语言支持
- 国际化文本配置
- 动态语言切换
- 本地化适配

---

**实现状态**：✅ 完成  
**测试状态**：✅ 配置加载测试通过  
**兼容性**：✅ 向后兼容，配置文件缺失时使用默认值  
**文档状态**：✅ 配置指南已完成  

**总结**：成功实现了选择性寄存器插件的全面配置化，将硬编码值转换为配置驱动，大幅提升了插件的灵活性、可维护性和可扩展性。
