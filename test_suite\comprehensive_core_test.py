#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
FSJ04832寄存器配置工具 - 核心功能综合测试
测试寄存器管理、PLL控制、时钟输出、同步参考等核心业务功能
"""

import sys
import os
import unittest
import json
from unittest.mock import Mock, patch, MagicMock
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from PyQt5.QtTest import QTest

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from test_config import TestConfig
from test_utils import TestUtils

class CoreFunctionalityTest(unittest.TestCase):
    """核心功能测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        cls.app = TestUtils.get_test_app()
        cls.test_config = TestConfig()
        
    def setUp(self):
        """每个测试方法前的初始化"""
        self.main_window = None
        
    def tearDown(self):
        """每个测试方法后的清理"""
        if self.main_window:
            self.main_window.close()
            self.main_window = None
            
    def test_01_register_manager_initialization(self):
        """测试寄存器管理器初始化"""
        print("\n=== 测试寄存器管理器初始化 ===")
        
        try:
            from core.services.register.RegisterManager import RegisterManager
            
            # 模拟寄存器配置
            mock_registers = {
                "0x00": {"name": "TEST_REG", "value": 0x0000, "bits": {}},
                "0x01": {"name": "TEST_REG2", "value": 0x1234, "bits": {}}
            }
            
            # 创建寄存器管理器
            register_manager = RegisterManager(mock_registers)
            
            # 验证初始化
            self.assertIsNotNone(register_manager)
            self.assertEqual(len(register_manager.register_objects), 2)
            
            # 测试寄存器值获取
            value = register_manager.get_register_value("0x01")
            self.assertEqual(value, 0x1234)
            
            print("✅ 寄存器管理器初始化测试通过")
            
        except Exception as e:
            print(f"❌ 寄存器管理器初始化测试失败: {str(e)}")
            raise
            
    def test_02_register_operations(self):
        """测试寄存器基本操作"""
        print("\n=== 测试寄存器基本操作 ===")
        
        try:
            from core.services.register.RegisterManager import RegisterManager
            
            # 创建测试寄存器
            mock_registers = {
                "0x10": {
                    "name": "PLL_CTRL", 
                    "value": 0x0000,
                    "bits": {
                        "PLL_EN": {"position": [0, 0], "default": 0},
                        "PLL_DIV": {"position": [7, 4], "default": 1}
                    }
                }
            }
            
            register_manager = RegisterManager(mock_registers)
            
            # 测试设置寄存器值
            register_manager.set_register_value("0x10", 0x00F1)
            value = register_manager.get_register_value("0x10")
            self.assertEqual(value, 0x00F1)
            
            # 测试位字段操作
            register_manager.set_bit_field_value("0x10", "PLL_EN", 1)
            bit_value = register_manager.get_bit_field_value("0x10", "PLL_EN")
            self.assertEqual(bit_value, 1)
            
            print("✅ 寄存器基本操作测试通过")
            
        except Exception as e:
            print(f"❌ 寄存器基本操作测试失败: {str(e)}")
            raise
            
    def test_03_event_bus_communication(self):
        """测试事件总线通信"""
        print("\n=== 测试事件总线通信 ===")
        
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            
            # 获取事件总线实例
            bus = RegisterUpdateBus.instance()
            self.assertIsNotNone(bus)
            
            # 测试信号发送
            received_signals = []
            
            def signal_handler(addr, value):
                received_signals.append((addr, value))
                
            bus.register_updated.connect(signal_handler)
            
            # 发送测试信号
            bus.emit_register_updated("0x20", 0x1234)
            
            # 处理事件循环
            QTest.qWait(100)
            
            # 验证信号接收
            self.assertEqual(len(received_signals), 1)
            self.assertEqual(received_signals[0], ("0x20", 0x1234))
            
            print("✅ 事件总线通信测试通过")
            
        except Exception as e:
            print(f"❌ 事件总线通信测试失败: {str(e)}")
            raise
            
    def test_04_pll_frequency_calculation(self):
        """测试PLL频率计算功能"""
        print("\n=== 测试PLL频率计算功能 ===")
        
        try:
            # 模拟PLL计算逻辑
            # 基于项目中的PLL计算公式
            
            # 测试参数
            osc_freq = 122.88  # MHz
            pll1_n_divider = 50
            pll1_r_divider = 1
            
            # PLL1 PFD频率计算: OSCin / PLL1_R_Divider
            pll1_pfd_freq = osc_freq / pll1_r_divider
            self.assertAlmostEqual(pll1_pfd_freq, 122.88, places=2)
            
            # PLL1 VCO频率计算: PLL1_PFD * PLL1_N_Divider
            pll1_vco_freq = pll1_pfd_freq * pll1_n_divider
            self.assertAlmostEqual(pll1_vco_freq, 6144.0, places=1)
            
            print(f"✅ PLL频率计算测试通过")
            print(f"   PLL1 PFD频率: {pll1_pfd_freq:.2f} MHz")
            print(f"   PLL1 VCO频率: {pll1_vco_freq:.1f} MHz")
            
        except Exception as e:
            print(f"❌ PLL频率计算测试失败: {str(e)}")
            raise
            
    def test_05_clock_output_calculation(self):
        """测试时钟输出计算功能"""
        print("\n=== 测试时钟输出计算功能 ===")
        
        try:
            # 模拟时钟输出计算
            vco_dist_freq = 6144.0  # MHz
            clk_divider = 50
            
            # 时钟输出频率计算: VCO_Dist_Freq / Divider
            clk_output_freq = vco_dist_freq / clk_divider
            self.assertAlmostEqual(clk_output_freq, 122.88, places=2)
            
            print(f"✅ 时钟输出计算测试通过")
            print(f"   VCO分布频率: {vco_dist_freq} MHz")
            print(f"   分频比: {clk_divider}")
            print(f"   输出频率: {clk_output_freq:.2f} MHz")
            
        except Exception as e:
            print(f"❌ 时钟输出计算测试失败: {str(e)}")
            raise
            
    def test_06_configuration_management(self):
        """测试配置管理功能"""
        print("\n=== 测试配置管理功能 ===")
        
        try:
            # 测试配置文件加载
            config_path = "config/default.json"
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    
                self.assertIsInstance(config, dict)
                print("✅ 默认配置文件加载成功")
                
                # 验证关键配置项
                if 'window' in config:
                    print(f"   窗口配置: {config['window']}")
                if 'plugins' in config:
                    print(f"   插件配置: {config['plugins']}")
                    
            else:
                print("⚠️  默认配置文件不存在，跳过配置测试")
                
        except Exception as e:
            print(f"❌ 配置管理测试失败: {str(e)}")
            raise
            
    def test_07_plugin_system_basic(self):
        """测试插件系统基本功能"""
        print("\n=== 测试插件系统基本功能 ===")
        
        try:
            from core.services.plugin.PluginManager import PluginManager
            
            # 创建插件管理器
            plugin_manager = PluginManager()
            self.assertIsNotNone(plugin_manager)
            
            # 测试插件目录添加
            plugin_manager.add_plugin_directory("plugins")
            
            # 测试插件扫描
            plugin_manager.scan_plugins()
            
            # 验证插件发现
            plugins = plugin_manager.get_plugins()
            print(f"✅ 插件系统基本功能测试通过")
            print(f"   发现插件数量: {len(plugins)}")
            
            for plugin_name in plugins:
                print(f"   - {plugin_name}")
                
        except Exception as e:
            print(f"❌ 插件系统基本功能测试失败: {str(e)}")
            raise
            
    def test_08_simulation_mode(self):
        """测试模拟模式功能"""
        print("\n=== 测试模拟模式功能 ===")
        
        try:
            # 模拟模拟模式下的寄存器操作
            simulation_enabled = True
            
            if simulation_enabled:
                # 在模拟模式下，所有操作都应该成功但不实际写入硬件
                mock_register_value = 0x1234
                
                # 模拟读操作
                read_result = mock_register_value
                self.assertEqual(read_result, 0x1234)
                
                # 模拟写操作
                write_success = True  # 模拟模式下总是成功
                self.assertTrue(write_success)
                
                print("✅ 模拟模式功能测试通过")
                print("   模拟读操作成功")
                print("   模拟写操作成功")
                
        except Exception as e:
            print(f"❌ 模拟模式功能测试失败: {str(e)}")
            raise

def run_core_functionality_tests():
    """运行核心功能测试"""
    print("🚀 开始FSJ04832寄存器配置工具核心功能测试")
    print("=" * 60)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(CoreFunctionalityTest)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果摘要
    print("\n" + "=" * 60)
    print("📊 核心功能测试结果摘要:")
    print(f"   总测试数: {result.testsRun}")
    print(f"   成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"   失败: {len(result.failures)}")
    print(f"   错误: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"   - {test}: {traceback}")
            
    if result.errors:
        print("\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"   - {test}: {traceback}")
            
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\n🎯 测试成功率: {success_rate:.1f}%")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_core_functionality_tests()
    sys.exit(0 if success else 1)
