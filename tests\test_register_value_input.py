#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
寄存器值输入功能测试脚本
测试R24 Value等寄存器值输入框的智能进制转换功能
"""

import sys
import subprocess
import time
from pathlib import Path

def test_register_value_input():
    """测试寄存器值输入的智能进制转换"""
    print("🔍 测试寄存器值输入的智能进制转换功能")
    print("=" * 60)
    
    # 查找最新的Release版本
    project_root = Path.cwd().parent
    releases_dir = project_root / 'releases'
    latest_dir = releases_dir / 'latest'
    
    if not latest_dir.exists():
        print("❌ 找不到最新发布版本")
        return False
    
    # 查找Release exe文件
    exe_files = list(latest_dir.glob('*_Release.exe'))
    if not exe_files:
        print("❌ 找不到Release版本的exe文件")
        return False
    
    exe_file = exe_files[0]
    print(f"📦 测试文件: {exe_file.name}")
    print(f"📏 文件大小: {exe_file.stat().st_size / 1024 / 1024:.1f} MB")
    print()
    
    print("🔧 寄存器值输入智能转换功能:")
    print("   需求: 支持十进制和十六进制输入")
    print("   • 无前缀 (如: 50) -> 十进制，自动转换为十六进制显示")
    print("   • 0x前缀 (如: 0x32) -> 十六进制，直接显示")
    print("   • 0X前缀 (如: 0X32) -> 十六进制，直接显示")
    print()
    
    print("🚀 启动程序进行验证...")
    print("   程序将启动，请手动验证以下功能:")
    print()
    print("📋 寄存器值输入测试清单:")
    print()
    print("   1. 找到寄存器值输入框:")
    print("      在主界面右侧找到 'R24 Value:' 或类似的输入框")
    print()
    print("   2. 测试十进制输入:")
    print("      • 输入: 50")
    print("      • 预期: 自动转换显示为 0x0032")
    print("      • 输入: 255")
    print("      • 预期: 自动转换显示为 0x00FF")
    print("      • 输入: 1234")
    print("      • 预期: 自动转换显示为 0x04D2")
    print()
    print("   3. 测试十六进制输入:")
    print("      • 输入: 0x32")
    print("      • 预期: 直接显示为 0x0032")
    print("      • 输入: 0XFF")
    print("      • 预期: 直接显示为 0x00FF")
    print("      • 输入: 0x1A2B")
    print("      • 预期: 直接显示为 0x1A2B")
    print()
    print("   4. 测试边界情况:")
    print("      • 输入: 0 (十进制)")
    print("      • 预期: 显示为 0x0000")
    print("      • 输入: 65535 (十进制)")
    print("      • 预期: 显示为 0xFFFF")
    print("      • 输入: 65536 (十进制)")
    print("      • 预期: 显示错误提示")
    print()
    print("   5. 测试错误处理:")
    print("      • 输入: abc (无0x前缀)")
    print("      • 预期: 显示错误提示")
    print("      • 输入: 0xGGG")
    print("      • 预期: 显示错误提示")
    print()
    print("✅ 如果所有测试都通过，说明智能转换功能正常!")
    print("❌ 如果有测试失败，请记录具体现象")
    print()
    
    try:
        # 启动程序
        print(f"启动程序: {exe_file}")
        process = subprocess.Popen([str(exe_file)], 
                                 cwd=exe_file.parent,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)
        
        print("程序已启动，请进行寄存器值输入测试...")
        print("测试完成后，关闭程序窗口即可")
        print()
        print("💡 测试提示:")
        print("   - 找到寄存器值输入框（通常在右侧）")
        print("   - 按照测试清单逐项测试")
        print("   - 观察输入后的显示变化")
        print("   - 注意错误提示的显示")
        
        # 等待程序结束
        process.wait()
        
        print()
        print("程序已关闭")
        
        if process.returncode == 0:
            print("✅ 程序正常退出")
            return True
        else:
            print(f"⚠️  程序退出码: {process.returncode}")
            stderr_output = process.stderr.read().decode('utf-8', errors='ignore')
            if stderr_output:
                print(f"错误输出: {stderr_output}")
            return False
            
    except Exception as e:
        print(f"❌ 启动程序失败: {e}")
        return False

def create_register_input_test_report():
    """创建寄存器输入测试报告模板"""
    report_content = """
# 寄存器值输入智能转换测试报告

## 📋 测试信息
- 测试版本: FSJ04832_RegisterTool_v1.0.3.14_Release.exe
- 测试日期: 2025-07-02
- 功能: 寄存器值输入的智能进制转换

## 🎯 测试项目

### 1. 十进制输入测试
- [ ] 输入 50 -> 显示 0x0032
- [ ] 输入 255 -> 显示 0x00FF
- [ ] 输入 1234 -> 显示 0x04D2
- [ ] 输入 0 -> 显示 0x0000
- [ ] 输入 65535 -> 显示 0xFFFF

### 2. 十六进制输入测试
- [ ] 输入 0x32 -> 显示 0x0032
- [ ] 输入 0XFF -> 显示 0x00FF
- [ ] 输入 0x1A2B -> 显示 0x1A2B
- [ ] 输入 0x0 -> 显示 0x0000

### 3. 边界和错误测试
- [ ] 输入 65536 -> 显示错误提示
- [ ] 输入 abc -> 显示错误提示
- [ ] 输入 0xGGG -> 显示错误提示
- [ ] 输入 -1 -> 显示错误提示

### 4. 用户体验测试
- [ ] 输入过程流畅
- [ ] 错误提示清晰
- [ ] 自动转换及时
- [ ] 显示格式一致

## 📝 测试结果

### 十进制转换
- 50 -> ___
- 255 -> ___
- 1234 -> ___

### 十六进制处理
- 0x32 -> ___
- 0XFF -> ___
- 0x1A2B -> ___

### 错误处理
- 65536 -> ___
- abc -> ___
- 0xGGG -> ___

### 问题描述
- 

### 验证状态
- [ ] 完全正常 - 所有功能按预期工作
- [ ] 基本正常 - 主要功能正常，有小问题
- [ ] 部分正常 - 部分功能正常
- [ ] 不正常 - 主要功能不工作

## 💡 技术细节

### 实现逻辑
1. 检测输入格式（是否有0x/0X前缀）
2. 根据格式选择解析方式（十进制/十六进制）
3. 验证数值范围（0-65535）
4. 统一转换为十六进制显示格式

### 代码位置
- 文件: ui/handlers/ModernRegisterIOHandler.py
- 函数: validate_hex_input(), on_value_changed()

## 📞 技术支持
如有问题，请提供:
1. 具体的输入值和显示结果
2. 错误提示截图
3. 操作步骤描述
"""
    
    report_file = Path(__file__).parent / 'register_input_test_report.md'
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"📝 测试报告模板已创建: {report_file}")

def main():
    """主函数"""
    print("🔧 寄存器值输入智能转换测试工具")
    print("=" * 60)
    
    # 创建测试报告模板
    create_register_input_test_report()
    print()
    
    # 运行测试
    success = test_register_value_input()
    
    print()
    print("=" * 60)
    if success:
        print("🎉 测试启动成功!")
        print()
        print("💡 下一步:")
        print("   1. 请根据测试清单验证寄存器值输入功能")
        print("   2. 填写测试报告: tests/register_input_test_report.md")
        print("   3. 确认智能转换是否按预期工作")
        print("   4. 如果功能正常，客户版本就可以发布了!")
    else:
        print("⚠️  测试启动失败")
        print("   请检查exe文件是否存在和可执行")

if __name__ == '__main__':
    main()
