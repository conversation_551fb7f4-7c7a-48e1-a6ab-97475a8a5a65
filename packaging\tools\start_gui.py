#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
版本管理工具启动器
避免批处理文件编码问题的Python启动脚本
"""

import sys
import os

def main():
    """主函数"""
    print("=" * 60)
    print("FSJ04832 版本管理工具 (字体优化版)")
    print("=" * 60)
    print("字体改进说明:")
    print("- 使用微软雅黑字体，更大更清晰")
    print("- 增强颜色对比度和视觉效果")
    print("- 优化按钮和控件大小")
    print("=" * 60)
    print()
    
    # 检查Python版本
    if sys.version_info < (3, 6):
        print("❌ 错误: 需要Python 3.6或更高版本")
        print(f"   当前版本: {sys.version}")
        input("按回车键退出...")
        return False
    
    # 检查必要的模块
    try:
        import PyQt5
        print("✓ PyQt5 已安装")
    except ImportError:
        print("❌ 错误: 未找到PyQt5模块")
        print("   请运行: pip install PyQt5")
        input("按回车键退出...")
        return False
    
    # 检查版本管理器文件
    version_manager_path = os.path.join(os.path.dirname(__file__), 'version_manager.py')
    if not os.path.exists(version_manager_path):
        print(f"❌ 错误: 未找到版本管理器文件: {version_manager_path}")
        input("按回车键退出...")
        return False
    
    print("✓ 环境检查通过")
    print("🚀 正在启动版本管理工具...")
    print()
    
    try:
        # 添加项目根目录到Python路径
        packaging_root = os.path.dirname(os.path.dirname(__file__))
        project_root = os.path.dirname(packaging_root)
        sys.path.insert(0, project_root)

        # 导入版本管理GUI
        from ui.tools.VersionManagerGUI import main as gui_main

        # 启动GUI
        gui_main()

        print("✓ 程序正常退出")
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断程序")
        return True
        
    except Exception as e:
        print(f"❌ 启动失败: {str(e)}")
        input("按回车键退出...")
        return False

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ 启动器异常: {str(e)}")
        input("按回车键退出...")
        sys.exit(1)
