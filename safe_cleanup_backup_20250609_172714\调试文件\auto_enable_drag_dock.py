#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
自动启用拖拽停靠功能的脚本

这个脚本会自动修改配置，让插件窗口默认以悬浮模式显示，从而支持拖拽停靠功能。
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from core.services.config.ConfigurationManager import set_config, get_config, config_manager

def main():
    """自动启用拖拽停靠功能"""
    print("🚀 自动启用拖拽停靠功能")
    print("=" * 50)
    
    try:
        # 检查当前状态
        current_status = get_config('plugins.force_floating_mode', False)
        print(f"\n📊 当前状态：")
        print(f"   拖拽停靠功能: {'✅ 已启用' if current_status else '❌ 已禁用'}")
        print(f"   插件显示模式: {'悬浮窗口' if current_status else '标签页集成'}")
        
        if current_status:
            print("\n⚠️  拖拽停靠功能已经启用，无需重复操作")
            return
        
        print("\n🔧 正在启用拖拽停靠功能...")
        
        # 启用强制悬浮模式
        set_config('plugins.force_floating_mode', True)
        print("   ✅ 已启用强制悬浮模式")
        
        # 保存配置到本地配置文件
        try:
            config_manager.save_to_file('config/local.json')
            print("   ✅ 配置已保存到 config/local.json")
        except Exception as save_error:
            print(f"   ⚠️  保存配置文件失败: {str(save_error)}")
            print("   ℹ️  配置仍在内存中生效，重启应用后需要重新设置")
        
        print("\n🎉 拖拽停靠功能已成功启用！")
        print("\n📋 使用说明：")
        print("   1. 重新启动应用程序")
        print("   2. 打开任意工具窗口（如时钟输入控制、PLL控制等）")
        print("   3. 拖拽窗口标题栏到主窗口底部30%区域")
        print("   4. 看到蓝色高亮提示时释放鼠标")
        print("   5. 窗口将自动停靠到主界面标签页中")
        
        print("\n💡 提示：")
        print("   - 拖拽时会显示蓝色边框提示停靠区域")
        print("   - 停靠后的窗口可以通过标签页切换")
        print("   - 可以通过右键菜单重新分离窗口")
        
        print("\n🔄 如需恢复默认模式，请运行：")
        print("   python disable_drag_dock.py")
        
    except Exception as e:
        print(f"\n❌ 启用失败: {str(e)}")
        print("\n🔍 可能的解决方案：")
        print("   1. 确保配置文件权限正确")
        print("   2. 检查 config 目录是否存在")
        print("   3. 尝试以管理员权限运行")
        return False
    
    return True

if __name__ == "__main__":
    main()