#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试初始化顺序修复
验证RegisterOperationService能够正确访问spi_service
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_service_initialization_order():
    """测试服务初始化顺序"""
    print("=== 测试服务初始化顺序 ===")
    
    try:
        # 模拟一个简单的主窗口对象
        class MockMainWindow:
            def __init__(self):
                # 模拟spi_service
                self.spi_service = MockSPIService()
                self.register_manager = MockRegisterManager()
                
        class MockSPIService:
            def __init__(self):
                self.connected = False
                
            def get_connection_status(self):
                return {'connected': self.connected, 'mode': 'Simulation'}
                
        class MockRegisterManager:
            def __init__(self):
                self.values = {}
                
            def get_all_register_values(self):
                return self.values
        
        # 创建模拟主窗口
        mock_window = MockMainWindow()
        
        # 测试RegisterOperationService的初始化
        from core.services.register.RegisterOperationService import RegisterOperationService
        
        # 这应该不会抛出AttributeError
        register_service = RegisterOperationService(mock_window)
        
        print("✓ RegisterOperationService 初始化成功")
        print(f"✓ register_service.spi_service 存在: {hasattr(register_service, 'spi_service')}")
        print(f"✓ register_service.register_manager 存在: {hasattr(register_service, 'register_manager')}")
        
        # 测试关键方法是否存在
        methods = ['read_register', 'write_register', 'get_all_registers']
        for method in methods:
            if hasattr(register_service, method):
                print(f"✓ {method} 方法存在")
            else:
                print(f"✗ {method} 方法缺失")
                return False
        
        return True
        
    except AttributeError as e:
        if "'RegisterMainWindow' object has no attribute 'spi_service'" in str(e):
            print("✗ 仍然存在初始化顺序问题")
            print(f"错误: {e}")
            return False
        else:
            print(f"✗ 其他AttributeError: {e}")
            return False
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_configuration_service():
    """测试配置服务"""
    print("\n=== 测试配置服务 ===")
    
    try:
        # 模拟主窗口
        class MockMainWindow:
            def __init__(self):
                self.settings = None
                
            def show_status_message(self, msg, timeout):
                print(f"状态消息: {msg}")
                
        mock_window = MockMainWindow()
        
        from core.services.config.ConfigurationService import ConfigurationService
        config_service = ConfigurationService(mock_window)
        
        print("✓ ConfigurationService 初始化成功")
        
        # 测试关键方法
        methods = ['get_setting', 'set_setting', 'toggle_preload']
        for method in methods:
            if hasattr(config_service, method):
                print(f"✓ {method} 方法存在")
            else:
                print(f"✗ {method} 方法缺失")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ 配置服务测试失败: {e}")
        return False

def test_window_service():
    """测试窗口服务"""
    print("\n=== 测试窗口服务 ===")
    
    try:
        # 模拟主窗口
        class MockMainWindow:
            def __init__(self):
                self.loading_progress = MockProgress()
                
        class MockProgress:
            def setVisible(self, visible):
                pass
                
        mock_window = MockMainWindow()
        
        from core.services.ui.WindowManagementService import WindowManagementService
        window_service = WindowManagementService(mock_window)
        
        print("✓ WindowManagementService 初始化成功")
        
        # 测试关键方法
        methods = ['create_window_in_tab', 'close_all_windows', 'get_window_count']
        for method in methods:
            if hasattr(window_service, method):
                print(f"✓ {method} 方法存在")
            else:
                print(f"✗ {method} 方法缺失")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ 窗口服务测试失败: {e}")
        return False

def test_import_order():
    """测试导入顺序"""
    print("\n=== 测试导入顺序 ===")
    
    try:
        # 按照主窗口中的顺序导入
        from core.services.config.ConfigurationService import ConfigurationService
        print("✓ ConfigurationService 导入成功")
        
        from core.services.ui.WindowManagementService import WindowManagementService
        print("✓ WindowManagementService 导入成功")
        
        from core.services.register.RegisterOperationService import RegisterOperationService
        print("✓ RegisterOperationService 导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试初始化顺序修复...")
    print("=" * 50)
    
    tests = [
        ("导入顺序", test_import_order),
        ("配置服务", test_configuration_service),
        ("窗口服务", test_window_service),
        ("服务初始化顺序", test_service_initialization_order)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} 测试出错: {e}")
            results.append((test_name, False))
    
    # 输出结果
    print("\n" + "=" * 50)
    print("=== 测试结果 ===")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("\n🎉 初始化顺序修复成功！")
        print("RegisterOperationService 现在可以正确访问 spi_service")
        return True
    else:
        print(f"\n⚠️ 有 {total - passed} 项测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
