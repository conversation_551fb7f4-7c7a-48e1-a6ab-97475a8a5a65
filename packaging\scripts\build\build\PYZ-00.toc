('E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\packaging\\scripts\\build\\build\\PYZ-00.pyz',
 [('PyQt5',
   'd:\\program files\\python38\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('__future__', 'd:\\program files\\python38\\lib\\__future__.py', 'PYMODULE'),
  ('_compat_pickle',
   'd:\\program files\\python38\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'd:\\program files\\python38\\lib\\_compression.py',
   'PYMODULE'),
  ('_py_abc', 'd:\\program files\\python38\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'd:\\program files\\python38\\lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_strptime', 'd:\\program files\\python38\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'd:\\program files\\python38\\lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'd:\\program files\\python38\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'd:\\program files\\python38\\lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'd:\\program files\\python38\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'd:\\program files\\python38\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'd:\\program files\\python38\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'd:\\program files\\python38\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'd:\\program files\\python38\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'd:\\program files\\python38\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'd:\\program files\\python38\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'd:\\program files\\python38\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'd:\\program files\\python38\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'd:\\program files\\python38\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'd:\\program files\\python38\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'd:\\program files\\python38\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'd:\\program files\\python38\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'd:\\program files\\python38\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'd:\\program files\\python38\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'd:\\program files\\python38\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'd:\\program files\\python38\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'd:\\program files\\python38\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'd:\\program files\\python38\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'd:\\program files\\python38\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'd:\\program files\\python38\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'd:\\program files\\python38\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'd:\\program files\\python38\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.transports',
   'd:\\program files\\python38\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'd:\\program files\\python38\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'd:\\program files\\python38\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'd:\\program files\\python38\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'd:\\program files\\python38\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64', 'd:\\program files\\python38\\lib\\base64.py', 'PYMODULE'),
  ('bdb', 'd:\\program files\\python38\\lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'd:\\program files\\python38\\lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'd:\\program files\\python38\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'd:\\program files\\python38\\lib\\calendar.py', 'PYMODULE'),
  ('cmd', 'd:\\program files\\python38\\lib\\cmd.py', 'PYMODULE'),
  ('code', 'd:\\program files\\python38\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'd:\\program files\\python38\\lib\\codeop.py', 'PYMODULE'),
  ('concurrent',
   'd:\\program files\\python38\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'd:\\program files\\python38\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'd:\\program files\\python38\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'd:\\program files\\python38\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'd:\\program files\\python38\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'd:\\program files\\python38\\lib\\configparser.py',
   'PYMODULE'),
  ('contextlib', 'd:\\program files\\python38\\lib\\contextlib.py', 'PYMODULE'),
  ('contextvars',
   'd:\\program files\\python38\\lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'd:\\program files\\python38\\lib\\copy.py', 'PYMODULE'),
  ('core', '-', 'PYMODULE'),
  ('core.event_bus', '-', 'PYMODULE'),
  ('core.event_bus.RegisterUpdateBus',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\event_bus\\RegisterUpdateBus.py',
   'PYMODULE'),
  ('core.models', '-', 'PYMODULE'),
  ('core.models.RegisterModel',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\models\\RegisterModel.py',
   'PYMODULE'),
  ('core.services',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\__init__.py',
   'PYMODULE'),
  ('core.services.BatchOperationState',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\BatchOperationState.py',
   'PYMODULE'),
  ('core.services.DIContainer',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\DIContainer.py',
   'PYMODULE'),
  ('core.services.config',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\config\\__init__.py',
   'PYMODULE'),
  ('core.services.config.ConfigurationManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\config\\ConfigurationManager.py',
   'PYMODULE'),
  ('core.services.config.ConfigurationService',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\config\\ConfigurationService.py',
   'PYMODULE'),
  ('core.services.plugin', '-', 'PYMODULE'),
  ('core.services.plugin.MenuClickFixer',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\plugin\\MenuClickFixer.py',
   'PYMODULE'),
  ('core.services.plugin.PluginIntegrationService',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\plugin\\PluginIntegrationService.py',
   'PYMODULE'),
  ('core.services.plugin.PluginManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\plugin\\PluginManager.py',
   'PYMODULE'),
  ('core.services.plugin.dock', '-', 'PYMODULE'),
  ('core.services.plugin.dock.PluginDockService',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\plugin\\dock\\PluginDockService.py',
   'PYMODULE'),
  ('core.services.plugin.menu', '-', 'PYMODULE'),
  ('core.services.plugin.menu.PluginMenuService',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\plugin\\menu\\PluginMenuService.py',
   'PYMODULE'),
  ('core.services.plugin.window', '-', 'PYMODULE'),
  ('core.services.plugin.window.PluginWindowService',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\plugin\\window\\PluginWindowService.py',
   'PYMODULE'),
  ('core.services.register',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\register\\__init__.py',
   'PYMODULE'),
  ('core.services.register.RegisterManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\register\\RegisterManager.py',
   'PYMODULE'),
  ('core.services.register.RegisterOperationService',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\register\\RegisterOperationService.py',
   'PYMODULE'),
  ('core.services.spi', '-', 'PYMODULE'),
  ('core.services.spi.port_manager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\spi\\port_manager.py',
   'PYMODULE'),
  ('core.services.spi.spiPrivacy',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\spi\\spiPrivacy.py',
   'PYMODULE'),
  ('core.services.spi.spi_interface',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\spi\\spi_interface.py',
   'PYMODULE'),
  ('core.services.spi.spi_service',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\spi\\spi_service.py',
   'PYMODULE'),
  ('core.services.spi.spi_service_impl',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\spi\\spi_service_impl.py',
   'PYMODULE'),
  ('core.services.ui',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\ui\\__init__.py',
   'PYMODULE'),
  ('core.services.ui.WindowManagementService',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\ui\\WindowManagementService.py',
   'PYMODULE'),
  ('core.services.version', '-', 'PYMODULE'),
  ('core.services.version.VersionService',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\version\\VersionService.py',
   'PYMODULE'),
  ('csv', 'd:\\program files\\python38\\lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'd:\\program files\\python38\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'd:\\program files\\python38\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'd:\\program files\\python38\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'd:\\program files\\python38\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'd:\\program files\\python38\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'd:\\program files\\python38\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'd:\\program files\\python38\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'd:\\program files\\python38\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'd:\\program files\\python38\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('datetime', 'd:\\program files\\python38\\lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'd:\\program files\\python38\\lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'd:\\program files\\python38\\lib\\difflib.py', 'PYMODULE'),
  ('dis', 'd:\\program files\\python38\\lib\\dis.py', 'PYMODULE'),
  ('doctest', 'd:\\program files\\python38\\lib\\doctest.py', 'PYMODULE'),
  ('email', 'd:\\program files\\python38\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'd:\\program files\\python38\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'd:\\program files\\python38\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'd:\\program files\\python38\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'd:\\program files\\python38\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'd:\\program files\\python38\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'd:\\program files\\python38\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'd:\\program files\\python38\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'd:\\program files\\python38\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'd:\\program files\\python38\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'd:\\program files\\python38\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'd:\\program files\\python38\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'd:\\program files\\python38\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'd:\\program files\\python38\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'd:\\program files\\python38\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'd:\\program files\\python38\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'd:\\program files\\python38\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'd:\\program files\\python38\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'd:\\program files\\python38\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'd:\\program files\\python38\\lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch', 'd:\\program files\\python38\\lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'd:\\program files\\python38\\lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'd:\\program files\\python38\\lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'd:\\program files\\python38\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'd:\\program files\\python38\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'd:\\program files\\python38\\lib\\gettext.py', 'PYMODULE'),
  ('glob', 'd:\\program files\\python38\\lib\\glob.py', 'PYMODULE'),
  ('gzip', 'd:\\program files\\python38\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'd:\\program files\\python38\\lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'd:\\program files\\python38\\lib\\hmac.py', 'PYMODULE'),
  ('html', 'd:\\program files\\python38\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'd:\\program files\\python38\\lib\\html\\entities.py',
   'PYMODULE'),
  ('http', 'd:\\program files\\python38\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client',
   'd:\\program files\\python38\\lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'd:\\program files\\python38\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server',
   'd:\\program files\\python38\\lib\\http\\server.py',
   'PYMODULE'),
  ('importlib',
   'd:\\program files\\python38\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'd:\\program files\\python38\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'd:\\program files\\python38\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'd:\\program files\\python38\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'd:\\program files\\python38\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'd:\\program files\\python38\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.util',
   'd:\\program files\\python38\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'd:\\program files\\python38\\lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'd:\\program files\\python38\\lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'd:\\program files\\python38\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'd:\\program files\\python38\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'd:\\program files\\python38\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'd:\\program files\\python38\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'd:\\program files\\python38\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('logging.handlers',
   'd:\\program files\\python38\\lib\\logging\\handlers.py',
   'PYMODULE'),
  ('lzma', 'd:\\program files\\python38\\lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'd:\\program files\\python38\\lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'd:\\program files\\python38\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'd:\\program files\\python38\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'd:\\program files\\python38\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'd:\\program files\\python38\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'd:\\program files\\python38\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'd:\\program files\\python38\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'd:\\program files\\python38\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'd:\\program files\\python38\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'd:\\program files\\python38\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'd:\\program files\\python38\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'd:\\program files\\python38\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'd:\\program files\\python38\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'd:\\program files\\python38\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'd:\\program files\\python38\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'd:\\program files\\python38\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'd:\\program files\\python38\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'd:\\program files\\python38\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'd:\\program files\\python38\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'd:\\program files\\python38\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'd:\\program files\\python38\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'd:\\program files\\python38\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'd:\\program files\\python38\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'd:\\program files\\python38\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'd:\\program files\\python38\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'd:\\program files\\python38\\lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'd:\\program files\\python38\\lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'd:\\program files\\python38\\lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'd:\\program files\\python38\\lib\\optparse.py', 'PYMODULE'),
  ('pathlib', 'd:\\program files\\python38\\lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'd:\\program files\\python38\\lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'd:\\program files\\python38\\lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'd:\\program files\\python38\\lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'd:\\program files\\python38\\lib\\platform.py', 'PYMODULE'),
  ('plugins', '-', 'PYMODULE'),
  ('plugins.clk_output_plugin',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\clk_output_plugin.py',
   'PYMODULE'),
  ('plugins.clkin_control_plugin',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\clkin_control_plugin.py',
   'PYMODULE'),
  ('plugins.config', '-', 'PYMODULE'),
  ('plugins.config.selective_register_config',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\config\\selective_register_config.py',
   'PYMODULE'),
  ('plugins.data_analysis_plugin',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\data_analysis_plugin.py',
   'PYMODULE'),
  ('plugins.example_tool_plugin',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\example_tool_plugin.py',
   'PYMODULE'),
  ('plugins.performance_monitor_plugin',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\performance_monitor_plugin.py',
   'PYMODULE'),
  ('plugins.pll_control_plugin',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\pll_control_plugin.py',
   'PYMODULE'),
  ('plugins.selective_register_plugin',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\selective_register_plugin.py',
   'PYMODULE'),
  ('plugins.set_modes_plugin',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\set_modes_plugin.py',
   'PYMODULE'),
  ('plugins.sync_sysref_plugin',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\sync_sysref_plugin.py',
   'PYMODULE'),
  ('pprint', 'd:\\program files\\python38\\lib\\pprint.py', 'PYMODULE'),
  ('psutil',
   'd:\\program files\\python38\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'd:\\program files\\python38\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'd:\\program files\\python38\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile', 'd:\\program files\\python38\\lib\\py_compile.py', 'PYMODULE'),
  ('pydoc', 'd:\\program files\\python38\\lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'd:\\program files\\python38\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'd:\\program files\\python38\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('queue', 'd:\\program files\\python38\\lib\\queue.py', 'PYMODULE'),
  ('quopri', 'd:\\program files\\python38\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'd:\\program files\\python38\\lib\\random.py', 'PYMODULE'),
  ('runpy', 'd:\\program files\\python38\\lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'd:\\program files\\python38\\lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'd:\\program files\\python38\\lib\\selectors.py', 'PYMODULE'),
  ('serial',
   'd:\\program files\\python38\\lib\\site-packages\\serial\\__init__.py',
   'PYMODULE'),
  ('serial.serialcli',
   'd:\\program files\\python38\\lib\\site-packages\\serial\\serialcli.py',
   'PYMODULE'),
  ('serial.serialjava',
   'd:\\program files\\python38\\lib\\site-packages\\serial\\serialjava.py',
   'PYMODULE'),
  ('serial.serialposix',
   'd:\\program files\\python38\\lib\\site-packages\\serial\\serialposix.py',
   'PYMODULE'),
  ('serial.serialutil',
   'd:\\program files\\python38\\lib\\site-packages\\serial\\serialutil.py',
   'PYMODULE'),
  ('serial.serialwin32',
   'd:\\program files\\python38\\lib\\site-packages\\serial\\serialwin32.py',
   'PYMODULE'),
  ('serial.tools',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\serial\\tools\\__init__.py',
   'PYMODULE'),
  ('serial.tools.list_ports',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\serial\\tools\\list_ports.py',
   'PYMODULE'),
  ('serial.tools.list_ports_common',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\serial\\tools\\list_ports_common.py',
   'PYMODULE'),
  ('serial.tools.list_ports_linux',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\serial\\tools\\list_ports_linux.py',
   'PYMODULE'),
  ('serial.tools.list_ports_osx',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\serial\\tools\\list_ports_osx.py',
   'PYMODULE'),
  ('serial.tools.list_ports_posix',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\serial\\tools\\list_ports_posix.py',
   'PYMODULE'),
  ('serial.tools.list_ports_windows',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\serial\\tools\\list_ports_windows.py',
   'PYMODULE'),
  ('serial.win32',
   'd:\\program files\\python38\\lib\\site-packages\\serial\\win32.py',
   'PYMODULE'),
  ('shlex', 'd:\\program files\\python38\\lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'd:\\program files\\python38\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'd:\\program files\\python38\\lib\\signal.py', 'PYMODULE'),
  ('smtplib', 'd:\\program files\\python38\\lib\\smtplib.py', 'PYMODULE'),
  ('socket', 'd:\\program files\\python38\\lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'd:\\program files\\python38\\lib\\socketserver.py',
   'PYMODULE'),
  ('ssl', 'd:\\program files\\python38\\lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'd:\\program files\\python38\\lib\\statistics.py', 'PYMODULE'),
  ('string', 'd:\\program files\\python38\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'd:\\program files\\python38\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'd:\\program files\\python38\\lib\\subprocess.py', 'PYMODULE'),
  ('tarfile', 'd:\\program files\\python38\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'd:\\program files\\python38\\lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'd:\\program files\\python38\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'd:\\program files\\python38\\lib\\threading.py', 'PYMODULE'),
  ('token', 'd:\\program files\\python38\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'd:\\program files\\python38\\lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc',
   'd:\\program files\\python38\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty', 'd:\\program files\\python38\\lib\\tty.py', 'PYMODULE'),
  ('typing', 'd:\\program files\\python38\\lib\\typing.py', 'PYMODULE'),
  ('ui', '-', 'PYMODULE'),
  ('ui.components',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\components\\__init__.py',
   'PYMODULE'),
  ('ui.components.MainWindowUI',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\components\\MainWindowUI.py',
   'PYMODULE'),
  ('ui.components.MenuManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\components\\MenuManager.py',
   'PYMODULE'),
  ('ui.coordinators', '-', 'PYMODULE'),
  ('ui.coordinators.EventCoordinator',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\coordinators\\EventCoordinator.py',
   'PYMODULE'),
  ('ui.coordinators.SPIOperationCoordinator',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\coordinators\\SPIOperationCoordinator.py',
   'PYMODULE'),
  ('ui.factories',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\factories\\__init__.py',
   'PYMODULE'),
  ('ui.factories.ModernToolWindowFactory',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\factories\\ModernToolWindowFactory.py',
   'PYMODULE'),
  ('ui.factories.ToolWindowFactory',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\factories\\ToolWindowFactory.py',
   'PYMODULE'),
  ('ui.forms', '-', 'PYMODULE'),
  ('ui.forms.Ui_ClkOutputs',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\Ui_ClkOutputs.py',
   'PYMODULE'),
  ('ui.forms.Ui_PLL1_2',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\Ui_PLL1_2.py',
   'PYMODULE'),
  ('ui.forms.Ui_clkinControl',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\Ui_clkinControl.py',
   'PYMODULE'),
  ('ui.forms.Ui_setModes',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\Ui_setModes.py',
   'PYMODULE'),
  ('ui.forms.Ui_syncSysref',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\Ui_syncSysref.py',
   'PYMODULE'),
  ('ui.handlers',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\__init__.py',
   'PYMODULE'),
  ('ui.handlers.BaseHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\BaseHandler.py',
   'PYMODULE'),
  ('ui.handlers.ClkinControlHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\ClkinControlHandler.py',
   'PYMODULE'),
  ('ui.handlers.ModernBaseHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\ModernBaseHandler.py',
   'PYMODULE'),
  ('ui.handlers.ModernClkOutputsHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\ModernClkOutputsHandler.py',
   'PYMODULE'),
  ('ui.handlers.ModernClkinControlHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\ModernClkinControlHandler.py',
   'PYMODULE'),
  ('ui.handlers.ModernPLLHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\ModernPLLHandler.py',
   'PYMODULE'),
  ('ui.handlers.ModernRegisterIOHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\ModernRegisterIOHandler.py',
   'PYMODULE'),
  ('ui.handlers.ModernRegisterTableHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\ModernRegisterTableHandler.py',
   'PYMODULE'),
  ('ui.handlers.ModernRegisterTreeHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\ModernRegisterTreeHandler.py',
   'PYMODULE'),
  ('ui.handlers.ModernSetModesHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\ModernSetModesHandler.py',
   'PYMODULE'),
  ('ui.handlers.ModernSyncSysRefHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\ModernSyncSysRefHandler.py',
   'PYMODULE'),
  ('ui.handlers.ModernUIEventHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\ModernUIEventHandler.py',
   'PYMODULE'),
  ('ui.handlers.RegisterIOHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\RegisterIOHandler.py',
   'PYMODULE'),
  ('ui.handlers.RegisterTableHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\RegisterTableHandler.py',
   'PYMODULE'),
  ('ui.handlers.RegisterTreeHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\RegisterTreeHandler.py',
   'PYMODULE'),
  ('ui.handlers.SetModesHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\SetModesHandler.py',
   'PYMODULE'),
  ('ui.handlers.UIEventHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\UIEventHandler.py',
   'PYMODULE'),
  ('ui.managers', '-', 'PYMODULE'),
  ('ui.managers.ApplicationLifecycleManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\managers\\ApplicationLifecycleManager.py',
   'PYMODULE'),
  ('ui.managers.BatchOperationManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\managers\\BatchOperationManager.py',
   'PYMODULE'),
  ('ui.managers.GlobalEventManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\managers\\GlobalEventManager.py',
   'PYMODULE'),
  ('ui.managers.InitializationManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\managers\\InitializationManager.py',
   'PYMODULE'),
  ('ui.managers.RegisterDisplayManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\managers\\RegisterDisplayManager.py',
   'PYMODULE'),
  ('ui.managers.RegisterOperationManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\managers\\RegisterOperationManager.py',
   'PYMODULE'),
  ('ui.managers.ResourceAndUtilityManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\managers\\ResourceAndUtilityManager.py',
   'PYMODULE'),
  ('ui.managers.SPISignalManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\managers\\SPISignalManager.py',
   'PYMODULE'),
  ('ui.managers.StatusAndConfigManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\managers\\StatusAndConfigManager.py',
   'PYMODULE'),
  ('ui.managers.TabWindowManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\managers\\TabWindowManager.py',
   'PYMODULE'),
  ('ui.managers.ToolWindowManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\managers\\ToolWindowManager.py',
   'PYMODULE'),
  ('ui.managers.UIUtilityManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\managers\\UIUtilityManager.py',
   'PYMODULE'),
  ('ui.processors',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\processors\\__init__.py',
   'PYMODULE'),
  ('ui.processors.RegisterUpdateProcessor',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\processors\\RegisterUpdateProcessor.py',
   'PYMODULE'),
  ('ui.resources', '-', 'PYMODULE'),
  ('ui.resources.PLL1_2_rc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\resources\\PLL1_2_rc.py',
   'PYMODULE'),
  ('ui.resources.clkinControl_rc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\resources\\clkinControl_rc.py',
   'PYMODULE'),
  ('ui.resources.clkoutput_rc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\resources\\clkoutput_rc.py',
   'PYMODULE'),
  ('ui.resources.setModes_rc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\resources\\setModes_rc.py',
   'PYMODULE'),
  ('ui.resources.syncSysref_rc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\resources\\syncSysref_rc.py',
   'PYMODULE'),
  ('ui.styles', '-', 'PYMODULE'),
  ('ui.styles.ProgressBarStyleManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\styles\\ProgressBarStyleManager.py',
   'PYMODULE'),
  ('ui.tools', '-', 'PYMODULE'),
  ('ui.tools.PluginManagerGUI',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\tools\\PluginManagerGUI.py',
   'PYMODULE'),
  ('ui.windows', '-', 'PYMODULE'),
  ('ui.windows.RegisterMainWindow',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\windows\\RegisterMainWindow.py',
   'PYMODULE'),
  ('unittest',
   'd:\\program files\\python38\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'd:\\program files\\python38\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'd:\\program files\\python38\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'd:\\program files\\python38\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'd:\\program files\\python38\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.result',
   'd:\\program files\\python38\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'd:\\program files\\python38\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'd:\\program files\\python38\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'd:\\program files\\python38\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'd:\\program files\\python38\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'd:\\program files\\python38\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'd:\\program files\\python38\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'd:\\program files\\python38\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'd:\\program files\\python38\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'd:\\program files\\python38\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('utils', '-', 'PYMODULE'),
  ('utils.Log',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\utils\\Log.py',
   'PYMODULE'),
  ('utils.address_utils',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\utils\\address_utils.py',
   'PYMODULE'),
  ('utils.configFileHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\utils\\configFileHandler.py',
   'PYMODULE'),
  ('utils.error_handler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\utils\\error_handler.py',
   'PYMODULE'),
  ('utils.message_strings',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\utils\\message_strings.py',
   'PYMODULE'),
  ('uu', 'd:\\program files\\python38\\lib\\uu.py', 'PYMODULE'),
  ('uuid', 'd:\\program files\\python38\\lib\\uuid.py', 'PYMODULE'),
  ('webbrowser', 'd:\\program files\\python38\\lib\\webbrowser.py', 'PYMODULE'),
  ('xml', 'd:\\program files\\python38\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.parsers',
   'd:\\program files\\python38\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'd:\\program files\\python38\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'd:\\program files\\python38\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'd:\\program files\\python38\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'd:\\program files\\python38\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'd:\\program files\\python38\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'd:\\program files\\python38\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'd:\\program files\\python38\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'd:\\program files\\python38\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'd:\\program files\\python38\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile', 'd:\\program files\\python38\\lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'd:\\program files\\python38\\lib\\zipimport.py', 'PYMODULE')])
