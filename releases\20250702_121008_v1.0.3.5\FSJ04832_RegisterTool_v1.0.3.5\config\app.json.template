{"_comment": "应用级配置模板 - 复制此文件为 app.json 以启用应用级配置", "_description": "此文件包含应用级别的配置，优先级介于 default.json 和 local.json 之间", "_usage": "cp app.json.template app.json", "app": {"name": "FSJ04832 寄存器配置工具", "version": "1.0.0", "environment": "production", "window": {"width": 1840, "height": 1100, "title": "FSJ04832 寄存器配置工具", "resizable": true, "center_on_startup": true}, "features": {"advanced_mode": false, "debug_mode": false, "experimental_features": false}}, "ui": {"theme": "fusion", "language": "zh_CN", "font": {"family": "Microsoft YaHei", "size": 9}, "layout": {"remember_window_state": true, "auto_save_layout": true, "splitter_sizes": [400, 1440], "table_column_widths": [100, 150, 200, 300]}, "behavior": {"confirm_on_exit": true, "auto_refresh_ports": true, "show_tooltips": true}}, "spi": {"timeout": 5000, "retry_count": 3, "batch_size": 50, "auto_scan_ports": true, "preferred_baudrate": 115200, "connection": {"auto_connect": false, "remember_last_port": true, "connection_timeout": 3000}}, "register": {"auto_write": false, "simulation_mode": false, "preload_enabled": false, "preload_count": 5, "default_register": "0x00", "validation": {"check_readonly_bits": true, "warn_on_modification": true}}, "logging": {"level": "INFO", "file_enabled": true, "console_enabled": true, "max_file_size": "10MB", "backup_count": 5, "log_to_file": true, "log_format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"}, "performance": {"async_operations": true, "ui_update_interval": 10, "batch_operation_delay": 50, "batch_timeout_multiplier": 1.5, "memory_optimization": true}, "plugins": {"enabled": true, "directories": ["plugins", "ui/tools"], "auto_load": true, "plugin_timeout": 30, "allow_external_plugins": false}, "security": {"verify_register_access": true, "log_all_operations": true, "backup_on_write": false}}