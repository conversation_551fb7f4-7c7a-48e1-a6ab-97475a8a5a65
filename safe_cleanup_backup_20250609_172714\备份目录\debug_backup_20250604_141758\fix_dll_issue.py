#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复PyInstaller打包后的DLL问题
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def get_python_info():
    """获取Python环境信息"""
    python_dir = Path(sys.executable).parent
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}"
    
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"Python目录: {python_dir}")
    
    return python_dir, python_version

def find_missing_dlls():
    """查找缺失的DLL文件"""
    python_dir, python_version = get_python_info()
    
    # 需要的DLL文件
    required_dlls = [
        f"python{python_version.replace('.', '')}.dll",  # python38.dll
        "vcruntime140.dll",
        "msvcp140.dll",
        "api-ms-win-crt-runtime-l1-1-0.dll",
    ]
    
    found_dlls = []
    missing_dlls = []
    
    # 在Python目录中查找
    for dll_name in required_dlls:
        dll_path = python_dir / dll_name
        if dll_path.exists():
            found_dlls.append(dll_path)
            print(f"✓ 找到: {dll_path}")
        else:
            missing_dlls.append(dll_name)
            print(f"❌ 缺失: {dll_name}")
    
    # 在系统目录中查找缺失的DLL
    system_dirs = [
        Path("C:/Windows/System32"),
        Path("C:/Windows/SysWOW64"),
        python_dir / "DLLs",
    ]
    
    for dll_name in missing_dlls[:]:  # 使用切片复制列表
        for sys_dir in system_dirs:
            dll_path = sys_dir / dll_name
            if dll_path.exists():
                found_dlls.append(dll_path)
                missing_dlls.remove(dll_name)
                print(f"✓ 在系统目录找到: {dll_path}")
                break
    
    return found_dlls, missing_dlls

def copy_dlls_to_dist(found_dlls):
    """复制DLL文件到dist目录"""
    dist_dir = Path("dist/FSJ04832_RegisterTool")
    
    if not dist_dir.exists():
        print(f"错误: dist目录不存在: {dist_dir}")
        return False
    
    print(f"\n复制DLL文件到: {dist_dir}")
    
    for dll_path in found_dlls:
        try:
            dest_path = dist_dir / dll_path.name
            shutil.copy2(dll_path, dest_path)
            print(f"✓ 复制: {dll_path.name}")
        except Exception as e:
            print(f"❌ 复制失败 {dll_path.name}: {str(e)}")
            return False
    
    return True

def create_simple_spec():
    """创建简化的spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

# 获取Python DLL
python_dir = Path(sys.executable).parent
python_dll = python_dir / f"python{sys.version_info.major}{sys.version_info.minor}.dll"

binaries = []
if python_dll.exists():
    binaries.append((str(python_dll), '.'))

# 添加其他必要的DLL
for dll_name in ['vcruntime140.dll', 'msvcp140.dll']:
    dll_path = python_dir / dll_name
    if dll_path.exists():
        binaries.append((str(dll_path), '.'))

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=binaries,
    datas=[
        ('config', 'config'),
        ('images', 'images'),
    ],
    hiddenimports=[
        'ui.handlers.ModernSetModesHandler',
        'ui.handlers.ModernClkinControlHandler',
        'ui.handlers.ModernPLLHandler',
        'ui.handlers.ModernSyncSysRefHandler',
        'ui.handlers.ModernClkOutputsHandler',
        'ui.handlers.ModernRegisterTableHandler',
        'ui.handlers.ModernUIEventHandler',
        'ui.handlers.ModernRegisterIOHandler',
        'ui.handlers.ModernRegisterTreeHandler',
        'ui.handlers.ModernBaseHandler',
        'PyQt5.QtCore',
        'PyQt5.QtWidgets',
        'PyQt5.QtGui',
        'serial',
        'serial.tools.list_ports',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='FSJ04832_RegisterTool',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    console=True,  # 启用控制台以查看错误
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=False,
    upx_exclude=[],
    name='FSJ04832_RegisterTool',
)
'''
    
    with open('simple.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ 创建简化的spec文件: simple.spec")

def build_with_simple_spec():
    """使用简化的spec文件构建"""
    print("\n使用简化的spec文件构建...")
    
    cmd = ['pyinstaller', '--clean', '--noconfirm', 'simple.spec']
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ 构建成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("PyInstaller DLL问题修复工具")
    print("=" * 60)
    
    # 1. 检查Python环境
    python_dir, python_version = get_python_info()
    
    # 2. 查找DLL文件
    print(f"\n查找必要的DLL文件...")
    found_dlls, missing_dlls = find_missing_dlls()
    
    if missing_dlls:
        print(f"\n⚠️  仍有 {len(missing_dlls)} 个DLL文件缺失:")
        for dll in missing_dlls:
            print(f"  - {dll}")
        print("\n建议:")
        print("1. 安装Microsoft Visual C++ Redistributable")
        print("2. 重新安装Python")
        print("3. 使用虚拟环境")
    
    # 3. 创建简化的spec文件
    create_simple_spec()
    
    # 4. 尝试构建
    if build_with_simple_spec():
        # 5. 复制DLL文件到dist目录
        if copy_dlls_to_dist(found_dlls):
            print("\n🎉 DLL修复完成!")
            print("请尝试运行 dist/FSJ04832_RegisterTool/FSJ04832_RegisterTool.exe")
        else:
            print("\n❌ DLL复制失败")
    else:
        print("\n❌ 构建失败")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
