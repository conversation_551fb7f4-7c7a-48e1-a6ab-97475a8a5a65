# 中文编码修复文档

## 问题描述

用户报告读取所有寄存器和写入所有寄存器的进度对话框中的中文文字显示为乱码，如下图所示：

![乱码对话框](../images/garbled_dialog.png)

对话框应该显示"正在读取所有寄存器..."，但实际显示为乱码字符。

## 问题分析

经过分析，问题的根本原因是：

1. **缺少字体配置** - 应用程序没有正确配置支持中文的字体
2. **编码设置不完整** - Qt应用程序没有设置正确的UTF-8编码和中文本地化
3. **进度对话框字体继承** - 进度对话框没有继承主应用程序的字体设置

## 解决方案

### 1. 主应用程序中文支持设置

在 `main.py` 中添加了 `setup_chinese_support()` 函数：

```python
def setup_chinese_support(app):
    """设置中文支持"""
    try:
        # 设置UTF-8编码
        if hasattr(QTextCodec, 'setCodecForLocale'):
            utf8_codec = QTextCodec.codecForName("UTF-8")
            if utf8_codec:
                QTextCodec.setCodecForLocale(utf8_codec)
        
        # 设置中文本地化
        QLocale.setDefault(QLocale(QLocale.Chinese, QLocale.China))
        
        # 设置支持中文的字体
        font = QFont()
        
        # 根据操作系统选择合适的中文字体
        if sys.platform.startswith('win'):
            # Windows系统
            font.setFamily("Microsoft YaHei")  # 微软雅黑
            if not font.exactMatch():
                font.setFamily("SimHei")  # 黑体
                if not font.exactMatch():
                    font.setFamily("SimSun")  # 宋体
        elif sys.platform.startswith('darwin'):
            # macOS系统
            font.setFamily("PingFang SC")
            if not font.exactMatch():
                font.setFamily("Hiragino Sans GB")
        else:
            # Linux系统
            font.setFamily("WenQuanYi Micro Hei")
            if not font.exactMatch():
                font.setFamily("Noto Sans CJK SC")
                if not font.exactMatch():
                    font.setFamily("DejaVu Sans")
        
        font.setPointSize(9)
        app.setFont(font)
        
        logger.info(f"中文支持设置完成，使用字体: {font.family()}")
        
    except Exception as e:
        logger.warning(f"设置中文支持时出现警告: {str(e)}")
```

### 2. 进度对话框字体设置

修改了 `ui/controllers/BatchOperationController.py` 和 `ui/managers/BatchOperationManager.py` 中的 `_create_progress_dialog()` 方法：

```python
def _create_progress_dialog(self, title, label, total):
    """创建进度对话框"""
    # 确保文本使用正确的编码
    title_text = str(title) if title else "进度"
    label_text = str(label) if label else "正在处理..."
    cancel_text = "取消"
    
    self.progress_dialog = QProgressDialog(label_text, cancel_text, 0, total, self.main_window)
    self.progress_dialog.setWindowTitle(title_text)
    self.progress_dialog.setWindowModality(Qt.WindowModal)
    self.progress_dialog.setMinimumDuration(500)
    self.progress_dialog.setValue(0)
    
    # 确保对话框使用正确的字体
    if hasattr(self.main_window, 'font') and self.main_window.font():
        self.progress_dialog.setFont(self.main_window.font())
    
    self.progress_dialog.show()
```

### 3. 批量操作控制器初始化

在 `ui/managers/InitializationManager.py` 中添加了批量操作控制器的创建：

```python
# 创建批量操作控制器
from ui.controllers.BatchOperationController import BatchOperationController
self.main_window.batch_operation_controller = BatchOperationController(self.main_window)
```

## 修改的文件

1. **main.py** - 添加中文支持设置
2. **ui/controllers/BatchOperationController.py** - 修改进度对话框创建方法
3. **ui/managers/BatchOperationManager.py** - 修改进度对话框创建方法
4. **ui/managers/InitializationManager.py** - 添加批量操作控制器初始化

## 测试验证

创建了两个测试脚本来验证修复效果：

### 1. 独立测试脚本 (`test_chinese_dialog.py`)
- 测试基本的中文字体和编码设置
- 验证进度对话框的中文显示

### 2. 集成测试脚本 (`test_suite/ui/test_chinese_encoding_fix.py`)
- 测试完整应用程序中的中文对话框显示
- 验证批量读取和写入对话框的中文文本

## 测试结果

所有测试均通过：

```
✅ 读取对话框中文显示正常
✅ 写入对话框中文显示正常  
✅ 使用了支持中文的字体 (Microsoft YaHei/SimSun)
```

## 支持的字体

修复方案支持多种中文字体，按优先级排序：

### Windows系统
1. Microsoft YaHei (微软雅黑) - 首选
2. SimHei (黑体) - 备选
3. SimSun (宋体) - 最后备选

### macOS系统
1. PingFang SC - 首选
2. Hiragino Sans GB - 备选

### Linux系统
1. WenQuanYi Micro Hei - 首选
2. Noto Sans CJK SC - 备选
3. DejaVu Sans - 最后备选

## 注意事项

1. 修复会自动检测操作系统并选择最合适的中文字体
2. 如果首选字体不可用，会自动回退到备选字体
3. 所有新创建的进度对话框都会自动继承主应用程序的字体设置
4. 修复向后兼容，不会影响现有功能

## 后续维护

如果将来遇到其他UI组件的中文显示问题，可以参考此修复方案：

1. 确保组件继承主应用程序的字体设置
2. 使用 `str()` 函数确保文本编码正确
3. 在组件创建时显式设置字体（如果需要）

## 相关文件

- `main.py` - 主应用程序入口和中文支持设置
- `ui/controllers/BatchOperationController.py` - 批量操作控制器
- `ui/managers/BatchOperationManager.py` - 批量操作管理器
- `ui/managers/InitializationManager.py` - 初始化管理器
- `test_chinese_dialog.py` - 独立测试脚本
- `test_suite/ui/test_chinese_encoding_fix.py` - 集成测试脚本
