#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试工具函数
提供测试过程中需要的通用工具和辅助函数
"""

import os
import sys
import time
import json
import logging
import traceback
from datetime import datetime
from contextlib import contextmanager

# 导入测试配置
from test_config import PROJECT_ROOT, TEST_CONFIG, MOCK_REGISTER_CONFIG

class TestLogger:
    """测试日志记录器"""
    
    def __init__(self, name="test", level=logging.DEBUG):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(level)
        
        # 创建日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # 文件处理器
        log_dir = TEST_CONFIG['test_data']['test_logs']
        log_file = os.path.join(log_dir, f"{name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
    
    def info(self, message):
        self.logger.info(message)
    
    def debug(self, message):
        self.logger.debug(message)
    
    def warning(self, message):
        self.logger.warning(message)
    
    def error(self, message):
        self.logger.error(message)
    
    def exception(self, message):
        self.logger.exception(message)

class TestResult:
    """测试结果记录器"""
    
    def __init__(self, test_name):
        self.test_name = test_name
        self.start_time = time.time()
        self.end_time = None
        self.success = False
        self.error_message = None
        self.details = {}
        self.performance_data = {}
    
    def set_success(self, success=True):
        self.success = success
        self.end_time = time.time()
    
    def set_error(self, error_message):
        self.success = False
        self.error_message = error_message
        self.end_time = time.time()
    
    def add_detail(self, key, value):
        self.details[key] = value
    
    def add_performance_data(self, key, value):
        self.performance_data[key] = value
    
    def get_duration(self):
        if self.end_time:
            return self.end_time - self.start_time
        return time.time() - self.start_time
    
    def to_dict(self):
        return {
            'test_name': self.test_name,
            'success': self.success,
            'duration': self.get_duration(),
            'error_message': self.error_message,
            'details': self.details,
            'performance_data': self.performance_data,
            'start_time': self.start_time,
            'end_time': self.end_time
        }

class MockSPIService:
    """模拟SPI服务"""
    
    def __init__(self):
        self.registers = {}
        self.read_count = 0
        self.write_count = 0
        self.is_initialized = False
    
    def initialize(self):
        self.is_initialized = True
        return True
    
    def read_register(self, address):
        self.read_count += 1
        # 返回模拟值或之前写入的值
        return self.registers.get(address, 0x0000)
    
    def write_register(self, address, value):
        self.write_count += 1
        self.registers[address] = value
        return True
    
    def get_statistics(self):
        return {
            'read_count': self.read_count,
            'write_count': self.write_count,
            'register_count': len(self.registers)
        }

class MockRegisterManager:
    """模拟寄存器管理器"""
    
    def __init__(self, config=None):
        self.config = config or MOCK_REGISTER_CONFIG
        self.register_objects = {}
        self.values = {}
        self._create_register_objects()
    
    def _create_register_objects(self):
        """创建寄存器对象"""
        for addr, reg_config in self.config.items():
            self.register_objects[addr] = {
                'name': reg_config['name'],
                'description': reg_config['description'],
                'bit_fields': reg_config['bit_fields']
            }
            self.values[addr] = 0x0000
    
    def get_register_value(self, address):
        return self.values.get(address, 0x0000)
    
    def set_register_value(self, address, value):
        self.values[address] = value
    
    def get_bit_field_value(self, address, field_name):
        reg_value = self.get_register_value(address)
        if address in self.config:
            bit_fields = self.config[address]['bit_fields']
            if field_name in bit_fields:
                bit_range = bit_fields[field_name]['bit_range']
                start_bit, end_bit = bit_range
                mask = ((1 << (end_bit - start_bit + 1)) - 1) << start_bit
                return (reg_value & mask) >> start_bit
        return 0
    
    def set_bit_field_value(self, address, field_name, value):
        if address in self.config:
            bit_fields = self.config[address]['bit_fields']
            if field_name in bit_fields:
                bit_range = bit_fields[field_name]['bit_range']
                start_bit, end_bit = bit_range
                mask = ((1 << (end_bit - start_bit + 1)) - 1) << start_bit
                reg_value = self.get_register_value(address)
                reg_value = (reg_value & ~mask) | ((value << start_bit) & mask)
                self.set_register_value(address, reg_value)
    
    def get_all_registers(self):
        return list(self.config.keys())

@contextmanager
def qt_application():
    """Qt应用程序上下文管理器"""
    from PyQt5.QtWidgets import QApplication
    import sys
    
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
        created_app = True
    else:
        created_app = False
    
    try:
        yield app
    finally:
        if created_app:
            app.quit()

@contextmanager
def performance_monitor(test_result, operation_name):
    """性能监控上下文管理器"""
    start_time = time.time()
    start_memory = get_memory_usage()
    
    try:
        yield
    finally:
        end_time = time.time()
        end_memory = get_memory_usage()
        
        duration = end_time - start_time
        memory_delta = end_memory - start_memory
        
        test_result.add_performance_data(f"{operation_name}_duration", duration)
        test_result.add_performance_data(f"{operation_name}_memory_delta", memory_delta)

def get_memory_usage():
    """获取当前内存使用量（MB）"""
    try:
        import psutil
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024  # 转换为MB
    except ImportError:
        return 0

def wait_for_condition(condition_func, timeout=5.0, interval=0.1):
    """等待条件满足"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        if condition_func():
            return True
        time.sleep(interval)
    return False

def compare_values(expected, actual, tolerance=0.001):
    """比较数值（支持浮点数容差）"""
    if isinstance(expected, (int, float)) and isinstance(actual, (int, float)):
        return abs(expected - actual) <= tolerance
    return expected == actual

def create_mock_main_window():
    """创建模拟主窗口"""
    class MockMainWindow:
        def __init__(self):
            self.auto_write_mode = False
            self.register_service = MockSPIService()
            self.register_manager = MockRegisterManager()
            self.tool_windows = {}
            self.is_closed = False
        
        def close_tool_window(self, window_type):
            if window_type in self.tool_windows:
                del self.tool_windows[window_type]
        
        def add_tool_window(self, window_type, window):
            self.tool_windows[window_type] = window
        
        def close(self):
            self.is_closed = True
    
    return MockMainWindow()

def validate_test_environment():
    """验证测试环境"""
    errors = []
    
    # 检查项目根目录
    if not os.path.exists(PROJECT_ROOT):
        errors.append(f"项目根目录不存在: {PROJECT_ROOT}")
    
    # 检查关键文件
    key_files = [
        'main.py',
        'lib/register.json',
        'ui/windows/RegisterMainWindow.py'
    ]
    
    for file_path in key_files:
        full_path = os.path.join(PROJECT_ROOT, file_path)
        if not os.path.exists(full_path):
            errors.append(f"关键文件不存在: {file_path}")
    
    # 检查Python模块
    required_modules = ['PyQt5', 'json']
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            errors.append(f"缺少必需模块: {module}")
    
    return errors

def generate_test_report(test_results, output_file=None):
    """生成测试报告"""
    if not output_file:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = os.path.join(
            TEST_CONFIG['reporting']['output_dir'],
            f"test_report_{timestamp}.json"
        )

    # 统计信息
    total_tests = len(test_results)
    passed_tests = sum(1 for result in test_results if result.success)
    failed_tests = total_tests - passed_tests
    total_duration = sum(result.get_duration() for result in test_results)

    report = {
        'summary': {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'success_rate': passed_tests / total_tests if total_tests > 0 else 0,
            'total_duration': total_duration,
            'generated_at': datetime.now().isoformat()
        },
        'test_results': [result.to_dict() for result in test_results]
    }

    # 保存报告
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)

    return output_file

class TestUtils:
    """测试工具类"""

    _test_app = None

    @classmethod
    def get_test_app(cls):
        """获取测试用的Qt应用程序实例"""
        if cls._test_app is None:
            from PyQt5.QtWidgets import QApplication
            import sys

            app = QApplication.instance()
            if app is None:
                cls._test_app = QApplication(sys.argv)
            else:
                cls._test_app = app
        return cls._test_app

    @staticmethod
    def create_mock_register_config():
        """创建模拟寄存器配置"""
        return MOCK_REGISTER_CONFIG.copy()

    @staticmethod
    def setup_test_logging():
        """设置测试日志"""
        return TestLogger("test_runner")

    @staticmethod
    def cleanup_test_files():
        """清理测试文件"""
        # 清理临时测试文件
        pass

if __name__ == "__main__":
    # 测试工具函数
    print("测试工具函数验证:")
    
    # 验证测试环境
    errors = validate_test_environment()
    if errors:
        print("环境验证失败:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("✓ 测试环境验证通过")
    
    # 测试模拟组件
    mock_spi = MockSPIService()
    mock_spi.initialize()
    mock_spi.write_register("0x50", 0x1234)
    value = mock_spi.read_register("0x50")
    print(f"✓ 模拟SPI服务测试: 写入0x1234，读取{hex(value)}")
    
    # 测试寄存器管理器
    mock_manager = MockRegisterManager()
    mock_manager.set_bit_field_value("0x50", "PLL1_PD", 1)
    pll1_pd = mock_manager.get_bit_field_value("0x50", "PLL1_PD")
    print(f"✓ 模拟寄存器管理器测试: PLL1_PD = {pll1_pd}")
