#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
插件集成服务 - 重构版本
负责将插件集成到主应用程序中，采用模块化设计
"""

from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from core.services.plugin.PluginManager import plugin_manager, IToolWindowPlugin
from core.services.config.ConfigurationManager import get_config
from utils.Log import get_module_logger

# 导入子服务
from core.services.plugin.menu.PluginMenuService import PluginMenuService
from core.services.plugin.window.PluginWindowService import PluginWindowService
from core.services.plugin.dock.PluginDockService import PluginDockService

logger = get_module_logger(__name__)


class PluginIntegrationService(QObject):
    """插件集成服务 - 重构版本，采用模块化设计"""

    # 信号定义
    plugin_window_opened = pyqtSignal(str, object)  # plugin_name, window
    plugin_window_closed = pyqtSignal(str)  # plugin_name

    def __init__(self, main_window):
        """初始化插件集成服务

        Args:
            main_window: 主窗口实例
        """
        super().__init__()
        self.main_window = main_window

        # 初始化子服务
        self.menu_service = PluginMenuService(main_window)
        self.window_service = PluginWindowService(main_window)
        self.dock_service = PluginDockService(main_window)

        # 连接子服务信号
        self._connect_service_signals()

        # 存储子服务引用到主窗口，供其他组件使用
        main_window._plugin_menu_service = self.menu_service
        main_window._plugin_window_service = self.window_service
        main_window._plugin_dock_service = self.dock_service

    def _connect_service_signals(self):
        """连接子服务的信号"""
        # 连接菜单服务信号
        self.menu_service.plugin_action_triggered.connect(self._on_plugin_action_triggered)

        # 连接窗口服务信号
        self.window_service.plugin_window_opened.connect(self.plugin_window_opened.emit)
        self.window_service.plugin_window_closed.connect(self._on_plugin_window_closed)

        # 连接停靠服务信号
        self.dock_service.window_docked.connect(self._on_window_docked)
        self.dock_service.window_undocked.connect(self._on_window_undocked)

    def initialize_plugins(self):
        """初始化插件系统"""
        try:
            # 检查插件是否启用
            if not get_config('plugins.enabled', True):
                logger.info("插件系统已禁用")
                return

            # 添加插件目录
            plugin_dirs = get_config('plugins.directories', ['plugins'])
            for plugin_dir in plugin_dirs:
                plugin_manager.add_plugin_directory(plugin_dir)

            # 扫描插件
            plugin_manager.scan_plugins()

            # 初始化插件
            plugin_manager.initialize_plugins(self.main_window)

            # 集成工具窗口插件到菜单
            self.menu_service.integrate_tool_window_plugins()

            logger.info("插件系统初始化完成")

        except Exception as e:
            logger.error(f"插件系统初始化失败: {str(e)}")

    def _on_plugin_action_triggered(self, plugin: IToolWindowPlugin, checked: bool):
        """处理插件菜单动作触发"""
        logger.info(f"🎯 插件菜单动作触发: {plugin.name}, checked={checked}")

        try:
            # 使用QTimer.singleShot确保在事件循环中处理，避免阻塞
            if checked:
                QTimer.singleShot(0, lambda: self._show_plugin_window(plugin))
            else:
                QTimer.singleShot(0, lambda: self._hide_plugin_window(plugin))

        except Exception as e:
            logger.error(f"处理插件动作失败 {plugin.name}: {str(e)}")
            # 重置菜单状态
            self.menu_service.update_action_state(plugin.name, False)

    def _show_plugin_window(self, plugin: IToolWindowPlugin):
        """显示插件窗口"""
        try:
            # 检查是否应该集成到标签页
            if self.dock_service.should_integrate_to_tab(plugin.name):
                # 先创建窗口
                self.window_service.show_plugin_window(plugin)
                # 然后集成到标签页
                window = self.window_service.get_plugin_window(plugin.name)
                if window:
                    self.dock_service.integrate_window_to_tab(window, plugin.name)
            else:
                # 直接显示为悬浮窗口
                self.window_service.show_plugin_window(plugin)

        except Exception as e:
            logger.error(f"显示插件窗口失败 {plugin.name}: {str(e)}")
            self.menu_service.update_action_state(plugin.name, False)

    def _hide_plugin_window(self, plugin: IToolWindowPlugin):
        """隐藏插件窗口"""
        try:
            # 如果窗口在标签页中，关闭标签页
            if self.dock_service.should_integrate_to_tab(plugin.name):
                self.dock_service.close_plugin_tab(plugin.name)

            # 隐藏窗口
            self.window_service.hide_plugin_window(plugin)

        except Exception as e:
            logger.error(f"隐藏插件窗口失败 {plugin.name}: {str(e)}")

    def _on_plugin_window_closed(self, plugin_name: str, from_tab_close: bool = False):
        """处理插件窗口关闭

        Args:
            plugin_name: 插件名称
            from_tab_close: 是否来自标签页关闭（避免重复关闭标签页）
        """
        try:
            # 更新菜单状态
            self.menu_service.update_action_state(plugin_name, False)

            # 只有不是来自标签页关闭时才关闭标签页（避免重复关闭）
            if not from_tab_close:
                self.dock_service.close_plugin_tab(plugin_name)

            # 停止拖拽监控（如果存在）
            self.dock_service.stop_drag_monitor(plugin_name)

            # 发送信号
            self.plugin_window_closed.emit(plugin_name)

            logger.info(f"插件窗口关闭处理完成: {plugin_name}, from_tab_close={from_tab_close}")

        except Exception as e:
            logger.error(f"处理插件窗口关闭失败 {plugin_name}: {str(e)}")

    def _on_window_docked(self, plugin_name: str):
        """处理窗口停靠事件"""
        logger.info(f"窗口已停靠: {plugin_name}")

    def _on_window_undocked(self, plugin_name: str):
        """处理窗口分离事件"""
        logger.info(f"窗口已分离: {plugin_name}")

    # 公共接口方法
    def get_plugin_window(self, plugin_name: str):
        """获取插件窗口实例"""
        return self.window_service.get_plugin_window(plugin_name)

    def close_all_plugin_windows(self):
        """关闭所有插件窗口"""
        return self.window_service.close_all_plugin_windows()

    def dock_floating_window(self, plugin_name: str):
        """将悬浮窗口停靠到标签页"""
        return self.dock_service.dock_floating_window(plugin_name)

    def undock_window_from_tab(self, plugin_name: str):
        """从标签页分离窗口"""
        return self.dock_service.undock_window_from_tab(plugin_name)

    def toggle_window_docking(self, plugin_name: str):
        """切换窗口停靠状态"""
        return self.dock_service.toggle_window_docking(plugin_name)

    def cleanup(self):
        """清理资源"""
        try:
            # 清理子服务
            self.menu_service.cleanup()
            self.window_service.cleanup()
            self.dock_service.cleanup()

            logger.info("插件集成服务清理完成")

        except Exception as e:
            logger.error(f"插件集成服务清理失败: {str(e)}")

    # 以下是保留的旧方法，用于向后兼容
    def _integrate_tool_window_plugins(self):
        """将工具窗口插件集成到菜单中 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _integrate_tool_window_plugins，请使用 menu_service.integrate_tool_window_plugins()")
        return self.menu_service.integrate_tool_window_plugins()

    def _show_plugin_manager(self):
        """显示插件管理器 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _show_plugin_manager，请使用 menu_service._show_plugin_manager()")
        return self.menu_service._show_plugin_manager()

    def _add_plugin_to_menu(self, plugin, menu):
        """将插件添加到菜单 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _add_plugin_to_menu，请使用 menu_service._add_plugin_to_menu()")
        return self.menu_service._add_plugin_to_menu(plugin, menu)

    def _add_actions_to_toolbar(self, actions):
        """将动作添加到工具栏 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _add_actions_to_toolbar，请使用 MenuManager._add_actions_to_toolbar()")
        # 这个方法现在在MenuManager中实现
        pass

    def _on_plugin_manager_closed(self):
        """插件管理器关闭处理 - 向后兼容方法"""
        if hasattr(self, 'plugin_manager_window'):
            self.plugin_manager_window = None
        logger.info("插件管理器已关闭")

    # 以下是旧代码的存根，用于向后兼容
    def _cleanup_main_window_references(self, plugin_name: str):
        """清理主窗口中的窗口引用 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _cleanup_main_window_references")
        pass

    def _close_plugin_tab(self, plugin_name: str):
        """关闭插件对应的标签页 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _close_plugin_tab，请使用 dock_service")
        return self.dock_service.close_plugin_tab(plugin_name)

    # 更多向后兼容的方法存根
    def _is_window_in_tab(self, plugin_name: str) -> bool:
        """检查插件窗口是否在标签页中 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _is_window_in_tab，请使用 dock_service")
        return self.dock_service._is_window_already_docked(plugin_name)

    def _is_window_valid(self, window) -> bool:
        """检查窗口是否仍然有效 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _is_window_valid，请使用 window_service")
        return self.window_service._is_window_valid(window)

    def _cleanup_invalid_plugin_window(self, plugin_name: str):
        """清理无效的插件窗口引用 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _cleanup_invalid_plugin_window，请使用 window_service")
        return self.window_service._cleanup_invalid_plugin_window(plugin_name)

    # 更多向后兼容的方法存根（这些方法已移到子服务中）
    def _configure_plugin_window(self, window, plugin_name: str):
        """配置插件窗口 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _configure_plugin_window，请使用 window_service")
        # 这个方法的功能现在在 window_service 中实现

    # 更多向后兼容的方法存根
    def _add_scroll_support_to_window(self, window, plugin_name: str):
        """为插件窗口添加滚动支持 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _add_scroll_support_to_window，请使用 window_service")

    def _should_enable_scroll_for_plugin(self, plugin_name: str) -> bool:
        """判断插件是否需要滚动支持 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _should_enable_scroll_for_plugin，请使用 window_service")
        return False

    def _add_manual_scroll_support(self, window, plugin_name: str):
        """为非现代化处理器手动添加滚动支持 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _add_manual_scroll_support，请使用 window_service")

    def _get_plugin_minimum_size(self, plugin_name: str):
        """获取插件的最小尺寸 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _get_plugin_minimum_size，请使用 window_service")
        return (700, 600)

    # 更多向后兼容的方法存根（这些方法已移到子服务中）
    def _smart_resize_window(self, window, plugin_name: str):
        """智能调整窗口大小 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _smart_resize_window，请使用 window_service")
        # 这个方法的功能现在在 window_service 中实现

    # 更多向后兼容的方法存根
    def _calculate_optimal_window_size(self, window, plugin_name: str):
        """计算窗口的最优大小 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _calculate_optimal_window_size，请使用 window_service")
        return (800, 600)

    def _get_content_size_hint(self, window):
        """获取窗口内容的大小提示 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _get_content_size_hint，请使用 window_service")
        return None

    def _get_plugin_optimal_size(self, plugin_name: str):
        """获取插件的最优大小 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _get_plugin_optimal_size，请使用 window_service")
        return None

    def _adjust_current_size(self, current_size, plugin_name: str):
        """基于当前大小进行智能调整 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _adjust_current_size，请使用 window_service")
        return (800, 600)

    # 更多向后兼容的方法存根
    def _fine_tune_window_size(self, window, plugin_name: str):
        """微调窗口大小 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _fine_tune_window_size，请使用 window_service")

    def _basic_resize_window(self, window, plugin_name: str):
        """基本的窗口大小调整 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _basic_resize_window，请使用 window_service")

    # 更多向后兼容的方法存根
    def _optimize_undocked_window_size(self, window, plugin_name: str):
        """优化分离窗口的大小 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _optimize_undocked_window_size，请使用 dock_service")
        return self.dock_service._optimize_undocked_window_size(window, plugin_name)

    def _fine_tune_undocked_window(self, window, plugin_name: str):
        """微调分离窗口 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _fine_tune_undocked_window，请使用 dock_service")

    # 更多向后兼容的方法存根（这些方法已移到子服务中）
    def _adjust_undocked_window_position(self, window, plugin_name: str):
        """调整分离窗口的位置 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _adjust_undocked_window_position，请使用 dock_service")

    # 更多向后兼容的方法存根（这些方法已移到子服务中）
    def _add_context_menu_to_window(self, window, plugin_name: str):
        """为窗口添加右键菜单 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _add_context_menu_to_window，请使用 window_service")

    def _toggle_window_topmost(self, window, topmost: bool):
        """切换窗口置顶状态 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _toggle_window_topmost，请使用 window_service")

    def _show_drag_dock_help(self, plugin_name: str):
        """显示拖拽停靠帮助信息 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _show_drag_dock_help，请使用 dock_service")

    def _should_integrate_to_tab(self, plugin_name: str) -> bool:
        """判断插件是否应该集成到标签页中 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _should_integrate_to_tab，请使用 dock_service")
        return self.dock_service.should_integrate_to_tab(plugin_name)





    # 更多向后兼容的方法存根（这些方法已移到子服务中）
    def _integrate_window_to_tab(self, window, plugin_name: str):
        """将插件窗口集成到主界面标签页中 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _integrate_window_to_tab，请使用 dock_service")
        return self.dock_service.integrate_window_to_tab(window, plugin_name)

    def _get_plugin_display_name(self, plugin_name: str) -> str:
        """获取插件的显示名称 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _get_plugin_display_name，请使用 dock_service")
        return self.dock_service._get_plugin_display_name(plugin_name)

    # 更多向后兼容的方法存根（这些方法已移到子服务中）
    def _is_window_already_docked(self, plugin_name: str):
        """检查窗口是否已经在标签页中 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _is_window_already_docked，请使用 dock_service")
        return self.dock_service._is_window_already_docked(plugin_name)

    def _cleanup_container_resources(self, container, plugin_name):
        """完全清理容器资源 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _cleanup_container_resources，请使用 dock_service")

    def _cleanup_container_only(self, container, plugin_name):
        """只清理容器资源，不影响窗口 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _cleanup_container_only，请使用 dock_service")

    def _check_and_hide_empty_tab_widget(self):
        """检查并隐藏空的标签页容器 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _check_and_hide_empty_tab_widget，请使用 dock_service")

    # 更多向后兼容的方法存根（这些方法已移到子服务中）
    def _add_drag_dock_support(self, window, plugin_name: str):
        """为插件窗口添加拖拽停靠支持 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _add_drag_dock_support，请使用 dock_service")
        return self.dock_service._add_drag_dock_support(window, plugin_name)

    def _setup_drag_event_handling(self, window, plugin_name):
        """设置拖拽事件处理 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _setup_drag_event_handling，请使用 dock_service")

    # 更多向后兼容的方法存根（这些方法已移到子服务中）
    def _force_enable_mouse_events(self, window, plugin_name):
        """强制启用鼠标事件捕获 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _force_enable_mouse_events，请使用 dock_service")

    def _force_enable_mouse_events_recursive(self, widget):
        """递归为所有子控件强制启用鼠标事件 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _force_enable_mouse_events_recursive，请使用 dock_service")

    def _add_global_mouse_monitor(self, window, plugin_name):
        """添加全局鼠标监控 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _add_global_mouse_monitor，请使用 dock_service")

    def _check_mouse_state(self, window, plugin_name):
        """检查鼠标状态 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _check_mouse_state，请使用 dock_service")

    # 更多向后兼容的方法存根（这些方法已移到子服务中）
    def _simulate_mouse_press_event(self, window, global_pos, plugin_name):
        """模拟鼠标按下事件 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _simulate_mouse_press_event，请使用 dock_service")

    def _stop_global_mouse_monitor(self, window, plugin_name):
        """停止全局鼠标监控 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _stop_global_mouse_monitor，请使用 dock_service")

    # 更多向后兼容的方法存根（这些方法已移到子服务中）
    def _setup_traditional_drag_events(self, window, plugin_name):
        """使用传统的事件重写方法设置拖拽事件 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _setup_traditional_drag_events，请使用 dock_service")

    def _setup_drag_for_child_widgets(self, window, plugin_name):
        """为窗口的子控件设置拖拽事件处理 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _setup_drag_for_child_widgets，请使用 dock_service")

    def _should_skip_widget_for_drag(self, widget):
        """判断是否应该跳过某个控件的拖拽设置 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _should_skip_widget_for_drag，请使用 dock_service")
        return False

    def _setup_child_widget_drag_events(self, child_widget, parent_window, plugin_name):
        """为子控件设置拖拽事件 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _setup_child_widget_drag_events，请使用 dock_service")





    # 更多向后兼容的方法存根（这些方法已移到子服务中）
    def _handle_drag_move(self, window, global_pos, plugin_name):
        """处理拖拽移动事件 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _handle_drag_move，请使用 dock_service")
        return self.dock_service._handle_drag_move(window, global_pos, plugin_name)

    def _handle_drag_release(self, window, global_pos, plugin_name):
        """处理拖拽释放事件 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _handle_drag_release，请使用 dock_service")
        return self.dock_service._handle_drag_release(window, global_pos, plugin_name)

    def _install_event_filter_recursively(self, widget, event_filter):
        """递归地为widget及其所有子控件安装事件过滤器 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _install_event_filter_recursively，请使用 dock_service")

    def _enable_mouse_tracking_recursively(self, widget):
        """递归地为widget及其所有子控件启用鼠标跟踪 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _enable_mouse_tracking_recursively，请使用 dock_service")

    def _check_dock_area(self, window, global_pos):
        """检查拖拽位置并提供视觉反馈 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _check_dock_area，请使用 dock_service")

    def _restore_window_state(self, window):
        """恢复窗口状态 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _restore_window_state，请使用 dock_service")

    # 更多向后兼容的方法存根（这些方法已移到子服务中）
    def _is_in_dock_area(self, window, global_pos):
        """判断鼠标位置是否在停靠区域内 - 向后兼容方法"""
        logger.warning("调用了已废弃的方法 _is_in_dock_area，请使用 dock_service")
        return self.dock_service._is_in_dock_area(window, global_pos)
