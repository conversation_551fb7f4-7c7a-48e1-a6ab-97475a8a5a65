#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
插件系统测试脚本
专门测试安全打包版本中的插件系统和工具窗口功能
"""

import sys
import subprocess
import time
from pathlib import Path

def test_plugin_system():
    """测试插件系统"""
    print("🔍 测试安全打包版本中的插件系统")
    print("=" * 60)
    
    # 查找最新的Release版本
    project_root = Path.cwd().parent
    releases_dir = project_root / 'releases'
    latest_dir = releases_dir / 'latest'
    
    if not latest_dir.exists():
        print("❌ 找不到最新发布版本")
        return False
    
    # 查找Release exe文件
    exe_files = list(latest_dir.glob('*_Release.exe'))
    if not exe_files:
        print("❌ 找不到Release版本的exe文件")
        return False
    
    exe_file = exe_files[0]
    print(f"📦 测试文件: {exe_file.name}")
    print(f"📏 文件大小: {exe_file.stat().st_size / 1024 / 1024:.1f} MB")
    print()
    
    print("🔒 安全特性验证:")
    print(f"   ✅ 单文件可执行程序: {exe_file.name}")
    print(f"   ✅ 无目录结构暴露: 只有一个exe文件")
    print(f"   ✅ 代码完全封装: 客户无法看到内部文件")
    print()
    
    print("🚀 启动程序测试插件系统...")
    print("   程序将启动，请手动验证以下功能:")
    print()
    print("📋 插件系统测试清单:")
    print("   1. 版本号显示: 窗口标题应显示正确版本号")
    print("   2. 工具菜单: 菜单栏 → 工具 → 应包含以下选项:")
    print("      • 模式设置(&M)")
    print("      • 时钟输入控制(&I)")  
    print("      • PLL控制(&P)")
    print("      • 同步系统参考(&S)")
    print("      • 时钟输出(&O)")
    print("   3. 工具窗口: 每个工具窗口都应能正常打开")
    print("   4. 功能完整: 工具窗口内的功能应正常工作")
    print()
    print("✅ 如果所有功能都正常，说明插件系统修复成功!")
    print("❌ 如果工具菜单为空或工具窗口无法打开，请告知具体现象")
    print()
    
    try:
        # 启动程序
        print(f"启动程序: {exe_file}")
        process = subprocess.Popen([str(exe_file)], 
                                 cwd=exe_file.parent,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)
        
        print("程序已启动，请进行插件系统测试...")
        print("测试完成后，关闭程序窗口即可")
        print()
        print("💡 测试提示:")
        print("   - 检查窗口标题是否显示正确版本号")
        print("   - 点击菜单栏的'工具'菜单")
        print("   - 逐一测试每个工具窗口选项")
        print("   - 验证工具窗口是否能正常打开和使用")
        
        # 等待程序结束
        process.wait()
        
        print()
        print("程序已关闭")
        
        if process.returncode == 0:
            print("✅ 程序正常退出")
            return True
        else:
            print(f"⚠️  程序退出码: {process.returncode}")
            stderr_output = process.stderr.read().decode('utf-8', errors='ignore')
            if stderr_output:
                print(f"错误输出: {stderr_output}")
            return False
            
    except Exception as e:
        print(f"❌ 启动程序失败: {e}")
        return False

def create_plugin_test_report():
    """创建插件测试报告模板"""
    report_content = """
# 插件系统测试报告

## 📋 测试环境
- 测试版本: FSJ04832_RegisterTool_v1.0.3.11_Release.exe
- 测试日期: 2025-07-02
- 测试类型: 安全打包版本插件系统测试

## 🎯 测试项目

### 1. 版本号显示
- [ ] 窗口标题显示正确版本号 (v1.0.3.11)
- [ ] 关于对话框显示完整版本信息

### 2. 工具菜单
- [ ] 菜单栏包含"工具"菜单
- [ ] 工具菜单包含以下选项:
  - [ ] 模式设置(&M)
  - [ ] 时钟输入控制(&I)
  - [ ] PLL控制(&P)
  - [ ] 同步系统参考(&S)
  - [ ] 时钟输出(&O)

### 3. 工具窗口功能
- [ ] 模式设置窗口能正常打开
- [ ] 时钟输入控制窗口能正常打开
- [ ] PLL控制窗口能正常打开
- [ ] 同步系统参考窗口能正常打开
- [ ] 时钟输出窗口能正常打开

### 4. 窗口交互
- [ ] 工具窗口可以正常关闭
- [ ] 工具窗口可以重复打开
- [ ] 工具窗口内的控件响应正常
- [ ] 工具窗口与主窗口交互正常

## 📝 测试结果

### 成功项目
- 

### 失败项目
- 

### 问题描述
- 

## 🎯 总体评价
- [ ] 完全成功 - 所有功能正常
- [ ] 基本成功 - 主要功能正常，有小问题
- [ ] 部分成功 - 部分功能正常
- [ ] 失败 - 主要功能不正常

## 💡 改进建议
- 

## 📞 技术支持
如有问题，请提供:
1. 具体的错误现象
2. 操作步骤
3. 错误信息截图
"""
    
    report_file = Path(__file__).parent / 'plugin_test_report.md'
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"📝 测试报告模板已创建: {report_file}")

def main():
    """主函数"""
    print("🔧 插件系统测试工具")
    print("=" * 60)
    
    # 创建测试报告模板
    create_plugin_test_report()
    print()
    
    # 运行测试
    success = test_plugin_system()
    
    print()
    print("=" * 60)
    if success:
        print("🎉 测试启动成功!")
        print()
        print("💡 下一步:")
        print("   1. 请根据测试清单验证所有功能")
        print("   2. 填写测试报告: packaging/tests/plugin_test_report.md")
        print("   3. 如果发现问题，请告诉我具体现象")
        print("   4. 如果一切正常，客户版本就可以发布了!")
    else:
        print("⚠️  测试启动失败")
        print("   请检查exe文件是否存在和可执行")

if __name__ == '__main__':
    main()
