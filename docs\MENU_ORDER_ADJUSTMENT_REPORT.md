# 菜单顺序调整报告

## 🎯 用户需求

用户希望调整工具菜单中的项目顺序：

**原始顺序**：时钟输入(I) → 时钟输出(O) → PLL控制(P) → 模式设置(M) → 同步系统参考(R)

**期望顺序**：模式设置(M) → 时钟输入(I) → 时钟输出(O) → 同步系统参考(R) → PLL控制(P)

## 🔍 问题分析

### 原因分析
1. **插件顺序不确定**：`PluginManager.get_tool_window_plugins()` 返回的插件顺序基于字典 `self._plugins.values()` 的顺序，这个顺序是不确定的
2. **缺乏排序机制**：原有代码没有对核心工具插件进行特定的排序
3. **多处需要修改**：菜单顺序控制分布在 `MenuManager.py` 和 `PluginMenuService.py` 两个文件中

## ✅ 解决方案

### 1. 修改 MenuManager.py
**文件路径**：`ui/components/MenuManager.py`

**修改内容**：
- 定义期望的工具顺序：`['模式设置', '时钟输入控制', '时钟输出', '同步系统参考', 'PLL控制']`
- 创建插件名称到插件对象的映射
- 按指定顺序收集核心工具插件

```python
# 定义期望的工具顺序
desired_order = ['模式设置', '时钟输入控制', '时钟输出', '同步系统参考', 'PLL控制']

# 创建插件名称到插件对象的映射
plugin_map = {plugin.name: plugin for plugin in tool_plugins}

# 按指定顺序收集核心工具插件
core_tool_plugins = []
for tool_name in desired_order:
    if tool_name in plugin_map:
        core_tool_plugins.append(plugin_map[tool_name])
```

### 2. 修改 PluginMenuService.py
**文件路径**：`core/services/plugin/menu/PluginMenuService.py`

**修改内容**：
- 应用相同的排序逻辑，确保一致性
- 更新核心工具插件的收集和排序方式

## 🧪 测试验证

### 测试脚本
创建了 `test_simple_order.py` 来验证排序逻辑：

### 测试结果
```
📊 发现 9 个工具窗口插件

📋 当前插件顺序:
   1. 时钟输入控制
   2. 时钟输出
   3. 数据分析器
   4. 示例工具
   5. 性能监控器
   6. PLL控制
   7. 选择性寄存器操作
   8. 模式设置
   9. 同步系统参考

🎯 期望的顺序: ['模式设置', '时钟输入控制', '时钟输出', '同步系统参考', 'PLL控制']

✅ 重新排序后的插件顺序:
   1. 模式设置 (Ctrl+M)
   2. 时钟输入控制 (Ctrl+I)
   3. 时钟输出 (Ctrl+O)
   4. 同步系统参考 (Ctrl+R)
   5. PLL控制 (Ctrl+P)

🎉 所有期望的插件都已找到并正确排序!
```

## 📊 修改统计

### 修改的文件
1. `ui/components/MenuManager.py` - 核心菜单管理器
2. `core/services/plugin/menu/PluginMenuService.py` - 插件菜单服务

### 修改的方法
1. `_integrate_tool_window_plugins_to_menu()` - MenuManager中的工具插件集成方法
2. `integrate_tool_window_plugins()` - PluginMenuService中的插件集成方法

### 新增的逻辑
- 定义了明确的工具顺序数组
- 实现了基于名称映射的插件排序
- 保持了对其他插件的兼容性

## 🎯 最终效果

### 菜单顺序变更
**修改前**：
```
工具(&T)
├── 模拟通信(&W)
├── 自动写入(&A)
├── ──────────────
├── 时钟输入控制(&I)
├── 时钟输出(&O)
├── PLL控制(&P)
├── 模式设置(&M)
└── 同步系统参考(&R)
```

**修改后**：
```
工具(&T)
├── 模拟通信(&W)
├── 自动写入(&A)
├── ──────────────
├── 模式设置(&M)
├── 时钟输入控制(&I)
├── 时钟输出(&O)
├── 同步系统参考(&R)
└── PLL控制(&P)
```

### 快捷键保持不变
- 模式设置：Ctrl+M
- 时钟输入控制：Ctrl+I
- 时钟输出：Ctrl+O
- 同步系统参考：Ctrl+R
- PLL控制：Ctrl+P

## 🔧 技术细节

### 排序算法
使用了简单而有效的基于映射的排序方法：
1. 创建插件名称到插件对象的映射字典
2. 按照预定义的顺序数组遍历
3. 从映射中提取对应的插件对象
4. 构建有序的插件列表

### 兼容性保证
- 保持了对非核心工具插件的支持
- 不影响现有的插件加载机制
- 向后兼容原有的菜单结构

### 可维护性
- 顺序定义集中在一个数组中，易于修改
- 两个文件使用相同的排序逻辑，保持一致性
- 代码结构清晰，易于理解和维护

## 🎉 总结

成功实现了用户要求的菜单顺序调整：
- ✅ 模式设置移到最前面
- ✅ 同步系统参考移到PLL控制前面
- ✅ 保持了所有功能的正常工作
- ✅ 快捷键和其他特性不受影响
- ✅ 代码结构清晰，易于维护

用户现在可以看到按照期望顺序排列的工具菜单，提升了使用体验。
