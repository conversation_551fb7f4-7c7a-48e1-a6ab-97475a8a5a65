#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
性能监控插件
实时监控系统性能和批量操作性能
"""

import time
import threading
from typing import Dict, List, Optional
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QTextEdit, QProgressBar, QTabWidget,
                            QTableWidget, QTableWidgetItem, QGroupBox, QGridLayout,
                            QMessageBox)
from PyQt5.QtCore import pyqtSignal, QTimer, Qt, QThread
from PyQt5.QtGui import QFont
from core.services.plugin.PluginManager import IToolWindowPlugin
from utils.Log import get_module_logger

logger = get_module_logger(__name__)

# 检查psutil依赖
PSUTIL_AVAILABLE = False
try:
    import psutil
    PSUTIL_AVAILABLE = True
    logger.info("psutil库可用，将提供完整的系统监控功能")
except ImportError:
    logger.warning("psutil库不可用，将使用模拟数据")
    psutil = None


class PerformanceData:
    """性能数据类"""
    
    def __init__(self):
        self.start_time = time.time()
        self.operation_count = 0
        self.total_duration = 0.0
        self.min_duration = float('inf')
        self.max_duration = 0.0
        self.error_count = 0
        self.memory_usage = []
        self.cpu_usage = []
        
    def add_operation(self, duration: float, success: bool = True):
        """添加操作记录"""
        self.operation_count += 1
        self.total_duration += duration
        self.min_duration = min(self.min_duration, duration)
        self.max_duration = max(self.max_duration, duration)
        if not success:
            self.error_count += 1
    
    def get_average_duration(self) -> float:
        """获取平均执行时间"""
        return self.total_duration / self.operation_count if self.operation_count > 0 else 0.0
    
    def get_throughput(self) -> float:
        """获取吞吐量（操作/秒）"""
        elapsed = time.time() - self.start_time
        return self.operation_count / elapsed if elapsed > 0 else 0.0
    
    def get_success_rate(self) -> float:
        """获取成功率"""
        return (self.operation_count - self.error_count) / self.operation_count * 100 if self.operation_count > 0 else 100.0


class SystemMonitorThread(QThread):
    """系统监控线程 - 使用Qt线程确保线程安全"""

    # 定义信号
    data_updated = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.monitoring = False
        self._stop_requested = False

    def start_monitoring(self):
        """开始监控"""
        if not self.isRunning():
            self.monitoring = True
            self._stop_requested = False
            self.start()
            logger.info("系统监控线程已启动")

    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        self._stop_requested = True
        if self.isRunning():
            self.quit()
            # 进一步减少等待时间，提高关闭速度
            if not self.wait(200):  # 只等待200毫秒
                logger.warning("系统监控线程未能在200ms内正常停止，强制终止")
                self.terminate()  # 强制终止线程
                self.wait(50)  # 再等待50ms确保终止
            logger.info("系统监控线程已停止")

    def run(self):
        """线程运行方法"""
        logger.info("系统监控线程开始运行")
        while self.monitoring and not self._stop_requested:
            try:
                # 检查停止请求，提高响应速度
                if self._stop_requested:
                    break

                # 获取系统信息
                memory_info = self._get_memory_info()
                cpu_info = self._get_cpu_info()

                data = {
                    'memory': memory_info,
                    'cpu': cpu_info,
                    'timestamp': time.time()
                }

                # 发送信号到主线程
                if not self._stop_requested:  # 再次检查避免关闭时发送信号
                    self.data_updated.emit(data)

                # 分段休眠，提高停止响应速度
                for _ in range(10):  # 将1秒分成10个100ms段
                    if self._stop_requested:
                        break
                    self.msleep(100)

            except Exception as e:
                if not self._stop_requested:  # 只在非停止状态下记录错误
                    error_msg = f"系统监控错误: {str(e)}"
                    logger.error(error_msg)
                    self.error_occurred.emit(error_msg)
                # 错误时也要分段休眠
                for _ in range(10):
                    if self._stop_requested:
                        break
                    self.msleep(100)

    def _get_memory_info(self) -> Dict:
        """获取内存信息"""
        if not PSUTIL_AVAILABLE:
            return {'total': 8192, 'available': 4096, 'used': 4096, 'percent': 50.0}

        try:
            memory = psutil.virtual_memory()
            return {
                'total': memory.total / 1024 / 1024,  # MB
                'available': memory.available / 1024 / 1024,  # MB
                'used': memory.used / 1024 / 1024,  # MB
                'percent': memory.percent
            }
        except Exception as e:
            logger.warning(f"获取内存信息失败: {e}")
            return {'total': 8192, 'available': 4096, 'used': 4096, 'percent': 50.0}

    def _get_cpu_info(self) -> Dict:
        """获取CPU信息"""
        if not PSUTIL_AVAILABLE:
            return {'percent': 25.0, 'count': 4}

        try:
            cpu_percent = psutil.cpu_percent(interval=None)
            cpu_count = psutil.cpu_count()
            return {
                'percent': cpu_percent,
                'count': cpu_count
            }
        except Exception as e:
            logger.warning(f"获取CPU信息失败: {e}")
            return {'percent': 25.0, 'count': 4}


class PerformanceMonitorWindow(QWidget):
    """性能监控窗口"""
    
    # 定义信号
    window_closed = pyqtSignal()
    
    def __init__(self, parent=None):
        """初始化性能监控窗口"""
        super().__init__(parent)

        # 添加关闭标志，用于快速关闭
        self._is_closing = False

        try:
            self.setWindowTitle("性能监控器")
            self.resize(900, 600)
            self.setMinimumSize(700, 450)

            # 设置窗口属性
            from PyQt5.QtCore import Qt
            self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint |
                               Qt.WindowMinimizeButtonHint | Qt.WindowMaximizeButtonHint)

            # 初始化数据
            self.performance_data = PerformanceData()
            self.system_monitor = SystemMonitorThread(self)

            # 连接系统监控信号
            self.system_monitor.data_updated.connect(self._on_system_data)
            self.system_monitor.error_occurred.connect(self._on_monitor_error)

            # 设置UI
            self._setup_ui()

            # 设置定时器
            self.update_timer = QTimer()
            self.update_timer.timeout.connect(self._update_display)
            self.update_timer.start(1000)  # 每秒更新

            # 延迟启动系统监控，避免初始化时的线程冲突
            QTimer.singleShot(1000, self._start_monitoring)

            logger.info("性能监控窗口初始化完成")

        except Exception as e:
            logger.error(f"性能监控窗口初始化失败: {str(e)}")
            # 显示错误信息但不崩溃
            self._show_initialization_error(str(e))

    def _start_monitoring(self):
        """延迟启动监控"""
        try:
            self.system_monitor.start_monitoring()
        except Exception as e:
            logger.error(f"启动系统监控失败: {str(e)}")

    def _on_monitor_error(self, error_msg):
        """处理监控错误"""
        logger.warning(f"系统监控错误: {error_msg}")
        # 可以在这里添加错误处理逻辑

    def _show_initialization_error(self, error_msg):
        """显示初始化错误"""
        try:
            from PyQt5.QtWidgets import QLabel, QVBoxLayout
            layout = QVBoxLayout(self)
            error_label = QLabel(f"性能监控器初始化失败:\n{error_msg}")
            error_label.setStyleSheet("color: red; padding: 20px;")
            layout.addWidget(error_label)
        except Exception:
            pass  # 如果连错误显示都失败，就静默处理

    def _apply_progress_bar_style(self, progress_bar):
        """为进度条应用绿色样式"""
        try:
            from ui.styles.ProgressBarStyleManager import apply_green_progress_style
            apply_green_progress_style(progress_bar, "default")
        except ImportError:
            # 如果样式管理器不可用，使用内联样式
            progress_bar.setStyleSheet("""
                QProgressBar {
                    border: 2px solid #C0C0C0;
                    border-radius: 5px;
                    background-color: #F0F0F0;
                    text-align: center;
                    font-weight: bold;
                    color: #333333;
                }

                QProgressBar::chunk {
                    background-color: qlineargradient(
                        x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #7ED321,
                        stop: 0.5 #5CB85C,
                        stop: 1 #4CAF50
                    );
                    border-radius: 3px;
                    margin: 1px;
                }
            """)
        except Exception as e:
            logger.warning(f"应用进度条样式失败: {str(e)}")

    def _setup_ui(self):
        """设置UI"""
        try:
            layout = QVBoxLayout(self)

            # 创建标签页
            self.tab_widget = QTabWidget()
            layout.addWidget(self.tab_widget)

            # 分步创建标签页，每步都有错误处理
            self._create_realtime_tab()
            self._create_statistics_tab()
            self._create_system_tab()

            # 控制按钮
            self._create_control_buttons(layout)

            logger.info("性能监控UI创建完成")

        except Exception as e:
            logger.error(f"创建性能监控UI失败: {str(e)}")
            # 创建简化的错误UI
            self._create_fallback_ui(str(e))

    def _create_fallback_ui(self, error_msg):
        """创建备用UI"""
        try:
            layout = QVBoxLayout(self)
            error_label = QLabel(f"UI创建失败: {error_msg}")
            error_label.setStyleSheet("color: red; padding: 20px;")
            layout.addWidget(error_label)

            # 添加基本的关闭按钮
            close_btn = QPushButton("关闭")
            close_btn.clicked.connect(self.close)
            layout.addWidget(close_btn)
        except Exception:
            pass
    
    def _create_realtime_tab(self):
        """创建实时监控标签页"""
        try:
            tab = QWidget()
            layout = QVBoxLayout(tab)

            # 当前操作状态
            status_group = QGroupBox("当前状态")
            status_layout = QGridLayout(status_group)

            self.current_operation_label = QLabel("无操作")
            self.current_throughput_label = QLabel("0.0 操作/秒")
            self.current_memory_label = QLabel("内存: 0 MB")
            self.current_cpu_label = QLabel("CPU: 0%")

            status_layout.addWidget(QLabel("当前操作:"), 0, 0)
            status_layout.addWidget(self.current_operation_label, 0, 1)
            status_layout.addWidget(QLabel("吞吐量:"), 1, 0)
            status_layout.addWidget(self.current_throughput_label, 1, 1)
            status_layout.addWidget(QLabel("内存使用:"), 2, 0)
            status_layout.addWidget(self.current_memory_label, 2, 1)
            status_layout.addWidget(QLabel("CPU使用:"), 3, 0)
            status_layout.addWidget(self.current_cpu_label, 3, 1)

            layout.addWidget(status_group)

            # 进度条
            progress_group = QGroupBox("操作进度")
            progress_layout = QVBoxLayout(progress_group)

            self.operation_progress = QProgressBar()
            self.operation_progress.setVisible(False)

            # 应用绿色样式
            self._apply_progress_bar_style(self.operation_progress)

            progress_layout.addWidget(self.operation_progress)

            layout.addWidget(progress_group)

            # 实时日志
            log_group = QGroupBox("实时日志")
            log_layout = QVBoxLayout(log_group)

            self.log_text = QTextEdit()
            # 限制日志行数（兼容不同PyQt版本）
            if hasattr(self.log_text, 'setMaximumBlockCount'):
                self.log_text.setMaximumBlockCount(1000)
            self.log_text.setReadOnly(True)
            log_layout.addWidget(self.log_text)

            layout.addWidget(log_group)

            self.tab_widget.addTab(tab, "实时监控")
            logger.debug("实时监控标签页创建完成")

        except Exception as e:
            logger.error(f"创建实时监控标签页失败: {str(e)}")
            # 创建简化版本
            try:
                tab = QWidget()
                layout = QVBoxLayout(tab)
                error_label = QLabel(f"实时监控标签页创建失败: {str(e)}")
                layout.addWidget(error_label)
                self.tab_widget.addTab(tab, "实时监控(错误)")
            except Exception:
                pass
    
    def _create_statistics_tab(self):
        """创建性能统计标签页"""
        try:
            tab = QWidget()
            layout = QVBoxLayout(tab)

            # 统计信息
            stats_group = QGroupBox("性能统计")
            stats_layout = QGridLayout(stats_group)

            self.total_operations_label = QLabel("0")
            self.avg_duration_label = QLabel("0.000 秒")
            self.min_duration_label = QLabel("0.000 秒")
            self.max_duration_label = QLabel("0.000 秒")
            self.success_rate_label = QLabel("100.0%")
            self.total_throughput_label = QLabel("0.0 操作/秒")

            stats_layout.addWidget(QLabel("总操作数:"), 0, 0)
            stats_layout.addWidget(self.total_operations_label, 0, 1)
            stats_layout.addWidget(QLabel("平均耗时:"), 1, 0)
            stats_layout.addWidget(self.avg_duration_label, 1, 1)
            stats_layout.addWidget(QLabel("最短耗时:"), 2, 0)
            stats_layout.addWidget(self.min_duration_label, 2, 1)
            stats_layout.addWidget(QLabel("最长耗时:"), 3, 0)
            stats_layout.addWidget(self.max_duration_label, 3, 1)
            stats_layout.addWidget(QLabel("成功率:"), 4, 0)
            stats_layout.addWidget(self.success_rate_label, 4, 1)
            stats_layout.addWidget(QLabel("总吞吐量:"), 5, 0)
            stats_layout.addWidget(self.total_throughput_label, 5, 1)

            layout.addWidget(stats_group)

            # 性能历史表格
            history_group = QGroupBox("操作历史")
            history_layout = QVBoxLayout(history_group)

            self.history_table = QTableWidget()
            self.history_table.setColumnCount(4)
            self.history_table.setHorizontalHeaderLabels(["时间", "操作类型", "耗时(秒)", "状态"])
            history_layout.addWidget(self.history_table)

            layout.addWidget(history_group)

            self.tab_widget.addTab(tab, "性能统计")
            logger.debug("性能统计标签页创建完成")

        except Exception as e:
            logger.error(f"创建性能统计标签页失败: {str(e)}")
            # 创建简化版本
            try:
                tab = QWidget()
                layout = QVBoxLayout(tab)
                error_label = QLabel(f"性能统计标签页创建失败: {str(e)}")
                layout.addWidget(error_label)
                self.tab_widget.addTab(tab, "性能统计(错误)")
            except Exception:
                pass
    
    def _create_system_tab(self):
        """创建系统信息标签页"""
        try:
            tab = QWidget()
            layout = QVBoxLayout(tab)

            # 系统信息
            system_group = QGroupBox("系统资源")
            system_layout = QGridLayout(system_group)

            self.memory_total_label = QLabel("0 MB")
            self.memory_used_label = QLabel("0 MB")
            self.memory_percent_label = QLabel("0%")
            self.cpu_count_label = QLabel("0")
            self.cpu_percent_label = QLabel("0%")

            system_layout.addWidget(QLabel("总内存:"), 0, 0)
            system_layout.addWidget(self.memory_total_label, 0, 1)
            system_layout.addWidget(QLabel("已用内存:"), 1, 0)
            system_layout.addWidget(self.memory_used_label, 1, 1)
            system_layout.addWidget(QLabel("内存使用率:"), 2, 0)
            system_layout.addWidget(self.memory_percent_label, 2, 1)
            system_layout.addWidget(QLabel("CPU核心数:"), 3, 0)
            system_layout.addWidget(self.cpu_count_label, 3, 1)
            system_layout.addWidget(QLabel("CPU使用率:"), 4, 0)
            system_layout.addWidget(self.cpu_percent_label, 4, 1)

            layout.addWidget(system_group)

            # 系统详细信息
            details_group = QGroupBox("详细信息")
            details_layout = QVBoxLayout(details_group)

            self.system_details_text = QTextEdit()
            self.system_details_text.setReadOnly(True)
            self._update_system_details()
            details_layout.addWidget(self.system_details_text)

            layout.addWidget(details_group)

            self.tab_widget.addTab(tab, "系统信息")
            logger.debug("系统信息标签页创建完成")

        except Exception as e:
            logger.error(f"创建系统信息标签页失败: {str(e)}")
            # 创建简化版本
            try:
                tab = QWidget()
                layout = QVBoxLayout(tab)
                error_label = QLabel(f"系统信息标签页创建失败: {str(e)}")
                layout.addWidget(error_label)
                self.tab_widget.addTab(tab, "系统信息(错误)")
            except Exception:
                pass
    
    def _create_control_buttons(self, layout):
        """创建控制按钮"""
        button_layout = QHBoxLayout()
        
        self.reset_button = QPushButton("重置统计")
        self.reset_button.clicked.connect(self._reset_statistics)
        button_layout.addWidget(self.reset_button)
        
        self.export_button = QPushButton("导出报告")
        self.export_button.clicked.connect(self._export_report)
        button_layout.addWidget(self.export_button)
        
        button_layout.addStretch()
        
        layout.addLayout(button_layout)

    def _update_system_details(self):
        """更新系统详细信息"""
        import platform
        import sys

        details = f"""系统详细信息：
操作系统: {platform.system()} {platform.release()}
Python版本: {sys.version.split()[0]}
架构: {platform.architecture()[0]}
处理器: {platform.processor()}
机器类型: {platform.machine()}
网络名称: {platform.node()}
"""
        self.system_details_text.setPlainText(details)

    def _on_system_data(self, data):
        """处理系统监控数据"""
        # 如果正在关闭，立即返回
        if self._is_closing:
            return

        try:
            # 检查UI控件是否存在，避免关闭时的错误
            if not hasattr(self, 'current_memory_label') or not self.current_memory_label:
                return

            # 更新内存信息
            memory = data.get('memory', {})
            self.current_memory_label.setText(f"内存: {memory.get('used', 0):.1f} MB")

            if hasattr(self, 'memory_total_label') and self.memory_total_label:
                self.memory_total_label.setText(f"{memory.get('total', 0):.1f} MB")
                self.memory_used_label.setText(f"{memory.get('used', 0):.1f} MB")
                self.memory_percent_label.setText(f"{memory.get('percent', 0):.1f}%")

            # 更新CPU信息
            cpu = data.get('cpu', {})
            if hasattr(self, 'current_cpu_label') and self.current_cpu_label:
                self.current_cpu_label.setText(f"CPU: {cpu.get('percent', 0):.1f}%")

            if hasattr(self, 'cpu_count_label') and self.cpu_count_label:
                self.cpu_count_label.setText(str(cpu.get('count', 0)))
                self.cpu_percent_label.setText(f"{cpu.get('percent', 0):.1f}%")

        except Exception as e:
            if not self._is_closing:  # 只在非关闭状态下记录错误
                logger.error(f"更新系统数据时出错: {str(e)}")

    def _update_display(self):
        """更新显示"""
        # 如果正在关闭，立即返回
        if self._is_closing:
            return

        try:
            # 检查性能数据是否存在
            if not hasattr(self, 'performance_data') or not self.performance_data:
                return

            # 检查UI控件是否存在
            if not hasattr(self, 'total_operations_label') or not self.total_operations_label:
                return

            # 更新性能统计
            self.total_operations_label.setText(str(self.performance_data.operation_count))
            self.avg_duration_label.setText(f"{self.performance_data.get_average_duration():.3f} 秒")

            if self.performance_data.min_duration != float('inf'):
                self.min_duration_label.setText(f"{self.performance_data.min_duration:.3f} 秒")
            else:
                self.min_duration_label.setText("0.000 秒")

            self.max_duration_label.setText(f"{self.performance_data.max_duration:.3f} 秒")
            self.success_rate_label.setText(f"{self.performance_data.get_success_rate():.1f}%")
            self.total_throughput_label.setText(f"{self.performance_data.get_throughput():.1f} 操作/秒")

            if hasattr(self, 'current_throughput_label') and self.current_throughput_label:
                self.current_throughput_label.setText(f"{self.performance_data.get_throughput():.1f} 操作/秒")

        except Exception as e:
            if not self._is_closing:  # 只在非关闭状态下记录错误
                logger.error(f"更新显示时出错: {str(e)}")

    def _reset_statistics(self):
        """重置统计数据"""
        self.performance_data = PerformanceData()
        self.history_table.setRowCount(0)
        self.log_text.clear()
        self._add_log("统计数据已重置")
        logger.info("性能统计数据已重置")

    def _export_report(self):
        """导出性能报告"""
        try:
            from PyQt5.QtWidgets import QFileDialog

            filename, _ = QFileDialog.getSaveFileName(
                self, "导出性能报告", "performance_report.txt", "文本文件 (*.txt)"
            )

            if filename:
                report = self._generate_report()
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(report)
                self._add_log(f"性能报告已导出到: {filename}")
                logger.info(f"性能报告已导出到: {filename}")

        except Exception as e:
            self._add_log(f"导出报告失败: {str(e)}")
            logger.error(f"导出报告失败: {str(e)}")

    def _generate_report(self) -> str:
        """生成性能报告"""
        report = f"""性能监控报告
生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}

=== 性能统计 ===
总操作数: {self.performance_data.operation_count}
平均耗时: {self.performance_data.get_average_duration():.3f} 秒
最短耗时: {self.performance_data.min_duration:.3f} 秒
最长耗时: {self.performance_data.max_duration:.3f} 秒
成功率: {self.performance_data.get_success_rate():.1f}%
总吞吐量: {self.performance_data.get_throughput():.1f} 操作/秒
错误数量: {self.performance_data.error_count}

=== 系统信息 ===
{self.system_details_text.toPlainText()}

=== 监控时长 ===
开始时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(self.performance_data.start_time))}
监控时长: {time.time() - self.performance_data.start_time:.1f} 秒
"""
        return report

    def _add_log(self, message: str):
        """添加日志消息"""
        timestamp = time.strftime('%H:%M:%S')
        self.log_text.append(f"[{timestamp}] {message}")

    def _fast_cleanup_ui_data(self):
        """快速清理UI数据，优化关闭速度"""
        try:
            # 快速清理日志文本 - 使用setPlainText比clear更快
            if hasattr(self, 'log_text') and self.log_text:
                self.log_text.setPlainText("")

            # 快速清理历史表格 - 直接设置行数为0
            if hasattr(self, 'history_table') and self.history_table:
                self.history_table.setRowCount(0)

            # 清理性能数据对象
            if hasattr(self, 'performance_data'):
                self.performance_data = None

            # 清理系统详细信息
            if hasattr(self, 'system_details_text') and self.system_details_text:
                self.system_details_text.setPlainText("")

            logger.debug("UI数据快速清理完成")

        except Exception as e:
            logger.warning(f"快速清理UI数据时出错: {str(e)}")

    def add_operation_record(self, operation_type: str, duration: float, success: bool = True):
        """添加操作记录"""
        self.performance_data.add_operation(duration, success)

        # 添加到历史表格
        row = self.history_table.rowCount()
        self.history_table.insertRow(row)

        timestamp = time.strftime('%H:%M:%S')
        status = "成功" if success else "失败"

        self.history_table.setItem(row, 0, QTableWidgetItem(timestamp))
        self.history_table.setItem(row, 1, QTableWidgetItem(operation_type))
        self.history_table.setItem(row, 2, QTableWidgetItem(f"{duration:.3f}"))
        self.history_table.setItem(row, 3, QTableWidgetItem(status))

        # 滚动到最新记录
        self.history_table.scrollToBottom()

        # 添加日志
        self._add_log(f"{operation_type} - {duration:.3f}秒 - {status}")

        # 限制表格行数
        if self.history_table.rowCount() > 1000:
            self.history_table.removeRow(0)

    def set_operation_progress(self, current: int, total: int, operation_name: str = ""):
        """设置操作进度"""
        if total > 0:
            self.operation_progress.setVisible(True)
            self.operation_progress.setMaximum(total)
            self.operation_progress.setValue(current)

            if operation_name:
                self.current_operation_label.setText(f"{operation_name} ({current}/{total})")
        else:
            self.operation_progress.setVisible(False)
            self.current_operation_label.setText("无操作")

    def start_batch_operation(self, operation_type: str, total_count: int):
        """开始批量操作监控"""
        self.current_operation_label.setText(f"{operation_type} (0/{total_count})")
        self.set_operation_progress(0, total_count, operation_type)
        self._add_log(f"开始 {operation_type}，总数: {total_count}")

        # 记录开始时间
        self.batch_start_time = time.time()
        self.batch_operation_type = operation_type
        self.batch_total_count = total_count

    def update_batch_progress(self, current: int, total: int, operation_type: str):
        """更新批量操作进度"""
        self.current_operation_label.setText(f"{operation_type} ({current}/{total})")
        self.set_operation_progress(current, total, operation_type)

        # 计算当前速度
        if hasattr(self, 'batch_start_time') and current > 0:
            elapsed = time.time() - self.batch_start_time
            if elapsed > 0:
                current_speed = current / elapsed
                self.current_throughput_label.setText(f"{current_speed:.1f} 操作/秒")

    def finish_batch_operation(self, operation_type: str, duration: float, success: bool, completed_count: int):
        """完成批量操作监控"""
        # 添加操作记录
        self.add_operation_record(operation_type, duration, success)

        # 更新显示
        status = "成功" if success else "失败"
        self.current_operation_label.setText(f"{operation_type} 完成 - {status}")
        self.operation_progress.setVisible(False)

        # 计算平均速度
        if duration > 0:
            avg_speed = completed_count / duration
            self._add_log(f"{operation_type} 完成: {completed_count} 个操作，耗时 {duration:.3f}秒，平均速度 {avg_speed:.1f} 操作/秒")
        else:
            self._add_log(f"{operation_type} 完成: {completed_count} 个操作")

    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            logger.info("性能监控窗口开始关闭...")

            # 立即设置关闭标志，停止所有更新操作
            self._is_closing = True

            # 立即停止定时器
            if hasattr(self, 'update_timer') and self.update_timer:
                self.update_timer.stop()
                logger.debug("更新定时器已停止")

            # 快速停止系统监控线程
            if hasattr(self, 'system_monitor') and self.system_monitor:
                self.system_monitor.stop_monitoring()
                logger.debug("系统监控线程已停止")

            # 断开所有信号连接，避免关闭时的信号处理
            if hasattr(self, 'system_monitor') and self.system_monitor:
                try:
                    self.system_monitor.data_updated.disconnect()
                    self.system_monitor.error_occurred.disconnect()
                    logger.debug("系统监控信号已断开")
                except Exception:
                    pass  # 忽略断开连接的错误

            # 快速清理大量数据，释放内存
            self._fast_cleanup_ui_data()

            # 发出窗口关闭信号
            self.window_closed.emit()

            logger.info("性能监控窗口关闭完成")

        except Exception as e:
            logger.warning(f"关闭性能监控窗口时出错: {str(e)}")
        finally:
            # 确保窗口能够关闭
            super().closeEvent(event)


class PerformanceMonitorPlugin(IToolWindowPlugin):
    """性能监控插件"""

    @property
    def name(self) -> str:
        return "性能监控器"

    @property
    def version(self) -> str:
        return "1.0.0"

    @property
    def description(self) -> str:
        return "实时监控系统性能和批量操作性能"

    @property
    def menu_text(self) -> str:
        return "性能监控器"

    @property
    def icon_path(self) -> Optional[str]:
        return None  # 没有图标

    def __init__(self):
        """初始化插件"""
        self.context = None
        self.window_instance = None

    def initialize(self, context):
        """初始化插件

        Args:
            context: 应用程序上下文（通常是主窗口）
        """
        self.context = context
        logger.info(f"插件 '{self.name}' 初始化完成")

        # 尝试连接到批量操作管理器的信号
        self._connect_to_batch_operations()

    def _connect_to_batch_operations(self):
        """连接到批量操作管理器"""
        try:
            # 尝试多种可能的属性名
            batch_manager = None
            if hasattr(self.context, 'batch_operation_manager'):
                batch_manager = self.context.batch_operation_manager
            elif hasattr(self.context, 'batch_manager'):
                batch_manager = self.context.batch_manager

            if batch_manager:
                # 连接批量读取信号
                if hasattr(batch_manager, 'batch_read_started'):
                    batch_manager.batch_read_started.connect(self._on_batch_read_started)
                    logger.debug("已连接批量读取开始信号")
                if hasattr(batch_manager, 'batch_read_finished'):
                    batch_manager.batch_read_finished.connect(self._on_batch_read_finished)
                    logger.debug("已连接批量读取完成信号")
                if hasattr(batch_manager, 'batch_read_progress'):
                    batch_manager.batch_read_progress.connect(self._on_batch_read_progress)
                    logger.debug("已连接批量读取进度信号")

                # 连接批量写入信号
                if hasattr(batch_manager, 'batch_write_started'):
                    batch_manager.batch_write_started.connect(self._on_batch_write_started)
                    logger.debug("已连接批量写入开始信号")
                if hasattr(batch_manager, 'batch_write_finished'):
                    batch_manager.batch_write_finished.connect(self._on_batch_write_finished)
                    logger.debug("已连接批量写入完成信号")
                if hasattr(batch_manager, 'batch_write_progress'):
                    batch_manager.batch_write_progress.connect(self._on_batch_write_progress)
                    logger.debug("已连接批量写入进度信号")

                self.batch_manager = batch_manager
                logger.info("✅ 已成功连接到批量操作管理器，性能监控功能已激活")
            else:
                logger.warning("❌ 主窗口没有批量操作管理器，性能监控功能将受限")
                # 列出可用的属性以便调试
                available_attrs = [attr for attr in dir(self.context) if 'batch' in attr.lower()]
                if available_attrs:
                    logger.debug(f"可用的批量相关属性: {available_attrs}")

        except Exception as e:
            logger.warning(f"连接批量操作管理器失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def _on_batch_read_started(self, total_count):
        """批量读取开始"""
        if self.window_instance:
            self.window_instance.start_batch_operation("批量读取", total_count)
            logger.info(f"开始监控批量读取操作，总数: {total_count}")

    def _on_batch_read_finished(self, success, duration, completed_count):
        """批量读取完成"""
        if self.window_instance:
            self.window_instance.finish_batch_operation("批量读取", duration, success, completed_count)
            logger.info(f"批量读取完成，耗时: {duration:.3f}秒，成功: {success}")

    def _on_batch_read_progress(self, current, total):
        """批量读取进度更新"""
        if self.window_instance:
            self.window_instance.update_batch_progress(current, total, "批量读取")

    def _on_batch_write_started(self, total_count):
        """批量写入开始"""
        if self.window_instance:
            self.window_instance.start_batch_operation("批量写入", total_count)
            logger.info(f"开始监控批量写入操作，总数: {total_count}")

    def _on_batch_write_finished(self, success, duration, completed_count):
        """批量写入完成"""
        if self.window_instance:
            self.window_instance.finish_batch_operation("批量写入", duration, success, completed_count)
            logger.info(f"批量写入完成，耗时: {duration:.3f}秒，成功: {success}")

    def _on_batch_write_progress(self, current, total):
        """批量写入进度更新"""
        if self.window_instance:
            self.window_instance.update_batch_progress(current, total, "批量写入")

    def create_window(self, parent=None):
        """创建工具窗口

        Args:
            parent: 父窗口

        Returns:
            工具窗口实例
        """
        try:
            if self.window_instance is None:
                # 检查依赖
                if not PSUTIL_AVAILABLE:
                    logger.warning("psutil库不可用，性能监控功能将受限")

                self.window_instance = PerformanceMonitorWindow(parent)
                self.window_instance.window_closed.connect(self._on_window_closed)
                logger.info(f"创建 '{self.name}' 工具窗口")

            return self.window_instance

        except Exception as e:
            logger.error(f"创建性能监控窗口失败: {str(e)}")
            # 创建一个简单的错误窗口
            try:
                from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton
                error_window = QWidget(parent)
                error_window.setWindowTitle("性能监控器 - 错误")
                error_window.resize(400, 200)

                layout = QVBoxLayout(error_window)
                error_label = QLabel(f"性能监控器创建失败:\n{str(e)}")
                error_label.setStyleSheet("color: red; padding: 20px;")
                layout.addWidget(error_label)

                close_btn = QPushButton("关闭")
                close_btn.clicked.connect(error_window.close)
                layout.addWidget(close_btn)

                self.window_instance = error_window
                return error_window

            except Exception as fallback_error:
                logger.error(f"创建错误窗口也失败: {str(fallback_error)}")
                return None

    def _on_window_closed(self):
        """窗口关闭处理"""
        self.window_instance = None
        logger.info(f"'{self.name}' 工具窗口已关闭")

    def cleanup(self):
        """清理插件资源"""
        if self.window_instance:
            self.window_instance.close()
            self.window_instance = None

        logger.info(f"插件 '{self.name}' 已清理")


# 插件入口点
# 插件管理器会自动发现这个类
plugin_class = PerformanceMonitorPlugin
