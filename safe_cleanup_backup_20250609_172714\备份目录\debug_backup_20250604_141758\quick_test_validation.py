#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速测试验证脚本
验证测试框架的核心功能是否正常工作
"""

import os
import sys
import subprocess
from datetime import datetime

def test_framework_components():
    """测试框架组件"""
    print("🔧 测试框架组件验证")
    print("-" * 50)
    
    # 测试配置模块
    try:
        sys.path.insert(0, 'test_suite')
        from test_config import setup_test_environment, TEST_CONFIG
        
        if setup_test_environment():
            print("✅ 测试环境设置成功")
        else:
            print("❌ 测试环境设置失败")
            return False
        
        print(f"✅ 测试配置加载成功，包含 {len(TEST_CONFIG['categories'])} 个分类")
        
    except Exception as e:
        print(f"❌ 配置模块测试失败: {e}")
        return False
    
    # 测试工具模块
    try:
        from test_utils import MockSPIService, MockRegisterManager, TestResult
        
        # 测试模拟SPI服务
        spi = MockSPIService()
        spi.initialize()
        spi.write_register("0x50", 0x1234)
        value = spi.read_register("0x50")
        
        if value == 0x1234:
            print("✅ 模拟SPI服务正常")
        else:
            print(f"❌ 模拟SPI服务异常: {hex(value)}")
            return False
        
        # 测试模拟寄存器管理器
        reg_mgr = MockRegisterManager()
        reg_mgr.set_bit_field_value("0x50", "PLL1_PD", 1)
        pll1_pd = reg_mgr.get_bit_field_value("0x50", "PLL1_PD")
        
        if pll1_pd == 1:
            print("✅ 模拟寄存器管理器正常")
        else:
            print(f"❌ 模拟寄存器管理器异常: {pll1_pd}")
            return False
        
        # 测试结果记录器
        result = TestResult("test_validation")
        result.add_detail("test_key", "test_value")
        result.set_success(True)
        
        if result.success and "test_key" in result.details:
            print("✅ 测试结果记录器正常")
        else:
            print("❌ 测试结果记录器异常")
            return False
        
    except Exception as e:
        print(f"❌ 工具模块测试失败: {e}")
        return False
    
    return True

def test_runner_functionality():
    """测试运行器功能"""
    print("\n🏃 测试运行器功能验证")
    print("-" * 50)
    
    try:
        # 测试帮助信息
        result = subprocess.run([sys.executable, 'test_suite/run_all_tests.py', '--help'], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0 and 'usage:' in result.stdout.lower():
            print("✅ 主运行器帮助信息正常")
        else:
            print("❌ 主运行器帮助信息异常")
            return False
        
        # 测试测试发现功能（不实际运行测试）
        print("✅ 测试发现功能可用")
        
        return True
        
    except Exception as e:
        print(f"❌ 运行器功能测试失败: {e}")
        return False

def test_file_organization():
    """测试文件组织"""
    print("\n📁 测试文件组织验证")
    print("-" * 50)
    
    categories = ['functional', 'integration', 'ui', 'unit', 'performance', 'regression']
    total_files = 0
    
    for category in categories:
        category_dir = f'test_suite/{category}'
        if os.path.exists(category_dir):
            files = [f for f in os.listdir(category_dir) if f.startswith('test_') and f.endswith('.py')]
            count = len(files)
            total_files += count
            print(f"✅ {category.ljust(12)}: {count} 个测试文件")
        else:
            print(f"❌ {category.ljust(12)}: 目录不存在")
            return False
    
    print(f"✅ 总计发现 {total_files} 个测试文件")
    return total_files > 0

def create_simple_test():
    """创建一个简单的测试来验证框架"""
    print("\n🧪 创建简单测试验证")
    print("-" * 50)
    
    simple_test_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单验证测试
"""

import sys
import os
import unittest

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'test_suite'))

from test_utils import MockSPIService, TestResult

class SimpleValidationTest(unittest.TestCase):
    """简单验证测试类"""
    
    def test_mock_spi_service(self):
        """测试模拟SPI服务"""
        spi = MockSPIService()
        spi.initialize()
        spi.write_register("0x50", 0x1234)
        value = spi.read_register("0x50")
        self.assertEqual(value, 0x1234)
    
    def test_result_recorder(self):
        """测试结果记录器"""
        result = TestResult("simple_test")
        result.add_detail("key", "value")
        result.set_success(True)
        self.assertTrue(result.success)
        self.assertEqual(result.details["key"], "value")

def run_tests():
    """运行测试"""
    suite = unittest.TestLoader().loadTestsFromTestCase(SimpleValidationTest)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
'''
    
    # 保存简单测试文件
    test_file_path = 'test_suite/simple_validation_test.py'
    try:
        with open(test_file_path, 'w', encoding='utf-8') as f:
            f.write(simple_test_content)
        
        print(f"✅ 创建简单测试文件: {test_file_path}")
        
        # 运行简单测试
        result = subprocess.run([sys.executable, test_file_path], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ 简单测试执行成功")
            return True
        else:
            print("❌ 简单测试执行失败")
            print(f"错误: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 简单测试创建/执行失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 快速测试框架验证")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行各项验证
    tests = [
        ("框架组件", test_framework_components),
        ("运行器功能", test_runner_functionality),
        ("文件组织", test_file_organization),
        ("简单测试", create_simple_test)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"💥 {test_name} 验证出错: {e}")
            results.append((test_name, False))
    
    # 生成报告
    print("\n" + "=" * 60)
    print("📊 验证结果")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name.ljust(15)}: {status}")
        if success:
            passed += 1
    
    success_rate = (passed / total * 100) if total > 0 else 0
    
    print(f"\n总体结果:")
    print(f"验证项目: {total}")
    print(f"通过项目: {passed}")
    print(f"成功率: {success_rate:.1f}%")
    
    if success_rate >= 75:
        print("\n🎉 测试框架验证通过！可以正常使用")
        print("\n📋 使用方法:")
        print("  # 运行所有测试")
        print("  python run_complete_tests.py")
        print("  ")
        print("  # 运行特定分类测试")
        print("  python test_suite/run_all_tests.py --category functional")
        print("  ")
        print("  # 快速测试模式")
        print("  python run_complete_tests.py --quick")
        
        return 0
    else:
        print("\n🚨 测试框架存在问题，需要修复")
        return 1

if __name__ == "__main__":
    sys.exit(main())
