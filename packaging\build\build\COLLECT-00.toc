([('FSJ04832_RegisterTool_v1.0.0.0.exe',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\packaging\\build\\build\\FSJ04832_RegisterTool_v1.0.0.0.exe',
   'EXECUTABLE'),
  ('python38.dll', 'd:\\program files\\python38\\python38.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\libglesv2.dll',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libglesv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libegl.dll',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libegl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\printsupport\\windowsprintersupport.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\printsupport\\windowsprintersupport.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('_ctypes.pyd',
   'd:\\program files\\python38\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'd:\\program files\\python38\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd', 'd:\\program files\\python38\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd',
   'd:\\program files\\python38\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'd:\\program files\\python38\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('pyexpat.pyd',
   'd:\\program files\\python38\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'd:\\program files\\python38\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'd:\\program files\\python38\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'd:\\program files\\python38\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'd:\\program files\\python38\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'd:\\program files\\python38\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_lzma.pyd', 'd:\\program files\\python38\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'd:\\program files\\python38\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_queue.pyd', 'd:\\program files\\python38\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp38-win_amd64.pyd',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\sip.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtPrintSupport.pyd',
   'd:\\program files\\python38\\lib\\site-packages\\PyQt5\\QtPrintSupport.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'd:\\program files\\python38\\lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'd:\\program files\\python38\\lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'd:\\program files\\python38\\lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'd:\\program files\\python38\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5PrintSupport.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5PrintSupport.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('libffi-7.dll', 'd:\\program files\\python38\\DLLs\\libffi-7.dll', 'BINARY'),
  ('libcrypto-1_1.dll',
   'd:\\program files\\python38\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'd:\\program files\\python38\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('python3.dll', 'd:\\program files\\python38\\python3.dll', 'BINARY'),
  ('ucrtbase.dll', 'C:\\windows\\system32\\ucrtbase.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\windows\\system32\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('config\\README.md',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\config\\README.md',
   'DATA'),
  ('config\\app.json.template',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\config\\app.json.template',
   'DATA'),
  ('config\\default.json',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\config\\default.json',
   'DATA'),
  ('config\\local.json',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\config\\local.json',
   'DATA'),
  ('config\\migration.json',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\config\\migration.json',
   'DATA'),
  ('config\\selective_register_plugin.json',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\config\\selective_register_plugin.json',
   'DATA'),
  ('core\\event_bus\\RegisterUpdateBus.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\event_bus\\RegisterUpdateBus.py',
   'DATA'),
  ('core\\event_bus\\__pycache__\\RegisterUpdateBus.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\event_bus\\__pycache__\\RegisterUpdateBus.cpython-313.pyc',
   'DATA'),
  ('core\\event_bus\\__pycache__\\RegisterUpdateBus.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\event_bus\\__pycache__\\RegisterUpdateBus.cpython-38.pyc',
   'DATA'),
  ('core\\models\\RegisterModel.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\models\\RegisterModel.py',
   'DATA'),
  ('core\\models\\__pycache__\\RegisterModel.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\models\\__pycache__\\RegisterModel.cpython-313.pyc',
   'DATA'),
  ('core\\models\\__pycache__\\RegisterModel.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\models\\__pycache__\\RegisterModel.cpython-38.pyc',
   'DATA'),
  ('core\\protection\\__init__.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\protection\\__init__.py',
   'DATA'),
  ('core\\services\\BatchOperationState.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\BatchOperationState.py',
   'DATA'),
  ('core\\services\\DIContainer.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\DIContainer.py',
   'DATA'),
  ('core\\services\\__init__.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\__init__.py',
   'DATA'),
  ('core\\services\\__pycache__\\BatchOperationState.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\__pycache__\\BatchOperationState.cpython-38.pyc',
   'DATA'),
  ('core\\services\\__pycache__\\DIContainer.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\__pycache__\\DIContainer.cpython-313.pyc',
   'DATA'),
  ('core\\services\\__pycache__\\DIContainer.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\__pycache__\\DIContainer.cpython-38.pyc',
   'DATA'),
  ('core\\services\\__pycache__\\__init__.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('core\\services\\__pycache__\\__init__.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\__pycache__\\__init__.cpython-38.pyc',
   'DATA'),
  ('core\\services\\async\\AsyncOperationManager.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\async\\AsyncOperationManager.py',
   'DATA'),
  ('core\\services\\config\\ConfigManager.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\config\\ConfigManager.py',
   'DATA'),
  ('core\\services\\config\\ConfigurationManager.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\config\\ConfigurationManager.py',
   'DATA'),
  ('core\\services\\config\\ConfigurationService.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\config\\ConfigurationService.py',
   'DATA'),
  ('core\\services\\config\\__init__.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\config\\__init__.py',
   'DATA'),
  ('core\\services\\config\\__pycache__\\ConfigurationManager.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\config\\__pycache__\\ConfigurationManager.cpython-313.pyc',
   'DATA'),
  ('core\\services\\config\\__pycache__\\ConfigurationManager.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\config\\__pycache__\\ConfigurationManager.cpython-38.pyc',
   'DATA'),
  ('core\\services\\config\\__pycache__\\ConfigurationService.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\config\\__pycache__\\ConfigurationService.cpython-313.pyc',
   'DATA'),
  ('core\\services\\config\\__pycache__\\ConfigurationService.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\config\\__pycache__\\ConfigurationService.cpython-38.pyc',
   'DATA'),
  ('core\\services\\config\\__pycache__\\__init__.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\config\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('core\\services\\config\\__pycache__\\__init__.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\config\\__pycache__\\__init__.cpython-38.pyc',
   'DATA'),
  ('core\\services\\plugin\\MenuClickFixer.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\plugin\\MenuClickFixer.py',
   'DATA'),
  ('core\\services\\plugin\\PluginIntegrationService.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\plugin\\PluginIntegrationService.py',
   'DATA'),
  ('core\\services\\plugin\\PluginManager.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\plugin\\PluginManager.py',
   'DATA'),
  ('core\\services\\plugin\\__pycache__\\MenuClickFixer.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\plugin\\__pycache__\\MenuClickFixer.cpython-38.pyc',
   'DATA'),
  ('core\\services\\plugin\\__pycache__\\PluginIntegrationService.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\plugin\\__pycache__\\PluginIntegrationService.cpython-38.pyc',
   'DATA'),
  ('core\\services\\plugin\\__pycache__\\PluginManager.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\plugin\\__pycache__\\PluginManager.cpython-38.pyc',
   'DATA'),
  ('core\\services\\plugin\\dock\\PluginDockService.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\plugin\\dock\\PluginDockService.py',
   'DATA'),
  ('core\\services\\plugin\\dock\\__pycache__\\PluginDockService.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\plugin\\dock\\__pycache__\\PluginDockService.cpython-38.pyc',
   'DATA'),
  ('core\\services\\plugin\\menu\\PluginMenuService.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\plugin\\menu\\PluginMenuService.py',
   'DATA'),
  ('core\\services\\plugin\\menu\\__pycache__\\PluginMenuService.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\plugin\\menu\\__pycache__\\PluginMenuService.cpython-38.pyc',
   'DATA'),
  ('core\\services\\plugin\\window\\PluginWindowService.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\plugin\\window\\PluginWindowService.py',
   'DATA'),
  ('core\\services\\plugin\\window\\__pycache__\\PluginWindowService.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\plugin\\window\\__pycache__\\PluginWindowService.cpython-38.pyc',
   'DATA'),
  ('core\\services\\register\\RegisterManager.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\register\\RegisterManager.py',
   'DATA'),
  ('core\\services\\register\\RegisterOperationService.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\register\\RegisterOperationService.py',
   'DATA'),
  ('core\\services\\register\\__init__.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\register\\__init__.py',
   'DATA'),
  ('core\\services\\register\\__pycache__\\RegisterManager.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\register\\__pycache__\\RegisterManager.cpython-313.pyc',
   'DATA'),
  ('core\\services\\register\\__pycache__\\RegisterManager.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\register\\__pycache__\\RegisterManager.cpython-38.pyc',
   'DATA'),
  ('core\\services\\register\\__pycache__\\RegisterOperationService.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\register\\__pycache__\\RegisterOperationService.cpython-313.pyc',
   'DATA'),
  ('core\\services\\register\\__pycache__\\RegisterOperationService.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\register\\__pycache__\\RegisterOperationService.cpython-38.pyc',
   'DATA'),
  ('core\\services\\register\\__pycache__\\__init__.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\register\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('core\\services\\register\\__pycache__\\__init__.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\register\\__pycache__\\__init__.cpython-38.pyc',
   'DATA'),
  ('core\\services\\spi\\__pycache__\\port_manager.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\spi\\__pycache__\\port_manager.cpython-38.pyc',
   'DATA'),
  ('core\\services\\spi\\__pycache__\\spiPrivacy.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\spi\\__pycache__\\spiPrivacy.cpython-38.pyc',
   'DATA'),
  ('core\\services\\spi\\__pycache__\\spi_interface.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\spi\\__pycache__\\spi_interface.cpython-38.pyc',
   'DATA'),
  ('core\\services\\spi\\__pycache__\\spi_service.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\spi\\__pycache__\\spi_service.cpython-313.pyc',
   'DATA'),
  ('core\\services\\spi\\__pycache__\\spi_service.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\spi\\__pycache__\\spi_service.cpython-38.pyc',
   'DATA'),
  ('core\\services\\spi\\__pycache__\\spi_service_impl.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\spi\\__pycache__\\spi_service_impl.cpython-38.pyc',
   'DATA'),
  ('core\\services\\spi\\port_manager.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\spi\\port_manager.py',
   'DATA'),
  ('core\\services\\spi\\spiPrivacy.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\spi\\spiPrivacy.py',
   'DATA'),
  ('core\\services\\spi\\spi_interface.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\spi\\spi_interface.py',
   'DATA'),
  ('core\\services\\spi\\spi_service.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\spi\\spi_service.py',
   'DATA'),
  ('core\\services\\spi\\spi_service_impl.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\spi\\spi_service_impl.py',
   'DATA'),
  ('core\\services\\ui\\WindowManagementService.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\ui\\WindowManagementService.py',
   'DATA'),
  ('core\\services\\ui\\__init__.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\ui\\__init__.py',
   'DATA'),
  ('core\\services\\ui\\__pycache__\\WindowManagementService.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\ui\\__pycache__\\WindowManagementService.cpython-313.pyc',
   'DATA'),
  ('core\\services\\ui\\__pycache__\\WindowManagementService.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\ui\\__pycache__\\WindowManagementService.cpython-38.pyc',
   'DATA'),
  ('core\\services\\ui\\__pycache__\\__init__.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\ui\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('core\\services\\ui\\__pycache__\\__init__.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\ui\\__pycache__\\__init__.cpython-38.pyc',
   'DATA'),
  ('core\\services\\version\\VersionService.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\version\\VersionService.py',
   'DATA'),
  ('core\\services\\version\\__pycache__\\VersionService.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\version\\__pycache__\\VersionService.cpython-313.pyc',
   'DATA'),
  ('core\\services\\version\\__pycache__\\VersionService.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\version\\__pycache__\\VersionService.cpython-38.pyc',
   'DATA'),
  ('core\\utils\\path_utils.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\utils\\path_utils.py',
   'DATA'),
  ('gui\\cascaded_0_delay_dual_loop.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\gui\\cascaded_0_delay_dual_loop.jpg',
   'DATA'),
  ('gui\\clkInput.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\gui\\clkInput.jpg',
   'DATA'),
  ('gui\\dual_loop_0_delay_cascaded.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\gui\\dual_loop_0_delay_cascaded.jpg',
   'DATA'),
  ('gui\\dual_loop_mode.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\gui\\dual_loop_mode.jpg',
   'DATA'),
  ('gui\\dual_loop_mode_calculate.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\gui\\dual_loop_mode_calculate.jpg',
   'DATA'),
  ('gui\\dualloop0_delay_nested.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\gui\\dualloop0_delay_nested.jpg',
   'DATA'),
  ('gui\\nested_0_delay_dual_loop_mode.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\gui\\nested_0_delay_dual_loop_mode.jpg',
   'DATA'),
  ('gui\\pll.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\gui\\pll.jpg',
   'DATA'),
  ('gui\\singleLoop.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\gui\\singleLoop.jpg',
   'DATA'),
  ('gui\\systemREF.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\gui\\systemREF.jpg',
   'DATA'),
  ('images\\logo.ico',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\images\\logo.ico',
   'DATA'),
  ('lib\\register.json',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\lib\\register.json',
   'DATA'),
  ('packaging\\config\\version.json',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\packaging\\config\\version.json',
   'DATA'),
  ('plugins\\__pycache__\\IOsPagePlugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\__pycache__\\IOsPagePlugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\clk_output_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\__pycache__\\clk_output_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\clkin_control_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\__pycache__\\clkin_control_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\data_analysis_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\__pycache__\\data_analysis_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\drag_dock_test_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\__pycache__\\drag_dock_test_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\example_tool_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\__pycache__\\example_tool_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\performance_monitor_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\__pycache__\\performance_monitor_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\pll_control_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\__pycache__\\pll_control_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\selective_register_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\__pycache__\\selective_register_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\set_modes_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\__pycache__\\set_modes_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\sync_sysref_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\__pycache__\\sync_sysref_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\clk_output_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\clk_output_plugin.py',
   'DATA'),
  ('plugins\\clkin_control_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\clkin_control_plugin.py',
   'DATA'),
  ('plugins\\config\\__pycache__\\selective_register_config.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\config\\__pycache__\\selective_register_config.cpython-38.pyc',
   'DATA'),
  ('plugins\\config\\selective_register_config.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\config\\selective_register_config.py',
   'DATA'),
  ('plugins\\data_analysis_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\data_analysis_plugin.py',
   'DATA'),
  ('plugins\\example_tool_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\example_tool_plugin.py',
   'DATA'),
  ('plugins\\performance_monitor_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\performance_monitor_plugin.py',
   'DATA'),
  ('plugins\\pll_control_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\pll_control_plugin.py',
   'DATA'),
  ('plugins\\selective_register_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\selective_register_plugin.py',
   'DATA'),
  ('plugins\\set_modes_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\set_modes_plugin.py',
   'DATA'),
  ('plugins\\sync_sysref_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\sync_sysref_plugin.py',
   'DATA'),
  ('ui\\components\\MainWindowUI.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\components\\MainWindowUI.py',
   'DATA'),
  ('ui\\components\\MenuManager.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\components\\MenuManager.py',
   'DATA'),
  ('ui\\components\\__init__.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\components\\__init__.py',
   'DATA'),
  ('ui\\components\\__pycache__\\MainWindowUI.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\components\\__pycache__\\MainWindowUI.cpython-38.pyc',
   'DATA'),
  ('ui\\components\\__pycache__\\MenuManager.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\components\\__pycache__\\MenuManager.cpython-38.pyc',
   'DATA'),
  ('ui\\components\\__pycache__\\SmoothProgressDialog.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\components\\__pycache__\\SmoothProgressDialog.cpython-38.pyc',
   'DATA'),
  ('ui\\components\\__pycache__\\__init__.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\components\\__pycache__\\__init__.cpython-38.pyc',
   'DATA'),
  ('ui\\controllers\\__init__.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\controllers\\__init__.py',
   'DATA'),
  ('ui\\controllers\\__pycache__\\BatchOperationController.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\controllers\\__pycache__\\BatchOperationController.cpython-38.pyc',
   'DATA'),
  ('ui\\controllers\\__pycache__\\__init__.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\controllers\\__pycache__\\__init__.cpython-38.pyc',
   'DATA'),
  ('ui\\coordinators\\EventCoordinator.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\coordinators\\EventCoordinator.py',
   'DATA'),
  ('ui\\coordinators\\SPIOperationCoordinator.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\coordinators\\SPIOperationCoordinator.py',
   'DATA'),
  ('ui\\coordinators\\__pycache__\\EventCoordinator.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\coordinators\\__pycache__\\EventCoordinator.cpython-313.pyc',
   'DATA'),
  ('ui\\coordinators\\__pycache__\\EventCoordinator.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\coordinators\\__pycache__\\EventCoordinator.cpython-38.pyc',
   'DATA'),
  ('ui\\coordinators\\__pycache__\\SPIOperationCoordinator.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\coordinators\\__pycache__\\SPIOperationCoordinator.cpython-313.pyc',
   'DATA'),
  ('ui\\coordinators\\__pycache__\\SPIOperationCoordinator.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\coordinators\\__pycache__\\SPIOperationCoordinator.cpython-38.pyc',
   'DATA'),
  ('ui\\factories\\ModernToolWindowFactory.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\factories\\ModernToolWindowFactory.py',
   'DATA'),
  ('ui\\factories\\ToolWindowFactory.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\factories\\ToolWindowFactory.py',
   'DATA'),
  ('ui\\factories\\__init__.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\factories\\__init__.py',
   'DATA'),
  ('ui\\factories\\__pycache__\\ModernToolWindowFactory.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\factories\\__pycache__\\ModernToolWindowFactory.cpython-313.pyc',
   'DATA'),
  ('ui\\factories\\__pycache__\\ModernToolWindowFactory.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\factories\\__pycache__\\ModernToolWindowFactory.cpython-38.pyc',
   'DATA'),
  ('ui\\factories\\__pycache__\\ToolWindowFactory.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\factories\\__pycache__\\ToolWindowFactory.cpython-313.pyc',
   'DATA'),
  ('ui\\factories\\__pycache__\\ToolWindowFactory.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\factories\\__pycache__\\ToolWindowFactory.cpython-38.pyc',
   'DATA'),
  ('ui\\factories\\__pycache__\\__init__.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\factories\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('ui\\factories\\__pycache__\\__init__.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\factories\\__pycache__\\__init__.cpython-38.pyc',
   'DATA'),
  ('ui\\forms\\ClkOutputs.ui',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\ClkOutputs.ui',
   'DATA'),
  ('ui\\forms\\PLL1_2.ui',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\PLL1_2.ui',
   'DATA'),
  ('ui\\forms\\Ui_ClkOutputs.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\Ui_ClkOutputs.py',
   'DATA'),
  ('ui\\forms\\Ui_PLL1_2.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\Ui_PLL1_2.py',
   'DATA'),
  ('ui\\forms\\Ui_clkinControl.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\Ui_clkinControl.py',
   'DATA'),
  ('ui\\forms\\Ui_setModes.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\Ui_setModes.py',
   'DATA'),
  ('ui\\forms\\Ui_syncSysref.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\Ui_syncSysref.py',
   'DATA'),
  ('ui\\forms\\__pycache__\\Ui_ClkOutputs.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\__pycache__\\Ui_ClkOutputs.cpython-313.pyc',
   'DATA'),
  ('ui\\forms\\__pycache__\\Ui_ClkOutputs.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\__pycache__\\Ui_ClkOutputs.cpython-38.pyc',
   'DATA'),
  ('ui\\forms\\__pycache__\\Ui_PLL1_2.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\__pycache__\\Ui_PLL1_2.cpython-313.pyc',
   'DATA'),
  ('ui\\forms\\__pycache__\\Ui_PLL1_2.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\__pycache__\\Ui_PLL1_2.cpython-38.pyc',
   'DATA'),
  ('ui\\forms\\__pycache__\\Ui_clkinControl.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\__pycache__\\Ui_clkinControl.cpython-313.pyc',
   'DATA'),
  ('ui\\forms\\__pycache__\\Ui_clkinControl.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\__pycache__\\Ui_clkinControl.cpython-38.pyc',
   'DATA'),
  ('ui\\forms\\__pycache__\\Ui_setModes.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\__pycache__\\Ui_setModes.cpython-313.pyc',
   'DATA'),
  ('ui\\forms\\__pycache__\\Ui_setModes.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\__pycache__\\Ui_setModes.cpython-38.pyc',
   'DATA'),
  ('ui\\forms\\__pycache__\\Ui_syncSysref.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\__pycache__\\Ui_syncSysref.cpython-313.pyc',
   'DATA'),
  ('ui\\forms\\__pycache__\\Ui_syncSysref.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\__pycache__\\Ui_syncSysref.cpython-38.pyc',
   'DATA'),
  ('ui\\forms\\clkinControl.ui',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\clkinControl.ui',
   'DATA'),
  ('ui\\forms\\log\\app.log',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\log\\app.log',
   'DATA'),
  ('ui\\forms\\setModes.ui',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\setModes.ui',
   'DATA'),
  ('ui\\forms\\syncSysref.ui',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\syncSysref.ui',
   'DATA'),
  ('ui\\handlers\\BaseHandler.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\BaseHandler.py',
   'DATA'),
  ('ui\\handlers\\ClkOutputsHandler.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\ClkOutputsHandler.py',
   'DATA'),
  ('ui\\handlers\\ClkinControlHandler.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\ClkinControlHandler.py',
   'DATA'),
  ('ui\\handlers\\ModernBaseHandler.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\ModernBaseHandler.py',
   'DATA'),
  ('ui\\handlers\\ModernClkOutputsHandler.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\ModernClkOutputsHandler.py',
   'DATA'),
  ('ui\\handlers\\ModernClkinControlHandler.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\ModernClkinControlHandler.py',
   'DATA'),
  ('ui\\handlers\\ModernPLLHandler.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\ModernPLLHandler.py',
   'DATA'),
  ('ui\\handlers\\ModernRegisterIOHandler.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\ModernRegisterIOHandler.py',
   'DATA'),
  ('ui\\handlers\\ModernRegisterTableHandler.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\ModernRegisterTableHandler.py',
   'DATA'),
  ('ui\\handlers\\ModernRegisterTreeHandler.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\ModernRegisterTreeHandler.py',
   'DATA'),
  ('ui\\handlers\\ModernSetModesHandler.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\ModernSetModesHandler.py',
   'DATA'),
  ('ui\\handlers\\ModernSyncSysRefHandler.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\ModernSyncSysRefHandler.py',
   'DATA'),
  ('ui\\handlers\\ModernUIEventHandler.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\ModernUIEventHandler.py',
   'DATA'),
  ('ui\\handlers\\RegisterIOHandler.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\RegisterIOHandler.py',
   'DATA'),
  ('ui\\handlers\\RegisterTableHandler.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\RegisterTableHandler.py',
   'DATA'),
  ('ui\\handlers\\RegisterTreeHandler.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\RegisterTreeHandler.py',
   'DATA'),
  ('ui\\handlers\\SetModesHandler.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\SetModesHandler.py',
   'DATA'),
  ('ui\\handlers\\UIEventHandler.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\UIEventHandler.py',
   'DATA'),
  ('ui\\handlers\\__init__.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__init__.py',
   'DATA'),
  ('ui\\handlers\\__pycache__\\BaseHandler.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\BaseHandler.cpython-313.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\BaseHandler.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\BaseHandler.cpython-38.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\ClkinControlHandler.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\ClkinControlHandler.cpython-313.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\ClkinControlHandler.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\ClkinControlHandler.cpython-38.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\ModernBaseHandler.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\ModernBaseHandler.cpython-313.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\ModernBaseHandler.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\ModernBaseHandler.cpython-38.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\ModernClkOutputsHandler.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\ModernClkOutputsHandler.cpython-313.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\ModernClkOutputsHandler.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\ModernClkOutputsHandler.cpython-38.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\ModernClkinControlHandler.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\ModernClkinControlHandler.cpython-313.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\ModernClkinControlHandler.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\ModernClkinControlHandler.cpython-38.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\ModernPLLHandler.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\ModernPLLHandler.cpython-313.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\ModernPLLHandler.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\ModernPLLHandler.cpython-38.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\ModernRegisterIOHandler.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\ModernRegisterIOHandler.cpython-313.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\ModernRegisterIOHandler.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\ModernRegisterIOHandler.cpython-38.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\ModernRegisterTableHandler.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\ModernRegisterTableHandler.cpython-313.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\ModernRegisterTableHandler.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\ModernRegisterTableHandler.cpython-38.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\ModernRegisterTreeHandler.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\ModernRegisterTreeHandler.cpython-313.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\ModernRegisterTreeHandler.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\ModernRegisterTreeHandler.cpython-38.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\ModernSetModesHandler.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\ModernSetModesHandler.cpython-313.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\ModernSetModesHandler.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\ModernSetModesHandler.cpython-38.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\ModernSyncSysRefHandler.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\ModernSyncSysRefHandler.cpython-313.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\ModernSyncSysRefHandler.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\ModernSyncSysRefHandler.cpython-38.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\ModernUIEventHandler.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\ModernUIEventHandler.cpython-313.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\ModernUIEventHandler.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\ModernUIEventHandler.cpython-38.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\RegisterIOHandler.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\RegisterIOHandler.cpython-313.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\RegisterIOHandler.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\RegisterIOHandler.cpython-38.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\RegisterTableHandler.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\RegisterTableHandler.cpython-313.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\RegisterTableHandler.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\RegisterTableHandler.cpython-38.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\RegisterTreeHandler.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\RegisterTreeHandler.cpython-313.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\RegisterTreeHandler.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\RegisterTreeHandler.cpython-38.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\SetModesHandler.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\SetModesHandler.cpython-313.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\SetModesHandler.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\SetModesHandler.cpython-38.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\UIEventHandler.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\UIEventHandler.cpython-313.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\UIEventHandler.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\UIEventHandler.cpython-38.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\__init__.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('ui\\handlers\\__pycache__\\__init__.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\__pycache__\\__init__.cpython-38.pyc',
   'DATA'),
  ('ui\\managers\\ApplicationLifecycleManager.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\ApplicationLifecycleManager.py',
   'DATA'),
  ('ui\\managers\\BatchOperationManager.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\BatchOperationManager.py',
   'DATA'),
  ('ui\\managers\\GlobalEventManager.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\GlobalEventManager.py',
   'DATA'),
  ('ui\\managers\\InitializationManager.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\InitializationManager.py',
   'DATA'),
  ('ui\\managers\\RegisterDisplayManager.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\RegisterDisplayManager.py',
   'DATA'),
  ('ui\\managers\\RegisterOperationManager.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\RegisterOperationManager.py',
   'DATA'),
  ('ui\\managers\\ResourceAndUtilityManager.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\ResourceAndUtilityManager.py',
   'DATA'),
  ('ui\\managers\\SPISignalManager.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\SPISignalManager.py',
   'DATA'),
  ('ui\\managers\\StatusAndConfigManager.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\StatusAndConfigManager.py',
   'DATA'),
  ('ui\\managers\\TabWindowManager.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\TabWindowManager.py',
   'DATA'),
  ('ui\\managers\\ToolWindowManager.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\ToolWindowManager.py',
   'DATA'),
  ('ui\\managers\\UIUtilityManager.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\UIUtilityManager.py',
   'DATA'),
  ('ui\\managers\\__pycache__\\ApplicationLifecycleManager.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\__pycache__\\ApplicationLifecycleManager.cpython-313.pyc',
   'DATA'),
  ('ui\\managers\\__pycache__\\ApplicationLifecycleManager.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\__pycache__\\ApplicationLifecycleManager.cpython-38.pyc',
   'DATA'),
  ('ui\\managers\\__pycache__\\BatchOperationManager.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\__pycache__\\BatchOperationManager.cpython-313.pyc',
   'DATA'),
  ('ui\\managers\\__pycache__\\BatchOperationManager.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\__pycache__\\BatchOperationManager.cpython-38.pyc',
   'DATA'),
  ('ui\\managers\\__pycache__\\GlobalEventManager.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\__pycache__\\GlobalEventManager.cpython-313.pyc',
   'DATA'),
  ('ui\\managers\\__pycache__\\GlobalEventManager.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\__pycache__\\GlobalEventManager.cpython-38.pyc',
   'DATA'),
  ('ui\\managers\\__pycache__\\InitializationManager.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\__pycache__\\InitializationManager.cpython-313.pyc',
   'DATA'),
  ('ui\\managers\\__pycache__\\InitializationManager.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\__pycache__\\InitializationManager.cpython-38.pyc',
   'DATA'),
  ('ui\\managers\\__pycache__\\RegisterDisplayManager.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\__pycache__\\RegisterDisplayManager.cpython-313.pyc',
   'DATA'),
  ('ui\\managers\\__pycache__\\RegisterDisplayManager.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\__pycache__\\RegisterDisplayManager.cpython-38.pyc',
   'DATA'),
  ('ui\\managers\\__pycache__\\RegisterOperationManager.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\__pycache__\\RegisterOperationManager.cpython-313.pyc',
   'DATA'),
  ('ui\\managers\\__pycache__\\RegisterOperationManager.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\__pycache__\\RegisterOperationManager.cpython-38.pyc',
   'DATA'),
  ('ui\\managers\\__pycache__\\ResourceAndUtilityManager.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\__pycache__\\ResourceAndUtilityManager.cpython-313.pyc',
   'DATA'),
  ('ui\\managers\\__pycache__\\ResourceAndUtilityManager.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\__pycache__\\ResourceAndUtilityManager.cpython-38.pyc',
   'DATA'),
  ('ui\\managers\\__pycache__\\SPISignalManager.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\__pycache__\\SPISignalManager.cpython-313.pyc',
   'DATA'),
  ('ui\\managers\\__pycache__\\SPISignalManager.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\__pycache__\\SPISignalManager.cpython-38.pyc',
   'DATA'),
  ('ui\\managers\\__pycache__\\StatusAndConfigManager.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\__pycache__\\StatusAndConfigManager.cpython-313.pyc',
   'DATA'),
  ('ui\\managers\\__pycache__\\StatusAndConfigManager.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\__pycache__\\StatusAndConfigManager.cpython-38.pyc',
   'DATA'),
  ('ui\\managers\\__pycache__\\TabWindowManager.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\__pycache__\\TabWindowManager.cpython-313.pyc',
   'DATA'),
  ('ui\\managers\\__pycache__\\TabWindowManager.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\__pycache__\\TabWindowManager.cpython-38.pyc',
   'DATA'),
  ('ui\\managers\\__pycache__\\ToolWindowManager.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\__pycache__\\ToolWindowManager.cpython-313.pyc',
   'DATA'),
  ('ui\\managers\\__pycache__\\ToolWindowManager.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\__pycache__\\ToolWindowManager.cpython-38.pyc',
   'DATA'),
  ('ui\\managers\\__pycache__\\UIUtilityManager.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\__pycache__\\UIUtilityManager.cpython-313.pyc',
   'DATA'),
  ('ui\\managers\\__pycache__\\UIUtilityManager.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\__pycache__\\UIUtilityManager.cpython-38.pyc',
   'DATA'),
  ('ui\\processors\\RegisterUpdateProcessor.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\processors\\RegisterUpdateProcessor.py',
   'DATA'),
  ('ui\\processors\\__init__.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\processors\\__init__.py',
   'DATA'),
  ('ui\\processors\\__pycache__\\RegisterUpdateProcessor.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\processors\\__pycache__\\RegisterUpdateProcessor.cpython-313.pyc',
   'DATA'),
  ('ui\\processors\\__pycache__\\RegisterUpdateProcessor.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\processors\\__pycache__\\RegisterUpdateProcessor.cpython-38.pyc',
   'DATA'),
  ('ui\\processors\\__pycache__\\__init__.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\processors\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('ui\\processors\\__pycache__\\__init__.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\processors\\__pycache__\\__init__.cpython-38.pyc',
   'DATA'),
  ('ui\\qrc\\PLL1_2.qrc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\qrc\\PLL1_2.qrc',
   'DATA'),
  ('ui\\qrc\\PLL1_2_rc.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\qrc\\PLL1_2_rc.py',
   'DATA'),
  ('ui\\qrc\\PLL1and2.bmp',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\qrc\\PLL1and2.bmp',
   'DATA'),
  ('ui\\qrc\\SYNC&SYSREF.bmp',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\qrc\\SYNC&SYSREF.bmp',
   'DATA'),
  ('ui\\qrc\\clkControl.bmp',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\qrc\\clkControl.bmp',
   'DATA'),
  ('ui\\qrc\\clkinControl.qrc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\qrc\\clkinControl.qrc',
   'DATA'),
  ('ui\\qrc\\clkinControl_rc.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\qrc\\clkinControl_rc.py',
   'DATA'),
  ('ui\\qrc\\clkoutput.qrc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\qrc\\clkoutput.qrc',
   'DATA'),
  ('ui\\qrc\\clkoutput_rc.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\qrc\\clkoutput_rc.py',
   'DATA'),
  ('ui\\qrc\\clockOutputs.bmp',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\qrc\\clockOutputs.bmp',
   'DATA'),
  ('ui\\qrc\\setModes.bmp',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\qrc\\setModes.bmp',
   'DATA'),
  ('ui\\qrc\\setModes.qrc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\qrc\\setModes.qrc',
   'DATA'),
  ('ui\\qrc\\setModes_rc.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\qrc\\setModes_rc.py',
   'DATA'),
  ('ui\\qrc\\syncSysref.qrc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\qrc\\syncSysref.qrc',
   'DATA'),
  ('ui\\qrc\\syncSysref_rc.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\qrc\\syncSysref_rc.py',
   'DATA'),
  ('ui\\resources\\PLL1_2_rc.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\resources\\PLL1_2_rc.py',
   'DATA'),
  ('ui\\resources\\__pycache__\\PLL1_2_rc.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\resources\\__pycache__\\PLL1_2_rc.cpython-313.pyc',
   'DATA'),
  ('ui\\resources\\__pycache__\\PLL1_2_rc.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\resources\\__pycache__\\PLL1_2_rc.cpython-38.pyc',
   'DATA'),
  ('ui\\resources\\__pycache__\\clkinControl_rc.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\resources\\__pycache__\\clkinControl_rc.cpython-313.pyc',
   'DATA'),
  ('ui\\resources\\__pycache__\\clkinControl_rc.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\resources\\__pycache__\\clkinControl_rc.cpython-38.pyc',
   'DATA'),
  ('ui\\resources\\__pycache__\\clkoutput_rc.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\resources\\__pycache__\\clkoutput_rc.cpython-313.pyc',
   'DATA'),
  ('ui\\resources\\__pycache__\\clkoutput_rc.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\resources\\__pycache__\\clkoutput_rc.cpython-38.pyc',
   'DATA'),
  ('ui\\resources\\__pycache__\\setModes_rc.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\resources\\__pycache__\\setModes_rc.cpython-313.pyc',
   'DATA'),
  ('ui\\resources\\__pycache__\\setModes_rc.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\resources\\__pycache__\\setModes_rc.cpython-38.pyc',
   'DATA'),
  ('ui\\resources\\__pycache__\\syncSysref_rc.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\resources\\__pycache__\\syncSysref_rc.cpython-313.pyc',
   'DATA'),
  ('ui\\resources\\__pycache__\\syncSysref_rc.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\resources\\__pycache__\\syncSysref_rc.cpython-38.pyc',
   'DATA'),
  ('ui\\resources\\clkinControl_rc.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\resources\\clkinControl_rc.py',
   'DATA'),
  ('ui\\resources\\clkoutput_rc.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\resources\\clkoutput_rc.py',
   'DATA'),
  ('ui\\resources\\setModes_rc.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\resources\\setModes_rc.py',
   'DATA'),
  ('ui\\resources\\syncSysref_rc.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\resources\\syncSysref_rc.py',
   'DATA'),
  ('ui\\styles\\ProgressBarStyleManager.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\styles\\ProgressBarStyleManager.py',
   'DATA'),
  ('ui\\styles\\__pycache__\\ProgressBarStyleManager.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\styles\\__pycache__\\ProgressBarStyleManager.cpython-38.pyc',
   'DATA'),
  ('ui\\tools\\PluginManagerGUI.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\tools\\PluginManagerGUI.py',
   'DATA'),
  ('ui\\tools\\VersionManagerGUI.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\tools\\VersionManagerGUI.py',
   'DATA'),
  ('ui\\tools\\__pycache__\\PluginManagerGUI.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\tools\\__pycache__\\PluginManagerGUI.cpython-38.pyc',
   'DATA'),
  ('ui\\tools\\__pycache__\\VersionManagerGUI.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\tools\\__pycache__\\VersionManagerGUI.cpython-38.pyc',
   'DATA'),
  ('ui\\tools\\log\\app.log',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\tools\\log\\app.log',
   'DATA'),
  ('ui\\windows\\RegisterMainWindow.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\windows\\RegisterMainWindow.py',
   'DATA'),
  ('ui\\windows\\__pycache__\\RegisterMainWindow.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\windows\\__pycache__\\RegisterMainWindow.cpython-313.pyc',
   'DATA'),
  ('ui\\windows\\__pycache__\\RegisterMainWindow.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\windows\\__pycache__\\RegisterMainWindow.cpython-38.pyc',
   'DATA'),
  ('utils\\CursorUtils.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\utils\\CursorUtils.py',
   'DATA'),
  ('utils\\FSJ048xxx_register.xlsx',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\utils\\FSJ048xxx_register.xlsx',
   'DATA'),
  ('utils\\Log.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\utils\\Log.py',
   'DATA'),
  ('utils\\__pycache__\\CursorUtils.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\utils\\__pycache__\\CursorUtils.cpython-38.pyc',
   'DATA'),
  ('utils\\__pycache__\\Log.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\utils\\__pycache__\\Log.cpython-313.pyc',
   'DATA'),
  ('utils\\__pycache__\\Log.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\utils\\__pycache__\\Log.cpython-38.pyc',
   'DATA'),
  ('utils\\address_utils.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\utils\\address_utils.py',
   'DATA'),
  ('utils\\configFileHandler.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\utils\\configFileHandler.py',
   'DATA'),
  ('utils\\error_handler.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\utils\\error_handler.py',
   'DATA'),
  ('utils\\excelTOjason.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\utils\\excelTOjason.py',
   'DATA'),
  ('utils\\message_strings.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\utils\\message_strings.py',
   'DATA'),
  ('base_library.zip',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\packaging\\build\\build\\base_library.zip',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA')],)
