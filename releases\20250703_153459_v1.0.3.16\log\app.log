2025-07-03 15:37:05,336 - main - [main.py:50] - INFO - 中文支持设置完成，使用字体: Microsoft YaHei
2025-07-03 15:37:05,337 - ConfigurationManager - [ConfigurationManager.py:59] - INFO - 已加载配置文件: C:\Users\<USER>\AppData\Local\Temp\_MEI14362\config\default.json
2025-07-03 15:37:05,337 - ConfigurationManager - [ConfigurationManager.py:85] - DEBUG - 跳过可选配置文件: app.json
2025-07-03 15:37:05,337 - ConfigurationManager - [ConfigurationManager.py:59] - INFO - 已加载配置文件: C:\Users\<USER>\AppData\Local\Temp\_MEI14362\config\local.json
2025-07-03 15:37:05,337 - ConfigurationManager - [ConfigurationManager.py:87] - INFO - 配置加载完成，已加载文件: default.json, local.json
2025-07-03 15:37:05,338 - RegisterMainWindow - [RegisterMainWindow.py:172] - INFO - 配置加载完成
2025-07-03 15:37:05,338 - VersionService - [VersionService.py:75] - INFO - 成功加载版本文件: C:\Users\<USER>\AppData\Local\Temp\_MEI14362\packaging\config\version.json
2025-07-03 15:37:05,338 - RegisterMainWindow - [RegisterMainWindow.py:214] - INFO - 窗口标题已设置为: FSJ04832 寄存器配置工具 v1.0.3.16
2025-07-03 15:37:05,338 - RegisterMainWindow - [RegisterMainWindow.py:183] - INFO - 窗口大小设置为: 1840x1100
2025-07-03 15:37:05,339 - RegisterMainWindow - [RegisterMainWindow.py:200] - INFO - 操作常量已加载: TIMEOUT=5000ms, BATCH_SIZE=50
2025-07-03 15:37:05,339 - DIContainer - [DIContainer.py:46] - INFO - 注册单例实例: main_window
2025-07-03 15:37:05,347 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: spi_service
2025-07-03 15:37:05,347 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: register_manager
2025-07-03 15:37:05,347 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: event_bus
2025-07-03 15:37:05,347 - DIContainer - [DIContainer.py:181] - INFO - 核心服务配置完成
2025-07-03 15:37:05,363 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: initialization_manager
2025-07-03 15:37:05,363 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: register_operation_manager
2025-07-03 15:37:05,363 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: display_manager
2025-07-03 15:37:05,363 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: event_coordinator
2025-07-03 15:37:05,363 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: tool_window_factory
2025-07-03 15:37:05,363 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: register_update_processor
2025-07-03 15:37:05,363 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: lifecycle_manager
2025-07-03 15:37:05,363 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: ui_utility_manager
2025-07-03 15:37:05,363 - DIContainer - [DIContainer.py:210] - INFO - UI管理器服务配置完成
2025-07-03 15:37:05,364 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: batch_manager
2025-07-03 15:37:05,364 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: spi_coordinator
2025-07-03 15:37:05,365 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: ui_event_handler
2025-07-03 15:37:05,365 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: tool_window_manager
2025-07-03 15:37:05,365 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: status_config_manager
2025-07-03 15:37:05,365 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: resource_utility_manager
2025-07-03 15:37:05,365 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: tab_window_manager
2025-07-03 15:37:05,365 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: global_event_manager
2025-07-03 15:37:05,365 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: spi_signal_manager
2025-07-03 15:37:05,365 - DIContainer - [DIContainer.py:243] - INFO - 额外管理器服务配置完成
2025-07-03 15:37:05,365 - RegisterMainWindow - [RegisterMainWindow.py:69] - INFO - 依赖注入容器设置完成
2025-07-03 15:37:05,365 - RegisterMainWindow - [RegisterMainWindow.py:87] - INFO - 管理器依赖注入设置完成
2025-07-03 15:37:05,365 - InitializationManager - [InitializationManager.py:31] - INFO - 正在初始化寄存器配置工具...
2025-07-03 15:37:05,366 - InitializationManager - [InitializationManager.py:40] - INFO - 成功加载寄存器配置文件，包含 125 个寄存器
2025-07-03 15:37:05,368 - spi_service - [spi_service.py:67] - INFO - SPI配置已加载: TIMEOUT=5000ms, RETRY_COUNT=3, RETRY_DELAY=1000ms
2025-07-03 15:37:05,368 - spi_service - [spi_service.py:54] - INFO - SPIService initialized
2025-07-03 15:37:05,368 - spi_service - [spi_service.py:84] - INFO - SPIService initializing...
2025-07-03 15:37:05,368 - spi_service - [spi_service.py:421] - DEBUG - Cleaning up SPI thread...
2025-07-03 15:37:05,368 - spi_service - [spi_service.py:455] - DEBUG - SPI thread resources cleaned.
2025-07-03 15:37:05,368 - spi_service_impl - [spi_service_impl.py:79] - INFO - 已启用模拟模式
2025-07-03 15:37:05,368 - spi_service - [spi_service.py:387] - DEBUG - SPI thread components initialized and signals connected.
2025-07-03 15:37:05,368 - spi_service - [spi_service.py:395] - INFO - Starting SPI thread...
2025-07-03 15:37:05,368 - spi_service - [spi_service.py:111] - INFO - Scanning for available COM ports during initialization...
2025-07-03 15:37:05,369 - spi_service_impl - [spi_service_impl.py:411] - INFO - SPI操作处理线程已启动
2025-07-03 15:37:05,375 - spi_service - [spi_service.py:119] - DEBUG - Found port during init: COM4 - USB 串行设备 (COM4)
2025-07-03 15:37:05,375 - spi_service - [spi_service.py:128] - INFO - Attempting to connect to first available port: COM4
2025-07-03 15:37:05,385 - port_manager - [port_manager.py:123] - INFO - 成功打开端口: COM4
2025-07-03 15:37:05,385 - spiPrivacy - [spiPrivacy.py:75] - INFO - 通过端口管理器成功打开串口: COM4
2025-07-03 15:37:05,385 - spi_service_impl - [spi_service_impl.py:347] - INFO - 成功连接到SPI端口: COM4
2025-07-03 15:37:05,385 - spi_service - [spi_service.py:134] - INFO - Successfully connected to COM4 during initialization. Using hardware mode.
2025-07-03 15:37:05,385 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:05,385 - spi_service_impl - [spi_service_impl.py:81] - INFO - 已禁用模拟模式，使用实际SPI硬件
2025-07-03 15:37:05,386 - spi_service - [spi_service.py:318] - INFO - Simulation mode disabled.
2025-07-03 15:37:05,386 - spi_service - [spi_service.py:357] - INFO - SPI连接状态已更新: 已连接
2025-07-03 15:37:05,386 - spi_service - [spi_service.py:96] - INFO - SPIService initialization complete.
2025-07-03 15:37:05,386 - InitializationManager - [InitializationManager.py:92] - INFO - SPI服务初始化成功，当前模式：硬件模式
2025-07-03 15:37:05,387 - RegisterUpdateBus - [RegisterUpdateBus.py:62] - INFO - RegisterUpdateBus: Initial clock source set based on register default (0x57, CLKin_SEL_MANUAL): ClkIn1
2025-07-03 15:37:05,387 - ModernBaseHandler - [ModernBaseHandler.py:79] - DEBUG - 成功连接到RegisterUpdateBus
2025-07-03 15:37:05,387 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:77] - INFO - 表格配置初始化完成
2025-07-03 15:37:06,033 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:142] - INFO - 位域表格创建完成
2025-07-03 15:37:06,033 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:102] - INFO - 表格UI创建完成
2025-07-03 15:37:06,033 - ModernBaseHandler - [ModernBaseHandler.py:452] - WARNING - _post_init方法已废弃，请使用新的初始化流程
2025-07-03 15:37:06,033 - ModernBaseHandler - [ModernBaseHandler.py:97] - INFO - ModernBaseHandler: 开始完成初始化
2025-07-03 15:37:06,033 - ModernBaseHandler - [ModernBaseHandler.py:112] - DEBUG - 此处理器不需要UI映射，跳过控件映射构建
2025-07-03 15:37:06,034 - ModernBaseHandler - [ModernBaseHandler.py:273] - INFO - ModernRegisterTableHandler: 窗口已居中显示在 (585, 190)，窗口尺寸: 750x700
2025-07-03 15:37:06,034 - ModernBaseHandler - [ModernBaseHandler.py:289] - DEBUG - ModernRegisterTableHandler: 已设置窗口激活功能
2025-07-03 15:37:06,034 - ModernBaseHandler - [ModernBaseHandler.py:242] - INFO - ModernRegisterTableHandler: 设置窗口默认尺寸为 750 x 700，已居中显示
2025-07-03 15:37:06,034 - ModernBaseHandler - [ModernBaseHandler.py:121] - INFO - ModernBaseHandler: 初始化完成
2025-07-03 15:37:06,034 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:51] - INFO - 现代化寄存器表格处理器初始化完成
2025-07-03 15:37:06,034 - ModernBaseHandler - [ModernBaseHandler.py:79] - DEBUG - 成功连接到RegisterUpdateBus
2025-07-03 15:37:06,035 - ModernBaseHandler - [ModernBaseHandler.py:452] - WARNING - _post_init方法已废弃，请使用新的初始化流程
2025-07-03 15:37:06,035 - ModernBaseHandler - [ModernBaseHandler.py:97] - INFO - ModernBaseHandler: 开始完成初始化
2025-07-03 15:37:06,036 - ModernBaseHandler - [ModernBaseHandler.py:112] - DEBUG - 此处理器不需要UI映射，跳过控件映射构建
2025-07-03 15:37:06,036 - ModernBaseHandler - [ModernBaseHandler.py:273] - INFO - ModernRegisterIOHandler: 窗口已居中显示在 (585, 190)，窗口尺寸: 750x700
2025-07-03 15:37:06,036 - ModernBaseHandler - [ModernBaseHandler.py:289] - DEBUG - ModernRegisterIOHandler: 已设置窗口激活功能
2025-07-03 15:37:06,036 - ModernBaseHandler - [ModernBaseHandler.py:242] - INFO - ModernRegisterIOHandler: 设置窗口默认尺寸为 750 x 700，已居中显示
2025-07-03 15:37:06,036 - ModernBaseHandler - [ModernBaseHandler.py:121] - INFO - ModernBaseHandler: 初始化完成
2025-07-03 15:37:06,038 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:78] - INFO - 现代化寄存器IO处理器初始化完成
2025-07-03 15:37:06,038 - ModernBaseHandler - [ModernBaseHandler.py:79] - DEBUG - 成功连接到RegisterUpdateBus
2025-07-03 15:37:06,039 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:81] - DEBUG - 现代化TreeWidget设置最小宽度: 180px，防止布局抖动
2025-07-03 15:37:06,039 - ModernBaseHandler - [ModernBaseHandler.py:452] - WARNING - _post_init方法已废弃，请使用新的初始化流程
2025-07-03 15:37:06,039 - ModernBaseHandler - [ModernBaseHandler.py:97] - INFO - ModernBaseHandler: 开始完成初始化
2025-07-03 15:37:06,039 - ModernBaseHandler - [ModernBaseHandler.py:112] - DEBUG - 此处理器不需要UI映射，跳过控件映射构建
2025-07-03 15:37:06,039 - ModernBaseHandler - [ModernBaseHandler.py:273] - INFO - ModernRegisterTreeHandler: 窗口已居中显示在 (585, 190)，窗口尺寸: 750x700
2025-07-03 15:37:06,039 - ModernBaseHandler - [ModernBaseHandler.py:289] - DEBUG - ModernRegisterTreeHandler: 已设置窗口激活功能
2025-07-03 15:37:06,040 - ModernBaseHandler - [ModernBaseHandler.py:242] - INFO - ModernRegisterTreeHandler: 设置窗口默认尺寸为 750 x 700，已居中显示
2025-07-03 15:37:06,040 - ModernBaseHandler - [ModernBaseHandler.py:121] - INFO - ModernBaseHandler: 初始化完成
2025-07-03 15:37:06,040 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:51] - INFO - 现代化寄存器树处理器初始化完成
2025-07-03 15:37:06,040 - InitializationManager - [InitializationManager.py:145] - INFO - 现代化处理器创建成功
2025-07-03 15:37:06,040 - InitializationManager - [InitializationManager.py:113] - INFO - 使用现代化处理器创建核心处理器
2025-07-03 15:37:06,040 - BatchOperationManager - [BatchOperationManager.py:85] - INFO - 批量操作配置已加载: BATCH_SIZE=50, TIMEOUT=5000ms, UI_UPDATE_INTERVAL=10
2025-07-03 15:37:06,040 - BatchOperationManager - [BatchOperationManager.py:72] - INFO - 使用传统批量操作方式（已优化性能）
2025-07-03 15:37:06,040 - SPIOperationCoordinator - [SPIOperationCoordinator.py:283] - DEBUG - 已连接SPI操作完成信号
2025-07-03 15:37:06,040 - InitializationManager - [InitializationManager.py:182] - INFO - 使用依赖注入创建管理器完成
2025-07-03 15:37:06,265 - MainWindowUI - [MainWindowUI.py:137] - INFO - MainWindowUI: 检查现代化处理器 - IO: True, Table: True
2025-07-03 15:37:06,265 - MainWindowUI - [MainWindowUI.py:138] - INFO - MainWindowUI: IO处理器类型: <class 'ui.handlers.ModernRegisterIOHandler.ModernRegisterIOHandler'>
2025-07-03 15:37:06,265 - MainWindowUI - [MainWindowUI.py:139] - INFO - MainWindowUI: Table处理器类型: <class 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler'>
2025-07-03 15:37:06,265 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:741] - INFO - ModernRegisterIOHandler: get_io_widget() 被调用
2025-07-03 15:37:06,266 - MainWindowUI - [MainWindowUI.py:69] - INFO - MainWindowUI: 使用QSplitter创建稳定的TreeWidget和TableWidget分割布局
2025-07-03 15:37:06,269 - ProgressBarStyleManager - [ProgressBarStyleManager.py:135] - DEBUG - 已为进度条应用绿色样式: default
2025-07-03 15:37:06,271 - MenuManager - [MenuManager.py:330] - INFO - 插件服务尚未可用，跳过工具插件集成（将在插件系统设置完成后执行）
2025-07-03 15:37:06,273 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:128] - INFO - 填充了 125 个寄存器到树视图
2025-07-03 15:37:06,273 - SPISignalManager - [SPISignalManager.py:41] - DEBUG - SPI信号连接完成
2025-07-03 15:37:06,273 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:164] - INFO - 现代化TreeHandler: 选择默认寄存器: 'R0 (0x00)'
2025-07-03 15:37:06,273 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:176] - INFO - 现代化TreeHandler: 树项点击 - 项: 'R0 (0x00)', 地址: 0x00, 列: 0
2025-07-03 15:37:06,274 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:185] - INFO - 现代化TreeHandler: 选择寄存器地址: 0x00 从项 'R0 (0x00)'
2025-07-03 15:37:06,274 - EventCoordinator - [EventCoordinator.py:169] - DEBUG - EventCoordinator: 开始处理寄存器选择 0x00
2025-07-03 15:37:06,274 - EventCoordinator - [EventCoordinator.py:172] - DEBUG - EventCoordinator: 找到显示管理器，调用 handle_register_selection(0x00)
2025-07-03 15:37:06,274 - RegisterDisplayManager - [RegisterDisplayManager.py:209] - DEBUG - DisplayManager: 开始处理寄存器选择 0x00
2025-07-03 15:37:06,274 - RegisterOperationService - [RegisterOperationService.py:420] - INFO - RegisterOperationService: 处理寄存器选择, addr='0x00'
2025-07-03 15:37:06,274 - RegisterOperationService - [RegisterOperationService.py:234] - INFO - RegisterOperationService: 更新UI显示 - 地址: 0x00, 值: 0x1300
2025-07-03 15:37:06,274 - RegisterOperationService - [RegisterOperationService.py:242] - DEBUG - RegisterOperationService: 跳过表格更新，避免重复调用 - 0x00
2025-07-03 15:37:06,274 - RegisterDisplayManager - [RegisterDisplayManager.py:213] - DEBUG - DisplayManager: 寄存器操作服务返回: success=True, reg_num=0, value=0x1300
2025-07-03 15:37:06,274 - RegisterDisplayManager - [RegisterDisplayManager.py:218] - INFO - DisplayManager: 已更新主窗口选中寄存器地址为: 0x00
2025-07-03 15:37:06,274 - RegisterDisplayManager - [RegisterDisplayManager.py:71] - INFO - DisplayManager: 开始更新位字段显示 - 地址: 0x00, 值: 0x1300
2025-07-03 15:37:06,275 - RegisterDisplayManager - [RegisterDisplayManager.py:75] - INFO - DisplayManager: 找到 table_handler: <class 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler'>
2025-07-03 15:37:06,275 - RegisterDisplayManager - [RegisterDisplayManager.py:76] - INFO - DisplayManager: 调用 table_handler.show_bit_fields(0x00, 4864, from_global_update=True)
2025-07-03 15:37:06,275 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:817] - DEBUG - ModernTableHandler: 尝试获取主窗口实例...
2025-07-03 15:37:06,275 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:818] - DEBUG - ModernTableHandler: self类型: <class 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler'>
2025-07-03 15:37:06,275 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:819] - DEBUG - ModernTableHandler: hasattr(self, 'main_window'): False
2025-07-03 15:37:06,275 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:820] - DEBUG - ModernTableHandler: hasattr(self, 'parent'): True
2025-07-03 15:37:06,275 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:829] - DEBUG - ModernTableHandler: parent类型: <class 'ui.windows.RegisterMainWindow.RegisterMainWindow'>
2025-07-03 15:37:06,275 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:836] - DEBUG - ModernTableHandler: parent本身就是主窗口
2025-07-03 15:37:06,275 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:233] - INFO - ModernTableHandler: 开始显示位域信息 - 地址: 0x00, 值: 0x1300
2025-07-03 15:37:06,275 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:246] - INFO - ModernTableHandler: 设置当前寄存器 - 地址: 0x00, 值: 0x1300
2025-07-03 15:37:06,275 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:251] - INFO - ModernTableHandler: 获取到 1 个位域
2025-07-03 15:37:06,275 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:265] - DEBUG - ModernTableHandler: 断开表格信号连接
2025-07-03 15:37:06,275 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:269] - INFO - ModernTableHandler: 开始更新表格内容
2025-07-03 15:37:06,275 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:817] - DEBUG - ModernTableHandler: 尝试获取主窗口实例...
2025-07-03 15:37:06,276 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:818] - DEBUG - ModernTableHandler: self类型: <class 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler'>
2025-07-03 15:37:06,276 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:819] - DEBUG - ModernTableHandler: hasattr(self, 'main_window'): False
2025-07-03 15:37:06,276 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:820] - DEBUG - ModernTableHandler: hasattr(self, 'parent'): True
2025-07-03 15:37:06,276 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:829] - DEBUG - ModernTableHandler: parent类型: <class 'ui.windows.RegisterMainWindow.RegisterMainWindow'>
2025-07-03 15:37:06,276 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:836] - DEBUG - ModernTableHandler: parent本身就是主窗口
2025-07-03 15:37:06,276 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:273] - DEBUG - ModernTableHandler: 重新连接表格信号
2025-07-03 15:37:06,276 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:280] - INFO - ModernTableHandler: 成功显示寄存器 0x00 的 1 个位域
2025-07-03 15:37:06,276 - RegisterDisplayManager - [RegisterDisplayManager.py:78] - INFO - DisplayManager: 位字段显示更新完成
2025-07-03 15:37:06,276 - EventCoordinator - [EventCoordinator.py:174] - DEBUG - EventCoordinator: 显示管理器返回结果: True
2025-07-03 15:37:06,277 - InitializationManager - [InitializationManager.py:334] - INFO - 主窗口初始化完成
2025-07-03 15:37:06,277 - RegisterMainWindow - [RegisterMainWindow.py:247] - INFO - 主窗口点击置顶功能已设置
2025-07-03 15:37:06,280 - DIContainer - [DIContainer.py:46] - INFO - 注册单例实例: plugin_service
2025-07-03 15:37:06,281 - PluginManager - [PluginManager.py:151] - INFO - 添加插件目录: C:\Users\<USER>\AppData\Local\Temp\_MEI14362\plugins
2025-07-03 15:37:06,281 - PluginManager - [PluginManager.py:154] - DEBUG - 跳过不存在的插件目录: _internal/plugins (尝试了 3 个路径)
2025-07-03 15:37:06,281 - PluginManager - [PluginManager.py:151] - INFO - 添加插件目录: C:\Users\<USER>\AppData\Local\Temp\_MEI14362\ui\tools
2025-07-03 15:37:06,281 - PluginManager - [PluginManager.py:154] - DEBUG - 跳过不存在的插件目录: _internal/ui/tools (尝试了 3 个路径)
2025-07-03 15:37:06,282 - PluginManager - [PluginManager.py:209] - DEBUG - 尝试加载插件模块: plugins.clkin_control_plugin (文件: C:\Users\<USER>\AppData\Local\Temp\_MEI14362\plugins\clkin_control_plugin.py)
2025-07-03 15:37:06,282 - PluginManager - [PluginManager.py:239] - DEBUG - 成功导入模块: plugins.clkin_control_plugin
2025-07-03 15:37:06,282 - PluginManager - [PluginManager.py:267] - INFO - 发现插件: 时钟输入控制 v1.0.0
2025-07-03 15:37:06,282 - PluginManager - [PluginManager.py:209] - DEBUG - 尝试加载插件模块: plugins.clk_output_plugin (文件: C:\Users\<USER>\AppData\Local\Temp\_MEI14362\plugins\clk_output_plugin.py)
2025-07-03 15:37:06,283 - PluginManager - [PluginManager.py:239] - DEBUG - 成功导入模块: plugins.clk_output_plugin
2025-07-03 15:37:06,283 - PluginManager - [PluginManager.py:267] - INFO - 发现插件: 时钟输出 v1.0.0
2025-07-03 15:37:06,283 - PluginManager - [PluginManager.py:209] - DEBUG - 尝试加载插件模块: plugins.data_analysis_plugin (文件: C:\Users\<USER>\AppData\Local\Temp\_MEI14362\plugins\data_analysis_plugin.py)
2025-07-03 15:37:06,284 - PluginManager - [PluginManager.py:239] - DEBUG - 成功导入模块: plugins.data_analysis_plugin
2025-07-03 15:37:06,284 - PluginManager - [PluginManager.py:267] - INFO - 发现插件: 数据分析器 v1.0.0
2025-07-03 15:37:06,284 - PluginManager - [PluginManager.py:209] - DEBUG - 尝试加载插件模块: plugins.example_tool_plugin (文件: C:\Users\<USER>\AppData\Local\Temp\_MEI14362\plugins\example_tool_plugin.py)
2025-07-03 15:37:06,287 - PluginManager - [PluginManager.py:239] - DEBUG - 成功导入模块: plugins.example_tool_plugin
2025-07-03 15:37:06,287 - PluginManager - [PluginManager.py:267] - INFO - 发现插件: 示例工具 v1.0.0
2025-07-03 15:37:06,288 - PluginManager - [PluginManager.py:209] - DEBUG - 尝试加载插件模块: plugins.performance_monitor_plugin (文件: C:\Users\<USER>\AppData\Local\Temp\_MEI14362\plugins\performance_monitor_plugin.py)
2025-07-03 15:37:06,325 - performance_monitor_plugin - [performance_monitor_plugin.py:28] - INFO - psutil库可用，将提供完整的系统监控功能
2025-07-03 15:37:06,325 - PluginManager - [PluginManager.py:239] - DEBUG - 成功导入模块: plugins.performance_monitor_plugin
2025-07-03 15:37:06,326 - PluginManager - [PluginManager.py:267] - INFO - 发现插件: 性能监控器 v1.0.0
2025-07-03 15:37:06,326 - PluginManager - [PluginManager.py:209] - DEBUG - 尝试加载插件模块: plugins.pll_control_plugin (文件: C:\Users\<USER>\AppData\Local\Temp\_MEI14362\plugins\pll_control_plugin.py)
2025-07-03 15:37:06,327 - PluginManager - [PluginManager.py:239] - DEBUG - 成功导入模块: plugins.pll_control_plugin
2025-07-03 15:37:06,327 - PluginManager - [PluginManager.py:267] - INFO - 发现插件: PLL控制 v1.0.0
2025-07-03 15:37:06,327 - PluginManager - [PluginManager.py:209] - DEBUG - 尝试加载插件模块: plugins.selective_register_plugin (文件: C:\Users\<USER>\AppData\Local\Temp\_MEI14362\plugins\selective_register_plugin.py)
2025-07-03 15:37:06,330 - PluginManager - [PluginManager.py:239] - DEBUG - 成功导入模块: plugins.selective_register_plugin
2025-07-03 15:37:06,331 - selective_register_config - [selective_register_config.py:43] - INFO - 成功加载插件配置文件: C:\Users\<USER>\AppData\Local\Temp\_MEI14362\config\selective_register_plugin.json
2025-07-03 15:37:06,331 - PluginManager - [PluginManager.py:267] - INFO - 发现插件: 选择性寄存器操作 v1.0.1
2025-07-03 15:37:06,331 - PluginManager - [PluginManager.py:209] - DEBUG - 尝试加载插件模块: plugins.set_modes_plugin (文件: C:\Users\<USER>\AppData\Local\Temp\_MEI14362\plugins\set_modes_plugin.py)
2025-07-03 15:37:06,332 - PluginManager - [PluginManager.py:239] - DEBUG - 成功导入模块: plugins.set_modes_plugin
2025-07-03 15:37:06,332 - PluginManager - [PluginManager.py:267] - INFO - 发现插件: 模式设置 v1.0.0
2025-07-03 15:37:06,332 - PluginManager - [PluginManager.py:209] - DEBUG - 尝试加载插件模块: plugins.sync_sysref_plugin (文件: C:\Users\<USER>\AppData\Local\Temp\_MEI14362\plugins\sync_sysref_plugin.py)
2025-07-03 15:37:06,333 - PluginManager - [PluginManager.py:239] - DEBUG - 成功导入模块: plugins.sync_sysref_plugin
2025-07-03 15:37:06,333 - PluginManager - [PluginManager.py:267] - INFO - 发现插件: 同步系统参考 v1.0.0
2025-07-03 15:37:06,334 - PluginManager - [PluginManager.py:209] - DEBUG - 尝试加载插件模块: plugins.config.selective_register_config (文件: C:\Users\<USER>\AppData\Local\Temp\_MEI14362\plugins\config\selective_register_config.py)
2025-07-03 15:37:06,334 - PluginManager - [PluginManager.py:239] - DEBUG - 成功导入模块: plugins.config.selective_register_config
2025-07-03 15:37:06,335 - PluginManager - [PluginManager.py:202] - WARNING - 无法处理插件文件路径: C:\Users\<USER>\AppData\Local\Temp\_MEI14362\ui\tools\PluginManagerGUI.py
2025-07-03 15:37:06,335 - PluginManager - [PluginManager.py:202] - WARNING - 无法处理插件文件路径: C:\Users\<USER>\AppData\Local\Temp\_MEI14362\ui\tools\VersionManagerGUI.py
2025-07-03 15:37:06,336 - clkin_control_plugin - [clkin_control_plugin.py:59] - INFO - 插件 '时钟输入控制' 初始化完成
2025-07-03 15:37:06,336 - PluginManager - [PluginManager.py:310] - INFO - 插件初始化成功: 时钟输入控制
2025-07-03 15:37:06,336 - clk_output_plugin - [clk_output_plugin.py:59] - INFO - 插件 '时钟输出' 初始化完成
2025-07-03 15:37:06,336 - PluginManager - [PluginManager.py:310] - INFO - 插件初始化成功: 时钟输出
2025-07-03 15:37:06,336 - data_analysis_plugin - [data_analysis_plugin.py:712] - INFO - 插件 '数据分析器' 初始化完成
2025-07-03 15:37:06,337 - PluginManager - [PluginManager.py:310] - INFO - 插件初始化成功: 数据分析器
2025-07-03 15:37:06,337 - example_tool_plugin - [example_tool_plugin.py:308] - INFO - 插件 '示例工具' 初始化完成
2025-07-03 15:37:06,337 - PluginManager - [PluginManager.py:310] - INFO - 插件初始化成功: 示例工具
2025-07-03 15:37:06,337 - performance_monitor_plugin - [performance_monitor_plugin.py:835] - INFO - 插件 '性能监控器' 初始化完成
2025-07-03 15:37:06,337 - performance_monitor_plugin - [performance_monitor_plugin.py:854] - DEBUG - 已连接批量读取开始信号
2025-07-03 15:37:06,337 - performance_monitor_plugin - [performance_monitor_plugin.py:857] - DEBUG - 已连接批量读取完成信号
2025-07-03 15:37:06,337 - performance_monitor_plugin - [performance_monitor_plugin.py:860] - DEBUG - 已连接批量读取进度信号
2025-07-03 15:37:06,337 - performance_monitor_plugin - [performance_monitor_plugin.py:865] - DEBUG - 已连接批量写入开始信号
2025-07-03 15:37:06,337 - performance_monitor_plugin - [performance_monitor_plugin.py:868] - DEBUG - 已连接批量写入完成信号
2025-07-03 15:37:06,337 - performance_monitor_plugin - [performance_monitor_plugin.py:871] - DEBUG - 已连接批量写入进度信号
2025-07-03 15:37:06,338 - performance_monitor_plugin - [performance_monitor_plugin.py:874] - INFO - ✅ 已成功连接到批量操作管理器，性能监控功能已激活
2025-07-03 15:37:06,338 - PluginManager - [PluginManager.py:310] - INFO - 插件初始化成功: 性能监控器
2025-07-03 15:37:06,338 - pll_control_plugin - [pll_control_plugin.py:54] - DEBUG - PLLControlPlugin: 初始化时context是有效对象: RegisterMainWindow
2025-07-03 15:37:06,338 - pll_control_plugin - [pll_control_plugin.py:64] - INFO - 插件 'PLL控制' 初始化完成
2025-07-03 15:37:06,338 - PluginManager - [PluginManager.py:310] - INFO - 插件初始化成功: PLL控制
2025-07-03 15:37:06,338 - selective_register_plugin - [selective_register_plugin.py:977] - INFO - 插件 '选择性寄存器操作' 初始化完成
2025-07-03 15:37:06,338 - PluginManager - [PluginManager.py:310] - INFO - 插件初始化成功: 选择性寄存器操作
2025-07-03 15:37:06,338 - set_modes_plugin - [set_modes_plugin.py:59] - INFO - 插件 '模式设置' 初始化完成
2025-07-03 15:37:06,338 - PluginManager - [PluginManager.py:310] - INFO - 插件初始化成功: 模式设置
2025-07-03 15:37:06,338 - sync_sysref_plugin - [sync_sysref_plugin.py:59] - INFO - 插件 '同步系统参考' 初始化完成
2025-07-03 15:37:06,338 - PluginManager - [PluginManager.py:310] - INFO - 插件初始化成功: 同步系统参考
2025-07-03 15:37:06,338 - PluginManager - [PluginManager.py:349] - DEBUG - 添加工具窗口插件: 时钟输入控制
2025-07-03 15:37:06,338 - PluginManager - [PluginManager.py:349] - DEBUG - 添加工具窗口插件: 时钟输出
2025-07-03 15:37:06,338 - PluginManager - [PluginManager.py:349] - DEBUG - 添加工具窗口插件: 数据分析器
2025-07-03 15:37:06,338 - PluginManager - [PluginManager.py:349] - DEBUG - 添加工具窗口插件: 示例工具
2025-07-03 15:37:06,338 - PluginManager - [PluginManager.py:349] - DEBUG - 添加工具窗口插件: 性能监控器
2025-07-03 15:37:06,339 - PluginManager - [PluginManager.py:349] - DEBUG - 添加工具窗口插件: PLL控制
2025-07-03 15:37:06,339 - PluginManager - [PluginManager.py:349] - DEBUG - 添加工具窗口插件: 选择性寄存器操作
2025-07-03 15:37:06,339 - PluginManager - [PluginManager.py:349] - DEBUG - 添加工具窗口插件: 模式设置
2025-07-03 15:37:06,339 - PluginManager - [PluginManager.py:349] - DEBUG - 添加工具窗口插件: 同步系统参考
2025-07-03 15:37:06,339 - PluginManager - [PluginManager.py:353] - INFO - 找到 9 个工具窗口插件
2025-07-03 15:37:06,339 - PluginMenuService - [PluginMenuService.py:157] - INFO - 找到现有的工具菜单
2025-07-03 15:37:06,339 - PluginMenuService - [PluginMenuService.py:216] - DEBUG - 为插件 '模式设置' 设置快捷键: Ctrl+M
2025-07-03 15:37:06,339 - PluginMenuService - [PluginMenuService.py:242] - INFO - 已将插件 '模式设置' 的动作设置为主窗口属性: set_modes_action
2025-07-03 15:37:06,339 - PluginMenuService - [PluginMenuService.py:253] - INFO - 插件 '模式设置' 已添加到菜单
2025-07-03 15:37:06,340 - MenuClickFixer - [MenuClickFixer.py:43] - INFO - 开始修复动作点击响应: 模式设置(&M)
2025-07-03 15:37:06,340 - MenuClickFixer - [MenuClickFixer.py:83] - INFO - 动作点击响应修复完成: 模式设置(&M)
2025-07-03 15:37:06,340 - PluginMenuService - [PluginMenuService.py:265] - INFO - 已为插件 '模式设置' 应用菜单点击修复
2025-07-03 15:37:06,340 - PluginMenuService - [PluginMenuService.py:72] - INFO - 核心工具插件 '模式设置' 已添加到工具菜单
2025-07-03 15:37:06,340 - PluginMenuService - [PluginMenuService.py:216] - DEBUG - 为插件 '时钟输入控制' 设置快捷键: Ctrl+I
2025-07-03 15:37:06,340 - PluginMenuService - [PluginMenuService.py:242] - INFO - 已将插件 '时钟输入控制' 的动作设置为主窗口属性: clkin_control_action
2025-07-03 15:37:06,340 - PluginMenuService - [PluginMenuService.py:253] - INFO - 插件 '时钟输入控制' 已添加到菜单
2025-07-03 15:37:06,340 - MenuClickFixer - [MenuClickFixer.py:43] - INFO - 开始修复动作点击响应: 时钟输入(&I)
2025-07-03 15:37:06,341 - MenuClickFixer - [MenuClickFixer.py:83] - INFO - 动作点击响应修复完成: 时钟输入(&I)
2025-07-03 15:37:06,341 - PluginMenuService - [PluginMenuService.py:265] - INFO - 已为插件 '时钟输入控制' 应用菜单点击修复
2025-07-03 15:37:06,341 - PluginMenuService - [PluginMenuService.py:72] - INFO - 核心工具插件 '时钟输入控制' 已添加到工具菜单
2025-07-03 15:37:06,341 - PluginMenuService - [PluginMenuService.py:216] - DEBUG - 为插件 'PLL控制' 设置快捷键: Ctrl+P
2025-07-03 15:37:06,341 - PluginMenuService - [PluginMenuService.py:242] - INFO - 已将插件 'PLL控制' 的动作设置为主窗口属性: pll_control_action
2025-07-03 15:37:06,341 - PluginMenuService - [PluginMenuService.py:253] - INFO - 插件 'PLL控制' 已添加到菜单
2025-07-03 15:37:06,341 - MenuClickFixer - [MenuClickFixer.py:43] - INFO - 开始修复动作点击响应: PLL控制(&P)
2025-07-03 15:37:06,341 - MenuClickFixer - [MenuClickFixer.py:83] - INFO - 动作点击响应修复完成: PLL控制(&P)
2025-07-03 15:37:06,341 - PluginMenuService - [PluginMenuService.py:265] - INFO - 已为插件 'PLL控制' 应用菜单点击修复
2025-07-03 15:37:06,341 - PluginMenuService - [PluginMenuService.py:72] - INFO - 核心工具插件 'PLL控制' 已添加到工具菜单
2025-07-03 15:37:06,341 - PluginMenuService - [PluginMenuService.py:216] - DEBUG - 为插件 '同步系统参考' 设置快捷键: Ctrl+R
2025-07-03 15:37:06,341 - PluginMenuService - [PluginMenuService.py:242] - INFO - 已将插件 '同步系统参考' 的动作设置为主窗口属性: sync_sysref_action
2025-07-03 15:37:06,341 - PluginMenuService - [PluginMenuService.py:253] - INFO - 插件 '同步系统参考' 已添加到菜单
2025-07-03 15:37:06,341 - MenuClickFixer - [MenuClickFixer.py:43] - INFO - 开始修复动作点击响应: 同步系统参考(&R)
2025-07-03 15:37:06,341 - MenuClickFixer - [MenuClickFixer.py:83] - INFO - 动作点击响应修复完成: 同步系统参考(&R)
2025-07-03 15:37:06,341 - PluginMenuService - [PluginMenuService.py:265] - INFO - 已为插件 '同步系统参考' 应用菜单点击修复
2025-07-03 15:37:06,342 - PluginMenuService - [PluginMenuService.py:72] - INFO - 核心工具插件 '同步系统参考' 已添加到工具菜单
2025-07-03 15:37:06,342 - PluginMenuService - [PluginMenuService.py:216] - DEBUG - 为插件 '时钟输出' 设置快捷键: Ctrl+O
2025-07-03 15:37:06,342 - PluginMenuService - [PluginMenuService.py:242] - INFO - 已将插件 '时钟输出' 的动作设置为主窗口属性: clk_output_action
2025-07-03 15:37:06,342 - PluginMenuService - [PluginMenuService.py:253] - INFO - 插件 '时钟输出' 已添加到菜单
2025-07-03 15:37:06,342 - MenuClickFixer - [MenuClickFixer.py:43] - INFO - 开始修复动作点击响应: 时钟输出(&O)
2025-07-03 15:37:06,342 - MenuClickFixer - [MenuClickFixer.py:83] - INFO - 动作点击响应修复完成: 时钟输出(&O)
2025-07-03 15:37:06,342 - PluginMenuService - [PluginMenuService.py:265] - INFO - 已为插件 '时钟输出' 应用菜单点击修复
2025-07-03 15:37:06,342 - PluginMenuService - [PluginMenuService.py:72] - INFO - 核心工具插件 '时钟输出' 已添加到工具菜单
2025-07-03 15:37:06,343 - PluginMenuService - [PluginMenuService.py:364] - DEBUG - 已将动作 '模式设置(&M)' 添加到工具栏
2025-07-03 15:37:06,343 - PluginMenuService - [PluginMenuService.py:364] - DEBUG - 已将动作 '时钟输入(&I)' 添加到工具栏
2025-07-03 15:37:06,343 - PluginMenuService - [PluginMenuService.py:364] - DEBUG - 已将动作 'PLL控制(&P)' 添加到工具栏
2025-07-03 15:37:06,343 - PluginMenuService - [PluginMenuService.py:364] - DEBUG - 已将动作 '同步系统参考(&R)' 添加到工具栏
2025-07-03 15:37:06,343 - PluginMenuService - [PluginMenuService.py:364] - DEBUG - 已将动作 '时钟输出(&O)' 添加到工具栏
2025-07-03 15:37:06,343 - PluginMenuService - [PluginMenuService.py:366] - INFO - 已将 5 个动作添加到工具栏
2025-07-03 15:37:06,343 - PluginMenuService - [PluginMenuService.py:79] - INFO - 已将 5 个核心工具插件添加到工具栏
2025-07-03 15:37:06,343 - PluginMenuService - [PluginMenuService.py:81] - INFO - 已将 5 个核心工具插件添加到工具菜单和工具栏
2025-07-03 15:37:06,343 - PluginMenuService - [PluginMenuService.py:127] - INFO - 在设置菜单前创建新的插件菜单
2025-07-03 15:37:06,466 - PluginMenuService - [PluginMenuService.py:188] - INFO - 添加插件管理器菜单项
2025-07-03 15:37:06,466 - PluginMenuService - [PluginMenuService.py:144] - INFO - 插件菜单设置完成
2025-07-03 15:37:06,469 - PluginMenuService - [PluginMenuService.py:253] - INFO - 插件 '数据分析器' 已添加到菜单
2025-07-03 15:37:06,469 - MenuClickFixer - [MenuClickFixer.py:43] - INFO - 开始修复动作点击响应: 数据分析器
2025-07-03 15:37:06,471 - MenuClickFixer - [MenuClickFixer.py:83] - INFO - 动作点击响应修复完成: 数据分析器
2025-07-03 15:37:06,471 - PluginMenuService - [PluginMenuService.py:265] - INFO - 已为插件 '数据分析器' 应用菜单点击修复
2025-07-03 15:37:06,473 - PluginMenuService - [PluginMenuService.py:253] - INFO - 插件 '示例工具' 已添加到菜单
2025-07-03 15:37:06,473 - MenuClickFixer - [MenuClickFixer.py:43] - INFO - 开始修复动作点击响应: 示例工具
2025-07-03 15:37:06,474 - MenuClickFixer - [MenuClickFixer.py:83] - INFO - 动作点击响应修复完成: 示例工具
2025-07-03 15:37:06,474 - PluginMenuService - [PluginMenuService.py:265] - INFO - 已为插件 '示例工具' 应用菜单点击修复
2025-07-03 15:37:06,475 - PluginMenuService - [PluginMenuService.py:253] - INFO - 插件 '性能监控器' 已添加到菜单
2025-07-03 15:37:06,475 - MenuClickFixer - [MenuClickFixer.py:43] - INFO - 开始修复动作点击响应: 性能监控器
2025-07-03 15:37:06,475 - MenuClickFixer - [MenuClickFixer.py:83] - INFO - 动作点击响应修复完成: 性能监控器
2025-07-03 15:37:06,475 - PluginMenuService - [PluginMenuService.py:265] - INFO - 已为插件 '性能监控器' 应用菜单点击修复
2025-07-03 15:37:06,477 - PluginMenuService - [PluginMenuService.py:253] - INFO - 插件 '选择性寄存器操作' 已添加到菜单
2025-07-03 15:37:06,477 - MenuClickFixer - [MenuClickFixer.py:43] - INFO - 开始修复动作点击响应: 选择性寄存器操作
2025-07-03 15:37:06,477 - MenuClickFixer - [MenuClickFixer.py:83] - INFO - 动作点击响应修复完成: 选择性寄存器操作
2025-07-03 15:37:06,477 - PluginMenuService - [PluginMenuService.py:265] - INFO - 已为插件 '选择性寄存器操作' 应用菜单点击修复
2025-07-03 15:37:06,477 - PluginMenuService - [PluginMenuService.py:94] - INFO - 已将 4 个插件添加到插件菜单
2025-07-03 15:37:06,477 - PluginIntegrationService - [PluginIntegrationService.py:86] - INFO - 插件系统初始化完成
2025-07-03 15:37:06,477 - RegisterMainWindow - [RegisterMainWindow.py:134] - INFO - 插件系统设置完成
2025-07-03 15:37:06,503 - InitializationManager - [InitializationManager.py:274] - INFO - InitializationManager: 执行延迟端口刷新，确保UI同步端口状态
2025-07-03 15:37:06,503 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:537] - INFO - ModernRegisterIOHandler: 请求SPI服务刷新端口
2025-07-03 15:37:06,503 - spi_service - [spi_service.py:179] - INFO - Refreshing available COM ports...
2025-07-03 15:37:06,508 - spi_service - [spi_service.py:186] - DEBUG - Found port: COM4 - USB 串行设备 (COM4)
2025-07-03 15:37:06,508 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:545] - DEBUG - ModernRegisterIOHandler: _update_port_combo 被调用，port_combo: <PyQt5.QtWidgets.QComboBox object at 0x000001D25D6F9940>, 类型: <class 'PyQt5.QtWidgets.QComboBox'>
2025-07-03 15:37:06,508 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:564] - INFO - ModernRegisterIOHandler: SPI服务已连接到端口: COM4
2025-07-03 15:37:06,508 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:573] - INFO - ModernRegisterIOHandler: 添加COM端口到下拉框: COM4
2025-07-03 15:37:06,508 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:582] - INFO - ModernRegisterIOHandler: 选择端口: COM4
2025-07-03 15:37:06,508 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:597] - INFO - ModernRegisterIOHandler: SPI服务已连接，UI已同步端口显示
2025-07-03 15:37:06,508 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:615] - INFO - ModernRegisterIOHandler: 触发状态栏更新
2025-07-03 15:37:06,508 - StatusAndConfigManager - [StatusAndConfigManager.py:25] - INFO - StatusAndConfigManager.update_status_bar() 被调用
2025-07-03 15:37:06,508 - StatusAndConfigManager - [StatusAndConfigManager.py:30] - INFO - 获取到SPI服务状态: {'connected': True, 'port': 'COM4', 'mode': 'hardware', 'last_error': None, 'retry_count': 0}
2025-07-03 15:37:06,508 - StatusAndConfigManager - [StatusAndConfigManager.py:33] - INFO - 原始模式文本: 'hardware'
2025-07-03 15:37:06,509 - StatusAndConfigManager - [StatusAndConfigManager.py:60] - INFO - 状态栏已更新: 模式=硬件模式, 端口=COM4
2025-07-03 15:37:06,509 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:545] - DEBUG - ModernRegisterIOHandler: _update_port_combo 被调用，port_combo: <PyQt5.QtWidgets.QComboBox object at 0x000001D25D6F9940>, 类型: <class 'PyQt5.QtWidgets.QComboBox'>
2025-07-03 15:37:06,509 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:552] - INFO - ModernRegisterIOHandler: 端口信息未变化，跳过重复更新
2025-07-03 15:37:06,509 - InitializationManager - [InitializationManager.py:277] - INFO - InitializationManager: 端口刷新完成
2025-07-03 15:37:06,553 - RegisterMainWindow - [RegisterMainWindow.py:387] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-03 15:37:06,553 - RegisterMainWindow - [RegisterMainWindow.py:387] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-03 15:37:06,558 - RegisterMainWindow - [RegisterMainWindow.py:406] - DEBUG - 主窗口强制置顶完成
2025-07-03 15:37:06,558 - RegisterMainWindow - [RegisterMainWindow.py:311] - DEBUG - 主窗口已通过焦点置顶
2025-07-03 15:37:06,569 - RegisterMainWindow - [RegisterMainWindow.py:406] - DEBUG - 主窗口强制置顶完成
2025-07-03 15:37:06,569 - RegisterMainWindow - [RegisterMainWindow.py:315] - DEBUG - 主窗口已通过激活事件置顶
2025-07-03 15:37:06,773 - InitializationManager - [InitializationManager.py:289] - INFO - InitializationManager: 执行延迟状态栏更新
2025-07-03 15:37:06,774 - StatusAndConfigManager - [StatusAndConfigManager.py:25] - INFO - StatusAndConfigManager.update_status_bar() 被调用
2025-07-03 15:37:06,774 - StatusAndConfigManager - [StatusAndConfigManager.py:30] - INFO - 获取到SPI服务状态: {'connected': True, 'port': 'COM4', 'mode': 'hardware', 'last_error': None, 'retry_count': 0}
2025-07-03 15:37:06,774 - StatusAndConfigManager - [StatusAndConfigManager.py:33] - INFO - 原始模式文本: 'hardware'
2025-07-03 15:37:06,774 - StatusAndConfigManager - [StatusAndConfigManager.py:60] - INFO - 状态栏已更新: 模式=硬件模式, 端口=COM4
2025-07-03 15:37:07,278 - RegisterMainWindow - [RegisterMainWindow.py:281] - INFO - 已为寄存器树安装点击事件过滤器
2025-07-03 15:37:07,279 - RegisterMainWindow - [RegisterMainWindow.py:286] - INFO - 已为寄存器表格安装点击事件过滤器
2025-07-03 15:37:07,549 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: PLL控制(&P)
2025-07-03 15:37:08,697 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: PLL控制(&P)
2025-07-03 15:37:17,130 - BatchOperationManager - [BatchOperationManager.py:552] - DEBUG - 开始强制清理批量操作状态
2025-07-03 15:37:17,130 - BatchOperationManager - [BatchOperationManager.py:483] - DEBUG - 断开读取信号时出现异常（可能未连接）: 'method' object is not connected
2025-07-03 15:37:17,130 - BatchOperationManager - [BatchOperationManager.py:495] - DEBUG - 断开写入信号时出现异常（可能未连接）: 'method' object is not connected
2025-07-03 15:37:17,130 - spi_service_impl - [spi_service_impl.py:241] - INFO - 操作队列已清空
2025-07-03 15:37:17,130 - spi_service - [spi_service.py:289] - INFO - SPI操作队列已清空
2025-07-03 15:37:17,130 - BatchOperationManager - [BatchOperationManager.py:575] - DEBUG - 清空了SPI操作队列
2025-07-03 15:37:17,132 - BatchOperationManager - [BatchOperationManager.py:588] - DEBUG - 强制清理完成
2025-07-03 15:37:17,132 - BatchOperationManager - [BatchOperationManager.py:1206] - DEBUG - 已禁用主窗口更新
2025-07-03 15:37:17,132 - BatchOperationManager - [BatchOperationManager.py:1213] - DEBUG - 已禁用中央控件更新
2025-07-03 15:37:17,132 - BatchOperationManager - [BatchOperationManager.py:1215] - DEBUG - UI更新已完全暂停，防止批量操作期间的布局抖动
2025-07-03 15:37:17,132 - BatchOperationManager - [BatchOperationManager.py:270] - INFO - 🚀 开始批量读取: 总共 125 个寄存器
2025-07-03 15:37:17,132 - BatchOperationManager - [BatchOperationManager.py:271] - DEBUG - 寄存器列表: ['0x00', '0x02', '0x03', '0x04', '0x05', '0x06', '0x0C', '0x10', '0x11', '0x12']...
2025-07-03 15:37:17,132 - BatchOperationManager - [BatchOperationManager.py:272] - DEBUG - 寄存器地址范围: 0x00 到 0xDA
2025-07-03 15:37:17,149 - BatchOperationManager - [BatchOperationManager.py:287] - INFO - 📦 创建了 3 个批次，每批最多 50 个寄存器
2025-07-03 15:37:17,150 - BatchOperationManager - [BatchOperationManager.py:289] - DEBUG - 批次 1: 50 个寄存器 - ['0x00', '0x02', '0x03']...
2025-07-03 15:37:17,150 - BatchOperationManager - [BatchOperationManager.py:289] - DEBUG - 批次 2: 50 个寄存器 - ['0x3B', '0x3C', '0x3D']...
2025-07-03 15:37:17,150 - BatchOperationManager - [BatchOperationManager.py:289] - DEBUG - 批次 3: 25 个寄存器 - ['0x7E', '0x83', '0x87']...
2025-07-03 15:37:17,150 - BatchOperationManager - [BatchOperationManager.py:454] - INFO - 已连接批量读取操作信号到BatchOperationManager
2025-07-03 15:37:17,150 - BatchOperationManager - [BatchOperationManager.py:646] - DEBUG - 开始处理批次中的 50 个寄存器
2025-07-03 15:37:17,150 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x00
2025-07-03 15:37:17,150 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x00
2025-07-03 15:37:17,150 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x02
2025-07-03 15:37:17,150 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x02
2025-07-03 15:37:17,150 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x03
2025-07-03 15:37:17,150 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x03
2025-07-03 15:37:17,151 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x04
2025-07-03 15:37:17,151 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x04
2025-07-03 15:37:17,151 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x05
2025-07-03 15:37:17,151 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x05
2025-07-03 15:37:17,151 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x06
2025-07-03 15:37:17,151 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x06
2025-07-03 15:37:17,151 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x0C
2025-07-03 15:37:17,151 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x0C
2025-07-03 15:37:17,151 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x10
2025-07-03 15:37:17,151 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x10
2025-07-03 15:37:17,151 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x11
2025-07-03 15:37:17,151 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x11
2025-07-03 15:37:17,151 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x12
2025-07-03 15:37:17,151 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x12
2025-07-03 15:37:17,151 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x13
2025-07-03 15:37:17,151 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x13
2025-07-03 15:37:17,151 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x14
2025-07-03 15:37:17,152 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x14
2025-07-03 15:37:17,152 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x15
2025-07-03 15:37:17,152 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x15
2025-07-03 15:37:17,152 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x16
2025-07-03 15:37:17,152 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x16
2025-07-03 15:37:17,152 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x17
2025-07-03 15:37:17,152 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x17
2025-07-03 15:37:17,152 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x18
2025-07-03 15:37:17,152 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x18
2025-07-03 15:37:17,152 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x19
2025-07-03 15:37:17,152 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x19
2025-07-03 15:37:17,152 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x1A
2025-07-03 15:37:17,152 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x1A
2025-07-03 15:37:17,152 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x1B
2025-07-03 15:37:17,152 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x1B
2025-07-03 15:37:17,152 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x1C
2025-07-03 15:37:17,152 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x1C
2025-07-03 15:37:17,152 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x1D
2025-07-03 15:37:17,152 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x1D
2025-07-03 15:37:17,152 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x1E
2025-07-03 15:37:17,152 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x1E
2025-07-03 15:37:17,152 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x1F
2025-07-03 15:37:17,153 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x1F
2025-07-03 15:37:17,153 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x20
2025-07-03 15:37:17,153 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x20
2025-07-03 15:37:17,153 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x21
2025-07-03 15:37:17,153 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x21
2025-07-03 15:37:17,153 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x22
2025-07-03 15:37:17,153 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x22
2025-07-03 15:37:17,153 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x23
2025-07-03 15:37:17,153 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x23
2025-07-03 15:37:17,153 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x24
2025-07-03 15:37:17,153 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x24
2025-07-03 15:37:17,153 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x25
2025-07-03 15:37:17,153 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x25
2025-07-03 15:37:17,153 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x26
2025-07-03 15:37:17,153 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x26
2025-07-03 15:37:17,153 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x27
2025-07-03 15:37:17,153 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x27
2025-07-03 15:37:17,153 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x28
2025-07-03 15:37:17,153 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x28
2025-07-03 15:37:17,153 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x29
2025-07-03 15:37:17,153 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x29
2025-07-03 15:37:17,153 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x2A
2025-07-03 15:37:17,153 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x2A
2025-07-03 15:37:17,153 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x2B
2025-07-03 15:37:17,154 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x2B
2025-07-03 15:37:17,154 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x2C
2025-07-03 15:37:17,154 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x2C
2025-07-03 15:37:17,154 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x2D
2025-07-03 15:37:17,154 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x2D
2025-07-03 15:37:17,154 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x2E
2025-07-03 15:37:17,154 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x2E
2025-07-03 15:37:17,154 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x2F
2025-07-03 15:37:17,154 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x2F
2025-07-03 15:37:17,154 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x30
2025-07-03 15:37:17,154 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x30
2025-07-03 15:37:17,154 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x31
2025-07-03 15:37:17,154 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x31
2025-07-03 15:37:17,154 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x32
2025-07-03 15:37:17,154 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x32
2025-07-03 15:37:17,154 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x33
2025-07-03 15:37:17,154 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x33
2025-07-03 15:37:17,154 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x34
2025-07-03 15:37:17,154 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x34
2025-07-03 15:37:17,154 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x35
2025-07-03 15:37:17,154 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x35
2025-07-03 15:37:17,154 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x36
2025-07-03 15:37:17,154 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x36
2025-07-03 15:37:17,154 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x37
2025-07-03 15:37:17,154 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x37
2025-07-03 15:37:17,154 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x38
2025-07-03 15:37:17,154 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x38
2025-07-03 15:37:17,155 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x39
2025-07-03 15:37:17,155 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x39
2025-07-03 15:37:17,155 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x3A
2025-07-03 15:37:17,155 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x3A
2025-07-03 15:37:17,155 - BatchOperationManager - [BatchOperationManager.py:673] - DEBUG - 批次中的所有寄存器读取请求已发送，等待回调
2025-07-03 15:37:17,167 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作1开始: read 0x00 None
2025-07-03 15:37:17,167 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 1
2025-07-03 15:37:17,167 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,167 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x00，操作ID: 1
2025-07-03 15:37:17,167 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 0004 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,179 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x00 → 原始字节 1300 → 整数值 4864 (0x1300)
2025-07-03 15:37:17,179 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x00，操作ID: 1
2025-07-03 15:37:17,179 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作1数据解析: 4864 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,179 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作1成功: 地址0x00 读取值0x1300
2025-07-03 15:37:17,179 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作2开始: read 0x02 None
2025-07-03 15:37:17,179 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x00 = 0x1300 (读取)
2025-07-03 15:37:17,179 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 2
2025-07-03 15:37:17,180 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x00, 值=0x1300
2025-07-03 15:37:17,180 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x00 = 0x1300
2025-07-03 15:37:17,180 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,180 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x00 = 0x1300
2025-07-03 15:37:17,180 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x02，操作ID: 2
2025-07-03 15:37:17,180 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x00
2025-07-03 15:37:17,180 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 0204 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,181 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x00 的UI更新
2025-07-03 15:37:17,192 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x02 → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:17,192 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x02，操作ID: 2
2025-07-03 15:37:17,192 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作2数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,192 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作2成功: 地址0x02 读取值0x0000
2025-07-03 15:37:17,192 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作3开始: read 0x03 None
2025-07-03 15:37:17,192 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 3
2025-07-03 15:37:17,192 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,193 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x02 = 0x0000 (读取)
2025-07-03 15:37:17,193 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x03，操作ID: 3
2025-07-03 15:37:17,193 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x02, 值=0x0000
2025-07-03 15:37:17,193 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 0304 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,193 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x02 = 0x0000
2025-07-03 15:37:17,194 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x02 = 0x0000
2025-07-03 15:37:17,194 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x02
2025-07-03 15:37:17,194 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x02 的UI更新
2025-07-03 15:37:17,205 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x03 → 原始字节 0006 → 整数值 6 (0x0006)
2025-07-03 15:37:17,205 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x03，操作ID: 3
2025-07-03 15:37:17,205 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作3数据解析: 6 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,206 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作3成功: 地址0x03 读取值0x0006
2025-07-03 15:37:17,206 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作4开始: read 0x04 None
2025-07-03 15:37:17,206 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 4
2025-07-03 15:37:17,206 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,206 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x04，操作ID: 4
2025-07-03 15:37:17,206 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x03 = 0x0006 (读取)
2025-07-03 15:37:17,206 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 0404 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,207 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x03, 值=0x0006
2025-07-03 15:37:17,207 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x03 = 0x0006
2025-07-03 15:37:17,207 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x03 = 0x0006
2025-07-03 15:37:17,208 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x03
2025-07-03 15:37:17,208 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x03 的UI更新
2025-07-03 15:37:17,218 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x04 → 原始字节 D163 → 整数值 53603 (0xD163)
2025-07-03 15:37:17,218 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x04，操作ID: 4
2025-07-03 15:37:17,218 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作4数据解析: 53603 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,218 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作4成功: 地址0x04 读取值0xD163
2025-07-03 15:37:17,218 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作5开始: read 0x05 None
2025-07-03 15:37:17,218 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x04 = 0xD163 (读取)
2025-07-03 15:37:17,219 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 5
2025-07-03 15:37:17,219 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x04, 值=0xD163
2025-07-03 15:37:17,219 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,219 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x04 = 0xD163
2025-07-03 15:37:17,219 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x05，操作ID: 5
2025-07-03 15:37:17,220 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x04 = 0xD163
2025-07-03 15:37:17,220 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 0504 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,221 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x04
2025-07-03 15:37:17,221 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x04 的UI更新
2025-07-03 15:37:17,232 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x05 → 原始字节 0101 → 整数值 257 (0x0101)
2025-07-03 15:37:17,232 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x05，操作ID: 5
2025-07-03 15:37:17,232 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作5数据解析: 257 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,232 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作5成功: 地址0x05 读取值0x0101
2025-07-03 15:37:17,232 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作6开始: read 0x06 None
2025-07-03 15:37:17,232 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 6
2025-07-03 15:37:17,232 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x05 = 0x0101 (读取)
2025-07-03 15:37:17,232 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,232 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0x05, 值=0101
2025-07-03 15:37:17,232 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x06，操作ID: 6
2025-07-03 15:37:17,232 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x05, 值=0x0101
2025-07-03 15:37:17,233 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x05 = 0x0101
2025-07-03 15:37:17,233 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 0604 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,233 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x05 = 0x0101
2025-07-03 15:37:17,233 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x05
2025-07-03 15:37:17,233 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x05 的UI更新
2025-07-03 15:37:17,245 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x06 → 原始字节 0070 → 整数值 112 (0x0070)
2025-07-03 15:37:17,245 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x06，操作ID: 6
2025-07-03 15:37:17,246 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作6数据解析: 112 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,246 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作6成功: 地址0x06 读取值0x0070
2025-07-03 15:37:17,246 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作7开始: read 0x0C None
2025-07-03 15:37:17,246 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x06 = 0x0070 (读取)
2025-07-03 15:37:17,246 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 7
2025-07-03 15:37:17,247 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x06, 值=0x0070
2025-07-03 15:37:17,247 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x06 = 0x0070
2025-07-03 15:37:17,247 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,248 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x06 = 0x0070
2025-07-03 15:37:17,248 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x06
2025-07-03 15:37:17,249 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x06 的UI更新
2025-07-03 15:37:17,248 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x0C，操作ID: 7
2025-07-03 15:37:17,249 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 0C04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,276 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x0C → 原始字节 5104 → 整数值 20740 (0x5104)
2025-07-03 15:37:17,276 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x0C，操作ID: 7
2025-07-03 15:37:17,277 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作7数据解析: 20740 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,277 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作7成功: 地址0x0C 读取值0x5104
2025-07-03 15:37:17,277 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x0C = 0x5104 (读取)
2025-07-03 15:37:17,277 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作8开始: read 0x10 None
2025-07-03 15:37:17,278 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x0C, 值=0x5104
2025-07-03 15:37:17,278 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 8
2025-07-03 15:37:17,278 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x0C = 0x5104
2025-07-03 15:37:17,279 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,279 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x0C = 0x5104
2025-07-03 15:37:17,280 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x10，操作ID: 8
2025-07-03 15:37:17,280 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x0C
2025-07-03 15:37:17,280 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 1004 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,281 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x0C 的UI更新
2025-07-03 15:37:17,303 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x10 → 原始字节 0002 → 整数值 2 (0x0002)
2025-07-03 15:37:17,304 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x10，操作ID: 8
2025-07-03 15:37:17,304 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作8数据解析: 2 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,304 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作8成功: 地址0x10 读取值0x0002
2025-07-03 15:37:17,304 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x10 = 0x0002 (读取)
2025-07-03 15:37:17,304 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作9开始: read 0x11 None
2025-07-03 15:37:17,305 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x10, 值=0x0002
2025-07-03 15:37:17,305 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 9
2025-07-03 15:37:17,305 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x10 = 0x0002
2025-07-03 15:37:17,305 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,306 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x10 = 0x0002
2025-07-03 15:37:17,306 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x11，操作ID: 9
2025-07-03 15:37:17,306 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x10
2025-07-03 15:37:17,306 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 1104 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,307 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x10 的UI更新
2025-07-03 15:37:17,332 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x11 → 原始字节 000A → 整数值 10 (0x000A)
2025-07-03 15:37:17,333 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x11，操作ID: 9
2025-07-03 15:37:17,333 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作9数据解析: 10 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,333 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作9成功: 地址0x11 读取值0x000A
2025-07-03 15:37:17,333 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作10开始: read 0x12 None
2025-07-03 15:37:17,333 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x11 = 0x000A (读取)
2025-07-03 15:37:17,334 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 10
2025-07-03 15:37:17,334 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x11, 值=0x000A
2025-07-03 15:37:17,334 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,335 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x11 = 0x000A
2025-07-03 15:37:17,335 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x12，操作ID: 10
2025-07-03 15:37:17,335 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x11 = 0x000A
2025-07-03 15:37:17,336 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 1204 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,336 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x11
2025-07-03 15:37:17,336 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x11 的UI更新
2025-07-03 15:37:17,363 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x12 → 原始字节 0080 → 整数值 128 (0x0080)
2025-07-03 15:37:17,363 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x12，操作ID: 10
2025-07-03 15:37:17,363 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作10数据解析: 128 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,363 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作10成功: 地址0x12 读取值0x0080
2025-07-03 15:37:17,363 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作11开始: read 0x13 None
2025-07-03 15:37:17,364 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x12 = 0x0080 (读取)
2025-07-03 15:37:17,364 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 11
2025-07-03 15:37:17,364 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0x12, 值=0080
2025-07-03 15:37:17,364 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x12, 值=0x0080
2025-07-03 15:37:17,365 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x12 = 0x0080
2025-07-03 15:37:17,364 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,365 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x12 = 0x0080
2025-07-03 15:37:17,365 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x12
2025-07-03 15:37:17,365 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x12 的UI更新
2025-07-03 15:37:17,365 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x13，操作ID: 11
2025-07-03 15:37:17,366 - BatchOperationManager - [BatchOperationManager.py:752] - INFO - 📊 读取进度更新: 10/125
2025-07-03 15:37:17,366 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 1304 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,367 - BatchOperationManager - [BatchOperationManager.py:783] - DEBUG - 🔍 批次检查: 已完成=10, 当前批次开始=0, 当前批次结束=50
2025-07-03 15:37:17,368 - BatchOperationManager - [BatchOperationManager.py:810] - DEBUG - ⏳ 当前批次未完成，继续等待 (已完成: 10, 批次结束: 50)
2025-07-03 15:37:17,394 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x13 → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:17,394 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x13，操作ID: 11
2025-07-03 15:37:17,394 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作11数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,395 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作11成功: 地址0x13 读取值0x0000
2025-07-03 15:37:17,395 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x13 = 0x0000 (读取)
2025-07-03 15:37:17,395 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作12开始: read 0x14 None
2025-07-03 15:37:17,395 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x13, 值=0x0000
2025-07-03 15:37:17,395 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x13 = 0x0000
2025-07-03 15:37:17,395 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 12
2025-07-03 15:37:17,396 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x13 = 0x0000
2025-07-03 15:37:17,396 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x13
2025-07-03 15:37:17,396 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,396 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x13 的UI更新
2025-07-03 15:37:17,396 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x14，操作ID: 12
2025-07-03 15:37:17,397 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 1404 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,409 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x14 → 原始字节 0018 → 整数值 24 (0x0018)
2025-07-03 15:37:17,410 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x14，操作ID: 12
2025-07-03 15:37:17,410 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作12数据解析: 24 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,410 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作12成功: 地址0x14 读取值0x0018
2025-07-03 15:37:17,410 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x14 = 0x0018 (读取)
2025-07-03 15:37:17,410 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作13开始: read 0x15 None
2025-07-03 15:37:17,410 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0x14, 值=0018
2025-07-03 15:37:17,411 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 13
2025-07-03 15:37:17,411 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x14, 值=0x0018
2025-07-03 15:37:17,411 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,411 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x14 = 0x0018
2025-07-03 15:37:17,411 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x15，操作ID: 13
2025-07-03 15:37:17,411 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x14 = 0x0018
2025-07-03 15:37:17,412 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 1504 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,412 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x14
2025-07-03 15:37:17,413 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x14 的UI更新
2025-07-03 15:37:17,425 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x15 → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:17,425 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x15，操作ID: 13
2025-07-03 15:37:17,426 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作13数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,426 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作13成功: 地址0x15 读取值0x0000
2025-07-03 15:37:17,427 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x15 = 0x0000 (读取)
2025-07-03 15:37:17,427 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作14开始: read 0x16 None
2025-07-03 15:37:17,427 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x15, 值=0x0000
2025-07-03 15:37:17,428 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 14
2025-07-03 15:37:17,428 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x15 = 0x0000
2025-07-03 15:37:17,428 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,429 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x15 = 0x0000
2025-07-03 15:37:17,429 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x16，操作ID: 14
2025-07-03 15:37:17,429 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x15
2025-07-03 15:37:17,430 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x15 的UI更新
2025-07-03 15:37:17,429 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 1604 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,456 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x16 → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:17,456 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x16，操作ID: 14
2025-07-03 15:37:17,456 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作14数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,457 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作14成功: 地址0x16 读取值0x0000
2025-07-03 15:37:17,457 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作15开始: read 0x17 None
2025-07-03 15:37:17,457 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 15
2025-07-03 15:37:17,457 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,457 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x17，操作ID: 15
2025-07-03 15:37:17,457 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 1704 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,457 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x16 = 0x0000 (读取)
2025-07-03 15:37:17,457 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0x16, 值=0000
2025-07-03 15:37:17,458 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x16, 值=0x0000
2025-07-03 15:37:17,458 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x16 = 0x0000
2025-07-03 15:37:17,458 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x16 = 0x0000
2025-07-03 15:37:17,458 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x16
2025-07-03 15:37:17,458 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x16 的UI更新
2025-07-03 15:37:17,472 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x17 → 原始字节 0051 → 整数值 81 (0x0051)
2025-07-03 15:37:17,472 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x17，操作ID: 15
2025-07-03 15:37:17,472 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作15数据解析: 81 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,473 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作15成功: 地址0x17 读取值0x0051
2025-07-03 15:37:17,473 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作16开始: read 0x18 None
2025-07-03 15:37:17,473 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 16
2025-07-03 15:37:17,473 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,473 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x17 = 0x0051 (读取)
2025-07-03 15:37:17,473 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x18，操作ID: 16
2025-07-03 15:37:17,473 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0x17, 值=0051
2025-07-03 15:37:17,473 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 1804 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,473 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x17, 值=0x0051
2025-07-03 15:37:17,474 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x17 = 0x0051
2025-07-03 15:37:17,474 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x17 = 0x0051
2025-07-03 15:37:17,474 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x17
2025-07-03 15:37:17,474 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x17 的UI更新
2025-07-03 15:37:17,487 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x18 → 原始字节 0004 → 整数值 4 (0x0004)
2025-07-03 15:37:17,487 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x18，操作ID: 16
2025-07-03 15:37:17,487 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作16数据解析: 4 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,487 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作16成功: 地址0x18 读取值0x0004
2025-07-03 15:37:17,487 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x18 = 0x0004 (读取)
2025-07-03 15:37:17,488 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作17开始: read 0x19 None
2025-07-03 15:37:17,488 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x18, 值=0x0004
2025-07-03 15:37:17,488 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 17
2025-07-03 15:37:17,488 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x18 = 0x0004
2025-07-03 15:37:17,488 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,488 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x18 = 0x0004
2025-07-03 15:37:17,488 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x19，操作ID: 17
2025-07-03 15:37:17,488 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x18
2025-07-03 15:37:17,488 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 1904 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,488 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x18 的UI更新
2025-07-03 15:37:17,502 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x19 → 原始字节 000A → 整数值 10 (0x000A)
2025-07-03 15:37:17,502 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x19，操作ID: 17
2025-07-03 15:37:17,502 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作17数据解析: 10 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,503 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作17成功: 地址0x19 读取值0x000A
2025-07-03 15:37:17,503 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x19 = 0x000A (读取)
2025-07-03 15:37:17,503 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作18开始: read 0x1A None
2025-07-03 15:37:17,503 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x19, 值=0x000A
2025-07-03 15:37:17,503 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 18
2025-07-03 15:37:17,503 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x19 = 0x000A
2025-07-03 15:37:17,503 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,504 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x19 = 0x000A
2025-07-03 15:37:17,504 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x1A，操作ID: 18
2025-07-03 15:37:17,504 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x19
2025-07-03 15:37:17,504 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 1A04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,504 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x19 的UI更新
2025-07-03 15:37:17,518 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x1A → 原始字节 0080 → 整数值 128 (0x0080)
2025-07-03 15:37:17,518 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x1A，操作ID: 18
2025-07-03 15:37:17,518 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作18数据解析: 128 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,518 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作18成功: 地址0x1A 读取值0x0080
2025-07-03 15:37:17,518 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x1A = 0x0080 (读取)
2025-07-03 15:37:17,518 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作19开始: read 0x1B None
2025-07-03 15:37:17,519 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0x1A, 值=0080
2025-07-03 15:37:17,519 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 19
2025-07-03 15:37:17,519 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x1A, 值=0x0080
2025-07-03 15:37:17,519 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,519 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x1A = 0x0080
2025-07-03 15:37:17,519 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x1B，操作ID: 19
2025-07-03 15:37:17,520 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x1A = 0x0080
2025-07-03 15:37:17,520 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 1B04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,520 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x1A
2025-07-03 15:37:17,520 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x1A 的UI更新
2025-07-03 15:37:17,545 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x1B → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:17,546 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x1B，操作ID: 19
2025-07-03 15:37:17,546 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作19数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,546 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作19成功: 地址0x1B 读取值0x0000
2025-07-03 15:37:17,546 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x1B = 0x0000 (读取)
2025-07-03 15:37:17,547 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作20开始: read 0x1C None
2025-07-03 15:37:17,548 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x1B, 值=0x0000
2025-07-03 15:37:17,548 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x1B = 0x0000
2025-07-03 15:37:17,548 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 20
2025-07-03 15:37:17,549 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x1B = 0x0000
2025-07-03 15:37:17,549 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,549 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x1B
2025-07-03 15:37:17,550 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x1C，操作ID: 20
2025-07-03 15:37:17,550 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x1B 的UI更新
2025-07-03 15:37:17,550 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 1C04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,576 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x1C → 原始字节 0010 → 整数值 16 (0x0010)
2025-07-03 15:37:17,576 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x1C，操作ID: 20
2025-07-03 15:37:17,577 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作20数据解析: 16 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,577 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作20成功: 地址0x1C 读取值0x0010
2025-07-03 15:37:17,577 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x1C = 0x0010 (读取)
2025-07-03 15:37:17,577 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作21开始: read 0x1D None
2025-07-03 15:37:17,578 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x1C, 值=0x0010
2025-07-03 15:37:17,578 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 21
2025-07-03 15:37:17,579 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x1C = 0x0010
2025-07-03 15:37:17,579 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,580 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x1C = 0x0010
2025-07-03 15:37:17,581 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x1D，操作ID: 21
2025-07-03 15:37:17,581 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x1C
2025-07-03 15:37:17,581 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 1D04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,581 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x1C 的UI更新
2025-07-03 15:37:17,582 - BatchOperationManager - [BatchOperationManager.py:752] - INFO - 📊 读取进度更新: 20/125
2025-07-03 15:37:17,583 - BatchOperationManager - [BatchOperationManager.py:783] - DEBUG - 🔍 批次检查: 已完成=20, 当前批次开始=0, 当前批次结束=50
2025-07-03 15:37:17,583 - BatchOperationManager - [BatchOperationManager.py:810] - DEBUG - ⏳ 当前批次未完成，继续等待 (已完成: 20, 批次结束: 50)
2025-07-03 15:37:17,607 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x1D → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:17,607 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x1D，操作ID: 21
2025-07-03 15:37:17,608 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作21数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,608 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作21成功: 地址0x1D 读取值0x0000
2025-07-03 15:37:17,608 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x1D = 0x0000 (读取)
2025-07-03 15:37:17,608 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作22开始: read 0x1E None
2025-07-03 15:37:17,609 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x1D, 值=0x0000
2025-07-03 15:37:17,609 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x1D = 0x0000
2025-07-03 15:37:17,609 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 22
2025-07-03 15:37:17,609 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x1D = 0x0000
2025-07-03 15:37:17,610 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x1D
2025-07-03 15:37:17,610 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,610 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x1D 的UI更新
2025-07-03 15:37:17,610 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x1E，操作ID: 22
2025-07-03 15:37:17,611 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 1E04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,623 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x1E → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:17,623 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x1E，操作ID: 22
2025-07-03 15:37:17,623 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作22数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,624 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作22成功: 地址0x1E 读取值0x0000
2025-07-03 15:37:17,624 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作23开始: read 0x1F None
2025-07-03 15:37:17,624 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 23
2025-07-03 15:37:17,624 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,625 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x1E = 0x0000 (读取)
2025-07-03 15:37:17,625 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x1F，操作ID: 23
2025-07-03 15:37:17,626 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0x1E, 值=0000
2025-07-03 15:37:17,626 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 1F04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,626 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x1E, 值=0x0000
2025-07-03 15:37:17,627 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x1E = 0x0000
2025-07-03 15:37:17,627 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x1E = 0x0000
2025-07-03 15:37:17,627 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x1E
2025-07-03 15:37:17,628 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x1E 的UI更新
2025-07-03 15:37:17,637 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x1F → 原始字节 000E → 整数值 14 (0x000E)
2025-07-03 15:37:17,637 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x1F，操作ID: 23
2025-07-03 15:37:17,637 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作23数据解析: 14 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,638 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作23成功: 地址0x1F 读取值0x000E
2025-07-03 15:37:17,638 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x1F = 0x000E (读取)
2025-07-03 15:37:17,638 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作24开始: read 0x20 None
2025-07-03 15:37:17,638 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0x1F, 值=000E
2025-07-03 15:37:17,639 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 24
2025-07-03 15:37:17,639 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x1F, 值=0x000E
2025-07-03 15:37:17,639 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,639 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x1F = 0x000E
2025-07-03 15:37:17,639 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x20，操作ID: 24
2025-07-03 15:37:17,640 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x1F = 0x000E
2025-07-03 15:37:17,640 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 2004 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,640 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x1F
2025-07-03 15:37:17,641 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x1F 的UI更新
2025-07-03 15:37:17,653 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x20 → 原始字节 0008 → 整数值 8 (0x0008)
2025-07-03 15:37:17,653 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x20，操作ID: 24
2025-07-03 15:37:17,654 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作24数据解析: 8 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,654 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作24成功: 地址0x20 读取值0x0008
2025-07-03 15:37:17,654 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x20 = 0x0008 (读取)
2025-07-03 15:37:17,655 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作25开始: read 0x21 None
2025-07-03 15:37:17,655 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x20, 值=0x0008
2025-07-03 15:37:17,655 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 25
2025-07-03 15:37:17,656 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x20 = 0x0008
2025-07-03 15:37:17,656 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,657 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x20 = 0x0008
2025-07-03 15:37:17,657 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x21，操作ID: 25
2025-07-03 15:37:17,657 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x20
2025-07-03 15:37:17,657 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 2104 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,658 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x20 的UI更新
2025-07-03 15:37:17,669 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x21 → 原始字节 000A → 整数值 10 (0x000A)
2025-07-03 15:37:17,669 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x21，操作ID: 25
2025-07-03 15:37:17,669 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作25数据解析: 10 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,669 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作25成功: 地址0x21 读取值0x000A
2025-07-03 15:37:17,670 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x21 = 0x000A (读取)
2025-07-03 15:37:17,670 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作26开始: read 0x22 None
2025-07-03 15:37:17,670 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x21, 值=0x000A
2025-07-03 15:37:17,670 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 26
2025-07-03 15:37:17,671 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x21 = 0x000A
2025-07-03 15:37:17,671 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,671 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x21 = 0x000A
2025-07-03 15:37:17,671 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x22，操作ID: 26
2025-07-03 15:37:17,672 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x21
2025-07-03 15:37:17,672 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 2204 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,672 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x21 的UI更新
2025-07-03 15:37:17,695 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x22 → 原始字节 0080 → 整数值 128 (0x0080)
2025-07-03 15:37:17,695 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x22，操作ID: 26
2025-07-03 15:37:17,696 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作26数据解析: 128 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,697 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作26成功: 地址0x22 读取值0x0080
2025-07-03 15:37:17,697 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x22 = 0x0080 (读取)
2025-07-03 15:37:17,698 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作27开始: read 0x23 None
2025-07-03 15:37:17,698 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x22, 值=0x0080
2025-07-03 15:37:17,698 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 27
2025-07-03 15:37:17,699 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x22 = 0x0080
2025-07-03 15:37:17,699 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,699 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x22 = 0x0080
2025-07-03 15:37:17,700 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x23，操作ID: 27
2025-07-03 15:37:17,700 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x22
2025-07-03 15:37:17,700 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 2304 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,700 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x22 的UI更新
2025-07-03 15:37:17,726 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x23 → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:17,727 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x23，操作ID: 27
2025-07-03 15:37:17,727 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作27数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,727 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作27成功: 地址0x23 读取值0x0000
2025-07-03 15:37:17,727 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作28开始: read 0x24 None
2025-07-03 15:37:17,728 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x23 = 0x0000 (读取)
2025-07-03 15:37:17,728 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 28
2025-07-03 15:37:17,728 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x23, 值=0x0000
2025-07-03 15:37:17,729 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,730 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x23 = 0x0000
2025-07-03 15:37:17,730 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x24，操作ID: 28
2025-07-03 15:37:17,730 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x23 = 0x0000
2025-07-03 15:37:17,731 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 2404 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,731 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x23
2025-07-03 15:37:17,732 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x23 的UI更新
2025-07-03 15:37:17,757 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x24 → 原始字节 0010 → 整数值 16 (0x0010)
2025-07-03 15:37:17,757 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x24，操作ID: 28
2025-07-03 15:37:17,757 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作28数据解析: 16 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,757 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作28成功: 地址0x24 读取值0x0010
2025-07-03 15:37:17,757 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x24 = 0x0010 (读取)
2025-07-03 15:37:17,758 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作29开始: read 0x25 None
2025-07-03 15:37:17,758 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x24, 值=0x0010
2025-07-03 15:37:17,759 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x24 = 0x0010
2025-07-03 15:37:17,759 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 29
2025-07-03 15:37:17,759 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x24 = 0x0010
2025-07-03 15:37:17,759 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x24
2025-07-03 15:37:17,759 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,760 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x24 的UI更新
2025-07-03 15:37:17,760 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x25，操作ID: 29
2025-07-03 15:37:17,760 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 2504 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,772 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x25 → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:17,772 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x25，操作ID: 29
2025-07-03 15:37:17,772 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作29数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,773 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作29成功: 地址0x25 读取值0x0000
2025-07-03 15:37:17,773 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作30开始: read 0x26 None
2025-07-03 15:37:17,773 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 30
2025-07-03 15:37:17,773 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,774 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x25 = 0x0000 (读取)
2025-07-03 15:37:17,774 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x26，操作ID: 30
2025-07-03 15:37:17,774 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x25, 值=0x0000
2025-07-03 15:37:17,775 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 2604 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,775 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x25 = 0x0000
2025-07-03 15:37:17,776 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x25 = 0x0000
2025-07-03 15:37:17,776 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x25
2025-07-03 15:37:17,776 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x25 的UI更新
2025-07-03 15:37:17,788 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x26 → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:17,788 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x26，操作ID: 30
2025-07-03 15:37:17,788 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作30数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,788 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作30成功: 地址0x26 读取值0x0000
2025-07-03 15:37:17,788 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作31开始: read 0x27 None
2025-07-03 15:37:17,788 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 31
2025-07-03 15:37:17,789 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x26 = 0x0000 (读取)
2025-07-03 15:37:17,789 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,789 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0x26, 值=0000
2025-07-03 15:37:17,789 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x27，操作ID: 31
2025-07-03 15:37:17,789 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x26, 值=0x0000
2025-07-03 15:37:17,790 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 2704 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,790 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x26 = 0x0000
2025-07-03 15:37:17,790 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x26 = 0x0000
2025-07-03 15:37:17,791 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x26
2025-07-03 15:37:17,791 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x26 的UI更新
2025-07-03 15:37:17,791 - BatchOperationManager - [BatchOperationManager.py:752] - INFO - 📊 读取进度更新: 30/125
2025-07-03 15:37:17,792 - BatchOperationManager - [BatchOperationManager.py:783] - DEBUG - 🔍 批次检查: 已完成=30, 当前批次开始=0, 当前批次结束=50
2025-07-03 15:37:17,792 - BatchOperationManager - [BatchOperationManager.py:810] - DEBUG - ⏳ 当前批次未完成，继续等待 (已完成: 30, 批次结束: 50)
2025-07-03 15:37:17,803 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x27 → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:17,803 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x27，操作ID: 31
2025-07-03 15:37:17,803 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作31数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,804 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作31成功: 地址0x27 读取值0x0000
2025-07-03 15:37:17,804 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作32开始: read 0x28 None
2025-07-03 15:37:17,804 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x27 = 0x0000 (读取)
2025-07-03 15:37:17,804 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 32
2025-07-03 15:37:17,804 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x27, 值=0x0000
2025-07-03 15:37:17,804 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,804 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x27 = 0x0000
2025-07-03 15:37:17,804 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x28，操作ID: 32
2025-07-03 15:37:17,805 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x27 = 0x0000
2025-07-03 15:37:17,805 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 2804 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,805 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x27
2025-07-03 15:37:17,805 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x27 的UI更新
2025-07-03 15:37:17,818 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x28 → 原始字节 0008 → 整数值 8 (0x0008)
2025-07-03 15:37:17,818 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x28，操作ID: 32
2025-07-03 15:37:17,818 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作32数据解析: 8 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,819 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作32成功: 地址0x28 读取值0x0008
2025-07-03 15:37:17,819 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x28 = 0x0008 (读取)
2025-07-03 15:37:17,819 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作33开始: read 0x29 None
2025-07-03 15:37:17,819 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x28, 值=0x0008
2025-07-03 15:37:17,819 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 33
2025-07-03 15:37:17,819 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x28 = 0x0008
2025-07-03 15:37:17,819 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,819 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x28 = 0x0008
2025-07-03 15:37:17,820 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x29，操作ID: 33
2025-07-03 15:37:17,820 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x28
2025-07-03 15:37:17,820 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 2904 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,820 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x28 的UI更新
2025-07-03 15:37:17,846 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x29 → 原始字节 000A → 整数值 10 (0x000A)
2025-07-03 15:37:17,847 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x29，操作ID: 33
2025-07-03 15:37:17,847 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作33数据解析: 10 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,847 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作33成功: 地址0x29 读取值0x000A
2025-07-03 15:37:17,848 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作34开始: read 0x2A None
2025-07-03 15:37:17,848 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 34
2025-07-03 15:37:17,848 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,848 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x2A，操作ID: 34
2025-07-03 15:37:17,849 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x29 = 0x000A (读取)
2025-07-03 15:37:17,849 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 2A04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,849 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x29, 值=0x000A
2025-07-03 15:37:17,850 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x29 = 0x000A
2025-07-03 15:37:17,851 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x29 = 0x000A
2025-07-03 15:37:17,851 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x29
2025-07-03 15:37:17,851 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x29 的UI更新
2025-07-03 15:37:17,861 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x2A → 原始字节 0080 → 整数值 128 (0x0080)
2025-07-03 15:37:17,862 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x2A，操作ID: 34
2025-07-03 15:37:17,862 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作34数据解析: 128 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,862 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作34成功: 地址0x2A 读取值0x0080
2025-07-03 15:37:17,863 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x2A = 0x0080 (读取)
2025-07-03 15:37:17,864 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作35开始: read 0x2B None
2025-07-03 15:37:17,864 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0x2A, 值=0080
2025-07-03 15:37:17,865 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 35
2025-07-03 15:37:17,865 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x2A, 值=0x0080
2025-07-03 15:37:17,866 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x2A = 0x0080
2025-07-03 15:37:17,866 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,866 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x2A = 0x0080
2025-07-03 15:37:17,867 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x2A
2025-07-03 15:37:17,867 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x2B，操作ID: 35
2025-07-03 15:37:17,867 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x2A 的UI更新
2025-07-03 15:37:17,867 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 2B04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,893 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x2B → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:17,893 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x2B，操作ID: 35
2025-07-03 15:37:17,894 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作35数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,894 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作35成功: 地址0x2B 读取值0x0000
2025-07-03 15:37:17,894 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x2B = 0x0000 (读取)
2025-07-03 15:37:17,895 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作36开始: read 0x2C None
2025-07-03 15:37:17,895 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x2B, 值=0x0000
2025-07-03 15:37:17,896 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 36
2025-07-03 15:37:17,896 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x2B = 0x0000
2025-07-03 15:37:17,896 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,896 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x2B = 0x0000
2025-07-03 15:37:17,898 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x2C，操作ID: 36
2025-07-03 15:37:17,898 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x2B
2025-07-03 15:37:17,898 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 2C04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,898 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x2B 的UI更新
2025-07-03 15:37:17,924 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x2C → 原始字节 0010 → 整数值 16 (0x0010)
2025-07-03 15:37:17,925 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x2C，操作ID: 36
2025-07-03 15:37:17,925 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作36数据解析: 16 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,925 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作36成功: 地址0x2C 读取值0x0010
2025-07-03 15:37:17,925 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作37开始: read 0x2D None
2025-07-03 15:37:17,925 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 37
2025-07-03 15:37:17,925 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,926 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x2C = 0x0010 (读取)
2025-07-03 15:37:17,927 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x2D，操作ID: 37
2025-07-03 15:37:17,927 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x2C, 值=0x0010
2025-07-03 15:37:17,928 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x2C = 0x0010
2025-07-03 15:37:17,928 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 2D04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,929 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x2C = 0x0010
2025-07-03 15:37:17,930 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x2C
2025-07-03 15:37:17,930 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x2C 的UI更新
2025-07-03 15:37:17,940 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x2D → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:17,940 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x2D，操作ID: 37
2025-07-03 15:37:17,941 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作37数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,941 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作37成功: 地址0x2D 读取值0x0000
2025-07-03 15:37:17,941 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作38开始: read 0x2E None
2025-07-03 15:37:17,941 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x2D = 0x0000 (读取)
2025-07-03 15:37:17,941 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 38
2025-07-03 15:37:17,942 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x2D, 值=0x0000
2025-07-03 15:37:17,942 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,943 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x2D = 0x0000
2025-07-03 15:37:17,943 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x2E，操作ID: 38
2025-07-03 15:37:17,944 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x2D = 0x0000
2025-07-03 15:37:17,944 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 2E04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,944 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x2D
2025-07-03 15:37:17,945 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x2D 的UI更新
2025-07-03 15:37:17,955 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x2E → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:17,955 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x2E，操作ID: 38
2025-07-03 15:37:17,955 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作38数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,956 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作38成功: 地址0x2E 读取值0x0000
2025-07-03 15:37:17,956 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x2E = 0x0000 (读取)
2025-07-03 15:37:17,956 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作39开始: read 0x2F None
2025-07-03 15:37:17,956 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0x2E, 值=0000
2025-07-03 15:37:17,956 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 39
2025-07-03 15:37:17,956 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x2E, 值=0x0000
2025-07-03 15:37:17,957 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,957 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x2E = 0x0000
2025-07-03 15:37:17,957 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x2F，操作ID: 39
2025-07-03 15:37:17,957 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x2E = 0x0000
2025-07-03 15:37:17,958 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 2F04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,958 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x2E
2025-07-03 15:37:17,958 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x2E 的UI更新
2025-07-03 15:37:17,971 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x2F → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:17,971 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x2F，操作ID: 39
2025-07-03 15:37:17,972 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作39数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,972 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作39成功: 地址0x2F 读取值0x0000
2025-07-03 15:37:17,972 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x2F = 0x0000 (读取)
2025-07-03 15:37:17,972 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作40开始: read 0x30 None
2025-07-03 15:37:17,973 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0x2F, 值=0000
2025-07-03 15:37:17,973 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 40
2025-07-03 15:37:17,974 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x2F, 值=0x0000
2025-07-03 15:37:17,974 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:17,974 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x2F = 0x0000
2025-07-03 15:37:17,974 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x30，操作ID: 40
2025-07-03 15:37:17,975 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x2F = 0x0000
2025-07-03 15:37:17,975 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 3004 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:17,976 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x2F
2025-07-03 15:37:17,976 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x2F 的UI更新
2025-07-03 15:37:17,998 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x30 → 原始字节 0008 → 整数值 8 (0x0008)
2025-07-03 15:37:17,998 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x30，操作ID: 40
2025-07-03 15:37:17,998 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作40数据解析: 8 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:17,998 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作40成功: 地址0x30 读取值0x0008
2025-07-03 15:37:17,999 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x30 = 0x0008 (读取)
2025-07-03 15:37:17,999 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作41开始: read 0x31 None
2025-07-03 15:37:17,999 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x30, 值=0x0008
2025-07-03 15:37:18,000 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x30 = 0x0008
2025-07-03 15:37:18,000 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 41
2025-07-03 15:37:18,000 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x30 = 0x0008
2025-07-03 15:37:18,001 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x30
2025-07-03 15:37:18,001 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,001 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x30 的UI更新
2025-07-03 15:37:18,002 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x31，操作ID: 41
2025-07-03 15:37:18,002 - BatchOperationManager - [BatchOperationManager.py:752] - INFO - 📊 读取进度更新: 40/125
2025-07-03 15:37:18,002 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 3104 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,003 - BatchOperationManager - [BatchOperationManager.py:783] - DEBUG - 🔍 批次检查: 已完成=40, 当前批次开始=0, 当前批次结束=50
2025-07-03 15:37:18,004 - BatchOperationManager - [BatchOperationManager.py:810] - DEBUG - ⏳ 当前批次未完成，继续等待 (已完成: 40, 批次结束: 50)
2025-07-03 15:37:18,029 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x31 → 原始字节 000A → 整数值 10 (0x000A)
2025-07-03 15:37:18,029 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x31，操作ID: 41
2025-07-03 15:37:18,029 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作41数据解析: 10 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,029 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作41成功: 地址0x31 读取值0x000A
2025-07-03 15:37:18,029 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作42开始: read 0x32 None
2025-07-03 15:37:18,029 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x31 = 0x000A (读取)
2025-07-03 15:37:18,031 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 42
2025-07-03 15:37:18,031 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,031 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x31, 值=0x000A
2025-07-03 15:37:18,031 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x32，操作ID: 42
2025-07-03 15:37:18,031 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x31 = 0x000A
2025-07-03 15:37:18,032 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 3204 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,032 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x31 = 0x000A
2025-07-03 15:37:18,032 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x31
2025-07-03 15:37:18,032 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x31 的UI更新
2025-07-03 15:37:18,044 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x32 → 原始字节 0080 → 整数值 128 (0x0080)
2025-07-03 15:37:18,044 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x32，操作ID: 42
2025-07-03 15:37:18,045 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作42数据解析: 128 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,045 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作42成功: 地址0x32 读取值0x0080
2025-07-03 15:37:18,045 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作43开始: read 0x33 None
2025-07-03 15:37:18,045 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 43
2025-07-03 15:37:18,045 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,047 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x32 = 0x0080 (读取)
2025-07-03 15:37:18,047 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x32, 值=0x0080
2025-07-03 15:37:18,047 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x33，操作ID: 43
2025-07-03 15:37:18,047 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x32 = 0x0080
2025-07-03 15:37:18,047 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 3304 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,049 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x32 = 0x0080
2025-07-03 15:37:18,049 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x32
2025-07-03 15:37:18,050 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x32 的UI更新
2025-07-03 15:37:18,076 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x33 → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:18,077 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x33，操作ID: 43
2025-07-03 15:37:18,077 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作43数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,077 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作43成功: 地址0x33 读取值0x0000
2025-07-03 15:37:18,077 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作44开始: read 0x34 None
2025-07-03 15:37:18,078 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x33 = 0x0000 (读取)
2025-07-03 15:37:18,078 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 44
2025-07-03 15:37:18,078 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x33, 值=0x0000
2025-07-03 15:37:18,079 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x33 = 0x0000
2025-07-03 15:37:18,079 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,080 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x33 = 0x0000
2025-07-03 15:37:18,081 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x34，操作ID: 44
2025-07-03 15:37:18,081 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x33
2025-07-03 15:37:18,081 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x33 的UI更新
2025-07-03 15:37:18,081 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 3404 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,107 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x34 → 原始字节 0010 → 整数值 16 (0x0010)
2025-07-03 15:37:18,107 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x34，操作ID: 44
2025-07-03 15:37:18,107 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作44数据解析: 16 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,108 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作44成功: 地址0x34 读取值0x0010
2025-07-03 15:37:18,109 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作45开始: read 0x35 None
2025-07-03 15:37:18,109 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 45
2025-07-03 15:37:18,109 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,109 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x35，操作ID: 45
2025-07-03 15:37:18,109 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 3504 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,110 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x34 = 0x0010 (读取)
2025-07-03 15:37:18,110 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x34, 值=0x0010
2025-07-03 15:37:18,110 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x34 = 0x0010
2025-07-03 15:37:18,111 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x34 = 0x0010
2025-07-03 15:37:18,111 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x34
2025-07-03 15:37:18,111 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x34 的UI更新
2025-07-03 15:37:18,122 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x35 → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:18,122 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x35，操作ID: 45
2025-07-03 15:37:18,122 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作45数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,123 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作45成功: 地址0x35 读取值0x0000
2025-07-03 15:37:18,123 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x35 = 0x0000 (读取)
2025-07-03 15:37:18,123 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作46开始: read 0x36 None
2025-07-03 15:37:18,124 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x35, 值=0x0000
2025-07-03 15:37:18,124 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 46
2025-07-03 15:37:18,124 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x35 = 0x0000
2025-07-03 15:37:18,124 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,125 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x35 = 0x0000
2025-07-03 15:37:18,125 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x36，操作ID: 46
2025-07-03 15:37:18,125 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x35
2025-07-03 15:37:18,126 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 3604 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,126 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x35 的UI更新
2025-07-03 15:37:18,138 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x36 → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:18,138 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x36，操作ID: 46
2025-07-03 15:37:18,138 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作46数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,139 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作46成功: 地址0x36 读取值0x0000
2025-07-03 15:37:18,139 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x36 = 0x0000 (读取)
2025-07-03 15:37:18,139 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作47开始: read 0x37 None
2025-07-03 15:37:18,139 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0x36, 值=0000
2025-07-03 15:37:18,140 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 47
2025-07-03 15:37:18,140 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x36, 值=0x0000
2025-07-03 15:37:18,140 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,140 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x36 = 0x0000
2025-07-03 15:37:18,141 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x37，操作ID: 47
2025-07-03 15:37:18,141 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x36 = 0x0000
2025-07-03 15:37:18,141 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 3704 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,142 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x36
2025-07-03 15:37:18,142 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x36 的UI更新
2025-07-03 15:37:18,154 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x37 → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:18,155 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x37，操作ID: 47
2025-07-03 15:37:18,155 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作47数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,155 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作47成功: 地址0x37 读取值0x0000
2025-07-03 15:37:18,155 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x37 = 0x0000 (读取)
2025-07-03 15:37:18,155 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作48开始: read 0x38 None
2025-07-03 15:37:18,155 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x37, 值=0x0000
2025-07-03 15:37:18,156 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 48
2025-07-03 15:37:18,156 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x37 = 0x0000
2025-07-03 15:37:18,156 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,156 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x37 = 0x0000
2025-07-03 15:37:18,157 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x38，操作ID: 48
2025-07-03 15:37:18,157 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x37
2025-07-03 15:37:18,157 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 3804 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,157 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x37 的UI更新
2025-07-03 15:37:18,169 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x38 → 原始字节 0008 → 整数值 8 (0x0008)
2025-07-03 15:37:18,169 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x38，操作ID: 48
2025-07-03 15:37:18,169 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作48数据解析: 8 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,170 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作48成功: 地址0x38 读取值0x0008
2025-07-03 15:37:18,170 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x38 = 0x0008 (读取)
2025-07-03 15:37:18,170 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作49开始: read 0x39 None
2025-07-03 15:37:18,170 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x38, 值=0x0008
2025-07-03 15:37:18,171 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x38 = 0x0008
2025-07-03 15:37:18,171 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 49
2025-07-03 15:37:18,171 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x38 = 0x0008
2025-07-03 15:37:18,172 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,172 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x38
2025-07-03 15:37:18,172 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x39，操作ID: 49
2025-07-03 15:37:18,172 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x38 的UI更新
2025-07-03 15:37:18,172 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 3904 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,185 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x39 → 原始字节 000A → 整数值 10 (0x000A)
2025-07-03 15:37:18,185 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x39，操作ID: 49
2025-07-03 15:37:18,185 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作49数据解析: 10 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,186 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作49成功: 地址0x39 读取值0x000A
2025-07-03 15:37:18,186 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作50开始: read 0x3A None
2025-07-03 15:37:18,186 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x39 = 0x000A (读取)
2025-07-03 15:37:18,186 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 50
2025-07-03 15:37:18,187 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x39, 值=0x000A
2025-07-03 15:37:18,187 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,187 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x39 = 0x000A
2025-07-03 15:37:18,188 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x3A，操作ID: 50
2025-07-03 15:37:18,188 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x39 = 0x000A
2025-07-03 15:37:18,189 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 3A04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,189 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x39
2025-07-03 15:37:18,189 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x39 的UI更新
2025-07-03 15:37:18,212 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x3A → 原始字节 0080 → 整数值 128 (0x0080)
2025-07-03 15:37:18,212 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x3A，操作ID: 50
2025-07-03 15:37:18,214 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作50数据解析: 128 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,214 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作50成功: 地址0x3A 读取值0x0080
2025-07-03 15:37:18,215 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x3A = 0x0080 (读取)
2025-07-03 15:37:18,215 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x3A, 值=0x0080
2025-07-03 15:37:18,215 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x3A = 0x0080
2025-07-03 15:37:18,215 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x3A = 0x0080
2025-07-03 15:37:18,216 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x3A
2025-07-03 15:37:18,216 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x3A 的UI更新
2025-07-03 15:37:18,216 - BatchOperationManager - [BatchOperationManager.py:752] - INFO - 📊 读取进度更新: 50/125
2025-07-03 15:37:18,217 - BatchOperationManager - [BatchOperationManager.py:783] - DEBUG - 🔍 批次检查: 已完成=50, 当前批次开始=0, 当前批次结束=50
2025-07-03 15:37:18,217 - BatchOperationManager - [BatchOperationManager.py:787] - INFO - ✅ 当前批次已完成，尝试处理下一批 (已完成: 50)
2025-07-03 15:37:18,217 - BatchOperationManager - [BatchOperationManager.py:791] - INFO - 🔄 获取到下一批数据，包含 50 个寄存器
2025-07-03 15:37:18,217 - BatchOperationManager - [BatchOperationManager.py:800] - DEBUG - 开始处理下一批，共 50 个寄存器
2025-07-03 15:37:18,218 - BatchOperationManager - [BatchOperationManager.py:646] - DEBUG - 开始处理批次中的 50 个寄存器
2025-07-03 15:37:18,218 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x3B
2025-07-03 15:37:18,218 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x3B
2025-07-03 15:37:18,218 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x3C
2025-07-03 15:37:18,218 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x3C
2025-07-03 15:37:18,218 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x3D
2025-07-03 15:37:18,218 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x3D
2025-07-03 15:37:18,219 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x3E
2025-07-03 15:37:18,219 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x3E
2025-07-03 15:37:18,219 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x3F
2025-07-03 15:37:18,219 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x3F
2025-07-03 15:37:18,219 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x40
2025-07-03 15:37:18,219 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x40
2025-07-03 15:37:18,219 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x41
2025-07-03 15:37:18,219 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x41
2025-07-03 15:37:18,219 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x42
2025-07-03 15:37:18,219 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x42
2025-07-03 15:37:18,219 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x43
2025-07-03 15:37:18,220 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x43
2025-07-03 15:37:18,220 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x44
2025-07-03 15:37:18,220 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x44
2025-07-03 15:37:18,220 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x45
2025-07-03 15:37:18,220 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x45
2025-07-03 15:37:18,220 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x46
2025-07-03 15:37:18,220 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x46
2025-07-03 15:37:18,220 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x47
2025-07-03 15:37:18,220 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x47
2025-07-03 15:37:18,220 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x48
2025-07-03 15:37:18,220 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x48
2025-07-03 15:37:18,220 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x49
2025-07-03 15:37:18,221 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x49
2025-07-03 15:37:18,221 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x4A
2025-07-03 15:37:18,221 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x4A
2025-07-03 15:37:18,221 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x4C
2025-07-03 15:37:18,221 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x4C
2025-07-03 15:37:18,221 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x4E
2025-07-03 15:37:18,221 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x4E
2025-07-03 15:37:18,221 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x4F
2025-07-03 15:37:18,221 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x4F
2025-07-03 15:37:18,221 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x50
2025-07-03 15:37:18,221 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x50
2025-07-03 15:37:18,221 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x51
2025-07-03 15:37:18,221 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x51
2025-07-03 15:37:18,222 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x52
2025-07-03 15:37:18,222 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x52
2025-07-03 15:37:18,222 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x53
2025-07-03 15:37:18,222 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x53
2025-07-03 15:37:18,222 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x54
2025-07-03 15:37:18,222 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x54
2025-07-03 15:37:18,222 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x55
2025-07-03 15:37:18,222 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x55
2025-07-03 15:37:18,222 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x56
2025-07-03 15:37:18,222 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x56
2025-07-03 15:37:18,222 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x57
2025-07-03 15:37:18,222 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x57
2025-07-03 15:37:18,222 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x58
2025-07-03 15:37:18,223 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x58
2025-07-03 15:37:18,223 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x59
2025-07-03 15:37:18,223 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x59
2025-07-03 15:37:18,223 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x5A
2025-07-03 15:37:18,223 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x5A
2025-07-03 15:37:18,223 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x5B
2025-07-03 15:37:18,223 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x5B
2025-07-03 15:37:18,223 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x5C
2025-07-03 15:37:18,223 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x5C
2025-07-03 15:37:18,223 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x5D
2025-07-03 15:37:18,223 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x5D
2025-07-03 15:37:18,223 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x5E
2025-07-03 15:37:18,223 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x5E
2025-07-03 15:37:18,224 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x5F
2025-07-03 15:37:18,224 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x5F
2025-07-03 15:37:18,224 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x60
2025-07-03 15:37:18,224 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x60
2025-07-03 15:37:18,224 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x61
2025-07-03 15:37:18,224 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x61
2025-07-03 15:37:18,224 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x63
2025-07-03 15:37:18,224 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x63
2025-07-03 15:37:18,224 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x65
2025-07-03 15:37:18,224 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x65
2025-07-03 15:37:18,224 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x67
2025-07-03 15:37:18,224 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x67
2025-07-03 15:37:18,225 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x69
2025-07-03 15:37:18,225 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x69
2025-07-03 15:37:18,225 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x6B
2025-07-03 15:37:18,225 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x6B
2025-07-03 15:37:18,225 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x6C
2025-07-03 15:37:18,225 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x6C
2025-07-03 15:37:18,225 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x6F
2025-07-03 15:37:18,225 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x6F
2025-07-03 15:37:18,225 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x70
2025-07-03 15:37:18,225 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x70
2025-07-03 15:37:18,225 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x72
2025-07-03 15:37:18,225 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x72
2025-07-03 15:37:18,225 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x76
2025-07-03 15:37:18,225 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x76
2025-07-03 15:37:18,225 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x77
2025-07-03 15:37:18,226 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x77
2025-07-03 15:37:18,226 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x79
2025-07-03 15:37:18,226 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x79
2025-07-03 15:37:18,226 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x7A
2025-07-03 15:37:18,226 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x7A
2025-07-03 15:37:18,226 - BatchOperationManager - [BatchOperationManager.py:673] - DEBUG - 批次中的所有寄存器读取请求已发送，等待回调
2025-07-03 15:37:18,228 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作51开始: read 0x3B None
2025-07-03 15:37:18,228 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 51
2025-07-03 15:37:18,228 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,228 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x3B，操作ID: 51
2025-07-03 15:37:18,228 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 3B04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,243 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x3B → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:18,245 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x3B，操作ID: 51
2025-07-03 15:37:18,245 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作51数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,245 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作51成功: 地址0x3B 读取值0x0000
2025-07-03 15:37:18,245 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作52开始: read 0x3C None
2025-07-03 15:37:18,246 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x3B = 0x0000 (读取)
2025-07-03 15:37:18,246 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 52
2025-07-03 15:37:18,246 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x3B, 值=0x0000
2025-07-03 15:37:18,247 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,247 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x3B = 0x0000
2025-07-03 15:37:18,248 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x3C，操作ID: 52
2025-07-03 15:37:18,249 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x3B = 0x0000
2025-07-03 15:37:18,250 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x3B
2025-07-03 15:37:18,250 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x3B 的UI更新
2025-07-03 15:37:18,249 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 3C04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,274 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x3C → 原始字节 0010 → 整数值 16 (0x0010)
2025-07-03 15:37:18,275 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x3C，操作ID: 52
2025-07-03 15:37:18,275 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作52数据解析: 16 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,276 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作52成功: 地址0x3C 读取值0x0010
2025-07-03 15:37:18,276 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作53开始: read 0x3D None
2025-07-03 15:37:18,276 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 53
2025-07-03 15:37:18,278 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,278 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x3D，操作ID: 53
2025-07-03 15:37:18,278 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 3D04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,278 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x3C = 0x0010 (读取)
2025-07-03 15:37:18,278 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x3C, 值=0x0010
2025-07-03 15:37:18,278 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x3C = 0x0010
2025-07-03 15:37:18,278 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x3C = 0x0010
2025-07-03 15:37:18,279 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x3C
2025-07-03 15:37:18,279 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x3C 的UI更新
2025-07-03 15:37:18,305 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x3D → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:18,305 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x3D，操作ID: 53
2025-07-03 15:37:18,305 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作53数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,305 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作53成功: 地址0x3D 读取值0x0000
2025-07-03 15:37:18,306 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作54开始: read 0x3E None
2025-07-03 15:37:18,306 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x3D = 0x0000 (读取)
2025-07-03 15:37:18,306 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x3D, 值=0x0000
2025-07-03 15:37:18,306 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x3D = 0x0000
2025-07-03 15:37:18,306 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 54
2025-07-03 15:37:18,307 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x3D = 0x0000
2025-07-03 15:37:18,308 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x3D
2025-07-03 15:37:18,308 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x3D 的UI更新
2025-07-03 15:37:18,307 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,308 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x3E，操作ID: 54
2025-07-03 15:37:18,308 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 3E04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,321 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x3E → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:18,321 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x3E，操作ID: 54
2025-07-03 15:37:18,321 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作54数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,321 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作54成功: 地址0x3E 读取值0x0000
2025-07-03 15:37:18,322 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作55开始: read 0x3F None
2025-07-03 15:37:18,322 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x3E = 0x0000 (读取)
2025-07-03 15:37:18,322 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 55
2025-07-03 15:37:18,323 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0x3E, 值=0000
2025-07-03 15:37:18,323 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,323 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x3E, 值=0x0000
2025-07-03 15:37:18,324 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x3E = 0x0000
2025-07-03 15:37:18,324 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x3F，操作ID: 55
2025-07-03 15:37:18,325 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x3E = 0x0000
2025-07-03 15:37:18,325 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x3E
2025-07-03 15:37:18,326 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x3E 的UI更新
2025-07-03 15:37:18,325 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 3F04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,348 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x3F → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:18,348 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x3F，操作ID: 55
2025-07-03 15:37:18,348 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作55数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,349 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作55成功: 地址0x3F 读取值0x0000
2025-07-03 15:37:18,349 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x3F = 0x0000 (读取)
2025-07-03 15:37:18,350 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作56开始: read 0x40 None
2025-07-03 15:37:18,350 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x3F, 值=0x0000
2025-07-03 15:37:18,351 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x3F = 0x0000
2025-07-03 15:37:18,350 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 56
2025-07-03 15:37:18,351 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x3F = 0x0000
2025-07-03 15:37:18,352 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x3F
2025-07-03 15:37:18,351 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,352 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x3F 的UI更新
2025-07-03 15:37:18,352 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x40，操作ID: 56
2025-07-03 15:37:18,352 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 4004 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,364 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x40 → 原始字节 0002 → 整数值 2 (0x0002)
2025-07-03 15:37:18,365 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x40，操作ID: 56
2025-07-03 15:37:18,365 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作56数据解析: 2 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,365 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作56成功: 地址0x40 读取值0x0002
2025-07-03 15:37:18,365 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作57开始: read 0x41 None
2025-07-03 15:37:18,366 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 57
2025-07-03 15:37:18,365 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x40 = 0x0002 (读取)
2025-07-03 15:37:18,366 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,366 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x40, 值=0x0002
2025-07-03 15:37:18,367 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x41，操作ID: 57
2025-07-03 15:37:18,367 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x40 = 0x0002
2025-07-03 15:37:18,368 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 4104 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,368 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x40 = 0x0002
2025-07-03 15:37:18,369 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x40
2025-07-03 15:37:18,369 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x40 的UI更新
2025-07-03 15:37:18,395 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x41 → 原始字节 000A → 整数值 10 (0x000A)
2025-07-03 15:37:18,395 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x41，操作ID: 57
2025-07-03 15:37:18,396 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作57数据解析: 10 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,396 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作57成功: 地址0x41 读取值0x000A
2025-07-03 15:37:18,397 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作58开始: read 0x42 None
2025-07-03 15:37:18,397 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 58
2025-07-03 15:37:18,397 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x41 = 0x000A (读取)
2025-07-03 15:37:18,397 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,398 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x41, 值=0x000A
2025-07-03 15:37:18,398 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x41 = 0x000A
2025-07-03 15:37:18,398 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x42，操作ID: 58
2025-07-03 15:37:18,398 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x41 = 0x000A
2025-07-03 15:37:18,400 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x41
2025-07-03 15:37:18,400 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 4204 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,400 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x41 的UI更新
2025-07-03 15:37:18,426 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x42 → 原始字节 0080 → 整数值 128 (0x0080)
2025-07-03 15:37:18,426 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x42，操作ID: 58
2025-07-03 15:37:18,427 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作58数据解析: 128 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,427 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作58成功: 地址0x42 读取值0x0080
2025-07-03 15:37:18,427 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作59开始: read 0x43 None
2025-07-03 15:37:18,427 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 59
2025-07-03 15:37:18,428 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,428 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x43，操作ID: 59
2025-07-03 15:37:18,428 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x42 = 0x0080 (读取)
2025-07-03 15:37:18,429 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 4304 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,429 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0x42, 值=0080
2025-07-03 15:37:18,430 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x42, 值=0x0080
2025-07-03 15:37:18,430 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x42 = 0x0080
2025-07-03 15:37:18,431 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x42 = 0x0080
2025-07-03 15:37:18,431 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x42
2025-07-03 15:37:18,431 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x42 的UI更新
2025-07-03 15:37:18,457 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x43 → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:18,458 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x43，操作ID: 59
2025-07-03 15:37:18,458 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作59数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,458 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作59成功: 地址0x43 读取值0x0000
2025-07-03 15:37:18,458 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作60开始: read 0x44 None
2025-07-03 15:37:18,458 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x43 = 0x0000 (读取)
2025-07-03 15:37:18,458 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 60
2025-07-03 15:37:18,459 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x43, 值=0x0000
2025-07-03 15:37:18,459 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x43 = 0x0000
2025-07-03 15:37:18,459 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,459 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x43 = 0x0000
2025-07-03 15:37:18,460 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x44，操作ID: 60
2025-07-03 15:37:18,460 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x43
2025-07-03 15:37:18,460 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 4404 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,461 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x43 的UI更新
2025-07-03 15:37:18,473 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x44 → 原始字节 0010 → 整数值 16 (0x0010)
2025-07-03 15:37:18,473 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x44，操作ID: 60
2025-07-03 15:37:18,473 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作60数据解析: 16 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,473 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作60成功: 地址0x44 读取值0x0010
2025-07-03 15:37:18,474 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作61开始: read 0x45 None
2025-07-03 15:37:18,474 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 61
2025-07-03 15:37:18,474 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,474 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x44 = 0x0010 (读取)
2025-07-03 15:37:18,474 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x45，操作ID: 61
2025-07-03 15:37:18,474 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x44, 值=0x0010
2025-07-03 15:37:18,474 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 4504 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,474 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x44 = 0x0010
2025-07-03 15:37:18,474 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x44 = 0x0010
2025-07-03 15:37:18,474 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x44
2025-07-03 15:37:18,474 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x44 的UI更新
2025-07-03 15:37:18,474 - BatchOperationManager - [BatchOperationManager.py:752] - INFO - 📊 读取进度更新: 60/125
2025-07-03 15:37:18,475 - BatchOperationManager - [BatchOperationManager.py:783] - DEBUG - 🔍 批次检查: 已完成=60, 当前批次开始=50, 当前批次结束=100
2025-07-03 15:37:18,475 - BatchOperationManager - [BatchOperationManager.py:810] - DEBUG - ⏳ 当前批次未完成，继续等待 (已完成: 60, 批次结束: 100)
2025-07-03 15:37:18,488 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x45 → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:18,488 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x45，操作ID: 61
2025-07-03 15:37:18,488 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作61数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,488 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作61成功: 地址0x45 读取值0x0000
2025-07-03 15:37:18,488 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x45 = 0x0000 (读取)
2025-07-03 15:37:18,489 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x45, 值=0x0000
2025-07-03 15:37:18,489 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x45 = 0x0000
2025-07-03 15:37:18,489 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作62开始: read 0x46 None
2025-07-03 15:37:18,489 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x45 = 0x0000
2025-07-03 15:37:18,489 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 62
2025-07-03 15:37:18,489 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x45
2025-07-03 15:37:18,489 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,489 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x45 的UI更新
2025-07-03 15:37:18,489 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x46，操作ID: 62
2025-07-03 15:37:18,489 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 4604 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,504 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x46 → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:18,504 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x46，操作ID: 62
2025-07-03 15:37:18,504 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作62数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,504 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作62成功: 地址0x46 读取值0x0000
2025-07-03 15:37:18,504 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作63开始: read 0x47 None
2025-07-03 15:37:18,504 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x46 = 0x0000 (读取)
2025-07-03 15:37:18,504 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 63
2025-07-03 15:37:18,504 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0x46, 值=0000
2025-07-03 15:37:18,505 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,505 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x46, 值=0x0000
2025-07-03 15:37:18,505 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x47，操作ID: 63
2025-07-03 15:37:18,505 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x46 = 0x0000
2025-07-03 15:37:18,505 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 4704 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,505 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x46 = 0x0000
2025-07-03 15:37:18,505 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x46
2025-07-03 15:37:18,505 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x46 的UI更新
2025-07-03 15:37:18,519 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x47 → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:18,519 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x47，操作ID: 63
2025-07-03 15:37:18,519 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作63数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,519 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作63成功: 地址0x47 读取值0x0000
2025-07-03 15:37:18,519 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作64开始: read 0x48 None
2025-07-03 15:37:18,519 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x47 = 0x0000 (读取)
2025-07-03 15:37:18,519 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 64
2025-07-03 15:37:18,520 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0x47, 值=0000
2025-07-03 15:37:18,520 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,520 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x47, 值=0x0000
2025-07-03 15:37:18,520 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x48，操作ID: 64
2025-07-03 15:37:18,520 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x47 = 0x0000
2025-07-03 15:37:18,520 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 4804 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,520 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x47 = 0x0000
2025-07-03 15:37:18,521 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x47
2025-07-03 15:37:18,521 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x47 的UI更新
2025-07-03 15:37:18,534 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x48 → 原始字节 0044 → 整数值 68 (0x0044)
2025-07-03 15:37:18,534 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x48，操作ID: 64
2025-07-03 15:37:18,534 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作64数据解析: 68 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,534 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作64成功: 地址0x48 读取值0x0044
2025-07-03 15:37:18,535 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x48 = 0x0044 (读取)
2025-07-03 15:37:18,535 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0x48, 值=0044
2025-07-03 15:37:18,535 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x48, 值=0x0044
2025-07-03 15:37:18,535 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x48 = 0x0044
2025-07-03 15:37:18,535 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作65开始: read 0x49 None
2025-07-03 15:37:18,535 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x48 = 0x0044
2025-07-03 15:37:18,535 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x48
2025-07-03 15:37:18,535 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 65
2025-07-03 15:37:18,535 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x48 的UI更新
2025-07-03 15:37:18,536 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,536 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x49，操作ID: 65
2025-07-03 15:37:18,536 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 4904 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,547 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x49 → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:18,547 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x49，操作ID: 65
2025-07-03 15:37:18,547 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作65数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,547 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作65成功: 地址0x49 读取值0x0000
2025-07-03 15:37:18,547 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x49 = 0x0000 (读取)
2025-07-03 15:37:18,547 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作66开始: read 0x4A None
2025-07-03 15:37:18,547 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x49, 值=0x0000
2025-07-03 15:37:18,547 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 66
2025-07-03 15:37:18,547 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x49 = 0x0000
2025-07-03 15:37:18,547 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,547 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x49 = 0x0000
2025-07-03 15:37:18,547 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x4A，操作ID: 66
2025-07-03 15:37:18,548 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x49
2025-07-03 15:37:18,548 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 4A04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,548 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x49 的UI更新
2025-07-03 15:37:18,562 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x4A → 原始字节 0C00 → 整数值 3072 (0x0C00)
2025-07-03 15:37:18,563 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x4A，操作ID: 66
2025-07-03 15:37:18,563 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作66数据解析: 3072 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,563 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作66成功: 地址0x4A 读取值0x0C00
2025-07-03 15:37:18,564 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作67开始: read 0x4C None
2025-07-03 15:37:18,564 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x4A = 0x0C00 (读取)
2025-07-03 15:37:18,564 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 67
2025-07-03 15:37:18,564 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x4A, 值=0x0C00
2025-07-03 15:37:18,564 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,564 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x4A = 0x0C00
2025-07-03 15:37:18,564 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x4C，操作ID: 67
2025-07-03 15:37:18,564 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x4A = 0x0C00
2025-07-03 15:37:18,565 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 4C04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,565 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x4A
2025-07-03 15:37:18,565 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x4A 的UI更新
2025-07-03 15:37:18,578 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x4C → 原始字节 0008 → 整数值 8 (0x0008)
2025-07-03 15:37:18,578 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x4C，操作ID: 67
2025-07-03 15:37:18,578 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作67数据解析: 8 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,578 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作67成功: 地址0x4C 读取值0x0008
2025-07-03 15:37:18,578 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作68开始: read 0x4E None
2025-07-03 15:37:18,578 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 68
2025-07-03 15:37:18,579 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,579 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x4C = 0x0008 (读取)
2025-07-03 15:37:18,579 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x4E，操作ID: 68
2025-07-03 15:37:18,579 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x4C, 值=0x0008
2025-07-03 15:37:18,579 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 4E04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,579 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x4C = 0x0008
2025-07-03 15:37:18,580 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x4C = 0x0008
2025-07-03 15:37:18,580 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x4C
2025-07-03 15:37:18,580 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x4C 的UI更新
2025-07-03 15:37:18,593 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x4E → 原始字节 0043 → 整数值 67 (0x0043)
2025-07-03 15:37:18,593 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x4E，操作ID: 68
2025-07-03 15:37:18,594 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作68数据解析: 67 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,594 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作68成功: 地址0x4E 读取值0x0043
2025-07-03 15:37:18,594 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作69开始: read 0x4F None
2025-07-03 15:37:18,594 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x4E = 0x0043 (读取)
2025-07-03 15:37:18,595 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 69
2025-07-03 15:37:18,595 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x4E, 值=0x0043
2025-07-03 15:37:18,595 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,596 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x4E = 0x0043
2025-07-03 15:37:18,597 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x4F，操作ID: 69
2025-07-03 15:37:18,597 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x4E = 0x0043
2025-07-03 15:37:18,597 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 4F04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,597 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x4E
2025-07-03 15:37:18,598 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x4E 的UI更新
2025-07-03 15:37:18,609 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x4F → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:18,609 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x4F，操作ID: 69
2025-07-03 15:37:18,609 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作69数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,609 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作69成功: 地址0x4F 读取值0x0000
2025-07-03 15:37:18,609 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作70开始: read 0x50 None
2025-07-03 15:37:18,609 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x4F = 0x0000 (读取)
2025-07-03 15:37:18,609 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 70
2025-07-03 15:37:18,610 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x4F, 值=0x0000
2025-07-03 15:37:18,610 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,610 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x4F = 0x0000
2025-07-03 15:37:18,610 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x50，操作ID: 70
2025-07-03 15:37:18,610 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x4F = 0x0000
2025-07-03 15:37:18,610 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 5004 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,611 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x4F
2025-07-03 15:37:18,611 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x4F 的UI更新
2025-07-03 15:37:18,624 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x50 → 原始字节 00E0 → 整数值 224 (0x00E0)
2025-07-03 15:37:18,624 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x50，操作ID: 70
2025-07-03 15:37:18,624 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作70数据解析: 224 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,624 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作70成功: 地址0x50 读取值0x00E0
2025-07-03 15:37:18,624 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作71开始: read 0x51 None
2025-07-03 15:37:18,625 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x50 = 0x00E0 (读取)
2025-07-03 15:37:18,625 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 71
2025-07-03 15:37:18,625 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0x50, 值=00E0
2025-07-03 15:37:18,626 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,626 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x50, 值=0x00E0
2025-07-03 15:37:18,626 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x51，操作ID: 71
2025-07-03 15:37:18,626 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x50 = 0x00E0
2025-07-03 15:37:18,626 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 5104 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,627 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x50 = 0x00E0
2025-07-03 15:37:18,628 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x50
2025-07-03 15:37:18,628 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x50 的UI更新
2025-07-03 15:37:18,628 - BatchOperationManager - [BatchOperationManager.py:752] - INFO - 📊 读取进度更新: 70/125
2025-07-03 15:37:18,630 - BatchOperationManager - [BatchOperationManager.py:783] - DEBUG - 🔍 批次检查: 已完成=70, 当前批次开始=50, 当前批次结束=100
2025-07-03 15:37:18,630 - BatchOperationManager - [BatchOperationManager.py:810] - DEBUG - ⏳ 当前批次未完成，继续等待 (已完成: 70, 批次结束: 100)
2025-07-03 15:37:18,640 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x51 → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:18,640 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x51，操作ID: 71
2025-07-03 15:37:18,640 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作71数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,641 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作71成功: 地址0x51 读取值0x0000
2025-07-03 15:37:18,641 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x51 = 0x0000 (读取)
2025-07-03 15:37:18,641 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作72开始: read 0x52 None
2025-07-03 15:37:18,641 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x51, 值=0x0000
2025-07-03 15:37:18,642 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 72
2025-07-03 15:37:18,642 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x51 = 0x0000
2025-07-03 15:37:18,642 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,642 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x51 = 0x0000
2025-07-03 15:37:18,643 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x52，操作ID: 72
2025-07-03 15:37:18,643 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x51
2025-07-03 15:37:18,643 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 5204 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,643 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x51 的UI更新
2025-07-03 15:37:18,655 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x52 → 原始字节 0100 → 整数值 256 (0x0100)
2025-07-03 15:37:18,655 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x52，操作ID: 72
2025-07-03 15:37:18,655 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作72数据解析: 256 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,656 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作72成功: 地址0x52 读取值0x0100
2025-07-03 15:37:18,656 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x52 = 0x0100 (读取)
2025-07-03 15:37:18,656 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作73开始: read 0x53 None
2025-07-03 15:37:18,656 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x52, 值=0x0100
2025-07-03 15:37:18,657 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 73
2025-07-03 15:37:18,657 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x52 = 0x0100
2025-07-03 15:37:18,657 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,658 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x52 = 0x0100
2025-07-03 15:37:18,658 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x53，操作ID: 73
2025-07-03 15:37:18,658 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x52
2025-07-03 15:37:18,658 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 5304 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,659 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x52 的UI更新
2025-07-03 15:37:18,670 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x53 → 原始字节 0011 → 整数值 17 (0x0011)
2025-07-03 15:37:18,670 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x53，操作ID: 73
2025-07-03 15:37:18,670 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作73数据解析: 17 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,671 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作73成功: 地址0x53 读取值0x0011
2025-07-03 15:37:18,671 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x53 = 0x0011 (读取)
2025-07-03 15:37:18,671 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作74开始: read 0x54 None
2025-07-03 15:37:18,672 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x53, 值=0x0011
2025-07-03 15:37:18,672 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 74
2025-07-03 15:37:18,672 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x53 = 0x0011
2025-07-03 15:37:18,672 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,673 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x53 = 0x0011
2025-07-03 15:37:18,673 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x54，操作ID: 74
2025-07-03 15:37:18,673 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x53
2025-07-03 15:37:18,674 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 5404 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,674 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x53 的UI更新
2025-07-03 15:37:18,687 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x54 → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:18,687 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x54，操作ID: 74
2025-07-03 15:37:18,687 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作74数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,687 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作74成功: 地址0x54 读取值0x0000
2025-07-03 15:37:18,687 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作75开始: read 0x55 None
2025-07-03 15:37:18,687 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x54 = 0x0000 (读取)
2025-07-03 15:37:18,688 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 75
2025-07-03 15:37:18,688 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x54, 值=0x0000
2025-07-03 15:37:18,688 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,689 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x54 = 0x0000
2025-07-03 15:37:18,689 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x55，操作ID: 75
2025-07-03 15:37:18,690 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x54 = 0x0000
2025-07-03 15:37:18,690 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 5504 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,690 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x54
2025-07-03 15:37:18,691 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x54 的UI更新
2025-07-03 15:37:18,702 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x55 → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:18,702 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x55，操作ID: 75
2025-07-03 15:37:18,702 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作75数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,703 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作75成功: 地址0x55 读取值0x0000
2025-07-03 15:37:18,703 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x55 = 0x0000 (读取)
2025-07-03 15:37:18,703 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作76开始: read 0x56 None
2025-07-03 15:37:18,703 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x55, 值=0x0000
2025-07-03 15:37:18,704 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 76
2025-07-03 15:37:18,704 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x55 = 0x0000
2025-07-03 15:37:18,704 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,705 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x55 = 0x0000
2025-07-03 15:37:18,705 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x56，操作ID: 76
2025-07-03 15:37:18,705 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x55
2025-07-03 15:37:18,706 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 5604 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,706 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x55 的UI更新
2025-07-03 15:37:18,728 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x56 → 原始字节 0018 → 整数值 24 (0x0018)
2025-07-03 15:37:18,728 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x56，操作ID: 76
2025-07-03 15:37:18,729 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作76数据解析: 24 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,730 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作76成功: 地址0x56 读取值0x0018
2025-07-03 15:37:18,730 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x56 = 0x0018 (读取)
2025-07-03 15:37:18,731 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作77开始: read 0x57 None
2025-07-03 15:37:18,732 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 77
2025-07-03 15:37:18,731 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x56, 值=0x0018
2025-07-03 15:37:18,732 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,732 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x56 = 0x0018
2025-07-03 15:37:18,733 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x57，操作ID: 77
2025-07-03 15:37:18,734 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x56 = 0x0018
2025-07-03 15:37:18,734 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 5704 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,734 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x56
2025-07-03 15:37:18,735 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x56 的UI更新
2025-07-03 15:37:18,759 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x57 → 原始字节 001A → 整数值 26 (0x001A)
2025-07-03 15:37:18,760 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x57，操作ID: 77
2025-07-03 15:37:18,760 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作77数据解析: 26 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,760 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作77成功: 地址0x57 读取值0x001A
2025-07-03 15:37:18,760 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作78开始: read 0x58 None
2025-07-03 15:37:18,761 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 78
2025-07-03 15:37:18,761 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,761 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x58，操作ID: 78
2025-07-03 15:37:18,761 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 5804 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,761 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x57 = 0x001A (读取)
2025-07-03 15:37:18,763 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x57, 值=0x001A
2025-07-03 15:37:18,763 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x57 = 0x001A
2025-07-03 15:37:18,763 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x57 = 0x001A
2025-07-03 15:37:18,763 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x57
2025-07-03 15:37:18,764 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x57 的UI更新
2025-07-03 15:37:18,774 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x58 → 原始字节 0002 → 整数值 2 (0x0002)
2025-07-03 15:37:18,774 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x58，操作ID: 78
2025-07-03 15:37:18,774 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作78数据解析: 2 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,775 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作78成功: 地址0x58 读取值0x0002
2025-07-03 15:37:18,775 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作79开始: read 0x59 None
2025-07-03 15:37:18,775 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x58 = 0x0002 (读取)
2025-07-03 15:37:18,776 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 79
2025-07-03 15:37:18,776 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x58, 值=0x0002
2025-07-03 15:37:18,777 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x58 = 0x0002
2025-07-03 15:37:18,776 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,777 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x58 = 0x0002
2025-07-03 15:37:18,778 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x58
2025-07-03 15:37:18,777 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x59，操作ID: 79
2025-07-03 15:37:18,778 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x58 的UI更新
2025-07-03 15:37:18,778 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 5904 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,790 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x59 → 原始字节 0042 → 整数值 66 (0x0042)
2025-07-03 15:37:18,791 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x59，操作ID: 79
2025-07-03 15:37:18,791 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作79数据解析: 66 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,792 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作79成功: 地址0x59 读取值0x0042
2025-07-03 15:37:18,792 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作80开始: read 0x5A None
2025-07-03 15:37:18,792 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 80
2025-07-03 15:37:18,792 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,792 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x5A，操作ID: 80
2025-07-03 15:37:18,793 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x59 = 0x0042 (读取)
2025-07-03 15:37:18,793 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 5A04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,793 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x59, 值=0x0042
2025-07-03 15:37:18,794 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x59 = 0x0042
2025-07-03 15:37:18,795 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x59 = 0x0042
2025-07-03 15:37:18,795 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x59
2025-07-03 15:37:18,795 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x59 的UI更新
2025-07-03 15:37:18,806 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x5A → 原始字节 0002 → 整数值 2 (0x0002)
2025-07-03 15:37:18,806 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x5A，操作ID: 80
2025-07-03 15:37:18,806 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作80数据解析: 2 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,807 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作80成功: 地址0x5A 读取值0x0002
2025-07-03 15:37:18,807 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作81开始: read 0x5B None
2025-07-03 15:37:18,807 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x5A = 0x0002 (读取)
2025-07-03 15:37:18,807 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 81
2025-07-03 15:37:18,808 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0x5A, 值=0002
2025-07-03 15:37:18,809 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x5A, 值=0x0002
2025-07-03 15:37:18,809 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x5A = 0x0002
2025-07-03 15:37:18,809 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,810 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x5A = 0x0002
2025-07-03 15:37:18,810 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x5B，操作ID: 81
2025-07-03 15:37:18,810 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x5A
2025-07-03 15:37:18,811 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 5B04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,811 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x5A 的UI更新
2025-07-03 15:37:18,811 - BatchOperationManager - [BatchOperationManager.py:752] - INFO - 📊 读取进度更新: 80/125
2025-07-03 15:37:18,812 - BatchOperationManager - [BatchOperationManager.py:783] - DEBUG - 🔍 批次检查: 已完成=80, 当前批次开始=50, 当前批次结束=100
2025-07-03 15:37:18,813 - BatchOperationManager - [BatchOperationManager.py:810] - DEBUG - ⏳ 当前批次未完成，继续等待 (已完成: 80, 批次结束: 100)
2025-07-03 15:37:18,837 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x5B → 原始字节 0004 → 整数值 4 (0x0004)
2025-07-03 15:37:18,837 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x5B，操作ID: 81
2025-07-03 15:37:18,837 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作81数据解析: 4 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,837 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作81成功: 地址0x5B 读取值0x0004
2025-07-03 15:37:18,838 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作82开始: read 0x5C None
2025-07-03 15:37:18,838 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x5B = 0x0004 (读取)
2025-07-03 15:37:18,838 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 82
2025-07-03 15:37:18,838 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x5B, 值=0x0004
2025-07-03 15:37:18,838 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x5B = 0x0004
2025-07-03 15:37:18,838 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,838 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x5B = 0x0004
2025-07-03 15:37:18,839 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x5B
2025-07-03 15:37:18,839 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x5C，操作ID: 82
2025-07-03 15:37:18,839 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x5B 的UI更新
2025-07-03 15:37:18,839 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 5C04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,852 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x5C → 原始字节 0200 → 整数值 512 (0x0200)
2025-07-03 15:37:18,852 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x5C，操作ID: 82
2025-07-03 15:37:18,852 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作82数据解析: 512 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,853 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作82成功: 地址0x5C 读取值0x0200
2025-07-03 15:37:18,853 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x5C = 0x0200 (读取)
2025-07-03 15:37:18,853 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作83开始: read 0x5D None
2025-07-03 15:37:18,853 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x5C, 值=0x0200
2025-07-03 15:37:18,853 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 83
2025-07-03 15:37:18,853 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x5C = 0x0200
2025-07-03 15:37:18,854 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,854 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x5C = 0x0200
2025-07-03 15:37:18,854 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x5D，操作ID: 83
2025-07-03 15:37:18,854 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x5C
2025-07-03 15:37:18,854 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 5D04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,854 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x5C 的UI更新
2025-07-03 15:37:18,868 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x5D → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:18,868 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x5D，操作ID: 83
2025-07-03 15:37:18,868 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作83数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,868 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作83成功: 地址0x5D 读取值0x0000
2025-07-03 15:37:18,868 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x5D = 0x0000 (读取)
2025-07-03 15:37:18,869 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作84开始: read 0x5E None
2025-07-03 15:37:18,869 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x5D, 值=0x0000
2025-07-03 15:37:18,869 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 84
2025-07-03 15:37:18,869 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x5D = 0x0000
2025-07-03 15:37:18,869 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,869 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x5D = 0x0000
2025-07-03 15:37:18,869 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x5E，操作ID: 84
2025-07-03 15:37:18,869 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x5D
2025-07-03 15:37:18,869 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 5E04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,870 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x5D 的UI更新
2025-07-03 15:37:18,895 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x5E → 原始字节 00C3 → 整数值 195 (0x00C3)
2025-07-03 15:37:18,896 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x5E，操作ID: 84
2025-07-03 15:37:18,896 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作84数据解析: 195 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,896 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作84成功: 地址0x5E 读取值0x00C3
2025-07-03 15:37:18,896 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x5E = 0x00C3 (读取)
2025-07-03 15:37:18,896 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作85开始: read 0x5F None
2025-07-03 15:37:18,896 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0x5E, 值=00C3
2025-07-03 15:37:18,896 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 85
2025-07-03 15:37:18,896 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x5E, 值=0x00C3
2025-07-03 15:37:18,897 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x5E = 0x00C3
2025-07-03 15:37:18,897 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,898 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x5E = 0x00C3
2025-07-03 15:37:18,898 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x5F，操作ID: 85
2025-07-03 15:37:18,898 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x5E
2025-07-03 15:37:18,898 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 5F04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,898 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x5E 的UI更新
2025-07-03 15:37:18,911 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x5F → 原始字节 00D1 → 整数值 209 (0x00D1)
2025-07-03 15:37:18,911 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x5F，操作ID: 85
2025-07-03 15:37:18,911 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作85数据解析: 209 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,912 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作85成功: 地址0x5F 读取值0x00D1
2025-07-03 15:37:18,912 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作86开始: read 0x60 None
2025-07-03 15:37:18,912 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x5F = 0x00D1 (读取)
2025-07-03 15:37:18,912 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 86
2025-07-03 15:37:18,913 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0x5F, 值=00D1
2025-07-03 15:37:18,913 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,913 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x5F, 值=0x00D1
2025-07-03 15:37:18,914 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x60，操作ID: 86
2025-07-03 15:37:18,914 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x5F = 0x00D1
2025-07-03 15:37:18,914 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 6004 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,915 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x5F = 0x00D1
2025-07-03 15:37:18,915 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x5F
2025-07-03 15:37:18,915 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x5F 的UI更新
2025-07-03 15:37:18,926 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x60 → 原始字节 1680 → 整数值 5760 (0x1680)
2025-07-03 15:37:18,926 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x60，操作ID: 86
2025-07-03 15:37:18,926 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作86数据解析: 5760 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,927 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作86成功: 地址0x60 读取值0x1680
2025-07-03 15:37:18,927 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x60 = 0x1680 (读取)
2025-07-03 15:37:18,927 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作87开始: read 0x61 None
2025-07-03 15:37:18,928 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0x60, 值=1680
2025-07-03 15:37:18,928 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 87
2025-07-03 15:37:18,928 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x60, 值=0x1680
2025-07-03 15:37:18,929 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,929 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x60 = 0x1680
2025-07-03 15:37:18,930 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x61，操作ID: 87
2025-07-03 15:37:18,930 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x60 = 0x1680
2025-07-03 15:37:18,930 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 6104 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,930 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x60
2025-07-03 15:37:18,930 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x60 的UI更新
2025-07-03 15:37:18,957 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x61 → 原始字节 0200 → 整数值 512 (0x0200)
2025-07-03 15:37:18,957 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x61，操作ID: 87
2025-07-03 15:37:18,957 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作87数据解析: 512 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,958 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作87成功: 地址0x61 读取值0x0200
2025-07-03 15:37:18,958 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x61 = 0x0200 (读取)
2025-07-03 15:37:18,958 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作88开始: read 0x63 None
2025-07-03 15:37:18,959 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x61, 值=0x0200
2025-07-03 15:37:18,959 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 88
2025-07-03 15:37:18,959 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x61 = 0x0200
2025-07-03 15:37:18,960 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,960 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x61 = 0x0200
2025-07-03 15:37:18,960 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x63，操作ID: 88
2025-07-03 15:37:18,961 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x61
2025-07-03 15:37:18,961 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 6304 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,961 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x61 的UI更新
2025-07-03 15:37:18,972 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x63 → 原始字节 0078 → 整数值 120 (0x0078)
2025-07-03 15:37:18,972 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x63，操作ID: 88
2025-07-03 15:37:18,973 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作88数据解析: 120 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,973 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作88成功: 地址0x63 读取值0x0078
2025-07-03 15:37:18,973 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x63 = 0x0078 (读取)
2025-07-03 15:37:18,974 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作89开始: read 0x65 None
2025-07-03 15:37:18,974 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x63, 值=0x0078
2025-07-03 15:37:18,975 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 89
2025-07-03 15:37:18,975 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x63 = 0x0078
2025-07-03 15:37:18,975 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,976 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x63 = 0x0078
2025-07-03 15:37:18,976 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x65，操作ID: 89
2025-07-03 15:37:18,976 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x63
2025-07-03 15:37:18,976 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 6504 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,977 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x63 的UI更新
2025-07-03 15:37:18,988 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x65 → 原始字节 0096 → 整数值 150 (0x0096)
2025-07-03 15:37:18,988 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x65，操作ID: 89
2025-07-03 15:37:18,988 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作89数据解析: 150 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:18,989 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作89成功: 地址0x65 读取值0x0096
2025-07-03 15:37:18,989 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x65 = 0x0096 (读取)
2025-07-03 15:37:18,989 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作90开始: read 0x67 None
2025-07-03 15:37:18,990 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0x65, 值=0096
2025-07-03 15:37:18,990 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 90
2025-07-03 15:37:18,990 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x65, 值=0x0096
2025-07-03 15:37:18,990 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:18,991 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x65 = 0x0096
2025-07-03 15:37:18,991 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x67，操作ID: 90
2025-07-03 15:37:18,991 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x65 = 0x0096
2025-07-03 15:37:18,992 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 6704 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:18,992 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x65
2025-07-03 15:37:18,992 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x65 的UI更新
2025-07-03 15:37:19,004 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x67 → 原始字节 0096 → 整数值 150 (0x0096)
2025-07-03 15:37:19,005 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x67，操作ID: 90
2025-07-03 15:37:19,005 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作90数据解析: 150 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,005 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作90成功: 地址0x67 读取值0x0096
2025-07-03 15:37:19,005 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作91开始: read 0x69 None
2025-07-03 15:37:19,005 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x67 = 0x0096 (读取)
2025-07-03 15:37:19,006 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 91
2025-07-03 15:37:19,006 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x67, 值=0x0096
2025-07-03 15:37:19,007 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x67 = 0x0096
2025-07-03 15:37:19,006 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,007 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x67 = 0x0096
2025-07-03 15:37:19,008 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x67
2025-07-03 15:37:19,007 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x69，操作ID: 91
2025-07-03 15:37:19,008 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x67 的UI更新
2025-07-03 15:37:19,008 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 6904 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,009 - BatchOperationManager - [BatchOperationManager.py:752] - INFO - 📊 读取进度更新: 90/125
2025-07-03 15:37:19,010 - BatchOperationManager - [BatchOperationManager.py:783] - DEBUG - 🔍 批次检查: 已完成=90, 当前批次开始=50, 当前批次结束=100
2025-07-03 15:37:19,010 - BatchOperationManager - [BatchOperationManager.py:810] - DEBUG - ⏳ 当前批次未完成，继续等待 (已完成: 90, 批次结束: 100)
2025-07-03 15:37:19,031 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x69 → 原始字节 0078 → 整数值 120 (0x0078)
2025-07-03 15:37:19,031 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x69，操作ID: 91
2025-07-03 15:37:19,032 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作91数据解析: 120 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,032 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作91成功: 地址0x69 读取值0x0078
2025-07-03 15:37:19,032 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作92开始: read 0x6B None
2025-07-03 15:37:19,032 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x69 = 0x0078 (读取)
2025-07-03 15:37:19,032 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 92
2025-07-03 15:37:19,033 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x69, 值=0x0078
2025-07-03 15:37:19,033 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,033 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x69 = 0x0078
2025-07-03 15:37:19,033 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x6B，操作ID: 92
2025-07-03 15:37:19,034 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x69 = 0x0078
2025-07-03 15:37:19,034 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 6B04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,034 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x69
2025-07-03 15:37:19,034 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x69 的UI更新
2025-07-03 15:37:19,047 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x6B → 原始字节 00D4 → 整数值 212 (0x00D4)
2025-07-03 15:37:19,048 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x6B，操作ID: 92
2025-07-03 15:37:19,048 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作92数据解析: 212 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,048 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作92成功: 地址0x6B 读取值0x00D4
2025-07-03 15:37:19,048 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作93开始: read 0x6C None
2025-07-03 15:37:19,048 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x6B = 0x00D4 (读取)
2025-07-03 15:37:19,048 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 93
2025-07-03 15:37:19,049 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x6B, 值=0x00D4
2025-07-03 15:37:19,049 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,049 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x6B = 0x00D4
2025-07-03 15:37:19,050 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x6C，操作ID: 93
2025-07-03 15:37:19,050 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x6B = 0x00D4
2025-07-03 15:37:19,050 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 6C04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,051 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x6B
2025-07-03 15:37:19,051 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x6B 的UI更新
2025-07-03 15:37:19,063 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x6C → 原始字节 2000 → 整数值 8192 (0x2000)
2025-07-03 15:37:19,063 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x6C，操作ID: 93
2025-07-03 15:37:19,064 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作93数据解析: 8192 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,064 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作93成功: 地址0x6C 读取值0x2000
2025-07-03 15:37:19,064 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x6C = 0x2000 (读取)
2025-07-03 15:37:19,064 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作94开始: read 0x6F None
2025-07-03 15:37:19,064 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x6C, 值=0x2000
2025-07-03 15:37:19,065 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 94
2025-07-03 15:37:19,065 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x6C = 0x2000
2025-07-03 15:37:19,065 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,065 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x6C = 0x2000
2025-07-03 15:37:19,066 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x6F，操作ID: 94
2025-07-03 15:37:19,066 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x6C
2025-07-03 15:37:19,066 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 6F04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,066 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x6C 的UI更新
2025-07-03 15:37:19,078 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x6F → 原始字节 000B → 整数值 11 (0x000B)
2025-07-03 15:37:19,079 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x6F，操作ID: 94
2025-07-03 15:37:19,079 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作94数据解析: 11 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,079 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作94成功: 地址0x6F 读取值0x000B
2025-07-03 15:37:19,080 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作95开始: read 0x70 None
2025-07-03 15:37:19,080 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 95
2025-07-03 15:37:19,080 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x6F = 0x000B (读取)
2025-07-03 15:37:19,081 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,081 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x6F, 值=0x000B
2025-07-03 15:37:19,082 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x70，操作ID: 95
2025-07-03 15:37:19,082 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x6F = 0x000B
2025-07-03 15:37:19,082 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 7004 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,082 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x6F = 0x000B
2025-07-03 15:37:19,084 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x6F
2025-07-03 15:37:19,084 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x6F 的UI更新
2025-07-03 15:37:19,109 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x70 → 原始字节 0002 → 整数值 2 (0x0002)
2025-07-03 15:37:19,110 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x70，操作ID: 95
2025-07-03 15:37:19,110 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作95数据解析: 2 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,110 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作95成功: 地址0x70 读取值0x0002
2025-07-03 15:37:19,110 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作96开始: read 0x72 None
2025-07-03 15:37:19,110 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x70 = 0x0002 (读取)
2025-07-03 15:37:19,110 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 96
2025-07-03 15:37:19,111 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0x70, 值=0002
2025-07-03 15:37:19,111 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,111 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x70, 值=0x0002
2025-07-03 15:37:19,111 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x72，操作ID: 96
2025-07-03 15:37:19,111 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x70 = 0x0002
2025-07-03 15:37:19,111 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 7204 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,112 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x70 = 0x0002
2025-07-03 15:37:19,112 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x70
2025-07-03 15:37:19,112 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x70 的UI更新
2025-07-03 15:37:19,124 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x72 → 原始字节 006A → 整数值 106 (0x006A)
2025-07-03 15:37:19,124 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x72，操作ID: 96
2025-07-03 15:37:19,124 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作96数据解析: 106 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,124 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作96成功: 地址0x72 读取值0x006A
2025-07-03 15:37:19,124 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作97开始: read 0x76 None
2025-07-03 15:37:19,124 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x72 = 0x006A (读取)
2025-07-03 15:37:19,124 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 97
2025-07-03 15:37:19,124 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0x72, 值=006A
2025-07-03 15:37:19,124 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,124 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x72, 值=0x006A
2025-07-03 15:37:19,124 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x76，操作ID: 97
2025-07-03 15:37:19,125 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x72 = 0x006A
2025-07-03 15:37:19,125 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 7604 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,125 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x72 = 0x006A
2025-07-03 15:37:19,125 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x72
2025-07-03 15:37:19,125 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x72 的UI更新
2025-07-03 15:37:19,140 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x76 → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:19,140 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x76，操作ID: 97
2025-07-03 15:37:19,140 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作97数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,140 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作97成功: 地址0x76 读取值0x0000
2025-07-03 15:37:19,140 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x76 = 0x0000 (读取)
2025-07-03 15:37:19,140 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作98开始: read 0x77 None
2025-07-03 15:37:19,141 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x76, 值=0x0000
2025-07-03 15:37:19,141 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 98
2025-07-03 15:37:19,141 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x76 = 0x0000
2025-07-03 15:37:19,141 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,141 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x76 = 0x0000
2025-07-03 15:37:19,141 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x77，操作ID: 98
2025-07-03 15:37:19,141 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x76
2025-07-03 15:37:19,141 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 7704 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,141 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x76 的UI更新
2025-07-03 15:37:19,156 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x77 → 原始字节 0001 → 整数值 1 (0x0001)
2025-07-03 15:37:19,156 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x77，操作ID: 98
2025-07-03 15:37:19,156 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作98数据解析: 1 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,156 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作98成功: 地址0x77 读取值0x0001
2025-07-03 15:37:19,156 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作99开始: read 0x79 None
2025-07-03 15:37:19,156 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 99
2025-07-03 15:37:19,156 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,156 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x77 = 0x0001 (读取)
2025-07-03 15:37:19,156 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x79，操作ID: 99
2025-07-03 15:37:19,156 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0x77, 值=0001
2025-07-03 15:37:19,156 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x77, 值=0x0001
2025-07-03 15:37:19,156 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 7904 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,156 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x77 = 0x0001
2025-07-03 15:37:19,156 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x77 = 0x0001
2025-07-03 15:37:19,156 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x77
2025-07-03 15:37:19,156 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x77 的UI更新
2025-07-03 15:37:19,172 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x79 → 原始字节 0058 → 整数值 88 (0x0058)
2025-07-03 15:37:19,172 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x79，操作ID: 99
2025-07-03 15:37:19,172 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作99数据解析: 88 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,172 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作99成功: 地址0x79 读取值0x0058
2025-07-03 15:37:19,172 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x79 = 0x0058 (读取)
2025-07-03 15:37:19,172 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作100开始: read 0x7A None
2025-07-03 15:37:19,173 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x79, 值=0x0058
2025-07-03 15:37:19,173 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x79 = 0x0058
2025-07-03 15:37:19,173 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 100
2025-07-03 15:37:19,173 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x79 = 0x0058
2025-07-03 15:37:19,173 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,174 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x79
2025-07-03 15:37:19,174 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x7A，操作ID: 100
2025-07-03 15:37:19,174 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x79 的UI更新
2025-07-03 15:37:19,174 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 7A04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,187 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x7A → 原始字节 2000 → 整数值 8192 (0x2000)
2025-07-03 15:37:19,187 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x7A，操作ID: 100
2025-07-03 15:37:19,187 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作100数据解析: 8192 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,188 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作100成功: 地址0x7A 读取值0x2000
2025-07-03 15:37:19,188 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x7A = 0x2000 (读取)
2025-07-03 15:37:19,189 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0x7A, 值=2000
2025-07-03 15:37:19,189 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x7A, 值=0x2000
2025-07-03 15:37:19,189 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x7A = 0x2000
2025-07-03 15:37:19,189 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x7A = 0x2000
2025-07-03 15:37:19,190 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x7A
2025-07-03 15:37:19,190 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x7A 的UI更新
2025-07-03 15:37:19,190 - BatchOperationManager - [BatchOperationManager.py:752] - INFO - 📊 读取进度更新: 100/125
2025-07-03 15:37:19,191 - BatchOperationManager - [BatchOperationManager.py:783] - DEBUG - 🔍 批次检查: 已完成=100, 当前批次开始=50, 当前批次结束=100
2025-07-03 15:37:19,191 - BatchOperationManager - [BatchOperationManager.py:787] - INFO - ✅ 当前批次已完成，尝试处理下一批 (已完成: 100)
2025-07-03 15:37:19,191 - BatchOperationManager - [BatchOperationManager.py:791] - INFO - 🔄 获取到下一批数据，包含 25 个寄存器
2025-07-03 15:37:19,192 - BatchOperationManager - [BatchOperationManager.py:800] - DEBUG - 开始处理下一批，共 25 个寄存器
2025-07-03 15:37:19,192 - BatchOperationManager - [BatchOperationManager.py:646] - DEBUG - 开始处理批次中的 25 个寄存器
2025-07-03 15:37:19,192 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x7E
2025-07-03 15:37:19,192 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x7E
2025-07-03 15:37:19,192 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x83
2025-07-03 15:37:19,192 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x83
2025-07-03 15:37:19,192 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x87
2025-07-03 15:37:19,192 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x87
2025-07-03 15:37:19,192 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x92
2025-07-03 15:37:19,193 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x92
2025-07-03 15:37:19,193 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x93
2025-07-03 15:37:19,193 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x93
2025-07-03 15:37:19,193 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x94
2025-07-03 15:37:19,193 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x94
2025-07-03 15:37:19,193 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x95
2025-07-03 15:37:19,193 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x95
2025-07-03 15:37:19,193 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0x98
2025-07-03 15:37:19,193 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0x98
2025-07-03 15:37:19,193 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0xA0
2025-07-03 15:37:19,194 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0xA0
2025-07-03 15:37:19,194 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0xA1
2025-07-03 15:37:19,194 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0xA1
2025-07-03 15:37:19,194 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0xA2
2025-07-03 15:37:19,194 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0xA2
2025-07-03 15:37:19,194 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0xA3
2025-07-03 15:37:19,194 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0xA3
2025-07-03 15:37:19,194 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0xA4
2025-07-03 15:37:19,194 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0xA4
2025-07-03 15:37:19,194 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0xA5
2025-07-03 15:37:19,194 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0xA5
2025-07-03 15:37:19,194 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0xA6
2025-07-03 15:37:19,195 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0xA6
2025-07-03 15:37:19,195 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0xA7
2025-07-03 15:37:19,195 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0xA7
2025-07-03 15:37:19,195 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0xA8
2025-07-03 15:37:19,195 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0xA8
2025-07-03 15:37:19,195 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0xA9
2025-07-03 15:37:19,195 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0xA9
2025-07-03 15:37:19,195 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0xAA
2025-07-03 15:37:19,195 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0xAA
2025-07-03 15:37:19,196 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0xAB
2025-07-03 15:37:19,196 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0xAB
2025-07-03 15:37:19,196 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0xAC
2025-07-03 15:37:19,196 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0xAC
2025-07-03 15:37:19,197 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0xAD
2025-07-03 15:37:19,197 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0xAD
2025-07-03 15:37:19,197 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0xAE
2025-07-03 15:37:19,197 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0xAE
2025-07-03 15:37:19,197 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0xD8
2025-07-03 15:37:19,197 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0xD8
2025-07-03 15:37:19,197 - BatchOperationManager - [BatchOperationManager.py:670] - DEBUG - 发送读取请求: 0xDA
2025-07-03 15:37:19,197 - spi_service - [spi_service.py:241] - DEBUG - Queueing read for address: 0xDA
2025-07-03 15:37:19,197 - BatchOperationManager - [BatchOperationManager.py:673] - DEBUG - 批次中的所有寄存器读取请求已发送，等待回调
2025-07-03 15:37:19,202 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作101开始: read 0x7E None
2025-07-03 15:37:19,202 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 101
2025-07-03 15:37:19,202 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,202 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x7E，操作ID: 101
2025-07-03 15:37:19,202 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 7E04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,215 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x7E → 原始字节 0003 → 整数值 3 (0x0003)
2025-07-03 15:37:19,216 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x7E，操作ID: 101
2025-07-03 15:37:19,216 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作101数据解析: 3 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,216 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作101成功: 地址0x7E 读取值0x0003
2025-07-03 15:37:19,216 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作102开始: read 0x83 None
2025-07-03 15:37:19,216 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 102
2025-07-03 15:37:19,216 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x7E = 0x0003 (读取)
2025-07-03 15:37:19,216 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,217 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x7E, 值=0x0003
2025-07-03 15:37:19,217 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x83，操作ID: 102
2025-07-03 15:37:19,217 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x7E = 0x0003
2025-07-03 15:37:19,218 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 8304 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,218 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x7E = 0x0003
2025-07-03 15:37:19,219 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x7E
2025-07-03 15:37:19,219 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x7E 的UI更新
2025-07-03 15:37:19,245 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x83 → 原始字节 0070 → 整数值 112 (0x0070)
2025-07-03 15:37:19,246 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x83，操作ID: 102
2025-07-03 15:37:19,246 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作102数据解析: 112 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,246 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作102成功: 地址0x83 读取值0x0070
2025-07-03 15:37:19,246 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x83 = 0x0070 (读取)
2025-07-03 15:37:19,247 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作103开始: read 0x87 None
2025-07-03 15:37:19,247 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0x83, 值=0070
2025-07-03 15:37:19,249 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x83, 值=0x0070
2025-07-03 15:37:19,249 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x83 = 0x0070
2025-07-03 15:37:19,247 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 103
2025-07-03 15:37:19,249 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x83 = 0x0070
2025-07-03 15:37:19,250 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,250 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x83
2025-07-03 15:37:19,251 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x83 的UI更新
2025-07-03 15:37:19,250 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x87，操作ID: 103
2025-07-03 15:37:19,251 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 8704 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,276 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x87 → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:19,277 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x87，操作ID: 103
2025-07-03 15:37:19,277 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作103数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,277 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作103成功: 地址0x87 读取值0x0000
2025-07-03 15:37:19,278 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作104开始: read 0x92 None
2025-07-03 15:37:19,278 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 104
2025-07-03 15:37:19,278 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,278 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x92，操作ID: 104
2025-07-03 15:37:19,278 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 9204 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,279 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x87 = 0x0000 (读取)
2025-07-03 15:37:19,279 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x87, 值=0x0000
2025-07-03 15:37:19,279 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x87 = 0x0000
2025-07-03 15:37:19,280 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x87 = 0x0000
2025-07-03 15:37:19,280 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x87
2025-07-03 15:37:19,280 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x87 的UI更新
2025-07-03 15:37:19,292 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x92 → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:19,292 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x92，操作ID: 104
2025-07-03 15:37:19,293 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作104数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,293 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作104成功: 地址0x92 读取值0x0000
2025-07-03 15:37:19,294 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作105开始: read 0x93 None
2025-07-03 15:37:19,294 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 105
2025-07-03 15:37:19,294 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,294 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x92 = 0x0000 (读取)
2025-07-03 15:37:19,294 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x93，操作ID: 105
2025-07-03 15:37:19,295 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x92, 值=0x0000
2025-07-03 15:37:19,295 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 9304 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,296 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x92 = 0x0000
2025-07-03 15:37:19,297 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x92 = 0x0000
2025-07-03 15:37:19,297 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x92
2025-07-03 15:37:19,297 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x92 的UI更新
2025-07-03 15:37:19,322 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x93 → 原始字节 0007 → 整数值 7 (0x0007)
2025-07-03 15:37:19,323 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x93，操作ID: 105
2025-07-03 15:37:19,323 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作105数据解析: 7 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,324 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作105成功: 地址0x93 读取值0x0007
2025-07-03 15:37:19,325 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作106开始: read 0x94 None
2025-07-03 15:37:19,325 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 106
2025-07-03 15:37:19,325 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,325 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x94，操作ID: 106
2025-07-03 15:37:19,325 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 9404 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,326 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x93 = 0x0007 (读取)
2025-07-03 15:37:19,326 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0x93, 值=0007
2025-07-03 15:37:19,326 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x93, 值=0x0007
2025-07-03 15:37:19,326 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x93 = 0x0007
2025-07-03 15:37:19,327 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x93 = 0x0007
2025-07-03 15:37:19,327 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x93
2025-07-03 15:37:19,327 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x93 的UI更新
2025-07-03 15:37:19,338 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x94 → 原始字节 0014 → 整数值 20 (0x0014)
2025-07-03 15:37:19,338 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x94，操作ID: 106
2025-07-03 15:37:19,339 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作106数据解析: 20 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,339 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作106成功: 地址0x94 读取值0x0014
2025-07-03 15:37:19,339 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作107开始: read 0x95 None
2025-07-03 15:37:19,339 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x94 = 0x0014 (读取)
2025-07-03 15:37:19,339 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 107
2025-07-03 15:37:19,340 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0x94, 值=0014
2025-07-03 15:37:19,340 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,341 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x94, 值=0x0014
2025-07-03 15:37:19,341 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x95，操作ID: 107
2025-07-03 15:37:19,341 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x94 = 0x0014
2025-07-03 15:37:19,342 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 9504 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,342 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x94 = 0x0014
2025-07-03 15:37:19,343 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x94
2025-07-03 15:37:19,343 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x94 的UI更新
2025-07-03 15:37:19,365 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x95 → 原始字节 0200 → 整数值 512 (0x0200)
2025-07-03 15:37:19,365 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x95，操作ID: 107
2025-07-03 15:37:19,365 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作107数据解析: 512 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,365 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作107成功: 地址0x95 读取值0x0200
2025-07-03 15:37:19,366 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作108开始: read 0x98 None
2025-07-03 15:37:19,366 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x95 = 0x0200 (读取)
2025-07-03 15:37:19,366 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 108
2025-07-03 15:37:19,366 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0x95, 值=0200
2025-07-03 15:37:19,366 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,367 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x95, 值=0x0200
2025-07-03 15:37:19,367 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0x98，操作ID: 108
2025-07-03 15:37:19,367 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x95 = 0x0200
2025-07-03 15:37:19,367 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: 9804 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,368 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x95 = 0x0200
2025-07-03 15:37:19,368 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x95
2025-07-03 15:37:19,369 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x95 的UI更新
2025-07-03 15:37:19,381 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0x98 → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:19,381 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0x98，操作ID: 108
2025-07-03 15:37:19,381 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作108数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,381 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作108成功: 地址0x98 读取值0x0000
2025-07-03 15:37:19,381 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0x98 = 0x0000 (读取)
2025-07-03 15:37:19,381 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作109开始: read 0xA0 None
2025-07-03 15:37:19,381 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0x98, 值=0x0000
2025-07-03 15:37:19,381 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 109
2025-07-03 15:37:19,383 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0x98 = 0x0000
2025-07-03 15:37:19,383 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,383 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0x98 = 0x0000
2025-07-03 15:37:19,384 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0xA0，操作ID: 109
2025-07-03 15:37:19,384 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0x98
2025-07-03 15:37:19,384 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: A004 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,384 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0x98 的UI更新
2025-07-03 15:37:19,396 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0xA0 → 原始字节 1280 → 整数值 4736 (0x1280)
2025-07-03 15:37:19,397 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0xA0，操作ID: 109
2025-07-03 15:37:19,397 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作109数据解析: 4736 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,397 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作109成功: 地址0xA0 读取值0x1280
2025-07-03 15:37:19,397 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作110开始: read 0xA1 None
2025-07-03 15:37:19,397 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 110
2025-07-03 15:37:19,397 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,397 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0xA1，操作ID: 110
2025-07-03 15:37:19,397 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: A104 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,398 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0xA0 = 0x1280 (读取)
2025-07-03 15:37:19,398 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0xA0, 值=0x1280
2025-07-03 15:37:19,399 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0xA0 = 0x1280
2025-07-03 15:37:19,399 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0xA0 = 0x1280
2025-07-03 15:37:19,399 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0xA0
2025-07-03 15:37:19,399 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0xA0 的UI更新
2025-07-03 15:37:19,411 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0xA1 → 原始字节 5249 → 整数值 21065 (0x5249)
2025-07-03 15:37:19,412 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0xA1，操作ID: 110
2025-07-03 15:37:19,412 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作110数据解析: 21065 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,412 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作110成功: 地址0xA1 读取值0x5249
2025-07-03 15:37:19,412 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作111开始: read 0xA2 None
2025-07-03 15:37:19,413 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 111
2025-07-03 15:37:19,413 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,413 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0xA2，操作ID: 111
2025-07-03 15:37:19,413 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: A204 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,413 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0xA1 = 0x5249 (读取)
2025-07-03 15:37:19,414 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0xA1, 值=5249
2025-07-03 15:37:19,414 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0xA1, 值=0x5249
2025-07-03 15:37:19,414 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0xA1 = 0x5249
2025-07-03 15:37:19,416 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0xA1 = 0x5249
2025-07-03 15:37:19,416 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0xA1
2025-07-03 15:37:19,416 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0xA1 的UI更新
2025-07-03 15:37:19,416 - BatchOperationManager - [BatchOperationManager.py:752] - INFO - 📊 读取进度更新: 110/125
2025-07-03 15:37:19,417 - BatchOperationManager - [BatchOperationManager.py:783] - DEBUG - 🔍 批次检查: 已完成=110, 当前批次开始=100, 当前批次结束=125
2025-07-03 15:37:19,417 - BatchOperationManager - [BatchOperationManager.py:810] - DEBUG - ⏳ 当前批次未完成，继续等待 (已完成: 110, 批次结束: 125)
2025-07-03 15:37:19,427 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0xA2 → 原始字节 2C07 → 整数值 11271 (0x2C07)
2025-07-03 15:37:19,427 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0xA2，操作ID: 111
2025-07-03 15:37:19,427 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作111数据解析: 11271 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,427 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作111成功: 地址0xA2 读取值0x2C07
2025-07-03 15:37:19,427 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作112开始: read 0xA3 None
2025-07-03 15:37:19,427 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0xA2 = 0x2C07 (读取)
2025-07-03 15:37:19,427 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 112
2025-07-03 15:37:19,429 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,429 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0xA3，操作ID: 112
2025-07-03 15:37:19,428 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0xA2, 值=2C07
2025-07-03 15:37:19,429 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0xA2, 值=0x2C07
2025-07-03 15:37:19,429 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0xA2 = 0x2C07
2025-07-03 15:37:19,429 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: A304 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,430 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0xA2 = 0x2C07
2025-07-03 15:37:19,431 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0xA2
2025-07-03 15:37:19,431 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0xA2 的UI更新
2025-07-03 15:37:19,442 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0xA3 → 原始字节 8080 → 整数值 32896 (0x8080)
2025-07-03 15:37:19,442 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0xA3，操作ID: 112
2025-07-03 15:37:19,442 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作112数据解析: 32896 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,443 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作112成功: 地址0xA3 读取值0x8080
2025-07-03 15:37:19,443 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作113开始: read 0xA4 None
2025-07-03 15:37:19,443 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0xA3 = 0x8080 (读取)
2025-07-03 15:37:19,444 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 113
2025-07-03 15:37:19,444 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0xA3, 值=0x8080
2025-07-03 15:37:19,445 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,445 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0xA3 = 0x8080
2025-07-03 15:37:19,446 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0xA4，操作ID: 113
2025-07-03 15:37:19,446 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0xA3 = 0x8080
2025-07-03 15:37:19,447 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: A404 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,447 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0xA3
2025-07-03 15:37:19,448 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0xA3 的UI更新
2025-07-03 15:37:19,473 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0xA4 → 原始字节 0AA0 → 整数值 2720 (0x0AA0)
2025-07-03 15:37:19,473 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0xA4，操作ID: 113
2025-07-03 15:37:19,474 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作113数据解析: 2720 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,475 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作113成功: 地址0xA4 读取值0x0AA0
2025-07-03 15:37:19,475 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作114开始: read 0xA5 None
2025-07-03 15:37:19,475 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 114
2025-07-03 15:37:19,475 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,475 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0xA5，操作ID: 114
2025-07-03 15:37:19,476 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: A504 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,476 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0xA4 = 0x0AA0 (读取)
2025-07-03 15:37:19,476 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0xA4, 值=0x0AA0
2025-07-03 15:37:19,477 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0xA4 = 0x0AA0
2025-07-03 15:37:19,477 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0xA4 = 0x0AA0
2025-07-03 15:37:19,477 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0xA4
2025-07-03 15:37:19,477 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0xA4 的UI更新
2025-07-03 15:37:19,489 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0xA5 → 原始字节 0780 → 整数值 1920 (0x0780)
2025-07-03 15:37:19,489 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0xA5，操作ID: 114
2025-07-03 15:37:19,489 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作114数据解析: 1920 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,490 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作114成功: 地址0xA5 读取值0x0780
2025-07-03 15:37:19,490 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作115开始: read 0xA6 None
2025-07-03 15:37:19,490 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0xA5 = 0x0780 (读取)
2025-07-03 15:37:19,491 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 115
2025-07-03 15:37:19,491 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0xA5, 值=0x0780
2025-07-03 15:37:19,492 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0xA5 = 0x0780
2025-07-03 15:37:19,492 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,492 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0xA5 = 0x0780
2025-07-03 15:37:19,493 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0xA5
2025-07-03 15:37:19,493 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0xA5 的UI更新
2025-07-03 15:37:19,493 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0xA6，操作ID: 115
2025-07-03 15:37:19,494 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: A604 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,519 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0xA6 → 原始字节 00F8 → 整数值 248 (0x00F8)
2025-07-03 15:37:19,519 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0xA6，操作ID: 115
2025-07-03 15:37:19,520 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作115数据解析: 248 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,520 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作115成功: 地址0xA6 读取值0x00F8
2025-07-03 15:37:19,520 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0xA6 = 0x00F8 (读取)
2025-07-03 15:37:19,520 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作116开始: read 0xA7 None
2025-07-03 15:37:19,521 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 116
2025-07-03 15:37:19,521 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0xA6, 值=00F8
2025-07-03 15:37:19,521 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,522 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0xA6, 值=0x00F8
2025-07-03 15:37:19,522 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0xA6 = 0x00F8
2025-07-03 15:37:19,522 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0xA7，操作ID: 116
2025-07-03 15:37:19,523 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0xA6 = 0x00F8
2025-07-03 15:37:19,523 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: A704 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,523 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0xA6
2025-07-03 15:37:19,524 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0xA6 的UI更新
2025-07-03 15:37:19,546 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0xA7 → 原始字节 0080 → 整数值 128 (0x0080)
2025-07-03 15:37:19,547 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0xA7，操作ID: 116
2025-07-03 15:37:19,547 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作116数据解析: 128 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,547 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作116成功: 地址0xA7 读取值0x0080
2025-07-03 15:37:19,547 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作117开始: read 0xA8 None
2025-07-03 15:37:19,547 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 117
2025-07-03 15:37:19,548 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,548 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0xA7 = 0x0080 (读取)
2025-07-03 15:37:19,548 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0xA8，操作ID: 117
2025-07-03 15:37:19,548 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0xA7, 值=0x0080
2025-07-03 15:37:19,549 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: A804 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,549 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0xA7 = 0x0080
2025-07-03 15:37:19,550 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0xA7 = 0x0080
2025-07-03 15:37:19,551 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0xA7
2025-07-03 15:37:19,551 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0xA7 的UI更新
2025-07-03 15:37:19,562 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0xA8 → 原始字节 0026 → 整数值 38 (0x0026)
2025-07-03 15:37:19,562 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0xA8，操作ID: 117
2025-07-03 15:37:19,562 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作117数据解析: 38 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,563 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作117成功: 地址0xA8 读取值0x0026
2025-07-03 15:37:19,563 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作118开始: read 0xA9 None
2025-07-03 15:37:19,563 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 118
2025-07-03 15:37:19,564 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,564 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0xA9，操作ID: 118
2025-07-03 15:37:19,564 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0xA8 = 0x0026 (读取)
2025-07-03 15:37:19,564 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: A904 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,564 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0xA8, 值=0x0026
2025-07-03 15:37:19,565 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0xA8 = 0x0026
2025-07-03 15:37:19,565 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0xA8 = 0x0026
2025-07-03 15:37:19,565 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0xA8
2025-07-03 15:37:19,565 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0xA8 的UI更新
2025-07-03 15:37:19,578 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0xA9 → 原始字节 0000 → 整数值 0 (0x0000)
2025-07-03 15:37:19,578 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0xA9，操作ID: 118
2025-07-03 15:37:19,578 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作118数据解析: 0 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,579 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作118成功: 地址0xA9 读取值0x0000
2025-07-03 15:37:19,579 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作119开始: read 0xAA None
2025-07-03 15:37:19,579 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 119
2025-07-03 15:37:19,579 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,579 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0xAA，操作ID: 119
2025-07-03 15:37:19,579 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: AA04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,579 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0xA9 = 0x0000 (读取)
2025-07-03 15:37:19,579 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0xA9, 值=0x0000
2025-07-03 15:37:19,579 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0xA9 = 0x0000
2025-07-03 15:37:19,580 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0xA9 = 0x0000
2025-07-03 15:37:19,580 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0xA9
2025-07-03 15:37:19,580 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0xA9 的UI更新
2025-07-03 15:37:19,594 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0xAA → 原始字节 1124 → 整数值 4388 (0x1124)
2025-07-03 15:37:19,594 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0xAA，操作ID: 119
2025-07-03 15:37:19,594 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作119数据解析: 4388 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,594 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作119成功: 地址0xAA 读取值0x1124
2025-07-03 15:37:19,595 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作120开始: read 0xAB None
2025-07-03 15:37:19,595 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0xAA = 0x1124 (读取)
2025-07-03 15:37:19,595 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 120
2025-07-03 15:37:19,595 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0xAA, 值=1124
2025-07-03 15:37:19,596 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0xAA, 值=0x1124
2025-07-03 15:37:19,596 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0xAA = 0x1124
2025-07-03 15:37:19,596 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,596 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0xAB，操作ID: 120
2025-07-03 15:37:19,596 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0xAA = 0x1124
2025-07-03 15:37:19,597 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: AB04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,597 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0xAA
2025-07-03 15:37:19,597 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0xAA 的UI更新
2025-07-03 15:37:19,609 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0xAB → 原始字节 1124 → 整数值 4388 (0x1124)
2025-07-03 15:37:19,609 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0xAB，操作ID: 120
2025-07-03 15:37:19,610 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作120数据解析: 4388 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,610 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作120成功: 地址0xAB 读取值0x1124
2025-07-03 15:37:19,610 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0xAB = 0x1124 (读取)
2025-07-03 15:37:19,610 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作121开始: read 0xAC None
2025-07-03 15:37:19,611 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 121
2025-07-03 15:37:19,611 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0xAB, 值=1124
2025-07-03 15:37:19,611 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,612 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0xAB, 值=0x1124
2025-07-03 15:37:19,612 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0xAC，操作ID: 121
2025-07-03 15:37:19,612 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0xAB = 0x1124
2025-07-03 15:37:19,612 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: AC04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,613 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0xAB = 0x1124
2025-07-03 15:37:19,613 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0xAB
2025-07-03 15:37:19,613 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0xAB 的UI更新
2025-07-03 15:37:19,613 - BatchOperationManager - [BatchOperationManager.py:752] - INFO - 📊 读取进度更新: 120/125
2025-07-03 15:37:19,615 - BatchOperationManager - [BatchOperationManager.py:783] - DEBUG - 🔍 批次检查: 已完成=120, 当前批次开始=100, 当前批次结束=125
2025-07-03 15:37:19,615 - BatchOperationManager - [BatchOperationManager.py:810] - DEBUG - ⏳ 当前批次未完成，继续等待 (已完成: 120, 批次结束: 125)
2025-07-03 15:37:19,624 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0xAC → 原始字节 2244 → 整数值 8772 (0x2244)
2025-07-03 15:37:19,625 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0xAC，操作ID: 121
2025-07-03 15:37:19,625 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作121数据解析: 8772 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,626 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作121成功: 地址0xAC 读取值0x2244
2025-07-03 15:37:19,626 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作122开始: read 0xAD None
2025-07-03 15:37:19,626 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0xAC = 0x2244 (读取)
2025-07-03 15:37:19,627 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 122
2025-07-03 15:37:19,627 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0xAC, 值=2244
2025-07-03 15:37:19,628 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,628 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0xAC, 值=0x2244
2025-07-03 15:37:19,629 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0xAD，操作ID: 122
2025-07-03 15:37:19,629 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0xAC = 0x2244
2025-07-03 15:37:19,629 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: AD04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,630 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0xAC = 0x2244
2025-07-03 15:37:19,630 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0xAC
2025-07-03 15:37:19,630 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0xAC 的UI更新
2025-07-03 15:37:19,655 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0xAD → 原始字节 2244 → 整数值 8772 (0x2244)
2025-07-03 15:37:19,655 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0xAD，操作ID: 122
2025-07-03 15:37:19,655 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作122数据解析: 8772 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,655 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作122成功: 地址0xAD 读取值0x2244
2025-07-03 15:37:19,657 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作123开始: read 0xAE None
2025-07-03 15:37:19,657 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0xAD = 0x2244 (读取)
2025-07-03 15:37:19,657 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 123
2025-07-03 15:37:19,657 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0xAD, 值=2244
2025-07-03 15:37:19,658 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,658 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0xAD, 值=0x2244
2025-07-03 15:37:19,658 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0xAE，操作ID: 123
2025-07-03 15:37:19,658 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0xAD = 0x2244
2025-07-03 15:37:19,659 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: AE04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,659 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0xAD = 0x2244
2025-07-03 15:37:19,660 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0xAD
2025-07-03 15:37:19,660 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0xAD 的UI更新
2025-07-03 15:37:19,671 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0xAE → 原始字节 008F → 整数值 143 (0x008F)
2025-07-03 15:37:19,671 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0xAE，操作ID: 123
2025-07-03 15:37:19,671 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作123数据解析: 143 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,672 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作123成功: 地址0xAE 读取值0x008F
2025-07-03 15:37:19,672 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作124开始: read 0xD8 None
2025-07-03 15:37:19,672 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0xAE = 0x008F (读取)
2025-07-03 15:37:19,672 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 124
2025-07-03 15:37:19,673 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0xAE, 值=008F
2025-07-03 15:37:19,673 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,674 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0xAE, 值=0x008F
2025-07-03 15:37:19,674 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0xD8，操作ID: 124
2025-07-03 15:37:19,674 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0xAE = 0x008F
2025-07-03 15:37:19,674 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: D804 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,675 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0xAE = 0x008F
2025-07-03 15:37:19,676 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0xAE
2025-07-03 15:37:19,676 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0xAE 的UI更新
2025-07-03 15:37:19,686 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0xD8 → 原始字节 0014 → 整数值 20 (0x0014)
2025-07-03 15:37:19,686 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0xD8，操作ID: 124
2025-07-03 15:37:19,686 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作124数据解析: 20 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,686 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作124成功: 地址0xD8 读取值0x0014
2025-07-03 15:37:19,686 - spi_service_impl - [spi_service_impl.py:433] - INFO - 操作125开始: read 0xDA None
2025-07-03 15:37:19,687 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0xD8 = 0x0014 (读取)
2025-07-03 15:37:19,687 - spi_service_impl - [spi_service_impl.py:463] - DEBUG - 检查SPI可用性，操作ID: 125
2025-07-03 15:37:19,687 - RegisterManager - [RegisterManager.py:77] - DEBUG - RegisterManager更新寄存器: 0xD8, 值=0014
2025-07-03 15:37:19,687 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-03 15:37:19,687 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0xD8, 值=0x0014
2025-07-03 15:37:19,688 - spi_service_impl - [spi_service_impl.py:537] - INFO - 开始执行读操作，地址: 0xDA，操作ID: 125
2025-07-03 15:37:19,688 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0xD8 = 0x0014
2025-07-03 15:37:19,688 - spiPrivacy - [spiPrivacy.py:222] - INFO - 生成控制字: DA04 (AE=0 R/W=1 Step=0 Cycle=0 Z=0)
2025-07-03 15:37:19,688 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0xD8 = 0x0014
2025-07-03 15:37:19,689 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0xD8
2025-07-03 15:37:19,689 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0xD8 的UI更新
2025-07-03 15:37:19,702 - spiPrivacy - [spiPrivacy.py:428] - INFO - 读取成功: 地址 0xDA → 原始字节 8000 → 整数值 32768 (0x8000)
2025-07-03 15:37:19,702 - spi_service_impl - [spi_service_impl.py:549] - INFO - 读操作成功，地址: 0xDA，操作ID: 125
2025-07-03 15:37:19,702 - spi_service_impl - [spi_service_impl.py:566] - DEBUG - 操作125数据解析: 32768 , type: <class 'int'>， address type: <class 'str'>
2025-07-03 15:37:19,702 - spi_service_impl - [spi_service_impl.py:581] - INFO - 操作125成功: 地址0xDA 读取值0x8000
2025-07-03 15:37:19,703 - spi_service - [spi_service.py:466] - DEBUG - SPI操作完成: 0xDA = 0x8000 (读取)
2025-07-03 15:37:19,703 - RegisterOperationService - [RegisterOperationService.py:552] - DEBUG - 批量操作期间跳过RegisterOperationService更新信号: 0xDA, 值=0x8000
2025-07-03 15:37:19,703 - RegisterOperationService - [RegisterOperationService.py:231] - DEBUG - 批量模式下跳过UI更新: 0xDA = 0x8000
2025-07-03 15:37:19,704 - RegisterOperationService - [RegisterOperationService.py:570] - INFO - SPI操作完成: 读取 0xDA = 0x8000
2025-07-03 15:37:19,704 - SPIOperationCoordinator - [SPIOperationCoordinator.py:145] - DEBUG - handle_spi_result: in_batch_operation=True, addr=0xDA
2025-07-03 15:37:19,704 - SPIOperationCoordinator - [SPIOperationCoordinator.py:165] - DEBUG - 批量操作期间，跳过寄存器 0xDA 的UI更新
2025-07-03 15:37:19,704 - BatchOperationManager - [BatchOperationManager.py:752] - INFO - 📊 读取进度更新: 125/125
2025-07-03 15:37:19,711 - BatchOperationManager - [BatchOperationManager.py:769] - INFO - 所有读取操作已完成: 125/125
2025-07-03 15:37:19,712 - BatchOperationManager - [BatchOperationManager.py:923] - INFO - 开始完成批量读取操作的清理工作
2025-07-03 15:37:19,712 - BatchOperationManager - [BatchOperationManager.py:481] - DEBUG - 成功断开读取操作信号
2025-07-03 15:37:19,722 - BatchOperationManager - [BatchOperationManager.py:955] - DEBUG - 成功隐藏进度对话框，已安排延迟删除
2025-07-03 15:37:19,723 - BatchOperationManager - [BatchOperationManager.py:1231] - DEBUG - 已恢复中央控件更新
2025-07-03 15:37:19,723 - BatchOperationManager - [BatchOperationManager.py:1237] - DEBUG - 已恢复主窗口更新
2025-07-03 15:37:19,723 - BatchOperationManager - [BatchOperationManager.py:1239] - DEBUG - UI更新已完全恢复
2025-07-03 15:37:19,723 - BatchOperationManager - [BatchOperationManager.py:969] - INFO - 批量读取操作完成，UI按钮已重新启用
2025-07-03 15:37:19,723 - spi_service_impl - [spi_service_impl.py:241] - INFO - 操作队列已清空
2025-07-03 15:37:19,723 - spi_service - [spi_service.py:289] - INFO - SPI操作队列已清空
2025-07-03 15:37:19,723 - BatchOperationManager - [BatchOperationManager.py:975] - DEBUG - 批量读取完成后已清空SPI操作队列
2025-07-03 15:37:19,724 - BatchOperationManager - [BatchOperationManager.py:987] - INFO - 成功读取所有寄存器：共 125 个
2025-07-03 15:37:19,724 - BatchOperationManager - [BatchOperationManager.py:1145] - INFO - 批量读取完成，开始更新所有寄存器变量并刷新UI
2025-07-03 15:37:19,724 - BatchOperationManager - [BatchOperationManager.py:1149] - INFO - 所有寄存器变量已在读取过程中更新完成
2025-07-03 15:37:19,724 - BatchOperationManager - [BatchOperationManager.py:1153] - INFO - 开始刷新树视图中的所有寄存器显示
2025-07-03 15:37:19,725 - BatchOperationManager - [BatchOperationManager.py:1160] - INFO - 成功刷新了 125 个寄存器的树视图显示
2025-07-03 15:37:19,725 - BatchOperationManager - [BatchOperationManager.py:1167] - INFO - 刷新当前选中寄存器的详细显示: 0x00
2025-07-03 15:37:19,725 - RegisterDisplayManager - [RegisterDisplayManager.py:67] - DEBUG - DisplayManager: 跳过重复的位字段显示更新 - 地址: 0x00, 值: 0x1300
2025-07-03 15:37:19,726 - BatchOperationManager - [BatchOperationManager.py:1175] - INFO - 批量读取完成后成功刷新了当前寄存器 0x00 的显示
2025-07-03 15:37:19,726 - BatchOperationManager - [BatchOperationManager.py:1181] - INFO - 批量读取完成后UI刷新完成
2025-07-03 15:37:19,732 - BatchOperationManager - [BatchOperationManager.py:1008] - INFO - 批量读取操作清理工作完成
2025-07-03 15:37:19,733 - BatchOperationManager - [BatchOperationManager.py:1015] - INFO - 批量读取性能数据: 耗时 2.601秒, 成功 True, 完成数 0
2025-07-03 15:37:19,733 - RegisterMainWindow - [RegisterMainWindow.py:387] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-03 15:37:19,735 - RegisterMainWindow - [RegisterMainWindow.py:387] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-03 15:37:19,747 - RegisterMainWindow - [RegisterMainWindow.py:387] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-03 15:37:19,747 - RegisterMainWindow - [RegisterMainWindow.py:387] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-03 15:37:19,775 - RegisterMainWindow - [RegisterMainWindow.py:406] - DEBUG - 主窗口强制置顶完成
2025-07-03 15:37:19,775 - RegisterMainWindow - [RegisterMainWindow.py:311] - DEBUG - 主窗口已通过焦点置顶
2025-07-03 15:37:19,794 - RegisterMainWindow - [RegisterMainWindow.py:406] - DEBUG - 主窗口强制置顶完成
2025-07-03 15:37:19,794 - RegisterMainWindow - [RegisterMainWindow.py:315] - DEBUG - 主窗口已通过激活事件置顶
2025-07-03 15:37:19,804 - RegisterMainWindow - [RegisterMainWindow.py:406] - DEBUG - 主窗口强制置顶完成
2025-07-03 15:37:19,804 - RegisterMainWindow - [RegisterMainWindow.py:311] - DEBUG - 主窗口已通过焦点置顶
2025-07-03 15:37:19,824 - RegisterMainWindow - [RegisterMainWindow.py:406] - DEBUG - 主窗口强制置顶完成
2025-07-03 15:37:19,825 - RegisterMainWindow - [RegisterMainWindow.py:315] - DEBUG - 主窗口已通过激活事件置顶
2025-07-03 15:37:19,922 - BatchOperationManager - [BatchOperationManager.py:948] - DEBUG - 成功安全删除进度对话框
2025-07-03 15:37:21,321 - ApplicationLifecycleManager - [ApplicationLifecycleManager.py:41] - INFO - 应用程序正在关闭...
2025-07-03 15:37:21,324 - BatchOperationManager - [BatchOperationManager.py:483] - DEBUG - 断开读取信号时出现异常（可能未连接）: 'method' object is not connected
2025-07-03 15:37:21,324 - BatchOperationManager - [BatchOperationManager.py:495] - DEBUG - 断开写入信号时出现异常（可能未连接）: 'method' object is not connected
2025-07-03 15:37:21,324 - BatchOperationManager - [BatchOperationManager.py:533] - INFO - 已强制取消所有批量操作
2025-07-03 15:37:21,324 - ApplicationLifecycleManager - [ApplicationLifecycleManager.py:69] - INFO - 已强制取消所有批量操作并清理资源
2025-07-03 15:37:21,325 - PluginWindowService - [PluginWindowService.py:513] - INFO - 所有插件窗口已关闭
2025-07-03 15:37:21,325 - WindowManagementService - [WindowManagementService.py:200] - INFO - 已通过插件服务关闭所有插件窗口
2025-07-03 15:37:21,325 - WindowManagementService - [WindowManagementService.py:250] - INFO - 已清空所有标签页并隐藏标签页容器
2025-07-03 15:37:21,325 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-03 15:37:21,325 - WindowManagementService - [WindowManagementService.py:227] - INFO - 所有管理的窗口已关闭
2025-07-03 15:37:21,325 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-03 15:37:21,325 - ConfigurationService - [ConfigurationService.py:283] - DEBUG - 设置已更新: simulation_mode = False
2025-07-03 15:37:21,325 - ConfigurationService - [ConfigurationService.py:333] - INFO - 模拟模式设置已保存: False
2025-07-03 15:37:21,326 - spi_service - [spi_service.py:156] - INFO - SPIService cleaning up...
2025-07-03 15:37:21,326 - spi_service - [spi_service.py:421] - DEBUG - Cleaning up SPI thread...
2025-07-03 15:37:21,330 - port_manager - [port_manager.py:149] - INFO - 已关闭端口: COM4
2025-07-03 15:37:21,330 - spiPrivacy - [spiPrivacy.py:466] - INFO - 通过端口管理器关闭串口: COM4
2025-07-03 15:37:21,330 - spi_service - [spi_service.py:428] - DEBUG - Worker stop requested
2025-07-03 15:37:21,330 - spi_service - [spi_service.py:437] - INFO - Requesting SPI thread termination...
2025-07-03 15:37:21,331 - spi_service - [spi_service.py:443] - INFO - SPI thread stopped.
2025-07-03 15:37:21,332 - spi_service - [spi_service.py:455] - DEBUG - SPI thread resources cleaned.
2025-07-03 15:37:21,332 - spi_service - [spi_service.py:166] - INFO - SPIService cleanup finished.
2025-07-03 15:37:21,332 - ApplicationLifecycleManager - [ApplicationLifecycleManager.py:103] - INFO - 应用程序关闭完成
2025-07-03 15:37:21,392 - port_manager - [port_manager.py:211] - INFO - 已清理所有COM端口连接
