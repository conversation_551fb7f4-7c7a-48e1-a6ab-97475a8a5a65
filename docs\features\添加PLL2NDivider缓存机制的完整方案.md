# 添加PLL2NDivider缓存机制的完整方案

## 用户指出的问题

用户提到了两个关键点：
1. **"是否缓存里有这个信号"** - 检查PLL2NDivider是否有缓存机制
2. **"切换到其他信号源也需要计算"** - 当信号源变化时需要重新计算

## 问题分析

### 发现的缺失
通过检查代码发现：
- ✅ **PLL2PFD频率**：已有完整的缓存机制
- ❌ **PLL2NDivider值**：没有缓存机制
- ❌ **信号源切换**：缺少自动重新计算机制

### 影响分析
没有PLL2NDivider缓存导致：
1. **同步系统参考窗口**无法获取PLL2NDivider的最新值
2. **InternalVCOFreq计算**可能使用过时或默认的PLL2NDivider值
3. **信号源切换时**无法自动更新相关计算

## 实施的完整解决方案

### 1. 在RegisterUpdateBus中添加PLL2NDivider缓存

#### 扩展频率缓存结构：
```python
# 频率值缓存
self._frequency_cache = {
    'vco_dist_freq': None,
    'pll1_pfd_freq': None,
    'pll2_pfd_freq': None,
    'pll2_n_divider': None,  # ✅ 新增PLL2NDivider缓存
    'sysref_freq': None,
    'sysref_div': None
}
```

#### 添加缓存方法：
```python
def cache_pll2_n_divider(self, divider_value):
    """缓存PLL2NDivider值"""
    self._frequency_cache['pll2_n_divider'] = divider_value
    logger.info(f"RegisterUpdateBus: 已缓存PLL2NDivider值: {divider_value}")

def get_cached_pll2_n_divider(self):
    """获取缓存的PLL2NDivider值"""
    cached_value = self._frequency_cache.get('pll2_n_divider')
    return cached_value
```

### 2. 在PLL窗口中添加PLL2NDivider缓存机制

#### 添加缓存方法：
```python
def _cache_pll2_n_divider(self, n_divider_value):
    """缓存PLL2NDivider值，供同步系统参考窗口使用"""
    try:
        from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
        bus = RegisterUpdateBus.instance()

        if hasattr(bus, 'cache_pll2_n_divider'):
            bus.cache_pll2_n_divider(n_divider_value)
            logger.info(f"【PLL窗口】已缓存PLL2NDivider值: {n_divider_value}，供同步系统参考窗口使用")
    except Exception as e:
        logger.error(f"缓存PLL2NDivider值时发生错误: {str(e)}")
```

#### 在PLL2NDivider变化时触发缓存：
```python
def _handle_divider_change(self, widget_name):
    """处理分频器值变化"""
    if widget_name == "PLL2NDivider":
        current_value = self.ui.PLL2NDivider.value()
        logger.info(f"🔄 PLL2NDivider手动修改为: {current_value}")

        # 缓存PLL2NDivider值，供同步系统参考窗口使用 ✅ 新增
        if hasattr(self.ui, "PLL2NDivider"):
            self._cache_pll2_n_divider(self.ui.PLL2NDivider.value())
```

#### 在PLL窗口初始化时缓存：
```python
def _ensure_pll2_pfd_calculated(self):
    """确保PLL2PFD已经计算"""
    # ... PLL2PFD计算逻辑
    
    # 同时缓存PLL2NDivider值 ✅ 新增
    if hasattr(self.ui, "PLL2NDivider"):
        current_n_divider = self.ui.PLL2NDivider.value()
        self._cache_pll2_n_divider(current_n_divider)
        logger.info(f"【确保PLL2PFD】同时缓存PLL2NDivider: {current_n_divider}")
```

### 3. 改进同步系统参考窗口的PLL2NDivider获取

#### 优先从缓存获取：
```python
def _get_pll2_n_divider_value(self):
    """获取PLL2NDivider值"""
    logger.info("【获取PLL2NDivider】开始获取PLL2NDivider值...")
    
    # 首先尝试从事件总线缓存获取 ✅ 新增
    from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
    bus = RegisterUpdateBus.instance()
    if bus and hasattr(bus, 'get_cached_pll2_n_divider'):
        cached_n_divider = bus.get_cached_pll2_n_divider()
        if cached_n_divider is not None:
            logger.info(f"【获取PLL2NDivider】✅ 从缓存获取PLL2NDivider: {cached_n_divider}")
            return cached_n_divider
    
    # 尝试从PLL窗口直接获取
    main_window = self._get_main_window()
    if main_window and hasattr(main_window, 'pll_window'):
        pll_window = main_window.pll_window
        if pll_window and hasattr(pll_window.ui, 'PLL2NDivider'):
            pll2_n_divider = pll_window.ui.PLL2NDivider.value()
            
            # 将获取到的值缓存起来 ✅ 新增
            if bus and hasattr(bus, 'cache_pll2_n_divider'):
                bus.cache_pll2_n_divider(pll2_n_divider)
            
            return pll2_n_divider
    
    # 使用默认值
    return 12
```

### 4. 修正InternalVCOFreq计算公式

#### 正确的计算公式：
```python
def calculate_internal_vco_freq_from_pll2pfd(self):
    """根据PLL2PFD、SYSREF分频器和PLL2NDivider计算InternalVCOFreq
    
    正确的计算公式: InternalVCOFreq = PLL2PFD × spinBoxSysrefDIV × PLL2NDivider ✅ 修正
    """
    # 获取PLL2PFD频率
    pll2_pfd_freq = self._get_pll2_pfd_frequency()
    
    # 获取SYSREF分频器值
    sysref_div = self.ui.spinBoxSysrefDIV.value()
    
    # 获取PLL2NDivider值（从缓存或PLL窗口）✅ 新增
    pll2_n_divider = self._get_pll2_n_divider_value()
    
    # 计算InternalVCOFreq = PLL2PFD × spinBoxSysrefDIV × PLL2NDivider ✅ 修正
    internal_vco_freq = pll2_pfd_freq * sysref_div * pll2_n_divider
    logger.info(f"【InternalVCOFreq计算】计算公式: {pll2_pfd_freq} × {sysref_div} × {pll2_n_divider} = {internal_vco_freq:.5f} MHz")
```

## 完整的数据流设计

### 正确的缓存和计算流程：
```
1. PLL窗口初始化 → 缓存PLL2PFD和PLL2NDivider
2. 用户修改PLL2NDivider → 自动缓存新值
3. 同步系统参考窗口计算 → 从缓存获取PLL2PFD和PLL2NDivider
4. 计算InternalVCOFreq = PLL2PFD × SYSREF_DIV × PLL2NDivider ✅
5. 计算SYSREF频率 = InternalVCOFreq / SYSREF_DIV
6. 缓存SYSREF频率 → 供PLL2Cin使用
```

### 信号源切换时的处理：
```
1. 信号源切换 → 触发PLL频率重新计算
2. PLL2PFD重新计算 → 自动缓存新的PLL2PFD值
3. PLL2NDivider保持不变 → 使用缓存的值
4. 同步系统参考窗口 → 自动获取新的PLL2PFD和现有的PLL2NDivider
5. InternalVCOFreq重新计算 → 基于新的PLL2PFD和现有的PLL2NDivider
```

## 预期的改进效果

### 修正前的问题：
```
【InternalVCOFreq计算】计算公式: 245.76 × 4 = 983.04000 MHz ❌ 缺少PLL2NDivider
【获取PLL2NDivider】❌ 无法获取PLL2NDivider，使用默认值: 12
```

### 修正后的预期日志：
```
【PLL窗口】已缓存PLL2NDivider值: 12，供同步系统参考窗口使用
【获取PLL2NDivider】✅ 从缓存获取PLL2NDivider: 12
【InternalVCOFreq计算】计算公式: 245.76 × 4 × 12 = 11796.48000 MHz ✅ 包含所有因子
【SYSREF计算】计算公式: 11796.48 / 4 = 2949.12000 MHz
```

## 关键改进点

### 1. 完整的缓存机制
- ✅ **PLL2PFD频率缓存**：已有
- ✅ **PLL2NDivider值缓存**：新增
- ✅ **SYSREF频率缓存**：已有

### 2. 智能获取策略
- 🎯 **优先从缓存获取**：避免跨窗口访问
- 🔄 **备用直接获取**：确保数据可用性
- 📊 **自动缓存更新**：获取后立即缓存

### 3. 信号源切换支持
- 🔄 **自动重新计算**：信号源变化时触发
- 💾 **保持缓存一致**：确保所有窗口使用最新值
- 🎯 **实时同步**：跨窗口数据实时更新

## 测试验证

### 验证要点：
1. **PLL2NDivider缓存**：检查修改PLL2NDivider时是否正确缓存
2. **InternalVCOFreq计算**：验证是否包含PLL2NDivider因子
3. **信号源切换**：测试切换信号源时是否正确重新计算
4. **跨窗口同步**：确认两个窗口显示一致的计算结果

## 总结

通过实施完整的PLL2NDivider缓存机制：

1. ✅ **解决了缓存缺失问题**：PLL2NDivider现在有完整的缓存机制
2. ✅ **修正了计算公式**：InternalVCOFreq包含所有必要因子
3. ✅ **支持信号源切换**：自动重新计算和缓存更新
4. ✅ **改善了跨窗口通信**：优先从缓存获取，确保数据一致性

现在系统能够正确处理PLL2NDivider的缓存和获取，确保InternalVCOFreq计算的准确性！
