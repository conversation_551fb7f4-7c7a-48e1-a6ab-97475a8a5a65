# 分离窗口修复报告

## 🚨 问题描述

用户报告了一个严重的资源管理问题：

1. **分离窗口后窗口消失**：分离出来的窗口应该保持可用，但实际上窗口对象被删除了
2. **后台持续报错**：分离后控制台持续输出错误日志
3. **定时器未停止**：全局鼠标监控定时器在窗口删除后仍在运行

### 错误日志示例
```
2025-06-09 12:34:28,572 - core.services.plugin.PluginIntegrationService - ERROR - 检查鼠标状态失败: wrapped C/C++ object of type ModernClkinControlHandler has been deleted
```

## 🔍 问题根本原因

### 1. 资源清理过度
原始的分离窗口实现过于激进地清理了资源：

```python
# 问题代码
def _cleanup_container_resources(self, container, plugin_name):
    if container.layout():
        while layout.count():
            item = layout.takeAt(0)
            if item.widget():
                item.widget().setParent(None)  # ❌ 这里删除了窗口对象
```

### 2. 定时器生命周期管理问题
全局鼠标监控定时器没有在窗口分离时正确停止：

```python
# 问题：定时器继续运行，试图访问已删除的窗口对象
def _check_mouse_state(self, window, plugin_name):
    # 没有检查窗口有效性
    window_rect = window.frameGeometry()  # ❌ 访问已删除对象
```

### 3. 窗口有效性检查缺失
没有在访问窗口对象前检查其有效性，导致访问已删除的C++对象。

## 🛠️ 修复方案

### 1. 分离容器清理和窗口清理

#### 1.1 新增专用的容器清理方法
```python
def _cleanup_container_only(self, container, plugin_name):
    """只清理容器资源，不影响窗口（用于分离窗口时）"""
    try:
        if container:
            # 只清理布局，不删除子控件
            if container.layout():
                layout = container.layout()
                # 移除所有子控件，但不删除它们
                while layout.count():
                    item = layout.takeAt(0)
                    # ✅ 不调用 item.widget().setParent(None)，保持窗口存活
                
                # 删除布局
                container.setLayout(None)

            # 清理容器属性
            if hasattr(container, 'plugin_name'):
                delattr(container, 'plugin_name')
            if hasattr(container, 'plugin_window'):
                delattr(container, 'plugin_window')

            # 设置容器父级为None，确保被垃圾回收
            container.setParent(None)
```

#### 1.2 改进分离窗口方法
```python
def undock_window_from_tab(self, plugin_name: str):
    """将窗口从标签页中分离为悬浮窗口（修复版本，保持窗口存活）"""
    try:
        # 获取窗口引用
        if container and container.layout():
            if container.layout().count() > 0:
                item = container.layout().itemAt(0)
                if item:
                    window = item.widget()
                    # 从容器中移除窗口，但不删除窗口
                    container.layout().removeWidget(window)
                    # ✅ 重要：设置窗口父级为None，但不删除窗口
                    window.setParent(None)

        # 移除标签页（这会删除容器，但不会删除窗口）
        tab_widget.removeTab(i)

        # ✅ 只清理容器资源，不清理窗口
        if container:
            self._cleanup_container_only(container, plugin_name)

        # 如果找到了窗口，重新配置为悬浮窗口
        if window:
            # ✅ 确保窗口在插件窗口字典中
            self.plugin_windows[plugin_name] = window
            
            # 重新配置为悬浮窗口
            self._configure_plugin_window(window, plugin_name)
            window.show()
```

### 2. 定时器生命周期管理

#### 2.1 新增停止监控方法
```python
def _stop_global_mouse_monitor(self, window, plugin_name):
    """停止全局鼠标监控"""
    try:
        if hasattr(window, '_mouse_monitor_timer'):
            timer = window._mouse_monitor_timer
            if timer and timer.isActive():
                timer.stop()  # ✅ 停止定时器
                logger.info(f"🔧 [全局监控] 已停止 {plugin_name} 的鼠标状态监控")
            
            # 清理定时器引用
            delattr(window, '_mouse_monitor_timer')
            
        # 清理全局鼠标状态
        if hasattr(window, '_global_mouse_pressed'):
            delattr(window, '_global_mouse_pressed')
        if hasattr(window, '_global_drag_start'):
            delattr(window, '_global_drag_start')
        if hasattr(window, '_global_dragging'):
            delattr(window, '_global_dragging')
```

#### 2.2 在窗口关闭时停止监控
```python
def _on_plugin_window_closed(self, plugin_name: str, from_tab_close: bool = False):
    """处理插件窗口关闭"""
    try:
        # ✅ 停止全局鼠标监控（如果存在）
        if plugin_name in self.plugin_windows:
            window = self.plugin_windows[plugin_name]
            if window and self._is_window_valid(window):
                self._stop_global_mouse_monitor(window, plugin_name)
            
            # 移除窗口引用
            del self.plugin_windows[plugin_name]
```

### 3. 窗口有效性检查

#### 3.1 改进窗口有效性检查
```python
def _is_window_valid(self, window):
    """检查窗口是否有效"""
    try:
        if not window:
            return False
            
        # 检查窗口是否被删除
        try:
            # 尝试访问窗口的基本属性
            _ = window.isVisible()
            _ = window.windowTitle()
            return True
        except RuntimeError as e:
            if "wrapped C/C++ object" in str(e) and "has been deleted" in str(e):
                logger.warning(f"检测到窗口对象已被删除: {str(e)}")
                return False
            else:
                raise e
                
    except Exception as e:
        logger.error(f"检查窗口有效性时出错: {str(e)}")
        return False
```

#### 3.2 在监控方法中添加有效性检查
```python
def _check_mouse_state(self, window, plugin_name):
    """检查鼠标状态（全局监控方法）"""
    try:
        # ✅ 首先检查窗口是否仍然有效
        if not self._is_window_valid(window):
            logger.warning(f"窗口已无效，停止监控: {plugin_name}")
            self._stop_global_mouse_monitor(window, plugin_name)
            return
        
        # 获取当前鼠标位置和按钮状态
        current_pos = QCursor.pos()
        mouse_buttons = QApplication.mouseButtons()
        
        # ✅ 检查鼠标是否在窗口内（带异常处理）
        try:
            window_rect = window.frameGeometry()
            if not window_rect.contains(current_pos):
                return
        except RuntimeError as e:
            if "wrapped C/C++ object" in str(e) and "has been deleted" in str(e):
                logger.warning(f"窗口在检查过程中被删除: {plugin_name}")
                self._stop_global_mouse_monitor(window, plugin_name)
                return
            else:
                raise e
```

## 🧪 测试工具

创建了专门的测试工具 `test_undock_fix.py`：

### 功能特点
1. **连接插件服务**：自动查找并连接到主程序的插件服务
2. **打开测试窗口**：创建测试用的插件窗口
3. **分离测试**：测试窗口分离功能
4. **状态检查**：检查窗口和定时器状态
5. **停靠测试**：测试窗口停靠功能

### 使用方法
```bash
python test_undock_fix.py
```

### 测试步骤
1. 点击"查找插件服务"确保连接成功
2. 点击"打开测试窗口"创建一个测试窗口
3. 点击"分离窗口"测试分离功能
4. 观察窗口是否正常显示且可操作
5. 点击"检查定时器状态"验证定时器已停止
6. 检查控制台是否还有错误日志

## ✅ 修复效果

### 1. 分离窗口后
- ✅ 窗口对象保持存活，正常显示和操作
- ✅ 窗口功能完全可用
- ✅ 没有"wrapped C/C++ object has been deleted"错误

### 2. 定时器管理
- ✅ 分离时自动停止全局鼠标监控定时器
- ✅ 关闭时正确清理所有定时器资源
- ✅ 不再产生后台错误日志

### 3. 资源管理
- ✅ 容器资源正确清理
- ✅ 窗口对象生命周期正确管理
- ✅ 内存泄漏风险降低

### 4. 稳定性
- ✅ 窗口有效性检查防止访问无效对象
- ✅ 异常处理机制完善
- ✅ 状态管理清晰

## 🎯 技术要点

### 1. 对象生命周期管理
- **分离关注点**：容器清理和窗口管理分离
- **引用管理**：正确管理窗口对象引用
- **资源清理**：分层次的资源清理策略

### 2. 定时器管理
- **生命周期绑定**：定时器生命周期与窗口绑定
- **主动清理**：在适当时机主动停止定时器
- **状态检查**：定时器运行前检查目标对象有效性

### 3. 异常处理
- **预防性检查**：在访问对象前检查有效性
- **优雅降级**：发现问题时优雅地停止相关功能
- **错误恢复**：自动清理无效资源

### 4. 调试和监控
- **详细日志**：提供详细的操作日志
- **状态监控**：实时监控窗口和定时器状态
- **测试工具**：专门的测试和诊断工具

## 🚀 后续建议

### 1. 监控和维护
- 定期检查窗口对象的生命周期管理
- 监控定时器的创建和销毁
- 收集用户反馈和错误报告

### 2. 功能增强
- 考虑添加窗口状态持久化
- 支持批量分离和停靠操作
- 添加更多的窗口管理功能

### 3. 代码优化
- 抽象窗口生命周期管理为独立模块
- 统一定时器管理机制
- 改进错误处理和恢复机制

## 📝 结论

通过这次修复，彻底解决了分离窗口后的资源管理问题：

1. **根本解决**：从对象生命周期管理的根本层面解决问题
2. **防御机制**：添加多重检查和保护机制
3. **用户体验**：分离窗口功能正常工作，用户体验良好
4. **系统稳定性**：消除了后台错误日志，提高系统稳定性

现在分离窗口功能能够正常工作，窗口保持可用状态，不再产生资源访问错误，用户可以正常使用分离出来的工具窗口。
