import logging
from logging.handlers import RotatingFileHandler
import atexit
import os
import json
from pathlib import Path

class LogManager:
    """集中式日志管理器"""

    # 单例实例
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if self._initialized:
            return

        # 加载日志配置
        self.config = self._load_log_config()

        # 配置日志目录
        self.log_dir = Path("log")
        self.log_dir.mkdir(exist_ok=True)

        # 主日志文件路径
        self.main_log = self.log_dir / "app.log"

        # 配置根记录器
        self.root_logger = logging.getLogger()
        log_level = self._get_log_level(self.config.get('level', 'INFO'))
        self.root_logger.setLevel(log_level)

        # 配置控制台日志
        if self.config.get('console_enabled', True):
            self._setup_console_logging()

        # 配置文件日志
        if self.config.get('file_enabled', True):
            self._setup_file_logging()

        self._initialized = True

    def _load_log_config(self):
        """加载日志配置"""
        config_files = [
            Path("config/default.json"),
            Path("config/app.json"),
            Path("app.json")  # 备用配置文件
        ]

        for config_file in config_files:
            if config_file.exists():
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        return data.get('logging', {})
                except (json.JSONDecodeError, KeyError):
                    continue

        # 默认配置
        return {
            'level': 'INFO',
            'console_enabled': True,
            'file_enabled': True,
            'max_file_size': '10MB',
            'backup_count': 5
        }

    def _get_log_level(self, level_str):
        """将字符串日志级别转换为logging级别"""
        level_map = {
            'DEBUG': logging.DEBUG,
            'INFO': logging.INFO,
            'WARNING': logging.WARNING,
            'ERROR': logging.ERROR,
            'CRITICAL': logging.CRITICAL
        }
        return level_map.get(level_str.upper(), logging.INFO)

    def _parse_file_size(self, size_str):
        """解析文件大小字符串"""
        if isinstance(size_str, int):
            return size_str

        size_str = str(size_str).upper()
        if size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        else:
            return int(size_str)

    def _setup_console_logging(self):
        """配置控制台日志"""
        console_handler = logging.StreamHandler()
        log_level = self._get_log_level(self.config.get('level', 'INFO'))
        console_handler.setLevel(log_level)
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(formatter)
        self.root_logger.addHandler(console_handler)

    def _setup_file_logging(self):
        """配置文件日志，带轮转功能"""
        max_bytes = self._parse_file_size(self.config.get('max_file_size', '10MB'))
        backup_count = self.config.get('backup_count', 5)

        file_handler = RotatingFileHandler(
            self.main_log,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        # 文件日志保持DEBUG级别，便于调试
        file_handler.setLevel(logging.DEBUG)
        formatter = logging.Formatter(
    '%(asctime)s - %(module)s - [%(filename)s:%(lineno)d] - %(levelname)s - %(message)s'
)
        file_handler.setFormatter(formatter)
        self.root_logger.addHandler(file_handler)

    def get_logger(self, name=None):
        """获取模块专用日志记录器"""
        return logging.getLogger(name)

    def set_log_level(self, level):
        """动态设置日志级别"""
        log_level = self._get_log_level(level)
        self.root_logger.setLevel(log_level)

        # 更新所有处理器的级别（除了文件处理器保持DEBUG）
        for handler in self.root_logger.handlers:
            if isinstance(handler, logging.StreamHandler) and not isinstance(handler, RotatingFileHandler):
                handler.setLevel(log_level)

# 初始化日志系统
log_manager = LogManager()
logger = log_manager.get_logger()

# 确保退出时清理
@atexit.register
def cleanup():
    logging.shutdown()

# 提供便捷访问方法
def get_module_logger(module_name):
    """获取模块专用日志记录器
    Args:
        module_name: 模块名(通常使用__name__)
    Returns:
        配置好的日志记录器，自动包含模块名
    """
    logger = log_manager.get_logger(module_name)

    # 添加模块过滤器
    class ModuleFilter(logging.Filter):
        def filter(self, record):
            record.module = module_name.split('.')[-1]
            return True

    logger.addFilter(ModuleFilter())
    return logger

def get_page_logger(page_name):
    """获取页面专用日志记录器
    Args:
        page_name: 页面名称(如'ClkOutputs')
    Returns:
        配置好的页面日志记录器，自动包含页面名
    """
    logger = log_manager.get_logger(f"page.{page_name}")

    # 添加页面过滤器
    class PageFilter(logging.Filter):
        def filter(self, record):
            record.page = page_name
            return True

    logger.addFilter(PageFilter())
    return logger

# 示例日志记录
# logger.debug('这是一个调试信息')
# logger.info('这是一个信息')
# logger.warning('这是一个警告')
# logger.error('这是一个错误')
# logger.critical('这是一个严重错误')