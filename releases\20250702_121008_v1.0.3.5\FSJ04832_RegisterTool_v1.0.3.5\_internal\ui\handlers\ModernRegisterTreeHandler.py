#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
现代化的寄存器树处理器
使用ModernBaseHandler作为基类，重构自原RegisterTreeHandler
主要功能：寄存器树视图管理、选择处理、搜索过滤
"""

import os
from PyQt5.QtWidgets import QTreeWidget, QTreeWidgetItem, QVBoxLayout
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QColor, QBrush
from ui.handlers.ModernBaseHandler import ModernBaseHandler
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class ModernRegisterTreeHandler(ModernBaseHandler):
    """现代化的寄存器树处理器"""
    
    # 定义信号（保持与原版本兼容）
    register_selected = pyqtSignal(object)  # 寄存器被选中
    
    def __init__(self, parent=None, register_manager=None, **kwargs):
        """初始化现代化寄存器树处理器

        Args:
            parent: 父窗口
            register_manager: RegisterManager实例
            **kwargs: 其他参数（如register_repo等，用于兼容性）
        """
        super().__init__(parent, register_manager, **kwargs)
        
        # 设置窗口标题
        self.setWindowTitle("寄存器树 (现代化版本)")
        
        # 初始化树相关属性
        self.tree_widget = None
        self.register_items = {}  # 存储寄存器项的引用
        self._last_selected_addr = None  # 记录最后选中的地址
        self._user_has_selected = False  # 标记用户是否已经手动选择过寄存器
        
        # 创建UI
        self._create_tree_ui()
        
        # 手动调用初始化（因为测试环境没有事件循环）
        self._post_init()
        
        logger.info("现代化寄存器树处理器初始化完成")

    def _create_tree_ui(self):
        """创建树视图UI"""
        # 创建主布局
        main_layout = QVBoxLayout(self.content_widget)

        # 创建树控件
        self.tree_widget = QTreeWidget()
        self.tree_widget.setHeaderLabels(["Registers"])
        self.tree_widget.headerItem().setHidden(True)  # 隐藏表头

        # 设置最小宽度，确保能完整显示所有寄存器名称，防止布局抖动
        # 计算最长寄存器名称的宽度：如 "R42 (0x2A)" 大约需要 120-150 像素
        min_width = 180  # 足够显示完整的寄存器名称和地址
        self.tree_widget.setMinimumWidth(min_width)

        # 设置合适的尺寸策略，防止因内容变化导致布局重新计算
        from PyQt5.QtWidgets import QSizePolicy
        self.tree_widget.setSizePolicy(QSizePolicy.Minimum, QSizePolicy.Expanding)

        # 连接信号
        self.tree_widget.itemClicked.connect(self.on_tree_item_clicked)

        # 添加到布局
        main_layout.addWidget(self.tree_widget)

        # 设置布局间距
        main_layout.setContentsMargins(5, 5, 5, 5)

        logger.debug(f"现代化TreeWidget设置最小宽度: {min_width}px，防止布局抖动")

    def create_tree_widget(self):
        """创建寄存器树视图（兼容性方法）"""
        return self.tree_widget

    def populate_tree_widget(self):
        """填充寄存器树视图"""
        if not self.tree_widget or not self.register_manager:
            logger.warning("树控件或寄存器管理器未初始化")
            return
            
        # 清空树视图
        self.tree_widget.clear()
        self.register_items = {}
        
        # 创建根节点
        root_item = QTreeWidgetItem(["Registers"])
        self.tree_widget.addTopLevelItem(root_item)
        
        # 获取所有寄存器
        registers = self.register_manager.get_all_registers()
        
        # 按地址排序添加寄存器节点
        for register in sorted(registers, key=lambda r: self._get_register_sort_key(r)):
            reg_name = register.get("name", "")
            reg_addr = register.get("address", "")
            
            # 格式化显示R0 (0x00)等形式
            addr_int = self._parse_register_address(reg_addr)
            display_text = f"R{addr_int} (0x{addr_int:02X})"
            
            # 创建寄存器项
            reg_item = QTreeWidgetItem([display_text])
            
            # 保存寄存器地址作为用户数据
            reg_item.setData(0, Qt.UserRole, reg_addr)
            
            # 添加到根节点
            root_item.addChild(reg_item)
            
            # 保存引用以便后续更新
            self.register_items[reg_addr] = reg_item
                
        # 展开根节点
        root_item.setExpanded(True)
        
        logger.info(f"填充了 {len(self.register_items)} 个寄存器到树视图")

    def _get_register_sort_key(self, register):
        """获取寄存器排序键"""
        reg_addr = register.get("address", 0)
        return self._parse_register_address(reg_addr)

    def _parse_register_address(self, reg_addr):
        """解析寄存器地址"""
        if isinstance(reg_addr, int):
            return reg_addr
        elif isinstance(reg_addr, str):
            if reg_addr.startswith("0x"):
                return int(reg_addr, 16)
            else:
                try:
                    return int(reg_addr)
                except ValueError:
                    return 0
        return 0

    def select_default_register(self):
        """选择默认寄存器"""
        if not self.tree_widget:
            return

        # 只有在用户还没有手动选择过寄存器时才选择默认寄存器
        if self._user_has_selected:
            logger.debug(f"现代化TreeHandler: 用户已手动选择过寄存器，跳过默认选择")
            return

        # 如果有根节点且有子项，选择第一个寄存器
        if self.tree_widget.topLevelItemCount() > 0:
            root_item = self.tree_widget.topLevelItem(0)
            if root_item.childCount() > 0:
                reg_item = root_item.child(0)
                logger.info(f"现代化TreeHandler: 选择默认寄存器: '{reg_item.text(0)}'")
                self.tree_widget.setCurrentItem(reg_item)
                self.on_tree_item_clicked(reg_item, 0)

    def on_tree_item_clicked(self, item, column):
        """处理树项点击事件"""
        if item is None:
            logger.debug("现代化TreeHandler: on_tree_item_clicked called with None item.")
            return

        reg_addr = item.data(0, Qt.UserRole)
        item_text = item.text(0)
        logger.info(f"现代化TreeHandler: 树项点击 - 项: '{item_text}', 地址: {reg_addr}, 列: {column}")
        
        # 只处理寄存器项（有地址的项）
        if reg_addr is not None:
            # 检查是否是相同的寄存器地址，避免重复触发
            if self._last_selected_addr == reg_addr:
                logger.debug(f"现代化TreeHandler: 忽略重复选择地址: {reg_addr}")
                return
            
            logger.info(f"现代化TreeHandler: 选择寄存器地址: {reg_addr} 从项 '{item_text}'")

            # 更新最后选择的地址
            self._last_selected_addr = reg_addr

            # 标记用户已经手动选择过寄存器
            self._user_has_selected = True

            # 发出信号
            self.register_selected.emit(reg_addr)

            # 调用父窗口的回调方法（兼容性）
            if self.parent and hasattr(self.parent, "on_register_selected"):
                self.parent.on_register_selected(reg_addr)
        elif reg_addr is None:
            logger.debug(f"现代化TreeHandler: 项 '{item_text}' 的reg_addr为None，不处理")

    def update_register_value(self, addr, value):
        """更新寄存器值显示"""
        # 在批量操作期间跳过树控件更新，防止布局抖动
        if hasattr(self.parent, 'is_batch_reading') and self.parent.is_batch_reading:
            return
        if hasattr(self.parent, 'is_batch_writing') and self.parent.is_batch_writing:
            return
        if hasattr(self.parent, 'is_batch_updating') and self.parent.is_batch_updating:
            return

        if addr in self.register_items:
            reg_item = self.register_items[addr]
            # 注意：原版本使用第2列显示值，但我们的树只有一列
            # 可以考虑在显示文本中包含值，或者添加列
            if reg_item.columnCount() > 1:
                reg_item.setText(1, f"0x{value:04X}" if isinstance(value, int) else str(value))

    def get_current_register_addr(self):
        """获取当前选中的寄存器地址"""
        if not self.tree_widget:
            return None
            
        current_item = self.tree_widget.currentItem()
        if not current_item:
            return None
            
        return current_item.data(0, Qt.UserRole)

    def select_register_by_addr(self, reg_addr):
        """通过地址选择寄存器项，并触发相应的更新操作"""
        if reg_addr in self.register_items:
            item = self.register_items[reg_addr]
            
            # 检查是否已经是当前选中的项，避免不必要的重复操作
            if self.tree_widget.currentItem() == item:
                logger.debug(f"现代化TreeHandler: 地址 '{reg_addr}' 已经是当前选中项，无需重复选择")
                return

            self.tree_widget.setCurrentItem(item)
            logger.info(f"现代化TreeHandler: 通过地址 '{reg_addr}' 成功选择树项 '{item.text(0)}'")
            
            # 触发点击事件来确保所有相关逻辑（如信号发射）都被执行
            self.on_tree_item_clicked(item, 0)
        else:
            logger.warning(f"现代化TreeHandler: 无法找到地址为 '{reg_addr}' 的寄存器项")

    def find_register_by_name(self, name):
        """通过名称查找寄存器"""
        if not self.tree_widget:
            return None
            
        # 搜索所有项
        found_items = []
        for group_idx in range(self.tree_widget.topLevelItemCount()):
            group_item = self.tree_widget.topLevelItem(group_idx)
            
            for reg_idx in range(group_item.childCount()):
                reg_item = group_item.child(reg_idx)
                reg_name = reg_item.text(0)
                
                if name.lower() in reg_name.lower():
                    found_items.append(reg_item)
                    
        return found_items

    def filter_registers_by_bit_field_name(self, keyword):
        """根据位段名称关键字过滤树视图中的寄存器项"""
        if not self.tree_widget or not self.register_manager:
            logger.warning("现代化TreeHandler: 树控件或寄存器管理器未初始化，无法过滤")
            return

        keyword_lower = keyword.lower().strip() if keyword else ""
        
        # 默认背景色
        default_brush = QBrush()
        # 高亮背景色
        highlight_brush = QBrush(QColor(255, 255, 0, 100))  # 淡黄色高亮
        
        found_any_match = False
        exact_match_item = None
        first_partial_match_item = None
        
        if keyword_lower:
            logger.info(f"现代化TreeHandler: 开始过滤，关键字: '{keyword_lower}'")
            
            # 遍历所有寄存器项
            for i in range(self.tree_widget.topLevelItemCount()):
                root_item = self.tree_widget.topLevelItem(i)
                for j in range(root_item.childCount()):
                    reg_item = root_item.child(j)
                    reg_addr = reg_item.data(0, Qt.UserRole)
                    
                    # 重置背景色
                    reg_item.setBackground(0, default_brush)
                    reg_item.setHidden(False)
                    
                    if reg_addr is not None:
                        # 使用RegisterManager搜索位字段
                        bit_fields = self.register_manager.get_register_bit_info(reg_addr)
                        
                        has_match = False
                        exact_match = False
                        
                        for bit_field in bit_fields:
                            bit_name = bit_field.get("name", "").lower()
                            if keyword_lower in bit_name:
                                has_match = True
                                if bit_name == keyword_lower:
                                    exact_match = True
                                    break
                        
                        if has_match:
                            found_any_match = True
                            reg_item.setBackground(0, highlight_brush)
                            
                            if exact_match and exact_match_item is None:
                                exact_match_item = reg_item
                            elif first_partial_match_item is None:
                                first_partial_match_item = reg_item
            
            # 选择最佳匹配项
            item_to_select = exact_match_item or first_partial_match_item
            if item_to_select:
                self.tree_widget.setCurrentItem(item_to_select)
                self.tree_widget.scrollToItem(item_to_select)
                self.on_tree_item_clicked(item_to_select, 0)
                logger.info(f"现代化TreeHandler: 过滤完成，选择项 '{item_to_select.text(0)}'")
            elif not found_any_match:
                logger.info(f"现代化TreeHandler: 未找到与关键字 '{keyword_lower}' 匹配的位段")
        else:
            # 清除过滤，显示所有项
            for i in range(self.tree_widget.topLevelItemCount()):
                root_item = self.tree_widget.topLevelItem(i)
                for j in range(root_item.childCount()):
                    reg_item = root_item.child(j)
                    reg_item.setBackground(0, default_brush)
                    reg_item.setHidden(False)
            
            self.tree_widget.setCurrentItem(None)
            if self.parent and hasattr(self.parent, "clear_register_details_view"):
                self.parent.clear_register_details_view()
            logger.info("现代化TreeHandler: 关键字为空，显示所有寄存器，已清除高亮和选择")

    # === ModernBaseHandler重写方法 ===
    
    def on_global_register_update(self, reg_addr, reg_value):
        """处理全局寄存器更新"""
        logger.debug(f"现代化TreeHandler: 收到全局更新 {reg_addr} = 0x{reg_value:04X}")
        # 树视图通常不需要显示寄存器值，但可以在这里添加逻辑
        self.update_register_value(reg_addr, reg_value)

    # === 兼容性和工厂方法 ===
    
    @classmethod
    def create_for_testing(cls, parent=None):
        """创建测试实例"""
        try:
            # 创建模拟的RegisterManager
            from core.services.register.RegisterManager import RegisterManager
            import json
            import os
            
            # 加载寄存器配置
            config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'lib', 'register.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                registers_config = json.load(f)
            
            register_manager = RegisterManager(registers_config)
            
            # 创建实例
            instance = cls(parent, register_manager)
            
            logger.info("创建现代化RegisterTreeHandler测试实例成功")
            return instance
            
        except Exception as e:
            logger.error(f"创建测试实例时出错: {str(e)}")
            raise

    def get_tree_widget(self):
        """获取树控件（用于集成到主窗口）"""
        return self.tree_widget
