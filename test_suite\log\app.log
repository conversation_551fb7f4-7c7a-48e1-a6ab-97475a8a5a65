2025-06-03 17:15:00,601 - RegisterUpdateBus - [RegisterUpdateBus.py:57] - INFO - RegisterUpdateBus: Initial clock source set based on register default (0x57, CL<PERSON>in_SEL_MANUAL): ClkIn1
2025-06-03 17:15:00,601 - ModernBaseHandler - [ModernBaseHandler.py:67] - DEBUG - 成功连接到RegisterUpdateBus
2025-06-03 17:15:00,955 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout0FMT 格式选项
2025-06-03 17:15:00,955 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout1FMT 格式选项
2025-06-03 17:15:00,955 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout2FMT 格式选项
2025-06-03 17:15:00,956 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout3FMT 格式选项
2025-06-03 17:15:00,961 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout4FMT 格式选项
2025-06-03 17:15:00,961 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout5FMT 格式选项
2025-06-03 17:15:00,961 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout6FMT 格式选项
2025-06-03 17:15:00,961 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout7FMT 格式选项
2025-06-03 17:15:00,961 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout8FMT 格式选项
2025-06-03 17:15:00,962 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout9FMT 格式选项
2025-06-03 17:15:00,962 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout10FMT 格式选项
2025-06-03 17:15:00,963 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout11FMT 格式选项
2025-06-03 17:15:00,963 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout12FMT 格式选项
2025-06-03 17:15:00,963 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout13FMT 格式选项
2025-06-03 17:15:00,963 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:172] - INFO - 所有格式下拉框初始化完成
2025-06-03 17:15:00,964 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK0_1DIV: 范围(1-1023), 默认值(2)
2025-06-03 17:15:00,964 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK2_3DIV: 范围(1-1023), 默认值(4)
2025-06-03 17:15:00,964 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK4_5DIV: 范围(1-1023), 默认值(8)
2025-06-03 17:15:00,965 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK6_7DIV: 范围(1-1023), 默认值(8)
2025-06-03 17:15:00,965 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK8_9DIV: 范围(1-1023), 默认值(8)
2025-06-03 17:15:00,965 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK10_11DIV: 范围(1-1023), 默认值(8)
2025-06-03 17:15:00,966 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK12_13DIV: 范围(1-1023), 默认值(2)
2025-06-03 17:15:00,966 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK0_1DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:00,966 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK2_3DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:00,966 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK4_5DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:00,966 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK6_7DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:00,966 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK8_9DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:00,967 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK10_11DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:00,967 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK12_13DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:00,967 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:227] - DEBUG - 已初始化DDLYdStepCNT_1: 范围(0-255), 默认值(0)
2025-06-03 17:15:00,967 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:229] - INFO - 分频器控件初始化完成
2025-06-03 17:15:00,968 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK0_1PD为启用状态
2025-06-03 17:15:00,968 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK2_3PD为启用状态
2025-06-03 17:15:00,968 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK4_5PD为启用状态
2025-06-03 17:15:00,968 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK6_7PD为启用状态
2025-06-03 17:15:00,968 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK8_9PD为启用状态
2025-06-03 17:15:00,968 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK10_11PD为启用状态
2025-06-03 17:15:00,968 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK12_13PD为启用状态
2025-06-03 17:15:00,968 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK0_1DDLYPD为启用状态
2025-06-03 17:15:00,968 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK2_3DDLYPD为启用状态
2025-06-03 17:15:00,968 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK4_5DDLYPD为启用状态
2025-06-03 17:15:00,969 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK6_7DDLYPD为启用状态
2025-06-03 17:15:00,969 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK8_9DDLYPD为启用状态
2025-06-03 17:15:00,969 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK10_11DDLYPD为启用状态
2025-06-03 17:15:00,969 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK12_13DDLYPD为启用状态
2025-06-03 17:15:00,969 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd0EN为禁用状态
2025-06-03 17:15:00,969 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd2EN为禁用状态
2025-06-03 17:15:00,969 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd4EN为禁用状态
2025-06-03 17:15:00,969 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd6EN为禁用状态
2025-06-03 17:15:00,969 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd8EN为禁用状态
2025-06-03 17:15:00,969 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd10EN为禁用状态
2025-06-03 17:15:00,969 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd12EN为禁用状态
2025-06-03 17:15:00,969 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK0_1PD为关闭状态
2025-06-03 17:15:00,969 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK2_3PD为关闭状态
2025-06-03 17:15:00,970 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK4_5PD为关闭状态
2025-06-03 17:15:00,970 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK6_7PD为关闭状态
2025-06-03 17:15:00,970 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK8_9PD为关闭状态
2025-06-03 17:15:00,970 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK10_11PD为关闭状态
2025-06-03 17:15:00,970 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK12_13PD为关闭状态
2025-06-03 17:15:00,970 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK0_1HSTrig为无相位调整
2025-06-03 17:15:00,970 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK2_3HSTrig为无相位调整
2025-06-03 17:15:00,970 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK4_5HSTrig为无相位调整
2025-06-03 17:15:00,970 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK6_7HSTrig为无相位调整
2025-06-03 17:15:00,970 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK8_9HSTrig为无相位调整
2025-06-03 17:15:00,970 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK10_11HSTrig为无相位调整
2025-06-03 17:15:00,971 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK12_13HSTrig为无相位调整
2025-06-03 17:15:00,971 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS0为不禁用同步
2025-06-03 17:15:00,971 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS2为不禁用同步
2025-06-03 17:15:00,971 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS4为不禁用同步
2025-06-03 17:15:00,971 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS6为不禁用同步
2025-06-03 17:15:00,971 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS8为不禁用同步
2025-06-03 17:15:00,971 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS10为不禁用同步
2025-06-03 17:15:00,971 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS12为不禁用同步
2025-06-03 17:15:00,971 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:309] - INFO - 电源管理和控制状态初始化完成
2025-06-03 17:15:00,971 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK0_1DDLY: 默认0.5ns延迟
2025-06-03 17:15:00,971 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK2_3DDLY: 默认0.5ns延迟
2025-06-03 17:15:00,971 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK4_5DDLY: 默认0.5ns延迟
2025-06-03 17:15:00,971 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK6_7DDLY: 默认0.5ns延迟
2025-06-03 17:15:00,971 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK8_9DDLY: 默认0.5ns延迟
2025-06-03 17:15:00,971 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK10_11DDLY: 默认0.5ns延迟
2025-06-03 17:15:00,972 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK12_13DDLY: 默认0.5ns延迟
2025-06-03 17:15:00,972 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:335] - INFO - SYSREF延迟控件初始化完成
2025-06-03 17:15:00,972 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK2_3DIV 当前值为 4，保持不变
2025-06-03 17:15:00,972 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK4_5DIV 当前值为 8，保持不变
2025-06-03 17:15:00,973 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK6_7DIV 当前值为 8，保持不变
2025-06-03 17:15:00,973 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK8_9DIV 当前值为 8，保持不变
2025-06-03 17:15:00,973 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK10_11DIV 当前值为 8，保持不变
2025-06-03 17:15:00,973 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:376] - DEBUG - 分频器值初始化检查完成
2025-06-03 17:15:00,973 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:404] - DEBUG - 分频器值同步完成
2025-06-03 17:15:00,973 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:351] - INFO - 输出频率初始化完成（延迟计算）
2025-06-03 17:15:00,974 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:416] - INFO - 已连接 lineEditFvco 信号
2025-06-03 17:15:00,974 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout0_SRCMUX 信号
2025-06-03 17:15:00,974 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout1_SRCMUX 信号
2025-06-03 17:15:00,974 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout2_SRCMUX 信号
2025-06-03 17:15:00,974 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout3_SRCMUX 信号
2025-06-03 17:15:00,974 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout4_SRCMUX 信号
2025-06-03 17:15:00,974 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout5_SRCMUX 信号
2025-06-03 17:15:00,975 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout6_SRCMUX 信号
2025-06-03 17:15:00,975 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout7_SRCMUX 信号
2025-06-03 17:15:00,975 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout8_SRCMUX 信号
2025-06-03 17:15:00,975 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout9_SRCMUX 信号
2025-06-03 17:15:00,975 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout10_SRCMUX 信号
2025-06-03 17:15:00,975 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout11_SRCMUX 信号
2025-06-03 17:15:00,975 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout12_SRCMUX 信号
2025-06-03 17:15:00,975 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout13_SRCMUX 信号
2025-06-03 17:15:00,975 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:437] - INFO - 已连接所有时钟输出的SRCMUX复选框信号
2025-06-03 17:15:00,976 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:119] - INFO - 时钟输出特定配置初始化完成
2025-06-03 17:15:00,976 - ModernBaseHandler - [ModernBaseHandler.py:86] - INFO - ModernBaseHandler._post_init 被调用
2025-06-03 17:15:00,977 - ModernBaseHandler - [ModernBaseHandler.py:88] - INFO - UI已设置，开始构建控件映射
2025-06-03 17:15:00,977 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout0FMT (QComboBox)
2025-06-03 17:15:00,977 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout0_1PD (QCheckBox)
2025-06-03 17:15:00,977 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout0_SRCMUX (QCheckBox)
2025-06-03 17:15:00,977 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout10FMT (QComboBox)
2025-06-03 17:15:00,977 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout10_11PD (QCheckBox)
2025-06-03 17:15:00,977 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout10_SRCMUX (QCheckBox)
2025-06-03 17:15:00,978 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout11FMT (QComboBox)
2025-06-03 17:15:00,978 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout11_SRCMUX (QCheckBox)
2025-06-03 17:15:00,978 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout12FMT (QComboBox)
2025-06-03 17:15:00,978 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout12_13PD (QCheckBox)
2025-06-03 17:15:00,978 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout12_SRCMUX (QCheckBox)
2025-06-03 17:15:00,978 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout13FMT (QComboBox)
2025-06-03 17:15:00,978 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout13_SRCMUX (QCheckBox)
2025-06-03 17:15:00,978 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout1FMT (QComboBox)
2025-06-03 17:15:00,978 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout1_SRCMUX (QCheckBox)
2025-06-03 17:15:00,978 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout2FMT (QComboBox)
2025-06-03 17:15:00,978 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout2_3PD (QCheckBox)
2025-06-03 17:15:00,978 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout2_SRCMUX (QCheckBox)
2025-06-03 17:15:00,978 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout3FMT (QComboBox)
2025-06-03 17:15:00,978 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout3_SRCMUX (QCheckBox)
2025-06-03 17:15:00,978 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout4FMT (QComboBox)
2025-06-03 17:15:00,979 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout4_5PD (QCheckBox)
2025-06-03 17:15:00,979 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout4_SRCMUX (QCheckBox)
2025-06-03 17:15:00,979 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout5FMT (QComboBox)
2025-06-03 17:15:00,979 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout5_SRCMUX (QCheckBox)
2025-06-03 17:15:00,979 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout6FMT (QComboBox)
2025-06-03 17:15:00,979 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout6_7PD (QCheckBox)
2025-06-03 17:15:00,979 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout6_SRCMUX (QCheckBox)
2025-06-03 17:15:00,979 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout7FMT (QComboBox)
2025-06-03 17:15:00,979 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout7_SRCMUX (QCheckBox)
2025-06-03 17:15:00,979 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout8FMT (QComboBox)
2025-06-03 17:15:00,979 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout8_9PD (QCheckBox)
2025-06-03 17:15:00,979 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout8_SRCMUX (QCheckBox)
2025-06-03 17:15:00,979 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout9FMT (QComboBox)
2025-06-03 17:15:00,979 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout9_SRCMUX (QCheckBox)
2025-06-03 17:15:00,979 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1BYPASS (QCheckBox)
2025-06-03 17:15:00,979 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1DDLY (QSpinBox)
2025-06-03 17:15:00,979 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1DDLYPD (QCheckBox)
2025-06-03 17:15:00,979 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1DIV (QSpinBox)
2025-06-03 17:15:00,980 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1HSTrig (QCheckBox)
2025-06-03 17:15:00,980 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1PD (QCheckBox)
2025-06-03 17:15:00,980 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1POL (QCheckBox)
2025-06-03 17:15:00,980 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11BYPASS (QCheckBox)
2025-06-03 17:15:00,980 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11DDLY (QSpinBox)
2025-06-03 17:15:00,980 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11DDLYPD (QCheckBox)
2025-06-03 17:15:00,980 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11DIV (QSpinBox)
2025-06-03 17:15:00,980 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11HSTrig (QCheckBox)
2025-06-03 17:15:00,980 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11PD (QCheckBox)
2025-06-03 17:15:00,980 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11POL (QCheckBox)
2025-06-03 17:15:00,981 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13BYPASS (QCheckBox)
2025-06-03 17:15:00,981 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13DDLY (QSpinBox)
2025-06-03 17:15:00,981 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13DDLYPD (QCheckBox)
2025-06-03 17:15:00,981 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13DIV (QSpinBox)
2025-06-03 17:15:00,981 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13HSTrig (QCheckBox)
2025-06-03 17:15:00,981 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13PD (QCheckBox)
2025-06-03 17:15:00,982 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13POL (QCheckBox)
2025-06-03 17:15:00,982 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3BYPASS (QCheckBox)
2025-06-03 17:15:00,982 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3DDLY (QSpinBox)
2025-06-03 17:15:00,982 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3DDLYPD (QCheckBox)
2025-06-03 17:15:00,982 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3DIV (QSpinBox)
2025-06-03 17:15:00,983 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3HSTrig (QCheckBox)
2025-06-03 17:15:00,983 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3PD (QCheckBox)
2025-06-03 17:15:00,983 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3POL (QCheckBox)
2025-06-03 17:15:00,983 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5BYPASS (QCheckBox)
2025-06-03 17:15:00,983 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5DDLY (QSpinBox)
2025-06-03 17:15:00,983 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5DDLYPD (QCheckBox)
2025-06-03 17:15:00,983 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5DIV (QSpinBox)
2025-06-03 17:15:00,983 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5HSTrig (QCheckBox)
2025-06-03 17:15:00,983 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5PD (QCheckBox)
2025-06-03 17:15:00,983 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5POL (QCheckBox)
2025-06-03 17:15:00,983 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7BYPASS (QCheckBox)
2025-06-03 17:15:00,983 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7DDLY (QSpinBox)
2025-06-03 17:15:00,983 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7DDLYPD (QCheckBox)
2025-06-03 17:15:00,984 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7DIV (QSpinBox)
2025-06-03 17:15:00,984 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7HSTrig (QCheckBox)
2025-06-03 17:15:00,984 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7PD (QCheckBox)
2025-06-03 17:15:00,984 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7POL (QCheckBox)
2025-06-03 17:15:00,984 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9BYPASS (QCheckBox)
2025-06-03 17:15:00,984 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9DDLY (QSpinBox)
2025-06-03 17:15:00,984 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9DDLYPD (QCheckBox)
2025-06-03 17:15:00,984 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9DIV (QSpinBox)
2025-06-03 17:15:00,984 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9HSTrig (QCheckBox)
2025-06-03 17:15:00,984 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9PD (QCheckBox)
2025-06-03 17:15:00,984 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9POL (QCheckBox)
2025-06-03 17:15:00,984 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd0EN (QCheckBox)
2025-06-03 17:15:00,984 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd10EN (QCheckBox)
2025-06-03 17:15:00,984 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd12EN (QCheckBox)
2025-06-03 17:15:00,984 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd2EN (QCheckBox)
2025-06-03 17:15:00,984 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd4EN (QCheckBox)
2025-06-03 17:15:00,984 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd6EN (QCheckBox)
2025-06-03 17:15:00,984 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd8EN (QCheckBox)
2025-06-03 17:15:00,984 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_1 (QSpinBox)
2025-06-03 17:15:00,984 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_2 (QSpinBox)
2025-06-03 17:15:00,984 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_3 (QSpinBox)
2025-06-03 17:15:00,985 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_4 (QSpinBox)
2025-06-03 17:15:00,985 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_5 (QSpinBox)
2025-06-03 17:15:00,985 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_6 (QSpinBox)
2025-06-03 17:15:00,985 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_7 (QSpinBox)
2025-06-03 17:15:00,985 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK0_1HSEN (QCheckBox)
2025-06-03 17:15:00,985 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK10_11HSEN (QCheckBox)
2025-06-03 17:15:00,985 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK12_13HSEN (QCheckBox)
2025-06-03 17:15:00,985 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK2_3HSEN (QCheckBox)
2025-06-03 17:15:00,985 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK4_5HSEN (QCheckBox)
2025-06-03 17:15:00,985 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK6_7HSEN (QCheckBox)
2025-06-03 17:15:00,985 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK8_9HSEN (QCheckBox)
2025-06-03 17:15:00,985 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1ADLY (QSpinBox)
2025-06-03 17:15:00,985 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1ADLYEnb (QCheckBox)
2025-06-03 17:15:00,985 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1DDLY (QComboBox)
2025-06-03 17:15:00,985 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1HSTrig (QCheckBox)
2025-06-03 17:15:00,985 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1PD (QCheckBox)
2025-06-03 17:15:00,986 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1POL (QCheckBox)
2025-06-03 17:15:00,986 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11ADLY (QSpinBox)
2025-06-03 17:15:00,986 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11ADLYEnb (QCheckBox)
2025-06-03 17:15:00,986 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11DDLY (QComboBox)
2025-06-03 17:15:00,986 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11HSTrig (QCheckBox)
2025-06-03 17:15:00,986 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11PD (QCheckBox)
2025-06-03 17:15:00,986 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11POL (QCheckBox)
2025-06-03 17:15:00,986 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13ADLY (QSpinBox)
2025-06-03 17:15:00,986 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13ADLYEnb (QCheckBox)
2025-06-03 17:15:00,986 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13DDLY (QComboBox)
2025-06-03 17:15:00,986 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13HSTrig (QCheckBox)
2025-06-03 17:15:00,986 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13PD (QCheckBox)
2025-06-03 17:15:00,986 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13POL (QCheckBox)
2025-06-03 17:15:00,986 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3ADLY (QSpinBox)
2025-06-03 17:15:00,986 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3ADLYEnb (QCheckBox)
2025-06-03 17:15:00,986 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3DDLY (QComboBox)
2025-06-03 17:15:00,986 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3HSTrig (QCheckBox)
2025-06-03 17:15:00,986 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3PD (QCheckBox)
2025-06-03 17:15:00,987 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3POL (QCheckBox)
2025-06-03 17:15:00,987 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5ADLY (QSpinBox)
2025-06-03 17:15:00,987 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5ADLYEnb (QCheckBox)
2025-06-03 17:15:00,987 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5DDLY (QComboBox)
2025-06-03 17:15:00,987 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5HSTrig (QCheckBox)
2025-06-03 17:15:00,987 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5PD (QCheckBox)
2025-06-03 17:15:00,987 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5POL (QCheckBox)
2025-06-03 17:15:00,987 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7ADLY (QSpinBox)
2025-06-03 17:15:00,987 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7ADLYEnb (QCheckBox)
2025-06-03 17:15:00,987 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7DDLY (QComboBox)
2025-06-03 17:15:00,987 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7HSTrig (QCheckBox)
2025-06-03 17:15:00,987 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7PD (QCheckBox)
2025-06-03 17:15:00,987 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7POL (QCheckBox)
2025-06-03 17:15:00,987 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9ADLY (QSpinBox)
2025-06-03 17:15:00,987 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9ADLYEnb (QCheckBox)
2025-06-03 17:15:00,987 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9DDLY (QComboBox)
2025-06-03 17:15:00,988 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9HSTrig (QCheckBox)
2025-06-03 17:15:00,988 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9PD (QCheckBox)
2025-06-03 17:15:00,988 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9POL (QCheckBox)
2025-06-03 17:15:00,988 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS0 (QCheckBox)
2025-06-03 17:15:00,988 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS10 (QCheckBox)
2025-06-03 17:15:00,988 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS12 (QCheckBox)
2025-06-03 17:15:00,988 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS2 (QCheckBox)
2025-06-03 17:15:00,988 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS4 (QCheckBox)
2025-06-03 17:15:00,988 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS6 (QCheckBox)
2025-06-03 17:15:00,988 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS8 (QCheckBox)
2025-06-03 17:15:00,988 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SysrefCLR (QCheckBox)
2025-06-03 17:15:00,988 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SysrefGBLPD (QCheckBox)
2025-06-03 17:15:00,988 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: label (QLabel)
2025-06-03 17:15:00,988 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: labelFvco (QLabel)
2025-06-03 17:15:00,988 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout0Output (QLineEdit)
2025-06-03 17:15:00,989 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout10Output (QLineEdit)
2025-06-03 17:15:00,989 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout11Output (QLineEdit)
2025-06-03 17:15:00,989 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout12Output (QLineEdit)
2025-06-03 17:15:00,989 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout13Output (QLineEdit)
2025-06-03 17:15:00,989 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout1Output (QLineEdit)
2025-06-03 17:15:00,989 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout2Output (QLineEdit)
2025-06-03 17:15:00,989 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout3Output (QLineEdit)
2025-06-03 17:15:00,989 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout4Output (QLineEdit)
2025-06-03 17:15:00,989 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout5Output (QLineEdit)
2025-06-03 17:15:00,989 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout6Output (QLineEdit)
2025-06-03 17:15:00,989 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout7Output (QLineEdit)
2025-06-03 17:15:00,989 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout8Output (QLineEdit)
2025-06-03 17:15:00,989 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout9Output (QLineEdit)
2025-06-03 17:15:00,989 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFvco (QLineEdit)
2025-06-03 17:15:00,989 - ModernBaseHandler - [ModernBaseHandler.py:119] - INFO - UI中发现 173 个控件
2025-06-03 17:15:00,990 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:452] - ERROR - 后初始化时出错: 'MockRegisterManager' object has no attribute 'get_widget_register_mapping'
2025-06-03 17:15:00,990 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:56] - INFO - 现代化时钟输出处理器初始化完成
2025-06-03 17:15:00,990 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出0分频值: DCLK0_1DIV = 2
2025-06-03 17:15:00,990 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出0频率: 1474.56 MHz (VCO: 2949.12, 分频: 2)
2025-06-03 17:15:00,990 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出1分频值: DCLK0_1DIV = 2
2025-06-03 17:15:00,991 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出1频率: 1474.56 MHz (VCO: 2949.12, 分频: 2)
2025-06-03 17:15:00,991 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出2分频值: DCLK2_3DIV = 4
2025-06-03 17:15:00,991 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出2频率: 737.28 MHz (VCO: 2949.12, 分频: 4)
2025-06-03 17:15:00,991 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出3分频值: DCLK2_3DIV = 4
2025-06-03 17:15:00,991 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出3频率: 737.28 MHz (VCO: 2949.12, 分频: 4)
2025-06-03 17:15:00,991 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出4分频值: DCLK4_5DIV = 8
2025-06-03 17:15:00,991 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出4频率: 368.64 MHz (VCO: 2949.12, 分频: 8)
2025-06-03 17:15:00,991 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出5分频值: DCLK4_5DIV = 8
2025-06-03 17:15:00,991 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出5频率: 368.64 MHz (VCO: 2949.12, 分频: 8)
2025-06-03 17:15:00,991 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出6分频值: DCLK6_7DIV = 8
2025-06-03 17:15:00,991 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出6频率: 368.64 MHz (VCO: 2949.12, 分频: 8)
2025-06-03 17:15:00,991 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出7分频值: DCLK6_7DIV = 8
2025-06-03 17:15:00,991 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出7频率: 368.64 MHz (VCO: 2949.12, 分频: 8)
2025-06-03 17:15:00,991 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出8分频值: DCLK8_9DIV = 8
2025-06-03 17:15:00,991 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出8频率: 368.64 MHz (VCO: 2949.12, 分频: 8)
2025-06-03 17:15:00,991 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出9分频值: DCLK8_9DIV = 8
2025-06-03 17:15:00,991 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出9频率: 368.64 MHz (VCO: 2949.12, 分频: 8)
2025-06-03 17:15:00,991 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出10分频值: DCLK10_11DIV = 8
2025-06-03 17:15:00,991 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出10频率: 368.64 MHz (VCO: 2949.12, 分频: 8)
2025-06-03 17:15:00,992 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出11分频值: DCLK10_11DIV = 8
2025-06-03 17:15:00,992 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出11频率: 368.64 MHz (VCO: 2949.12, 分频: 8)
2025-06-03 17:15:00,992 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出12分频值: DCLK12_13DIV = 2
2025-06-03 17:15:00,992 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出12频率: 1474.56 MHz (VCO: 2949.12, 分频: 2)
2025-06-03 17:15:00,992 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出13分频值: DCLK12_13DIV = 2
2025-06-03 17:15:00,992 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出13频率: 1474.56 MHz (VCO: 2949.12, 分频: 2)
2025-06-03 17:15:00,992 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出0分频值: DCLK0_1DIV = 2
2025-06-03 17:15:00,992 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出2分频值: DCLK2_3DIV = 4
2025-06-03 17:15:00,992 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出4分频值: DCLK4_5DIV = 8
2025-06-03 17:15:00,992 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出6分频值: DCLK6_7DIV = 8
2025-06-03 17:15:00,992 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出8分频值: DCLK8_9DIV = 8
2025-06-03 17:15:00,992 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出10分频值: DCLK10_11DIV = 8
2025-06-03 17:15:00,992 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出12分频值: DCLK12_13DIV = 2
2025-06-03 17:15:01,001 - ModernBaseHandler - [ModernBaseHandler.py:71] - WARNING - 连接RegisterUpdateBus时出错: wrapped C/C++ object of type RegisterUpdateBus has been deleted，继续初始化
2025-06-03 17:15:01,363 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout0FMT 格式选项
2025-06-03 17:15:01,363 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout1FMT 格式选项
2025-06-03 17:15:01,363 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout2FMT 格式选项
2025-06-03 17:15:01,364 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout3FMT 格式选项
2025-06-03 17:15:01,364 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout4FMT 格式选项
2025-06-03 17:15:01,364 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout5FMT 格式选项
2025-06-03 17:15:01,364 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout6FMT 格式选项
2025-06-03 17:15:01,364 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout7FMT 格式选项
2025-06-03 17:15:01,364 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout8FMT 格式选项
2025-06-03 17:15:01,364 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout9FMT 格式选项
2025-06-03 17:15:01,364 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout10FMT 格式选项
2025-06-03 17:15:01,364 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout11FMT 格式选项
2025-06-03 17:15:01,364 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout12FMT 格式选项
2025-06-03 17:15:01,364 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout13FMT 格式选项
2025-06-03 17:15:01,365 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:172] - INFO - 所有格式下拉框初始化完成
2025-06-03 17:15:01,366 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK0_1DIV: 范围(1-1023), 默认值(2)
2025-06-03 17:15:01,366 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK2_3DIV: 范围(1-1023), 默认值(4)
2025-06-03 17:15:01,366 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK4_5DIV: 范围(1-1023), 默认值(8)
2025-06-03 17:15:01,366 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK6_7DIV: 范围(1-1023), 默认值(8)
2025-06-03 17:15:01,367 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK8_9DIV: 范围(1-1023), 默认值(8)
2025-06-03 17:15:01,367 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK10_11DIV: 范围(1-1023), 默认值(8)
2025-06-03 17:15:01,367 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK12_13DIV: 范围(1-1023), 默认值(2)
2025-06-03 17:15:01,368 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK0_1DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:01,368 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK2_3DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:01,368 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK4_5DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:01,368 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK6_7DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:01,368 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK8_9DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:01,368 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK10_11DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:01,368 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK12_13DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:01,368 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:227] - DEBUG - 已初始化DDLYdStepCNT_1: 范围(0-255), 默认值(0)
2025-06-03 17:15:01,368 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:229] - INFO - 分频器控件初始化完成
2025-06-03 17:15:01,369 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK0_1PD为启用状态
2025-06-03 17:15:01,369 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK2_3PD为启用状态
2025-06-03 17:15:01,369 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK4_5PD为启用状态
2025-06-03 17:15:01,369 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK6_7PD为启用状态
2025-06-03 17:15:01,369 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK8_9PD为启用状态
2025-06-03 17:15:01,369 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK10_11PD为启用状态
2025-06-03 17:15:01,369 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK12_13PD为启用状态
2025-06-03 17:15:01,369 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK0_1DDLYPD为启用状态
2025-06-03 17:15:01,369 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK2_3DDLYPD为启用状态
2025-06-03 17:15:01,369 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK4_5DDLYPD为启用状态
2025-06-03 17:15:01,369 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK6_7DDLYPD为启用状态
2025-06-03 17:15:01,369 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK8_9DDLYPD为启用状态
2025-06-03 17:15:01,369 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK10_11DDLYPD为启用状态
2025-06-03 17:15:01,369 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK12_13DDLYPD为启用状态
2025-06-03 17:15:01,369 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd0EN为禁用状态
2025-06-03 17:15:01,369 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd2EN为禁用状态
2025-06-03 17:15:01,369 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd4EN为禁用状态
2025-06-03 17:15:01,369 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd6EN为禁用状态
2025-06-03 17:15:01,369 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd8EN为禁用状态
2025-06-03 17:15:01,369 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd10EN为禁用状态
2025-06-03 17:15:01,369 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd12EN为禁用状态
2025-06-03 17:15:01,370 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK0_1PD为关闭状态
2025-06-03 17:15:01,370 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK2_3PD为关闭状态
2025-06-03 17:15:01,370 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK4_5PD为关闭状态
2025-06-03 17:15:01,370 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK6_7PD为关闭状态
2025-06-03 17:15:01,370 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK8_9PD为关闭状态
2025-06-03 17:15:01,370 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK10_11PD为关闭状态
2025-06-03 17:15:01,370 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK12_13PD为关闭状态
2025-06-03 17:15:01,370 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK0_1HSTrig为无相位调整
2025-06-03 17:15:01,370 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK2_3HSTrig为无相位调整
2025-06-03 17:15:01,370 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK4_5HSTrig为无相位调整
2025-06-03 17:15:01,370 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK6_7HSTrig为无相位调整
2025-06-03 17:15:01,370 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK8_9HSTrig为无相位调整
2025-06-03 17:15:01,370 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK10_11HSTrig为无相位调整
2025-06-03 17:15:01,370 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK12_13HSTrig为无相位调整
2025-06-03 17:15:01,370 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS0为不禁用同步
2025-06-03 17:15:01,370 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS2为不禁用同步
2025-06-03 17:15:01,370 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS4为不禁用同步
2025-06-03 17:15:01,370 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS6为不禁用同步
2025-06-03 17:15:01,370 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS8为不禁用同步
2025-06-03 17:15:01,370 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS10为不禁用同步
2025-06-03 17:15:01,370 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS12为不禁用同步
2025-06-03 17:15:01,370 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:309] - INFO - 电源管理和控制状态初始化完成
2025-06-03 17:15:01,371 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK0_1DDLY: 默认0.5ns延迟
2025-06-03 17:15:01,371 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK2_3DDLY: 默认0.5ns延迟
2025-06-03 17:15:01,371 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK4_5DDLY: 默认0.5ns延迟
2025-06-03 17:15:01,371 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK6_7DDLY: 默认0.5ns延迟
2025-06-03 17:15:01,371 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK8_9DDLY: 默认0.5ns延迟
2025-06-03 17:15:01,371 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK10_11DDLY: 默认0.5ns延迟
2025-06-03 17:15:01,372 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK12_13DDLY: 默认0.5ns延迟
2025-06-03 17:15:01,372 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:335] - INFO - SYSREF延迟控件初始化完成
2025-06-03 17:15:01,372 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK2_3DIV 当前值为 4，保持不变
2025-06-03 17:15:01,372 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK4_5DIV 当前值为 8，保持不变
2025-06-03 17:15:01,372 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK6_7DIV 当前值为 8，保持不变
2025-06-03 17:15:01,372 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK8_9DIV 当前值为 8，保持不变
2025-06-03 17:15:01,372 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK10_11DIV 当前值为 8，保持不变
2025-06-03 17:15:01,372 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:376] - DEBUG - 分频器值初始化检查完成
2025-06-03 17:15:01,372 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:404] - DEBUG - 分频器值同步完成
2025-06-03 17:15:01,372 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:351] - INFO - 输出频率初始化完成（延迟计算）
2025-06-03 17:15:01,373 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:416] - INFO - 已连接 lineEditFvco 信号
2025-06-03 17:15:01,373 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout0_SRCMUX 信号
2025-06-03 17:15:01,373 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout1_SRCMUX 信号
2025-06-03 17:15:01,373 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout2_SRCMUX 信号
2025-06-03 17:15:01,373 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout3_SRCMUX 信号
2025-06-03 17:15:01,373 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout4_SRCMUX 信号
2025-06-03 17:15:01,373 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout5_SRCMUX 信号
2025-06-03 17:15:01,373 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout6_SRCMUX 信号
2025-06-03 17:15:01,373 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout7_SRCMUX 信号
2025-06-03 17:15:01,373 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout8_SRCMUX 信号
2025-06-03 17:15:01,373 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout9_SRCMUX 信号
2025-06-03 17:15:01,373 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout10_SRCMUX 信号
2025-06-03 17:15:01,373 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout11_SRCMUX 信号
2025-06-03 17:15:01,373 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout12_SRCMUX 信号
2025-06-03 17:15:01,373 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout13_SRCMUX 信号
2025-06-03 17:15:01,373 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:437] - INFO - 已连接所有时钟输出的SRCMUX复选框信号
2025-06-03 17:15:01,373 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:119] - INFO - 时钟输出特定配置初始化完成
2025-06-03 17:15:01,373 - ModernBaseHandler - [ModernBaseHandler.py:86] - INFO - ModernBaseHandler._post_init 被调用
2025-06-03 17:15:01,374 - ModernBaseHandler - [ModernBaseHandler.py:88] - INFO - UI已设置，开始构建控件映射
2025-06-03 17:15:01,374 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout0FMT (QComboBox)
2025-06-03 17:15:01,374 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout0_1PD (QCheckBox)
2025-06-03 17:15:01,374 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout0_SRCMUX (QCheckBox)
2025-06-03 17:15:01,374 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout10FMT (QComboBox)
2025-06-03 17:15:01,374 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout10_11PD (QCheckBox)
2025-06-03 17:15:01,374 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout10_SRCMUX (QCheckBox)
2025-06-03 17:15:01,374 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout11FMT (QComboBox)
2025-06-03 17:15:01,374 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout11_SRCMUX (QCheckBox)
2025-06-03 17:15:01,374 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout12FMT (QComboBox)
2025-06-03 17:15:01,374 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout12_13PD (QCheckBox)
2025-06-03 17:15:01,374 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout12_SRCMUX (QCheckBox)
2025-06-03 17:15:01,374 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout13FMT (QComboBox)
2025-06-03 17:15:01,374 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout13_SRCMUX (QCheckBox)
2025-06-03 17:15:01,374 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout1FMT (QComboBox)
2025-06-03 17:15:01,374 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout1_SRCMUX (QCheckBox)
2025-06-03 17:15:01,374 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout2FMT (QComboBox)
2025-06-03 17:15:01,374 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout2_3PD (QCheckBox)
2025-06-03 17:15:01,374 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout2_SRCMUX (QCheckBox)
2025-06-03 17:15:01,374 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout3FMT (QComboBox)
2025-06-03 17:15:01,374 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout3_SRCMUX (QCheckBox)
2025-06-03 17:15:01,374 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout4FMT (QComboBox)
2025-06-03 17:15:01,374 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout4_5PD (QCheckBox)
2025-06-03 17:15:01,374 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout4_SRCMUX (QCheckBox)
2025-06-03 17:15:01,375 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout5FMT (QComboBox)
2025-06-03 17:15:01,375 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout5_SRCMUX (QCheckBox)
2025-06-03 17:15:01,375 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout6FMT (QComboBox)
2025-06-03 17:15:01,375 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout6_7PD (QCheckBox)
2025-06-03 17:15:01,375 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout6_SRCMUX (QCheckBox)
2025-06-03 17:15:01,375 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout7FMT (QComboBox)
2025-06-03 17:15:01,375 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout7_SRCMUX (QCheckBox)
2025-06-03 17:15:01,375 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout8FMT (QComboBox)
2025-06-03 17:15:01,375 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout8_9PD (QCheckBox)
2025-06-03 17:15:01,375 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout8_SRCMUX (QCheckBox)
2025-06-03 17:15:01,375 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout9FMT (QComboBox)
2025-06-03 17:15:01,375 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout9_SRCMUX (QCheckBox)
2025-06-03 17:15:01,375 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1BYPASS (QCheckBox)
2025-06-03 17:15:01,375 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1DDLY (QSpinBox)
2025-06-03 17:15:01,375 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1DDLYPD (QCheckBox)
2025-06-03 17:15:01,375 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1DIV (QSpinBox)
2025-06-03 17:15:01,375 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1HSTrig (QCheckBox)
2025-06-03 17:15:01,375 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1PD (QCheckBox)
2025-06-03 17:15:01,375 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1POL (QCheckBox)
2025-06-03 17:15:01,375 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11BYPASS (QCheckBox)
2025-06-03 17:15:01,375 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11DDLY (QSpinBox)
2025-06-03 17:15:01,375 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11DDLYPD (QCheckBox)
2025-06-03 17:15:01,375 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11DIV (QSpinBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11HSTrig (QCheckBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11PD (QCheckBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11POL (QCheckBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13BYPASS (QCheckBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13DDLY (QSpinBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13DDLYPD (QCheckBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13DIV (QSpinBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13HSTrig (QCheckBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13PD (QCheckBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13POL (QCheckBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3BYPASS (QCheckBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3DDLY (QSpinBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3DDLYPD (QCheckBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3DIV (QSpinBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3HSTrig (QCheckBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3PD (QCheckBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3POL (QCheckBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5BYPASS (QCheckBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5DDLY (QSpinBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5DDLYPD (QCheckBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5DIV (QSpinBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5HSTrig (QCheckBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5PD (QCheckBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5POL (QCheckBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7BYPASS (QCheckBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7DDLY (QSpinBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7DDLYPD (QCheckBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7DIV (QSpinBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7HSTrig (QCheckBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7PD (QCheckBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7POL (QCheckBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9BYPASS (QCheckBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9DDLY (QSpinBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9DDLYPD (QCheckBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9DIV (QSpinBox)
2025-06-03 17:15:01,376 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9HSTrig (QCheckBox)
2025-06-03 17:15:01,377 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9PD (QCheckBox)
2025-06-03 17:15:01,377 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9POL (QCheckBox)
2025-06-03 17:15:01,377 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd0EN (QCheckBox)
2025-06-03 17:15:01,377 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd10EN (QCheckBox)
2025-06-03 17:15:01,377 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd12EN (QCheckBox)
2025-06-03 17:15:01,377 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd2EN (QCheckBox)
2025-06-03 17:15:01,377 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd4EN (QCheckBox)
2025-06-03 17:15:01,377 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd6EN (QCheckBox)
2025-06-03 17:15:01,377 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd8EN (QCheckBox)
2025-06-03 17:15:01,377 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_1 (QSpinBox)
2025-06-03 17:15:01,377 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_2 (QSpinBox)
2025-06-03 17:15:01,377 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_3 (QSpinBox)
2025-06-03 17:15:01,377 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_4 (QSpinBox)
2025-06-03 17:15:01,377 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_5 (QSpinBox)
2025-06-03 17:15:01,377 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_6 (QSpinBox)
2025-06-03 17:15:01,377 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_7 (QSpinBox)
2025-06-03 17:15:01,377 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK0_1HSEN (QCheckBox)
2025-06-03 17:15:01,377 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK10_11HSEN (QCheckBox)
2025-06-03 17:15:01,377 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK12_13HSEN (QCheckBox)
2025-06-03 17:15:01,377 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK2_3HSEN (QCheckBox)
2025-06-03 17:15:01,377 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK4_5HSEN (QCheckBox)
2025-06-03 17:15:01,378 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK6_7HSEN (QCheckBox)
2025-06-03 17:15:01,378 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK8_9HSEN (QCheckBox)
2025-06-03 17:15:01,378 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1ADLY (QSpinBox)
2025-06-03 17:15:01,378 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1ADLYEnb (QCheckBox)
2025-06-03 17:15:01,378 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1DDLY (QComboBox)
2025-06-03 17:15:01,378 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1HSTrig (QCheckBox)
2025-06-03 17:15:01,378 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1PD (QCheckBox)
2025-06-03 17:15:01,378 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1POL (QCheckBox)
2025-06-03 17:15:01,378 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11ADLY (QSpinBox)
2025-06-03 17:15:01,378 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11ADLYEnb (QCheckBox)
2025-06-03 17:15:01,378 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11DDLY (QComboBox)
2025-06-03 17:15:01,378 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11HSTrig (QCheckBox)
2025-06-03 17:15:01,378 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11PD (QCheckBox)
2025-06-03 17:15:01,378 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11POL (QCheckBox)
2025-06-03 17:15:01,378 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13ADLY (QSpinBox)
2025-06-03 17:15:01,378 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13ADLYEnb (QCheckBox)
2025-06-03 17:15:01,379 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13DDLY (QComboBox)
2025-06-03 17:15:01,379 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13HSTrig (QCheckBox)
2025-06-03 17:15:01,379 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13PD (QCheckBox)
2025-06-03 17:15:01,379 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13POL (QCheckBox)
2025-06-03 17:15:01,379 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3ADLY (QSpinBox)
2025-06-03 17:15:01,379 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3ADLYEnb (QCheckBox)
2025-06-03 17:15:01,379 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3DDLY (QComboBox)
2025-06-03 17:15:01,379 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3HSTrig (QCheckBox)
2025-06-03 17:15:01,379 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3PD (QCheckBox)
2025-06-03 17:15:01,379 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3POL (QCheckBox)
2025-06-03 17:15:01,379 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5ADLY (QSpinBox)
2025-06-03 17:15:01,379 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5ADLYEnb (QCheckBox)
2025-06-03 17:15:01,379 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5DDLY (QComboBox)
2025-06-03 17:15:01,379 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5HSTrig (QCheckBox)
2025-06-03 17:15:01,379 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5PD (QCheckBox)
2025-06-03 17:15:01,379 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5POL (QCheckBox)
2025-06-03 17:15:01,379 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7ADLY (QSpinBox)
2025-06-03 17:15:01,379 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7ADLYEnb (QCheckBox)
2025-06-03 17:15:01,379 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7DDLY (QComboBox)
2025-06-03 17:15:01,379 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7HSTrig (QCheckBox)
2025-06-03 17:15:01,379 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7PD (QCheckBox)
2025-06-03 17:15:01,380 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7POL (QCheckBox)
2025-06-03 17:15:01,380 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9ADLY (QSpinBox)
2025-06-03 17:15:01,380 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9ADLYEnb (QCheckBox)
2025-06-03 17:15:01,380 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9DDLY (QComboBox)
2025-06-03 17:15:01,380 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9HSTrig (QCheckBox)
2025-06-03 17:15:01,380 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9PD (QCheckBox)
2025-06-03 17:15:01,380 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9POL (QCheckBox)
2025-06-03 17:15:01,380 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS0 (QCheckBox)
2025-06-03 17:15:01,380 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS10 (QCheckBox)
2025-06-03 17:15:01,380 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS12 (QCheckBox)
2025-06-03 17:15:01,380 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS2 (QCheckBox)
2025-06-03 17:15:01,380 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS4 (QCheckBox)
2025-06-03 17:15:01,380 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS6 (QCheckBox)
2025-06-03 17:15:01,380 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS8 (QCheckBox)
2025-06-03 17:15:01,380 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SysrefCLR (QCheckBox)
2025-06-03 17:15:01,380 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SysrefGBLPD (QCheckBox)
2025-06-03 17:15:01,380 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: label (QLabel)
2025-06-03 17:15:01,380 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: labelFvco (QLabel)
2025-06-03 17:15:01,380 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout0Output (QLineEdit)
2025-06-03 17:15:01,380 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout10Output (QLineEdit)
2025-06-03 17:15:01,380 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout11Output (QLineEdit)
2025-06-03 17:15:01,380 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout12Output (QLineEdit)
2025-06-03 17:15:01,380 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout13Output (QLineEdit)
2025-06-03 17:15:01,380 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout1Output (QLineEdit)
2025-06-03 17:15:01,381 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout2Output (QLineEdit)
2025-06-03 17:15:01,381 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout3Output (QLineEdit)
2025-06-03 17:15:01,381 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout4Output (QLineEdit)
2025-06-03 17:15:01,381 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout5Output (QLineEdit)
2025-06-03 17:15:01,381 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout6Output (QLineEdit)
2025-06-03 17:15:01,381 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout7Output (QLineEdit)
2025-06-03 17:15:01,381 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout8Output (QLineEdit)
2025-06-03 17:15:01,381 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout9Output (QLineEdit)
2025-06-03 17:15:01,381 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFvco (QLineEdit)
2025-06-03 17:15:01,381 - ModernBaseHandler - [ModernBaseHandler.py:119] - INFO - UI中发现 173 个控件
2025-06-03 17:15:01,383 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:452] - ERROR - 后初始化时出错: 'MockRegisterManager' object has no attribute 'get_widget_register_mapping'
2025-06-03 17:15:01,383 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:56] - INFO - 现代化时钟输出处理器初始化完成
2025-06-03 17:15:01,388 - test_utils - [test_utils.py:55] - ERROR - 现代化时钟输出处理器创建失败: 0 not greater than 0
2025-06-03 17:15:01,392 - ModernBaseHandler - [ModernBaseHandler.py:71] - WARNING - 连接RegisterUpdateBus时出错: wrapped C/C++ object of type RegisterUpdateBus has been deleted，继续初始化
2025-06-03 17:15:01,821 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout0FMT 格式选项
2025-06-03 17:15:01,821 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout1FMT 格式选项
2025-06-03 17:15:01,821 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout2FMT 格式选项
2025-06-03 17:15:01,821 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout3FMT 格式选项
2025-06-03 17:15:01,821 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout4FMT 格式选项
2025-06-03 17:15:01,821 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout5FMT 格式选项
2025-06-03 17:15:01,821 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout6FMT 格式选项
2025-06-03 17:15:01,821 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout7FMT 格式选项
2025-06-03 17:15:01,821 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout8FMT 格式选项
2025-06-03 17:15:01,821 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout9FMT 格式选项
2025-06-03 17:15:01,821 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout10FMT 格式选项
2025-06-03 17:15:01,821 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout11FMT 格式选项
2025-06-03 17:15:01,822 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout12FMT 格式选项
2025-06-03 17:15:01,822 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout13FMT 格式选项
2025-06-03 17:15:01,822 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:172] - INFO - 所有格式下拉框初始化完成
2025-06-03 17:15:01,822 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK0_1DIV: 范围(1-1023), 默认值(2)
2025-06-03 17:15:01,822 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK2_3DIV: 范围(1-1023), 默认值(4)
2025-06-03 17:15:01,822 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK4_5DIV: 范围(1-1023), 默认值(8)
2025-06-03 17:15:01,822 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK6_7DIV: 范围(1-1023), 默认值(8)
2025-06-03 17:15:01,822 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK8_9DIV: 范围(1-1023), 默认值(8)
2025-06-03 17:15:01,822 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK10_11DIV: 范围(1-1023), 默认值(8)
2025-06-03 17:15:01,823 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK12_13DIV: 范围(1-1023), 默认值(2)
2025-06-03 17:15:01,823 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK0_1DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:01,823 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK2_3DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:01,823 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK4_5DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:01,823 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK6_7DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:01,823 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK8_9DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:01,823 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK10_11DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:01,823 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK12_13DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:01,823 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:227] - DEBUG - 已初始化DDLYdStepCNT_1: 范围(0-255), 默认值(0)
2025-06-03 17:15:01,823 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:229] - INFO - 分频器控件初始化完成
2025-06-03 17:15:01,823 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK0_1PD为启用状态
2025-06-03 17:15:01,823 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK2_3PD为启用状态
2025-06-03 17:15:01,823 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK4_5PD为启用状态
2025-06-03 17:15:01,823 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK6_7PD为启用状态
2025-06-03 17:15:01,823 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK8_9PD为启用状态
2025-06-03 17:15:01,823 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK10_11PD为启用状态
2025-06-03 17:15:01,823 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK12_13PD为启用状态
2025-06-03 17:15:01,823 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK0_1DDLYPD为启用状态
2025-06-03 17:15:01,823 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK2_3DDLYPD为启用状态
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK4_5DDLYPD为启用状态
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK6_7DDLYPD为启用状态
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK8_9DDLYPD为启用状态
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK10_11DDLYPD为启用状态
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK12_13DDLYPD为启用状态
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd0EN为禁用状态
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd2EN为禁用状态
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd4EN为禁用状态
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd6EN为禁用状态
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd8EN为禁用状态
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd10EN为禁用状态
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd12EN为禁用状态
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK0_1PD为关闭状态
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK2_3PD为关闭状态
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK4_5PD为关闭状态
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK6_7PD为关闭状态
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK8_9PD为关闭状态
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK10_11PD为关闭状态
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK12_13PD为关闭状态
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK0_1HSTrig为无相位调整
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK2_3HSTrig为无相位调整
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK4_5HSTrig为无相位调整
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK6_7HSTrig为无相位调整
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK8_9HSTrig为无相位调整
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK10_11HSTrig为无相位调整
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK12_13HSTrig为无相位调整
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS0为不禁用同步
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS2为不禁用同步
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS4为不禁用同步
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS6为不禁用同步
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS8为不禁用同步
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS10为不禁用同步
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS12为不禁用同步
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:309] - INFO - 电源管理和控制状态初始化完成
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK0_1DDLY: 默认0.5ns延迟
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK2_3DDLY: 默认0.5ns延迟
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK4_5DDLY: 默认0.5ns延迟
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK6_7DDLY: 默认0.5ns延迟
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK8_9DDLY: 默认0.5ns延迟
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK10_11DDLY: 默认0.5ns延迟
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK12_13DDLY: 默认0.5ns延迟
2025-06-03 17:15:01,824 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:335] - INFO - SYSREF延迟控件初始化完成
2025-06-03 17:15:01,826 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK2_3DIV 当前值为 4，保持不变
2025-06-03 17:15:01,826 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK4_5DIV 当前值为 8，保持不变
2025-06-03 17:15:01,826 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK6_7DIV 当前值为 8，保持不变
2025-06-03 17:15:01,826 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK8_9DIV 当前值为 8，保持不变
2025-06-03 17:15:01,826 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK10_11DIV 当前值为 8，保持不变
2025-06-03 17:15:01,826 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:376] - DEBUG - 分频器值初始化检查完成
2025-06-03 17:15:01,826 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:404] - DEBUG - 分频器值同步完成
2025-06-03 17:15:01,826 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:351] - INFO - 输出频率初始化完成（延迟计算）
2025-06-03 17:15:01,826 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:416] - INFO - 已连接 lineEditFvco 信号
2025-06-03 17:15:01,826 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout0_SRCMUX 信号
2025-06-03 17:15:01,826 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout1_SRCMUX 信号
2025-06-03 17:15:01,826 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout2_SRCMUX 信号
2025-06-03 17:15:01,826 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout3_SRCMUX 信号
2025-06-03 17:15:01,826 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout4_SRCMUX 信号
2025-06-03 17:15:01,826 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout5_SRCMUX 信号
2025-06-03 17:15:01,826 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout6_SRCMUX 信号
2025-06-03 17:15:01,826 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout7_SRCMUX 信号
2025-06-03 17:15:01,826 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout8_SRCMUX 信号
2025-06-03 17:15:01,826 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout9_SRCMUX 信号
2025-06-03 17:15:01,826 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout10_SRCMUX 信号
2025-06-03 17:15:01,826 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout11_SRCMUX 信号
2025-06-03 17:15:01,826 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout12_SRCMUX 信号
2025-06-03 17:15:01,826 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout13_SRCMUX 信号
2025-06-03 17:15:01,826 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:437] - INFO - 已连接所有时钟输出的SRCMUX复选框信号
2025-06-03 17:15:01,826 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:119] - INFO - 时钟输出特定配置初始化完成
2025-06-03 17:15:01,826 - ModernBaseHandler - [ModernBaseHandler.py:86] - INFO - ModernBaseHandler._post_init 被调用
2025-06-03 17:15:01,826 - ModernBaseHandler - [ModernBaseHandler.py:88] - INFO - UI已设置，开始构建控件映射
2025-06-03 17:15:01,826 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout0FMT (QComboBox)
2025-06-03 17:15:01,826 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout0_1PD (QCheckBox)
2025-06-03 17:15:01,826 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout0_SRCMUX (QCheckBox)
2025-06-03 17:15:01,826 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout10FMT (QComboBox)
2025-06-03 17:15:01,826 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout10_11PD (QCheckBox)
2025-06-03 17:15:01,827 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout10_SRCMUX (QCheckBox)
2025-06-03 17:15:01,827 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout11FMT (QComboBox)
2025-06-03 17:15:01,827 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout11_SRCMUX (QCheckBox)
2025-06-03 17:15:01,827 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout12FMT (QComboBox)
2025-06-03 17:15:01,827 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout12_13PD (QCheckBox)
2025-06-03 17:15:01,827 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout12_SRCMUX (QCheckBox)
2025-06-03 17:15:01,827 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout13FMT (QComboBox)
2025-06-03 17:15:01,827 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout13_SRCMUX (QCheckBox)
2025-06-03 17:15:01,827 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout1FMT (QComboBox)
2025-06-03 17:15:01,827 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout1_SRCMUX (QCheckBox)
2025-06-03 17:15:01,828 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout2FMT (QComboBox)
2025-06-03 17:15:01,828 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout2_3PD (QCheckBox)
2025-06-03 17:15:01,828 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout2_SRCMUX (QCheckBox)
2025-06-03 17:15:01,828 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout3FMT (QComboBox)
2025-06-03 17:15:01,828 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout3_SRCMUX (QCheckBox)
2025-06-03 17:15:01,828 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout4FMT (QComboBox)
2025-06-03 17:15:01,828 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout4_5PD (QCheckBox)
2025-06-03 17:15:01,828 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout4_SRCMUX (QCheckBox)
2025-06-03 17:15:01,828 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout5FMT (QComboBox)
2025-06-03 17:15:01,828 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout5_SRCMUX (QCheckBox)
2025-06-03 17:15:01,828 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout6FMT (QComboBox)
2025-06-03 17:15:01,828 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout6_7PD (QCheckBox)
2025-06-03 17:15:01,828 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout6_SRCMUX (QCheckBox)
2025-06-03 17:15:01,828 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout7FMT (QComboBox)
2025-06-03 17:15:01,828 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout7_SRCMUX (QCheckBox)
2025-06-03 17:15:01,828 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout8FMT (QComboBox)
2025-06-03 17:15:01,828 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout8_9PD (QCheckBox)
2025-06-03 17:15:01,828 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout8_SRCMUX (QCheckBox)
2025-06-03 17:15:01,828 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout9FMT (QComboBox)
2025-06-03 17:15:01,828 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout9_SRCMUX (QCheckBox)
2025-06-03 17:15:01,828 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1BYPASS (QCheckBox)
2025-06-03 17:15:01,828 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1DDLY (QSpinBox)
2025-06-03 17:15:01,828 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1DDLYPD (QCheckBox)
2025-06-03 17:15:01,828 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1DIV (QSpinBox)
2025-06-03 17:15:01,828 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1HSTrig (QCheckBox)
2025-06-03 17:15:01,828 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1PD (QCheckBox)
2025-06-03 17:15:01,828 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1POL (QCheckBox)
2025-06-03 17:15:01,828 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11BYPASS (QCheckBox)
2025-06-03 17:15:01,828 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11DDLY (QSpinBox)
2025-06-03 17:15:01,828 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11DDLYPD (QCheckBox)
2025-06-03 17:15:01,828 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11DIV (QSpinBox)
2025-06-03 17:15:01,829 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11HSTrig (QCheckBox)
2025-06-03 17:15:01,829 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11PD (QCheckBox)
2025-06-03 17:15:01,829 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11POL (QCheckBox)
2025-06-03 17:15:01,829 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13BYPASS (QCheckBox)
2025-06-03 17:15:01,829 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13DDLY (QSpinBox)
2025-06-03 17:15:01,829 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13DDLYPD (QCheckBox)
2025-06-03 17:15:01,829 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13DIV (QSpinBox)
2025-06-03 17:15:01,829 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13HSTrig (QCheckBox)
2025-06-03 17:15:01,829 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13PD (QCheckBox)
2025-06-03 17:15:01,829 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13POL (QCheckBox)
2025-06-03 17:15:01,829 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3BYPASS (QCheckBox)
2025-06-03 17:15:01,829 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3DDLY (QSpinBox)
2025-06-03 17:15:01,829 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3DDLYPD (QCheckBox)
2025-06-03 17:15:01,829 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3DIV (QSpinBox)
2025-06-03 17:15:01,829 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3HSTrig (QCheckBox)
2025-06-03 17:15:01,829 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3PD (QCheckBox)
2025-06-03 17:15:01,829 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3POL (QCheckBox)
2025-06-03 17:15:01,829 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5BYPASS (QCheckBox)
2025-06-03 17:15:01,829 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5DDLY (QSpinBox)
2025-06-03 17:15:01,829 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5DDLYPD (QCheckBox)
2025-06-03 17:15:01,830 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5DIV (QSpinBox)
2025-06-03 17:15:01,830 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5HSTrig (QCheckBox)
2025-06-03 17:15:01,830 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5PD (QCheckBox)
2025-06-03 17:15:01,830 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5POL (QCheckBox)
2025-06-03 17:15:01,830 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7BYPASS (QCheckBox)
2025-06-03 17:15:01,830 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7DDLY (QSpinBox)
2025-06-03 17:15:01,830 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7DDLYPD (QCheckBox)
2025-06-03 17:15:01,830 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7DIV (QSpinBox)
2025-06-03 17:15:01,830 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7HSTrig (QCheckBox)
2025-06-03 17:15:01,830 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7PD (QCheckBox)
2025-06-03 17:15:01,830 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7POL (QCheckBox)
2025-06-03 17:15:01,830 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9BYPASS (QCheckBox)
2025-06-03 17:15:01,830 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9DDLY (QSpinBox)
2025-06-03 17:15:01,830 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9DDLYPD (QCheckBox)
2025-06-03 17:15:01,830 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9DIV (QSpinBox)
2025-06-03 17:15:01,830 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9HSTrig (QCheckBox)
2025-06-03 17:15:01,830 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9PD (QCheckBox)
2025-06-03 17:15:01,830 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9POL (QCheckBox)
2025-06-03 17:15:01,830 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd0EN (QCheckBox)
2025-06-03 17:15:01,830 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd10EN (QCheckBox)
2025-06-03 17:15:01,830 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd12EN (QCheckBox)
2025-06-03 17:15:01,830 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd2EN (QCheckBox)
2025-06-03 17:15:01,830 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd4EN (QCheckBox)
2025-06-03 17:15:01,830 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd6EN (QCheckBox)
2025-06-03 17:15:01,830 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd8EN (QCheckBox)
2025-06-03 17:15:01,830 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_1 (QSpinBox)
2025-06-03 17:15:01,830 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_2 (QSpinBox)
2025-06-03 17:15:01,830 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_3 (QSpinBox)
2025-06-03 17:15:01,830 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_4 (QSpinBox)
2025-06-03 17:15:01,830 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_5 (QSpinBox)
2025-06-03 17:15:01,830 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_6 (QSpinBox)
2025-06-03 17:15:01,830 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_7 (QSpinBox)
2025-06-03 17:15:01,831 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK0_1HSEN (QCheckBox)
2025-06-03 17:15:01,831 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK10_11HSEN (QCheckBox)
2025-06-03 17:15:01,831 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK12_13HSEN (QCheckBox)
2025-06-03 17:15:01,831 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK2_3HSEN (QCheckBox)
2025-06-03 17:15:01,831 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK4_5HSEN (QCheckBox)
2025-06-03 17:15:01,831 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK6_7HSEN (QCheckBox)
2025-06-03 17:15:01,831 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK8_9HSEN (QCheckBox)
2025-06-03 17:15:01,831 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1ADLY (QSpinBox)
2025-06-03 17:15:01,831 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1ADLYEnb (QCheckBox)
2025-06-03 17:15:01,831 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1DDLY (QComboBox)
2025-06-03 17:15:01,831 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1HSTrig (QCheckBox)
2025-06-03 17:15:01,831 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1PD (QCheckBox)
2025-06-03 17:15:01,831 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1POL (QCheckBox)
2025-06-03 17:15:01,831 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11ADLY (QSpinBox)
2025-06-03 17:15:01,832 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11ADLYEnb (QCheckBox)
2025-06-03 17:15:01,832 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11DDLY (QComboBox)
2025-06-03 17:15:01,832 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11HSTrig (QCheckBox)
2025-06-03 17:15:01,832 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11PD (QCheckBox)
2025-06-03 17:15:01,832 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11POL (QCheckBox)
2025-06-03 17:15:01,832 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13ADLY (QSpinBox)
2025-06-03 17:15:01,832 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13ADLYEnb (QCheckBox)
2025-06-03 17:15:01,832 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13DDLY (QComboBox)
2025-06-03 17:15:01,832 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13HSTrig (QCheckBox)
2025-06-03 17:15:01,832 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13PD (QCheckBox)
2025-06-03 17:15:01,832 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13POL (QCheckBox)
2025-06-03 17:15:01,832 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3ADLY (QSpinBox)
2025-06-03 17:15:01,832 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3ADLYEnb (QCheckBox)
2025-06-03 17:15:01,832 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3DDLY (QComboBox)
2025-06-03 17:15:01,832 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3HSTrig (QCheckBox)
2025-06-03 17:15:01,832 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3PD (QCheckBox)
2025-06-03 17:15:01,832 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3POL (QCheckBox)
2025-06-03 17:15:01,832 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5ADLY (QSpinBox)
2025-06-03 17:15:01,832 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5ADLYEnb (QCheckBox)
2025-06-03 17:15:01,832 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5DDLY (QComboBox)
2025-06-03 17:15:01,832 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5HSTrig (QCheckBox)
2025-06-03 17:15:01,833 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5PD (QCheckBox)
2025-06-03 17:15:01,833 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5POL (QCheckBox)
2025-06-03 17:15:01,833 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7ADLY (QSpinBox)
2025-06-03 17:15:01,833 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7ADLYEnb (QCheckBox)
2025-06-03 17:15:01,833 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7DDLY (QComboBox)
2025-06-03 17:15:01,833 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7HSTrig (QCheckBox)
2025-06-03 17:15:01,833 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7PD (QCheckBox)
2025-06-03 17:15:01,833 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7POL (QCheckBox)
2025-06-03 17:15:01,833 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9ADLY (QSpinBox)
2025-06-03 17:15:01,833 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9ADLYEnb (QCheckBox)
2025-06-03 17:15:01,833 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9DDLY (QComboBox)
2025-06-03 17:15:01,833 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9HSTrig (QCheckBox)
2025-06-03 17:15:01,833 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9PD (QCheckBox)
2025-06-03 17:15:01,833 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9POL (QCheckBox)
2025-06-03 17:15:01,833 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS0 (QCheckBox)
2025-06-03 17:15:01,833 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS10 (QCheckBox)
2025-06-03 17:15:01,833 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS12 (QCheckBox)
2025-06-03 17:15:01,833 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS2 (QCheckBox)
2025-06-03 17:15:01,833 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS4 (QCheckBox)
2025-06-03 17:15:01,833 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS6 (QCheckBox)
2025-06-03 17:15:01,833 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS8 (QCheckBox)
2025-06-03 17:15:01,833 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SysrefCLR (QCheckBox)
2025-06-03 17:15:01,833 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SysrefGBLPD (QCheckBox)
2025-06-03 17:15:01,833 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: label (QLabel)
2025-06-03 17:15:01,833 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: labelFvco (QLabel)
2025-06-03 17:15:01,833 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout0Output (QLineEdit)
2025-06-03 17:15:01,833 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout10Output (QLineEdit)
2025-06-03 17:15:01,833 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout11Output (QLineEdit)
2025-06-03 17:15:01,833 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout12Output (QLineEdit)
2025-06-03 17:15:01,833 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout13Output (QLineEdit)
2025-06-03 17:15:01,834 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout1Output (QLineEdit)
2025-06-03 17:15:01,834 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout2Output (QLineEdit)
2025-06-03 17:15:01,834 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout3Output (QLineEdit)
2025-06-03 17:15:01,834 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout4Output (QLineEdit)
2025-06-03 17:15:01,834 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout5Output (QLineEdit)
2025-06-03 17:15:01,834 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout6Output (QLineEdit)
2025-06-03 17:15:01,834 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout7Output (QLineEdit)
2025-06-03 17:15:01,834 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout8Output (QLineEdit)
2025-06-03 17:15:01,834 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout9Output (QLineEdit)
2025-06-03 17:15:01,834 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFvco (QLineEdit)
2025-06-03 17:15:01,834 - ModernBaseHandler - [ModernBaseHandler.py:119] - INFO - UI中发现 173 个控件
2025-06-03 17:15:01,834 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:452] - ERROR - 后初始化时出错: 'MockRegisterManager' object has no attribute 'get_widget_register_mapping'
2025-06-03 17:15:01,835 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:56] - INFO - 现代化时钟输出处理器初始化完成
2025-06-03 17:15:01,835 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:744] - INFO - 应用时钟输出预设: default
2025-06-03 17:15:01,835 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出0分频值: DCLK0_1DIV = 1
2025-06-03 17:15:01,836 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出0频率: 2949.12 MHz (VCO: 2949.12, 分频: 1)
2025-06-03 17:15:01,836 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出1分频值: DCLK0_1DIV = 1
2025-06-03 17:15:01,836 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出1频率: 2949.12 MHz (VCO: 2949.12, 分频: 1)
2025-06-03 17:15:01,836 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出2分频值: DCLK2_3DIV = 1
2025-06-03 17:15:01,836 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出2频率: 2949.12 MHz (VCO: 2949.12, 分频: 1)
2025-06-03 17:15:01,836 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出3分频值: DCLK2_3DIV = 1
2025-06-03 17:15:01,836 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出3频率: 2949.12 MHz (VCO: 2949.12, 分频: 1)
2025-06-03 17:15:01,836 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出4分频值: DCLK4_5DIV = 1
2025-06-03 17:15:01,836 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出4频率: 2949.12 MHz (VCO: 2949.12, 分频: 1)
2025-06-03 17:15:01,836 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出5分频值: DCLK4_5DIV = 1
2025-06-03 17:15:01,836 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出5频率: 2949.12 MHz (VCO: 2949.12, 分频: 1)
2025-06-03 17:15:01,837 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出6分频值: DCLK6_7DIV = 1
2025-06-03 17:15:01,837 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出6频率: 2949.12 MHz (VCO: 2949.12, 分频: 1)
2025-06-03 17:15:01,837 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出7分频值: DCLK6_7DIV = 1
2025-06-03 17:15:01,837 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出7频率: 2949.12 MHz (VCO: 2949.12, 分频: 1)
2025-06-03 17:15:01,837 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出8分频值: DCLK8_9DIV = 1
2025-06-03 17:15:01,837 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出8频率: 2949.12 MHz (VCO: 2949.12, 分频: 1)
2025-06-03 17:15:01,837 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出9分频值: DCLK8_9DIV = 1
2025-06-03 17:15:01,837 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出9频率: 2949.12 MHz (VCO: 2949.12, 分频: 1)
2025-06-03 17:15:01,837 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出10分频值: DCLK10_11DIV = 1
2025-06-03 17:15:01,837 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出10频率: 2949.12 MHz (VCO: 2949.12, 分频: 1)
2025-06-03 17:15:01,837 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出11分频值: DCLK10_11DIV = 1
2025-06-03 17:15:01,837 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出11频率: 2949.12 MHz (VCO: 2949.12, 分频: 1)
2025-06-03 17:15:01,837 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出12分频值: DCLK12_13DIV = 1
2025-06-03 17:15:01,837 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出12频率: 2949.12 MHz (VCO: 2949.12, 分频: 1)
2025-06-03 17:15:01,837 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出13分频值: DCLK12_13DIV = 1
2025-06-03 17:15:01,838 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出13频率: 2949.12 MHz (VCO: 2949.12, 分频: 1)
2025-06-03 17:15:01,838 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:834] - INFO - 时钟输出预设 default 应用完成
2025-06-03 17:15:01,838 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:744] - INFO - 应用时钟输出预设: low_frequency
2025-06-03 17:15:01,839 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出0分频值: DCLK0_1DIV = 4
2025-06-03 17:15:01,839 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出0频率: 737.28 MHz (VCO: 2949.12, 分频: 4)
2025-06-03 17:15:01,839 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出1分频值: DCLK0_1DIV = 4
2025-06-03 17:15:01,839 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出1频率: 737.28 MHz (VCO: 2949.12, 分频: 4)
2025-06-03 17:15:01,839 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出2分频值: DCLK2_3DIV = 4
2025-06-03 17:15:01,839 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出2频率: 737.28 MHz (VCO: 2949.12, 分频: 4)
2025-06-03 17:15:01,839 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出3分频值: DCLK2_3DIV = 4
2025-06-03 17:15:01,839 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出3频率: 737.28 MHz (VCO: 2949.12, 分频: 4)
2025-06-03 17:15:01,839 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出4分频值: DCLK4_5DIV = 4
2025-06-03 17:15:01,840 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出4频率: 737.28 MHz (VCO: 2949.12, 分频: 4)
2025-06-03 17:15:01,840 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出5分频值: DCLK4_5DIV = 4
2025-06-03 17:15:01,840 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出5频率: 737.28 MHz (VCO: 2949.12, 分频: 4)
2025-06-03 17:15:01,840 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出6分频值: DCLK6_7DIV = 4
2025-06-03 17:15:01,840 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出6频率: 737.28 MHz (VCO: 2949.12, 分频: 4)
2025-06-03 17:15:01,840 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出7分频值: DCLK6_7DIV = 4
2025-06-03 17:15:01,840 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出7频率: 737.28 MHz (VCO: 2949.12, 分频: 4)
2025-06-03 17:15:01,840 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出8分频值: DCLK8_9DIV = 4
2025-06-03 17:15:01,840 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出8频率: 737.28 MHz (VCO: 2949.12, 分频: 4)
2025-06-03 17:15:01,840 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出9分频值: DCLK8_9DIV = 4
2025-06-03 17:15:01,840 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出9频率: 737.28 MHz (VCO: 2949.12, 分频: 4)
2025-06-03 17:15:01,840 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出10分频值: DCLK10_11DIV = 4
2025-06-03 17:15:01,840 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出10频率: 737.28 MHz (VCO: 2949.12, 分频: 4)
2025-06-03 17:15:01,840 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出11分频值: DCLK10_11DIV = 4
2025-06-03 17:15:01,840 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出11频率: 737.28 MHz (VCO: 2949.12, 分频: 4)
2025-06-03 17:15:01,840 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出12分频值: DCLK12_13DIV = 4
2025-06-03 17:15:01,840 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出12频率: 737.28 MHz (VCO: 2949.12, 分频: 4)
2025-06-03 17:15:01,841 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出13分频值: DCLK12_13DIV = 4
2025-06-03 17:15:01,841 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出13频率: 737.28 MHz (VCO: 2949.12, 分频: 4)
2025-06-03 17:15:01,841 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:834] - INFO - 时钟输出预设 low_frequency 应用完成
2025-06-03 17:15:01,841 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出0分频值: DCLK0_1DIV = 4
2025-06-03 17:15:01,841 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出2分频值: DCLK2_3DIV = 4
2025-06-03 17:15:01,841 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出4分频值: DCLK4_5DIV = 4
2025-06-03 17:15:01,841 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出6分频值: DCLK6_7DIV = 4
2025-06-03 17:15:01,841 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出8分频值: DCLK8_9DIV = 4
2025-06-03 17:15:01,841 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出10分频值: DCLK10_11DIV = 4
2025-06-03 17:15:01,841 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出12分频值: DCLK12_13DIV = 4
2025-06-03 17:15:01,848 - ModernBaseHandler - [ModernBaseHandler.py:71] - WARNING - 连接RegisterUpdateBus时出错: wrapped C/C++ object of type RegisterUpdateBus has been deleted，继续初始化
2025-06-03 17:15:02,234 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout0FMT 格式选项
2025-06-03 17:15:02,234 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout1FMT 格式选项
2025-06-03 17:15:02,234 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout2FMT 格式选项
2025-06-03 17:15:02,235 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout3FMT 格式选项
2025-06-03 17:15:02,235 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout4FMT 格式选项
2025-06-03 17:15:02,235 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout5FMT 格式选项
2025-06-03 17:15:02,235 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout6FMT 格式选项
2025-06-03 17:15:02,235 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout7FMT 格式选项
2025-06-03 17:15:02,235 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout8FMT 格式选项
2025-06-03 17:15:02,236 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout9FMT 格式选项
2025-06-03 17:15:02,236 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout10FMT 格式选项
2025-06-03 17:15:02,236 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout11FMT 格式选项
2025-06-03 17:15:02,236 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout12FMT 格式选项
2025-06-03 17:15:02,236 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout13FMT 格式选项
2025-06-03 17:15:02,236 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:172] - INFO - 所有格式下拉框初始化完成
2025-06-03 17:15:02,236 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK0_1DIV: 范围(1-1023), 默认值(2)
2025-06-03 17:15:02,237 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK2_3DIV: 范围(1-1023), 默认值(4)
2025-06-03 17:15:02,237 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK4_5DIV: 范围(1-1023), 默认值(8)
2025-06-03 17:15:02,237 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK6_7DIV: 范围(1-1023), 默认值(8)
2025-06-03 17:15:02,237 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK8_9DIV: 范围(1-1023), 默认值(8)
2025-06-03 17:15:02,237 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK10_11DIV: 范围(1-1023), 默认值(8)
2025-06-03 17:15:02,237 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK12_13DIV: 范围(1-1023), 默认值(2)
2025-06-03 17:15:02,237 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK0_1DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:02,237 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK2_3DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:02,237 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK4_5DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:02,237 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK6_7DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:02,238 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK8_9DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:02,238 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK10_11DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:02,238 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK12_13DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:02,238 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:227] - DEBUG - 已初始化DDLYdStepCNT_1: 范围(0-255), 默认值(0)
2025-06-03 17:15:02,238 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:229] - INFO - 分频器控件初始化完成
2025-06-03 17:15:02,238 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK0_1PD为启用状态
2025-06-03 17:15:02,238 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK2_3PD为启用状态
2025-06-03 17:15:02,238 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK4_5PD为启用状态
2025-06-03 17:15:02,238 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK6_7PD为启用状态
2025-06-03 17:15:02,238 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK8_9PD为启用状态
2025-06-03 17:15:02,238 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK10_11PD为启用状态
2025-06-03 17:15:02,238 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK12_13PD为启用状态
2025-06-03 17:15:02,238 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK0_1DDLYPD为启用状态
2025-06-03 17:15:02,238 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK2_3DDLYPD为启用状态
2025-06-03 17:15:02,239 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK4_5DDLYPD为启用状态
2025-06-03 17:15:02,239 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK6_7DDLYPD为启用状态
2025-06-03 17:15:02,239 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK8_9DDLYPD为启用状态
2025-06-03 17:15:02,239 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK10_11DDLYPD为启用状态
2025-06-03 17:15:02,239 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK12_13DDLYPD为启用状态
2025-06-03 17:15:02,239 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd0EN为禁用状态
2025-06-03 17:15:02,239 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd2EN为禁用状态
2025-06-03 17:15:02,239 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd4EN为禁用状态
2025-06-03 17:15:02,239 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd6EN为禁用状态
2025-06-03 17:15:02,239 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd8EN为禁用状态
2025-06-03 17:15:02,239 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd10EN为禁用状态
2025-06-03 17:15:02,239 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd12EN为禁用状态
2025-06-03 17:15:02,239 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK0_1PD为关闭状态
2025-06-03 17:15:02,239 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK2_3PD为关闭状态
2025-06-03 17:15:02,239 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK4_5PD为关闭状态
2025-06-03 17:15:02,239 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK6_7PD为关闭状态
2025-06-03 17:15:02,239 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK8_9PD为关闭状态
2025-06-03 17:15:02,239 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK10_11PD为关闭状态
2025-06-03 17:15:02,239 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK12_13PD为关闭状态
2025-06-03 17:15:02,239 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK0_1HSTrig为无相位调整
2025-06-03 17:15:02,239 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK2_3HSTrig为无相位调整
2025-06-03 17:15:02,239 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK4_5HSTrig为无相位调整
2025-06-03 17:15:02,240 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK6_7HSTrig为无相位调整
2025-06-03 17:15:02,240 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK8_9HSTrig为无相位调整
2025-06-03 17:15:02,240 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK10_11HSTrig为无相位调整
2025-06-03 17:15:02,240 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK12_13HSTrig为无相位调整
2025-06-03 17:15:02,240 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS0为不禁用同步
2025-06-03 17:15:02,240 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS2为不禁用同步
2025-06-03 17:15:02,240 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS4为不禁用同步
2025-06-03 17:15:02,240 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS6为不禁用同步
2025-06-03 17:15:02,240 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS8为不禁用同步
2025-06-03 17:15:02,240 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS10为不禁用同步
2025-06-03 17:15:02,240 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS12为不禁用同步
2025-06-03 17:15:02,240 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:309] - INFO - 电源管理和控制状态初始化完成
2025-06-03 17:15:02,241 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK0_1DDLY: 默认0.5ns延迟
2025-06-03 17:15:02,241 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK2_3DDLY: 默认0.5ns延迟
2025-06-03 17:15:02,241 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK4_5DDLY: 默认0.5ns延迟
2025-06-03 17:15:02,241 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK6_7DDLY: 默认0.5ns延迟
2025-06-03 17:15:02,241 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK8_9DDLY: 默认0.5ns延迟
2025-06-03 17:15:02,241 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK10_11DDLY: 默认0.5ns延迟
2025-06-03 17:15:02,241 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK12_13DDLY: 默认0.5ns延迟
2025-06-03 17:15:02,241 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:335] - INFO - SYSREF延迟控件初始化完成
2025-06-03 17:15:02,241 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK2_3DIV 当前值为 4，保持不变
2025-06-03 17:15:02,241 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK4_5DIV 当前值为 8，保持不变
2025-06-03 17:15:02,241 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK6_7DIV 当前值为 8，保持不变
2025-06-03 17:15:02,241 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK8_9DIV 当前值为 8，保持不变
2025-06-03 17:15:02,241 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK10_11DIV 当前值为 8，保持不变
2025-06-03 17:15:02,241 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:376] - DEBUG - 分频器值初始化检查完成
2025-06-03 17:15:02,242 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:404] - DEBUG - 分频器值同步完成
2025-06-03 17:15:02,242 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:351] - INFO - 输出频率初始化完成（延迟计算）
2025-06-03 17:15:02,242 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:416] - INFO - 已连接 lineEditFvco 信号
2025-06-03 17:15:02,242 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout0_SRCMUX 信号
2025-06-03 17:15:02,242 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout1_SRCMUX 信号
2025-06-03 17:15:02,242 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout2_SRCMUX 信号
2025-06-03 17:15:02,242 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout3_SRCMUX 信号
2025-06-03 17:15:02,242 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout4_SRCMUX 信号
2025-06-03 17:15:02,242 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout5_SRCMUX 信号
2025-06-03 17:15:02,242 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout6_SRCMUX 信号
2025-06-03 17:15:02,242 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout7_SRCMUX 信号
2025-06-03 17:15:02,242 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout8_SRCMUX 信号
2025-06-03 17:15:02,242 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout9_SRCMUX 信号
2025-06-03 17:15:02,243 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout10_SRCMUX 信号
2025-06-03 17:15:02,243 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout11_SRCMUX 信号
2025-06-03 17:15:02,243 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout12_SRCMUX 信号
2025-06-03 17:15:02,243 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout13_SRCMUX 信号
2025-06-03 17:15:02,243 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:437] - INFO - 已连接所有时钟输出的SRCMUX复选框信号
2025-06-03 17:15:02,243 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:119] - INFO - 时钟输出特定配置初始化完成
2025-06-03 17:15:02,243 - ModernBaseHandler - [ModernBaseHandler.py:86] - INFO - ModernBaseHandler._post_init 被调用
2025-06-03 17:15:02,243 - ModernBaseHandler - [ModernBaseHandler.py:88] - INFO - UI已设置，开始构建控件映射
2025-06-03 17:15:02,243 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout0FMT (QComboBox)
2025-06-03 17:15:02,243 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout0_1PD (QCheckBox)
2025-06-03 17:15:02,243 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout0_SRCMUX (QCheckBox)
2025-06-03 17:15:02,243 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout10FMT (QComboBox)
2025-06-03 17:15:02,243 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout10_11PD (QCheckBox)
2025-06-03 17:15:02,243 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout10_SRCMUX (QCheckBox)
2025-06-03 17:15:02,243 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout11FMT (QComboBox)
2025-06-03 17:15:02,243 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout11_SRCMUX (QCheckBox)
2025-06-03 17:15:02,243 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout12FMT (QComboBox)
2025-06-03 17:15:02,243 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout12_13PD (QCheckBox)
2025-06-03 17:15:02,243 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout12_SRCMUX (QCheckBox)
2025-06-03 17:15:02,243 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout13FMT (QComboBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout13_SRCMUX (QCheckBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout1FMT (QComboBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout1_SRCMUX (QCheckBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout2FMT (QComboBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout2_3PD (QCheckBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout2_SRCMUX (QCheckBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout3FMT (QComboBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout3_SRCMUX (QCheckBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout4FMT (QComboBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout4_5PD (QCheckBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout4_SRCMUX (QCheckBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout5FMT (QComboBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout5_SRCMUX (QCheckBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout6FMT (QComboBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout6_7PD (QCheckBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout6_SRCMUX (QCheckBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout7FMT (QComboBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout7_SRCMUX (QCheckBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout8FMT (QComboBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout8_9PD (QCheckBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout8_SRCMUX (QCheckBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout9FMT (QComboBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout9_SRCMUX (QCheckBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1BYPASS (QCheckBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1DDLY (QSpinBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1DDLYPD (QCheckBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1DIV (QSpinBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1HSTrig (QCheckBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1PD (QCheckBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1POL (QCheckBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11BYPASS (QCheckBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11DDLY (QSpinBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11DDLYPD (QCheckBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11DIV (QSpinBox)
2025-06-03 17:15:02,244 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11HSTrig (QCheckBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11PD (QCheckBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11POL (QCheckBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13BYPASS (QCheckBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13DDLY (QSpinBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13DDLYPD (QCheckBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13DIV (QSpinBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13HSTrig (QCheckBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13PD (QCheckBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13POL (QCheckBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3BYPASS (QCheckBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3DDLY (QSpinBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3DDLYPD (QCheckBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3DIV (QSpinBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3HSTrig (QCheckBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3PD (QCheckBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3POL (QCheckBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5BYPASS (QCheckBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5DDLY (QSpinBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5DDLYPD (QCheckBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5DIV (QSpinBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5HSTrig (QCheckBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5PD (QCheckBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5POL (QCheckBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7BYPASS (QCheckBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7DDLY (QSpinBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7DDLYPD (QCheckBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7DIV (QSpinBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7HSTrig (QCheckBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7PD (QCheckBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7POL (QCheckBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9BYPASS (QCheckBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9DDLY (QSpinBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9DDLYPD (QCheckBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9DIV (QSpinBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9HSTrig (QCheckBox)
2025-06-03 17:15:02,245 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9PD (QCheckBox)
2025-06-03 17:15:02,246 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9POL (QCheckBox)
2025-06-03 17:15:02,246 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd0EN (QCheckBox)
2025-06-03 17:15:02,246 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd10EN (QCheckBox)
2025-06-03 17:15:02,246 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd12EN (QCheckBox)
2025-06-03 17:15:02,246 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd2EN (QCheckBox)
2025-06-03 17:15:02,246 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd4EN (QCheckBox)
2025-06-03 17:15:02,246 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd6EN (QCheckBox)
2025-06-03 17:15:02,246 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd8EN (QCheckBox)
2025-06-03 17:15:02,246 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_1 (QSpinBox)
2025-06-03 17:15:02,246 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_2 (QSpinBox)
2025-06-03 17:15:02,246 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_3 (QSpinBox)
2025-06-03 17:15:02,246 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_4 (QSpinBox)
2025-06-03 17:15:02,246 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_5 (QSpinBox)
2025-06-03 17:15:02,246 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_6 (QSpinBox)
2025-06-03 17:15:02,246 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_7 (QSpinBox)
2025-06-03 17:15:02,246 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK0_1HSEN (QCheckBox)
2025-06-03 17:15:02,246 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK10_11HSEN (QCheckBox)
2025-06-03 17:15:02,246 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK12_13HSEN (QCheckBox)
2025-06-03 17:15:02,246 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK2_3HSEN (QCheckBox)
2025-06-03 17:15:02,246 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK4_5HSEN (QCheckBox)
2025-06-03 17:15:02,246 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK6_7HSEN (QCheckBox)
2025-06-03 17:15:02,246 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK8_9HSEN (QCheckBox)
2025-06-03 17:15:02,246 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1ADLY (QSpinBox)
2025-06-03 17:15:02,246 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1ADLYEnb (QCheckBox)
2025-06-03 17:15:02,246 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1DDLY (QComboBox)
2025-06-03 17:15:02,246 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1HSTrig (QCheckBox)
2025-06-03 17:15:02,246 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1PD (QCheckBox)
2025-06-03 17:15:02,246 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1POL (QCheckBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11ADLY (QSpinBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11ADLYEnb (QCheckBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11DDLY (QComboBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11HSTrig (QCheckBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11PD (QCheckBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11POL (QCheckBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13ADLY (QSpinBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13ADLYEnb (QCheckBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13DDLY (QComboBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13HSTrig (QCheckBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13PD (QCheckBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13POL (QCheckBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3ADLY (QSpinBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3ADLYEnb (QCheckBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3DDLY (QComboBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3HSTrig (QCheckBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3PD (QCheckBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3POL (QCheckBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5ADLY (QSpinBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5ADLYEnb (QCheckBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5DDLY (QComboBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5HSTrig (QCheckBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5PD (QCheckBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5POL (QCheckBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7ADLY (QSpinBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7ADLYEnb (QCheckBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7DDLY (QComboBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7HSTrig (QCheckBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7PD (QCheckBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7POL (QCheckBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9ADLY (QSpinBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9ADLYEnb (QCheckBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9DDLY (QComboBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9HSTrig (QCheckBox)
2025-06-03 17:15:02,247 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9PD (QCheckBox)
2025-06-03 17:15:02,248 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9POL (QCheckBox)
2025-06-03 17:15:02,248 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS0 (QCheckBox)
2025-06-03 17:15:02,248 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS10 (QCheckBox)
2025-06-03 17:15:02,248 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS12 (QCheckBox)
2025-06-03 17:15:02,248 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS2 (QCheckBox)
2025-06-03 17:15:02,248 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS4 (QCheckBox)
2025-06-03 17:15:02,248 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS6 (QCheckBox)
2025-06-03 17:15:02,248 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS8 (QCheckBox)
2025-06-03 17:15:02,248 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SysrefCLR (QCheckBox)
2025-06-03 17:15:02,248 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SysrefGBLPD (QCheckBox)
2025-06-03 17:15:02,248 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: label (QLabel)
2025-06-03 17:15:02,248 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: labelFvco (QLabel)
2025-06-03 17:15:02,248 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout0Output (QLineEdit)
2025-06-03 17:15:02,248 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout10Output (QLineEdit)
2025-06-03 17:15:02,248 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout11Output (QLineEdit)
2025-06-03 17:15:02,248 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout12Output (QLineEdit)
2025-06-03 17:15:02,248 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout13Output (QLineEdit)
2025-06-03 17:15:02,248 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout1Output (QLineEdit)
2025-06-03 17:15:02,248 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout2Output (QLineEdit)
2025-06-03 17:15:02,248 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout3Output (QLineEdit)
2025-06-03 17:15:02,248 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout4Output (QLineEdit)
2025-06-03 17:15:02,248 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout5Output (QLineEdit)
2025-06-03 17:15:02,248 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout6Output (QLineEdit)
2025-06-03 17:15:02,248 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout7Output (QLineEdit)
2025-06-03 17:15:02,249 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout8Output (QLineEdit)
2025-06-03 17:15:02,249 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout9Output (QLineEdit)
2025-06-03 17:15:02,249 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFvco (QLineEdit)
2025-06-03 17:15:02,249 - ModernBaseHandler - [ModernBaseHandler.py:119] - INFO - UI中发现 173 个控件
2025-06-03 17:15:02,249 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:452] - ERROR - 后初始化时出错: 'MockRegisterManager' object has no attribute 'get_widget_register_mapping'
2025-06-03 17:15:02,250 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:56] - INFO - 现代化时钟输出处理器初始化完成
2025-06-03 17:15:02,257 - ModernBaseHandler - [ModernBaseHandler.py:71] - WARNING - 连接RegisterUpdateBus时出错: wrapped C/C++ object of type RegisterUpdateBus has been deleted，继续初始化
2025-06-03 17:15:02,557 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout0FMT 格式选项
2025-06-03 17:15:02,558 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout1FMT 格式选项
2025-06-03 17:15:02,558 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout2FMT 格式选项
2025-06-03 17:15:02,558 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout3FMT 格式选项
2025-06-03 17:15:02,558 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout4FMT 格式选项
2025-06-03 17:15:02,558 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout5FMT 格式选项
2025-06-03 17:15:02,558 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout6FMT 格式选项
2025-06-03 17:15:02,558 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout7FMT 格式选项
2025-06-03 17:15:02,558 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout8FMT 格式选项
2025-06-03 17:15:02,558 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout9FMT 格式选项
2025-06-03 17:15:02,559 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout10FMT 格式选项
2025-06-03 17:15:02,559 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout11FMT 格式选项
2025-06-03 17:15:02,559 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout12FMT 格式选项
2025-06-03 17:15:02,559 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout13FMT 格式选项
2025-06-03 17:15:02,559 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:172] - INFO - 所有格式下拉框初始化完成
2025-06-03 17:15:02,559 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK0_1DIV: 范围(1-1023), 默认值(2)
2025-06-03 17:15:02,559 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK2_3DIV: 范围(1-1023), 默认值(4)
2025-06-03 17:15:02,559 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK4_5DIV: 范围(1-1023), 默认值(8)
2025-06-03 17:15:02,559 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK6_7DIV: 范围(1-1023), 默认值(8)
2025-06-03 17:15:02,559 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK8_9DIV: 范围(1-1023), 默认值(8)
2025-06-03 17:15:02,559 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK10_11DIV: 范围(1-1023), 默认值(8)
2025-06-03 17:15:02,559 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK12_13DIV: 范围(1-1023), 默认值(2)
2025-06-03 17:15:02,559 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK0_1DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:02,559 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK2_3DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:02,560 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK4_5DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:02,560 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK6_7DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:02,560 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK8_9DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:02,560 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK10_11DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:02,560 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK12_13DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:02,560 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:227] - DEBUG - 已初始化DDLYdStepCNT_1: 范围(0-255), 默认值(0)
2025-06-03 17:15:02,560 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:229] - INFO - 分频器控件初始化完成
2025-06-03 17:15:02,560 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK0_1PD为启用状态
2025-06-03 17:15:02,560 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK2_3PD为启用状态
2025-06-03 17:15:02,560 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK4_5PD为启用状态
2025-06-03 17:15:02,560 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK6_7PD为启用状态
2025-06-03 17:15:02,560 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK8_9PD为启用状态
2025-06-03 17:15:02,560 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK10_11PD为启用状态
2025-06-03 17:15:02,560 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK12_13PD为启用状态
2025-06-03 17:15:02,560 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK0_1DDLYPD为启用状态
2025-06-03 17:15:02,560 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK2_3DDLYPD为启用状态
2025-06-03 17:15:02,560 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK4_5DDLYPD为启用状态
2025-06-03 17:15:02,560 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK6_7DDLYPD为启用状态
2025-06-03 17:15:02,560 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK8_9DDLYPD为启用状态
2025-06-03 17:15:02,561 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK10_11DDLYPD为启用状态
2025-06-03 17:15:02,561 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK12_13DDLYPD为启用状态
2025-06-03 17:15:02,561 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd0EN为禁用状态
2025-06-03 17:15:02,561 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd2EN为禁用状态
2025-06-03 17:15:02,561 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd4EN为禁用状态
2025-06-03 17:15:02,561 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd6EN为禁用状态
2025-06-03 17:15:02,561 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd8EN为禁用状态
2025-06-03 17:15:02,561 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd10EN为禁用状态
2025-06-03 17:15:02,561 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd12EN为禁用状态
2025-06-03 17:15:02,561 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK0_1PD为关闭状态
2025-06-03 17:15:02,561 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK2_3PD为关闭状态
2025-06-03 17:15:02,561 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK4_5PD为关闭状态
2025-06-03 17:15:02,561 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK6_7PD为关闭状态
2025-06-03 17:15:02,561 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK8_9PD为关闭状态
2025-06-03 17:15:02,561 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK10_11PD为关闭状态
2025-06-03 17:15:02,561 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK12_13PD为关闭状态
2025-06-03 17:15:02,561 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK0_1HSTrig为无相位调整
2025-06-03 17:15:02,561 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK2_3HSTrig为无相位调整
2025-06-03 17:15:02,561 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK4_5HSTrig为无相位调整
2025-06-03 17:15:02,561 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK6_7HSTrig为无相位调整
2025-06-03 17:15:02,561 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK8_9HSTrig为无相位调整
2025-06-03 17:15:02,561 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK10_11HSTrig为无相位调整
2025-06-03 17:15:02,561 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK12_13HSTrig为无相位调整
2025-06-03 17:15:02,561 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS0为不禁用同步
2025-06-03 17:15:02,561 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS2为不禁用同步
2025-06-03 17:15:02,561 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS4为不禁用同步
2025-06-03 17:15:02,561 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS6为不禁用同步
2025-06-03 17:15:02,561 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS8为不禁用同步
2025-06-03 17:15:02,561 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS10为不禁用同步
2025-06-03 17:15:02,561 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS12为不禁用同步
2025-06-03 17:15:02,562 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:309] - INFO - 电源管理和控制状态初始化完成
2025-06-03 17:15:02,562 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK0_1DDLY: 默认0.5ns延迟
2025-06-03 17:15:02,562 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK2_3DDLY: 默认0.5ns延迟
2025-06-03 17:15:02,562 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK4_5DDLY: 默认0.5ns延迟
2025-06-03 17:15:02,562 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK6_7DDLY: 默认0.5ns延迟
2025-06-03 17:15:02,562 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK8_9DDLY: 默认0.5ns延迟
2025-06-03 17:15:02,562 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK10_11DDLY: 默认0.5ns延迟
2025-06-03 17:15:02,562 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK12_13DDLY: 默认0.5ns延迟
2025-06-03 17:15:02,562 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:335] - INFO - SYSREF延迟控件初始化完成
2025-06-03 17:15:02,562 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK2_3DIV 当前值为 4，保持不变
2025-06-03 17:15:02,562 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK4_5DIV 当前值为 8，保持不变
2025-06-03 17:15:02,562 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK6_7DIV 当前值为 8，保持不变
2025-06-03 17:15:02,562 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK8_9DIV 当前值为 8，保持不变
2025-06-03 17:15:02,562 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK10_11DIV 当前值为 8，保持不变
2025-06-03 17:15:02,562 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:376] - DEBUG - 分频器值初始化检查完成
2025-06-03 17:15:02,562 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:404] - DEBUG - 分频器值同步完成
2025-06-03 17:15:02,562 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:351] - INFO - 输出频率初始化完成（延迟计算）
2025-06-03 17:15:02,562 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:416] - INFO - 已连接 lineEditFvco 信号
2025-06-03 17:15:02,563 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout0_SRCMUX 信号
2025-06-03 17:15:02,563 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout1_SRCMUX 信号
2025-06-03 17:15:02,563 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout2_SRCMUX 信号
2025-06-03 17:15:02,563 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout3_SRCMUX 信号
2025-06-03 17:15:02,563 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout4_SRCMUX 信号
2025-06-03 17:15:02,563 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout5_SRCMUX 信号
2025-06-03 17:15:02,563 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout6_SRCMUX 信号
2025-06-03 17:15:02,563 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout7_SRCMUX 信号
2025-06-03 17:15:02,563 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout8_SRCMUX 信号
2025-06-03 17:15:02,563 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout9_SRCMUX 信号
2025-06-03 17:15:02,563 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout10_SRCMUX 信号
2025-06-03 17:15:02,563 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout11_SRCMUX 信号
2025-06-03 17:15:02,563 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout12_SRCMUX 信号
2025-06-03 17:15:02,563 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout13_SRCMUX 信号
2025-06-03 17:15:02,563 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:437] - INFO - 已连接所有时钟输出的SRCMUX复选框信号
2025-06-03 17:15:02,563 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:119] - INFO - 时钟输出特定配置初始化完成
2025-06-03 17:15:02,564 - ModernBaseHandler - [ModernBaseHandler.py:86] - INFO - ModernBaseHandler._post_init 被调用
2025-06-03 17:15:02,564 - ModernBaseHandler - [ModernBaseHandler.py:88] - INFO - UI已设置，开始构建控件映射
2025-06-03 17:15:02,564 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout0FMT (QComboBox)
2025-06-03 17:15:02,564 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout0_1PD (QCheckBox)
2025-06-03 17:15:02,564 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout0_SRCMUX (QCheckBox)
2025-06-03 17:15:02,564 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout10FMT (QComboBox)
2025-06-03 17:15:02,564 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout10_11PD (QCheckBox)
2025-06-03 17:15:02,564 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout10_SRCMUX (QCheckBox)
2025-06-03 17:15:02,564 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout11FMT (QComboBox)
2025-06-03 17:15:02,565 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout11_SRCMUX (QCheckBox)
2025-06-03 17:15:02,565 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout12FMT (QComboBox)
2025-06-03 17:15:02,565 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout12_13PD (QCheckBox)
2025-06-03 17:15:02,565 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout12_SRCMUX (QCheckBox)
2025-06-03 17:15:02,565 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout13FMT (QComboBox)
2025-06-03 17:15:02,565 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout13_SRCMUX (QCheckBox)
2025-06-03 17:15:02,565 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout1FMT (QComboBox)
2025-06-03 17:15:02,565 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout1_SRCMUX (QCheckBox)
2025-06-03 17:15:02,565 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout2FMT (QComboBox)
2025-06-03 17:15:02,565 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout2_3PD (QCheckBox)
2025-06-03 17:15:02,565 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout2_SRCMUX (QCheckBox)
2025-06-03 17:15:02,565 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout3FMT (QComboBox)
2025-06-03 17:15:02,565 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout3_SRCMUX (QCheckBox)
2025-06-03 17:15:02,566 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout4FMT (QComboBox)
2025-06-03 17:15:02,566 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout4_5PD (QCheckBox)
2025-06-03 17:15:02,566 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout4_SRCMUX (QCheckBox)
2025-06-03 17:15:02,566 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout5FMT (QComboBox)
2025-06-03 17:15:02,566 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout5_SRCMUX (QCheckBox)
2025-06-03 17:15:02,566 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout6FMT (QComboBox)
2025-06-03 17:15:02,566 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout6_7PD (QCheckBox)
2025-06-03 17:15:02,566 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout6_SRCMUX (QCheckBox)
2025-06-03 17:15:02,566 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout7FMT (QComboBox)
2025-06-03 17:15:02,566 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout7_SRCMUX (QCheckBox)
2025-06-03 17:15:02,566 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout8FMT (QComboBox)
2025-06-03 17:15:02,566 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout8_9PD (QCheckBox)
2025-06-03 17:15:02,566 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout8_SRCMUX (QCheckBox)
2025-06-03 17:15:02,566 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout9FMT (QComboBox)
2025-06-03 17:15:02,566 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout9_SRCMUX (QCheckBox)
2025-06-03 17:15:02,566 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1BYPASS (QCheckBox)
2025-06-03 17:15:02,566 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1DDLY (QSpinBox)
2025-06-03 17:15:02,566 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1DDLYPD (QCheckBox)
2025-06-03 17:15:02,566 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1DIV (QSpinBox)
2025-06-03 17:15:02,566 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1HSTrig (QCheckBox)
2025-06-03 17:15:02,566 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1PD (QCheckBox)
2025-06-03 17:15:02,566 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1POL (QCheckBox)
2025-06-03 17:15:02,566 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11BYPASS (QCheckBox)
2025-06-03 17:15:02,566 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11DDLY (QSpinBox)
2025-06-03 17:15:02,566 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11DDLYPD (QCheckBox)
2025-06-03 17:15:02,566 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11DIV (QSpinBox)
2025-06-03 17:15:02,566 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11HSTrig (QCheckBox)
2025-06-03 17:15:02,566 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11PD (QCheckBox)
2025-06-03 17:15:02,567 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11POL (QCheckBox)
2025-06-03 17:15:02,567 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13BYPASS (QCheckBox)
2025-06-03 17:15:02,567 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13DDLY (QSpinBox)
2025-06-03 17:15:02,567 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13DDLYPD (QCheckBox)
2025-06-03 17:15:02,567 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13DIV (QSpinBox)
2025-06-03 17:15:02,567 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13HSTrig (QCheckBox)
2025-06-03 17:15:02,567 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13PD (QCheckBox)
2025-06-03 17:15:02,567 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13POL (QCheckBox)
2025-06-03 17:15:02,567 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3BYPASS (QCheckBox)
2025-06-03 17:15:02,567 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3DDLY (QSpinBox)
2025-06-03 17:15:02,567 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3DDLYPD (QCheckBox)
2025-06-03 17:15:02,567 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3DIV (QSpinBox)
2025-06-03 17:15:02,567 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3HSTrig (QCheckBox)
2025-06-03 17:15:02,567 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3PD (QCheckBox)
2025-06-03 17:15:02,567 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3POL (QCheckBox)
2025-06-03 17:15:02,567 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5BYPASS (QCheckBox)
2025-06-03 17:15:02,567 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5DDLY (QSpinBox)
2025-06-03 17:15:02,567 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5DDLYPD (QCheckBox)
2025-06-03 17:15:02,567 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5DIV (QSpinBox)
2025-06-03 17:15:02,567 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5HSTrig (QCheckBox)
2025-06-03 17:15:02,567 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5PD (QCheckBox)
2025-06-03 17:15:02,567 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5POL (QCheckBox)
2025-06-03 17:15:02,567 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7BYPASS (QCheckBox)
2025-06-03 17:15:02,567 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7DDLY (QSpinBox)
2025-06-03 17:15:02,567 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7DDLYPD (QCheckBox)
2025-06-03 17:15:02,567 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7DIV (QSpinBox)
2025-06-03 17:15:02,567 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7HSTrig (QCheckBox)
2025-06-03 17:15:02,567 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7PD (QCheckBox)
2025-06-03 17:15:02,567 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7POL (QCheckBox)
2025-06-03 17:15:02,567 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9BYPASS (QCheckBox)
2025-06-03 17:15:02,568 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9DDLY (QSpinBox)
2025-06-03 17:15:02,568 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9DDLYPD (QCheckBox)
2025-06-03 17:15:02,568 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9DIV (QSpinBox)
2025-06-03 17:15:02,568 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9HSTrig (QCheckBox)
2025-06-03 17:15:02,568 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9PD (QCheckBox)
2025-06-03 17:15:02,568 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9POL (QCheckBox)
2025-06-03 17:15:02,568 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd0EN (QCheckBox)
2025-06-03 17:15:02,568 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd10EN (QCheckBox)
2025-06-03 17:15:02,568 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd12EN (QCheckBox)
2025-06-03 17:15:02,568 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd2EN (QCheckBox)
2025-06-03 17:15:02,568 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd4EN (QCheckBox)
2025-06-03 17:15:02,568 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd6EN (QCheckBox)
2025-06-03 17:15:02,568 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd8EN (QCheckBox)
2025-06-03 17:15:02,568 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_1 (QSpinBox)
2025-06-03 17:15:02,568 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_2 (QSpinBox)
2025-06-03 17:15:02,568 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_3 (QSpinBox)
2025-06-03 17:15:02,568 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_4 (QSpinBox)
2025-06-03 17:15:02,568 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_5 (QSpinBox)
2025-06-03 17:15:02,568 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_6 (QSpinBox)
2025-06-03 17:15:02,568 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_7 (QSpinBox)
2025-06-03 17:15:02,568 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK0_1HSEN (QCheckBox)
2025-06-03 17:15:02,568 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK10_11HSEN (QCheckBox)
2025-06-03 17:15:02,568 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK12_13HSEN (QCheckBox)
2025-06-03 17:15:02,568 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK2_3HSEN (QCheckBox)
2025-06-03 17:15:02,568 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK4_5HSEN (QCheckBox)
2025-06-03 17:15:02,568 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK6_7HSEN (QCheckBox)
2025-06-03 17:15:02,568 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK8_9HSEN (QCheckBox)
2025-06-03 17:15:02,568 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1ADLY (QSpinBox)
2025-06-03 17:15:02,568 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1ADLYEnb (QCheckBox)
2025-06-03 17:15:02,568 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1DDLY (QComboBox)
2025-06-03 17:15:02,569 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1HSTrig (QCheckBox)
2025-06-03 17:15:02,569 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1PD (QCheckBox)
2025-06-03 17:15:02,569 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1POL (QCheckBox)
2025-06-03 17:15:02,569 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11ADLY (QSpinBox)
2025-06-03 17:15:02,569 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11ADLYEnb (QCheckBox)
2025-06-03 17:15:02,569 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11DDLY (QComboBox)
2025-06-03 17:15:02,569 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11HSTrig (QCheckBox)
2025-06-03 17:15:02,569 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11PD (QCheckBox)
2025-06-03 17:15:02,569 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11POL (QCheckBox)
2025-06-03 17:15:02,569 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13ADLY (QSpinBox)
2025-06-03 17:15:02,569 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13ADLYEnb (QCheckBox)
2025-06-03 17:15:02,569 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13DDLY (QComboBox)
2025-06-03 17:15:02,569 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13HSTrig (QCheckBox)
2025-06-03 17:15:02,569 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13PD (QCheckBox)
2025-06-03 17:15:02,569 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13POL (QCheckBox)
2025-06-03 17:15:02,569 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3ADLY (QSpinBox)
2025-06-03 17:15:02,569 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3ADLYEnb (QCheckBox)
2025-06-03 17:15:02,569 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3DDLY (QComboBox)
2025-06-03 17:15:02,569 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3HSTrig (QCheckBox)
2025-06-03 17:15:02,569 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3PD (QCheckBox)
2025-06-03 17:15:02,569 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3POL (QCheckBox)
2025-06-03 17:15:02,569 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5ADLY (QSpinBox)
2025-06-03 17:15:02,569 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5ADLYEnb (QCheckBox)
2025-06-03 17:15:02,569 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5DDLY (QComboBox)
2025-06-03 17:15:02,569 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5HSTrig (QCheckBox)
2025-06-03 17:15:02,569 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5PD (QCheckBox)
2025-06-03 17:15:02,569 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5POL (QCheckBox)
2025-06-03 17:15:02,569 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7ADLY (QSpinBox)
2025-06-03 17:15:02,569 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7ADLYEnb (QCheckBox)
2025-06-03 17:15:02,570 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7DDLY (QComboBox)
2025-06-03 17:15:02,570 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7HSTrig (QCheckBox)
2025-06-03 17:15:02,570 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7PD (QCheckBox)
2025-06-03 17:15:02,570 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7POL (QCheckBox)
2025-06-03 17:15:02,570 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9ADLY (QSpinBox)
2025-06-03 17:15:02,570 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9ADLYEnb (QCheckBox)
2025-06-03 17:15:02,570 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9DDLY (QComboBox)
2025-06-03 17:15:02,570 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9HSTrig (QCheckBox)
2025-06-03 17:15:02,570 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9PD (QCheckBox)
2025-06-03 17:15:02,570 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9POL (QCheckBox)
2025-06-03 17:15:02,570 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS0 (QCheckBox)
2025-06-03 17:15:02,570 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS10 (QCheckBox)
2025-06-03 17:15:02,570 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS12 (QCheckBox)
2025-06-03 17:15:02,570 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS2 (QCheckBox)
2025-06-03 17:15:02,570 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS4 (QCheckBox)
2025-06-03 17:15:02,570 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS6 (QCheckBox)
2025-06-03 17:15:02,570 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS8 (QCheckBox)
2025-06-03 17:15:02,570 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SysrefCLR (QCheckBox)
2025-06-03 17:15:02,570 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SysrefGBLPD (QCheckBox)
2025-06-03 17:15:02,570 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: label (QLabel)
2025-06-03 17:15:02,570 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: labelFvco (QLabel)
2025-06-03 17:15:02,570 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout0Output (QLineEdit)
2025-06-03 17:15:02,570 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout10Output (QLineEdit)
2025-06-03 17:15:02,570 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout11Output (QLineEdit)
2025-06-03 17:15:02,570 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout12Output (QLineEdit)
2025-06-03 17:15:02,570 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout13Output (QLineEdit)
2025-06-03 17:15:02,570 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout1Output (QLineEdit)
2025-06-03 17:15:02,570 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout2Output (QLineEdit)
2025-06-03 17:15:02,570 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout3Output (QLineEdit)
2025-06-03 17:15:02,570 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout4Output (QLineEdit)
2025-06-03 17:15:02,570 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout5Output (QLineEdit)
2025-06-03 17:15:02,571 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout6Output (QLineEdit)
2025-06-03 17:15:02,571 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout7Output (QLineEdit)
2025-06-03 17:15:02,571 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout8Output (QLineEdit)
2025-06-03 17:15:02,571 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout9Output (QLineEdit)
2025-06-03 17:15:02,571 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFvco (QLineEdit)
2025-06-03 17:15:02,571 - ModernBaseHandler - [ModernBaseHandler.py:119] - INFO - UI中发现 173 个控件
2025-06-03 17:15:02,571 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:452] - ERROR - 后初始化时出错: 'MockRegisterManager' object has no attribute 'get_widget_register_mapping'
2025-06-03 17:15:02,571 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:56] - INFO - 现代化时钟输出处理器初始化完成
2025-06-03 17:15:02,572 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出0分频值: DCLK0_1DIV = 2
2025-06-03 17:15:02,572 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出2分频值: DCLK2_3DIV = 4
2025-06-03 17:15:02,572 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出4分频值: DCLK4_5DIV = 8
2025-06-03 17:15:02,572 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出6分频值: DCLK6_7DIV = 8
2025-06-03 17:15:02,572 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出8分频值: DCLK8_9DIV = 8
2025-06-03 17:15:02,572 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出10分频值: DCLK10_11DIV = 8
2025-06-03 17:15:02,572 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出12分频值: DCLK12_13DIV = 2
2025-06-03 17:15:02,579 - ModernBaseHandler - [ModernBaseHandler.py:71] - WARNING - 连接RegisterUpdateBus时出错: wrapped C/C++ object of type RegisterUpdateBus has been deleted，继续初始化
2025-06-03 17:15:03,023 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout0FMT 格式选项
2025-06-03 17:15:03,023 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout1FMT 格式选项
2025-06-03 17:15:03,023 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout2FMT 格式选项
2025-06-03 17:15:03,023 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout3FMT 格式选项
2025-06-03 17:15:03,024 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout4FMT 格式选项
2025-06-03 17:15:03,024 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout5FMT 格式选项
2025-06-03 17:15:03,024 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout6FMT 格式选项
2025-06-03 17:15:03,024 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout7FMT 格式选项
2025-06-03 17:15:03,024 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout8FMT 格式选项
2025-06-03 17:15:03,024 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout9FMT 格式选项
2025-06-03 17:15:03,024 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout10FMT 格式选项
2025-06-03 17:15:03,024 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout11FMT 格式选项
2025-06-03 17:15:03,024 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout12FMT 格式选项
2025-06-03 17:15:03,024 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout13FMT 格式选项
2025-06-03 17:15:03,024 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:172] - INFO - 所有格式下拉框初始化完成
2025-06-03 17:15:03,025 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK0_1DIV: 范围(1-1023), 默认值(2)
2025-06-03 17:15:03,025 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK2_3DIV: 范围(1-1023), 默认值(4)
2025-06-03 17:15:03,025 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK4_5DIV: 范围(1-1023), 默认值(8)
2025-06-03 17:15:03,026 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK6_7DIV: 范围(1-1023), 默认值(8)
2025-06-03 17:15:03,026 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK8_9DIV: 范围(1-1023), 默认值(8)
2025-06-03 17:15:03,026 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK10_11DIV: 范围(1-1023), 默认值(8)
2025-06-03 17:15:03,026 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK12_13DIV: 范围(1-1023), 默认值(2)
2025-06-03 17:15:03,026 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK0_1DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:03,026 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK2_3DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:03,026 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK4_5DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:03,026 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK6_7DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:03,026 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK8_9DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:03,026 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK10_11DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:03,026 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK12_13DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:03,027 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:227] - DEBUG - 已初始化DDLYdStepCNT_1: 范围(0-255), 默认值(0)
2025-06-03 17:15:03,027 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:229] - INFO - 分频器控件初始化完成
2025-06-03 17:15:03,027 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK0_1PD为启用状态
2025-06-03 17:15:03,027 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK2_3PD为启用状态
2025-06-03 17:15:03,028 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK4_5PD为启用状态
2025-06-03 17:15:03,028 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK6_7PD为启用状态
2025-06-03 17:15:03,028 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK8_9PD为启用状态
2025-06-03 17:15:03,028 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK10_11PD为启用状态
2025-06-03 17:15:03,028 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK12_13PD为启用状态
2025-06-03 17:15:03,028 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK0_1DDLYPD为启用状态
2025-06-03 17:15:03,028 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK2_3DDLYPD为启用状态
2025-06-03 17:15:03,028 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK4_5DDLYPD为启用状态
2025-06-03 17:15:03,028 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK6_7DDLYPD为启用状态
2025-06-03 17:15:03,028 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK8_9DDLYPD为启用状态
2025-06-03 17:15:03,028 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK10_11DDLYPD为启用状态
2025-06-03 17:15:03,028 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK12_13DDLYPD为启用状态
2025-06-03 17:15:03,028 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd0EN为禁用状态
2025-06-03 17:15:03,028 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd2EN为禁用状态
2025-06-03 17:15:03,028 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd4EN为禁用状态
2025-06-03 17:15:03,028 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd6EN为禁用状态
2025-06-03 17:15:03,028 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd8EN为禁用状态
2025-06-03 17:15:03,028 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd10EN为禁用状态
2025-06-03 17:15:03,028 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd12EN为禁用状态
2025-06-03 17:15:03,028 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK0_1PD为关闭状态
2025-06-03 17:15:03,029 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK2_3PD为关闭状态
2025-06-03 17:15:03,029 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK4_5PD为关闭状态
2025-06-03 17:15:03,029 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK6_7PD为关闭状态
2025-06-03 17:15:03,029 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK8_9PD为关闭状态
2025-06-03 17:15:03,029 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK10_11PD为关闭状态
2025-06-03 17:15:03,029 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK12_13PD为关闭状态
2025-06-03 17:15:03,029 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK0_1HSTrig为无相位调整
2025-06-03 17:15:03,029 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK2_3HSTrig为无相位调整
2025-06-03 17:15:03,029 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK4_5HSTrig为无相位调整
2025-06-03 17:15:03,029 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK6_7HSTrig为无相位调整
2025-06-03 17:15:03,029 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK8_9HSTrig为无相位调整
2025-06-03 17:15:03,029 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK10_11HSTrig为无相位调整
2025-06-03 17:15:03,029 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK12_13HSTrig为无相位调整
2025-06-03 17:15:03,029 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS0为不禁用同步
2025-06-03 17:15:03,029 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS2为不禁用同步
2025-06-03 17:15:03,029 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS4为不禁用同步
2025-06-03 17:15:03,029 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS6为不禁用同步
2025-06-03 17:15:03,029 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS8为不禁用同步
2025-06-03 17:15:03,029 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS10为不禁用同步
2025-06-03 17:15:03,029 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS12为不禁用同步
2025-06-03 17:15:03,029 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:309] - INFO - 电源管理和控制状态初始化完成
2025-06-03 17:15:03,030 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK0_1DDLY: 默认0.5ns延迟
2025-06-03 17:15:03,030 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK2_3DDLY: 默认0.5ns延迟
2025-06-03 17:15:03,030 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK4_5DDLY: 默认0.5ns延迟
2025-06-03 17:15:03,030 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK6_7DDLY: 默认0.5ns延迟
2025-06-03 17:15:03,030 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK8_9DDLY: 默认0.5ns延迟
2025-06-03 17:15:03,030 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK10_11DDLY: 默认0.5ns延迟
2025-06-03 17:15:03,030 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK12_13DDLY: 默认0.5ns延迟
2025-06-03 17:15:03,031 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:335] - INFO - SYSREF延迟控件初始化完成
2025-06-03 17:15:03,031 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK2_3DIV 当前值为 4，保持不变
2025-06-03 17:15:03,031 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK4_5DIV 当前值为 8，保持不变
2025-06-03 17:15:03,031 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK6_7DIV 当前值为 8，保持不变
2025-06-03 17:15:03,031 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK8_9DIV 当前值为 8，保持不变
2025-06-03 17:15:03,031 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK10_11DIV 当前值为 8，保持不变
2025-06-03 17:15:03,031 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:376] - DEBUG - 分频器值初始化检查完成
2025-06-03 17:15:03,031 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:404] - DEBUG - 分频器值同步完成
2025-06-03 17:15:03,031 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:351] - INFO - 输出频率初始化完成（延迟计算）
2025-06-03 17:15:03,031 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:416] - INFO - 已连接 lineEditFvco 信号
2025-06-03 17:15:03,032 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout0_SRCMUX 信号
2025-06-03 17:15:03,032 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout1_SRCMUX 信号
2025-06-03 17:15:03,032 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout2_SRCMUX 信号
2025-06-03 17:15:03,032 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout3_SRCMUX 信号
2025-06-03 17:15:03,032 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout4_SRCMUX 信号
2025-06-03 17:15:03,032 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout5_SRCMUX 信号
2025-06-03 17:15:03,032 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout6_SRCMUX 信号
2025-06-03 17:15:03,032 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout7_SRCMUX 信号
2025-06-03 17:15:03,032 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout8_SRCMUX 信号
2025-06-03 17:15:03,033 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout9_SRCMUX 信号
2025-06-03 17:15:03,033 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout10_SRCMUX 信号
2025-06-03 17:15:03,033 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout11_SRCMUX 信号
2025-06-03 17:15:03,033 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout12_SRCMUX 信号
2025-06-03 17:15:03,033 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout13_SRCMUX 信号
2025-06-03 17:15:03,033 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:437] - INFO - 已连接所有时钟输出的SRCMUX复选框信号
2025-06-03 17:15:03,033 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:119] - INFO - 时钟输出特定配置初始化完成
2025-06-03 17:15:03,033 - ModernBaseHandler - [ModernBaseHandler.py:86] - INFO - ModernBaseHandler._post_init 被调用
2025-06-03 17:15:03,033 - ModernBaseHandler - [ModernBaseHandler.py:88] - INFO - UI已设置，开始构建控件映射
2025-06-03 17:15:03,033 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout0FMT (QComboBox)
2025-06-03 17:15:03,034 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout0_1PD (QCheckBox)
2025-06-03 17:15:03,034 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout0_SRCMUX (QCheckBox)
2025-06-03 17:15:03,034 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout10FMT (QComboBox)
2025-06-03 17:15:03,034 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout10_11PD (QCheckBox)
2025-06-03 17:15:03,034 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout10_SRCMUX (QCheckBox)
2025-06-03 17:15:03,034 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout11FMT (QComboBox)
2025-06-03 17:15:03,034 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout11_SRCMUX (QCheckBox)
2025-06-03 17:15:03,034 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout12FMT (QComboBox)
2025-06-03 17:15:03,034 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout12_13PD (QCheckBox)
2025-06-03 17:15:03,034 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout12_SRCMUX (QCheckBox)
2025-06-03 17:15:03,034 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout13FMT (QComboBox)
2025-06-03 17:15:03,034 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout13_SRCMUX (QCheckBox)
2025-06-03 17:15:03,034 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout1FMT (QComboBox)
2025-06-03 17:15:03,034 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout1_SRCMUX (QCheckBox)
2025-06-03 17:15:03,034 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout2FMT (QComboBox)
2025-06-03 17:15:03,034 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout2_3PD (QCheckBox)
2025-06-03 17:15:03,035 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout2_SRCMUX (QCheckBox)
2025-06-03 17:15:03,035 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout3FMT (QComboBox)
2025-06-03 17:15:03,035 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout3_SRCMUX (QCheckBox)
2025-06-03 17:15:03,035 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout4FMT (QComboBox)
2025-06-03 17:15:03,035 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout4_5PD (QCheckBox)
2025-06-03 17:15:03,035 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout4_SRCMUX (QCheckBox)
2025-06-03 17:15:03,035 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout5FMT (QComboBox)
2025-06-03 17:15:03,035 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout5_SRCMUX (QCheckBox)
2025-06-03 17:15:03,035 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout6FMT (QComboBox)
2025-06-03 17:15:03,035 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout6_7PD (QCheckBox)
2025-06-03 17:15:03,035 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout6_SRCMUX (QCheckBox)
2025-06-03 17:15:03,035 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout7FMT (QComboBox)
2025-06-03 17:15:03,035 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout7_SRCMUX (QCheckBox)
2025-06-03 17:15:03,035 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout8FMT (QComboBox)
2025-06-03 17:15:03,035 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout8_9PD (QCheckBox)
2025-06-03 17:15:03,035 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout8_SRCMUX (QCheckBox)
2025-06-03 17:15:03,035 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout9FMT (QComboBox)
2025-06-03 17:15:03,035 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout9_SRCMUX (QCheckBox)
2025-06-03 17:15:03,035 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1BYPASS (QCheckBox)
2025-06-03 17:15:03,035 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1DDLY (QSpinBox)
2025-06-03 17:15:03,035 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1DDLYPD (QCheckBox)
2025-06-03 17:15:03,035 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1DIV (QSpinBox)
2025-06-03 17:15:03,035 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1HSTrig (QCheckBox)
2025-06-03 17:15:03,035 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1PD (QCheckBox)
2025-06-03 17:15:03,035 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1POL (QCheckBox)
2025-06-03 17:15:03,035 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11BYPASS (QCheckBox)
2025-06-03 17:15:03,035 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11DDLY (QSpinBox)
2025-06-03 17:15:03,035 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11DDLYPD (QCheckBox)
2025-06-03 17:15:03,035 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11DIV (QSpinBox)
2025-06-03 17:15:03,035 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11HSTrig (QCheckBox)
2025-06-03 17:15:03,035 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11PD (QCheckBox)
2025-06-03 17:15:03,036 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11POL (QCheckBox)
2025-06-03 17:15:03,036 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13BYPASS (QCheckBox)
2025-06-03 17:15:03,036 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13DDLY (QSpinBox)
2025-06-03 17:15:03,036 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13DDLYPD (QCheckBox)
2025-06-03 17:15:03,036 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13DIV (QSpinBox)
2025-06-03 17:15:03,036 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13HSTrig (QCheckBox)
2025-06-03 17:15:03,036 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13PD (QCheckBox)
2025-06-03 17:15:03,036 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13POL (QCheckBox)
2025-06-03 17:15:03,036 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3BYPASS (QCheckBox)
2025-06-03 17:15:03,036 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3DDLY (QSpinBox)
2025-06-03 17:15:03,036 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3DDLYPD (QCheckBox)
2025-06-03 17:15:03,036 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3DIV (QSpinBox)
2025-06-03 17:15:03,036 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3HSTrig (QCheckBox)
2025-06-03 17:15:03,036 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3PD (QCheckBox)
2025-06-03 17:15:03,036 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3POL (QCheckBox)
2025-06-03 17:15:03,036 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5BYPASS (QCheckBox)
2025-06-03 17:15:03,036 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5DDLY (QSpinBox)
2025-06-03 17:15:03,036 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5DDLYPD (QCheckBox)
2025-06-03 17:15:03,036 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5DIV (QSpinBox)
2025-06-03 17:15:03,036 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5HSTrig (QCheckBox)
2025-06-03 17:15:03,036 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5PD (QCheckBox)
2025-06-03 17:15:03,036 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5POL (QCheckBox)
2025-06-03 17:15:03,036 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7BYPASS (QCheckBox)
2025-06-03 17:15:03,036 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7DDLY (QSpinBox)
2025-06-03 17:15:03,036 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7DDLYPD (QCheckBox)
2025-06-03 17:15:03,037 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7DIV (QSpinBox)
2025-06-03 17:15:03,037 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7HSTrig (QCheckBox)
2025-06-03 17:15:03,037 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7PD (QCheckBox)
2025-06-03 17:15:03,037 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7POL (QCheckBox)
2025-06-03 17:15:03,037 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9BYPASS (QCheckBox)
2025-06-03 17:15:03,037 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9DDLY (QSpinBox)
2025-06-03 17:15:03,037 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9DDLYPD (QCheckBox)
2025-06-03 17:15:03,037 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9DIV (QSpinBox)
2025-06-03 17:15:03,037 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9HSTrig (QCheckBox)
2025-06-03 17:15:03,037 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9PD (QCheckBox)
2025-06-03 17:15:03,037 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9POL (QCheckBox)
2025-06-03 17:15:03,037 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd0EN (QCheckBox)
2025-06-03 17:15:03,037 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd10EN (QCheckBox)
2025-06-03 17:15:03,037 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd12EN (QCheckBox)
2025-06-03 17:15:03,037 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd2EN (QCheckBox)
2025-06-03 17:15:03,037 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd4EN (QCheckBox)
2025-06-03 17:15:03,037 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd6EN (QCheckBox)
2025-06-03 17:15:03,037 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd8EN (QCheckBox)
2025-06-03 17:15:03,037 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_1 (QSpinBox)
2025-06-03 17:15:03,037 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_2 (QSpinBox)
2025-06-03 17:15:03,037 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_3 (QSpinBox)
2025-06-03 17:15:03,037 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_4 (QSpinBox)
2025-06-03 17:15:03,037 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_5 (QSpinBox)
2025-06-03 17:15:03,037 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_6 (QSpinBox)
2025-06-03 17:15:03,037 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_7 (QSpinBox)
2025-06-03 17:15:03,037 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK0_1HSEN (QCheckBox)
2025-06-03 17:15:03,038 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK10_11HSEN (QCheckBox)
2025-06-03 17:15:03,038 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK12_13HSEN (QCheckBox)
2025-06-03 17:15:03,038 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK2_3HSEN (QCheckBox)
2025-06-03 17:15:03,038 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK4_5HSEN (QCheckBox)
2025-06-03 17:15:03,038 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK6_7HSEN (QCheckBox)
2025-06-03 17:15:03,038 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK8_9HSEN (QCheckBox)
2025-06-03 17:15:03,038 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1ADLY (QSpinBox)
2025-06-03 17:15:03,038 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1ADLYEnb (QCheckBox)
2025-06-03 17:15:03,038 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1DDLY (QComboBox)
2025-06-03 17:15:03,038 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1HSTrig (QCheckBox)
2025-06-03 17:15:03,038 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1PD (QCheckBox)
2025-06-03 17:15:03,038 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1POL (QCheckBox)
2025-06-03 17:15:03,038 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11ADLY (QSpinBox)
2025-06-03 17:15:03,038 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11ADLYEnb (QCheckBox)
2025-06-03 17:15:03,038 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11DDLY (QComboBox)
2025-06-03 17:15:03,038 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11HSTrig (QCheckBox)
2025-06-03 17:15:03,038 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11PD (QCheckBox)
2025-06-03 17:15:03,038 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11POL (QCheckBox)
2025-06-03 17:15:03,038 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13ADLY (QSpinBox)
2025-06-03 17:15:03,038 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13ADLYEnb (QCheckBox)
2025-06-03 17:15:03,038 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13DDLY (QComboBox)
2025-06-03 17:15:03,038 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13HSTrig (QCheckBox)
2025-06-03 17:15:03,038 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13PD (QCheckBox)
2025-06-03 17:15:03,038 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13POL (QCheckBox)
2025-06-03 17:15:03,038 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3ADLY (QSpinBox)
2025-06-03 17:15:03,038 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3ADLYEnb (QCheckBox)
2025-06-03 17:15:03,038 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3DDLY (QComboBox)
2025-06-03 17:15:03,038 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3HSTrig (QCheckBox)
2025-06-03 17:15:03,038 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3PD (QCheckBox)
2025-06-03 17:15:03,038 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3POL (QCheckBox)
2025-06-03 17:15:03,039 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5ADLY (QSpinBox)
2025-06-03 17:15:03,039 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5ADLYEnb (QCheckBox)
2025-06-03 17:15:03,039 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5DDLY (QComboBox)
2025-06-03 17:15:03,039 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5HSTrig (QCheckBox)
2025-06-03 17:15:03,039 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5PD (QCheckBox)
2025-06-03 17:15:03,039 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5POL (QCheckBox)
2025-06-03 17:15:03,039 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7ADLY (QSpinBox)
2025-06-03 17:15:03,039 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7ADLYEnb (QCheckBox)
2025-06-03 17:15:03,039 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7DDLY (QComboBox)
2025-06-03 17:15:03,039 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7HSTrig (QCheckBox)
2025-06-03 17:15:03,039 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7PD (QCheckBox)
2025-06-03 17:15:03,039 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7POL (QCheckBox)
2025-06-03 17:15:03,039 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9ADLY (QSpinBox)
2025-06-03 17:15:03,039 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9ADLYEnb (QCheckBox)
2025-06-03 17:15:03,039 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9DDLY (QComboBox)
2025-06-03 17:15:03,039 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9HSTrig (QCheckBox)
2025-06-03 17:15:03,039 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9PD (QCheckBox)
2025-06-03 17:15:03,039 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9POL (QCheckBox)
2025-06-03 17:15:03,039 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS0 (QCheckBox)
2025-06-03 17:15:03,039 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS10 (QCheckBox)
2025-06-03 17:15:03,040 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS12 (QCheckBox)
2025-06-03 17:15:03,040 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS2 (QCheckBox)
2025-06-03 17:15:03,040 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS4 (QCheckBox)
2025-06-03 17:15:03,040 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS6 (QCheckBox)
2025-06-03 17:15:03,040 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS8 (QCheckBox)
2025-06-03 17:15:03,040 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SysrefCLR (QCheckBox)
2025-06-03 17:15:03,040 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SysrefGBLPD (QCheckBox)
2025-06-03 17:15:03,040 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: label (QLabel)
2025-06-03 17:15:03,040 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: labelFvco (QLabel)
2025-06-03 17:15:03,040 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout0Output (QLineEdit)
2025-06-03 17:15:03,040 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout10Output (QLineEdit)
2025-06-03 17:15:03,040 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout11Output (QLineEdit)
2025-06-03 17:15:03,040 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout12Output (QLineEdit)
2025-06-03 17:15:03,040 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout13Output (QLineEdit)
2025-06-03 17:15:03,040 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout1Output (QLineEdit)
2025-06-03 17:15:03,040 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout2Output (QLineEdit)
2025-06-03 17:15:03,040 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout3Output (QLineEdit)
2025-06-03 17:15:03,040 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout4Output (QLineEdit)
2025-06-03 17:15:03,040 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout5Output (QLineEdit)
2025-06-03 17:15:03,040 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout6Output (QLineEdit)
2025-06-03 17:15:03,040 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout7Output (QLineEdit)
2025-06-03 17:15:03,040 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout8Output (QLineEdit)
2025-06-03 17:15:03,040 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout9Output (QLineEdit)
2025-06-03 17:15:03,040 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFvco (QLineEdit)
2025-06-03 17:15:03,040 - ModernBaseHandler - [ModernBaseHandler.py:119] - INFO - UI中发现 173 个控件
2025-06-03 17:15:03,041 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:452] - ERROR - 后初始化时出错: 'MockRegisterManager' object has no attribute 'get_widget_register_mapping'
2025-06-03 17:15:03,041 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:56] - INFO - 现代化时钟输出处理器初始化完成
2025-06-03 17:15:03,041 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:744] - INFO - 应用时钟输出预设: all_sysref
2025-06-03 17:15:03,041 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:519] - WARNING - 输出0使用SRCMUX但未设置系统参考处理器
2025-06-03 17:15:03,041 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出0分频值: DCLK0_1DIV = 2
2025-06-03 17:15:03,042 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出0频率: 1474.56 MHz (VCO: 2949.12, 分频: 2)
2025-06-03 17:15:03,042 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:625] - INFO - 输出0 SRCMUX状态变化: True
2025-06-03 17:15:03,042 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:519] - WARNING - 输出1使用SRCMUX但未设置系统参考处理器
2025-06-03 17:15:03,042 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出1分频值: DCLK0_1DIV = 2
2025-06-03 17:15:03,042 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出1频率: 1474.56 MHz (VCO: 2949.12, 分频: 2)
2025-06-03 17:15:03,042 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:625] - INFO - 输出1 SRCMUX状态变化: True
2025-06-03 17:15:03,042 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:519] - WARNING - 输出2使用SRCMUX但未设置系统参考处理器
2025-06-03 17:15:03,042 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出2分频值: DCLK2_3DIV = 4
2025-06-03 17:15:03,043 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出2频率: 737.28 MHz (VCO: 2949.12, 分频: 4)
2025-06-03 17:15:03,043 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:625] - INFO - 输出2 SRCMUX状态变化: True
2025-06-03 17:15:03,043 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:519] - WARNING - 输出3使用SRCMUX但未设置系统参考处理器
2025-06-03 17:15:03,043 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出3分频值: DCLK2_3DIV = 4
2025-06-03 17:15:03,043 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出3频率: 737.28 MHz (VCO: 2949.12, 分频: 4)
2025-06-03 17:15:03,043 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:625] - INFO - 输出3 SRCMUX状态变化: True
2025-06-03 17:15:03,043 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:519] - WARNING - 输出4使用SRCMUX但未设置系统参考处理器
2025-06-03 17:15:03,044 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出4分频值: DCLK4_5DIV = 8
2025-06-03 17:15:03,044 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出4频率: 368.64 MHz (VCO: 2949.12, 分频: 8)
2025-06-03 17:15:03,044 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:625] - INFO - 输出4 SRCMUX状态变化: True
2025-06-03 17:15:03,044 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:519] - WARNING - 输出5使用SRCMUX但未设置系统参考处理器
2025-06-03 17:15:03,044 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出5分频值: DCLK4_5DIV = 8
2025-06-03 17:15:03,044 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出5频率: 368.64 MHz (VCO: 2949.12, 分频: 8)
2025-06-03 17:15:03,044 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:625] - INFO - 输出5 SRCMUX状态变化: True
2025-06-03 17:15:03,044 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:519] - WARNING - 输出6使用SRCMUX但未设置系统参考处理器
2025-06-03 17:15:03,044 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出6分频值: DCLK6_7DIV = 8
2025-06-03 17:15:03,044 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出6频率: 368.64 MHz (VCO: 2949.12, 分频: 8)
2025-06-03 17:15:03,044 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:625] - INFO - 输出6 SRCMUX状态变化: True
2025-06-03 17:15:03,044 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:519] - WARNING - 输出7使用SRCMUX但未设置系统参考处理器
2025-06-03 17:15:03,044 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出7分频值: DCLK6_7DIV = 8
2025-06-03 17:15:03,044 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出7频率: 368.64 MHz (VCO: 2949.12, 分频: 8)
2025-06-03 17:15:03,045 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:625] - INFO - 输出7 SRCMUX状态变化: True
2025-06-03 17:15:03,045 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:519] - WARNING - 输出8使用SRCMUX但未设置系统参考处理器
2025-06-03 17:15:03,045 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出8分频值: DCLK8_9DIV = 8
2025-06-03 17:15:03,045 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出8频率: 368.64 MHz (VCO: 2949.12, 分频: 8)
2025-06-03 17:15:03,045 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:625] - INFO - 输出8 SRCMUX状态变化: True
2025-06-03 17:15:03,045 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:519] - WARNING - 输出9使用SRCMUX但未设置系统参考处理器
2025-06-03 17:15:03,045 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出9分频值: DCLK8_9DIV = 8
2025-06-03 17:15:03,045 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出9频率: 368.64 MHz (VCO: 2949.12, 分频: 8)
2025-06-03 17:15:03,045 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:625] - INFO - 输出9 SRCMUX状态变化: True
2025-06-03 17:15:03,045 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:519] - WARNING - 输出10使用SRCMUX但未设置系统参考处理器
2025-06-03 17:15:03,045 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出10分频值: DCLK10_11DIV = 8
2025-06-03 17:15:03,045 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出10频率: 368.64 MHz (VCO: 2949.12, 分频: 8)
2025-06-03 17:15:03,045 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:625] - INFO - 输出10 SRCMUX状态变化: True
2025-06-03 17:15:03,045 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:519] - WARNING - 输出11使用SRCMUX但未设置系统参考处理器
2025-06-03 17:15:03,045 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出11分频值: DCLK10_11DIV = 8
2025-06-03 17:15:03,046 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出11频率: 368.64 MHz (VCO: 2949.12, 分频: 8)
2025-06-03 17:15:03,046 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:625] - INFO - 输出11 SRCMUX状态变化: True
2025-06-03 17:15:03,046 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:519] - WARNING - 输出12使用SRCMUX但未设置系统参考处理器
2025-06-03 17:15:03,046 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出12分频值: DCLK12_13DIV = 2
2025-06-03 17:15:03,046 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出12频率: 1474.56 MHz (VCO: 2949.12, 分频: 2)
2025-06-03 17:15:03,046 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:625] - INFO - 输出12 SRCMUX状态变化: True
2025-06-03 17:15:03,046 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:519] - WARNING - 输出13使用SRCMUX但未设置系统参考处理器
2025-06-03 17:15:03,046 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出13分频值: DCLK12_13DIV = 2
2025-06-03 17:15:03,046 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出13频率: 1474.56 MHz (VCO: 2949.12, 分频: 2)
2025-06-03 17:15:03,046 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:625] - INFO - 输出13 SRCMUX状态变化: True
2025-06-03 17:15:03,046 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:519] - WARNING - 输出0使用SRCMUX但未设置系统参考处理器
2025-06-03 17:15:03,046 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出0分频值: DCLK0_1DIV = 2
2025-06-03 17:15:03,046 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出0频率: 1474.56 MHz (VCO: 2949.12, 分频: 2)
2025-06-03 17:15:03,046 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:519] - WARNING - 输出1使用SRCMUX但未设置系统参考处理器
2025-06-03 17:15:03,046 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出1分频值: DCLK0_1DIV = 2
2025-06-03 17:15:03,046 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出1频率: 1474.56 MHz (VCO: 2949.12, 分频: 2)
2025-06-03 17:15:03,047 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:519] - WARNING - 输出2使用SRCMUX但未设置系统参考处理器
2025-06-03 17:15:03,047 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出2分频值: DCLK2_3DIV = 4
2025-06-03 17:15:03,047 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出2频率: 737.28 MHz (VCO: 2949.12, 分频: 4)
2025-06-03 17:15:03,047 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:519] - WARNING - 输出3使用SRCMUX但未设置系统参考处理器
2025-06-03 17:15:03,047 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出3分频值: DCLK2_3DIV = 4
2025-06-03 17:15:03,047 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出3频率: 737.28 MHz (VCO: 2949.12, 分频: 4)
2025-06-03 17:15:03,047 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:519] - WARNING - 输出4使用SRCMUX但未设置系统参考处理器
2025-06-03 17:15:03,047 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出4分频值: DCLK4_5DIV = 8
2025-06-03 17:15:03,047 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出4频率: 368.64 MHz (VCO: 2949.12, 分频: 8)
2025-06-03 17:15:03,047 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:519] - WARNING - 输出5使用SRCMUX但未设置系统参考处理器
2025-06-03 17:15:03,047 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出5分频值: DCLK4_5DIV = 8
2025-06-03 17:15:03,047 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出5频率: 368.64 MHz (VCO: 2949.12, 分频: 8)
2025-06-03 17:15:03,047 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:519] - WARNING - 输出6使用SRCMUX但未设置系统参考处理器
2025-06-03 17:15:03,047 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出6分频值: DCLK6_7DIV = 8
2025-06-03 17:15:03,047 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出6频率: 368.64 MHz (VCO: 2949.12, 分频: 8)
2025-06-03 17:15:03,047 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:519] - WARNING - 输出7使用SRCMUX但未设置系统参考处理器
2025-06-03 17:15:03,047 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出7分频值: DCLK6_7DIV = 8
2025-06-03 17:15:03,047 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出7频率: 368.64 MHz (VCO: 2949.12, 分频: 8)
2025-06-03 17:15:03,047 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:519] - WARNING - 输出8使用SRCMUX但未设置系统参考处理器
2025-06-03 17:15:03,048 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出8分频值: DCLK8_9DIV = 8
2025-06-03 17:15:03,048 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出8频率: 368.64 MHz (VCO: 2949.12, 分频: 8)
2025-06-03 17:15:03,048 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:519] - WARNING - 输出9使用SRCMUX但未设置系统参考处理器
2025-06-03 17:15:03,048 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出9分频值: DCLK8_9DIV = 8
2025-06-03 17:15:03,048 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出9频率: 368.64 MHz (VCO: 2949.12, 分频: 8)
2025-06-03 17:15:03,048 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:519] - WARNING - 输出10使用SRCMUX但未设置系统参考处理器
2025-06-03 17:15:03,048 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出10分频值: DCLK10_11DIV = 8
2025-06-03 17:15:03,048 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出10频率: 368.64 MHz (VCO: 2949.12, 分频: 8)
2025-06-03 17:15:03,049 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:519] - WARNING - 输出11使用SRCMUX但未设置系统参考处理器
2025-06-03 17:15:03,049 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出11分频值: DCLK10_11DIV = 8
2025-06-03 17:15:03,049 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出11频率: 368.64 MHz (VCO: 2949.12, 分频: 8)
2025-06-03 17:15:03,049 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:519] - WARNING - 输出12使用SRCMUX但未设置系统参考处理器
2025-06-03 17:15:03,049 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出12分频值: DCLK12_13DIV = 2
2025-06-03 17:15:03,049 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出12频率: 1474.56 MHz (VCO: 2949.12, 分频: 2)
2025-06-03 17:15:03,049 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:519] - WARNING - 输出13使用SRCMUX但未设置系统参考处理器
2025-06-03 17:15:03,049 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出13分频值: DCLK12_13DIV = 2
2025-06-03 17:15:03,049 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出13频率: 1474.56 MHz (VCO: 2949.12, 分频: 2)
2025-06-03 17:15:03,049 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:834] - INFO - 时钟输出预设 all_sysref 应用完成
2025-06-03 17:15:03,049 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出0分频值: DCLK0_1DIV = 2
2025-06-03 17:15:03,049 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出2分频值: DCLK2_3DIV = 4
2025-06-03 17:15:03,049 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出4分频值: DCLK4_5DIV = 8
2025-06-03 17:15:03,050 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出6分频值: DCLK6_7DIV = 8
2025-06-03 17:15:03,050 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出8分频值: DCLK8_9DIV = 8
2025-06-03 17:15:03,050 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出10分频值: DCLK10_11DIV = 8
2025-06-03 17:15:03,050 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出12分频值: DCLK12_13DIV = 2
2025-06-03 17:15:03,050 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:519] - WARNING - 输出0使用SRCMUX但未设置系统参考处理器
2025-06-03 17:15:03,050 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出0分频值: DCLK0_1DIV = 2
2025-06-03 17:15:03,050 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出0频率: 1474.56 MHz (VCO: 2949.12, 分频: 2)
2025-06-03 17:15:03,050 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:625] - INFO - 输出0 SRCMUX状态变化: True
2025-06-03 17:15:03,057 - ModernBaseHandler - [ModernBaseHandler.py:71] - WARNING - 连接RegisterUpdateBus时出错: wrapped C/C++ object of type RegisterUpdateBus has been deleted，继续初始化
2025-06-03 17:15:03,481 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout0FMT 格式选项
2025-06-03 17:15:03,481 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout1FMT 格式选项
2025-06-03 17:15:03,481 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout2FMT 格式选项
2025-06-03 17:15:03,481 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout3FMT 格式选项
2025-06-03 17:15:03,481 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout4FMT 格式选项
2025-06-03 17:15:03,481 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout5FMT 格式选项
2025-06-03 17:15:03,481 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout6FMT 格式选项
2025-06-03 17:15:03,481 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout7FMT 格式选项
2025-06-03 17:15:03,481 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout8FMT 格式选项
2025-06-03 17:15:03,481 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout9FMT 格式选项
2025-06-03 17:15:03,481 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout10FMT 格式选项
2025-06-03 17:15:03,482 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout11FMT 格式选项
2025-06-03 17:15:03,482 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout12FMT 格式选项
2025-06-03 17:15:03,482 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:170] - DEBUG - 已初始化 CLKout13FMT 格式选项
2025-06-03 17:15:03,482 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:172] - INFO - 所有格式下拉框初始化完成
2025-06-03 17:15:03,482 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK0_1DIV: 范围(1-1023), 默认值(2)
2025-06-03 17:15:03,482 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK2_3DIV: 范围(1-1023), 默认值(4)
2025-06-03 17:15:03,482 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK4_5DIV: 范围(1-1023), 默认值(8)
2025-06-03 17:15:03,482 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK6_7DIV: 范围(1-1023), 默认值(8)
2025-06-03 17:15:03,482 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK8_9DIV: 范围(1-1023), 默认值(8)
2025-06-03 17:15:03,483 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK10_11DIV: 范围(1-1023), 默认值(8)
2025-06-03 17:15:03,483 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:205] - DEBUG - 已初始化DCLK12_13DIV: 范围(1-1023), 默认值(2)
2025-06-03 17:15:03,483 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK0_1DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:03,483 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK2_3DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:03,483 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK4_5DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:03,483 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK6_7DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:03,483 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK8_9DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:03,483 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK10_11DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:03,483 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:220] - DEBUG - 已初始化DCLK12_13DDLY: 范围(2-1023), 默认值(10)
2025-06-03 17:15:03,483 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:227] - DEBUG - 已初始化DDLYdStepCNT_1: 范围(0-255), 默认值(0)
2025-06-03 17:15:03,483 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:229] - INFO - 分频器控件初始化完成
2025-06-03 17:15:03,483 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK0_1PD为启用状态
2025-06-03 17:15:03,483 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK2_3PD为启用状态
2025-06-03 17:15:03,483 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK4_5PD为启用状态
2025-06-03 17:15:03,483 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK6_7PD为启用状态
2025-06-03 17:15:03,483 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK8_9PD为启用状态
2025-06-03 17:15:03,484 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK10_11PD为启用状态
2025-06-03 17:15:03,484 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:247] - DEBUG - 已设置DCLK12_13PD为启用状态
2025-06-03 17:15:03,484 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK0_1DDLYPD为启用状态
2025-06-03 17:15:03,484 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK2_3DDLYPD为启用状态
2025-06-03 17:15:03,484 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK4_5DDLYPD为启用状态
2025-06-03 17:15:03,484 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK6_7DDLYPD为启用状态
2025-06-03 17:15:03,484 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK8_9DDLYPD为启用状态
2025-06-03 17:15:03,484 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK10_11DDLYPD为启用状态
2025-06-03 17:15:03,484 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:259] - DEBUG - 已设置DCLK12_13DDLYPD为启用状态
2025-06-03 17:15:03,484 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd0EN为禁用状态
2025-06-03 17:15:03,484 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd2EN为禁用状态
2025-06-03 17:15:03,484 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd4EN为禁用状态
2025-06-03 17:15:03,484 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd6EN为禁用状态
2025-06-03 17:15:03,484 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd8EN为禁用状态
2025-06-03 17:15:03,484 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd10EN为禁用状态
2025-06-03 17:15:03,484 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:271] - DEBUG - 已设置DDLYd12EN为禁用状态
2025-06-03 17:15:03,484 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK0_1PD为关闭状态
2025-06-03 17:15:03,484 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK2_3PD为关闭状态
2025-06-03 17:15:03,484 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK4_5PD为关闭状态
2025-06-03 17:15:03,484 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK6_7PD为关闭状态
2025-06-03 17:15:03,484 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK8_9PD为关闭状态
2025-06-03 17:15:03,484 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK10_11PD为关闭状态
2025-06-03 17:15:03,484 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:283] - DEBUG - 已设置SCLK12_13PD为关闭状态
2025-06-03 17:15:03,484 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK0_1HSTrig为无相位调整
2025-06-03 17:15:03,484 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK2_3HSTrig为无相位调整
2025-06-03 17:15:03,484 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK4_5HSTrig为无相位调整
2025-06-03 17:15:03,484 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK6_7HSTrig为无相位调整
2025-06-03 17:15:03,484 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK8_9HSTrig为无相位调整
2025-06-03 17:15:03,485 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK10_11HSTrig为无相位调整
2025-06-03 17:15:03,485 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:295] - DEBUG - 已设置SCLK12_13HSTrig为无相位调整
2025-06-03 17:15:03,485 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS0为不禁用同步
2025-06-03 17:15:03,485 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS2为不禁用同步
2025-06-03 17:15:03,485 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS4为不禁用同步
2025-06-03 17:15:03,485 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS6为不禁用同步
2025-06-03 17:15:03,485 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS8为不禁用同步
2025-06-03 17:15:03,485 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS10为不禁用同步
2025-06-03 17:15:03,485 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:307] - DEBUG - 已设置SYNCDIS12为不禁用同步
2025-06-03 17:15:03,485 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:309] - INFO - 电源管理和控制状态初始化完成
2025-06-03 17:15:03,485 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK0_1DDLY: 默认0.5ns延迟
2025-06-03 17:15:03,485 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK2_3DDLY: 默认0.5ns延迟
2025-06-03 17:15:03,485 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK4_5DDLY: 默认0.5ns延迟
2025-06-03 17:15:03,485 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK6_7DDLY: 默认0.5ns延迟
2025-06-03 17:15:03,485 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK8_9DDLY: 默认0.5ns延迟
2025-06-03 17:15:03,485 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK10_11DDLY: 默认0.5ns延迟
2025-06-03 17:15:03,485 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:333] - DEBUG - 已初始化SCLK12_13DDLY: 默认0.5ns延迟
2025-06-03 17:15:03,485 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:335] - INFO - SYSREF延迟控件初始化完成
2025-06-03 17:15:03,486 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK2_3DIV 当前值为 4，保持不变
2025-06-03 17:15:03,486 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK4_5DIV 当前值为 8，保持不变
2025-06-03 17:15:03,486 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK6_7DIV 当前值为 8，保持不变
2025-06-03 17:15:03,486 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK8_9DIV 当前值为 8，保持不变
2025-06-03 17:15:03,486 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:374] - DEBUG - DCLK10_11DIV 当前值为 8，保持不变
2025-06-03 17:15:03,486 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:376] - DEBUG - 分频器值初始化检查完成
2025-06-03 17:15:03,486 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:404] - DEBUG - 分频器值同步完成
2025-06-03 17:15:03,486 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:351] - INFO - 输出频率初始化完成（延迟计算）
2025-06-03 17:15:03,486 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:416] - INFO - 已连接 lineEditFvco 信号
2025-06-03 17:15:03,486 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout0_SRCMUX 信号
2025-06-03 17:15:03,486 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout1_SRCMUX 信号
2025-06-03 17:15:03,486 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout2_SRCMUX 信号
2025-06-03 17:15:03,486 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout3_SRCMUX 信号
2025-06-03 17:15:03,486 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout4_SRCMUX 信号
2025-06-03 17:15:03,486 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout5_SRCMUX 信号
2025-06-03 17:15:03,486 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout6_SRCMUX 信号
2025-06-03 17:15:03,486 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout7_SRCMUX 信号
2025-06-03 17:15:03,486 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout8_SRCMUX 信号
2025-06-03 17:15:03,486 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout9_SRCMUX 信号
2025-06-03 17:15:03,487 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout10_SRCMUX 信号
2025-06-03 17:15:03,487 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout11_SRCMUX 信号
2025-06-03 17:15:03,487 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout12_SRCMUX 信号
2025-06-03 17:15:03,487 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:436] - DEBUG - 已连接 CLKout13_SRCMUX 信号
2025-06-03 17:15:03,487 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:437] - INFO - 已连接所有时钟输出的SRCMUX复选框信号
2025-06-03 17:15:03,487 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:119] - INFO - 时钟输出特定配置初始化完成
2025-06-03 17:15:03,487 - ModernBaseHandler - [ModernBaseHandler.py:86] - INFO - ModernBaseHandler._post_init 被调用
2025-06-03 17:15:03,487 - ModernBaseHandler - [ModernBaseHandler.py:88] - INFO - UI已设置，开始构建控件映射
2025-06-03 17:15:03,487 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout0FMT (QComboBox)
2025-06-03 17:15:03,487 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout0_1PD (QCheckBox)
2025-06-03 17:15:03,487 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout0_SRCMUX (QCheckBox)
2025-06-03 17:15:03,487 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout10FMT (QComboBox)
2025-06-03 17:15:03,487 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout10_11PD (QCheckBox)
2025-06-03 17:15:03,487 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout10_SRCMUX (QCheckBox)
2025-06-03 17:15:03,487 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout11FMT (QComboBox)
2025-06-03 17:15:03,487 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout11_SRCMUX (QCheckBox)
2025-06-03 17:15:03,487 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout12FMT (QComboBox)
2025-06-03 17:15:03,487 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout12_13PD (QCheckBox)
2025-06-03 17:15:03,487 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout12_SRCMUX (QCheckBox)
2025-06-03 17:15:03,487 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout13FMT (QComboBox)
2025-06-03 17:15:03,487 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout13_SRCMUX (QCheckBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout1FMT (QComboBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout1_SRCMUX (QCheckBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout2FMT (QComboBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout2_3PD (QCheckBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout2_SRCMUX (QCheckBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout3FMT (QComboBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout3_SRCMUX (QCheckBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout4FMT (QComboBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout4_5PD (QCheckBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout4_SRCMUX (QCheckBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout5FMT (QComboBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout5_SRCMUX (QCheckBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout6FMT (QComboBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout6_7PD (QCheckBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout6_SRCMUX (QCheckBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout7FMT (QComboBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout7_SRCMUX (QCheckBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout8FMT (QComboBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout8_9PD (QCheckBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout8_SRCMUX (QCheckBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout9FMT (QComboBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: CLKout9_SRCMUX (QCheckBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1BYPASS (QCheckBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1DDLY (QSpinBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1DDLYPD (QCheckBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1DIV (QSpinBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1HSTrig (QCheckBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1PD (QCheckBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK0_1POL (QCheckBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11BYPASS (QCheckBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11DDLY (QSpinBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11DDLYPD (QCheckBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11DIV (QSpinBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11HSTrig (QCheckBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11PD (QCheckBox)
2025-06-03 17:15:03,488 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK10_11POL (QCheckBox)
2025-06-03 17:15:03,489 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13BYPASS (QCheckBox)
2025-06-03 17:15:03,489 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13DDLY (QSpinBox)
2025-06-03 17:15:03,489 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13DDLYPD (QCheckBox)
2025-06-03 17:15:03,489 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13DIV (QSpinBox)
2025-06-03 17:15:03,489 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13HSTrig (QCheckBox)
2025-06-03 17:15:03,489 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13PD (QCheckBox)
2025-06-03 17:15:03,489 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK12_13POL (QCheckBox)
2025-06-03 17:15:03,489 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3BYPASS (QCheckBox)
2025-06-03 17:15:03,489 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3DDLY (QSpinBox)
2025-06-03 17:15:03,489 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3DDLYPD (QCheckBox)
2025-06-03 17:15:03,489 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3DIV (QSpinBox)
2025-06-03 17:15:03,489 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3HSTrig (QCheckBox)
2025-06-03 17:15:03,489 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3PD (QCheckBox)
2025-06-03 17:15:03,489 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK2_3POL (QCheckBox)
2025-06-03 17:15:03,489 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5BYPASS (QCheckBox)
2025-06-03 17:15:03,489 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5DDLY (QSpinBox)
2025-06-03 17:15:03,489 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5DDLYPD (QCheckBox)
2025-06-03 17:15:03,489 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5DIV (QSpinBox)
2025-06-03 17:15:03,489 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5HSTrig (QCheckBox)
2025-06-03 17:15:03,489 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5PD (QCheckBox)
2025-06-03 17:15:03,489 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK4_5POL (QCheckBox)
2025-06-03 17:15:03,489 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7BYPASS (QCheckBox)
2025-06-03 17:15:03,489 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7DDLY (QSpinBox)
2025-06-03 17:15:03,489 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7DDLYPD (QCheckBox)
2025-06-03 17:15:03,489 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7DIV (QSpinBox)
2025-06-03 17:15:03,489 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7HSTrig (QCheckBox)
2025-06-03 17:15:03,489 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7PD (QCheckBox)
2025-06-03 17:15:03,489 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK6_7POL (QCheckBox)
2025-06-03 17:15:03,489 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9BYPASS (QCheckBox)
2025-06-03 17:15:03,489 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9DDLY (QSpinBox)
2025-06-03 17:15:03,489 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9DDLYPD (QCheckBox)
2025-06-03 17:15:03,489 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9DIV (QSpinBox)
2025-06-03 17:15:03,490 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9HSTrig (QCheckBox)
2025-06-03 17:15:03,490 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9PD (QCheckBox)
2025-06-03 17:15:03,490 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DCLK8_9POL (QCheckBox)
2025-06-03 17:15:03,490 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd0EN (QCheckBox)
2025-06-03 17:15:03,490 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd10EN (QCheckBox)
2025-06-03 17:15:03,490 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd12EN (QCheckBox)
2025-06-03 17:15:03,490 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd2EN (QCheckBox)
2025-06-03 17:15:03,490 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd4EN (QCheckBox)
2025-06-03 17:15:03,490 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd6EN (QCheckBox)
2025-06-03 17:15:03,490 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYd8EN (QCheckBox)
2025-06-03 17:15:03,490 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_1 (QSpinBox)
2025-06-03 17:15:03,490 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_2 (QSpinBox)
2025-06-03 17:15:03,490 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_3 (QSpinBox)
2025-06-03 17:15:03,490 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_4 (QSpinBox)
2025-06-03 17:15:03,490 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_5 (QSpinBox)
2025-06-03 17:15:03,490 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_6 (QSpinBox)
2025-06-03 17:15:03,490 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DDLYdStepCNT_7 (QSpinBox)
2025-06-03 17:15:03,490 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK0_1HSEN (QCheckBox)
2025-06-03 17:15:03,490 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK10_11HSEN (QCheckBox)
2025-06-03 17:15:03,490 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK12_13HSEN (QCheckBox)
2025-06-03 17:15:03,490 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK2_3HSEN (QCheckBox)
2025-06-03 17:15:03,490 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK4_5HSEN (QCheckBox)
2025-06-03 17:15:03,490 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK6_7HSEN (QCheckBox)
2025-06-03 17:15:03,490 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGDCLK8_9HSEN (QCheckBox)
2025-06-03 17:15:03,490 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1ADLY (QSpinBox)
2025-06-03 17:15:03,490 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1ADLYEnb (QCheckBox)
2025-06-03 17:15:03,490 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1DDLY (QComboBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1HSTrig (QCheckBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1PD (QCheckBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK0_1POL (QCheckBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11ADLY (QSpinBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11ADLYEnb (QCheckBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11DDLY (QComboBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11HSTrig (QCheckBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11PD (QCheckBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK10_11POL (QCheckBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13ADLY (QSpinBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13ADLYEnb (QCheckBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13DDLY (QComboBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13HSTrig (QCheckBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13PD (QCheckBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK12_13POL (QCheckBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3ADLY (QSpinBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3ADLYEnb (QCheckBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3DDLY (QComboBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3HSTrig (QCheckBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3PD (QCheckBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK2_3POL (QCheckBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5ADLY (QSpinBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5ADLYEnb (QCheckBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5DDLY (QComboBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5HSTrig (QCheckBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5PD (QCheckBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK4_5POL (QCheckBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7ADLY (QSpinBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7ADLYEnb (QCheckBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7DDLY (QComboBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7HSTrig (QCheckBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7PD (QCheckBox)
2025-06-03 17:15:03,491 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK6_7POL (QCheckBox)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9ADLY (QSpinBox)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9ADLYEnb (QCheckBox)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9DDLY (QComboBox)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9HSTrig (QCheckBox)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9PD (QCheckBox)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SCLK8_9POL (QCheckBox)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS0 (QCheckBox)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS10 (QCheckBox)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS12 (QCheckBox)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS2 (QCheckBox)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS4 (QCheckBox)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS6 (QCheckBox)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SYNCDIS8 (QCheckBox)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SysrefCLR (QCheckBox)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: SysrefGBLPD (QCheckBox)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: label (QLabel)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: labelFvco (QLabel)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout0Output (QLineEdit)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout10Output (QLineEdit)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout11Output (QLineEdit)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout12Output (QLineEdit)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout13Output (QLineEdit)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout1Output (QLineEdit)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout2Output (QLineEdit)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout3Output (QLineEdit)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout4Output (QLineEdit)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout5Output (QLineEdit)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout6Output (QLineEdit)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout7Output (QLineEdit)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout8Output (QLineEdit)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFout9Output (QLineEdit)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: lineEditFvco (QLineEdit)
2025-06-03 17:15:03,492 - ModernBaseHandler - [ModernBaseHandler.py:119] - INFO - UI中发现 173 个控件
2025-06-03 17:15:03,493 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:452] - ERROR - 后初始化时出错: 'MockRegisterManager' object has no attribute 'get_widget_register_mapping'
2025-06-03 17:15:03,493 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:56] - INFO - 现代化时钟输出处理器初始化完成
2025-06-03 17:15:03,494 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出0分频值: DCLK0_1DIV = 2
2025-06-03 17:15:03,494 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出0频率: 1500.00 MHz (VCO: 3000.0, 分频: 2)
2025-06-03 17:15:03,494 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出1分频值: DCLK0_1DIV = 2
2025-06-03 17:15:03,494 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出1频率: 1500.00 MHz (VCO: 3000.0, 分频: 2)
2025-06-03 17:15:03,494 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出2分频值: DCLK2_3DIV = 4
2025-06-03 17:15:03,494 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出2频率: 750.00 MHz (VCO: 3000.0, 分频: 4)
2025-06-03 17:15:03,494 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出3分频值: DCLK2_3DIV = 4
2025-06-03 17:15:03,495 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出3频率: 750.00 MHz (VCO: 3000.0, 分频: 4)
2025-06-03 17:15:03,495 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出4分频值: DCLK4_5DIV = 8
2025-06-03 17:15:03,495 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出4频率: 375.00 MHz (VCO: 3000.0, 分频: 8)
2025-06-03 17:15:03,495 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出5分频值: DCLK4_5DIV = 8
2025-06-03 17:15:03,495 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出5频率: 375.00 MHz (VCO: 3000.0, 分频: 8)
2025-06-03 17:15:03,495 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出6分频值: DCLK6_7DIV = 8
2025-06-03 17:15:03,495 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出6频率: 375.00 MHz (VCO: 3000.0, 分频: 8)
2025-06-03 17:15:03,495 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出7分频值: DCLK6_7DIV = 8
2025-06-03 17:15:03,495 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出7频率: 375.00 MHz (VCO: 3000.0, 分频: 8)
2025-06-03 17:15:03,495 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出8分频值: DCLK8_9DIV = 8
2025-06-03 17:15:03,495 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出8频率: 375.00 MHz (VCO: 3000.0, 分频: 8)
2025-06-03 17:15:03,495 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出9分频值: DCLK8_9DIV = 8
2025-06-03 17:15:03,495 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出9频率: 375.00 MHz (VCO: 3000.0, 分频: 8)
2025-06-03 17:15:03,495 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出10分频值: DCLK10_11DIV = 8
2025-06-03 17:15:03,495 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出10频率: 375.00 MHz (VCO: 3000.0, 分频: 8)
2025-06-03 17:15:03,495 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出11分频值: DCLK10_11DIV = 8
2025-06-03 17:15:03,495 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出11频率: 375.00 MHz (VCO: 3000.0, 分频: 8)
2025-06-03 17:15:03,495 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出12分频值: DCLK12_13DIV = 2
2025-06-03 17:15:03,495 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出12频率: 1500.00 MHz (VCO: 3000.0, 分频: 2)
2025-06-03 17:15:03,495 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出13分频值: DCLK12_13DIV = 2
2025-06-03 17:15:03,495 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出13频率: 1500.00 MHz (VCO: 3000.0, 分频: 2)
2025-06-03 17:15:03,495 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出0分频值: DCLK0_1DIV = 2
2025-06-03 17:15:03,496 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出0频率: 1500.00 MHz (VCO: 3000.0, 分频: 2)
2025-06-03 17:15:03,496 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出1分频值: DCLK0_1DIV = 2
2025-06-03 17:15:03,496 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出1频率: 1500.00 MHz (VCO: 3000.0, 分频: 2)
2025-06-03 17:15:03,496 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出2分频值: DCLK2_3DIV = 4
2025-06-03 17:15:03,496 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出2频率: 750.00 MHz (VCO: 3000.0, 分频: 4)
2025-06-03 17:15:03,496 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出3分频值: DCLK2_3DIV = 4
2025-06-03 17:15:03,496 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出3频率: 750.00 MHz (VCO: 3000.0, 分频: 4)
2025-06-03 17:15:03,496 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出4分频值: DCLK4_5DIV = 8
2025-06-03 17:15:03,496 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出4频率: 375.00 MHz (VCO: 3000.0, 分频: 8)
2025-06-03 17:15:03,496 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出5分频值: DCLK4_5DIV = 8
2025-06-03 17:15:03,496 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出5频率: 375.00 MHz (VCO: 3000.0, 分频: 8)
2025-06-03 17:15:03,496 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出6分频值: DCLK6_7DIV = 8
2025-06-03 17:15:03,497 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出6频率: 375.00 MHz (VCO: 3000.0, 分频: 8)
2025-06-03 17:15:03,497 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出7分频值: DCLK6_7DIV = 8
2025-06-03 17:15:03,497 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出7频率: 375.00 MHz (VCO: 3000.0, 分频: 8)
2025-06-03 17:15:03,497 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出8分频值: DCLK8_9DIV = 8
2025-06-03 17:15:03,497 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出8频率: 375.00 MHz (VCO: 3000.0, 分频: 8)
2025-06-03 17:15:03,497 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出9分频值: DCLK8_9DIV = 8
2025-06-03 17:15:03,497 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出9频率: 375.00 MHz (VCO: 3000.0, 分频: 8)
2025-06-03 17:15:03,497 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出10分频值: DCLK10_11DIV = 8
2025-06-03 17:15:03,497 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出10频率: 375.00 MHz (VCO: 3000.0, 分频: 8)
2025-06-03 17:15:03,497 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出11分频值: DCLK10_11DIV = 8
2025-06-03 17:15:03,497 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出11频率: 375.00 MHz (VCO: 3000.0, 分频: 8)
2025-06-03 17:15:03,497 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出12分频值: DCLK12_13DIV = 2
2025-06-03 17:15:03,497 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出12频率: 1500.00 MHz (VCO: 3000.0, 分频: 2)
2025-06-03 17:15:03,497 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:557] - DEBUG - 从UI控件获取输出13分频值: DCLK12_13DIV = 2
2025-06-03 17:15:03,497 - ModernClkOutputsHandler - [ModernClkOutputsHandler.py:526] - DEBUG - 输出13频率: 1500.00 MHz (VCO: 3000.0, 分频: 2)
2025-06-03 17:15:03,503 - test_utils - [test_utils.py:55] - ERROR - ✗ test_clk_outputs 失败: None
2025-06-03 17:15:03,503 - test_utils - [test_utils.py:46] - INFO - 运行测试: test_clk_output_ui (分类: functional)
2025-06-03 17:15:03,508 - test_utils - [test_utils.py:46] - INFO - ✓ test_clk_output_ui 通过 (0.00s)
2025-06-03 17:15:03,509 - test_utils - [test_utils.py:46] - INFO - 运行测试: test_enhanced_sync_initialization (分类: functional)
2025-06-03 17:15:03,511 - test_utils - [test_utils.py:46] - INFO - ✓ test_enhanced_sync_initialization 通过 (0.00s)
2025-06-03 17:15:03,512 - test_utils - [test_utils.py:46] - INFO - 运行测试: test_final_frequency_calculation (分类: functional)
2025-06-03 17:15:03,513 - test_utils - [test_utils.py:46] - INFO - ✓ test_final_frequency_calculation 通过 (0.00s)
2025-06-03 17:15:03,513 - test_utils - [test_utils.py:46] - INFO - 运行测试: test_modern_clk_outputs (分类: functional)
2025-06-03 17:15:03,514 - test_utils - [test_utils.py:46] - INFO - ✓ test_modern_clk_outputs 通过 (0.00s)
2025-06-03 17:15:03,514 - test_utils - [test_utils.py:46] - INFO - 运行测试: test_modern_pll (分类: functional)
2025-06-03 17:15:03,516 - test_utils - [test_utils.py:46] - INFO - ✓ test_modern_pll 通过 (0.00s)
2025-06-03 17:15:03,516 - test_utils - [test_utils.py:46] - INFO - 运行测试: test_modern_pll_fix (分类: functional)
2025-06-03 17:15:03,517 - test_utils - [test_utils.py:46] - INFO - ✓ test_modern_pll_fix 通过 (0.00s)
2025-06-03 17:15:03,518 - test_utils - [test_utils.py:46] - INFO - 运行测试: test_modern_pll_fixed (分类: functional)
2025-06-03 17:15:03,527 - test_utils - [test_utils.py:55] - ERROR - ✗ test_modern_pll_fixed 失败: None
2025-06-03 17:15:03,528 - test_utils - [test_utils.py:46] - INFO - 运行测试: test_modern_sync_sysref (分类: functional)
2025-06-03 17:15:03,529 - test_utils - [test_utils.py:46] - INFO - ✓ test_modern_sync_sysref 通过 (0.00s)
2025-06-03 17:15:03,529 - test_utils - [test_utils.py:46] - INFO - 运行测试: test_pll_and_clkout_calculations (分类: functional)
2025-06-03 17:15:03,541 - test_utils - [test_utils.py:55] - ERROR - ✗ test_pll_and_clkout_calculations 失败: None
2025-06-03 17:15:03,542 - test_utils - [test_utils.py:46] - INFO - 运行测试: test_pll_control (分类: functional)
2025-06-03 17:15:03,550 - ModernBaseHandler - [ModernBaseHandler.py:71] - WARNING - 连接RegisterUpdateBus时出错: wrapped C/C++ object of type RegisterUpdateBus has been deleted，继续初始化
2025-06-03 17:15:03,575 - ModernPLLHandler - [ModernPLLHandler.py:128] - INFO - 从RegisterUpdateBus获取时钟源配置: ClkIn1
2025-06-03 17:15:03,575 - ModernPLLHandler - [ModernPLLHandler.py:136] - INFO - 时钟源配置初始化完成: 频率={'ClkIn0': 122.88, 'ClkIn1': 122.88, 'ClkIn2': 122.88, 'ClkIn3': 0}, 分频={'ClkIn0': 120, 'ClkIn1': 120, 'ClkIn2': 120, 'ClkIn3': 1}
2025-06-03 17:15:03,575 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 comboPLL1WindSize 设置选项映射
2025-06-03 17:15:03,575 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 comboPLL1WindSize 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:03,576 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2WINDSIZE 设置选项映射
2025-06-03 17:15:03,576 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2WINDSIZE 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:03,576 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL1CPState 设置选项映射
2025-06-03 17:15:03,576 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL1CPState 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:03,576 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2CPState 设置选项映射
2025-06-03 17:15:03,576 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2CPState 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:03,576 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL1PFDPolarity 设置选项映射
2025-06-03 17:15:03,576 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL1PFDPolarity 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:03,577 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2PFDPolarity 设置选项映射
2025-06-03 17:15:03,577 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2PFDPolarity 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:03,577 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL1CPGain 设置选项映射
2025-06-03 17:15:03,577 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL1CPGain 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:03,577 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2CPGain 设置选项映射
2025-06-03 17:15:03,577 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2CPGain 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:03,577 - ModernPLLHandler - [ModernPLLHandler.py:311] - INFO - 已为 PLL2R3 设置特殊映射: [0, 1, 2, 4] -> ['2.4KOhm', '0.2KOhm', '0.5KOhm', '1.1KOhm']
2025-06-03 17:15:03,577 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2R3 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:03,577 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2C1 设置选项映射
2025-06-03 17:15:03,577 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2C1 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:03,578 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2C3 设置选项映射
2025-06-03 17:15:03,578 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2C3 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:03,578 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL1NclkMux 设置选项映射
2025-06-03 17:15:03,578 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL1NclkMux 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:03,578 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2NclkMux 设置选项映射
2025-06-03 17:15:03,578 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2NclkMux 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:03,578 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2RclkMux 设置选项映射
2025-06-03 17:15:03,578 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2RclkMux 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:03,578 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2Prescaler 设置选项映射
2025-06-03 17:15:03,578 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2Prescaler 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:03,578 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 FBMUX 设置选项映射
2025-06-03 17:15:03,579 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 FBMUX 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:03,579 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 comboVcoMode 设置选项映射
2025-06-03 17:15:03,579 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 comboVcoMode 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:03,579 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 Doubler 设置选项映射
2025-06-03 17:15:03,579 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 Doubler 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:03,579 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 Fin0InputType 设置选项映射
2025-06-03 17:15:03,579 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 Fin0InputType 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:03,579 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 FSMDIV 设置选项映射
2025-06-03 17:15:03,579 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 FSMDIV 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:03,579 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 DACClkMult 设置选项映射
2025-06-03 17:15:03,579 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 DACClkMult 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:03,579 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 HoldoverExitMode 设置选项映射
2025-06-03 17:15:03,580 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 HoldoverExitMode 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:03,580 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 RGHOExitDacassistStep 设置选项映射
2025-06-03 17:15:03,580 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 RGHOExitDacassistStep 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:03,580 - ModernPLLHandler - [ModernPLLHandler.py:321] - INFO - ComboBox控件初始化完成
2025-06-03 17:15:03,950 - ModernPLLHandler - [ModernPLLHandler.py:284] - INFO - UI默认值初始化完成
2025-06-03 17:15:03,951 - ModernPLLHandler - [ModernPLLHandler.py:409] - INFO - 已设置PLL2NDivider控件范围: 2-262143
2025-06-03 17:15:03,951 - ModernPLLHandler - [ModernPLLHandler.py:414] - INFO - 已设置PLL1RDividerSetting控件最大值: 16383
2025-06-03 17:15:03,951 - ModernPLLHandler - [ModernPLLHandler.py:629] - INFO - 已注册跨寄存器控件 PLL2NDivider, 关联寄存器: 0x76, 0x77
2025-06-03 17:15:03,951 - ModernPLLHandler - [ModernPLLHandler.py:554] - INFO - 已注册PLL2NDivider为跨寄存器控件
2025-06-03 17:15:03,951 - ModernPLLHandler - [ModernPLLHandler.py:688] - INFO - 已初始化PLL2NDivider控件，值为 12 (高2位=0, 低16位=0)
2025-06-03 17:15:03,951 - ModernPLLHandler - [ModernPLLHandler.py:629] - INFO - 已注册跨寄存器控件 PLL1RDividerSetting, 关联寄存器: 0x63
2025-06-03 17:15:03,952 - ModernPLLHandler - [ModernPLLHandler.py:573] - INFO - 已注册PLL1RDividerSetting为动态映射控件
2025-06-03 17:15:03,952 - ModernPLLHandler - [ModernPLLHandler.py:577] - INFO - 已设置PLL1RDividerSetting控件范围
2025-06-03 17:15:03,952 - ModernPLLHandler - [ModernPLLHandler.py:581] - INFO - 跨寄存器控件注册完成
2025-06-03 17:15:03,952 - ModernPLLHandler - [ModernPLLHandler.py:425] - INFO - 已连接 OSCinFreq textChanged 信号
2025-06-03 17:15:03,952 - ModernPLLHandler - [ModernPLLHandler.py:71] - INFO - PLL特定配置初始化完成
2025-06-03 17:15:03,952 - ModernBaseHandler - [ModernBaseHandler.py:86] - INFO - ModernBaseHandler._post_init 被调用
2025-06-03 17:15:03,952 - ModernBaseHandler - [ModernBaseHandler.py:88] - INFO - UI已设置，开始构建控件映射
2025-06-03 17:15:03,952 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DACClkCntr (QComboBox)
2025-06-03 17:15:03,952 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DACClkMult (QComboBox)
2025-06-03 17:15:03,952 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DACHighTrip (QSpinBox)
2025-06-03 17:15:03,952 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DACLowTrip (QSpinBox)
2025-06-03 17:15:03,952 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DACUpdateRate (QLineEdit)
2025-06-03 17:15:03,952 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: Div2 (QCheckBox)
2025-06-03 17:15:03,952 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: Doubler (QComboBox)
2025-06-03 17:15:03,952 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: ExternalVCXOFreq (QLineEdit)
2025-06-03 17:15:03,952 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FBMUX (QComboBox)
2025-06-03 17:15:03,952 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FBMuxEn (QCheckBox)
2025-06-03 17:15:03,952 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FCALEN (QCheckBox)
2025-06-03 17:15:03,952 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FCALM1 (QComboBox)
2025-06-03 17:15:03,952 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FCALM2 (QComboBox)
2025-06-03 17:15:03,952 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FSMDIV (QComboBox)
2025-06-03 17:15:03,952 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: Fin0Freq (QLineEdit)
2025-06-03 17:15:03,952 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: Fin0InputType (QComboBox)
2025-06-03 17:15:03,952 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: Fin0PD (QCheckBox)
2025-06-03 17:15:03,952 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FreFin (QLineEdit)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: HoldOverEn (QCheckBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: HoldOverPLL1Det (QCheckBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: HoldoverExitMode (QComboBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: LosExternalInput (QCheckBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: MANDAC (QSpinBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: ManDacEN (QCheckBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: OSCinFreq (QLineEdit)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: OSCinPD (QCheckBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1CPGain (QComboBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1CPState (QComboBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1NDivider (QSpinBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1NclkMux (QComboBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1PD (QCheckBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1PFDFreq (QLineEdit)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1PFDPolarity (QComboBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1RDividerSetting (QSpinBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1RRst (QCheckBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2C1 (QComboBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2C3 (QComboBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2CPGain (QComboBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2CPState (QComboBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2NDivider (QSpinBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2NclkMux (QComboBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2OpenLoop (QCheckBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2PD (QCheckBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2PFDFreq (QLineEdit)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2PFDPolarity (QComboBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2PrePD (QCheckBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2Prescaler (QComboBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2R3 (QComboBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2RDivider (QSpinBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2RclkMux (QComboBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2WINDSIZE (QComboBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RBFcalCAPMODE (QLineEdit)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RBPLL1DLD (QCheckBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RBPLL2DLD (QCheckBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RBPLL2VtuneADC (QLineEdit)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGCycleslipEn (QCheckBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGHOExitDacassist (QCheckBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGHOExitDacassistStep (QComboBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGHoFastEnterEn (QCheckBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGVtunedetRelativeEn (QCheckBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGZpsEn (QCheckBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: TrackEn (QCheckBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: VCOLdoPD (QCheckBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: VCOPD (QCheckBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: comboPLL1WindSize (QComboBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: comboVcoMode (QComboBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: label (QLabel)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: spinBoxPLL1DLDCNT (QSpinBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: spinBoxPLL2DLDCNT (QSpinBox)
2025-06-03 17:15:03,953 - ModernBaseHandler - [ModernBaseHandler.py:119] - INFO - UI中发现 69 个控件
2025-06-03 17:15:03,957 - test_utils - [test_utils.py:55] - ERROR - 时钟源更新测试失败: 'MockRegisterManager' object has no attribute 'get_widget_register_mapping'
2025-06-03 17:15:03,961 - ModernBaseHandler - [ModernBaseHandler.py:71] - WARNING - 连接RegisterUpdateBus时出错: wrapped C/C++ object of type RegisterUpdateBus has been deleted，继续初始化
2025-06-03 17:15:03,995 - ModernPLLHandler - [ModernPLLHandler.py:128] - INFO - 从RegisterUpdateBus获取时钟源配置: ClkIn1
2025-06-03 17:15:03,996 - ModernPLLHandler - [ModernPLLHandler.py:136] - INFO - 时钟源配置初始化完成: 频率={'ClkIn0': 122.88, 'ClkIn1': 122.88, 'ClkIn2': 122.88, 'ClkIn3': 0}, 分频={'ClkIn0': 120, 'ClkIn1': 120, 'ClkIn2': 120, 'ClkIn3': 1}
2025-06-03 17:15:03,996 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 comboPLL1WindSize 设置选项映射
2025-06-03 17:15:03,996 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 comboPLL1WindSize 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:03,996 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2WINDSIZE 设置选项映射
2025-06-03 17:15:03,997 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2WINDSIZE 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:03,997 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL1CPState 设置选项映射
2025-06-03 17:15:03,997 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL1CPState 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:03,997 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2CPState 设置选项映射
2025-06-03 17:15:03,997 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2CPState 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:03,998 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL1PFDPolarity 设置选项映射
2025-06-03 17:15:03,998 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL1PFDPolarity 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:03,998 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2PFDPolarity 设置选项映射
2025-06-03 17:15:03,998 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2PFDPolarity 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:03,998 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL1CPGain 设置选项映射
2025-06-03 17:15:03,999 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL1CPGain 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:03,999 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2CPGain 设置选项映射
2025-06-03 17:15:03,999 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2CPGain 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:03,999 - ModernPLLHandler - [ModernPLLHandler.py:311] - INFO - 已为 PLL2R3 设置特殊映射: [0, 1, 2, 4] -> ['2.4KOhm', '0.2KOhm', '0.5KOhm', '1.1KOhm']
2025-06-03 17:15:04,000 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2R3 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,000 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2C1 设置选项映射
2025-06-03 17:15:04,000 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2C1 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,000 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2C3 设置选项映射
2025-06-03 17:15:04,000 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2C3 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,000 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL1NclkMux 设置选项映射
2025-06-03 17:15:04,001 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL1NclkMux 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,001 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2NclkMux 设置选项映射
2025-06-03 17:15:04,001 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2NclkMux 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,001 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2RclkMux 设置选项映射
2025-06-03 17:15:04,001 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2RclkMux 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,002 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2Prescaler 设置选项映射
2025-06-03 17:15:04,002 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2Prescaler 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,002 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 FBMUX 设置选项映射
2025-06-03 17:15:04,002 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 FBMUX 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,002 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 comboVcoMode 设置选项映射
2025-06-03 17:15:04,002 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 comboVcoMode 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,003 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 Doubler 设置选项映射
2025-06-03 17:15:04,003 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 Doubler 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,003 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 Fin0InputType 设置选项映射
2025-06-03 17:15:04,003 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 Fin0InputType 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,003 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 FSMDIV 设置选项映射
2025-06-03 17:15:04,003 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 FSMDIV 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,003 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 DACClkMult 设置选项映射
2025-06-03 17:15:04,003 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 DACClkMult 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,003 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 HoldoverExitMode 设置选项映射
2025-06-03 17:15:04,003 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 HoldoverExitMode 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,004 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 RGHOExitDacassistStep 设置选项映射
2025-06-03 17:15:04,004 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 RGHOExitDacassistStep 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,004 - ModernPLLHandler - [ModernPLLHandler.py:321] - INFO - ComboBox控件初始化完成
2025-06-03 17:15:04,399 - ModernPLLHandler - [ModernPLLHandler.py:284] - INFO - UI默认值初始化完成
2025-06-03 17:15:04,400 - ModernPLLHandler - [ModernPLLHandler.py:409] - INFO - 已设置PLL2NDivider控件范围: 2-262143
2025-06-03 17:15:04,400 - ModernPLLHandler - [ModernPLLHandler.py:414] - INFO - 已设置PLL1RDividerSetting控件最大值: 16383
2025-06-03 17:15:04,400 - ModernPLLHandler - [ModernPLLHandler.py:629] - INFO - 已注册跨寄存器控件 PLL2NDivider, 关联寄存器: 0x76, 0x77
2025-06-03 17:15:04,401 - ModernPLLHandler - [ModernPLLHandler.py:554] - INFO - 已注册PLL2NDivider为跨寄存器控件
2025-06-03 17:15:04,401 - ModernPLLHandler - [ModernPLLHandler.py:688] - INFO - 已初始化PLL2NDivider控件，值为 12 (高2位=0, 低16位=0)
2025-06-03 17:15:04,402 - ModernPLLHandler - [ModernPLLHandler.py:629] - INFO - 已注册跨寄存器控件 PLL1RDividerSetting, 关联寄存器: 0x63
2025-06-03 17:15:04,402 - ModernPLLHandler - [ModernPLLHandler.py:573] - INFO - 已注册PLL1RDividerSetting为动态映射控件
2025-06-03 17:15:04,403 - ModernPLLHandler - [ModernPLLHandler.py:577] - INFO - 已设置PLL1RDividerSetting控件范围
2025-06-03 17:15:04,404 - ModernPLLHandler - [ModernPLLHandler.py:581] - INFO - 跨寄存器控件注册完成
2025-06-03 17:15:04,404 - ModernPLLHandler - [ModernPLLHandler.py:425] - INFO - 已连接 OSCinFreq textChanged 信号
2025-06-03 17:15:04,405 - ModernPLLHandler - [ModernPLLHandler.py:71] - INFO - PLL特定配置初始化完成
2025-06-03 17:15:04,405 - ModernBaseHandler - [ModernBaseHandler.py:86] - INFO - ModernBaseHandler._post_init 被调用
2025-06-03 17:15:04,406 - ModernBaseHandler - [ModernBaseHandler.py:88] - INFO - UI已设置，开始构建控件映射
2025-06-03 17:15:04,406 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DACClkCntr (QComboBox)
2025-06-03 17:15:04,406 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DACClkMult (QComboBox)
2025-06-03 17:15:04,406 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DACHighTrip (QSpinBox)
2025-06-03 17:15:04,406 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DACLowTrip (QSpinBox)
2025-06-03 17:15:04,406 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DACUpdateRate (QLineEdit)
2025-06-03 17:15:04,407 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: Div2 (QCheckBox)
2025-06-03 17:15:04,407 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: Doubler (QComboBox)
2025-06-03 17:15:04,407 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: ExternalVCXOFreq (QLineEdit)
2025-06-03 17:15:04,407 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FBMUX (QComboBox)
2025-06-03 17:15:04,407 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FBMuxEn (QCheckBox)
2025-06-03 17:15:04,407 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FCALEN (QCheckBox)
2025-06-03 17:15:04,407 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FCALM1 (QComboBox)
2025-06-03 17:15:04,407 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FCALM2 (QComboBox)
2025-06-03 17:15:04,407 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FSMDIV (QComboBox)
2025-06-03 17:15:04,407 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: Fin0Freq (QLineEdit)
2025-06-03 17:15:04,407 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: Fin0InputType (QComboBox)
2025-06-03 17:15:04,407 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: Fin0PD (QCheckBox)
2025-06-03 17:15:04,407 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FreFin (QLineEdit)
2025-06-03 17:15:04,408 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: HoldOverEn (QCheckBox)
2025-06-03 17:15:04,408 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: HoldOverPLL1Det (QCheckBox)
2025-06-03 17:15:04,408 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: HoldoverExitMode (QComboBox)
2025-06-03 17:15:04,408 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: LosExternalInput (QCheckBox)
2025-06-03 17:15:04,408 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: MANDAC (QSpinBox)
2025-06-03 17:15:04,408 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: ManDacEN (QCheckBox)
2025-06-03 17:15:04,408 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: OSCinFreq (QLineEdit)
2025-06-03 17:15:04,408 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: OSCinPD (QCheckBox)
2025-06-03 17:15:04,408 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1CPGain (QComboBox)
2025-06-03 17:15:04,408 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1CPState (QComboBox)
2025-06-03 17:15:04,408 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1NDivider (QSpinBox)
2025-06-03 17:15:04,408 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1NclkMux (QComboBox)
2025-06-03 17:15:04,408 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1PD (QCheckBox)
2025-06-03 17:15:04,408 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1PFDFreq (QLineEdit)
2025-06-03 17:15:04,408 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1PFDPolarity (QComboBox)
2025-06-03 17:15:04,409 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1RDividerSetting (QSpinBox)
2025-06-03 17:15:04,409 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1RRst (QCheckBox)
2025-06-03 17:15:04,409 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2C1 (QComboBox)
2025-06-03 17:15:04,409 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2C3 (QComboBox)
2025-06-03 17:15:04,409 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2CPGain (QComboBox)
2025-06-03 17:15:04,409 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2CPState (QComboBox)
2025-06-03 17:15:04,409 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2NDivider (QSpinBox)
2025-06-03 17:15:04,409 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2NclkMux (QComboBox)
2025-06-03 17:15:04,409 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2OpenLoop (QCheckBox)
2025-06-03 17:15:04,409 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2PD (QCheckBox)
2025-06-03 17:15:04,409 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2PFDFreq (QLineEdit)
2025-06-03 17:15:04,409 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2PFDPolarity (QComboBox)
2025-06-03 17:15:04,409 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2PrePD (QCheckBox)
2025-06-03 17:15:04,409 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2Prescaler (QComboBox)
2025-06-03 17:15:04,409 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2R3 (QComboBox)
2025-06-03 17:15:04,409 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2RDivider (QSpinBox)
2025-06-03 17:15:04,409 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2RclkMux (QComboBox)
2025-06-03 17:15:04,410 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2WINDSIZE (QComboBox)
2025-06-03 17:15:04,410 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RBFcalCAPMODE (QLineEdit)
2025-06-03 17:15:04,410 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RBPLL1DLD (QCheckBox)
2025-06-03 17:15:04,410 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RBPLL2DLD (QCheckBox)
2025-06-03 17:15:04,410 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RBPLL2VtuneADC (QLineEdit)
2025-06-03 17:15:04,410 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGCycleslipEn (QCheckBox)
2025-06-03 17:15:04,410 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGHOExitDacassist (QCheckBox)
2025-06-03 17:15:04,410 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGHOExitDacassistStep (QComboBox)
2025-06-03 17:15:04,410 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGHoFastEnterEn (QCheckBox)
2025-06-03 17:15:04,410 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGVtunedetRelativeEn (QCheckBox)
2025-06-03 17:15:04,410 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGZpsEn (QCheckBox)
2025-06-03 17:15:04,410 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: TrackEn (QCheckBox)
2025-06-03 17:15:04,411 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: VCOLdoPD (QCheckBox)
2025-06-03 17:15:04,411 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: VCOPD (QCheckBox)
2025-06-03 17:15:04,411 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: comboPLL1WindSize (QComboBox)
2025-06-03 17:15:04,411 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: comboVcoMode (QComboBox)
2025-06-03 17:15:04,411 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: label (QLabel)
2025-06-03 17:15:04,411 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: spinBoxPLL1DLDCNT (QSpinBox)
2025-06-03 17:15:04,411 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: spinBoxPLL2DLDCNT (QSpinBox)
2025-06-03 17:15:04,411 - ModernBaseHandler - [ModernBaseHandler.py:119] - INFO - UI中发现 69 个控件
2025-06-03 17:15:04,417 - test_utils - [test_utils.py:55] - ERROR - 频率计算测试失败: 'MockRegisterManager' object has no attribute 'get_widget_register_mapping'
2025-06-03 17:15:04,423 - ModernBaseHandler - [ModernBaseHandler.py:71] - WARNING - 连接RegisterUpdateBus时出错: wrapped C/C++ object of type RegisterUpdateBus has been deleted，继续初始化
2025-06-03 17:15:04,451 - ModernPLLHandler - [ModernPLLHandler.py:128] - INFO - 从RegisterUpdateBus获取时钟源配置: ClkIn1
2025-06-03 17:15:04,452 - ModernPLLHandler - [ModernPLLHandler.py:136] - INFO - 时钟源配置初始化完成: 频率={'ClkIn0': 122.88, 'ClkIn1': 122.88, 'ClkIn2': 122.88, 'ClkIn3': 0}, 分频={'ClkIn0': 120, 'ClkIn1': 120, 'ClkIn2': 120, 'ClkIn3': 1}
2025-06-03 17:15:04,452 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 comboPLL1WindSize 设置选项映射
2025-06-03 17:15:04,452 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 comboPLL1WindSize 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,453 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2WINDSIZE 设置选项映射
2025-06-03 17:15:04,453 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2WINDSIZE 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,454 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL1CPState 设置选项映射
2025-06-03 17:15:04,455 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL1CPState 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,455 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2CPState 设置选项映射
2025-06-03 17:15:04,456 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2CPState 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,456 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL1PFDPolarity 设置选项映射
2025-06-03 17:15:04,457 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL1PFDPolarity 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,457 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2PFDPolarity 设置选项映射
2025-06-03 17:15:04,458 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2PFDPolarity 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,458 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL1CPGain 设置选项映射
2025-06-03 17:15:04,458 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL1CPGain 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,459 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2CPGain 设置选项映射
2025-06-03 17:15:04,459 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2CPGain 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,461 - ModernPLLHandler - [ModernPLLHandler.py:311] - INFO - 已为 PLL2R3 设置特殊映射: [0, 1, 2, 4] -> ['2.4KOhm', '0.2KOhm', '0.5KOhm', '1.1KOhm']
2025-06-03 17:15:04,461 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2R3 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,461 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2C1 设置选项映射
2025-06-03 17:15:04,462 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2C1 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,462 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2C3 设置选项映射
2025-06-03 17:15:04,462 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2C3 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,462 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL1NclkMux 设置选项映射
2025-06-03 17:15:04,462 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL1NclkMux 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,463 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2NclkMux 设置选项映射
2025-06-03 17:15:04,463 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2NclkMux 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,463 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2RclkMux 设置选项映射
2025-06-03 17:15:04,463 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2RclkMux 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,463 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2Prescaler 设置选项映射
2025-06-03 17:15:04,463 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2Prescaler 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,464 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 FBMUX 设置选项映射
2025-06-03 17:15:04,464 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 FBMUX 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,464 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 comboVcoMode 设置选项映射
2025-06-03 17:15:04,464 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 comboVcoMode 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,465 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 Doubler 设置选项映射
2025-06-03 17:15:04,465 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 Doubler 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,465 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 Fin0InputType 设置选项映射
2025-06-03 17:15:04,465 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 Fin0InputType 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,465 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 FSMDIV 设置选项映射
2025-06-03 17:15:04,465 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 FSMDIV 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,466 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 DACClkMult 设置选项映射
2025-06-03 17:15:04,466 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 DACClkMult 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,466 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 HoldoverExitMode 设置选项映射
2025-06-03 17:15:04,466 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 HoldoverExitMode 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,466 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 RGHOExitDacassistStep 设置选项映射
2025-06-03 17:15:04,466 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 RGHOExitDacassistStep 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,467 - ModernPLLHandler - [ModernPLLHandler.py:321] - INFO - ComboBox控件初始化完成
2025-06-03 17:15:04,801 - ModernPLLHandler - [ModernPLLHandler.py:284] - INFO - UI默认值初始化完成
2025-06-03 17:15:04,802 - ModernPLLHandler - [ModernPLLHandler.py:409] - INFO - 已设置PLL2NDivider控件范围: 2-262143
2025-06-03 17:15:04,802 - ModernPLLHandler - [ModernPLLHandler.py:414] - INFO - 已设置PLL1RDividerSetting控件最大值: 16383
2025-06-03 17:15:04,802 - ModernPLLHandler - [ModernPLLHandler.py:629] - INFO - 已注册跨寄存器控件 PLL2NDivider, 关联寄存器: 0x76, 0x77
2025-06-03 17:15:04,803 - ModernPLLHandler - [ModernPLLHandler.py:554] - INFO - 已注册PLL2NDivider为跨寄存器控件
2025-06-03 17:15:04,803 - ModernPLLHandler - [ModernPLLHandler.py:688] - INFO - 已初始化PLL2NDivider控件，值为 12 (高2位=0, 低16位=0)
2025-06-03 17:15:04,803 - ModernPLLHandler - [ModernPLLHandler.py:629] - INFO - 已注册跨寄存器控件 PLL1RDividerSetting, 关联寄存器: 0x63
2025-06-03 17:15:04,803 - ModernPLLHandler - [ModernPLLHandler.py:573] - INFO - 已注册PLL1RDividerSetting为动态映射控件
2025-06-03 17:15:04,803 - ModernPLLHandler - [ModernPLLHandler.py:577] - INFO - 已设置PLL1RDividerSetting控件范围
2025-06-03 17:15:04,804 - ModernPLLHandler - [ModernPLLHandler.py:581] - INFO - 跨寄存器控件注册完成
2025-06-03 17:15:04,804 - ModernPLLHandler - [ModernPLLHandler.py:425] - INFO - 已连接 OSCinFreq textChanged 信号
2025-06-03 17:15:04,804 - ModernPLLHandler - [ModernPLLHandler.py:71] - INFO - PLL特定配置初始化完成
2025-06-03 17:15:04,804 - ModernBaseHandler - [ModernBaseHandler.py:86] - INFO - ModernBaseHandler._post_init 被调用
2025-06-03 17:15:04,804 - ModernBaseHandler - [ModernBaseHandler.py:88] - INFO - UI已设置，开始构建控件映射
2025-06-03 17:15:04,804 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DACClkCntr (QComboBox)
2025-06-03 17:15:04,804 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DACClkMult (QComboBox)
2025-06-03 17:15:04,804 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DACHighTrip (QSpinBox)
2025-06-03 17:15:04,804 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DACLowTrip (QSpinBox)
2025-06-03 17:15:04,804 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DACUpdateRate (QLineEdit)
2025-06-03 17:15:04,804 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: Div2 (QCheckBox)
2025-06-03 17:15:04,804 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: Doubler (QComboBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: ExternalVCXOFreq (QLineEdit)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FBMUX (QComboBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FBMuxEn (QCheckBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FCALEN (QCheckBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FCALM1 (QComboBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FCALM2 (QComboBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FSMDIV (QComboBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: Fin0Freq (QLineEdit)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: Fin0InputType (QComboBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: Fin0PD (QCheckBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FreFin (QLineEdit)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: HoldOverEn (QCheckBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: HoldOverPLL1Det (QCheckBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: HoldoverExitMode (QComboBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: LosExternalInput (QCheckBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: MANDAC (QSpinBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: ManDacEN (QCheckBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: OSCinFreq (QLineEdit)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: OSCinPD (QCheckBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1CPGain (QComboBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1CPState (QComboBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1NDivider (QSpinBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1NclkMux (QComboBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1PD (QCheckBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1PFDFreq (QLineEdit)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1PFDPolarity (QComboBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1RDividerSetting (QSpinBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1RRst (QCheckBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2C1 (QComboBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2C3 (QComboBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2CPGain (QComboBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2CPState (QComboBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2NDivider (QSpinBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2NclkMux (QComboBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2OpenLoop (QCheckBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2PD (QCheckBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2PFDFreq (QLineEdit)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2PFDPolarity (QComboBox)
2025-06-03 17:15:04,805 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2PrePD (QCheckBox)
2025-06-03 17:15:04,806 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2Prescaler (QComboBox)
2025-06-03 17:15:04,806 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2R3 (QComboBox)
2025-06-03 17:15:04,806 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2RDivider (QSpinBox)
2025-06-03 17:15:04,806 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2RclkMux (QComboBox)
2025-06-03 17:15:04,806 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2WINDSIZE (QComboBox)
2025-06-03 17:15:04,806 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RBFcalCAPMODE (QLineEdit)
2025-06-03 17:15:04,806 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RBPLL1DLD (QCheckBox)
2025-06-03 17:15:04,806 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RBPLL2DLD (QCheckBox)
2025-06-03 17:15:04,806 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RBPLL2VtuneADC (QLineEdit)
2025-06-03 17:15:04,806 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGCycleslipEn (QCheckBox)
2025-06-03 17:15:04,806 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGHOExitDacassist (QCheckBox)
2025-06-03 17:15:04,806 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGHOExitDacassistStep (QComboBox)
2025-06-03 17:15:04,806 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGHoFastEnterEn (QCheckBox)
2025-06-03 17:15:04,806 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGVtunedetRelativeEn (QCheckBox)
2025-06-03 17:15:04,806 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGZpsEn (QCheckBox)
2025-06-03 17:15:04,806 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: TrackEn (QCheckBox)
2025-06-03 17:15:04,806 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: VCOLdoPD (QCheckBox)
2025-06-03 17:15:04,806 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: VCOPD (QCheckBox)
2025-06-03 17:15:04,806 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: comboPLL1WindSize (QComboBox)
2025-06-03 17:15:04,806 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: comboVcoMode (QComboBox)
2025-06-03 17:15:04,806 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: label (QLabel)
2025-06-03 17:15:04,806 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: spinBoxPLL1DLDCNT (QSpinBox)
2025-06-03 17:15:04,806 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: spinBoxPLL2DLDCNT (QSpinBox)
2025-06-03 17:15:04,806 - ModernBaseHandler - [ModernBaseHandler.py:119] - INFO - UI中发现 69 个控件
2025-06-03 17:15:04,809 - test_utils - [test_utils.py:55] - ERROR - 现代化PLL处理器创建失败: 'MockRegisterManager' object has no attribute 'get_widget_register_mapping'
2025-06-03 17:15:04,818 - ModernBaseHandler - [ModernBaseHandler.py:71] - WARNING - 连接RegisterUpdateBus时出错: wrapped C/C++ object of type RegisterUpdateBus has been deleted，继续初始化
2025-06-03 17:15:04,847 - ModernPLLHandler - [ModernPLLHandler.py:128] - INFO - 从RegisterUpdateBus获取时钟源配置: ClkIn1
2025-06-03 17:15:04,847 - ModernPLLHandler - [ModernPLLHandler.py:136] - INFO - 时钟源配置初始化完成: 频率={'ClkIn0': 122.88, 'ClkIn1': 122.88, 'ClkIn2': 122.88, 'ClkIn3': 0}, 分频={'ClkIn0': 120, 'ClkIn1': 120, 'ClkIn2': 120, 'ClkIn3': 1}
2025-06-03 17:15:04,847 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 comboPLL1WindSize 设置选项映射
2025-06-03 17:15:04,848 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 comboPLL1WindSize 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,848 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2WINDSIZE 设置选项映射
2025-06-03 17:15:04,849 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2WINDSIZE 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,849 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL1CPState 设置选项映射
2025-06-03 17:15:04,849 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL1CPState 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,849 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2CPState 设置选项映射
2025-06-03 17:15:04,850 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2CPState 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,850 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL1PFDPolarity 设置选项映射
2025-06-03 17:15:04,850 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL1PFDPolarity 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,850 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2PFDPolarity 设置选项映射
2025-06-03 17:15:04,850 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2PFDPolarity 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,851 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL1CPGain 设置选项映射
2025-06-03 17:15:04,851 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL1CPGain 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,851 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2CPGain 设置选项映射
2025-06-03 17:15:04,851 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2CPGain 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,851 - ModernPLLHandler - [ModernPLLHandler.py:311] - INFO - 已为 PLL2R3 设置特殊映射: [0, 1, 2, 4] -> ['2.4KOhm', '0.2KOhm', '0.5KOhm', '1.1KOhm']
2025-06-03 17:15:04,851 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2R3 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,851 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2C1 设置选项映射
2025-06-03 17:15:04,852 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2C1 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,852 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2C3 设置选项映射
2025-06-03 17:15:04,852 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2C3 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,852 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL1NclkMux 设置选项映射
2025-06-03 17:15:04,852 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL1NclkMux 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,852 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2NclkMux 设置选项映射
2025-06-03 17:15:04,852 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2NclkMux 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,853 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2RclkMux 设置选项映射
2025-06-03 17:15:04,853 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2RclkMux 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,853 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2Prescaler 设置选项映射
2025-06-03 17:15:04,853 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2Prescaler 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,854 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 FBMUX 设置选项映射
2025-06-03 17:15:04,854 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 FBMUX 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,854 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 comboVcoMode 设置选项映射
2025-06-03 17:15:04,854 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 comboVcoMode 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,854 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 Doubler 设置选项映射
2025-06-03 17:15:04,854 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 Doubler 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,854 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 Fin0InputType 设置选项映射
2025-06-03 17:15:04,855 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 Fin0InputType 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,855 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 FSMDIV 设置选项映射
2025-06-03 17:15:04,855 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 FSMDIV 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,855 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 DACClkMult 设置选项映射
2025-06-03 17:15:04,855 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 DACClkMult 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,855 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 HoldoverExitMode 设置选项映射
2025-06-03 17:15:04,855 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 HoldoverExitMode 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,856 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 RGHOExitDacassistStep 设置选项映射
2025-06-03 17:15:04,856 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 RGHOExitDacassistStep 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:04,856 - ModernPLLHandler - [ModernPLLHandler.py:321] - INFO - ComboBox控件初始化完成
2025-06-03 17:15:05,247 - ModernPLLHandler - [ModernPLLHandler.py:284] - INFO - UI默认值初始化完成
2025-06-03 17:15:05,248 - ModernPLLHandler - [ModernPLLHandler.py:409] - INFO - 已设置PLL2NDivider控件范围: 2-262143
2025-06-03 17:15:05,248 - ModernPLLHandler - [ModernPLLHandler.py:414] - INFO - 已设置PLL1RDividerSetting控件最大值: 16383
2025-06-03 17:15:05,248 - ModernPLLHandler - [ModernPLLHandler.py:629] - INFO - 已注册跨寄存器控件 PLL2NDivider, 关联寄存器: 0x76, 0x77
2025-06-03 17:15:05,248 - ModernPLLHandler - [ModernPLLHandler.py:554] - INFO - 已注册PLL2NDivider为跨寄存器控件
2025-06-03 17:15:05,249 - ModernPLLHandler - [ModernPLLHandler.py:688] - INFO - 已初始化PLL2NDivider控件，值为 12 (高2位=0, 低16位=0)
2025-06-03 17:15:05,249 - ModernPLLHandler - [ModernPLLHandler.py:629] - INFO - 已注册跨寄存器控件 PLL1RDividerSetting, 关联寄存器: 0x63
2025-06-03 17:15:05,249 - ModernPLLHandler - [ModernPLLHandler.py:573] - INFO - 已注册PLL1RDividerSetting为动态映射控件
2025-06-03 17:15:05,249 - ModernPLLHandler - [ModernPLLHandler.py:577] - INFO - 已设置PLL1RDividerSetting控件范围
2025-06-03 17:15:05,250 - ModernPLLHandler - [ModernPLLHandler.py:581] - INFO - 跨寄存器控件注册完成
2025-06-03 17:15:05,250 - ModernPLLHandler - [ModernPLLHandler.py:425] - INFO - 已连接 OSCinFreq textChanged 信号
2025-06-03 17:15:05,250 - ModernPLLHandler - [ModernPLLHandler.py:71] - INFO - PLL特定配置初始化完成
2025-06-03 17:15:05,250 - ModernBaseHandler - [ModernBaseHandler.py:86] - INFO - ModernBaseHandler._post_init 被调用
2025-06-03 17:15:05,250 - ModernBaseHandler - [ModernBaseHandler.py:88] - INFO - UI已设置，开始构建控件映射
2025-06-03 17:15:05,250 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DACClkCntr (QComboBox)
2025-06-03 17:15:05,250 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DACClkMult (QComboBox)
2025-06-03 17:15:05,250 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DACHighTrip (QSpinBox)
2025-06-03 17:15:05,250 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DACLowTrip (QSpinBox)
2025-06-03 17:15:05,250 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DACUpdateRate (QLineEdit)
2025-06-03 17:15:05,250 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: Div2 (QCheckBox)
2025-06-03 17:15:05,250 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: Doubler (QComboBox)
2025-06-03 17:15:05,250 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: ExternalVCXOFreq (QLineEdit)
2025-06-03 17:15:05,250 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FBMUX (QComboBox)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FBMuxEn (QCheckBox)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FCALEN (QCheckBox)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FCALM1 (QComboBox)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FCALM2 (QComboBox)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FSMDIV (QComboBox)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: Fin0Freq (QLineEdit)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: Fin0InputType (QComboBox)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: Fin0PD (QCheckBox)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FreFin (QLineEdit)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: HoldOverEn (QCheckBox)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: HoldOverPLL1Det (QCheckBox)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: HoldoverExitMode (QComboBox)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: LosExternalInput (QCheckBox)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: MANDAC (QSpinBox)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: ManDacEN (QCheckBox)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: OSCinFreq (QLineEdit)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: OSCinPD (QCheckBox)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1CPGain (QComboBox)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1CPState (QComboBox)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1NDivider (QSpinBox)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1NclkMux (QComboBox)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1PD (QCheckBox)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1PFDFreq (QLineEdit)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1PFDPolarity (QComboBox)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1RDividerSetting (QSpinBox)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1RRst (QCheckBox)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2C1 (QComboBox)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2C3 (QComboBox)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2CPGain (QComboBox)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2CPState (QComboBox)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2NDivider (QSpinBox)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2NclkMux (QComboBox)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2OpenLoop (QCheckBox)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2PD (QCheckBox)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2PFDFreq (QLineEdit)
2025-06-03 17:15:05,251 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2PFDPolarity (QComboBox)
2025-06-03 17:15:05,252 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2PrePD (QCheckBox)
2025-06-03 17:15:05,252 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2Prescaler (QComboBox)
2025-06-03 17:15:05,252 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2R3 (QComboBox)
2025-06-03 17:15:05,252 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2RDivider (QSpinBox)
2025-06-03 17:15:05,252 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2RclkMux (QComboBox)
2025-06-03 17:15:05,252 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2WINDSIZE (QComboBox)
2025-06-03 17:15:05,252 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RBFcalCAPMODE (QLineEdit)
2025-06-03 17:15:05,252 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RBPLL1DLD (QCheckBox)
2025-06-03 17:15:05,252 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RBPLL2DLD (QCheckBox)
2025-06-03 17:15:05,252 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RBPLL2VtuneADC (QLineEdit)
2025-06-03 17:15:05,252 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGCycleslipEn (QCheckBox)
2025-06-03 17:15:05,252 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGHOExitDacassist (QCheckBox)
2025-06-03 17:15:05,252 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGHOExitDacassistStep (QComboBox)
2025-06-03 17:15:05,252 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGHoFastEnterEn (QCheckBox)
2025-06-03 17:15:05,252 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGVtunedetRelativeEn (QCheckBox)
2025-06-03 17:15:05,252 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGZpsEn (QCheckBox)
2025-06-03 17:15:05,252 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: TrackEn (QCheckBox)
2025-06-03 17:15:05,252 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: VCOLdoPD (QCheckBox)
2025-06-03 17:15:05,252 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: VCOPD (QCheckBox)
2025-06-03 17:15:05,252 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: comboPLL1WindSize (QComboBox)
2025-06-03 17:15:05,252 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: comboVcoMode (QComboBox)
2025-06-03 17:15:05,252 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: label (QLabel)
2025-06-03 17:15:05,252 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: spinBoxPLL1DLDCNT (QSpinBox)
2025-06-03 17:15:05,252 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: spinBoxPLL2DLDCNT (QSpinBox)
2025-06-03 17:15:05,252 - ModernBaseHandler - [ModernBaseHandler.py:119] - INFO - UI中发现 69 个控件
2025-06-03 17:15:05,254 - test_utils - [test_utils.py:55] - ERROR - PLL预设功能测试失败: 'MockRegisterManager' object has no attribute 'get_widget_register_mapping'
2025-06-03 17:15:05,260 - ModernBaseHandler - [ModernBaseHandler.py:71] - WARNING - 连接RegisterUpdateBus时出错: wrapped C/C++ object of type RegisterUpdateBus has been deleted，继续初始化
2025-06-03 17:15:05,290 - ModernPLLHandler - [ModernPLLHandler.py:128] - INFO - 从RegisterUpdateBus获取时钟源配置: ClkIn1
2025-06-03 17:15:05,290 - ModernPLLHandler - [ModernPLLHandler.py:136] - INFO - 时钟源配置初始化完成: 频率={'ClkIn0': 122.88, 'ClkIn1': 122.88, 'ClkIn2': 122.88, 'ClkIn3': 0}, 分频={'ClkIn0': 120, 'ClkIn1': 120, 'ClkIn2': 120, 'ClkIn3': 1}
2025-06-03 17:15:05,290 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 comboPLL1WindSize 设置选项映射
2025-06-03 17:15:05,291 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 comboPLL1WindSize 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,291 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2WINDSIZE 设置选项映射
2025-06-03 17:15:05,291 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2WINDSIZE 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,291 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL1CPState 设置选项映射
2025-06-03 17:15:05,291 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL1CPState 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,291 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2CPState 设置选项映射
2025-06-03 17:15:05,292 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2CPState 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,292 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL1PFDPolarity 设置选项映射
2025-06-03 17:15:05,292 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL1PFDPolarity 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,292 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2PFDPolarity 设置选项映射
2025-06-03 17:15:05,292 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2PFDPolarity 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,292 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL1CPGain 设置选项映射
2025-06-03 17:15:05,292 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL1CPGain 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,292 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2CPGain 设置选项映射
2025-06-03 17:15:05,293 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2CPGain 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,293 - ModernPLLHandler - [ModernPLLHandler.py:311] - INFO - 已为 PLL2R3 设置特殊映射: [0, 1, 2, 4] -> ['2.4KOhm', '0.2KOhm', '0.5KOhm', '1.1KOhm']
2025-06-03 17:15:05,293 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2R3 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,293 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2C1 设置选项映射
2025-06-03 17:15:05,293 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2C1 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,293 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2C3 设置选项映射
2025-06-03 17:15:05,293 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2C3 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,293 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL1NclkMux 设置选项映射
2025-06-03 17:15:05,293 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL1NclkMux 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,293 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2NclkMux 设置选项映射
2025-06-03 17:15:05,293 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2NclkMux 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,294 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2RclkMux 设置选项映射
2025-06-03 17:15:05,294 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2RclkMux 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,294 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2Prescaler 设置选项映射
2025-06-03 17:15:05,294 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2Prescaler 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,294 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 FBMUX 设置选项映射
2025-06-03 17:15:05,294 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 FBMUX 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,294 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 comboVcoMode 设置选项映射
2025-06-03 17:15:05,294 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 comboVcoMode 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,294 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 Doubler 设置选项映射
2025-06-03 17:15:05,294 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 Doubler 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,294 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 Fin0InputType 设置选项映射
2025-06-03 17:15:05,295 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 Fin0InputType 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,295 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 FSMDIV 设置选项映射
2025-06-03 17:15:05,295 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 FSMDIV 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,295 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 DACClkMult 设置选项映射
2025-06-03 17:15:05,295 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 DACClkMult 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,295 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 HoldoverExitMode 设置选项映射
2025-06-03 17:15:05,295 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 HoldoverExitMode 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,295 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 RGHOExitDacassistStep 设置选项映射
2025-06-03 17:15:05,295 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 RGHOExitDacassistStep 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,295 - ModernPLLHandler - [ModernPLLHandler.py:321] - INFO - ComboBox控件初始化完成
2025-06-03 17:15:05,644 - ModernPLLHandler - [ModernPLLHandler.py:284] - INFO - UI默认值初始化完成
2025-06-03 17:15:05,644 - ModernPLLHandler - [ModernPLLHandler.py:409] - INFO - 已设置PLL2NDivider控件范围: 2-262143
2025-06-03 17:15:05,644 - ModernPLLHandler - [ModernPLLHandler.py:414] - INFO - 已设置PLL1RDividerSetting控件最大值: 16383
2025-06-03 17:15:05,645 - ModernPLLHandler - [ModernPLLHandler.py:629] - INFO - 已注册跨寄存器控件 PLL2NDivider, 关联寄存器: 0x76, 0x77
2025-06-03 17:15:05,645 - ModernPLLHandler - [ModernPLLHandler.py:554] - INFO - 已注册PLL2NDivider为跨寄存器控件
2025-06-03 17:15:05,645 - ModernPLLHandler - [ModernPLLHandler.py:688] - INFO - 已初始化PLL2NDivider控件，值为 12 (高2位=0, 低16位=0)
2025-06-03 17:15:05,645 - ModernPLLHandler - [ModernPLLHandler.py:629] - INFO - 已注册跨寄存器控件 PLL1RDividerSetting, 关联寄存器: 0x63
2025-06-03 17:15:05,645 - ModernPLLHandler - [ModernPLLHandler.py:573] - INFO - 已注册PLL1RDividerSetting为动态映射控件
2025-06-03 17:15:05,645 - ModernPLLHandler - [ModernPLLHandler.py:577] - INFO - 已设置PLL1RDividerSetting控件范围
2025-06-03 17:15:05,646 - ModernPLLHandler - [ModernPLLHandler.py:581] - INFO - 跨寄存器控件注册完成
2025-06-03 17:15:05,646 - ModernPLLHandler - [ModernPLLHandler.py:425] - INFO - 已连接 OSCinFreq textChanged 信号
2025-06-03 17:15:05,646 - ModernPLLHandler - [ModernPLLHandler.py:71] - INFO - PLL特定配置初始化完成
2025-06-03 17:15:05,646 - ModernBaseHandler - [ModernBaseHandler.py:86] - INFO - ModernBaseHandler._post_init 被调用
2025-06-03 17:15:05,646 - ModernBaseHandler - [ModernBaseHandler.py:88] - INFO - UI已设置，开始构建控件映射
2025-06-03 17:15:05,646 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DACClkCntr (QComboBox)
2025-06-03 17:15:05,646 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DACClkMult (QComboBox)
2025-06-03 17:15:05,646 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DACHighTrip (QSpinBox)
2025-06-03 17:15:05,646 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DACLowTrip (QSpinBox)
2025-06-03 17:15:05,646 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DACUpdateRate (QLineEdit)
2025-06-03 17:15:05,646 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: Div2 (QCheckBox)
2025-06-03 17:15:05,646 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: Doubler (QComboBox)
2025-06-03 17:15:05,646 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: ExternalVCXOFreq (QLineEdit)
2025-06-03 17:15:05,646 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FBMUX (QComboBox)
2025-06-03 17:15:05,646 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FBMuxEn (QCheckBox)
2025-06-03 17:15:05,647 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FCALEN (QCheckBox)
2025-06-03 17:15:05,647 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FCALM1 (QComboBox)
2025-06-03 17:15:05,647 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FCALM2 (QComboBox)
2025-06-03 17:15:05,647 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FSMDIV (QComboBox)
2025-06-03 17:15:05,647 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: Fin0Freq (QLineEdit)
2025-06-03 17:15:05,647 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: Fin0InputType (QComboBox)
2025-06-03 17:15:05,647 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: Fin0PD (QCheckBox)
2025-06-03 17:15:05,647 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FreFin (QLineEdit)
2025-06-03 17:15:05,647 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: HoldOverEn (QCheckBox)
2025-06-03 17:15:05,647 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: HoldOverPLL1Det (QCheckBox)
2025-06-03 17:15:05,647 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: HoldoverExitMode (QComboBox)
2025-06-03 17:15:05,647 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: LosExternalInput (QCheckBox)
2025-06-03 17:15:05,647 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: MANDAC (QSpinBox)
2025-06-03 17:15:05,647 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: ManDacEN (QCheckBox)
2025-06-03 17:15:05,647 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: OSCinFreq (QLineEdit)
2025-06-03 17:15:05,647 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: OSCinPD (QCheckBox)
2025-06-03 17:15:05,647 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1CPGain (QComboBox)
2025-06-03 17:15:05,647 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1CPState (QComboBox)
2025-06-03 17:15:05,647 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1NDivider (QSpinBox)
2025-06-03 17:15:05,647 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1NclkMux (QComboBox)
2025-06-03 17:15:05,647 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1PD (QCheckBox)
2025-06-03 17:15:05,647 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1PFDFreq (QLineEdit)
2025-06-03 17:15:05,647 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1PFDPolarity (QComboBox)
2025-06-03 17:15:05,647 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1RDividerSetting (QSpinBox)
2025-06-03 17:15:05,647 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1RRst (QCheckBox)
2025-06-03 17:15:05,647 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2C1 (QComboBox)
2025-06-03 17:15:05,647 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2C3 (QComboBox)
2025-06-03 17:15:05,647 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2CPGain (QComboBox)
2025-06-03 17:15:05,647 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2CPState (QComboBox)
2025-06-03 17:15:05,647 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2NDivider (QSpinBox)
2025-06-03 17:15:05,647 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2NclkMux (QComboBox)
2025-06-03 17:15:05,647 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2OpenLoop (QCheckBox)
2025-06-03 17:15:05,648 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2PD (QCheckBox)
2025-06-03 17:15:05,648 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2PFDFreq (QLineEdit)
2025-06-03 17:15:05,648 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2PFDPolarity (QComboBox)
2025-06-03 17:15:05,648 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2PrePD (QCheckBox)
2025-06-03 17:15:05,648 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2Prescaler (QComboBox)
2025-06-03 17:15:05,648 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2R3 (QComboBox)
2025-06-03 17:15:05,648 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2RDivider (QSpinBox)
2025-06-03 17:15:05,648 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2RclkMux (QComboBox)
2025-06-03 17:15:05,648 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2WINDSIZE (QComboBox)
2025-06-03 17:15:05,648 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RBFcalCAPMODE (QLineEdit)
2025-06-03 17:15:05,648 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RBPLL1DLD (QCheckBox)
2025-06-03 17:15:05,648 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RBPLL2DLD (QCheckBox)
2025-06-03 17:15:05,648 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RBPLL2VtuneADC (QLineEdit)
2025-06-03 17:15:05,648 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGCycleslipEn (QCheckBox)
2025-06-03 17:15:05,648 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGHOExitDacassist (QCheckBox)
2025-06-03 17:15:05,648 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGHOExitDacassistStep (QComboBox)
2025-06-03 17:15:05,648 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGHoFastEnterEn (QCheckBox)
2025-06-03 17:15:05,648 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGVtunedetRelativeEn (QCheckBox)
2025-06-03 17:15:05,648 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGZpsEn (QCheckBox)
2025-06-03 17:15:05,648 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: TrackEn (QCheckBox)
2025-06-03 17:15:05,649 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: VCOLdoPD (QCheckBox)
2025-06-03 17:15:05,649 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: VCOPD (QCheckBox)
2025-06-03 17:15:05,649 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: comboPLL1WindSize (QComboBox)
2025-06-03 17:15:05,649 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: comboVcoMode (QComboBox)
2025-06-03 17:15:05,649 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: label (QLabel)
2025-06-03 17:15:05,649 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: spinBoxPLL1DLDCNT (QSpinBox)
2025-06-03 17:15:05,649 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: spinBoxPLL2DLDCNT (QSpinBox)
2025-06-03 17:15:05,649 - ModernBaseHandler - [ModernBaseHandler.py:119] - INFO - UI中发现 69 个控件
2025-06-03 17:15:05,652 - test_utils - [test_utils.py:55] - ERROR - PLL状态操作测试失败: 'MockRegisterManager' object has no attribute 'get_widget_register_mapping'
2025-06-03 17:15:05,657 - ModernBaseHandler - [ModernBaseHandler.py:71] - WARNING - 连接RegisterUpdateBus时出错: wrapped C/C++ object of type RegisterUpdateBus has been deleted，继续初始化
2025-06-03 17:15:05,683 - ModernPLLHandler - [ModernPLLHandler.py:128] - INFO - 从RegisterUpdateBus获取时钟源配置: ClkIn1
2025-06-03 17:15:05,683 - ModernPLLHandler - [ModernPLLHandler.py:136] - INFO - 时钟源配置初始化完成: 频率={'ClkIn0': 122.88, 'ClkIn1': 122.88, 'ClkIn2': 122.88, 'ClkIn3': 0}, 分频={'ClkIn0': 120, 'ClkIn1': 120, 'ClkIn2': 120, 'ClkIn3': 1}
2025-06-03 17:15:05,683 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 comboPLL1WindSize 设置选项映射
2025-06-03 17:15:05,683 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 comboPLL1WindSize 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,684 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2WINDSIZE 设置选项映射
2025-06-03 17:15:05,684 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2WINDSIZE 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,684 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL1CPState 设置选项映射
2025-06-03 17:15:05,684 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL1CPState 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,685 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2CPState 设置选项映射
2025-06-03 17:15:05,685 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2CPState 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,685 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL1PFDPolarity 设置选项映射
2025-06-03 17:15:05,685 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL1PFDPolarity 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,685 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2PFDPolarity 设置选项映射
2025-06-03 17:15:05,685 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2PFDPolarity 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,686 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL1CPGain 设置选项映射
2025-06-03 17:15:05,686 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL1CPGain 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,686 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2CPGain 设置选项映射
2025-06-03 17:15:05,686 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2CPGain 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,686 - ModernPLLHandler - [ModernPLLHandler.py:311] - INFO - 已为 PLL2R3 设置特殊映射: [0, 1, 2, 4] -> ['2.4KOhm', '0.2KOhm', '0.5KOhm', '1.1KOhm']
2025-06-03 17:15:05,687 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2R3 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,687 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2C1 设置选项映射
2025-06-03 17:15:05,687 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2C1 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,687 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2C3 设置选项映射
2025-06-03 17:15:05,687 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2C3 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,687 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL1NclkMux 设置选项映射
2025-06-03 17:15:05,687 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL1NclkMux 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,688 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2NclkMux 设置选项映射
2025-06-03 17:15:05,688 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2NclkMux 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,688 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2RclkMux 设置选项映射
2025-06-03 17:15:05,688 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2RclkMux 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,688 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 PLL2Prescaler 设置选项映射
2025-06-03 17:15:05,689 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 PLL2Prescaler 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,689 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 FBMUX 设置选项映射
2025-06-03 17:15:05,689 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 FBMUX 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,689 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 comboVcoMode 设置选项映射
2025-06-03 17:15:05,689 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 comboVcoMode 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,689 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 Doubler 设置选项映射
2025-06-03 17:15:05,689 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 Doubler 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,689 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 Fin0InputType 设置选项映射
2025-06-03 17:15:05,689 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 Fin0InputType 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,689 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 FSMDIV 设置选项映射
2025-06-03 17:15:05,689 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 FSMDIV 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,690 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 DACClkMult 设置选项映射
2025-06-03 17:15:05,690 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 DACClkMult 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,690 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 HoldoverExitMode 设置选项映射
2025-06-03 17:15:05,690 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 HoldoverExitMode 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,690 - ModernPLLHandler - [ModernPLLHandler.py:316] - INFO - 已为 RGHOExitDacassistStep 设置选项映射
2025-06-03 17:15:05,690 - ModernPLLHandler - [ModernPLLHandler.py:389] - WARNING - 控件 RGHOExitDacassistStep 未在 widget_register_map 中找到。设置索引0。
2025-06-03 17:15:05,690 - ModernPLLHandler - [ModernPLLHandler.py:321] - INFO - ComboBox控件初始化完成
2025-06-03 17:15:05,998 - ModernPLLHandler - [ModernPLLHandler.py:284] - INFO - UI默认值初始化完成
2025-06-03 17:15:05,999 - ModernPLLHandler - [ModernPLLHandler.py:409] - INFO - 已设置PLL2NDivider控件范围: 2-262143
2025-06-03 17:15:05,999 - ModernPLLHandler - [ModernPLLHandler.py:414] - INFO - 已设置PLL1RDividerSetting控件最大值: 16383
2025-06-03 17:15:06,000 - ModernPLLHandler - [ModernPLLHandler.py:629] - INFO - 已注册跨寄存器控件 PLL2NDivider, 关联寄存器: 0x76, 0x77
2025-06-03 17:15:06,000 - ModernPLLHandler - [ModernPLLHandler.py:554] - INFO - 已注册PLL2NDivider为跨寄存器控件
2025-06-03 17:15:06,000 - ModernPLLHandler - [ModernPLLHandler.py:688] - INFO - 已初始化PLL2NDivider控件，值为 12 (高2位=0, 低16位=0)
2025-06-03 17:15:06,000 - ModernPLLHandler - [ModernPLLHandler.py:629] - INFO - 已注册跨寄存器控件 PLL1RDividerSetting, 关联寄存器: 0x63
2025-06-03 17:15:06,000 - ModernPLLHandler - [ModernPLLHandler.py:573] - INFO - 已注册PLL1RDividerSetting为动态映射控件
2025-06-03 17:15:06,001 - ModernPLLHandler - [ModernPLLHandler.py:577] - INFO - 已设置PLL1RDividerSetting控件范围
2025-06-03 17:15:06,001 - ModernPLLHandler - [ModernPLLHandler.py:581] - INFO - 跨寄存器控件注册完成
2025-06-03 17:15:06,001 - ModernPLLHandler - [ModernPLLHandler.py:425] - INFO - 已连接 OSCinFreq textChanged 信号
2025-06-03 17:15:06,001 - ModernPLLHandler - [ModernPLLHandler.py:71] - INFO - PLL特定配置初始化完成
2025-06-03 17:15:06,001 - ModernBaseHandler - [ModernBaseHandler.py:86] - INFO - ModernBaseHandler._post_init 被调用
2025-06-03 17:15:06,002 - ModernBaseHandler - [ModernBaseHandler.py:88] - INFO - UI已设置，开始构建控件映射
2025-06-03 17:15:06,002 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DACClkCntr (QComboBox)
2025-06-03 17:15:06,002 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DACClkMult (QComboBox)
2025-06-03 17:15:06,002 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DACHighTrip (QSpinBox)
2025-06-03 17:15:06,002 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DACLowTrip (QSpinBox)
2025-06-03 17:15:06,002 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: DACUpdateRate (QLineEdit)
2025-06-03 17:15:06,002 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: Div2 (QCheckBox)
2025-06-03 17:15:06,002 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: Doubler (QComboBox)
2025-06-03 17:15:06,002 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: ExternalVCXOFreq (QLineEdit)
2025-06-03 17:15:06,002 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FBMUX (QComboBox)
2025-06-03 17:15:06,002 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FBMuxEn (QCheckBox)
2025-06-03 17:15:06,002 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FCALEN (QCheckBox)
2025-06-03 17:15:06,002 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FCALM1 (QComboBox)
2025-06-03 17:15:06,002 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FCALM2 (QComboBox)
2025-06-03 17:15:06,002 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FSMDIV (QComboBox)
2025-06-03 17:15:06,002 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: Fin0Freq (QLineEdit)
2025-06-03 17:15:06,002 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: Fin0InputType (QComboBox)
2025-06-03 17:15:06,002 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: Fin0PD (QCheckBox)
2025-06-03 17:15:06,002 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: FreFin (QLineEdit)
2025-06-03 17:15:06,002 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: HoldOverEn (QCheckBox)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: HoldOverPLL1Det (QCheckBox)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: HoldoverExitMode (QComboBox)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: LosExternalInput (QCheckBox)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: MANDAC (QSpinBox)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: ManDacEN (QCheckBox)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: OSCinFreq (QLineEdit)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: OSCinPD (QCheckBox)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1CPGain (QComboBox)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1CPState (QComboBox)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1NDivider (QSpinBox)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1NclkMux (QComboBox)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1PD (QCheckBox)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1PFDFreq (QLineEdit)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1PFDPolarity (QComboBox)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1RDividerSetting (QSpinBox)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL1RRst (QCheckBox)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2C1 (QComboBox)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2C3 (QComboBox)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2CPGain (QComboBox)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2CPState (QComboBox)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2NDivider (QSpinBox)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2NclkMux (QComboBox)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2OpenLoop (QCheckBox)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2PD (QCheckBox)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2PFDFreq (QLineEdit)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2PFDPolarity (QComboBox)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2PrePD (QCheckBox)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2Prescaler (QComboBox)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2R3 (QComboBox)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2RDivider (QSpinBox)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2RclkMux (QComboBox)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: PLL2WINDSIZE (QComboBox)
2025-06-03 17:15:06,003 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RBFcalCAPMODE (QLineEdit)
2025-06-03 17:15:06,004 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RBPLL1DLD (QCheckBox)
2025-06-03 17:15:06,004 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RBPLL2DLD (QCheckBox)
2025-06-03 17:15:06,004 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RBPLL2VtuneADC (QLineEdit)
2025-06-03 17:15:06,004 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGCycleslipEn (QCheckBox)
2025-06-03 17:15:06,004 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGHOExitDacassist (QCheckBox)
2025-06-03 17:15:06,004 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGHOExitDacassistStep (QComboBox)
2025-06-03 17:15:06,004 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGHoFastEnterEn (QCheckBox)
2025-06-03 17:15:06,004 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGVtunedetRelativeEn (QCheckBox)
2025-06-03 17:15:06,004 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: RGZpsEn (QCheckBox)
2025-06-03 17:15:06,004 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: TrackEn (QCheckBox)
2025-06-03 17:15:06,004 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: VCOLdoPD (QCheckBox)
2025-06-03 17:15:06,004 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: VCOPD (QCheckBox)
2025-06-03 17:15:06,004 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: comboPLL1WindSize (QComboBox)
2025-06-03 17:15:06,004 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: comboVcoMode (QComboBox)
2025-06-03 17:15:06,004 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: label (QLabel)
2025-06-03 17:15:06,004 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: spinBoxPLL1DLDCNT (QSpinBox)
2025-06-03 17:15:06,004 - ModernBaseHandler - [ModernBaseHandler.py:115] - DEBUG - 发现UI控件: spinBoxPLL2DLDCNT (QSpinBox)
2025-06-03 17:15:06,004 - ModernBaseHandler - [ModernBaseHandler.py:119] - INFO - UI中发现 69 个控件
2025-06-03 17:15:06,007 - test_utils - [test_utils.py:55] - ERROR - 控件值变化测试失败: 'MockRegisterManager' object has no attribute 'get_widget_register_mapping'
2025-06-03 17:15:06,010 - test_utils - [test_utils.py:55] - ERROR - ✗ test_pll_control 失败: None
2025-06-03 17:15:06,010 - test_utils - [test_utils.py:46] - INFO - 运行测试: test_pll_controls_only (分类: functional)
2025-06-03 17:15:06,017 - test_utils - [test_utils.py:55] - ERROR - ✗ test_pll_controls_only 失败: None
2025-06-03 17:15:06,017 - test_utils - [test_utils.py:46] - INFO - 运行测试: test_pll_functionality (分类: functional)
2025-06-03 17:15:06,020 - test_utils - [test_utils.py:46] - INFO - ✓ test_pll_functionality 通过 (0.00s)
2025-06-03 17:15:06,020 - test_utils - [test_utils.py:46] - INFO - 运行测试: test_sync_sysref_final (分类: functional)
2025-06-03 17:15:06,023 - test_utils - [test_utils.py:46] - INFO - ✓ test_sync_sysref_final 通过 (0.00s)
2025-06-03 17:15:06,023 - test_utils - [test_utils.py:46] - INFO - 运行测试: test_sync_sysref_fix (分类: functional)
2025-06-03 17:15:06,025 - test_utils - [test_utils.py:46] - INFO - ✓ test_sync_sysref_fix 通过 (0.00s)
2025-06-03 17:15:06,025 - test_utils - [test_utils.py:46] - INFO - 运行测试: test_table_auto_write (分类: functional)
2025-06-03 17:15:06,034 - ModernBaseHandler - [ModernBaseHandler.py:71] - WARNING - 连接RegisterUpdateBus时出错: wrapped C/C++ object of type RegisterUpdateBus has been deleted，继续初始化
2025-06-03 17:15:06,034 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:75] - INFO - 表格配置初始化完成
2025-06-03 17:15:06,367 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:131] - INFO - 位域表格创建完成
2025-06-03 17:15:06,368 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:100] - INFO - 表格UI创建完成
2025-06-03 17:15:06,368 - ModernBaseHandler - [ModernBaseHandler.py:86] - INFO - ModernBaseHandler._post_init 被调用
2025-06-03 17:15:06,368 - ModernBaseHandler - [ModernBaseHandler.py:93] - WARNING - UI未设置，跳过控件映射构建
2025-06-03 17:15:06,368 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:49] - INFO - 现代化寄存器表格处理器初始化完成
2025-06-03 17:15:06,368 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:761] - INFO - 创建现代化RegisterTableHandler测试实例成功
2025-06-03 17:15:06,368 - test_table_auto_write - [test_table_auto_write.py:60] - INFO - 现代化表格处理器创建成功
2025-06-03 17:15:06,527 - ModernBaseHandler - [ModernBaseHandler.py:86] - INFO - ModernBaseHandler._post_init 被调用
2025-06-03 17:15:06,527 - ModernBaseHandler - [ModernBaseHandler.py:93] - WARNING - UI未设置，跳过控件映射构建
2025-06-03 17:15:27,847 - test_table_auto_write - [test_table_auto_write.py:90] - INFO - === 显示寄存器 0x14 ===
2025-06-03 17:15:27,847 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:207] - INFO - ModernTableHandler: 开始显示位域信息 - 地址: 20, 值: 0x1234
2025-06-03 17:15:27,847 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:216] - INFO - ModernTableHandler: 设置当前寄存器 - 地址: 20, 值: 0x1234
2025-06-03 17:15:27,847 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:221] - INFO - ModernTableHandler: 获取到 6 个位域
2025-06-03 17:15:27,847 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:235] - DEBUG - ModernTableHandler: 断开表格信号连接
2025-06-03 17:15:27,848 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:239] - INFO - ModernTableHandler: 开始更新表格内容
2025-06-03 17:15:27,860 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:243] - DEBUG - ModernTableHandler: 重新连接表格信号
2025-06-03 17:15:27,860 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:250] - INFO - ModernTableHandler: 成功显示寄存器 20 的 6 个位域
2025-06-03 17:15:27,860 - test_table_auto_write - [test_table_auto_write.py:97] - INFO - 已显示寄存器 0x14 的位域，值: 0x1234
2025-06-03 17:15:34,432 - RegisterManager - [RegisterManager.py:80] - INFO - RegisterManager发送寄存器更新信号: 0x14, 值=1224
2025-06-03 17:15:34,433 - RegisterUpdateBus - [RegisterUpdateBus.py:303] - INFO - 寄存器总线发送更新: 地址=0x14, 值=0x1224
2025-06-03 17:15:34,433 - RegisterManager - [RegisterManager.py:85] - ERROR - 发送寄存器更新信号时出错: wrapped C/C++ object of type RegisterUpdateBus has been deleted
2025-06-03 17:15:34,434 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:584] - INFO - 位域变更: SCLK0_1_PD = 0 (寄存器 20 = 0x1224)
2025-06-03 17:15:34,434 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:588] - INFO - ModernTableHandler: 表格修改后立即写入寄存器 20 = 0x1224 到芯片
2025-06-03 17:15:34,435 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:622] - INFO - ModernTableHandler: 使用register_service写入寄存器 20 = 0x1224
2025-06-03 17:15:34,435 - test_table_auto_write - [test_table_auto_write.py:182] - INFO - 🚀 MockRegisterService: 写入寄存器 20 = 0x1224 (第1次写入)
2025-06-03 17:15:41,914 - RegisterManager - [RegisterManager.py:80] - INFO - RegisterManager发送寄存器更新信号: 0x14, 值=1234
2025-06-03 17:15:41,914 - RegisterUpdateBus - [RegisterUpdateBus.py:303] - INFO - 寄存器总线发送更新: 地址=0x14, 值=0x1234
2025-06-03 17:15:41,914 - RegisterManager - [RegisterManager.py:85] - ERROR - 发送寄存器更新信号时出错: wrapped C/C++ object of type RegisterUpdateBus has been deleted
2025-06-03 17:15:41,914 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:584] - INFO - 位域变更: SCLK0_1_PD = 1 (寄存器 20 = 0x1234)
2025-06-03 17:15:41,915 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:588] - INFO - ModernTableHandler: 表格修改后立即写入寄存器 20 = 0x1234 到芯片
2025-06-03 17:15:41,915 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:622] - INFO - ModernTableHandler: 使用register_service写入寄存器 20 = 0x1234
2025-06-03 17:15:41,915 - test_table_auto_write - [test_table_auto_write.py:182] - INFO - 🚀 MockRegisterService: 写入寄存器 20 = 0x1234 (第2次写入)
2025-06-08 10:39:54,429 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: test_service
2025-06-08 10:39:54,430 - DIContainer - [DIContainer.py:69] - INFO - 注册瞬态服务: transient_service
2025-06-08 10:39:54,430 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: dependent_service
2025-06-08 10:39:54,430 - DIContainer - [DIContainer.py:143] - INFO - 依赖注入容器已清空
2025-06-08 10:39:54,456 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: spi_service
2025-06-08 10:39:54,456 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: register_manager
2025-06-08 10:39:54,457 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: event_bus
2025-06-08 10:39:54,457 - DIContainer - [DIContainer.py:177] - INFO - 核心服务配置完成
2025-06-08 10:39:54,457 - DIContainer - [DIContainer.py:46] - INFO - 注册单例实例: main_window
2025-06-08 10:39:54,490 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: initialization_manager
2025-06-08 10:39:54,490 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: register_operation_manager
2025-06-08 10:39:54,490 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: display_manager
2025-06-08 10:39:54,490 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: event_coordinator
2025-06-08 10:39:54,491 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: tool_window_factory
2025-06-08 10:39:54,491 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: register_update_processor
2025-06-08 10:39:54,491 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: lifecycle_manager
2025-06-08 10:39:54,491 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: ui_utility_manager
2025-06-08 10:39:54,491 - DIContainer - [DIContainer.py:206] - INFO - UI管理器服务配置完成
2025-06-08 10:39:54,494 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: batch_manager
2025-06-08 10:39:54,495 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: batch_operation_controller
2025-06-08 10:39:54,495 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: spi_coordinator
2025-06-08 10:39:54,495 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: ui_event_handler
2025-06-08 10:39:54,495 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: tool_window_manager
2025-06-08 10:39:54,495 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: status_config_manager
2025-06-08 10:39:54,495 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: resource_utility_manager
2025-06-08 10:39:54,495 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: tab_window_manager
2025-06-08 10:39:54,495 - DIContainer - [DIContainer.py:235] - INFO - 额外管理器服务配置完成
2025-06-08 10:39:54,496 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: decorator_test_service
2025-06-08 10:39:54,497 - port_manager - [port_manager.py:211] - INFO - 已清理所有COM端口连接
