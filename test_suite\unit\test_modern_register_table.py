#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试现代化寄存器表格处理器
验证位域表格显示、编辑、验证功能
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_modern_register_table():
    """测试现代化寄存器表格处理器"""
    try:
        print("=" * 60)
        print("测试现代化寄存器表格处理器")
        print("=" * 60)
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 导入必要的模块
        from ui.handlers.ModernRegisterTableHandler import ModernRegisterTableHandler
        
        print("1. 创建现代化寄存器表格处理器...")
        try:
            handler = ModernRegisterTableHandler.create_for_testing()
            print("   ✓ 现代化寄存器表格处理器创建成功")
            print(f"   窗口标题: {handler.windowTitle()}")
            
            # 检查表格是否创建
            if handler.bit_field_table:
                print(f"   ✓ 位域表格创建成功")
                print(f"   表格列数: {handler.bit_field_table.columnCount()}")
                print(f"   表格列标题: {[handler.bit_field_table.horizontalHeaderItem(i).text() for i in range(handler.bit_field_table.columnCount())]}")
            else:
                print("   ❌ 位域表格创建失败")
                return False
                
        except Exception as e:
            print(f"   ❌ 创建处理器失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
        print("2. 测试表格配置...")
        try:
            config = handler.table_config
            print(f"   表格配置:")
            print(f"     列数: {config['column_count']}")
            print(f"     行高: {config['row_height']}")
            print(f"     可编辑列: {config['editable_column']}")
            print(f"     颜色配置: {len(config['colors'])} 种")
            print("   ✓ 表格配置正常")
            
        except Exception as e:
            print(f"   ❌ 表格配置测试失败: {str(e)}")
        
        print("3. 测试位域显示功能...")
        try:
            # 选择一个有位域定义的寄存器进行测试
            test_register = "0x02"  # powerDown寄存器
            test_value = 0x0000
            
            print(f"   测试寄存器: {test_register}, 值: 0x{test_value:04X}")
            
            # 显示位域
            handler.show_bit_fields(test_register, test_value)
            
            # 检查表格内容
            row_count = handler.bit_field_table.rowCount()
            print(f"   ✓ 显示了 {row_count} 个位域")
            
            if row_count > 0:
                # 检查第一行内容
                first_row_data = []
                for col in range(handler.bit_field_table.columnCount()):
                    item = handler.bit_field_table.item(0, col)
                    if item:
                        first_row_data.append(item.text())
                    else:
                        first_row_data.append("None")
                
                print(f"   第一行数据: {first_row_data}")
                
                # 检查可编辑字段
                editable_count = 0
                for row in range(row_count):
                    value_item = handler.bit_field_table.item(row, 2)  # 值列
                    if value_item and (value_item.flags() & 0x2):  # Qt.ItemIsEditable
                        editable_count += 1
                
                print(f"   ✓ 可编辑字段数: {editable_count}")
            
        except Exception as e:
            print(f"   ❌ 位域显示测试失败: {str(e)}")
            import traceback
            traceback.print_exc()
        
        print("4. 测试状态获取功能...")
        try:
            status = handler.get_current_status()
            print(f"   当前状态:")
            for key, value in status.items():
                print(f"     {key}: {value}")
            print("   ✓ 状态获取正常")
            
        except Exception as e:
            print(f"   ❌ 状态获取测试失败: {str(e)}")
        
        print("5. 测试位操作辅助方法...")
        try:
            # 测试位范围解析
            test_cases = [
                ("15-0", 16, 0x0000FFFF, 0),
                ("7-4", 4, 0x000000F0, 4),
                ("3", 1, 0x00000008, 3),
                ("1:0", 2, 0x00000003, 0)
            ]
            
            for bit_range, expected_width, expected_mask, expected_start in test_cases:
                width = handler._get_bit_width(bit_range, "0")
                mask = handler._get_bit_mask(bit_range)
                start = handler._get_bit_start(bit_range)
                
                print(f"   位范围 {bit_range}:")
                print(f"     位宽: {width} (期望: {expected_width}) {'✓' if width == expected_width else '❌'}")
                print(f"     掩码: 0x{mask:08X} (期望: 0x{expected_mask:08X}) {'✓' if mask == expected_mask else '❌'}")
                print(f"     起始: {start} (期望: {expected_start}) {'✓' if start == expected_start else '❌'}")
            
            print("   ✓ 位操作辅助方法测试完成")
            
        except Exception as e:
            print(f"   ❌ 位操作辅助方法测试失败: {str(e)}")
        
        print("6. 测试二进制输入验证...")
        try:
            # 测试二进制输入验证
            validation_tests = [
                ("1010", 4, True),
                ("101", 4, False),  # 长度不匹配
                ("10102", 4, False),  # 包含非二进制字符
                ("", 4, False),  # 空字符串
                ("0", 1, True),
                ("1", 1, True),
                ("2", 1, False)  # 非二进制字符
            ]
            
            for test_input, expected_length, expected_result in validation_tests:
                result = handler._validate_binary_input(test_input, expected_length)
                status = "✓" if result == expected_result else "❌"
                print(f"   输入 '{test_input}' (长度{expected_length}): {result} {status}")
            
            print("   ✓ 二进制输入验证测试完成")
            
        except Exception as e:
            print(f"   ❌ 二进制输入验证测试失败: {str(e)}")
        
        print("7. 测试寄存器值变化处理...")
        try:
            # 模拟寄存器值变化
            test_register = "0x02"
            old_value = 0x0000
            new_value = 0x0001
            
            print(f"   模拟寄存器 {test_register} 值从 0x{old_value:04X} 变为 0x{new_value:04X}")
            
            # 先显示旧值
            handler.show_bit_fields(test_register, old_value)
            old_row_count = handler.bit_field_table.rowCount()
            
            # 再显示新值
            handler.on_register_value_changed("test_widget", test_register, new_value)
            new_row_count = handler.bit_field_table.rowCount()
            
            print(f"   ✓ 表格更新成功 (行数: {old_row_count} -> {new_row_count})")
            
            # 检查当前值是否更新
            if handler.current_register_value == new_value:
                print(f"   ✓ 当前寄存器值已更新: 0x{handler.current_register_value:04X}")
            else:
                print(f"   ❌ 当前寄存器值未更新: 0x{handler.current_register_value:04X}")
            
        except Exception as e:
            print(f"   ❌ 寄存器值变化处理测试失败: {str(e)}")
        
        print("8. 测试错误处理...")
        try:
            # 测试无效寄存器地址
            print("   测试无效寄存器地址...")
            handler.show_bit_fields("0xFF", 0x0000)  # 不存在的寄存器
            
            # 检查是否显示错误信息
            if handler.bit_field_table.rowCount() == 1:
                error_item = handler.bit_field_table.item(0, 0)
                if error_item and "无法获取" in error_item.text():
                    print("   ✓ 正确显示了错误信息")
                else:
                    print("   ❌ 错误信息显示不正确")
            else:
                print("   ❌ 未正确处理无效寄存器")
            
        except Exception as e:
            print(f"   ❌ 错误处理测试失败: {str(e)}")
        
        print("9. 测试信号连接...")
        try:
            # 检查信号是否正确定义
            signals = ['bit_field_changed', 'table_updated']
            for signal_name in signals:
                if hasattr(handler, signal_name):
                    signal = getattr(handler, signal_name)
                    print(f"   ✓ 信号 {signal_name} 已定义: {type(signal)}")
                else:
                    print(f"   ❌ 信号 {signal_name} 未定义")
            
        except Exception as e:
            print(f"   ❌ 信号连接测试失败: {str(e)}")
        
        print("10. 生成测试报告...")
        try:
            final_status = handler.get_current_status()
            
            print(f"   最终测试报告:")
            print(f"     处理器类型: {type(handler).__name__}")
            print(f"     窗口标题: {handler.windowTitle()}")
            print(f"     当前寄存器: {final_status.get('current_register', 'None')}")
            print(f"     当前值: 0x{final_status.get('current_value', 0):04X}")
            print(f"     表格行数: {final_status.get('row_count', 0)}")
            print(f"     表格列数: {final_status.get('column_count', 0)}")
            print(f"     可编辑字段: {final_status.get('editable_fields', 0)}")
            
        except Exception as e:
            print(f"   ❌ 生成测试报告时出错: {str(e)}")
        
        # 显示窗口（可选）
        # handler.show()
        
        print("\n" + "=" * 60)
        print("🎉 现代化寄存器表格处理器测试完成！")
        print("=" * 60)
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_modern_register_table()
    sys.exit(0 if success else 1)
