#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
综合测试现代化工具窗口功能
测试所有现代化工具窗口的基本功能和交互
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_modern_tool_windows():
    """综合测试现代化工具窗口"""
    print("=" * 70)
    print("🧪 综合测试现代化工具窗口功能")
    print("=" * 70)
    
    # 创建QApplication实例
    app = QApplication(sys.argv)
    
    test_results = []
    
    try:
        # 1. 测试模拟的RegisterManager
        print("\n1. 创建模拟RegisterManager...")
        from core.services.register.RegisterManager import RegisterManager
        import json

        # 加载寄存器配置
        config_path = os.path.join('lib', 'register.json')
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                registers_config = json.load(f)
        else:
            # 创建最小的模拟配置
            registers_config = {
                "0x00": {"name": "Test Register", "bits": []},
                "0x01": {"name": "Test Register 2", "bits": []}
            }

        register_manager = RegisterManager(registers_config)
        
        print("   ✓ RegisterManager创建成功")
        test_results.append("RegisterManager创建成功")
        
        # 2. 测试现代化工厂
        print("\n2. 测试现代化工厂...")
        from ui.factories.ModernToolWindowFactory import ModernToolWindowFactory
        
        # 创建模拟主窗口
        class MockMainWindow:
            def __init__(self):
                self.register_manager = register_manager
                self.auto_write_mode = False
        
        mock_main_window = MockMainWindow()
        modern_factory = ModernToolWindowFactory(mock_main_window)
        
        print("   ✓ 现代化工厂创建成功")
        test_results.append("现代化工厂创建成功")
        
        # 3. 测试各个现代化工具窗口
        window_types = ['set_modes', 'clkin_control', 'pll_control', 'sync_sysref', 'clk_output']
        
        created_windows = {}
        
        for window_type in window_types:
            print(f"\n3.{window_types.index(window_type) + 1} 测试 {window_type} 窗口...")
            
            try:
                # 创建窗口
                window = modern_factory.create_window_by_type(window_type)
                
                if window:
                    created_windows[window_type] = window
                    print(f"   ✓ {window_type} 窗口创建成功")
                    print(f"   ✓ 窗口类型: {type(window).__name__}")
                    
                    # 测试窗口基本属性
                    if hasattr(window, 'register_manager'):
                        print(f"   ✓ 具有register_manager属性")
                    else:
                        print(f"   ⚠️  缺少register_manager属性")
                    
                    # 测试窗口标题
                    if hasattr(window, 'windowTitle'):
                        title = window.windowTitle()
                        print(f"   ✓ 窗口标题: {title}")
                    
                    # 测试UI组件
                    if hasattr(window, 'ui'):
                        print(f"   ✓ 具有UI组件")
                    elif hasattr(window, 'content_widget'):
                        print(f"   ✓ 具有content_widget")
                    
                    test_results.append(f"{window_type}窗口创建成功")
                    
                else:
                    print(f"   ❌ {window_type} 窗口创建失败")
                    test_results.append(f"{window_type}窗口创建失败")
                    
            except Exception as e:
                print(f"   ❌ {window_type} 窗口测试出错: {str(e)}")
                test_results.append(f"{window_type}窗口测试出错: {str(e)}")
        
        # 4. 测试传统工厂的现代化迁移
        print("\n4. 测试传统工厂的现代化迁移...")
        from ui.factories.ToolWindowFactory import ToolWindowFactory
        
        traditional_factory = ToolWindowFactory(mock_main_window)
        
        if traditional_factory.is_using_modern_factory():
            print("   ✅ 传统工厂已启用现代化工厂")
            test_results.append("传统工厂现代化迁移成功")
            
            # 测试通过传统工厂创建现代化窗口
            for window_type in ['set_modes', 'clkin_control']:
                try:
                    window = traditional_factory.create_window_by_type(window_type)
                    if window:
                        print(f"   ✓ 通过传统工厂创建 {window_type} 现代化窗口成功")
                    else:
                        print(f"   ❌ 通过传统工厂创建 {window_type} 窗口失败")
                except Exception as e:
                    print(f"   ❌ 通过传统工厂创建 {window_type} 窗口出错: {str(e)}")
        else:
            print("   ❌ 传统工厂未启用现代化工厂")
            test_results.append("传统工厂现代化迁移失败")
        
        # 5. 测试迁移状态
        print("\n5. 测试迁移状态...")
        migration_status = traditional_factory.get_migration_status()
        
        if migration_status:
            total_handlers = migration_status.get('total_handlers', 0)
            configured_modern = migration_status.get('configured_modern', 0)
            migration_progress = migration_status.get('migration_progress', 0.0)
            
            print(f"   总处理器数量: {total_handlers}")
            print(f"   现代化处理器数量: {configured_modern}")
            print(f"   迁移进度: {migration_progress:.1%}")
            
            if migration_progress >= 1.0:
                print("   ✅ 迁移完成度: 100%")
                test_results.append("迁移状态检查通过")
            else:
                print(f"   ⚠️  迁移完成度: {migration_progress:.1%}")
                test_results.append(f"迁移未完成，进度: {migration_progress:.1%}")
        
        # 6. 清理资源
        print("\n6. 清理测试资源...")
        for window_type, window in created_windows.items():
            try:
                if hasattr(window, 'close'):
                    window.close()
                print(f"   ✓ {window_type} 窗口已关闭")
            except Exception as e:
                print(f"   ⚠️  关闭 {window_type} 窗口时出错: {str(e)}")
        
        # 7. 总结测试结果
        print("\n" + "=" * 70)
        print("📊 综合测试结果总结")
        print("=" * 70)
        
        success_count = 0
        total_count = len(test_results)
        
        for i, result in enumerate(test_results, 1):
            if "成功" in result or "通过" in result:
                print(f"{i}. ✅ {result}")
                success_count += 1
            else:
                print(f"{i}. ❌ {result}")
        
        print(f"\n成功率: {success_count}/{total_count} ({success_count/total_count:.1%})")
        
        if success_count == total_count:
            print("\n🎉 所有现代化工具窗口功能测试通过！")
            return True
        else:
            print(f"\n⚠️  有 {total_count - success_count} 项测试未通过，请检查相关问题。")
            return False
            
    except Exception as e:
        print(f"\n❌ 综合测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        app.quit()

if __name__ == "__main__":
    success = test_modern_tool_windows()
    sys.exit(0 if success else 1)
