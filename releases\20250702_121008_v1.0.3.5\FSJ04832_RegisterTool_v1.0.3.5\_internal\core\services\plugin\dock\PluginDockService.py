#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
插件停靠服务
负责管理插件窗口的停靠、分离和标签页集成
"""

from PyQt5.QtCore import QObject, pyqtSignal, QTimer, Qt, QPoint
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout
from PyQt5.QtGui import QCursor
from utils.Log import get_module_logger
from utils.CursorUtils import force_restore_cursor

logger = get_module_logger(__name__)


class PluginDockService(QObject):
    """插件停靠服务"""

    # 信号定义
    window_docked = pyqtSignal(str)  # plugin_name
    window_undocked = pyqtSignal(str)  # plugin_name

    def __init__(self, main_window):
        """初始化插件停靠服务

        Args:
            main_window: 主窗口实例
        """
        super().__init__()
        self.main_window = main_window

        # 拖拽监控相关
        self._drag_monitors = {}  # 存储每个窗口的拖拽监控器
        
    def should_integrate_to_tab(self, plugin_name: str) -> bool:
        """检查插件是否应该集成到标签页中"""
        # 定义需要集成到标签页的插件列表
        tab_integrated_plugins = {
            'clkin_control_plugin',
            'pll_control_plugin', 
            'sync_sysref_plugin',
            'clk_output_plugin',
            'set_modes_plugin',
            '示例工具',
            'performance_monitor_plugin',
            'selective_register_plugin'
        }
        return plugin_name in tab_integrated_plugins

    def integrate_window_to_tab(self, window, plugin_name: str):
        """将插件窗口集成到标签页中"""
        try:
            if not hasattr(self.main_window, 'tools_tab_widget'):
                logger.warning("主窗口没有标签页容器，无法集成到标签页")
                return False

            tab_widget = self.main_window.tools_tab_widget
            display_name = self._get_plugin_display_name(plugin_name)

            # 检查是否已经存在相同的标签页
            for i in range(tab_widget.count()):
                if tab_widget.tabText(i) == display_name:
                    # 标签页已存在，激活它
                    tab_widget.setCurrentIndex(i)
                    logger.info(f"激活现有标签页: {display_name}")
                    return True

            # 确保窗口可见并正确初始化
            self._ensure_window_content_ready(window, plugin_name)

            # 创建容器来包装插件窗口
            container = QWidget()
            layout = QVBoxLayout(container)
            layout.setContentsMargins(0, 0, 0, 0)
            layout.addWidget(window)

            # 在容器上存储插件信息，用于标签页关闭时的清理
            container.plugin_name = plugin_name
            container.plugin_window = window

            # 确保容器和窗口都可见
            container.setVisible(True)
            window.setVisible(True)

            # 添加到标签页
            index = tab_widget.addTab(container, display_name)
            tab_widget.setCurrentIndex(index)

            # 确保标签页容器可见
            tab_widget.setVisible(True)
            tab_widget.show()

            # 强制更新布局和显示
            self._force_update_tab_content(container, window)

            logger.info(f"插件窗口已集成到标签页: {plugin_name} -> {display_name}")
            return True

        except Exception as e:
            logger.error(f"集成插件窗口到标签页失败 {plugin_name}: {str(e)}")
            return False

    def _ensure_window_content_ready(self, window, plugin_name: str):
        """确保窗口内容已准备好显示"""
        try:
            logger.debug(f"确保窗口内容准备就绪: {plugin_name}")

            # 确保窗口可见
            if not window.isVisible():
                window.setVisible(True)
                logger.debug(f"设置窗口可见: {plugin_name}")

            # 如果窗口有content_widget，确保它也可见
            if hasattr(window, 'content_widget'):
                content = window.content_widget
                if not content.isVisible():
                    content.setVisible(True)
                    logger.debug(f"设置内容控件可见: {plugin_name}")

                # 确保内容控件有合适的大小
                if content.size().width() <= 0 or content.size().height() <= 0:
                    # 获取最小尺寸或设置默认尺寸
                    min_size = content.minimumSize()
                    if min_size.width() > 0 and min_size.height() > 0:
                        content.resize(min_size)
                    else:
                        content.resize(800, 600)  # 默认尺寸
                    logger.debug(f"调整内容控件尺寸: {plugin_name}")

            # 如果窗口有UI，确保UI已正确设置
            if hasattr(window, 'ui'):
                logger.debug(f"窗口有UI属性: {plugin_name}")
                # 触发UI更新
                window.update()

            # 强制处理待处理的事件
            QApplication.processEvents()

        except Exception as e:
            logger.warning(f"确保窗口内容准备就绪时出错 {plugin_name}: {str(e)}")

    def _force_update_tab_content(self, container, window):
        """强制更新标签页内容显示"""
        try:
            # 更新容器
            container.updateGeometry()
            container.update()

            # 更新窗口
            window.updateGeometry()
            window.update()

            # 如果窗口有content_widget，也更新它
            if hasattr(window, 'content_widget'):
                content = window.content_widget
                content.updateGeometry()
                content.update()

            # 强制处理待处理的事件
            QApplication.processEvents()

            logger.debug("强制更新标签页内容完成")

        except Exception as e:
            logger.warning(f"强制更新标签页内容时出错: {str(e)}")

    def dock_floating_window(self, plugin_name: str):
        """将悬浮窗口停靠到标签页"""
        try:
            # 检查窗口是否已经在标签页中
            if self._is_window_already_docked(plugin_name):
                logger.info(f"插件窗口 {plugin_name} 已经在标签页中")
                return True

            # 获取插件窗口（从插件集成服务获取）
            plugin_integration_service = getattr(self.main_window, 'plugin_integration_service', None)
            if not plugin_integration_service:
                logger.error("无法获取插件集成服务")
                return False

            window_service = getattr(plugin_integration_service, 'window_service', None)
            if not window_service:
                logger.error("无法获取插件窗口服务")
                return False

            window = window_service.get_plugin_window(plugin_name)
            if not window:
                logger.warning(f"未找到插件窗口: {plugin_name}")
                return False

            # 隐藏悬浮窗口
            window.hide()

            # 集成到标签页
            success = self.integrate_window_to_tab(window, plugin_name)
            if success:
                self.window_docked.emit(plugin_name)
                logger.info(f"插件窗口已停靠: {plugin_name}")
            
            return success

        except Exception as e:
            logger.error(f"停靠插件窗口失败 {plugin_name}: {str(e)}")
            return False

    def undock_window_from_tab(self, plugin_name: str):
        """从标签页分离窗口"""
        try:
            if not hasattr(self.main_window, 'tools_tab_widget'):
                logger.warning("主窗口没有标签页容器")
                return False

            tab_widget = self.main_window.tools_tab_widget
            display_name = self._get_plugin_display_name(plugin_name)

            # 查找对应的标签页
            for i in range(tab_widget.count()):
                tab_text = tab_widget.tabText(i)
                if tab_text == display_name:
                    logger.info(f"找到插件 {plugin_name} 对应的标签页: {tab_text} (index: {i})")

                    # 获取容器和窗口
                    container = tab_widget.widget(i)
                    if not container:
                        logger.error(f"无法获取标签页容器: {plugin_name}")
                        return False

                    # 从容器中提取窗口
                    layout = container.layout()
                    if layout and layout.count() > 0:
                        window = layout.itemAt(0).widget()
                        if window:
                            # 从布局中移除窗口
                            layout.removeWidget(window)
                            
                            # 设置窗口为独立窗口
                            window.setParent(None)
                            
                            # 配置为悬浮窗口
                            self._configure_undocked_window(window, plugin_name)
                            
                            # 显示窗口
                            window.show()
                            window.raise_()
                            window.activateWindow()

                    # 移除标签页
                    tab_widget.removeTab(i)
                    logger.info(f"已移除插件 {plugin_name} 的标签页")

                    # 检查并隐藏空的标签页容器
                    self._check_and_hide_empty_tab_widget()

                    # 停止拖拽监控（如果存在）
                    self._stop_drag_monitor(plugin_name)

                    # 发送信号
                    self.window_undocked.emit(plugin_name)

                    logger.info(f"插件窗口已分离: {plugin_name}")
                    return True

            logger.warning(f"未找到插件 {plugin_name} 对应的标签页")
            return False

        except Exception as e:
            logger.error(f"分离插件窗口失败 {plugin_name}: {str(e)}")
            return False

    def toggle_window_docking(self, plugin_name: str):
        """切换窗口停靠状态"""
        try:
            if self._is_window_already_docked(plugin_name):
                # 当前在标签页中，分离它
                return self.undock_window_from_tab(plugin_name)
            else:
                # 当前是悬浮窗口，停靠它
                return self.dock_floating_window(plugin_name)
        except Exception as e:
            logger.error(f"切换窗口停靠状态失败 {plugin_name}: {str(e)}")
            return False

    def close_plugin_tab(self, plugin_name: str):
        """关闭插件对应的标签页"""
        try:
            if not hasattr(self.main_window, 'tools_tab_widget'):
                logger.debug(f"主窗口没有标签页容器，跳过标签页关闭: {plugin_name}")
                return

            tab_widget = self.main_window.tools_tab_widget
            display_name = self._get_plugin_display_name(plugin_name)

            # 查找对应的标签页
            for i in range(tab_widget.count()):
                tab_text = tab_widget.tabText(i)
                if tab_text == display_name:
                    logger.info(f"找到插件 {plugin_name} 对应的标签页: {tab_text} (index: {i})")

                    # 获取容器并清理资源
                    container = tab_widget.widget(i)
                    if container:
                        self._cleanup_container_resources(container, plugin_name)

                    # 移除标签页
                    tab_widget.removeTab(i)
                    logger.info(f"已移除插件 {plugin_name} 的标签页")

                    # 检查并隐藏空的标签页容器
                    self._check_and_hide_empty_tab_widget()

                    # 停止拖拽监控（如果存在）
                    self._stop_drag_monitor(plugin_name)

                    break
            else:
                logger.debug(f"未找到插件 {plugin_name} 对应的标签页")

        except Exception as e:
            logger.error(f"关闭插件标签页时出错 {plugin_name}: {str(e)}")

    def _is_window_already_docked(self, plugin_name: str):
        """检查窗口是否已经在标签页中停靠"""
        try:
            if not hasattr(self.main_window, 'tools_tab_widget'):
                return False

            display_name = self._get_plugin_display_name(plugin_name)
            tab_widget = self.main_window.tools_tab_widget

            for i in range(tab_widget.count()):
                if tab_widget.tabText(i) == display_name:
                    return True

            return False

        except Exception as e:
            logger.error(f"检查窗口停靠状态时出错 {plugin_name}: {str(e)}")
            return False

    def _get_plugin_display_name(self, plugin_name: str) -> str:
        """获取插件的显示名称"""
        display_name_mapping = {
            'clkin_control_plugin': '时钟输入控制',
            'pll_control_plugin': 'PLL控制',
            'sync_sysref_plugin': '同步系统参考',
            'clk_output_plugin': '时钟输出',
            'set_modes_plugin': '模式设置',
            '示例工具': '示例工具',
            'performance_monitor_plugin': '性能监控',
            'selective_register_plugin': '选择性寄存器'
        }
        return display_name_mapping.get(plugin_name, plugin_name)

    def _configure_undocked_window(self, window, plugin_name: str):
        """配置分离的窗口"""
        try:
            from PyQt5.QtCore import Qt

            # 设置窗口标志
            window.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint |
                                Qt.WindowMinimizeButtonHint | Qt.WindowMaximizeButtonHint)

            # 优化窗口大小
            self._optimize_undocked_window_size(window, plugin_name)

            logger.info(f"已配置分离窗口: {plugin_name}")

        except Exception as e:
            logger.error(f"配置分离窗口失败 {plugin_name}: {str(e)}")

    def _optimize_undocked_window_size(self, window, plugin_name: str):
        """优化分离窗口的大小"""
        try:
            # 获取屏幕信息
            screen = QApplication.primaryScreen()
            screen_geometry = screen.availableGeometry()
            screen_width = screen_geometry.width()
            screen_height = screen_geometry.height()

            # 获取当前窗口大小
            current_size = window.size()
            current_width = current_size.width()
            current_height = current_size.height()

            # 检查窗口是否过大
            is_too_wide = current_width > screen_width * 0.6
            is_too_tall = current_height > screen_height * 0.8

            if is_too_wide or is_too_tall:
                # 获取最优大小
                optimal_size = self._get_plugin_optimal_size(plugin_name)
                if optimal_size:
                    optimal_width, optimal_height = optimal_size
                else:
                    # 基于当前大小进行智能缩放
                    optimal_width = min(current_width, int(screen_width * 0.5)) if is_too_wide else current_width
                    optimal_height = min(current_height, int(screen_height * 0.7)) if is_too_tall else current_height

                # 应用新大小
                window.resize(optimal_width, optimal_height)
                logger.info(f"已调整分离窗口大小: {current_width}x{current_height} -> {optimal_width}x{optimal_height}")

        except Exception as e:
            logger.error(f"优化分离窗口大小失败 {plugin_name}: {str(e)}")

    def _get_plugin_optimal_size(self, plugin_name: str):
        """获取插件的最优大小"""
        optimal_size_mapping = {
            'clkin_control_plugin': (950, 750),
            'pll_control_plugin': (1200, 900),
            'sync_sysref_plugin': (950, 750),
            'clk_output_plugin': (1100, 1000),
            'set_modes_plugin': (950, 750),
            '示例工具': (650, 500),
            'performance_monitor_plugin': (800, 600),
            'selective_register_plugin': (900, 700)
        }
        return optimal_size_mapping.get(plugin_name, None)

    def _cleanup_container_resources(self, container, plugin_name):
        """清理容器资源"""
        try:
            if container and container.layout():
                layout = container.layout()
                
                # 清理布局中的所有控件
                while layout.count():
                    item = layout.takeAt(0)
                    if item.widget():
                        widget = item.widget()
                        widget.setParent(None)
                        logger.debug(f"已从容器中移除控件: {widget.__class__.__name__}")
                
                logger.debug(f"已清理插件容器资源: {plugin_name}")
                
        except Exception as e:
            logger.error(f"清理容器资源时出错 {plugin_name}: {str(e)}")

    def _check_and_hide_empty_tab_widget(self):
        """检查并隐藏空的标签页容器"""
        try:
            if hasattr(self.main_window, 'tools_tab_widget'):
                tab_widget = self.main_window.tools_tab_widget
                
                if tab_widget.count() == 0:
                    # 没有标签页了，隐藏标签页容器
                    tab_widget.setVisible(False)
                    logger.info("标签页容器已隐藏（无标签页）")
                    
                    # 通知主窗口调整布局
                    if hasattr(self.main_window, 'adjust_layout_after_tab_close'):
                        QTimer.singleShot(100, self.main_window.adjust_layout_after_tab_close)
                else:
                    # 确保标签页容器可见
                    if not tab_widget.isVisible():
                        tab_widget.setVisible(True)
                        logger.debug("标签页容器已显示")
                        
        except Exception as e:
            logger.error(f"检查标签页容器状态时出错: {str(e)}")

    def add_drag_dock_support(self, window, plugin_name: str):
        """为插件窗口添加拖拽停靠支持"""
        try:
            if not window:
                logger.warning(f"窗口为空，无法添加拖拽支持: {plugin_name}")
                return False

            # 检查是否已经添加过拖拽支持
            if hasattr(window, '_drag_dock_enabled'):
                logger.debug(f"窗口已有拖拽支持: {plugin_name}")
                return True

            # 标记已添加拖拽支持
            window._drag_dock_enabled = True

            # 启动拖拽监控
            self._start_drag_monitor(window, plugin_name)

            logger.info(f"已为插件窗口添加拖拽停靠支持: {plugin_name}")
            return True

        except Exception as e:
            logger.error(f"添加拖拽停靠支持失败 {plugin_name}: {str(e)}")
            return False

    def _start_drag_monitor(self, window, plugin_name: str):
        """启动拖拽监控"""
        try:
            # 创建监控定时器
            monitor_timer = QTimer()
            monitor_timer.timeout.connect(lambda: self._check_mouse_state(window, plugin_name))
            monitor_timer.start(50)  # 50ms检查一次

            # 存储监控器
            self._drag_monitors[plugin_name] = monitor_timer

            logger.debug(f"已启动拖拽监控: {plugin_name}")

        except Exception as e:
            logger.error(f"启动拖拽监控失败 {plugin_name}: {str(e)}")

    def _check_mouse_state(self, window, plugin_name: str):
        """检查鼠标状态（全局监控方法）"""
        try:
            # 检查窗口是否仍然存在和有效
            if not self._is_window_valid(window):
                self._stop_drag_monitor(plugin_name)
                return

            # 检查是否启用了拖拽停靠功能
            if not hasattr(window, '_drag_dock_enabled'):
                self._stop_drag_monitor(plugin_name)
                return

            # 获取当前鼠标位置和按钮状态
            current_pos = QCursor.pos()
            mouse_buttons = QApplication.mouseButtons()

            # 检查鼠标是否在窗口内
            try:
                window_rect = window.frameGeometry()
                if not window_rect.contains(current_pos):
                    return
            except RuntimeError as e:
                # Qt对象已被删除
                if "wrapped C/C++ object" in str(e):
                    logger.debug(f"检测到Qt对象已删除，停止监控: {plugin_name}")
                    self._stop_drag_monitor(plugin_name)
                    return
                else:
                    raise

            # 检查是否有按钮按下
            if mouse_buttons & Qt.LeftButton:
                if not hasattr(window, '_global_mouse_pressed'):
                    # 鼠标按下处理
                    window._global_mouse_pressed = True
                    window._global_drag_start = current_pos
                    logger.debug(f"🔧 [拖拽监控] 检测到鼠标按下 - {plugin_name}, 位置: {current_pos}")

                elif hasattr(window, '_global_drag_start'):
                    # 拖拽处理
                    distance = (current_pos - window._global_drag_start).manhattanLength()
                    if distance >= 10 and not getattr(window, '_global_dragging', False):
                        window._global_dragging = True
                        logger.info(f"🔧 [拖拽监控] 检测到拖拽开始 - {plugin_name}, 距离: {distance}")

                    if getattr(window, '_global_dragging', False):
                        self._handle_drag_move(window, current_pos, plugin_name)
            else:
                # 鼠标释放处理
                if hasattr(window, '_global_mouse_pressed'):
                    logger.info(f"🔧 [拖拽监控] 检测到鼠标释放 - {plugin_name}, 位置: {current_pos}")

                    if getattr(window, '_global_dragging', False):
                        self._handle_drag_release(window, current_pos, plugin_name)

                    # 清理状态
                    self._cleanup_global_mouse_state(window)

        except RuntimeError as e:
            # 处理Qt对象已删除的情况
            if "wrapped C/C++ object" in str(e):
                logger.debug(f"检测到Qt对象已删除，停止监控: {plugin_name}")
                self._stop_drag_monitor(plugin_name)
            else:
                logger.error(f"检查鼠标状态失败 {plugin_name}: {str(e)}")
        except Exception as e:
            logger.error(f"检查鼠标状态失败 {plugin_name}: {str(e)}")

    def _handle_drag_move(self, window, global_pos, plugin_name: str):
        """处理拖拽移动"""
        try:
            # 检查窗口是否仍然有效
            if not self._is_window_valid(window):
                self._stop_drag_monitor(plugin_name)
                return

            # 检查是否在停靠区域
            in_dock_area = self._is_in_dock_area(global_pos)

            if in_dock_area:
                # 提供视觉反馈
                if not hasattr(window, '_original_title'):
                    window._original_title = window.windowTitle()
                window.setWindowTitle(f"{window._original_title} - 释放鼠标停靠到主界面")

                # 改变鼠标光标
                QApplication.setOverrideCursor(Qt.PointingHandCursor)
            else:
                # 恢复原始状态
                if hasattr(window, '_original_title'):
                    window.setWindowTitle(window._original_title)
                # 强制恢复光标状态
                force_restore_cursor()

        except RuntimeError as e:
            # 处理Qt对象已删除的情况
            if "wrapped C/C++ object" in str(e):
                logger.debug(f"拖拽移动时检测到Qt对象已删除，停止监控: {plugin_name}")
                self._stop_drag_monitor(plugin_name)
            else:
                logger.error(f"处理拖拽移动失败 {plugin_name}: {str(e)}")
        except Exception as e:
            logger.error(f"处理拖拽移动失败 {plugin_name}: {str(e)}")

    def _handle_drag_release(self, window, global_pos, plugin_name: str):
        """处理拖拽释放"""
        try:
            # 检查窗口是否仍然有效
            if not self._is_window_valid(window):
                self._stop_drag_monitor(plugin_name)
                return

            # 检查是否在停靠区域释放
            in_dock_area = self._is_in_dock_area(global_pos)

            if in_dock_area:
                logger.info(f"✅ [拖拽停靠] 在停靠区域释放，停靠窗口: {plugin_name}")
                # 停靠窗口
                self.dock_floating_window(plugin_name)
            else:
                logger.debug(f"❌ [拖拽停靠] 不在停靠区域，保持悬浮: {plugin_name}")

            # 恢复窗口状态
            self._restore_window_state(window)

        except RuntimeError as e:
            # 处理Qt对象已删除的情况
            if "wrapped C/C++ object" in str(e):
                logger.debug(f"拖拽释放时检测到Qt对象已删除，停止监控: {plugin_name}")
                self._stop_drag_monitor(plugin_name)
            else:
                logger.error(f"处理拖拽释放失败 {plugin_name}: {str(e)}")
        except Exception as e:
            logger.error(f"处理拖拽释放失败 {plugin_name}: {str(e)}")

    def _is_in_dock_area(self, global_pos):
        """判断是否在停靠区域内"""
        try:
            if not self.main_window:
                return False

            # 获取主窗口的全局几何信息
            main_geometry = None

            # 方法1: 尝试使用frameGeometry()
            try:
                main_geometry = self.main_window.frameGeometry()
                if main_geometry.isEmpty() or main_geometry.width() <= 0 or main_geometry.height() <= 0:
                    main_geometry = None
            except Exception:
                main_geometry = None

            # 方法2: 使用geometry()并转换为全局坐标
            if main_geometry is None:
                try:
                    main_geometry = self.main_window.geometry()
                    main_global_pos = self.main_window.mapToGlobal(QPoint(0, 0))
                    main_geometry.moveTopLeft(main_global_pos)
                except Exception:
                    return False

            # 验证几何信息的有效性
            if (main_geometry.isEmpty() or
                main_geometry.width() <= 0 or
                main_geometry.height() <= 0):
                return False

            # 定义停靠区域：主窗口底部30%的区域
            dock_area_height = int(main_geometry.height() * 0.3)
            dock_area_top = main_geometry.top() + main_geometry.height() - dock_area_height
            dock_area_bottom = main_geometry.bottom()

            # 添加边距，避免边界误触发
            margin = 10
            dock_area_left = main_geometry.left() + margin
            dock_area_right = main_geometry.right() - margin
            dock_area_top += margin
            dock_area_bottom -= margin

            # 检查鼠标是否在停靠区域内
            is_in_area = (global_pos.x() >= dock_area_left and
                         global_pos.x() <= dock_area_right and
                         global_pos.y() >= dock_area_top and
                         global_pos.y() <= dock_area_bottom)

            return is_in_area

        except Exception as e:
            logger.error(f"检查停靠区域失败: {str(e)}")
            return False

    def _restore_window_state(self, window):
        """恢复窗口状态"""
        try:
            # 检查窗口是否仍然有效
            if not self._is_window_valid(window):
                return

            # 恢复窗口标题
            if hasattr(window, '_original_title'):
                window.setWindowTitle(window._original_title)
                delattr(window, '_original_title')

            # 强制恢复鼠标光标
            force_restore_cursor()

        except RuntimeError as e:
            # 处理Qt对象已删除的情况
            if "wrapped C/C++ object" in str(e):
                logger.debug("恢复窗口状态时检测到Qt对象已删除")
            else:
                logger.error(f"恢复窗口状态失败: {str(e)}")
        except Exception as e:
            logger.error(f"恢复窗口状态失败: {str(e)}")

    def _cleanup_global_mouse_state(self, window):
        """清理全局鼠标状态"""
        try:
            # 检查窗口是否仍然有效
            if not self._is_window_valid(window):
                return

            # 清理拖拽相关属性
            attrs_to_remove = ['_global_mouse_pressed', '_global_drag_start', '_global_dragging']
            for attr in attrs_to_remove:
                if hasattr(window, attr):
                    delattr(window, attr)

            # 恢复窗口状态
            self._restore_window_state(window)

        except RuntimeError as e:
            # 处理Qt对象已删除的情况
            if "wrapped C/C++ object" in str(e):
                logger.debug("清理全局鼠标状态时检测到Qt对象已删除")
            else:
                logger.error(f"清理全局鼠标状态失败: {str(e)}")
        except Exception as e:
            logger.error(f"清理全局鼠标状态失败: {str(e)}")

    def _is_window_valid(self, window):
        """检查窗口对象是否有效

        Args:
            window: 要检查的窗口对象

        Returns:
            bool: 窗口是否有效
        """
        if not window:
            return False

        try:
            # 尝试访问Qt对象的基本属性来检查是否已被删除
            _ = window.isVisible()
            return True
        except RuntimeError as e:
            # Qt对象已被删除
            if "wrapped C/C++ object" in str(e):
                return False
            else:
                # 其他运行时错误，重新抛出
                raise
        except Exception:
            # 其他异常，认为窗口无效
            return False

    def stop_drag_monitor(self, plugin_name: str):
        """停止指定插件的拖拽监控（公共方法）"""
        self._stop_drag_monitor(plugin_name)

    def _stop_drag_monitor(self, plugin_name: str):
        """停止拖拽监控"""
        try:
            if plugin_name in self._drag_monitors:
                monitor_timer = self._drag_monitors[plugin_name]
                monitor_timer.stop()
                monitor_timer.deleteLater()
                del self._drag_monitors[plugin_name]
                
                # 强制恢复光标状态
                force_restore_cursor()
                
                logger.debug(f"已停止拖拽监控: {plugin_name}")
        except Exception as e:
            logger.error(f"停止拖拽监控失败 {plugin_name}: {str(e)}")

    def cleanup(self):
        """清理资源"""
        try:
            # 停止所有拖拽监控
            for plugin_name in list(self._drag_monitors.keys()):
                self._stop_drag_monitor(plugin_name)

            logger.info("插件停靠服务清理完成")
        except Exception as e:
            logger.error(f"插件停靠服务清理失败: {str(e)}")
