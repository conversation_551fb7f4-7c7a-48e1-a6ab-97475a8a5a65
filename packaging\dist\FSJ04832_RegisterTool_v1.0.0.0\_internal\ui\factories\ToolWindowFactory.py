# -*- coding: utf-8 -*-

"""
工具窗口工厂
负责创建和管理各种工具窗口的统一工厂类
支持现代化处理器的渐进式迁移
"""

from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class ToolWindowFactory:
    """工具窗口工厂类

    职责：
    1. 统一创建各种工具窗口
    2. 管理窗口创建的通用逻辑
    3. 提供窗口创建的配置和回调机制
    4. 支持现代化处理器的渐进式迁移
    """

    def __init__(self, main_window):
        """初始化工具窗口工厂

        Args:
            main_window: 主窗口实例
        """
        self.main_window = main_window

        # 初始化现代化工厂
        self._init_modern_factory()

    def _init_modern_factory(self):
        """初始化现代化工厂"""
        try:
            from .ModernToolWindowFactory import ModernToolWindowFactory
            self.modern_factory = ModernToolWindowFactory(self.main_window)
            self.use_modern_factory = True
            logger.info("现代化工厂初始化成功")
        except Exception as e:
            logger.warning(f"现代化工厂初始化失败，将使用传统方法: {str(e)}")
            self.modern_factory = None
            self.use_modern_factory = False

    def create_set_modes_window(self):
        """创建模式设置窗口 - 已迁移到现代化工厂

        Returns:
            创建的窗口实例
        """
        # 优先使用现代化工厂
        if self.use_modern_factory and self.modern_factory:
            try:
                window = self.modern_factory.create_window_by_type('set_modes')
                if window:
                    logger.info("使用现代化工厂创建模式设置窗口")
                    return window
                else:
                    logger.warning("现代化工厂创建模式设置窗口返回None，尝试传统方法")
            except Exception as e:
                logger.error(f"现代化工厂创建模式设置窗口失败: {str(e)}，尝试传统方法")
                import traceback
                traceback.print_exc()

        # 回退到传统方法
        logger.info("使用传统方法创建模式设置窗口")
        from ui.handlers.SetModesHandler import SetModesHandler

        def post_init(window):
            """窗口创建后的初始化回调"""
            # 设置主窗口引用，以便自动写入功能能够访问主窗口
            window.main_window = self.main_window
            logger.info(f"已为模式设置窗口设置主窗口引用，自动写入模式: {getattr(self.main_window, 'auto_write_mode', False)}")

        # 使用通用窗口创建方法
        return self._create_window_in_tab(
            title="模式设置",
            window_attr="set_modes_window",
            window_class=SetModesHandler,
            action_attr="set_modes_action",
            post_init_callback=post_init,
            parent=None,  # 确保窗口可以独立使用
            registers=self.main_window.register_manager.register_objects
        )
        
    def create_clkin_control_window(self):
        """创建时钟输入控制窗口 - 已迁移到现代化工厂

        Returns:
            创建的窗口实例
        """
        # 优先使用现代化工厂
        if self.use_modern_factory and self.modern_factory:
            try:
                window = self.modern_factory.create_window_by_type('clkin_control')
                if window:
                    logger.info("使用现代化工厂创建时钟输入控制窗口")
                    return window
                else:
                    logger.warning("现代化工厂创建时钟输入控制窗口返回None，尝试传统方法")
            except Exception as e:
                logger.error(f"现代化工厂创建时钟输入控制窗口失败: {str(e)}，尝试传统方法")
                import traceback
                traceback.print_exc()

        # 回退到传统方法
        logger.info("使用传统方法创建时钟输入控制窗口")
        from ui.handlers.ClkinControlHandler import ClkinControlHandler

        def post_init(window):
            """窗口创建后的初始化回调"""
            # 设置主窗口引用，以便自动写入功能能够访问主窗口
            window.main_window = self.main_window
            logger.info(f"已为时钟输入控制窗口设置主窗口引用，自动写入模式: {getattr(self.main_window, 'auto_write_mode', False)}")

        # 使用通用窗口创建方法
        return self._create_window_in_tab(
            title="时钟输入控制",
            window_attr="clkin_control_window",
            window_class=ClkinControlHandler,
            action_attr="clkin_control_action",
            post_init_callback=post_init,
            parent=None,  # 确保窗口可以独立使用
            registers=self.main_window.register_manager.register_objects
        )
        
    def create_pll_control_window(self):
        """创建PLL控制窗口 - 已迁移到现代化工厂
        
        Returns:
            None - 此方法已废弃，请使用现代化工厂
        """
        logger.warning("create_pll_control_window已废弃，请使用现代化工厂")
        return None
        
    def create_sync_sysref_window(self):
        """创建同步系统参考窗口

        Returns:
            创建的窗口实例
        """
        # 传统处理器已移除，使用现代化版本
        from ui.handlers.ModernSyncSysRefHandler import ModernSyncSysRefHandler
        
        def post_init(window):
            """窗口创建后的初始化回调"""
            # 设置主窗口引用，以便自动写入功能能够访问主窗口
            window.main_window = self.main_window
            logger.info(f"已为同步系统参考窗口设置主窗口引用，自动写入模式: {getattr(self.main_window, 'auto_write_mode', False)}")

            # 如果时钟输出窗口已存在，连接两者
            if hasattr(self.main_window, 'clk_outputs_window') and self.main_window.clk_outputs_window:
                logger.info("设置时钟输出窗口与同步系统参考窗口的连接")
                self.main_window.clk_outputs_window.set_sync_sysref_handler(window)

        # 现代化处理器需要RegisterManager而不是register_objects
        # 使用通用窗口创建方法，但传递RegisterManager
        return self._create_window_in_tab(
            title="同步系统参考",
            window_attr="sync_sysref_window",
            window_class=ModernSyncSysRefHandler,
            action_attr="sync_sysref_action",
            post_init_callback=post_init,
            parent=None,  # 确保窗口可以独立使用
            register_manager=self.main_window.register_manager  # 现代化处理器需要RegisterManager
        )
        
    def create_clk_output_window(self):
        """创建时钟输出窗口

        Returns:
            创建的窗口实例
        """
        # 只使用现代化工厂，不再回退到旧处理器
        if self.use_modern_factory and self.modern_factory:
            try:
                window = self.modern_factory.create_window_by_type('clk_output')
                if window:
                    logger.info("使用现代化工厂创建时钟输出窗口")
                    return window
                else:
                    logger.error("现代化工厂创建时钟输出窗口返回None")
            except Exception as e:
                logger.error(f"现代化工厂创建时钟输出窗口失败: {str(e)}")
                import traceback
                traceback.print_exc()

        # 旧的时钟输出处理器已被移除，不再提供回退
        logger.error("时钟输出窗口创建失败：现代化工厂不可用或失败，且旧处理器已被移除")
        return None
        
    def _create_window_in_tab(self, title, window_attr, window_class, action_attr=None, post_init_callback=None, *args, **kwargs):
        """通用方法：在标签页中创建窗口
        
        Args:
            title: 标签页标题
            window_attr: 窗口对象的属性名
            window_class: 窗口类
            action_attr: 菜单动作的属性名(可选)
            post_init_callback: 窗口初始化后的回调(可选)
            *args, **kwargs: 传递给窗口构造函数的参数
            
        Returns:
            创建的窗口对象
        """
        # 委托给窗口管理服务
        if hasattr(self.main_window, 'window_service'):
            return self.main_window.window_service.create_window_in_tab(
                title, window_attr, window_class, action_attr, post_init_callback, *args, **kwargs
            )
        else:
            logger.error("窗口管理服务不可用")
            return None

    # 窗口配置映射
    WINDOW_CONFIGS = {
        'set_modes': {
            'title': '模式设置',
            'window_attr': 'set_modes_window',
            'action_attr': 'set_modes_action',
            'handler_class': 'ui.handlers.ModernSetModesHandler.ModernSetModesHandler'  # 已迁移到现代化版本
        },
        'clkin_control': {
            'title': '时钟输入控制',
            'window_attr': 'clkin_control_window',
            'action_attr': 'clkin_control_action',
            'handler_class': 'ui.handlers.ModernClkinControlHandler.ModernClkinControlHandler'  # 已迁移到现代化版本
        },
        'pll_control': {
            'title': 'PLL1 & PLL2 控制',
            'window_attr': 'pll_control_window',
            'action_attr': 'pll_control_action',
            'handler_class': 'ui.handlers.ModernPLLHandler.ModernPLLHandler'  # 已迁移到现代化版本
        },
        'sync_sysref': {
            'title': '同步系统参考',
            'window_attr': 'sync_sysref_window',
            'action_attr': 'sync_sysref_action',
            'handler_class': 'ui.handlers.ModernSyncSysRefHandler.ModernSyncSysRefHandler'  # 已迁移到现代化版本
        },
        'clk_output': {
            'title': '时钟输出配置',
            'window_attr': 'clk_output_window',
            'action_attr': 'clk_output_action',
            'handler_class': 'ui.handlers.ModernClkOutputsHandler.ModernClkOutputsHandler'  # 已迁移到现代化版本
        }
    }

    def create_window_by_type(self, window_type):
        """根据窗口类型创建窗口

        Args:
            window_type: 窗口类型（如 'set_modes', 'clkin_control' 等）

        Returns:
            创建的窗口实例，如果类型不支持则返回None
        """
        # 优先使用现代化工厂
        if self.use_modern_factory and self.modern_factory:
            try:
                window = self.modern_factory.create_window_by_type(window_type)
                if window:
                    logger.info(f"使用现代化工厂创建窗口: {window_type}")
                    return window
                else:
                    logger.warning(f"现代化工厂无法创建窗口: {window_type}，尝试传统方法")
            except Exception as e:
                logger.error(f"现代化工厂创建窗口 {window_type} 时出错: {str(e)}，尝试传统方法")

        # 回退到传统方法
        method_map = {
            'set_modes': self.create_set_modes_window,
            'clkin_control': self.create_clkin_control_window,
            # 'pll_control': self.create_pll_control_window,  # 已迁移到现代化工厂
            'sync_sysref': self.create_sync_sysref_window,
            'clk_output': self.create_clk_output_window
        }

        create_method = method_map.get(window_type)
        if create_method:
            try:
                window = create_method()
                logger.info(f"使用传统方法创建窗口: {window_type}")
                return window
            except Exception as e:
                logger.error(f"传统方法创建窗口 {window_type} 时出错: {str(e)}")
                return None
        else:
            logger.warning(f"不支持的窗口类型: {window_type}")
            return None

    # === 迁移管理方法 ===

    def get_migration_status(self):
        """获取迁移状态

        Returns:
            dict: 迁移状态信息
        """
        if self.modern_factory:
            return self.modern_factory.get_migration_status()
        else:
            return {
                'total_handlers': 5,
                'configured_modern': 0,
                'configured_legacy': 5,
                'active_windows': 0,
                'active_modern': 0,
                'active_legacy': 0,
                'migration_progress': 0.0,
                'active_status': {},
                'error': 'Modern factory not available'
            }

    def switch_handler_type(self, window_type, use_modern=True):
        """切换处理器类型

        Args:
            window_type: 窗口类型
            use_modern: 是否使用现代化版本

        Returns:
            bool: 是否成功切换
        """
        if self.modern_factory:
            return self.modern_factory.switch_handler_type(window_type, use_modern)
        else:
            logger.error("现代化工厂不可用，无法切换处理器类型")
            return False

    def is_using_modern_factory(self):
        """检查是否正在使用现代化工厂

        Returns:
            bool: 是否使用现代化工厂
        """
        return self.use_modern_factory and self.modern_factory is not None

    def get_supported_window_types(self):
        """获取支持的窗口类型列表

        Returns:
            list: 支持的窗口类型
        """
        if self.modern_factory:
            return list(self.modern_factory.HANDLER_CONFIGS.keys())
        else:
            return ['set_modes', 'clkin_control', 'pll_control', 'sync_sysref', 'clk_output']
