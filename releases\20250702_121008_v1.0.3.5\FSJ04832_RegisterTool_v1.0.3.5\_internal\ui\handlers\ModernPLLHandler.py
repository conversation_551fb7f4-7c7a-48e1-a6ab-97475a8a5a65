﻿#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
现代化的PLL处理器
使用ModernBaseHandler作为基类，重构自原PLLHandler
主要功能：PLL1和PLL2的配置管理、频率计算、时钟源管理
"""

from PyQt5 import QtCore
from ui.handlers.ModernBaseHandler import ModernBaseHandler
from ui.handlers.CrossRegisterWidgetManager import CrossRegisterWidgetManager, CrossRegisterWidgetConfig
from ui.forms.Ui_PLL1_2 import Ui_PLL1_2
from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class ModernPLLHandler(ModernBaseHandler):
    """现代化的PLL处理器"""

    # 添加窗口关闭信号
    window_closed = QtCore.pyqtSignal()

    # === 类常量配置 ===

    # 默认时钟源频率配置
    DEFAULT_CLOCK_FREQUENCIES = {
        "ClkIn0": 122.88,
        "ClkIn1": 245.76,
        "ClkIn2": 0,
        "ClkIn3": 0
    }

    # 默认时钟源分频配置
    DEFAULT_CLOCK_DIVIDERS = {
        "ClkIn0": 120,
        "ClkIn1": 120,
        "ClkIn2": 120,
        "ClkIn3": 120
    }

    # ComboBox选项映射配置
    COMBOBOX_OPTIONS_MAP = {
        "comboPLL1WindSize": {0: "4ns", 1: "9ns", 2: "19ns", 3: "43ns"},
        "PLL2WINDSIZE": {0: "Reserved", 1: "1ns", 2: "18ns", 3: "26ns"},
        "PLL1CPState": {0: "Active", 1: "Tristate"},
        "PLL2CPState": {0: "Active", 1: "Tristate"},
        "PLL1PFDPolarity": {0: "Negative", 1: "Positive"},
        "PLL2PFDPolarity": {0: "Negative", 1: "Positive"},
        "PLL1CPGain": {i: f"{50 + i*100}μA" for i in range(16)},
        "PLL2CPGain": {0: "Reserved", 1: "Reserved", 2: "1600uA", 3: "3200uA"},
        "PLL2R3": {0: "2.4KOhm", 1: "0.2KOhm", 2: "0.5KOhm", 4: "1.1KOhm"},
        "PLL2C1": {0: "10pF", 1: "20pF", 2: "40pF"},
        "PLL2C3": {0: "10pF", 1: "20pF", 2: "40pF"},
        "PLL1NclkMux": {0: "OSCin", 1: "Feedback Mux", 2: "PLL2 Prescaler"},
        "PLL2NclkMux": {0: "PLL2 Prescaler", 1: "Feedback Mux"},
        "PLL2RclkMux": {0: "OSCin", 1: "PLL1 CLKinX"},
        "PLL2Prescaler": {i: str(i) if i == 0 else str(i) for i in range(8)},
        "FBMUX": {0: "CLKout6", 1: "CLKout8", 2: "SYSREF Divider", 4: "External"},
        "comboVcoMode": {0: "VCO 0", 1: "VCO 1", 2: "CLKin1", 3: "Fin0"},
        "Doubler": {0: "1×", 1: "2×"},
        "Fin0InputType": {0: "Diff Input", 1: "Single Ended Input(Fin)",
                         2: "Single Ended Input(Fin*)", 3: "Reserved"},
        "OSCin_FREQ": {0: "0-63MHz,not valid", 1: "div1 -> 63-127MHz",
                      2: "div2 -> 127-255MHz", 3: "div3 -> Reserved",
                      4: "div4 -> 255-500MHz", 5: "div5 -> Reserved",
                      6: "div6 -> Reserved", 7: "div7 -> Reserved"},
        "DACClkMult": {0: "4", 1: "64", 2: "1024", 3: "16384"},
        "RGHOExitDacassistStep": {0: "slowest", 1: "slow", 2: "fast", 3: "fastest"},
        "FCALM1": {i: str(i) for i in range(256)},
        "FCALM2": {i: str(i) for i in range(16384)}
    }

    # 特殊ComboBox映射（PLL2R3的值映射）
    PLL2R3_VALUE_MAP = [0, 1, 2, 4]  # 实际寄存器值

    # 已知控件的默认值映射
    KNOWN_WIDGET_DEFAULTS = {
        "comboPLL1WindSize": 3,      # 寄存器0x6B位7:6，默认值"11"=3
        "PLL2WINDSIZE": 2,           # 寄存器0x79位6:5，默认值"10"=2
        "PLL1CPState": 0,            # 寄存器0x6B位5，默认值"0"=0
        "PLL2CPState": 0,            # 假设默认值为0
        "PLL1PFDPolarity": 1,        # 寄存器0x6B位4，默认值"1"=1
        "PLL2PFDPolarity": 0,        # 假设默认值为0
        "PLL1CPGain": 0,             # 假设默认值为0
        "PLL2CPGain": 2,             # 假设默认值为2
        "PLL1NclkMux": 0,            # 假设默认值为0
        "PLL2NclkMux": 0,            # 假设默认值为0
        "PLL2RclkMux": 0,            # 假设默认值为0
        "PLL2Prescaler": 0,          # 假设默认值为0
        "FBMUX": 0,                  # 假设默认值为0
        "comboVcoMode": 0,           # 假设默认值为0
        "Doubler": 0,                # 假设默认值为0
        "Fin0InputType": 0,          # 假设默认值为0
        "OSCinFreq": 0,              # 假设默认值为0
        "DACClkMult": 0,             # 假设默认值为0
        "RGHOExitDacassistStep": 0,  # 假设默认值为0
    }

    def __init__(self, parent=None, register_manager=None, main_window=None, **kwargs):
        """初始化现代化PLL处理器

        Args:
            parent: 父窗口
            register_manager: RegisterManager实例
            main_window: 主窗口引用（用于避免控件变化时的引用问题）
            **kwargs: 其他参数（如register_repo等，用于兼容性）
        """
        super().__init__(parent, register_manager, **kwargs)

        # 立即设置主窗口引用（在任何可能触发控件变化的操作之前）
        if main_window:
            self.main_window = main_window
            logger.info(f"ModernPLLHandler: 已在构造函数中设置主窗口引用: {type(main_window)}")

        # 设置窗口标题
        self.setWindowTitle("PLL配置 (现代化版本)")

        # 创建UI实例
        self.ui = Ui_PLL1_2()
        self.ui.setupUi(self.content_widget)

        # 初始化PLL特定配置
        self._init_pll_config()

        # 在所有初始化完成后，进行初始频率计算
        QtCore.QTimer.singleShot(100, self._perform_initial_calculations)

        # 确保内容完全可见
        QtCore.QTimer.singleShot(200, self.ensure_content_fully_visible)

        logger.info("现代化PLL处理器初始化完成")



    def _init_pll_config(self):
        """初始化PLL特定配置"""
        try:
            # 初始化跨寄存器控件管理器
            self.cross_register_manager = CrossRegisterWidgetManager(self.register_manager)
            self.cross_register_manager.set_ui(self.ui)

            # 初始化时钟源配置（参考传统PLL处理器）
            self._init_clock_source_config()

            # 设置当前时钟源
            self.current_clock_source = "ClkIn0"

            # 初始化UI默认值
            self._init_ui_defaults()

            # 设置控件范围
            self._setup_widget_ranges()

            # 注册跨寄存器控件（参考传统PLL处理器）
            self._register_cross_register_controls()

            # 连接特殊信号
            self._connect_special_signals()

            logger.info("PLL特定配置初始化完成")

        except Exception as e:
            logger.error(f"初始化PLL配置时出错: {str(e)}")

    def _init_clock_source_config(self):
        """初始化时钟源配置（参考传统PLL处理器）"""
        try:
            # 使用类常量初始化默认配置
            self.clkin_frequencies = self.DEFAULT_CLOCK_FREQUENCIES.copy()
            self.clkin_divider_values = self.DEFAULT_CLOCK_DIVIDERS.copy()

            # 尝试从RegisterUpdateBus获取时钟源配置
            try:
                clock_bus = RegisterUpdateBus.instance()
                self._load_clock_config_from_bus(clock_bus)
                logger.info(f"从RegisterUpdateBus获取时钟源配置: {self.current_clock_source}")

            except Exception as e:
                logger.warning(f"无法从RegisterUpdateBus获取时钟源配置，使用默认值: {str(e)}")

            logger.info(f"时钟源配置初始化完成: 频率={self.clkin_frequencies}, 分频={self.clkin_divider_values}")

        except Exception as e:
            logger.error(f"初始化时钟源配置时出错: {str(e)}")
            # 使用最基本的默认配置
            self.clkin_frequencies = self.DEFAULT_CLOCK_FREQUENCIES.copy()
            self.clkin_divider_values = self.DEFAULT_CLOCK_DIVIDERS.copy()

    def _load_clock_config_from_bus(self, clock_bus):
        """从RegisterUpdateBus加载时钟源配置"""
        # 获取RegisterUpdateBus中存储的时钟源配置
        for source in ["ClkIn0", "ClkIn1", "ClkIn2", "ClkIn3"]:
            freq = clock_bus.get_clock_frequency(source)
            divider = clock_bus.get_clock_divider(source)

            # 只有在有配置的情况下才更新
            if freq > 0:
                self.clkin_frequencies[source] = freq
            if divider > 0:
                self.clkin_divider_values[source] = divider

        # 获取当前时钟源
        current_source = clock_bus.get_current_clock_source()
        if current_source:
            self.current_clock_source = self._normalize_clock_source_name(current_source)

    def _normalize_clock_source_name(self, source_name):
        """规范化时钟源名称"""
        if source_name.startswith("CLK"):
            return "ClkIn" + source_name[5:]
        return source_name

    # === 通用工具方法 ===

    def _get_register_value_safely(self, widget_name, reg_addr, bit_def_name):
        """安全地从RegisterManager获取寄存器值

        Args:
            widget_name: 控件名称
            reg_addr: 寄存器地址
            bit_def_name: 位字段名称

        Returns:
            tuple: (value, success) - 值和是否成功获取
        """
        if not self.register_manager or not reg_addr or not bit_def_name:
            return None, False

        try:
            value = self.register_manager.get_bit_field_value(reg_addr, bit_def_name)
            if value is not None:
                logger.info(f"从RegisterManager获取 {widget_name} 值: {value} (reg={reg_addr}, bit='{bit_def_name}')")
                return value, True
            else:
                logger.info(f"RegisterManager中未找到 {widget_name} ({reg_addr}[{bit_def_name}]) 的值")
                return None, False
        except Exception as e:
            logger.warning(f"从RegisterManager获取 {widget_name} ({reg_addr}[{bit_def_name}]) 值失败: {e}")
            return None, False

    def _clamp_value_to_range(self, value, min_val, max_val, widget_name=""):
        """将值限制在指定范围内

        Args:
            value: 要检查的值
            min_val: 最小值
            max_val: 最大值
            widget_name: 控件名称（用于日志）

        Returns:
            tuple: (clamped_value, was_clamped) - 限制后的值和是否被限制
        """
        if min_val <= value <= max_val:
            return value, False

        clamped_value = max(min_val, min(max_val, value))
        logger.warning(f"{widget_name} 值 {value} 超出范围 [{min_val}-{max_val}]，修正为 {clamped_value}")
        return clamped_value, True

    def _parse_default_value(self, default_value_str):
        """解析默认值字符串

        Args:
            default_value_str: 默认值字符串

        Returns:
            tuple: (parsed_value, success) - 解析后的值和是否成功
        """
        if not default_value_str:
            return 0, False

        default_value_str = default_value_str.strip()

        # 尝试解析二进制
        if all(c in '01' for c in default_value_str):
            try:
                return int(default_value_str, 2), True
            except ValueError:
                pass

        # 尝试解析十进制
        if default_value_str.isdigit():
            try:
                return int(default_value_str), True
            except ValueError:
                pass

        return 0, False

    def _init_ui_defaults(self):
        """初始化UI默认值"""
        try:
            # 使用类常量配置ComboBox选项映射
            self.combobox_options_map = self.COMBOBOX_OPTIONS_MAP.copy()

            # 初始化ComboBox控件
            self._init_combobox_controls()

            # 设置默认频率值
            self._set_default_frequency_values()

            logger.info("UI默认值初始化完成")

        except Exception as e:
            logger.error(f"初始化UI默认值时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _set_default_frequency_values(self):
        """设置默认频率值"""
        frequency_widgets = {
            "OSCinFreq": "122.88",
            "ExternalVCXOFreq": "122.88",
            "FreFin": str(self.clkin_frequencies[self.current_clock_source])
        }

        for widget_name, default_value in frequency_widgets.items():
            if hasattr(self.ui, widget_name):
                getattr(self.ui, widget_name).setText(default_value)

    def _init_combobox_controls(self):
        """初始化ComboBox控件"""
        try:
            from PyQt5.QtWidgets import QComboBox

            # 为所有ComboBox控件设置选项
            for widget_name, options in self.combobox_options_map.items():
                if hasattr(self.ui, widget_name):
                    combo_box = getattr(self.ui, widget_name)
                    if isinstance(combo_box, QComboBox):
                        self._setup_combobox_options(combo_box, widget_name, options)
                        self._set_combobox_default_value(combo_box, widget_name)

            logger.info("ComboBox控件初始化完成")

            # 应用之前延迟设置的ComboBox值
            if hasattr(self, 'apply_pending_combobox_values'):
                self.apply_pending_combobox_values()

        except Exception as e:
            logger.error(f"初始化ComboBox控件时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _setup_combobox_options(self, combo_box, widget_name, options):
        """设置ComboBox选项"""
        combo_box.clear()

        # 特殊处理PLL2R3控件
        if widget_name == "PLL2R3":
            texts = ["2.4KOhm", "0.2KOhm", "0.5KOhm", "1.1KOhm"]
            for value, text in zip(self.PLL2R3_VALUE_MAP, texts):
                combo_box.addItem(text, value)
            logger.info(f"已为 {widget_name} 设置特殊映射: {self.PLL2R3_VALUE_MAP} -> {texts}")
        else:
            # 常规ComboBox处理
            for value, text in sorted(options.items()):
                combo_box.addItem(text, value)
            logger.info(f"已为 {widget_name} 设置选项映射")

    def _set_combobox_default_value(self, combo_box, widget_name):
        """设置ComboBox的默认值，优先从RegisterManager获取"""
        try:
            default_index = 0
            widget_info = self.widget_register_map.get(widget_name)

            # 1. 尝试从RegisterManager获取值
            reg_val, success = self._try_get_register_value(widget_info, widget_name)
            if success:
                default_index = self._find_combobox_index_by_value(combo_box, reg_val, widget_name)
                if default_index != -1:
                    logger.info(f"为 {widget_name} 从RegisterManager设置索引: {default_index}")
                else:
                    default_index = self._get_fallback_default_index(widget_name, widget_info)
            else:
                default_index = self._get_fallback_default_index(widget_name, widget_info)

            # 设置ComboBox值
            self._apply_combobox_index(combo_box, default_index, widget_name)

        except Exception as e:
            logger.error(f"设置 {widget_name} 默认值时出错: {str(e)}")
            self._apply_combobox_index(combo_box, 0, widget_name)

    def _try_get_register_value(self, widget_info, widget_name):
        """尝试从RegisterManager获取寄存器值"""
        if not widget_info or not self.register_manager:
            return None, False

        reg_addr = widget_info.get("register_addr")
        bit_def_name = widget_info.get("bit_def_name")

        if reg_addr and bit_def_name:
            return self._get_register_value_safely(widget_name, reg_addr, bit_def_name)
        return None, False

    def _find_combobox_index_by_value(self, combo_box, value, widget_name):
        """在ComboBox中查找指定值对应的索引"""
        for i in range(combo_box.count()):
            if combo_box.itemData(i) == value:
                return i
        logger.warning(f"ComboBox {widget_name} 中未找到值 {value} 对应的项")
        return -1

    def _get_fallback_default_index(self, widget_name, widget_info):
        """获取备用默认索引"""
        # 特殊处理PLL2C1和PLL2C3控件
        if widget_name in ["PLL2C1", "PLL2C3"]:
            logger.info(f"为 {widget_name} 设置特殊默认值: 索引2 (40pF)")
            return 2

        # 尝试从widget_info解析默认值
        if widget_info and widget_info.get("default_value"):
            _, success = self._parse_default_value(widget_info["default_value"])
            if success and widget_name in self.KNOWN_WIDGET_DEFAULTS:
                return self.KNOWN_WIDGET_DEFAULTS[widget_name]

        # 使用已知默认值
        return self.KNOWN_WIDGET_DEFAULTS.get(widget_name, 0)

    def _apply_combobox_index(self, combo_box, index, widget_name):
        """应用ComboBox索引"""
        combo_box.blockSignals(True)
        try:
            if 0 <= index < combo_box.count():
                combo_box.setCurrentIndex(index)
            elif combo_box.count() > 0:
                logger.warning(f"{widget_name} 索引 {index} 超出范围，设为0")
                combo_box.setCurrentIndex(0)
            else:
                logger.warning(f"{widget_name} 为空，无法设置索引")

            # 更新内部跟踪的值
            if hasattr(self, 'widget_values') and combo_box.count() > 0:
                current_data = combo_box.itemData(combo_box.currentIndex())
                self.widget_values[widget_name] = current_data
                logger.debug(f"{widget_name} 初始值: {current_data} (索引: {combo_box.currentIndex()})")
        finally:
            combo_box.blockSignals(False)

    def _setup_widget_ranges(self):
        """设置控件范围"""
        try:
            # 设置PLL2NDivider控件范围
            if hasattr(self.ui, "PLL2NDivider"):
                self.ui.PLL2NDivider.setMinimum(2)
                self.ui.PLL2NDivider.setMaximum(262143)  # 最大值为2^18-1
                logger.info("已设置PLL2NDivider控件范围: 2-262143")

            # 设置PLL1RDividerSetting的最大值
            if hasattr(self.ui, "PLL1RDividerSetting"):
                self.ui.PLL1RDividerSetting.setMaximum(16383)
                logger.info("已设置PLL1RDividerSetting控件最大值: 16383")

            if hasattr(self.ui, "spinBoxPLL1DLDCNT"):
                self.ui.spinBoxPLL1DLDCNT.setMinimum(0)
                self.ui.spinBoxPLL1DLDCNT.setMaximum(16383)
                logger.info("已设置spinBoxPLL1DLDCNT控件范围: 0-16383")

            if hasattr(self.ui, "spinBoxPLL2DLDCNT"):
                self.ui.spinBoxPLL2DLDCNT.setMinimum(0)
                self.ui.spinBoxPLL2DLDCNT.setMaximum(16383)
                logger.info("已设置spinBoxPLL2DLDCNT控件范围: 0-16383")

        except Exception as e:
            logger.error(f"设置控件范围时出错: {str(e)}")

    def _connect_special_signals(self):
        """连接特殊信号"""
        try:
            # 连接OSCinFreq的textChanged信号到频率计算
            if hasattr(self.ui, "OSCinFreq"):
                self.ui.OSCinFreq.textChanged.connect(self.calculate_output_frequencies)
                logger.info("已连接 OSCinFreq textChanged 信号")

            # 连接时钟源选择信号
            RegisterUpdateBus.instance().clock_source_selected.connect(self.on_global_clock_source_selected)
            logger.info("已连接时钟源选择信号")

            # 连接模式变化信号
            RegisterUpdateBus.instance().mode_changed.connect(self.on_mode_changed)
            logger.info("已连接模式变化信号")

            # 连接全局寄存器更新信号
            RegisterUpdateBus.instance().register_updated.connect(self.on_global_register_update)
            logger.info("已连接全局寄存器更新信号")

            # 连接跨寄存器控件的信号
            self._connect_cross_register_widget_signals()

        except Exception as e:
            logger.error(f"连接特殊信号时出错: {str(e)}")

    def _connect_cross_register_widget_signals(self):
        """连接跨寄存器控件的信号"""
        try:
            for widget_name in self.cross_register_manager.widget_configs.keys():
                if hasattr(self.ui, widget_name):
                    widget = getattr(self.ui, widget_name)

                    # 断开现有连接
                    try:
                        if hasattr(widget, 'valueChanged'):
                            widget.valueChanged.disconnect()
                        elif hasattr(widget, 'currentIndexChanged'):
                            widget.currentIndexChanged.disconnect()
                    except Exception:
                        pass

                    # 连接新信号
                    if hasattr(widget, 'valueChanged'):
                        widget.valueChanged.connect(
                            lambda value, w=widget_name: self.update_register_value(w, value)
                        )
                    elif hasattr(widget, 'currentIndexChanged'):
                        widget.currentIndexChanged.connect(
                            lambda index, w=widget_name: self.update_register_value(w, index)
                        )

                    logger.info(f"已连接跨寄存器控件 {widget_name} 的信号")

        except Exception as e:
            logger.error(f"连接跨寄存器控件信号失败: {str(e)}")

    def _perform_initial_calculations(self):
        """执行初始计算，确保所有频率显示控件都有正确的初始值"""
        try:
            logger.info("开始执行初始频率计算...")

            # 1. 初始化FreFin
            self._initialize_freq_fin()

            # 2. 初始化PLL1RDividerSetting
            self._initialize_pll1r_divider()

            # 3. 验证PLL2NDivider
            self._verify_pll2n_divider()

            # 4. 初始化PLL2RDivider
            self._initialize_pll2r_divider()

            # 5. 执行频率计算
            self.calculate_output_frequencies()

            logger.info("初始频率计算完成")

        except Exception as e:
            logger.error(f"执行初始计算时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _initialize_freq_fin(self):
        """初始化FreFin控件"""
        if hasattr(self.ui, "FreFin"):
            current_fin_freq = self.clkin_frequencies.get(self.current_clock_source, 122.88)
            self.ui.FreFin.setText(str(current_fin_freq))
            logger.info(f"设置FreFin为: {current_fin_freq} (基于当前时钟源: {self.current_clock_source})")

    def _initialize_pll1r_divider(self):
        """初始化PLL1RDividerSetting控件"""
        widget_name = "PLL1RDividerSetting"
        if not hasattr(self.ui, widget_name):
            return

        widget = getattr(self.ui, widget_name)
        default_divider = self.clkin_divider_values.get(self.current_clock_source, 120)

        # 尝试从RegisterManager获取值
        final_value, from_register = self._get_pll1r_value_from_register(widget_name, default_divider)

        # 确保值在控件范围内
        min_val, max_val = widget.minimum(), widget.maximum()
        final_value, was_clamped = self._clamp_value_to_range(final_value, min_val, max_val, widget_name)

        widget.setValue(final_value)
        source = "寄存器" if from_register and not was_clamped else "默认/修正值"
        logger.info(f"设置 {widget_name} 为: {final_value} (来自{source})")

    def _get_pll1r_value_from_register(self, widget_name, default_value):
        """从RegisterManager获取PLL1RDivider值"""
        if not self.register_manager:
            return default_value, False

        widget_info = self.widget_register_map.get(widget_name)
        if not widget_info:
            logger.info(f"{widget_name} 未在 widget_register_map 中定义，使用默认值: {default_value}")
            return default_value, False

        reg_addr = widget_info.get("register_addr")
        bit_def_name = widget_info.get("bit_def_name")

        # 对于PLL1RDividerSetting，bit_def_name可能需要从bits字段获取
        if "bit_def" in widget_info and "bits" in widget_info["bit_def"] and widget_info["bit_def"]["bits"]:
            bit_def_name = widget_info["bit_def"]["bits"][0]

        if reg_addr and bit_def_name:
            value, success = self._get_register_value_safely(widget_name, reg_addr, bit_def_name)
            if success:
                return int(value), True

        logger.info(f"{widget_name} 无法从RegisterManager获取，使用默认值: {default_value}")
        return default_value, False

    def _verify_pll2n_divider(self):
        """验证PLL2NDivider控件"""
        if hasattr(self.ui, "PLL2NDivider"):
            widget = self.ui.PLL2NDivider
            if widget.value() < widget.minimum():
                logger.warning(f"PLL2NDivider 值 {widget.value()} 小于最小值，可能未正确初始化")
            else:
                logger.info(f"PLL2NDivider 初始化验证通过，当前值: {widget.value()}")

    def _initialize_pll2r_divider(self):
        """初始化PLL2RDivider控件"""
        widget_name = "PLL2RDivider"
        if not hasattr(self.ui, widget_name):
            return

        widget = getattr(self.ui, widget_name)
        default_value = 1
        min_val, max_val = 1, 127

        # 尝试从widget_register_map获取配置
        final_value, from_register = self._get_pll2r_value_from_config(widget_name, default_value, min_val, max_val)

        # 确保值在范围内
        final_value, was_clamped = self._clamp_value_to_range(final_value, min_val, max_val, widget_name)

        widget.setValue(final_value)
        source = "寄存器" if from_register and not was_clamped else "默认/修正值"
        logger.info(f"设置 {widget_name} 为: {final_value} (来自{source})")

    def _get_pll2r_value_from_config(self, widget_name, default_value, min_val, max_val):
        """从配置获取PLL2RDivider值"""
        # 避免未使用参数警告
        _ = min_val, max_val

        if not self.widget_register_map or widget_name not in self.widget_register_map:
            return default_value, False

        widget_info = self.widget_register_map[widget_name]

        # 尝试解析默认值
        if "default_value" in widget_info:
            parsed_val, success = self._parse_default_value(widget_info["default_value"])
            if success:
                default_value = parsed_val

        # 尝试从RegisterManager获取值
        if self.register_manager:
            reg_addr = widget_info.get("register_addr")
            bit_def_name = widget_info.get("bit_def_name")
            if reg_addr and bit_def_name:
                value, success = self._get_register_value_safely(widget_name, reg_addr, bit_def_name)
                if success:
                    return int(value), True

        return default_value, False



    def _register_cross_register_controls(self):
        """注册跨寄存器控件（使用新的CrossRegisterWidgetManager）"""
        try:
            # 注册PLL2NDivider为跨寄存器控件
            if hasattr(self.ui, "PLL2NDivider"):
                config = CrossRegisterWidgetConfig(
                    widget_name="PLL2NDivider",
                    bit_fields=["PLL2_N[17:16]", "PLL2_N[15:0]"],
                    reg_addrs=["0x76", "0x77"],
                    default_value="000000000000000000",
                    widget_type="spinbox",
                    options="2:262143",
                    min_value=2,
                    max_value=262143
                )

                if self.cross_register_manager.register_widget(config):
                    logger.info("已注册PLL2NDivider为跨寄存器控件")
                    # 初始化PLL2NDivider控件值
                    if self.cross_register_manager.initialize_widget("PLL2NDivider"):
                        logger.info("PLL2NDivider控件初始化成功")
                    else:
                        logger.error("PLL2NDivider控件初始化失败")
                else:
                    logger.error("注册PLL2NDivider跨寄存器控件失败")

            # 注册PLL1RDividerSetting控件（动态映射控件）
            if hasattr(self.ui, "PLL1RDividerSetting"):
                # PLL1RDividerSetting是一个动态控件，根据当前时钟源映射到不同寄存器
                # 默认映射到ClkIn0对应的寄存器0x63
                success = self._register_cross_register_control(
                    widget_name="PLL1RDividerSetting",
                    bit_fields=["CLKin0_R[13:0]"],
                    reg_addrs=["0x63"],
                    default_value="00000001111000",
                    widget_type="spinbox",
                    options="1:16383"
                )
                if success:
                    logger.info("已注册PLL1RDividerSetting为动态映射控件")
                    # 设置控件范围
                    self.ui.PLL1RDividerSetting.setMinimum(1)
                    self.ui.PLL1RDividerSetting.setMaximum(16383)
                    logger.info("已设置PLL1RDividerSetting控件范围")
                else:
                    logger.error("注册PLL1RDividerSetting控件失败")

            logger.info("跨寄存器控件注册完成")

        except Exception as e:
            logger.error(f"注册跨寄存器控件时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _register_cross_register_control(self, widget_name, bit_fields, reg_addrs, default_value, widget_type="spinbox", options="0:65535"):
        """注册跨寄存器控件（参考BaseHandler的实现）

        Args:
            widget_name: 控件名称
            bit_fields: 关联的位字段列表，从高位到低位排序
            reg_addrs: 关联的寄存器地址列表，从高位到低位排序
            default_value: 默认值
            widget_type: 控件类型
            options: 选项范围
        """
        try:
            if not hasattr(self.ui, widget_name):
                logger.warning(f"跨寄存器控件 {widget_name} 不存在")
                return False

            # 如果控件已在映射表中，则跳过
            if widget_name in self.widget_register_map:
                logger.info(f"跨寄存器控件 {widget_name} 已在映射表中")
                return True

            # 确保bit_fields和reg_addrs长度一致
            if len(bit_fields) != len(reg_addrs):
                logger.error("跨寄存器控件错误: bit_fields和reg_addrs长度不一致")
                return False

            # 创建映射条目
            self.widget_register_map[widget_name] = {
                "register_addr": reg_addrs[0],  # 使用第一个寄存器地址作为主地址
                "widget_type": widget_type,
                "default_value": default_value,
                "bit_def": {
                    "options": options,
                    "default": default_value,
                    "name": "_".join(bit_fields),  # 组合名称
                    "is_cross_register": True,     # 特殊标记，表示跨寄存器字段
                    "bits": bit_fields,            # 存储相关联的寄存器位字段
                    "registers": reg_addrs         # 存储相关联的寄存器地址
                }
            }

            logger.info(f"已注册跨寄存器控件 {widget_name}, 关联寄存器: {', '.join(reg_addrs)}")
            return True

        except Exception as e:
            logger.error(f"注册跨寄存器控件 {widget_name} 时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return False




    def on_register_value_changed(self, widget_name, reg_addr, reg_value):
        """处理寄存器值变化"""
        logger.info(f"PLL: 寄存器 {reg_addr} 值变化 (控件: {widget_name}) -> 0x{reg_value:04X}")

        # 重新计算频率
        self.calculate_output_frequencies()

        # 处理特定控件的业务逻辑
        if widget_name == "PLL1PD":
            self._handle_pll1_power_down()
        elif widget_name == "PLL2PD":
            self._handle_pll2_power_down()
        elif widget_name == "PLL1RST":
            self._handle_pll1_reset()
        elif widget_name == "PLL2R3":
            self._handle_pll2r3_change()
        elif widget_name in ["PLL1RDividerSetting", "PLL2RDivider", "PLL2NDivider"]:
            self._handle_divider_change(widget_name)

        # 注意：寄存器表格跳转功能已在基类ModernBaseHandler中处理
        # 不需要在这里重复调用，避免跳转逻辑混乱

    def update_register_value(self, widget_name, value):
        """重写父类方法，处理特殊控件的值更新"""
        try:
            # 处理跨寄存器控件
            if self.cross_register_manager.is_cross_register_widget(widget_name):
                success = self.cross_register_manager.update_registers(widget_name, value)
                if success:
                    logger.info(f"跨寄存器控件 {widget_name} 更新成功")

                    # 获取跨寄存器控件配置，触发每个相关寄存器的标准处理流程
                    config = self.cross_register_manager.get_widget_config(widget_name)
                    if config:
                        for reg_addr in config.reg_addrs:
                            # 将寄存器地址转换为整数（去掉0x前缀）
                            try:
                                reg_addr_int = int(reg_addr, 16)
                                # 获取当前寄存器值
                                current_reg_value = self.register_manager.get_register_value(reg_addr)

                                # 发送RegisterUpdateBus信号
                                try:
                                    from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
                                    bus_instance = RegisterUpdateBus.instance()
                                    if bus_instance:
                                        bus_instance.register_updated.emit(reg_addr, current_reg_value)
                                        logger.debug(f"已发送跨寄存器控件 {widget_name} 的寄存器更新信号: {reg_addr}")
                                except Exception as e:
                                    logger.warning(f"发送RegisterUpdateBus信号时出错: {str(e)}")

                                # 触发寄存器表格跳转（只跳转到第一个寄存器）
                                if reg_addr == config.reg_addrs[0] and not self._is_in_batch_operation():
                                    logger.info(f"跨寄存器控件 {widget_name} 触发寄存器表格跳转到 {reg_addr}")
                                    self._trigger_register_table_navigation(reg_addr_int)

                            except Exception as e:
                                logger.error(f"处理跨寄存器控件 {widget_name} 的寄存器 {reg_addr} 时出错: {str(e)}")

                    # 触发频率重新计算
                    self.calculate_output_frequencies()
                else:
                    logger.error(f"跨寄存器控件 {widget_name} 更新失败")
                return

            # 特殊处理PLL2R3控件
            elif widget_name == "PLL2R3" and hasattr(self.ui, "PLL2R3"):
                # 从ComboBox中获取itemData作为实际寄存器值
                actual_value = self.ui.PLL2R3.itemData(value)
                if actual_value is not None:
                    logger.info(f"更新PLL2R3寄存器值: 索引={value}, 实际值={actual_value}")
                    # 调用父类方法，传递实际值
                    super().update_register_value(widget_name, actual_value)
                else:
                    logger.error(f"PLL2R3控件索引{value}的itemData为None")
            else:
                # 对其他控件使用默认处理
                super().update_register_value(widget_name, value)
        except Exception as e:
            logger.error(f"更新寄存器值时发生错误: {str(e)}")



    def _handle_pll2r3_change(self):
        """处理PLL2R3控件变化"""
        try:
            if hasattr(self.ui, "PLL2R3"):
                index = self.ui.PLL2R3.currentIndex()
                text = self.ui.PLL2R3.currentText()
                value = self.ui.PLL2R3.itemData(index)
                if value is None:
                    # 如果itemData未设置，使用映射表
                    pll2r3_values = {0: 0, 1: 1, 2: 2, 3: 4}
                    value = pll2r3_values.get(index, 0)
                logger.info(f"PLL2R3选择了电阻值: {text}, 索引: {index}, 实际值: {value}")
        except Exception as e:
            logger.error(f"处理PLL2R3变化时出错: {str(e)}")

    def on_global_register_update(self, reg_addr, reg_value):
        """处理全局寄存器更新"""
        logger.debug(f"PLL: 收到全局更新 {reg_addr} = 0x{reg_value:04X}")

        # 处理跨寄存器控件的反向同步
        if hasattr(self, 'cross_register_manager') and self.cross_register_manager:
            updated_widgets = self.cross_register_manager.update_widgets_from_register(reg_addr)
            if updated_widgets:
                logger.info(f"PLL: 寄存器 {reg_addr} 更新了跨寄存器控件: {updated_widgets}")

        # 重新计算频率
        self.calculate_output_frequencies()

    # === 业务逻辑方法 ===

    def calculate_output_frequencies(self):
        """计算PLL输出频率"""
        try:
            # 获取输入频率
            fin_freq = self._get_float_from_lineedit(self.ui.FreFin) if hasattr(self.ui, "FreFin") else 0.0
            oscin_freq = self._get_float_from_lineedit(self.ui.OSCinFreq) if hasattr(self.ui, "OSCinFreq") else 0.0

            # 计算PLL1输出频率
            self._calculate_pll1_output(fin_freq)

            # 计算PLL2输出频率
            pll2_pfd_freq = self._calculate_pll2_output(oscin_freq)

            # 计算Fin0输出频率
            self._calculate_fin0_output(pll2_pfd_freq)

            logger.debug(f"频率计算完成: Fin={fin_freq}, OSCin={oscin_freq}")

        except Exception as e:
            logger.error(f"计算输出频率时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def _calculate_pll1_output(self, fin_freq):
        """计算PLL1的PFD频率"""
        try:
            if not hasattr(self.ui, "PLL1PFDFreq"):
                return 0.0

            # 检查PLL1是否掉电
            if hasattr(self.ui, "PLL1PD") and self.ui.PLL1PD.isChecked():
                self.ui.PLL1PFDFreq.setText("0.00")
                self._notify_pll1_pfd_freq_changed(0.0)
                return 0.0

            # 获取R分频器值
            r_div = 1
            if hasattr(self.ui, "PLL1RDividerSetting"):
                r_div = max(1, self.ui.PLL1RDividerSetting.value())

            # 计算PFD频率
            if r_div > 0:
                pll1_pfd_freq = fin_freq / r_div
                self.ui.PLL1PFDFreq.setText(f"{pll1_pfd_freq:.5f}")
                logger.debug(f"PLL1 PFD频率: {pll1_pfd_freq:.5f} MHz (Fin: {fin_freq}, R: {r_div})")

                # 通知其他窗口PLL1PFDFreq值已更新
                self._notify_pll1_pfd_freq_changed(pll1_pfd_freq)

                return pll1_pfd_freq
            else:
                self.ui.PLL1PFDFreq.setText("0.00")
                logger.warning("PLL1 R Divider 为0，无法计算PFD频率")
                self._notify_pll1_pfd_freq_changed(0.0)
                return 0.0

        except Exception as e:
            logger.error(f"计算PLL1输出时出错: {str(e)}")
            if hasattr(self.ui, "PLL1PFDFreq"):
                self.ui.PLL1PFDFreq.setText("Error")
            self._notify_pll1_pfd_freq_changed(0.0)
            return 0.0

    def _notify_pll1_pfd_freq_changed(self, pfd_freq):
        """通知其他窗口PLL1PFDFreq值已更新"""
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            bus = RegisterUpdateBus.instance()

            # 发送PLL1PFDFreq更新信号
            if hasattr(bus, 'pll1_pfd_freq_updated'):
                bus.pll1_pfd_freq_updated.emit(pfd_freq)
                logger.debug(f"已发送PLL1PFDFreq更新信号: {pfd_freq} MHz")
            else:
                # 如果信号不存在，我们需要添加它
                logger.warning("RegisterUpdateBus中缺少pll1_pfd_freq_updated信号")

        except Exception as e:
            logger.error(f"通知PLL1PFDFreq更新时发生错误: {str(e)}")

    def _calculate_pll2_output(self, oscin_freq):
        """计算PLL2的PFD频率"""
        try:
            if not hasattr(self.ui, "PLL2PFDFreq"):
                return 0.0

            # 检查PLL2是否掉电
            if hasattr(self.ui, "PLL2PD") and self.ui.PLL2PD.isChecked():
                self.ui.PLL2PFDFreq.setText("0.00")
                return 0.0

            # 获取R分频器值
            r_div = 1
            if hasattr(self.ui, "PLL2RDivider"):
                r_div = max(1, self.ui.PLL2RDivider.value())

            # 获取倍频器值
            doubler_value = 1
            if hasattr(self.ui, "Doubler"):
                doubler_value = self.ui.Doubler.currentIndex() + 1  # 0->1, 1->2

            # 计算PFD频率
            if r_div > 0:
                pll2_pfd_freq = oscin_freq * doubler_value / r_div
                self.ui.PLL2PFDFreq.setText(f"{pll2_pfd_freq:.3f}")
                logger.debug(f"PLL2 PFD频率: {pll2_pfd_freq:.3f} MHz (OSCin: {oscin_freq}, Doubler: {doubler_value}, R: {r_div})")
                return pll2_pfd_freq
            else:
                self.ui.PLL2PFDFreq.setText("0.00")
                logger.warning("PLL2 R Divider 为0，无法计算PFD频率")
                return 0.0

        except Exception as e:
            logger.error(f"计算PLL2输出时出错: {str(e)}")
            if hasattr(self.ui, "PLL2PFDFreq"):
                self.ui.PLL2PFDFreq.setText("Error")
            return 0.0

    def _calculate_fin0_output(self, pll2_pfd_freq):
        """计算Fin0的输出频率"""
        try:
            if not hasattr(self.ui, "Fin0Freq"):
                return

            # 检查PLL2是否掉电
            if hasattr(self.ui, "PLL2PD") and self.ui.PLL2PD.isChecked():
                self.ui.Fin0Freq.setText("0.00")
                return

            # 获取N分频器值
            n_div = 1
            if hasattr(self.ui, "PLL2NDivider"):
                n_div = max(1, self.ui.PLL2NDivider.value())

            # 获取预分频器值
            prescaler_val = 1.0
            if hasattr(self.ui, "PLL2Prescaler"):
                try:
                    prescaler_text = self.ui.PLL2Prescaler.currentText()
                    prescaler_val = float(prescaler_text) if prescaler_text else 1.0
                except ValueError:
                    logger.error(f"无法将PLL2Prescaler值 '{prescaler_text}' 转换为数字")
                    prescaler_val = 1.0

            # 计算VCO频率和Fin0频率
            if prescaler_val > 0:
                vco2_freq = pll2_pfd_freq * n_div
                fin0_freq = vco2_freq * prescaler_val
                self.ui.Fin0Freq.setText(f"{fin0_freq:.5f}")
                logger.debug(f"Fin0频率: {fin0_freq:.5f} MHz (PFD: {pll2_pfd_freq}, N: {n_div}, Prescaler: {prescaler_val})")
            else:
                logger.warning("PLL2 Prescaler 值为0或无效，无法计算Fin0频率")
                self.ui.Fin0Freq.setText("0.00")

        except Exception as e:
            logger.error(f"计算Fin0输出时出错: {str(e)}")
            if hasattr(self.ui, "Fin0Freq"):
                self.ui.Fin0Freq.setText("Error")

    def _get_float_from_lineedit(self, line_edit, default_value=0.0):
        """安全地从QLineEdit获取浮点数"""
        try:
            text = line_edit.text()
            return float(text) if text else default_value
        except (ValueError, AttributeError):
            return default_value



    def _handle_pll1_power_down(self):
        """处理PLL1电源控制"""
        try:
            pll1_pd = self.register_manager.get_bit_field_value("0x50", "PLL1_PD") if self.register_manager else 0
            logger.info(f"PLL1电源状态: {'关闭' if pll1_pd else '开启'}")
            # 重新计算频率
            self.calculate_output_frequencies()
        except Exception as e:
            logger.error(f"处理PLL1电源控制时出错: {str(e)}")

    def _handle_pll2_power_down(self):
        """处理PLL2电源控制"""
        try:
            pll2_pd = self.register_manager.get_bit_field_value("0x83", "PLL2_PD") if self.register_manager else 0
            logger.info(f"PLL2电源状态: {'关闭' if pll2_pd else '开启'}")
            # 重新计算频率
            self.calculate_output_frequencies()
        except Exception as e:
            logger.error(f"处理PLL2电源控制时出错: {str(e)}")

    def _handle_pll1_reset(self):
        """处理PLL1复位"""
        try:
            logger.info("PLL1复位操作")
            # 可以在这里添加复位后的特殊处理
        except Exception as e:
            logger.error(f"处理PLL1复位时出错: {str(e)}")

    def _handle_divider_change(self, widget_name):
        """处理分频器值变化"""
        try:
            logger.info(f"分频器 {widget_name} 值变化")
            # 重新计算频率
            self.calculate_output_frequencies()
        except Exception as e:
            logger.error(f"处理分频器变化时出错: {str(e)}")

    # === 公共接口方法 ===

    def get_current_status(self):
        """获取当前PLL状态

        Returns:
            dict: 当前状态信息
        """
        try:
            status = {}

            if self.register_manager:
                # 获取PLL状态
                status["pll1_enabled"] = not self.register_manager.get_bit_field_value("0x50", "PLL1_PD")
                status["pll2_enabled"] = not self.register_manager.get_bit_field_value("0x83", "PLL2_PD")
                status["vco_enabled"] = not self.register_manager.get_bit_field_value("0x50", "VCO_PD")

                # 获取频率信息
                status["current_clock_source"] = self.current_clock_source
                status["clkin_frequencies"] = self.clkin_frequencies.copy()

            return status

        except Exception as e:
            logger.error(f"获取PLL状态时出错: {str(e)}")
            return {}

    def update_clock_source(self, source_name, frequency, divider):
        """更新时钟源配置

        Args:
            source_name: 时钟源名称
            frequency: 频率值
            divider: 分频值
        """
        try:
            # 规范化时钟源名称
            normalized_source = self._normalize_clock_source_name(source_name)

            # 更新当前时钟源
            self.current_clock_source = normalized_source

            # 更新频率和分频值
            self.clkin_frequencies[normalized_source] = frequency
            self.clkin_divider_values[normalized_source] = divider

            # 更新UI显示
            self._update_clock_source_ui(frequency, divider)

            # 重新计算频率
            self.calculate_output_frequencies()

            logger.info(f"已更新时钟源: {normalized_source}, 频率: {frequency}MHz, 分频: {divider}")

        except Exception as e:
            logger.error(f"更新时钟源时出错: {str(e)}")

    def _update_clock_source_ui(self, frequency, divider):
        """更新时钟源相关的UI控件"""
        if hasattr(self.ui, "FreFin"):
            self.ui.FreFin.setText(str(frequency))
        if hasattr(self.ui, "PLL1RDividerSetting"):
            self.ui.PLL1RDividerSetting.setValue(divider)

    def on_global_clock_source_selected(self, source_name, frequency, divider):
        """处理全局时钟源选择事件"""
        logger.info(f"【PLL工具窗口】收到时钟源选择事件: 源={source_name}, 频率={frequency}MHz, 分频比={divider}")

        try:
            # 规范化时钟源名称
            normalized_source = self._normalize_clock_source_name(source_name)
            logger.info(f"【PLL工具窗口】规范化时钟源名称: {source_name} -> {normalized_source}")

            # 更新当前选中的时钟源
            self.current_clock_source = normalized_source

            # 更新存储的时钟源配置
            self.clkin_frequencies[normalized_source] = frequency
            self.clkin_divider_values[normalized_source] = divider
            logger.info(f"【PLL工具窗口】已更新时钟源 {normalized_source} 配置: 频率={frequency}MHz, 分频={divider}")

            # 更新UI显示
            self._update_clock_source_selection_ui(frequency, divider, normalized_source)

            # 重新计算输出频率
            self.calculate_output_frequencies()
            logger.info("【PLL工具窗口】已重新计算输出频率")

        except Exception as e:
            logger.error(f"【PLL工具窗口】处理时钟源选择事件时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def _update_clock_source_selection_ui(self, frequency, divider, normalized_source):
        """更新时钟源选择相关的UI"""
        # 更新FreFin控件
        if hasattr(self.ui, "FreFin"):
            old_value = self.ui.FreFin.text()
            self.ui.FreFin.setText(str(frequency))
            logger.info(f"【PLL工具窗口】FreFin更新: '{old_value}' -> '{frequency}'")
        else:
            logger.warning("【PLL工具窗口】FreFin控件不存在!")

        # 更新PLL1RDividerSetting控件
        if hasattr(self.ui, "PLL1RDividerSetting"):
            old_value = self.ui.PLL1RDividerSetting.value()

            # 优先从RegisterManager获取值
            reg_value = self._get_pll1r_divider_from_register(normalized_source)

            if reg_value is not None and 0 < reg_value <= 127:
                self.ui.PLL1RDividerSetting.setValue(reg_value)
                logger.info(f"【PLL工具窗口】PLL1RDividerSetting更新 (来自RM): {old_value} -> {reg_value}")
            else:
                self.ui.PLL1RDividerSetting.setValue(divider)
                logger.info(f"【PLL工具窗口】PLL1RDividerSetting更新 (来自事件): {old_value} -> {divider}")
        else:
            logger.warning("【PLL工具窗口】PLL1RDividerSetting控件不存在!")

    def _get_pll1r_divider_from_register(self, normalized_source):
        """从RegisterManager获取PLL1RDivider值"""
        if not self.register_manager:
            return None

        widget_map = self.widget_register_map.get("PLL1RDividerSetting", {})
        if normalized_source not in widget_map:
            return None

        try:
            reg_addr, bit_field = widget_map[normalized_source]
            reg_value = self.register_manager.get_bit_field_value(reg_addr, bit_field)
            logger.info(f"【PLL工具窗口】从RegisterManager获取 {normalized_source} 的 PLL1RDivider ({reg_addr}[{bit_field}]): {reg_value}")
            return reg_value
        except Exception as e:
            logger.warning(f"【PLL工具窗口】从RegisterManager获取 {normalized_source} 的 PLL1RDivider 值失败: {e}")
            return None

    def on_mode_changed(self, mode_name):
        """处理模式变化信号"""
        logger.info(f"ModernPLLHandler 收到模式变化信号: {mode_name}")
        # 模式变化后重新计算频率
        self.calculate_output_frequencies()

    def set_pll_preset(self, preset_name):
        """设置PLL预设配置

        Args:
            preset_name: 预设名称
        """
        try:
            logger.info(f"应用PLL预设: {preset_name}")

            # 定义预设配置
            presets = {
                "default": {
                    "PLL1_PD": 0,
                    "PLL2_PD": 0,
                    "VCO_PD": 0,
                    "PLL1RDividerSetting": 1,
                    "PLL2RDivider": 1,
                    "PLL2NDivider": 100
                },
                "low_power": {
                    "PLL1_PD": 1,
                    "PLL2_PD": 1,
                    "VCO_PD": 1
                },
                "high_performance": {
                    "PLL1_PD": 0,
                    "PLL2_PD": 0,
                    "VCO_PD": 0,
                    "PLL2NDivider": 200
                }
            }

            if preset_name not in presets:
                logger.warning(f"未知的预设: {preset_name}")
                return

            preset = presets[preset_name]

            # 应用预设
            for param_name, value in preset.items():
                if param_name.endswith("_PD"):
                    # 电源控制位
                    bit_name = param_name
                    reg_addr = "0x50" if param_name.startswith("PLL1") or param_name.startswith("VCO") else "0x83"
                    if self.register_manager:
                        self.register_manager.set_bit_field_value(reg_addr, bit_name, value)
                elif hasattr(self.ui, param_name):
                    # UI控件值
                    widget = getattr(self.ui, param_name)
                    if hasattr(widget, 'setValue'):
                        widget.setValue(value)

            # 重新计算频率
            self.calculate_output_frequencies()

        except Exception as e:
            logger.error(f"设置PLL预设时出错: {str(e)}")



    @classmethod
    def create_for_testing(cls, parent=None):
        """创建测试实例"""
        try:
            # 创建模拟的RegisterManager
            from core.services.register.RegisterManager import RegisterManager
            import json
            import os

            # 加载寄存器配置
            config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'lib', 'register.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                registers_config = json.load(f)

            register_manager = RegisterManager(registers_config)

            # 创建实例
            instance = cls(parent, register_manager)

            # 创建模拟主窗口用于测试
            class MockMainWindow:
                def __init__(self):
                    self.auto_write_mode = False
                    self.register_manager = register_manager
                    # 模拟寄存器服务
                    self.register_service = None

                def on_register_selected(self, reg_addr):
                    """模拟寄存器选择方法"""
                    logger.debug(f"模拟主窗口: 寄存器选择 {reg_addr}")

            # 设置模拟主窗口引用
            instance.main_window = MockMainWindow()
            logger.info("已为测试实例设置模拟主窗口引用")

            logger.info("创建现代化PLLHandler测试实例成功")
            return instance

        except Exception as e:
            logger.error(f"创建测试实例时出错: {str(e)}")
            raise


if __name__ == "__main__":
    import sys
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    # 创建测试实例
    handler = ModernPLLHandler.create_for_testing()
    handler.show()
    
    sys.exit(app.exec_())
