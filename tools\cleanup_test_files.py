#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试文件清理脚本
自动删除重复和过时的测试文件，整理剩余的测试文件
"""

import os
import shutil
import sys
from datetime import datetime

# 项目根目录
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
TEST_SUITE_DIR = os.path.join(PROJECT_ROOT, 'test_suite')
BACKUP_DIR = os.path.join(PROJECT_ROOT, 'test_backup_' + datetime.now().strftime('%Y%m%d_%H%M%S'))

# 重复文件列表（已在test_suite中存在）
DUPLICATE_FILES = [
    'test_app_after_removal.py',
    'test_auto_write.py',
    'test_auto_write_fix.py',
    'test_batch_write.py',
    'test_batch_write_completion.py',
    'test_batch_write_stability.py',
    'test_bottom_buttons_restored.py',
    'test_buttons_removed.py',
    'test_clk_output_removal.py',
    'test_clk_output_ui.py',
    'test_crash_after_write_all.py',
    'test_enhanced_sync_initialization.py',
    'test_final_frequency_calculation.py',
    'test_final_layout.py',
    'test_final_refactoring.py',
    'test_improved_search.py',
    'test_initialization_fix.py',
    'test_main_window_init.py',
    'test_modern_architecture.py',
    'test_modern_clk_outputs.py',
    'test_modern_factory.py',
    'test_modern_handlers.py',
    'test_modern_pll.py',
    'test_modern_pll_fix.py',
    'test_modern_pll_fixed.py',
    'test_modern_register_table.py',
    'test_modern_search.py',
    'test_modern_set_modes.py',
    'test_modern_sync_sysref.py',
    'test_modern_ui_event.py',
    'test_pll_and_clkout_calculations.py',
    'test_pll_controls_only.py',
    'test_pll_functionality.py',
    'test_pll_removal_simple.py',
    'test_pll_removal_verification.py',
    'test_port_display.py',
    'test_port_refresh_fix.py',
    'test_register_bit_update.py',
    'test_register_bus_fix.py',
    'test_register_navigation.py',
    'test_register_update_fix.py',
    'test_scroll_area_fix.py',
    'test_search_final.py',
    'test_search_layout.py',
    'test_search_style.py',
    'test_simple_table_write.py',
    'test_single_search_box.py',
    'test_stage3_refactor.py',
    'test_sync_sysref_final.py',
    'test_sync_sysref_fix.py',
    'test_sync_sysref_removal.py',
    'test_table_auto_write.py',
    'test_table_jump.py',
    'test_ui_display.py',
    'test_ui_refactor.py',
    'test_widget_register_jump.py',
    'test_window_api_fix.py',
    'test_window_fix.py'
]

# 过时的测试文件
OBSOLETE_FILES = [
    'test_clkin_control_fix.py',
    'test_clkin_initialization_fix.py',
    'test_combobox_initialization_fix.py',
    'test_constructor_fix.py',
    'test_initialization_order.py',
    'test_layout_modifications.py',
    'test_modern_handlers_fix.py',
    'test_pll2_calibration.py',
    'test_pll2c1_c3_fix.py',
    'test_pll_register_jump_fix.py',
    'test_spinbox_minimum.py',
    'test_spinbox_range.py',
    'test_spinbox_setvalue.py',
    'test_tool_window_factory_migration.py'
]

# 需要整理到test_suite的文件
REMAINING_USEFUL_FILES = {
    'functional': [
        'test_all_modern_handlers.py',
        'test_core_functions_simplified.py',
        'test_modern_clkin_init.py',
        'test_modern_clkin_initialization.py',
        'test_modern_tool_windows_comprehensive.py',
        'test_sync_sysref_initialization.py',
        'test_sync_sysref_simple.py'
    ],
    'integration': [
        'test_final_register_refactor.py',
        'test_framework_validation.py',
        'test_required_features.py'
    ],
    'ui': [
        'test_clock_window_warnings.py',
        'test_com_port_fix.py'
    ],
    'unit': [
        'test_register_defaults.py'
    ],
    'performance': [
        'test_exe.py',
        'test_pyinstaller_fix.py'
    ],
    'regression': [
        'test_factory_fix_simple.py',
        'test_rollback_verification.py'
    ]
}

def create_backup():
    """创建备份目录"""
    print(f"创建备份目录: {BACKUP_DIR}")
    os.makedirs(BACKUP_DIR, exist_ok=True)
    return BACKUP_DIR

def backup_and_remove_files(file_list, reason):
    """备份并删除文件"""
    print(f"\n{reason}:")
    removed_count = 0
    
    for file_name in file_list:
        file_path = os.path.join(PROJECT_ROOT, file_name)
        if os.path.exists(file_path):
            # 备份文件
            backup_path = os.path.join(BACKUP_DIR, file_name)
            try:
                shutil.copy2(file_path, backup_path)
                os.remove(file_path)
                print(f"  ✓ 删除: {file_name}")
                removed_count += 1
            except Exception as e:
                print(f"  ✗ 删除失败: {file_name} - {e}")
        else:
            print(f"  - 文件不存在: {file_name}")
    
    print(f"  删除了 {removed_count} 个文件")
    return removed_count

def organize_remaining_files():
    """整理剩余的有用文件到test_suite"""
    print(f"\n整理剩余文件到test_suite:")
    organized_count = 0
    
    for category, files in REMAINING_USEFUL_FILES.items():
        category_dir = os.path.join(TEST_SUITE_DIR, category)
        os.makedirs(category_dir, exist_ok=True)
        
        print(f"\n  {category.upper()}:")
        for file_name in files:
            source_path = os.path.join(PROJECT_ROOT, file_name)
            dest_path = os.path.join(category_dir, file_name)
            
            if os.path.exists(source_path):
                try:
                    shutil.move(source_path, dest_path)
                    print(f"    ✓ 移动: {file_name}")
                    organized_count += 1
                except Exception as e:
                    print(f"    ✗ 移动失败: {file_name} - {e}")
            else:
                print(f"    - 文件不存在: {file_name}")
    
    print(f"\n  整理了 {organized_count} 个文件")
    return organized_count

def cleanup_verification_files():
    """清理验证文件"""
    verification_files = [
        'complete_refactor_verification.py',
        'final_refactor_verification.py', 
        'final_verification.py',
        'verify_auto_write.py',
        'verify_fixes.py',
        'verify_refactor.py',
        'verify_required_features.py'
    ]
    
    print(f"\n清理验证文件:")
    removed_count = 0
    
    for file_name in verification_files:
        file_path = os.path.join(PROJECT_ROOT, file_name)
        if os.path.exists(file_path):
            # 备份文件
            backup_path = os.path.join(BACKUP_DIR, file_name)
            try:
                shutil.copy2(file_path, backup_path)
                os.remove(file_path)
                print(f"  ✓ 删除: {file_name}")
                removed_count += 1
            except Exception as e:
                print(f"  ✗ 删除失败: {file_name} - {e}")
    
    print(f"  删除了 {removed_count} 个验证文件")
    return removed_count

def generate_cleanup_report():
    """生成清理报告"""
    report_content = f"""# 测试文件清理报告

## 清理时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 清理统计
- 重复文件删除: {len(DUPLICATE_FILES)} 个
- 过时文件删除: {len(OBSOLETE_FILES)} 个  
- 验证文件删除: 7 个
- 剩余文件整理: {sum(len(files) for files in REMAINING_USEFUL_FILES.values())} 个

## 备份位置
{BACKUP_DIR}

## 清理后的测试结构
```
test_suite/
├── functional/     # 功能测试
├── integration/    # 集成测试  
├── ui/            # UI测试
├── unit/          # 单元测试
├── performance/   # 性能测试
└── regression/    # 回归测试
```

## 建议
1. 使用 `python test_suite/run_all_tests.py` 运行所有测试
2. 如需恢复文件，可从备份目录中找到
3. 定期清理日志文件和临时文件
"""
    
    report_path = os.path.join(PROJECT_ROOT, 'TEST_CLEANUP_REPORT.md')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"\n✓ 生成清理报告: TEST_CLEANUP_REPORT.md")

def main():
    """主函数"""
    print("=" * 60)
    print("测试文件清理工具")
    print("=" * 60)
    
    # 创建备份
    create_backup()
    
    # 删除重复文件
    duplicate_count = backup_and_remove_files(DUPLICATE_FILES, "删除重复文件")
    
    # 删除过时文件  
    obsolete_count = backup_and_remove_files(OBSOLETE_FILES, "删除过时文件")
    
    # 清理验证文件
    verification_count = cleanup_verification_files()
    
    # 整理剩余文件
    organized_count = organize_remaining_files()
    
    # 生成报告
    generate_cleanup_report()
    
    # 总结
    total_removed = duplicate_count + obsolete_count + verification_count
    print(f"\n" + "=" * 60)
    print("清理完成!")
    print(f"总计删除文件: {total_removed} 个")
    print(f"总计整理文件: {organized_count} 个")
    print(f"备份位置: {BACKUP_DIR}")
    print("=" * 60)

if __name__ == "__main__":
    main()
