<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>setModes</class>
 <widget class="QWidget" name="setModes">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>673</width>
    <height>323</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QLabel" name="label">
   <property name="geometry">
    <rect>
     <x>-30</x>
     <y>-40</y>
     <width>719</width>
     <height>379</height>
    </rect>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="pixmap">
    <pixmap resource="../qrc/setModes.qrc">:/setModes/setModes.bmp</pixmap>
   </property>
   <property name="scaledContents">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QPushButton" name="pBtSetDualLoop">
   <property name="geometry">
    <rect>
     <x>28</x>
     <y>91</y>
     <width>291</width>
     <height>40</height>
    </rect>
   </property>
   <property name="text">
    <string>Set Dual Loop</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pBtSetSingleLoop">
   <property name="geometry">
    <rect>
     <x>333</x>
     <y>91</y>
     <width>281</width>
     <height>40</height>
    </rect>
   </property>
   <property name="text">
    <string>Set Single Loop</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pBtSetSingleLoop0Dealy">
   <property name="geometry">
    <rect>
     <x>333</x>
     <y>142</y>
     <width>281</width>
     <height>40</height>
    </rect>
   </property>
   <property name="text">
    <string>Set Single Loop 0-Delay</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pBtSetDualLoop0DealyCascaded">
   <property name="geometry">
    <rect>
     <x>29</x>
     <y>142</y>
     <width>291</width>
     <height>40</height>
    </rect>
   </property>
   <property name="text">
    <string>Set Dual Loop 0-Delay Cascaded</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pBtSetDualLoop0DealyNested">
   <property name="geometry">
    <rect>
     <x>29</x>
     <y>194</y>
     <width>291</width>
     <height>40</height>
    </rect>
   </property>
   <property name="text">
    <string>Set Dual Loop 0-Delay Nested</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pBtSetDualLoop0DealyNestedandCasc">
   <property name="geometry">
    <rect>
     <x>29</x>
     <y>245</y>
     <width>381</width>
     <height>40</height>
    </rect>
   </property>
   <property name="text">
    <string>Set Dual Loop 0-Delay Nested+Casc</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pBtSetDistributionFin1">
   <property name="geometry">
    <rect>
     <x>333</x>
     <y>194</y>
     <width>281</width>
     <height>41</height>
    </rect>
   </property>
   <property name="text">
    <string>Set Distribution Fin1</string>
   </property>
  </widget>
 </widget>
 <resources>
  <include location="../qrc/setModes.qrc"/>
  <include location="setModes.qrc"/>
 </resources>
 <connections/>
</ui>
