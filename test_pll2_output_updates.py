#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试PLL2参数变化时相关输出值的更新
验证VCODistFreq、InternalVCOFreq、Fin0Freq等值的正确更新
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from ui.handlers.ModernPLLHandler import ModernPLLHandler
from ui.handlers.ModernClkOutputsHandler import ModernClkOutputsHandler
from core.RegisterManager import RegisterManager
from utils.Log import get_module_logger

logger = get_module_logger(__name__)

def test_pll2_output_updates():
    """测试PLL2参数变化时相关输出值的更新"""
    print("="*60)
    print("测试PLL2参数变化时相关输出值的更新")
    print("="*60)
    
    app = QApplication(sys.argv)
    
    try:
        # 创建RegisterManager实例
        register_manager = RegisterManager()
        
        # 创建处理器实例
        pll_handler = ModernPLLHandler(register_manager=register_manager)
        clk_outputs_handler = ModernClkOutputsHandler(register_manager=register_manager)
        
        print("\n--- 测试1: 基础PLL2参数变化 ---")
        test_basic_pll2_parameter_changes(pll_handler)
        
        print("\n--- 测试2: PLL2NDivider变化的影响 ---")
        test_pll2_n_divider_changes(pll_handler)
        
        print("\n--- 测试3: PLL2Prescaler变化的影响 ---")
        test_pll2_prescaler_changes(pll_handler)
        
        print("\n--- 测试4: 输出值同步验证 ---")
        test_output_synchronization(pll_handler)
        
        print("\n--- 测试5: 反馈模式下的输出更新 ---")
        test_feedback_mode_output_updates(pll_handler, clk_outputs_handler)
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        app.quit()

def test_basic_pll2_parameter_changes(pll_handler):
    """测试基础PLL2参数变化"""
    try:
        print("测试基础PLL2参数变化...")
        
        # 设置基础参数
        test_oscin = 122.88
        test_doubler = 1
        test_r_divider = 2
        test_n_divider = 35
        test_prescaler = 2.0
        
        # 设置控件值
        if hasattr(pll_handler.ui, "OSCinFreq"):
            pll_handler.ui.OSCinFreq.setText(str(test_oscin))
        if hasattr(pll_handler.ui, "Doubler"):
            pll_handler.ui.Doubler.setCurrentIndex(test_doubler - 1)
        if hasattr(pll_handler.ui, "PLL2RDivider"):
            pll_handler.ui.PLL2RDivider.setValue(test_r_divider)
        if hasattr(pll_handler.ui, "PLL2NDivider"):
            pll_handler.ui.PLL2NDivider.setValue(test_n_divider)
        if hasattr(pll_handler.ui, "PLL2Prescaler"):
            # 假设PLL2Prescaler是ComboBox，设置文本值
            for i in range(pll_handler.ui.PLL2Prescaler.count()):
                if pll_handler.ui.PLL2Prescaler.itemText(i) == str(test_prescaler):
                    pll_handler.ui.PLL2Prescaler.setCurrentIndex(i)
                    break
        
        # 设置为正常模式
        if hasattr(pll_handler.ui, "PLL2NclkMux"):
            pll_handler.ui.PLL2NclkMux.setCurrentIndex(0)  # PLL2 Prescaler
        
        print(f"  设置参数:")
        print(f"    OSCin: {test_oscin} MHz")
        print(f"    Doubler: {test_doubler}x")
        print(f"    PLL2RDivider: {test_r_divider}")
        print(f"    PLL2NDivider: {test_n_divider}")
        print(f"    PLL2Prescaler: {test_prescaler}")
        
        # 执行计算
        result = pll_handler._calculate_pll2_unified_formula(test_oscin)
        
        # 手动计算期望值
        expected_pfd_freq = test_oscin * test_doubler / test_r_divider
        expected_vco_dist_freq = expected_pfd_freq * test_n_divider * test_prescaler
        
        print(f"\n  计算结果:")
        print(f"    期望PLL2PFDFreq: {expected_pfd_freq:.3f} MHz")
        print(f"    实际PLL2PFDFreq: {result:.3f} MHz")
        print(f"    期望VCODistFreq: {expected_vco_dist_freq:.3f} MHz")
        
        # 检查VCODistFreq是否正确更新
        if hasattr(pll_handler.ui, "VCODistFreq"):
            actual_vco_dist_freq = float(pll_handler.ui.VCODistFreq.text())
            print(f"    实际VCODistFreq: {actual_vco_dist_freq:.3f} MHz")
            
            if abs(actual_vco_dist_freq - expected_vco_dist_freq) < 0.001:
                print("    ✓ VCODistFreq更新正确")
            else:
                print(f"    ✗ VCODistFreq更新错误，差值: {abs(actual_vco_dist_freq - expected_vco_dist_freq):.6f}")
        
        # 检查InternalVCOFreq是否与VCODistFreq同步
        if hasattr(pll_handler.ui, "InternalVCOFreq"):
            internal_vco_freq = float(pll_handler.ui.InternalVCOFreq.text())
            print(f"    InternalVCOFreq: {internal_vco_freq:.3f} MHz")
            
            if abs(internal_vco_freq - actual_vco_dist_freq) < 0.001:
                print("    ✓ InternalVCOFreq与VCODistFreq同步正确")
            else:
                print("    ✗ InternalVCOFreq与VCODistFreq同步错误")
        
        # 检查Fin0Freq是否正确更新
        if hasattr(pll_handler.ui, "Fin0Freq"):
            fin0_freq = float(pll_handler.ui.Fin0Freq.text())
            print(f"    Fin0Freq: {fin0_freq:.3f} MHz")
            
            if abs(fin0_freq - expected_pfd_freq) < 0.001:
                print("    ✓ Fin0Freq更新正确")
            else:
                print("    ✗ Fin0Freq更新错误")
        
        print("✓ 基础PLL2参数变化测试完成")
        
    except Exception as e:
        print(f"✗ 基础PLL2参数变化测试失败: {str(e)}")

def test_pll2_n_divider_changes(pll_handler):
    """测试PLL2NDivider变化的影响"""
    try:
        print("测试PLL2NDivider变化的影响...")
        
        # 固定其他参数
        test_oscin = 100.0
        test_doubler = 1
        test_r_divider = 2
        test_prescaler = 2.0
        
        # 设置固定参数
        if hasattr(pll_handler.ui, "OSCinFreq"):
            pll_handler.ui.OSCinFreq.setText(str(test_oscin))
        if hasattr(pll_handler.ui, "Doubler"):
            pll_handler.ui.Doubler.setCurrentIndex(test_doubler - 1)
        if hasattr(pll_handler.ui, "PLL2RDivider"):
            pll_handler.ui.PLL2RDivider.setValue(test_r_divider)
        
        # 测试不同的NDivider值
        test_n_dividers = [20, 30, 40, 50]
        
        print(f"  固定参数: OSCin={test_oscin}, Doubler={test_doubler}x, RDiv={test_r_divider}, Prescaler={test_prescaler}")
        print(f"\n  {'NDivider':<10} {'PFDFreq':<12} {'VCODistFreq':<15} {'变化量':<10}")
        print(f"  {'-'*10} {'-'*12} {'-'*15} {'-'*10}")
        
        previous_vco_freq = 0
        for n_divider in test_n_dividers:
            # 设置NDivider值
            if hasattr(pll_handler.ui, "PLL2NDivider"):
                pll_handler.ui.PLL2NDivider.setValue(n_divider)
            
            # 执行计算
            pfd_freq = pll_handler._calculate_pll2_unified_formula(test_oscin)
            
            # 获取VCODistFreq
            vco_dist_freq = 0.0
            if hasattr(pll_handler.ui, "VCODistFreq"):
                vco_dist_freq = float(pll_handler.ui.VCODistFreq.text())
            
            # 计算变化量
            change = vco_dist_freq - previous_vco_freq if previous_vco_freq > 0 else 0
            
            print(f"  {n_divider:<10} {pfd_freq:<12.3f} {vco_dist_freq:<15.3f} {change:<10.3f}")
            
            # 验证计算正确性
            expected_vco_freq = pfd_freq * n_divider * test_prescaler
            if abs(vco_dist_freq - expected_vco_freq) > 0.001:
                print(f"    ✗ NDivider={n_divider}时VCODistFreq计算错误")
            
            previous_vco_freq = vco_dist_freq
        
        print("✓ PLL2NDivider变化影响测试完成")
        
    except Exception as e:
        print(f"✗ PLL2NDivider变化影响测试失败: {str(e)}")

def test_pll2_prescaler_changes(pll_handler):
    """测试PLL2Prescaler变化的影响"""
    try:
        print("测试PLL2Prescaler变化的影响...")
        
        # 固定其他参数
        test_oscin = 122.88
        test_doubler = 1
        test_r_divider = 2
        test_n_divider = 35
        
        # 设置固定参数
        if hasattr(pll_handler.ui, "OSCinFreq"):
            pll_handler.ui.OSCinFreq.setText(str(test_oscin))
        if hasattr(pll_handler.ui, "Doubler"):
            pll_handler.ui.Doubler.setCurrentIndex(test_doubler - 1)
        if hasattr(pll_handler.ui, "PLL2RDivider"):
            pll_handler.ui.PLL2RDivider.setValue(test_r_divider)
        if hasattr(pll_handler.ui, "PLL2NDivider"):
            pll_handler.ui.PLL2NDivider.setValue(test_n_divider)
        
        # 测试不同的Prescaler值
        test_prescalers = [1.0, 2.0, 4.0, 8.0]
        
        print(f"  固定参数: OSCin={test_oscin}, NDivider={test_n_divider}")
        print(f"\n  {'Prescaler':<10} {'PFDFreq':<12} {'VCODistFreq':<15} {'倍数关系':<10}")
        print(f"  {'-'*10} {'-'*12} {'-'*15} {'-'*10}")
        
        base_vco_freq = 0
        for i, prescaler in enumerate(test_prescalers):
            # 设置Prescaler值 (这里假设是ComboBox)
            if hasattr(pll_handler.ui, "PLL2Prescaler"):
                # 模拟设置Prescaler值
                pass  # 实际实现中需要根据UI控件类型设置
            
            # 执行计算
            pfd_freq = pll_handler._calculate_pll2_unified_formula(test_oscin)
            
            # 获取VCODistFreq
            vco_dist_freq = 0.0
            if hasattr(pll_handler.ui, "VCODistFreq"):
                vco_dist_freq = float(pll_handler.ui.VCODistFreq.text())
            
            # 计算倍数关系
            if i == 0:
                base_vco_freq = vco_dist_freq
                ratio = 1.0
            else:
                ratio = vco_dist_freq / base_vco_freq if base_vco_freq > 0 else 0
            
            print(f"  {prescaler:<10} {pfd_freq:<12.3f} {vco_dist_freq:<15.3f} {ratio:<10.2f}x")
            
            # 验证Prescaler的倍数关系
            expected_ratio = prescaler / test_prescalers[0]
            if abs(ratio - expected_ratio) > 0.01:
                print(f"    ✗ Prescaler倍数关系错误，期望: {expected_ratio:.2f}x")
        
        print("✓ PLL2Prescaler变化影响测试完成")
        
    except Exception as e:
        print(f"✗ PLL2Prescaler变化影响测试失败: {str(e)}")

def test_output_synchronization(pll_handler):
    """测试输出值同步验证"""
    try:
        print("测试输出值同步验证...")
        
        # 设置测试参数
        test_oscin = 122.88
        test_doubler = 1
        test_r_divider = 2
        test_n_divider = 35
        test_prescaler = 2.0
        
        # 设置参数
        if hasattr(pll_handler.ui, "OSCinFreq"):
            pll_handler.ui.OSCinFreq.setText(str(test_oscin))
        if hasattr(pll_handler.ui, "Doubler"):
            pll_handler.ui.Doubler.setCurrentIndex(test_doubler - 1)
        if hasattr(pll_handler.ui, "PLL2RDivider"):
            pll_handler.ui.PLL2RDivider.setValue(test_r_divider)
        if hasattr(pll_handler.ui, "PLL2NDivider"):
            pll_handler.ui.PLL2NDivider.setValue(test_n_divider)
        
        # 执行计算
        pfd_freq = pll_handler._calculate_pll2_unified_formula(test_oscin)
        
        print(f"  计算完成后的输出值:")
        
        # 检查所有相关输出值
        output_values = {}
        
        if hasattr(pll_handler.ui, "PLL2PFDFreq"):
            output_values["PLL2PFDFreq"] = pll_handler.ui.PLL2PFDFreq.text()
            print(f"    PLL2PFDFreq: {output_values['PLL2PFDFreq']} MHz")
        
        if hasattr(pll_handler.ui, "VCODistFreq"):
            output_values["VCODistFreq"] = pll_handler.ui.VCODistFreq.text()
            print(f"    VCODistFreq: {output_values['VCODistFreq']} MHz")
        
        if hasattr(pll_handler.ui, "InternalVCOFreq"):
            output_values["InternalVCOFreq"] = pll_handler.ui.InternalVCOFreq.text()
            print(f"    InternalVCOFreq: {output_values['InternalVCOFreq']} MHz")
        
        if hasattr(pll_handler.ui, "Fin0Freq"):
            output_values["Fin0Freq"] = pll_handler.ui.Fin0Freq.text()
            print(f"    Fin0Freq: {output_values['Fin0Freq']} MHz")
        
        # 验证同步关系
        print(f"\n  同步关系验证:")
        
        # VCODistFreq与InternalVCOFreq应该相等
        if "VCODistFreq" in output_values and "InternalVCOFreq" in output_values:
            if output_values["VCODistFreq"] == output_values["InternalVCOFreq"]:
                print("    ✓ VCODistFreq与InternalVCOFreq同步正确")
            else:
                print("    ✗ VCODistFreq与InternalVCOFreq同步错误")
        
        # Fin0Freq应该等于PLL2PFDFreq
        if "Fin0Freq" in output_values and "PLL2PFDFreq" in output_values:
            if output_values["Fin0Freq"] == output_values["PLL2PFDFreq"]:
                print("    ✓ Fin0Freq与PLL2PFDFreq同步正确")
            else:
                print("    ✗ Fin0Freq与PLL2PFDFreq同步错误")
        
        print("✓ 输出值同步验证测试完成")
        
    except Exception as e:
        print(f"✗ 输出值同步验证测试失败: {str(e)}")

def test_feedback_mode_output_updates(pll_handler, clk_outputs_handler):
    """测试反馈模式下的输出更新"""
    try:
        print("测试反馈模式下的输出更新...")
        
        # 设置反馈模式参数
        test_oscin = 122.88
        test_vco_dist_freq = 2949.12
        test_dclk6_7div = 8
        
        # 设置参数
        if hasattr(pll_handler.ui, "OSCinFreq"):
            pll_handler.ui.OSCinFreq.setText(str(test_oscin))
        if hasattr(pll_handler.ui, "VCODistFreq"):
            pll_handler.ui.VCODistFreq.setText(str(test_vco_dist_freq))
        if hasattr(clk_outputs_handler.ui, 'DCLK6_7DIV'):
            clk_outputs_handler.ui.DCLK6_7DIV.setValue(test_dclk6_7div)
        
        # 设置为反馈模式
        if hasattr(pll_handler.ui, "PLL2NclkMux"):
            pll_handler.ui.PLL2NclkMux.setCurrentIndex(1)  # Feedback Mux
        if hasattr(pll_handler.ui, "FBMUX"):
            pll_handler.ui.FBMUX.setCurrentIndex(0)  # CLKout6
        
        print(f"  反馈模式参数:")
        print(f"    OSCin: {test_oscin} MHz")
        print(f"    初始VCODistFreq: {test_vco_dist_freq} MHz")
        print(f"    DCLK6_7DIV: {test_dclk6_7div}")
        
        # 执行计算
        pfd_freq = pll_handler._calculate_pll2_unified_formula(test_oscin)
        
        print(f"\n  反馈模式计算结果:")
        print(f"    PLL2PFDFreq: {pfd_freq:.3f} MHz")
        
        # 检查输出值更新
        if hasattr(pll_handler.ui, "VCODistFreq"):
            updated_vco_freq = float(pll_handler.ui.VCODistFreq.text())
            print(f"    更新后VCODistFreq: {updated_vco_freq:.3f} MHz")
        
        # 计算CLKout6频率
        clkout6_freq = test_vco_dist_freq / test_dclk6_7div
        print(f"    CLKout6频率: {clkout6_freq:.3f} MHz")
        
        # 验证反馈模式的计算逻辑
        expected_pfd_freq = test_oscin / 2  # 假设R分频器为2
        print(f"    期望PFDFreq: {expected_pfd_freq:.3f} MHz")
        
        if abs(pfd_freq - expected_pfd_freq) < 0.001:
            print("    ✓ 反馈模式PFDFreq计算正确")
        else:
            print("    ✗ 反馈模式PFDFreq计算错误")
        
        print("✓ 反馈模式输出更新测试完成")
        
    except Exception as e:
        print(f"✗ 反馈模式输出更新测试失败: {str(e)}")

def print_output_updates_summary():
    """打印输出更新总结"""
    print("\n" + "="*60)
    print("PLL2输出值更新系统总结")
    print("="*60)
    print("""
当PLL2参数发生变化时，系统会自动更新以下相关输出值：

1. PLL2PFDFreq:
   - 直接由统一公式计算得出
   - 显示在PLL2PFDFreq控件中

2. VCODistFreq:
   - 公式: VCODistFreq = PLL2PFDFreq × PLL2NDivider × PLL2Prescaler
   - 是其他时钟输出计算的基础

3. InternalVCOFreq:
   - 与VCODistFreq保持同步
   - 用于内部VCO频率显示

4. Fin0Freq:
   - 通常等于PLL2PFDFreq
   - 用于特定的时钟输出计算

更新触发条件：
- PLL2RDivider值变化
- PLL2NDivider值变化
- PLL2Prescaler值变化
- Doubler设置变化
- 输入频率变化
- PLL2NclkMux模式切换

更新流程：
1. 检测参数变化
2. 重新计算PLL2PFDFreq
3. 更新VCODistFreq
4. 同步InternalVCOFreq
5. 更新Fin0Freq
6. 通知其他窗口值已更新

优势：
- 自动化的输出值更新
- 确保所有相关值的一致性
- 实时响应参数变化
- 支持跨窗口同步
- 完整的错误处理机制
""")
    print("="*60)

if __name__ == "__main__":
    print_output_updates_summary()
    test_pll2_output_updates()
