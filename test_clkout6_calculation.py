#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试CLKout6频率计算功能
验证VCODistFreq / DCLK6_7DIV的计算逻辑
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from ui.handlers.ModernPLLHandler import ModernPLLHandler
from ui.handlers.ModernClkOutputsHandler import ModernClkOutputsHandler
from core.RegisterManager import RegisterManager
from utils.Log import get_module_logger

logger = get_module_logger(__name__)

def test_clkout6_calculation():
    """测试CLKout6频率计算功能"""
    print("="*60)
    print("测试CLKout6频率计算功能")
    print("="*60)
    
    app = QApplication(sys.argv)
    
    try:
        # 创建RegisterManager实例
        register_manager = RegisterManager()
        
        # 创建处理器实例
        pll_handler = ModernPLLHandler(register_manager=register_manager)
        clk_outputs_handler = ModernClkOutputsHandler(register_manager=register_manager)
        
        # 设置测试数据
        test_vco_dist_freq = 2949.12  # MHz
        test_dclk6_7div_values = [1, 2, 4, 8, 16]
        
        # 设置VCODistFreq值
        if hasattr(pll_handler.ui, "VCODistFreq"):
            pll_handler.ui.VCODistFreq.setText(str(test_vco_dist_freq))
            print(f"设置VCODistFreq = {test_vco_dist_freq} MHz")
        
        print("\n--- 测试1: 基本CLKout6频率计算 ---")
        test_basic_clkout6_calculation(pll_handler, clk_outputs_handler, test_vco_dist_freq, test_dclk6_7div_values)
        
        print("\n--- 测试2: 跨窗口数值获取 ---")
        test_cross_window_value_retrieval(pll_handler, clk_outputs_handler)
        
        print("\n--- 测试3: 反馈多路复用器频率计算 ---")
        test_feedback_mux_calculation(pll_handler, clk_outputs_handler)
        
        print("\n--- 测试4: 实时更新测试 ---")
        test_realtime_update(pll_handler, clk_outputs_handler)
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        app.quit()

def test_basic_clkout6_calculation(pll_handler, clk_outputs_handler, vco_freq, div_values):
    """测试基本的CLKout6频率计算"""
    try:
        print("测试基本CLKout6频率计算...")
        
        for div_value in div_values:
            # 设置DCLK6_7DIV值
            if hasattr(clk_outputs_handler.ui, 'DCLK6_7DIV'):
                clk_outputs_handler.ui.DCLK6_7DIV.setValue(div_value)
                print(f"\n  设置DCLK6_7DIV = {div_value}")
                
                # 计算期望的CLKout6频率
                expected_freq = vco_freq / div_value
                
                # 获取实际计算的CLKout6频率
                actual_freq = pll_handler._get_clkout6_frequency()
                
                print(f"  期望CLKout6频率: {expected_freq:.3f} MHz")
                print(f"  实际CLKout6频率: {actual_freq:.3f} MHz")
                
                # 验证结果
                if abs(actual_freq - expected_freq) < 0.001:
                    print(f"  ✓ 计算正确")
                else:
                    print(f"  ✗ 计算错误，差值: {abs(actual_freq - expected_freq):.6f}")
            else:
                print("  ✗ DCLK6_7DIV控件不存在")
                break
        
        print("✓ 基本CLKout6频率计算测试完成")
        
    except Exception as e:
        print(f"✗ 基本CLKout6频率计算测试失败: {str(e)}")

def test_cross_window_value_retrieval(pll_handler, clk_outputs_handler):
    """测试跨窗口数值获取"""
    try:
        print("测试跨窗口数值获取...")
        
        # 设置测试值
        test_div_value = 12
        if hasattr(clk_outputs_handler.ui, 'DCLK6_7DIV'):
            clk_outputs_handler.ui.DCLK6_7DIV.setValue(test_div_value)
            print(f"  在时钟输出窗口设置DCLK6_7DIV = {test_div_value}")
            
            # 从PLL窗口获取这个值
            retrieved_value = pll_handler._get_dclk6_7div_value()
            print(f"  从PLL窗口获取DCLK6_7DIV = {retrieved_value}")
            
            if retrieved_value == test_div_value:
                print("  ✓ 跨窗口数值获取正确")
            else:
                print(f"  ✗ 跨窗口数值获取错误，期望: {test_div_value}, 实际: {retrieved_value}")
        else:
            print("  ✗ DCLK6_7DIV控件不存在")
        
        print("✓ 跨窗口数值获取测试完成")
        
    except Exception as e:
        print(f"✗ 跨窗口数值获取测试失败: {str(e)}")

def test_feedback_mux_calculation(pll_handler, clk_outputs_handler):
    """测试反馈多路复用器频率计算"""
    try:
        print("测试反馈多路复用器频率计算...")
        
        # 设置VCODistFreq
        test_vco_freq = 3000.0
        if hasattr(pll_handler.ui, "VCODistFreq"):
            pll_handler.ui.VCODistFreq.setText(str(test_vco_freq))
        
        # 设置DCLK6_7DIV
        test_div_value = 5
        if hasattr(clk_outputs_handler.ui, 'DCLK6_7DIV'):
            clk_outputs_handler.ui.DCLK6_7DIV.setValue(test_div_value)
        
        # 设置FBMUX为CLKout6 (值为0)
        if hasattr(pll_handler.ui, "FBMUX"):
            pll_handler.ui.FBMUX.setCurrentIndex(0)
            print(f"  设置FBMUX为CLKout6")
        
        # 获取反馈多路复用器频率
        feedback_freq = pll_handler._get_feedback_mux_frequency()
        expected_feedback_freq = test_vco_freq / test_div_value
        
        print(f"  VCODistFreq: {test_vco_freq} MHz")
        print(f"  DCLK6_7DIV: {test_div_value}")
        print(f"  期望反馈频率: {expected_feedback_freq:.3f} MHz")
        print(f"  实际反馈频率: {feedback_freq:.3f} MHz")
        
        if abs(feedback_freq - expected_feedback_freq) < 0.001:
            print("  ✓ 反馈多路复用器频率计算正确")
        else:
            print(f"  ✗ 反馈多路复用器频率计算错误")
        
        print("✓ 反馈多路复用器频率计算测试完成")
        
    except Exception as e:
        print(f"✗ 反馈多路复用器频率计算测试失败: {str(e)}")

def test_realtime_update(pll_handler, clk_outputs_handler):
    """测试实时更新功能"""
    try:
        print("测试实时更新功能...")
        
        # 设置初始值
        initial_vco_freq = 2400.0
        initial_div_value = 6
        
        if hasattr(pll_handler.ui, "VCODistFreq"):
            pll_handler.ui.VCODistFreq.setText(str(initial_vco_freq))
        
        if hasattr(clk_outputs_handler.ui, 'DCLK6_7DIV'):
            clk_outputs_handler.ui.DCLK6_7DIV.setValue(initial_div_value)
        
        # 获取初始CLKout6频率
        initial_clkout6_freq = pll_handler._get_clkout6_frequency()
        print(f"  初始CLKout6频率: {initial_clkout6_freq:.3f} MHz")
        
        # 改变分频器值
        new_div_value = 3
        if hasattr(clk_outputs_handler.ui, 'DCLK6_7DIV'):
            clk_outputs_handler.ui.DCLK6_7DIV.setValue(new_div_value)
            print(f"  改变DCLK6_7DIV为: {new_div_value}")
        
        # 获取更新后的CLKout6频率
        updated_clkout6_freq = pll_handler._get_clkout6_frequency()
        expected_updated_freq = initial_vco_freq / new_div_value
        
        print(f"  期望更新后频率: {expected_updated_freq:.3f} MHz")
        print(f"  实际更新后频率: {updated_clkout6_freq:.3f} MHz")
        
        if abs(updated_clkout6_freq - expected_updated_freq) < 0.001:
            print("  ✓ 实时更新功能正常")
        else:
            print("  ✗ 实时更新功能异常")
        
        print("✓ 实时更新测试完成")
        
    except Exception as e:
        print(f"✗ 实时更新测试失败: {str(e)}")

def print_clkout6_summary():
    """打印CLKout6计算总结"""
    print("\n" + "="*60)
    print("CLKout6频率计算系统总结")
    print("="*60)
    print("""
计算公式：
CLKout6频率 = VCODistFreq / DCLK6_7DIV

数据来源：
- VCODistFreq: 来自PLL窗口的VCODistFreq控件
- DCLK6_7DIV: 来自时钟输出窗口的DCLK6_7DIV控件

获取策略：
1. 优先从ModernClkOutputsHandler的UI控件获取DCLK6_7DIV值
2. 如果UI控件不可用，从寄存器获取
3. 如果都不可用，使用默认值8

实时更新：
- 连接DCLK6_7DIV.valueChanged信号
- 当分频器值改变时自动重新计算
- 支持跨窗口的实时同步

应用场景：
- PLL1反馈路径选择Feedback Mux时
- FBMUX设置为CLKout6时
- 需要CLKout6作为时钟源的计算

优势：
- 准确的跨窗口数值获取
- 实时响应分频器变化
- 良好的容错和回退机制
- 清晰的计算逻辑和调试信息
""")
    print("="*60)

if __name__ == "__main__":
    print_clkout6_summary()
    test_clkout6_calculation()
