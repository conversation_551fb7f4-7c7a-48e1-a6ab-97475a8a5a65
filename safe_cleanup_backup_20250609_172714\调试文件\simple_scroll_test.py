#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化的滚动功能测试脚本
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_imports():
    """测试模块导入"""
    print("开始测试模块导入...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel
        print("✓ PyQt5 导入成功")
    except ImportError as e:
        print(f"✗ PyQt5 导入失败: {e}")
        return False
    
    try:
        from ui.handlers.ModernBaseHandler import ModernBaseHandler
        print("✓ ModernBaseHandler 导入成功")
    except ImportError as e:
        print(f"✗ ModernBaseHandler 导入失败: {e}")
        return False
    
    try:
        from plugins.example_tool_plugin import ExampleToolWindow
        print("✓ ExampleToolWindow 导入成功")
    except ImportError as e:
        print(f"✗ ExampleToolWindow 导入失败: {e}")
        return False
    
    return True

def test_scroll_functionality():
    """测试滚动功能"""
    print("\n开始测试滚动功能...")
    
    from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel
    from ui.handlers.ModernBaseHandler import ModernBaseHandler
    
    # 创建应用程序（但不显示GUI）
    app = QApplication([])
    
    try:
        # 测试1: 自动滚动检测
        print("测试1: 自动滚动检测")
        handler1 = ModernBaseHandler()
        if hasattr(handler1, 'scroll_area'):
            print(f"  ✓ 滚动区域状态: {'已启用' if handler1.scroll_area else '未启用'}")
        else:
            print("  ✓ 使用直接布局（无滚动）")
        
        # 测试2: 强制启用滚动
        print("测试2: 强制启用滚动")
        handler2 = ModernBaseHandler(enable_scroll=True)
        if hasattr(handler2, 'scroll_area') and handler2.scroll_area:
            print("  ✓ 滚动区域已强制启用")
        else:
            print("  ✗ 滚动区域启用失败")
        
        # 测试3: 强制禁用滚动
        print("测试3: 强制禁用滚动")
        handler3 = ModernBaseHandler(enable_scroll=False)
        if not hasattr(handler3, 'scroll_area') or handler3.scroll_area is None:
            print("  ✓ 滚动区域已禁用")
        else:
            print("  ✗ 滚动区域禁用失败")
        
        # 测试4: 动态启用滚动
        print("测试4: 动态启用滚动")
        handler4 = ModernBaseHandler(enable_scroll=False)
        handler4.enable_scroll_area(600, 400)
        if hasattr(handler4, 'scroll_area') and handler4.scroll_area:
            print("  ✓ 动态启用滚动成功")
        else:
            print("  ✗ 动态启用滚动失败")
        
        # 测试5: 动态禁用滚动
        print("测试5: 动态禁用滚动")
        handler4.disable_scroll_area()
        if not hasattr(handler4, 'scroll_area') or handler4.scroll_area is None:
            print("  ✓ 动态禁用滚动成功")
        else:
            print("  ✗ 动态禁用滚动失败")
        
        print("\n✓ 所有滚动功能测试通过")
        return True
        
    except Exception as e:
        print(f"\n✗ 滚动功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        app.quit()

def test_example_plugin():
    """测试示例插件"""
    print("\n开始测试示例插件...")

    from PyQt5.QtWidgets import QApplication

    # 创建应用程序（但不显示GUI）
    app = QApplication([])

    try:
        from plugins.example_tool_plugin import ExampleToolWindow, ExampleToolPlugin

        # 测试插件类
        plugin = ExampleToolPlugin()
        print(f"  ✓ 插件名称: {plugin.name}")
        print(f"  ✓ 插件版本: {plugin.version}")
        print(f"  ✓ 插件描述: {plugin.description}")

        # 测试窗口创建（不显示）
        window = plugin.create_window()
        if window:
            print("  ✓ 插件窗口创建成功")
            print(f"  ✓ 窗口标题: {window.windowTitle()}")
            print(f"  ✓ 窗口尺寸: {window.size().width()} x {window.size().height()}")
        else:
            print("  ✗ 插件窗口创建失败")
            return False

        print("✓ 示例插件测试通过")
        return True

    except Exception as e:
        print(f"✗ 示例插件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

    finally:
        app.quit()

def main():
    """主函数"""
    print("=== 滚动功能测试 ===")
    
    # 测试模块导入
    if not test_imports():
        print("\n❌ 模块导入测试失败，退出")
        return 1
    
    # 测试滚动功能
    if not test_scroll_functionality():
        print("\n❌ 滚动功能测试失败")
        return 1
    
    # 测试示例插件
    if not test_example_plugin():
        print("\n❌ 示例插件测试失败")
        return 1
    
    print("\n🎉 所有测试通过！滚动功能已成功实现。")
    print("\n使用说明:")
    print("1. 现代化处理器会自动检测是否需要滚动功能")
    print("2. 可以通过 enable_scroll 参数强制启用/禁用滚动")
    print("3. 支持动态启用/禁用滚动区域")
    print("4. 插件窗口会自动获得滚动支持")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
