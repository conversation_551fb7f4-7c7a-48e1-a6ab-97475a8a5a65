# 手动控件说明机制使用指南

## 概述

手动控件说明机制允许为特殊控件（如FreFin）添加自定义的说明信息，这些控件的信息无法从JSON寄存器文件中获取，需要手动配置说明。

## 功能特点

1. **优先级机制**: 手动说明优先于寄存器映射信息显示
2. **配置文件管理**: 通过JSON配置文件统一管理所有手动说明
3. **实时重载**: 支持在运行时重新加载配置文件
4. **详细信息**: 支持控件类型、数据来源、计算公式、依赖关系等详细信息

## 配置文件结构

配置文件位置: `lib/widget_descriptions.json`

```json
{
    "description": "手动控件说明配置文件",
    "version": "1.0.0",
    "last_updated": "2025-01-01",
    "widgets": {
        "控件名称": {
            "type": "控件类型",
            "source": "数据来源",
            "description": "详细描述",
            "calculation": "计算公式（可选）",
            "dependencies": ["依赖项1", "依赖项2"],
            "is_manual_info": true
        }
    }
}
```

## 配置字段说明

### 必需字段
- `type`: 控件类型（如"频率显示控件"、"频率计算控件"等）
- `source`: 数据来源（如"时钟输入控制窗口"、"PLL计算结果"等）
- `description`: 详细描述控件的功能和特性
- `is_manual_info`: 必须设置为true，标识这是手动配置的信息

### 可选字段
- `calculation`: 计算公式或计算方法
- `dependencies`: 依赖的其他控件或参数列表

## 使用方法

### 1. 添加新的控件说明

编辑 `lib/widget_descriptions.json` 文件，在 `widgets` 对象中添加新的控件配置：

```json
"新控件名称": {
    "type": "控件类型",
    "source": "数据来源",
    "description": "详细描述新控件的功能...",
    "calculation": "计算公式（如果适用）",
    "dependencies": ["相关控件1", "相关控件2"],
    "is_manual_info": true
}
```

### 2. 修改现有控件说明

直接编辑配置文件中对应控件的信息，然后重新加载配置。

### 3. 重新加载配置

在信息面板插件中点击"重新加载控件说明"按钮，或者重启应用程序。

## 显示效果

当鼠标悬停在配置了手动说明的控件上时，信息面板会显示：

```
Widget Name: FreFin

Widget Type: 频率显示控件
Data Source: 时钟输入控制窗口

Description: PLL1的输入信号频率。该信号来自于时钟输入控制窗口中选择的时钟源，是一个传导过来的值，不能直接编辑。当时钟输入控制窗口中的时钟源选择发生变化时，此控件会自动更新显示相应的频率值。

Calculation: 根据时钟输入控制窗口中选择的时钟源自动设置

Dependencies: 时钟输入控制窗口的时钟源选择, 选定时钟源的频率设置

[手动配置的控件说明]
```

## 信息面板行为说明

信息面板具有智能的显示和清空机制，提供最佳的用户体验：

### 显示行为
- **鼠标悬停有用控件**：显示详细的控件信息（手动配置优先，然后寄存器映射）
- **鼠标悬停无用控件**：自动清空信息显示，保持界面简洁

### 有用控件类型
系统只对以下类型的控件显示信息：
- **QLineEdit**: 文本输入框（如FreFin、VCODistFreq等）
- **QSpinBox**: 整数输入框
- **QDoubleSpinBox**: 浮点数输入框
- **QComboBox**: 下拉选择框
- **QCheckBox**: 复选框

### 清空行为
- **鼠标离开有用控件**：1000ms后自动清空信息显示，给用户充分时间阅读完整信息
- **鼠标悬停无用控件**：50ms后清空信息显示，无需点击即可清空过时信息
- **点击无用区域**：立即清空信息显示，提供即时反馈

### 优势
- 避免显示无意义的"未找到寄存器信息"等提示
- 点击或悬停空白区域不会保留上一次的控件信息
- 界面状态清晰，用户体验流畅

## 已配置的控件

目前已配置的控件包括：

### PLL相关控件（9个）
1. **FreFin**: PLL1输入信号频率显示
2. **VCODistFreq**: VCO分布频率计算结果
3. **PLL1PFDFreq**: PLL1相位频率检测器频率
4. **PLL2PFDFreq**: PLL2相位频率检测器频率
5. **PLL2Cin**: PLL2NDivider输入频率显示
6. **InternalVCOFreq**: 内部VCO频率
7. **OSCinFreq**: 振荡器输入频率设置
8. **ExternalVCXOFreq**: 外部VCXO频率设置
9. **Fin0Freq**: Fin0输入频率设置

### 时钟输出控件（15个）
10. **lineEditFout0Output** - **lineEditFout13Output**: CLKout0-13输出频率显示（14个）
11. **lineEditFvco**: VCO频率输入控件

### 同步系统参考控件（1个）
12. **SyncSysrefFreq1**: 同步系统参考频率1

### 时钟输入控件（4个）
13. **lineEditClkinSelOut**: 当前选择的时钟输入输出频率
14. **lineEditClkin0**: CLKin0时钟输入频率设置
15. **lineEditClkin1**: CLKin1时钟输入频率设置
16. **lineEditClkin2Oscout**: CLKin2/OSCout时钟输入频率设置

### 其他控件（1个）
17. **DACUpdateRate**: DAC更新速率

**总计：30个控件已配置手动说明**

### 注意事项
- **分频器控件**（DCLK0_1DIV等）和**源选择控件**（CLKout0_SRCMUX等）已从手动配置中移除，因为它们在register.json文件中已有完整的寄存器映射信息
- 手动配置仅保留那些无法从寄存器映射获取说明信息的特殊控件
- 这样避免了重复配置和信息冲突，确保信息的一致性

## 最佳实践

1. **描述清晰**: 确保描述信息清晰易懂，说明控件的用途和特性
2. **包含来源**: 明确说明数据的来源，帮助用户理解控件值的获取方式
3. **列出依赖**: 如果控件值依赖其他控件，应在dependencies中列出
4. **计算公式**: 对于计算得出的值，提供计算公式有助于理解
5. **及时更新**: 当控件功能发生变化时，及时更新配置文件

## 故障排除

### 配置不生效
1. 检查JSON文件格式是否正确
2. 确认控件名称与实际控件的objectName()一致
3. 确认is_manual_info字段设置为true
4. 尝试点击"重新加载控件说明"按钮

### 显示异常
1. 检查配置文件编码是否为UTF-8
2. 确认所有必需字段都已填写
3. 查看应用程序日志获取详细错误信息

## 扩展说明

这个机制可以轻松扩展到其他需要特殊说明的控件，只需要在配置文件中添加相应的配置即可。对于复杂的控件关系和计算逻辑，可以通过详细的描述和依赖关系说明来帮助用户理解。
