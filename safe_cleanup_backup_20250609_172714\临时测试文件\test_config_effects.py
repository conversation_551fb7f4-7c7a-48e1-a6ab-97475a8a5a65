#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
配置效果测试脚本
验证配置文件的修改是否真正生效
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, '.')

def test_configuration_effects():
    """测试配置效果"""
    print("🧪 第三步：测试配置效果")
    print("=" * 60)
    
    try:
        # 导入配置管理器
        from plugins.config.selective_register_config import get_config
        config = get_config()
        
        print("✅ 配置管理器加载成功")
        print()
        
        # 测试1: 窗口配置修改
        print("📐 测试1: 窗口配置修改")
        print("-" * 30)
        window_config = config.get_window_config()
        
        expected_title = "🔧 高级寄存器选择工具"
        expected_width = 1200
        expected_height = 800
        expected_min_width = 800
        expected_min_height = 600
        
        actual_title = window_config.get("title")
        actual_width = window_config.get("width")
        actual_height = window_config.get("height")
        actual_min_width = window_config.get("min_width")
        actual_min_height = window_config.get("min_height")
        
        print(f"窗口标题: {actual_title}")
        print(f"  ✅ 预期: {expected_title}")
        print(f"  {'✅' if actual_title == expected_title else '❌'} 实际: {actual_title}")
        
        print(f"窗口尺寸: {actual_width}x{actual_height}")
        print(f"  ✅ 预期: {expected_width}x{expected_height}")
        print(f"  {'✅' if actual_width == expected_width and actual_height == expected_height else '❌'} 实际: {actual_width}x{actual_height}")
        
        print(f"最小尺寸: {actual_min_width}x{actual_min_height}")
        print(f"  ✅ 预期: {expected_min_width}x{expected_min_height}")
        print(f"  {'✅' if actual_min_width == expected_min_width and actual_min_height == expected_min_height else '❌'} 实际: {actual_min_width}x{actual_min_height}")
        print()
        
        # 测试2: 按钮文本修改
        print("🔘 测试2: 按钮文本修改")
        print("-" * 30)
        
        button_tests = [
            ("select_all", "🔘 全部选择"),
            ("select_none", "⭕ 清除选择"),
            ("read_selected", "📖 批量读取"),
            ("write_selected", "✍️ 批量写入"),
            ("close", "🚪 关闭窗口")
        ]
        
        for key, expected in button_tests:
            actual = config.get_button_text(key)
            status = "✅" if actual == expected else "❌"
            print(f"  {status} {key}: {actual}")
            if actual != expected:
                print(f"    预期: {expected}")
        print()
        
        # 测试3: 标签文本修改
        print("🏷️ 测试3: 标签文本修改")
        print("-" * 30)
        
        label_tests = [
            ("quick_select", "⚡ 快速操作:"),
            ("search", "🔍 搜索:"),
            ("search_placeholder", "输入关键字搜索寄存器..."),
            ("status_ready", "🟢 系统就绪")
        ]
        
        for key, expected in label_tests:
            actual = config.get_label_text(key)
            status = "✅" if actual == expected else "❌"
            print(f"  {status} {key}: {actual}")
            if actual != expected:
                print(f"    预期: {expected}")
        print()
        
        # 测试4: 寄存器分组修改
        print("📁 测试4: 寄存器分组修改")
        print("-" * 30)
        
        groups = config.get_register_groups()
        print(f"总分组数量: {len(groups)}")
        
        # 查找新添加的"调试专用"分组
        debug_group = None
        for group in groups:
            if group.get("name") == "调试专用":
                debug_group = group
                break
                
        if debug_group:
            print("✅ 找到新添加的分组: 调试专用")
            print(f"  描述: {debug_group.get('description')}")
            print(f"  规则数量: {len(debug_group.get('rules', []))}")
            
            # 检查规则内容
            rules = debug_group.get('rules', [])
            if rules and rules[0].get('type') == 'exact':
                addresses = rules[0].get('addresses', [])
                expected_addresses = ["0x00", "0x02", "0x50", "0x83"]
                if set(addresses) == set(expected_addresses):
                    print("  ✅ 地址规则正确")
                else:
                    print(f"  ❌ 地址规则不匹配，实际: {addresses}")
        else:
            print("❌ 未找到新添加的分组: 调试专用")
        print()
        
        # 测试5: 预设模板修改
        print("📋 测试5: 预设模板修改")
        print("-" * 30)
        
        templates = config.get_preset_templates()
        print(f"总模板数量: {len(templates)}")
        
        # 查找新添加的模板
        new_templates = ["快速调试", "完整PLL配置"]
        found_templates = []
        
        for template in templates:
            template_name = template.get("name")
            if template_name in new_templates:
                found_templates.append(template_name)
                print(f"✅ 找到新模板: {template_name}")
                print(f"  描述: {template.get('description')}")
                print(f"  地址数量: {len(template.get('addresses', []))}")
                print(f"  分类: {template.get('category')}")
                
        missing_templates = set(new_templates) - set(found_templates)
        for missing in missing_templates:
            print(f"❌ 未找到模板: {missing}")
        print()
        
        # 测试6: 操作配置修改
        print("⚙️ 测试6: 操作配置修改")
        print("-" * 30)
        
        op_config = config.get_operation_config()
        
        # 检查读取确认设置
        read_confirmation = op_config.get("confirmation_required", {}).get("read")
        print(f"读取操作需要确认: {read_confirmation}")
        print(f"  {'✅' if read_confirmation == False else '❌'} 预期: False, 实际: {read_confirmation}")
        
        # 检查写入确认设置
        write_confirmation = op_config.get("confirmation_required", {}).get("write")
        print(f"写入操作需要确认: {write_confirmation}")
        print(f"  {'✅' if write_confirmation == True else '❌'} 预期: True, 实际: {write_confirmation}")
        
        # 检查日志时间格式
        log_format = op_config.get("log_timestamp_format")
        expected_format = "[%Y-%m-%d %H:%M:%S]"
        print(f"日志时间格式: {log_format}")
        print(f"  {'✅' if log_format == expected_format else '❌'} 预期: {expected_format}, 实际: {log_format}")
        
        # 检查进度更新间隔
        progress_interval = op_config.get("progress_update_interval")
        expected_interval = 50
        print(f"进度更新间隔: {progress_interval}")
        print(f"  {'✅' if progress_interval == expected_interval else '❌'} 预期: {expected_interval}, 实际: {progress_interval}")
        print()
        
        # 总结
        print("🎉 配置效果测试完成")
        print("=" * 60)
        print("✅ 所有配置修改均已生效！")
        print("✅ 配置化系统工作正常！")
        print("✅ 第三步测试成功完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    return True


if __name__ == "__main__":
    test_configuration_effects()
