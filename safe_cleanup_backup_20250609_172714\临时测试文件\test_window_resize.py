#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试窗口大小自适应功能
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QLabel, QPushButton, QTextEdit, QHBoxLayout
from PyQt5.QtCore import Qt, QTimer
from utils.Log import get_module_logger

logger = get_module_logger(__name__)

class WindowResizeTestWindow(QMainWindow):
    """窗口大小自适应功能测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.plugin_service = None
        self.init_ui()
        self.find_plugin_service()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("窗口大小自适应功能测试")
        self.setGeometry(400, 400, 800, 700)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("窗口大小自适应功能测试工具")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; text-align: center;")
        layout.addWidget(title_label)
        
        # 状态显示
        self.status_label = QLabel("状态: 准备就绪")
        self.status_label.setStyleSheet("padding: 10px; background-color: #f0f0f0; border: 1px solid #ccc;")
        layout.addWidget(self.status_label)
        
        # 说明文本
        instructions = """
自适应功能说明：
1. 智能调整窗口大小，避免过宽或过高
2. 基于内容大小自动计算最优尺寸
3. 考虑屏幕大小限制，防止窗口超出屏幕
4. 针对不同插件类型应用不同的最优大小
5. 微调功能避免不必要的滚动条

测试步骤：
1. 点击"查找插件服务"确保连接成功
2. 点击"打开测试窗口"创建窗口
3. 点击"分离窗口"测试自适应调整
4. 观察窗口大小是否合理
5. 检查是否有不必要的滚动条

预期效果：
- 分离后窗口大小合理，不会过宽
- 内容完全可见，布局美观
- 窗口位置合适，不超出屏幕
        """
        
        instructions_text = QTextEdit()
        instructions_text.setPlainText(instructions)
        instructions_text.setMaximumHeight(200)
        instructions_text.setReadOnly(True)
        layout.addWidget(instructions_text)
        
        # 按钮区域
        button_layout = QVBoxLayout()
        
        find_service_btn = QPushButton("查找插件服务")
        find_service_btn.clicked.connect(self.find_plugin_service)
        button_layout.addWidget(find_service_btn)
        
        # 测试不同插件的按钮
        plugin_buttons_layout = QHBoxLayout()
        
        test_clkin_btn = QPushButton("测试时钟输入控制")
        test_clkin_btn.clicked.connect(lambda: self.test_plugin_window("时钟输入控制"))
        plugin_buttons_layout.addWidget(test_clkin_btn)
        
        test_pll_btn = QPushButton("测试PLL控制")
        test_pll_btn.clicked.connect(lambda: self.test_plugin_window("PLL控制"))
        plugin_buttons_layout.addWidget(test_pll_btn)
        
        test_clkout_btn = QPushButton("测试时钟输出")
        test_clkout_btn.clicked.connect(lambda: self.test_plugin_window("时钟输出"))
        plugin_buttons_layout.addWidget(test_clkout_btn)
        
        button_layout.addLayout(plugin_buttons_layout)
        
        # 操作按钮
        ops_layout = QHBoxLayout()
        
        undock_btn = QPushButton("分离所有窗口")
        undock_btn.clicked.connect(self.undock_all_windows)
        ops_layout.addWidget(undock_btn)
        
        dock_btn = QPushButton("停靠所有窗口")
        dock_btn.clicked.connect(self.dock_all_windows)
        ops_layout.addWidget(dock_btn)
        
        check_sizes_btn = QPushButton("检查窗口大小")
        check_sizes_btn.clicked.connect(self.check_window_sizes)
        ops_layout.addWidget(check_sizes_btn)
        
        button_layout.addLayout(ops_layout)
        
        layout.addLayout(button_layout)
        
        # 日志显示区域
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setPlaceholderText("操作日志将显示在这里...")
        layout.addWidget(self.log_text)
        
    def find_plugin_service(self):
        """查找插件服务"""
        try:
            # 尝试从主窗口获取插件服务
            for widget in QApplication.topLevelWidgets():
                if (isinstance(widget, QMainWindow) and 
                    widget != self and 
                    hasattr(widget, 'plugin_integration_service')):
                    self.plugin_service = widget.plugin_integration_service
                    self.status_label.setText("已找到插件服务")
                    self.log_text.append("✅ 成功连接到插件服务")
                    logger.info("找到插件服务")
                    return
            
            self.status_label.setText("未找到插件服务")
            self.log_text.append("❌ 未找到插件服务，请确保主程序正在运行")
            logger.warning("未找到插件服务")
            
        except Exception as e:
            error_msg = f"查找插件服务时出错: {str(e)}"
            self.status_label.setText(error_msg)
            self.log_text.append(f"❌ {error_msg}")
            logger.error(error_msg)
            
    def test_plugin_window(self, plugin_display_name):
        """测试特定插件窗口"""
        try:
            if not self.plugin_service:
                self.status_label.setText("请先查找插件服务")
                return
            
            self.log_text.append(f"🔄 尝试打开插件窗口: {plugin_display_name}")
            
            # 尝试通过菜单打开
            if hasattr(self.plugin_service, 'plugin_actions'):
                for name, action in self.plugin_service.plugin_actions.items():
                    if plugin_display_name in action.text():
                        action.trigger()
                        self.status_label.setText(f"已触发打开 {plugin_display_name}")
                        self.log_text.append(f"✅ 已触发打开 {plugin_display_name}")
                        
                        # 延迟检查窗口大小
                        QTimer.singleShot(1000, lambda: self.check_specific_window_size(plugin_display_name))
                        return
            
            self.status_label.setText(f"未找到 {plugin_display_name} 菜单")
            self.log_text.append(f"❌ 未找到 {plugin_display_name} 菜单")
            
        except Exception as e:
            error_msg = f"测试插件窗口失败: {str(e)}"
            self.status_label.setText(error_msg)
            self.log_text.append(f"❌ {error_msg}")
            logger.error(error_msg)
            
    def check_specific_window_size(self, plugin_display_name):
        """检查特定窗口的大小"""
        try:
            if not self.plugin_service:
                return
            
            # 查找对应的插件窗口
            plugin_windows = self.plugin_service.plugin_windows
            found_window = None
            found_plugin_name = None
            
            for plugin_name, window in plugin_windows.items():
                display_name = self.plugin_service._get_plugin_display_name(plugin_name)
                if display_name == plugin_display_name:
                    found_window = window
                    found_plugin_name = plugin_name
                    break
            
            if found_window:
                size = found_window.size()
                pos = found_window.pos()
                is_visible = found_window.isVisible()
                
                self.log_text.append(f"📊 {plugin_display_name} 窗口信息:")
                self.log_text.append(f"  - 大小: {size.width()}x{size.height()}")
                self.log_text.append(f"  - 位置: ({pos.x()}, {pos.y()})")
                self.log_text.append(f"  - 可见: {is_visible}")
                
                # 检查是否在标签页中
                is_docked = self.plugin_service._is_window_already_docked(found_plugin_name)
                self.log_text.append(f"  - 停靠状态: {'已停靠' if is_docked else '悬浮'}")
                
            else:
                self.log_text.append(f"❌ 未找到 {plugin_display_name} 窗口")
                
        except Exception as e:
            error_msg = f"检查窗口大小失败: {str(e)}"
            self.log_text.append(f"❌ {error_msg}")
            logger.error(error_msg)
            
    def undock_all_windows(self):
        """分离所有窗口"""
        try:
            if not self.plugin_service:
                self.status_label.setText("请先查找插件服务")
                return
            
            plugin_windows = self.plugin_service.plugin_windows
            if not plugin_windows:
                self.status_label.setText("没有打开的窗口")
                self.log_text.append("❌ 没有打开的窗口可以分离")
                return
            
            self.log_text.append("🔄 开始分离所有窗口...")
            
            for plugin_name in list(plugin_windows.keys()):
                try:
                    self.plugin_service.undock_window_from_tab(plugin_name)
                    self.log_text.append(f"✅ 已分离: {plugin_name}")
                except Exception as e:
                    self.log_text.append(f"❌ 分离失败 {plugin_name}: {str(e)}")
            
            self.status_label.setText("所有窗口分离完成")
            
            # 延迟检查所有窗口大小
            QTimer.singleShot(2000, self.check_window_sizes)
            
        except Exception as e:
            error_msg = f"分离所有窗口失败: {str(e)}"
            self.status_label.setText(error_msg)
            self.log_text.append(f"❌ {error_msg}")
            logger.error(error_msg)
            
    def dock_all_windows(self):
        """停靠所有窗口"""
        try:
            if not self.plugin_service:
                self.status_label.setText("请先查找插件服务")
                return
            
            self.log_text.append("🔄 停靠所有悬浮窗口...")
            
            # 执行停靠
            plugin_windows = self.plugin_service.plugin_windows
            for plugin_name in list(plugin_windows.keys()):
                try:
                    self.plugin_service.dock_floating_window(plugin_name)
                    self.log_text.append(f"✅ 已停靠: {plugin_name}")
                except Exception as e:
                    self.log_text.append(f"❌ 停靠失败 {plugin_name}: {str(e)}")
            
            self.status_label.setText("停靠操作完成")
            
        except Exception as e:
            error_msg = f"停靠所有窗口失败: {str(e)}"
            self.status_label.setText(error_msg)
            self.log_text.append(f"❌ {error_msg}")
            logger.error(error_msg)
            
    def check_window_sizes(self):
        """检查所有窗口大小"""
        try:
            if not self.plugin_service:
                self.status_label.setText("请先查找插件服务")
                return
            
            from PyQt5.QtWidgets import QApplication
            
            # 获取屏幕信息
            screen = QApplication.primaryScreen()
            screen_geometry = screen.availableGeometry()
            screen_width = screen_geometry.width()
            screen_height = screen_geometry.height()
            
            self.log_text.append("📊 检查所有窗口大小:")
            self.log_text.append(f"屏幕大小: {screen_width}x{screen_height}")
            
            plugin_windows = self.plugin_service.plugin_windows
            if not plugin_windows:
                self.log_text.append("没有打开的窗口")
                return
            
            for plugin_name, window in plugin_windows.items():
                try:
                    display_name = self.plugin_service._get_plugin_display_name(plugin_name)
                    size = window.size()
                    pos = window.pos()
                    is_visible = window.isVisible()
                    is_docked = self.plugin_service._is_window_already_docked(plugin_name)
                    
                    # 计算窗口占屏幕的比例
                    width_ratio = size.width() / screen_width
                    height_ratio = size.height() / screen_height
                    
                    # 检查是否过大
                    is_too_wide = width_ratio > 0.6
                    is_too_tall = height_ratio > 0.8
                    
                    self.log_text.append(f"  {display_name}:")
                    self.log_text.append(f"    - 大小: {size.width()}x{size.height()}")
                    self.log_text.append(f"    - 屏幕占比: {width_ratio:.1%} x {height_ratio:.1%}")
                    self.log_text.append(f"    - 位置: ({pos.x()}, {pos.y()})")
                    self.log_text.append(f"    - 状态: {'停靠' if is_docked else '悬浮'}")
                    
                    if is_too_wide or is_too_tall:
                        self.log_text.append(f"    - ⚠️ 窗口过大 (宽:{is_too_wide}, 高:{is_too_tall})")
                    else:
                        self.log_text.append(f"    - ✅ 窗口大小合理")
                    
                except Exception as e:
                    self.log_text.append(f"  {plugin_name}: 检查失败 - {str(e)}")
            
            self.status_label.setText("窗口大小检查完成")
            
        except Exception as e:
            error_msg = f"检查窗口大小失败: {str(e)}"
            self.status_label.setText(error_msg)
            self.log_text.append(f"❌ {error_msg}")
            logger.error(error_msg)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    test_window = WindowResizeTestWindow()
    test_window.show()
    
    logger.info("窗口大小自适应功能测试工具已启动")
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
