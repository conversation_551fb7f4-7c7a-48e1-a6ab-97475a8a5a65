#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试移除时钟输出旧文件的影响
验证现代化架构是否能正常工作
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.Log import get_module_logger

logger = get_module_logger(__name__)


def test_clk_output_removal():
    """测试移除时钟输出旧文件的影响"""
    print("=" * 60)
    print("测试移除时钟输出旧文件的影响")
    print("=" * 60)
    
    # 创建应用程序
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    
    test_results = []
    
    # 1. 测试现代化工厂是否能正常工作
    print("\n1. 测试现代化工厂...")
    try:
        from ui.factories.ModernToolWindowFactory import ModernToolWindowFactory
        
        # 创建模拟主窗口
        class MockMainWindow:
            def __init__(self):
                self.register_manager = None
                self.clk_output_window = None
                self.sync_sysref_window = None
        
        mock_main_window = MockMainWindow()
        modern_factory = ModernToolWindowFactory(mock_main_window)
        
        # 检查时钟输出配置
        clk_config = modern_factory.HANDLER_CONFIGS.get('clk_output')
        if clk_config:
            print(f"   ✓ 时钟输出配置存在: {clk_config['title']}")
            print(f"   ✓ 现代化处理器: {clk_config['modern_handler']}")
            print(f"   ✓ 传统处理器: {clk_config['legacy_handler']}")
            test_results.append("现代化工厂配置正常")
        else:
            print("   ❌ 时钟输出配置缺失")
            test_results.append("现代化工厂配置缺失")
            
    except Exception as e:
        print(f"   ❌ 现代化工厂测试失败: {str(e)}")
        test_results.append(f"现代化工厂测试失败: {str(e)}")
    
    # 2. 测试现代化处理器是否可用
    print("\n2. 测试现代化时钟输出处理器...")
    try:
        from ui.handlers.ModernClkOutputsHandler import ModernClkOutputsHandler
        print("   ✓ ModernClkOutputsHandler 导入成功")
        test_results.append("现代化处理器可用")
    except Exception as e:
        print(f"   ❌ ModernClkOutputsHandler 导入失败: {str(e)}")
        test_results.append(f"现代化处理器不可用: {str(e)}")
    
    # 3. 测试工具窗口工厂的回退机制
    print("\n3. 测试工具窗口工厂回退机制...")
    try:
        from ui.factories.ToolWindowFactory import ToolWindowFactory
        
        # 创建模拟主窗口
        class MockMainWindow2:
            def __init__(self):
                self.register_manager = None
                self.clk_output_window = None
                self.sync_sysref_window = None
                
        mock_main_window2 = MockMainWindow2()
        tool_factory = ToolWindowFactory(mock_main_window2)
        
        # 检查是否有create_clk_output_window方法
        if hasattr(tool_factory, 'create_clk_output_window'):
            print("   ✓ create_clk_output_window 方法存在")
            test_results.append("工具窗口工厂回退机制正常")
        else:
            print("   ❌ create_clk_output_window 方法缺失")
            test_results.append("工具窗口工厂回退机制缺失")
            
    except Exception as e:
        print(f"   ❌ 工具窗口工厂测试失败: {str(e)}")
        test_results.append(f"工具窗口工厂测试失败: {str(e)}")
    
    # 4. 测试主窗口的时钟输出窗口方法
    print("\n4. 测试主窗口时钟输出方法...")
    try:
        from ui.windows.RegisterMainWindow import RegisterMainWindow
        
        # 检查是否有_show_clk_output_window方法
        if hasattr(RegisterMainWindow, '_show_clk_output_window'):
            print("   ✓ _show_clk_output_window 方法存在")
            test_results.append("主窗口时钟输出方法正常")
        else:
            print("   ❌ _show_clk_output_window 方法缺失")
            test_results.append("主窗口时钟输出方法缺失")
            
    except Exception as e:
        print(f"   ❌ 主窗口测试失败: {str(e)}")
        test_results.append(f"主窗口测试失败: {str(e)}")
    
    # 5. 检查是否还有对旧ClkOutputsHandler的直接引用
    print("\n5. 检查旧处理器引用...")
    try:
        # 尝试导入旧处理器（应该仍然存在，但不应该被直接使用）
        from ui.handlers.ClkOutputsHandler import ClkOutputsHandler
        print("   ⚠ ClkOutputsHandler 仍然存在（这是正常的，作为回退）")
        test_results.append("旧处理器仍存在作为回退")
    except ImportError:
        print("   ✓ ClkOutputsHandler 已移除")
        test_results.append("旧处理器已移除")
    except Exception as e:
        print(f"   ❌ 检查旧处理器时出错: {str(e)}")
        test_results.append(f"检查旧处理器时出错: {str(e)}")
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print("=" * 60)
    
    for i, result in enumerate(test_results, 1):
        print(f"{i}. {result}")
    
    # 判断是否可以安全移除
    critical_issues = [r for r in test_results if "失败" in r or "缺失" in r]
    
    if not critical_issues:
        print("\n✅ 结论: 可以安全移除旧的时钟输出文件")
        print("   现代化架构已经完全接管了时钟输出功能")
        return True
    else:
        print("\n❌ 结论: 暂时不建议移除旧文件")
        print("   发现以下关键问题:")
        for issue in critical_issues:
            print(f"   - {issue}")
        return False


def main():
    """主函数"""
    success = test_clk_output_removal()
    
    if success:
        print("\n🎯 建议操作:")
        print("1. 可以将 ClkOutputsHandler.py 重命名为 ClkOutputsHandler.py.bak")
        print("2. 运行应用程序测试时钟输出功能")
        print("3. 如果一切正常，可以删除备份文件")
    else:
        print("\n⚠️ 建议操作:")
        print("1. 先修复发现的问题")
        print("2. 确保现代化架构完全可用")
        print("3. 再考虑移除旧文件")


if __name__ == '__main__':
    main()
