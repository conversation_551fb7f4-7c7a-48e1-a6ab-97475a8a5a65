# 日志级别控制说明

## 概述

项目现在支持动态控制日志输出级别，可以有效减少不必要的日志信息，提高程序运行效率和日志可读性。

## 日志级别说明

项目支持以下5个日志级别（从最少到最多）：

| 级别 | 说明 | 适用场景 |
|------|------|----------|
| **CRITICAL** | 只显示严重错误 | 生产环境，只关心致命问题 |
| **ERROR** | 显示错误和严重错误 | 生产环境，关心所有错误 |
| **WARNING** | 显示警告、错误和严重错误 | **推荐设置**，日常使用 |
| **INFO** | 显示一般信息和以上级别 | 开发调试，需要了解程序运行状态 |
| **DEBUG** | 显示所有调试信息 | 深度调试，查看详细执行过程 |

## 使用方法

### 方法1：信息面板控制（最便捷）

在应用程序中打开信息面板，切换到"调试日志"标签页：

1. **日志级别选择**：
   - 🔴 ERROR - 只显示错误
   - ⚠️ WARNING - 显示警告和错误（推荐）
   - ℹ️ INFO - 显示信息、警告和错误
   - 🔍 DEBUG - 显示所有日志

2. **实时更新控制**：
   - ✅ 启用：自动刷新日志显示
   - ❌ 禁用：手动刷新日志显示

3. **应用到系统**：
   - 点击"应用级别到系统"按钮
   - 将选择的级别应用到整个应用程序
   - 自动更新配置文件

### 方法2：使用批处理文件

双击运行 `set_log_level.bat` 文件，按照菜单提示选择日志级别：

```
========================================
          日志级别控制工具
========================================

当前可用的日志级别:
  1. CRITICAL - 只显示严重错误 (最少日志)
  2. ERROR    - 显示错误和严重错误
  3. WARNING  - 显示警告、错误和严重错误 (推荐)
  4. INFO     - 显示一般信息和以上级别
  5. DEBUG    - 显示所有调试信息 (最多日志)

  S. 显示当前状态
  Q. 退出

请选择日志级别 (1-5, S, Q):
```

### 方法3：使用命令行工具

```bash
# 显示当前日志状态
python utils/log_control.py status

# 直接设置日志级别
python utils/log_control.py WARNING

# 交互式设置
python utils/log_control.py
```

### 方法4：直接修改配置文件

编辑 `config/default.json` 文件中的 `logging.level` 字段：

```json
{
  "logging": {
    "level": "WARNING",
    "file_enabled": true,
    "console_enabled": true,
    "max_file_size": "10MB",
    "backup_count": 5
  }
}
```

## 测试日志输出

运行测试脚本查看不同级别的日志输出效果：

```bash
python test_logging.py
```

## 配置说明

### 控制台日志
- 级别：跟随配置文件设置
- 格式：`时间 - 模块名 - 级别 - 消息`

### 文件日志
- 级别：始终为 DEBUG（保存所有日志到文件）
- 位置：`log/app.log`
- 格式：`时间 - 模块名 - [文件名:行号] - 级别 - 消息`
- 轮转：文件大小超过10MB时自动轮转，保留5个备份

## 推荐设置

- **日常使用**：WARNING 级别（默认）
- **开发调试**：INFO 或 DEBUG 级别
- **生产环境**：ERROR 或 CRITICAL 级别

## 注意事项

1. **文件日志始终保持 DEBUG 级别**，确保所有信息都被记录到文件中
2. **只有控制台输出受级别控制**，可以减少屏幕上的日志干扰
3. **配置更改立即生效**，无需重启应用程序
4. **所有模块都会使用统一的日志配置**，确保一致性

## 故障排除

如果日志控制不生效，请检查：

1. 确保 `config/default.json` 文件存在且格式正确
2. 确保 `utils/Log.py` 文件没有被修改
3. 重启应用程序以确保配置生效
4. 检查是否有其他模块使用了独立的日志配置

## 文件说明

- `utils/Log.py` - 统一日志管理器
- `utils/log_control.py` - 日志级别控制工具
- `set_log_level.bat` - 批处理控制界面
- `test_logging.py` - 日志测试脚本
- `config/default.json` - 主配置文件
