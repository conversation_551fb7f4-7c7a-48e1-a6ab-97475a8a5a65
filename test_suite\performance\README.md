# 批量操作性能测试

## 概述
这个目录包含了专门用于测试批量读写操作性能的工具和脚本。主要用于评估在模拟模式和硬件模式下的批量操作性能表现。

## 模拟模式 vs 硬件模式

### 流程一致性
✅ **相同的架构层次**：
- 都使用相同的 `BatchOperationManager` 进行批量操作管理
- 都通过 `SPIService` 进行操作队列管理
- 都使用相同的进度更新和UI刷新机制

### 主要差异
🎯 **模拟模式**：
- 直接在内存中模拟读写操作
- 无硬件延迟，响应速度快
- 同步处理，立即返回结果
- 主要受UI更新频率和批次大小影响

🔌 **硬件模式**：
- 通过实际SPI设备进行读写
- 有硬件通信延迟
- 异步回调处理
- 额外受硬件响应时间、SPI通信速度影响

## 测试文件说明

### 核心性能测试
- `test_batch_write.py` - 综合批量读写性能测试
- `test_batch_read_performance.py` - 专项批量读取性能测试
- `run_performance_tests.py` - 性能测试运行器

### 稳定性测试
- `test_batch_write_completion.py` - 批量写入完成流程测试
- `test_batch_write_stability.py` - 批量写入稳定性测试
- `test_crash_after_write_all.py` - 批量操作后崩溃测试

## 快速开始

### 1. 快速性能评估
```bash
# 运行快速性能测试（推荐首次使用）
python test_suite/performance/run_performance_tests.py --test quick
```

### 2. 专项性能测试
```bash
# 批量读取性能测试
python test_suite/performance/run_performance_tests.py --test read

# 批量写入性能测试
python test_suite/performance/run_performance_tests.py --test write

# 运行所有性能测试
python test_suite/performance/run_performance_tests.py --test all
```

### 3. 查看系统信息
```bash
# 显示系统信息
python test_suite/performance/run_performance_tests.py --info
```

## 测试结果解读

### 性能指标
- **执行时间**: 完成批量操作所需的总时间
- **内存变化**: 操作过程中的内存使用变化
- **吞吐量**: 每秒处理的寄存器数量
- **成功率**: 操作成功完成的比例

### 典型性能表现
🎯 **模拟模式**：
- 小批量(10-50个): 通常 < 0.1秒
- 中批量(100-200个): 通常 < 0.5秒
- 大批量(500+个): 通常 < 2秒

🔌 **硬件模式**：
- 性能取决于具体硬件和SPI通信速度
- 通常比模拟模式慢2-10倍
- 受网络延迟和硬件响应时间影响

## 性能优化建议

### 1. 批次大小调优
- 模拟模式：可以使用较大批次(50-100个)
- 硬件模式：建议使用较小批次(10-20个)
- 根据实际硬件性能调整

### 2. UI更新优化
- 批量操作期间暂停频繁的UI更新
- 使用进度条而不是实时显示每个寄存器
- 操作完成后统一刷新UI

### 3. 内存管理
- 大批量操作时注意内存使用
- 及时清理不需要的数据
- 监控内存泄漏

## 故障排除

### 常见问题
1. **测试超时**: 增加超时时间或减少测试数量
2. **内存不足**: 减少批次大小或分批测试
3. **硬件连接失败**: 检查硬件连接和驱动
4. **Qt环境问题**: 设置 `QT_QPA_PLATFORM=offscreen`

### 调试技巧
```bash
# 启用详细日志
export PYTHONPATH=.
python -c "import logging; logging.basicConfig(level=logging.DEBUG)"

# 使用offscreen模式避免GUI问题
export QT_QPA_PLATFORM=offscreen
```
