2025-07-02 13:06:27,677 - main - [main.py:50] - INFO - 中文支持设置完成，使用字体: Microsoft YaHei
2025-07-02 13:06:27,678 - ConfigurationManager - [ConfigurationManager.py:59] - INFO - 已加载配置文件: C:\Users\<USER>\AppData\Local\Temp\_MEI23362\config\default.json
2025-07-02 13:06:27,678 - ConfigurationManager - [ConfigurationManager.py:85] - DEBUG - 跳过可选配置文件: app.json
2025-07-02 13:06:27,678 - ConfigurationManager - [ConfigurationManager.py:59] - INFO - 已加载配置文件: C:\Users\<USER>\AppData\Local\Temp\_MEI23362\config\local.json
2025-07-02 13:06:27,679 - ConfigurationManager - [ConfigurationManager.py:87] - INFO - 配置加载完成，已加载文件: default.json, local.json
2025-07-02 13:06:27,679 - RegisterMainWindow - [RegisterMainWindow.py:172] - INFO - 配置加载完成
2025-07-02 13:06:27,681 - VersionService - [VersionService.py:65] - WARNING - 未找到版本文件，使用默认版本信息
2025-07-02 13:06:27,681 - RegisterMainWindow - [RegisterMainWindow.py:214] - INFO - 窗口标题已设置为: FSJ04832 寄存器配置工具 v1.0.0.0
2025-07-02 13:06:27,681 - RegisterMainWindow - [RegisterMainWindow.py:183] - INFO - 窗口大小设置为: 1840x1100
2025-07-02 13:06:27,681 - RegisterMainWindow - [RegisterMainWindow.py:200] - INFO - 操作常量已加载: TIMEOUT=5000ms, BATCH_SIZE=50
2025-07-02 13:06:27,681 - DIContainer - [DIContainer.py:46] - INFO - 注册单例实例: main_window
2025-07-02 13:06:27,690 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: spi_service
2025-07-02 13:06:27,690 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: register_manager
2025-07-02 13:06:27,690 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: event_bus
2025-07-02 13:06:27,690 - DIContainer - [DIContainer.py:181] - INFO - 核心服务配置完成
2025-07-02 13:06:27,714 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: initialization_manager
2025-07-02 13:06:27,714 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: register_operation_manager
2025-07-02 13:06:27,714 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: display_manager
2025-07-02 13:06:27,714 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: event_coordinator
2025-07-02 13:06:27,714 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: tool_window_factory
2025-07-02 13:06:27,714 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: register_update_processor
2025-07-02 13:06:27,714 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: lifecycle_manager
2025-07-02 13:06:27,714 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: ui_utility_manager
2025-07-02 13:06:27,714 - DIContainer - [DIContainer.py:210] - INFO - UI管理器服务配置完成
2025-07-02 13:06:27,718 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: batch_manager
2025-07-02 13:06:27,718 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: spi_coordinator
2025-07-02 13:06:27,718 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: ui_event_handler
2025-07-02 13:06:27,718 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: tool_window_manager
2025-07-02 13:06:27,718 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: status_config_manager
2025-07-02 13:06:27,718 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: resource_utility_manager
2025-07-02 13:06:27,718 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: tab_window_manager
2025-07-02 13:06:27,718 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: global_event_manager
2025-07-02 13:06:27,718 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: spi_signal_manager
2025-07-02 13:06:27,718 - DIContainer - [DIContainer.py:243] - INFO - 额外管理器服务配置完成
2025-07-02 13:06:27,718 - RegisterMainWindow - [RegisterMainWindow.py:69] - INFO - 依赖注入容器设置完成
2025-07-02 13:06:27,718 - RegisterMainWindow - [RegisterMainWindow.py:87] - INFO - 管理器依赖注入设置完成
2025-07-02 13:06:27,718 - InitializationManager - [InitializationManager.py:31] - INFO - 正在初始化寄存器配置工具...
2025-07-02 13:06:27,720 - InitializationManager - [InitializationManager.py:40] - INFO - 成功加载寄存器配置文件，包含 125 个寄存器
2025-07-02 13:06:27,722 - spi_service - [spi_service.py:67] - INFO - SPI配置已加载: TIMEOUT=5000ms, RETRY_COUNT=3, RETRY_DELAY=1000ms
2025-07-02 13:06:27,722 - spi_service - [spi_service.py:54] - INFO - SPIService initialized
2025-07-02 13:06:27,722 - spi_service - [spi_service.py:84] - INFO - SPIService initializing...
2025-07-02 13:06:27,722 - spi_service - [spi_service.py:421] - DEBUG - Cleaning up SPI thread...
2025-07-02 13:06:27,722 - spi_service - [spi_service.py:455] - DEBUG - SPI thread resources cleaned.
2025-07-02 13:06:27,722 - spi_service_impl - [spi_service_impl.py:79] - INFO - 已启用模拟模式
2025-07-02 13:06:27,722 - spi_service - [spi_service.py:387] - DEBUG - SPI thread components initialized and signals connected.
2025-07-02 13:06:27,722 - spi_service - [spi_service.py:395] - INFO - Starting SPI thread...
2025-07-02 13:06:27,722 - spi_service - [spi_service.py:111] - INFO - Scanning for available COM ports during initialization...
2025-07-02 13:06:27,724 - spi_service_impl - [spi_service_impl.py:411] - INFO - SPI操作处理线程已启动
2025-07-02 13:06:27,731 - spi_service - [spi_service.py:121] - DEBUG - No COM ports detected during initialization.
2025-07-02 13:06:27,731 - spi_service - [spi_service.py:144] - INFO - No usable hardware ports found. Enabling simulation mode.
2025-07-02 13:06:27,733 - spi_service - [spi_service.py:96] - INFO - SPIService initialization complete.
2025-07-02 13:06:27,733 - InitializationManager - [InitializationManager.py:92] - INFO - SPI服务初始化成功，当前模式：模拟模式
2025-07-02 13:06:27,733 - RegisterUpdateBus - [RegisterUpdateBus.py:62] - INFO - RegisterUpdateBus: Initial clock source set based on register default (0x57, CLKin_SEL_MANUAL): ClkIn1
2025-07-02 13:06:27,733 - ModernBaseHandler - [ModernBaseHandler.py:79] - DEBUG - 成功连接到RegisterUpdateBus
2025-07-02 13:06:27,733 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:77] - INFO - 表格配置初始化完成
2025-07-02 13:06:28,129 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:142] - INFO - 位域表格创建完成
2025-07-02 13:06:28,131 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:102] - INFO - 表格UI创建完成
2025-07-02 13:06:28,132 - ModernBaseHandler - [ModernBaseHandler.py:452] - WARNING - _post_init方法已废弃，请使用新的初始化流程
2025-07-02 13:06:28,132 - ModernBaseHandler - [ModernBaseHandler.py:97] - INFO - ModernBaseHandler: 开始完成初始化
2025-07-02 13:06:28,132 - ModernBaseHandler - [ModernBaseHandler.py:112] - DEBUG - 此处理器不需要UI映射，跳过控件映射构建
2025-07-02 13:06:28,132 - ModernBaseHandler - [ModernBaseHandler.py:273] - INFO - ModernRegisterTableHandler: 窗口已居中显示在 (585, 190)，窗口尺寸: 750x700
2025-07-02 13:06:28,132 - ModernBaseHandler - [ModernBaseHandler.py:289] - DEBUG - ModernRegisterTableHandler: 已设置窗口激活功能
2025-07-02 13:06:28,132 - ModernBaseHandler - [ModernBaseHandler.py:242] - INFO - ModernRegisterTableHandler: 设置窗口默认尺寸为 750 x 700，已居中显示
2025-07-02 13:06:28,132 - ModernBaseHandler - [ModernBaseHandler.py:121] - INFO - ModernBaseHandler: 初始化完成
2025-07-02 13:06:28,132 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:51] - INFO - 现代化寄存器表格处理器初始化完成
2025-07-02 13:06:28,132 - ModernBaseHandler - [ModernBaseHandler.py:79] - DEBUG - 成功连接到RegisterUpdateBus
2025-07-02 13:06:28,134 - ModernBaseHandler - [ModernBaseHandler.py:452] - WARNING - _post_init方法已废弃，请使用新的初始化流程
2025-07-02 13:06:28,134 - ModernBaseHandler - [ModernBaseHandler.py:97] - INFO - ModernBaseHandler: 开始完成初始化
2025-07-02 13:06:28,136 - ModernBaseHandler - [ModernBaseHandler.py:112] - DEBUG - 此处理器不需要UI映射，跳过控件映射构建
2025-07-02 13:06:28,136 - ModernBaseHandler - [ModernBaseHandler.py:273] - INFO - ModernRegisterIOHandler: 窗口已居中显示在 (585, 190)，窗口尺寸: 750x700
2025-07-02 13:06:28,136 - ModernBaseHandler - [ModernBaseHandler.py:289] - DEBUG - ModernRegisterIOHandler: 已设置窗口激活功能
2025-07-02 13:06:28,136 - ModernBaseHandler - [ModernBaseHandler.py:242] - INFO - ModernRegisterIOHandler: 设置窗口默认尺寸为 750 x 700，已居中显示
2025-07-02 13:06:28,136 - ModernBaseHandler - [ModernBaseHandler.py:121] - INFO - ModernBaseHandler: 初始化完成
2025-07-02 13:06:28,147 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:78] - INFO - 现代化寄存器IO处理器初始化完成
2025-07-02 13:06:28,147 - ModernBaseHandler - [ModernBaseHandler.py:79] - DEBUG - 成功连接到RegisterUpdateBus
2025-07-02 13:06:28,149 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:81] - DEBUG - 现代化TreeWidget设置最小宽度: 180px，防止布局抖动
2025-07-02 13:06:28,149 - ModernBaseHandler - [ModernBaseHandler.py:452] - WARNING - _post_init方法已废弃，请使用新的初始化流程
2025-07-02 13:06:28,149 - ModernBaseHandler - [ModernBaseHandler.py:97] - INFO - ModernBaseHandler: 开始完成初始化
2025-07-02 13:06:28,150 - ModernBaseHandler - [ModernBaseHandler.py:112] - DEBUG - 此处理器不需要UI映射，跳过控件映射构建
2025-07-02 13:06:28,150 - ModernBaseHandler - [ModernBaseHandler.py:273] - INFO - ModernRegisterTreeHandler: 窗口已居中显示在 (585, 190)，窗口尺寸: 750x700
2025-07-02 13:06:28,150 - ModernBaseHandler - [ModernBaseHandler.py:289] - DEBUG - ModernRegisterTreeHandler: 已设置窗口激活功能
2025-07-02 13:06:28,150 - ModernBaseHandler - [ModernBaseHandler.py:242] - INFO - ModernRegisterTreeHandler: 设置窗口默认尺寸为 750 x 700，已居中显示
2025-07-02 13:06:28,150 - ModernBaseHandler - [ModernBaseHandler.py:121] - INFO - ModernBaseHandler: 初始化完成
2025-07-02 13:06:28,150 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:51] - INFO - 现代化寄存器树处理器初始化完成
2025-07-02 13:06:28,150 - InitializationManager - [InitializationManager.py:145] - INFO - 现代化处理器创建成功
2025-07-02 13:06:28,150 - InitializationManager - [InitializationManager.py:113] - INFO - 使用现代化处理器创建核心处理器
2025-07-02 13:06:28,151 - BatchOperationManager - [BatchOperationManager.py:85] - INFO - 批量操作配置已加载: BATCH_SIZE=50, TIMEOUT=5000ms, UI_UPDATE_INTERVAL=10
2025-07-02 13:06:28,151 - BatchOperationManager - [BatchOperationManager.py:72] - INFO - 使用传统批量操作方式（已优化性能）
2025-07-02 13:06:28,151 - SPIOperationCoordinator - [SPIOperationCoordinator.py:283] - DEBUG - 已连接SPI操作完成信号
2025-07-02 13:06:28,151 - InitializationManager - [InitializationManager.py:182] - INFO - 使用依赖注入创建管理器完成
2025-07-02 13:06:28,323 - MainWindowUI - [MainWindowUI.py:137] - INFO - MainWindowUI: 检查现代化处理器 - IO: True, Table: True
2025-07-02 13:06:28,323 - MainWindowUI - [MainWindowUI.py:138] - INFO - MainWindowUI: IO处理器类型: <class 'ui.handlers.ModernRegisterIOHandler.ModernRegisterIOHandler'>
2025-07-02 13:06:28,323 - MainWindowUI - [MainWindowUI.py:139] - INFO - MainWindowUI: Table处理器类型: <class 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler'>
2025-07-02 13:06:28,323 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:705] - INFO - ModernRegisterIOHandler: get_io_widget() 被调用
2025-07-02 13:06:28,323 - MainWindowUI - [MainWindowUI.py:69] - INFO - MainWindowUI: 使用QSplitter创建稳定的TreeWidget和TableWidget分割布局
2025-07-02 13:06:28,326 - ProgressBarStyleManager - [ProgressBarStyleManager.py:135] - DEBUG - 已为进度条应用绿色样式: default
2025-07-02 13:06:28,326 - MenuManager - [MenuManager.py:330] - INFO - 插件服务尚未可用，跳过工具插件集成（将在插件系统设置完成后执行）
2025-07-02 13:06:28,326 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:128] - INFO - 填充了 125 个寄存器到树视图
2025-07-02 13:06:28,326 - SPISignalManager - [SPISignalManager.py:41] - DEBUG - SPI信号连接完成
2025-07-02 13:06:28,331 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:164] - INFO - 现代化TreeHandler: 选择默认寄存器: 'R0 (0x00)'
2025-07-02 13:06:28,331 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:176] - INFO - 现代化TreeHandler: 树项点击 - 项: 'R0 (0x00)', 地址: 0x00, 列: 0
2025-07-02 13:06:28,331 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:185] - INFO - 现代化TreeHandler: 选择寄存器地址: 0x00 从项 'R0 (0x00)'
2025-07-02 13:06:28,331 - EventCoordinator - [EventCoordinator.py:169] - DEBUG - EventCoordinator: 开始处理寄存器选择 0x00
2025-07-02 13:06:28,331 - EventCoordinator - [EventCoordinator.py:172] - DEBUG - EventCoordinator: 找到显示管理器，调用 handle_register_selection(0x00)
2025-07-02 13:06:28,331 - RegisterDisplayManager - [RegisterDisplayManager.py:209] - DEBUG - DisplayManager: 开始处理寄存器选择 0x00
2025-07-02 13:06:28,331 - RegisterOperationService - [RegisterOperationService.py:420] - INFO - RegisterOperationService: 处理寄存器选择, addr='0x00'
2025-07-02 13:06:28,331 - RegisterOperationService - [RegisterOperationService.py:234] - INFO - RegisterOperationService: 更新UI显示 - 地址: 0x00, 值: 0x1300
2025-07-02 13:06:28,332 - RegisterOperationService - [RegisterOperationService.py:242] - DEBUG - RegisterOperationService: 跳过表格更新，避免重复调用 - 0x00
2025-07-02 13:06:28,332 - RegisterDisplayManager - [RegisterDisplayManager.py:213] - DEBUG - DisplayManager: 寄存器操作服务返回: success=True, reg_num=0, value=0x1300
2025-07-02 13:06:28,332 - RegisterDisplayManager - [RegisterDisplayManager.py:218] - INFO - DisplayManager: 已更新主窗口选中寄存器地址为: 0x00
2025-07-02 13:06:28,332 - RegisterDisplayManager - [RegisterDisplayManager.py:71] - INFO - DisplayManager: 开始更新位字段显示 - 地址: 0x00, 值: 0x1300
2025-07-02 13:06:28,333 - RegisterDisplayManager - [RegisterDisplayManager.py:75] - INFO - DisplayManager: 找到 table_handler: <class 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler'>
2025-07-02 13:06:28,333 - RegisterDisplayManager - [RegisterDisplayManager.py:76] - INFO - DisplayManager: 调用 table_handler.show_bit_fields(0x00, 4864, from_global_update=True)
2025-07-02 13:06:28,333 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:809] - DEBUG - ModernTableHandler: 尝试获取主窗口实例...
2025-07-02 13:06:28,333 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:810] - DEBUG - ModernTableHandler: self类型: <class 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler'>
2025-07-02 13:06:28,333 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:811] - DEBUG - ModernTableHandler: hasattr(self, 'main_window'): False
2025-07-02 13:06:28,333 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:812] - DEBUG - ModernTableHandler: hasattr(self, 'parent'): True
2025-07-02 13:06:28,333 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:821] - DEBUG - ModernTableHandler: parent类型: <class 'ui.windows.RegisterMainWindow.RegisterMainWindow'>
2025-07-02 13:06:28,333 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:828] - DEBUG - ModernTableHandler: parent本身就是主窗口
2025-07-02 13:06:28,333 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:233] - INFO - ModernTableHandler: 开始显示位域信息 - 地址: 0x00, 值: 0x1300
2025-07-02 13:06:28,333 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:246] - INFO - ModernTableHandler: 设置当前寄存器 - 地址: 0x00, 值: 0x1300
2025-07-02 13:06:28,333 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:251] - INFO - ModernTableHandler: 获取到 1 个位域
2025-07-02 13:06:28,333 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:265] - DEBUG - ModernTableHandler: 断开表格信号连接
2025-07-02 13:06:28,333 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:269] - INFO - ModernTableHandler: 开始更新表格内容
2025-07-02 13:06:28,333 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:809] - DEBUG - ModernTableHandler: 尝试获取主窗口实例...
2025-07-02 13:06:28,333 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:810] - DEBUG - ModernTableHandler: self类型: <class 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler'>
2025-07-02 13:06:28,333 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:811] - DEBUG - ModernTableHandler: hasattr(self, 'main_window'): False
2025-07-02 13:06:28,333 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:812] - DEBUG - ModernTableHandler: hasattr(self, 'parent'): True
2025-07-02 13:06:28,333 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:821] - DEBUG - ModernTableHandler: parent类型: <class 'ui.windows.RegisterMainWindow.RegisterMainWindow'>
2025-07-02 13:06:28,333 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:828] - DEBUG - ModernTableHandler: parent本身就是主窗口
2025-07-02 13:06:28,334 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:273] - DEBUG - ModernTableHandler: 重新连接表格信号
2025-07-02 13:06:28,334 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:280] - INFO - ModernTableHandler: 成功显示寄存器 0x00 的 1 个位域
2025-07-02 13:06:28,334 - RegisterDisplayManager - [RegisterDisplayManager.py:78] - INFO - DisplayManager: 位字段显示更新完成
2025-07-02 13:06:28,334 - EventCoordinator - [EventCoordinator.py:174] - DEBUG - EventCoordinator: 显示管理器返回结果: True
2025-07-02 13:06:28,334 - InitializationManager - [InitializationManager.py:334] - INFO - 主窗口初始化完成
2025-07-02 13:06:28,334 - RegisterMainWindow - [RegisterMainWindow.py:247] - INFO - 主窗口点击置顶功能已设置
2025-07-02 13:06:28,337 - DIContainer - [DIContainer.py:46] - INFO - 注册单例实例: plugin_service
2025-07-02 13:06:28,337 - PluginManager - [PluginManager.py:132] - DEBUG - 跳过不存在的插件目录: plugins
2025-07-02 13:06:28,337 - PluginManager - [PluginManager.py:132] - DEBUG - 跳过不存在的插件目录: _internal/plugins
2025-07-02 13:06:28,337 - PluginManager - [PluginManager.py:132] - DEBUG - 跳过不存在的插件目录: ui/tools
2025-07-02 13:06:28,337 - PluginManager - [PluginManager.py:132] - DEBUG - 跳过不存在的插件目录: _internal/ui/tools
2025-07-02 13:06:28,338 - PluginManager - [PluginManager.py:331] - INFO - 找到 0 个工具窗口插件
2025-07-02 13:06:28,338 - PluginMenuService - [PluginMenuService.py:38] - INFO - 没有发现工具窗口插件
2025-07-02 13:06:28,338 - PluginIntegrationService - [PluginIntegrationService.py:86] - INFO - 插件系统初始化完成
2025-07-02 13:06:28,338 - RegisterMainWindow - [RegisterMainWindow.py:134] - INFO - 插件系统设置完成
2025-07-02 13:06:28,519 - RegisterMainWindow - [RegisterMainWindow.py:387] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-02 13:06:28,520 - RegisterMainWindow - [RegisterMainWindow.py:387] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-02 13:06:28,529 - RegisterMainWindow - [RegisterMainWindow.py:406] - DEBUG - 主窗口强制置顶完成
2025-07-02 13:06:28,529 - RegisterMainWindow - [RegisterMainWindow.py:311] - DEBUG - 主窗口已通过焦点置顶
2025-07-02 13:06:28,530 - InitializationManager - [InitializationManager.py:274] - INFO - InitializationManager: 执行延迟端口刷新，确保UI同步端口状态
2025-07-02 13:06:28,530 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:501] - INFO - ModernRegisterIOHandler: 请求SPI服务刷新端口
2025-07-02 13:06:28,530 - spi_service - [spi_service.py:179] - INFO - Refreshing available COM ports...
2025-07-02 13:06:28,539 - spi_service - [spi_service.py:188] - DEBUG - No COM ports detected.
2025-07-02 13:06:28,539 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:509] - DEBUG - ModernRegisterIOHandler: _update_port_combo 被调用，port_combo: <PyQt5.QtWidgets.QComboBox object at 0x0000020108B44B80>, 类型: <class 'PyQt5.QtWidgets.QComboBox'>
2025-07-02 13:06:28,540 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:567] - INFO - ModernRegisterIOHandler: 没有检测到COM端口
2025-07-02 13:06:28,540 - spi_service - [spi_service.py:203] - INFO - Attempting to set SPI port to: None
2025-07-02 13:06:28,547 - spiPrivacy - [spiPrivacy.py:61] - WARNING - 未检测到可用的串口
2025-07-02 13:06:28,547 - spi_service_impl - [spi_service_impl.py:353] - WARNING - 无法连接到SPI端口: None
2025-07-02 13:06:28,548 - spi_service - [spi_service.py:230] - ERROR - Failed to set SPI port to None. Enabling simulation mode.
2025-07-02 13:06:28,548 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:579] - INFO - ModernRegisterIOHandler: 触发状态栏更新
2025-07-02 13:06:28,548 - StatusAndConfigManager - [StatusAndConfigManager.py:25] - INFO - StatusAndConfigManager.update_status_bar() 被调用
2025-07-02 13:06:28,548 - StatusAndConfigManager - [StatusAndConfigManager.py:30] - INFO - 获取到SPI服务状态: {'connected': False, 'port': None, 'mode': 'simulation', 'last_error': '无法连接到端口 None', 'retry_count': 0}
2025-07-02 13:06:28,548 - StatusAndConfigManager - [StatusAndConfigManager.py:33] - INFO - 原始模式文本: 'simulation'
2025-07-02 13:06:28,549 - StatusAndConfigManager - [StatusAndConfigManager.py:60] - INFO - 状态栏已更新: 模式=模拟模式, 端口=模拟模式
2025-07-02 13:06:28,549 - InitializationManager - [InitializationManager.py:277] - INFO - InitializationManager: 端口刷新完成
2025-07-02 13:06:28,562 - RegisterMainWindow - [RegisterMainWindow.py:406] - DEBUG - 主窗口强制置顶完成
2025-07-02 13:06:28,562 - RegisterMainWindow - [RegisterMainWindow.py:315] - DEBUG - 主窗口已通过激活事件置顶
2025-07-02 13:06:28,833 - InitializationManager - [InitializationManager.py:289] - INFO - InitializationManager: 执行延迟状态栏更新
2025-07-02 13:06:28,833 - StatusAndConfigManager - [StatusAndConfigManager.py:25] - INFO - StatusAndConfigManager.update_status_bar() 被调用
2025-07-02 13:06:28,834 - StatusAndConfigManager - [StatusAndConfigManager.py:30] - INFO - 获取到SPI服务状态: {'connected': False, 'port': None, 'mode': 'simulation', 'last_error': '无法连接到端口 None', 'retry_count': 0}
2025-07-02 13:06:28,834 - StatusAndConfigManager - [StatusAndConfigManager.py:33] - INFO - 原始模式文本: 'simulation'
2025-07-02 13:06:28,835 - StatusAndConfigManager - [StatusAndConfigManager.py:60] - INFO - 状态栏已更新: 模式=模拟模式, 端口=模拟模式
2025-07-02 13:06:29,336 - RegisterMainWindow - [RegisterMainWindow.py:281] - INFO - 已为寄存器树安装点击事件过滤器
2025-07-02 13:06:29,336 - RegisterMainWindow - [RegisterMainWindow.py:286] - INFO - 已为寄存器表格安装点击事件过滤器
2025-07-02 13:06:34,052 - RegisterMainWindow - [RegisterMainWindow.py:387] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-02 13:06:34,059 - RegisterMainWindow - [RegisterMainWindow.py:406] - DEBUG - 主窗口强制置顶完成
2025-07-02 13:06:34,059 - RegisterMainWindow - [RegisterMainWindow.py:311] - DEBUG - 主窗口已通过焦点置顶
2025-07-02 13:06:36,415 - ApplicationLifecycleManager - [ApplicationLifecycleManager.py:41] - INFO - 应用程序正在关闭...
2025-07-02 13:06:36,419 - BatchOperationManager - [BatchOperationManager.py:483] - DEBUG - 断开读取信号时出现异常（可能未连接）: 'method' object is not connected
2025-07-02 13:06:36,419 - BatchOperationManager - [BatchOperationManager.py:495] - DEBUG - 断开写入信号时出现异常（可能未连接）: 'method' object is not connected
2025-07-02 13:06:36,419 - BatchOperationManager - [BatchOperationManager.py:533] - INFO - 已强制取消所有批量操作
2025-07-02 13:06:36,419 - ApplicationLifecycleManager - [ApplicationLifecycleManager.py:69] - INFO - 已强制取消所有批量操作并清理资源
2025-07-02 13:06:36,419 - PluginWindowService - [PluginWindowService.py:513] - INFO - 所有插件窗口已关闭
2025-07-02 13:06:36,419 - WindowManagementService - [WindowManagementService.py:200] - INFO - 已通过插件服务关闭所有插件窗口
2025-07-02 13:06:36,419 - WindowManagementService - [WindowManagementService.py:250] - INFO - 已清空所有标签页并隐藏标签页容器
2025-07-02 13:06:36,419 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-02 13:06:36,419 - WindowManagementService - [WindowManagementService.py:227] - INFO - 所有管理的窗口已关闭
2025-07-02 13:06:36,419 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-02 13:06:36,419 - ConfigurationService - [ConfigurationService.py:283] - DEBUG - 设置已更新: simulation_mode = False
2025-07-02 13:06:36,421 - ConfigurationService - [ConfigurationService.py:333] - INFO - 模拟模式设置已保存: False
2025-07-02 13:06:36,421 - spi_service - [spi_service.py:156] - INFO - SPIService cleaning up...
2025-07-02 13:06:36,421 - spi_service - [spi_service.py:421] - DEBUG - Cleaning up SPI thread...
2025-07-02 13:06:36,421 - spi_service - [spi_service.py:428] - DEBUG - Worker stop requested
2025-07-02 13:06:36,421 - spi_service - [spi_service.py:437] - INFO - Requesting SPI thread termination...
2025-07-02 13:06:36,423 - spi_service - [spi_service.py:443] - INFO - SPI thread stopped.
2025-07-02 13:06:36,423 - spi_service - [spi_service.py:455] - DEBUG - SPI thread resources cleaned.
2025-07-02 13:06:36,423 - spi_service - [spi_service.py:166] - INFO - SPIService cleanup finished.
2025-07-02 13:06:36,423 - ApplicationLifecycleManager - [ApplicationLifecycleManager.py:103] - INFO - 应用程序关闭完成
2025-07-02 13:06:36,504 - port_manager - [port_manager.py:211] - INFO - 已清理所有COM端口连接
2025-07-02 13:07:58,776 - main - [main.py:50] - INFO - 中文支持设置完成，使用字体: Microsoft YaHei
2025-07-02 13:07:58,779 - ConfigurationManager - [ConfigurationManager.py:59] - INFO - 已加载配置文件: C:\Users\<USER>\AppData\Local\Temp\_MEI292122\config\default.json
2025-07-02 13:07:58,779 - ConfigurationManager - [ConfigurationManager.py:85] - DEBUG - 跳过可选配置文件: app.json
2025-07-02 13:07:58,781 - ConfigurationManager - [ConfigurationManager.py:59] - INFO - 已加载配置文件: C:\Users\<USER>\AppData\Local\Temp\_MEI292122\config\local.json
2025-07-02 13:07:58,781 - ConfigurationManager - [ConfigurationManager.py:87] - INFO - 配置加载完成，已加载文件: default.json, local.json
2025-07-02 13:07:58,781 - RegisterMainWindow - [RegisterMainWindow.py:172] - INFO - 配置加载完成
2025-07-02 13:07:58,781 - VersionService - [VersionService.py:65] - WARNING - 未找到版本文件，使用默认版本信息
2025-07-02 13:07:58,781 - RegisterMainWindow - [RegisterMainWindow.py:214] - INFO - 窗口标题已设置为: FSJ04832 寄存器配置工具 v1.0.0.0
2025-07-02 13:07:58,781 - RegisterMainWindow - [RegisterMainWindow.py:183] - INFO - 窗口大小设置为: 1840x1100
2025-07-02 13:07:58,781 - RegisterMainWindow - [RegisterMainWindow.py:200] - INFO - 操作常量已加载: TIMEOUT=5000ms, BATCH_SIZE=50
2025-07-02 13:07:58,781 - DIContainer - [DIContainer.py:46] - INFO - 注册单例实例: main_window
2025-07-02 13:07:58,792 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: spi_service
2025-07-02 13:07:58,793 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: register_manager
2025-07-02 13:07:58,793 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: event_bus
2025-07-02 13:07:58,793 - DIContainer - [DIContainer.py:181] - INFO - 核心服务配置完成
2025-07-02 13:07:58,818 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: initialization_manager
2025-07-02 13:07:58,818 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: register_operation_manager
2025-07-02 13:07:58,818 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: display_manager
2025-07-02 13:07:58,818 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: event_coordinator
2025-07-02 13:07:58,818 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: tool_window_factory
2025-07-02 13:07:58,818 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: register_update_processor
2025-07-02 13:07:58,818 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: lifecycle_manager
2025-07-02 13:07:58,818 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: ui_utility_manager
2025-07-02 13:07:58,818 - DIContainer - [DIContainer.py:210] - INFO - UI管理器服务配置完成
2025-07-02 13:07:58,822 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: batch_manager
2025-07-02 13:07:58,822 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: spi_coordinator
2025-07-02 13:07:58,822 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: ui_event_handler
2025-07-02 13:07:58,822 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: tool_window_manager
2025-07-02 13:07:58,822 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: status_config_manager
2025-07-02 13:07:58,822 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: resource_utility_manager
2025-07-02 13:07:58,822 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: tab_window_manager
2025-07-02 13:07:58,822 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: global_event_manager
2025-07-02 13:07:58,822 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: spi_signal_manager
2025-07-02 13:07:58,822 - DIContainer - [DIContainer.py:243] - INFO - 额外管理器服务配置完成
2025-07-02 13:07:58,822 - RegisterMainWindow - [RegisterMainWindow.py:69] - INFO - 依赖注入容器设置完成
2025-07-02 13:07:58,822 - RegisterMainWindow - [RegisterMainWindow.py:87] - INFO - 管理器依赖注入设置完成
2025-07-02 13:07:58,822 - InitializationManager - [InitializationManager.py:31] - INFO - 正在初始化寄存器配置工具...
2025-07-02 13:07:58,825 - InitializationManager - [InitializationManager.py:40] - INFO - 成功加载寄存器配置文件，包含 125 个寄存器
2025-07-02 13:07:58,825 - spi_service - [spi_service.py:67] - INFO - SPI配置已加载: TIMEOUT=5000ms, RETRY_COUNT=3, RETRY_DELAY=1000ms
2025-07-02 13:07:58,825 - spi_service - [spi_service.py:54] - INFO - SPIService initialized
2025-07-02 13:07:58,825 - spi_service - [spi_service.py:84] - INFO - SPIService initializing...
2025-07-02 13:07:58,825 - spi_service - [spi_service.py:421] - DEBUG - Cleaning up SPI thread...
2025-07-02 13:07:58,825 - spi_service - [spi_service.py:455] - DEBUG - SPI thread resources cleaned.
2025-07-02 13:07:58,825 - spi_service_impl - [spi_service_impl.py:79] - INFO - 已启用模拟模式
2025-07-02 13:07:58,825 - spi_service - [spi_service.py:387] - DEBUG - SPI thread components initialized and signals connected.
2025-07-02 13:07:58,825 - spi_service - [spi_service.py:395] - INFO - Starting SPI thread...
2025-07-02 13:07:58,825 - spi_service - [spi_service.py:111] - INFO - Scanning for available COM ports during initialization...
2025-07-02 13:07:58,825 - spi_service_impl - [spi_service_impl.py:411] - INFO - SPI操作处理线程已启动
2025-07-02 13:07:58,836 - spi_service - [spi_service.py:121] - DEBUG - No COM ports detected during initialization.
2025-07-02 13:07:58,836 - spi_service - [spi_service.py:144] - INFO - No usable hardware ports found. Enabling simulation mode.
2025-07-02 13:07:58,836 - spi_service - [spi_service.py:96] - INFO - SPIService initialization complete.
2025-07-02 13:07:58,836 - InitializationManager - [InitializationManager.py:92] - INFO - SPI服务初始化成功，当前模式：模拟模式
2025-07-02 13:07:58,836 - RegisterUpdateBus - [RegisterUpdateBus.py:62] - INFO - RegisterUpdateBus: Initial clock source set based on register default (0x57, CLKin_SEL_MANUAL): ClkIn1
2025-07-02 13:07:58,836 - ModernBaseHandler - [ModernBaseHandler.py:79] - DEBUG - 成功连接到RegisterUpdateBus
2025-07-02 13:07:58,836 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:77] - INFO - 表格配置初始化完成
2025-07-02 13:07:59,206 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:142] - INFO - 位域表格创建完成
2025-07-02 13:07:59,206 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:102] - INFO - 表格UI创建完成
2025-07-02 13:07:59,206 - ModernBaseHandler - [ModernBaseHandler.py:452] - WARNING - _post_init方法已废弃，请使用新的初始化流程
2025-07-02 13:07:59,206 - ModernBaseHandler - [ModernBaseHandler.py:97] - INFO - ModernBaseHandler: 开始完成初始化
2025-07-02 13:07:59,206 - ModernBaseHandler - [ModernBaseHandler.py:112] - DEBUG - 此处理器不需要UI映射，跳过控件映射构建
2025-07-02 13:07:59,206 - ModernBaseHandler - [ModernBaseHandler.py:273] - INFO - ModernRegisterTableHandler: 窗口已居中显示在 (585, 190)，窗口尺寸: 750x700
2025-07-02 13:07:59,206 - ModernBaseHandler - [ModernBaseHandler.py:289] - DEBUG - ModernRegisterTableHandler: 已设置窗口激活功能
2025-07-02 13:07:59,206 - ModernBaseHandler - [ModernBaseHandler.py:242] - INFO - ModernRegisterTableHandler: 设置窗口默认尺寸为 750 x 700，已居中显示
2025-07-02 13:07:59,206 - ModernBaseHandler - [ModernBaseHandler.py:121] - INFO - ModernBaseHandler: 初始化完成
2025-07-02 13:07:59,206 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:51] - INFO - 现代化寄存器表格处理器初始化完成
2025-07-02 13:07:59,206 - ModernBaseHandler - [ModernBaseHandler.py:79] - DEBUG - 成功连接到RegisterUpdateBus
2025-07-02 13:07:59,211 - ModernBaseHandler - [ModernBaseHandler.py:452] - WARNING - _post_init方法已废弃，请使用新的初始化流程
2025-07-02 13:07:59,211 - ModernBaseHandler - [ModernBaseHandler.py:97] - INFO - ModernBaseHandler: 开始完成初始化
2025-07-02 13:07:59,211 - ModernBaseHandler - [ModernBaseHandler.py:112] - DEBUG - 此处理器不需要UI映射，跳过控件映射构建
2025-07-02 13:07:59,211 - ModernBaseHandler - [ModernBaseHandler.py:273] - INFO - ModernRegisterIOHandler: 窗口已居中显示在 (585, 190)，窗口尺寸: 750x700
2025-07-02 13:07:59,211 - ModernBaseHandler - [ModernBaseHandler.py:289] - DEBUG - ModernRegisterIOHandler: 已设置窗口激活功能
2025-07-02 13:07:59,211 - ModernBaseHandler - [ModernBaseHandler.py:242] - INFO - ModernRegisterIOHandler: 设置窗口默认尺寸为 750 x 700，已居中显示
2025-07-02 13:07:59,211 - ModernBaseHandler - [ModernBaseHandler.py:121] - INFO - ModernBaseHandler: 初始化完成
2025-07-02 13:07:59,211 - main - [main.py:50] - INFO - 中文支持设置完成，使用字体: Microsoft YaHei
2025-07-02 13:07:59,211 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:78] - INFO - 现代化寄存器IO处理器初始化完成
2025-07-02 13:07:59,211 - ModernBaseHandler - [ModernBaseHandler.py:79] - DEBUG - 成功连接到RegisterUpdateBus
2025-07-02 13:07:59,211 - ConfigurationManager - [ConfigurationManager.py:59] - INFO - 已加载配置文件: C:\Users\<USER>\AppData\Local\Temp\_MEI335282\config\default.json
2025-07-02 13:07:59,211 - ConfigurationManager - [ConfigurationManager.py:85] - DEBUG - 跳过可选配置文件: app.json
2025-07-02 13:07:59,211 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:81] - DEBUG - 现代化TreeWidget设置最小宽度: 180px，防止布局抖动
2025-07-02 13:07:59,211 - ConfigurationManager - [ConfigurationManager.py:59] - INFO - 已加载配置文件: C:\Users\<USER>\AppData\Local\Temp\_MEI335282\config\local.json
2025-07-02 13:07:59,215 - ModernBaseHandler - [ModernBaseHandler.py:452] - WARNING - _post_init方法已废弃，请使用新的初始化流程
2025-07-02 13:07:59,215 - ConfigurationManager - [ConfigurationManager.py:87] - INFO - 配置加载完成，已加载文件: default.json, local.json
2025-07-02 13:07:59,215 - ModernBaseHandler - [ModernBaseHandler.py:112] - DEBUG - 此处理器不需要UI映射，跳过控件映射构建
2025-07-02 13:07:59,215 - RegisterMainWindow - [RegisterMainWindow.py:172] - INFO - 配置加载完成
2025-07-02 13:07:59,216 - ModernBaseHandler - [ModernBaseHandler.py:273] - INFO - ModernRegisterTreeHandler: 窗口已居中显示在 (585, 190)，窗口尺寸: 750x700
2025-07-02 13:07:59,216 - ModernBaseHandler - [ModernBaseHandler.py:289] - DEBUG - ModernRegisterTreeHandler: 已设置窗口激活功能
2025-07-02 13:07:59,216 - ModernBaseHandler - [ModernBaseHandler.py:242] - INFO - ModernRegisterTreeHandler: 设置窗口默认尺寸为 750 x 700，已居中显示
2025-07-02 13:07:59,216 - ModernBaseHandler - [ModernBaseHandler.py:121] - INFO - ModernBaseHandler: 初始化完成
2025-07-02 13:07:59,216 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:51] - INFO - 现代化寄存器树处理器初始化完成
2025-07-02 13:07:59,216 - InitializationManager - [InitializationManager.py:145] - INFO - 现代化处理器创建成功
2025-07-02 13:07:59,216 - InitializationManager - [InitializationManager.py:113] - INFO - 使用现代化处理器创建核心处理器
2025-07-02 13:07:59,216 - BatchOperationManager - [BatchOperationManager.py:85] - INFO - 批量操作配置已加载: BATCH_SIZE=50, TIMEOUT=5000ms, UI_UPDATE_INTERVAL=10
2025-07-02 13:07:59,216 - BatchOperationManager - [BatchOperationManager.py:72] - INFO - 使用传统批量操作方式（已优化性能）
2025-07-02 13:07:59,216 - SPIOperationCoordinator - [SPIOperationCoordinator.py:283] - DEBUG - 已连接SPI操作完成信号
2025-07-02 13:07:59,216 - InitializationManager - [InitializationManager.py:182] - INFO - 使用依赖注入创建管理器完成
2025-07-02 13:07:59,216 - VersionService - [VersionService.py:65] - WARNING - 未找到版本文件，使用默认版本信息
2025-07-02 13:07:59,216 - RegisterMainWindow - [RegisterMainWindow.py:214] - INFO - 窗口标题已设置为: FSJ04832 寄存器配置工具 v1.0.0.0
2025-07-02 13:07:59,216 - RegisterMainWindow - [RegisterMainWindow.py:183] - INFO - 窗口大小设置为: 1840x1100
2025-07-02 13:07:59,216 - RegisterMainWindow - [RegisterMainWindow.py:200] - INFO - 操作常量已加载: TIMEOUT=5000ms, BATCH_SIZE=50
2025-07-02 13:07:59,216 - DIContainer - [DIContainer.py:46] - INFO - 注册单例实例: main_window
2025-07-02 13:07:59,225 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: spi_service
2025-07-02 13:07:59,225 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: register_manager
2025-07-02 13:07:59,225 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: event_bus
2025-07-02 13:07:59,228 - DIContainer - [DIContainer.py:181] - INFO - 核心服务配置完成
2025-07-02 13:07:59,253 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: initialization_manager
2025-07-02 13:07:59,253 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: register_operation_manager
2025-07-02 13:07:59,253 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: display_manager
2025-07-02 13:07:59,253 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: event_coordinator
2025-07-02 13:07:59,255 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: tool_window_factory
2025-07-02 13:07:59,255 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: register_update_processor
2025-07-02 13:07:59,255 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: lifecycle_manager
2025-07-02 13:07:59,255 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: ui_utility_manager
2025-07-02 13:07:59,255 - DIContainer - [DIContainer.py:210] - INFO - UI管理器服务配置完成
2025-07-02 13:07:59,257 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: batch_manager
2025-07-02 13:07:59,257 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: spi_coordinator
2025-07-02 13:07:59,257 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: ui_event_handler
2025-07-02 13:07:59,257 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: tool_window_manager
2025-07-02 13:07:59,257 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: status_config_manager
2025-07-02 13:07:59,257 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: resource_utility_manager
2025-07-02 13:07:59,257 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: tab_window_manager
2025-07-02 13:07:59,257 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: global_event_manager
2025-07-02 13:07:59,257 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: spi_signal_manager
2025-07-02 13:07:59,257 - DIContainer - [DIContainer.py:243] - INFO - 额外管理器服务配置完成
2025-07-02 13:07:59,257 - RegisterMainWindow - [RegisterMainWindow.py:69] - INFO - 依赖注入容器设置完成
2025-07-02 13:07:59,257 - RegisterMainWindow - [RegisterMainWindow.py:87] - INFO - 管理器依赖注入设置完成
2025-07-02 13:07:59,257 - InitializationManager - [InitializationManager.py:31] - INFO - 正在初始化寄存器配置工具...
2025-07-02 13:07:59,260 - InitializationManager - [InitializationManager.py:40] - INFO - 成功加载寄存器配置文件，包含 125 个寄存器
2025-07-02 13:07:59,266 - spi_service - [spi_service.py:67] - INFO - SPI配置已加载: TIMEOUT=5000ms, RETRY_COUNT=3, RETRY_DELAY=1000ms
2025-07-02 13:07:59,266 - spi_service - [spi_service.py:54] - INFO - SPIService initialized
2025-07-02 13:07:59,266 - spi_service - [spi_service.py:84] - INFO - SPIService initializing...
2025-07-02 13:07:59,266 - spi_service - [spi_service.py:421] - DEBUG - Cleaning up SPI thread...
2025-07-02 13:07:59,266 - spi_service - [spi_service.py:455] - DEBUG - SPI thread resources cleaned.
2025-07-02 13:07:59,266 - spi_service_impl - [spi_service_impl.py:79] - INFO - 已启用模拟模式
2025-07-02 13:07:59,266 - spi_service - [spi_service.py:387] - DEBUG - SPI thread components initialized and signals connected.
2025-07-02 13:07:59,266 - spi_service - [spi_service.py:395] - INFO - Starting SPI thread...
2025-07-02 13:07:59,266 - spi_service - [spi_service.py:111] - INFO - Scanning for available COM ports during initialization...
2025-07-02 13:07:59,266 - spi_service_impl - [spi_service_impl.py:411] - INFO - SPI操作处理线程已启动
2025-07-02 13:07:59,272 - spi_service - [spi_service.py:121] - DEBUG - No COM ports detected during initialization.
2025-07-02 13:07:59,272 - spi_service - [spi_service.py:144] - INFO - No usable hardware ports found. Enabling simulation mode.
2025-07-02 13:07:59,272 - spi_service - [spi_service.py:96] - INFO - SPIService initialization complete.
2025-07-02 13:07:59,272 - InitializationManager - [InitializationManager.py:92] - INFO - SPI服务初始化成功，当前模式：模拟模式
2025-07-02 13:07:59,275 - RegisterUpdateBus - [RegisterUpdateBus.py:62] - INFO - RegisterUpdateBus: Initial clock source set based on register default (0x57, CLKin_SEL_MANUAL): ClkIn1
2025-07-02 13:07:59,275 - ModernBaseHandler - [ModernBaseHandler.py:79] - DEBUG - 成功连接到RegisterUpdateBus
2025-07-02 13:07:59,275 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:77] - INFO - 表格配置初始化完成
2025-07-02 13:07:59,371 - MainWindowUI - [MainWindowUI.py:137] - INFO - MainWindowUI: 检查现代化处理器 - IO: True, Table: True
2025-07-02 13:07:59,371 - MainWindowUI - [MainWindowUI.py:138] - INFO - MainWindowUI: IO处理器类型: <class 'ui.handlers.ModernRegisterIOHandler.ModernRegisterIOHandler'>
2025-07-02 13:07:59,371 - MainWindowUI - [MainWindowUI.py:139] - INFO - MainWindowUI: Table处理器类型: <class 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler'>
2025-07-02 13:07:59,371 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:705] - INFO - ModernRegisterIOHandler: get_io_widget() 被调用
2025-07-02 13:07:59,371 - MainWindowUI - [MainWindowUI.py:69] - INFO - MainWindowUI: 使用QSplitter创建稳定的TreeWidget和TableWidget分割布局
2025-07-02 13:07:59,371 - ProgressBarStyleManager - [ProgressBarStyleManager.py:135] - DEBUG - 已为进度条应用绿色样式: default
2025-07-02 13:07:59,377 - MenuManager - [MenuManager.py:330] - INFO - 插件服务尚未可用，跳过工具插件集成（将在插件系统设置完成后执行）
2025-07-02 13:07:59,380 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:128] - INFO - 填充了 125 个寄存器到树视图
2025-07-02 13:07:59,380 - SPISignalManager - [SPISignalManager.py:41] - DEBUG - SPI信号连接完成
2025-07-02 13:07:59,381 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:164] - INFO - 现代化TreeHandler: 选择默认寄存器: 'R0 (0x00)'
2025-07-02 13:07:59,381 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:176] - INFO - 现代化TreeHandler: 树项点击 - 项: 'R0 (0x00)', 地址: 0x00, 列: 0
2025-07-02 13:07:59,381 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:185] - INFO - 现代化TreeHandler: 选择寄存器地址: 0x00 从项 'R0 (0x00)'
2025-07-02 13:07:59,381 - EventCoordinator - [EventCoordinator.py:169] - DEBUG - EventCoordinator: 开始处理寄存器选择 0x00
2025-07-02 13:07:59,381 - EventCoordinator - [EventCoordinator.py:172] - DEBUG - EventCoordinator: 找到显示管理器，调用 handle_register_selection(0x00)
2025-07-02 13:07:59,381 - RegisterDisplayManager - [RegisterDisplayManager.py:209] - DEBUG - DisplayManager: 开始处理寄存器选择 0x00
2025-07-02 13:07:59,381 - RegisterOperationService - [RegisterOperationService.py:420] - INFO - RegisterOperationService: 处理寄存器选择, addr='0x00'
2025-07-02 13:07:59,381 - RegisterOperationService - [RegisterOperationService.py:234] - INFO - RegisterOperationService: 更新UI显示 - 地址: 0x00, 值: 0x1300
2025-07-02 13:07:59,382 - RegisterOperationService - [RegisterOperationService.py:242] - DEBUG - RegisterOperationService: 跳过表格更新，避免重复调用 - 0x00
2025-07-02 13:07:59,382 - RegisterDisplayManager - [RegisterDisplayManager.py:213] - DEBUG - DisplayManager: 寄存器操作服务返回: success=True, reg_num=0, value=0x1300
2025-07-02 13:07:59,382 - RegisterDisplayManager - [RegisterDisplayManager.py:218] - INFO - DisplayManager: 已更新主窗口选中寄存器地址为: 0x00
2025-07-02 13:07:59,382 - RegisterDisplayManager - [RegisterDisplayManager.py:71] - INFO - DisplayManager: 开始更新位字段显示 - 地址: 0x00, 值: 0x1300
2025-07-02 13:07:59,382 - RegisterDisplayManager - [RegisterDisplayManager.py:75] - INFO - DisplayManager: 找到 table_handler: <class 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler'>
2025-07-02 13:07:59,383 - RegisterDisplayManager - [RegisterDisplayManager.py:76] - INFO - DisplayManager: 调用 table_handler.show_bit_fields(0x00, 4864, from_global_update=True)
2025-07-02 13:07:59,383 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:809] - DEBUG - ModernTableHandler: 尝试获取主窗口实例...
2025-07-02 13:07:59,383 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:810] - DEBUG - ModernTableHandler: self类型: <class 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler'>
2025-07-02 13:07:59,383 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:811] - DEBUG - ModernTableHandler: hasattr(self, 'main_window'): False
2025-07-02 13:07:59,383 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:812] - DEBUG - ModernTableHandler: hasattr(self, 'parent'): True
2025-07-02 13:07:59,383 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:821] - DEBUG - ModernTableHandler: parent类型: <class 'ui.windows.RegisterMainWindow.RegisterMainWindow'>
2025-07-02 13:07:59,383 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:828] - DEBUG - ModernTableHandler: parent本身就是主窗口
2025-07-02 13:07:59,383 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:233] - INFO - ModernTableHandler: 开始显示位域信息 - 地址: 0x00, 值: 0x1300
2025-07-02 13:07:59,383 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:246] - INFO - ModernTableHandler: 设置当前寄存器 - 地址: 0x00, 值: 0x1300
2025-07-02 13:07:59,383 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:251] - INFO - ModernTableHandler: 获取到 1 个位域
2025-07-02 13:07:59,383 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:265] - DEBUG - ModernTableHandler: 断开表格信号连接
2025-07-02 13:07:59,383 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:269] - INFO - ModernTableHandler: 开始更新表格内容
2025-07-02 13:07:59,383 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:809] - DEBUG - ModernTableHandler: 尝试获取主窗口实例...
2025-07-02 13:07:59,383 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:810] - DEBUG - ModernTableHandler: self类型: <class 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler'>
2025-07-02 13:07:59,383 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:811] - DEBUG - ModernTableHandler: hasattr(self, 'main_window'): False
2025-07-02 13:07:59,383 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:812] - DEBUG - ModernTableHandler: hasattr(self, 'parent'): True
2025-07-02 13:07:59,383 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:821] - DEBUG - ModernTableHandler: parent类型: <class 'ui.windows.RegisterMainWindow.RegisterMainWindow'>
2025-07-02 13:07:59,383 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:828] - DEBUG - ModernTableHandler: parent本身就是主窗口
2025-07-02 13:07:59,384 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:273] - DEBUG - ModernTableHandler: 重新连接表格信号
2025-07-02 13:07:59,384 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:280] - INFO - ModernTableHandler: 成功显示寄存器 0x00 的 1 个位域
2025-07-02 13:07:59,384 - RegisterDisplayManager - [RegisterDisplayManager.py:78] - INFO - DisplayManager: 位字段显示更新完成
2025-07-02 13:07:59,384 - EventCoordinator - [EventCoordinator.py:174] - DEBUG - EventCoordinator: 显示管理器返回结果: True
2025-07-02 13:07:59,384 - InitializationManager - [InitializationManager.py:334] - INFO - 主窗口初始化完成
2025-07-02 13:07:59,384 - RegisterMainWindow - [RegisterMainWindow.py:247] - INFO - 主窗口点击置顶功能已设置
2025-07-02 13:07:59,388 - DIContainer - [DIContainer.py:46] - INFO - 注册单例实例: plugin_service
2025-07-02 13:07:59,388 - PluginManager - [PluginManager.py:132] - DEBUG - 跳过不存在的插件目录: plugins
2025-07-02 13:07:59,388 - PluginManager - [PluginManager.py:132] - DEBUG - 跳过不存在的插件目录: _internal/plugins
2025-07-02 13:07:59,388 - PluginManager - [PluginManager.py:132] - DEBUG - 跳过不存在的插件目录: ui/tools
2025-07-02 13:07:59,389 - PluginManager - [PluginManager.py:132] - DEBUG - 跳过不存在的插件目录: _internal/ui/tools
2025-07-02 13:07:59,389 - PluginManager - [PluginManager.py:331] - INFO - 找到 0 个工具窗口插件
2025-07-02 13:07:59,389 - PluginMenuService - [PluginMenuService.py:38] - INFO - 没有发现工具窗口插件
2025-07-02 13:07:59,389 - PluginIntegrationService - [PluginIntegrationService.py:86] - INFO - 插件系统初始化完成
2025-07-02 13:07:59,389 - RegisterMainWindow - [RegisterMainWindow.py:134] - INFO - 插件系统设置完成
2025-07-02 13:07:59,653 - RegisterMainWindow - [RegisterMainWindow.py:387] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-02 13:07:59,654 - RegisterMainWindow - [RegisterMainWindow.py:387] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-02 13:07:59,655 - InitializationManager - [InitializationManager.py:274] - INFO - InitializationManager: 执行延迟端口刷新，确保UI同步端口状态
2025-07-02 13:07:59,655 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:501] - INFO - ModernRegisterIOHandler: 请求SPI服务刷新端口
2025-07-02 13:07:59,655 - spi_service - [spi_service.py:179] - INFO - Refreshing available COM ports...
2025-07-02 13:07:59,660 - spi_service - [spi_service.py:188] - DEBUG - No COM ports detected.
2025-07-02 13:07:59,660 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:509] - DEBUG - ModernRegisterIOHandler: _update_port_combo 被调用，port_combo: <PyQt5.QtWidgets.QComboBox object at 0x000001BF7BC690D0>, 类型: <class 'PyQt5.QtWidgets.QComboBox'>
2025-07-02 13:07:59,660 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:567] - INFO - ModernRegisterIOHandler: 没有检测到COM端口
2025-07-02 13:07:59,660 - spi_service - [spi_service.py:203] - INFO - Attempting to set SPI port to: None
2025-07-02 13:07:59,667 - spiPrivacy - [spiPrivacy.py:61] - WARNING - 未检测到可用的串口
2025-07-02 13:07:59,668 - spi_service_impl - [spi_service_impl.py:353] - WARNING - 无法连接到SPI端口: None
2025-07-02 13:07:59,668 - spi_service - [spi_service.py:230] - ERROR - Failed to set SPI port to None. Enabling simulation mode.
2025-07-02 13:07:59,668 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:579] - INFO - ModernRegisterIOHandler: 触发状态栏更新
2025-07-02 13:07:59,668 - StatusAndConfigManager - [StatusAndConfigManager.py:25] - INFO - StatusAndConfigManager.update_status_bar() 被调用
2025-07-02 13:07:59,668 - StatusAndConfigManager - [StatusAndConfigManager.py:30] - INFO - 获取到SPI服务状态: {'connected': False, 'port': None, 'mode': 'simulation', 'last_error': '无法连接到端口 None', 'retry_count': 0}
2025-07-02 13:07:59,668 - StatusAndConfigManager - [StatusAndConfigManager.py:33] - INFO - 原始模式文本: 'simulation'
2025-07-02 13:07:59,669 - StatusAndConfigManager - [StatusAndConfigManager.py:60] - INFO - 状态栏已更新: 模式=模拟模式, 端口=模拟模式
2025-07-02 13:07:59,669 - InitializationManager - [InitializationManager.py:277] - INFO - InitializationManager: 端口刷新完成
2025-07-02 13:07:59,700 - RegisterMainWindow - [RegisterMainWindow.py:406] - DEBUG - 主窗口强制置顶完成
2025-07-02 13:07:59,700 - RegisterMainWindow - [RegisterMainWindow.py:311] - DEBUG - 主窗口已通过焦点置顶
2025-07-02 13:07:59,716 - RegisterMainWindow - [RegisterMainWindow.py:406] - DEBUG - 主窗口强制置顶完成
2025-07-02 13:07:59,716 - RegisterMainWindow - [RegisterMainWindow.py:315] - DEBUG - 主窗口已通过激活事件置顶
2025-07-02 13:07:59,749 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:142] - INFO - 位域表格创建完成
2025-07-02 13:07:59,749 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:102] - INFO - 表格UI创建完成
2025-07-02 13:07:59,749 - ModernBaseHandler - [ModernBaseHandler.py:452] - WARNING - _post_init方法已废弃，请使用新的初始化流程
2025-07-02 13:07:59,749 - ModernBaseHandler - [ModernBaseHandler.py:97] - INFO - ModernBaseHandler: 开始完成初始化
2025-07-02 13:07:59,749 - ModernBaseHandler - [ModernBaseHandler.py:112] - DEBUG - 此处理器不需要UI映射，跳过控件映射构建
2025-07-02 13:07:59,750 - ModernBaseHandler - [ModernBaseHandler.py:273] - INFO - ModernRegisterTableHandler: 窗口已居中显示在 (585, 190)，窗口尺寸: 750x700
2025-07-02 13:07:59,751 - ModernBaseHandler - [ModernBaseHandler.py:289] - DEBUG - ModernRegisterTableHandler: 已设置窗口激活功能
2025-07-02 13:07:59,751 - ModernBaseHandler - [ModernBaseHandler.py:242] - INFO - ModernRegisterTableHandler: 设置窗口默认尺寸为 750 x 700，已居中显示
2025-07-02 13:07:59,751 - ModernBaseHandler - [ModernBaseHandler.py:121] - INFO - ModernBaseHandler: 初始化完成
2025-07-02 13:07:59,751 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:51] - INFO - 现代化寄存器表格处理器初始化完成
2025-07-02 13:07:59,752 - ModernBaseHandler - [ModernBaseHandler.py:79] - DEBUG - 成功连接到RegisterUpdateBus
2025-07-02 13:07:59,753 - ModernBaseHandler - [ModernBaseHandler.py:452] - WARNING - _post_init方法已废弃，请使用新的初始化流程
2025-07-02 13:07:59,754 - ModernBaseHandler - [ModernBaseHandler.py:97] - INFO - ModernBaseHandler: 开始完成初始化
2025-07-02 13:07:59,754 - ModernBaseHandler - [ModernBaseHandler.py:112] - DEBUG - 此处理器不需要UI映射，跳过控件映射构建
2025-07-02 13:07:59,754 - ModernBaseHandler - [ModernBaseHandler.py:273] - INFO - ModernRegisterIOHandler: 窗口已居中显示在 (585, 190)，窗口尺寸: 750x700
2025-07-02 13:07:59,754 - ModernBaseHandler - [ModernBaseHandler.py:289] - DEBUG - ModernRegisterIOHandler: 已设置窗口激活功能
2025-07-02 13:07:59,754 - ModernBaseHandler - [ModernBaseHandler.py:242] - INFO - ModernRegisterIOHandler: 设置窗口默认尺寸为 750 x 700，已居中显示
2025-07-02 13:07:59,754 - ModernBaseHandler - [ModernBaseHandler.py:121] - INFO - ModernBaseHandler: 初始化完成
2025-07-02 13:07:59,755 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:78] - INFO - 现代化寄存器IO处理器初始化完成
2025-07-02 13:07:59,755 - ModernBaseHandler - [ModernBaseHandler.py:79] - DEBUG - 成功连接到RegisterUpdateBus
2025-07-02 13:07:59,756 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:81] - DEBUG - 现代化TreeWidget设置最小宽度: 180px，防止布局抖动
2025-07-02 13:07:59,756 - ModernBaseHandler - [ModernBaseHandler.py:452] - WARNING - _post_init方法已废弃，请使用新的初始化流程
2025-07-02 13:07:59,756 - ModernBaseHandler - [ModernBaseHandler.py:97] - INFO - ModernBaseHandler: 开始完成初始化
2025-07-02 13:07:59,756 - ModernBaseHandler - [ModernBaseHandler.py:112] - DEBUG - 此处理器不需要UI映射，跳过控件映射构建
2025-07-02 13:07:59,756 - ModernBaseHandler - [ModernBaseHandler.py:273] - INFO - ModernRegisterTreeHandler: 窗口已居中显示在 (585, 190)，窗口尺寸: 750x700
2025-07-02 13:07:59,756 - ModernBaseHandler - [ModernBaseHandler.py:289] - DEBUG - ModernRegisterTreeHandler: 已设置窗口激活功能
2025-07-02 13:07:59,756 - ModernBaseHandler - [ModernBaseHandler.py:242] - INFO - ModernRegisterTreeHandler: 设置窗口默认尺寸为 750 x 700，已居中显示
2025-07-02 13:07:59,756 - ModernBaseHandler - [ModernBaseHandler.py:121] - INFO - ModernBaseHandler: 初始化完成
2025-07-02 13:07:59,756 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:51] - INFO - 现代化寄存器树处理器初始化完成
2025-07-02 13:07:59,756 - InitializationManager - [InitializationManager.py:145] - INFO - 现代化处理器创建成功
2025-07-02 13:07:59,756 - InitializationManager - [InitializationManager.py:113] - INFO - 使用现代化处理器创建核心处理器
2025-07-02 13:07:59,756 - BatchOperationManager - [BatchOperationManager.py:85] - INFO - 批量操作配置已加载: BATCH_SIZE=50, TIMEOUT=5000ms, UI_UPDATE_INTERVAL=10
2025-07-02 13:07:59,756 - BatchOperationManager - [BatchOperationManager.py:72] - INFO - 使用传统批量操作方式（已优化性能）
2025-07-02 13:07:59,756 - SPIOperationCoordinator - [SPIOperationCoordinator.py:283] - DEBUG - 已连接SPI操作完成信号
2025-07-02 13:07:59,757 - InitializationManager - [InitializationManager.py:182] - INFO - 使用依赖注入创建管理器完成
2025-07-02 13:07:59,881 - InitializationManager - [InitializationManager.py:289] - INFO - InitializationManager: 执行延迟状态栏更新
2025-07-02 13:07:59,881 - StatusAndConfigManager - [StatusAndConfigManager.py:25] - INFO - StatusAndConfigManager.update_status_bar() 被调用
2025-07-02 13:07:59,881 - StatusAndConfigManager - [StatusAndConfigManager.py:30] - INFO - 获取到SPI服务状态: {'connected': False, 'port': None, 'mode': 'simulation', 'last_error': '无法连接到端口 None', 'retry_count': 0}
2025-07-02 13:07:59,881 - StatusAndConfigManager - [StatusAndConfigManager.py:33] - INFO - 原始模式文本: 'simulation'
2025-07-02 13:07:59,882 - StatusAndConfigManager - [StatusAndConfigManager.py:60] - INFO - 状态栏已更新: 模式=模拟模式, 端口=模拟模式
2025-07-02 13:07:59,981 - MainWindowUI - [MainWindowUI.py:137] - INFO - MainWindowUI: 检查现代化处理器 - IO: True, Table: True
2025-07-02 13:07:59,981 - MainWindowUI - [MainWindowUI.py:138] - INFO - MainWindowUI: IO处理器类型: <class 'ui.handlers.ModernRegisterIOHandler.ModernRegisterIOHandler'>
2025-07-02 13:07:59,981 - MainWindowUI - [MainWindowUI.py:139] - INFO - MainWindowUI: Table处理器类型: <class 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler'>
2025-07-02 13:07:59,981 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:705] - INFO - ModernRegisterIOHandler: get_io_widget() 被调用
2025-07-02 13:07:59,983 - MainWindowUI - [MainWindowUI.py:69] - INFO - MainWindowUI: 使用QSplitter创建稳定的TreeWidget和TableWidget分割布局
2025-07-02 13:07:59,986 - ProgressBarStyleManager - [ProgressBarStyleManager.py:135] - DEBUG - 已为进度条应用绿色样式: default
2025-07-02 13:07:59,987 - MenuManager - [MenuManager.py:330] - INFO - 插件服务尚未可用，跳过工具插件集成（将在插件系统设置完成后执行）
2025-07-02 13:07:59,990 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:128] - INFO - 填充了 125 个寄存器到树视图
2025-07-02 13:07:59,990 - SPISignalManager - [SPISignalManager.py:41] - DEBUG - SPI信号连接完成
2025-07-02 13:07:59,990 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:164] - INFO - 现代化TreeHandler: 选择默认寄存器: 'R0 (0x00)'
2025-07-02 13:07:59,991 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:176] - INFO - 现代化TreeHandler: 树项点击 - 项: 'R0 (0x00)', 地址: 0x00, 列: 0
2025-07-02 13:07:59,991 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:185] - INFO - 现代化TreeHandler: 选择寄存器地址: 0x00 从项 'R0 (0x00)'
2025-07-02 13:07:59,991 - EventCoordinator - [EventCoordinator.py:169] - DEBUG - EventCoordinator: 开始处理寄存器选择 0x00
2025-07-02 13:07:59,991 - EventCoordinator - [EventCoordinator.py:172] - DEBUG - EventCoordinator: 找到显示管理器，调用 handle_register_selection(0x00)
2025-07-02 13:07:59,991 - RegisterDisplayManager - [RegisterDisplayManager.py:209] - DEBUG - DisplayManager: 开始处理寄存器选择 0x00
2025-07-02 13:07:59,991 - RegisterOperationService - [RegisterOperationService.py:420] - INFO - RegisterOperationService: 处理寄存器选择, addr='0x00'
2025-07-02 13:07:59,991 - RegisterOperationService - [RegisterOperationService.py:234] - INFO - RegisterOperationService: 更新UI显示 - 地址: 0x00, 值: 0x1300
2025-07-02 13:07:59,991 - RegisterOperationService - [RegisterOperationService.py:242] - DEBUG - RegisterOperationService: 跳过表格更新，避免重复调用 - 0x00
2025-07-02 13:07:59,991 - RegisterDisplayManager - [RegisterDisplayManager.py:213] - DEBUG - DisplayManager: 寄存器操作服务返回: success=True, reg_num=0, value=0x1300
2025-07-02 13:07:59,991 - RegisterDisplayManager - [RegisterDisplayManager.py:218] - INFO - DisplayManager: 已更新主窗口选中寄存器地址为: 0x00
2025-07-02 13:07:59,991 - RegisterDisplayManager - [RegisterDisplayManager.py:71] - INFO - DisplayManager: 开始更新位字段显示 - 地址: 0x00, 值: 0x1300
2025-07-02 13:07:59,991 - RegisterDisplayManager - [RegisterDisplayManager.py:75] - INFO - DisplayManager: 找到 table_handler: <class 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler'>
2025-07-02 13:07:59,992 - RegisterDisplayManager - [RegisterDisplayManager.py:76] - INFO - DisplayManager: 调用 table_handler.show_bit_fields(0x00, 4864, from_global_update=True)
2025-07-02 13:07:59,992 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:809] - DEBUG - ModernTableHandler: 尝试获取主窗口实例...
2025-07-02 13:07:59,992 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:810] - DEBUG - ModernTableHandler: self类型: <class 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler'>
2025-07-02 13:07:59,992 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:811] - DEBUG - ModernTableHandler: hasattr(self, 'main_window'): False
2025-07-02 13:07:59,992 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:812] - DEBUG - ModernTableHandler: hasattr(self, 'parent'): True
2025-07-02 13:07:59,992 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:821] - DEBUG - ModernTableHandler: parent类型: <class 'ui.windows.RegisterMainWindow.RegisterMainWindow'>
2025-07-02 13:07:59,992 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:828] - DEBUG - ModernTableHandler: parent本身就是主窗口
2025-07-02 13:07:59,992 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:233] - INFO - ModernTableHandler: 开始显示位域信息 - 地址: 0x00, 值: 0x1300
2025-07-02 13:07:59,992 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:246] - INFO - ModernTableHandler: 设置当前寄存器 - 地址: 0x00, 值: 0x1300
2025-07-02 13:07:59,992 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:251] - INFO - ModernTableHandler: 获取到 1 个位域
2025-07-02 13:07:59,992 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:265] - DEBUG - ModernTableHandler: 断开表格信号连接
2025-07-02 13:07:59,992 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:269] - INFO - ModernTableHandler: 开始更新表格内容
2025-07-02 13:07:59,992 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:809] - DEBUG - ModernTableHandler: 尝试获取主窗口实例...
2025-07-02 13:07:59,992 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:810] - DEBUG - ModernTableHandler: self类型: <class 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler'>
2025-07-02 13:07:59,992 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:811] - DEBUG - ModernTableHandler: hasattr(self, 'main_window'): False
2025-07-02 13:07:59,992 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:812] - DEBUG - ModernTableHandler: hasattr(self, 'parent'): True
2025-07-02 13:07:59,992 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:821] - DEBUG - ModernTableHandler: parent类型: <class 'ui.windows.RegisterMainWindow.RegisterMainWindow'>
2025-07-02 13:07:59,992 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:828] - DEBUG - ModernTableHandler: parent本身就是主窗口
2025-07-02 13:07:59,993 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:273] - DEBUG - ModernTableHandler: 重新连接表格信号
2025-07-02 13:07:59,993 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:280] - INFO - ModernTableHandler: 成功显示寄存器 0x00 的 1 个位域
2025-07-02 13:07:59,993 - RegisterDisplayManager - [RegisterDisplayManager.py:78] - INFO - DisplayManager: 位字段显示更新完成
2025-07-02 13:07:59,993 - EventCoordinator - [EventCoordinator.py:174] - DEBUG - EventCoordinator: 显示管理器返回结果: True
2025-07-02 13:07:59,993 - InitializationManager - [InitializationManager.py:334] - INFO - 主窗口初始化完成
2025-07-02 13:07:59,993 - RegisterMainWindow - [RegisterMainWindow.py:247] - INFO - 主窗口点击置顶功能已设置
2025-07-02 13:07:59,997 - DIContainer - [DIContainer.py:46] - INFO - 注册单例实例: plugin_service
2025-07-02 13:07:59,997 - PluginManager - [PluginManager.py:132] - DEBUG - 跳过不存在的插件目录: plugins
2025-07-02 13:07:59,997 - PluginManager - [PluginManager.py:132] - DEBUG - 跳过不存在的插件目录: _internal/plugins
2025-07-02 13:07:59,997 - PluginManager - [PluginManager.py:132] - DEBUG - 跳过不存在的插件目录: ui/tools
2025-07-02 13:07:59,997 - PluginManager - [PluginManager.py:132] - DEBUG - 跳过不存在的插件目录: _internal/ui/tools
2025-07-02 13:07:59,997 - PluginManager - [PluginManager.py:331] - INFO - 找到 0 个工具窗口插件
2025-07-02 13:07:59,998 - PluginMenuService - [PluginMenuService.py:38] - INFO - 没有发现工具窗口插件
2025-07-02 13:07:59,998 - PluginIntegrationService - [PluginIntegrationService.py:86] - INFO - 插件系统初始化完成
2025-07-02 13:07:59,998 - RegisterMainWindow - [RegisterMainWindow.py:134] - INFO - 插件系统设置完成
2025-07-02 13:08:00,161 - RegisterMainWindow - [RegisterMainWindow.py:387] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-02 13:08:00,161 - RegisterMainWindow - [RegisterMainWindow.py:387] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-02 13:08:00,170 - RegisterMainWindow - [RegisterMainWindow.py:406] - DEBUG - 主窗口强制置顶完成
2025-07-02 13:08:00,170 - RegisterMainWindow - [RegisterMainWindow.py:311] - DEBUG - 主窗口已通过焦点置顶
2025-07-02 13:08:00,182 - RegisterMainWindow - [RegisterMainWindow.py:406] - DEBUG - 主窗口强制置顶完成
2025-07-02 13:08:00,182 - RegisterMainWindow - [RegisterMainWindow.py:315] - DEBUG - 主窗口已通过激活事件置顶
2025-07-02 13:08:00,195 - InitializationManager - [InitializationManager.py:274] - INFO - InitializationManager: 执行延迟端口刷新，确保UI同步端口状态
2025-07-02 13:08:00,196 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:501] - INFO - ModernRegisterIOHandler: 请求SPI服务刷新端口
2025-07-02 13:08:00,196 - spi_service - [spi_service.py:179] - INFO - Refreshing available COM ports...
2025-07-02 13:08:00,201 - spi_service - [spi_service.py:188] - DEBUG - No COM ports detected.
2025-07-02 13:08:00,202 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:509] - DEBUG - ModernRegisterIOHandler: _update_port_combo 被调用，port_combo: <PyQt5.QtWidgets.QComboBox object at 0x000001AD29B490D0>, 类型: <class 'PyQt5.QtWidgets.QComboBox'>
2025-07-02 13:08:00,202 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:567] - INFO - ModernRegisterIOHandler: 没有检测到COM端口
2025-07-02 13:08:00,202 - spi_service - [spi_service.py:203] - INFO - Attempting to set SPI port to: None
2025-07-02 13:08:00,207 - spiPrivacy - [spiPrivacy.py:61] - WARNING - 未检测到可用的串口
2025-07-02 13:08:00,207 - spi_service_impl - [spi_service_impl.py:353] - WARNING - 无法连接到SPI端口: None
2025-07-02 13:08:00,207 - spi_service - [spi_service.py:230] - ERROR - Failed to set SPI port to None. Enabling simulation mode.
2025-07-02 13:08:00,207 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:579] - INFO - ModernRegisterIOHandler: 触发状态栏更新
2025-07-02 13:08:00,207 - StatusAndConfigManager - [StatusAndConfigManager.py:25] - INFO - StatusAndConfigManager.update_status_bar() 被调用
2025-07-02 13:08:00,207 - StatusAndConfigManager - [StatusAndConfigManager.py:30] - INFO - 获取到SPI服务状态: {'connected': False, 'port': None, 'mode': 'simulation', 'last_error': '无法连接到端口 None', 'retry_count': 0}
2025-07-02 13:08:00,207 - StatusAndConfigManager - [StatusAndConfigManager.py:33] - INFO - 原始模式文本: 'simulation'
2025-07-02 13:08:00,208 - StatusAndConfigManager - [StatusAndConfigManager.py:60] - INFO - 状态栏已更新: 模式=模拟模式, 端口=模拟模式
2025-07-02 13:08:00,208 - InitializationManager - [InitializationManager.py:277] - INFO - InitializationManager: 端口刷新完成
2025-07-02 13:08:00,385 - RegisterMainWindow - [RegisterMainWindow.py:281] - INFO - 已为寄存器树安装点击事件过滤器
2025-07-02 13:08:00,385 - RegisterMainWindow - [RegisterMainWindow.py:286] - INFO - 已为寄存器表格安装点击事件过滤器
2025-07-02 13:08:00,491 - InitializationManager - [InitializationManager.py:289] - INFO - InitializationManager: 执行延迟状态栏更新
2025-07-02 13:08:00,491 - StatusAndConfigManager - [StatusAndConfigManager.py:25] - INFO - StatusAndConfigManager.update_status_bar() 被调用
2025-07-02 13:08:00,492 - StatusAndConfigManager - [StatusAndConfigManager.py:30] - INFO - 获取到SPI服务状态: {'connected': False, 'port': None, 'mode': 'simulation', 'last_error': '无法连接到端口 None', 'retry_count': 0}
2025-07-02 13:08:00,492 - StatusAndConfigManager - [StatusAndConfigManager.py:33] - INFO - 原始模式文本: 'simulation'
2025-07-02 13:08:00,493 - StatusAndConfigManager - [StatusAndConfigManager.py:60] - INFO - 状态栏已更新: 模式=模拟模式, 端口=模拟模式
2025-07-02 13:08:00,995 - RegisterMainWindow - [RegisterMainWindow.py:281] - INFO - 已为寄存器树安装点击事件过滤器
2025-07-02 13:08:00,995 - RegisterMainWindow - [RegisterMainWindow.py:286] - INFO - 已为寄存器表格安装点击事件过滤器
2025-07-02 13:08:08,548 - ApplicationLifecycleManager - [ApplicationLifecycleManager.py:41] - INFO - 应用程序正在关闭...
2025-07-02 13:08:08,551 - BatchOperationManager - [BatchOperationManager.py:483] - DEBUG - 断开读取信号时出现异常（可能未连接）: 'method' object is not connected
2025-07-02 13:08:08,553 - BatchOperationManager - [BatchOperationManager.py:495] - DEBUG - 断开写入信号时出现异常（可能未连接）: 'method' object is not connected
2025-07-02 13:08:08,553 - BatchOperationManager - [BatchOperationManager.py:533] - INFO - 已强制取消所有批量操作
2025-07-02 13:08:08,553 - ApplicationLifecycleManager - [ApplicationLifecycleManager.py:69] - INFO - 已强制取消所有批量操作并清理资源
2025-07-02 13:08:08,553 - PluginWindowService - [PluginWindowService.py:513] - INFO - 所有插件窗口已关闭
2025-07-02 13:08:08,553 - WindowManagementService - [WindowManagementService.py:200] - INFO - 已通过插件服务关闭所有插件窗口
2025-07-02 13:08:08,553 - WindowManagementService - [WindowManagementService.py:250] - INFO - 已清空所有标签页并隐藏标签页容器
2025-07-02 13:08:08,553 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-02 13:08:08,553 - WindowManagementService - [WindowManagementService.py:227] - INFO - 所有管理的窗口已关闭
2025-07-02 13:08:08,553 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-02 13:08:08,553 - ConfigurationService - [ConfigurationService.py:283] - DEBUG - 设置已更新: simulation_mode = False
2025-07-02 13:08:08,553 - ConfigurationService - [ConfigurationService.py:333] - INFO - 模拟模式设置已保存: False
2025-07-02 13:08:08,553 - spi_service - [spi_service.py:156] - INFO - SPIService cleaning up...
2025-07-02 13:08:08,553 - spi_service - [spi_service.py:421] - DEBUG - Cleaning up SPI thread...
2025-07-02 13:08:08,553 - spi_service - [spi_service.py:428] - DEBUG - Worker stop requested
2025-07-02 13:08:08,553 - spi_service - [spi_service.py:437] - INFO - Requesting SPI thread termination...
2025-07-02 13:08:08,558 - spi_service - [spi_service.py:443] - INFO - SPI thread stopped.
2025-07-02 13:08:08,560 - spi_service - [spi_service.py:455] - DEBUG - SPI thread resources cleaned.
2025-07-02 13:08:08,560 - spi_service - [spi_service.py:166] - INFO - SPIService cleanup finished.
2025-07-02 13:08:08,560 - ApplicationLifecycleManager - [ApplicationLifecycleManager.py:103] - INFO - 应用程序关闭完成
2025-07-02 13:08:08,621 - port_manager - [port_manager.py:211] - INFO - 已清理所有COM端口连接
2025-07-02 13:08:13,651 - ApplicationLifecycleManager - [ApplicationLifecycleManager.py:41] - INFO - 应用程序正在关闭...
2025-07-02 13:08:13,656 - BatchOperationManager - [BatchOperationManager.py:483] - DEBUG - 断开读取信号时出现异常（可能未连接）: 'method' object is not connected
2025-07-02 13:08:13,656 - BatchOperationManager - [BatchOperationManager.py:495] - DEBUG - 断开写入信号时出现异常（可能未连接）: 'method' object is not connected
2025-07-02 13:08:13,656 - BatchOperationManager - [BatchOperationManager.py:533] - INFO - 已强制取消所有批量操作
2025-07-02 13:08:13,656 - ApplicationLifecycleManager - [ApplicationLifecycleManager.py:69] - INFO - 已强制取消所有批量操作并清理资源
2025-07-02 13:08:13,656 - PluginWindowService - [PluginWindowService.py:513] - INFO - 所有插件窗口已关闭
2025-07-02 13:08:13,656 - WindowManagementService - [WindowManagementService.py:200] - INFO - 已通过插件服务关闭所有插件窗口
2025-07-02 13:08:13,656 - WindowManagementService - [WindowManagementService.py:250] - INFO - 已清空所有标签页并隐藏标签页容器
2025-07-02 13:08:13,656 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-02 13:08:13,656 - WindowManagementService - [WindowManagementService.py:227] - INFO - 所有管理的窗口已关闭
2025-07-02 13:08:13,656 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-02 13:08:13,656 - ConfigurationService - [ConfigurationService.py:283] - DEBUG - 设置已更新: simulation_mode = False
2025-07-02 13:08:13,656 - ConfigurationService - [ConfigurationService.py:333] - INFO - 模拟模式设置已保存: False
2025-07-02 13:08:13,656 - spi_service - [spi_service.py:156] - INFO - SPIService cleaning up...
2025-07-02 13:08:13,656 - spi_service - [spi_service.py:421] - DEBUG - Cleaning up SPI thread...
2025-07-02 13:08:13,659 - spi_service - [spi_service.py:428] - DEBUG - Worker stop requested
2025-07-02 13:08:13,659 - spi_service - [spi_service.py:437] - INFO - Requesting SPI thread termination...
2025-07-02 13:08:13,680 - spi_service - [spi_service.py:443] - INFO - SPI thread stopped.
2025-07-02 13:08:13,682 - spi_service - [spi_service.py:455] - DEBUG - SPI thread resources cleaned.
2025-07-02 13:08:13,682 - spi_service - [spi_service.py:166] - INFO - SPIService cleanup finished.
2025-07-02 13:08:13,683 - ApplicationLifecycleManager - [ApplicationLifecycleManager.py:103] - INFO - 应用程序关闭完成
2025-07-02 13:08:13,770 - port_manager - [port_manager.py:211] - INFO - 已清理所有COM端口连接
2025-07-02 13:08:20,060 - main - [main.py:50] - INFO - 中文支持设置完成，使用字体: Microsoft YaHei
2025-07-02 13:08:20,060 - ConfigurationManager - [ConfigurationManager.py:59] - INFO - 已加载配置文件: C:\Users\<USER>\AppData\Local\Temp\_MEI134962\config\default.json
2025-07-02 13:08:20,065 - ConfigurationManager - [ConfigurationManager.py:85] - DEBUG - 跳过可选配置文件: app.json
2025-07-02 13:08:20,065 - ConfigurationManager - [ConfigurationManager.py:59] - INFO - 已加载配置文件: C:\Users\<USER>\AppData\Local\Temp\_MEI134962\config\local.json
2025-07-02 13:08:20,065 - ConfigurationManager - [ConfigurationManager.py:87] - INFO - 配置加载完成，已加载文件: default.json, local.json
2025-07-02 13:08:20,065 - RegisterMainWindow - [RegisterMainWindow.py:172] - INFO - 配置加载完成
2025-07-02 13:08:20,065 - VersionService - [VersionService.py:65] - WARNING - 未找到版本文件，使用默认版本信息
2025-07-02 13:08:20,065 - RegisterMainWindow - [RegisterMainWindow.py:214] - INFO - 窗口标题已设置为: FSJ04832 寄存器配置工具 v1.0.0.0
2025-07-02 13:08:20,065 - RegisterMainWindow - [RegisterMainWindow.py:183] - INFO - 窗口大小设置为: 1840x1100
2025-07-02 13:08:20,065 - RegisterMainWindow - [RegisterMainWindow.py:200] - INFO - 操作常量已加载: TIMEOUT=5000ms, BATCH_SIZE=50
2025-07-02 13:08:20,067 - DIContainer - [DIContainer.py:46] - INFO - 注册单例实例: main_window
2025-07-02 13:08:20,075 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: spi_service
2025-07-02 13:08:20,075 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: register_manager
2025-07-02 13:08:20,075 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: event_bus
2025-07-02 13:08:20,075 - DIContainer - [DIContainer.py:181] - INFO - 核心服务配置完成
2025-07-02 13:08:20,102 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: initialization_manager
2025-07-02 13:08:20,102 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: register_operation_manager
2025-07-02 13:08:20,102 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: display_manager
2025-07-02 13:08:20,102 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: event_coordinator
2025-07-02 13:08:20,102 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: tool_window_factory
2025-07-02 13:08:20,102 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: register_update_processor
2025-07-02 13:08:20,102 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: lifecycle_manager
2025-07-02 13:08:20,102 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: ui_utility_manager
2025-07-02 13:08:20,102 - DIContainer - [DIContainer.py:210] - INFO - UI管理器服务配置完成
2025-07-02 13:08:20,107 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: batch_manager
2025-07-02 13:08:20,107 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: spi_coordinator
2025-07-02 13:08:20,107 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: ui_event_handler
2025-07-02 13:08:20,107 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: tool_window_manager
2025-07-02 13:08:20,107 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: status_config_manager
2025-07-02 13:08:20,107 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: resource_utility_manager
2025-07-02 13:08:20,107 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: tab_window_manager
2025-07-02 13:08:20,107 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: global_event_manager
2025-07-02 13:08:20,107 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: spi_signal_manager
2025-07-02 13:08:20,107 - DIContainer - [DIContainer.py:243] - INFO - 额外管理器服务配置完成
2025-07-02 13:08:20,107 - RegisterMainWindow - [RegisterMainWindow.py:69] - INFO - 依赖注入容器设置完成
2025-07-02 13:08:20,107 - RegisterMainWindow - [RegisterMainWindow.py:87] - INFO - 管理器依赖注入设置完成
2025-07-02 13:08:20,107 - InitializationManager - [InitializationManager.py:31] - INFO - 正在初始化寄存器配置工具...
2025-07-02 13:08:20,107 - InitializationManager - [InitializationManager.py:40] - INFO - 成功加载寄存器配置文件，包含 125 个寄存器
2025-07-02 13:08:20,113 - spi_service - [spi_service.py:67] - INFO - SPI配置已加载: TIMEOUT=5000ms, RETRY_COUNT=3, RETRY_DELAY=1000ms
2025-07-02 13:08:20,113 - spi_service - [spi_service.py:54] - INFO - SPIService initialized
2025-07-02 13:08:20,113 - spi_service - [spi_service.py:84] - INFO - SPIService initializing...
2025-07-02 13:08:20,113 - spi_service - [spi_service.py:421] - DEBUG - Cleaning up SPI thread...
2025-07-02 13:08:20,113 - spi_service - [spi_service.py:455] - DEBUG - SPI thread resources cleaned.
2025-07-02 13:08:20,113 - spi_service_impl - [spi_service_impl.py:79] - INFO - 已启用模拟模式
2025-07-02 13:08:20,113 - spi_service - [spi_service.py:387] - DEBUG - SPI thread components initialized and signals connected.
2025-07-02 13:08:20,113 - spi_service - [spi_service.py:395] - INFO - Starting SPI thread...
2025-07-02 13:08:20,113 - spi_service - [spi_service.py:111] - INFO - Scanning for available COM ports during initialization...
2025-07-02 13:08:20,113 - spi_service_impl - [spi_service_impl.py:411] - INFO - SPI操作处理线程已启动
2025-07-02 13:08:20,118 - spi_service - [spi_service.py:121] - DEBUG - No COM ports detected during initialization.
2025-07-02 13:08:20,118 - spi_service - [spi_service.py:144] - INFO - No usable hardware ports found. Enabling simulation mode.
2025-07-02 13:08:20,118 - spi_service - [spi_service.py:96] - INFO - SPIService initialization complete.
2025-07-02 13:08:20,118 - InitializationManager - [InitializationManager.py:92] - INFO - SPI服务初始化成功，当前模式：模拟模式
2025-07-02 13:08:20,123 - RegisterUpdateBus - [RegisterUpdateBus.py:62] - INFO - RegisterUpdateBus: Initial clock source set based on register default (0x57, CLKin_SEL_MANUAL): ClkIn1
2025-07-02 13:08:20,123 - ModernBaseHandler - [ModernBaseHandler.py:79] - DEBUG - 成功连接到RegisterUpdateBus
2025-07-02 13:08:20,123 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:77] - INFO - 表格配置初始化完成
2025-07-02 13:08:20,477 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:142] - INFO - 位域表格创建完成
2025-07-02 13:08:20,477 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:102] - INFO - 表格UI创建完成
2025-07-02 13:08:20,477 - ModernBaseHandler - [ModernBaseHandler.py:452] - WARNING - _post_init方法已废弃，请使用新的初始化流程
2025-07-02 13:08:20,477 - ModernBaseHandler - [ModernBaseHandler.py:97] - INFO - ModernBaseHandler: 开始完成初始化
2025-07-02 13:08:20,477 - ModernBaseHandler - [ModernBaseHandler.py:112] - DEBUG - 此处理器不需要UI映射，跳过控件映射构建
2025-07-02 13:08:20,477 - ModernBaseHandler - [ModernBaseHandler.py:273] - INFO - ModernRegisterTableHandler: 窗口已居中显示在 (585, 190)，窗口尺寸: 750x700
2025-07-02 13:08:20,477 - ModernBaseHandler - [ModernBaseHandler.py:289] - DEBUG - ModernRegisterTableHandler: 已设置窗口激活功能
2025-07-02 13:08:20,477 - ModernBaseHandler - [ModernBaseHandler.py:242] - INFO - ModernRegisterTableHandler: 设置窗口默认尺寸为 750 x 700，已居中显示
2025-07-02 13:08:20,477 - ModernBaseHandler - [ModernBaseHandler.py:121] - INFO - ModernBaseHandler: 初始化完成
2025-07-02 13:08:20,477 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:51] - INFO - 现代化寄存器表格处理器初始化完成
2025-07-02 13:08:20,477 - ModernBaseHandler - [ModernBaseHandler.py:79] - DEBUG - 成功连接到RegisterUpdateBus
2025-07-02 13:08:20,477 - ModernBaseHandler - [ModernBaseHandler.py:452] - WARNING - _post_init方法已废弃，请使用新的初始化流程
2025-07-02 13:08:20,477 - ModernBaseHandler - [ModernBaseHandler.py:97] - INFO - ModernBaseHandler: 开始完成初始化
2025-07-02 13:08:20,477 - ModernBaseHandler - [ModernBaseHandler.py:112] - DEBUG - 此处理器不需要UI映射，跳过控件映射构建
2025-07-02 13:08:20,477 - ModernBaseHandler - [ModernBaseHandler.py:273] - INFO - ModernRegisterIOHandler: 窗口已居中显示在 (585, 190)，窗口尺寸: 750x700
2025-07-02 13:08:20,477 - ModernBaseHandler - [ModernBaseHandler.py:289] - DEBUG - ModernRegisterIOHandler: 已设置窗口激活功能
2025-07-02 13:08:20,477 - ModernBaseHandler - [ModernBaseHandler.py:242] - INFO - ModernRegisterIOHandler: 设置窗口默认尺寸为 750 x 700，已居中显示
2025-07-02 13:08:20,477 - ModernBaseHandler - [ModernBaseHandler.py:121] - INFO - ModernBaseHandler: 初始化完成
2025-07-02 13:08:20,483 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:78] - INFO - 现代化寄存器IO处理器初始化完成
2025-07-02 13:08:20,483 - ModernBaseHandler - [ModernBaseHandler.py:79] - DEBUG - 成功连接到RegisterUpdateBus
2025-07-02 13:08:20,484 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:81] - DEBUG - 现代化TreeWidget设置最小宽度: 180px，防止布局抖动
2025-07-02 13:08:20,484 - ModernBaseHandler - [ModernBaseHandler.py:452] - WARNING - _post_init方法已废弃，请使用新的初始化流程
2025-07-02 13:08:20,484 - ModernBaseHandler - [ModernBaseHandler.py:97] - INFO - ModernBaseHandler: 开始完成初始化
2025-07-02 13:08:20,484 - ModernBaseHandler - [ModernBaseHandler.py:112] - DEBUG - 此处理器不需要UI映射，跳过控件映射构建
2025-07-02 13:08:20,484 - ModernBaseHandler - [ModernBaseHandler.py:273] - INFO - ModernRegisterTreeHandler: 窗口已居中显示在 (585, 190)，窗口尺寸: 750x700
2025-07-02 13:08:20,484 - ModernBaseHandler - [ModernBaseHandler.py:289] - DEBUG - ModernRegisterTreeHandler: 已设置窗口激活功能
2025-07-02 13:08:20,484 - ModernBaseHandler - [ModernBaseHandler.py:242] - INFO - ModernRegisterTreeHandler: 设置窗口默认尺寸为 750 x 700，已居中显示
2025-07-02 13:08:20,484 - ModernBaseHandler - [ModernBaseHandler.py:121] - INFO - ModernBaseHandler: 初始化完成
2025-07-02 13:08:20,484 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:51] - INFO - 现代化寄存器树处理器初始化完成
2025-07-02 13:08:20,484 - InitializationManager - [InitializationManager.py:145] - INFO - 现代化处理器创建成功
2025-07-02 13:08:20,484 - InitializationManager - [InitializationManager.py:113] - INFO - 使用现代化处理器创建核心处理器
2025-07-02 13:08:20,484 - BatchOperationManager - [BatchOperationManager.py:85] - INFO - 批量操作配置已加载: BATCH_SIZE=50, TIMEOUT=5000ms, UI_UPDATE_INTERVAL=10
2025-07-02 13:08:20,484 - BatchOperationManager - [BatchOperationManager.py:72] - INFO - 使用传统批量操作方式（已优化性能）
2025-07-02 13:08:20,484 - SPIOperationCoordinator - [SPIOperationCoordinator.py:283] - DEBUG - 已连接SPI操作完成信号
2025-07-02 13:08:20,484 - InitializationManager - [InitializationManager.py:182] - INFO - 使用依赖注入创建管理器完成
2025-07-02 13:08:20,661 - MainWindowUI - [MainWindowUI.py:137] - INFO - MainWindowUI: 检查现代化处理器 - IO: True, Table: True
2025-07-02 13:08:20,661 - MainWindowUI - [MainWindowUI.py:138] - INFO - MainWindowUI: IO处理器类型: <class 'ui.handlers.ModernRegisterIOHandler.ModernRegisterIOHandler'>
2025-07-02 13:08:20,661 - MainWindowUI - [MainWindowUI.py:139] - INFO - MainWindowUI: Table处理器类型: <class 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler'>
2025-07-02 13:08:20,661 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:705] - INFO - ModernRegisterIOHandler: get_io_widget() 被调用
2025-07-02 13:08:20,661 - MainWindowUI - [MainWindowUI.py:69] - INFO - MainWindowUI: 使用QSplitter创建稳定的TreeWidget和TableWidget分割布局
2025-07-02 13:08:20,661 - ProgressBarStyleManager - [ProgressBarStyleManager.py:135] - DEBUG - 已为进度条应用绿色样式: default
2025-07-02 13:08:20,661 - MenuManager - [MenuManager.py:330] - INFO - 插件服务尚未可用，跳过工具插件集成（将在插件系统设置完成后执行）
2025-07-02 13:08:20,666 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:128] - INFO - 填充了 125 个寄存器到树视图
2025-07-02 13:08:20,666 - SPISignalManager - [SPISignalManager.py:41] - DEBUG - SPI信号连接完成
2025-07-02 13:08:20,668 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:164] - INFO - 现代化TreeHandler: 选择默认寄存器: 'R0 (0x00)'
2025-07-02 13:08:20,668 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:176] - INFO - 现代化TreeHandler: 树项点击 - 项: 'R0 (0x00)', 地址: 0x00, 列: 0
2025-07-02 13:08:20,668 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:185] - INFO - 现代化TreeHandler: 选择寄存器地址: 0x00 从项 'R0 (0x00)'
2025-07-02 13:08:20,668 - EventCoordinator - [EventCoordinator.py:169] - DEBUG - EventCoordinator: 开始处理寄存器选择 0x00
2025-07-02 13:08:20,668 - EventCoordinator - [EventCoordinator.py:172] - DEBUG - EventCoordinator: 找到显示管理器，调用 handle_register_selection(0x00)
2025-07-02 13:08:20,668 - RegisterDisplayManager - [RegisterDisplayManager.py:209] - DEBUG - DisplayManager: 开始处理寄存器选择 0x00
2025-07-02 13:08:20,668 - RegisterOperationService - [RegisterOperationService.py:420] - INFO - RegisterOperationService: 处理寄存器选择, addr='0x00'
2025-07-02 13:08:20,668 - RegisterOperationService - [RegisterOperationService.py:234] - INFO - RegisterOperationService: 更新UI显示 - 地址: 0x00, 值: 0x1300
2025-07-02 13:08:20,670 - RegisterOperationService - [RegisterOperationService.py:242] - DEBUG - RegisterOperationService: 跳过表格更新，避免重复调用 - 0x00
2025-07-02 13:08:20,670 - RegisterDisplayManager - [RegisterDisplayManager.py:213] - DEBUG - DisplayManager: 寄存器操作服务返回: success=True, reg_num=0, value=0x1300
2025-07-02 13:08:20,670 - RegisterDisplayManager - [RegisterDisplayManager.py:218] - INFO - DisplayManager: 已更新主窗口选中寄存器地址为: 0x00
2025-07-02 13:08:20,670 - RegisterDisplayManager - [RegisterDisplayManager.py:71] - INFO - DisplayManager: 开始更新位字段显示 - 地址: 0x00, 值: 0x1300
2025-07-02 13:08:20,670 - RegisterDisplayManager - [RegisterDisplayManager.py:75] - INFO - DisplayManager: 找到 table_handler: <class 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler'>
2025-07-02 13:08:20,670 - RegisterDisplayManager - [RegisterDisplayManager.py:76] - INFO - DisplayManager: 调用 table_handler.show_bit_fields(0x00, 4864, from_global_update=True)
2025-07-02 13:08:20,670 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:809] - DEBUG - ModernTableHandler: 尝试获取主窗口实例...
2025-07-02 13:08:20,670 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:810] - DEBUG - ModernTableHandler: self类型: <class 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler'>
2025-07-02 13:08:20,670 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:811] - DEBUG - ModernTableHandler: hasattr(self, 'main_window'): False
2025-07-02 13:08:20,670 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:812] - DEBUG - ModernTableHandler: hasattr(self, 'parent'): True
2025-07-02 13:08:20,670 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:821] - DEBUG - ModernTableHandler: parent类型: <class 'ui.windows.RegisterMainWindow.RegisterMainWindow'>
2025-07-02 13:08:20,670 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:828] - DEBUG - ModernTableHandler: parent本身就是主窗口
2025-07-02 13:08:20,670 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:233] - INFO - ModernTableHandler: 开始显示位域信息 - 地址: 0x00, 值: 0x1300
2025-07-02 13:08:20,670 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:246] - INFO - ModernTableHandler: 设置当前寄存器 - 地址: 0x00, 值: 0x1300
2025-07-02 13:08:20,670 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:251] - INFO - ModernTableHandler: 获取到 1 个位域
2025-07-02 13:08:20,670 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:265] - DEBUG - ModernTableHandler: 断开表格信号连接
2025-07-02 13:08:20,671 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:269] - INFO - ModernTableHandler: 开始更新表格内容
2025-07-02 13:08:20,671 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:809] - DEBUG - ModernTableHandler: 尝试获取主窗口实例...
2025-07-02 13:08:20,671 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:810] - DEBUG - ModernTableHandler: self类型: <class 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler'>
2025-07-02 13:08:20,671 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:811] - DEBUG - ModernTableHandler: hasattr(self, 'main_window'): False
2025-07-02 13:08:20,671 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:812] - DEBUG - ModernTableHandler: hasattr(self, 'parent'): True
2025-07-02 13:08:20,671 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:821] - DEBUG - ModernTableHandler: parent类型: <class 'ui.windows.RegisterMainWindow.RegisterMainWindow'>
2025-07-02 13:08:20,671 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:828] - DEBUG - ModernTableHandler: parent本身就是主窗口
2025-07-02 13:08:20,671 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:273] - DEBUG - ModernTableHandler: 重新连接表格信号
2025-07-02 13:08:20,671 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:280] - INFO - ModernTableHandler: 成功显示寄存器 0x00 的 1 个位域
2025-07-02 13:08:20,671 - RegisterDisplayManager - [RegisterDisplayManager.py:78] - INFO - DisplayManager: 位字段显示更新完成
2025-07-02 13:08:20,671 - EventCoordinator - [EventCoordinator.py:174] - DEBUG - EventCoordinator: 显示管理器返回结果: True
2025-07-02 13:08:20,671 - InitializationManager - [InitializationManager.py:334] - INFO - 主窗口初始化完成
2025-07-02 13:08:20,671 - RegisterMainWindow - [RegisterMainWindow.py:247] - INFO - 主窗口点击置顶功能已设置
2025-07-02 13:08:20,675 - DIContainer - [DIContainer.py:46] - INFO - 注册单例实例: plugin_service
2025-07-02 13:08:20,675 - PluginManager - [PluginManager.py:132] - DEBUG - 跳过不存在的插件目录: plugins
2025-07-02 13:08:20,675 - PluginManager - [PluginManager.py:132] - DEBUG - 跳过不存在的插件目录: _internal/plugins
2025-07-02 13:08:20,675 - PluginManager - [PluginManager.py:132] - DEBUG - 跳过不存在的插件目录: ui/tools
2025-07-02 13:08:20,675 - PluginManager - [PluginManager.py:132] - DEBUG - 跳过不存在的插件目录: _internal/ui/tools
2025-07-02 13:08:20,675 - PluginManager - [PluginManager.py:331] - INFO - 找到 0 个工具窗口插件
2025-07-02 13:08:20,675 - PluginMenuService - [PluginMenuService.py:38] - INFO - 没有发现工具窗口插件
2025-07-02 13:08:20,675 - PluginIntegrationService - [PluginIntegrationService.py:86] - INFO - 插件系统初始化完成
2025-07-02 13:08:20,675 - RegisterMainWindow - [RegisterMainWindow.py:134] - INFO - 插件系统设置完成
2025-07-02 13:08:20,847 - RegisterMainWindow - [RegisterMainWindow.py:387] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-02 13:08:20,847 - RegisterMainWindow - [RegisterMainWindow.py:387] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-02 13:08:20,856 - RegisterMainWindow - [RegisterMainWindow.py:406] - DEBUG - 主窗口强制置顶完成
2025-07-02 13:08:20,857 - RegisterMainWindow - [RegisterMainWindow.py:311] - DEBUG - 主窗口已通过焦点置顶
2025-07-02 13:08:20,871 - RegisterMainWindow - [RegisterMainWindow.py:406] - DEBUG - 主窗口强制置顶完成
2025-07-02 13:08:20,871 - RegisterMainWindow - [RegisterMainWindow.py:315] - DEBUG - 主窗口已通过激活事件置顶
2025-07-02 13:08:20,872 - InitializationManager - [InitializationManager.py:274] - INFO - InitializationManager: 执行延迟端口刷新，确保UI同步端口状态
2025-07-02 13:08:20,872 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:501] - INFO - ModernRegisterIOHandler: 请求SPI服务刷新端口
2025-07-02 13:08:20,872 - spi_service - [spi_service.py:179] - INFO - Refreshing available COM ports...
2025-07-02 13:08:20,878 - spi_service - [spi_service.py:188] - DEBUG - No COM ports detected.
2025-07-02 13:08:20,879 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:509] - DEBUG - ModernRegisterIOHandler: _update_port_combo 被调用，port_combo: <PyQt5.QtWidgets.QComboBox object at 0x000001A2522490D0>, 类型: <class 'PyQt5.QtWidgets.QComboBox'>
2025-07-02 13:08:20,879 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:567] - INFO - ModernRegisterIOHandler: 没有检测到COM端口
2025-07-02 13:08:20,879 - spi_service - [spi_service.py:203] - INFO - Attempting to set SPI port to: None
2025-07-02 13:08:20,884 - spiPrivacy - [spiPrivacy.py:61] - WARNING - 未检测到可用的串口
2025-07-02 13:08:20,884 - spi_service_impl - [spi_service_impl.py:353] - WARNING - 无法连接到SPI端口: None
2025-07-02 13:08:20,884 - spi_service - [spi_service.py:230] - ERROR - Failed to set SPI port to None. Enabling simulation mode.
2025-07-02 13:08:20,884 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:579] - INFO - ModernRegisterIOHandler: 触发状态栏更新
2025-07-02 13:08:20,884 - StatusAndConfigManager - [StatusAndConfigManager.py:25] - INFO - StatusAndConfigManager.update_status_bar() 被调用
2025-07-02 13:08:20,884 - StatusAndConfigManager - [StatusAndConfigManager.py:30] - INFO - 获取到SPI服务状态: {'connected': False, 'port': None, 'mode': 'simulation', 'last_error': '无法连接到端口 None', 'retry_count': 0}
2025-07-02 13:08:20,884 - StatusAndConfigManager - [StatusAndConfigManager.py:33] - INFO - 原始模式文本: 'simulation'
2025-07-02 13:08:20,884 - StatusAndConfigManager - [StatusAndConfigManager.py:60] - INFO - 状态栏已更新: 模式=模拟模式, 端口=模拟模式
2025-07-02 13:08:20,885 - InitializationManager - [InitializationManager.py:277] - INFO - InitializationManager: 端口刷新完成
2025-07-02 13:08:21,169 - InitializationManager - [InitializationManager.py:289] - INFO - InitializationManager: 执行延迟状态栏更新
2025-07-02 13:08:21,170 - StatusAndConfigManager - [StatusAndConfigManager.py:25] - INFO - StatusAndConfigManager.update_status_bar() 被调用
2025-07-02 13:08:21,170 - StatusAndConfigManager - [StatusAndConfigManager.py:30] - INFO - 获取到SPI服务状态: {'connected': False, 'port': None, 'mode': 'simulation', 'last_error': '无法连接到端口 None', 'retry_count': 0}
2025-07-02 13:08:21,170 - StatusAndConfigManager - [StatusAndConfigManager.py:33] - INFO - 原始模式文本: 'simulation'
2025-07-02 13:08:21,171 - StatusAndConfigManager - [StatusAndConfigManager.py:60] - INFO - 状态栏已更新: 模式=模拟模式, 端口=模拟模式
2025-07-02 13:08:21,671 - RegisterMainWindow - [RegisterMainWindow.py:281] - INFO - 已为寄存器树安装点击事件过滤器
2025-07-02 13:08:21,671 - RegisterMainWindow - [RegisterMainWindow.py:286] - INFO - 已为寄存器表格安装点击事件过滤器
2025-07-02 13:08:43,570 - RegisterMainWindow - [RegisterMainWindow.py:387] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-02 13:08:43,572 - RegisterMainWindow - [RegisterMainWindow.py:387] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-02 13:08:43,581 - RegisterMainWindow - [RegisterMainWindow.py:406] - DEBUG - 主窗口强制置顶完成
2025-07-02 13:08:43,581 - RegisterMainWindow - [RegisterMainWindow.py:311] - DEBUG - 主窗口已通过焦点置顶
2025-07-02 13:08:43,597 - RegisterMainWindow - [RegisterMainWindow.py:406] - DEBUG - 主窗口强制置顶完成
2025-07-02 13:08:43,599 - RegisterMainWindow - [RegisterMainWindow.py:315] - DEBUG - 主窗口已通过激活事件置顶
2025-07-02 13:08:44,928 - RegisterMainWindow - [RegisterMainWindow.py:387] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-02 13:08:44,928 - RegisterMainWindow - [RegisterMainWindow.py:387] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-02 13:08:44,938 - RegisterMainWindow - [RegisterMainWindow.py:406] - DEBUG - 主窗口强制置顶完成
2025-07-02 13:08:44,938 - RegisterMainWindow - [RegisterMainWindow.py:311] - DEBUG - 主窗口已通过焦点置顶
2025-07-02 13:08:44,951 - RegisterMainWindow - [RegisterMainWindow.py:406] - DEBUG - 主窗口强制置顶完成
2025-07-02 13:08:44,951 - RegisterMainWindow - [RegisterMainWindow.py:315] - DEBUG - 主窗口已通过激活事件置顶
2025-07-02 13:08:51,138 - ApplicationLifecycleManager - [ApplicationLifecycleManager.py:41] - INFO - 应用程序正在关闭...
2025-07-02 13:08:51,141 - BatchOperationManager - [BatchOperationManager.py:483] - DEBUG - 断开读取信号时出现异常（可能未连接）: 'method' object is not connected
2025-07-02 13:08:51,141 - BatchOperationManager - [BatchOperationManager.py:495] - DEBUG - 断开写入信号时出现异常（可能未连接）: 'method' object is not connected
2025-07-02 13:08:51,141 - BatchOperationManager - [BatchOperationManager.py:533] - INFO - 已强制取消所有批量操作
2025-07-02 13:08:51,141 - ApplicationLifecycleManager - [ApplicationLifecycleManager.py:69] - INFO - 已强制取消所有批量操作并清理资源
2025-07-02 13:08:51,141 - PluginWindowService - [PluginWindowService.py:513] - INFO - 所有插件窗口已关闭
2025-07-02 13:08:51,141 - WindowManagementService - [WindowManagementService.py:200] - INFO - 已通过插件服务关闭所有插件窗口
2025-07-02 13:08:51,143 - WindowManagementService - [WindowManagementService.py:250] - INFO - 已清空所有标签页并隐藏标签页容器
2025-07-02 13:08:51,143 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-02 13:08:51,143 - WindowManagementService - [WindowManagementService.py:227] - INFO - 所有管理的窗口已关闭
2025-07-02 13:08:51,143 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-02 13:08:51,143 - ConfigurationService - [ConfigurationService.py:283] - DEBUG - 设置已更新: simulation_mode = False
2025-07-02 13:08:51,143 - ConfigurationService - [ConfigurationService.py:333] - INFO - 模拟模式设置已保存: False
2025-07-02 13:08:51,143 - spi_service - [spi_service.py:156] - INFO - SPIService cleaning up...
2025-07-02 13:08:51,143 - spi_service - [spi_service.py:421] - DEBUG - Cleaning up SPI thread...
2025-07-02 13:08:51,145 - spi_service - [spi_service.py:428] - DEBUG - Worker stop requested
2025-07-02 13:08:51,145 - spi_service - [spi_service.py:437] - INFO - Requesting SPI thread termination...
2025-07-02 13:08:51,150 - spi_service - [spi_service.py:443] - INFO - SPI thread stopped.
2025-07-02 13:08:51,151 - spi_service - [spi_service.py:455] - DEBUG - SPI thread resources cleaned.
2025-07-02 13:08:51,151 - spi_service - [spi_service.py:166] - INFO - SPIService cleanup finished.
2025-07-02 13:08:51,151 - ApplicationLifecycleManager - [ApplicationLifecycleManager.py:103] - INFO - 应用程序关闭完成
2025-07-02 13:08:51,226 - port_manager - [port_manager.py:211] - INFO - 已清理所有COM端口连接
