#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试单一搜索框功能
验证修复后的界面只有一个搜索框
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QLineEdit
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt

def test_single_search_box():
    """测试界面只有一个搜索框"""
    app = QApplication(sys.argv)
    
    try:
        # 创建主窗口
        from ui.windows.RegisterMainWindow import RegisterMainWindow
        from core.services.spi.spi_service_impl import SPIServiceImpl
        from core.repositories.register_repository import RegisterRepository

        spi_service = SPIServiceImpl()
        spi_service.initialize()
        register_repo = RegisterRepository(spi_service)
        main_window = RegisterMainWindow(register_repo)
        main_window.show()
        
        # 等待界面完全加载
        QTest.qWait(2000)
        
        # 查找所有搜索框
        search_boxes = main_window.findChildren(QLineEdit)
        search_related_boxes = []
        
        for box in search_boxes:
            placeholder = box.placeholderText()
            if "搜索" in placeholder or "位段" in placeholder:
                search_related_boxes.append(box)
                print(f"找到搜索框: {placeholder}")
        
        print(f"\n总共找到 {len(search_related_boxes)} 个搜索相关的输入框")
        
        if len(search_related_boxes) == 1:
            print("✅ 成功！界面只有一个搜索框")
            return True
        elif len(search_related_boxes) == 0:
            print("❌ 错误：没有找到搜索框")
            return False
        else:
            print(f"❌ 错误：找到了 {len(search_related_boxes)} 个搜索框，应该只有1个")
            return False
            
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        app.quit()

if __name__ == "__main__":
    success = test_single_search_box()
    sys.exit(0 if success else 1)
