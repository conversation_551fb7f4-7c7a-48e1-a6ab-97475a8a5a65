#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试插件菜单点击响应
"""

import sys
import os

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication, QMainWindow, QMenuBar, QAction
from PyQt5.QtCore import QTimer
from core.services.plugin.PluginManager import plugin_manager
from core.services.plugin.PluginIntegrationService import PluginIntegrationService


class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("插件菜单点击测试")
        self.resize(800, 600)
        
        # 创建菜单栏
        self.menuBar()
        
        # 设置插件系统
        self.setup_plugins()
        
        # 添加测试菜单项
        self.add_test_menu()
    
    def setup_plugins(self):
        """设置插件系统"""
        # 添加插件目录
        plugin_dir = os.path.join(project_root, "plugins")
        plugin_manager.add_plugin_directory(plugin_dir)
        
        # 扫描并初始化插件
        plugin_manager.scan_plugins()
        plugin_manager.initialize_plugins(self)
        
        # 创建插件集成服务
        self.plugin_service = PluginIntegrationService(self)
        self.plugin_service.initialize_plugins()
        
        print(f"插件系统初始化完成，发现 {len(plugin_manager.get_plugin_list())} 个插件")
    
    def add_test_menu(self):
        """添加测试菜单"""
        test_menu = self.menuBar().addMenu("测试(&T)")
        
        # 添加测试动作
        test_action = QAction("测试点击", self)
        test_action.triggered.connect(self.on_test_click)
        test_menu.addAction(test_action)
        
        print("添加了测试菜单")
    
    def on_test_click(self):
        """测试点击处理"""
        print("测试菜单被点击了！")


def test_menu_click():
    """测试菜单点击"""
    app = QApplication([])
    
    # 创建测试窗口
    window = TestMainWindow()
    window.show()
    
    print("测试窗口已显示")
    print("请测试以下操作：")
    print("1. 直接点击插件菜单项")
    print("2. 先划过其他菜单再点击插件菜单项")
    print("3. 点击测试菜单验证基本功能")
    print("程序将在30秒后自动关闭")
    
    # 设置定时器自动关闭
    timer = QTimer()
    timer.timeout.connect(app.quit)
    timer.start(30000)  # 30秒后关闭
    
    # 运行应用
    app.exec_()
    
    print("测试完成")


if __name__ == "__main__":
    test_menu_click()
