#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速测试拖拽停靠功能
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication, QMessageBox
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


def test_drag_dock_functionality():
    """测试拖拽停靠功能"""
    try:
        print("🧪 开始测试拖拽停靠功能...")
        
        # 1. 启用强制悬浮模式
        print("1. 启用强制悬浮模式...")
        from core.services.config.ConfigurationManager import set_config
        set_config('plugins.force_floating_mode', True)
        print("   ✅ 强制悬浮模式已启用")
        
        # 2. 创建主窗口
        print("2. 创建主窗口...")
        from ui.windows.RegisterMainWindow import RegisterMainWindow
        main_window = RegisterMainWindow()
        main_window.show()
        print("   ✅ 主窗口创建成功")
        
        # 3. 获取插件服务
        print("3. 获取插件服务...")
        if hasattr(main_window, 'plugin_service'):
            plugin_service = main_window.plugin_service
            print("   ✅ 插件服务获取成功")
        else:
            print("   ❌ 插件服务未找到")
            return False
            
        # 4. 检查拖拽停靠测试插件
        print("4. 检查拖拽停靠测试插件...")
        from core.services.plugin.PluginManager import plugin_manager
        test_plugins = [p for p in plugin_manager.get_tool_window_plugins() 
                       if 'drag_dock_test' in p.name]
        
        if test_plugins:
            test_plugin = test_plugins[0]
            print(f"   ✅ 找到测试插件: {test_plugin.name}")
        else:
            print("   ⚠️  未找到专用测试插件，使用现有插件进行测试")
            # 使用第一个可用的插件
            all_plugins = plugin_manager.get_tool_window_plugins()
            if all_plugins:
                test_plugin = all_plugins[0]
                print(f"   ✅ 使用插件: {test_plugin.name}")
            else:
                print("   ❌ 没有可用的插件")
                return False
        
        # 5. 创建测试窗口
        print("5. 创建测试窗口...")
        test_window = test_plugin.create_window(main_window)
        if test_window:
            print("   ✅ 测试窗口创建成功")
        else:
            print("   ❌ 测试窗口创建失败")
            return False
            
        # 6. 配置为悬浮窗口并添加拖拽支持
        print("6. 配置拖拽停靠功能...")
        try:
            # 强制配置为悬浮窗口
            plugin_service._configure_plugin_window(test_window, test_plugin.name)
            print("   ✅ 拖拽停靠功能配置成功")
        except Exception as e:
            print(f"   ❌ 拖拽停靠功能配置失败: {str(e)}")
            return False
            
        # 7. 显示测试窗口
        print("7. 显示测试窗口...")
        test_window.show()
        plugin_service.plugin_windows[test_plugin.name] = test_window
        print("   ✅ 测试窗口已显示")
        
        # 8. 显示测试说明
        print("8. 显示测试说明...")
        show_test_instructions(test_plugin.name)
        
        print("\n🎯 测试准备完成！")
        print("请按照弹出的说明进行拖拽停靠测试。")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试准备失败: {str(e)}")
        logger.error(f"测试拖拽停靠功能失败: {str(e)}")
        return False


def show_test_instructions(plugin_name):
    """显示测试说明"""
    try:
        instructions = f"""
🎯 拖拽停靠功能测试说明

当前测试窗口: {plugin_name}

📋 测试步骤：
1. 找到刚才创建的测试窗口
2. 按住鼠标左键拖拽窗口
3. 拖拽到主窗口底部30%区域
4. 观察以下视觉反馈：
   • 窗口标题变为"释放鼠标停靠到主界面"
   • 鼠标光标变为手型指针
5. 在停靠区域内释放鼠标
6. 窗口应自动停靠到主界面标签页

🔍 调试信息：
• 查看控制台输出获取详细调试信息
• 日志标签：[拖拽调试] 和 [停靠区域调试]

❓ 如果拖拽停靠不工作：
• 确保拖拽到主窗口底部30%区域
• 检查主窗口是否可见且未被遮挡
• 查看控制台日志获取错误信息

点击"确定"开始测试...
        """
        
        msg_box = QMessageBox()
        msg_box.setWindowTitle("拖拽停靠功能测试")
        msg_box.setText(instructions)
        msg_box.setIcon(QMessageBox.Information)
        msg_box.exec_()
        
    except Exception as e:
        print(f"显示测试说明失败: {str(e)}")


def main():
    """主函数"""
    try:
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 运行测试
        success = test_drag_dock_functionality()
        
        if success:
            print("\n✅ 测试环境准备成功，开始手动测试...")
            # 运行应用程序，等待用户手动测试
            sys.exit(app.exec_())
        else:
            print("\n❌ 测试环境准备失败")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ 程序运行失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
