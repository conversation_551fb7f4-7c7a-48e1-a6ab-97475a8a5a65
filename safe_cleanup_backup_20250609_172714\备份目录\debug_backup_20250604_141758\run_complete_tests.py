#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
完整测试运行脚本
用于在代码修改后运行完整的测试套件，防止功能丢失
"""

import os
import sys
import time
import argparse
from datetime import datetime

def print_banner():
    """打印测试横幅"""
    print("=" * 80)
    print("🧪 寄存器配置工具完整测试套件")
    print("=" * 80)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    print("=" * 80)

def run_test_category(category, timeout=300):
    """运行特定分类的测试"""
    print(f"\n🔍 运行 {category.upper()} 测试...")
    print("-" * 60)
    
    start_time = time.time()
    
    try:
        import subprocess
        cmd = [sys.executable, "test_suite/run_all_tests.py", "--category", category]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=timeout)
        
        duration = time.time() - start_time
        
        if result.returncode == 0:
            print(f"✅ {category.upper()} 测试通过 ({duration:.1f}s)")
            return True, duration, result.stdout
        else:
            print(f"❌ {category.upper()} 测试失败 ({duration:.1f}s)")
            print("错误输出:")
            print(result.stderr)
            return False, duration, result.stderr
            
    except subprocess.TimeoutExpired:
        duration = time.time() - start_time
        print(f"⏰ {category.upper()} 测试超时 ({duration:.1f}s)")
        return False, duration, "测试超时"
    except Exception as e:
        duration = time.time() - start_time
        print(f"💥 {category.upper()} 测试出错 ({duration:.1f}s): {e}")
        return False, duration, str(e)

def run_legacy_tests():
    """运行根目录下的传统测试"""
    print(f"\n🔍 运行传统测试...")
    print("-" * 60)
    
    # 重要的传统测试文件
    important_tests = [
        'test_scroll_area_fix.py',
        'test_modern_pll.py',
        'test_modern_clk_outputs.py',
        'test_auto_write.py',
        'test_final_refactoring.py'
    ]
    
    results = []
    
    for test_file in important_tests:
        if os.path.exists(test_file):
            print(f"运行: {test_file}")
            start_time = time.time()
            
            try:
                import subprocess
                result = subprocess.run([sys.executable, test_file], 
                                      capture_output=True, text=True, timeout=120)
                duration = time.time() - start_time
                
                if result.returncode == 0:
                    print(f"  ✅ 通过 ({duration:.1f}s)")
                    results.append((test_file, True, duration))
                else:
                    print(f"  ❌ 失败 ({duration:.1f}s)")
                    results.append((test_file, False, duration))
                    
            except Exception as e:
                duration = time.time() - start_time
                print(f"  💥 出错 ({duration:.1f}s): {e}")
                results.append((test_file, False, duration))
        else:
            print(f"跳过: {test_file} (文件不存在)")
    
    return results

def generate_test_report(category_results, legacy_results, total_duration):
    """生成测试报告"""
    print("\n" + "=" * 80)
    print("📊 测试报告")
    print("=" * 80)
    
    # 分类测试结果
    print("\n🏷️  分类测试结果:")
    total_categories = len(category_results)
    passed_categories = sum(1 for _, success, _ in category_results if success)
    
    for category, success, duration in category_results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {category.ljust(12)}: {status} ({duration:.1f}s)")
    
    # 传统测试结果
    if legacy_results:
        print("\n🗂️  传统测试结果:")
        total_legacy = len(legacy_results)
        passed_legacy = sum(1 for _, success, _ in legacy_results if success)
        
        for test_file, success, duration in legacy_results:
            status = "✅ 通过" if success else "❌ 失败"
            print(f"  {test_file.ljust(30)}: {status} ({duration:.1f}s)")
    else:
        total_legacy = 0
        passed_legacy = 0
    
    # 总体统计
    print("\n📈 总体统计:")
    print(f"  分类测试: {passed_categories}/{total_categories} 通过")
    print(f"  传统测试: {passed_legacy}/{total_legacy} 通过")
    print(f"  总耗时: {total_duration:.1f}秒")
    
    # 成功率
    total_tests = total_categories + total_legacy
    total_passed = passed_categories + passed_legacy
    success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
    
    print(f"  成功率: {success_rate:.1f}%")
    
    # 结论
    print("\n🎯 测试结论:")
    if success_rate >= 90:
        print("  🎉 优秀！大部分测试通过，代码质量良好")
    elif success_rate >= 70:
        print("  👍 良好！多数测试通过，但需要关注失败的测试")
    elif success_rate >= 50:
        print("  ⚠️  警告！部分测试失败，需要修复问题")
    else:
        print("  🚨 严重！大量测试失败，代码可能存在重大问题")
    
    return success_rate

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='运行完整的测试套件')
    parser.add_argument('--skip-legacy', action='store_true', 
                       help='跳过传统测试')
    parser.add_argument('--categories', nargs='+',
                       choices=['functional', 'integration', 'ui', 'unit', 'performance', 'regression'],
                       help='指定要运行的测试分类')
    parser.add_argument('--quick', action='store_true',
                       help='快速测试模式（只运行核心测试）')
    
    args = parser.parse_args()
    
    print_banner()
    
    # 检查测试套件是否存在
    if not os.path.exists('test_suite'):
        print("❌ 测试套件目录不存在，请先运行 test_suite/organize_existing_tests.py")
        return 1
    
    start_time = time.time()
    category_results = []
    legacy_results = []
    
    # 确定要运行的测试分类
    if args.quick:
        categories = ['functional', 'unit']
    elif args.categories:
        categories = args.categories
    else:
        categories = ['functional', 'integration', 'ui', 'unit', 'performance', 'regression']
    
    # 运行分类测试
    for category in categories:
        success, duration, output = run_test_category(category)
        category_results.append((category, success, duration))
    
    # 运行传统测试
    if not args.skip_legacy:
        legacy_results = run_legacy_tests()
    
    # 计算总耗时
    total_duration = time.time() - start_time
    
    # 生成报告
    success_rate = generate_test_report(category_results, legacy_results, total_duration)
    
    print(f"\n完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # 返回适当的退出码
    return 0 if success_rate >= 70 else 1

if __name__ == "__main__":
    sys.exit(main())
