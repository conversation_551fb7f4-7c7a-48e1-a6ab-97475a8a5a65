#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
自动移除传统PLL处理器
按照分析结果逐步移除PLLHandler的所有引用
"""

import sys
import os
import shutil
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)


def backup_file(file_path):
    """备份文件"""
    if os.path.exists(file_path):
        backup_path = f"{file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(file_path, backup_path)
        print(f"✓ 已备份: {file_path} -> {backup_path}")
        return backup_path
    return None


def update_modern_tool_window_factory():
    """更新现代化工具窗口工厂，移除传统处理器引用"""
    print("\n1. 更新现代化工具窗口工厂...")

    file_path = os.path.join(project_root, 'ui', 'factories', 'ModernToolWindowFactory.py')
    backup_file(file_path)
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 移除传统处理器配置
        old_config = """        'pll_control': {
            'title': 'PLL1 & PLL2 控制',
            'window_attr': 'pll_control_window',
            'action_attr': 'pll_control_action',
            'legacy_handler': 'ui.handlers.PLLHandler.PLLHandler',
            'modern_handler': 'ui.handlers.ModernPLLHandler.ModernPLLHandler',
            'use_modern': True,  # 默认使用现代化版本
            'requires_register_manager': True,
            'supports_cross_handler_interaction': True
        },"""
        
        new_config = """        'pll_control': {
            'title': 'PLL1 & PLL2 控制',
            'window_attr': 'pll_control_window',
            'action_attr': 'pll_control_action',
            'modern_handler': 'ui.handlers.ModernPLLHandler.ModernPLLHandler',
            'use_modern': True,  # 使用现代化版本
            'requires_register_manager': True,
            'supports_cross_handler_interaction': True
        },"""
        
        content = content.replace(old_config, new_config)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✓ 现代化工具窗口工厂已更新")
        return True
        
    except Exception as e:
        print(f"❌ 更新现代化工具窗口工厂失败: {str(e)}")
        return False


def update_tool_window_factory():
    """更新传统工具窗口工厂"""
    print("\n2. 更新传统工具窗口工厂...")
    
    file_path = os.path.join(project_root, 'ui', 'factories', 'ToolWindowFactory.py')
    backup_file(file_path)
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 移除PLL控制窗口创建方法
        old_method = """    def create_pll_control_window(self):
        \"\"\"创建PLL控制窗口
        
        Returns:
            创建的窗口实例
        \"\"\"
        from ui.handlers.PLLHandler import PLLHandler
        
        def post_init(window):
            \"\"\"窗口创建后的初始化回调\"\"\"
            # 设置主窗口引用，以便自动写入功能能够访问主窗口
            window.main_window = self.main_window
            logger.info(f"已为PLL控制窗口设置主窗口引用，自动写入模式: {getattr(self.main_window, 'auto_write_mode', False)}")
            
        # 使用通用窗口创建方法
        return self._create_window_in_tab(
            title="PLL1 & PLL2 控制",
            window_attr="pll_control_window",
            window_class=PLLHandler,
            action_attr="pll_control_action",
            post_init_callback=post_init,
            parent=None,  # 确保窗口可以独立使用
            registers=self.main_window.register_manager.register_objects
        )"""
        
        new_method = """    def create_pll_control_window(self):
        \"\"\"创建PLL控制窗口 - 已迁移到现代化工厂
        
        Returns:
            None - 此方法已废弃，请使用现代化工厂
        \"\"\"
        logger.warning("create_pll_control_window已废弃，请使用现代化工厂")
        return None"""
        
        content = content.replace(old_method, new_method)
        
        # 更新配置映射
        old_config_line = "            'handler_class': 'ui.handlers.PLLHandler.PLLHandler'"
        new_config_line = "            'handler_class': 'ui.handlers.ModernPLLHandler.ModernPLLHandler'  # 已迁移到现代化版本"
        
        content = content.replace(old_config_line, new_config_line)
        
        # 移除方法映射
        old_mapping = "            'pll_control': self.create_pll_control_window,"
        new_mapping = "            # 'pll_control': self.create_pll_control_window,  # 已迁移到现代化工厂"
        
        content = content.replace(old_mapping, new_mapping)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✓ 传统工具窗口工厂已更新")
        return True
        
    except Exception as e:
        print(f"❌ 更新传统工具窗口工厂失败: {str(e)}")
        return False


def update_managers():
    """更新管理器类"""
    print("\n3. 更新管理器类...")
    
    # 更新TabWindowManager
    tab_manager_path = os.path.join(project_root, 'ui', 'managers', 'TabWindowManager.py')
    backup_file(tab_manager_path)
    
    try:
        with open(tab_manager_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换导入和方法
        old_import = "from ui.handlers.PLLHandler import PLLHandler"
        new_import = "# from ui.handlers.PLLHandler import PLLHandler  # 已移除，使用现代化工厂"
        
        content = content.replace(old_import, new_import)
        
        # 更新方法实现
        old_method = """    def show_pll_control_window(self):
        \"\"\"显示PLL控制窗口\"\"\"
        from ui.handlers.PLLHandler import PLLHandler
        
        def post_init(window):
            \"\"\"窗口创建后的初始化回调\"\"\"
            # 可以在这里添加一些初始化逻辑
            pass
            
        # 使用通用窗口创建方法
        return self.main_window._create_window_in_tab(
            title="PLL1 & PLL2 控制",
            window_attr="pll_control_window",
            window_class=PLLHandler,
            action_attr="pll_control_action",
            post_init_callback=post_init,
            parent=None,  # 确保窗口可以独立使用
            registers=self.main_window.register_manager.register_objects
        )"""
        
        new_method = """    def show_pll_control_window(self):
        \"\"\"显示PLL控制窗口 - 使用现代化工厂\"\"\"
        # 使用现代化工厂创建窗口
        return self.main_window.tool_window_factory.create_window_by_type('pll_control')"""
        
        content = content.replace(old_method, new_method)
        
        with open(tab_manager_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✓ TabWindowManager已更新")
        
    except Exception as e:
        print(f"❌ 更新TabWindowManager失败: {str(e)}")
        return False
    
    # 更新ToolWindowManager
    tool_manager_path = os.path.join(project_root, 'ui', 'managers', 'ToolWindowManager.py')
    backup_file(tool_manager_path)
    
    try:
        with open(tool_manager_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换导入和方法
        old_import = "from ui.handlers.PLLHandler import PLLHandler"
        new_import = "# from ui.handlers.PLLHandler import PLLHandler  # 已移除，使用现代化工厂"
        
        content = content.replace(old_import, new_import)
        
        # 更新方法实现
        old_method = """    def show_pll_control_window(self):
        \"\"\"显示PLL控制窗口\"\"\"
        from ui.handlers.PLLHandler import PLLHandler
        
        def post_init(window):
            \"\"\"窗口创建后的初始化回调\"\"\"
            # 可以在这里添加一些初始化逻辑
            pass
        
        # 委托给窗口管理服务
        self.main_window.window_service.show_window(
            "PLL控制窗口",
            PLLHandler,
            "pll_control_action",
            "pll_control_window",
            post_init_callback=post_init
        )"""
        
        new_method = """    def show_pll_control_window(self):
        \"\"\"显示PLL控制窗口 - 使用现代化工厂\"\"\"
        # 使用现代化工厂创建窗口
        return self.main_window.tool_window_factory.create_window_by_type('pll_control')"""
        
        content = content.replace(old_method, new_method)
        
        with open(tool_manager_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✓ ToolWindowManager已更新")
        return True
        
    except Exception as e:
        print(f"❌ 更新ToolWindowManager失败: {str(e)}")
        return False


def update_test_files():
    """更新测试文件"""
    print("\n4. 更新测试文件...")
    
    test_files = [
        'test_pll_functionality.py',
        'test_scroll_area_fix.py'
    ]
    
    for test_file in test_files:
        file_path = os.path.join(project_root, test_file)
        if os.path.exists(file_path):
            backup_file(file_path)
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 注释掉传统处理器测试
                content = content.replace(
                    "from ui.handlers.PLLHandler import PLLHandler",
                    "# from ui.handlers.PLLHandler import PLLHandler  # 已移除"
                )
                
                content = content.replace(
                    "legacy_handler = PLLHandler(None, register_manager.register_objects)",
                    "# legacy_handler = PLLHandler(None, register_manager.register_objects)  # 已移除"
                )
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"✓ {test_file}已更新")
                
            except Exception as e:
                print(f"❌ 更新{test_file}失败: {str(e)}")
                return False
    
    return True


def remove_pll_handler_file():
    """移除PLLHandler文件"""
    print("\n5. 移除PLLHandler文件...")
    
    file_path = os.path.join(project_root, 'ui', 'handlers', 'PLLHandler.py')
    
    if os.path.exists(file_path):
        # 备份文件
        backup_path = backup_file(file_path)
        
        # 移除文件
        os.remove(file_path)
        print(f"✓ 已移除: {file_path}")
        print(f"✓ 备份保存在: {backup_path}")
        return True
    else:
        print("⚠️  PLLHandler.py文件不存在")
        return True


def main():
    """主函数"""
    print("=" * 60)
    print("开始移除传统PLL处理器")
    print("=" * 60)
    
    steps = [
        ("更新现代化工具窗口工厂", update_modern_tool_window_factory),
        ("更新传统工具窗口工厂", update_tool_window_factory),
        ("更新管理器类", update_managers),
        ("更新测试文件", update_test_files),
        ("移除PLLHandler文件", remove_pll_handler_file),
    ]
    
    success_count = 0
    
    for step_name, step_func in steps:
        try:
            if step_func():
                success_count += 1
            else:
                print(f"❌ {step_name}失败")
        except Exception as e:
            print(f"❌ {step_name}出错: {str(e)}")
    
    print("\n" + "=" * 60)
    print("移除结果总结")
    print("=" * 60)
    
    if success_count == len(steps):
        print("🎉 传统PLL处理器移除完成！")
        print(f"✅ 成功完成 {success_count}/{len(steps)} 个步骤")
        print("\n📋 已完成的操作:")
        print("   - ✅ 更新现代化工厂配置")
        print("   - ✅ 更新传统工厂配置")
        print("   - ✅ 更新管理器类")
        print("   - ✅ 更新测试文件")
        print("   - ✅ 移除PLLHandler.py文件")
        print("\n🔧 建议后续操作:")
        print("   1. 运行测试验证功能正常")
        print("   2. 检查应用程序启动和PLL窗口创建")
        print("   3. 如果一切正常，可以删除备份文件")
        return True
    else:
        print("❌ 传统PLL处理器移除未完全成功")
        print(f"⚠️  完成 {success_count}/{len(steps)} 个步骤")
        print("\n🔧 建议操作:")
        print("   1. 检查失败的步骤")
        print("   2. 手动修复问题")
        print("   3. 重新运行移除脚本")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
