"""
CrossRegisterWidgetManager - 跨寄存器控件管理器

用于管理跨越多个寄存器的控件，如PLL2NDivider控件
提供统一的初始化、更新和状态管理功能
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from PyQt5.QtWidgets import QWidget

logger = logging.getLogger(__name__)


class CrossRegisterWidgetConfig:
    """跨寄存器控件配置类"""
    
    def __init__(self, widget_name: str, bit_fields: List[str], reg_addrs: List[str], 
                 default_value: str = "0", widget_type: str = "spinbox", 
                 options: str = "0:65535", min_value: int = 0, max_value: int = 65535):
        """
        初始化跨寄存器控件配置
        
        Args:
            widget_name: 控件名称
            bit_fields: 关联的位字段列表，从高位到低位排序
            reg_addrs: 关联的寄存器地址列表，从高位到低位排序
            default_value: 默认值（二进制字符串）
            widget_type: 控件类型
            options: 选项范围
            min_value: 最小值
            max_value: 最大值
        """
        if len(bit_fields) != len(reg_addrs):
            raise ValueError("bit_fields和reg_addrs长度必须一致")
            
        self.widget_name = widget_name
        self.bit_fields = bit_fields
        self.reg_addrs = reg_addrs
        self.default_value = default_value
        self.widget_type = widget_type
        self.options = options
        self.min_value = min_value
        self.max_value = max_value
        
        # 计算每个位字段的位宽
        self.bit_widths = []
        for bit_field in bit_fields:
            # 解析位字段格式，如 "PLL2_N[17:16]" -> 2位
            if '[' in bit_field and ':' in bit_field:
                bit_range = bit_field.split('[')[1].split(']')[0]
                high_bit, low_bit = map(int, bit_range.split(':'))
                width = high_bit - low_bit + 1
            else:
                width = 1
            self.bit_widths.append(width)
        
        self.total_bits = sum(self.bit_widths)


class CrossRegisterWidgetManager:
    """跨寄存器控件管理器"""

    def __init__(self, register_manager=None):
        """
        初始化跨寄存器控件管理器

        Args:
            register_manager: 寄存器管理器实例
        """
        self.register_manager = register_manager
        self.widget_configs: Dict[str, CrossRegisterWidgetConfig] = {}
        self.ui = None
        self._error_recovery_enabled = True
        self._max_retry_attempts = 3

        
    def set_ui(self, ui):
        """设置UI实例"""
        self.ui = ui
        
    def set_register_manager(self, register_manager):
        """设置寄存器管理器"""
        self.register_manager = register_manager


        
    def register_widget(self, config: CrossRegisterWidgetConfig) -> bool:
        """
        注册跨寄存器控件
        
        Args:
            config: 控件配置
            
        Returns:
            bool: 注册是否成功
        """
        try:
            if not self.ui or not hasattr(self.ui, config.widget_name):
                logger.warning(f"跨寄存器控件 {config.widget_name} 不存在")
                return False
                
            self.widget_configs[config.widget_name] = config
            logger.info(f"已注册跨寄存器控件: {config.widget_name}")
            return True
            
        except Exception as e:
            logger.error(f"注册跨寄存器控件 {config.widget_name} 失败: {str(e)}")
            return False
    
    def initialize_widget(self, widget_name: str) -> bool:
        """
        初始化跨寄存器控件
        
        Args:
            widget_name: 控件名称
            
        Returns:
            bool: 初始化是否成功
        """
        try:
            if widget_name not in self.widget_configs:
                logger.error(f"未找到控件 {widget_name} 的配置")
                return False
                
            config = self.widget_configs[widget_name]
            widget = getattr(self.ui, widget_name, None)
            
            if not widget:
                logger.error(f"控件 {widget_name} 不存在")
                return False
                
            # 从寄存器读取当前值
            combined_value = self._read_combined_value(config)
            
            # 设置控件范围
            if hasattr(widget, 'setMinimum') and hasattr(widget, 'setMaximum'):
                widget.setMinimum(config.min_value)
                widget.setMaximum(config.max_value)
                
            # 设置控件值
            widget.blockSignals(True)
            try:
                if hasattr(widget, 'setValue'):
                    widget.setValue(int(combined_value))
                elif hasattr(widget, 'setCurrentIndex'):
                    widget.setCurrentIndex(int(combined_value))
                    
                logger.info(f"已初始化跨寄存器控件 {widget_name}，值为 {combined_value}")
                return True
                
            finally:
                widget.blockSignals(False)
                
        except Exception as e:
            logger.error(f"初始化跨寄存器控件 {widget_name} 失败: {str(e)}")
            return False
    
    def update_registers(self, widget_name: str, value: int) -> bool:
        """
        原子性更新跨寄存器控件对应的寄存器值

        Args:
            widget_name: 控件名称
            value: 新值

        Returns:
            bool: 更新是否成功
        """
        try:
            if widget_name not in self.widget_configs:
                logger.error(f"未找到控件 {widget_name} 的配置")
                return False

            if not self.register_manager:
                logger.error("RegisterManager未设置")
                return False

            config = self.widget_configs[widget_name]

            # 验证值范围
            if not (config.min_value <= value <= config.max_value):
                logger.error(f"控件 {widget_name} 值 {value} 超出范围 [{config.min_value}, {config.max_value}]")
                return False

            # 分解值到各个位字段
            bit_values = self._decompose_value(value, config)

            logger.info(f"更新跨寄存器控件 {widget_name}: 总值={value}, 分解值={bit_values}")

            # 原子性更新：先收集所有需要更新的寄存器状态
            update_operations = []
            for i, (reg_addr, bit_field, bit_value) in enumerate(zip(config.reg_addrs, config.bit_fields, bit_values)):
                update_operations.append({
                    'reg_addr': reg_addr,
                    'bit_field': bit_field,
                    'bit_value': bit_value,
                    'index': i
                })

            # 执行原子性更新（带重试机制）
            return self._update_with_retry(widget_name, update_operations)

        except Exception as e:
            logger.error(f"更新跨寄存器控件 {widget_name} 失败: {str(e)}")
            return False

    def _update_with_retry(self, widget_name: str, update_operations: List[Dict]) -> bool:
        """
        带重试机制的更新

        Args:
            widget_name: 控件名称
            update_operations: 更新操作列表

        Returns:
            bool: 更新是否成功
        """
        last_error = None

        for attempt in range(self._max_retry_attempts + 1):  # +1 因为第一次不算重试
            try:
                if attempt > 0:
                    logger.info(f"重试更新 {widget_name}，第 {attempt} 次尝试")

                success = self._atomic_update_registers(widget_name, update_operations)

                if success:
                    if attempt > 0:
                        logger.info(f"重试成功：{widget_name} 在第 {attempt} 次尝试后更新成功")
                    return True
                else:
                    last_error = f"原子性更新失败（尝试 {attempt + 1}）"

            except Exception as e:
                last_error = f"更新异常（尝试 {attempt + 1}）: {str(e)}"
                logger.error(last_error)

            # 如果不是最后一次尝试，等待一小段时间再重试
            if attempt < self._max_retry_attempts:
                import time
                time.sleep(0.1)  # 等待100ms

        # 所有重试都失败了
        logger.error(f"更新 {widget_name} 失败，已尝试 {self._max_retry_attempts + 1} 次。最后错误: {last_error}")
        return False

    def _atomic_update_registers(self, widget_name: str, update_operations: List[Dict]) -> bool:
        """
        原子性更新多个寄存器

        Args:
            widget_name: 控件名称
            update_operations: 更新操作列表

        Returns:
            bool: 更新是否成功
        """
        try:
            # 第一阶段：验证所有操作的可行性（预检查）
            logger.debug(f"开始原子性更新 {widget_name}，共 {len(update_operations)} 个操作")

            # 备份当前寄存器状态（用于回滚）
            backup_states = {}
            for op in update_operations:
                reg_addr = op['reg_addr']
                bit_field = op['bit_field']

                # 获取当前值作为备份
                current_value = self._safe_get_bit_field_value(reg_addr, bit_field)
                backup_states[f"{reg_addr}:{bit_field}"] = current_value
                logger.debug(f"备份 {reg_addr}:{bit_field} = {current_value}")

            # 第二阶段：执行所有更新操作
            success_list = []
            completed_operations = []

            for op in update_operations:
                reg_addr = op['reg_addr']
                bit_field = op['bit_field']
                bit_value = op['bit_value']

                success = self._safe_set_bit_field_value(reg_addr, bit_field, bit_value)
                success_list.append(success)

                if success:
                    completed_operations.append(op)
                    logger.info(f"成功更新寄存器 {reg_addr} 的 {bit_field} 位字段: {bit_value}")
                else:
                    logger.error(f"更新寄存器 {reg_addr} 的 {bit_field} 位字段失败")
                    # 如果任何操作失败，立即开始回滚
                    self._rollback_operations(widget_name, completed_operations, backup_states)
                    return False

            # 第三阶段：验证所有更新都成功
            all_success = all(success_list)

            if all_success:
                logger.info(f"跨寄存器控件 {widget_name} 原子性更新成功")
                return True
            else:
                # 理论上不应该到达这里，因为失败会在上面被捕获
                logger.error(f"跨寄存器控件 {widget_name} 原子性更新失败")
                self._rollback_operations(widget_name, completed_operations, backup_states)
                return False

        except Exception as e:
            logger.error(f"原子性更新 {widget_name} 时发生异常: {str(e)}")
            # 尝试回滚已完成的操作
            if 'completed_operations' in locals() and 'backup_states' in locals():
                self._rollback_operations(widget_name, completed_operations, backup_states)
            return False

    def _rollback_operations(self, widget_name: str, completed_operations: List[Dict], backup_states: Dict) -> None:
        """
        回滚已完成的操作

        Args:
            widget_name: 控件名称
            completed_operations: 已完成的操作列表
            backup_states: 备份状态
        """
        try:
            logger.warning(f"开始回滚 {widget_name} 的 {len(completed_operations)} 个已完成操作")

            # 逆序回滚操作
            for op in reversed(completed_operations):
                reg_addr = op['reg_addr']
                bit_field = op['bit_field']
                backup_key = f"{reg_addr}:{bit_field}"

                if backup_key in backup_states:
                    backup_value = backup_states[backup_key]
                    rollback_success = self._safe_set_bit_field_value(reg_addr, bit_field, backup_value)

                    if rollback_success:
                        logger.info(f"成功回滚 {reg_addr}:{bit_field} 到 {backup_value}")
                    else:
                        logger.error(f"回滚 {reg_addr}:{bit_field} 失败")
                else:
                    logger.error(f"未找到 {backup_key} 的备份值")

            logger.warning(f"{widget_name} 回滚操作完成")

        except Exception as e:
            logger.error(f"回滚 {widget_name} 操作时发生异常: {str(e)}")
            # 回滚失败是严重问题，但我们不能抛出异常，只能记录
    
    def _read_combined_value(self, config: CrossRegisterWidgetConfig) -> int:
        """
        从寄存器读取组合值
        
        Args:
            config: 控件配置
            
        Returns:
            int: 组合值
        """
        try:
            if not self.register_manager:
                # 如果没有寄存器管理器，返回默认值
                return int(config.default_value, 2) if config.default_value.startswith('0b') or all(c in '01' for c in config.default_value) else int(config.default_value)
            
            combined_value = 0
            bit_offset = 0
            
            # 从低位到高位组合值（反向遍历）
            for i in reversed(range(len(config.reg_addrs))):
                reg_addr = config.reg_addrs[i]
                bit_field = config.bit_fields[i]
                bit_width = config.bit_widths[i]
                
                # 从寄存器读取位字段值
                bit_value = self._safe_get_bit_field_value(reg_addr, bit_field)
                if bit_value is None:
                    bit_value = 0
                    
                # 组合到总值中
                combined_value |= (bit_value << bit_offset)
                bit_offset += bit_width
            
            return combined_value
            
        except Exception as e:
            logger.error(f"读取组合值失败: {str(e)}")
            # 返回默认值
            try:
                return int(config.default_value, 2) if config.default_value.startswith('0b') or all(c in '01' for c in config.default_value) else int(config.default_value)
            except:
                return config.min_value
    
    def _decompose_value(self, value: int, config: CrossRegisterWidgetConfig) -> List[int]:
        """
        将组合值分解为各个位字段的值
        
        Args:
            value: 组合值
            config: 控件配置
            
        Returns:
            List[int]: 各个位字段的值列表
        """
        bit_values = []
        remaining_value = value
        
        # 从低位到高位分解（反向遍历）
        for i in reversed(range(len(config.bit_widths))):
            bit_width = config.bit_widths[i]
            mask = (1 << bit_width) - 1
            bit_value = remaining_value & mask
            bit_values.insert(0, bit_value)  # 插入到前面保持顺序
            remaining_value >>= bit_width
            
        return bit_values
    
    def get_widget_config(self, widget_name: str) -> Optional[CrossRegisterWidgetConfig]:
        """获取控件配置"""
        return self.widget_configs.get(widget_name)
    
    def is_cross_register_widget(self, widget_name: str) -> bool:
        """检查是否为跨寄存器控件"""
        return widget_name in self.widget_configs

    def update_widget_from_register(self, widget_name: str, reg_addr: str) -> bool:
        """
        从寄存器更新控件值（反向同步）

        Args:
            widget_name: 控件名称
            reg_addr: 发生变化的寄存器地址

        Returns:
            bool: 更新是否成功
        """
        try:
            if widget_name not in self.widget_configs:
                return False

            config = self.widget_configs[widget_name]

            # 检查这个寄存器是否与该控件相关
            if reg_addr not in config.reg_addrs:
                return False

            if not self.ui or not hasattr(self.ui, widget_name):
                logger.warning(f"控件 {widget_name} 不存在")
                return False

            widget = getattr(self.ui, widget_name)

            # 读取所有相关寄存器的当前值并组合
            combined_value = self._read_combined_value_fresh(config)

            # 更新控件值（阻止信号避免循环更新）
            widget.blockSignals(True)
            try:
                if hasattr(widget, 'setValue'):
                    widget.setValue(int(combined_value))
                elif hasattr(widget, 'setCurrentIndex'):
                    widget.setCurrentIndex(int(combined_value))

                logger.info(f"已从寄存器 {reg_addr} 更新跨寄存器控件 {widget_name} 为: {combined_value}")
                return True

            finally:
                widget.blockSignals(False)

        except Exception as e:
            logger.error(f"从寄存器更新控件 {widget_name} 失败: {str(e)}")
            return False

    def _read_combined_value_fresh(self, config: CrossRegisterWidgetConfig) -> int:
        """
        从寄存器读取最新的组合值（强制刷新）

        Args:
            config: 控件配置

        Returns:
            int: 组合值
        """
        try:
            if not self.register_manager:
                # 如果没有寄存器管理器，返回默认值
                return int(config.default_value, 2) if config.default_value.startswith('0b') or all(c in '01' for c in config.default_value) else int(config.default_value)

            combined_value = 0
            bit_offset = 0

            # 从低位到高位组合值（反向遍历）
            for i in reversed(range(len(config.reg_addrs))):
                reg_addr = config.reg_addrs[i]
                bit_field = config.bit_fields[i]
                bit_width = config.bit_widths[i]

                # 强制从寄存器读取最新值
                bit_value = self._get_fresh_bit_field_value(reg_addr, bit_field)
                if bit_value is None:
                    bit_value = 0

                # 组合到总值中
                combined_value |= (bit_value << bit_offset)
                bit_offset += bit_width

                logger.debug(f"寄存器 {reg_addr}.{bit_field}: {bit_value} (位宽{bit_width}, 偏移{bit_offset-bit_width})")

            logger.debug(f"组合值计算结果: {combined_value}")
            return combined_value

        except Exception as e:
            logger.error(f"读取最新组合值失败: {str(e)}")
            # 返回默认值
            try:
                return int(config.default_value, 2) if config.default_value.startswith('0b') or all(c in '01' for c in config.default_value) else int(config.default_value)
            except:
                return config.min_value

    def _get_fresh_bit_field_value(self, reg_addr: str, bit_field: str) -> Optional[int]:
        """
        获取最新的位字段值（可能需要强制刷新）

        Args:
            reg_addr: 寄存器地址
            bit_field: 位字段名称

        Returns:
            Optional[int]: 位字段值，失败时返回None
        """
        try:
            if not self.register_manager:
                return None

            # 首先尝试直接获取位字段值
            result = self.register_manager.get_bit_field_value(reg_addr, bit_field)

            # 如果获取失败，尝试通过寄存器值计算
            if result is None:
                reg_value = self.register_manager.get_register_value(reg_addr)
                if reg_value is not None:
                    # 这里需要根据位字段定义来计算，暂时返回None
                    logger.warning(f"无法直接获取位字段 {reg_addr}.{bit_field}，寄存器值为 0x{reg_value:04X}")

            logger.debug(f"获取位字段 {reg_addr}.{bit_field} = {result}")
            return result

        except Exception as e:
            logger.error(f"获取最新位字段 {reg_addr}.{bit_field} 时发生异常: {str(e)}")
            return None

    def update_widgets_from_register(self, reg_addr: str) -> List[str]:
        """
        更新所有与指定寄存器相关的跨寄存器控件

        Args:
            reg_addr: 寄存器地址

        Returns:
            List[str]: 成功更新的控件名称列表
        """
        updated_widgets = []

        try:
            for widget_name, config in self.widget_configs.items():
                if reg_addr in config.reg_addrs:
                    if self.update_widget_from_register(widget_name, reg_addr):
                        updated_widgets.append(widget_name)

            if updated_widgets:
                logger.info(f"寄存器 {reg_addr} 更新了跨寄存器控件: {updated_widgets}")

        except Exception as e:
            logger.error(f"批量更新跨寄存器控件失败: {str(e)}")

        return updated_widgets

    def set_error_recovery_enabled(self, enabled: bool) -> None:
        """设置是否启用错误恢复"""
        self._error_recovery_enabled = enabled
        logger.info(f"错误恢复已{'启用' if enabled else '禁用'}")

    def set_max_retry_attempts(self, max_attempts: int) -> None:
        """设置最大重试次数"""
        if max_attempts < 0:
            raise ValueError("最大重试次数不能为负数")
        self._max_retry_attempts = max_attempts
        logger.info(f"最大重试次数设置为: {max_attempts}")

    def validate_register_manager(self) -> bool:
        """验证寄存器管理器是否可用"""
        try:
            if not self.register_manager:
                logger.error("寄存器管理器未设置")
                return False

            # 检查寄存器管理器是否有必要的方法
            required_methods = ['get_bit_field_value', 'set_bit_field_value']
            for method in required_methods:
                if not hasattr(self.register_manager, method):
                    logger.error(f"寄存器管理器缺少必要方法: {method}")
                    return False

            logger.debug("寄存器管理器验证通过")
            return True

        except Exception as e:
            logger.error(f"验证寄存器管理器时发生异常: {str(e)}")
            return False

    def validate_widget_config(self, config: CrossRegisterWidgetConfig) -> bool:
        """验证控件配置的有效性"""
        try:
            # 检查基本参数
            if not config.widget_name:
                logger.error("控件名称不能为空")
                return False

            if not config.bit_fields or not config.reg_addrs:
                logger.error("位字段和寄存器地址不能为空")
                return False

            if len(config.bit_fields) != len(config.reg_addrs):
                logger.error("位字段和寄存器地址数量不匹配")
                return False

            # 检查值范围
            if config.min_value >= config.max_value:
                logger.error(f"最小值 {config.min_value} 必须小于最大值 {config.max_value}")
                return False

            # 检查位宽是否合理
            if config.total_bits <= 0 or config.total_bits > 32:
                logger.error(f"总位宽 {config.total_bits} 不在合理范围内 (1-32)")
                return False

            # 检查值范围是否与位宽匹配
            max_possible_value = (1 << config.total_bits) - 1
            if config.max_value > max_possible_value:
                logger.warning(f"最大值 {config.max_value} 超过位宽允许的最大值 {max_possible_value}")

            logger.debug(f"控件配置 {config.widget_name} 验证通过")
            return True

        except Exception as e:
            logger.error(f"验证控件配置时发生异常: {str(e)}")
            return False

    def get_error_statistics(self) -> Dict[str, int]:
        """获取错误统计信息"""
        # 这里可以添加错误计数器来跟踪各种错误
        # 目前返回一个基本的统计信息
        return {
            "total_widgets": len(self.widget_configs),
            "register_manager_available": 1 if self.register_manager else 0,
            "ui_available": 1 if self.ui else 0
        }

    def diagnose_widget_issues(self, widget_name: str) -> List[str]:
        """诊断特定控件的问题"""
        issues = []

        try:
            # 检查控件是否已注册
            if widget_name not in self.widget_configs:
                issues.append(f"控件 {widget_name} 未注册")
                return issues

            config = self.widget_configs[widget_name]

            # 检查UI控件是否存在
            if not self.ui or not hasattr(self.ui, widget_name):
                issues.append(f"UI中不存在控件 {widget_name}")

            # 检查寄存器管理器
            if not self.validate_register_manager():
                issues.append("寄存器管理器不可用")

            # 检查配置有效性
            if not self.validate_widget_config(config):
                issues.append("控件配置无效")

            # 检查寄存器连通性
            if self.register_manager:
                for reg_addr, bit_field in zip(config.reg_addrs, config.bit_fields):
                    try:
                        value = self._safe_get_bit_field_value(reg_addr, bit_field)
                        if value is None:
                            issues.append(f"无法读取寄存器 {reg_addr} 的位字段 {bit_field}")
                    except Exception as e:
                        issues.append(f"读取寄存器 {reg_addr}:{bit_field} 时出错: {str(e)}")

            if not issues:
                logger.info(f"控件 {widget_name} 诊断通过，未发现问题")
            else:
                logger.warning(f"控件 {widget_name} 诊断发现 {len(issues)} 个问题")

        except Exception as e:
            issues.append(f"诊断过程中发生异常: {str(e)}")

        return issues

    def _safe_set_bit_field_value(self, reg_addr: str, bit_field: str, value: int) -> bool:
        """
        安全地设置位字段值，处理不同的返回值类型

        Args:
            reg_addr: 寄存器地址
            bit_field: 位字段名称
            value: 值

        Returns:
            bool: 操作是否成功
        """
        try:
            if not self.register_manager:
                logger.error("RegisterManager未设置")
                return False

            # 调用寄存器管理器的方法
            result = self.register_manager.set_bit_field_value(reg_addr, bit_field, value)

            # 处理不同的返回值类型
            if result is None:
                # 如果返回None，检查是否真的更新成功
                # 通过读取值来验证
                try:
                    current_value = self.register_manager.get_bit_field_value(reg_addr, bit_field)
                    success = (current_value == value)
                    if success:
                        logger.debug(f"位字段更新成功（通过验证）: {reg_addr}.{bit_field} = {value}")
                    else:
                        logger.warning(f"位字段更新可能失败（验证不匹配）: {reg_addr}.{bit_field} 期望={value}, 实际={current_value}")
                    return success
                except Exception as verify_error:
                    logger.warning(f"无法验证位字段更新结果: {verify_error}")
                    # 如果无法验证，假设成功（因为没有抛出异常）
                    return True
            elif isinstance(result, bool):
                # 如果返回布尔值，直接使用
                return result
            elif isinstance(result, (int, float)):
                # 如果返回数值（可能是更新后的寄存器值），认为成功
                return True
            else:
                # 其他情况，记录警告并假设成功
                logger.warning(f"位字段设置返回了未预期的类型: {type(result)}, 值: {result}")
                return True

        except Exception as e:
            logger.error(f"设置位字段 {reg_addr}.{bit_field} 时发生异常: {str(e)}")
            return False

    def _safe_get_bit_field_value(self, reg_addr: str, bit_field: str) -> Optional[int]:
        """
        安全地获取位字段值

        Args:
            reg_addr: 寄存器地址
            bit_field: 位字段名称

        Returns:
            Optional[int]: 位字段值，失败时返回None
        """
        try:
            if not self.register_manager:
                return None

            result = self.register_manager.get_bit_field_value(reg_addr, bit_field)
            return result

        except Exception as e:
            logger.error(f"获取位字段 {reg_addr}.{bit_field} 时发生异常: {str(e)}")
            return None
