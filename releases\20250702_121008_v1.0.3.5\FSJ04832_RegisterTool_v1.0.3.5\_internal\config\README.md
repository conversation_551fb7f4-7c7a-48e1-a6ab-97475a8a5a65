# 配置系统说明

## 📋 概述

本项目使用分层配置系统，支持多个配置文件的优先级加载和合并。

## 📁 配置文件结构

```
config/
├── default.json          # 默认配置（必需）
├── app.json              # 应用配置（可选）
├── local.json            # 本地配置（可选）
├── app.json.template     # 应用配置模板
└── README.md            # 本说明文件
```

## 🔄 加载优先级

配置文件按以下优先级顺序加载，后加载的配置会覆盖先加载的配置：

1. **default.json** (必需) - 系统默认配置
2. **app.json** (可选) - 应用级配置
3. **local.json** (可选) - 本地覆盖配置

## 📝 配置文件说明

### default.json
- **用途**: 包含系统的默认配置
- **状态**: 必需文件，不存在会导致错误
- **修改**: 不建议直接修改，应通过其他配置文件覆盖

### app.json
- **用途**: 应用级别的配置，适用于特定部署环境
- **状态**: 可选文件，不存在不会产生警告
- **创建**: 复制 `app.json.template` 并重命名为 `app.json`
- **适用场景**: 
  - 生产环境配置
  - 团队共享配置
  - 特定功能开关

### local.json
- **用途**: 本地开发者的个人配置
- **状态**: 可选文件，通常用于开发环境
- **版本控制**: 建议添加到 `.gitignore`
- **适用场景**:
  - 开发者个人偏好
  - 本地测试配置
  - 临时配置修改

## 🛠️ 使用方法

### 创建应用配置
```bash
# 复制模板文件
cp config/app.json.template config/app.json

# 编辑配置
# 修改 app.json 中的配置项
```

### 创建本地配置
```bash
# 创建本地配置文件
echo '{"app": {"window": {"width": 1600}}}' > config/local.json
```

### 配置访问
```python
from core.services.config.ConfigurationManager import config_manager

# 获取配置值
window_width = config_manager.get('app.window.width', 1840)
spi_timeout = config_manager.get('spi.timeout', 5000)

# 设置配置值
config_manager.set('app.window.width', 1600)

# 监听配置变化
def on_theme_changed(new_theme):
    print(f"主题已更改为: {new_theme}")

config_manager.watch('ui.theme', on_theme_changed)
```

## 🔧 配置项说明

### 应用配置 (app)
- `name`: 应用名称
- `version`: 应用版本
- `window`: 窗口相关配置

### UI配置 (ui)
- `theme`: 界面主题
- `language`: 界面语言
- `font`: 字体配置
- `layout`: 布局配置

### SPI配置 (spi)
- `timeout`: 操作超时时间
- `retry_count`: 重试次数
- `batch_size`: 批量操作大小
- `auto_scan_ports`: 自动扫描端口

### 寄存器配置 (register)
- `auto_write`: 自动写入模式
- `simulation_mode`: 模拟模式
- `preload_enabled`: 预加载功能
- `default_register`: 默认寄存器

### 日志配置 (logging)
- `level`: 日志级别
- `file_enabled`: 文件日志开关
- `console_enabled`: 控制台日志开关

### 性能配置 (performance)
- `async_operations`: 异步操作开关
- `ui_update_interval`: UI更新间隔
- `batch_operation_delay`: 批量操作延迟

### 插件配置 (plugins)
- `enabled`: 插件系统开关
- `directories`: 插件目录
- `auto_load`: 自动加载插件

## ⚠️ 注意事项

1. **不要直接修改 default.json**，应通过 app.json 或 local.json 覆盖配置
2. **local.json 应添加到 .gitignore**，避免个人配置影响其他开发者
3. **app.json 可以提交到版本控制**，用于团队共享配置
4. **配置文件必须是有效的 JSON 格式**
5. **使用点号分隔的路径访问嵌套配置**，如 `app.window.width`

## 🐛 故障排除

### 配置文件不存在警告
- **问题**: 看到 "可选配置文件不存在" 的警告
- **解决**: 这是正常现象，可选配置文件不存在不会影响系统运行

### 配置值未生效
- **检查**: 确认配置文件格式正确
- **检查**: 确认配置路径正确
- **检查**: 确认配置文件加载顺序

### JSON 格式错误
- **工具**: 使用 JSON 验证工具检查格式
- **常见错误**: 缺少逗号、多余逗号、引号不匹配
