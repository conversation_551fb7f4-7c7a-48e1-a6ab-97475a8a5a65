#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的拖拽停靠功能测试脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QTimer
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


def test_drag_dock_functionality():
    """测试拖拽停靠功能"""
    try:
        print("🧪 开始测试拖拽停靠功能...")
        
        # 1. 启用强制悬浮模式
        print("1. 启用强制悬浮模式...")
        from core.services.config.ConfigurationManager import set_config, get_config
        set_config('plugins.force_floating_mode', True)
        print(f"   ✅ 强制悬浮模式已启用: {get_config('plugins.force_floating_mode')}")
        
        # 2. 创建主窗口
        print("2. 创建主窗口...")
        from ui.windows.RegisterMainWindow import RegisterMainWindow
        main_window = RegisterMainWindow()
        main_window.show()
        print("   ✅ 主窗口创建成功")
        
        # 3. 获取插件服务
        print("3. 获取插件服务...")
        if hasattr(main_window, 'plugin_service'):
            plugin_service = main_window.plugin_service
            print("   ✅ 插件服务获取成功")
        else:
            print("   ❌ 插件服务未找到")
            return False
        
        # 4. 显示测试说明
        def show_test_instructions():
            """显示测试说明"""
            try:
                msg_box = QMessageBox()
                msg_box.setWindowTitle("拖拽停靠功能测试")
                msg_box.setText("拖拽停靠功能测试已准备就绪！")
                msg_box.setDetailedText("""
测试步骤：

1. 通过菜单栏 -> 工具 -> 打开任意工具窗口
   （如：时钟输入控制、PLL控制等）

2. 窗口将以悬浮模式显示（而不是标签页）

3. 按住鼠标左键拖拽窗口到主窗口底部50%区域
   - 可以拖拽窗口标题栏
   - 也可以拖拽窗口内容区域（非输入控件）

4. 观察视觉反馈：
   - 窗口标题显示"释放鼠标停靠到主界面"
   - 鼠标光标变为手型指针

5. 在停靠区域释放鼠标，窗口将自动停靠到标签页

调试信息：
- 查看控制台日志获取详细的拖拽调试信息
- 停靠区域为主窗口底部50%的区域
- 如果拖拽不工作，请检查日志中的错误信息
- 现在支持从窗口内容区域拖拽（已增强）

测试完成后：
- 通过菜单栏 -> 插件 -> 禁用拖拽停靠测试模式
- 或者关闭应用程序重启

注意：
- 如果看不到拖拽调试信息，说明事件没有被触发
- 尝试从不同的窗口区域开始拖拽
                """)
                msg_box.setIcon(QMessageBox.Information)
                msg_box.exec_()

                print("   ✅ 测试说明已显示")

            except Exception as e:
                print(f"   ❌ 显示测试说明失败: {str(e)}")

        # 延迟显示测试说明，确保主窗口完全加载
        QTimer.singleShot(1000, show_test_instructions)

        # 5. 自动测试拖拽事件
        def auto_test_drag_events():
            """自动测试拖拽事件"""
            try:
                print("5. 开始自动测试拖拽事件...")

                # 等待一段时间确保窗口完全加载
                QTimer.singleShot(3000, lambda: test_drag_events_for_plugin(plugin_service))

            except Exception as e:
                print(f"   ❌ 自动测试拖拽事件失败: {str(e)}")

        def test_drag_events_for_plugin(plugin_service):
            """测试插件的拖拽事件"""
            try:
                # 测试时钟输入控制插件的拖拽事件
                plugin_name = "时钟输入控制"
                print(f"   🧪 测试插件 {plugin_name} 的拖拽事件...")

                result = plugin_service.test_drag_events(plugin_name)
                if result:
                    print(f"   ✅ 插件 {plugin_name} 拖拽事件测试成功")
                else:
                    print(f"   ❌ 插件 {plugin_name} 拖拽事件测试失败")

            except Exception as e:
                print(f"   ❌ 测试拖拽事件时出错: {str(e)}")

        # 启动自动测试
        auto_test_drag_events()

        print("4. 测试环境准备完成")
        print("\n🎯 请按照弹出的说明进行拖拽停靠测试")
        print("📝 查看控制台输出获取详细调试信息")
        print("🤖 自动拖拽事件测试将在3秒后开始")

        return True
        
    except Exception as e:
        print(f"❌ 测试准备失败: {str(e)}")
        logger.error(f"测试准备失败: {str(e)}")
        return False


def main():
    """主函数"""
    try:
        # 创建QApplication
        app = QApplication(sys.argv)
        
        # 设置应用程序信息
        app.setApplicationName("拖拽停靠功能测试")
        app.setApplicationVersion("1.0.0")
        
        print("🚀 启动拖拽停靠功能测试...")
        
        # 运行测试
        if test_drag_dock_functionality():
            print("✅ 测试环境启动成功")
            print("💡 提示：关闭主窗口或按Ctrl+C退出测试")
            
            # 运行应用程序
            sys.exit(app.exec_())
        else:
            print("❌ 测试环境启动失败")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 测试运行失败: {str(e)}")
        logger.error(f"测试运行失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
