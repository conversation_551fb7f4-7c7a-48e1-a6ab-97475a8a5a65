#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试寄存器表格跳转功能

用于验证工具界面修改控件状态时，寄存器表格是否正确跳转
"""

import sys
import os
import logging
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QCheckBox
from PyQt5.QtCore import QTimer

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_register_navigation():
    """测试寄存器表格跳转功能"""
    print("=" * 60)
    print("测试寄存器表格跳转功能")
    print("=" * 60)
    
    try:
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建主窗口
        from ui.windows.RegisterMainWindow import RegisterMainWindow
        from core.services.spi.spi_service_impl import SPIServiceImpl
        from core.repositories.register_repository import RegisterRepository
        
        # 初始化SPI服务
        spi_service = SPIServiceImpl()
        spi_service.initialize()
        register_repo = RegisterRepository(spi_service)
        
        # 创建主窗口
        main_window = RegisterMainWindow(register_repo)
        main_window.show()
        
        print("✅ 主窗口创建成功")
        
        # 检查现代化处理器
        if hasattr(main_window, 'table_handler'):
            from ui.handlers.ModernRegisterTableHandler import ModernRegisterTableHandler
            if isinstance(main_window.table_handler, ModernRegisterTableHandler):
                print("✅ 现代化表格处理器已加载")
            else:
                print(f"❌ 表格处理器类型错误: {type(main_window.table_handler)}")
        else:
            print("❌ 主窗口没有 table_handler")
            
        # 检查工具窗口
        tool_windows = []
        
        # 创建时钟输入控制窗口
        try:
            clkin_window = main_window._show_clkin_control_window()
            if clkin_window:
                tool_windows.append(("时钟输入控制", clkin_window))
                print("✅ 时钟输入控制窗口创建成功")
            else:
                print("❌ 时钟输入控制窗口创建失败")
        except Exception as e:
            print(f"❌ 创建时钟输入控制窗口时出错: {str(e)}")
        
        # 测试寄存器跳转
        def test_navigation():
            print("\n开始测试寄存器表格跳转...")

            # 首先测试直接调用 on_register_selected
            print("\n=== 测试1: 直接调用 on_register_selected ===")
            try:
                test_addr = "0x5B"
                print(f"直接调用 main_window.on_register_selected('{test_addr}')")
                main_window.on_register_selected(test_addr)
                print("✅ 直接调用完成")
            except Exception as e:
                print(f"❌ 直接调用出错: {str(e)}")
                import traceback
                traceback.print_exc()

            # 然后测试工具窗口控件修改
            print("\n=== 测试2: 工具窗口控件修改 ===")
            for window_name, window in tool_windows:
                print(f"\n测试 {window_name} 窗口:")

                # 查找控件
                if hasattr(window, 'widget_register_map'):
                    print(f"  找到 widget_register_map，包含 {len(window.widget_register_map)} 个控件")

                    # 列出所有控件
                    for widget_name, widget_info in window.widget_register_map.items():
                        print(f"    控件: {widget_name} -> 寄存器: {widget_info['register_addr']}")

                    # 查找第一个控件进行测试
                    if window.widget_register_map:
                        first_widget = list(window.widget_register_map.keys())[0]
                        widget_info = window.widget_register_map[first_widget]

                        print(f"  测试控件: {first_widget} -> 寄存器: {widget_info['register_addr']}")

                        # 模拟控件值变化
                        try:
                            if hasattr(window, '_on_widget_changed'):
                                print(f"  调用 window._on_widget_changed('{first_widget}', 1)")
                                window._on_widget_changed(first_widget, 1)
                                print(f"  ✅ 控件修改完成")
                            else:
                                print(f"  ❌ 窗口没有 _on_widget_changed 方法")
                                print(f"  窗口类型: {type(window)}")
                                print(f"  窗口方法: {[m for m in dir(window) if not m.startswith('_')]}")
                        except Exception as e:
                            print(f"  ❌ 修改控件时出错: {str(e)}")
                            import traceback
                            traceback.print_exc()
                else:
                    print(f"  ❌ {window_name} 窗口没有 widget_register_map")
                    print(f"  窗口类型: {type(window)}")
                    print(f"  窗口属性: {[attr for attr in dir(window) if not attr.startswith('_')]}")

            # 检查表格状态
            print("\n=== 测试3: 检查表格状态 ===")
            if hasattr(main_window, 'table_handler'):
                try:
                    status = main_window.table_handler.get_current_status()
                    print(f"表格状态: {status}")
                except Exception as e:
                    print(f"获取表格状态时出错: {str(e)}")
            else:
                print("主窗口没有 table_handler")
        
        # 延迟执行测试
        QTimer.singleShot(2000, test_navigation)
        
        # 运行应用程序
        print("\n启动应用程序...")
        print("请观察日志输出，查看寄存器表格跳转是否正常工作")
        print("修改工具界面控件后，应该看到表格跳转到对应的寄存器页面")
        
        # 运行一段时间后自动退出
        QTimer.singleShot(10000, app.quit)
        
        app.exec_()
        
        print("\n" + "=" * 60)
        print("测试完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_register_navigation()
