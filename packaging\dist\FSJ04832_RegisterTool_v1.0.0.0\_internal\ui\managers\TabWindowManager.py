"""
标签页窗口管理器
负责管理工具窗口的标签页创建、关闭和状态管理
"""

from PyQt5 import sip
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class TabWindowManager:
    """标签页窗口管理器，专门处理标签页相关的逻辑"""
    
    def __init__(self, main_window):
        """初始化标签页窗口管理器
        
        Args:
            main_window: 主窗口实例
        """
        self.main_window = main_window
    
    def close_tool_tab(self, index):
        """关闭工具标签页"""
        widget = self.main_window.tools_tab_widget.widget(index)  # 获取与标签页关联的控件
        tab_text = self.main_window.tools_tab_widget.tabText(index)

        logger.info(f"开始关闭工具标签页: {tab_text} (index: {index})")

        # 在移除标签页之前，通知插件集成服务
        self._notify_plugin_tab_closed(tab_text, widget)

        # 清理窗口资源
        self._cleanup_tab_resources(widget, tab_text)

        # 移除标签页
        self.main_window.tools_tab_widget.removeTab(index)

        # 处理菜单状态重置
        action_unchecked_successfully = False
        if widget:
            # 尝试通过直接存储在widget上的属性来获取关联的QAction
            associated_action = getattr(widget, 'associated_action', None)
            if associated_action and hasattr(associated_action, 'setChecked') and callable(associated_action.setChecked):
                if not sip.isdeleted(associated_action):
                    associated_action.setChecked(False)
                    logger.info(f"通过直接属性取消选中菜单项: '{associated_action.text()}' (for tab '{tab_text}')")
                    action_unchecked_successfully = True
                else:
                    logger.warning(f"QAction '{associated_action.text()}' (for tab '{tab_text}') 已被删除，无法取消选中.")
            else:
                logger.debug(f"在控件上未找到有效的 'associated_action' (for tab '{tab_text}'). 尝试后备方案.")
        else:
            logger.warning(f"未找到标签页 '{tab_text}' (index {index}) 的关联控件. 尝试后备方案取消选中.")

        if not action_unchecked_successfully:
            self._uncheck_action_by_tab_text(tab_text)  # 后备方案

        # 检查是否需要隐藏标签页容器
        self._check_and_hide_tab_widget()

        logger.info(f"工具标签页关闭完成: {tab_text}")

    def _check_and_hide_tab_widget(self):
        """检查并隐藏标签页容器（如果没有标签页）"""
        try:
            if hasattr(self.main_window, 'tools_tab_widget'):
                tab_count = self.main_window.tools_tab_widget.count()
                logger.debug(f"当前标签页数量: {tab_count}")

                if tab_count == 0:
                    self.main_window.tools_tab_widget.setVisible(False)
                    logger.info("所有标签页已关闭，隐藏标签页容器")

                    # 强制刷新UI以确保布局更新
                    self.main_window.tools_tab_widget.update()
                    if hasattr(self.main_window, 'centralWidget'):
                        self.main_window.centralWidget().update()
                else:
                    logger.debug(f"仍有 {tab_count} 个标签页，保持容器可见")
            else:
                logger.warning("主窗口没有 tools_tab_widget 属性")
        except Exception as e:
            logger.error(f"检查和隐藏标签页容器时出错: {str(e)}")

    def _cleanup_tab_resources(self, widget, tab_text):
        """清理标签页相关资源

        Args:
            widget: 标签页关联的控件
            tab_text: 标签页文本
        """
        try:
            if not widget:
                return

            # 如果容器中有插件窗口，尝试清理
            if hasattr(widget, 'plugin_window'):
                plugin_window = widget.plugin_window
                if plugin_window:
                    try:
                        # 断开信号连接
                        if hasattr(plugin_window, 'disconnect'):
                            plugin_window.disconnect()

                        # 清理窗口资源
                        if hasattr(plugin_window, 'cleanup'):
                            plugin_window.cleanup()

                        logger.debug(f"已清理插件窗口资源: {tab_text}")
                    except Exception as e:
                        logger.warning(f"清理插件窗口资源时出错 {tab_text}: {str(e)}")

            # 清理容器本身
            if hasattr(widget, 'deleteLater'):
                widget.deleteLater()

            logger.debug(f"已清理标签页容器资源: {tab_text}")

        except Exception as e:
            logger.error(f"清理标签页资源时出错 {tab_text}: {str(e)}")

    def _notify_plugin_tab_closed(self, tab_text, widget):
        """通知插件集成服务标签页已关闭

        Args:
            tab_text: 标签页文本
            widget: 标签页关联的控件
        """
        try:
            # 检查是否有插件集成服务
            if not hasattr(self.main_window, 'plugin_integration_service'):
                return

            plugin_service = self.main_window.plugin_integration_service
            plugin_name = None

            # 方法1: 从容器widget中获取插件信息（优先）
            if widget and hasattr(widget, 'plugin_name'):
                plugin_name = widget.plugin_name
                logger.debug(f"从容器获取插件名称: {plugin_name}")

            # 方法2: 根据标签页文本映射到插件名称（后备）
            if not plugin_name:
                tab_to_plugin_map = {
                    '时钟输入控制': 'clkin_control_plugin',
                    'PLL控制': 'pll_control_plugin',
                    '同步系统参考': 'sync_sysref_plugin',
                    '时钟输出': 'clk_output_plugin',
                    '模式设置': 'set_modes_plugin',
                    '示例工具': '示例工具'  # 示例工具的映射
                }
                plugin_name = tab_to_plugin_map.get(tab_text)
                if plugin_name:
                    logger.debug(f"从文本映射获取插件名称: {plugin_name}")

            if plugin_name:
                # 通知插件集成服务窗口已关闭，标记为来自标签页关闭
                plugin_service._on_plugin_window_closed(plugin_name, from_tab_close=True)
                logger.info(f"已通知插件集成服务标签页关闭: {plugin_name}")
            else:
                logger.debug(f"未找到标签页 '{tab_text}' 对应的插件名称")

        except Exception as e:
            logger.error(f"通知插件集成服务标签页关闭时出错: {str(e)}")

    def _uncheck_action_by_tab_text(self, tab_text):
        """根据标签页文本取消选中关联的QAction（后备方法）"""
        # 将标签页标题的已知前缀映射到其对应的QAction属性名称
        # 注意：这里的键是菜单项的文本，可能需要与工具栏按钮的文本或标签页的实际标题匹配
        action_map = {
            # 这些key应该与标签页的完整文本匹配
            "模式设置": self.main_window.set_modes_action if hasattr(self.main_window, 'set_modes_action') else None,
            "时钟输入控制": self.main_window.clkin_control_action if hasattr(self.main_window, 'clkin_control_action') else None,
            "时钟输入": self.main_window.clkin_control_action if hasattr(self.main_window, 'clkin_control_action') else None, # 后备匹配
            "PLL控制": self.main_window.pll_control_action if hasattr(self.main_window, 'pll_control_action') else None,
            "PLL1 & PLL2 控制": self.main_window.pll_control_action if hasattr(self.main_window, 'pll_control_action') else None, # 后备匹配
            "同步系统参考": self.main_window.sync_sysref_action if hasattr(self.main_window, 'sync_sysref_action') else None,
            "时钟输出": self.main_window.clk_output_action if hasattr(self.main_window, 'clk_output_action') else None,
            "时钟输出配置": self.main_window.clk_output_action if hasattr(self.main_window, 'clk_output_action') else None, # 后备匹配
        }

        action_to_uncheck = None
        matched_prefix_key = None

        # 首先尝试完全匹配
        if tab_text in action_map and action_map[tab_text]:
            action_to_uncheck = action_map[tab_text]
            matched_prefix_key = tab_text
        else:
            # 如果完全匹配失败，尝试前缀匹配
            for prefix_key, action_instance in action_map.items():
                if action_instance and tab_text.startswith(prefix_key):
                    action_to_uncheck = action_instance
                    matched_prefix_key = prefix_key
                    break
        
        if action_to_uncheck:
            if hasattr(action_to_uncheck, 'setChecked') and callable(action_to_uncheck.setChecked):
                if not sip.isdeleted(action_to_uncheck):
                    action_to_uncheck.setChecked(False)
                    logger.info(f"后备方案: 取消选中菜单项 '{action_to_uncheck.text()}' (for tab '{tab_text}', matched prefix '{matched_prefix_key}')")
                else:
                    logger.warning(f"后备方案: QAction '{action_to_uncheck.text()}' (for tab '{tab_text}') 已被删除.")
            else:
                logger.warning(f"后备方案: 为标签页 '{tab_text}' 找到的Action对象无效或没有setChecked方法.")
        else:
            # 检查主窗口是否有对应的 action 属性
            missing_actions = []
            for key, action in action_map.items():
                if action is None:
                    missing_actions.append(key)

            if missing_actions:
                logger.warning(f"后备方案: 标签页 '{tab_text}' 匹配失败。缺少的 action 属性: {missing_actions}")
                logger.info(f"提示: 请检查主窗口是否正确设置了对应的 action 属性")
            else:
                logger.warning(f"后备方案: 未能为标签页文本 '{tab_text}' 匹配到任何已知的QAction. 已知前缀: {list(action_map.keys())}")
    
    def show_set_modes_window(self):
        """显示模式设置窗口"""
        # 传统处理器已移除，使用现代化版本
        from ui.handlers.ModernSetModesHandler import ModernSetModesHandler

        def post_init(window):
            """窗口创建后的初始化回调"""
            # 可以在这里添加一些初始化逻辑
            pass

        # 使用通用窗口创建方法
        return self.main_window._create_window_in_tab(
            title="模式设置",
            window_attr="set_modes_window",
            window_class=ModernSetModesHandler,
            action_attr="set_modes_action",
            post_init_callback=post_init,
            parent=None,  # 确保窗口可以独立使用
            register_manager=self.main_window.register_manager  # 现代化处理器需要RegisterManager
        )
    
    def show_clkin_control_window(self):
        """显示时钟输入控制窗口"""
        # 传统处理器已移除，使用现代化版本
        from ui.handlers.ModernClkinControlHandler import ModernClkinControlHandler

        def post_init(window):
            """窗口创建后的初始化回调"""
            # 可以在这里添加一些初始化逻辑
            pass

        # 使用通用窗口创建方法
        return self.main_window._create_window_in_tab(
            title="时钟输入控制",
            window_attr="clkin_control_window",
            window_class=ModernClkinControlHandler,
            action_attr="clkin_control_action",
            post_init_callback=post_init,
            parent=None,  # 确保窗口可以独立使用
            register_manager=self.main_window.register_manager  # 现代化处理器需要RegisterManager
        )
    
    def show_pll_control_window(self):
        """显示PLL控制窗口"""
        # 传统处理器已移除，使用现代化版本
        from ui.handlers.ModernPLLHandler import ModernPLLHandler

        def post_init(window):
            """窗口创建后的初始化回调"""
            # 可以在这里添加一些初始化逻辑
            pass

        # 使用通用窗口创建方法
        return self.main_window._create_window_in_tab(
            title="PLL1 & PLL2 控制",
            window_attr="pll_control_window",
            window_class=ModernPLLHandler,
            action_attr="pll_control_action",
            post_init_callback=post_init,
            parent=None,  # 确保窗口可以独立使用
            register_manager=self.main_window.register_manager  # 现代化处理器需要RegisterManager
        )
    
    def show_sync_sysref_window(self):
        """显示同步系统参考窗口"""
        # 传统处理器已移除，使用现代化版本
        from ui.handlers.ModernSyncSysRefHandler import ModernSyncSysRefHandler
        
        def post_init(window):
            """窗口创建后的初始化回调"""
            # 如果时钟输出窗口已存在，连接两者
            if hasattr(self.main_window, 'clk_outputs_window') and self.main_window.clk_outputs_window:
                logger.info("设置时钟输出窗口与同步系统参考窗口的连接")
                self.main_window.clk_outputs_window.set_sync_sysref_handler(window)
            
        # 使用通用窗口创建方法
        return self.main_window._create_window_in_tab(
            title="同步系统参考",
            window_attr="sync_sysref_window",
            window_class=ModernSyncSysRefHandler,
            action_attr="sync_sysref_action",
            post_init_callback=post_init,
            parent=None,  # 确保窗口可以独立使用
            register_manager=self.main_window.register_manager  # 现代化处理器需要RegisterManager
        )
    
    def show_clk_output_window(self):
        """显示时钟输出窗口"""
        # 传统处理器已移除，使用现代化版本
        from ui.handlers.ModernClkOutputsHandler import ModernClkOutputsHandler

        def post_init(window):
            """窗口创建后的初始化回调"""
            # 如果同步系统参考窗口已存在，连接两者
            if hasattr(self.main_window, 'sync_sysref_window') and self.main_window.sync_sysref_window:
                logger.info("设置时钟输出窗口与同步系统参考窗口的连接")
                window.set_sync_sysref_handler(self.main_window.sync_sysref_window)

        # 使用通用窗口创建方法
        return self.main_window._create_window_in_tab(
            title="时钟输出配置",
            window_attr="clk_output_window",
            window_class=ModernClkOutputsHandler,
            action_attr="clk_output_action",
            post_init_callback=post_init,
            parent=None,  # 确保窗口可以独立使用
            register_manager=self.main_window.register_manager  # 现代化处理器需要RegisterManager
        )
    
    def get_tab_count(self):
        """获取当前标签页数量"""
        if hasattr(self.main_window, 'tools_tab_widget'):
            return self.main_window.tools_tab_widget.count()
        return 0
    
    def is_tab_visible(self):
        """检查标签页控件是否可见"""
        if hasattr(self.main_window, 'tools_tab_widget'):
            return self.main_window.tools_tab_widget.isVisible()
        return False
    
    def set_tab_visibility(self, visible):
        """设置标签页控件的可见性"""
        if hasattr(self.main_window, 'tools_tab_widget'):
            self.main_window.tools_tab_widget.setVisible(visible)
