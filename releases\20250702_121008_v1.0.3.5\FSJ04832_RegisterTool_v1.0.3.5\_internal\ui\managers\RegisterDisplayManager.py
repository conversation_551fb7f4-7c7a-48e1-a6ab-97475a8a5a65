# -*- coding: utf-8 -*-

"""
寄存器显示管理器
负责管理寄存器值的显示、更新和刷新逻辑
"""

import traceback
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QTimer
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class RegisterDisplayManager:
    """寄存器显示管理器
    
    职责：
    1. 管理寄存器值的显示更新
    2. 处理UI刷新逻辑
    3. 管理寄存器选择和显示状态
    4. 处理显示相关的错误和异常
    """
    
    def __init__(self, main_window):
        """初始化显示管理器

        Args:
            main_window: 主窗口实例
        """
        self.main_window = main_window
        self._in_global_update = False
        self._last_bit_field_update = None  # 防止重复更新位字段显示
        
    def update_register_display(self, reg_num, value):
        """更新寄存器值显示
        
        Args:
            reg_num: 寄存器编号
            value: 寄存器值
        """
        try:
            # 更新值输入框
            if hasattr(self.main_window, 'io_handler'):
                self.main_window.io_handler.set_value_display(value)
                
                # 更新地址显示
                if hasattr(self.main_window.io_handler, 'addr_line_edit'):
                    self.main_window.io_handler.addr_line_edit.setText(f"0x{reg_num:02X}")
                    self.main_window.io_handler.value_label.setText(f"R{reg_num} Value:")
                    
        except Exception as e:
            logger.error(f"更新寄存器显示时出错: {str(e)}")
            
    def update_bit_field_display(self, addr, value):
        """更新位字段显示，不触发写操作

        Args:
            addr: 寄存器地址
            value: 寄存器值
        """
        try:
            # 防止重复更新相同的寄存器和值
            current_update = (addr, value)
            if self._last_bit_field_update == current_update:
                logger.debug(f"DisplayManager: 跳过重复的位字段显示更新 - 地址: {addr}, 值: 0x{value:04X}")
                return

            self._last_bit_field_update = current_update
            logger.info(f"DisplayManager: 开始更新位字段显示 - 地址: {addr}, 值: 0x{value:04X}")

            # 直接调用table_handler的方法显示位字段，不触发写操作
            if hasattr(self.main_window, 'table_handler'):
                logger.info(f"DisplayManager: 找到 table_handler: {type(self.main_window.table_handler)}")
                logger.info(f"DisplayManager: 调用 table_handler.show_bit_fields({addr}, {value}, from_global_update=True)")
                self.main_window.table_handler.show_bit_fields(addr, value, from_global_update=True)
                logger.info(f"DisplayManager: 位字段显示更新完成")
            else:
                logger.warning("DisplayManager: 主窗口没有 table_handler")
        except Exception as e:
            logger.error(f"更新位字段显示时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            
    def refresh_current_view(self):
        """刷新当前视图显示
        在控件状态改变后调用此方法，确保表格视图同步更新
        """
        if not hasattr(self.main_window, 'selected_register_addr') or not self.main_window.selected_register_addr:
            return
            
        try:
            # 获取当前寄存器值（强制从寄存器对象获取最新值）
            register_value = self.main_window.register_manager.get_register_value(self.main_window.selected_register_addr)
            old_value = getattr(self.main_window, 'current_register_value', 0)
            self.main_window.current_register_value = register_value
            
            # 更新寄存器值显示
            reg_num = int(self.main_window.selected_register_addr, 16) if isinstance(self.main_window.selected_register_addr, str) else self.main_window.selected_register_addr
            
            # 更新输入框显示
            self.update_register_display(reg_num, register_value)
            
            # 更新位字段表格
            self.update_bit_field_display(self.main_window.selected_register_addr, register_value)
                    
            # 检查是否在批量操作期间，避免触发布局重新计算
            is_batch_operation = (getattr(self.main_window, 'is_batch_reading', False) or
                                getattr(self.main_window, 'is_batch_writing', False) or
                                getattr(self.main_window, 'is_batch_updating', False))

            # 只在非批量操作且非寄存器切换时处理事件，避免布局抖动
            if not is_batch_operation:
                # 使用更温和的更新方式，避免强制处理所有事件
                if hasattr(self.main_window, 'update'):
                    self.main_window.update()
                # QApplication.processEvents()  # 移除此调用，避免布局重新计算
            
            # 如果值有变化，在状态栏显示详细信息
            if old_value != register_value:
                self.show_status_message(f"寄存器 R{reg_num} (0x{reg_num:02X}) 已刷新: 0x{old_value:04X} → 0x{register_value:04X}", 5000)
            else:
                self.show_status_message(f"寄存器 R{reg_num} (0x{reg_num:02X}) = 0x{register_value:04X} 视图已刷新", 3000)
                
        except Exception as e:
            logger.error(f"刷新视图时出错: {str(e)}\n{traceback.format_exc()}")
            
    def handle_global_register_update(self, reg_addr, reg_value):
        """处理全局寄存器更新事件
        
        Args:
            reg_addr: 寄存器地址
            reg_value: 寄存器值
        """
        normalized_addr = self._normalize_register_address(reg_addr)
        
        if self._in_global_update:
            return
            
        self._in_global_update = True
        try:
            # 更新树形控件中的寄存器值
            if hasattr(self.main_window, 'tree_handler'):
                self.main_window.tree_handler.update_register_value(normalized_addr, reg_value)

            # 只有在非批量操作时才切换页面，避免读写所有寄存器时的跳转
            if not self._is_in_batch_operation():
                if hasattr(self.main_window, 'tree_handler') and hasattr(self.main_window.tree_handler, 'select_register_by_addr'):
                    self.main_window.tree_handler.select_register_by_addr(normalized_addr)

            # 如果是当前选中的寄存器，刷新整个视图
            selected_addr = getattr(self.main_window, 'selected_register_addr', None)
            logger.info(f"DisplayManager: 全局更新事件触发，当前选中寄存器5555555555555 {selected_addr}")
            if selected_addr:
                normalized_selected_addr = self._normalize_register_address(selected_addr)
                if normalized_addr == normalized_selected_addr:
                    logger.info(f"DisplayManager: 全局更新事件触发，刷新当前视图 888888888888888888")
                    self.refresh_current_view()
                
        except Exception as e:
            logger.error(f"处理全局寄存器更新时出错: {str(e)}\n{traceback.format_exc()}")
        finally:
            self._in_global_update = False
            
    def handle_value_changed(self, addr, new_value):
        """处理输入值变化事件
        
        Args:
            addr: 寄存器地址
            new_value: 新值
        """
        try:
            # 标准化地址
            normalized_addr = self._normalize_register_address(addr)
            
            # 记录当前输入值，但不立即触发写操作
            # 只有当用户点击写入按钮时才执行写操作
            if normalized_addr == getattr(self.main_window, 'selected_register_addr', None):
                # 记录原值用于显示变化
                old_value = getattr(self.main_window, 'current_register_value', 0)
                
                # 仅更新显示，不执行写操作
                self.main_window.current_register_value = new_value
                
                # 仅更新位字段表格显示，不触发写操作
                self.update_bit_field_display(normalized_addr, new_value)

                # 注意：移除refresh_current_view调用，避免重复更新位字段
                logger.debug(f"DisplayManager: handle_value_changed跳过refresh_current_view调用，避免重复更新位字段")
                
                # 在状态栏显示寄存器变化详情
                reg_num = int(normalized_addr, 16) if isinstance(normalized_addr, str) else normalized_addr
                self.show_status_message(f"寄存器 R{reg_num} (0x{reg_num:02X}) 值已修改: 0x{old_value:04X} → 0x{new_value:04X}", 5000)
                
        except Exception as e:
            logger.error(f"处理值变化事件时出错: {str(e)}\n{traceback.format_exc()}")
            
    def handle_register_selection(self, reg_addr):
        """处理寄存器选择事件

        Args:
            reg_addr: 寄存器地址

        Returns:
            bool: 是否成功处理选择
        """
        try:
            logger.debug(f"DisplayManager: 开始处理寄存器选择 {reg_addr}")
            # 委托给寄存器操作服务
            success, reg_num, register_value = self.main_window.register_service.handle_register_selection(reg_addr)
            value_str = f"0x{register_value:04X}" if register_value is not None else "0x0000"
            logger.debug(f"DisplayManager: 寄存器操作服务返回: success={success}, reg_num={reg_num}, value={value_str}")

            if success:
                self.main_window.selected_register_addr = reg_addr
                self.main_window.current_register_value = register_value
                logger.info(f"DisplayManager: 已更新主窗口选中寄存器地址为: {reg_addr}")

                # 刷新视图
                self.refresh_current_view()
                return True
            else:
                # 错误处理已在服务中完成
                if hasattr(self.main_window, 'io_handler'):
                    if hasattr(self.main_window.io_handler, 'set_address_display') and hasattr(self.main_window.io_handler, 'set_value_display'):
                        self.main_window.io_handler.set_address_display(reg_addr)
                        self.main_window.io_handler.set_value_display(0) # Placeholder for value
                        
                if hasattr(self.main_window, 'table_handler'):
                    if hasattr(self.main_window.table_handler, 'clear_table_and_show_error'):
                        self.main_window.table_handler.clear_table_and_show_error(f"无法加载寄存器 {reg_addr}")
                return False
                
        except Exception as e:
            logger.error(f"处理寄存器选择时出错: {str(e)}\n{traceback.format_exc()}")
            return False
            
    def show_status_message(self, message, timeout=3000):
        """统一显示状态栏消息
        
        Args:
            message: 消息内容
            timeout: 超时时间（毫秒）
        """
        try:
            if hasattr(self.main_window, "status_bar"):
                self.main_window.status_bar.showMessage(message, timeout)
        except Exception as e:
            logger.error(f"显示状态消息时出错: {str(e)}")
            
    def _normalize_register_address(self, addr):
        """标准化寄存器地址格式
        
        Args:
            addr: 寄存器地址
            
        Returns:
            str: 标准化后的地址
        """
        if hasattr(self.main_window, 'register_manager'):
            return self.main_window.register_manager._normalize_register_address(addr)
        return str(addr)
        
    def _is_in_batch_operation(self):
        """检查是否正在进行批量操作
        
        Returns:
            bool: 如果正在进行批量读取、写入或更新操作则返回True
        """
        if hasattr(self.main_window, 'global_event_manager'):
            return self.main_window.global_event_manager.is_in_batch_operation()
        return False
