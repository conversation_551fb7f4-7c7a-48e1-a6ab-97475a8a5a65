#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
安全清理脚本
只清理明确可以安全删除的文件，不会影响系统功能
"""

import os
import shutil
from datetime import datetime
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent
BACKUP_DIR = PROJECT_ROOT / f'safe_cleanup_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}'

def create_backup_dir():
    """创建备份目录"""
    BACKUP_DIR.mkdir(exist_ok=True)
    print(f"📁 创建备份目录: {BACKUP_DIR}")

def safe_remove_files(file_list, category_name):
    """安全删除文件列表"""
    print(f"\n🧹 清理 {category_name}...")
    
    removed_count = 0
    for file_path in file_list:
        full_path = PROJECT_ROOT / file_path
        
        if full_path.exists():
            try:
                # 创建备份
                backup_path = BACKUP_DIR / category_name / full_path.name
                backup_path.parent.mkdir(parents=True, exist_ok=True)
                
                if full_path.is_file():
                    shutil.copy2(full_path, backup_path)
                    full_path.unlink()
                else:
                    shutil.copytree(full_path, backup_path)
                    shutil.rmtree(full_path)
                
                print(f"  ✅ 删除: {file_path}")
                removed_count += 1
                
            except Exception as e:
                print(f"  ❌ 删除失败: {file_path} - {e}")
        else:
            print(f"  ⚠️  文件不存在: {file_path}")
    
    print(f"  📊 删除了 {removed_count} 个文件")
    return removed_count

def cleanup_backup_directories():
    """清理明确的备份目录"""
    backup_dirs = [
        'debug_backup_20250604_141758',
        'test_backup_20250604_141306', 
        'md_backup_20250604_142235'
    ]
    
    return safe_remove_files(backup_dirs, "备份目录")

def cleanup_pycache():
    """清理所有__pycache__目录"""
    pycache_dirs = []
    
    # 查找所有__pycache__目录
    for pycache_dir in PROJECT_ROOT.rglob('__pycache__'):
        pycache_dirs.append(pycache_dir.relative_to(PROJECT_ROOT))
    
    return safe_remove_files(pycache_dirs, "Python缓存")

def cleanup_backup_files():
    """清理.backup文件"""
    backup_files = []
    
    # 查找所有.backup文件
    for backup_file in PROJECT_ROOT.rglob('*.backup_*'):
        backup_files.append(backup_file.relative_to(PROJECT_ROOT))
    
    return safe_remove_files(backup_files, "备份文件")

def cleanup_temp_test_files():
    """清理根目录下的临时测试文件"""
    temp_test_files = [
        'test_auto_dock.py',
        'test_balanced_layout.py',
        'test_com_port_layout_fix.py',
        'test_complete_drag_dock.py',
        'test_complete_resource_cleanup.py',
        'test_config_effects.py',
        'test_coordinate_fix.py',
        'test_current_issues.py',
        'test_drag_dock.py',
        'test_drag_dock_functionality.py',
        'test_drag_dock_quick.py',
        'test_drag_dock_simple.py',
        'test_drag_release_final.py',
        'test_final_drag_dock.py',
        'test_fixed_drag_dock.py',
        'test_green_progress_bars.py',
        'test_menu_state_fix.py',
        'test_optimized_drag_dock.py',
        'test_output.log',
        'test_pll_scroll.py',
        'test_plugin_dock_fix.py',
        'test_plugin_tab_close_fix.py',
        'test_progress_bar_styles.py',
        'test_real_drag_dock.py',
        'test_runtime_error_fix.py',
        'test_scroll_functionality.py',
        'test_tab_cleanup.py',
        'test_ui_layout_fix.py',
        'test_undock_fix.py',
        'test_window_resize.py',
        'test_window_validity_fix.py'
    ]
    
    return safe_remove_files(temp_test_files, "临时测试文件")

def cleanup_debug_files():
    """清理调试文件"""
    debug_files = [
        'auto_enable_drag_dock.py',
        'debug_drag_dock.py',
        'debug_drag_release.py',
        'demo_layout_improvements.py',
        'diagnose_mouse_events.py',
        'disable_drag_dock.py',
        'enable_drag_dock.py',
        'fix_dock_blank_area.py',
        'fix_toolbar_tools.py',
        'quick_dock_test.py',
        'quick_drag_test.py',
        'simple_drag_test.py',
        'simple_scroll_test.py'
    ]
    
    return safe_remove_files(debug_files, "调试文件")

def cleanup_redundant_docs():
    """清理冗余的中文文档"""
    redundant_docs = [
        'DRAG_DOCK_FIX_SUMMARY.md',
        '专利申请_嵌入式设备寄存器配置的事件驱动管理系统.md',
        '分离窗口修复报告.md',
        '寄存器表格修改四动作实现总结.md',
        '寄存器表格修改重复调用完整解决方案.md',
        '寄存器表格修改重复调用风险修复总结.md',
        '性能监控器使用说明.md',
        '性能监控插件闪退修复总结.md',
        '拖拽停靠功能修复总结报告.md',
        '标签页资源清理修复报告.md',
        '窗口大小自适应功能报告.md',
        '解决方案总结.md'
    ]
    
    return safe_remove_files(redundant_docs, "冗余文档")

def cleanup_old_spec_files():
    """清理旧的spec文件"""
    old_spec_files = [
        'build.spec',  # 根目录下的旧spec文件
        'packaging/FSJ04832_RegisterTool.spec'  # packaging目录下的旧spec文件
    ]
    
    return safe_remove_files(old_spec_files, "旧Spec文件")

def cleanup_duplicate_event_bus():
    """清理重复的event_bus目录"""
    # 根目录下的event_bus是重复的，应该使用core/event_bus
    duplicate_dirs = ['event_bus']
    
    return safe_remove_files(duplicate_dirs, "重复目录")

def generate_cleanup_summary():
    """生成清理总结"""
    summary_path = PROJECT_ROOT / 'docs' / 'cleanup' / 'SAFE_CLEANUP_SUMMARY.md'
    summary_path.parent.mkdir(parents=True, exist_ok=True)
    
    summary_content = f"""# 安全清理总结

## 清理时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 清理内容

本次清理只删除了明确可以安全删除的文件，不会影响系统功能：

### 1. 备份目录
- `debug_backup_20250604_141758/` - 调试文件备份
- `test_backup_20250604_141306/` - 测试文件备份  
- `md_backup_20250604_142235/` - 文档备份

### 2. Python缓存
- 所有 `__pycache__/` 目录
- Python字节码缓存文件

### 3. 备份文件
- 所有 `.backup_*` 文件
- 开发过程中的临时备份

### 4. 临时测试文件
- 根目录下的 `test_*.py` 文件（非正式测试）
- 调试和验证用的临时脚本

### 5. 调试文件
- 拖拽功能调试脚本
- UI布局调试脚本
- 临时修复脚本

### 6. 冗余文档
- 重复的中文文档
- 临时修复报告
- 过时的说明文件

### 7. 旧配置文件
- 废弃的spec文件
- 重复的配置目录

## 保留的重要文件

以下文件和目录被保留，确保系统正常运行：

- ✅ 所有现代化处理器 (`ui/handlers/Modern*.py`)
- ✅ 传统处理器 (作为回退方案保留)
- ✅ 核心服务 (`core/`)
- ✅ UI管理器 (`ui/managers/`)
- ✅ 测试套件 (`test_suite/`)
- ✅ 正式测试 (`tests/`)
- ✅ 打包系统 (`packaging/`)
- ✅ 配置文件 (`config/`)
- ✅ 资源文件 (`images/`, `lib/`)
- ✅ 文档 (`docs/`)
- ✅ 插件系统 (`plugins/`)

## 备份位置

所有删除的文件都已备份到: `{BACKUP_DIR}`

如果需要恢复任何文件，可以从备份目录中找到。

## 清理效果

- 🧹 **项目更整洁**: 移除了开发过程中的临时文件
- 💾 **空间优化**: 清理了缓存和备份文件
- 📁 **结构清晰**: 保持了清晰的项目结构
- 🔒 **安全可靠**: 所有重要文件都被保留

---

**清理状态**: ✅ 完成  
**备份状态**: ✅ 已备份  
**系统状态**: ✅ 正常
"""
    
    with open(summary_path, 'w', encoding='utf-8') as f:
        f.write(summary_content)
    
    print(f"📄 清理总结已生成: {summary_path}")

def main():
    """主函数"""
    print("🚀 开始安全清理...")
    print("=" * 60)
    print("⚠️  本次清理只会删除明确可以安全删除的文件")
    print("   所有重要的系统文件都会被保留")
    print("   所有删除的文件都会先备份")
    print()
    
    response = input("是否继续？(y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("❌ 清理操作已取消")
        return
    
    # 创建备份目录
    create_backup_dir()
    
    # 执行清理
    cleanup_tasks = [
        ("备份目录", cleanup_backup_directories),
        ("Python缓存", cleanup_pycache),
        ("备份文件", cleanup_backup_files),
        ("临时测试文件", cleanup_temp_test_files),
        ("调试文件", cleanup_debug_files),
        ("冗余文档", cleanup_redundant_docs),
        ("旧Spec文件", cleanup_old_spec_files),
        ("重复目录", cleanup_duplicate_event_bus),
    ]
    
    total_removed = 0
    for task_name, task_func in cleanup_tasks:
        try:
            removed_count = task_func()
            total_removed += removed_count
        except Exception as e:
            print(f"❌ 清理 {task_name} 时出错: {str(e)}")
    
    # 生成总结
    generate_cleanup_summary()
    
    # 最终总结
    print("\n" + "=" * 60)
    print("🎉 安全清理完成!")
    print(f"📊 总计删除文件: {total_removed} 个")
    print(f"💾 备份位置: {BACKUP_DIR}")
    print("📄 详细总结: docs/cleanup/SAFE_CLEANUP_SUMMARY.md")
    print("=" * 60)

if __name__ == "__main__":
    main()
