#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
配置管理器别名文件
为了向后兼容，提供ConfigManager别名
"""

# 导入实际的配置管理器
from .ConfigurationManager import (
    ConfigurationManager,
    config_manager,
    get_config,
    set_config,
    watch_config
)

# 为了向后兼容，提供ConfigManager别名
ConfigManager = ConfigurationManager

# 导出所有需要的符号
__all__ = [
    'ConfigManager',
    'ConfigurationManager', 
    'config_manager',
    'get_config',
    'set_config',
    'watch_config'
]
