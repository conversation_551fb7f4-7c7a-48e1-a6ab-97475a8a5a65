#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试PLL2反向计算功能
验证当PLL2NclkMux为Feedback Mux时的反向计算逻辑
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from ui.handlers.ModernPLLHandler import ModernPLLHandler
from ui.handlers.ModernClkOutputsHandler import ModernClkOutputsHandler
from core.RegisterManager import RegisterManager
from utils.Log import get_module_logger

logger = get_module_logger(__name__)

def test_pll2_reverse_calculation():
    """测试PLL2反向计算功能"""
    print("="*60)
    print("测试PLL2反向计算功能")
    print("="*60)
    
    app = QApplication(sys.argv)
    
    try:
        # 创建RegisterManager实例
        register_manager = RegisterManager()
        
        # 创建处理器实例
        pll_handler = ModernPLLHandler(register_manager=register_manager)
        clk_outputs_handler = ModernClkOutputsHandler(register_manager=register_manager)
        
        print("\n--- 测试1: 正常模式vs反馈模式对比 ---")
        test_normal_vs_feedback_mode(pll_handler, clk_outputs_handler)
        
        print("\n--- 测试2: 反向计算PLL2NDivider ---")
        test_reverse_n_divider_calculation(pll_handler, clk_outputs_handler)
        
        print("\n--- 测试3: 不同CLKout6频率下的反向计算 ---")
        test_different_clkout6_frequencies(pll_handler, clk_outputs_handler)
        
        print("\n--- 测试4: 验证计算精度 ---")
        test_calculation_accuracy(pll_handler, clk_outputs_handler)
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        app.quit()

def test_normal_vs_feedback_mode(pll_handler, clk_outputs_handler):
    """测试正常模式vs反馈模式的对比"""
    try:
        print("测试正常模式vs反馈模式...")
        
        # 设置基础参数
        test_input_freq = 122.88
        test_vco_dist_freq = 2949.12
        test_dclk6_7div = 8
        
        # 设置控件值
        if hasattr(pll_handler.ui, "OSCinFreq"):
            pll_handler.ui.OSCinFreq.setText(str(test_input_freq))
        if hasattr(pll_handler.ui, "VCODistFreq"):
            pll_handler.ui.VCODistFreq.setText(str(test_vco_dist_freq))
        if hasattr(clk_outputs_handler.ui, 'DCLK6_7DIV'):
            clk_outputs_handler.ui.DCLK6_7DIV.setValue(test_dclk6_7div)
        
        # 设置PLL2参数
        if hasattr(pll_handler.ui, "PLL2RDivider"):
            pll_handler.ui.PLL2RDivider.setValue(2)
        if hasattr(pll_handler.ui, "Doubler"):
            pll_handler.ui.Doubler.setCurrentIndex(0)  # 1x
        
        print(f"  基础参数: 输入频率={test_input_freq} MHz, VCODistFreq={test_vco_dist_freq} MHz")
        print(f"  PLL2参数: R分频器=2, 倍频器=1x, DCLK6_7DIV={test_dclk6_7div}")
        
        # 测试正常模式
        print("\n  正常模式 (PLL2NclkMux = 0):")
        if hasattr(pll_handler.ui, "PLL2NclkMux"):
            pll_handler.ui.PLL2NclkMux.setCurrentIndex(0)  # PLL2 Prescaler
        
        normal_result = pll_handler._calculate_pll2_with_normal_mode(test_input_freq)
        print(f"    PLL2 PFD频率: {normal_result:.3f} MHz")
        
        # 测试反馈模式
        print("\n  反馈模式 (PLL2NclkMux = 1):")
        if hasattr(pll_handler.ui, "PLL2NclkMux"):
            pll_handler.ui.PLL2NclkMux.setCurrentIndex(1)  # Feedback Mux
        if hasattr(pll_handler.ui, "FBMUX"):
            pll_handler.ui.FBMUX.setCurrentIndex(0)  # CLKout6
        
        feedback_result = pll_handler._calculate_pll2_with_feedback_mode(test_input_freq)
        print(f"    PLL2 PFD频率: {feedback_result:.3f} MHz")
        
        # 计算CLKout6频率
        clkout6_freq = test_vco_dist_freq / test_dclk6_7div
        print(f"    CLKout6频率: {clkout6_freq:.3f} MHz")
        
        print("✓ 正常模式vs反馈模式对比测试完成")
        
    except Exception as e:
        print(f"✗ 正常模式vs反馈模式对比测试失败: {str(e)}")

def test_reverse_n_divider_calculation(pll_handler, clk_outputs_handler):
    """测试反向计算PLL2NDivider"""
    try:
        print("测试反向计算PLL2NDivider...")
        
        # 设置测试场景
        test_scenarios = [
            {
                "name": "场景1: 高频输出",
                "vco_dist_freq": 3000.0,
                "dclk6_7div": 4,
                "input_freq": 122.88,
                "r_divider": 2,
                "doubler": 1
            },
            {
                "name": "场景2: 中频输出", 
                "vco_dist_freq": 2400.0,
                "dclk6_7div": 8,
                "input_freq": 100.0,
                "r_divider": 1,
                "doubler": 2
            },
            {
                "name": "场景3: 低频输出",
                "vco_dist_freq": 1200.0,
                "dclk6_7div": 16,
                "input_freq": 75.0,
                "r_divider": 3,
                "doubler": 1
            }
        ]
        
        for scenario in test_scenarios:
            print(f"\n  {scenario['name']}:")
            
            # 设置参数
            if hasattr(pll_handler.ui, "VCODistFreq"):
                pll_handler.ui.VCODistFreq.setText(str(scenario['vco_dist_freq']))
            if hasattr(clk_outputs_handler.ui, 'DCLK6_7DIV'):
                clk_outputs_handler.ui.DCLK6_7DIV.setValue(scenario['dclk6_7div'])
            if hasattr(pll_handler.ui, "PLL2RDivider"):
                pll_handler.ui.PLL2RDivider.setValue(scenario['r_divider'])
            if hasattr(pll_handler.ui, "Doubler"):
                pll_handler.ui.Doubler.setCurrentIndex(scenario['doubler'] - 1)
            
            # 设置为反馈模式
            if hasattr(pll_handler.ui, "PLL2NclkMux"):
                pll_handler.ui.PLL2NclkMux.setCurrentIndex(1)  # Feedback Mux
            if hasattr(pll_handler.ui, "FBMUX"):
                pll_handler.ui.FBMUX.setCurrentIndex(0)  # CLKout6
            
            # 计算CLKout6频率
            clkout6_freq = scenario['vco_dist_freq'] / scenario['dclk6_7div']
            
            # 计算参考频率
            ref_freq = scenario['input_freq'] * scenario['doubler'] / scenario['r_divider']
            
            # 理论PLL2NDivider值
            theoretical_n_divider = ref_freq / clkout6_freq
            
            print(f"    VCODistFreq: {scenario['vco_dist_freq']} MHz")
            print(f"    DCLK6_7DIV: {scenario['dclk6_7div']}")
            print(f"    CLKout6频率: {clkout6_freq:.3f} MHz")
            print(f"    参考频率: {ref_freq:.3f} MHz")
            print(f"    理论PLL2NDivider: {theoretical_n_divider:.6f}")
            print(f"    建议PLL2NDivider: {round(theoretical_n_divider)}")
            
            # 执行反向计算
            result = pll_handler._calculate_pll2_with_feedback_mode(scenario['input_freq'])
            print(f"    计算结果PFD频率: {result:.3f} MHz")
        
        print("✓ 反向计算PLL2NDivider测试完成")
        
    except Exception as e:
        print(f"✗ 反向计算PLL2NDivider测试失败: {str(e)}")

def test_different_clkout6_frequencies(pll_handler, clk_outputs_handler):
    """测试不同CLKout6频率下的反向计算"""
    try:
        print("测试不同CLKout6频率下的反向计算...")
        
        # 固定参数
        fixed_vco_dist_freq = 2949.12
        fixed_input_freq = 122.88
        fixed_r_divider = 2
        
        # 不同的DCLK6_7DIV值
        test_dividers = [1, 2, 4, 8, 16, 32]
        
        print(f"  固定参数: VCODistFreq={fixed_vco_dist_freq} MHz, 输入频率={fixed_input_freq} MHz, R分频器={fixed_r_divider}")
        
        # 设置固定参数
        if hasattr(pll_handler.ui, "VCODistFreq"):
            pll_handler.ui.VCODistFreq.setText(str(fixed_vco_dist_freq))
        if hasattr(pll_handler.ui, "PLL2RDivider"):
            pll_handler.ui.PLL2RDivider.setValue(fixed_r_divider)
        if hasattr(pll_handler.ui, "Doubler"):
            pll_handler.ui.Doubler.setCurrentIndex(0)  # 1x
        
        # 设置为反馈模式
        if hasattr(pll_handler.ui, "PLL2NclkMux"):
            pll_handler.ui.PLL2NclkMux.setCurrentIndex(1)
        if hasattr(pll_handler.ui, "FBMUX"):
            pll_handler.ui.FBMUX.setCurrentIndex(0)
        
        print(f"\n  {'DCLK6_7DIV':<12} {'CLKout6频率':<15} {'理论NDivider':<15} {'建议NDivider':<15}")
        print(f"  {'-'*12} {'-'*15} {'-'*15} {'-'*15}")
        
        for divider in test_dividers:
            # 设置分频器
            if hasattr(clk_outputs_handler.ui, 'DCLK6_7DIV'):
                clk_outputs_handler.ui.DCLK6_7DIV.setValue(divider)
            
            # 计算CLKout6频率
            clkout6_freq = fixed_vco_dist_freq / divider
            
            # 计算参考频率
            ref_freq = fixed_input_freq / fixed_r_divider
            
            # 理论PLL2NDivider
            theoretical_n_divider = ref_freq / clkout6_freq
            suggested_n_divider = round(theoretical_n_divider)
            
            print(f"  {divider:<12} {clkout6_freq:<15.3f} {theoretical_n_divider:<15.6f} {suggested_n_divider:<15}")
        
        print("✓ 不同CLKout6频率反向计算测试完成")
        
    except Exception as e:
        print(f"✗ 不同CLKout6频率反向计算测试失败: {str(e)}")

def test_calculation_accuracy(pll_handler, clk_outputs_handler):
    """验证计算精度"""
    try:
        print("验证计算精度...")
        
        # 设置精确的测试参数
        test_vco_dist_freq = 2949.12
        test_dclk6_7div = 8
        test_input_freq = 122.88
        test_r_divider = 2
        
        # 设置参数
        if hasattr(pll_handler.ui, "VCODistFreq"):
            pll_handler.ui.VCODistFreq.setText(str(test_vco_dist_freq))
        if hasattr(clk_outputs_handler.ui, 'DCLK6_7DIV'):
            clk_outputs_handler.ui.DCLK6_7DIV.setValue(test_dclk6_7div)
        if hasattr(pll_handler.ui, "PLL2RDivider"):
            pll_handler.ui.PLL2RDivider.setValue(test_r_divider)
        if hasattr(pll_handler.ui, "Doubler"):
            pll_handler.ui.Doubler.setCurrentIndex(0)
        
        # 手动计算期望值
        clkout6_freq = test_vco_dist_freq / test_dclk6_7div
        ref_freq = test_input_freq / test_r_divider
        expected_n_divider = ref_freq / clkout6_freq
        
        print(f"  手动计算:")
        print(f"    CLKout6频率: {test_vco_dist_freq} / {test_dclk6_7div} = {clkout6_freq:.6f} MHz")
        print(f"    参考频率: {test_input_freq} / {test_r_divider} = {ref_freq:.6f} MHz")
        print(f"    期望PLL2NDivider: {ref_freq:.6f} / {clkout6_freq:.6f} = {expected_n_divider:.6f}")
        
        # 程序计算
        if hasattr(pll_handler.ui, "PLL2NclkMux"):
            pll_handler.ui.PLL2NclkMux.setCurrentIndex(1)
        if hasattr(pll_handler.ui, "FBMUX"):
            pll_handler.ui.FBMUX.setCurrentIndex(0)
        
        result = pll_handler._calculate_pll2_with_feedback_mode(test_input_freq)
        
        print(f"  程序计算:")
        print(f"    PLL2 PFD频率: {result:.6f} MHz")
        print(f"    期望PFD频率: {ref_freq:.6f} MHz")
        
        # 验证精度
        error = abs(result - ref_freq)
        relative_error = error / ref_freq * 100 if ref_freq > 0 else 0
        
        print(f"  精度验证:")
        print(f"    绝对误差: {error:.9f} MHz")
        print(f"    相对误差: {relative_error:.6f}%")
        
        if relative_error < 0.001:  # 0.001%精度
            print("  ✓ 计算精度满足要求")
        else:
            print("  ✗ 计算精度不满足要求")
        
        print("✓ 计算精度验证完成")
        
    except Exception as e:
        print(f"✗ 计算精度验证失败: {str(e)}")

def print_reverse_calculation_summary():
    """打印反向计算总结"""
    print("\n" + "="*60)
    print("PLL2反向计算系统总结")
    print("="*60)
    print("""
反向计算原理：
当PLL2NclkMux = Feedback Mux时，PLL2的反馈路径来自CLKout6
此时需要反向计算PLL2NDivider的合理值

计算公式：
正常模式: PLL2PFDFreq = 输入频率 × 倍频器 / R分频器
反馈模式: CLKout6频率 × PLL2NDivider = PLL2PFDFreq
因此: PLL2NDivider = PLL2PFDFreq / CLKout6频率

其中：
- CLKout6频率 = VCODistFreq / DCLK6_7DIV
- PLL2PFDFreq = 输入频率 × 倍频器 / R分频器

计算流程：
1. 检测PLL2NclkMux是否为Feedback Mux模式
2. 获取CLKout6频率（从时钟输出窗口）
3. 计算参考频率（输入频率处理）
4. 反向计算PLL2NDivider = 参考频率 / CLKout6频率
5. 建议用户设置合适的PLL2NDivider值

应用场景：
- 零延迟时钟生成
- 精确的时钟同步
- 复杂的时钟树配置
- 反馈环路优化

优势：
- 自动计算合理的分频比
- 确保PLL锁定的稳定性
- 提供精确的频率控制
- 支持复杂的时钟架构
""")
    print("="*60)

if __name__ == "__main__":
    print_reverse_calculation_summary()
    test_pll2_reverse_calculation()
