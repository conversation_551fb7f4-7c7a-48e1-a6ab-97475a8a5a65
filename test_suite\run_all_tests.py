#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
主测试运行脚本
运行所有测试并生成报告
"""

import os
import sys
import argparse
import importlib
import traceback
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from test_config import setup_test_environment, TEST_CONFIG
from test_utils import TestLogger, TestResult, generate_test_report, validate_test_environment

class TestRunner:
    """测试运行器"""
    
    def __init__(self):
        self.logger = TestLogger("test_runner")
        self.test_results = []
        self.categories = TEST_CONFIG['categories']
    
    def discover_tests(self, category=None):
        """发现测试文件"""
        test_files = []
        test_suite_dir = os.path.dirname(os.path.abspath(__file__))
        
        if category:
            # 运行特定分类的测试
            category_dir = os.path.join(test_suite_dir, category)
            if os.path.exists(category_dir):
                test_files.extend(self._find_test_files(category_dir, category))
        else:
            # 运行所有分类的测试
            for cat_name in self.categories.keys():
                category_dir = os.path.join(test_suite_dir, cat_name)
                if os.path.exists(category_dir):
                    test_files.extend(self._find_test_files(category_dir, cat_name))
            
            # 同时包含根目录下的测试文件
            root_test_dir = os.path.dirname(test_suite_dir)
            test_files.extend(self._find_test_files(root_test_dir, 'legacy'))
        
        return test_files
    
    def _find_test_files(self, directory, category):
        """在指定目录中查找测试文件"""
        test_files = []
        for file_name in os.listdir(directory):
            if file_name.startswith('test_') and file_name.endswith('.py'):
                file_path = os.path.join(directory, file_name)
                test_files.append({
                    'path': file_path,
                    'name': file_name[:-3],  # 去掉.py扩展名
                    'category': category
                })
        return test_files
    
    def run_test_file(self, test_file_info):
        """运行单个测试文件"""
        test_name = test_file_info['name']
        test_path = test_file_info['path']
        category = test_file_info['category']
        
        self.logger.info(f"运行测试: {test_name} (分类: {category})")
        
        result = TestResult(test_name)
        result.add_detail('category', category)
        result.add_detail('file_path', test_path)
        
        try:
            # 动态导入并运行测试
            if category == 'legacy':
                # 对于根目录的测试文件，直接执行
                success = self._execute_legacy_test(test_path)
            else:
                # 对于分类测试，尝试导入模块
                success = self._execute_module_test(test_path, category)
            
            result.set_success(success)
            
        except Exception as e:
            error_msg = f"测试执行失败: {str(e)}"
            self.logger.error(error_msg)
            self.logger.error(traceback.format_exc())
            result.set_error(error_msg)
        
        self.test_results.append(result)
        return result
    
    def _execute_legacy_test(self, test_path):
        """执行传统测试文件"""
        try:
            # 使用subprocess执行测试文件
            import subprocess
            result = subprocess.run([sys.executable, test_path], 
                                  capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                self.logger.info(f"传统测试执行成功: {os.path.basename(test_path)}")
                return True
            else:
                self.logger.error(f"传统测试执行失败: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            self.logger.error(f"传统测试超时: {os.path.basename(test_path)}")
            return False
        except Exception as e:
            self.logger.error(f"传统测试执行出错: {str(e)}")
            return False
    
    def _execute_module_test(self, test_path, category):
        """执行模块化测试"""
        try:
            # 构建模块路径
            rel_path = os.path.relpath(test_path, os.path.dirname(os.path.dirname(test_path)))
            module_path = rel_path.replace(os.sep, '.').replace('.py', '')
            
            # 导入模块
            module = importlib.import_module(module_path)
            
            # 查找并执行测试函数
            if hasattr(module, 'run_tests'):
                return module.run_tests()
            elif hasattr(module, 'main'):
                return module.main()
            else:
                # 尝试执行模块
                return True
                
        except Exception as e:
            self.logger.error(f"模块测试执行出错: {str(e)}")
            return False
    
    def run_tests(self, category=None, pattern=None):
        """运行测试"""
        self.logger.info("开始运行测试套件")
        self.logger.info(f"测试分类: {category or '全部'}")
        
        # 验证测试环境
        env_errors = validate_test_environment()
        if env_errors:
            self.logger.error("测试环境验证失败:")
            for error in env_errors:
                self.logger.error(f"  - {error}")
            return False
        
        # 发现测试文件
        test_files = self.discover_tests(category)
        
        if pattern:
            test_files = [f for f in test_files if pattern in f['name']]
        
        self.logger.info(f"发现 {len(test_files)} 个测试文件")
        
        if not test_files:
            self.logger.warning("没有找到测试文件")
            return True
        
        # 运行测试
        for test_file in test_files:
            result = self.run_test_file(test_file)
            
            if result.success:
                self.logger.info(f"✓ {result.test_name} 通过 ({result.get_duration():.2f}s)")
            else:
                self.logger.error(f"✗ {result.test_name} 失败: {result.error_message}")
        
        # 生成统计信息
        self._print_summary()
        
        return True
    
    def _print_summary(self):
        """打印测试摘要"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r.success)
        failed_tests = total_tests - passed_tests
        total_duration = sum(r.get_duration() for r in self.test_results)
        
        self.logger.info("\n" + "=" * 60)
        self.logger.info("测试摘要")
        self.logger.info("=" * 60)
        self.logger.info(f"总测试数: {total_tests}")
        self.logger.info(f"通过测试: {passed_tests}")
        self.logger.info(f"失败测试: {failed_tests}")
        self.logger.info(f"成功率: {passed_tests/total_tests*100:.1f}%" if total_tests > 0 else "成功率: 0%")
        self.logger.info(f"总耗时: {total_duration:.2f}秒")
        
        if failed_tests > 0:
            self.logger.info("\n失败的测试:")
            for result in self.test_results:
                if not result.success:
                    self.logger.info(f"  - {result.test_name}: {result.error_message}")
    
    def generate_report(self, output_file=None):
        """生成测试报告"""
        report_file = generate_test_report(self.test_results, output_file)
        self.logger.info(f"测试报告已生成: {report_file}")
        return report_file

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='运行寄存器配置工具测试套件')
    parser.add_argument('--category', '-c', 
                       choices=['unit', 'integration', 'functional', 'ui', 'performance', 'regression'],
                       help='运行特定分类的测试')
    parser.add_argument('--pattern', '-p', 
                       help='测试文件名模式过滤')
    parser.add_argument('--report', '-r', 
                       action='store_true',
                       help='生成测试报告')
    parser.add_argument('--output', '-o',
                       help='报告输出文件路径')
    parser.add_argument('--verbose', '-v',
                       action='store_true',
                       help='详细输出')
    
    args = parser.parse_args()
    
    # 设置测试环境
    if not setup_test_environment():
        print("测试环境设置失败")
        return 1
    
    # 创建测试运行器
    runner = TestRunner()
    
    # 运行测试
    success = runner.run_tests(args.category, args.pattern)
    
    if not success:
        return 1
    
    # 生成报告
    if args.report:
        runner.generate_report(args.output)
    
    # 检查是否有失败的测试
    failed_count = sum(1 for r in runner.test_results if not r.success)
    return 0 if failed_count == 0 else 1

if __name__ == "__main__":
    sys.exit(main())
