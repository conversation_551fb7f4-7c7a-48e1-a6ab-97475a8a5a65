#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试dis模块是否可用
"""

def test_dis_module():
    """测试dis模块"""
    try:
        import dis
        print("✅ dis模块导入成功")
        
        # 测试dis模块的基本功能
        def sample_function():
            return "Hello, World!"
        
        print("📋 反汇编示例函数:")
        dis.dis(sample_function)
        
        return True
        
    except ImportError as e:
        print(f"❌ dis模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ dis模块测试失败: {e}")
        return False

def test_other_modules():
    """测试其他可能有问题的模块"""
    modules_to_test = [
        'os', 'sys', 'io', 'codecs', 'encodings',
        'inspect', 'types', 'typing'
    ]
    
    failed_modules = []
    
    for module_name in modules_to_test:
        try:
            __import__(module_name)
            print(f"✅ {module_name} 模块正常")
        except ImportError as e:
            print(f"❌ {module_name} 模块失败: {e}")
            failed_modules.append(module_name)
    
    return len(failed_modules) == 0

if __name__ == "__main__":
    print("🧪 测试PyInstaller打包后的模块可用性...")
    print("=" * 50)
    
    dis_ok = test_dis_module()
    print()
    
    other_ok = test_other_modules()
    print()
    
    if dis_ok and other_ok:
        print("🎉 所有模块测试通过！")
    else:
        print("⚠️ 部分模块测试失败，可能需要进一步调整spec文件")
