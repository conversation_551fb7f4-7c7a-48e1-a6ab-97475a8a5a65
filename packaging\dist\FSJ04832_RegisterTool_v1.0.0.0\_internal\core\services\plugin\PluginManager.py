#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
插件管理系统
支持动态加载和管理插件
"""

import importlib
import inspect
import os
import sys
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Dict, List, Type, Any, Optional
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class IPlugin(ABC):
    """插件接口"""
    
    @property
    @abstractmethod
    def name(self) -> str:
        """插件名称"""
        pass
    
    @property
    @abstractmethod
    def version(self) -> str:
        """插件版本"""
        pass
    
    @property
    @abstractmethod
    def description(self) -> str:
        """插件描述"""
        pass
    
    @abstractmethod
    def initialize(self, context: Any):
        """初始化插件
        
        Args:
            context: 应用程序上下文
        """
        pass
    
    @abstractmethod
    def cleanup(self):
        """清理插件资源"""
        pass


class IToolWindowPlugin(IPlugin):
    """工具窗口插件接口"""
    
    @abstractmethod
    def create_window(self, parent=None) -> Any:
        """创建工具窗口
        
        Args:
            parent: 父窗口
            
        Returns:
            工具窗口实例
        """
        pass
    
    @property
    @abstractmethod
    def menu_text(self) -> str:
        """菜单文本"""
        pass
    
    @property
    @abstractmethod
    def icon_path(self) -> Optional[str]:
        """图标路径"""
        pass


class PluginInfo:
    """插件信息"""
    
    def __init__(self, name: str, version: str, description: str, 
                 plugin_class: Type[IPlugin], file_path: str):
        self.name = name
        self.version = version
        self.description = description
        self.plugin_class = plugin_class
        self.file_path = file_path
        self.instance: Optional[IPlugin] = None
        self.enabled = True


class PluginManager:
    """插件管理器"""
    
    _instance = None
    
    def __init__(self):
        """初始化插件管理器"""
        self._plugins: Dict[str, PluginInfo] = {}
        self._plugin_directories: List[str] = []
        self._context: Any = None
        
    @classmethod
    def instance(cls):
        """获取单例实例"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def add_plugin_directory(self, directory: str):
        """添加插件目录
        
        Args:
            directory: 插件目录路径
        """
        if os.path.exists(directory):
            # 添加插件目录的原有逻辑
            plugin_dir = Path(directory)
            if plugin_dir.exists() and plugin_dir.is_dir():
                self._plugin_directories.append(str(plugin_dir))
                logger.info(f"添加插件目录: {directory}")
            else:
                logger.warning(f"插件目录不存在: {directory}")
        else:
            logger.debug(f"跳过不存在的插件目录: {directory}")
    
    def scan_plugins(self):
        """扫描并加载所有插件"""
        for directory in self._plugin_directories:
            self._scan_directory(directory)
    
    def _scan_directory(self, directory: str):
        """扫描指定目录中的插件"""
        plugin_dir = Path(directory)
        
        for py_file in plugin_dir.glob("**/*.py"):
            if py_file.name.startswith("__"):
                continue
                
            try:
                self._load_plugin_from_file(py_file)
            except Exception as e:
                logger.error(f"加载插件文件失败 {py_file}: {str(e)}")
    
    def _load_plugin_from_file(self, file_path: Path):
        """从文件加载插件"""
        try:
            # 构建模块名 - 简化路径处理逻辑
            if file_path.is_absolute():
                # 获取相对于项目根目录的路径
                try:
                    # 尝试相对于当前工作目录
                    cwd = Path.cwd()
                    relative_path = file_path.relative_to(cwd)
                except ValueError:
                    # 如果无法相对化，尝试从plugins目录开始
                    if 'plugins' in str(file_path):
                        # 提取从plugins开始的路径
                        path_parts = file_path.parts
                        plugins_index = None
                        for i, part in enumerate(path_parts):
                            if part == 'plugins':
                                plugins_index = i
                                break

                        if plugins_index is not None:
                            relative_parts = path_parts[plugins_index:]
                            relative_path = Path(*relative_parts)
                        else:
                            logger.warning(f"无法处理插件文件路径: {file_path}")
                            return
                    else:
                        logger.warning(f"无法处理插件文件路径: {file_path}")
                        return
            else:
                relative_path = file_path

            # 构建模块名
            module_name = str(relative_path.with_suffix("")).replace(os.sep, ".")
            logger.debug(f"尝试加载插件模块: {module_name} (文件: {file_path})")

        except Exception as e:
            logger.error(f"路径处理失败 {file_path}: {str(e)}")
            return

        try:
            # 动态导入模块
            # 对于打包后的程序，需要特殊处理_internal路径
            module = None

            # 尝试多种导入方式
            import_attempts = [module_name]

            # 如果模块名以_internal开头，尝试直接导入
            if module_name.startswith('_internal.'):
                # 尝试去掉_internal前缀
                alt_module_name = module_name[10:]  # 去掉'_internal.'
                import_attempts.append(alt_module_name)

                # 尝试添加到sys.path并导入
                internal_path = os.path.join(os.getcwd(), '_internal')
                if internal_path not in sys.path:
                    sys.path.insert(0, internal_path)
                import_attempts.append(alt_module_name)

            # 尝试各种导入方式
            for attempt_name in import_attempts:
                try:
                    module = importlib.import_module(attempt_name)
                    logger.debug(f"成功导入模块: {attempt_name}")
                    break
                except ImportError as ie:
                    logger.debug(f"导入失败 {attempt_name}: {ie}")
                    continue

            if module is None:
                raise ImportError(f"无法导入模块 {module_name}")

            # 查找插件类
            plugin_class = None

            # 首先尝试查找 plugin_class 变量
            if hasattr(module, 'plugin_class'):
                plugin_class = getattr(module, 'plugin_class')
                if inspect.isclass(plugin_class) and issubclass(plugin_class, IPlugin):
                    # 创建插件实例来获取属性
                    try:
                        temp_instance = plugin_class()
                        plugin_info = PluginInfo(
                            name=temp_instance.name,
                            version=temp_instance.version,
                            description=temp_instance.description,
                            plugin_class=plugin_class,
                            file_path=str(file_path)
                        )

                        self._plugins[plugin_info.name] = plugin_info
                        logger.info(f"发现插件: {plugin_info.name} v{plugin_info.version}")
                        return  # 找到插件类后直接返回
                    except Exception as e:
                        logger.warning(f"无法创建插件实例 {plugin_class.__name__}: {str(e)}")

            # 如果没有找到 plugin_class 变量，则扫描所有类
            for name, obj in inspect.getmembers(module, inspect.isclass):
                if (obj != IPlugin and obj != IToolWindowPlugin and
                    issubclass(obj, IPlugin) and not inspect.isabstract(obj)):

                    try:
                        # 创建临时实例来获取属性
                        temp_instance = obj()
                        plugin_info = PluginInfo(
                            name=temp_instance.name,
                            version=temp_instance.version,
                            description=temp_instance.description,
                            plugin_class=obj,
                            file_path=str(file_path)
                        )

                        self._plugins[plugin_info.name] = plugin_info
                        logger.info(f"发现插件: {plugin_info.name} v{plugin_info.version}")
                        break  # 只取第一个有效的插件类
                    except Exception as e:
                        logger.warning(f"无法创建插件实例 {obj.__name__}: {str(e)}")
                    
        except Exception as e:
            logger.error(f"导入插件模块失败 {module_name}: {str(e)}")
    
    def initialize_plugins(self, context: Any):
        """初始化所有插件
        
        Args:
            context: 应用程序上下文
        """
        self._context = context
        
        for plugin_info in self._plugins.values():
            if plugin_info.enabled:
                try:
                    plugin_info.instance = plugin_info.plugin_class()
                    plugin_info.instance.initialize(context)
                    logger.info(f"插件初始化成功: {plugin_info.name}")
                except Exception as e:
                    logger.error(f"插件初始化失败 {plugin_info.name}: {str(e)}")
                    plugin_info.enabled = False
    
    def get_plugin(self, name: str) -> Optional[IPlugin]:
        """获取插件实例
        
        Args:
            name: 插件名称
            
        Returns:
            插件实例
        """
        plugin_info = self._plugins.get(name)
        if plugin_info and plugin_info.enabled:
            return plugin_info.instance
        return None
    
    def get_tool_window_plugins(self) -> List[IToolWindowPlugin]:
        """获取所有工具窗口插件"""
        tool_plugins = []

        for plugin_info in self._plugins.values():
            if (plugin_info.enabled and plugin_info.instance):
                # 使用更灵活的类型检查，支持不同导入路径的相同类
                instance = plugin_info.instance

                # 检查是否具有工具窗口插件的必要方法和属性
                has_create_window = hasattr(instance, 'create_window') and callable(getattr(instance, 'create_window'))
                has_menu_text = hasattr(instance, 'menu_text')
                has_icon_path = hasattr(instance, 'icon_path')

                # 也检查传统的isinstance，以防正常情况
                is_tool_window_plugin = (isinstance(instance, IToolWindowPlugin) or
                                        (has_create_window and has_menu_text and has_icon_path))

                if is_tool_window_plugin:
                    tool_plugins.append(instance)
                    logger.debug(f"添加工具窗口插件: {plugin_info.name}")
                else:
                    logger.debug(f"跳过非工具窗口插件: {plugin_info.name}")

        logger.info(f"找到 {len(tool_plugins)} 个工具窗口插件")
        return tool_plugins
    
    def enable_plugin(self, name: str):
        """启用插件
        
        Args:
            name: 插件名称
        """
        plugin_info = self._plugins.get(name)
        if plugin_info:
            plugin_info.enabled = True
            if not plugin_info.instance and self._context:
                try:
                    plugin_info.instance = plugin_info.plugin_class()
                    plugin_info.instance.initialize(self._context)
                    logger.info(f"插件已启用: {name}")
                except Exception as e:
                    logger.error(f"启用插件失败 {name}: {str(e)}")
                    plugin_info.enabled = False
    
    def disable_plugin(self, name: str):
        """禁用插件
        
        Args:
            name: 插件名称
        """
        plugin_info = self._plugins.get(name)
        if plugin_info:
            plugin_info.enabled = False
            if plugin_info.instance:
                try:
                    plugin_info.instance.cleanup()
                    plugin_info.instance = None
                    logger.info(f"插件已禁用: {name}")
                except Exception as e:
                    logger.error(f"禁用插件失败 {name}: {str(e)}")
    
    def get_plugin_list(self) -> List[PluginInfo]:
        """获取所有插件信息"""
        return list(self._plugins.values())
    
    def cleanup_all_plugins(self):
        """清理所有插件"""
        for plugin_info in self._plugins.values():
            if plugin_info.instance:
                try:
                    plugin_info.instance.cleanup()
                except Exception as e:
                    logger.error(f"清理插件失败 {plugin_info.name}: {str(e)}")
        
        self._plugins.clear()
        logger.info("所有插件已清理")


# 全局插件管理器实例
plugin_manager = PluginManager.instance()
