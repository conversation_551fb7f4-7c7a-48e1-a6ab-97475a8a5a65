#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单测试ComboBox初始化修复
"""

import sys
import os
import json

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_fix():
    """测试修复是否有效"""
    try:
        print("测试ComboBox初始化修复...")
        
        # 导入必要的模块
        from PyQt5.QtWidgets import QApplication
        from core.services.register.RegisterManager import RegisterManager
        from ui.handlers.ModernClkinControlHandler import ModernClkinControlHandler
        
        # 创建QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 加载寄存器配置
        config_path = os.path.join('lib', 'register.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        # 创建RegisterManager
        register_manager = RegisterManager(registers_config)
        
        # 创建处理器（这里应该不会有警告了）
        print("创建现代化时钟输入控制处理器...")
        modern_handler = ModernClkinControlHandler(None, register_manager)

        # 等待定时器执行
        print("等待定时器执行...")
        import time
        time.sleep(0.2)  # 等待200ms让定时器执行
        app.processEvents()  # 处理事件
        
        # 检查几个关键控件
        test_controls = ["CLKinSelManual", "CLKin0Demux", "OSCoutMux"]
        success = True
        
        for control_name in test_controls:
            if hasattr(modern_handler.ui, control_name):
                control = getattr(modern_handler.ui, control_name)
                if hasattr(control, 'count'):
                    count = control.count()
                    print(f"{control_name}: {count} 项")
                    if count == 0:
                        success = False
                        print(f"  ❌ {control_name} 没有选项!")
                    else:
                        print(f"  ✓ {control_name} 有选项")
                else:
                    print(f"  ⚠ {control_name} 不是ComboBox")
            else:
                print(f"  ❌ {control_name} 不存在")
                success = False
        
        # 检查是否有待设置的值
        if hasattr(modern_handler, '_pending_combobox_values'):
            pending = modern_handler._pending_combobox_values
            if pending:
                print(f"仍有 {len(pending)} 个待设置的值: {list(pending.keys())}")
                success = False
            else:
                print("✓ 没有待设置的ComboBox值")
        else:
            print("✓ 没有待设置的ComboBox值（属性不存在）")
        
        if success:
            print("✓ 测试通过 - ComboBox初始化修复成功!")
        else:
            print("❌ 测试失败 - 仍有问题")
        
        return success
        
    except Exception as e:
        print(f"测试出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_fix()
    print(f"测试结果: {'成功' if success else '失败'}")
