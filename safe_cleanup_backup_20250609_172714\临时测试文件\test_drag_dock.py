#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
拖拽停靠功能测试脚本
用于测试工具窗口拖拽到底部的自动停靠功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QLabel, QPushButton, QTextEdit
from PyQt5.QtCore import Qt, QPoint
from PyQt5.QtGui import QMouseEvent
import logging

# 设置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TestDragWindow(QWidget):
    """测试拖拽窗口"""
    
    def __init__(self, window_name="测试窗口"):
        super().__init__()
        self.window_name = window_name
        self.setWindowTitle(window_name)
        self.setGeometry(100, 100, 300, 200)
        
        # 初始化拖拽状态
        self._drag_start_position = None
        self._is_dragging = False
        self._original_title = None
        
        # 设置UI
        layout = QVBoxLayout()
        layout.addWidget(QLabel(f"这是{window_name}"))
        layout.addWidget(QLabel("拖拽我到主窗口底部进行停靠测试"))
        
        self.status_label = QLabel("状态: 准备就绪")
        layout.addWidget(self.status_label)
        
        self.setLayout(layout)
        
        # 启用鼠标跟踪
        self.setMouseTracking(True)
        
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self._drag_start_position = event.globalPos()
            self._is_dragging = False
            self.status_label.setText("状态: 鼠标按下")
            logger.info(f"🖱️ [测试拖拽] 鼠标按下 - 位置: {event.globalPos()}")
        super().mousePressEvent(event)
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if (event.buttons() == Qt.LeftButton and 
            self._drag_start_position is not None):
            
            # 计算拖拽距离
            distance = (event.globalPos() - self._drag_start_position).manhattanLength()
            
            # 如果拖拽距离足够大，开始拖拽
            if distance >= QApplication.startDragDistance():
                if not self._is_dragging:
                    logger.info(f"🎯 [测试拖拽] 开始拖拽模式 - 距离: {distance}")
                    self.status_label.setText(f"状态: 拖拽中 (距离: {distance})")
                self._is_dragging = True
                
                # 检查停靠区域
                self._check_dock_area(event.globalPos())
        
        super().mouseMoveEvent(event)
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton and self._is_dragging:
            logger.info(f"🎯 [测试拖拽] 鼠标释放 - 位置: {event.globalPos()}")
            
            # 检查是否在停靠区域释放
            in_dock_area = self._is_in_dock_area(event.globalPos())
            
            if in_dock_area:
                self.status_label.setText("状态: 在停靠区域释放 - 应该停靠")
                logger.info("✅ [测试拖拽] 在停靠区域释放，应该触发停靠")
            else:
                self.status_label.setText("状态: 不在停靠区域 - 保持悬浮")
                logger.info("❌ [测试拖拽] 不在停靠区域，保持悬浮状态")
            
            # 恢复窗口状态
            self._restore_window_state()
        
        # 重置拖拽状态
        self._drag_start_position = None
        self._is_dragging = False
        
        super().mouseReleaseEvent(event)
    
    def _check_dock_area(self, global_pos):
        """检查停靠区域并提供视觉反馈"""
        try:
            if self._is_in_dock_area(global_pos):
                # 提供视觉反馈
                if not self._original_title:
                    self._original_title = self.windowTitle()
                self.setWindowTitle(f"{self._original_title} - 释放鼠标停靠到主界面")
                
                # 改变鼠标光标
                QApplication.setOverrideCursor(Qt.PointingHandCursor)
                self.status_label.setText("状态: 在停靠区域 - 可以释放停靠")
            else:
                # 恢复原始状态
                if self._original_title:
                    self.setWindowTitle(self._original_title)
                QApplication.restoreOverrideCursor()
                self.status_label.setText("状态: 拖拽中")
        except Exception as e:
            logger.error(f"检查停靠区域时出错: {str(e)}")
    
    def _is_in_dock_area(self, global_pos):
        """判断是否在停靠区域内"""
        try:
            # 获取主窗口（假设是父窗口或应用程序的主窗口）
            main_window = QApplication.activeWindow()
            if not main_window or main_window == self:
                # 如果没有主窗口或主窗口就是自己，查找其他窗口
                for widget in QApplication.topLevelWidgets():
                    if isinstance(widget, QMainWindow) and widget != self:
                        main_window = widget
                        break
            
            if not main_window:
                logger.warning("未找到主窗口，无法检测停靠区域")
                return False
            
            # 获取主窗口的全局几何信息
            main_geometry = main_window.frameGeometry()
            
            # 定义停靠区域：主窗口底部30%的区域
            dock_area_height = int(main_geometry.height() * 0.3)
            dock_area_top = main_geometry.top() + main_geometry.height() - dock_area_height
            dock_area_bottom = main_geometry.bottom()
            
            # 添加边距，避免边界误触发
            margin = 10
            dock_area_left = main_geometry.left() + margin
            dock_area_right = main_geometry.right() - margin
            dock_area_top += margin
            dock_area_bottom -= margin
            
            # 检查鼠标是否在停靠区域内
            is_in_area = (global_pos.x() >= dock_area_left and
                         global_pos.x() <= dock_area_right and
                         global_pos.y() >= dock_area_top and
                         global_pos.y() <= dock_area_bottom)
            
            logger.debug(f"🎯 [停靠区域检测] 主窗口: {main_geometry}")
            logger.debug(f"🎯 [停靠区域检测] 停靠区域: X({dock_area_left}-{dock_area_right}), Y({dock_area_top}-{dock_area_bottom})")
            logger.debug(f"🎯 [停靠区域检测] 鼠标位置: {global_pos}, 在区域内: {is_in_area}")
            
            return is_in_area
            
        except Exception as e:
            logger.error(f"判断停靠区域时出错: {str(e)}")
            return False
    
    def _restore_window_state(self):
        """恢复窗口状态"""
        try:
            # 恢复窗口标题
            if self._original_title:
                self.setWindowTitle(self._original_title)
                self._original_title = None
            
            # 恢复鼠标光标
            QApplication.restoreOverrideCursor()
            
        except Exception as e:
            logger.error(f"恢复窗口状态时出错: {str(e)}")


class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("拖拽停靠测试 - 主窗口")
        self.setGeometry(200, 200, 800, 600)
        
        # 设置中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        layout.addWidget(QLabel("这是主窗口"))
        layout.addWidget(QLabel("底部30%区域是停靠区域"))
        
        # 创建测试按钮
        create_btn = QPushButton("创建测试拖拽窗口")
        create_btn.clicked.connect(self.create_test_window)
        layout.addWidget(create_btn)
        
        # 日志显示区域
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        layout.addWidget(QLabel("日志输出:"))
        layout.addWidget(self.log_text)
        
        central_widget.setLayout(layout)
        
        self.test_windows = []
    
    def create_test_window(self):
        """创建测试窗口"""
        window_count = len(self.test_windows) + 1
        test_window = TestDragWindow(f"测试窗口 {window_count}")
        test_window.show()
        self.test_windows.append(test_window)
        
        logger.info(f"创建了测试窗口 {window_count}")
        self.log_text.append(f"创建了测试窗口 {window_count}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = TestMainWindow()
    main_window.show()
    
    # 创建一个测试窗口
    test_window = TestDragWindow("初始测试窗口")
    test_window.show()
    
    logger.info("拖拽停靠测试程序启动")
    logger.info("使用说明:")
    logger.info("1. 拖拽测试窗口到主窗口底部30%区域")
    logger.info("2. 观察窗口标题和鼠标光标变化")
    logger.info("3. 在停靠区域释放鼠标查看日志输出")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
