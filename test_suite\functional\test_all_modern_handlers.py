#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试所有现代化处理器的ComboBox初始化
验证修复是否适用于所有现代化处理器
"""

import sys
import os
import json
import logging
import io
from contextlib import redirect_stderr

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from utils.Log import get_module_logger

logger = get_module_logger(__name__)
from core.services.register.RegisterManager import RegisterManager

def test_modern_handler(handler_class, handler_name):
    """测试单个现代化处理器"""
    try:
        print(f"\n--- 测试 {handler_name} ---")
        
        # 加载寄存器配置
        config_path = os.path.join('lib', 'register.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        # 创建RegisterManager
        register_manager = RegisterManager(registers_config)
        
        # 创建处理器
        handler = handler_class(None, register_manager)
        
        # 等待初始化完成
        import time
        time.sleep(0.3)
        QApplication.instance().processEvents()
        
        # 检查ComboBox控件
        combobox_count = 0
        success_count = 0
        
        if hasattr(handler, 'ui'):
            for attr_name in dir(handler.ui):
                if not attr_name.startswith('_'):
                    widget = getattr(handler.ui, attr_name)
                    if hasattr(widget, 'count') and hasattr(widget, 'currentIndex'):
                        combobox_count += 1
                        item_count = widget.count()
                        current_index = widget.currentIndex()
                        
                        if item_count > 0 and 0 <= current_index < item_count:
                            success_count += 1
                            print(f"  ✓ {attr_name}: {item_count}项, 索引={current_index}")
                        elif item_count > 0:
                            print(f"  ⚠ {attr_name}: {item_count}项, 索引={current_index} (索引可能有问题)")
                            success_count += 1  # 至少有选项
                        else:
                            print(f"  ❌ {attr_name}: 没有选项!")
        
        # 检查待设置的值
        pending_count = 0
        if hasattr(handler, '_pending_combobox_values'):
            pending_count = len(handler._pending_combobox_values)
            if pending_count > 0:
                print(f"  ⚠ 有 {pending_count} 个待设置的ComboBox值")
        
        print(f"  总结: {success_count}/{combobox_count} 个ComboBox正常, {pending_count} 个待设置")
        
        return combobox_count > 0 and success_count == combobox_count and pending_count == 0
        
    except Exception as e:
        print(f"  ❌ 测试 {handler_name} 时出错: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("测试所有现代化处理器的ComboBox初始化")
    print("=" * 60)
    
    # 创建QApplication
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    # 要测试的处理器
    handlers_to_test = [
        ("ui.handlers.ModernClkinControlHandler", "ModernClkinControlHandler", "时钟输入控制"),
        ("ui.handlers.ModernPLLHandler", "ModernPLLHandler", "PLL配置"),
        ("ui.handlers.ModernClkOutputsHandler", "ModernClkOutputsHandler", "时钟输出"),
        ("ui.handlers.ModernSyncSysRefHandler", "ModernSyncSysRefHandler", "同步系统参考"),
    ]
    
    results = []
    
    for module_name, class_name, display_name in handlers_to_test:
        try:
            # 动态导入模块
            module = __import__(module_name, fromlist=[class_name])
            handler_class = getattr(module, class_name)
            
            # 测试处理器
            success = test_modern_handler(handler_class, display_name)
            results.append((display_name, success))
            
        except ImportError as e:
            print(f"\n--- 测试 {display_name} ---")
            print(f"  ❌ 无法导入 {module_name}: {str(e)}")
            results.append((display_name, False))
        except Exception as e:
            print(f"\n--- 测试 {display_name} ---")
            print(f"  ❌ 测试出错: {str(e)}")
            results.append((display_name, False))
    
    # 总结结果
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    success_count = 0
    total_count = len(results)
    
    for display_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{display_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n总体结果: {success_count}/{total_count} 个处理器测试通过")
    
    if success_count == total_count:
        print("\n🎉 所有现代化处理器的ComboBox初始化都正常！")
        return True
    else:
        print(f"\n⚠ 有 {total_count - success_count} 个处理器仍有问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
