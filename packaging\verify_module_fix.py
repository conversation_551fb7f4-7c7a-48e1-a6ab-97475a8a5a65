#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证模块修复效果
检查所有spec文件中的关键模块配置
"""

import re
from pathlib import Path

def verify_spec_file(spec_file_path):
    """验证单个spec文件"""
    print(f"🔍 验证文件: {spec_file_path.name}")
    
    with open(spec_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 关键模块列表
    critical_modules = [
        'dis', 'email', 'os', 'sys', 'io', 'codecs', 'encodings'
    ]
    
    # 检查这些模块是否在hiddenimports中
    missing_in_hidden = []
    for module in critical_modules:
        if f"'{module}'" not in content or 'hiddenimports' not in content:
            missing_in_hidden.append(module)
    
    # 检查这些模块是否在excludes中（不应该在）
    found_in_excludes = []
    if 'excludes' in content:
        excludes_section = re.search(r'excludes\s*=\s*\[(.*?)\]', content, re.DOTALL)
        if excludes_section:
            excludes_content = excludes_section.group(1)
            for module in critical_modules:
                if f"'{module}'" in excludes_content:
                    found_in_excludes.append(module)
    
    # 报告结果
    if not missing_in_hidden and not found_in_excludes:
        print(f"  ✅ {spec_file_path.name} 配置正确")
        return True
    else:
        print(f"  ❌ {spec_file_path.name} 配置有问题:")
        if missing_in_hidden:
            print(f"    - 缺少在hiddenimports中: {missing_in_hidden}")
        if found_in_excludes:
            print(f"    - 错误地在excludes中: {found_in_excludes}")
        return False

def test_module_imports():
    """测试关键模块是否可以导入"""
    print("\n🧪 测试关键模块导入...")
    
    critical_modules = [
        'dis', 'email', 'email.mime', 'email.mime.text', 'email.mime.multipart',
        'os', 'sys', 'io', 'codecs', 'encodings', 'encodings.utf_8', 'encodings.cp1252',
        'inspect', 'types', 'typing', 'json', 'pathlib'
    ]
    
    failed_modules = []
    
    for module_name in critical_modules:
        try:
            __import__(module_name)
            print(f"  ✅ {module_name}")
        except ImportError as e:
            print(f"  ❌ {module_name}: {e}")
            failed_modules.append(module_name)
    
    return len(failed_modules) == 0

def main():
    """主函数"""
    print("🔍 验证PyInstaller模块修复效果")
    print("=" * 50)
    
    # 验证所有spec文件
    scripts_dir = Path(__file__).parent / 'scripts'
    spec_files = list(scripts_dir.glob('*.spec'))
    
    all_specs_ok = True
    for spec_file in spec_files:
        if not verify_spec_file(spec_file):
            all_specs_ok = False
    
    # 测试模块导入
    imports_ok = test_module_imports()
    
    print("\n" + "=" * 50)
    
    if all_specs_ok and imports_ok:
        print("🎉 验证通过！所有配置都正确")
        print("\n📋 现在可以安全地重新打包:")
        print("  cd packaging")
        print("  python package.py secure patch")
        print("\n💡 如果仍有问题，可能是其他模块依赖问题")
    else:
        print("⚠️ 验证未完全通过")
        if not all_specs_ok:
            print("  - spec文件配置有问题")
        if not imports_ok:
            print("  - 模块导入有问题")
        print("\n🔧 建议:")
        print("  1. 检查Python环境是否完整")
        print("  2. 重新运行修复脚本")
        print("  3. 手动检查spec文件配置")

if __name__ == "__main__":
    main()
