"""
寄存器操作管理器
负责管理寄存器的读写、更新、显示等操作
"""

import traceback
from PyQt5.QtWidgets import QMessageBox, QApplication
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class RegisterOperationManager:
    """寄存器操作管理器，处理寄存器相关的操作"""
    
    def __init__(self, main_window):
        """初始化寄存器操作管理器
        
        Args:
            main_window: 主窗口实例
        """
        self.main_window = main_window
    
    def handle_register_selection(self, reg_addr):
        """处理寄存器被选中事件

        Args:
            reg_addr: 寄存器地址
        """
        # 委托给寄存器操作服务
        success, reg_num, register_value = self.main_window.register_service.handle_register_selection(reg_addr)

        if success:
            self.main_window.selected_register_addr = reg_addr
            self.main_window.current_register_value = register_value

            # 更新UI显示
            self.update_rx_value_display(reg_num, register_value)
            self.update_bit_field_display(reg_addr, register_value)
            self.refresh_view()
        else:
            # 错误处理已在服务中完成
            if hasattr(self.main_window.io_handler, 'set_address_display') and hasattr(self.main_window.io_handler, 'set_value_display'):
                self.main_window.io_handler.set_address_display(reg_addr)
                self.main_window.io_handler.set_value_display(0) # Placeholder for value
            if hasattr(self.main_window.table_handler, 'clear_table_and_show_error'):
                self.main_window.table_handler.clear_table_and_show_error(f"无法加载寄存器 {reg_addr}")
    
    def update_rx_value_display(self, reg_num, value):
        """更新寄存器值显示"""
        # 更新值输入框
        self.main_window.io_handler.set_value_display(value)
        
        # 更新地址显示
        if hasattr(self.main_window.io_handler, 'addr_line_edit'):
            self.main_window.io_handler.addr_line_edit.setText(f"0x{reg_num:02X}")
            self.main_window.io_handler.value_label.setText(f"R{reg_num} Value:")
    
    def update_register_from_input(self):
        """从输入框更新寄存器值"""
        # 获取当前输入值
        new_value = self.main_window.io_handler.get_current_value()

        # 委托给寄存器操作服务
        self.main_window.register_service.update_register_from_input(self.main_window.selected_register_addr, new_value)

    def update_register_from_table(self, addr, new_value):
        """从表格更新寄存器值"""
        # 委托给寄存器操作服务
        self.main_window.register_service.write_register(addr, new_value)
    
    def update_register_value_and_display(self, addr, new_value):
        """更新寄存器值并显示
        
        Args:
            addr: 寄存器地址
            new_value: 新的寄存器值
            
        Returns:
            bool: 是否成功更新
        """
        try:
            # 标准化地址
            normalized_addr = self.main_window._normalize_register_address(addr)
            
            # 检查是否修改只读位
            if self.main_window.register_service.check_readonly_bits_modification(normalized_addr, new_value):
                response = QMessageBox.warning(
                    self.main_window,
                    "警告",
                    "您正在尝试修改只读位的值。\n\n继续操作可能会导致不可预测的结果。\n\n是否继续？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )

                if response == QMessageBox.No:
                    # 如果用户取消，恢复原始值
                    self.main_window.register_service.restore_original_value(normalized_addr)
                    return False
            
            # 检查当前处理模式
            if self.main_window.simulation_mode:
                # 模拟模式直接更新本地寄存器
                self.handle_simulation_write(normalized_addr, new_value)
            else:
                # 实际模式通过SPI更新寄存器
                self.main_window._execute_spi_write(normalized_addr, new_value)
                
            # 更新显示（使用十六进制显示）
            reg_num = int(normalized_addr, 16)
            self.update_rx_value_display(reg_num, new_value)
            
            # 保存当前值，用于可能的恢复
            self.main_window.current_register_value = new_value
            
            return True
            
        except Exception as e:
            QMessageBox.critical(self.main_window, "错误", f"更新寄存器值时出错: {str(e)}")
            logger.error(f"更新寄存器值时出错: {str(e)}\n{traceback.format_exc()}")
            return False
    
    def handle_simulation_write(self, addr, new_value):
        """模拟模式下处理写入操作
        
        Args:
            addr: 寄存器地址
            new_value: 新值
        """
        normalized_addr = self.main_window._normalize_register_address(addr)
        
        # 获取当前值
        old_value = self.main_window.register_manager.get_register_value(normalized_addr)
        
        # 如果值未变化，不执行更新
        if old_value == new_value:
            return
            
        # 更新寄存器管理器中的值
        self.main_window.register_manager.set_register_value(normalized_addr, new_value)
        
        # 更新显示(寄存器管理器已经发送了更新信号，所以这里不需要再更新显示)
    
    def update_bit_field_display(self, addr, value):
        """更新位字段显示，不触发写操作"""
        # 直接调用table_handler的方法显示位字段，不触发写操作
        self.main_window.table_handler.show_bit_fields(addr, value)
    
    def normalize_register_address(self, addr):
        """标准化寄存器地址格式"""
        return self.main_window.register_manager._normalize_register_address(addr)
    
    def refresh_view(self):
        """刷新当前视图显示
        在控件状态改变后调用此方法，确保表格视图同步更新
        """
        if not self.main_window.selected_register_addr:
            return
        # 获取当前寄存器值（强制从寄存器对象获取最新值）
        register_value = self.main_window.register_manager.get_register_value(self.main_window.selected_register_addr)
        old_value = self.main_window.current_register_value
        self.main_window.current_register_value = register_value
        # 更新寄存器值显示
        reg_num = int(self.main_window.selected_register_addr, 16) if isinstance(self.main_window.selected_register_addr, str) else self.main_window.selected_register_addr
        # 更新输入框显示
        self.update_rx_value_display(reg_num, register_value)
        # 更新位字段表格显示 - 使用强制刷新方法
        if hasattr(self.main_window.table_handler, 'refresh_display'):
            self.main_window.table_handler.show_bit_fields(self.main_window.selected_register_addr, register_value)
        elif hasattr(self.main_window.table_handler, 'show_bit_fields'):
            self.main_window.table_handler.show_bit_fields(self.main_window.selected_register_addr, register_value)
        # 检查是否在批量操作期间，避免触发布局重新计算
        is_batch_operation = (getattr(self.main_window, 'is_batch_reading', False) or
                            getattr(self.main_window, 'is_batch_writing', False) or
                            getattr(self.main_window, 'is_batch_updating', False))

        # 只在非批量操作且非寄存器切换时处理事件，避免布局抖动
        if not is_batch_operation:
            # 使用更温和的更新方式，避免强制处理所有事件
            if hasattr(self.main_window, 'update'):
                self.main_window.update()
            # QApplication.processEvents()  # 移除此调用，避免布局重新计算
        # 如果值有变化，在状态栏显示详细信息
        if old_value != register_value:
            self.main_window.show_status_message(f"寄存器 R{reg_num} (0x{reg_num:02X}) 已刷新: 0x{old_value:04X} → 0x{register_value:04X}", 5000)
        else:
            self.main_window.show_status_message(f"寄存器 R{reg_num} (0x{reg_num:02X}) = 0x{register_value:04X} 视图已刷新", 3000)

    def refresh_ui(self):
        """刷新主界面UI，确保寄存器和表格等控件同步最新值"""
        # 刷新当前选中寄存器的详细信息和表格
        self.refresh_view()
        # 可以根据需要扩展刷新其他控件

    def get_register_addr_from_tree_item(self):
        """从树形控件中获取当前选中的寄存器地址"""
        # 获取当前选中的寄存器地址
        return self.main_window.tree_handler.get_current_register_addr()
    
    def show_status_message(self, message, timeout=3000):
        """统一显示状态栏消息"""
        if hasattr(self.main_window, "status_bar"):
            self.main_window.status_bar.showMessage(message, timeout)

    def check_spi_availability(self):
        """检查SPI可用性"""
        if self.main_window.simulation_mode:
            return True
        
        if not hasattr(self.main_window, 'spi_service') or not self.main_window.spi_service:
            QMessageBox.warning(self.main_window, "错误", "SPI操作不可用，请检查连接")
            return False
        
        # 使用get_connection_status()方法检查端口状态
        status = self.main_window.spi_service.get_connection_status()
        if not status.get('connected', False):
            QMessageBox.warning(self.main_window, "错误", "请先选择COM端口")
            return False
        
        return True
    
    def cleanup_resources(self):
        """清理资源"""
        try:
            logger.debug("寄存器操作管理器资源已清理")
        except Exception as e:
            logger.error(f"清理寄存器操作管理器资源时出错: {str(e)}")
