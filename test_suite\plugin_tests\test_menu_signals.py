#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试插件菜单信号连接
"""

import sys
import os

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication, QMainWindow, QMenuBar, QAction
from PyQt5.QtCore import QTimer
from core.services.plugin.PluginManager import plugin_manager
from core.services.plugin.PluginIntegrationService import PluginIntegrationService


class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("插件菜单测试")
        self.resize(800, 600)
        
        # 创建菜单栏
        self.menuBar()
        
        # 设置插件系统
        self.setup_plugins()
    
    def setup_plugins(self):
        """设置插件系统"""
        # 添加插件目录
        plugin_dir = os.path.join(project_root, "plugins")
        plugin_manager.add_plugin_directory(plugin_dir)
        
        # 扫描并初始化插件
        plugin_manager.scan_plugins()
        plugin_manager.initialize_plugins(self)
        
        # 创建插件集成服务
        self.plugin_service = PluginIntegrationService(self)
        self.plugin_service.initialize_plugins()
        
        print(f"插件系统初始化完成，发现 {len(plugin_manager.get_plugin_list())} 个插件")


def test_menu_signals():
    """测试菜单信号"""
    app = QApplication([])
    
    # 创建测试窗口
    window = TestMainWindow()
    window.show()
    
    # 等待一段时间让用户测试菜单
    print("请在窗口中测试插件菜单项的点击...")
    print("程序将在10秒后自动关闭")
    
    # 设置定时器自动关闭
    timer = QTimer()
    timer.timeout.connect(app.quit)
    timer.start(10000)  # 10秒后关闭
    
    # 运行应用
    app.exec_()
    
    print("测试完成")


if __name__ == "__main__":
    test_menu_signals()
