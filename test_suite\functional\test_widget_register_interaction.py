#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
控件寄存器交互测试
专门测试：
8. 修改控件状态后寄存器表格跳转功能
9. 自动写入芯片并读取验证功能
"""

import sys
import os
import unittest
import time

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'test_suite'))

from test_utils import TestLogger, TestResult, qt_application, MockRegisterManager, create_mock_main_window
from test_config import get_test_config

class TestWidgetRegisterInteraction(unittest.TestCase):
    """控件寄存器交互测试类"""
    
    def setUp(self):
        """测试设置"""
        self.logger = TestLogger("widget_register_interaction")
        self.test_results = []
    
    def tearDown(self):
        """测试清理"""
        pass
    
    def test_widget_change_register_table_jump(self):
        """测试8: 修改控件状态后寄存器表格跳转"""
        result = TestResult("widget_change_register_table_jump")
        
        try:
            with qt_application():
                from ui.windows.RegisterMainWindow import RegisterMainWindow
                from core.services.spi.spi_service_impl import SPIServiceImpl
                from core.repositories.register_repository import RegisterRepository
                
                # 创建主窗口
                spi_service = SPIServiceImpl()
                spi_service.initialize()
                register_repo = RegisterRepository(spi_service)
                main_window = RegisterMainWindow(register_repo)
                
                # 创建PLL工具窗口
                pll_window = main_window.tool_window_factory.create_window_by_type('pll_control')
                
                if pll_window:
                    # 记录初始寄存器表格状态
                    initial_register = None
                    if hasattr(main_window, 'table_handler') and hasattr(main_window.table_handler, 'current_register_addr'):
                        initial_register = main_window.table_handler.current_register_addr
                    
                    # 模拟修改PLL控件状态
                    if hasattr(pll_window, 'widget_register_map'):
                        # 查找PLL相关控件
                        pll_widgets = [name for name in pll_window.widget_register_map.keys() if "PLL" in name]
                        
                        if pll_widgets:
                            test_widget = pll_widgets[0]
                            
                            # 模拟控件值变化
                            pll_window._on_widget_changed(test_widget, True)
                            
                            # 等待处理完成
                            time.sleep(0.5)
                            
                            # 检查寄存器表格是否跳转
                            final_register = None
                            if hasattr(main_window, 'table_handler') and hasattr(main_window.table_handler, 'current_register_addr'):
                                final_register = main_window.table_handler.current_register_addr
                            
                            # 验证跳转是否发生
                            register_changed = (initial_register != final_register)
                            
                            result.add_detail('initial_register', initial_register)
                            result.add_detail('final_register', final_register)
                            result.add_detail('widget_changed', test_widget)
                            result.add_detail('register_table_jumped', register_changed)
                            result.set_success(True)
                        else:
                            result.set_error("PLL窗口中没有找到PLL相关控件")
                    else:
                        result.set_error("PLL窗口缺少widget_register_map")
                else:
                    result.set_error("无法创建PLL工具窗口")
                
        except Exception as e:
            result.set_error(str(e))
            self.logger.error(f"控件状态修改寄存器表格跳转测试失败: {e}")
        
        self.test_results.append(result)
        self.assertTrue(result.success)
    
    def test_auto_write_and_read_verification(self):
        """测试9: 自动写入芯片并读取验证功能"""
        result = TestResult("auto_write_and_read_verification")
        
        try:
            with qt_application():
                from ui.windows.RegisterMainWindow import RegisterMainWindow

                # 创建主窗口并启用自动写入 (现在使用依赖注入，不需要repository参数)
                main_window = RegisterMainWindow()
                
                # 启用自动写入模式
                if hasattr(main_window, 'auto_write_mode'):
                    main_window.auto_write_mode = True
                
                # 创建PLL工具窗口
                pll_window = main_window.tool_window_factory.create_window_by_type('pll_control')
                
                if pll_window:
                    # 测试自动写入功能
                    test_register = "0x50"  # PLL控制寄存器
                    test_value = 0x1234
                    
                    # 记录写入前的值
                    initial_value = None
                    if hasattr(main_window, 'register_service'):
                        initial_value = main_window.register_service.read_register(test_register)
                    
                    # 模拟控件修改触发自动写入
                    if hasattr(pll_window, 'register_manager'):
                        # 修改寄存器值
                        pll_window.register_manager.set_register_value(test_register, test_value)
                        
                        # 触发自动写入（如果启用）
                        if hasattr(pll_window, '_auto_write_register_to_chip'):
                            pll_window._auto_write_register_to_chip(test_register, test_value)
                        
                        # 等待写入完成
                        time.sleep(0.5)
                        
                        # 读取验证
                        if hasattr(main_window, 'register_service'):
                            read_value = main_window.register_service.read_register(test_register)
                            
                            # 验证写入是否成功
                            write_success = (read_value == test_value)
                            
                            result.add_detail('test_register', test_register)
                            result.add_detail('initial_value', hex(initial_value) if initial_value is not None else None)
                            result.add_detail('written_value', hex(test_value))
                            result.add_detail('read_value', hex(read_value))
                            result.add_detail('auto_write_success', write_success)
                            result.set_success(write_success)
                        else:
                            result.set_error("主窗口缺少register_service")
                    else:
                        result.set_error("PLL窗口缺少register_manager")
                else:
                    result.set_error("无法创建PLL工具窗口")
                
        except Exception as e:
            result.set_error(str(e))
            self.logger.error(f"自动写入验证测试失败: {e}")
        
        self.test_results.append(result)
        self.assertTrue(result.success)
    
    def test_clk_output_widget_interaction(self):
        """测试时钟输出控件交互"""
        result = TestResult("clk_output_widget_interaction")
        
        try:
            with qt_application():
                from ui.windows.RegisterMainWindow import RegisterMainWindow

                # 创建主窗口 (现在使用依赖注入，不需要repository参数)
                main_window = RegisterMainWindow()
                
                # 创建时钟输出工具窗口
                clk_window = main_window.tool_window_factory.create_window_by_type('clk_outputs')
                
                if clk_window:
                    # 测试时钟输出控件修改
                    if hasattr(clk_window, 'widget_register_map'):
                        clk_widgets = [name for name in clk_window.widget_register_map.keys() if "CLK" in name or "DIV" in name]
                        
                        if clk_widgets:
                            test_widget = clk_widgets[0]
                            
                            # 模拟控件值变化
                            clk_window._on_widget_changed(test_widget, 2)  # 设置分频值为2
                            
                            # 验证寄存器值是否更新
                            if hasattr(clk_window, 'register_manager'):
                                # 检查相关寄存器是否被更新
                                widget_config = clk_window.widget_register_map[test_widget]
                                register_addr = widget_config['register']
                                
                                updated_value = clk_window.register_manager.get_register_value(register_addr)
                                
                                result.add_detail('clk_widget_changed', test_widget)
                                result.add_detail('register_address', register_addr)
                                result.add_detail('updated_value', hex(updated_value))
                                result.set_success(True)
                            else:
                                result.set_error("时钟输出窗口缺少register_manager")
                        else:
                            result.set_error("时钟输出窗口中没有找到时钟相关控件")
                    else:
                        result.set_error("时钟输出窗口缺少widget_register_map")
                else:
                    result.set_error("无法创建时钟输出工具窗口")
                
        except Exception as e:
            result.set_error(str(e))
            self.logger.error(f"时钟输出控件交互测试失败: {e}")
        
        self.test_results.append(result)
        self.assertTrue(result.success)
    
    def test_sync_sysref_widget_interaction(self):
        """测试同步参考控件交互"""
        result = TestResult("sync_sysref_widget_interaction")
        
        try:
            with qt_application():
                from ui.windows.RegisterMainWindow import RegisterMainWindow

                # 创建主窗口 (现在使用依赖注入，不需要repository参数)
                main_window = RegisterMainWindow()
                
                # 创建同步参考工具窗口
                sync_window = main_window.tool_window_factory.create_window_by_type('sync_sysref')
                
                if sync_window:
                    # 测试同步参考控件修改
                    if hasattr(sync_window, 'widget_register_map'):
                        sync_widgets = [name for name in sync_window.widget_register_map.keys() if "SYNC" in name or "SYSREF" in name]
                        
                        if sync_widgets:
                            test_widget = sync_widgets[0]
                            
                            # 模拟控件值变化
                            sync_window._on_widget_changed(test_widget, True)
                            
                            # 验证寄存器值是否更新
                            if hasattr(sync_window, 'register_manager'):
                                widget_config = sync_window.widget_register_map[test_widget]
                                register_addr = widget_config['register']
                                
                                updated_value = sync_window.register_manager.get_register_value(register_addr)
                                
                                result.add_detail('sync_widget_changed', test_widget)
                                result.add_detail('register_address', register_addr)
                                result.add_detail('updated_value', hex(updated_value))
                                result.set_success(True)
                            else:
                                result.set_error("同步参考窗口缺少register_manager")
                        else:
                            result.set_error("同步参考窗口中没有找到同步相关控件")
                    else:
                        result.set_error("同步参考窗口缺少widget_register_map")
                else:
                    result.set_error("无法创建同步参考工具窗口")
                
        except Exception as e:
            result.set_error(str(e))
            self.logger.error(f"同步参考控件交互测试失败: {e}")
        
        self.test_results.append(result)
        self.assertTrue(result.success)

def run_tests():
    """运行控件寄存器交互测试"""
    suite = unittest.TestLoader().loadTestsFromTestCase(TestWidgetRegisterInteraction)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
