#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终测试搜索功能
验证修改后的搜索功能是否与重构前的实现一致
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
from PyQt5.QtCore import QTimer, Qt
from ui.handlers.ModernRegisterIOHandler import ModernRegisterIOHandler
from utils.Log import logger

class FinalTestWindow(QMainWindow):
    """最终测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("最终搜索功能测试")
        self.setGeometry(100, 100, 1000, 700)
        
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 添加说明标签
        info_label = QLabel("""
        搜索功能测试说明：
        1. 在搜索框中输入关键字（如 'sclk'）
        2. 观察搜索结果列表是否正确显示（无Python图标）
        3. 点击搜索结果项测试选择功能
        4. 测试键盘导航（上下箭头、回车、ESC）
        """)
        layout.addWidget(info_label)
        
        # 创建现代化IO处理器
        try:
            self.io_handler = ModernRegisterIOHandler.create_for_testing(self)
            layout.addWidget(self.io_handler)
            
            # 连接信号
            self.io_handler.search_requested.connect(self.on_search_requested)
            self.io_handler.bit_field_selected.connect(self.on_bit_field_selected)
            
            logger.info("现代化IO处理器创建成功")
            
            # 添加测试按钮
            test_layout = QVBoxLayout()
            
            auto_test_btn = QPushButton("自动测试搜索功能")
            auto_test_btn.clicked.connect(self.auto_test_search)
            test_layout.addWidget(auto_test_btn)
            
            check_style_btn = QPushButton("检查搜索结果列表样式")
            check_style_btn.clicked.connect(self.check_search_list_style)
            test_layout.addWidget(check_style_btn)
            
            layout.addLayout(test_layout)
            
        except Exception as e:
            logger.error(f"创建现代化IO处理器失败: {str(e)}")
            error_label = QLabel(f"创建失败: {str(e)}")
            layout.addWidget(error_label)
    
    def auto_test_search(self):
        """自动测试搜索功能"""
        logger.info("=== 开始自动测试搜索功能 ===")
        
        if not hasattr(self.io_handler, 'search_edit'):
            logger.error("搜索框不存在")
            return
        
        # 测试1：搜索 "sclk"
        logger.info("测试1：搜索 'sclk'")
        self.io_handler.search_edit.setText("sclk")
        self.io_handler._handle_search_click("sclk")
        
        # 检查搜索结果
        if hasattr(self.io_handler, 'search_results_list'):
            count = self.io_handler.search_results_list.count()
            logger.info(f"搜索结果数量: {count}")
            
            if count > 0:
                # 显示前几个结果
                for i in range(min(5, count)):
                    item = self.io_handler.search_results_list.item(i)
                    if item:
                        logger.info(f"  结果 {i+1}: {item.text()}")
        
        # 测试2：搜索 "pll"
        logger.info("测试2：搜索 'pll'")
        self.io_handler.search_edit.setText("pll")
        self.io_handler._handle_search_click("pll")
        
        if hasattr(self.io_handler, 'search_results_list'):
            count = self.io_handler.search_results_list.count()
            logger.info(f"搜索结果数量: {count}")
        
        logger.info("=== 自动测试完成 ===")
    
    def check_search_list_style(self):
        """检查搜索结果列表的样式"""
        logger.info("=== 检查搜索结果列表样式 ===")
        
        if not hasattr(self.io_handler, 'search_results_list'):
            logger.error("搜索结果列表不存在")
            return
        
        search_list = self.io_handler.search_results_list
        
        # 检查窗口标志
        flags = search_list.windowFlags()
        logger.info(f"窗口标志: {flags}")
        logger.info(f"是否为弹出窗口: {bool(flags & Qt.Popup)}")
        
        # 检查其他属性
        logger.info(f"最大高度: {search_list.maximumHeight()}")
        logger.info(f"当前可见性: {search_list.isVisible()}")
        logger.info(f"父控件: {search_list.parent()}")
        
        # 触发一次搜索来显示列表
        self.io_handler.search_edit.setText("test")
        self.io_handler._handle_search_click("test")
        
        logger.info(f"搜索后可见性: {search_list.isVisible()}")
        logger.info(f"搜索后项目数: {search_list.count()}")
        
        logger.info("=== 样式检查完成 ===")
    
    def on_search_requested(self, search_text):
        """处理搜索请求"""
        logger.info(f"搜索请求: '{search_text}'")
    
    def on_bit_field_selected(self, reg_addr):
        """处理位字段选择"""
        logger.info(f"位字段选择: 寄存器 {reg_addr}")
        # 显示当前寄存器信息
        try:
            addr = int(reg_addr, 16) if reg_addr.startswith("0x") else int(reg_addr)
            if hasattr(self.io_handler, 'register_manager') and self.io_handler.register_manager:
                value = self.io_handler.register_manager.get_register_value(addr)
                logger.info(f"已切换到寄存器 0x{addr:02X}, 值: 0x{value:04X}")
        except ValueError:
            logger.error(f"无效的寄存器地址: {reg_addr}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = FinalTestWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
