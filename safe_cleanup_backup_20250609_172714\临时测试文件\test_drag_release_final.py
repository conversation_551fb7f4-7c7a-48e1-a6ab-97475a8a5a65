#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
拖拽释放检测测试脚本
测试工具窗口拖拽到下方时是否能正确检测到鼠标释放事件
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer, QPoint, QEvent, Qt
from PyQt5.QtGui import QMouseEvent

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ui.windows.RegisterMainWindow import RegisterMainWindow
from core.services.plugin.PluginIntegrationService import PluginIntegrationService
from core.services.DIContainer import DIContainer
from utils.Log import logger

def setup_chinese_support(app):
    """设置中文支持"""
    from PyQt5.QtCore import QTextCodec, QLocale
    from PyQt5.QtGui import QFont
    
    try:
        # 设置UTF-8编码
        if hasattr(QTextCodec, 'setCodecForLocale'):
            utf8_codec = QTextCodec.codecForName("UTF-8")
            if utf8_codec:
                QTextCodec.setCodecForLocale(utf8_codec)

        # 设置中文本地化
        QLocale.setDefault(QLocale(QLocale.Chinese, QLocale.China))

        # 设置支持中文的字体
        font = QFont()
        if sys.platform.startswith('win'):
            font.setFamily("Microsoft YaHei")
        font.setPointSize(9)
        app.setFont(font)
        
        logger.info(f"中文支持设置完成，使用字体: {font.family()}")
    except Exception as e:
        logger.warning(f"设置中文支持时出现警告: {str(e)}")

def test_drag_release_detection():
    original_stdout = sys.stdout
    test_log_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "test_output.log")
    
    app_for_cleanup = None
    result_of_test = False 

    with open(test_log_path, 'w', encoding='utf-8') as f_out:
        sys.stdout = f_out # Redirect stdout for the scope of this 'with'

        try:
            print("\n=== 拖拽释放检测测试 ===")
            
            app = QApplication(sys.argv)
            app_for_cleanup = app
            app.setStyle('Fusion')
            setup_chinese_support(app)
            
            main_window = RegisterMainWindow()
            main_window.show()
            
            app.processEvents()
            time.sleep(1)
            
            container = DIContainer.instance() # Initialize container
            plugin_service = container.get('plugin_service') # Fetch and use 'plugin_service'

            if not plugin_service: 
                print("❌ 无法获取PluginIntegrationService (using key 'plugin_service')")
                result_of_test = False
            else:
                print(f"✓ 获取到插件服务: {type(plugin_service).__name__}")
                plugin_service.force_floating_mode = True
                print("✓ 已启用强制浮动模式")
                
                from core.services.plugin.PluginManager import PluginManager
                plugin_manager = PluginManager.instance()
                if not plugin_manager:
                    print("❌ 无法获取PluginManager")
                    result_of_test = False
                else:
                    test_plugin_name = "时钟输入控制"
                    plugin = plugin_manager.get_plugin(test_plugin_name)
                    if not plugin:
                        print(f"❌ 无法找到插件: {test_plugin_name}")
                        result_of_test = False
                    else:
                        print(f"✓ 找到插件: {test_plugin_name}")
                        plugin_service._show_plugin_window(plugin)
                        app.processEvents()
                        time.sleep(1)
                        
                        plugin_window = plugin_service.get_plugin_window(test_plugin_name)
                        if not plugin_window:
                            print(f"❌ 无法获取插件窗口: {test_plugin_name}")
                            result_of_test = False
                        else:
                            print(f"✓ 获取到插件窗口: {plugin_window.windowTitle()}")
                            print(f"\n--- 拖拽支持检查 ---")
                            print(f"窗口类型: {type(plugin_window).__name__}")
                            print(f"窗口标题: {plugin_window.windowTitle()}")
                            print(f"鼠标跟踪: {plugin_window.hasMouseTracking()}")
                            
                            drag_attrs = ['_is_dragging', '_drag_start_position', '_original_title']
                            for attr in drag_attrs:
                                if hasattr(plugin_window, attr):
                                    value = getattr(plugin_window, attr)
                                    print(f"{attr}: {value}")
                                else:
                                    print(f"{attr}: 未找到")
                            
                            print(f"\n--- 模拟拖拽操作 ---")
                            press_pos = QPoint(50, 20)
                            press_event = QMouseEvent(QEvent.MouseButtonPress, press_pos, Qt.LeftButton, Qt.LeftButton, Qt.NoModifier)
                            print(f"1. 发送鼠标按下事件: {press_pos}")
                            app.sendEvent(plugin_window, press_event)
                            app.processEvents()
                            is_dragging_after_press = getattr(plugin_window, '_is_dragging', None)
                            print(f"   按下后_is_dragging: {is_dragging_after_press}")
                            
                            move_pos = QPoint(50, 120)
                            move_event = QMouseEvent(QEvent.MouseMove, move_pos, Qt.NoButton, Qt.LeftButton, Qt.NoModifier)
                            print(f"2. 发送鼠标移动事件: {move_pos}")
                            app.sendEvent(plugin_window, move_event)
                            app.processEvents()
                            is_dragging_after_move = getattr(plugin_window, '_is_dragging', None)
                            print(f"   移动后_is_dragging: {is_dragging_after_move}")
                            
                            release_pos = move_pos
                            release_event = QMouseEvent(QEvent.MouseButtonRelease, release_pos, Qt.LeftButton, Qt.NoButton, Qt.NoModifier)
                            print(f"3. 发送鼠标释放事件: {release_pos}, Window ID before sendEvent: {id(plugin_window)}")
                            app.sendEvent(plugin_window, release_event)
                            app.processEvents()
                            print(f"   Window ID after sendEvent and processEvents: {id(plugin_window)}")
                            is_dragging_after_release = getattr(plugin_window, '_is_dragging', None)
                            print(f"   释放后_is_dragging: {is_dragging_after_release}")
                            
                            print(f"\n--- 测试结果分析 ---")
                            if is_dragging_after_move is True:
                                print("✓ 拖拽状态正确设置为True")
                                if is_dragging_after_release is False:
                                    print("✓ 鼠标释放后拖拽状态正确重置为False")
                                    print("✅ 拖拽释放检测正常工作")
                                    result_of_test = True
                                else:
                                    print("❌ 鼠标释放后拖拽状态未重置")
                                    print("🔍 这可能是导致无法自动停靠的原因")
                                    result_of_test = False
                            else:
                                print("❌ 拖拽状态未正确设置")
                                print("🔍 可能是拖拽距离不够或事件处理有问题")
                                result_of_test = False
        except Exception as e:
            print(f"❌ 测试过程中出现错误: {str(e)}")
            import traceback
            traceback.print_exc()
            result_of_test = False
        finally:
            if app_for_cleanup:
                app_for_cleanup.quit()

    sys.stdout = original_stdout
    print(f"Test output saved to {test_log_path}")
    
    return result_of_test

def main():
    """主函数"""
    success = test_drag_release_detection()
    
    print(f"\n=== 测试完成 ===")
    if success:
        print("✅ 拖拽释放检测测试通过")
    else:
        print("❌ 拖拽释放检测测试失败")
        print("\n建议检查:")
        print("1. PluginIntegrationService中的事件过滤器逻辑")
        print("2. _handle_drag_release方法是否被正确调用")
        print("3. 鼠标释放事件的处理逻辑")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())