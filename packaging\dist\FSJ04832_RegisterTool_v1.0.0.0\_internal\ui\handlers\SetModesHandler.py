# -*- coding: utf-8 -*-

from PyQt5 import QtCore, QtWidgets
from ui.forms.Ui_setModes import Ui_setModes
from ui.handlers.BaseHandler import BaseClockHandler
from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class SetModesHandler(BaseClockHandler):
    """模式设置界面处理类"""

    # 添加一个关闭信号
    window_closed = QtCore.pyqtSignal()

    def __init__(self, parent=None, registers=None):
        super(SetModesHandler, self).__init__(parent, registers)

        # 设置窗口标题
        self.setWindowTitle("模式设置")
        
        # 设置UI到内容widget
        self.ui = Ui_setModes()
        self.ui.setupUi(self.content_widget)

        # 初始化特定配置
        self.init_modes_config()

    def init_modes_config(self):
        """初始化模式设置特定配置"""
        # 连接信号
        self.connect_signals()

    def connect_signals(self):
        """连接信号与槽"""
        self.ui.pBtSetDualLoop.clicked.connect(lambda: self.set_mode("DualLoop"))
        self.ui.pBtSetSingleLoop.clicked.connect(lambda: self.set_mode("SingleLoop"))
        self.ui.pBtSetSingleLoop0Dealy.clicked.connect(lambda: self.set_mode("SingleLoop0Dealy"))
        self.ui.pBtSetDualLoop0DealyCascaded.clicked.connect(lambda: self.set_mode("DualLoop0DealyCascaded"))
        self.ui.pBtSetDualLoop0DealyNested.clicked.connect(lambda: self.set_mode("DualLoop0DealyNested"))
        self.ui.pBtSetDualLoop0DealyNestedandCasc.clicked.connect(lambda: self.set_mode("DualLoop0DealyNestedandCasc"))
        self.ui.pBtSetDistributionFin1.clicked.connect(lambda: self.set_mode("DistributionFin1"))

    def set_mode(self, mode_name):
        """设置特定模式
        
        Args:
            mode_name: 模式名称，指示要设置的模式
        """
        try:
            logger.info(f"正在设置模式: {mode_name}")
            
            # 寄存器设置映射
            # 根据图表中的寄存器信息设置寄存器位段值
            register_settings = {
                "DualLoop": {
                    "0x4F": {
                        "PLL1_NCLK_MUX[1:0]": 0b00,  # 4:3位
                        "PLL2_NCLK_MUX": 0,     # 5位
                        "PLL2_RCLK_MUX": 0,     # 7位
                        "FB_MUX_EN": 0,         # 0位
                        "FB_MUX[1:0]": 0b00          # 2:1位
                    },
                    "0x50": {
                        "OSCin_PD": 0,          # 4位
                        "VCO_LDO_PD": 0,        # 6位
                        "VCO_PD": 0,            # 5位
                        "PLL1_PD": 0            # 7位
                    },
                    "0x57": {
                        "CLKin0_OUT_MUX": 0b10, # 1:0位
                        "CLKin1_OUT_MUX": 0b10  # 3:2位
                    },
                    "0x48": {
                        "VCO_MUX": 0b01         # 6:5位
                    },
                    "0x83": {
                        "PLL2_PRE_PD": 0,       # 6位
                        "PLL2_PD": 0            # 5位
                    }
                },
                "SingleLoop": {
                    "0x4F": {
                        "PLL1_NCLK_MUX[1:0]": 0b00,  # 4:3位 = 00
                        "PLL2_NCLK_MUX": 0,     # 5位 = 0
                        "PLL2_RCLK_MUX": 0,     # 7位 = 0
                        "FB_MUX_EN": 0,         # 0位 = 0
                        "FB_MUX[1:0]": 0b00          # 2:1位 = 00
                    },
                    "0x50": {
                        "OSCin_PD": 0,          # 4位
                        "VCO_LDO_PD": 0,        # 6位
                        "VCO_PD": 0,            # 5位
                        "PLL1_PD": 1            # 7位 = 1
                    },
                    "0x57": {
                        "CLKin0_OUT_MUX": 0b10, # 1:0位
                        "CLKin1_OUT_MUX": 0b10  # 3:2位
                    },
                    "0x48": {
                        "VCO_MUX": 0b01         # 6:5位
                    },
                    "0x83": {
                        "PLL2_PRE_PD": 0,       # 6位
                        "PLL2_PD": 0            # 5位
                    }
                },
                "SingleLoop0Dealy": {
                    "0x4F": {
                        "PLL1_NCLK_MUX[1:0]": 0b00,  # 4:3位 = 00
                        "PLL2_NCLK_MUX": 1,     # 5位 = 1
                        "PLL2_RCLK_MUX": 0,     # 7位 = 0
                        "FB_MUX_EN": 1,         # 0位 = 1
                        "FB_MUX[1:0]": 0b00          # 2:1位 = 00
                    },
                    "0x50": {
                        "OSCin_PD": 0,          # 4位
                        "VCO_LDO_PD": 0,        # 6位
                        "VCO_PD": 0,            # 5位
                        "PLL1_PD": 1            # 7位 = 1
                    },
                    "0x57": {
                        "CLKin0_OUT_MUX": 0b10, # 1:0位
                        "CLKin1_OUT_MUX": 0b10  # 3:2位
                    },
                    "0x48": {
                        "VCO_MUX": 0b01         # 6:5位
                    },
                    "0x83": {
                        "PLL2_PRE_PD": 0,       # 6位
                        "PLL2_PD": 0            # 5位
                    }
                },
                "DualLoop0DealyCascaded": {
                    "0x4F": {
                        "PLL1_NCLK_MUX[1:0]": 0b00,  # 4:3位 = 00
                        "PLL2_NCLK_MUX": 1,     # 5位 = 1
                        "PLL2_RCLK_MUX": 0,     # 7位 = 0
                        "FB_MUX_EN": 1,         # 0位 = 1
                        "FB_MUX[1:0]": 0b00          # 2:1位 = 00
                    },
                    "0x50": {
                        "OSCin_PD": 0,          # 4位
                        "VCO_LDO_PD": 0,        # 6位
                        "VCO_PD": 0,            # 5位
                        "PLL1_PD": 0            # 7位
                    },
                    "0x57": {
                        "CLKin0_OUT_MUX": 0b10, # 1:0位
                        "CLKin1_OUT_MUX": 0b10  # 3:2位
                    },
                    "0x48": {
                        "VCO_MUX": 0b01         # 6:5位
                    },
                    "0x83": {
                        "PLL2_PRE_PD": 0,       # 6位
                        "PLL2_PD": 0            # 5位
                    }
                },
                "DualLoop0DealyNested": {
                    "0x4F": {
                        "PLL1_NCLK_MUX[1:0]": 0b10,  # 4:3位 = 10
                        "PLL2_NCLK_MUX": 0,     # 5位 = 0
                        "PLL2_RCLK_MUX": 0,     # 7位 = 0
                        "FB_MUX_EN": 1,         # 0位 = 1
                        "FB_MUX[1:0]": 0b00          # 2:1位 = 00
                    },
                    "0x50": {
                        "OSCin_PD": 0,          # 4位
                        "VCO_LDO_PD": 0,        # 6位
                        "VCO_PD": 0,            # 5位
                        "PLL1_PD": 0            # 7位
                    },
                    "0x57": {
                        "CLKin0_OUT_MUX": 0b10, # 1:0位
                        "CLKin1_OUT_MUX": 0b10  # 3:2位
                    },
                    "0x48": {
                        "VCO_MUX": 0b01         # 6:5位
                    },
                    "0x83": {
                        "PLL2_PRE_PD": 0,       # 6位
                        "PLL2_PD": 0            # 5位
                    }
                },
                "DualLoop0DealyNestedandCasc": {
                    "0x4F": {
                        "PLL1_NCLK_MUX[1:0]": 0b10,  # 4:3位 = 10
                        "PLL2_NCLK_MUX": 1,     # 5位 = 1
                        "PLL2_RCLK_MUX": 0,     # 7位 = 0
                        "FB_MUX_EN": 1,         # 0位 = 1
                        "FB_MUX[1:0]": 0b00          # 2:1位 = 00
                    },
                    "0x50": {
                        "OSCin_PD": 0,          # 4位
                        "VCO_LDO_PD": 0,        # 6位
                        "VCO_PD": 0,            # 5位
                        "PLL1_PD": 0            # 7位
                    },
                    "0x57": {
                        "CLKin0_OUT_MUX": 0b10, # 1:0位
                        "CLKin1_OUT_MUX": 0b10  # 3:2位
                    },
                    "0x48": {
                        "VCO_MUX": 0b01         # 6:5位
                    },
                    "0x83": {
                        "PLL2_PRE_PD": 0,       # 6位
                        "PLL2_PD": 0            # 5位
                    }
                },
                "DistributionFin1": {
                    "0x4F": {
                        "PLL1_NCLK_MUX[1:0]": 0b00,  # 4:3位 = 00
                        "PLL2_NCLK_MUX": 0,     # 5位 = 0
                        "PLL2_RCLK_MUX": 0,     # 7位 = 0
                        "FB_MUX_EN": 0,         # 0位 = 0
                        "FB_MUX[1:0]": 0b00          # 2:1位 = 00
                    },
                    "0x50": {
                        "OSCin_PD": 1,          # 4位 = 1
                        "VCO_LDO_PD": 1,        # 6位 = 1
                        "VCO_PD": 1,            # 5位 = 1
                        "PLL1_PD": 1            # 7位 = 1
                    },
                    "0x57": {
                        "CLKin0_OUT_MUX": 0b10, # 1:0位
                        "CLKin1_OUT_MUX": 0b10  # 3:2位
                    },
                    "0x48": {
                        "VCO_MUX": 0b10         # 6:5位 = 10
                    },
                    "0x83": {
                        "PLL2_PRE_PD": 1,       # 6位 = 1
                        "PLL2_PD": 1            # 5位 = 1
                    }
                }
            }
            
            # 检查模式是否存在
            if mode_name not in register_settings:
                logger.error(f"未找到模式 {mode_name} 的配置")
                return
                
            # 设置选定模式的寄存器值
            mode_settings = register_settings[mode_name]
            
            # 遍历需要修改的寄存器地址
            for reg_addr, bit_settings in mode_settings.items():
                # 遍历每个寄存器中需要修改的位段
                for bit_name, bit_value in bit_settings.items():
                    # 设置寄存器位段值
                    normalized_addr = self._normalize_register_address(reg_addr)
                    
                    # 获取寄存器对象
                    register = self.registers.get(normalized_addr)
                    if register is None:
                        logger.warning(f"寄存器 {normalized_addr} 不存在，跳过设置")
                        continue
                        
                    # 设置位段值
                    register.set_bit_field_value(bit_name, bit_value)
                    
                    # 发送寄存器更新信号
                    updated_value = register.value
                    RegisterUpdateBus.instance().register_updated.emit(normalized_addr, updated_value)
                    logger.info(f"设置寄存器 {normalized_addr} 位段 {bit_name} = {bit_value}, 寄存器完整值 = 0x{updated_value:04X}")
            
            logger.info(f"模式 {mode_name} 设置完成")
            
            # 发送模式变化信号
            RegisterUpdateBus.instance().mode_changed.emit(mode_name)
            
        except Exception as e:
            logger.error(f"设置模式时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            
    def _normalize_register_address(self, addr):
        """标准化寄存器地址格式
        
        Args:
            addr: 寄存器地址，可以是字符串或整数
            
        Returns:
            str: 标准化后的寄存器地址，格式为0xXX
        """
        if isinstance(addr, str):
            addr = addr.strip().lower().replace(' ', '')
            addr_num = int(addr, 16) if addr.startswith('0x') else int(addr, 16)
            return f"0x{addr_num:02X}"
        return f"0x{addr:02X}"

    def closeEvent(self, event):
        """重写closeEvent方法，发出window_closed信号"""
        self.window_closed.emit()
        event.accept()

if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    set_modes = SetModesHandler()
    set_modes.show()
    sys.exit(app.exec_())