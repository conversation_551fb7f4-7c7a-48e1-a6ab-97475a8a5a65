class Register:
    def __init__(self, address, bits):
        self.address = address
        self.bits = bits
        self.bit_field_values = {bit['name']: int(bit['default'], 2) for bit in bits}
        self._value = self._calculate_register_value()

    @property
    def value(self):
        return self._value

    @value.setter
    def value(self, new_value):
        self._value = new_value
        self._update_bit_field_values()

    def _update_bit_field_values(self):
        """更新位段的值"""
        for bit in self.bits:
            bit_range = bit["bit"]
            start = self.get_bit_start(bit_range)
            mask = self.get_bit_mask(bit_range)
            
            # 从寄存器值中提取位段值：先右移到最低位，然后应用掩码
            # 确保只获取掩码定义的位
            bit_value = (self._value >> start) & mask
            
            # 更新位段值字典
            self.bit_field_values[bit['name']] = bit_value

    def _calculate_register_value(self):
        """计算整个寄存器的值"""
        register_value = 0
        for bit in self.bits:
            bit_range = bit["bit"]
            start = self.get_bit_start(bit_range)
            mask = self.get_bit_mask(bit_range)
            value = self.bit_field_values[bit['name']]
            # 确保值不超过位字段的范围
            if value > mask:
                # 如果值超出范围，截断为掩码大小
                value = value & mask

            # 将值左移到正确的位置并与寄存器值进行OR操作
            register_value |= (value << start)
        return register_value

    def get_bit_field_value(self, bit_name):
        """获取指定位字段的值"""
        return self.bit_field_values.get(bit_name, None)

    def set_bit_field_value(self, bit_name, value):
        """设置指定位字段的值并更新寄存器值"""
        for bit in self.bits:
            if bit['name'] == bit_name:
                if self._validate_value(bit, value):
                    # 获取当前位字段的位范围和起始位
                    bit_range = bit["bit"]
                    start = self.get_bit_start(bit_range)
                    mask = self.get_bit_mask(bit_range)
                    
                    # 更新位字段值字典
                    self.bit_field_values[bit_name] = value
                    
                    # 清除寄存器值中对应位字段的比特位
                    clear_mask = ~(mask << start)
                    self._value = self._value & clear_mask
                    
                    # 设置新的位字段值
                    self._value |= (value & mask) << start
                else:
                    raise ValueError(f"Invalid value for {bit_name}")
                break

    def _validate_value(self, bit, value):
        """验证值是否在允许范围内"""
        if not bit.get('options'):
            return True
            
        options = bit['options']
        # 处理离散选项值，如 "0,1,2,4"
        if ',' in options:
            try:
                valid_values = [int(v.strip()) for v in options.split(',')]
                return value in valid_values
            except ValueError as e:
                # 如果转换失败，记录错误并返回False
                import logging
                logging.error(f"选项值转换错误: {options}, 尝试转换: {value}, 错误: {e}")
                return False
        # 处理范围选项值，如 "0:15"
        elif ':' in options:
            try:
                min_val, max_val = map(int, options.split(':'))
                return min_val <= value <= max_val
            except ValueError as e:
                import logging
                logging.error(f"范围选项转换错误: {options}, 值: {value}, 错误: {e}")
                return False
        # 处理单个选项值
        else:
            try:
                return value == int(options)
            except ValueError as e:
                import logging
                logging.error(f"单个选项转换错误: {options}, 值: {value}, 错误: {e}")
                return False

    def to_dict(self):
        """将寄存器转换为字典格式"""
        return {
            'address': self.address,
            'bit_field_values': self.bit_field_values,
            'value': self.value
        }

    def get_bit_start(self, bit_range):
        """获取位字段的起始位位置
        
        Args:
            bit_range: 位范围，如 "5:0" 或 "5"
            
        Returns:
            int: 位字段的起始位位置
        """
        if ':' in bit_range:
            # 对于类似 "5:0" 的范围，起始位是较小的位数值
            high_bit, low_bit = map(int, bit_range.split(':'))
            return min(high_bit, low_bit) 
        return int(bit_range)  # 对于单个位，如 "5"，起始位就是该位
        
    def get_bit_mask(self, bit_range):
        """计算位字段的掩码
        
        Args:
            bit_range: 位范围，如 "5:0" 或 "5"
            
        Returns:
            int: 位字段的掩码
        """
        if ':' in bit_range:
            # 对于类似 "5:0" 的范围，计算掩码
            high_bit, low_bit = map(int, bit_range.split(':'))
            # 确保高位和低位被正确识别
            high = max(high_bit, low_bit)
            low = min(high_bit, low_bit)
            # 创建一个掩码，宽度为high-low+1
            mask = (1 << (high - low + 1)) - 1
            return mask
        return 1  # 对于单个位，如 "5"，掩码就是1