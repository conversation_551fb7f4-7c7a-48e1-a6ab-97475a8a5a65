#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PLL控制功能测试
测试PLL相关的所有功能，包括现代化和传统处理器
"""

import sys
import os
import unittest

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

# 添加test_suite到路径
test_suite_path = os.path.join(project_root, 'test_suite')
sys.path.insert(0, test_suite_path)

from test_utils import TestLogger, TestResult, qt_application, MockRegisterManager
from test_config import get_test_config

class TestPLLControl(unittest.TestCase):
    """PLL控制测试类"""
    
    def setUp(self):
        """测试设置"""
        self.logger = TestLogger("pll_control_test")
        self.register_manager = MockRegisterManager()
        self.test_results = []
    
    def tearDown(self):
        """测试清理"""
        pass
    
    def test_modern_pll_handler_creation(self):
        """测试现代化PLL处理器创建"""
        result = TestResult("modern_pll_handler_creation")
        
        try:
            with qt_application():
                from ui.handlers.ModernPLLHandler import ModernPLLHandler
                
                # 创建处理器
                handler = ModernPLLHandler(None, self.register_manager)
                
                # 验证创建成功
                self.assertIsNotNone(handler)
                self.assertEqual(type(handler).__name__, 'ModernPLLHandler')
                
                # 验证UI对象
                self.assertTrue(hasattr(handler, 'ui'))
                
                # 验证控件映射
                self.assertTrue(hasattr(handler, 'widget_register_map'))
                self.assertGreater(len(handler.widget_register_map), 0)
                
                result.add_detail('handler_type', type(handler).__name__)
                result.add_detail('widget_count', len(handler.widget_register_map))
                result.set_success(True)
                
        except Exception as e:
            result.set_error(str(e))
            self.logger.error(f"现代化PLL处理器创建失败: {e}")
        
        self.test_results.append(result)
        self.assertTrue(result.success)
    
    def test_pll_status_operations(self):
        """测试PLL状态操作"""
        result = TestResult("pll_status_operations")
        
        try:
            with qt_application():
                from ui.handlers.ModernPLLHandler import ModernPLLHandler
                
                handler = ModernPLLHandler(None, self.register_manager)
                
                # 测试获取初始状态
                initial_status = handler.get_current_status()
                self.assertIsInstance(initial_status, dict)
                
                # 验证状态字段
                expected_fields = ["pll1_enabled", "pll2_enabled", "vco_enabled", "current_clock_source"]
                for field in expected_fields:
                    self.assertIn(field, initial_status)
                
                result.add_detail('initial_status', initial_status)
                result.set_success(True)
                
        except Exception as e:
            result.set_error(str(e))
            self.logger.error(f"PLL状态操作测试失败: {e}")
        
        self.test_results.append(result)
        self.assertTrue(result.success)
    
    def test_pll_preset_functionality(self):
        """测试PLL预设功能"""
        result = TestResult("pll_preset_functionality")
        
        try:
            with qt_application():
                from ui.handlers.ModernPLLHandler import ModernPLLHandler
                
                handler = ModernPLLHandler(None, self.register_manager)
                
                # 测试默认预设
                handler.set_pll_preset("default")
                
                # 验证PLL状态
                pll1_pd = self.register_manager.get_bit_field_value("0x50", "PLL1_PD")
                pll2_pd = self.register_manager.get_bit_field_value("0x83", "PLL2_PD")
                vco_pd = self.register_manager.get_bit_field_value("0x50", "VCO_PD")
                
                # 默认预设应该启用所有PLL
                self.assertEqual(pll1_pd, 0, "PLL1应该被启用")
                self.assertEqual(pll2_pd, 0, "PLL2应该被启用")
                self.assertEqual(vco_pd, 0, "VCO应该被启用")
                
                # 测试低功耗预设
                handler.set_pll_preset("low_power")
                
                pll1_pd = self.register_manager.get_bit_field_value("0x50", "PLL1_PD")
                pll2_pd = self.register_manager.get_bit_field_value("0x83", "PLL2_PD")
                vco_pd = self.register_manager.get_bit_field_value("0x50", "VCO_PD")
                
                # 低功耗预设应该禁用所有PLL
                self.assertEqual(pll1_pd, 1, "PLL1应该被禁用")
                self.assertEqual(pll2_pd, 1, "PLL2应该被禁用")
                self.assertEqual(vco_pd, 1, "VCO应该被禁用")
                
                result.add_detail('default_preset_test', 'passed')
                result.add_detail('low_power_preset_test', 'passed')
                result.set_success(True)
                
        except Exception as e:
            result.set_error(str(e))
            self.logger.error(f"PLL预设功能测试失败: {e}")
        
        self.test_results.append(result)
        self.assertTrue(result.success)
    
    def test_clock_source_update(self):
        """测试时钟源更新"""
        result = TestResult("clock_source_update")
        
        try:
            with qt_application():
                from ui.handlers.ModernPLLHandler import ModernPLLHandler
                
                handler = ModernPLLHandler(None, self.register_manager)
                
                # 测试时钟源更新
                test_freq = 245.76
                test_div = 2
                handler.update_clock_source("ClkIn1", test_freq, test_div)
                
                # 验证更新结果
                self.assertEqual(handler.current_clock_source, "ClkIn1")
                self.assertEqual(handler.clkin_frequencies.get("ClkIn1"), test_freq)
                self.assertEqual(handler.clkin_divider_values.get("ClkIn1"), test_div)
                
                result.add_detail('clock_source', "ClkIn1")
                result.add_detail('frequency', test_freq)
                result.add_detail('divider', test_div)
                result.set_success(True)
                
        except Exception as e:
            result.set_error(str(e))
            self.logger.error(f"时钟源更新测试失败: {e}")
        
        self.test_results.append(result)
        self.assertTrue(result.success)
    
    def test_frequency_calculation(self):
        """测试频率计算"""
        result = TestResult("frequency_calculation")
        
        try:
            with qt_application():
                from ui.handlers.ModernPLLHandler import ModernPLLHandler
                
                handler = ModernPLLHandler(None, self.register_manager)
                
                # 设置默认预设以确保PLL开启
                handler.set_pll_preset("default")
                
                # 执行频率计算
                handler.calculate_output_frequencies()
                
                # 验证计算完成（不会抛出异常）
                result.add_detail('calculation_completed', True)
                result.set_success(True)
                
        except Exception as e:
            result.set_error(str(e))
            self.logger.error(f"频率计算测试失败: {e}")
        
        self.test_results.append(result)
        self.assertTrue(result.success)
    
    def test_widget_value_changes(self):
        """测试控件值变化处理"""
        result = TestResult("widget_value_changes")
        
        try:
            with qt_application():
                from ui.handlers.ModernPLLHandler import ModernPLLHandler
                
                handler = ModernPLLHandler(None, self.register_manager)
                
                # 测试PLL1电源控制变化
                if "PLL1PD" in handler.widget_register_map:
                    handler._on_widget_changed("PLL1PD", True)
                    result.add_detail('pll1pd_change', 'success')
                
                # 测试分频器变化
                if "PLL1RDividerSetting" in handler.widget_register_map:
                    handler._on_widget_changed("PLL1RDividerSetting", 2)
                    result.add_detail('divider_change', 'success')
                
                result.set_success(True)
                
        except Exception as e:
            result.set_error(str(e))
            self.logger.error(f"控件值变化测试失败: {e}")
        
        self.test_results.append(result)
        self.assertTrue(result.success)

def run_tests():
    """运行PLL控制测试"""
    suite = unittest.TestLoader().loadTestsFromTestCase(TestPLLControl)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
