# -*- coding: utf-8 -*-

from math import log
from PyQt5 import QtCore, QtWidgets
from ..forms.Ui_ClkOutputs import Ui_ClkOutputs
from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
from ui.handlers.BaseHandler import BaseClockHandler
from functools import partial
from utils.Log import get_page_logger
logger = get_page_logger("ClkOutputs")


class ClkOutputsHandler(BaseClockHandler):
    """时钟输出配置界面处理类"""

    # 添加一个关闭信号
    window_closed = QtCore.pyqtSignal()

    def __init__(self, parent=None, registers=None):
        super(ClkOutputsHandler, self).__init__(parent, registers)

        # 设置窗口标题
        self.setWindowTitle("时钟输出配置")

        # 设置UI到内容widget
        self.ui = Ui_ClkOutputs()
        self.ui.setupUi(self.content_widget)

        # 初始化SRCMUX状态字典，记录每个输出的SRCMUX复选框状态
        self.srcmux_states = {i: False for i in range(14)}

        # 存储对SyncSysRefHandler的引用
        self.sync_sysref_handler = None

        # 初始化特定配置
        self.init_clk_config()
        
        # 强制检查一次DCLK0_1DIV寄存器值，确保初始化时正确设置
        self.debug_check_dclk01div()
        
        # 创建一个定时器，定期检查寄存器值是否有变化
        self._init_auto_sync_timer()
        
        # 手动连接RegisterUpdateBus的信号，确保信号传递正常
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            print("手动连接RegisterUpdateBus信号...")
            # 断开可能存在的旧连接
            try:
                RegisterUpdateBus.instance().register_updated.disconnect(self.on_global_register_updated)
            except:
                pass
            # 重新连接
            RegisterUpdateBus.instance().register_updated.connect(self.on_global_register_updated)
            print("RegisterUpdateBus信号连接成功")
        except Exception as e:
            print(f"连接RegisterUpdateBus信号时出错: {e}")
            import traceback
            traceback.print_exc()

    def init_clk_config(self):
        """初始化时钟输出特定配置"""
        # 定义格式名称映射字典
        self.combobox_options_map = {
            "CLKXFMT": {
                0: "Powerdown",
                1: "LVDS",
                2: "HSDS 6 mA",
                3: "HSDS 8 mA",
                4: "LVPECL 1600 mV",
                5: "LVPECL 2000 mV",
                6: "LCPECL",
                7: "CML 16 mA",
                8: "CML 24 mA",
                9: "CML 32 mA",
                10: "CMOS (Off/Inv)",
                11: "CMOS (Norm/Off)",
                12: "CMOS (Inv/Inv)",
                13: "CMOS (Inv/Norm)",
                14: "CMOS (Norm/Inv)",
                15: "CMOS (Norm/Norm)"
            }
        }

        print("=== 初始化时钟输出配置 ===")

        # 设置Fvco默认值为2949.12
        self.ui.lineEditFvco.setText("2949.12")
        # 连接Fvco输入框的信号
        self.ui.lineEditFvco.returnPressed.connect(self.calculate_output_frequencies)
        # 初始化Fvco值
        self.fvco = 0.0

        # 创建控件与寄存器的映射表
        self.create_widget_register_map()

        # 初始化界面控件
        self.initialize_widgets()

        # 连接控件信号
        self.connect_widget_signals()

        # 计算所有输出频率
        self.calculate_output_frequencies()

        # 连接所有奇数输出的SRCMUX复选框信号
        self.connect_srcmux_signals()
        
        # 连接模式变化信号
        RegisterUpdateBus.instance().mode_changed.connect(self.on_mode_changed)
        
        print("=== 初始化时钟输出配置完成 ===")

    def handle_register_value_change(self, widget_name):
        """处理寄存器值变化的回调函数"""
        self.update_frequency_for_divider(widget_name)

    def handle_global_register_update(self, reg_addr, reg_value):
        """处理全局寄存器更新的附加效果"""
        # 计算所有输出频率
        self.calculate_output_frequencies()

    def connect_srcmux_signals(self):
        """连接所有奇数输出的SRCMUX复选框信号"""
        try:
            # 为每个输出编号单独创建连接
            for output_num in range(14):
                srcmux_checkbox = getattr(self.ui, f"CLKout{output_num}_SRCMUX")
                # 使用functools.partial确保每个连接使用正确的output_num值
                srcmux_checkbox.stateChanged.connect(
                    partial(self.update_srcmux_output, output_num)
                )
            logger.debug("已连接所有时钟输出的SRCMUX复选框信号")
        except Exception as e:
            logger.error("连接SRCMUX信号时发生错误", exc_info=True)

    def set_sync_sysref_handler(self, handler):
        """设置系统参考处理器的引用

        Args:
            handler: SyncSysRefHandler实例
        """
        self.sync_sysref_handler = handler

        # 连接系统参考处理器的分频比变化信号
        if self.sync_sysref_handler:
            # 连接系统参考分频比变化时需要更新输出
            self.sync_sysref_handler.ui.spinBoxSysrefDIV.valueChanged.connect(
                self.update_srcmux_outputs
            )

    def update_srcmux_outputs(self):
        """更新所有使用SRCMUX的输出频率"""
        try:
            # 检查是否已经设置了系统参考处理器
            if not self.sync_sysref_handler:
                logger.warning("系统参考处理器未设置，无法更新SRCMUX输出")
                return

            # 获取系统参考频率
            fvco_text = self.sync_sysref_handler.ui.InternalVCOFreq.text()
            if not fvco_text:
                logger.warning("无法获取系统参考的VCO频率")
                return

            # 转换为浮点数
            fvco = float(fvco_text)

            # 获取系统参考分频比
            sysref_div = self.sync_sysref_handler.ui.spinBoxSysrefDIV.value()
            if sysref_div <= 0:
                logger.warning("系统参考分频比无效")
                return

            # 计算系统参考频率
            sysref_freq = fvco / sysref_div

            # 更新所有使用SRCMUX的输出
            for output_num, is_srcmux in self.srcmux_states.items():
                if is_srcmux:
                    output_widget = getattr(self.ui, f"lineEditFout{output_num}Output")
                    output_widget.setText(f"{sysref_freq:.2f}")
                    print(f"已将Fout{output_num}的值更新为系统参考频率: {sysref_freq:.2f} MHz (FVCO={fvco} / SysrefDIV={sysref_div})")
        except Exception as e:
            print(f"更新SRCMUX输出时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def update_srcmux_output(self, output_num, state):
        """更新SRCMUX控制的输出频率

        Args:
            output_num: 输出编号（0-13）
            state: 复选框状态
        """
        try:
            # 保存当前复选框状态
            self.srcmux_states[output_num] = (state == QtCore.Qt.Checked)

            # 获取对应的输出控件
            output_widget = getattr(self.ui, f"lineEditFout{output_num}Output")

            if state == QtCore.Qt.Checked:
                # 使用系统参考时钟
                if self.sync_sysref_handler:
                    # 获取系统参考频率信息
                    fvco_text = self.sync_sysref_handler.ui.InternalVCOFreq.text()
                    if not fvco_text:
                        print("无法获取系统参考的VCO频率")
                        return

                    fvco = float(fvco_text)

                    # 获取系统参考分频比
                    sysref_div = self.sync_sysref_handler.ui.spinBoxSysrefDIV.value()
                    if sysref_div <= 0:
                        print("系统参考分频比无效")
                        return

                    # 计算系统参考频率
                    sysref_freq = fvco / sysref_div

                    # 更新输出值
                    output_widget.setText(f"{sysref_freq:.2f}")
                    print(f"已将Fout{output_num}的值更新为系统参考频率: {sysref_freq:.2f} MHz (FVCO={fvco} / SysrefDIV={sysref_div})")
                else:
                    print("未连接到系统参考处理器，无法获取系统参考频率")
            else:
                # 如果取消选中，恢复原来的计算方式
                self.update_output_frequency(output_widget)
                print(f"已将Fout{output_num}恢复为默认计算方式")
        except Exception as e:
            print(f"更新Fout{output_num}值时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def get_all_output_widgets(self):
        """获取所有输出频率控件的列表"""
        return [
            self.ui.lineEditFout0Output,
            self.ui.lineEditFout1Output,
            self.ui.lineEditFout2Output,
            self.ui.lineEditFout3Output,
            self.ui.lineEditFout4Output,
            self.ui.lineEditFout5Output,
            self.ui.lineEditFout6Output,
            self.ui.lineEditFout7Output,
            self.ui.lineEditFout8Output,
            self.ui.lineEditFout9Output,
            self.ui.lineEditFout10Output,
            self.ui.lineEditFout11Output,
            self.ui.lineEditFout12Output,
            self.ui.lineEditFout13Output
        ]

    def calculate_output_frequencies(self, specific_outputs=None):
        """根据Fvco值计算所有输出频率，尊重SRCMUX状态"""
        try:
            # 获取Fvco输入值
            fvco_text = self.ui.lineEditFvco.text()
            if not fvco_text:
                return

            # 转换为浮点数
            self.fvco = float(fvco_text)

            # 获取所有输出控件
            all_outputs = self.get_all_output_widgets()

            # 先从UI控件更新所有SRCMUX的状态
            for output_num in range(14):
                srcmux_checkbox = getattr(self.ui, f"CLKout{output_num}_SRCMUX")
                self.srcmux_states[output_num] = (srcmux_checkbox.checkState() == QtCore.Qt.Checked)

            # 更新指定输出或所有输出
            if specific_outputs:
                for output in specific_outputs:
                    # 获取输出编号
                    name = output.objectName()
                    output_num = int(name.replace("lineEditFout", "").replace("Output", ""))

                    # 如果SRCMUX启用，使用系统参考计算；否则使用正常计算
                    if self.srcmux_states.get(output_num, False):
                        if self.sync_sysref_handler:
                            # 获取系统参考频率信息
                            sysref_fvco_text = self.sync_sysref_handler.ui.InternalVCOFreq.text()
                            if sysref_fvco_text:
                                sysref_fvco = float(sysref_fvco_text)
                                sysref_div = self.sync_sysref_handler.ui.spinBoxSysrefDIV.value()
                                if sysref_div > 0:
                                    sysref_freq = sysref_fvco / sysref_div
                                    output.setText(f"{sysref_freq:.2f}")
                                    continue
                    # 正常计算
                    self.update_output_frequency(output)
            else:
                for i, output in enumerate(all_outputs):
                    # 如果SRCMUX启用，使用系统参考计算；否则使用正常计算
                    if self.srcmux_states.get(i, False):
                        if self.sync_sysref_handler:
                            # 获取系统参考频率信息
                            sysref_fvco_text = self.sync_sysref_handler.ui.InternalVCOFreq.text()
                            if sysref_fvco_text:
                                sysref_fvco = float(sysref_fvco_text)
                                sysref_div = self.sync_sysref_handler.ui.spinBoxSysrefDIV.value()
                                if sysref_div > 0:
                                    sysref_freq = sysref_fvco / sysref_div
                                    output.setText(f"{sysref_freq:.2f}")
                                    continue
                    # 正常计算
                    self.update_output_frequency(output)

        except ValueError:
            self.show_error_message("输入错误", f"输入的Fvco值无效: '{fvco_text}'", "请输入有效的Fvco数值")
        except Exception as e:
            self.show_error_message("计算错误", f"计算输出频率时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def get_divider_value(self, divider_name):
        """从寄存器配置中获取分频值"""
        try:
            # 首先尝试从UI控件获取当前值
            try:
                widget = getattr(self.ui, divider_name)
                if isinstance(widget, QtWidgets.QSpinBox):
                    value = widget.value()
                    # 确保分频值至少为1，避免除以0错误
                    return max(1, value)
            except AttributeError:
                print(f"控件 {divider_name} 不存在，使用映射表方法")
                pass  # 如果控件不存在，继续使用映射表方法

            # 使用寄存器值获取分频值 - 直接访问寄存器获取当前值
            register_map = {
                "DCLK0_1DIV": "0x10",
                "DCLK2_3DIV": "0x18",
                "DCLK4_5DIV": "0x20",
                "DCLK6_7DIV": "0x28",
                "DCLK8_9DIV": "0x30",
                "DCLK10_11DIV": "0x38",
                "DCLK12_13DIV": "0x40"
            }
            
            if divider_name in register_map and register_map[divider_name] in self.registers:
                reg_addr = register_map[divider_name]
                print(f"从寄存器 {reg_addr} 获取 {divider_name} 值")
                
                # 获取寄存器对象
                register = self.registers[reg_addr]
                
                # 查找对应的位字段名
                bit_field_name = None
                for bit_def in register.bits:
                    if bit_def.get("widget_name") == divider_name:
                        bit_field_name = bit_def["name"]
                        break
                
                if bit_field_name:
                    # 获取位字段当前值
                    bit_value = register.get_bit_field_value(bit_field_name)
                    print(f"从寄存器获取 {divider_name} 值: {bit_value}")
                    return max(1, bit_value)  # 确保分频值至少为1
                
                # 如果没找到位字段名，尝试使用默认方法
                for bit_def in register.bits:
                    if bit_def.get("widget_name") == divider_name:
                        binary_value = bit_def["default"]
                        decimal_value = int(binary_value, 2)
                        print(f"找到 {divider_name} 的位定义: 二进制={binary_value}, 十进制={decimal_value}")
                        return max(1, decimal_value)
            
            # 如果仍然没有找到，使用映射表方法
            if divider_name in self.widget_register_map:
                widget_info = self.widget_register_map[divider_name]
                binary_value = widget_info["default_value"]
                decimal_value = int(binary_value, 2)
                print(f"从映射表获取 {divider_name} 值: 二进制={binary_value}, 十进制={decimal_value}")
                return max(1, decimal_value)

            # 如果找不到配置，返回默认值
            print(f"未找到 {divider_name} 的配置，使用默认值 1")
            return 1
        except Exception as e:
            print(f"获取分频值出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return 1

    # 获取分频器名称
    def get_divider_name(self, output_name):
        """根据输出线路名称获取对应的分频器名称"""
        # 示例: lineEditFout0Output -> DCLK0_1DIV
        output_num = output_name.replace("lineEditFout", "").replace("Output", "")
        output_num = int(output_num)

        # 确定分频器名称
        if output_num in [0, 1]:
            return "DCLK0_1DIV"
        elif output_num in [2, 3]:
            return "DCLK2_3DIV"
        elif output_num in [4, 5]:
            return "DCLK4_5DIV"
        elif output_num in [6, 7]:
            return "DCLK6_7DIV"
        elif output_num in [8, 9]:
            return "DCLK8_9DIV"
        elif output_num in [10, 11]:
            return "DCLK10_11DIV"
        elif output_num in [12, 13]:
            return "DCLK12_13DIV"
        else:
            return "DCLK0_1DIV"  # 默认返回第一个分频器

    def update_frequency_for_divider(self, widget_name):
        """根据分频器控件名称更新相关的输出频率

        Args:
            widget_name: 控件名称
        """
        # 先从UI控件更新所有SRCMUX的状态
        for output_num in range(14):
            srcmux_checkbox = getattr(self.ui, f"CLKout{output_num}_SRCMUX")
            self.srcmux_states[output_num] = (srcmux_checkbox.checkState() == QtCore.Qt.Checked)

        if "DCLK0_1DIV" in widget_name:
            if not self.srcmux_states.get(0, False):
                self.update_output_frequency(self.ui.lineEditFout0Output)
            if not self.srcmux_states.get(1, False):
                self.update_output_frequency(self.ui.lineEditFout1Output)
        elif "DCLK2_3DIV" in widget_name:
            if not self.srcmux_states.get(2, False):
                self.update_output_frequency(self.ui.lineEditFout2Output)
            if not self.srcmux_states.get(3, False):
                self.update_output_frequency(self.ui.lineEditFout3Output)
        elif "DCLK4_5DIV" in widget_name:
            if not self.srcmux_states.get(4, False):
                self.update_output_frequency(self.ui.lineEditFout4Output)
            if not self.srcmux_states.get(5, False):
                self.update_output_frequency(self.ui.lineEditFout5Output)
        elif "DCLK6_7DIV" in widget_name:
            if not self.srcmux_states.get(6, False):
                self.update_output_frequency(self.ui.lineEditFout6Output)
            if not self.srcmux_states.get(7, False):
                self.update_output_frequency(self.ui.lineEditFout7Output)
        elif "DCLK8_9DIV" in widget_name:
            if not self.srcmux_states.get(8, False):
                self.update_output_frequency(self.ui.lineEditFout8Output)
            if not self.srcmux_states.get(9, False):
                self.update_output_frequency(self.ui.lineEditFout9Output)
        elif "DCLK10_11DIV" in widget_name:
            if not self.srcmux_states.get(10, False):
                self.update_output_frequency(self.ui.lineEditFout10Output)
            if not self.srcmux_states.get(11, False):
                self.update_output_frequency(self.ui.lineEditFout11Output)
        elif "DCLK12_13DIV" in widget_name:
            if not self.srcmux_states.get(12, False):
                self.update_output_frequency(self.ui.lineEditFout12Output)
            if not self.srcmux_states.get(13, False):
                self.update_output_frequency(self.ui.lineEditFout13Output)

    def update_output_frequency(self, output_line_edit):
        """更新特定输出的频率显示

        根据FVCO/DCLK0_1DIV公式计算输出频率
        """
        if self.fvco <= 0:
            return

        # 获取输出线路的名称，用于确定分频比
        name = output_line_edit.objectName()
        output_num = int(name.replace("lineEditFout", "").replace("Output", ""))

        # 检查该输出是否使用SRCMUX
        if self.srcmux_states.get(output_num, False):
            # 如果使用SRCMUX，应该使用系统参考频率，不需要在这里更新
            return

        divider_name = self.get_divider_name(name)
        divider = 1.0

        # 如果没有直接设置divider值，则通过get_divider_value获取
        if divider == 1.0:
            divider = self.get_divider_value(divider_name)

        # 计算输出频率
        output_freq = self.fvco / divider
        # 更新显示，保留2位小数
        output_line_edit.setText(f"{output_freq:.2f}")
        # 调试输出
        print(f"{name}输出频率: {output_freq:.2f} MHz")

    def on_mode_changed(self, mode_name):
        """处理模式变化信号"""
        logger.info(f"ClkOutputsHandler 收到模式变化信号: {mode_name}")
        # 存储当前模式
        self.current_mode = mode_name
        # 根据模式选择计算逻辑并更新UI
        self.calculate_output_frequencies()

    def update_output_frequency(self, output_line_edit):
        """更新特定输出的频率显示

        根据FVCO/DCLK0_1DIV公式计算输出频率
        """
        if self.fvco <= 0:
            return

        # 获取输出线路的名称，用于确定分频比
        name = output_line_edit.objectName()
        output_num = int(name.replace("lineEditFout", "").replace("Output", ""))

        # 检查该输出是否使用SRCMUX
        if self.srcmux_states.get(output_num, False):
            # 如果使用SRCMUX，应该使用系统参考频率，不需要在这里更新
            return

        divider_name = self.get_divider_name(name)
        divider = 1.0

        # 如果没有直接设置divider值，则通过get_divider_value获取
        if divider == 1.0:
            divider = self.get_divider_value(divider_name)

        # 计算输出频率
        output_freq = self.fvco / divider
        # 更新显示，保留2位小数
        output_line_edit.setText(f"{output_freq:.2f}")
        # 调试输出
        print(f"{name}输出频率: {output_freq:.2f} MHz")

    def closeEvent(self, event):
        """重写closeEvent方法，发出关闭信号"""
        # 停止定时器
        if hasattr(self, "sync_timer"):
            self.sync_timer.stop()
            
        self.window_closed.emit()
        event.accept()

    def initialize_widgets(self):
        """重写初始化控件方法，确保从寄存器获取最新值"""
        # 调用父类的初始化方法
        super().initialize_widgets()
        
        # 从寄存器获取最新的分频值并设置到控件
        self.update_divider_controls_from_registers()
    
    def update_divider_controls_from_registers(self):
        """从寄存器获取最新值更新所有分频控件"""
        try:
            divider_names = [
                "DCLK0_1DIV", "DCLK2_3DIV", "DCLK4_5DIV", 
                "DCLK6_7DIV", "DCLK8_9DIV", "DCLK10_11DIV", 
                "DCLK12_13DIV"
            ]
            
            register_map = {
                "DCLK0_1DIV": "0x10",
                "DCLK2_3DIV": "0x18",
                "DCLK4_5DIV": "0x20",
                "DCLK6_7DIV": "0x28",
                "DCLK8_9DIV": "0x30",
                "DCLK10_11DIV": "0x38",
                "DCLK12_13DIV": "0x40"
            }
            
            updated_dividers = []
            
            for divider_name in divider_names:
                if hasattr(self.ui, divider_name):
                    reg_addr = register_map.get(divider_name)
                    if reg_addr and reg_addr in self.registers:
                        register = self.registers[reg_addr]
                        
                        # 查找对应的位字段
                        for bit_def in register.bits:
                            if bit_def.get("widget_name") == divider_name:
                                # 获取当前寄存器值
                                bit_field_name = bit_def["name"]
                                current_value = register.get_bit_field_value(bit_field_name)
                                
                                # 更新控件值
                                divider_widget = getattr(self.ui, divider_name)
                                if isinstance(divider_widget, QtWidgets.QSpinBox):
                                    divider_widget.blockSignals(True)
                                    divider_widget.setValue(current_value)
                                    divider_widget.blockSignals(False)
                                    print(f"从寄存器更新 {divider_name} 控件值为: {current_value}")
                                    updated_dividers.append(divider_name)
                                break
            
            # 重新计算所有输出频率
            if updated_dividers:
                print(f"已更新分频器控件: {', '.join(updated_dividers)}, 重新计算输出频率")
                self.calculate_output_frequencies()
                
        except Exception as e:
            print(f"更新分频控件值时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def on_global_register_updated(self, reg_addr, reg_value):
        """重写全局寄存器更新方法，确保分频值正确更新"""
        # 先保存被改变的寄存器地址
        print(f"===== ClkOutputsHandler收到全局寄存器更新 =====")
        print(f"寄存器={reg_addr}, 值={reg_value:04X}")
        
        # 记录这个地址是否已经处理，避免重复处理
        register_processed = False
        
        # 检查是否是分频相关寄存器
        register_map = {
            "0x10": "DCLK0_1DIV",
            "0x18": "DCLK2_3DIV", 
            "0x20": "DCLK4_5DIV",
            "0x28": "DCLK6_7DIV", 
            "0x30": "DCLK8_9DIV", 
            "0x38": "DCLK10_11DIV",
            "0x40": "DCLK12_13DIV"
        }
        
        # 测试寄存器对象是否已同步更新
        if reg_addr in self.registers:
            register = self.registers[reg_addr]
            print(f"寄存器对象 {reg_addr} 更新前值: {register.value:04X}")
            
            # 显示位字段当前值
            if reg_addr == "0x10":
                for bit in register.bits:
                    if bit.get("widget_name") == "DCLK0_1DIV":
                        field_name = bit["name"]
                        current_bit_value = register.get_bit_field_value(field_name)
                        print(f"更新前 DCLK0_1DIV 位字段 {field_name} 值: {current_bit_value}")
        
        # 直接更新寄存器对象中的值 - 强制重新加载
        if reg_addr in self.registers:
            # 强制更新整个寄存器值
            old_value = self.registers[reg_addr].value
            self.registers[reg_addr].value = reg_value
            print(f"强制更新寄存器值: {old_value:04X} -> {reg_value:04X}")
            
            # 重新获取位字段值
            if reg_addr == "0x10":
                for bit in self.registers[reg_addr].bits:
                    if bit.get("widget_name") == "DCLK0_1DIV":
                        field_name = bit["name"]
                        updated_bit_value = self.registers[reg_addr].get_bit_field_value(field_name)
                        print(f"更新后 DCLK0_1DIV 位字段 {field_name} 值: {updated_bit_value}")
        
        # 如果是分频相关寄存器，直接从寄存器获取值更新控件
        if reg_addr in register_map:
            register_processed = True
            divider_name = register_map[reg_addr]
            
            if hasattr(self.ui, divider_name):
                # 首先确保寄存器对象存在且已更新到最新值
                if reg_addr in self.registers:
                    # 查找对应的位字段
                    bit_field_name = None
                    bit_field_value = None
                    
                    for bit_def in self.registers[reg_addr].bits:
                        if bit_def.get("widget_name") == divider_name:
                            bit_field_name = bit_def["name"]
                            # 重新获取位字段值
                            bit_field_value = self.registers[reg_addr].get_bit_field_value(bit_field_name)
                            break
                    
                    if bit_field_name and bit_field_value is not None:
                        # 获取控件，准备更新
                        divider_widget = getattr(self.ui, divider_name)
                        
                        if isinstance(divider_widget, QtWidgets.QSpinBox):
                            # 获取控件当前值
                            old_widget_value = divider_widget.value()
                            print(f"控件 {divider_name} 当前值: {old_widget_value}")
                            print(f"准备更新控件 {divider_name} 到新值: {bit_field_value}")
                            
                            if old_widget_value != bit_field_value:
                                # 临时断开信号连接，防止触发额外的更新
                                divider_widget.blockSignals(True)
                                divider_widget.setValue(bit_field_value)
                                divider_widget.blockSignals(False)
                                print(f"成功更新控件 {divider_name} 值: {old_widget_value} -> {bit_field_value}")
                                
                                # 更新相关的输出频率
                                self.update_frequency_for_divider(divider_name)
                                print(f"已重新计算 {divider_name} 相关的输出频率")
                            else:
                                print(f"控件值与寄存器值相同，无需更新: {old_widget_value}")
        
        print(f"===== 寄存器更新处理完成 =====")
        
        # 如果是我们处理的寄存器，只调用子类处理；否则调用父类方法
        if not register_processed:
            # 调用父类处理其他寄存器
            super().on_global_register_updated(reg_addr, reg_value)
        else:
            # 只调用子类的特定处理方法
            self.handle_global_register_update(reg_addr, reg_value)

    def _init_auto_sync_timer(self):
        """初始化自动同步定时器"""
        from PyQt5.QtCore import QTimer
        
        # 创建定时器
        self.sync_timer = QTimer()
        self.sync_timer.timeout.connect(self.sync_registers_to_ui)
        
        # 记录上次同步的寄存器值
        self.last_register_values = {}
        if "0x10" in self.registers:
            self.last_register_values["0x10"] = self.registers["0x10"].value
        
        # 每秒检查一次
        self.sync_timer.start(1000)  # 1000毫秒 = 1秒
    
    def sync_registers_to_ui(self):
        """同步寄存器值到UI控件"""
        try:
            if "0x10" in self.registers:
                current_value = self.registers["0x10"].value
                last_value = self.last_register_values.get("0x10", None)
                
                # 检查值是否有变化
                if last_value is None or current_value != last_value:
                    print(f"定时同步检测到寄存器0x10值变化: {last_value} -> {current_value}")
                    
                    # 更新控件值
                    for bit in self.registers["0x10"].bits:
                        if bit.get("widget_name") == "DCLK0_1DIV":
                            bit_value = self.registers["0x10"].get_bit_field_value(bit["name"])
                            
                            # 更新控件
                            if hasattr(self.ui, "DCLK0_1DIV"):
                                ctrl_value = self.ui.DCLK0_1DIV.value()
                                if ctrl_value != bit_value:
                                    print(f"自动同步: 更新DCLK0_1DIV控件值 {ctrl_value} -> {bit_value}")
                                    
                                    # 更新控件值
                                    self.ui.DCLK0_1DIV.blockSignals(True)
                                    self.ui.DCLK0_1DIV.setValue(bit_value)
                                    self.ui.DCLK0_1DIV.blockSignals(False)
                                    
                                    # 重新计算相关频率
                                    self.update_frequency_for_divider("DCLK0_1DIV")
                            break
                    
                    # 更新最后同步值
                    self.last_register_values["0x10"] = current_value
        except Exception as e:
            print(f"自动同步寄存器值时出错: {str(e)}")

    def debug_check_dclk01div(self):
        """调试函数，检查DCLK0_1DIV控件与寄存器值的同步状态"""
        print("========== 调试DCLK0_1DIV ==========")
        
        # 检查寄存器状态
        if "0x10" in self.registers:
            reg = self.registers["0x10"]
            print(f"寄存器0x10当前值: {reg.value:04X}")
            
            # 查找DCLK0_1DIV对应的位字段
            for bit in reg.bits:
                if bit.get("widget_name") == "DCLK0_1DIV":
                    field_name = bit["name"]
                    bit_value = reg.get_bit_field_value(field_name)
                    print(f"位字段 {field_name} 值: {bit_value}")
                    
                    # 检查控件值
                    if hasattr(self.ui, "DCLK0_1DIV"):
                        ctrl_value = self.ui.DCLK0_1DIV.value()
                        print(f"控件DCLK0_1DIV当前值: {ctrl_value}")
                        
                        # 如果不一致，强制同步
                        if ctrl_value != bit_value:
                            print(f"值不一致，强制更新控件值: {ctrl_value} -> {bit_value}")
                            self.ui.DCLK0_1DIV.blockSignals(True)
                            self.ui.DCLK0_1DIV.setValue(bit_value)
                            self.ui.DCLK0_1DIV.blockSignals(False)
                            
                            # 重新计算输出频率
                            self.update_frequency_for_divider("DCLK0_1DIV")
                            print("已重新计算输出频率")
                        else:
                            print(f"控件值与寄存器值一致，无需更新")
                    else:
                        print(f"控件DCLK0_1DIV不存在!")
                    
                    break
        else:
            print(f"寄存器 0x10 不存在")
        
        print("========== 调试结束 ==========")

    def ensure_registers_synced_to_ui(self):
        """确保寄存器值同步到UI控件，用于在窗口显示后执行完整初始化"""
        try:
            logger.info("强制同步寄存器值到UI控件...")
            
            # 遍历当前使用的所有寄存器
            for reg_addr, register in self.registers.items():
                reg_value = register.value
                logger.debug(f"寄存器 {reg_addr} 当前值: 0x{reg_value:04X}")
                
                # 查找所有使用此寄存器的控件
                for widget_name, widget_info in self.widget_register_map.items():
                    if widget_info["register_addr"] == reg_addr:
                        try:
                            # 获取控件和位字段信息
                            widget_type = widget_info["widget_type"]
                            bit_def = widget_info["bit_def"]
                            bit_field_name = bit_def.get("name", "")
                            
                            if bit_field_name:
                                # 获取寄存器中对应位段的值
                                bit_value = register.get_bit_field_value(bit_field_name)
                                logger.debug(f"位字段 {bit_field_name} = {bit_value}")
                                
                                # 更新UI控件
                                if hasattr(self.ui, widget_name):
                                    widget = getattr(self.ui, widget_name)
                                    
                                    # 阻断信号以避免循环更新
                                    widget.blockSignals(True)
                                    
                                    # 根据控件类型更新
                                    if widget_type == "checkbox" and isinstance(widget, QtWidgets.QCheckBox):
                                        if widget_name.endswith("Enb"):
                                            widget.setChecked(bit_value == 0)
                                        else:
                                            widget.setChecked(bit_value == 1)
                                            
                                    elif widget_type == "spinbox" and isinstance(widget, QtWidgets.QSpinBox):
                                        widget.setValue(bit_value)
                                        
                                    elif widget_type == "combobox" and isinstance(widget, QtWidgets.QComboBox):
                                        if bit_value < widget.count():
                                            widget.setCurrentIndex(bit_value)
                                            
                                    elif widget_type == "lineedit" and isinstance(widget, QtWidgets.QLineEdit):
                                        widget.setText(str(bit_value))
                                    
                                    # 恢复信号连接
                                    widget.blockSignals(False)
                                    logger.info(f"已同步控件 {widget_name} 的值为 {bit_value}")
                        except Exception as e:
                            logger.error(f"更新控件 {widget_name} 失败: {str(e)}")
            
            # 特别处理分频控件
            self.update_divider_controls_from_registers()
            
            # 触发一次频率计算，确保显示正确
            self.calculate_output_frequencies()
            
            # 更新所有使用SRCMUX的输出
            self.update_srcmux_outputs()
            
            logger.info("寄存器同步到UI控件完成")
            
        except Exception as e:
            logger.error(f"同步寄存器到UI控件时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def showEvent(self, event):
        """窗口显示事件，用于在窗口显示时执行初始化操作
        
        Args:
            event: 事件对象
        """
        # 调用基类的实现
        super().showEvent(event)
        
        try:
            # 强制从寄存器管理器获取最新值并刷新UI
            logger.info("时钟输出窗口显示，正在刷新控件状态...")
            
            # 执行完整的寄存器到UI的同步
            self.ensure_registers_synced_to_ui()
            
            logger.info("时钟输出窗口控件状态刷新完成")
            
            # 启动自动同步定时器（如果启用）
            if hasattr(self, 'auto_sync_timer'):
                self.auto_sync_timer.start(5000)  # 每5秒同步一次
                
        except Exception as e:
            logger.error(f"窗口显示事件处理失败: {str(e)}", exc_info=True)
            import traceback
            traceback.print_exc()