<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ClkinControl</class>
 <widget class="QWidget" name="ClkinControl">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>2031</width>
    <height>694</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QLabel" name="label">
   <property name="geometry">
    <rect>
     <x>-40</x>
     <y>-20</y>
     <width>2101</width>
     <height>731</height>
    </rect>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="pixmap">
    <pixmap resource="../qrc/clkinControl.qrc">:/clkincontrol/clkControl.bmp</pixmap>
   </property>
   <property name="scaledContents">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QCheckBox" name="losEn">
   <property name="geometry">
    <rect>
     <x>121</x>
     <y>97</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 24px;  /* 设置方框宽度 */
                height: 24px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="clkin0LOS">
   <property name="geometry">
    <rect>
     <x>281</x>
     <y>94</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 24px;  /* 设置方框宽度 */
                height: 24px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="clkin0SEL">
   <property name="geometry">
    <rect>
     <x>451</x>
     <y>92</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 24px;  /* 设置方框宽度 */
                height: 24px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="clkin1LOS">
   <property name="geometry">
    <rect>
     <x>281</x>
     <y>122</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 24px;  /* 设置方框宽度 */
                height: 24px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="clkin2LOS">
   <property name="geometry">
    <rect>
     <x>281</x>
     <y>150</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 24px;  /* 设置方框宽度 */
                height: 24px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="clkin1SEL">
   <property name="geometry">
    <rect>
     <x>451</x>
     <y>123</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 24px;  /* 设置方框宽度 */
                height: 24px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="clkin2SEL">
   <property name="geometry">
    <rect>
     <x>451</x>
     <y>150</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 24px;  /* 设置方框宽度 */
                height: 24px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="clkin0En">
   <property name="geometry">
    <rect>
     <x>163</x>
     <y>252</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 22px;  /* 设置方框宽度 */
                height: 22px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="clkin1En">
   <property name="geometry">
    <rect>
     <x>163</x>
     <y>369</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 22px;  /* 设置方框宽度 */
                height: 22px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="clkin2En">
   <property name="geometry">
    <rect>
     <x>163</x>
     <y>466</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 22px;  /* 设置方框宽度 */
                height: 22px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="clkinSelAutoEn">
   <property name="geometry">
    <rect>
     <x>753</x>
     <y>136</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 24px;  /* 设置方框宽度 */
                height: 24px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="clkinSelAutoRevertEn">
   <property name="geometry">
    <rect>
     <x>753</x>
     <y>160</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 24px;  /* 设置方框宽度 */
                height: 24px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="clkinSelPinEn">
   <property name="geometry">
    <rect>
     <x>1023</x>
     <y>72</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 24px;  /* 设置方框宽度 */
                height: 24px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="clkinSelPinPol">
   <property name="geometry">
    <rect>
     <x>1024</x>
     <y>95</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 24px;  /* 设置方框宽度 */
                height: 24px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="PLL2SyncEn">
   <property name="geometry">
    <rect>
     <x>714</x>
     <y>593</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 22px;  /* 设置方框宽度 */
                height: 22px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="PLL1RSyncEn">
   <property name="geometry">
    <rect>
     <x>126</x>
     <y>593</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 22px;  /* 设置方框宽度 */
                height: 22px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="PLL1RRst">
   <property name="geometry">
    <rect>
     <x>126</x>
     <y>646</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 22px;  /* 设置方框宽度 */
                height: 22px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QComboBox" name="syncSource">
   <property name="geometry">
    <rect>
     <x>120</x>
     <y>631</y>
     <width>111</width>
     <height>20</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
     <pointsize>8</pointsize>
    </font>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEditClkin0">
   <property name="geometry">
    <rect>
     <x>143</x>
     <y>210</y>
     <width>108</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEditClkin1">
   <property name="geometry">
    <rect>
     <x>141</x>
     <y>318</y>
     <width>107</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="CLKin0Demux">
   <property name="geometry">
    <rect>
     <x>369</x>
     <y>204</y>
     <width>107</width>
     <height>25</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
     <pointsize>8</pointsize>
    </font>
   </property>
  </widget>
  <widget class="QComboBox" name="CLKin1Demux">
   <property name="geometry">
    <rect>
     <x>371</x>
     <y>314</y>
     <width>108</width>
     <height>25</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
     <pointsize>8</pointsize>
    </font>
   </property>
  </widget>
  <widget class="QComboBox" name="OSCoutMux">
   <property name="geometry">
    <rect>
     <x>383</x>
     <y>462</y>
     <width>101</width>
     <height>25</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
     <pointsize>8</pointsize>
    </font>
   </property>
  </widget>
  <widget class="QComboBox" name="CLKinSelManual">
   <property name="geometry">
    <rect>
     <x>753</x>
     <y>117</y>
     <width>136</width>
     <height>20</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
     <pointsize>8</pointsize>
    </font>
   </property>
  </widget>
  <widget class="QComboBox" name="CLKinSel0Type">
   <property name="geometry">
    <rect>
     <x>1026</x>
     <y>140</y>
     <width>168</width>
     <height>20</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
     <pointsize>8</pointsize>
    </font>
   </property>
  </widget>
  <widget class="QComboBox" name="CLKinSel0Mux">
   <property name="geometry">
    <rect>
     <x>1026</x>
     <y>161</y>
     <width>168</width>
     <height>20</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
     <pointsize>8</pointsize>
    </font>
   </property>
  </widget>
  <widget class="QComboBox" name="CLKinSel1Mux">
   <property name="geometry">
    <rect>
     <x>1028</x>
     <y>223</y>
     <width>167</width>
     <height>20</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
     <pointsize>8</pointsize>
    </font>
   </property>
  </widget>
  <widget class="QComboBox" name="CLKinSel1Type">
   <property name="geometry">
    <rect>
     <x>1028</x>
     <y>201</y>
     <width>167</width>
     <height>22</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
     <pointsize>8</pointsize>
    </font>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEditClkin2Oscout">
   <property name="geometry">
    <rect>
     <x>144</x>
     <y>440</y>
     <width>110</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="OSCoutClockFormat">
   <property name="geometry">
    <rect>
     <x>161</x>
     <y>512</y>
     <width>124</width>
     <height>22</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
     <pointsize>8</pointsize>
    </font>
   </property>
  </widget>
  <widget class="QComboBox" name="LOSTimeout">
   <property name="geometry">
    <rect>
     <x>120</x>
     <y>152</y>
     <width>134</width>
     <height>26</height>
    </rect>
   </property>
  </widget>
  <widget class="QSpinBox" name="PLL1R0Div">
   <property name="geometry">
    <rect>
     <x>555</x>
     <y>324</y>
     <width>96</width>
     <height>28</height>
    </rect>
   </property>
  </widget>
  <widget class="QSpinBox" name="PLL1R1Div">
   <property name="geometry">
    <rect>
     <x>555</x>
     <y>356</y>
     <width>96</width>
     <height>28</height>
    </rect>
   </property>
  </widget>
  <widget class="QSpinBox" name="PLL1R2Div">
   <property name="geometry">
    <rect>
     <x>555</x>
     <y>390</y>
     <width>96</width>
     <height>28</height>
    </rect>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEditClkinSelOut">
   <property name="geometry">
    <rect>
     <x>850</x>
     <y>372</y>
     <width>97</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QCheckBox" name="HoldOverEn">
   <property name="geometry">
    <rect>
     <x>1324</x>
     <y>230</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 22px;  /* 设置方框宽度 */
                height: 22px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="LosExternalInput">
   <property name="geometry">
    <rect>
     <x>753</x>
     <y>185</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 24px;  /* 设置方框宽度 */
                height: 24px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="RGHoFastEnterEn">
   <property name="geometry">
    <rect>
     <x>1327</x>
     <y>447</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 24px;  /* 设置方框宽度 */
                height: 24px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="HoldOverPLL1Det">
   <property name="geometry">
    <rect>
     <x>1327</x>
     <y>421</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 24px;  /* 设置方框宽度 */
                height: 24px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="RGVtunedetRelativeEn">
   <property name="geometry">
    <rect>
     <x>1659</x>
     <y>540</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 22px;  /* 设置方框宽度 */
                height: 22px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QComboBox" name="DACClkMult">
   <property name="geometry">
    <rect>
     <x>1422</x>
     <y>340</y>
     <width>68</width>
     <height>28</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="DACClkCntr">
   <property name="geometry">
    <rect>
     <x>1598</x>
     <y>340</y>
     <width>69</width>
     <height>28</height>
    </rect>
   </property>
  </widget>
  <widget class="QCheckBox" name="HoldoverExitMode">
   <property name="geometry">
    <rect>
     <x>1659</x>
     <y>422</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 22px;  /* 设置方框宽度 */
                height: 22px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="RGZpsEn">
   <property name="geometry">
    <rect>
     <x>1660</x>
     <y>448</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 22px;  /* 设置方框宽度 */
                height: 22px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="RGHOExitDacassist">
   <property name="geometry">
    <rect>
     <x>1660</x>
     <y>473</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 22px;  /* 设置方框宽度 */
                height: 22px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="ManDacEN">
   <property name="geometry">
    <rect>
     <x>1324</x>
     <y>308</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 22px;  /* 设置方框宽度 */
                height: 22px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="TrackEn">
   <property name="geometry">
    <rect>
     <x>1325</x>
     <y>341</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 22px;  /* 设置方框宽度 */
                height: 22px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QSpinBox" name="MANDAC">
   <property name="geometry">
    <rect>
     <x>1660</x>
     <y>310</y>
     <width>131</width>
     <height>26</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="RGHOExitDacassistStep">
   <property name="geometry">
    <rect>
     <x>1655</x>
     <y>504</y>
     <width>71</width>
     <height>25</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
     <pointsize>8</pointsize>
    </font>
   </property>
  </widget>
  <widget class="QSpinBox" name="DACLowTrip">
   <property name="geometry">
    <rect>
     <x>1656</x>
     <y>567</y>
     <width>101</width>
     <height>22</height>
    </rect>
   </property>
  </widget>
  <widget class="QSpinBox" name="DACHighTrip">
   <property name="geometry">
    <rect>
     <x>1656</x>
     <y>590</y>
     <width>101</width>
     <height>23</height>
    </rect>
   </property>
  </widget>
  <widget class="QLineEdit" name="DACUpdateRate">
   <property name="geometry">
    <rect>
     <x>1767</x>
     <y>340</y>
     <width>90</width>
     <height>28</height>
    </rect>
   </property>
  </widget>
  <widget class="QCheckBox" name="RB_DAC_LOCKED">
   <property name="geometry">
    <rect>
     <x>1327</x>
     <y>540</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 24px;  /* 设置方框宽度 */
                height: 24px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QLineEdit" name="RB_DAC_VALUE">
   <property name="geometry">
    <rect>
     <x>1320</x>
     <y>570</y>
     <width>101</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QCheckBox" name="RB_DAC_HIGH">
   <property name="geometry">
    <rect>
     <x>1659</x>
     <y>608</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 22px;  /* 设置方框宽度 */
                height: 22px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="RB_DAC_LOW">
   <property name="geometry">
    <rect>
     <x>1659</x>
     <y>630</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 22px;  /* 设置方框宽度 */
                height: 22px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="RB_DAC_RAIL">
   <property name="geometry">
    <rect>
     <x>1659</x>
     <y>650</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 22px;  /* 设置方框宽度 */
                height: 22px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QComboBox" name="ResetMux">
   <property name="geometry">
    <rect>
     <x>753</x>
     <y>252</y>
     <width>168</width>
     <height>20</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
     <pointsize>8</pointsize>
    </font>
   </property>
  </widget>
  <widget class="QComboBox" name="ResetType">
   <property name="geometry">
    <rect>
     <x>753</x>
     <y>232</y>
     <width>168</width>
     <height>20</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
     <pointsize>8</pointsize>
    </font>
   </property>
  </widget>
  <widget class="QComboBox" name="PLL1_LD_MUX">
   <property name="geometry">
    <rect>
     <x>1027</x>
     <y>310</y>
     <width>168</width>
     <height>20</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
     <pointsize>8</pointsize>
    </font>
   </property>
  </widget>
  <widget class="QComboBox" name="PLL1_LD_TYPE">
   <property name="geometry">
    <rect>
     <x>1027</x>
     <y>288</y>
     <width>168</width>
     <height>20</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
     <pointsize>8</pointsize>
    </font>
   </property>
  </widget>
  <widget class="QComboBox" name="PLL2_LD_MUX">
   <property name="geometry">
    <rect>
     <x>755</x>
     <y>310</y>
     <width>168</width>
     <height>20</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
     <pointsize>8</pointsize>
    </font>
   </property>
  </widget>
  <widget class="QComboBox" name="PLL2_LD_TYPE">
   <property name="geometry">
    <rect>
     <x>755</x>
     <y>288</y>
     <width>168</width>
     <height>20</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
     <pointsize>8</pointsize>
    </font>
   </property>
  </widget>
  <widget class="QCheckBox" name="HOLDOVER_FORCE">
   <property name="geometry">
    <rect>
     <x>1324</x>
     <y>270</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 22px;  /* 设置方框宽度 */
                height: 22px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
 </widget>
 <resources>
  <include location="../qrc/clkinControl.qrc"/>
  <include location="../qrc/clkinControl.qrc"/>
 </resources>
 <connections/>
</ui>
