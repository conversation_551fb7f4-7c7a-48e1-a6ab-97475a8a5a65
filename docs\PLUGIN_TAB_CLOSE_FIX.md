# 插件标签页关闭问题修复

## 问题描述

用户反馈：工具窗口停靠到主界面后，关闭标签页再也无法打开了。

### 问题现象
1. 插件窗口正常打开（悬浮状态）
2. 用户将窗口停靠到主界面标签页
3. 用户关闭标签页
4. 再次点击菜单无法打开插件窗口

### 问题分析

通过日志分析发现问题的根本原因：

```
2025-06-08 17:45:05,751 - PluginIntegrationService - INFO - 🎯 插件菜单动作触发: 时钟输入控制, checked=True
2025-06-08 17:45:05,772 - PluginIntegrationService - INFO - 显示现有插件窗口: 时钟输入控制
2025-06-08 17:45:10,699 - TabWindowManager - INFO - 关闭了工具标签页: 时钟输入控制
2025-06-08 17:45:12,013 - PluginIntegrationService - INFO - 🎯 插件菜单动作触发: 时钟输入控制, checked=False
```

**问题根源**：
1. 插件窗口停靠到标签页后，窗口被集成到标签页容器中
2. 当用户关闭标签页时，`TabWindowManager`只是取消了菜单项的选中状态
3. `PluginIntegrationService`没有收到窗口关闭的通知，仍然认为窗口存在
4. 再次点击菜单时，尝试显示"现有窗口"，但窗口实际上已被删除

## 解决方案

### 1. 修改TabWindowManager

**文件**: `ui/managers/TabWindowManager.py`

#### 添加插件服务通知机制

```python
def close_tool_tab(self, index):
    """关闭工具标签页"""
    widget = self.main_window.tools_tab_widget.widget(index)
    tab_text = self.main_window.tools_tab_widget.tabText(index)

    # 在移除标签页之前，通知插件集成服务
    self._notify_plugin_tab_closed(tab_text, widget)
    
    # ... 其余关闭逻辑
```

#### 实现通知方法

```python
def _notify_plugin_tab_closed(self, tab_text, widget):
    """通知插件集成服务标签页已关闭"""
    try:
        if not hasattr(self.main_window, 'plugin_integration_service'):
            return
            
        plugin_service = self.main_window.plugin_integration_service
        plugin_name = None
        
        # 方法1: 从容器widget中获取插件信息（优先）
        if widget and hasattr(widget, 'plugin_name'):
            plugin_name = widget.plugin_name
        
        # 方法2: 根据标签页文本映射（后备）
        if not plugin_name:
            tab_to_plugin_map = {
                '时钟输入控制': 'clkin_control_plugin',
                'PLL1 & PLL2 控制': 'pll_control_plugin',
                # ... 其他映射
            }
            plugin_name = tab_to_plugin_map.get(tab_text)
        
        if plugin_name:
            plugin_service._on_plugin_window_closed(plugin_name)
            
    except Exception as e:
        logger.error(f"通知插件集成服务标签页关闭时出错: {str(e)}")
```

### 2. 修改PluginIntegrationService

**文件**: `core/services/plugin/PluginIntegrationService.py`

#### 在标签页容器中存储插件信息

```python
def _integrate_window_to_tab(self, window, plugin_name: str):
    """将插件窗口集成到主界面标签页中"""
    # ... 创建容器逻辑
    
    # 在容器上存储插件信息，用于标签页关闭时的清理
    container.plugin_name = plugin_name
    container.plugin_window = window
    
    # ... 其余集成逻辑
```

#### 增强窗口有效性检查

```python
def _show_plugin_window(self, plugin: IToolWindowPlugin):
    """显示插件窗口"""
    try:
        # 检查窗口是否已存在且有效
        if plugin.name in self.plugin_windows:
            window = self.plugin_windows[plugin.name]
            
            # 检查窗口是否仍然有效（可能已被标签页关闭）
            try:
                window.isVisible()  # 验证窗口有效性
                window.show()
                return
            except (RuntimeError, AttributeError):
                # 窗口已被删除或无效，需要重新创建
                del self.plugin_windows[plugin.name]
        
        # 创建新窗口...
```

### 3. 添加窗口状态检查方法

```python
def _is_window_in_tab(self, plugin_name: str) -> bool:
    """检查插件窗口是否在标签页中"""
    try:
        if not hasattr(self.main_window, 'tools_tab_widget'):
            return False
            
        display_name = self._get_plugin_display_name(plugin_name)
        tab_widget = self.main_window.tools_tab_widget
        
        for i in range(tab_widget.count()):
            if tab_widget.tabText(i) == display_name:
                return True
                
        return False
        
    except Exception as e:
        logger.error(f"检查窗口是否在标签页中时出错: {str(e)}")
        return False
```

## 修复效果验证

### 测试场景

1. **创建插件窗口** ✅
   - 插件窗口正常创建
   - 窗口添加到插件服务管理

2. **窗口集成到标签页** ✅
   - 标签页创建成功
   - 容器包含插件信息

3. **标签页关闭处理** ✅
   - TabWindowManager通知PluginIntegrationService
   - 插件窗口引用从服务中移除
   - 菜单状态正确重置

4. **窗口重新创建** ✅
   - 插件窗口可以重新打开
   - 新窗口创建成功

5. **窗口有效性检查** ✅
   - 无效窗口检测正常
   - 自动重新创建功能正常

### 测试结果

```
=== 插件标签页关闭修复测试 ===
✓ 模块导入成功
✓ 服务初始化成功
✓ 插件初始化成功
✓ 插件窗口创建成功
✓ 标签页创建成功，当前标签页数量: 1
✓ 容器包含插件信息
✓ 插件窗口引用已从服务中移除
✓ 插件窗口可以重新打开
✓ 无效窗口检测和重新创建功能正常
🎉 插件标签页关闭修复测试完成
```

## 技术要点

### 1. 双向通信机制
- TabWindowManager → PluginIntegrationService：标签页关闭通知
- PluginIntegrationService → TabWindowManager：窗口状态查询

### 2. 容器信息存储
- 在标签页容器中存储插件名称和窗口引用
- 支持从容器直接获取插件信息，避免文本映射的不准确性

### 3. 窗口有效性检查
- 在显示窗口前检查窗口是否仍然有效
- 自动清理无效窗口引用并重新创建

### 4. 状态同步
- 确保菜单状态与实际窗口状态同步
- 标签页关闭时正确重置菜单选中状态

## 用户体验改善

### 修复前
❌ 插件窗口停靠后关闭无法重新打开
❌ 菜单状态与实际窗口状态不同步
❌ 用户需要重启应用程序才能重新使用插件

### 修复后
✅ 插件窗口可以正常关闭和重新打开
✅ 菜单状态与窗口状态完全同步
✅ 支持多次停靠、关闭、重新打开的完整流程
✅ 用户体验流畅，无需重启应用程序

## 兼容性

- ✅ 向后兼容：不影响现有的悬浮窗口功能
- ✅ 插件兼容：所有现有插件无需修改即可享受修复
- ✅ 功能完整：保持所有原有功能的同时修复问题

## 总结

通过建立TabWindowManager和PluginIntegrationService之间的通信机制，以及增强窗口有效性检查，成功解决了插件窗口停靠后无法重新打开的问题。修复后的系统具有更好的状态管理和用户体验。
