# -*- coding: utf-8 -*-

"""
现代化工具窗口工厂
支持新旧处理器的并行使用，提供渐进式迁移能力
"""
# 静态导入所有现代化处理器，确保PyInstaller能够检测到
from ui.handlers.ModernSetModesHandler import ModernSetModesHandler
from ui.handlers.ModernClkinControlHandler import ModernClkinControlHandler
from ui.handlers.ModernPLLHandler import ModernPLLHandler
from ui.handlers.ModernSyncSysRefHandler import ModernSyncSysRefHandler
from ui.handlers.ModernClkOutputsHandler import ModernClkOutputsHandler
from ui.handlers.ModernRegisterTableHandler import ModernRegisterTableHandler
from ui.handlers.ModernUIEventHandler import ModernUIEventHandler
from ui.handlers.ModernRegisterIOHandler import ModernRegisterIOHandler
from ui.handlers.ModernRegisterTreeHandler import ModernRegisterTreeHandler

# 传统处理器导入（作为回退方案）
from ui.handlers.SetModesHandler import SetModesHandler
from ui.handlers.ClkinControlHandler import ClkinControlHandler
from ui.handlers.RegisterTableHandler import RegisterTableHandler
from ui.handlers.UIEventHandler import UIEventHandler
from ui.handlers.RegisterIOHandler import RegisterIOHandler
from ui.handlers.RegisterTreeHandler import RegisterTreeHandler

import os
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class ModernToolWindowFactory:
    """现代化工具窗口工厂类

    职责：
    1. 支持新旧处理器的并行使用
    2. 提供配置驱动的处理器选择
    3. 管理现代化处理器的特殊初始化需求
    4. 提供迁移状态跟踪和报告
    5. 在现代化处理器失败时提供传统处理器回退
    """
    
    # 处理器配置映射
    HANDLER_CONFIGS = {
        'set_modes': {
            'title': '模式设置',
            'window_attr': 'set_modes_window',
            'action_attr': 'set_modes_action',
            'legacy_handler': 'ui.handlers.SetModesHandler.SetModesHandler',
            'modern_handler': 'ui.handlers.ModernSetModesHandler.ModernSetModesHandler',
            'use_modern': True,  # 优先使用现代化版本，可回退
            'requires_register_manager': True,
            'supports_cross_handler_interaction': False
        },
        'clkin_control': {
            'title': '时钟输入控制',
            'window_attr': 'clkin_control_window',
            'action_attr': 'clkin_control_action',
            'legacy_handler': 'ui.handlers.ClkinControlHandler.ClkinControlHandler',
            'modern_handler': 'ui.handlers.ModernClkinControlHandler.ModernClkinControlHandler',
            'use_modern': True,  # 优先使用现代化版本，可回退
            'requires_register_manager': True,
            'supports_cross_handler_interaction': False
        },
        'pll_control': {
            'title': 'PLL1 & PLL2 控制',
            'window_attr': 'pll_control_window',
            'action_attr': 'pll_control_action',
            'modern_handler': 'ui.handlers.ModernPLLHandler.ModernPLLHandler',
            'use_modern': True,  # 使用现代化版本
            'requires_register_manager': True,
            'supports_cross_handler_interaction': True
        },
        'sync_sysref': {
            'title': '同步系统参考',
            'window_attr': 'sync_sysref_window',
            'action_attr': 'sync_sysref_action',
            'legacy_handler': None,  # 旧处理器已移除
            'modern_handler': 'ui.handlers.ModernSyncSysRefHandler.ModernSyncSysRefHandler',
            'use_modern': True,  # 只使用现代化版本
            'requires_register_manager': True,
            'supports_cross_handler_interaction': True
        },
        'clk_output': {
            'title': '时钟输出配置',
            'window_attr': 'clk_output_window',
            'action_attr': 'clk_output_action',
            'legacy_handler': None,  # 旧处理器已移除
            'modern_handler': 'ui.handlers.ModernClkOutputsHandler.ModernClkOutputsHandler',
            'use_modern': True,  # 只使用现代化版本
            'requires_register_manager': True,
            'supports_cross_handler_interaction': True
        },
        'register_table': {
            'title': '寄存器位域表格',
            'window_attr': 'register_table_window',
            'action_attr': 'register_table_action',
            'legacy_handler': 'ui.handlers.RegisterTableHandler.RegisterTableHandler',
            'modern_handler': 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler',
            'use_modern': True,  # 优先使用现代化版本，可回退
            'requires_register_manager': True,
            'supports_cross_handler_interaction': False
        },
        'ui_event': {
            'title': 'UI事件处理器',
            'window_attr': 'ui_event_window',
            'action_attr': 'ui_event_action',
            'legacy_handler': 'ui.handlers.UIEventHandler.UIEventHandler',
            'modern_handler': 'ui.handlers.ModernUIEventHandler.ModernUIEventHandler',
            'use_modern': True,  # 优先使用现代化版本，可回退
            'requires_register_manager': True,
            'supports_cross_handler_interaction': False
        },
        'register_io': {
            'title': '寄存器IO控制',
            'window_attr': 'register_io_window',
            'action_attr': 'register_io_action',
            'legacy_handler': 'ui.handlers.RegisterIOHandler.RegisterIOHandler',
            'modern_handler': 'ui.handlers.ModernRegisterIOHandler.ModernRegisterIOHandler',
            'use_modern': True,  # 优先使用现代化版本，可回退
            'requires_register_manager': True,
            'supports_cross_handler_interaction': False
        },
        'register_tree': {
            'title': '寄存器树',
            'window_attr': 'register_tree_window',
            'action_attr': 'register_tree_action',
            'legacy_handler': 'ui.handlers.RegisterTreeHandler.RegisterTreeHandler',
            'modern_handler': 'ui.handlers.ModernRegisterTreeHandler.ModernRegisterTreeHandler',
            'use_modern': True,  # 优先使用现代化版本，可回退
            'requires_register_manager': True,
            'supports_cross_handler_interaction': False
        }
    }
    
    def __init__(self, main_window):
        """初始化现代化工具窗口工厂
        
        Args:
            main_window: 主窗口实例
        """
        self.main_window = main_window
        self.migration_status = {}
        self._load_migration_config()
        
    def _load_migration_config(self):
        """加载迁移配置"""
        try:
            # 尝试从配置文件加载迁移状态
            config_file = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'migration.json')
            if os.path.exists(config_file):
                import json
                with open(config_file, 'r', encoding='utf-8') as f:
                    migration_config = json.load(f)
                    
                # 更新处理器配置
                for handler_type, config in migration_config.get('handlers', {}).items():
                    if handler_type in self.HANDLER_CONFIGS:
                        self.HANDLER_CONFIGS[handler_type].update(config)
                        
                logger.info(f"已加载迁移配置: {config_file}")
            else:
                logger.info("未找到迁移配置文件，使用默认配置")
                
        except Exception as e:
            logger.warning(f"加载迁移配置时出错: {str(e)}，使用默认配置")
    
    def create_window_by_type(self, window_type):
        """根据窗口类型创建窗口
        
        Args:
            window_type: 窗口类型（如 'set_modes', 'clkin_control' 等）
            
        Returns:
            创建的窗口实例，如果类型不支持则返回None
        """
        if window_type not in self.HANDLER_CONFIGS:
            logger.warning(f"不支持的窗口类型: {window_type}")
            return None
            
        config = self.HANDLER_CONFIGS[window_type]
        
        try:
            # 决定使用现代化还是传统处理器
            use_modern = config.get('use_modern', False)

            if use_modern and config.get('modern_handler'):
                logger.info(f"创建现代化处理器: {window_type}")
                return self._create_modern_window(window_type, config)
            else:
                logger.info(f"创建传统处理器: {window_type}")
                return self._create_legacy_window(window_type, config)
                
        except Exception as e:
            logger.error(f"创建窗口 {window_type} 时出错: {str(e)}")
            import traceback
            traceback.print_exc()

            # 如果现代化版本失败，尝试回退到传统版本（仅当传统版本可用时）
            if use_modern and config.get('legacy_handler'):
                logger.warning(f"现代化版本失败，回退到传统版本: {window_type}")
                try:
                    return self._create_legacy_window(window_type, config)
                except Exception as fallback_error:
                    logger.error(f"传统版本也失败: {str(fallback_error)}")
            else:
                logger.error(f"窗口 {window_type} 创建失败，且无可用的回退选项")

            return None
    
    def _create_modern_window(self, window_type, config):
        """创建现代化窗口
        
        Args:
            window_type: 窗口类型
            config: 窗口配置
            
        Returns:
            创建的现代化窗口实例
        """
        # 动态导入现代化处理器类
        handler_class = self._import_handler_class(config['modern_handler'])
        
        # 准备初始化参数
        init_kwargs = {}
        
        # 添加RegisterManager参数
        if config.get('requires_register_manager', False):
            init_kwargs['register_manager'] = self.main_window.register_manager

        # 注意：不再添加RegisterRepository参数，现代化处理器使用register_manager作为唯一数据源
        
        # 创建现代化处理器实例
        window = handler_class(parent=None, **init_kwargs)
        
        # 设置主窗口引用
        window.main_window = self.main_window
        
        # 执行后初始化
        self._post_init_modern_window(window, window_type, config)
        
        # 添加到主窗口
        self._add_window_to_main_window(window, config, window_type)
        
        # 记录迁移状态
        self.migration_status[window_type] = {
            'type': 'modern',
            'handler_class': config['modern_handler'],
            'created_at': self._get_current_timestamp()
        }
        
        logger.info(f"现代化窗口 {window_type} 创建成功")
        return window
    
    def _create_legacy_window(self, window_type, config):
        """创建传统窗口

        Args:
            window_type: 窗口类型
            config: 窗口配置

        Returns:
            创建的传统窗口实例
        """
        # 动态导入传统处理器类
        handler_class = self._import_handler_class(config['legacy_handler'])

        # 创建传统处理器实例（使用传统参数格式）
        window = handler_class(
            parent=None,
            registers=self.main_window.register_manager.register_objects
        )

        # 设置主窗口引用
        window.main_window = self.main_window

        # 执行后初始化
        self._post_init_legacy_window(window, window_type, config)

        # 添加到主窗口
        self._add_window_to_main_window(window, config, window_type)

        # 记录迁移状态
        self.migration_status[window_type] = {
            'type': 'legacy',
            'handler_class': config['legacy_handler'],
            'created_at': self._get_current_timestamp()
        }

        logger.info(f"传统窗口 {window_type} 创建成功")
        return window
    
    def _import_handler_class(self, handler_path):
        """导入处理器类（使用静态映射以支持PyInstaller）

        Args:
            handler_path: 处理器类路径，格式为 'module.path.ClassName'

        Returns:
            导入的处理器类
        """
        # 静态映射表，确保PyInstaller能够检测到所有处理器
        handler_class_map = {
            # 现代化处理器
            'ui.handlers.ModernSetModesHandler.ModernSetModesHandler': ModernSetModesHandler,
            'ui.handlers.ModernClkinControlHandler.ModernClkinControlHandler': ModernClkinControlHandler,
            'ui.handlers.ModernPLLHandler.ModernPLLHandler': ModernPLLHandler,
            'ui.handlers.ModernSyncSysRefHandler.ModernSyncSysRefHandler': ModernSyncSysRefHandler,
            'ui.handlers.ModernClkOutputsHandler.ModernClkOutputsHandler': ModernClkOutputsHandler,
            'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler': ModernRegisterTableHandler,
            'ui.handlers.ModernUIEventHandler.ModernUIEventHandler': ModernUIEventHandler,
            'ui.handlers.ModernRegisterIOHandler.ModernRegisterIOHandler': ModernRegisterIOHandler,
            'ui.handlers.ModernRegisterTreeHandler.ModernRegisterTreeHandler': ModernRegisterTreeHandler,

            # 传统处理器（回退支持）
            'ui.handlers.SetModesHandler.SetModesHandler': SetModesHandler,
            'ui.handlers.ClkinControlHandler.ClkinControlHandler': ClkinControlHandler,
            'ui.handlers.RegisterTableHandler.RegisterTableHandler': RegisterTableHandler,
            'ui.handlers.UIEventHandler.UIEventHandler': UIEventHandler,
            'ui.handlers.RegisterIOHandler.RegisterIOHandler': RegisterIOHandler,
            'ui.handlers.RegisterTreeHandler.RegisterTreeHandler': RegisterTreeHandler,
        }

        # 从映射表获取处理器类
        handler_class = handler_class_map.get(handler_path)

        if handler_class is None:
            # 如果静态映射中没有找到，回退到动态导入
            logger.warning(f"处理器 {handler_path} 不在静态映射中，尝试动态导入")
            try:
                module_path, class_name = handler_path.rsplit('.', 1)
                import importlib
                module = importlib.import_module(module_path)
                handler_class = getattr(module, class_name)
                logger.info(f"动态导入成功: {handler_path}")
            except Exception as e:
                logger.error(f"动态导入失败: {handler_path}, 错误: {str(e)}")
                raise ImportError(f"无法导入处理器类: {handler_path}")

        return handler_class
    
    def _post_init_modern_window(self, window, window_type, config):
        """现代化窗口的后初始化
        
        Args:
            window: 窗口实例
            window_type: 窗口类型
            config: 窗口配置
        """
        try:
            # 设置跨处理器交互
            if config.get('supports_cross_handler_interaction', False):
                self._setup_cross_handler_interaction(window, window_type)
            
            # 连接现代化特有的信号
            if hasattr(window, 'window_closed'):
                window.window_closed.connect(lambda: self._on_modern_window_closed(window_type))
            
            logger.debug(f"现代化窗口 {window_type} 后初始化完成")
            
        except Exception as e:
            logger.error(f"现代化窗口 {window_type} 后初始化失败: {str(e)}")
    
    def _post_init_legacy_window(self, window, window_type, config):
        """传统窗口的后初始化

        Args:
            window: 窗口实例
            window_type: 窗口类型
            config: 窗口配置
        """
        try:
            # 设置跨处理器交互（如果支持）
            if config.get('supports_cross_handler_interaction', False):
                self._setup_cross_handler_interaction(window, window_type)

            logger.debug(f"传统窗口 {window_type} 后初始化完成")

        except Exception as e:
            logger.error(f"传统窗口 {window_type} 后初始化失败: {str(e)}")
    
    def _setup_cross_handler_interaction(self, window, window_type):
        """设置跨处理器交互
        
        Args:
            window: 窗口实例
            window_type: 窗口类型
        """
        try:
            # 设置时钟输出与同步系统参考的交互
            if window_type == 'clk_output':
                sync_window = getattr(self.main_window, 'sync_sysref_window', None)
                if sync_window and hasattr(window, 'set_sync_sysref_handler'):
                    window.set_sync_sysref_handler(sync_window)
                    logger.info("已设置时钟输出与同步系统参考的交互")
                    
            elif window_type == 'sync_sysref':
                clk_output_window = getattr(self.main_window, 'clk_output_window', None)
                if clk_output_window and hasattr(clk_output_window, 'set_sync_sysref_handler'):
                    clk_output_window.set_sync_sysref_handler(window)
                    logger.info("已设置同步系统参考与时钟输出的交互")
            
        except Exception as e:
            logger.error(f"设置跨处理器交互时出错: {str(e)}")
    
    def _add_window_to_main_window(self, window, config, window_type=None):
        """将窗口添加到主窗口

        Args:
            window: 窗口实例
            config: 窗口配置
            window_type: 窗口类型（可选）
        """
        try:
            # 使用窗口管理服务添加窗口
            if hasattr(self.main_window, 'window_service'):
                # 调用正确的方法名：_add_window_to_tab（私有方法）
                self.main_window.window_service._add_window_to_tab(
                    window,
                    config['title'],
                    config['window_attr'],
                    config.get('action_attr')
                )
                window_name = window_type or config.get('title', '未知窗口')
                logger.info(f"现代化窗口 {window_name} 已添加到主窗口")
            else:
                # 直接设置窗口属性（回退方案）
                setattr(self.main_window, config['window_attr'], window)
                logger.warning("窗口管理服务不可用，使用直接属性设置")

        except Exception as e:
            logger.error(f"添加窗口到主窗口时出错: {str(e)}")
    
    def _on_modern_window_closed(self, window_type):
        """处理现代化窗口关闭事件
        
        Args:
            window_type: 窗口类型
        """
        logger.info(f"现代化窗口 {window_type} 已关闭")
        
        # 清理迁移状态
        if window_type in self.migration_status:
            del self.migration_status[window_type]
    
    def _get_current_timestamp(self):
        """获取当前时间戳"""
        import datetime
        return datetime.datetime.now().isoformat()
    
    def get_migration_status(self):
        """获取迁移状态报告
        
        Returns:
            dict: 迁移状态信息
        """
        total_handlers = len(self.HANDLER_CONFIGS)
        modern_count = sum(1 for config in self.HANDLER_CONFIGS.values() if config.get('use_modern', False))
        legacy_count = total_handlers - modern_count
        
        active_windows = len(self.migration_status)
        active_modern = sum(1 for status in self.migration_status.values() if status['type'] == 'modern')
        active_legacy = active_windows - active_modern
        
        return {
            'total_handlers': total_handlers,
            'configured_modern': modern_count,
            'configured_legacy': legacy_count,
            'active_windows': active_windows,
            'active_modern': active_modern,
            'active_legacy': active_legacy,
            'migration_progress': (modern_count / total_handlers) * 100,
            'active_status': self.migration_status.copy()
        }
    
    def switch_handler_type(self, window_type, use_modern=True):
        """切换处理器类型
        
        Args:
            window_type: 窗口类型
            use_modern: 是否使用现代化版本
            
        Returns:
            bool: 是否成功切换
        """
        if window_type not in self.HANDLER_CONFIGS:
            logger.error(f"未知的窗口类型: {window_type}")
            return False
        
        # 更新配置
        self.HANDLER_CONFIGS[window_type]['use_modern'] = use_modern
        
        # 如果窗口当前是活跃的，需要重新创建
        window_attr = self.HANDLER_CONFIGS[window_type]['window_attr']
        if hasattr(self.main_window, window_attr) and getattr(self.main_window, window_attr):
            logger.info(f"重新创建窗口 {window_type} 以应用新的处理器类型")
            
            # 关闭现有窗口
            old_window = getattr(self.main_window, window_attr)
            if hasattr(old_window, 'close'):
                old_window.close()
            
            # 创建新窗口
            new_window = self.create_window_by_type(window_type)
            return new_window is not None
        
        logger.info(f"已配置窗口 {window_type} 使用 {'现代化' if use_modern else '传统'} 处理器")
        return True
