# 菜单状态重置问题修复

## 问题描述

用户反馈：工具窗口停靠到主界面后，点击标签页上的关闭按钮，时钟输入工具菜单仍然保持深色选中状态，导致再次点击时无法重新打开窗口。

### 问题现象
1. 插件窗口正常打开并停靠到标签页
2. 用户点击标签页的关闭按钮
3. 菜单项仍然显示为选中状态（深色）
4. 再次点击菜单无法打开窗口

### 根本原因

通过日志分析发现问题的根本原因：

```
2025-06-08 17:53:19,906 - TabWindowManager - WARNING - 后备方案: 未能为标签页文本 '时钟输入控制' 匹配到任何已知的QAction. 已知前缀: ['模式设置', '时钟输入', 'PLL1 & PLL2 控制', '同步系统参考', '时钟输出']
```

**问题分析**：
1. 标签页显示名称是 `'时钟输入控制'`
2. TabWindowManager中的映射表使用的是 `'时钟输入'`
3. 映射匹配失败，导致菜单状态没有重置
4. PluginIntegrationService收到通知但菜单状态已经在TabWindowManager中处理

## 解决方案

### 1. 修复TabWindowManager中的标签页文本映射

**文件**: `ui/managers/TabWindowManager.py`

#### 问题1: 映射表不完整

**修复前**:
```python
tab_to_plugin_map = {
    '时钟输入控制': 'clkin_control_plugin',
    'PLL1 & PLL2 控制': 'pll_control_plugin', 
    '同步系统参考': 'sync_sysref_plugin',
    '时钟输出配置': 'clk_output_plugin',
    '模式设置': 'set_modes_plugin'
}
```

**修复后**:
```python
tab_to_plugin_map = {
    '时钟输入控制': 'clkin_control_plugin',
    'PLL控制': 'pll_control_plugin', 
    '同步系统参考': 'sync_sysref_plugin',
    '时钟输出': 'clk_output_plugin',
    '模式设置': 'set_modes_plugin',
    '示例工具': '示例工具'  # 示例工具的映射
}
```

#### 问题2: 菜单动作映射表不匹配

**修复前**:
```python
action_map = {
    "模式设置": self.main_window.set_modes_action,
    "时钟输入": self.main_window.clkin_control_action, # 不匹配！
    "PLL1 & PLL2 控制": self.main_window.pll_control_action,
    "同步系统参考": self.main_window.sync_sysref_action,
    "时钟输出": self.main_window.clk_output_action,
}
```

**修复后**:
```python
action_map = {
    # 完全匹配（优先）
    "模式设置": self.main_window.set_modes_action,
    "时钟输入控制": self.main_window.clkin_control_action,
    "PLL控制": self.main_window.pll_control_action,
    "同步系统参考": self.main_window.sync_sysref_action,
    "时钟输出": self.main_window.clk_output_action,
    "时钟输出配置": self.main_window.clk_output_action,
    
    # 前缀匹配（后备）
    "时钟输入": self.main_window.clkin_control_action,
    "PLL1 & PLL2 控制": self.main_window.pll_control_action,
}
```

#### 问题3: 匹配逻辑不够健壮

**修复前**:
```python
# 只有前缀匹配
for prefix_key, action_instance in action_map.items():
    if action_instance and tab_text.startswith(prefix_key):
        action_to_uncheck = action_instance
        break
```

**修复后**:
```python
# 首先尝试完全匹配
if tab_text in action_map and action_map[tab_text]:
    action_to_uncheck = action_map[tab_text]
    matched_prefix_key = tab_text
else:
    # 如果完全匹配失败，尝试前缀匹配
    for prefix_key, action_instance in action_map.items():
        if action_instance and tab_text.startswith(prefix_key):
            action_to_uncheck = action_instance
            matched_prefix_key = prefix_key
            break
```

### 2. 确保双重保护机制

#### TabWindowManager层面
- 在标签页关闭时立即重置菜单状态
- 通知PluginIntegrationService窗口已关闭

#### PluginIntegrationService层面
- 清理窗口引用
- 重置菜单状态（双重保护）
- 发送窗口关闭信号

## 修复效果验证

### 测试结果

```
=== 菜单状态修复测试 ===
✓ 模块导入成功
✓ 服务初始化成功
✓ 插件初始化成功

--- 测试场景1: 创建插件窗口 ---
菜单状态设置为选中: True

--- 测试场景2: 集成到标签页 ---
✓ 标签页创建成功: '时钟输入控制'
菜单状态: True

--- 测试场景3: 测试标签页文本映射 ---
✓ 标签页文本映射成功: '时钟输入控制' -> 'clkin_control_plugin'

--- 测试场景4: 模拟标签页关闭 ---
关闭前菜单状态: True
关闭后菜单状态: False  ← 关键修复！
✓ 菜单状态正确重置为未选中
✓ 插件窗口引用已清理

--- 测试场景5: 验证重新打开功能 ---
✓ 插件窗口可以重新创建
```

### 映射表测试结果

```
标签页文本映射:
  ✓ '时钟输入控制' -> 'clkin_control_plugin' (完全匹配)
  ✓ 'PLL控制' -> 'pll_control_plugin' (完全匹配)
  ✓ '同步系统参考' -> 'sync_sysref_plugin' (完全匹配)
  ✓ '时钟输出' -> 'clk_output_plugin' (完全匹配)
  ✓ '模式设置' -> 'set_modes_plugin' (完全匹配)
  ✓ '时钟输出配置' -> 'clk_output_plugin' (前缀匹配)
```

## 技术要点

### 1. 标签页文本一致性
- 确保PluginIntegrationService中的显示名称与TabWindowManager中的映射一致
- 支持完全匹配和前缀匹配两种方式

### 2. 双重保护机制
- TabWindowManager：立即处理菜单状态重置
- PluginIntegrationService：清理窗口引用和状态

### 3. 健壮的匹配逻辑
- 优先使用完全匹配
- 后备使用前缀匹配
- 详细的日志记录便于调试

### 4. 状态同步
- 菜单状态与实际窗口状态保持同步
- 窗口引用的及时清理

## 用户体验改善

### 修复前
❌ 标签页关闭后菜单仍显示选中状态
❌ 无法重新打开插件窗口
❌ 用户需要重启应用程序

### 修复后
✅ 标签页关闭后菜单正确显示未选中状态
✅ 可以正常重新打开插件窗口
✅ 菜单状态与窗口状态完全同步
✅ 用户体验流畅，无需重启

## 兼容性

- ✅ 向后兼容：不影响现有功能
- ✅ 多插件支持：所有插件都受益于此修复
- ✅ 多种匹配方式：支持不同的标签页命名方式

## 预防措施

### 1. 统一命名规范
建议在添加新插件时，确保以下名称保持一致：
- PluginIntegrationService中的显示名称
- TabWindowManager中的映射表
- 实际的标签页文本

### 2. 测试覆盖
- 每个新插件都应该测试标签页关闭和重新打开功能
- 验证菜单状态的正确重置

### 3. 日志监控
- 关注TabWindowManager的警告日志
- 及时发现映射表不匹配的问题

## 总结

通过修复TabWindowManager中的标签页文本映射和匹配逻辑，成功解决了菜单状态不正确重置的问题。修复后的系统具有更好的状态管理和用户体验，确保插件窗口可以正常关闭和重新打开。
