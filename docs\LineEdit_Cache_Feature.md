# LineEdit控件值持久化功能

## 功能概述

为了解决用户修改lineEdit控件值后，关闭窗口再次打开时值丢失的问题，我们实现了一套完整的lineEdit控件值持久化缓存系统。

## 功能特性

### 1. 自动缓存
- 当用户修改lineEdit控件的值时，系统会自动将新值缓存到内存中
- 当窗口关闭时，系统会自动缓存所有lineEdit控件的当前值
- 支持多个窗口的独立缓存管理

### 2. 自动恢复
- 当窗口重新打开时，系统会自动从缓存中恢复之前保存的值
- 如果缓存中没有值，则使用默认值
- 恢复过程不会触发不必要的信号事件

### 3. 窗口支持
目前已实现缓存功能的窗口：
- **时钟输入控制窗口** (ClkinControl)
  - lineEditClkin0 (CLKin0频率)
  - lineEditClkin1 (CLKin1频率)
  - lineEditClkin2Oscout (CLKin2频率)
  - lineEditClkinSelOut (时钟选择输出)

- **PLL控制窗口** (PLLControl)
  - OSCinFreq (OSC输入频率)
  - ExternalVCXOFreq (外部VCXO频率)
  - FreFin (Fin频率)

- **时钟输出窗口** (ClkOutputs)
  - lineEditFvco (VCO频率)

## 技术实现

### 1. 事件总线扩展
在 `RegisterUpdateBus` 中添加了lineEdit缓存管理功能：
```python
# 缓存控件值
bus.cache_lineedit_value(window_name, widget_name, value)

# 获取缓存值
cached_value = bus.get_cached_lineedit_value(window_name, widget_name)

# 清空缓存
bus.clear_lineedit_cache(window_name)
```

### 2. 窗口处理器增强
每个支持缓存的窗口处理器都实现了：
- `_restore_lineedit_values_from_cache()` - 从缓存恢复值
- `_cache_lineedit_value()` - 缓存单个控件值
- `_cache_all_lineedit_values()` - 缓存所有控件值
- 重写 `closeEvent()` - 窗口关闭时自动缓存

### 3. 基类支持
`ModernBaseHandler` 基类在 `closeEvent()` 中自动调用子类的缓存方法，确保所有继承的窗口都能自动支持缓存功能。

## 使用方式

### 对用户
功能完全透明，用户无需任何额外操作：
1. 正常修改lineEdit控件的值
2. 关闭窗口
3. 重新打开窗口时，之前修改的值会自动恢复

### 对开发者
为新窗口添加缓存支持：

1. **添加窗口名称标识**：
```python
def __init__(self, ...):
    # 其他初始化代码...
    self._window_name = "YourWindowName"
```

2. **实现缓存方法**：
```python
def _restore_lineedit_values_from_cache(self):
    # 从缓存恢复值的实现
    
def _cache_lineedit_value(self, control_name, value):
    # 缓存单个值的实现
    
def _cache_all_lineedit_values(self):
    # 缓存所有值的实现
```

3. **在初始化时调用恢复方法**：
```python
def _init_default_values(self):
    # 首先从缓存恢复值
    self._restore_lineedit_values_from_cache()
    # 然后设置默认值（如果缓存中没有值）
```

4. **在值变化时缓存**：
```python
def on_value_changed(self):
    # 处理值变化
    new_value = self.ui.someLineEdit.text()
    # 缓存新值
    self._cache_lineedit_value("someLineEdit", new_value)
```

## 测试验证

运行测试脚本验证功能：
```bash
python test_lineedit_cache.py
```

## 注意事项

1. **内存缓存**：当前实现使用内存缓存，应用程序重启后缓存会丢失
2. **只读控件**：计算结果显示的只读控件（如PLL1PFDFreq等）不需要缓存
3. **信号阻塞**：恢复值时会阻塞信号，避免触发不必要的计算
4. **错误处理**：所有缓存操作都有完善的错误处理，不会影响正常功能

## 未来扩展

1. **持久化存储**：可以扩展为文件或数据库存储，实现跨会话持久化
2. **更多窗口**：为其他工具窗口添加缓存支持
3. **配置选项**：添加用户配置选项，允许启用/禁用缓存功能
4. **缓存策略**：实现更智能的缓存策略，如LRU淘汰等
