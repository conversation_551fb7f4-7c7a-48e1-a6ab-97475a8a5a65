2025-06-02 00:54:37,358 - RegisterUpdateBus - [RegisterUpdateBus.py:57] - INFO - RegisterUpdateBus: Initial clock source set based on register default (0x57, <PERSON><PERSON><PERSON>in_SEL_MANUAL): ClkIn1
2025-06-02 00:55:04,026 - RegisterUpdateBus - [RegisterUpdateBus.py:57] - INFO - RegisterUpdateBus: Initial clock source set based on register default (0x57, CLKin_SEL_MANUAL): ClkIn1
2025-06-02 00:55:33,417 - RegisterUpdateBus - [RegisterUpdateBus.py:57] - INFO - RegisterUpdateBus: Initial clock source set based on register default (0x57, CL<PERSON>in_SEL_MANUAL): ClkIn1
2025-06-02 00:55:33,432 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 00:55:33,432 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 00:55:33,433 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 00:55:33,837 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 00:55:33,838 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 00:55:33,838 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 00:55:33,839 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 00:55:33,839 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 00:55:33,839 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 00:55:33,840 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:323] - DEBUG - 现代化IOHandler: 发出 write_requested 信号, 地址: 0x02, 值: 0x1234
2025-06-02 00:55:33,842 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 00:55:33,842 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 00:55:33,842 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 00:55:33,843 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 00:55:33,843 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 00:55:33,844 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 00:55:33,845 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 00:55:33,845 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 00:55:33,845 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 00:55:33,846 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:337] - WARNING - 无效的十六进制输入: GHIJ
2025-06-02 00:55:33,846 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:337] - WARNING - 无效的十六进制输入: 12345
2025-06-02 00:55:33,847 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 00:55:33,847 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 00:55:33,847 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 00:55:33,848 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 00:55:33,849 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 00:55:33,849 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 00:55:33,852 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 00:55:33,853 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 00:55:33,853 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 00:55:33,857 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 00:55:33,857 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 00:55:33,857 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 00:55:33,860 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 00:55:33,860 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 00:55:33,860 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 00:55:33,862 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 00:55:33,862 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 00:55:33,862 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 00:56:57,199 - RegisterUpdateBus - [RegisterUpdateBus.py:57] - INFO - RegisterUpdateBus: Initial clock source set based on register default (0x57, CLKin_SEL_MANUAL): ClkIn1
2025-06-02 00:56:57,212 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 00:56:57,212 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 00:56:57,212 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 00:56:57,587 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 00:56:57,588 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 00:56:57,588 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 00:56:57,592 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 00:56:57,592 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 00:56:57,592 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 00:56:57,593 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:323] - DEBUG - 现代化IOHandler: 发出 write_requested 信号, 地址: 0x02, 值: 0x1234
2025-06-02 00:56:57,595 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 00:56:57,595 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 00:56:57,596 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 00:56:57,597 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 00:56:57,598 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 00:56:57,598 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 00:56:57,598 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:575] - DEBUG - 寄存器IO: 收到全局更新 2 = 0x9ABC
2025-06-02 00:56:57,598 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:323] - DEBUG - 现代化IOHandler: 发出 write_requested 信号, 地址: 0x02, 值: 0x9ABC
2025-06-02 00:56:57,599 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 00:56:57,599 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 00:56:57,599 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 00:56:57,600 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:337] - WARNING - 无效的十六进制输入: GHIJ
2025-06-02 00:56:57,600 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:337] - WARNING - 无效的十六进制输入: 12345
2025-06-02 00:56:57,601 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 00:56:57,601 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 00:56:57,601 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 00:56:57,602 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 00:56:57,604 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 00:56:57,604 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 00:56:57,608 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 00:56:57,609 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 00:56:57,609 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 00:56:57,609 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:558] - INFO - 寄存器IO: 寄存器 2 值变化 (控件: test_widget) -> 0x5678
2025-06-02 00:56:57,610 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:323] - DEBUG - 现代化IOHandler: 发出 write_requested 信号, 地址: 0x02, 值: 0x5678
2025-06-02 00:56:57,611 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 00:56:57,611 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 00:56:57,611 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 00:56:57,613 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 00:56:57,613 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 00:56:57,613 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 00:56:57,614 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 00:56:57,615 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 00:56:57,615 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 01:07:48,918 - RegisterUpdateBus - [RegisterUpdateBus.py:57] - INFO - RegisterUpdateBus: Initial clock source set based on register default (0x57, CLKin_SEL_MANUAL): ClkIn1
2025-06-02 01:07:48,938 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:07:48,938 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:07:48,938 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:07:48,939 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:07:48,939 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:07:48,940 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:07:48,940 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:113] - INFO - 填充了 3 个寄存器到树视图
2025-06-02 01:07:48,941 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:246] - INFO - 现代化TreeHandler: 开始过滤，关键字: 'powerdown'
2025-06-02 01:07:48,942 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:07:48,943 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:07:48,943 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:07:48,943 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:113] - INFO - 填充了 3 个寄存器到树视图
2025-06-02 01:07:48,943 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:161] - INFO - 现代化TreeHandler: 树项点击 - 项: 'R2 (0x02)', 地址: 0x02, 列: 0
2025-06-02 01:07:48,943 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:170] - INFO - 现代化TreeHandler: 选择寄存器地址: 0x02 从项 'R2 (0x02)'
2025-06-02 01:07:48,944 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:07:48,944 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:07:48,945 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:07:48,945 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:113] - INFO - 填充了 3 个寄存器到树视图
2025-06-02 01:07:48,945 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:144] - INFO - 现代化TreeHandler: 选择默认寄存器: 'R0 (0x00)'
2025-06-02 01:07:48,946 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:07:48,946 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:07:48,946 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:07:48,946 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:113] - INFO - 填充了 3 个寄存器到树视图
2025-06-02 01:07:48,947 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:161] - INFO - 现代化TreeHandler: 树项点击 - 项: 'R0 (0x00)', 地址: 0x00, 列: 0
2025-06-02 01:07:48,947 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:170] - INFO - 现代化TreeHandler: 选择寄存器地址: 0x00 从项 'R0 (0x00)'
2025-06-02 01:07:48,947 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:161] - INFO - 现代化TreeHandler: 树项点击 - 项: 'R0 (0x00)', 地址: 0x00, 列: 0
2025-06-02 01:07:48,947 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:167] - DEBUG - 现代化TreeHandler: 忽略重复选择地址: 0x00
2025-06-02 01:07:48,948 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:07:48,948 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:07:48,948 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:07:48,948 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:113] - INFO - 填充了 3 个寄存器到树视图
2025-06-02 01:07:48,949 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:07:48,949 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:07:48,949 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:07:48,949 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:113] - INFO - 填充了 3 个寄存器到树视图
2025-06-02 01:07:48,949 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:310] - DEBUG - 现代化TreeHandler: 收到全局更新 0x02 = 0x1234
2025-06-02 01:07:48,950 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:07:48,950 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:07:48,950 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:07:48,951 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:07:48,951 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:07:48,951 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:07:48,952 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:07:48,953 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:07:48,953 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:07:48,953 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:113] - INFO - 填充了 3 个寄存器到树视图
2025-06-02 01:07:48,953 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:07:48,954 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:07:48,954 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:07:48,954 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:113] - INFO - 填充了 3 个寄存器到树视图
2025-06-02 01:07:48,954 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:161] - INFO - 现代化TreeHandler: 树项点击 - 项: 'R2 (0x02)', 地址: 0x02, 列: 0
2025-06-02 01:07:48,954 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:170] - INFO - 现代化TreeHandler: 选择寄存器地址: 0x02 从项 'R2 (0x02)'
2025-06-02 01:07:48,955 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:07:48,955 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:07:48,955 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:07:48,956 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:113] - INFO - 填充了 3 个寄存器到树视图
2025-06-02 01:07:48,956 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:07:48,956 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:07:48,957 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:07:48,957 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:113] - INFO - 填充了 3 个寄存器到树视图
2025-06-02 01:07:48,957 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:161] - INFO - 现代化TreeHandler: 树项点击 - 项: 'R0 (0x00)', 地址: 0x00, 列: 0
2025-06-02 01:07:48,957 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:170] - INFO - 现代化TreeHandler: 选择寄存器地址: 0x00 从项 'R0 (0x00)'
2025-06-02 01:07:48,958 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:07:48,958 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:07:48,958 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:08:26,025 - RegisterUpdateBus - [RegisterUpdateBus.py:57] - INFO - RegisterUpdateBus: Initial clock source set based on register default (0x57, CLKin_SEL_MANUAL): ClkIn1
2025-06-02 01:08:26,034 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:08:26,034 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:08:26,034 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:08:26,036 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:08:26,036 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:08:26,036 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:08:26,036 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:113] - INFO - 填充了 3 个寄存器到树视图
2025-06-02 01:08:26,037 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:246] - INFO - 现代化TreeHandler: 开始过滤，关键字: 'powerdown'
2025-06-02 01:08:26,428 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:289] - INFO - 现代化TreeHandler: 过滤完成，选择项 'R2 (0x02)'
2025-06-02 01:08:26,429 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:246] - INFO - 现代化TreeHandler: 开始过滤，关键字: 'power'
2025-06-02 01:08:26,429 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:289] - INFO - 现代化TreeHandler: 过滤完成，选择项 'R2 (0x02)'
2025-06-02 01:08:26,429 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:304] - INFO - 现代化TreeHandler: 关键字为空，显示所有寄存器，已清除高亮和选择
2025-06-02 01:08:26,430 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:08:26,430 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:08:26,430 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:08:26,430 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:113] - INFO - 填充了 3 个寄存器到树视图
2025-06-02 01:08:26,430 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:161] - INFO - 现代化TreeHandler: 树项点击 - 项: 'R2 (0x02)', 地址: 0x02, 列: 0
2025-06-02 01:08:26,431 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:170] - INFO - 现代化TreeHandler: 选择寄存器地址: 0x02 从项 'R2 (0x02)'
2025-06-02 01:08:26,431 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:08:26,432 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:08:26,432 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:08:26,432 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:113] - INFO - 填充了 3 个寄存器到树视图
2025-06-02 01:08:26,432 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:144] - INFO - 现代化TreeHandler: 选择默认寄存器: 'R0 (0x00)'
2025-06-02 01:08:26,433 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:08:26,433 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:08:26,433 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:08:26,433 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:113] - INFO - 填充了 3 个寄存器到树视图
2025-06-02 01:08:26,433 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:161] - INFO - 现代化TreeHandler: 树项点击 - 项: 'R0 (0x00)', 地址: 0x00, 列: 0
2025-06-02 01:08:26,434 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:170] - INFO - 现代化TreeHandler: 选择寄存器地址: 0x00 从项 'R0 (0x00)'
2025-06-02 01:08:26,434 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:161] - INFO - 现代化TreeHandler: 树项点击 - 项: 'R0 (0x00)', 地址: 0x00, 列: 0
2025-06-02 01:08:26,434 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:167] - DEBUG - 现代化TreeHandler: 忽略重复选择地址: 0x00
2025-06-02 01:08:26,436 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:08:26,436 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:08:26,436 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:08:26,436 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:113] - INFO - 填充了 3 个寄存器到树视图
2025-06-02 01:08:26,437 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:08:26,437 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:08:26,437 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:08:26,438 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:113] - INFO - 填充了 3 个寄存器到树视图
2025-06-02 01:08:26,438 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:310] - DEBUG - 现代化TreeHandler: 收到全局更新 0x02 = 0x1234
2025-06-02 01:08:26,440 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:08:26,440 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:08:26,440 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:08:26,441 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:08:26,441 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:08:26,441 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:08:26,442 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:08:26,442 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:08:26,443 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:08:26,443 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:113] - INFO - 填充了 3 个寄存器到树视图
2025-06-02 01:08:26,443 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:161] - INFO - 现代化TreeHandler: 树项点击 - 项: 'R0 (0x00)', 地址: 0x00, 列: 0
2025-06-02 01:08:26,443 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:170] - INFO - 现代化TreeHandler: 选择寄存器地址: 0x00 从项 'R0 (0x00)'
2025-06-02 01:08:26,444 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:08:26,444 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:08:26,444 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:08:26,445 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:113] - INFO - 填充了 3 个寄存器到树视图
2025-06-02 01:08:26,445 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:08:26,446 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:08:26,446 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:08:26,446 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:113] - INFO - 填充了 3 个寄存器到树视图
2025-06-02 01:08:26,446 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:161] - INFO - 现代化TreeHandler: 树项点击 - 项: 'R2 (0x02)', 地址: 0x02, 列: 0
2025-06-02 01:08:26,446 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:170] - INFO - 现代化TreeHandler: 选择寄存器地址: 0x02 从项 'R2 (0x02)'
2025-06-02 01:08:26,447 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:08:26,447 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:08:26,448 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:08:26,448 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:113] - INFO - 填充了 3 个寄存器到树视图
2025-06-02 01:08:26,448 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:08:26,448 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:08:26,448 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:08:26,449 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:113] - INFO - 填充了 3 个寄存器到树视图
2025-06-02 01:08:26,449 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:161] - INFO - 现代化TreeHandler: 树项点击 - 项: 'R0 (0x00)', 地址: 0x00, 列: 0
2025-06-02 01:08:26,449 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:170] - INFO - 现代化TreeHandler: 选择寄存器地址: 0x00 从项 'R0 (0x00)'
2025-06-02 01:08:26,450 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:08:26,450 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:08:26,451 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:09:57,294 - InitializationManager - [InitializationManager.py:95] - WARNING - 现代化处理器创建失败，回退到传统处理器: QWidget(parent: Optional[QWidget] = None, flags: Union[Qt.WindowFlags, Qt.WindowType] = Qt.WindowFlags()): argument 1 has unexpected type 'MockMainWindow'
2025-06-02 01:09:57,300 - InitializationManager - [InitializationManager.py:95] - WARNING - 现代化处理器创建失败，回退到传统处理器: 现代化工厂不可用
2025-06-02 01:09:57,309 - InitializationManager - [InitializationManager.py:95] - WARNING - 现代化处理器创建失败，回退到传统处理器: QWidget(parent: Optional[QWidget] = None, flags: Union[Qt.WindowFlags, Qt.WindowType] = Qt.WindowFlags()): argument 1 has unexpected type 'MockMainWindow'
2025-06-02 01:09:57,312 - InitializationManager - [InitializationManager.py:95] - WARNING - 现代化处理器创建失败，回退到传统处理器: QWidget(parent: Optional[QWidget] = None, flags: Union[Qt.WindowFlags, Qt.WindowType] = Qt.WindowFlags()): argument 1 has unexpected type 'MockMainWindow'
2025-06-02 01:09:57,315 - InitializationManager - [InitializationManager.py:95] - WARNING - 现代化处理器创建失败，回退到传统处理器: QWidget(parent: Optional[QWidget] = None, flags: Union[Qt.WindowFlags, Qt.WindowType] = Qt.WindowFlags()): argument 1 has unexpected type 'MockMainWindow'
2025-06-02 01:09:57,317 - InitializationManager - [InitializationManager.py:95] - WARNING - 现代化处理器创建失败，回退到传统处理器: QWidget(parent: Optional[QWidget] = None, flags: Union[Qt.WindowFlags, Qt.WindowType] = Qt.WindowFlags()): argument 1 has unexpected type 'MockMainWindow'
2025-06-02 01:09:57,318 - InitializationManager - [InitializationManager.py:95] - WARNING - 现代化处理器创建失败，回退到传统处理器: QWidget(parent: Optional[QWidget] = None, flags: Union[Qt.WindowFlags, Qt.WindowType] = Qt.WindowFlags()): argument 1 has unexpected type 'MockMainWindow'
2025-06-02 01:09:57,320 - InitializationManager - [InitializationManager.py:95] - WARNING - 现代化处理器创建失败，回退到传统处理器: QWidget(parent: Optional[QWidget] = None, flags: Union[Qt.WindowFlags, Qt.WindowType] = Qt.WindowFlags()): argument 1 has unexpected type 'MockMainWindow'
2025-06-02 01:09:57,324 - InitializationManager - [InitializationManager.py:95] - WARNING - 现代化处理器创建失败，回退到传统处理器: QWidget(parent: Optional[QWidget] = None, flags: Union[Qt.WindowFlags, Qt.WindowType] = Qt.WindowFlags()): argument 1 has unexpected type 'MockMainWindow'
2025-06-02 01:09:57,326 - InitializationManager - [InitializationManager.py:95] - WARNING - 现代化处理器创建失败，回退到传统处理器: QWidget(parent: Optional[QWidget] = None, flags: Union[Qt.WindowFlags, Qt.WindowType] = Qt.WindowFlags()): argument 1 has unexpected type 'MockMainWindow'
2025-06-02 01:10:50,648 - RegisterUpdateBus - [RegisterUpdateBus.py:57] - INFO - RegisterUpdateBus: Initial clock source set based on register default (0x57, CLKin_SEL_MANUAL): ClkIn1
2025-06-02 01:10:50,664 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:10:50,665 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:10:50,665 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:10:50,665 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:68] - INFO - 表格配置初始化完成
2025-06-02 01:10:51,028 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:107] - INFO - 位域表格创建完成
2025-06-02 01:10:51,028 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:86] - INFO - 表格UI创建完成
2025-06-02 01:10:51,029 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:10:51,029 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:10:51,029 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:46] - INFO - 现代化寄存器表格处理器初始化完成
2025-06-02 01:10:51,032 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:10:51,032 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:10:51,032 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 01:10:51,033 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:124] - DEBUG - 寄存器表格: 收到全局更新 0x02 = 0x5678
2025-06-02 01:10:51,033 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:575] - DEBUG - 寄存器IO: 收到全局更新 0x02 = 0x5678
2025-06-02 01:10:51,035 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:10:51,035 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:10:51,036 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:10:51,038 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:10:51,038 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:10:51,038 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 01:10:51,040 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:10:51,040 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:10:51,041 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:10:51,043 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:10:51,043 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:10:51,043 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:10:51,043 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:10:51,044 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:10:51,044 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:10:51,045 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:10:51,045 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:10:51,045 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 01:10:51,048 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:323] - DEBUG - 现代化IOHandler: 发出 write_requested 信号, 地址: 0x02, 值: 0x1234
2025-06-02 01:10:51,048 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:68] - INFO - 表格配置初始化完成
2025-06-02 01:10:51,049 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:107] - INFO - 位域表格创建完成
2025-06-02 01:10:51,049 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:86] - INFO - 表格UI创建完成
2025-06-02 01:10:51,049 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:10:51,049 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:10:51,050 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:46] - INFO - 现代化寄存器表格处理器初始化完成
2025-06-02 01:10:51,051 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:10:51,051 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:10:51,051 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:10:51,053 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:113] - INFO - 填充了 2 个寄存器到树视图
2025-06-02 01:10:51,054 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:10:51,054 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:10:51,054 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:10:51,054 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:113] - INFO - 填充了 2 个寄存器到树视图
2025-06-02 01:10:51,056 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:10:51,056 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:10:51,056 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:10:51,057 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:68] - INFO - 表格配置初始化完成
2025-06-02 01:10:51,057 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:107] - INFO - 位域表格创建完成
2025-06-02 01:10:51,058 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:86] - INFO - 表格UI创建完成
2025-06-02 01:10:51,058 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:10:51,058 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:10:51,058 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:46] - INFO - 现代化寄存器表格处理器初始化完成
2025-06-02 01:10:51,058 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:10:51,059 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:10:51,059 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:10:51,059 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:68] - INFO - 表格配置初始化完成
2025-06-02 01:10:51,059 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:107] - INFO - 位域表格创建完成
2025-06-02 01:10:51,060 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:86] - INFO - 表格UI创建完成
2025-06-02 01:10:51,060 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:10:51,060 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:10:51,060 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:46] - INFO - 现代化寄存器表格处理器初始化完成
2025-06-02 01:10:51,060 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:10:51,061 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:10:51,061 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 01:11:31,131 - RegisterUpdateBus - [RegisterUpdateBus.py:57] - INFO - RegisterUpdateBus: Initial clock source set based on register default (0x57, CLKin_SEL_MANUAL): ClkIn1
2025-06-02 01:11:31,143 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:11:31,144 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:11:31,144 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:11:31,144 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:68] - INFO - 表格配置初始化完成
2025-06-02 01:11:31,479 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:107] - INFO - 位域表格创建完成
2025-06-02 01:11:31,479 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:86] - INFO - 表格UI创建完成
2025-06-02 01:11:31,479 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:11:31,479 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:11:31,479 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:46] - INFO - 现代化寄存器表格处理器初始化完成
2025-06-02 01:11:31,480 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:11:31,480 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:11:31,481 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 01:11:31,481 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:124] - DEBUG - 寄存器表格: 收到全局更新 0x02 = 0x5678
2025-06-02 01:11:31,481 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:575] - DEBUG - 寄存器IO: 收到全局更新 0x02 = 0x5678
2025-06-02 01:11:31,482 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:11:31,482 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:11:31,482 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:11:31,482 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:11:31,483 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:11:31,483 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 01:11:31,483 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:11:31,484 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:11:31,484 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:11:31,484 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:11:31,484 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:11:31,485 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:11:31,485 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:11:31,485 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:11:31,486 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:11:31,487 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:11:31,487 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:11:31,487 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 01:11:31,491 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:323] - DEBUG - 现代化IOHandler: 发出 write_requested 信号, 地址: 0x02, 值: 0x1234
2025-06-02 01:11:31,492 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:68] - INFO - 表格配置初始化完成
2025-06-02 01:11:31,492 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:107] - INFO - 位域表格创建完成
2025-06-02 01:11:31,493 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:86] - INFO - 表格UI创建完成
2025-06-02 01:11:31,493 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:11:31,493 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:11:31,493 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:46] - INFO - 现代化寄存器表格处理器初始化完成
2025-06-02 01:11:31,494 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:11:31,495 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:11:31,495 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:11:31,496 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:113] - INFO - 填充了 2 个寄存器到树视图
2025-06-02 01:11:31,496 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:11:31,497 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:11:31,497 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:11:31,497 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:113] - INFO - 填充了 2 个寄存器到树视图
2025-06-02 01:11:31,498 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:11:31,498 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:11:31,498 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:11:31,498 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:68] - INFO - 表格配置初始化完成
2025-06-02 01:11:31,499 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:107] - INFO - 位域表格创建完成
2025-06-02 01:11:31,499 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:86] - INFO - 表格UI创建完成
2025-06-02 01:11:31,499 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:11:31,499 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:11:31,500 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:46] - INFO - 现代化寄存器表格处理器初始化完成
2025-06-02 01:11:31,501 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:11:31,501 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:11:31,502 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:11:31,502 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:68] - INFO - 表格配置初始化完成
2025-06-02 01:11:31,504 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:107] - INFO - 位域表格创建完成
2025-06-02 01:11:31,504 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:86] - INFO - 表格UI创建完成
2025-06-02 01:11:31,504 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:11:31,504 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:11:31,505 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:46] - INFO - 现代化寄存器表格处理器初始化完成
2025-06-02 01:11:31,506 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:11:31,506 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:11:31,506 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 01:12:04,391 - RegisterUpdateBus - [RegisterUpdateBus.py:57] - INFO - RegisterUpdateBus: Initial clock source set based on register default (0x57, CLKin_SEL_MANUAL): ClkIn1
2025-06-02 01:12:04,409 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:12:04,409 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:12:04,409 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:12:04,410 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:68] - INFO - 表格配置初始化完成
2025-06-02 01:12:04,804 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:107] - INFO - 位域表格创建完成
2025-06-02 01:12:04,804 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:86] - INFO - 表格UI创建完成
2025-06-02 01:12:04,804 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:12:04,804 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:12:04,804 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:46] - INFO - 现代化寄存器表格处理器初始化完成
2025-06-02 01:12:04,805 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:12:04,806 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:12:04,806 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 01:12:04,806 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:124] - DEBUG - 寄存器表格: 收到全局更新 0x02 = 0x5678
2025-06-02 01:12:04,806 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:575] - DEBUG - 寄存器IO: 收到全局更新 0x02 = 0x5678
2025-06-02 01:12:04,807 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:12:04,807 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:12:04,807 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:12:04,807 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:12:04,807 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:12:04,808 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 01:12:04,808 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:12:04,808 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:12:04,808 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:12:04,809 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:12:04,809 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:12:04,809 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:12:04,810 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:12:04,810 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:12:04,810 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:12:04,811 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:12:04,811 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:12:04,812 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
2025-06-02 01:12:04,814 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:323] - DEBUG - 现代化IOHandler: 发出 write_requested 信号, 地址: 0x02, 值: 0x1234
2025-06-02 01:12:04,815 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:68] - INFO - 表格配置初始化完成
2025-06-02 01:12:04,817 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:107] - INFO - 位域表格创建完成
2025-06-02 01:12:04,817 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:86] - INFO - 表格UI创建完成
2025-06-02 01:12:04,817 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:12:04,817 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:12:04,817 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:46] - INFO - 现代化寄存器表格处理器初始化完成
2025-06-02 01:12:04,819 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:12:04,819 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:12:04,819 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:174] - INFO - 已显示寄存器 0x00 的 1 个位域
2025-06-02 01:12:04,820 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:12:04,820 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:12:04,820 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:12:04,820 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:113] - INFO - 填充了 2 个寄存器到树视图
2025-06-02 01:12:04,821 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:12:04,821 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:12:04,821 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:12:04,821 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:113] - INFO - 填充了 2 个寄存器到树视图
2025-06-02 01:12:04,822 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:12:04,822 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:12:04,822 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:12:04,822 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:68] - INFO - 表格配置初始化完成
2025-06-02 01:12:04,823 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:107] - INFO - 位域表格创建完成
2025-06-02 01:12:04,823 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:86] - INFO - 表格UI创建完成
2025-06-02 01:12:04,823 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:12:04,823 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:12:04,823 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:46] - INFO - 现代化寄存器表格处理器初始化完成
2025-06-02 01:12:04,824 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:12:04,824 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:12:04,824 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:47] - INFO - 现代化寄存器树处理器初始化完成
2025-06-02 01:12:04,824 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:68] - INFO - 表格配置初始化完成
2025-06-02 01:12:04,825 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:107] - INFO - 位域表格创建完成
2025-06-02 01:12:04,825 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:86] - INFO - 表格UI创建完成
2025-06-02 01:12:04,825 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:12:04,825 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:12:04,825 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:46] - INFO - 现代化寄存器表格处理器初始化完成
2025-06-02 01:12:04,826 - ModernBaseHandler - [ModernBaseHandler.py:47] - INFO - ModernBaseHandler._post_init 被调用
2025-06-02 01:12:04,826 - ModernBaseHandler - [ModernBaseHandler.py:54] - WARNING - UI未设置，跳过控件映射构建
2025-06-02 01:12:04,826 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:63] - INFO - 现代化寄存器IO处理器初始化完成
