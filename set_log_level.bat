@echo off
chcp 65001 >nul
title 日志级别控制工具

echo.
echo ========================================
echo           日志级别控制工具
echo ========================================
echo.
echo 当前可用的日志级别:
echo   1. CRITICAL - 只显示严重错误 (最少日志)
echo   2. ERROR    - 显示错误和严重错误
echo   3. WARNING  - 显示警告、错误和严重错误 (推荐)
echo   4. INFO     - 显示一般信息和以上级别
echo   5. DEBUG    - 显示所有调试信息 (最多日志)
echo.
echo   S. 显示当前状态
echo   Q. 退出
echo.

:menu
set /p choice="请选择日志级别 (1-5, S, Q): "

if /i "%choice%"=="1" (
    python utils\log_control.py CRITICAL
    goto end
)
if /i "%choice%"=="2" (
    python utils\log_control.py ERROR
    goto end
)
if /i "%choice%"=="3" (
    python utils\log_control.py WARNING
    goto end
)
if /i "%choice%"=="4" (
    python utils\log_control.py INFO
    goto end
)
if /i "%choice%"=="5" (
    python utils\log_control.py DEBUG
    goto end
)
if /i "%choice%"=="S" (
    python utils\log_control.py status
    echo.
    pause
    goto menu
)
if /i "%choice%"=="Q" (
    goto end
)

echo 无效选择，请重新输入
goto menu

:end
echo.
pause
