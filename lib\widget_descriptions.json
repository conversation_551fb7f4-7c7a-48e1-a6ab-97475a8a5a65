{"description": "手动控件说明配置文件 - 用于为特殊控件提供自定义说明信息", "version": "1.0.0", "last_updated": "2025-01-01", "widgets": {"FreFin": {"type": "频率显示控件", "source": "时钟输入控制窗口", "description": "PLL1的输入信号频率。该信号来自于时钟输入控制窗口中选择的时钟源，是一个传导过来的值，不能直接编辑。当时钟输入控制窗口中的时钟源选择发生变化时，此控件会自动更新显示相应的频率值。", "calculation": "根据时钟输入控制窗口中选择的时钟源自动设置", "dependencies": ["时钟输入控制窗口的时钟源选择", "选定时钟源的频率设置"], "is_manual_info": true}, "VCODistFreq": {"type": "频率计算控件", "source": "PLL2计算结果", "description": "VCO输出频率，由PLL2的计算结果得出。该值是根据PLL2PFDFreq、PLL2NDivider和PLL2Prescaler或者feedback的分频的乘积计算得出的，用于为时钟输出提供基准频率。", "calculation": "VCODistFreq = PLL2PFDFreq × PLL2NDivider × PLL2Prescaler", "dependencies": ["PLL2PFDFreq", "PLL2NDivider", "PLL2Prescaler", "PLL2Cin"], "is_manual_info": true}, "PLL1PFDFreq": {"type": "频率计算控件", "source": "PLL1计算结果", "description": "PLL1的相位频率检测器频率。该值是PLL1锁相环计算的关键参数，用于确定PLL1的输出特性。", "calculation": "根据PLL1的输入频率和分频器设置计算", "dependencies": ["FreFin", "PLL1RDividerSetting"], "is_manual_info": true}, "PLL2PFDFreq": {"type": "频率计算控件", "source": "PLL2计算结果", "description": "PLL2的相位频率检测器频率。该值是PLL2锁相环计算的关键参数，根据PLL2RclkMux的选择使用不同的计算公式。", "calculation": "根据PLL2RclkMux选择：OSCin模式或PLL1 CLKin模式", "dependencies": ["PLL2RclkMux", "OSCinFreq或FreFin", "PLL2RDivider"], "is_manual_info": true}, "InternalVCOFreq": {"type": "频率计算控件", "source": "同步系统参考计算", "description": "内部VCO频率，在同步系统参考窗口中计算得出。该值与PLL窗口中的VCODistFreq保持同步。", "calculation": "PLL2PFD × spinBoxSysrefDIV", "dependencies": ["PLL2PFD", "spinBoxSysrefDIV"], "is_manual_info": true}, "PLL2Cin": {"type": "频率显示控件", "source": "跨窗口同步值", "description": "PLL2NDivider的输入频率显示。该控件的可见性由FBMuxEn复选框控制，显示的值根据FBMUX选择而定：选择clkout6/clkout8时显示相应的时钟输出值，选择sysref时显示同步系统参考窗口的SyncSysrefFreq1值。", "calculation": "根据FBMUX选择：CLKout频率或SyncSysrefFreq1", "dependencies": ["FBMuxEn复选框状态", "FBMUX选择", "CLKout6/CLKout8频率", "SyncSysrefFreq1"], "is_manual_info": true}, "OSCinFreq": {"type": "频率输入控件", "source": "用户输入", "description": "振荡器输入频率设置控件。用户可以在此输入外部振荡器的频率值，该值用于PLL2的参考时钟计算。当PLL2RclkMux选择OSCin模式时，此频率值将作为PLL2计算的基准。", "calculation": "用户手动输入的振荡器频率值", "dependencies": ["外部振荡器频率", "PLL2RclkMux设置"], "is_manual_info": true}, "ExternalVCXOFreq": {"type": "频率输入控件", "source": "用户输入", "description": "外部VCXO频率设置控件。用户可以在此输入外部VCXO（电压控制晶体振荡器）的频率值。该控件与OSCinFreq控件同步，当一个控件的值被修改时，另一个控件会自动更新以保持一致。", "calculation": "用户手动输入，与OSCinFreq同步", "dependencies": ["外部VCXO频率", "OSCinFreq控件"], "is_manual_info": true}, "Fin0Freq": {"type": "频率输入控件", "source": "用户输入", "description": "Fin0输入频率设置控件。用户可以在此设置Fin0输入端口的频率值。该频率值用于相关的时钟计算和PLL配置。Fin0是一个独立的时钟输入端口，可以作为系统的辅助时钟源。", "calculation": "用户手动输入的Fin0频率值", "dependencies": ["Fin0输入端口频率", "Fin0相关的时钟计算"], "is_manual_info": true}, "SyncSysrefFreq1": {"type": "频率计算控件", "source": "同步系统参考计算", "description": "同步系统参考频率1计算结果显示。该值是根据系统参考分频器和相关参数计算得出的同步参考频率，用于为其他窗口提供同步参考值。当PLL2Cin控件选择sysref模式时，会显示此频率值。", "calculation": "根据系统参考分频器和VCO频率计算", "dependencies": ["系统参考分频器设置", "VCO频率", "同步参考相关参数"], "is_manual_info": true}, "lineEditClkinSelOut": {"type": "频率显示控件", "source": "时钟输入选择结果", "description": "当前选择的时钟输入输出频率显示。该控件显示当前被选中的时钟输入源的频率值，是时钟输入选择逻辑的结果显示。该值会根据时钟输入选择的变化而自动更新。", "calculation": "当前选择的时钟输入源频率", "dependencies": ["时钟输入选择逻辑", "选中的时钟输入源", "对应时钟输入的频率设置"], "is_manual_info": true}, "lineEditClkin0": {"type": "频率输入控件", "source": "用户输入", "description": "CLKin0时钟输入频率设置控件。用户可以在此输入CLKin0端口的时钟频率值。CLKin0是主要的时钟输入端口之一，可以作为PLL的参考时钟源。该频率值用于PLL计算和时钟输入选择逻辑。", "calculation": "用户手动输入的CLKin0频率值", "dependencies": ["CLKin0输入端口频率", "PLL1R0Div分频器", "时钟输入选择逻辑"], "is_manual_info": true}, "lineEditClkin1": {"type": "频率输入控件", "source": "用户输入", "description": "CLKin1时钟输入频率设置控件。用户可以在此输入CLKin1端口的时钟频率值。CLKin1是主要的时钟输入端口之一，可以作为PLL的参考时钟源。该频率值用于PLL计算和时钟输入选择逻辑。", "calculation": "用户手动输入的CLKin1频率值", "dependencies": ["CLKin1输入端口频率", "PLL1R1Div分频器", "时钟输入选择逻辑"], "is_manual_info": true}, "lineEditClkin2Oscout": {"type": "频率输入控件", "source": "用户输入", "description": "CLKin2/OSCout时钟输入频率设置控件。用户可以在此输入CLKin2端口的时钟频率值，该端口也可以作为OSCout输出使用。该频率值用于PLL计算和时钟输入选择逻辑。", "calculation": "用户手动输入的CLKin2频率值", "dependencies": ["CLKin2输入端口频率", "PLL1R2Div分频器", "时钟输入选择逻辑", "OSCout功能配置"], "is_manual_info": true}, "DACUpdateRate": {"type": "频率计算控件", "source": "时钟输入窗口计算", "description": "DAC更新速率，在时钟输入窗口中计算。该值依赖于PLL窗口中的PLL1PFDFreq值，需要跨窗口同步以获取正确的计算结果。", "calculation": "基于PLL1PFDFreq的计算", "dependencies": ["PLL1PFDFreq"], "is_manual_info": true}, "lineEditFout6Output": {"type": "频率计算控件", "source": "时钟输出窗口计算", "description": "CLKout6输出频率。计算公式为VCODistFreq除以DCLK6_7DIV控件的值。该值用于其他窗口的频率计算和显示。", "calculation": "VCODistFreq / DCLK6_7DIV", "dependencies": ["VCODistFreq", "DCLK6_7DIV"], "is_manual_info": true}, "lineEditFout0Output": {"type": "频率计算控件", "source": "时钟输出窗口计算", "description": "CLKout0输出频率显示。根据SRCMUX状态选择计算方式：未选中SRCMUX时使用VCO频率除以DCLK0_1DIV分频器；选中SRCMUX时显示系统参考频率。", "calculation": "SRCMUX未选中: lineEditFvco / DCLK0_1DIV; SRCMUX选中: 系统参考频率", "dependencies": ["lineEditFvco", "DCLK0_1DIV", "CLKout0_SRCMUX", "系统参考频率"], "is_manual_info": true}, "lineEditFout1Output": {"type": "频率计算控件", "source": "时钟输出窗口计算", "description": "CLKout1输出频率显示。根据SRCMUX状态选择计算方式：未选中SRCMUX时使用VCO频率除以DCLK0_1DIV分频器；选中SRCMUX时显示系统参考频率。", "calculation": "SRCMUX未选中: lineEditFvco / DCLK0_1DIV; SRCMUX选中: 系统参考频率", "dependencies": ["lineEditFvco", "DCLK0_1DIV", "CLKout1_SRCMUX", "系统参考频率"], "is_manual_info": true}, "lineEditFout2Output": {"type": "频率计算控件", "source": "时钟输出窗口计算", "description": "CLKout2输出频率显示。根据SRCMUX状态选择计算方式：未选中SRCMUX时使用VCO频率除以DCLK2_3DIV分频器；选中SRCMUX时显示系统参考频率。", "calculation": "SRCMUX未选中: lineEditFvco / DCLK2_3DIV; SRCMUX选中: 系统参考频率", "dependencies": ["lineEditFvco", "DCLK2_3DIV", "CLKout2_SRCMUX", "系统参考频率"], "is_manual_info": true}, "lineEditFout3Output": {"type": "频率计算控件", "source": "时钟输出窗口计算", "description": "CLKout3输出频率显示。根据SRCMUX状态选择计算方式：未选中SRCMUX时使用VCO频率除以DCLK2_3DIV分频器；选中SRCMUX时显示系统参考频率。", "calculation": "SRCMUX未选中: lineEditFvco / DCLK2_3DIV; SRCMUX选中: 系统参考频率", "dependencies": ["lineEditFvco", "DCLK2_3DIV", "CLKout3_SRCMUX", "系统参考频率"], "is_manual_info": true}, "lineEditFout4Output": {"type": "频率计算控件", "source": "时钟输出窗口计算", "description": "CLKout4输出频率显示。根据SRCMUX状态选择计算方式：未选中SRCMUX时使用VCO频率除以DCLK4_5DIV分频器；选中SRCMUX时显示系统参考频率。", "calculation": "SRCMUX未选中: lineEditFvco / DCLK4_5DIV; SRCMUX选中: 系统参考频率", "dependencies": ["lineEditFvco", "DCLK4_5DIV", "CLKout4_SRCMUX", "系统参考频率"], "is_manual_info": true}, "lineEditFout5Output": {"type": "频率计算控件", "source": "时钟输出窗口计算", "description": "CLKout5输出频率显示。根据SRCMUX状态选择计算方式：未选中SRCMUX时使用VCO频率除以DCLK4_5DIV分频器；选中SRCMUX时显示系统参考频率。", "calculation": "SRCMUX未选中: lineEditFvco / DCLK4_5DIV; SRCMUX选中: 系统参考频率", "dependencies": ["lineEditFvco", "DCLK4_5DIV", "CLKout5_SRCMUX", "系统参考频率"], "is_manual_info": true}, "lineEditFout7Output": {"type": "频率计算控件", "source": "时钟输出窗口计算", "description": "CLKout7输出频率显示。根据SRCMUX状态选择计算方式：未选中SRCMUX时使用VCO频率除以DCLK6_7DIV分频器；选中SRCMUX时显示系统参考频率。", "calculation": "SRCMUX未选中: lineEditFvco / DCLK6_7DIV; SRCMUX选中: 系统参考频率", "dependencies": ["lineEditFvco", "DCLK6_7DIV", "CLKout7_SRCMUX", "系统参考频率"], "is_manual_info": true}, "lineEditFout8Output": {"type": "频率计算控件", "source": "时钟输出窗口计算", "description": "CLKout8输出频率显示。根据SRCMUX状态选择计算方式：未选中SRCMUX时使用VCO频率除以DCLK8_9DIV分频器；选中SRCMUX时显示系统参考频率。", "calculation": "SRCMUX未选中: lineEditFvco / DCLK8_9DIV; SRCMUX选中: 系统参考频率", "dependencies": ["lineEditFvco", "DCLK8_9DIV", "CLKout8_SRCMUX", "系统参考频率"], "is_manual_info": true}, "lineEditFout9Output": {"type": "频率计算控件", "source": "时钟输出窗口计算", "description": "CLKout9输出频率显示。根据SRCMUX状态选择计算方式：未选中SRCMUX时使用VCO频率除以DCLK8_9DIV分频器；选中SRCMUX时显示系统参考频率。", "calculation": "SRCMUX未选中: lineEditFvco / DCLK8_9DIV; SRCMUX选中: 系统参考频率", "dependencies": ["lineEditFvco", "DCLK8_9DIV", "CLKout9_SRCMUX", "系统参考频率"], "is_manual_info": true}, "lineEditFout10Output": {"type": "频率计算控件", "source": "时钟输出窗口计算", "description": "CLKout10输出频率显示。根据SRCMUX状态选择计算方式：未选中SRCMUX时使用VCO频率除以DCLK10_11DIV分频器；选中SRCMUX时显示系统参考频率。", "calculation": "SRCMUX未选中: lineEditFvco / DCLK10_11DIV; SRCMUX选中: 系统参考频率", "dependencies": ["lineEditFvco", "DCLK10_11DIV", "CLKout10_SRCMUX", "系统参考频率"], "is_manual_info": true}, "lineEditFout11Output": {"type": "频率计算控件", "source": "时钟输出窗口计算", "description": "CLKout11输出频率显示。根据SRCMUX状态选择计算方式：未选中SRCMUX时使用VCO频率除以DCLK10_11DIV分频器；选中SRCMUX时显示系统参考频率。", "calculation": "SRCMUX未选中: lineEditFvco / DCLK10_11DIV; SRCMUX选中: 系统参考频率", "dependencies": ["lineEditFvco", "DCLK10_11DIV", "CLKout11_SRCMUX", "系统参考频率"], "is_manual_info": true}, "lineEditFout12Output": {"type": "频率计算控件", "source": "时钟输出窗口计算", "description": "CLKout12输出频率显示。根据SRCMUX状态选择计算方式：未选中SRCMUX时使用VCO频率除以DCLK12_13DIV分频器；选中SRCMUX时显示系统参考频率。", "calculation": "SRCMUX未选中: lineEditFvco / DCLK12_13DIV; SRCMUX选中: 系统参考频率", "dependencies": ["lineEditFvco", "DCLK12_13DIV", "CLKout12_SRCMUX", "系统参考频率"], "is_manual_info": true}, "lineEditFout13Output": {"type": "频率计算控件", "source": "时钟输出窗口计算", "description": "CLKout13输出频率显示。根据SRCMUX状态选择计算方式：未选中SRCMUX时使用VCO频率除以DCLK12_13DIV分频器；选中SRCMUX时显示系统参考频率。", "calculation": "SRCMUX未选中: lineEditFvco / DCLK12_13DIV; SRCMUX选中: 系统参考频率", "dependencies": ["lineEditFvco", "DCLK12_13DIV", "CLKout13_SRCMUX", "系统参考频率"], "is_manual_info": true}, "lineEditFvco": {"type": "频率输入控件", "source": "PLL窗口同步", "description": "VCO频率输入控件，用于时钟输出频率计算的基准。该值通常从PLL窗口的VCODistFreq同步获得，是所有时钟输出频率计算的基础。", "calculation": "从PLL窗口VCODistFreq同步获取", "dependencies": ["PLL窗口VCODistFreq"], "is_manual_info": true}}}