#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理和整理packaging/launchers目录
保留最有用的启动器，移除重复和过时的文件
"""

import os
import shutil
from pathlib import Path
from datetime import datetime

def analyze_launchers():
    """分析当前的启动器文件"""
    launchers_dir = Path(__file__).parent / 'launchers'
    
    print("📋 当前启动器文件分析:")
    print("=" * 50)
    
    files = list(launchers_dir.glob('*'))
    
    # 分类文件
    bat_files = [f for f in files if f.suffix == '.bat']
    other_files = [f for f in files if f.suffix != '.bat']
    
    print(f"📁 BAT启动器文件 ({len(bat_files)}个):")
    for bat_file in bat_files:
        size = bat_file.stat().st_size
        print(f"  - {bat_file.name} ({size} bytes)")
    
    print(f"\n📄 其他文件 ({len(other_files)}个):")
    for other_file in other_files:
        size = other_file.stat().st_size
        print(f"  - {other_file.name} ({size} bytes)")
    
    return bat_files, other_files

def recommend_cleanup():
    """推荐清理方案"""
    print("\n💡 推荐的清理方案:")
    print("=" * 50)
    
    # 推荐保留的文件
    keep_files = {
        '优化打包工具.bat': '最新的统一打包工具，支持4种打包模式',
        '启动说明.txt': '使用说明文档'
    }
    
    # 可以移除的文件
    remove_files = {
        'FSJ04832_PackageManager.bat': '功能被优化打包工具.bat替代',
        '安全打包工具.bat': '功能被优化打包工具.bat包含',
        'debug.bat': '调试用，日常不需要',
        'quick_start.bat': '功能重复'
    }
    
    print("✅ 建议保留:")
    for file, reason in keep_files.items():
        print(f"  - {file}: {reason}")
    
    print("\n🗑️ 建议移除:")
    for file, reason in remove_files.items():
        print(f"  - {file}: {reason}")
    
    return keep_files, remove_files

def create_backup():
    """创建备份"""
    launchers_dir = Path(__file__).parent / 'launchers'
    backup_dir = Path(__file__).parent / 'backup' / 'launchers_backup' / datetime.now().strftime("%Y%m%d_%H%M%S")
    
    backup_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"\n📦 创建备份到: {backup_dir}")
    
    for file in launchers_dir.glob('*'):
        if file.is_file():
            shutil.copy2(file, backup_dir / file.name)
            print(f"  ✓ 备份: {file.name}")
    
    return backup_dir

def cleanup_launchers(keep_files, remove_files, create_backup_flag=True):
    """执行清理"""
    launchers_dir = Path(__file__).parent / 'launchers'
    
    if create_backup_flag:
        backup_dir = create_backup()
    
    print(f"\n🧹 开始清理...")
    
    removed_count = 0
    
    for file_name in remove_files.keys():
        file_path = launchers_dir / file_name
        if file_path.exists():
            try:
                file_path.unlink()
                print(f"  🗑️ 已删除: {file_name}")
                removed_count += 1
            except Exception as e:
                print(f"  ❌ 删除失败 {file_name}: {e}")
    
    print(f"\n✅ 清理完成！删除了 {removed_count} 个文件")
    
    # 显示剩余文件
    remaining_files = list(launchers_dir.glob('*'))
    print(f"\n📁 剩余文件 ({len(remaining_files)}个):")
    for file in remaining_files:
        if file.is_file():
            print(f"  - {file.name}")

def create_unified_launcher():
    """创建统一的启动器说明"""
    launchers_dir = Path(__file__).parent / 'launchers'
    readme_file = launchers_dir / 'README.md'
    
    readme_content = """# FSJ04832 打包工具启动器

## 📋 可用启动器

### 🚀 优化打包工具.bat
**主要启动器** - 提供完整的打包功能

**功能特性:**
- ✅ 标准打包 - 平衡功能和大小
- ✅ 安全打包 - 最大化代码保护  
- ✅ 紧凑打包 - 最小化文件大小
- ✅ 便携打包 - 目录模式，便于调试
- ✅ 版本信息查看

**使用方法:**
1. 双击运行 `优化打包工具.bat`
2. 根据需要选择打包模式
3. 等待打包完成

## 🔧 命令行方式

如果启动器无法使用，可以使用命令行：

```bash
cd packaging

# 标准打包
python package.py build patch

# 安全打包（推荐客户发布）
python package.py secure patch

# 紧凑打包（最小体积）
python package.py optimized patch

# 便携打包（开发调试）
python package.py portable patch
```

## 📋 系统要求

- Windows操作系统
- Python 3.8+ 已安装并添加到PATH
- PyInstaller已安装: `pip install pyinstaller`

## 🔧 故障排除

1. **双击没反应**: 右键"以管理员身份运行"
2. **Python未找到**: 确保Python已安装并添加到系统PATH
3. **打包失败**: 检查项目文件是否完整，运行 `python verify_module_fix.py` 验证配置

## 📞 技术支持

如有问题，请检查：
1. Python环境是否正确安装
2. 项目文件结构是否完整
3. 是否有权限问题

---
*最后更新: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}*
"""
    
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"📝 已创建统一说明文档: {readme_file}")

def main():
    """主函数"""
    print("🧹 FSJ04832 启动器清理工具")
    print("=" * 50)
    
    # 分析当前文件
    bat_files, other_files = analyze_launchers()
    
    # 推荐清理方案
    keep_files, remove_files = recommend_cleanup()
    
    # 询问用户是否执行清理
    print(f"\n❓ 是否执行清理？")
    print("  [y] 是，执行清理（会先创建备份）")
    print("  [n] 否，仅查看分析结果")
    
    choice = input("\n请选择 (y/n): ").lower().strip()
    
    if choice == 'y':
        # 执行清理
        cleanup_launchers(keep_files, remove_files)
        
        # 创建统一说明
        create_unified_launcher()
        
        print("\n🎉 清理完成！")
        print("\n📋 建议:")
        print("  1. 使用 '优化打包工具.bat' 进行所有打包操作")
        print("  2. 查看 'README.md' 了解详细使用说明")
        print("  3. 如需恢复，备份文件在 backup/launchers_backup/ 目录")
        
    else:
        print("\n💡 清理已取消，仅显示分析结果")
        print("如需清理，请重新运行此脚本并选择 'y'")

if __name__ == "__main__":
    main()
