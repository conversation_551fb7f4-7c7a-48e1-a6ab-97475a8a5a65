#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
版本历史查看工具
列出所有构建的版本和相关信息
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
packaging_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
project_root = os.path.dirname(packaging_root)
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(packaging_root, 'scripts'))

from build_exe import list_all_versions

def main():
    """主函数"""
    print("=" * 80)
    print("FSJ04832 版本历史查看工具")
    print("=" * 80)
    
    try:
        versions = list_all_versions()
        
        if not versions:
            print("📁 没有找到任何构建版本")
            print("\n💡 提示:")
            print("   - 使用版本管理工具构建第一个版本")
            print("   - 版本将保存在 'releases' 目录中")
            return
        
        print(f"📁 找到 {len(versions)} 个版本:")
        print()
        
        # 显示版本列表
        for i, version in enumerate(versions, 1):
            timestamp_str = version['timestamp'].strftime('%Y-%m-%d %H:%M:%S')
            size_info = get_directory_size(version['path'])
            
            print(f"{i:2d}. 版本 {version['version']}")
            print(f"    📅 构建时间: {timestamp_str}")
            print(f"    📂 目录名称: {version['name']}")
            print(f"    📁 路径: {version['path']}")
            print(f"    💾 大小: {size_info}")
            
            # 检查版本信息文件
            version_info_file = version['path'] / 'version_info.json'
            if version_info_file.exists():
                try:
                    import json
                    with open(version_info_file, 'r', encoding='utf-8') as f:
                        info = json.load(f)
                    print(f"    📋 应用名称: {info.get('app_name', 'N/A')}")
                    print(f"    🔧 可执行文件: {info.get('exe_name', 'N/A')}.exe")
                except:
                    pass
            
            print()
        
        # 显示最新版本链接信息
        latest_link = Path('releases/latest')
        if latest_link.exists():
            if latest_link.is_symlink():
                target = latest_link.readlink()
                print(f"🔗 最新版本链接: releases/latest -> {target}")
            else:
                print(f"📂 最新版本副本: releases/latest")
        else:
            print("⚠️  没有找到最新版本链接")
        
        print()
        print("=" * 80)
        print("💡 使用说明:")
        print("   - 每次构建都会创建独立的时间戳文件夹")
        print("   - 'releases/latest' 指向最新版本")
        print("   - 可以安全删除旧版本文件夹来节省空间")
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 获取版本信息失败: {str(e)}")
        import traceback
        traceback.print_exc()

def get_directory_size(path):
    """获取目录大小"""
    try:
        total_size = 0
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                if os.path.exists(filepath):
                    total_size += os.path.getsize(filepath)
        
        # 转换为人类可读格式
        if total_size < 1024:
            return f"{total_size} B"
        elif total_size < 1024 * 1024:
            return f"{total_size / 1024:.1f} KB"
        elif total_size < 1024 * 1024 * 1024:
            return f"{total_size / (1024 * 1024):.1f} MB"
        else:
            return f"{total_size / (1024 * 1024 * 1024):.1f} GB"
    except:
        return "未知"

if __name__ == "__main__":
    main()
