# 手动控件说明快速参考

## 概述
本文档提供所有已配置手动说明的控件快速参考表。

## 控件分类统计
- **总计**: 30个控件
- **PLL相关**: 9个控件
- **时钟输出**: 15个控件（14个输出+1个输入）
- **同步系统参考**: 1个控件
- **时钟输入**: 4个控件
- **其他**: 1个控件

## 配置优化说明
- **分频器控件**和**源选择控件**已从手动配置中移除
- 这些控件在register.json文件中已有完整的寄存器映射信息
- 手动配置仅保留无法从寄存器映射获取说明的特殊控件

## 详细控件列表

### PLL相关控件
| 控件名称 | 类型 | 来源 | 主要功能 |
|---------|------|------|----------|
| FreFin | 频率显示控件 | 时钟输入控制窗口 | PLL1输入信号频率 |
| VCODistFreq | 频率计算控件 | PLL2计算结果 | VCO输出频率 |
| PLL1PFDFreq | 频率计算控件 | PLL1计算结果 | PLL1相位频率检测器频率 |
| PLL2PFDFreq | 频率计算控件 | PLL2计算结果 | PLL2相位频率检测器频率 |
| PLL2Cin | 频率显示控件 | 跨窗口同步值 | PLL2NDivider输入频率 |
| InternalVCOFreq | 频率计算控件 | 同步系统参考计算 | 内部VCO频率 |
| OSCinFreq | 频率输入控件 | 用户输入 | 振荡器输入频率设置 |
| ExternalVCXOFreq | 频率输入控件 | 用户输入 | 外部VCXO频率设置 |
| Fin0Freq | 频率输入控件 | 用户输入 | Fin0输入频率设置 |

### 时钟输出控件
| 控件名称 | 类型 | 计算公式 | SRCMUX支持 |
|---------|------|----------|------------|
| lineEditFvco | 频率输入控件 | 从PLL窗口同步 | - |
| lineEditFout0Output | 频率计算控件 | Fvco/DCLK0_1DIV | ✓ |
| lineEditFout1Output | 频率计算控件 | Fvco/DCLK0_1DIV | ✓ |
| lineEditFout2Output | 频率计算控件 | Fvco/DCLK2_3DIV | ✓ |
| lineEditFout3Output | 频率计算控件 | Fvco/DCLK2_3DIV | ✓ |
| lineEditFout4Output | 频率计算控件 | Fvco/DCLK4_5DIV | ✓ |
| lineEditFout5Output | 频率计算控件 | Fvco/DCLK4_5DIV | ✓ |
| lineEditFout6Output | 频率计算控件 | Fvco/DCLK6_7DIV | ✓ |
| lineEditFout7Output | 频率计算控件 | Fvco/DCLK6_7DIV | ✓ |
| lineEditFout8Output | 频率计算控件 | Fvco/DCLK8_9DIV | ✓ |
| lineEditFout9Output | 频率计算控件 | Fvco/DCLK8_9DIV | ✓ |
| lineEditFout10Output | 频率计算控件 | Fvco/DCLK10_11DIV | ✓ |
| lineEditFout11Output | 频率计算控件 | Fvco/DCLK10_11DIV | ✓ |
| lineEditFout12Output | 频率计算控件 | Fvco/DCLK12_13DIV | ✓ |
| lineEditFout13Output | 频率计算控件 | Fvco/DCLK12_13DIV | ✓ |

### 同步系统参考控件
| 控件名称 | 类型 | 来源 | 主要功能 |
|---------|------|------|----------|
| SyncSysrefFreq1 | 频率计算控件 | 同步系统参考计算 | 同步系统参考频率1 |

### 时钟输入控件
| 控件名称 | 类型 | 来源 | 主要功能 |
|---------|------|------|----------|
| lineEditClkinSelOut | 频率显示控件 | 时钟输入选择结果 | 当前选择的时钟输入输出频率 |
| lineEditClkin0 | 频率输入控件 | 用户输入 | CLKin0时钟输入频率设置 |
| lineEditClkin1 | 频率输入控件 | 用户输入 | CLKin1时钟输入频率设置 |
| lineEditClkin2Oscout | 频率输入控件 | 用户输入 | CLKin2/OSCout时钟输入频率设置 |

### 已移除的控件类型
以下控件类型已从手动配置中移除，因为它们在register.json中已有完整映射：

#### 分频器控件（7个）
- **DCLK0_1DIV** - **DCLK12_13DIV**: 控制各对时钟输出的分频器
- 这些控件的说明信息可以从寄存器映射中获取

#### 源选择控件（14个）
- **CLKout0_SRCMUX** - **CLKout13_SRCMUX**: 控制时钟源选择
- 这些控件的说明信息可以从寄存器映射中获取

### 其他控件
| 控件名称 | 类型 | 来源 | 功能 |
|---------|------|------|------|
| DACUpdateRate | 频率计算控件 | 时钟输入窗口计算 | DAC更新速率 |

## 使用说明

### 查看控件说明
1. 启动应用程序
2. 打开信息面板插件
3. 将鼠标悬停在任何已配置的控件上
4. 在信息面板中查看详细说明

### 时钟输出控件特殊说明
- 所有时钟输出控件都支持SRCMUX功能
- SRCMUX未选中时：使用VCO频率除以对应分频器
- SRCMUX选中时：使用系统参考频率
- CLKout6和CLKout8的值用于其他窗口的计算

### 分频器和源选择控件说明
- 这些控件已从手动配置中移除
- 它们的说明信息可以通过鼠标悬停从寄存器映射中获取
- 信息面板会自动显示来自register.json的详细说明

## 配置文件位置
- 配置文件：`lib/widget_descriptions.json`
- 重新加载：点击信息面板中的"重新加载控件说明"按钮

## 扩展说明
如需为其他控件添加手动说明，请参考：
- `docs/widget_descriptions_guide.md` - 详细使用指南
- `examples/add_widget_description_example.py` - 添加示例
- `docs/manual_widget_descriptions_implementation.md` - 实现说明
