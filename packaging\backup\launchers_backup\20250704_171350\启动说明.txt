FSJ04832 打包管理系统 - 启动器说明
=====================================

本目录包含多个启动器，如果一个不能用，请尝试其他的：

📁 可用启动器（按推荐程度排序）：

1. FSJ04832_PackageManager.bat ⭐ 主要启动器
   - 最稳定的启动器
   - 详细的错误检查和诊断信息
   - 完整的交互式菜单
   - 自动路径检测和修正
   - 支持所有打包功能

2. quick_start.bat ⚡ 快速启动
   - 最简单的启动器
   - 直接启动GUI界面
   - 适合快速启动版本管理工具

3. debug.bat 🔧 诊断工具
   - 环境诊断工具
   - 用于检查Python环境和路径问题
   - 故障排除时使用

🚀 使用方法：
1. 推荐使用 FSJ04832_PackageManager.bat（功能最完整）
2. 快速启动可使用 quick_start.bat
3. 遇到问题时使用 debug.bat 进行诊断

⚠️ 系统要求：
- Windows操作系统
- Python 3.6+ 已安装并添加到PATH
- 确保项目文件结构完整

💡 功能说明：
- 选项1: 启动GUI版本管理工具
- 选项2: 快速构建（增加构建号）
- 选项3: 补丁构建（增加补丁版本）
- 选项4: 查看版本历史
- 选项5: 清理旧版本
- 选项6: 运行测试
- 选项7: 显示帮助
- 选项8: 退出

🔧 故障排除：
1. 如果双击没反应：
   - 尝试其他bat文件
   - 检查Python是否安装
   - 右键"以管理员身份运行"

2. 如果提示"Python未找到"：
   - 安装Python 3.6+
   - 确保Python添加到系统PATH

3. 如果提示"package.py未找到"：
   - 确保在packaging目录中运行
   - 检查文件结构是否完整

4. 如果仍有问题：
   - 运行debug.bat查看详细信息
   - 或直接在命令行运行：
     cd packaging
     python package.py gui

如有问题，请联系开发团队。
