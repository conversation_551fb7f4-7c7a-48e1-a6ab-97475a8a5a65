#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终插件菜单测试
验证插件菜单的鼠标点击响应
"""

import sys
import os

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget
from PyQt5.QtCore import QTimer
from core.services.plugin.PluginManager import plugin_manager
from core.services.plugin.PluginIntegrationService import PluginIntegrationService


class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("最终插件菜单测试")
        self.resize(800, 600)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # 添加说明标签
        self.info_label = QLabel("插件菜单测试窗口\n\n请测试以下操作：\n1. 直接点击插件菜单项\n2. 验证插件窗口是否正确打开\n3. 验证窗口位置是否合理")
        layout.addWidget(self.info_label)
        
        # 创建菜单栏
        self.menuBar()
        
        # 设置插件系统
        self.setup_plugins()
        
        # 设置自动关闭定时器
        self.close_timer = QTimer()
        self.close_timer.timeout.connect(self.close)
        self.close_timer.start(60000)  # 60秒后自动关闭
    
    def setup_plugins(self):
        """设置插件系统"""
        try:
            # 添加插件目录
            plugin_dir = os.path.join(project_root, "plugins")
            plugin_manager.add_plugin_directory(plugin_dir)
            
            # 扫描并初始化插件
            plugin_manager.scan_plugins()
            plugin_manager.initialize_plugins(self)
            
            # 创建插件集成服务
            self.plugin_service = PluginIntegrationService(self)
            self.plugin_service.initialize_plugins()
            
            print(f"✅ 插件系统初始化完成，发现 {len(plugin_manager.get_plugin_list())} 个插件")
            
            # 连接插件窗口信号
            self.plugin_service.plugin_window_opened.connect(self.on_plugin_window_opened)
            self.plugin_service.plugin_window_closed.connect(self.on_plugin_window_closed)
            
        except Exception as e:
            print(f"❌ 插件系统初始化失败: {str(e)}")
    
    def on_plugin_window_opened(self, plugin_name, window):
        """插件窗口打开处理"""
        print(f"🎉 插件窗口已打开: {plugin_name}")
        self.info_label.setText(f"插件菜单测试窗口\n\n✅ 成功打开插件: {plugin_name}\n\n窗口对象: {type(window).__name__}\n\n测试结果: 插件菜单点击响应正常！")
    
    def on_plugin_window_closed(self, plugin_name):
        """插件窗口关闭处理"""
        print(f"🔒 插件窗口已关闭: {plugin_name}")
        self.info_label.setText(f"插件菜单测试窗口\n\n🔒 插件已关闭: {plugin_name}\n\n可以继续测试其他插件...")


def main():
    """主函数"""
    app = QApplication([])
    
    # 创建测试窗口
    window = TestMainWindow()
    window.show()
    
    print("🚀 最终插件菜单测试开始")
    print("请在窗口中测试插件菜单的鼠标点击响应")
    print("程序将在60秒后自动关闭")
    
    # 运行应用
    app.exec_()
    
    print("✅ 测试完成")


if __name__ == "__main__":
    main()
