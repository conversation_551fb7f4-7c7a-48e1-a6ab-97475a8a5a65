#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试端口显示功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import QTimer
from ui.handlers.ModernRegisterIOHandler import ModernRegisterIOHandler
from core.services.spi.spi_service_impl import SPIServiceImpl
from core.repositories.register_repository import RegisterRepository
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class TestPortWindow(QMainWindow):
    """测试端口显示的窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("测试端口显示")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        try:
            # 创建真实的SPI服务
            spi_service = SPIServiceImpl()
            spi_service.initialize()
            register_repo = RegisterRepository(spi_service)
            
            # 创建现代化IO处理器
            self.io_handler = ModernRegisterIOHandler.create_for_testing(self)
            
            # 但是使用真实的SPI服务
            self.io_handler.spi_service = spi_service
            self.io_handler.register_repo = register_repo
            
            # 重新连接信号
            if hasattr(spi_service, 'ports_refreshed'):
                spi_service.ports_refreshed.connect(self.io_handler._update_port_combo)
            
            layout.addWidget(self.io_handler)
            
            # 延迟刷新端口
            QTimer.singleShot(1000, self.refresh_ports)
            
            logger.info("测试端口窗口初始化完成")
            
        except Exception as e:
            logger.error(f"初始化测试端口窗口时出错: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def refresh_ports(self):
        """刷新端口"""
        try:
            logger.info("手动刷新端口...")
            if hasattr(self.io_handler, 'refresh_ports'):
                self.io_handler.refresh_ports()
            
            # 检查端口下拉框状态
            if hasattr(self.io_handler, 'port_combo') and self.io_handler.port_combo:
                count = self.io_handler.port_combo.count()
                logger.info(f"端口下拉框中有 {count} 个项目")
                
                for i in range(count):
                    text = self.io_handler.port_combo.itemText(i)
                    data = self.io_handler.port_combo.itemData(i)
                    logger.info(f"  项目 {i}: 文本='{text}', 数据='{data}'")
            else:
                logger.error("端口下拉框不存在")
                
        except Exception as e:
            logger.error(f"刷新端口时出错: {str(e)}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    print("开始测试端口显示功能...")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestPortWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
