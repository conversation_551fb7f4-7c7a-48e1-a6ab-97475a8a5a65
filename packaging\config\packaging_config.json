{"packaging_info": {"name": "FSJ04832 寄存器配置工具打包系统", "description": "优化的版本管理和打包解决方案", "version": "2.0.0", "author": "开发团队", "created_date": "2025-06-04", "last_updated": "2025-07-04 16:39:34"}, "build_config": {"default_version_type": "build", "output_base_dir": "../releases", "spec_file": "scripts/build.spec", "main_script": "../main.py", "app_name": "FSJ04832 寄存器配置工具", "exe_base_name": "FSJConfigTool", "spec_files": {"standard": "scripts/build.spec", "secure": "scripts/build_secure.spec", "optimized": "scripts/build_optimized.spec"}}, "paths": {"project_root": "..", "scripts_dir": "scripts", "tools_dir": "tools", "config_dir": "config", "docs_dir": "docs", "tests_dir": "tests", "launchers_dir": "launchers"}, "version_config": {"version_file": "config/version.json", "backup_versions": true, "max_versions_to_keep": 20, "auto_cleanup": false}, "build_options": {"clean_before_build": true, "create_version_info": true, "create_latest_link": true, "verify_build": true, "copy_additional_files": true}, "additional_files": [{"source": "../config", "destination": "config", "required": true}, {"source": "../images", "destination": "images", "required": true}, {"source": "../lib", "destination": "lib", "required": false}], "gui_config": {"window_title": "FSJ04832 版本管理和打包工具", "window_size": [1000, 750], "font_family": "Microsoft YaHei", "font_size": 10, "theme": "default"}, "encoding": {"use_safe_encoding": true, "fallback_to_ascii": true, "console_encoding": "utf-8"}, "logging": {"log_level": "WARNING", "log_file": "packaging.log", "max_log_size": "10MB", "backup_count": 5}, "optimization": {"file_exclusions": {"excel_files": ["*.xlsx", "*.xls", "*.xlsm"], "temp_files": ["~$*", "*.tmp", "*.temp"], "dev_files": ["app.json.template", "local.json", "migration.json"], "test_files": ["test_*", "*_test.py", "*.log", "*.log.*"], "docs": ["*.md", "*.txt", "README*"], "cache": ["__pycache__", "*.pyc", "*.pyo"], "backup": ["*.bak", "*.backup", "*.old"]}, "resource_optimization": {"enabled": true, "image_compression": true, "config_minification": true}, "size_optimization": {"exclude_large_modules": true, "strip_debug_info": true, "upx_compression": true}}, "security": {"code_protection": {"bytecode_encryption": true, "anti_debug": true, "runtime_verification": true}, "file_protection": {"single_file_mode": true, "config_obfuscation": true, "strip_symbols": true}}, "version_management": {"enhanced_versioning": true, "semantic_versioning": true, "build_tracking": true, "git_integration": true, "release_notes": true}}