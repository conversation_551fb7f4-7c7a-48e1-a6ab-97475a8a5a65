#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
分析传统PLL处理器移除的可行性
检查所有对PLLHandler的引用和依赖
"""

import sys
import os
import re

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)


def find_pll_handler_references():
    """查找所有对PLLHandler的引用"""
    print("=" * 60)
    print("分析传统PLL处理器(PLLHandler)的引用情况")
    print("=" * 60)
    
    references = []
    
    # 要扫描的目录
    scan_dirs = [
        'ui/factories',
        'ui/managers', 
        'ui/windows',
        'ui/handlers',
        'test_*.py',
        '*.py'
    ]
    
    # 要查找的模式
    patterns = [
        r'from\s+ui\.handlers\.PLLHandler\s+import\s+PLLHandler',
        r'import\s+ui\.handlers\.PLLHandler',
        r'PLLHandler\(',
        r'ui\.handlers\.PLLHandler\.PLLHandler',
        r'\'ui\.handlers\.PLLHandler\.PLLHandler\'',
        r'"ui\.handlers\.PLLHandler\.PLLHandler"',
    ]
    
    print("1. 扫描文件中的PLLHandler引用...")
    
    for root, dirs, files in os.walk(project_root):
        # 跳过一些不需要的目录
        dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'build', 'dist']]
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                rel_path = os.path.relpath(file_path, project_root)
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    for i, line in enumerate(content.split('\n'), 1):
                        for pattern in patterns:
                            if re.search(pattern, line):
                                references.append({
                                    'file': rel_path,
                                    'line': i,
                                    'content': line.strip(),
                                    'pattern': pattern
                                })
                                
                except Exception as e:
                    print(f"   ⚠️  无法读取文件 {rel_path}: {str(e)}")
    
    return references


def analyze_references(references):
    """分析引用的类型和重要性"""
    print(f"\n2. 分析发现的 {len(references)} 个引用...")
    
    # 按文件分组
    by_file = {}
    for ref in references:
        file = ref['file']
        if file not in by_file:
            by_file[file] = []
        by_file[file].append(ref)
    
    critical_files = []
    test_files = []
    config_files = []
    
    for file, refs in by_file.items():
        print(f"\n📁 {file}:")
        for ref in refs:
            print(f"   第{ref['line']}行: {ref['content']}")
        
        # 分类文件
        if file.startswith('test_'):
            test_files.append(file)
        elif 'factory' in file.lower() or 'manager' in file.lower() or 'window' in file.lower():
            critical_files.append(file)
        else:
            config_files.append(file)
    
    return critical_files, test_files, config_files


def check_modern_factory_status():
    """检查现代化工厂的状态"""
    print("\n3. 检查现代化工厂状态...")
    
    try:
        from ui.factories.ModernToolWindowFactory import ModernToolWindowFactory
        
        # 创建模拟主窗口
        class MockMainWindow:
            def __init__(self):
                pass
        
        mock_main_window = MockMainWindow()
        factory = ModernToolWindowFactory(mock_main_window)
        
        pll_config = factory.HANDLER_CONFIGS.get('pll_control')
        if pll_config:
            print("✓ 现代化工厂中PLL配置存在")
            print(f"  - 使用现代化版本: {pll_config.get('use_modern', False)}")
            print(f"  - 现代化处理器: {pll_config.get('modern_handler')}")
            print(f"  - 传统处理器: {pll_config.get('legacy_handler')}")
            
            if pll_config.get('use_modern', False):
                print("✅ 现代化工厂默认使用现代化处理器")
                return True
            else:
                print("⚠️  现代化工厂仍使用传统处理器")
                return False
        else:
            print("❌ 现代化工厂中没有PLL配置")
            return False
            
    except Exception as e:
        print(f"❌ 检查现代化工厂时出错: {str(e)}")
        return False


def test_modern_pll_functionality():
    """测试现代化PLL处理器功能"""
    print("\n4. 测试现代化PLL处理器功能...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建寄存器管理器
        from core.services.register.RegisterManager import RegisterManager
        config_path = os.path.join(project_root, 'lib', 'register.json')
        
        if not os.path.exists(config_path):
            print("❌ 寄存器配置文件不存在")
            return False

        import json
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        register_manager = RegisterManager(registers_config)
        
        # 测试现代化处理器
        from ui.handlers.ModernPLLHandler import ModernPLLHandler
        modern_handler = ModernPLLHandler(None, register_manager)
        
        print("✓ 现代化PLL处理器创建成功")
        
        # 检查关键功能
        if hasattr(modern_handler, 'calculate_output_frequencies'):
            modern_handler.calculate_output_frequencies()
            print("✓ 频率计算功能正常")
        
        if hasattr(modern_handler, 'ui') and hasattr(modern_handler.ui, 'PLL1PD'):
            print("✓ UI控件正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 现代化PLL处理器测试失败: {str(e)}")
        return False


def generate_removal_plan(critical_files, test_files):
    """生成移除计划"""
    print("\n5. 生成移除计划...")
    
    print("\n📋 需要修改的关键文件:")
    for file in critical_files:
        print(f"   - {file}")
    
    print("\n📋 需要更新的测试文件:")
    for file in test_files:
        print(f"   - {file}")
    
    print("\n🔧 建议的移除步骤:")
    print("1. 更新工厂配置，移除对传统PLLHandler的引用")
    print("2. 更新管理器类，使用现代化工厂")
    print("3. 更新测试文件，移除对传统处理器的测试")
    print("4. 备份并移除PLLHandler.py文件")
    print("5. 运行完整测试确保功能正常")


def main():
    """主函数"""
    # 查找引用
    references = find_pll_handler_references()
    
    # 分析引用
    critical_files, test_files, config_files = analyze_references(references)
    
    # 检查现代化工厂状态
    modern_factory_ready = check_modern_factory_status()
    
    # 测试现代化功能
    modern_functionality_ok = test_modern_pll_functionality()
    
    # 生成移除计划
    generate_removal_plan(critical_files, test_files)
    
    # 总结评估
    print("\n" + "=" * 60)
    print("移除可行性评估")
    print("=" * 60)
    
    if modern_factory_ready and modern_functionality_ok:
        print("✅ 可以安全移除传统PLL处理器")
        print("\n📊 评估结果:")
        print(f"   - 发现 {len(references)} 个引用")
        print(f"   - 关键文件: {len(critical_files)} 个")
        print(f"   - 测试文件: {len(test_files)} 个")
        print("   - 现代化工厂: ✅ 就绪")
        print("   - 现代化功能: ✅ 正常")
        
        print("\n🎯 建议操作:")
        print("1. 可以开始移除传统PLL处理器")
        print("2. 按照生成的移除计划逐步执行")
        print("3. 每步完成后运行测试验证")
        
        return True
    else:
        print("❌ 暂时不建议移除传统PLL处理器")
        print("\n📊 评估结果:")
        print(f"   - 发现 {len(references)} 个引用")
        print(f"   - 关键文件: {len(critical_files)} 个")
        print(f"   - 测试文件: {len(test_files)} 个")
        print(f"   - 现代化工厂: {'✅' if modern_factory_ready else '❌'} {'就绪' if modern_factory_ready else '未就绪'}")
        print(f"   - 现代化功能: {'✅' if modern_functionality_ok else '❌'} {'正常' if modern_functionality_ok else '异常'}")
        
        print("\n⚠️  建议操作:")
        print("1. 先解决现代化工厂或功能问题")
        print("2. 确保现代化版本完全可用")
        print("3. 再考虑移除传统版本")
        
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
