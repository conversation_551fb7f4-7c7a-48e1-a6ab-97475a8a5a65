(['E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\main.py'],
 ['E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2'],
 ['PyQt5.QtCore',
  'PyQt5.QtWidgets',
  'PyQt5.QtGui',
  'PyQt5.QtPrintSupport',
  'serial',
  'serial.tools.list_ports',
  'core.event_bus',
  'core.services.spi.spi_service',
  'core.services.spi.spi_service_impl',
  'core.services.spi.port_manager',
  'core.services.register.RegisterOperationService',
  'core.services.version.VersionService',
  'core.services.DIContainer',
  'core.services.plugin.PluginManager',
  'core.services.plugin.PluginIntegrationService',
  'core.services.plugin.menu.PluginMenuService',
  'core.services.plugin.window.PluginWindowService',
  'ui.handlers.ModernBaseHandler',
  'ui.handlers.ModernSetModesHandler',
  'ui.handlers.ModernClkinControlHandler',
  'ui.handlers.ModernPLLHandler',
  'ui.handlers.ModernSyncSysRefHandler',
  'ui.handlers.ModernClkOutputsHandler',
  'ui.handlers.ModernRegisterTableHandler',
  'ui.handlers.ModernUIEventHandler',
  'ui.handlers.ModernRegisterIOHandler',
  'ui.handlers.ModernRegisterTreeHandler',
  'ui.handlers.BaseHandler',
  'ui.handlers.SetModesHandler',
  'ui.handlers.ClkinControlHandler',
  'ui.handlers.RegisterTableHandler',
  'ui.handlers.UIEventHandler',
  'ui.handlers.RegisterIOHandler',
  'ui.handlers.RegisterTreeHandler',
  'ui.managers.InitializationManager',
  'ui.managers.StatusAndConfigManager',
  'ui.managers.SPISignalManager',
  'ui.managers.RegisterDisplayManager',
  'ui.managers.UIManager',
  'ui.managers.BatchOperationManager',
  'ui.components.MenuManager',
  'ui.components.ProgressBarManager',
  'ui.components.StatusBarManager',
  'ui.factories.ModernToolWindowFactory',
  'ui.factories.ToolWindowFactory',
  'ui.tools.PluginManagerGUI',
  'utils.Log',
  'utils.configFileHandler',
  'utils.address_utils',
  'utils.error_handler',
  'utils.message_strings',
  'plugins.set_modes_plugin',
  'plugins.clkin_control_plugin',
  'plugins.pll_control_plugin',
  'plugins.sync_sysref_plugin',
  'plugins.clk_output_plugin',
  'plugins.selective_register_plugin',
  'plugins.performance_monitor_plugin',
  'plugins.data_analysis_plugin',
  'plugins.example_tool_plugin',
  'json',
  'pathlib',
  'importlib',
  'importlib.util',
  'inspect',
  'threading',
  'queue',
  'datetime',
  'time',
  'logging',
  'logging.handlers',
  'configparser',
  'collections',
  'functools',
  'weakref',
  'copy',
  'pickle',
  'base64',
  'hashlib',
  'uuid',
  'platform',
  'subprocess',
  'shutil',
  'tempfile',
  'zipfile',
  'tarfile',
  'gzip',
  're',
  'math',
  'statistics',
  'random',
  'itertools',
  'operator',
  'types',
  'typing'],
 ['D:\\Program Files\\Python38\\Lib\\site-packages\\numpy\\_pyinstaller',
  'd:\\program '
  'files\\python38\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\stdhooks',
  'd:\\program '
  'files\\python38\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks'],
 {},
 ['tkinter',
  'matplotlib',
  'numpy',
  'pandas',
  'scipy',
  'PIL',
  'cv2',
  '__main__'],
 [],
 False,
 {},
 [],
 [('config\\README.md',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\config\\README.md',
   'DATA'),
  ('config\\app.json.template',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\config\\app.json.template',
   'DATA'),
  ('config\\default.json',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\config\\default.json',
   'DATA'),
  ('config\\local.json',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\config\\local.json',
   'DATA'),
  ('config\\migration.json',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\config\\migration.json',
   'DATA'),
  ('config\\selective_register_plugin.json',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\config\\selective_register_plugin.json',
   'DATA'),
  ('gui\\cascaded_0_delay_dual_loop.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\gui\\cascaded_0_delay_dual_loop.jpg',
   'DATA'),
  ('gui\\clkInput.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\gui\\clkInput.jpg',
   'DATA'),
  ('gui\\dual_loop_0_delay_cascaded.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\gui\\dual_loop_0_delay_cascaded.jpg',
   'DATA'),
  ('gui\\dual_loop_mode.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\gui\\dual_loop_mode.jpg',
   'DATA'),
  ('gui\\dual_loop_mode_calculate.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\gui\\dual_loop_mode_calculate.jpg',
   'DATA'),
  ('gui\\dualloop0_delay_nested.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\gui\\dualloop0_delay_nested.jpg',
   'DATA'),
  ('gui\\nested_0_delay_dual_loop_mode.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\gui\\nested_0_delay_dual_loop_mode.jpg',
   'DATA'),
  ('gui\\pll.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\gui\\pll.jpg',
   'DATA'),
  ('gui\\singleLoop.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\gui\\singleLoop.jpg',
   'DATA'),
  ('gui\\systemREF.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\gui\\systemREF.jpg',
   'DATA'),
  ('images\\logo.ico',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\images\\logo.ico',
   'DATA'),
  ('lib\\register.json',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\lib\\register.json',
   'DATA'),
  ('plugins\\__pycache__\\clk_output_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\__pycache__\\clk_output_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\clkin_control_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\__pycache__\\clkin_control_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\data_analysis_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\__pycache__\\data_analysis_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\drag_dock_test_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\__pycache__\\drag_dock_test_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\example_tool_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\__pycache__\\example_tool_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\performance_monitor_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\__pycache__\\performance_monitor_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\pll_control_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\__pycache__\\pll_control_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\selective_register_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\__pycache__\\selective_register_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\set_modes_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\__pycache__\\set_modes_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\sync_sysref_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\__pycache__\\sync_sysref_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\clk_output_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\clk_output_plugin.py',
   'DATA'),
  ('plugins\\clkin_control_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\clkin_control_plugin.py',
   'DATA'),
  ('plugins\\config\\__pycache__\\selective_register_config.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\config\\__pycache__\\selective_register_config.cpython-38.pyc',
   'DATA'),
  ('plugins\\config\\selective_register_config.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\config\\selective_register_config.py',
   'DATA'),
  ('plugins\\data_analysis_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\data_analysis_plugin.py',
   'DATA'),
  ('plugins\\example_tool_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\example_tool_plugin.py',
   'DATA'),
  ('plugins\\performance_monitor_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\performance_monitor_plugin.py',
   'DATA'),
  ('plugins\\pll_control_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\pll_control_plugin.py',
   'DATA'),
  ('plugins\\selective_register_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\selective_register_plugin.py',
   'DATA'),
  ('plugins\\set_modes_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\set_modes_plugin.py',
   'DATA'),
  ('plugins\\sync_sysref_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\sync_sysref_plugin.py',
   'DATA'),
  ('ui\\forms\\ClkOutputs.ui',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\ClkOutputs.ui',
   'DATA'),
  ('ui\\forms\\PLL1_2.ui',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\PLL1_2.ui',
   'DATA'),
  ('ui\\forms\\Ui_ClkOutputs.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\Ui_ClkOutputs.py',
   'DATA'),
  ('ui\\forms\\Ui_PLL1_2.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\Ui_PLL1_2.py',
   'DATA'),
  ('ui\\forms\\Ui_clkinControl.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\Ui_clkinControl.py',
   'DATA'),
  ('ui\\forms\\Ui_setModes.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\Ui_setModes.py',
   'DATA'),
  ('ui\\forms\\Ui_syncSysref.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\Ui_syncSysref.py',
   'DATA'),
  ('ui\\forms\\__pycache__\\Ui_ClkOutputs.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\__pycache__\\Ui_ClkOutputs.cpython-38.pyc',
   'DATA'),
  ('ui\\forms\\__pycache__\\Ui_PLL1_2.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\__pycache__\\Ui_PLL1_2.cpython-38.pyc',
   'DATA'),
  ('ui\\forms\\__pycache__\\Ui_clkinControl.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\__pycache__\\Ui_clkinControl.cpython-38.pyc',
   'DATA'),
  ('ui\\forms\\__pycache__\\Ui_setModes.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\__pycache__\\Ui_setModes.cpython-38.pyc',
   'DATA'),
  ('ui\\forms\\__pycache__\\Ui_syncSysref.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\__pycache__\\Ui_syncSysref.cpython-38.pyc',
   'DATA'),
  ('ui\\forms\\clkinControl.ui',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\clkinControl.ui',
   'DATA'),
  ('ui\\forms\\log\\app.log',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\log\\app.log',
   'DATA'),
  ('ui\\forms\\setModes.ui',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\setModes.ui',
   'DATA'),
  ('ui\\forms\\syncSysref.ui',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\syncSysref.ui',
   'DATA'),
  ('ui\\resources\\PLL1_2_rc.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\resources\\PLL1_2_rc.py',
   'DATA'),
  ('ui\\resources\\__pycache__\\PLL1_2_rc.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\resources\\__pycache__\\PLL1_2_rc.cpython-38.pyc',
   'DATA'),
  ('ui\\resources\\__pycache__\\clkinControl_rc.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\resources\\__pycache__\\clkinControl_rc.cpython-38.pyc',
   'DATA'),
  ('ui\\resources\\__pycache__\\clkoutput_rc.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\resources\\__pycache__\\clkoutput_rc.cpython-38.pyc',
   'DATA'),
  ('ui\\resources\\__pycache__\\setModes_rc.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\resources\\__pycache__\\setModes_rc.cpython-38.pyc',
   'DATA'),
  ('ui\\resources\\__pycache__\\syncSysref_rc.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\resources\\__pycache__\\syncSysref_rc.cpython-38.pyc',
   'DATA'),
  ('ui\\resources\\clkinControl_rc.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\resources\\clkinControl_rc.py',
   'DATA'),
  ('ui\\resources\\clkoutput_rc.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\resources\\clkoutput_rc.py',
   'DATA'),
  ('ui\\resources\\setModes_rc.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\resources\\setModes_rc.py',
   'DATA'),
  ('ui\\resources\\syncSysref_rc.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\resources\\syncSysref_rc.py',
   'DATA')],
 '3.8.0 (tags/v3.8.0:fa919fd, Oct 14 2019, 19:37:50) [MSC v.1916 64 bit '
 '(AMD64)]',
 [('pyi_rth_pkgutil',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('main',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\main.py',
   'PYSOURCE')],
 [('_pyi_rth_utils',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'd:\\program files\\python38\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'd:\\program files\\python38\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'd:\\program files\\python38\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'd:\\program files\\python38\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('signal', 'd:\\program files\\python38\\lib\\signal.py', 'PYMODULE'),
  ('selectors', 'd:\\program files\\python38\\lib\\selectors.py', 'PYMODULE'),
  ('xmlrpc.client',
   'd:\\program files\\python38\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'd:\\program files\\python38\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'd:\\program files\\python38\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'd:\\program files\\python38\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'd:\\program files\\python38\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'd:\\program files\\python38\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'd:\\program files\\python38\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'd:\\program files\\python38\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('fnmatch', 'd:\\program files\\python38\\lib\\fnmatch.py', 'PYMODULE'),
  ('getpass', 'd:\\program files\\python38\\lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'd:\\program files\\python38\\lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'd:\\program files\\python38\\lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'd:\\program files\\python38\\lib\\netrc.py', 'PYMODULE'),
  ('shlex', 'd:\\program files\\python38\\lib\\shlex.py', 'PYMODULE'),
  ('mimetypes', 'd:\\program files\\python38\\lib\\mimetypes.py', 'PYMODULE'),
  ('getopt', 'd:\\program files\\python38\\lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'd:\\program files\\python38\\lib\\gettext.py', 'PYMODULE'),
  ('email.utils',
   'd:\\program files\\python38\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email.charset',
   'd:\\program files\\python38\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'd:\\program files\\python38\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('quopri', 'd:\\program files\\python38\\lib\\quopri.py', 'PYMODULE'),
  ('email.errors',
   'd:\\program files\\python38\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.quoprimime',
   'd:\\program files\\python38\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.base64mime',
   'd:\\program files\\python38\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._parseaddr',
   'd:\\program files\\python38\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar', 'd:\\program files\\python38\\lib\\calendar.py', 'PYMODULE'),
  ('argparse', 'd:\\program files\\python38\\lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'd:\\program files\\python38\\lib\\textwrap.py', 'PYMODULE'),
  ('http.cookiejar',
   'd:\\program files\\python38\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http', 'd:\\program files\\python38\\lib\\http\\__init__.py', 'PYMODULE'),
  ('urllib',
   'd:\\program files\\python38\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('ssl', 'd:\\program files\\python38\\lib\\ssl.py', 'PYMODULE'),
  ('urllib.response',
   'd:\\program files\\python38\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'd:\\program files\\python38\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('contextlib', 'd:\\program files\\python38\\lib\\contextlib.py', 'PYMODULE'),
  ('string', 'd:\\program files\\python38\\lib\\string.py', 'PYMODULE'),
  ('email', 'd:\\program files\\python38\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser',
   'd:\\program files\\python38\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email._policybase',
   'd:\\program files\\python38\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.feedparser',
   'd:\\program files\\python38\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.message',
   'd:\\program files\\python38\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'd:\\program files\\python38\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'd:\\program files\\python38\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   'd:\\program files\\python38\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'd:\\program files\\python38\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'd:\\program files\\python38\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'd:\\program files\\python38\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('uu', 'd:\\program files\\python38\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'd:\\program files\\python38\\lib\\optparse.py', 'PYMODULE'),
  ('email._header_value_parser',
   'd:\\program files\\python38\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   'd:\\program files\\python38\\lib\\email\\header.py',
   'PYMODULE'),
  ('bisect', 'd:\\program files\\python38\\lib\\bisect.py', 'PYMODULE'),
  ('xml.sax',
   'd:\\program files\\python38\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'd:\\program files\\python38\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'd:\\program files\\python38\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'd:\\program files\\python38\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('urllib.parse',
   'd:\\program files\\python38\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('http.client',
   'd:\\program files\\python38\\lib\\http\\client.py',
   'PYMODULE'),
  ('decimal', 'd:\\program files\\python38\\lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'd:\\program files\\python38\\lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars',
   'd:\\program files\\python38\\lib\\contextvars.py',
   'PYMODULE'),
  ('numbers', 'd:\\program files\\python38\\lib\\numbers.py', 'PYMODULE'),
  ('hmac', 'd:\\program files\\python38\\lib\\hmac.py', 'PYMODULE'),
  ('struct', 'd:\\program files\\python38\\lib\\struct.py', 'PYMODULE'),
  ('socket', 'd:\\program files\\python38\\lib\\socket.py', 'PYMODULE'),
  ('multiprocessing.util',
   'd:\\program files\\python38\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'd:\\program files\\python38\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'd:\\program files\\python38\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'd:\\program files\\python38\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'd:\\program files\\python38\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'd:\\program files\\python38\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'd:\\program files\\python38\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes',
   'd:\\program files\\python38\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'd:\\program files\\python38\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'd:\\program files\\python38\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'd:\\program files\\python38\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'd:\\program files\\python38\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'd:\\program files\\python38\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'd:\\program files\\python38\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'd:\\program files\\python38\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'd:\\program files\\python38\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'd:\\program files\\python38\\lib\\secrets.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'd:\\program files\\python38\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'd:\\program files\\python38\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'd:\\program files\\python38\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'd:\\program files\\python38\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('runpy', 'd:\\program files\\python38\\lib\\runpy.py', 'PYMODULE'),
  ('pkgutil', 'd:\\program files\\python38\\lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'd:\\program files\\python38\\lib\\zipimport.py', 'PYMODULE'),
  ('importlib.abc',
   'd:\\program files\\python38\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'd:\\program files\\python38\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'd:\\program files\\python38\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('csv', 'd:\\program files\\python38\\lib\\csv.py', 'PYMODULE'),
  ('tokenize', 'd:\\program files\\python38\\lib\\tokenize.py', 'PYMODULE'),
  ('token', 'd:\\program files\\python38\\lib\\token.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'd:\\program files\\python38\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib.machinery',
   'd:\\program files\\python38\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('multiprocessing',
   'd:\\program files\\python38\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('typing', 'd:\\program files\\python38\\lib\\typing.py', 'PYMODULE'),
  ('random', 'd:\\program files\\python38\\lib\\random.py', 'PYMODULE'),
  ('statistics', 'd:\\program files\\python38\\lib\\statistics.py', 'PYMODULE'),
  ('doctest', 'd:\\program files\\python38\\lib\\doctest.py', 'PYMODULE'),
  ('unittest',
   'd:\\program files\\python38\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.signals',
   'd:\\program files\\python38\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'd:\\program files\\python38\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'd:\\program files\\python38\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'd:\\program files\\python38\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'd:\\program files\\python38\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'd:\\program files\\python38\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('pprint', 'd:\\program files\\python38\\lib\\pprint.py', 'PYMODULE'),
  ('unittest.async_case',
   'd:\\program files\\python38\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('asyncio',
   'd:\\program files\\python38\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'd:\\program files\\python38\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'd:\\program files\\python38\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'd:\\program files\\python38\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'd:\\program files\\python38\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'd:\\program files\\python38\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'd:\\program files\\python38\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'd:\\program files\\python38\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'd:\\program files\\python38\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'd:\\program files\\python38\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'd:\\program files\\python38\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'd:\\program files\\python38\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'd:\\program files\\python38\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'd:\\program files\\python38\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'd:\\program files\\python38\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('concurrent.futures',
   'd:\\program files\\python38\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'd:\\program files\\python38\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'd:\\program files\\python38\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'd:\\program files\\python38\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'd:\\program files\\python38\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'd:\\program files\\python38\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'd:\\program files\\python38\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'd:\\program files\\python38\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'd:\\program files\\python38\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'd:\\program files\\python38\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'd:\\program files\\python38\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'd:\\program files\\python38\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.events',
   'd:\\program files\\python38\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'd:\\program files\\python38\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'd:\\program files\\python38\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'd:\\program files\\python38\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'd:\\program files\\python38\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'd:\\program files\\python38\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('unittest.result',
   'd:\\program files\\python38\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'd:\\program files\\python38\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('pdb', 'd:\\program files\\python38\\lib\\pdb.py', 'PYMODULE'),
  ('pydoc', 'd:\\program files\\python38\\lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'd:\\program files\\python38\\lib\\webbrowser.py', 'PYMODULE'),
  ('http.server',
   'd:\\program files\\python38\\lib\\http\\server.py',
   'PYMODULE'),
  ('socketserver',
   'd:\\program files\\python38\\lib\\socketserver.py',
   'PYMODULE'),
  ('html', 'd:\\program files\\python38\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'd:\\program files\\python38\\lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'd:\\program files\\python38\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'd:\\program files\\python38\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty', 'd:\\program files\\python38\\lib\\tty.py', 'PYMODULE'),
  ('glob', 'd:\\program files\\python38\\lib\\glob.py', 'PYMODULE'),
  ('code', 'd:\\program files\\python38\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'd:\\program files\\python38\\lib\\codeop.py', 'PYMODULE'),
  ('dis', 'd:\\program files\\python38\\lib\\dis.py', 'PYMODULE'),
  ('opcode', 'd:\\program files\\python38\\lib\\opcode.py', 'PYMODULE'),
  ('bdb', 'd:\\program files\\python38\\lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'd:\\program files\\python38\\lib\\cmd.py', 'PYMODULE'),
  ('difflib', 'd:\\program files\\python38\\lib\\difflib.py', 'PYMODULE'),
  ('__future__', 'd:\\program files\\python38\\lib\\__future__.py', 'PYMODULE'),
  ('fractions', 'd:\\program files\\python38\\lib\\fractions.py', 'PYMODULE'),
  ('gzip', 'd:\\program files\\python38\\lib\\gzip.py', 'PYMODULE'),
  ('_compression',
   'd:\\program files\\python38\\lib\\_compression.py',
   'PYMODULE'),
  ('tarfile', 'd:\\program files\\python38\\lib\\tarfile.py', 'PYMODULE'),
  ('lzma', 'd:\\program files\\python38\\lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'd:\\program files\\python38\\lib\\bz2.py', 'PYMODULE'),
  ('zipfile', 'd:\\program files\\python38\\lib\\zipfile.py', 'PYMODULE'),
  ('py_compile', 'd:\\program files\\python38\\lib\\py_compile.py', 'PYMODULE'),
  ('tempfile', 'd:\\program files\\python38\\lib\\tempfile.py', 'PYMODULE'),
  ('shutil', 'd:\\program files\\python38\\lib\\shutil.py', 'PYMODULE'),
  ('subprocess', 'd:\\program files\\python38\\lib\\subprocess.py', 'PYMODULE'),
  ('platform', 'd:\\program files\\python38\\lib\\platform.py', 'PYMODULE'),
  ('uuid', 'd:\\program files\\python38\\lib\\uuid.py', 'PYMODULE'),
  ('ctypes.util',
   'd:\\program files\\python38\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'd:\\program files\\python38\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'd:\\program files\\python38\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'd:\\program files\\python38\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'd:\\program files\\python38\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'd:\\program files\\python38\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('hashlib', 'd:\\program files\\python38\\lib\\hashlib.py', 'PYMODULE'),
  ('base64', 'd:\\program files\\python38\\lib\\base64.py', 'PYMODULE'),
  ('pickle', 'd:\\program files\\python38\\lib\\pickle.py', 'PYMODULE'),
  ('_compat_pickle',
   'd:\\program files\\python38\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('copy', 'd:\\program files\\python38\\lib\\copy.py', 'PYMODULE'),
  ('configparser',
   'd:\\program files\\python38\\lib\\configparser.py',
   'PYMODULE'),
  ('logging.handlers',
   'd:\\program files\\python38\\lib\\logging\\handlers.py',
   'PYMODULE'),
  ('smtplib', 'd:\\program files\\python38\\lib\\smtplib.py', 'PYMODULE'),
  ('logging',
   'd:\\program files\\python38\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('_strptime', 'd:\\program files\\python38\\lib\\_strptime.py', 'PYMODULE'),
  ('datetime', 'd:\\program files\\python38\\lib\\datetime.py', 'PYMODULE'),
  ('queue', 'd:\\program files\\python38\\lib\\queue.py', 'PYMODULE'),
  ('threading', 'd:\\program files\\python38\\lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'd:\\program files\\python38\\lib\\_threading_local.py',
   'PYMODULE'),
  ('inspect', 'd:\\program files\\python38\\lib\\inspect.py', 'PYMODULE'),
  ('ast', 'd:\\program files\\python38\\lib\\ast.py', 'PYMODULE'),
  ('importlib.util',
   'd:\\program files\\python38\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib',
   'd:\\program files\\python38\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('pathlib', 'd:\\program files\\python38\\lib\\pathlib.py', 'PYMODULE'),
  ('json', 'd:\\program files\\python38\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder',
   'd:\\program files\\python38\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'd:\\program files\\python38\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'd:\\program files\\python38\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('plugins.example_tool_plugin',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\example_tool_plugin.py',
   'PYMODULE'),
  ('plugins', '-', 'PYMODULE'),
  ('ui.styles.ProgressBarStyleManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\styles\\ProgressBarStyleManager.py',
   'PYMODULE'),
  ('ui.styles', '-', 'PYMODULE'),
  ('ui', '-', 'PYMODULE'),
  ('plugins.data_analysis_plugin',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\data_analysis_plugin.py',
   'PYMODULE'),
  ('core.event_bus.RegisterUpdateBus',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\event_bus\\RegisterUpdateBus.py',
   'PYMODULE'),
  ('plugins.performance_monitor_plugin',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\performance_monitor_plugin.py',
   'PYMODULE'),
  ('psutil',
   'd:\\program files\\python38\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'd:\\program files\\python38\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._common',
   'd:\\program files\\python38\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('ipaddress', 'd:\\program files\\python38\\lib\\ipaddress.py', 'PYMODULE'),
  ('plugins.selective_register_plugin',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\selective_register_plugin.py',
   'PYMODULE'),
  ('plugins.config.selective_register_config',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\config\\selective_register_config.py',
   'PYMODULE'),
  ('plugins.config', '-', 'PYMODULE'),
  ('plugins.clk_output_plugin',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\clk_output_plugin.py',
   'PYMODULE'),
  ('plugins.sync_sysref_plugin',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\sync_sysref_plugin.py',
   'PYMODULE'),
  ('plugins.pll_control_plugin',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\pll_control_plugin.py',
   'PYMODULE'),
  ('plugins.clkin_control_plugin',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\clkin_control_plugin.py',
   'PYMODULE'),
  ('plugins.set_modes_plugin',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\set_modes_plugin.py',
   'PYMODULE'),
  ('utils.message_strings',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\utils\\message_strings.py',
   'PYMODULE'),
  ('utils', '-', 'PYMODULE'),
  ('utils.error_handler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\utils\\error_handler.py',
   'PYMODULE'),
  ('utils.address_utils',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\utils\\address_utils.py',
   'PYMODULE'),
  ('utils.configFileHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\utils\\configFileHandler.py',
   'PYMODULE'),
  ('ui.tools.PluginManagerGUI',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\tools\\PluginManagerGUI.py',
   'PYMODULE'),
  ('ui.tools', '-', 'PYMODULE'),
  ('ui.factories.ToolWindowFactory',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\factories\\ToolWindowFactory.py',
   'PYMODULE'),
  ('ui.factories',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\factories\\__init__.py',
   'PYMODULE'),
  ('ui.factories.ModernToolWindowFactory',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\factories\\ModernToolWindowFactory.py',
   'PYMODULE'),
  ('ui.components.MenuManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\components\\MenuManager.py',
   'PYMODULE'),
  ('ui.components',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\components\\__init__.py',
   'PYMODULE'),
  ('ui.managers.BatchOperationManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\managers\\BatchOperationManager.py',
   'PYMODULE'),
  ('ui.managers', '-', 'PYMODULE'),
  ('core.services.config.ConfigurationManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\config\\ConfigurationManager.py',
   'PYMODULE'),
  ('core.services.config',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\config\\__init__.py',
   'PYMODULE'),
  ('core.services',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\__init__.py',
   'PYMODULE'),
  ('core', '-', 'PYMODULE'),
  ('ui.managers.RegisterDisplayManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\managers\\RegisterDisplayManager.py',
   'PYMODULE'),
  ('ui.managers.SPISignalManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\managers\\SPISignalManager.py',
   'PYMODULE'),
  ('ui.managers.StatusAndConfigManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\managers\\StatusAndConfigManager.py',
   'PYMODULE'),
  ('ui.managers.InitializationManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\managers\\InitializationManager.py',
   'PYMODULE'),
  ('core.services.register.RegisterManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\register\\RegisterManager.py',
   'PYMODULE'),
  ('core.services.register',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\register\\__init__.py',
   'PYMODULE'),
  ('core.models.RegisterModel',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\models\\RegisterModel.py',
   'PYMODULE'),
  ('core.models', '-', 'PYMODULE'),
  ('ui.managers.GlobalEventManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\managers\\GlobalEventManager.py',
   'PYMODULE'),
  ('core.services.BatchOperationState',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\BatchOperationState.py',
   'PYMODULE'),
  ('ui.managers.TabWindowManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\managers\\TabWindowManager.py',
   'PYMODULE'),
  ('PyQt5',
   'd:\\program files\\python38\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('ui.managers.ResourceAndUtilityManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\managers\\ResourceAndUtilityManager.py',
   'PYMODULE'),
  ('ui.managers.ToolWindowManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\managers\\ToolWindowManager.py',
   'PYMODULE'),
  ('ui.coordinators.SPIOperationCoordinator',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\coordinators\\SPIOperationCoordinator.py',
   'PYMODULE'),
  ('ui.coordinators', '-', 'PYMODULE'),
  ('core.services.ui.WindowManagementService',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\ui\\WindowManagementService.py',
   'PYMODULE'),
  ('core.services.ui',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\ui\\__init__.py',
   'PYMODULE'),
  ('core.services.config.ConfigurationService',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\config\\ConfigurationService.py',
   'PYMODULE'),
  ('ui.handlers.RegisterTreeHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\RegisterTreeHandler.py',
   'PYMODULE'),
  ('ui.handlers',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\__init__.py',
   'PYMODULE'),
  ('ui.handlers.RegisterIOHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\RegisterIOHandler.py',
   'PYMODULE'),
  ('ui.handlers.UIEventHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\UIEventHandler.py',
   'PYMODULE'),
  ('ui.handlers.RegisterTableHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\RegisterTableHandler.py',
   'PYMODULE'),
  ('ui.handlers.ClkinControlHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\ClkinControlHandler.py',
   'PYMODULE'),
  ('ui.forms.Ui_clkinControl',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\Ui_clkinControl.py',
   'PYMODULE'),
  ('ui.forms', '-', 'PYMODULE'),
  ('ui.resources.clkinControl_rc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\resources\\clkinControl_rc.py',
   'PYMODULE'),
  ('ui.resources', '-', 'PYMODULE'),
  ('ui.resources.clkoutput_rc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\resources\\clkoutput_rc.py',
   'PYMODULE'),
  ('ui.resources.syncSysref_rc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\resources\\syncSysref_rc.py',
   'PYMODULE'),
  ('ui.resources.PLL1_2_rc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\resources\\PLL1_2_rc.py',
   'PYMODULE'),
  ('ui.resources.setModes_rc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\resources\\setModes_rc.py',
   'PYMODULE'),
  ('ui.handlers.SetModesHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\SetModesHandler.py',
   'PYMODULE'),
  ('ui.forms.Ui_setModes',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\Ui_setModes.py',
   'PYMODULE'),
  ('ui.handlers.BaseHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\BaseHandler.py',
   'PYMODULE'),
  ('ui.handlers.ModernRegisterTreeHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\ModernRegisterTreeHandler.py',
   'PYMODULE'),
  ('ui.handlers.ModernRegisterIOHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\ModernRegisterIOHandler.py',
   'PYMODULE'),
  ('ui.handlers.ModernUIEventHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\ModernUIEventHandler.py',
   'PYMODULE'),
  ('ui.handlers.ModernRegisterTableHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\ModernRegisterTableHandler.py',
   'PYMODULE'),
  ('ui.handlers.ModernClkOutputsHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\ModernClkOutputsHandler.py',
   'PYMODULE'),
  ('ui.forms.Ui_ClkOutputs',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\Ui_ClkOutputs.py',
   'PYMODULE'),
  ('ui.handlers.ModernSyncSysRefHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\ModernSyncSysRefHandler.py',
   'PYMODULE'),
  ('ui.forms.Ui_syncSysref',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\Ui_syncSysref.py',
   'PYMODULE'),
  ('ui.handlers.ModernPLLHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\ModernPLLHandler.py',
   'PYMODULE'),
  ('ui.forms.Ui_PLL1_2',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\Ui_PLL1_2.py',
   'PYMODULE'),
  ('ui.handlers.ModernClkinControlHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\ModernClkinControlHandler.py',
   'PYMODULE'),
  ('ui.handlers.ModernSetModesHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\ModernSetModesHandler.py',
   'PYMODULE'),
  ('ui.handlers.ModernBaseHandler',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\handlers\\ModernBaseHandler.py',
   'PYMODULE'),
  ('core.services.plugin.window.PluginWindowService',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\plugin\\window\\PluginWindowService.py',
   'PYMODULE'),
  ('core.services.plugin.window', '-', 'PYMODULE'),
  ('core.services.plugin', '-', 'PYMODULE'),
  ('core.services.plugin.menu.PluginMenuService',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\plugin\\menu\\PluginMenuService.py',
   'PYMODULE'),
  ('core.services.plugin.menu', '-', 'PYMODULE'),
  ('core.services.plugin.MenuClickFixer',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\plugin\\MenuClickFixer.py',
   'PYMODULE'),
  ('core.services.plugin.PluginIntegrationService',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\plugin\\PluginIntegrationService.py',
   'PYMODULE'),
  ('core.services.plugin.dock.PluginDockService',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\plugin\\dock\\PluginDockService.py',
   'PYMODULE'),
  ('core.services.plugin.dock', '-', 'PYMODULE'),
  ('core.services.plugin.PluginManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\plugin\\PluginManager.py',
   'PYMODULE'),
  ('core.services.DIContainer',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\DIContainer.py',
   'PYMODULE'),
  ('ui.managers.UIUtilityManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\managers\\UIUtilityManager.py',
   'PYMODULE'),
  ('ui.managers.ApplicationLifecycleManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\managers\\ApplicationLifecycleManager.py',
   'PYMODULE'),
  ('ui.processors.RegisterUpdateProcessor',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\processors\\RegisterUpdateProcessor.py',
   'PYMODULE'),
  ('ui.processors',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\processors\\__init__.py',
   'PYMODULE'),
  ('ui.coordinators.EventCoordinator',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\coordinators\\EventCoordinator.py',
   'PYMODULE'),
  ('ui.managers.RegisterOperationManager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\managers\\RegisterOperationManager.py',
   'PYMODULE'),
  ('core.services.version.VersionService',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\version\\VersionService.py',
   'PYMODULE'),
  ('core.services.version', '-', 'PYMODULE'),
  ('core.services.register.RegisterOperationService',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\register\\RegisterOperationService.py',
   'PYMODULE'),
  ('core.services.spi.port_manager',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\spi\\port_manager.py',
   'PYMODULE'),
  ('core.services.spi', '-', 'PYMODULE'),
  ('core.services.spi.spi_service_impl',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\spi\\spi_service_impl.py',
   'PYMODULE'),
  ('core.services.spi.spi_interface',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\spi\\spi_interface.py',
   'PYMODULE'),
  ('core.services.spi.spiPrivacy',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\spi\\spiPrivacy.py',
   'PYMODULE'),
  ('core.services.spi.spi_service',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\core\\services\\spi\\spi_service.py',
   'PYMODULE'),
  ('core.event_bus', '-', 'PYMODULE'),
  ('serial.tools.list_ports',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\serial\\tools\\list_ports.py',
   'PYMODULE'),
  ('serial.tools',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\serial\\tools\\__init__.py',
   'PYMODULE'),
  ('serial.tools.list_ports_common',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\serial\\tools\\list_ports_common.py',
   'PYMODULE'),
  ('serial.tools.list_ports_posix',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\serial\\tools\\list_ports_posix.py',
   'PYMODULE'),
  ('serial.tools.list_ports_osx',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\serial\\tools\\list_ports_osx.py',
   'PYMODULE'),
  ('serial.tools.list_ports_linux',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\serial\\tools\\list_ports_linux.py',
   'PYMODULE'),
  ('serial.tools.list_ports_windows',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\serial\\tools\\list_ports_windows.py',
   'PYMODULE'),
  ('serial.win32',
   'd:\\program files\\python38\\lib\\site-packages\\serial\\win32.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'd:\\program files\\python38\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('serial',
   'd:\\program files\\python38\\lib\\site-packages\\serial\\__init__.py',
   'PYMODULE'),
  ('serial.serialjava',
   'd:\\program files\\python38\\lib\\site-packages\\serial\\serialjava.py',
   'PYMODULE'),
  ('serial.serialposix',
   'd:\\program files\\python38\\lib\\site-packages\\serial\\serialposix.py',
   'PYMODULE'),
  ('serial.serialwin32',
   'd:\\program files\\python38\\lib\\site-packages\\serial\\serialwin32.py',
   'PYMODULE'),
  ('serial.serialcli',
   'd:\\program files\\python38\\lib\\site-packages\\serial\\serialcli.py',
   'PYMODULE'),
  ('serial.serialutil',
   'd:\\program files\\python38\\lib\\site-packages\\serial\\serialutil.py',
   'PYMODULE'),
  ('tracemalloc',
   'd:\\program files\\python38\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('stringprep', 'd:\\program files\\python38\\lib\\stringprep.py', 'PYMODULE'),
  ('_py_abc', 'd:\\program files\\python38\\lib\\_py_abc.py', 'PYMODULE'),
  ('utils.Log',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\utils\\Log.py',
   'PYMODULE'),
  ('ui.windows.RegisterMainWindow',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\windows\\RegisterMainWindow.py',
   'PYMODULE'),
  ('ui.windows', '-', 'PYMODULE'),
  ('ui.components.MainWindowUI',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\components\\MainWindowUI.py',
   'PYMODULE')],
 [('python38.dll', 'd:\\program files\\python38\\python38.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libglesv2.dll',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libglesv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libegl.dll',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libegl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\printsupport\\windowsprintersupport.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\printsupport\\windowsprintersupport.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('select.pyd', 'd:\\program files\\python38\\DLLs\\select.pyd', 'EXTENSION'),
  ('pyexpat.pyd',
   'd:\\program files\\python38\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'd:\\program files\\python38\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('unicodedata.pyd',
   'd:\\program files\\python38\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'd:\\program files\\python38\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'd:\\program files\\python38\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'd:\\program files\\python38\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'd:\\program files\\python38\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'd:\\program files\\python38\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'd:\\program files\\python38\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'd:\\program files\\python38\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_lzma.pyd', 'd:\\program files\\python38\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'd:\\program files\\python38\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_queue.pyd', 'd:\\program files\\python38\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp38-win_amd64.pyd',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\sip.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtPrintSupport.pyd',
   'd:\\program files\\python38\\lib\\site-packages\\PyQt5\\QtPrintSupport.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'd:\\program files\\python38\\lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'd:\\program files\\python38\\lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'd:\\program files\\python38\\lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'd:\\program files\\python38\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5PrintSupport.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5PrintSupport.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'd:\\program files\\python38\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'd:\\program files\\python38\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('libffi-7.dll', 'd:\\program files\\python38\\DLLs\\libffi-7.dll', 'BINARY'),
  ('python3.dll', 'd:\\program files\\python38\\python3.dll', 'BINARY'),
  ('ucrtbase.dll', 'C:\\windows\\system32\\ucrtbase.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\windows\\system32\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY')],
 [],
 [],
 [('config\\README.md',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\config\\README.md',
   'DATA'),
  ('config\\app.json.template',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\config\\app.json.template',
   'DATA'),
  ('config\\default.json',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\config\\default.json',
   'DATA'),
  ('config\\local.json',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\config\\local.json',
   'DATA'),
  ('config\\migration.json',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\config\\migration.json',
   'DATA'),
  ('config\\selective_register_plugin.json',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\config\\selective_register_plugin.json',
   'DATA'),
  ('gui\\cascaded_0_delay_dual_loop.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\gui\\cascaded_0_delay_dual_loop.jpg',
   'DATA'),
  ('gui\\clkInput.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\gui\\clkInput.jpg',
   'DATA'),
  ('gui\\dual_loop_0_delay_cascaded.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\gui\\dual_loop_0_delay_cascaded.jpg',
   'DATA'),
  ('gui\\dual_loop_mode.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\gui\\dual_loop_mode.jpg',
   'DATA'),
  ('gui\\dual_loop_mode_calculate.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\gui\\dual_loop_mode_calculate.jpg',
   'DATA'),
  ('gui\\dualloop0_delay_nested.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\gui\\dualloop0_delay_nested.jpg',
   'DATA'),
  ('gui\\nested_0_delay_dual_loop_mode.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\gui\\nested_0_delay_dual_loop_mode.jpg',
   'DATA'),
  ('gui\\pll.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\gui\\pll.jpg',
   'DATA'),
  ('gui\\singleLoop.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\gui\\singleLoop.jpg',
   'DATA'),
  ('gui\\systemREF.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\gui\\systemREF.jpg',
   'DATA'),
  ('images\\logo.ico',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\images\\logo.ico',
   'DATA'),
  ('lib\\register.json',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\lib\\register.json',
   'DATA'),
  ('plugins\\__pycache__\\clk_output_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\__pycache__\\clk_output_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\clkin_control_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\__pycache__\\clkin_control_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\data_analysis_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\__pycache__\\data_analysis_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\drag_dock_test_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\__pycache__\\drag_dock_test_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\example_tool_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\__pycache__\\example_tool_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\performance_monitor_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\__pycache__\\performance_monitor_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\pll_control_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\__pycache__\\pll_control_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\selective_register_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\__pycache__\\selective_register_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\set_modes_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\__pycache__\\set_modes_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\sync_sysref_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\__pycache__\\sync_sysref_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\clk_output_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\clk_output_plugin.py',
   'DATA'),
  ('plugins\\clkin_control_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\clkin_control_plugin.py',
   'DATA'),
  ('plugins\\config\\__pycache__\\selective_register_config.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\config\\__pycache__\\selective_register_config.cpython-38.pyc',
   'DATA'),
  ('plugins\\config\\selective_register_config.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\config\\selective_register_config.py',
   'DATA'),
  ('plugins\\data_analysis_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\data_analysis_plugin.py',
   'DATA'),
  ('plugins\\example_tool_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\example_tool_plugin.py',
   'DATA'),
  ('plugins\\performance_monitor_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\performance_monitor_plugin.py',
   'DATA'),
  ('plugins\\pll_control_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\pll_control_plugin.py',
   'DATA'),
  ('plugins\\selective_register_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\selective_register_plugin.py',
   'DATA'),
  ('plugins\\set_modes_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\set_modes_plugin.py',
   'DATA'),
  ('plugins\\sync_sysref_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\plugins\\sync_sysref_plugin.py',
   'DATA'),
  ('ui\\forms\\ClkOutputs.ui',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\ClkOutputs.ui',
   'DATA'),
  ('ui\\forms\\PLL1_2.ui',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\PLL1_2.ui',
   'DATA'),
  ('ui\\forms\\Ui_ClkOutputs.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\Ui_ClkOutputs.py',
   'DATA'),
  ('ui\\forms\\Ui_PLL1_2.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\Ui_PLL1_2.py',
   'DATA'),
  ('ui\\forms\\Ui_clkinControl.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\Ui_clkinControl.py',
   'DATA'),
  ('ui\\forms\\Ui_setModes.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\Ui_setModes.py',
   'DATA'),
  ('ui\\forms\\Ui_syncSysref.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\Ui_syncSysref.py',
   'DATA'),
  ('ui\\forms\\__pycache__\\Ui_ClkOutputs.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\__pycache__\\Ui_ClkOutputs.cpython-38.pyc',
   'DATA'),
  ('ui\\forms\\__pycache__\\Ui_PLL1_2.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\__pycache__\\Ui_PLL1_2.cpython-38.pyc',
   'DATA'),
  ('ui\\forms\\__pycache__\\Ui_clkinControl.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\__pycache__\\Ui_clkinControl.cpython-38.pyc',
   'DATA'),
  ('ui\\forms\\__pycache__\\Ui_setModes.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\__pycache__\\Ui_setModes.cpython-38.pyc',
   'DATA'),
  ('ui\\forms\\__pycache__\\Ui_syncSysref.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\__pycache__\\Ui_syncSysref.cpython-38.pyc',
   'DATA'),
  ('ui\\forms\\clkinControl.ui',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\clkinControl.ui',
   'DATA'),
  ('ui\\forms\\log\\app.log',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\log\\app.log',
   'DATA'),
  ('ui\\forms\\setModes.ui',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\setModes.ui',
   'DATA'),
  ('ui\\forms\\syncSysref.ui',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\forms\\syncSysref.ui',
   'DATA'),
  ('ui\\resources\\PLL1_2_rc.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\resources\\PLL1_2_rc.py',
   'DATA'),
  ('ui\\resources\\__pycache__\\PLL1_2_rc.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\resources\\__pycache__\\PLL1_2_rc.cpython-38.pyc',
   'DATA'),
  ('ui\\resources\\__pycache__\\clkinControl_rc.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\resources\\__pycache__\\clkinControl_rc.cpython-38.pyc',
   'DATA'),
  ('ui\\resources\\__pycache__\\clkoutput_rc.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\resources\\__pycache__\\clkoutput_rc.cpython-38.pyc',
   'DATA'),
  ('ui\\resources\\__pycache__\\setModes_rc.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\resources\\__pycache__\\setModes_rc.cpython-38.pyc',
   'DATA'),
  ('ui\\resources\\__pycache__\\syncSysref_rc.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\resources\\__pycache__\\syncSysref_rc.cpython-38.pyc',
   'DATA'),
  ('ui\\resources\\clkinControl_rc.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\resources\\clkinControl_rc.py',
   'DATA'),
  ('ui\\resources\\clkoutput_rc.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\resources\\clkoutput_rc.py',
   'DATA'),
  ('ui\\resources\\setModes_rc.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\resources\\setModes_rc.py',
   'DATA'),
  ('ui\\resources\\syncSysref_rc.py',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\ui\\resources\\syncSysref_rc.py',
   'DATA'),
  ('base_library.zip',
   'E:\\FSJ04832\\FSJReadOutput\\version2\\anotherCore2\\packaging\\scripts\\build\\build\\base_library.zip',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA')])
