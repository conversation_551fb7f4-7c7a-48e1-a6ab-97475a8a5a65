#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试寄存器表格修改后的自动写入功能
验证表格数据修改后是否正确写入芯片
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QHBoxLayout
from PyQt5.QtCore import QTimer, Qt
from ui.handlers.ModernRegisterTableHandler import ModernRegisterTableHandler
from utils.Log import get_module_logger

logger = get_module_logger(__name__)

class TableAutoWriteTestWindow(QMainWindow):
    """测试表格自动写入功能的主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("测试寄存器表格自动写入功能")
        self.setGeometry(100, 100, 1200, 800)
        
        # 模拟主窗口的寄存器写入服务
        self.register_repo = MockRegisterRepo()
        self.register_service = MockRegisterService()
        
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 添加说明标签
        info_label = QLabel("""
        寄存器表格自动写入功能测试：
        
        ✅ 修改表格中的位域值后，应该自动写入芯片
        ✅ 不依赖自动写入模式设置
        ✅ 支持多种写入服务（register_repo, register_service等）
        
        测试步骤：
        1. 点击"显示寄存器0x14"按钮加载寄存器表格
        2. 修改表格中的位域值（如将某个位从0改为1）
        3. 观察控制台输出，应该看到写入芯片的日志
        4. 测试不同的位域修改
        """)
        layout.addWidget(info_label)
        
        # 创建现代化表格处理器
        try:
            self.table_handler = ModernRegisterTableHandler.create_for_testing(self)

            # 设置主窗口引用，确保表格处理器能找到写入服务
            self.table_handler.main_window = self

            layout.addWidget(self.table_handler)
            
            logger.info("现代化表格处理器创建成功")
            
            # 添加测试按钮
            button_layout = QHBoxLayout()
            
            show_reg_btn = QPushButton("显示寄存器 0x14")
            show_reg_btn.clicked.connect(self.show_register_0x14)
            button_layout.addWidget(show_reg_btn)
            
            show_reg2_btn = QPushButton("显示寄存器 0x15")
            show_reg2_btn.clicked.connect(self.show_register_0x15)
            button_layout.addWidget(show_reg2_btn)
            
            test_modify_btn = QPushButton("模拟修改位域")
            test_modify_btn.clicked.connect(self.test_modify_bit_field)
            button_layout.addWidget(test_modify_btn)
            
            layout.addLayout(button_layout)
            
            # 添加写入日志显示
            self.write_log_label = QLabel("写入日志将在这里显示...")
            layout.addWidget(self.write_log_label)
            
        except Exception as e:
            logger.error(f"创建现代化表格处理器失败: {str(e)}")
            error_label = QLabel(f"创建失败: {str(e)}")
            layout.addWidget(error_label)
    
    def show_register_0x14(self):
        """显示寄存器0x14的位域"""
        logger.info("=== 显示寄存器 0x14 ===")
        try:
            # 模拟寄存器值
            reg_addr = 0x14
            reg_value = 0x1234  # 示例值
            
            self.table_handler.show_bit_fields(reg_addr, reg_value)
            logger.info(f"已显示寄存器 0x{reg_addr:02X} 的位域，值: 0x{reg_value:04X}")
            
        except Exception as e:
            logger.error(f"显示寄存器0x14时出错: {str(e)}")
    
    def show_register_0x15(self):
        """显示寄存器0x15的位域"""
        logger.info("=== 显示寄存器 0x15 ===")
        try:
            # 模拟寄存器值
            reg_addr = 0x15
            reg_value = 0x5678  # 示例值
            
            self.table_handler.show_bit_fields(reg_addr, reg_value)
            logger.info(f"已显示寄存器 0x{reg_addr:02X} 的位域，值: 0x{reg_value:04X}")
            
        except Exception as e:
            logger.error(f"显示寄存器0x15时出错: {str(e)}")
    
    def test_modify_bit_field(self):
        """测试修改位域"""
        logger.info("=== 测试修改位域 ===")
        try:
            if not hasattr(self.table_handler, 'bit_field_table') or not self.table_handler.bit_field_table:
                logger.warning("请先显示一个寄存器")
                return
            
            table = self.table_handler.bit_field_table
            if table.rowCount() == 0:
                logger.warning("表格中没有数据")
                return
            
            # 找到第一个可编辑的位域
            editable_column = self.table_handler.table_config["editable_column"]
            for row in range(table.rowCount()):
                item = table.item(row, editable_column)
                if item and (item.flags() & Qt.ItemIsEditable):
                    current_value = item.text()
                    # 切换位值（0变1，1变0）
                    if current_value == "0":
                        new_value = "1"
                    elif current_value == "1":
                        new_value = "0"
                    else:
                        # 对于多位字段，简单修改第一位
                        if len(current_value) > 1:
                            new_value = "1" + current_value[1:]
                        else:
                            new_value = "1"
                    
                    logger.info(f"修改第{row}行位域: '{current_value}' -> '{new_value}'")
                    item.setText(new_value)
                    
                    # 手动触发cellChanged信号
                    table.cellChanged.emit(row, editable_column)
                    break
            else:
                logger.warning("未找到可编辑的位域")
                
        except Exception as e:
            logger.error(f"测试修改位域时出错: {str(e)}")
            import traceback
            traceback.print_exc()

class MockRegisterRepo:
    """模拟寄存器仓库"""
    
    def __init__(self):
        self.write_count = 0
    
    def write_register(self, addr, value):
        """模拟写入寄存器"""
        self.write_count += 1
        logger.info(f"🔥 MockRegisterRepo: 写入寄存器 {addr} = 0x{value:04X} (第{self.write_count}次写入)")
        return True

class MockRegisterService:
    """模拟寄存器服务"""
    
    def __init__(self):
        self.write_count = 0
    
    def write_register(self, addr, value):
        """模拟写入寄存器"""
        self.write_count += 1
        logger.info(f"🚀 MockRegisterService: 写入寄存器 {addr} = 0x{value:04X} (第{self.write_count}次写入)")
        return True

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TableAutoWriteTestWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
