# -*- coding: utf-8 -*-

from PyQt5.QtCore import QObject, pyqtSignal
from utils.Log import get_module_logger

logger = get_module_logger(__name__)

class RegisterUpdateBus(QObject):
    """寄存器更新总线，用于在不同组件间传递寄存器更新事件"""
    
    # 单例模式
    _instance = None
    
    # 寄存器更新信号
    register_updated = pyqtSignal(str, int)

    # 时钟源选择信号：源名称, 频率值, 分频比
    clock_source_selected = pyqtSignal(str, float, int)

    # 模式变化信号：模式名称
    mode_changed = pyqtSignal(str)

    # 多寄存器更新信号：寄存器地址列表
    multiple_registers_updated = pyqtSignal(list)

    # PLL1PFDFreq更新信号：频率值(MHz)
    pll1_pfd_freq_updated = pyqtSignal(float)

    # VCODistFreq更新信号：频率值(MHz)
    vco_dist_freq_updated = pyqtSignal(float)

    # InternalVCOFreq更新信号：频率值(MHz)
    internal_vco_freq_updated = pyqtSignal(float)

    # SYSREF频率更新信号：频率值(MHz)
    sysref_freq_updated = pyqtSignal(float)

    @classmethod
    def instance(cls):
        """获取单例实例"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def __init__(self):
        """确保只有一个实例"""
        super().__init__()
        if RegisterUpdateBus._instance is not None:
            raise RuntimeError("RegisterUpdateBus已经实例化，请使用instance()方法获取实例")
        RegisterUpdateBus._instance = self

        # 频率值缓存
        self._frequency_cache = {
            'vco_dist_freq': None,  # VCODistFreq缓存值
            'pll1_pfd_freq': None,  # PLL1PFDFreq缓存值
            'pll2_pfd_freq': None,  # PLL2PFDFreq缓存值
            'pll2_n_divider': None, # PLL2NDivider缓存值
            'sysref_freq': None,    # SYSREF输出频率缓存值
            'sysref_div': None      # SYSREF分频器缓存值
        }

        # LineEdit控件值缓存 - 用于保存所有可修改的lineEdit控件的值
        self._lineedit_cache = {}

        # CLKin_SEL_MANUAL[1:0] (bits 5:4 of 0x57) default is "01" from register.json
        # This corresponds to index 1 in CLOCK_SOURCES array in ClkinControlHandler
        # 0: CLKin0, 1: CLKin1, 2: CLKin2, 3: Holdover
        initial_clkin_sel_manual_value_from_register = 1  # Simulating read from register default "01"

        clock_sources_map = {
            0: "CLKin0",
            1: "CLKin1",
            2: "CLKin2",
            3: "Holdover"
        }
        default_source_raw = clock_sources_map.get(initial_clkin_sel_manual_value_from_register, "CLKin0")

        # Normalize to ClkInX format for internal consistency
        if default_source_raw.startswith("CLK"):
            self._current_clock_source = "ClkIn" + default_source_raw[5:]
        else:
            self._current_clock_source = default_source_raw  # For "Holdover"

        logger.info(f"RegisterUpdateBus: Initial clock source set based on register default (0x57, CLKin_SEL_MANUAL): {self._current_clock_source}")

        # 存储各时钟源的频率和分频比
        self._clock_frequencies = {
            "ClkIn0": 122.88,  # 默认值
            "ClkIn1": 122.88,  # 默认值
            "ClkIn2": 122.88,
            "ClkIn3": 0,
            "Holdover": 0
        }
        
        self._clock_dividers = {
            "ClkIn0": 120,  # 默认值
            "ClkIn1": 120,  # 默认值
            "ClkIn2": 120,
            "ClkIn3": 1,
            "Holdover": 1
        }
        
        # 连接自身信号到日志函数，用于调试
        self.register_updated.connect(self.log_register_update)
        self.clock_source_selected.connect(self.log_clock_source_selection)
        self.multiple_registers_updated.connect(self.log_multiple_registers_update)

        # 初始化标志，用于跟踪是否已经从寄存器加载了实际值
        self._initialized_from_registers = False
        
    def log_register_update(self, reg_addr, reg_value):
        """记录寄存器更新事件"""
        # 确保地址是字符串格式
        if not isinstance(reg_addr, str):
            reg_addr = f"0x{reg_addr:X}"

        # 确保值是整数
        if isinstance(reg_value, str):
            try:
                if reg_value.startswith("0x"):
                    reg_value = int(reg_value, 16)
                else:
                    reg_value = int(reg_value)
            except ValueError:
                logger.warning(f"无法将寄存器值 '{reg_value}' 转换为整数，保持原值")
                # 如果转换失败，直接记录字符串值
                logger.info(f"寄存器总线收到更新: 地址={reg_addr}, 值={reg_value}")
                return

        # 只有当reg_value是整数时才使用X格式
        if isinstance(reg_value, int):
            logger.info(f"寄存器总线收到更新: 地址={reg_addr}, 值=0x{reg_value:X}")
        else:
            logger.info(f"寄存器总线收到更新: 地址={reg_addr}, 值={reg_value}")
    
    def log_clock_source_selection(self, source_name, frequency, divider):
        """记录时钟源选择事件，并更新内部存储的时钟源配置"""
        logger.info(f"时钟源选择: 源={source_name}, 频率={frequency}MHz, 分频比={divider}")
        
        # 规范化时钟源名称
        normalized_source = source_name
        if normalized_source.startswith("CLK"):
            # 将CLKinX规范为ClkInX
            if len(normalized_source) > 5:
                normalized_source = "ClkIn" + normalized_source[5:]
        elif normalized_source.startswith("Clk"):
            # 确保ClkInX格式一致
            pass
        else:
            # 其他类型的源名称保持不变
            pass
            
        # 更新当前选中的时钟源
        self._current_clock_source = normalized_source
        
        # 同时更新内部存储的频率和分频比
        self.set_clock_frequency(normalized_source, frequency)
        self.set_clock_divider(normalized_source, divider)
        
        # 也更新可能的其他格式的名称映射
        if normalized_source.startswith("ClkIn"):
            alt_name = "CLKin" + normalized_source[5:]
            self.set_clock_frequency(alt_name, frequency)
            self.set_clock_divider(alt_name, divider)
            
        logger.info(f"已更新时钟源配置: {normalized_source}, 频率={frequency}MHz, 分频比={divider}")
    
    def get_current_clock_source(self):
        """获取当前选中的时钟源（标准化格式）"""
        # 确保返回的是标准化格式（ClkInX）
        source = self._current_clock_source
        if source and source.startswith("CLK"):
            # 将CLKinX规范为ClkInX
            if len(source) > 5:
                return "ClkIn" + source[5:]
        return source
    
    def normalize_source_name(self, source_name):
        """规范化时钟源名称
        
        Args:
            source_name: 原始时钟源名称
            
        Returns:
            str: 规范化后的时钟源名称（ClkInX格式）
        """
        if not source_name:
            return "ClkIn0"  # 默认值
            
        # 规范化时钟源名称
        if source_name.startswith("CLK"):
            # 将CLKinX规范为ClkInX
            if len(source_name) > 5:
                return "ClkIn" + source_name[5:]
        return source_name
            
    def get_clock_frequency(self, source_name):
        """获取指定时钟源的频率
        
        Args:
            source_name: 时钟源名称
            
        Returns:
            float: 时钟源频率，如果未找到则返回0
        """
        # 尝试直接获取
        freq = self._clock_frequencies.get(source_name, None)
        if freq is not None:
            return freq
            
        # 尝试使用规范化名称获取
        norm_name = self.normalize_source_name(source_name)
        freq = self._clock_frequencies.get(norm_name, None)
        if freq is not None:
            return freq
            
        # 尝试使用替代格式获取
        if norm_name.startswith("ClkIn"):
            alt_name = "CLKin" + norm_name[5:]
            freq = self._clock_frequencies.get(alt_name, None)
            if freq is not None:
                return freq
                
        # 没有找到，返回默认值
        logger.warning(f"未找到时钟源 {source_name} 的频率配置，返回默认值0")
        return 0
        
    def get_clock_divider(self, source_name):
        """获取指定时钟源的分频比
        
        Args:
            source_name: 时钟源名称
            
        Returns:
            int: 时钟源分频比，如果未找到则返回1
        """
        # 尝试直接获取
        divider = self._clock_dividers.get(source_name, None)
        if divider is not None:
            return divider
            
        # 尝试使用规范化名称获取
        norm_name = self.normalize_source_name(source_name)
        divider = self._clock_dividers.get(norm_name, None)
        if divider is not None:
            return divider
            
        # 尝试使用替代格式获取
        if norm_name.startswith("ClkIn"):
            alt_name = "CLKin" + norm_name[5:]
            divider = self._clock_dividers.get(alt_name, None)
            if divider is not None:
                return divider
                
        # 没有找到，返回默认值
        logger.warning(f"未找到时钟源 {source_name} 的分频比配置，返回默认值1")
        return 1
        
    def set_clock_frequency(self, source_name, frequency):
        """设置指定时钟源的频率
        
        Args:
            source_name: 时钟源名称
            frequency: 时钟源频率
            
        Returns:
            bool: 设置是否成功
        """
        # 规范化时钟源名称
        norm_name = self.normalize_source_name(source_name)
        
        try:
            # 设置规范化名称的频率
            self._clock_frequencies[norm_name] = float(frequency)
            
            # 如果是ClkInX格式，同时设置CLKinX格式的频率
            if norm_name.startswith("ClkIn"):
                alt_name = "CLKin" + norm_name[5:]
                self._clock_frequencies[alt_name] = float(frequency)
                
            logger.info(f"设置时钟源 {norm_name} 的频率为 {frequency}MHz")
            return True
        except (ValueError, TypeError):
            logger.warning(f"设置时钟源频率失败: 无效的频率值 {frequency}")
            return False
        
    def set_clock_divider(self, source_name, divider):
        """设置指定时钟源的分频比
        
        Args:
            source_name: 时钟源名称
            divider: 时钟源分频比
            
        Returns:
            bool: 设置是否成功
        """
        # 规范化时钟源名称
        norm_name = self.normalize_source_name(source_name)
        
        try:
            # 设置规范化名称的分频比
            self._clock_dividers[norm_name] = int(divider)
            
            # 如果是ClkInX格式，同时设置CLKinX格式的分频比
            if norm_name.startswith("ClkIn"):
                alt_name = "CLKin" + norm_name[5:]
                self._clock_dividers[alt_name] = int(divider)
                
            logger.info(f"设置时钟源 {norm_name} 的分频比为 {divider}")
            return True
        except (ValueError, TypeError):
            logger.warning(f"设置时钟源分频比失败: 无效的分频比值 {divider}")
            return False
    
    def emit_register_updated(self, reg_addr, reg_value):
        """发送寄存器更新信号的安全方法"""
        # 标准化地址格式
        if isinstance(reg_addr, int):
            reg_addr = f"0x{reg_addr:X}"
        
        # 确保值是整数
        if isinstance(reg_value, str):
            try:
                if reg_value.startswith("0x"):
                    reg_value = int(reg_value, 16)
                else:
                    reg_value = int(reg_value)
            except ValueError:
                logger.warning(f"无法将寄存器值 '{reg_value}' 转换为整数，尝试使用默认值0")
                reg_value = 0
                
        # 发送信号
        logger.info(f"寄存器总线发送更新: 地址={reg_addr}, 值=0x{reg_value:X}")
        self.register_updated.emit(reg_addr, reg_value)

    def initialize_from_register_manager(self, register_manager):
        """从寄存器管理器初始化时钟源缓存

        Args:
            register_manager: RegisterManager实例
        """
        if not register_manager or self._initialized_from_registers:
            return

        try:
            # 从寄存器0x57读取CLKin_SEL_MANUAL[1:0] (bits 5:4)
            reg_0x57_value = register_manager.get_register_value(0x57)
            if reg_0x57_value is not None:
                # 提取bits 5:4
                clkin_sel_manual = (reg_0x57_value >> 4) & 0x3

                # 映射到时钟源
                clock_sources_map = {
                    0: "CLKin0",
                    1: "CLKin1",
                    2: "CLKin2",
                    3: "Holdover"
                }

                selected_source = clock_sources_map.get(clkin_sel_manual, "CLKin1")

                # 规范化为ClkInX格式
                if selected_source.startswith("CLK"):
                    normalized_source = "ClkIn" + selected_source[5:]
                else:
                    normalized_source = selected_source

                # 更新当前时钟源
                old_source = self._current_clock_source
                self._current_clock_source = normalized_source

                logger.info(f"从寄存器0x57初始化时钟源: 寄存器值=0x{reg_0x57_value:04X}, "
                           f"CLKin_SEL_MANUAL={clkin_sel_manual}, 时钟源={normalized_source} "
                           f"(原值: {old_source})")

            # 从相关寄存器读取分频值并更新缓存
            self._update_divider_cache_from_registers(register_manager)

            # 标记已初始化
            self._initialized_from_registers = True

        except Exception as e:
            logger.error(f"从寄存器管理器初始化时钟源缓存时出错: {str(e)}")

    def _update_divider_cache_from_registers(self, register_manager):
        """从寄存器更新分频值缓存"""
        try:
            # 更新CLKin0分频值 (寄存器0x63, bits 13:0)
            reg_0x63_value = register_manager.get_register_value(0x63)
            if reg_0x63_value is not None:
                clkin0_divider = reg_0x63_value & 0x3FFF  # bits 13:0
                self._clock_dividers["ClkIn0"] = clkin0_divider
                self._clock_dividers["CLKin0"] = clkin0_divider  # 兼容格式
                logger.debug(f"从寄存器0x63更新CLKin0分频值: {clkin0_divider}")

            # 更新CLKin1分频值 (寄存器0x65, bits 13:0)
            reg_0x65_value = register_manager.get_register_value(0x65)
            if reg_0x65_value is not None:
                clkin1_divider = reg_0x65_value & 0x3FFF  # bits 13:0
                self._clock_dividers["ClkIn1"] = clkin1_divider
                self._clock_dividers["CLKin1"] = clkin1_divider  # 兼容格式
                logger.debug(f"从寄存器0x65更新CLKin1分频值: {clkin1_divider}")

            # 更新CLKin2分频值 (寄存器0x67, bits 13:0)
            reg_0x67_value = register_manager.get_register_value(0x67)
            if reg_0x67_value is not None:
                clkin2_divider = reg_0x67_value & 0x3FFF  # bits 13:0
                self._clock_dividers["ClkIn2"] = clkin2_divider
                self._clock_dividers["CLKin2"] = clkin2_divider  # 兼容格式
                logger.debug(f"从寄存器0x67更新CLKin2分频值: {clkin2_divider}")

        except Exception as e:
            logger.error(f"从寄存器更新分频值缓存时出错: {str(e)}")

    def get_current_clock_source_info(self):
        """获取当前时钟源的完整信息

        Returns:
            tuple: (时钟源名称, 频率, 分频值)
        """
        current_source = self.get_current_clock_source()
        frequency = self.get_clock_frequency(current_source)
        divider = self.get_clock_divider(current_source)

        return current_source, frequency, divider

    def is_initialized_from_registers(self):
        """检查是否已从寄存器初始化"""
        return self._initialized_from_registers
        
    def emit_clock_source_selected(self, source_name, frequency, divider=1):
        """发送时钟源选择信号的安全方法"""
        # 确保频率是浮点数
        try:
            frequency = float(frequency)
        except (ValueError, TypeError):
            logger.warning(f"无法将频率值 '{frequency}' 转换为浮点数，使用默认值0.0")
            frequency = 0.0
            
        # 确保分频比是整数
        try:
            divider = int(divider)
        except (ValueError, TypeError):
            logger.warning(f"无法将分频比 '{divider}' 转换为整数，使用默认值1")
            divider = 1
            
        # 发送信号
        logger.info(f"发送时钟源选择: 源={source_name}, 频率={frequency}MHz, 分频比={divider}")
        self.clock_source_selected.emit(source_name, frequency, divider)
    
    def log_multiple_registers_update(self, reg_addr_list):
        """记录多寄存器更新事件"""
        logger.info(f"寄存器总线收到多寄存器更新通知: {', '.join(reg_addr_list)}")
        
    def emit_multiple_registers_updated(self, reg_addr_list):
        """发送多寄存器更新信号的安全方法"""
        if not isinstance(reg_addr_list, list):
            logger.warning(f"寄存器地址列表必须为list类型，而不是 {type(reg_addr_list)}")
            return
            
        # 标准化地址格式
        formatted_addresses = []
        for addr in reg_addr_list:
            if isinstance(addr, int):
                formatted_addresses.append(f"0x{addr:X}")
            else:
                formatted_addresses.append(str(addr))
                
        # 发送信号
        logger.info(f"寄存器总线发送多寄存器更新: {', '.join(formatted_addresses)}")
        self.multiple_registers_updated.emit(formatted_addresses)

    # ==================== 频率缓存管理方法 ====================

    def cache_vco_dist_freq(self, freq_value):
        """缓存VCODistFreq值

        Args:
            freq_value (float): VCODistFreq频率值(MHz)
        """
        try:
            self._frequency_cache['vco_dist_freq'] = freq_value
            logger.debug(f"RegisterUpdateBus: 已缓存VCODistFreq值: {freq_value} MHz")
        except Exception as e:
            logger.error(f"RegisterUpdateBus: 缓存VCODistFreq值时出错: {str(e)}")

    def get_cached_vco_dist_freq(self):
        """获取缓存的VCODistFreq值

        Returns:
            float or None: 缓存的VCODistFreq值，如果没有缓存则返回None
        """
        try:
            cached_value = self._frequency_cache.get('vco_dist_freq')
            if cached_value is not None:
                logger.debug(f"RegisterUpdateBus: 获取缓存的VCODistFreq值: {cached_value} MHz")
            else:
                logger.debug("RegisterUpdateBus: 没有缓存的VCODistFreq值")
            return cached_value
        except Exception as e:
            logger.error(f"RegisterUpdateBus: 获取缓存VCODistFreq值时出错: {str(e)}")
            return None

    def cache_pll1_pfd_freq(self, freq_value):
        """缓存PLL1PFDFreq值

        Args:
            freq_value (float): PLL1PFDFreq频率值(MHz)
        """
        try:
            self._frequency_cache['pll1_pfd_freq'] = freq_value
            logger.debug(f"RegisterUpdateBus: 已缓存PLL1PFDFreq值: {freq_value} MHz")
        except Exception as e:
            logger.error(f"RegisterUpdateBus: 缓存PLL1PFDFreq值时出错: {str(e)}")

    def get_cached_pll1_pfd_freq(self):
        """获取缓存的PLL1PFDFreq值

        Returns:
            float or None: 缓存的PLL1PFDFreq值，如果没有缓存则返回None
        """
        try:
            cached_value = self._frequency_cache.get('pll1_pfd_freq')
            if cached_value is not None:
                logger.debug(f"RegisterUpdateBus: 获取缓存的PLL1PFDFreq值: {cached_value} MHz")
            else:
                logger.debug("RegisterUpdateBus: 没有缓存的PLL1PFDFreq值")
            return cached_value
        except Exception as e:
            logger.error(f"RegisterUpdateBus: 获取缓存PLL1PFDFreq值时出错: {str(e)}")
            return None

    def clear_frequency_cache(self):
        """清空频率缓存"""
        try:
            self._frequency_cache = {
                'vco_dist_freq': None,
                'pll1_pfd_freq': None,
                'pll2_pfd_freq': None,
                'pll2_n_divider': None,
                'sysref_freq': None,
                'sysref_div': None
            }
            logger.info("RegisterUpdateBus: 已清空频率缓存")
        except Exception as e:
            logger.error(f"RegisterUpdateBus: 清空频率缓存时出错: {str(e)}")

    def cache_sysref_data(self, sysref_freq, sysref_div):
        """缓存SYSREF相关数据

        Args:
            sysref_freq (float): SYSREF输出频率值(MHz)
            sysref_div (int): SYSREF分频器值
        """
        try:
            self._frequency_cache['sysref_freq'] = sysref_freq
            self._frequency_cache['sysref_div'] = sysref_div
            logger.info(f"RegisterUpdateBus: 已缓存SYSREF数据 - 频率: {sysref_freq} MHz, 分频器: {sysref_div}")
        except Exception as e:
            logger.error(f"RegisterUpdateBus: 缓存SYSREF数据时出错: {str(e)}")

    def get_cached_sysref_freq(self):
        """获取缓存的SYSREF频率值

        Returns:
            float or None: 缓存的SYSREF频率值，如果没有缓存则返回None
        """
        try:
            cached_value = self._frequency_cache.get('sysref_freq')
            if cached_value is not None:
                logger.debug(f"RegisterUpdateBus: 获取缓存的SYSREF频率值: {cached_value} MHz")
            else:
                logger.debug("RegisterUpdateBus: 没有缓存的SYSREF频率值")
            return cached_value
        except Exception as e:
            logger.error(f"RegisterUpdateBus: 获取缓存SYSREF频率值时出错: {str(e)}")
            return None

    def get_cached_sysref_div(self):
        """获取缓存的SYSREF分频器值

        Returns:
            int or None: 缓存的SYSREF分频器值，如果没有缓存则返回None
        """
        try:
            cached_value = self._frequency_cache.get('sysref_div')
            if cached_value is not None:
                logger.debug(f"RegisterUpdateBus: 获取缓存的SYSREF分频器值: {cached_value}")
            else:
                logger.debug("RegisterUpdateBus: 没有缓存的SYSREF分频器值")
            return cached_value
        except Exception as e:
            logger.error(f"RegisterUpdateBus: 获取缓存SYSREF分频器值时出错: {str(e)}")
            return None

    def cache_sysref_freq(self, freq_value):
        """缓存SYSREF频率值（专门用于PLL2Cin）

        Args:
            freq_value (float): SYSREF频率值(MHz)
        """
        try:
            self._frequency_cache['sysref_freq'] = freq_value
            logger.info(f"RegisterUpdateBus: 已缓存SYSREF频率值(用于PLL2Cin): {freq_value} MHz")

            # 发送SYSREF频率更新信号
            if hasattr(self, 'sysref_freq_updated'):
                self.sysref_freq_updated.emit(freq_value)
        except Exception as e:
            logger.error(f"RegisterUpdateBus: 缓存SYSREF频率值时出错: {str(e)}")

    def cache_pll2_pfd_freq(self, freq_value):
        """缓存PLL2PFD频率值

        Args:
            freq_value (float): PLL2PFD频率值(MHz)
        """
        try:
            self._frequency_cache['pll2_pfd_freq'] = freq_value
            logger.info(f"RegisterUpdateBus: 已缓存PLL2PFD频率值: {freq_value} MHz")
        except Exception as e:
            logger.error(f"RegisterUpdateBus: 缓存PLL2PFD频率值时出错: {str(e)}")

    def get_cached_pll2_pfd_freq(self):
        """获取缓存的PLL2PFD频率值

        Returns:
            float or None: 缓存的PLL2PFD频率值，如果没有缓存则返回None
        """
        try:
            cached_value = self._frequency_cache.get('pll2_pfd_freq')
            if cached_value is not None:
                logger.debug(f"RegisterUpdateBus: 获取缓存的PLL2PFD频率值: {cached_value} MHz")
            else:
                logger.debug("RegisterUpdateBus: 没有缓存的PLL2PFD频率值")
            return cached_value
        except Exception as e:
            logger.error(f"RegisterUpdateBus: 获取缓存PLL2PFD频率值时出错: {str(e)}")
            return None

    def cache_pll2_n_divider(self, divider_value):
        """缓存PLL2NDivider值

        Args:
            divider_value (float): PLL2NDivider值
        """
        try:
            self._frequency_cache['pll2_n_divider'] = divider_value
            logger.info(f"RegisterUpdateBus: 已缓存PLL2NDivider值: {divider_value}")
        except Exception as e:
            logger.error(f"RegisterUpdateBus: 缓存PLL2NDivider值时出错: {str(e)}")

    def get_cached_pll2_n_divider(self):
        """获取缓存的PLL2NDivider值

        Returns:
            float or None: 缓存的PLL2NDivider值，如果没有缓存则返回None
        """
        try:
            cached_value = self._frequency_cache.get('pll2_n_divider')
            if cached_value is not None:
                logger.debug(f"RegisterUpdateBus: 获取缓存的PLL2NDivider值: {cached_value}")
            else:
                logger.debug("RegisterUpdateBus: 没有缓存的PLL2NDivider值")
            return cached_value
        except Exception as e:
            logger.error(f"RegisterUpdateBus: 获取缓存PLL2NDivider值时出错: {str(e)}")
            return None

    def notify_internal_vco_freq_changed(self, internal_vco_freq):
        """通知PLL窗口InternalVCOFreq已变化，需要同步到VCODistFreq

        Args:
            internal_vco_freq (float): InternalVCOFreq的新值(MHz)
        """
        try:
            logger.info(f"RegisterUpdateBus: 通知InternalVCOFreq变化: {internal_vco_freq} MHz")

            # 发送InternalVCOFreq更新信号
            if hasattr(self, 'internal_vco_freq_updated'):
                self.internal_vco_freq_updated.emit(internal_vco_freq)
                logger.debug("RegisterUpdateBus: 已发送InternalVCOFreq更新信号")
            else:
                logger.warning("RegisterUpdateBus: 缺少internal_vco_freq_updated信号")

        except Exception as e:
            logger.error(f"RegisterUpdateBus: 通知InternalVCOFreq变化时出错: {str(e)}")



    def get_frequency_cache_status(self):
        """获取频率缓存状态

        Returns:
            dict: 包含所有缓存值的字典
        """
        try:
            return self._frequency_cache.copy()
        except Exception as e:
            logger.error(f"RegisterUpdateBus: 获取频率缓存状态时出错: {str(e)}")
            return {}

    # ==================== LineEdit控件值缓存管理方法 ====================

    def cache_lineedit_value(self, window_name, widget_name, value):
        """缓存LineEdit控件的值

        Args:
            window_name (str): 窗口名称（如"ClkinControl", "PLLControl"等）
            widget_name (str): 控件名称（如"lineEditClkin0", "lineEditClkin1"等）
            value (str): 控件值
        """
        try:
            # 创建窗口级别的缓存字典
            if window_name not in self._lineedit_cache:
                self._lineedit_cache[window_name] = {}

            # 缓存控件值
            self._lineedit_cache[window_name][widget_name] = str(value)
            logger.debug(f"RegisterUpdateBus: 已缓存LineEdit值: {window_name}.{widget_name} = '{value}'")
        except Exception as e:
            logger.error(f"RegisterUpdateBus: 缓存LineEdit值时出错: {str(e)}")

    def get_cached_lineedit_value(self, window_name, widget_name):
        """获取缓存的LineEdit控件值

        Args:
            window_name (str): 窗口名称
            widget_name (str): 控件名称

        Returns:
            str or None: 缓存的控件值，如果没有缓存则返回None
        """
        try:
            if window_name in self._lineedit_cache:
                cached_value = self._lineedit_cache[window_name].get(widget_name)
                if cached_value is not None:
                    logger.debug(f"RegisterUpdateBus: 获取缓存的LineEdit值: {window_name}.{widget_name} = '{cached_value}'")
                    return cached_value
                else:
                    logger.debug(f"RegisterUpdateBus: 没有缓存的LineEdit值: {window_name}.{widget_name}")
            else:
                logger.debug(f"RegisterUpdateBus: 窗口 {window_name} 没有缓存的LineEdit值")
            return None
        except Exception as e:
            logger.error(f"RegisterUpdateBus: 获取缓存LineEdit值时出错: {str(e)}")
            return None

    def clear_lineedit_cache(self, window_name=None):
        """清空LineEdit控件值缓存

        Args:
            window_name (str, optional): 窗口名称，如果指定则只清空该窗口的缓存，否则清空所有缓存
        """
        try:
            if window_name:
                if window_name in self._lineedit_cache:
                    del self._lineedit_cache[window_name]
                    logger.info(f"RegisterUpdateBus: 已清空窗口 {window_name} 的LineEdit缓存")
                else:
                    logger.debug(f"RegisterUpdateBus: 窗口 {window_name} 没有LineEdit缓存需要清空")
            else:
                self._lineedit_cache = {}
                logger.info("RegisterUpdateBus: 已清空所有LineEdit缓存")
        except Exception as e:
            logger.error(f"RegisterUpdateBus: 清空LineEdit缓存时出错: {str(e)}")

    def get_lineedit_cache_status(self, window_name=None):
        """获取LineEdit缓存状态

        Args:
            window_name (str, optional): 窗口名称，如果指定则只返回该窗口的缓存状态

        Returns:
            dict: 包含缓存值的字典
        """
        try:
            if window_name:
                return self._lineedit_cache.get(window_name, {}).copy()
            else:
                return self._lineedit_cache.copy()
        except Exception as e:
            logger.error(f"RegisterUpdateBus: 获取LineEdit缓存状态时出错: {str(e)}")
            return {}