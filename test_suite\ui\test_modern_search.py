#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试现代化寄存器IO处理器的搜索功能
验证搜索功能是否按照重构前的逻辑正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import QTimer
from ui.handlers.ModernRegisterIOHandler import ModernRegisterIOHandler
from utils.Log import get_module_logger

logger = get_module_logger(__name__)

class TestSearchWindow(QMainWindow):
    """测试搜索功能的主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("测试现代化搜索功能")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 添加说明标签
        info_label = QLabel("测试现代化寄存器IO处理器的搜索功能")
        layout.addWidget(info_label)
        
        # 创建现代化IO处理器
        try:
            self.io_handler = ModernRegisterIOHandler.create_for_testing(self)
            layout.addWidget(self.io_handler)
            
            # 连接信号
            self.io_handler.search_requested.connect(self.on_search_requested)
            self.io_handler.bit_field_selected.connect(self.on_bit_field_selected)
            
            logger.info("现代化IO处理器创建成功")
            
        except Exception as e:
            logger.error(f"创建现代化IO处理器失败: {str(e)}")
            error_label = QLabel(f"创建失败: {str(e)}")
            layout.addWidget(error_label)
    
    def on_search_requested(self, search_text):
        """处理搜索请求"""
        logger.info(f"搜索请求: '{search_text}'")
        # 搜索功能由RegisterManager处理，这里只是记录
        if hasattr(self.io_handler, 'register_manager') and self.io_handler.register_manager:
            results = self.io_handler.register_manager.search_bit_fields(search_text)
            logger.info(f"  找到 {len(results)} 个结果")
            for bit_name, reg_addr, bit_range in results:
                logger.info(f"    {bit_name} (寄存器: {reg_addr}, 位: {bit_range})")
    
    def on_bit_field_selected(self, reg_addr):
        """处理位字段选择"""
        logger.info(f"位字段选择: 寄存器 {reg_addr}")
        # 切换到选中的寄存器
        try:
            addr = int(reg_addr, 16) if reg_addr.startswith("0x") else int(reg_addr)
            self.io_handler.set_address_display(addr)
            if hasattr(self.io_handler, 'register_manager') and self.io_handler.register_manager:
                value = self.io_handler.register_manager.get_register_value(addr)
                self.io_handler.set_value_display(value)
                logger.info(f"已切换到寄存器 0x{addr:02X}, 值: 0x{value:04X}")
        except ValueError:
            logger.error(f"无效的寄存器地址: {reg_addr}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestSearchWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
