#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
现代化的UI事件处理器
使用ModernBaseHandler作为基类，重构自原UIEventHandler
主要功能：处理主窗口的各种UI事件，包括按钮点击、寄存器操作、模式切换等
"""

import os
import sys
import traceback
from PyQt5.QtWidgets import QMessageBox, QVBoxLayout
from PyQt5.QtCore import pyqtSignal
from ui.handlers.ModernBaseHandler import ModernBaseHandler
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class ModernUIEventHandler(ModernBaseHandler):
    """现代化的UI事件处理器"""
    
    # 添加事件相关信号
    read_button_clicked = pyqtSignal()
    write_button_clicked = pyqtSignal()
    simulation_mode_toggled = pyqtSignal(bool)
    preload_toggled = pyqtSignal(bool)
    language_changed = pyqtSignal(str)
    
    def __init__(self, parent=None, register_manager=None, **kwargs):
        """初始化现代化UI事件处理器

        Args:
            parent: 父窗口
            register_manager: RegisterManager实例
            **kwargs: 其他参数（如register_repo等，用于兼容性）
        """
        super().__init__(parent, register_manager, **kwargs)
        
        # 设置窗口标题
        self.setWindowTitle("UI事件处理器 (现代化版本)")
        
        # 初始化事件处理特定属性
        self._init_event_config()
        
        # 创建UI
        self._create_event_ui()
        
        # 手动调用初始化（因为测试环境没有事件循环）
        self._post_init()
        
        logger.info("现代化UI事件处理器初始化完成")
    
    def _init_event_config(self):
        """初始化事件处理特定配置"""
        try:
            # 事件处理配置
            self.event_config = {
                "enable_batch_protection": True,  # 批量操作保护
                "enable_value_validation": True,  # 值验证
                "enable_status_messages": True,   # 状态消息
                "auto_refresh_view": True,        # 自动刷新视图
                "simulation_mode": False,         # 模拟模式状态
                "preload_enabled": False,         # 预加载启用状态
                "current_language": "zh_CN"       # 当前语言
            }
            
            # 事件统计
            self.event_stats = {
                "read_requests": 0,
                "write_requests": 0,
                "value_changes": 0,
                "mode_toggles": 0,
                "errors": 0
            }
            
            logger.info("事件处理配置初始化完成")
            
        except Exception as e:
            logger.error(f"初始化事件处理配置时出错: {str(e)}")
    
    def _create_event_ui(self):
        """创建事件处理UI"""
        try:
            # 创建主布局
            layout = QVBoxLayout()
            
            # 这个处理器主要是逻辑处理，UI相对简单
            # 可以添加一些状态显示控件
            
            # 设置内容控件的布局
            self.content_widget.setLayout(layout)
            
            logger.info("事件处理UI创建完成")
            
        except Exception as e:
            logger.error(f"创建事件处理UI时出错: {str(e)}")
    
    def on_register_value_changed(self, widget_name, reg_addr, reg_value):
        """处理寄存器值变化"""
        logger.info(f"UI事件处理器: 寄存器 {reg_addr} 值变化 (控件: {widget_name}) -> 0x{reg_value:04X}")
        
        # 更新事件统计
        self.event_stats["value_changes"] += 1
        
        # 处理值变化逻辑
        self._handle_register_value_change(reg_addr, reg_value)
    
    def on_global_register_update(self, reg_addr, reg_value):
        """处理全局寄存器更新"""
        logger.debug(f"UI事件处理器: 收到全局更新 {reg_addr} = 0x{reg_value:04X}")
        
        # 可以在这里添加全局更新的特殊处理逻辑
    
    # === 按钮事件处理方法 ===
    
    def handle_read_button_click(self):
        """处理读取按钮点击事件"""
        try:
            self.event_stats["read_requests"] += 1
            
            # 获取当前选中的寄存器地址
            addr = self._get_current_register_address()
            if addr:
                self.handle_read_requested(int(addr, 16) if isinstance(addr, str) else addr)
                self.read_button_clicked.emit()
            else:
                self._show_error_message("错误", "请先选择一个寄存器")
                
        except Exception as e:
            self.event_stats["errors"] += 1
            logger.error(f"处理读取按钮点击时出错: {str(e)}")
            self._show_error_message("错误", f"读取操作失败: {str(e)}")
    
    def handle_write_button_click(self):
        """处理写入按钮点击事件"""
        try:
            self.event_stats["write_requests"] += 1
            
            # 获取寄存器地址
            addr = self._get_current_register_address()
            if not addr:
                self._show_error_message("错误", "请先选择一个寄存器")
                return
                
            # 获取用户输入值
            input_value = self._get_current_input_value()
            logger.info(f"用户输入值: {input_value}")
            if input_value is None:
                self._show_error_message("错误", "请输入有效的值")
                return
                
            # 更新寄存器值
            self.handle_write_requested(int(addr, 16) if isinstance(addr, str) else addr, input_value)
            self.write_button_clicked.emit()
            
        except Exception as e:
            self.event_stats["errors"] += 1
            logger.error(f"写入操作失败: {str(e)}")
            self._show_error_message("错误", f"写入操作失败: {str(e)}")
    
    # === 寄存器操作方法 ===
    
    def handle_read_requested(self, addr):
        """处理读取请求"""
        try:
            # 将整数地址转换为标准格式的字符串地址
            addr_str = f"0x{addr:02X}" if isinstance(addr, int) else addr
            
            if self.event_config["simulation_mode"]:
                # 模拟读取
                value = self._simulate_register_read(addr_str)
                self._handle_spi_result(addr_str, value, True)
            else:
                # 实际读取
                self._execute_spi_read(addr_str)
                
            logger.info(f"处理读取请求: {addr_str}")
            
        except Exception as e:
            self.event_stats["errors"] += 1
            logger.error(f"处理读取请求时出错: {str(e)}")
    
    def handle_write_requested(self, addr, value):
        """处理写入请求"""
        try:
            # 将整数地址转换为标准格式的字符串地址
            addr_str = f"0x{addr:02X}" if isinstance(addr, int) else addr
            
            # 验证值的有效性
            if self.event_config["enable_value_validation"]:
                if not self._validate_register_value(value):
                    self._show_error_message("错误", f"无效的寄存器值: 0x{value:04X}")
                    return
            
            # 更新寄存器值并显示
            self._update_register_value_and_display(addr_str, value)
            
            logger.info(f"处理写入请求: {addr_str} = 0x{value:04X}")
            
        except Exception as e:
            self.event_stats["errors"] += 1
            logger.error(f"处理写入请求时出错: {str(e)}")
    
    def handle_io_write_request(self, addr, value):
        """处理来自 IO Handler 的直接写入请求 (例如，通过回车确认输入)"""
        try:
            logger.info(f"UI事件处理器: 收到来自 IO Handler 的写入请求, 地址: 0x{addr:02X}, 值: 0x{value:04X}")
            
            # 1. 更新 RegisterManager 中的值
            if self.register_manager:
                success = self.register_manager.set_register_value(addr, value)
                if success:
                    # 2. 执行SPI写入操作
                    self._execute_spi_write(addr, value)
                else:
                    logger.error(f"更新寄存器管理器中的值失败: 地址 0x{addr:02X}, 值 0x{value:04X}")
            
        except Exception as e:
            self.event_stats["errors"] += 1
            logger.error(f"处理IO写入请求时出错: {str(e)}")
    
    def handle_value_changed(self, addr, new_value):
        """处理输入值变化事件"""
        try:
            # 标准化地址
            normalized_addr = self._normalize_register_address(addr)
            
            # 记录当前输入值，但不立即触发写操作
            # 只有当用户点击写入按钮时才执行写操作
            current_addr = self._get_current_register_address()
            if normalized_addr == current_addr:
                # 记录原值用于显示变化
                old_value = self._get_current_register_value()
                
                # 仅更新显示，不执行写操作
                self._set_current_register_value(new_value)
                
                # 仅更新位字段表格显示，不触发写操作
                self._update_bit_field_display(normalized_addr, new_value)
                    
                # 刷新视图确保显示更新
                if self.event_config["auto_refresh_view"]:
                    self._refresh_view()
                
                # 在状态栏显示寄存器变化详情
                if self.event_config["enable_status_messages"]:
                    reg_num = int(normalized_addr, 16) if isinstance(normalized_addr, str) else normalized_addr
                    self._show_status_message(
                        f"寄存器 R{reg_num} (0x{reg_num:02X}) 值已修改: 0x{old_value:04X} → 0x{new_value:04X}", 
                        5000
                    )
                    
        except Exception as e:
            self.event_stats["errors"] += 1
            logger.error(f"处理值变化事件时出错: {str(e)}")
    
    def handle_bit_field_selected(self, addr):
        """处理位段选中事件，在批量操作期间不跳转
        
        Args:
            addr: 寄存器地址
        """
        try:
            # 只有在非批量操作时才允许跳转
            if not self._is_in_batch_operation():
                self._select_register_by_addr(addr)
            else:
                logger.debug(f"批量操作期间忽略位段选中跳转请求: {addr}")
                
        except Exception as e:
            logger.error(f"处理位段选中事件时出错: {str(e)}")
    
    # === 模式切换方法 ===
    
    def toggle_simulation_mode(self, checked):
        """切换模拟模式"""
        try:
            self.event_config["simulation_mode"] = checked
            self.event_stats["mode_toggles"] += 1
            
            # 更新SPI服务模式
            self._set_spi_simulation_mode(checked)
                
            # 更新状态栏
            mode_text = "模拟模式" if checked else "硬件模式"
            if self.event_config["enable_status_messages"]:
                self._show_status_message(f"已切换到{mode_text}", 3000)
            
            # 保存模拟模式状态
            self._save_simulation_mode(checked)
            
            # 发送信号
            self.simulation_mode_toggled.emit(checked)
            
            logger.info(f"已切换到{mode_text}")
            
        except Exception as e:
            self.event_stats["errors"] += 1
            logger.error(f"切换模拟模式时出错: {str(e)}")
    
    def toggle_preload(self, checked):
        """切换是否启用预读取功能
        
        Args:
            checked: 是否选中
        """
        try:
            self.event_config["preload_enabled"] = checked
            
            # 这里可以添加预读取功能的逻辑
            # 例如：在选择寄存器时自动读取其值
            logger.info(f"预读取功能已{'启用' if checked else '禁用'}")
            
            # 发送信号
            self.preload_toggled.emit(checked)
            
            # 可以保存这个设置到配置文件
            # self._save_preload_setting(checked)
            
        except Exception as e:
            logger.error(f"切换预读取功能时出错: {str(e)}")
    
    # === UI功能方法 ===
    
    def show_advanced_settings(self):
        """显示高级设置对话框"""
        try:
            # 这里可以显示一个更复杂的设置对话框，允许用户配置更多选项
            QMessageBox.information(
                self, 
                "高级设置", 
                "高级设置功能正在开发中。\n"
                "未来版本将支持更多自定义选项。",
                QMessageBox.Ok
            )
            
        except Exception as e:
            logger.error(f"显示高级设置时出错: {str(e)}")
    
    def show_user_manual(self):
        """显示用户手册"""
        try:
            # 检查用户手册文件是否存在
            manual_path = self._get_resource_path('docs/user_manual.pdf')
            if os.path.exists(manual_path):
                # 使用系统默认程序打开PDF文件
                self._open_file_with_system(manual_path)
            else:
                # 文件不存在，显示提示信息
                QMessageBox.information(
                    self,
                    "用户手册",
                    "用户手册文件不存在。\n\n"
                    "请联系技术支持获取最新的用户手册。",
                    QMessageBox.Ok
                )
                
        except Exception as e:
            logger.error(f"显示用户手册时出错: {str(e)}")
            self._show_error_message("打开失败", f"无法打开用户手册文件:\n{str(e)}")
    
    def show_about_dialog(self):
        """显示关于对话框"""
        try:
            # 获取版本信息
            from core.services.version.VersionService import VersionService
            version_service = VersionService.instance()

            app_name = version_service.get_app_name()
            version = version_service.get_version_string()
            description = version_service.get_app_description()
            company = version_service.get_company()
            copyright_info = version_service.get_copyright()
            build_date = version_service.get_build_date()

            # 构建关于信息
            about_text = f"""
            <h3>{app_name} (现代化版本)</h3>
            <p><b>版本:</b> {version}</p>
            <p><b>描述:</b> {description}</p>
            <p><b>开发者:</b> {company}</p>
            <p><b>版权:</b> {copyright_info}</p>
            """

            if build_date:
                about_text += f"<p><b>构建日期:</b> {build_date}</p>"

            about_text += """
            <hr>
            <p>本软件提供寄存器读写、批量操作、配置管理等功能。</p>
            <p>现代化版本提供更好的性能和用户体验。</p>
            <p>如有问题或建议，请联系技术支持。</p>
            """

            QMessageBox.about(self, "关于", about_text)

        except Exception as e:
            logger.error(f"显示关于对话框时出错: {str(e)}")
            # 使用默认信息
            about_text = """
            <h3>FSJ04832 寄存器配置工具 (现代化版本)</h3>
            <p><b>版本:</b> 1.0.0</p>
            <p><b>描述:</b> 用于配置和管理寄存器的专业工具</p>
            <p><b>开发者:</b> 开发团队</p>
            <p><b>版权:</b> © 2024 公司名称</p>
            <hr>
            <p>本软件提供寄存器读写、批量操作、配置管理等功能。</p>
            <p>现代化版本提供更好的性能和用户体验。</p>
            <p>如有问题或建议，请联系技术支持。</p>
            """
            QMessageBox.about(self, "关于", about_text)
    
    def handle_language_change(self, action):
        """处理语言切换"""
        try:
            if action:
                lang_code = action.data()
                self.event_config["current_language"] = lang_code
                
                # 委托给配置管理服务
                self._set_language(lang_code)
                
                # 发送信号
                self.language_changed.emit(lang_code)
                
                logger.info(f"语言已切换到: {lang_code}")
                
        except Exception as e:
            logger.error(f"处理语言切换时出错: {str(e)}")
    
    def global_exception_handler(self, exc_type, exc_value, exc_traceback):
        """全局异常处理器"""
        try:
            error_msg = "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))
            logger.error(f"未捕获的异常: {error_msg}")
            
            QMessageBox.critical(
                self, 
                "程序错误", 
                f"发生未捕获的异常:\n{str(exc_value)}\n\n详细信息已记录到日志文件"
            )
            
        except Exception as e:
            logger.error(f"全局异常处理器出错: {str(e)}")
    
    # === 辅助方法 ===
    
    def _handle_register_value_change(self, reg_addr, reg_value):
        """处理寄存器值变化的内部逻辑"""
        try:
            # 可以在这里添加特定的值变化处理逻辑
            pass

        except Exception as e:
            logger.error(f"处理寄存器值变化时出错: {str(e)}")

    def _get_current_register_address(self):
        """获取当前选中的寄存器地址"""
        try:
            # 从主窗口获取当前选中的寄存器地址
            main_window = self._get_main_window()
            if main_window:
                # 优先使用主窗口的selected_register_addr属性
                if hasattr(main_window, 'selected_register_addr') and main_window.selected_register_addr:
                    logger.debug(f"从主窗口获取当前寄存器地址: {main_window.selected_register_addr}")
                    return main_window.selected_register_addr

                # 如果没有，尝试从树形控件获取
                if hasattr(main_window, 'tree_handler') and hasattr(main_window.tree_handler, 'get_current_register_addr'):
                    tree_addr = main_window.tree_handler.get_current_register_addr()
                    logger.debug(f"从树形控件获取当前寄存器地址: {tree_addr}")
                    return tree_addr

                # 最后尝试从生命周期管理器获取
                if hasattr(main_window, 'lifecycle_manager') and hasattr(main_window.lifecycle_manager, 'get_register_addr_from_tree_item'):
                    lifecycle_addr = main_window.lifecycle_manager.get_register_addr_from_tree_item()
                    logger.debug(f"从生命周期管理器获取当前寄存器地址: {lifecycle_addr}")
                    return lifecycle_addr

            logger.warning("无法获取当前寄存器地址，所有方法都失败")
            return None
        except Exception as e:
            logger.error(f"获取当前寄存器地址时出错: {str(e)}")
            return None

    def _get_current_input_value(self):
        """获取当前输入值"""
        try:
            # 从主窗口的IO处理器获取当前输入值
            main_window = self._get_main_window()
            if main_window and hasattr(main_window, 'io_handler'):
                if hasattr(main_window.io_handler, 'get_current_value'):
                    return main_window.io_handler.get_current_value()

            logger.warning("无法获取当前输入值，IO处理器不可用")
            return None
        except Exception as e:
            logger.error(f"获取当前输入值时出错: {str(e)}")
            return None

    def _get_current_register_value(self):
        """获取当前寄存器值"""
        try:
            # 这里应该从寄存器管理器获取当前值
            if self.register_manager:
                addr = self._get_current_register_address()
                if addr:
                    return self.register_manager.get_register_value(addr)
            return 0x0000
        except Exception as e:
            logger.error(f"获取当前寄存器值时出错: {str(e)}")
            return 0x0000

    def _set_current_register_value(self, value):
        """设置当前寄存器值"""
        try:
            # 这里应该更新寄存器管理器中的值
            if self.register_manager:
                addr = self._get_current_register_address()
                if addr:
                    self.register_manager.set_register_value(addr, value)
        except Exception as e:
            logger.error(f"设置当前寄存器值时出错: {str(e)}")

    def _normalize_register_address(self, addr):
        """标准化寄存器地址格式"""
        try:
            if isinstance(addr, int):
                return f"0x{addr:02X}"
            elif isinstance(addr, str):
                if addr.startswith("0x") or addr.startswith("0X"):
                    return addr.upper()
                else:
                    return f"0x{int(addr, 16):02X}"
            return str(addr)
        except Exception as e:
            logger.error(f"标准化寄存器地址时出错: {str(e)}")
            return str(addr)

    def _validate_register_value(self, value):
        """验证寄存器值的有效性"""
        try:
            # 检查值是否在有效范围内（16位寄存器）
            return 0 <= value <= 0xFFFF
        except Exception as e:
            logger.error(f"验证寄存器值时出错: {str(e)}")
            return False

    def _simulate_register_read(self, addr_str):
        """模拟寄存器读取"""
        try:
            # 在模拟模式下返回模拟值
            if self.register_manager:
                return self.register_manager.get_register_value(addr_str)
            return 0x0000
        except Exception as e:
            logger.error(f"模拟寄存器读取时出错: {str(e)}")
            return 0x0000

    def _show_error_message(self, title, message):
        """显示错误消息"""
        try:
            QMessageBox.warning(self, title, message)
        except Exception as e:
            logger.error(f"显示错误消息时出错: {str(e)}")

    def _show_status_message(self, message, timeout=3000):
        """显示状态消息"""
        try:
            # 这里应该委托给主窗口的状态栏
            logger.info(f"状态消息: {message}")
        except Exception as e:
            logger.error(f"显示状态消息时出错: {str(e)}")

    def _get_resource_path(self, relative_path):
        """获取资源文件路径"""
        try:
            # 这里应该委托给主窗口的资源路径方法
            base_path = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            return os.path.join(base_path, relative_path)
        except Exception as e:
            logger.error(f"获取资源路径时出错: {str(e)}")
            return relative_path

    def _open_file_with_system(self, file_path):
        """使用系统默认程序打开文件"""
        try:
            if sys.platform == 'win32':
                os.startfile(file_path)  # Windows特有方法
            elif sys.platform == 'darwin':
                import subprocess
                subprocess.call(['open', file_path])  # macOS
            else:
                import subprocess
                subprocess.call(['xdg-open', file_path])  # Linux
        except Exception as e:
            logger.error(f"使用系统程序打开文件时出错: {str(e)}")
            raise e

    # === 委托方法（在实际应用中会委托给主窗口） ===

    def _handle_spi_result(self, addr_str, value, is_read):
        """处理SPI操作结果"""
        logger.info(f"SPI结果: {addr_str} = 0x{value:04X} ({'读取' if is_read else '写入'})")

    def _execute_spi_read(self, addr_str):
        """执行SPI读取操作"""
        logger.info(f"执行SPI读取: {addr_str}")

    def _execute_spi_write(self, addr, value):
        """执行SPI写入操作"""
        logger.info(f"执行SPI写入: 0x{addr:02X} = 0x{value:04X}")

    def _update_register_value_and_display(self, addr_str, value):
        """更新寄存器值并显示"""
        logger.info(f"更新寄存器值并显示: {addr_str} = 0x{value:04X}")

    def _update_bit_field_display(self, addr, value):
        """更新位字段显示"""
        logger.info(f"更新位字段显示: {addr} = 0x{value:04X}")

    def _refresh_view(self):
        """刷新视图"""
        logger.debug("刷新视图")

    def _is_in_batch_operation(self):
        """检查是否在批量操作中"""
        return self.event_config.get("batch_protection", False)

    def _select_register_by_addr(self, addr):
        """根据地址选择寄存器"""
        logger.info(f"选择寄存器: {addr}")

    def _set_spi_simulation_mode(self, enabled):
        """设置SPI模拟模式"""
        logger.info(f"设置SPI模拟模式: {enabled}")

    def _save_simulation_mode(self, enabled):
        """保存模拟模式设置"""
        logger.info(f"保存模拟模式设置: {enabled}")

    def _set_language(self, lang_code):
        """设置语言"""
        logger.info(f"设置语言: {lang_code}")

    # === 公共接口方法 ===
    
    def get_current_status(self):
        """获取当前事件处理器状态
        
        Returns:
            dict: 当前状态信息
        """
        try:
            status = {
                "simulation_mode": self.event_config["simulation_mode"],
                "preload_enabled": self.event_config["preload_enabled"],
                "current_language": self.event_config["current_language"],
                "event_stats": self.event_stats.copy(),
                "config": self.event_config.copy()
            }
            
            return status
            
        except Exception as e:
            logger.error(f"获取事件处理器状态时出错: {str(e)}")
            return {}
    
    @classmethod
    def create_for_testing(cls, parent=None):
        """创建测试实例"""
        try:
            # 创建模拟的RegisterManager
            from core.services.register.RegisterManager import RegisterManager
            import json
            import os
            
            # 加载寄存器配置
            config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'lib', 'register.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                registers_config = json.load(f)
            
            register_manager = RegisterManager(registers_config)
            
            # 创建处理器实例
            handler = cls(parent, register_manager)
            
            logger.info("创建现代化UIEventHandler测试实例成功")
            return handler
            
        except Exception as e:
            logger.error(f"创建测试实例时出错: {str(e)}")
            return None
