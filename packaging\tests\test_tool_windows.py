#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
工具窗口测试脚本
测试安全打包版本中的工具窗口是否能正常工作
"""

import sys
import os
import subprocess
import time
from pathlib import Path

def test_tool_windows_in_release():
    """测试发布版本中的工具窗口"""
    print("🔍 测试安全打包版本中的工具窗口功能")
    print("=" * 60)
    
    # 查找最新的Release版本
    current_dir = Path.cwd()  # 当前工作目录是packaging
    project_root = current_dir.parent  # 项目根目录
    releases_dir = project_root / 'releases'
    latest_dir = releases_dir / 'latest'

    print(f"🔍 查找路径:")
    print(f"   当前目录: {current_dir}")
    print(f"   项目根目录: {project_root}")
    print(f"   发布目录: {releases_dir}")
    print(f"   最新版本目录: {latest_dir}")
    print()
    
    if not latest_dir.exists():
        print("❌ 找不到最新发布版本")
        return False
    
    # 查找Release exe文件
    exe_files = list(latest_dir.glob('*_Release.exe'))
    if not exe_files:
        print("❌ 找不到Release版本的exe文件")
        return False
    
    exe_file = exe_files[0]
    print(f"📦 测试文件: {exe_file}")
    print(f"📏 文件大小: {exe_file.stat().st_size / 1024 / 1024:.1f} MB")
    print()
    
    # 检查是否为单文件
    print("🔒 安全特性验证:")
    print(f"   ✅ 单文件可执行程序: {exe_file.name}")
    print(f"   ✅ 无目录结构暴露: 只有一个exe文件")
    print(f"   ✅ 代码完全封装: 客户无法看到内部文件")
    print()
    
    # 启动程序进行测试
    print("🚀 启动程序测试工具窗口...")
    print("   注意: 程序将启动，请手动测试以下功能:")
    print()
    print("📋 测试清单:")
    print("   1. 主窗口是否正常显示")
    print("   2. 菜单栏 -> 工具 -> 时钟输入控制窗口")
    print("   3. 菜单栏 -> 工具 -> PLL控制窗口") 
    print("   4. 菜单栏 -> 工具 -> 时钟输出窗口")
    print("   5. 菜单栏 -> 工具 -> 同步系统参考窗口")
    print("   6. 菜单栏 -> 工具 -> 模式设置窗口")
    print()
    print("✅ 如果所有工具窗口都能正常打开，说明打包成功!")
    print("❌ 如果有工具窗口无法打开，说明还有模块缺失")
    print()
    
    try:
        # 启动程序
        print(f"启动程序: {exe_file}")
        process = subprocess.Popen([str(exe_file)], 
                                 cwd=exe_file.parent,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)
        
        print("程序已启动，请进行手动测试...")
        print("测试完成后，关闭程序窗口即可")
        
        # 等待程序结束
        process.wait()
        
        print()
        print("程序已关闭")
        
        if process.returncode == 0:
            print("✅ 程序正常退出")
            return True
        else:
            print(f"⚠️  程序退出码: {process.returncode}")
            stderr_output = process.stderr.read().decode('utf-8', errors='ignore')
            if stderr_output:
                print(f"错误输出: {stderr_output}")
            return False
            
    except Exception as e:
        print(f"❌ 启动程序失败: {e}")
        return False

def create_tool_window_test_guide():
    """创建工具窗口测试指南"""
    guide_content = """
# 工具窗口测试指南

## 🎯 测试目标
验证安全打包版本中的所有工具窗口是否能正常工作

## 📋 测试步骤

### 1. 启动程序
- 双击 `FSJ04832_RegisterTool_v1.0.3.9_Release.exe`
- 确认主窗口正常显示

### 2. 测试工具窗口
依次测试以下菜单项：

#### 时钟输入控制窗口
- 菜单: 工具 -> 时钟输入控制窗口
- 预期: 打开时钟输入配置界面
- 检查: 界面元素是否完整显示

#### PLL控制窗口  
- 菜单: 工具 -> PLL控制窗口
- 预期: 打开PLL1和PLL2配置界面
- 检查: PLL参数设置是否正常

#### 时钟输出窗口
- 菜单: 工具 -> 时钟输出窗口  
- 预期: 打开时钟输出配置界面
- 检查: 输出通道设置是否正常

#### 同步系统参考窗口
- 菜单: 工具 -> 同步系统参考窗口
- 预期: 打开同步配置界面
- 检查: 同步参数是否可设置

#### 模式设置窗口
- 菜单: 工具 -> 模式设置窗口
- 预期: 打开工作模式配置界面
- 检查: 模式选择是否正常

### 3. 测试结果
- ✅ 所有窗口都能正常打开 = 打包成功
- ❌ 有窗口无法打开 = 需要修复打包配置

## 🔍 常见问题

### 窗口无法打开
- 可能原因: 相关模块未正确打包
- 解决方案: 检查PyInstaller的hiddenimports配置

### 界面显示异常
- 可能原因: UI资源文件缺失
- 解决方案: 确保.ui文件和资源文件已包含

### 功能异常
- 可能原因: 依赖的服务模块缺失
- 解决方案: 检查核心服务模块的导入

## 📞 技术支持
如有问题，请检查:
1. 打包配置文件: packaging/scripts/build.spec
2. 隐藏导入列表: hiddenimports
3. 数据文件列表: added_files
"""
    
    guide_file = Path(__file__).parent / 'tool_window_test_guide.md'
    with open(guide_file, 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print(f"📝 测试指南已创建: {guide_file}")

def main():
    """主函数"""
    print("🔧 工具窗口测试工具")
    print("=" * 60)
    
    # 创建测试指南
    create_tool_window_test_guide()
    print()
    
    # 运行测试
    success = test_tool_windows_in_release()
    
    print()
    print("=" * 60)
    if success:
        print("🎉 测试完成!")
    else:
        print("⚠️  测试过程中发现问题")
    
    print()
    print("💡 提示:")
    print("   - 这是一个手动测试，需要您亲自验证工具窗口功能")
    print("   - 如果发现问题，请参考测试指南进行排查")
    print("   - 测试指南: packaging/tests/tool_window_test_guide.md")

if __name__ == '__main__':
    main()
