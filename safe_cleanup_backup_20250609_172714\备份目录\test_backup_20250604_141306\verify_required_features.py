#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证必需功能脚本
直接验证您要求的9个核心功能是否在代码中实现
"""

import os
import sys
from datetime import datetime

def print_section(title):
    """打印章节标题"""
    print(f"\n{'='*60}")
    print(f"🔍 {title}")
    print(f"{'='*60}")

def check_file_exists(file_path, description):
    """检查文件是否存在"""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} (不存在)")
        return False

def check_code_implementation(file_path, keywords, description):
    """检查代码中是否包含特定关键词"""
    if not os.path.exists(file_path):
        print(f"❌ {description}: 文件不存在 - {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        found_keywords = []
        for keyword in keywords:
            if keyword in content:
                found_keywords.append(keyword)
        
        if found_keywords:
            print(f"✅ {description}: 找到 {len(found_keywords)}/{len(keywords)} 个关键功能")
            for keyword in found_keywords:
                print(f"   - {keyword}")
            return True
        else:
            print(f"❌ {description}: 未找到相关功能")
            return False
            
    except Exception as e:
        print(f"❌ {description}: 读取文件出错 - {e}")
        return False

def verify_feature_1_register_read_write():
    """验证功能1: 读写寄存器"""
    print("\n📋 功能1: 读写寄存器")
    
    # 检查寄存器操作服务
    service_file = "core/services/register/RegisterOperationService.py"
    keywords = ["read_register", "write_register", "validate_register_address"]
    result1 = check_code_implementation(service_file, keywords, "寄存器操作服务")
    
    # 检查主窗口寄存器操作
    main_window_file = "ui/windows/RegisterMainWindow.py"
    keywords = ["register_service", "read_register", "write_register"]
    result2 = check_code_implementation(main_window_file, keywords, "主窗口寄存器操作")
    
    return result1 or result2

def verify_feature_2_read_write_all():
    """验证功能2: 读写所有寄存器"""
    print("\n📋 功能2: 读写所有寄存器")
    
    # 检查批量操作控制器
    batch_file = "ui/controllers/BatchOperationController.py"
    keywords = ["handle_read_all_requested", "handle_write_all_requested", "read_all", "write_all"]
    result1 = check_code_implementation(batch_file, keywords, "批量操作控制器")
    
    # 检查主窗口批量操作
    main_window_file = "ui/windows/RegisterMainWindow.py"
    keywords = ["batch_controller", "read_all", "write_all"]
    result2 = check_code_implementation(main_window_file, keywords, "主窗口批量操作")
    
    return result1 or result2

def verify_feature_3_dump():
    """验证功能3: dump功能"""
    print("\n📋 功能3: dump功能")
    
    # 检查UI工具管理器
    ui_manager_file = "ui/managers/UIUtilityManager.py"
    keywords = ["show_register_dump", "dump", "_create_dump_table"]
    result1 = check_code_implementation(ui_manager_file, keywords, "UI工具管理器dump功能")
    
    # 检查主窗口dump按钮
    main_window_file = "ui/windows/RegisterMainWindow.py"
    keywords = ["dumpall_button_clicked", "dump", "reg_table"]
    result2 = check_code_implementation(main_window_file, keywords, "主窗口dump按钮")
    
    return result1 or result2

def verify_feature_4_5_save_load():
    """验证功能4&5: save和load配置文件功能"""
    print("\n📋 功能4&5: save和load配置文件功能")
    
    # 检查配置服务
    config_file = "core/services/config/ConfigurationService.py"
    keywords = ["save_register_config", "load_register_config", "save", "load"]
    result1 = check_code_implementation(config_file, keywords, "配置服务")
    
    # 检查主窗口配置操作
    main_window_file = "ui/windows/RegisterMainWindow.py"
    keywords = ["save_config", "load_config", "config_service"]
    result2 = check_code_implementation(main_window_file, keywords, "主窗口配置操作")
    
    return result1 or result2

def verify_feature_6_tool_windows():
    """验证功能6: 工具窗口打开"""
    print("\n📋 功能6: 工具窗口打开")
    
    # 检查工具窗口工厂
    factory_file = "ui/factories/ModernToolWindowFactory.py"
    keywords = ["create_window_by_type", "pll_control", "clk_outputs", "sync_sysref"]
    result1 = check_code_implementation(factory_file, keywords, "工具窗口工厂")
    
    # 检查主窗口工具窗口创建
    main_window_file = "ui/windows/RegisterMainWindow.py"
    keywords = ["tool_window_factory", "_show_pll_window", "_show_clk_output_window"]
    result2 = check_code_implementation(main_window_file, keywords, "主窗口工具窗口创建")
    
    return result1 or result2

def verify_feature_7_hardware_communication():
    """验证功能7: 模拟硬件通信"""
    print("\n📋 功能7: 模拟硬件通信")
    
    # 检查SPI服务实现
    spi_file = "core/services/spi/spi_service_impl.py"
    keywords = ["read_register", "write_register", "initialize", "SPI"]
    result1 = check_code_implementation(spi_file, keywords, "SPI服务实现")
    
    # 检查寄存器仓库
    repo_file = "core/repositories/register_repository.py"
    keywords = ["read_register", "write_register", "spi_service"]
    result2 = check_code_implementation(repo_file, keywords, "寄存器仓库")
    
    return result1 or result2

def verify_feature_8_widget_register_jump():
    """验证功能8: 控件状态修改后寄存器表格跳转"""
    print("\n📋 功能8: 控件状态修改后寄存器表格跳转")
    
    # 检查现代化PLL处理器
    pll_handler_file = "ui/handlers/ModernPLLHandler.py"
    keywords = ["_on_widget_changed", "widget_register_map", "register_jump"]
    result1 = check_code_implementation(pll_handler_file, keywords, "PLL处理器控件变化")
    
    # 检查寄存器表格处理器
    table_handler_file = "ui/handlers/ModernRegisterTableHandler.py"
    keywords = ["jump_to_register", "current_register_addr", "on_register_value_changed"]
    result2 = check_code_implementation(table_handler_file, keywords, "寄存器表格跳转")
    
    return result1 or result2

def verify_feature_9_auto_write():
    """验证功能9: 自动写入验证"""
    print("\n📋 功能9: 自动写入验证")
    
    # 检查基础处理器
    base_handler_file = "ui/handlers/BaseHandler.py"
    keywords = ["_auto_write_register_to_chip", "_is_auto_write_enabled", "auto_write"]
    result1 = check_code_implementation(base_handler_file, keywords, "基础处理器自动写入")
    
    # 检查主窗口自动写入模式
    main_window_file = "ui/windows/RegisterMainWindow.py"
    keywords = ["auto_write_mode", "auto_write"]
    result2 = check_code_implementation(main_window_file, keywords, "主窗口自动写入模式")
    
    return result1 or result2

def check_test_coverage():
    """检查测试覆盖情况"""
    print_section("测试覆盖情况")
    
    test_files = [
        ("test_suite/functional/test_core_register_operations.py", "核心寄存器操作测试"),
        ("test_suite/functional/test_widget_register_interaction.py", "控件寄存器交互测试"),
        ("test_suite/functional/test_auto_write.py", "自动写入功能测试"),
        ("test_suite/performance/test_batch_write.py", "批量读写性能测试"),
        ("test_suite/ui/test_window_api_fix.py", "工具窗口API测试"),
        ("test_suite/unit/test_widget_register_jump.py", "控件寄存器跳转测试"),
        ("test_suite/integration/test_module_communication.py", "模块间通信测试")
    ]
    
    existing_tests = 0
    for test_file, description in test_files:
        if check_file_exists(test_file, description):
            existing_tests += 1
    
    print(f"\n📊 测试覆盖统计: {existing_tests}/{len(test_files)} 个测试文件存在")
    return existing_tests

def main():
    """主函数"""
    print("🚀 必需功能验证报告")
    print("=" * 80)
    print(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\n📋 验证您要求的9个核心功能的代码实现情况:")
    
    # 验证各项功能
    features = [
        ("1. 读写寄存器", verify_feature_1_register_read_write),
        ("2. 读写所有寄存器", verify_feature_2_read_write_all),
        ("3. dump功能", verify_feature_3_dump),
        ("4&5. save和load配置文件功能", verify_feature_4_5_save_load),
        ("6. 工具窗口打开", verify_feature_6_tool_windows),
        ("7. 模拟硬件通信", verify_feature_7_hardware_communication),
        ("8. 控件状态修改后寄存器表格跳转", verify_feature_8_widget_register_jump),
        ("9. 自动写入验证", verify_feature_9_auto_write)
    ]
    
    results = []
    for feature_name, verify_func in features:
        try:
            result = verify_func()
            results.append((feature_name, result))
        except Exception as e:
            print(f"❌ {feature_name}: 验证出错 - {e}")
            results.append((feature_name, False))
    
    # 检查测试覆盖
    test_count = check_test_coverage()
    
    # 生成总结报告
    print_section("验证总结")
    
    implemented_count = sum(1 for _, result in results if result)
    total_features = len(results)
    
    print("功能实现情况:")
    for feature_name, result in results:
        status = "✅ 已实现" if result else "❌ 未实现或不完整"
        print(f"  {feature_name}: {status}")
    
    implementation_rate = (implemented_count / total_features * 100) if total_features > 0 else 0
    
    print(f"\n📊 总体统计:")
    print(f"  功能实现: {implemented_count}/{total_features} ({implementation_rate:.1f}%)")
    print(f"  测试覆盖: {test_count}/7 个测试文件")
    
    print(f"\n🎯 结论:")
    if implementation_rate >= 80:
        print("  🎉 优秀！您要求的功能已基本实现")
        print("  📋 建议：可以使用现有测试框架验证功能正确性")
    elif implementation_rate >= 60:
        print("  👍 良好！大部分功能已实现")
        print("  📋 建议：完善未实现的功能并增加测试")
    else:
        print("  ⚠️  需要改进！部分核心功能可能未完全实现")
        print("  📋 建议：优先实现缺失的核心功能")
    
    print(f"\n💡 使用建议:")
    print(f"  1. 运行功能测试: python test_suite/run_all_tests.py --category functional")
    print(f"  2. 运行性能测试: python test_suite/run_all_tests.py --category performance")
    print(f"  3. 运行完整测试: python run_complete_tests.py --quick")
    
    return 0 if implementation_rate >= 70 else 1

if __name__ == "__main__":
    sys.exit(main())
