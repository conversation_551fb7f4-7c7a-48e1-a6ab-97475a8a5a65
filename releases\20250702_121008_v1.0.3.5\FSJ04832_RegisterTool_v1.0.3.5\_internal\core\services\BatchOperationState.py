"""
批量操作状态管理器
用于在整个应用程序中跟踪批量操作状态，避免在批量操作期间触发不必要的UI更新
"""

class BatchOperationState:
    """批量操作状态管理器（单例模式）"""
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(BatchOperationState, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._is_batch_reading = False
            self._is_batch_writing = False
            self._is_batch_updating = False
            self._initialized = True
    
    @classmethod
    def instance(cls):
        """获取单例实例"""
        return cls()
    
    def set_batch_reading(self, value):
        """设置批量读取状态"""
        self._is_batch_reading = value
    
    def set_batch_writing(self, value):
        """设置批量写入状态"""
        self._is_batch_writing = value
    
    def set_batch_updating(self, value):
        """设置批量更新状态"""
        self._is_batch_updating = value
    
    def is_in_batch_operation(self):
        """检查是否正在进行任何批量操作"""
        return (self._is_batch_reading or 
                self._is_batch_writing or 
                self._is_batch_updating)
    
    def is_batch_reading(self):
        """检查是否正在进行批量读取"""
        return self._is_batch_reading
    
    def is_batch_writing(self):
        """检查是否正在进行批量写入"""
        return self._is_batch_writing
    
    def is_batch_updating(self):
        """检查是否正在进行批量更新"""
        return self._is_batch_updating
    
    def reset_all(self):
        """重置所有批量操作状态"""
        self._is_batch_reading = False
        self._is_batch_writing = False
        self._is_batch_updating = False
