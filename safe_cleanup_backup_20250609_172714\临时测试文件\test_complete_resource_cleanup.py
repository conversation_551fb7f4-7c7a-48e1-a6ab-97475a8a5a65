#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试完整资源清理的脚本
验证标签页关闭后所有资源是否正确清理
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_complete_resource_cleanup():
    """测试完整的资源清理流程"""
    print("=== 完整资源清理测试 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication, QAction
        from core.services.plugin.PluginIntegrationService import PluginIntegrationService
        from ui.managers.TabWindowManager import TabWindowManager
        from plugins.example_tool_plugin import ExampleToolPlugin
        
        # 创建应用程序
        app = QApplication([])
        
        print("✓ 模块导入成功")
        
        # 模拟主窗口
        class MockMainWindow:
            def __init__(self):
                from PyQt5.QtWidgets import QTabWidget
                self.tools_tab_widget = QTabWidget()
                self.plugin_integration_service = None
                
                # 模拟菜单动作
                self.clkin_control_action = QAction("时钟输入(&I)")
                self.clkin_control_action.setCheckable(True)
                
                # 模拟主窗口中的窗口引用
                self.clkin_control_window = None
                
        main_window = MockMainWindow()
        
        # 创建插件集成服务
        plugin_service = PluginIntegrationService(main_window)
        main_window.plugin_integration_service = plugin_service
        
        # 创建标签页管理器
        tab_manager = TabWindowManager(main_window)
        
        print("✓ 服务初始化成功")
        
        # 测试插件
        plugin = ExampleToolPlugin()
        plugin.initialize(main_window)
        
        # 注册插件动作
        plugin_service.plugin_actions[plugin.name] = main_window.clkin_control_action
        
        print(f"✓ 插件初始化成功: {plugin.name}")
        
        # 测试场景1: 创建插件窗口并设置状态
        print("\n--- 测试场景1: 创建插件窗口并设置状态 ---")
        window = plugin.create_window()
        plugin_service.plugin_windows[plugin.name] = window
        main_window.clkin_control_window = window  # 模拟主窗口引用
        
        # 设置菜单为选中状态
        main_window.clkin_control_action.setChecked(True)
        
        print(f"插件窗口创建: {window is not None}")
        print(f"插件服务中的窗口: {plugin.name in plugin_service.plugin_windows}")
        print(f"主窗口中的引用: {main_window.clkin_control_window is not None}")
        print(f"菜单状态: {main_window.clkin_control_action.isChecked()}")
        
        # 测试场景2: 集成到标签页
        print("\n--- 测试场景2: 集成到标签页 ---")
        plugin_service._integrate_window_to_tab(window, plugin.name)
        
        tab_count = main_window.tools_tab_widget.count()
        if tab_count > 0:
            tab_text = main_window.tools_tab_widget.tabText(0)
            container = main_window.tools_tab_widget.widget(0)
            
            print(f"✓ 标签页创建成功: '{tab_text}'")
            print(f"容器有插件信息: {hasattr(container, 'plugin_name')}")
            print(f"容器有窗口引用: {hasattr(container, 'plugin_window')}")
        else:
            print("✗ 标签页创建失败")
            return False
        
        # 测试场景3: 完整的标签页关闭流程
        print("\n--- 测试场景3: 完整的标签页关闭流程 ---")
        
        print("关闭前状态:")
        print(f"  插件服务中的窗口: {plugin.name in plugin_service.plugin_windows}")
        print(f"  主窗口中的引用: {main_window.clkin_control_window is not None}")
        print(f"  菜单状态: {main_window.clkin_control_action.isChecked()}")
        print(f"  标签页数量: {main_window.tools_tab_widget.count()}")
        
        # 调用完整的关闭流程
        tab_manager.close_tool_tab(0)
        
        print("\n关闭后状态:")
        print(f"  插件服务中的窗口: {plugin.name in plugin_service.plugin_windows}")
        print(f"  主窗口中的引用: {main_window.clkin_control_window is not None}")
        print(f"  菜单状态: {main_window.clkin_control_action.isChecked()}")
        print(f"  标签页数量: {main_window.tools_tab_widget.count()}")
        
        # 验证清理结果
        cleanup_success = True
        
        if plugin.name in plugin_service.plugin_windows:
            print("✗ 插件服务中的窗口引用未清理")
            cleanup_success = False
        else:
            print("✓ 插件服务中的窗口引用已清理")
        
        if main_window.clkin_control_window is not None:
            print("✗ 主窗口中的引用未清理")
            cleanup_success = False
        else:
            print("✓ 主窗口中的引用已清理")
        
        if main_window.clkin_control_action.isChecked():
            print("✗ 菜单状态未重置")
            cleanup_success = False
        else:
            print("✓ 菜单状态已重置")
        
        if main_window.tools_tab_widget.count() != 0:
            print("✗ 标签页未正确移除")
            cleanup_success = False
        else:
            print("✓ 标签页已正确移除")
        
        # 测试场景4: 验证重新打开功能
        print("\n--- 测试场景4: 验证重新打开功能 ---")
        
        # 模拟用户再次点击菜单
        main_window.clkin_control_action.setChecked(True)
        
        # 尝试显示插件窗口
        plugin_service._show_plugin_window(plugin)
        
        # 检查是否创建了新窗口
        if plugin.name in plugin_service.plugin_windows:
            new_window = plugin_service.plugin_windows[plugin.name]
            print("✓ 插件窗口可以重新创建")
            
            if new_window != window:
                print("✓ 创建了新的窗口实例")
            else:
                print("? 重用了原窗口实例")
                
            # 验证新窗口可以正常工作
            print(f"新窗口标题: {new_window.windowTitle()}")
            
        else:
            print("✗ 插件窗口重新创建失败")
            cleanup_success = False
        
        if cleanup_success:
            print("\n🎉 完整资源清理测试通过")
        else:
            print("\n❌ 完整资源清理测试失败")
        
        # 总结修复内容
        print("\n--- 修复内容总结 ---")
        print("1. ✅ 增强了TabWindowManager的资源清理功能")
        print("2. ✅ 添加了标签页容器和插件窗口的资源清理")
        print("3. ✅ 增强了PluginIntegrationService的窗口关闭处理")
        print("4. ✅ 添加了主窗口引用的清理")
        print("5. ✅ 确保菜单状态正确重置")
        print("6. ✅ 支持窗口的完整重新创建")
        
        return cleanup_success
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        if 'app' in locals():
            app.quit()

def main():
    """主函数"""
    success = test_complete_resource_cleanup()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
