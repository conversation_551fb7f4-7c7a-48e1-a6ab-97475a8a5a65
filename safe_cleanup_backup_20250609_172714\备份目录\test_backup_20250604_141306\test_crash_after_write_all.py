#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试写完所有寄存器后是否会闪退的问题
"""

import sys
import os
sys.path.append('.')

# 设置环境变量以避免GUI问题
os.environ['QT_QPA_PLATFORM'] = 'offscreen'

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from ui.windows.RegisterMainWindow import RegisterMainWindow
from core.services.spi.spi_service_impl import SPIServiceImpl
from core.repositories.register_repository import RegisterRepository
from utils.Log import logger

def test_crash_after_write_all():
    """测试写完所有寄存器后是否会闪退"""
    
    print("=== 测试写完所有寄存器后是否会闪退 ===")
    
    # 创建应用程序
    app = QApplication([])
    
    try:
        # 创建服务
        spi_service = SPIServiceImpl()
        spi_service.initialize()
        register_repo = RegisterRepository(spi_service)
        
        # 创建主窗口
        main_window = RegisterMainWindow(register_repo)
        print("✓ 主窗口创建成功")
        
        # 检查批量操作管理器
        if hasattr(main_window, 'batch_operation_manager'):
            batch_mgr = main_window.batch_operation_manager
            print(f"✓ 批量操作管理器: {type(batch_mgr).__name__}")
            
            # 模拟批量写入所有寄存器的过程
            print("\n--- 模拟批量写入所有寄存器 ---")
            
            # 获取所有寄存器地址
            all_registers = register_repo.get_all_registers()
            total_registers = len(all_registers)
            print(f"总共有 {total_registers} 个寄存器")
            
            # 模拟批量写入状态
            batch_mgr.is_batch_writing = True
            batch_mgr.write_all_completed = 0
            batch_mgr.write_all_total = total_registers
            
            # 创建模拟进度对话框
            from PyQt5.QtWidgets import QProgressDialog
            batch_mgr.progress_dialog = QProgressDialog("写入所有寄存器", "取消", 0, total_registers, main_window)
            batch_mgr.progress_dialog.show()
            print(f"✓ 模拟进度对话框创建成功 (0/{total_registers})")
            
            # 模拟逐个完成写入
            print("\n--- 模拟写入进度 ---")
            for i, (addr, reg_info) in enumerate(all_registers.items(), 1):
                try:
                    # 模拟写入完成回调
                    batch_mgr.write_all_completed = i
                    
                    # 更新进度条
                    if batch_mgr.progress_dialog:
                        batch_mgr.progress_dialog.setValue(i)
                    
                    # 每10个寄存器打印一次进度
                    if i % 10 == 0 or i == total_registers:
                        print(f"  进度: {i}/{total_registers} ({i/total_registers*100:.1f}%)")
                    
                    # 如果是最后一个寄存器，触发完成
                    if i == total_registers:
                        print(f"\n--- 所有寄存器写入完成，触发清理 ---")
                        
                        # 调用完成方法
                        batch_mgr._finish_write_all()
                        print("✓ _finish_write_all() 调用成功")
                        
                        # 检查状态
                        if not batch_mgr.is_batch_writing:
                            print("✓ 批量写入标志已清除")
                        else:
                            print("✗ 批量写入标志未清除")
                            
                        if batch_mgr.progress_dialog is None:
                            print("✓ 进度对话框引用已清除")
                        else:
                            print("✗ 进度对话框引用未清除")
                        
                        break
                        
                except Exception as e:
                    print(f"✗ 处理寄存器 {addr} 时出错: {e}")
                    import traceback
                    traceback.print_exc()
                    break
            
            # 测试应用程序是否还能正常响应
            print("\n--- 测试应用程序响应性 ---")
            try:
                # 测试主窗口方法
                if hasattr(main_window, 'show_status_message'):
                    main_window.show_status_message("测试消息", 1000)
                    print("✓ 主窗口状态消息正常")
                
                # 测试IO处理器
                if hasattr(main_window, 'io_handler'):
                    if hasattr(main_window.io_handler, 'toggle_buttons'):
                        main_window.io_handler.toggle_buttons(True)
                        print("✓ IO处理器按钮切换正常")
                
                # 测试寄存器选择
                if hasattr(main_window, 'on_register_selected'):
                    main_window.on_register_selected('0x00')
                    print("✓ 寄存器选择功能正常")
                
                # 测试窗口显示
                main_window.show()
                print("✓ 主窗口显示正常")
                
                # 等待一段时间，看是否会闪退
                print("\n--- 等待检查是否闪退 ---")
                
                # 创建定时器来检查应用程序状态
                check_timer = QTimer()
                check_count = [0]  # 使用列表来在闭包中修改值
                
                def check_app_status():
                    check_count[0] += 1
                    print(f"  检查 {check_count[0]}/10: 应用程序仍在运行")
                    
                    if check_count[0] >= 10:
                        check_timer.stop()
                        print("✓ 应用程序在批量写入完成后10秒内没有闪退")
                        app.quit()
                
                check_timer.timeout.connect(check_app_status)
                check_timer.start(1000)  # 每秒检查一次
                
                # 运行事件循环
                app.exec_()
                
            except Exception as e:
                print(f"✗ 测试应用程序响应性时出错: {e}")
                import traceback
                traceback.print_exc()
                
        else:
            print("✗ 主窗口没有 batch_operation_manager")
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_crash_after_write_all()
