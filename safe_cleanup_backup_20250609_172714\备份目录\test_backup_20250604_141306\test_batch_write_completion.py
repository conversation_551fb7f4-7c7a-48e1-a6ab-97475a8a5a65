#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试批量写入完成时的清理过程，检查是否有导致闪退的问题
"""

import sys
import os
sys.path.append('.')

# 设置环境变量以避免GUI问题
os.environ['QT_QPA_PLATFORM'] = 'offscreen'

from PyQt5.QtWidgets import QApplication
from ui.windows.RegisterMainWindow import RegisterMainWindow
from core.services.spi.spi_service_impl import SPIServiceImpl
from core.repositories.register_repository import RegisterRepository
from utils.Log import logger

def test_batch_write_completion():
    """测试批量写入完成时的清理过程"""
    
    print("=== 测试批量写入完成时的清理过程 ===")
    
    # 创建应用程序
    app = QApplication([])
    
    try:
        # 创建服务
        spi_service = SPIServiceImpl()
        spi_service.initialize()
        register_repo = RegisterRepository(spi_service)
        
        # 创建主窗口
        main_window = RegisterMainWindow(register_repo)
        print("✓ 主窗口创建成功")
        
        # 检查批量操作管理器
        if hasattr(main_window, 'batch_operation_manager'):
            batch_mgr = main_window.batch_operation_manager
            print(f"✓ 批量操作管理器: {type(batch_mgr).__name__}")
            
            # 模拟批量写入状态
            print("\n--- 模拟批量写入状态 ---")
            batch_mgr.is_batch_writing = True
            batch_mgr.write_all_completed = 0
            batch_mgr.write_all_total = 10
            
            # 创建模拟进度对话框
            from PyQt5.QtWidgets import QProgressDialog
            batch_mgr.progress_dialog = QProgressDialog("测试进度", "取消", 0, 10, main_window)
            batch_mgr.progress_dialog.show()
            print("✓ 模拟进度对话框创建成功")
            
            # 测试1: 正常完成流程
            print("\n=== 测试1: 正常完成流程 ===")
            try:
                # 模拟所有写入完成
                batch_mgr.write_all_completed = 10
                
                # 调用完成方法
                print("调用 _finish_write_all()")
                batch_mgr._finish_write_all()
                print("✓ _finish_write_all() 调用成功")
                
                # 检查状态
                if not batch_mgr.is_batch_writing:
                    print("✓ 批量写入标志已清除")
                else:
                    print("✗ 批量写入标志未清除")
                    
                if batch_mgr.progress_dialog is None:
                    print("✓ 进度对话框引用已清除")
                else:
                    print("✗ 进度对话框引用未清除")
                    
            except Exception as e:
                print(f"✗ 正常完成流程测试失败: {e}")
                import traceback
                traceback.print_exc()
            
            # 测试2: 异常情况处理
            print("\n=== 测试2: 异常情况处理 ===")
            try:
                # 重新设置批量写入状态
                batch_mgr.is_batch_writing = True
                batch_mgr.write_all_completed = 5
                batch_mgr.write_all_total = 10
                
                # 创建新的进度对话框
                batch_mgr.progress_dialog = QProgressDialog("测试进度2", "取消", 0, 10, main_window)
                
                # 模拟异常情况下的完成
                print("模拟异常情况下调用 _finish_write_all()")
                batch_mgr._finish_write_all()
                print("✓ 异常情况下 _finish_write_all() 调用成功")
                
            except Exception as e:
                print(f"✗ 异常情况处理测试失败: {e}")
                import traceback
                traceback.print_exc()
            
            # 测试3: 重复调用保护
            print("\n=== 测试3: 重复调用保护 ===")
            try:
                # 确保批量写入标志已清除
                batch_mgr.is_batch_writing = False
                
                # 尝试再次调用完成方法
                print("尝试在非批量写入状态下调用 _finish_write_all()")
                batch_mgr._finish_write_all()
                print("✓ 重复调用保护正常工作")
                
            except Exception as e:
                print(f"✗ 重复调用保护测试失败: {e}")
                import traceback
                traceback.print_exc()
            
            # 测试4: 检查主窗口方法
            print("\n=== 测试4: 检查主窗口方法 ===")
            try:
                # 检查主窗口的相关方法
                methods = [
                    '_refresh_current_register_after_batch',
                    'show_status_message',
                    'io_handler'
                ]
                
                for method in methods:
                    if hasattr(main_window, method):
                        obj = getattr(main_window, method)
                        print(f"✓ {method}: {type(obj).__name__ if callable(obj) else 'attribute'}")
                    else:
                        print(f"✗ {method}: 不存在")
                
                # 测试调用这些方法
                print("\n--- 测试调用主窗口方法 ---")
                
                # 测试状态消息
                main_window.show_status_message("测试消息", 1000)
                print("✓ show_status_message 调用成功")
                
                # 测试刷新方法
                main_window._refresh_current_register_after_batch()
                print("✓ _refresh_current_register_after_batch 调用成功")
                
                # 测试IO处理器
                if hasattr(main_window.io_handler, 'toggle_buttons'):
                    main_window.io_handler.toggle_buttons(True)
                    print("✓ io_handler.toggle_buttons 调用成功")
                else:
                    print("✗ io_handler 没有 toggle_buttons 方法")
                    
            except Exception as e:
                print(f"✗ 主窗口方法测试失败: {e}")
                import traceback
                traceback.print_exc()
                
        else:
            print("✗ 主窗口没有 batch_operation_manager")
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_batch_write_completion()
