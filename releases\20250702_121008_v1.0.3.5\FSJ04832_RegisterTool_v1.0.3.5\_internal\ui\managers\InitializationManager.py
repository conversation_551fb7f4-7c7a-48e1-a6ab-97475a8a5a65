"""
初始化管理器
负责管理主窗口的初始化过程，包括配置加载、服务创建、信号连接等
"""

import os
import sys
import json
from PyQt5.QtWidgets import QMessageBox
from PyQt5.QtCore import QTimer, QSettings
from PyQt5.QtGui import QIcon
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class InitializationManager:
    """初始化管理器，负责主窗口的初始化过程"""
    
    def __init__(self, main_window):
        """初始化管理器
        
        Args:
            main_window: 主窗口实例
        """
        self.main_window = main_window
    
    def load_register_config(self):
        """加载寄存器配置文件"""
        try:
            logger.info("正在初始化寄存器配置工具...")

            # 临时使用直接路径，稍后会通过资源管理器处理
            config_path = 'lib/register.json'
            if not os.path.exists(config_path):
                config_path = os.path.join(os.path.dirname(__file__), '../../lib/register.json')

            with open(config_path, 'r', encoding='utf-8') as file:
                registers = json.load(file)
                logger.info(f"成功加载寄存器配置文件，包含 {len(registers)} 个寄存器")
                return registers
        except FileNotFoundError:
            self._safe_show_message("错误", "未找到 register.json 文件，请确保该文件存在于lib目录下。", QMessageBox.Critical)
            sys.exit(1)
        except json.JSONDecodeError:
            logger.error("register.json 文件格式错误")
            self._safe_show_message("错误", "register.json 文件格式错误。", QMessageBox.Critical)
            sys.exit(1)
    
    def load_user_settings(self):
        """加载用户设置"""
        settings = QSettings("FSJ", "FSJRead")
        simulation_mode = settings.value("simulation_mode", True, type=bool)
        auto_write_mode = settings.value("auto_write_mode", False, type=bool)
        return settings, simulation_mode, auto_write_mode
    
    def initialize_member_variables(self):
        """初始化成员变量"""
        self.main_window._thread_started = False
        self.main_window._port_refreshed = False  # 添加标志，跟踪端口是否已刷新
        self.main_window._status_bar_updated = False  # 添加标志，跟踪状态栏是否已更新
        
        self.main_window.tab_to_action_map = {}  # 新增：存储标签页到动作的映射
        # 初始化定时器和变量
        self.main_window.operation_timer = QTimer()
        self.main_window.operation_timer.timeout.connect(self.main_window._handle_operation_timeout)
        self.main_window.selected_register_addr = None
        self.main_window.current_register_value = None
        
        # 批量更新标志（配置加载时使用）
        self.main_window.is_batch_updating = False
    
    def create_core_services(self):
        """创建核心服务"""
        # 创建配置管理服务
        from core.services.config.ConfigurationService import ConfigurationService
        self.main_window.config_service = ConfigurationService(self.main_window)

        # 创建并初始化SPI服务（必须在其他服务之前初始化）
        self._create_and_initialize_spi_service()

    def _create_and_initialize_spi_service(self):
        """创建并初始化SPI服务"""
        try:
            # 创建SPI服务实例 - 使用包装器而不是直接实现
            from core.services.spi.spi_service import SPIService
            self.main_window.spi_service = SPIService(self.main_window.register_manager)

            # 初始化SPI服务
            if self.main_window.spi_service.initialize():
                mode_text = '模拟模式' if self.main_window.spi_service.simulation_mode else '硬件模式'
                logger.info(f"SPI服务初始化成功，当前模式：{mode_text}")
            else:
                logger.warning("SPI服务初始化失败")
        except Exception as e:
            logger.error(f"SPI服务创建或初始化异常: {str(e)}")
            # 可以在这里添加错误处理逻辑，比如显示错误对话框

        # 创建窗口管理服务
        from core.services.ui.WindowManagementService import WindowManagementService
        self.main_window.window_service = WindowManagementService(self.main_window)

        # 创建寄存器操作服务（依赖于spi_service）
        from core.services.register.RegisterOperationService import RegisterOperationService
        self.main_window.register_service = RegisterOperationService(self.main_window)
    
    def create_handlers(self):
        """创建处理器实例"""
        # 优先使用现代化处理器，失败时回退到传统处理器
        try:
            # 创建现代化处理器
            self._create_modern_handlers()
            logger.info("使用现代化处理器创建核心处理器")
        except Exception as e:
            logger.warning(f"现代化处理器创建失败，回退到传统处理器: {str(e)}")
            self._create_legacy_handlers()

    def _create_modern_handlers(self):
        """创建现代化处理器"""
        # 现在总是尝试创建现代化处理器，因为tool_window_factory已移除
        # 现代化处理器不再依赖tool_window_factory，而是通过插件系统管理
        try:
            # 创建现代化表格处理器
            from ui.handlers.ModernRegisterTableHandler import ModernRegisterTableHandler
            self.main_window.table_handler = ModernRegisterTableHandler(
                parent=self.main_window,
                register_manager=self.main_window.register_manager
            )

            # 创建现代化IO处理器
            from ui.handlers.ModernRegisterIOHandler import ModernRegisterIOHandler
            self.main_window.io_handler = ModernRegisterIOHandler(
                parent=self.main_window,
                register_manager=self.main_window.register_manager,
                spi_service=self.main_window.spi_service
            )

            # 创建现代化树形处理器
            from ui.handlers.ModernRegisterTreeHandler import ModernRegisterTreeHandler
            self.main_window.tree_handler = ModernRegisterTreeHandler(
                parent=self.main_window,
                register_manager=self.main_window.register_manager
            )

            logger.info("现代化处理器创建成功")

        except ImportError as e:
            logger.error(f"现代化处理器模块导入失败: {str(e)}")
            raise Exception(f"现代化处理器不可用: {str(e)}")
        except Exception as e:
            logger.error(f"现代化处理器创建失败: {str(e)}")
            raise

    def _create_legacy_handlers(self):
        """创建传统处理器（回退方案）"""
        logger.warning("回退到传统处理器，建议检查现代化处理器的问题")
        from ui.handlers.RegisterTreeHandler import RegisterTreeHandler
        from ui.handlers.RegisterTableHandler import RegisterTableHandler
        from ui.handlers.RegisterIOHandler import RegisterIOHandler

        self.main_window.tree_handler = RegisterTreeHandler(self.main_window, self.main_window.register_manager)
        self.main_window.table_handler = RegisterTableHandler(self.main_window, self.main_window.register_manager)
        self.main_window.io_handler = RegisterIOHandler(self.main_window, self.main_window.register_manager, self.main_window.spi_service)
    
    def create_managers(self):
        """创建各种管理器"""
        try:
            # 尝试使用依赖注入获取管理器
            from core.services.DIContainer import container

            self.main_window.batch_manager = container.get('batch_manager')
            # 移除 batch_operation_controller，统一使用 batch_manager
            self.main_window.spi_coordinator = container.get('spi_coordinator')
            self.main_window.ui_event_handler = container.get('ui_event_handler')
            self.main_window.tool_window_manager = container.get('tool_window_manager')
            self.main_window.status_config_manager = container.get('status_config_manager')
            self.main_window.resource_utility_manager = container.get('resource_utility_manager')
            self.main_window.tab_window_manager = container.get('tab_window_manager')
            self.main_window.global_event_manager = container.get('global_event_manager')
            self.main_window.spi_signal_manager = container.get('spi_signal_manager')

            logger.info("使用依赖注入创建管理器完成")
        except Exception as e:
            logger.warning(f"依赖注入创建管理器失败，回退到传统方式: {str(e)}")
            self._create_managers_traditional_way()

    def _create_managers_traditional_way(self):
        """传统方式创建管理器（回退方案）"""
        # 创建批量操作管理器
        from ui.managers.BatchOperationManager import BatchOperationManager
        self.main_window.batch_manager = BatchOperationManager(self.main_window)

        # 移除批量操作控制器，统一使用 BatchOperationManager

        # 创建SPI操作协调器
        from ui.coordinators.SPIOperationCoordinator import SPIOperationCoordinator
        self.main_window.spi_coordinator = SPIOperationCoordinator(self.main_window)

        # 创建UI事件处理器
        from ui.handlers.UIEventHandler import UIEventHandler
        self.main_window.ui_event_handler = UIEventHandler(self.main_window)

        # 创建工具窗口管理器
        from ui.managers.ToolWindowManager import ToolWindowManager
        self.main_window.tool_window_manager = ToolWindowManager(self.main_window)

        # 创建状态和配置管理器
        from ui.managers.StatusAndConfigManager import StatusAndConfigManager
        self.main_window.status_config_manager = StatusAndConfigManager(self.main_window)

        # 创建资源和工具管理器
        from ui.managers.ResourceAndUtilityManager import ResourceAndUtilityManager
        self.main_window.resource_utility_manager = ResourceAndUtilityManager(self.main_window)

        # 创建标签页窗口管理器
        from ui.managers.TabWindowManager import TabWindowManager
        self.main_window.tab_window_manager = TabWindowManager(self.main_window)

        # 创建SPI信号管理器
        from ui.managers.SPISignalManager import SPISignalManager
        self.main_window.spi_signal_manager = SPISignalManager(self.main_window)

        # 注意：显示管理器、事件协调器、工具窗口工厂、寄存器更新处理器、
        # 生命周期管理器和UI工具管理器已在主窗口构造函数中创建

        # 创建全局事件管理器
        from ui.managers.GlobalEventManager import GlobalEventManager
        self.main_window.global_event_manager = GlobalEventManager(self.main_window)
    
    def setup_exception_handler(self):
        """设置异常处理器"""
        sys.excepthook = self.main_window.resource_utility_manager.global_exception_handler
    
    def setup_window_icon(self):
        """设置窗口图标"""
        self.main_window.setWindowIcon(QIcon(self.main_window.resource_path('images/logo.ico')))
    
    def connect_signals(self):
        """连接信号"""
        # 连接到全局寄存器更新总线
        from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
        self.main_window.register_update_bus = RegisterUpdateBus.instance()
        self.main_window.register_update_bus.register_updated.connect(self.main_window.global_event_manager.on_global_register_updated)

        # 连接 IO Handler 的信号
        self.main_window.io_handler.write_requested.connect(self.main_window.global_event_manager.handle_io_write_request)
        self.main_window.io_handler.search_requested.connect(self.main_window.tree_handler.filter_registers_by_bit_field_name)
        self.main_window.io_handler.bit_field_selected.connect(self.main_window.global_event_manager.handle_bit_field_selected)
        
        # 连接处理器信号
        self.main_window._connect_signals()
    
    def finalize_initialization(self):
        """完成初始化"""
        # 强制刷新端口列表，确保UI显示正确的端口信息
        # 即使SPI服务在初始化时已经扫描了端口，UI可能还没有准备好接收信号
        if hasattr(self.main_window.io_handler, 'refresh_ports'):
            # 延迟一点时间，确保UI完全初始化后再刷新端口
            from PyQt5.QtCore import QTimer
            QTimer.singleShot(200, self._delayed_port_refresh)
        else:
            logger.info("IO处理器不支持端口刷新")

        # 延迟更新状态栏信息，确保SPI服务完全初始化后状态正确
        # 只进行一次延迟更新，避免重复调用
        from PyQt5.QtCore import QTimer
        QTimer.singleShot(500, self._delayed_status_bar_update)

        # 选择默认寄存器
        self.main_window.tree_handler.select_default_register()

    def _delayed_port_refresh(self):
        """延迟的端口刷新，确保UI显示正确的端口信息"""
        logger.info("InitializationManager: 执行延迟端口刷新，确保UI同步端口状态")
        try:
            self.main_window.io_handler.refresh_ports()
            logger.info("InitializationManager: 端口刷新完成")
        except Exception as e:
            logger.error(f"InitializationManager: 端口刷新失败: {str(e)}")

    def _delayed_status_bar_update(self):
        """延迟的状态栏更新，确保SPI服务完全初始化后状态正确"""
        try:
            # 检查是否需要更新状态栏（避免重复更新）
            if hasattr(self.main_window, '_status_bar_updated') and self.main_window._status_bar_updated:
                logger.info("InitializationManager: 状态栏已更新，跳过重复更新")
                return

            logger.info("InitializationManager: 执行延迟状态栏更新")
            self.main_window.status_config_manager.update_status_bar()
            self.main_window._status_bar_updated = True
        except Exception as e:
            logger.error(f"延迟状态栏更新失败: {str(e)}")
    
    def initialize_window(self):
        """执行完整的窗口初始化流程"""
        # 1. 加载用户设置
        self.main_window.settings, self.main_window.simulation_mode, self.main_window.auto_write_mode = self.load_user_settings()

        # 2. 加载寄存器配置
        self.main_window.registers = self.load_register_config()

        # 3. 初始化成员变量
        self.initialize_member_variables()

        # 4. 创建寄存器管理器
        from core.services.register.RegisterManager import RegisterManager
        self.main_window.register_manager = RegisterManager(self.main_window.registers)

        # 5. 创建核心服务
        self.create_core_services()

        # 6. 创建处理器
        self.create_handlers()
        
        # 7. 创建管理器
        self.create_managers()
        
        # 8. 设置异常处理器
        self.setup_exception_handler()
        
        # 9. 设置窗口图标
        self.setup_window_icon()
        
        # 10. 创建UI
        self.main_window._create_ui()
        
        # 11. 连接信号
        self.connect_signals()
        
        # 12. 完成初始化
        self.finalize_initialization()
        
        logger.info("主窗口初始化完成")
    
    def cleanup_resources(self):
        """清理初始化管理器资源"""
        try:
            logger.debug("初始化管理器资源已清理")
        except Exception as e:
            logger.error(f"清理初始化管理器资源时出错: {str(e)}")

    def _safe_show_message(self, title, message, icon_type=None):
        """安全地显示消息框，避免与主窗口事件过滤器冲突

        Args:
            title: 消息框标题
            message: 消息内容
            icon_type: 图标类型 (QMessageBox.Information, Warning, Critical等)
        """
        try:
            # 临时禁用主窗口的事件过滤器
            main_window_filter_disabled = False
            if hasattr(self.main_window, 'eventFilter'):
                try:
                    self.main_window.removeEventFilter(self.main_window)
                    main_window_filter_disabled = True
                    logger.debug("临时禁用主窗口事件过滤器")
                except Exception as e:
                    logger.debug(f"无法禁用主窗口事件过滤器: {str(e)}")

            # 创建独立的消息框，不使用主窗口作为父窗口
            from PyQt5.QtWidgets import QMessageBox
            msg = QMessageBox()
            msg.setWindowTitle(title)
            msg.setText(message)

            # 设置图标
            if icon_type:
                msg.setIcon(icon_type)
            else:
                msg.setIcon(QMessageBox.Information)

            # 设置按钮
            msg.setStandardButtons(QMessageBox.Ok)
            msg.setDefaultButton(QMessageBox.Ok)

            # 设置窗口属性，确保消息框正常显示
            from PyQt5.QtCore import Qt
            msg.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
            msg.setModal(True)

            # 显示消息框
            result = msg.exec_()

            # 恢复主窗口的事件过滤器
            if main_window_filter_disabled:
                try:
                    self.main_window.installEventFilter(self.main_window)
                    logger.debug("恢复主窗口事件过滤器")
                except Exception as e:
                    logger.debug(f"无法恢复主窗口事件过滤器: {str(e)}")

            logger.info(f"安全消息框显示完成: {title}")
            return result

        except Exception as e:
            logger.error(f"显示安全消息框时出错: {str(e)}")
            # 如果安全方式失败，尝试使用状态栏显示消息
            try:
                if hasattr(self.main_window, 'status_bar'):
                    self.main_window.status_bar.showMessage(f"{title}: {message}", 5000)
                    logger.info(f"通过状态栏显示消息: {title}")
            except Exception as e2:
                logger.error(f"状态栏显示消息也失败: {str(e2)}")
            return QMessageBox.Ok
