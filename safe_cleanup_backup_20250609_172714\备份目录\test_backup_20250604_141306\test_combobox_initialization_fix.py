#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试ComboBox初始化修复
验证控件初始化警告是否已解决
"""

import sys
import os
import json
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from utils.Log import logger
from core.services.register.RegisterManager import RegisterManager
from ui.handlers.ModernClkinControlHandler import ModernClkinControlHandler

def test_combobox_initialization():
    """测试ComboBox初始化是否正常"""
    try:
        print("=" * 60)
        print("测试ComboBox初始化修复")
        print("=" * 60)
        
        # 1. 加载寄存器配置
        print("\n1. 加载寄存器配置...")
        config_path = os.path.join('lib', 'register.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        print(f"   ✓ 已加载 {len(registers_config)} 个寄存器配置")
        
        # 2. 创建RegisterManager
        print("\n2. 创建RegisterManager...")
        register_manager = RegisterManager(registers_config)
        print("   ✓ RegisterManager创建成功")
        
        # 3. 创建QApplication
        print("\n3. 创建QApplication...")
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        print("   ✓ QApplication创建成功")

        # 4. 创建现代化时钟输入控制处理器
        print("\n4. 创建现代化时钟输入控制处理器...")

        # 设置日志级别为INFO以捕获警告
        logger.setLevel(logging.INFO)

        # 创建处理器
        modern_handler = ModernClkinControlHandler(None, register_manager)
        print("   ✓ 处理器创建成功")
        
        # 5. 检查ComboBox控件是否有选项
        print("\n5. 检查ComboBox控件选项...")
        
        # 检查的控件列表（这些是在警告中提到的控件）
        controls_to_check = [
            "OSCoutMux", "OSCoutClockFormat", "syncSource", 
            "CLKinSelManual", "CLKin1Demux", "CLKin0Demux",
            "CLKinSel0Mux", "CLKinSel0Type", "CLKinSel1Mux", 
            "CLKinSel1Type", "LOSTimeout", "CLKin1Type"
        ]
        
        success_count = 0
        total_count = len(controls_to_check)
        
        for control_name in controls_to_check:
            if hasattr(modern_handler.ui, control_name):
                control = getattr(modern_handler.ui, control_name)
                if hasattr(control, 'count'):
                    item_count = control.count()
                    current_index = control.currentIndex()
                    
                    if item_count > 0:
                        print(f"   ✓ {control_name}: {item_count}项, 当前索引={current_index}")
                        success_count += 1
                    else:
                        print(f"   ❌ {control_name}: 没有选项!")
                else:
                    print(f"   ⚠ {control_name}: 不是ComboBox控件")
            else:
                print(f"   ❌ {control_name}: 控件不存在")
        
        print(f"\n   总结: {success_count}/{total_count} 个控件初始化成功")
        
        # 6. 检查是否有待设置的ComboBox值
        print("\n6. 检查待设置的ComboBox值...")
        if hasattr(modern_handler, '_pending_combobox_values'):
            pending_count = len(modern_handler._pending_combobox_values)
            if pending_count > 0:
                print(f"   ⚠ 仍有 {pending_count} 个待设置的ComboBox值:")
                for widget_name, value in modern_handler._pending_combobox_values.items():
                    print(f"     - {widget_name}: {value}")
            else:
                print("   ✓ 没有待设置的ComboBox值")
        else:
            print("   ✓ 没有待设置的ComboBox值（属性不存在）")
        
        # 7. 测试控件值设置
        print("\n7. 测试控件值设置...")
        test_controls = ["CLKinSelManual", "CLKin0Demux", "CLKin1Demux"]
        
        for control_name in test_controls:
            if hasattr(modern_handler.ui, control_name):
                control = getattr(modern_handler.ui, control_name)
                if hasattr(control, 'count') and control.count() > 0:
                    # 尝试设置不同的值
                    original_index = control.currentIndex()
                    test_index = min(1, control.count() - 1)
                    
                    control.setCurrentIndex(test_index)
                    new_index = control.currentIndex()
                    
                    if new_index == test_index:
                        print(f"   ✓ {control_name}: 值设置成功 ({original_index} -> {new_index})")
                    else:
                        print(f"   ❌ {control_name}: 值设置失败 (期望{test_index}, 实际{new_index})")
                    
                    # 恢复原值
                    control.setCurrentIndex(original_index)
        
        print("\n" + "=" * 60)
        print("测试完成")
        print("=" * 60)
        
        return success_count == total_count
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_combobox_initialization()
    sys.exit(0 if success else 1)
