@echo off
chcp 65001 >nul
title FSJ04832 优化打包工具

echo.
echo ================================================================
echo                    FSJ04832 优化打包工具 v2.0
echo ================================================================
echo.
echo 🎯 此工具提供四种优化的打包方式：
echo    ✅ 标准打包 - 平衡功能和大小
echo    ✅ 安全打包 - 最大化代码保护
echo    ✅ 紧凑打包 - 最小化文件大小
echo    ✅ 便携打包 - 目录模式，便于调试
echo.

:MENU
echo 请选择打包方式：
echo.
echo [1] 标准打包 (推荐)
echo [2] 安全打包 (客户发布)
echo [3] 紧凑打包 (最小体积)
echo [4] 便携打包 (开发调试)
echo [5] 查看版本信息
echo [0] 退出
echo.

set /p choice=请输入选择 (0-5): 

if "%choice%"=="1" (
    echo.
    echo 🔨 开始标准打包...
    cd /d "%~dp0.."
    python package.py build patch
    goto END
)

if "%choice%"=="2" (
    echo.
    echo 🔒 开始安全打包...
    cd /d "%~dp0.."
    python package.py secure patch
    goto END
)

if "%choice%"=="3" (
    echo.
    echo 📦 开始紧凑打包...
    cd /d "%~dp0.."
    python package.py optimized patch
    goto END
)

if "%choice%"=="4" (
    echo.
    echo 🛠️ 开始便携打包...
    cd /d "%~dp0.."
    python package.py portable patch
    goto END
)

if "%choice%"=="5" (
    echo.
    echo 📋 版本信息...
    cd /d "%~dp0.."
    python -c "from tools.enhanced_version_manager import EnhancedVersionManager; vm=EnhancedVersionManager(); print(f'版本: {vm.get_version_string()}'); print(f'构建: {vm.get_build_date()}'); print(f'类型: {vm.get_build_type()}')"
    goto END
)

if "%choice%"=="0" (
    echo.
    echo 👋 再见！
    goto EXIT
)

echo.
echo ❌ 无效选择，请重新输入
echo.
goto MENU

:END
echo.
echo 按任意键返回菜单...
pause >nul
cls
goto MENU

:EXIT
pause
