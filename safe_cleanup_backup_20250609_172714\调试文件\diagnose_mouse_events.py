#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
诊断鼠标事件问题的工具
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QLabel, QPushButton
from PyQt5.QtCore import Qt, QEvent, QObject
from PyQt5.QtGui import QCursor
from utils.Log import get_module_logger

logger = get_module_logger(__name__)

class MouseEventDiagnosticFilter(QObject):
    """鼠标事件诊断过滤器"""
    
    def __init__(self, widget_name):
        super().__init__()
        self.widget_name = widget_name
        
    def eventFilter(self, obj, event):
        """事件过滤器"""
        if event.type() == QEvent.MouseButtonPress:
            logger.info(f"🔍 [诊断] {self.widget_name} - 鼠标按下事件被捕获")
            logger.info(f"🔍 [诊断] 按钮: {event.button()}, 位置: {event.globalPos()}")
        elif event.type() == QEvent.MouseMove:
            logger.info(f"🔍 [诊断] {self.widget_name} - 鼠标移动事件")
            logger.info(f"🔍 [诊断] 按钮状态: {event.buttons()}, 位置: {event.globalPos()}")
        elif event.type() == QEvent.MouseButtonRelease:
            logger.info(f"🔍 [诊断] {self.widget_name} - 鼠标释放事件")
            logger.info(f"🔍 [诊断] 按钮: {event.button()}, 位置: {event.globalPos()}")
        
        return False  # 继续传递事件

class DiagnosticWindow(QMainWindow):
    """诊断窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.setup_event_monitoring()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("鼠标事件诊断工具")
        self.setGeometry(400, 400, 500, 400)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("鼠标事件诊断工具")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; text-align: center;")
        layout.addWidget(title_label)
        
        # 状态显示
        self.status_label = QLabel("状态: 准备就绪")
        self.status_label.setStyleSheet("padding: 10px; background-color: #f0f0f0; border: 1px solid #ccc;")
        layout.addWidget(self.status_label)
        
        # 说明
        instruction_label = QLabel("""
诊断说明：
1. 此窗口会监控所有鼠标事件
2. 请在此窗口内进行鼠标操作
3. 观察控制台日志输出
4. 检查是否能正确捕获鼠标按下事件

如果此窗口能正确捕获鼠标事件，
说明问题在于插件窗口的特殊配置。
        """)
        instruction_label.setWordWrap(True)
        layout.addWidget(instruction_label)
        
        # 测试区域
        test_area = QLabel("测试区域 - 在此处进行鼠标操作")
        test_area.setStyleSheet("""
            background-color: #e0e0e0; 
            border: 2px dashed #999; 
            padding: 50px; 
            text-align: center;
            font-size: 14px;
        """)
        test_area.setMinimumHeight(100)
        layout.addWidget(test_area)
        
        # 按钮
        button_layout = QVBoxLayout()
        
        find_plugin_btn = QPushButton("查找插件窗口并诊断")
        find_plugin_btn.clicked.connect(self.diagnose_plugin_windows)
        button_layout.addWidget(find_plugin_btn)
        
        test_focus_btn = QPushButton("测试窗口焦点")
        test_focus_btn.clicked.connect(self.test_window_focus)
        button_layout.addWidget(test_focus_btn)
        
        layout.addLayout(button_layout)
        
        # 启用鼠标跟踪
        self.setMouseTracking(True)
        central_widget.setMouseTracking(True)
        test_area.setMouseTracking(True)
        
    def setup_event_monitoring(self):
        """设置事件监控"""
        # 为主窗口安装事件过滤器
        main_filter = MouseEventDiagnosticFilter("诊断窗口")
        self.installEventFilter(main_filter)
        
        # 为中央控件安装事件过滤器
        central_filter = MouseEventDiagnosticFilter("中央控件")
        self.centralWidget().installEventFilter(central_filter)
        
        logger.info("🔍 [诊断] 事件监控已设置")
        
    def diagnose_plugin_windows(self):
        """诊断插件窗口"""
        try:
            logger.info("🔍 [诊断] 开始查找和诊断插件窗口")
            
            plugin_windows = []
            for widget in QApplication.topLevelWidgets():
                if (isinstance(widget, QMainWindow) and 
                    widget != self and 
                    hasattr(widget, 'windowTitle')):
                    title = widget.windowTitle()
                    if '时钟输入控制' in title or '插件' in title:
                        plugin_windows.append(widget)
                        logger.info(f"🔍 [诊断] 找到插件窗口: {title}")
            
            if not plugin_windows:
                self.status_label.setText("未找到插件窗口")
                logger.warning("🔍 [诊断] 未找到插件窗口")
                return
            
            # 为找到的插件窗口安装诊断过滤器
            for i, window in enumerate(plugin_windows):
                try:
                    window_title = window.windowTitle()
                    filter_name = f"插件窗口_{i}_{window_title}"
                    diagnostic_filter = MouseEventDiagnosticFilter(filter_name)
                    window.installEventFilter(diagnostic_filter)
                    
                    # 检查窗口属性
                    logger.info(f"🔍 [诊断] 窗口属性检查 - {window_title}:")
                    logger.info(f"  - 窗口标志: {window.windowFlags()}")
                    logger.info(f"  - 是否可见: {window.isVisible()}")
                    logger.info(f"  - 是否启用: {window.isEnabled()}")
                    logger.info(f"  - 是否有焦点: {window.hasFocus()}")
                    logger.info(f"  - 鼠标跟踪: {window.hasMouseTracking()}")
                    logger.info(f"  - 窗口状态: {window.windowState()}")
                    
                    self.status_label.setText(f"已为 {len(plugin_windows)} 个插件窗口安装诊断过滤器")
                    
                except Exception as e:
                    logger.error(f"🔍 [诊断] 为窗口安装过滤器失败: {str(e)}")
            
        except Exception as e:
            logger.error(f"🔍 [诊断] 诊断插件窗口失败: {str(e)}")
            self.status_label.setText(f"诊断失败: {str(e)}")
            
    def test_window_focus(self):
        """测试窗口焦点"""
        try:
            logger.info("🔍 [诊断] 测试窗口焦点")
            
            # 检查当前活动窗口
            active_window = QApplication.activeWindow()
            if active_window:
                logger.info(f"🔍 [诊断] 当前活动窗口: {active_window.windowTitle()}")
            else:
                logger.info("🔍 [诊断] 没有活动窗口")
            
            # 检查焦点控件
            focus_widget = QApplication.focusWidget()
            if focus_widget:
                logger.info(f"🔍 [诊断] 当前焦点控件: {type(focus_widget).__name__}")
            else:
                logger.info("🔍 [诊断] 没有焦点控件")
            
            # 强制获取焦点
            self.activateWindow()
            self.raise_()
            self.setFocus()
            
            self.status_label.setText("焦点测试完成，查看控制台日志")
            
        except Exception as e:
            logger.error(f"🔍 [诊断] 测试窗口焦点失败: {str(e)}")
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        logger.info(f"🔍 [诊断] 诊断窗口直接捕获鼠标按下事件")
        logger.info(f"🔍 [诊断] 按钮: {event.button()}, 位置: {event.globalPos()}")
        self.status_label.setText(f"捕获鼠标按下: {event.globalPos()}")
        super().mousePressEvent(event)
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if event.buttons():  # 只在有按钮按下时记录
            logger.info(f"🔍 [诊断] 诊断窗口直接捕获鼠标移动事件")
            logger.info(f"🔍 [诊断] 按钮状态: {event.buttons()}, 位置: {event.globalPos()}")
        super().mouseMoveEvent(event)
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        logger.info(f"🔍 [诊断] 诊断窗口直接捕获鼠标释放事件")
        logger.info(f"🔍 [诊断] 按钮: {event.button()}, 位置: {event.globalPos()}")
        self.status_label.setText(f"捕获鼠标释放: {event.globalPos()}")
        super().mouseReleaseEvent(event)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建诊断窗口
    diagnostic_window = DiagnosticWindow()
    diagnostic_window.show()
    
    logger.info("🔍 [诊断] 鼠标事件诊断工具已启动")
    logger.info("🔍 [诊断] 请在诊断窗口内进行鼠标操作测试")
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
