#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试现代化架构
验证ModernBaseHandler和RegisterManager API是否正常工作
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_modern_architecture():
    """测试现代化架构"""
    try:
        print("=" * 60)
        print("测试现代化架构")
        print("=" * 60)
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 导入必要的模块
        from core.services.register.RegisterManager import RegisterManager
        import json
        
        print("1. 加载寄存器配置...")
        # 加载寄存器配置
        config_path = os.path.join(os.path.dirname(__file__), 'lib', 'register.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        print(f"   ✓ 加载了 {len(registers_config)} 个寄存器配置")
        
        print("2. 创建RegisterManager...")
        # 创建RegisterManager
        register_manager = RegisterManager(registers_config)
        print(f"   ✓ 创建了RegisterManager，包含 {len(register_manager.register_objects)} 个寄存器对象")
        
        print("3. 测试RegisterManager新API...")
        # 测试新的API
        
        # 测试get_register_bits
        bits_0x02 = register_manager.get_register_bits("0x02")
        print(f"   ✓ 寄存器0x02有 {len(bits_0x02)} 个位字段")
        
        # 测试get_widget_register_mapping
        widget_map_0x02 = register_manager.get_widget_register_mapping("0x02")
        print(f"   ✓ 寄存器0x02有 {len(widget_map_0x02)} 个控件映射")
        for widget_name in widget_map_0x02.keys():
            print(f"     - {widget_name}")
        
        # 测试位字段值获取和设置
        if "POWERDOWN" in [bit["name"] for bit in bits_0x02]:
            original_value = register_manager.get_bit_field_value("0x02", "POWERDOWN")
            print(f"   ✓ POWERDOWN位原始值: {original_value}")
            
            # 设置新值
            register_manager.set_bit_field_value("0x02", "POWERDOWN", 1)
            new_value = register_manager.get_bit_field_value("0x02", "POWERDOWN")
            print(f"   ✓ POWERDOWN位新值: {new_value}")
            
            # 恢复原值
            register_manager.set_bit_field_value("0x02", "POWERDOWN", original_value)
            restored_value = register_manager.get_bit_field_value("0x02", "POWERDOWN")
            print(f"   ✓ POWERDOWN位恢复值: {restored_value}")
        
        print("4. 测试update_widget_value API...")
        # 测试控件值更新API
        if "powerDown" in widget_map_0x02:
            success, addr, reg_value = register_manager.update_widget_value("powerDown", 1, "0x02")
            if success:
                print(f"   ✓ 成功通过控件更新寄存器: {addr} = 0x{reg_value:04X}")
            else:
                print("   ❌ 控件值更新失败")
        
        print("5. 创建现代化时钟输入控制处理器...")
        # 创建现代化处理器
        try:
            from ui.handlers.ModernClkinControlHandler import ModernClkinControlHandler
            modern_handler = ModernClkinControlHandler(None, register_manager)
            print("   ✓ 成功创建ModernClkinControlHandler")

            # 等待_post_init执行
            print("   等待控件映射构建...")
            import time
            time.sleep(0.2)  # 等待200ms确保_post_init执行

            # 检查控件映射是否正确构建
            print(f"   ✓ 构建了 {len(modern_handler.widget_register_map)} 个控件映射")
            for widget_name, widget_info in modern_handler.widget_register_map.items():
                print(f"     - {widget_name} -> {widget_info['register_addr']}")

            # 显示窗口（可选）
            # modern_handler.show()

        except Exception as e:
            print(f"   ❌ 创建ModernClkinControlHandler失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

        print("6. 测试控件值变化模拟...")
        # 模拟控件值变化
        if hasattr(modern_handler, '_on_widget_changed'):
            try:
                print("   模拟losEn控件变化...")
                modern_handler._on_widget_changed("losEn", True)
                print("   ✓ losEn控件值变化处理成功")

                print("   模拟powerDown控件变化...")
                modern_handler._on_widget_changed("powerDown", True)
                print("   ✓ powerDown控件值变化处理成功")

            except Exception as e:
                print(f"   ❌ 控件值变化处理失败: {str(e)}")
                import traceback
                traceback.print_exc()

        print("7. 测试获取状态API...")
        # 测试状态获取
        try:
            status = modern_handler.get_current_clkin_status()
            print(f"   ✓ 获取时钟输入状态成功: {status}")
        except Exception as e:
            print(f"   ❌ 获取状态失败: {str(e)}")
        
        print("\n" + "=" * 60)
        print("🎉 现代化架构测试完成！")
        print("=" * 60)
        
        # 不启动事件循环，直接退出
        app.quit()
        return True
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_modern_architecture()
    sys.exit(0 if success else 1)
