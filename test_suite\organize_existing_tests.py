#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
整理现有测试文件脚本
将根目录下的测试文件按功能分类整理到测试套件中
"""

import os
import shutil
import sys
from pathlib import Path

# 项目根目录
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
TEST_SUITE_DIR = os.path.join(PROJECT_ROOT, 'test_suite')

# 测试文件分类映射
TEST_FILE_CATEGORIES = {
    # 功能测试
    'functional': [
        'test_modern_pll.py',
        'test_modern_pll_fix.py', 
        'test_modern_pll_fixed.py',
        'test_modern_clk_outputs.py',
        'test_modern_sync_sysref.py',
        'test_auto_write.py',
        'test_auto_write_fix.py',
        'test_table_auto_write.py',
        'test_pll_functionality.py',
        'test_pll_controls_only.py',
        'test_clk_output_ui.py',
        'test_sync_sysref_final.py',
        'test_sync_sysref_fix.py',
        'test_enhanced_sync_initialization.py',
        'test_final_frequency_calculation.py',
        'test_pll_and_clkout_calculations.py'
    ],
    
    # 集成测试
    'integration': [
        'test_modern_architecture.py',
        'test_modern_handlers.py',
        'test_modern_factory.py',
        'test_stage3_refactor.py',
        'test_final_refactoring.py',
        'final_refactor_verification.py',
        'complete_refactor_verification.py',
        'test_app_after_removal.py'
    ],
    
    # UI测试
    'ui': [
        'test_scroll_area_fix.py',
        'test_ui_display.py',
        'test_ui_refactor.py',
        'test_final_layout.py',
        'test_search_final.py',
        'test_search_layout.py',
        'test_search_style.py',
        'test_single_search_box.py',
        'test_improved_search.py',
        'test_modern_search.py',
        'test_window_fix.py',
        'test_window_api_fix.py',
        'test_bottom_buttons_restored.py',
        'test_buttons_removed.py'
    ],
    
    # 单元测试
    'unit': [
        'test_modern_register_table.py',
        'test_modern_set_modes.py',
        'test_modern_ui_event.py',
        'test_register_bit_update.py',
        'test_register_update_fix.py',
        'test_register_navigation.py',
        'test_register_bus_fix.py',
        'test_widget_register_jump.py',
        'test_table_jump.py',
        'test_simple_table_write.py'
    ],
    
    # 性能测试
    'performance': [
        'test_batch_write.py',
        'test_batch_write_completion.py',
        'test_batch_write_stability.py',
        'test_crash_after_write_all.py',
        'test_write_timeout.py'
    ],
    
    # 回归测试
    'regression': [
        'test_pll_removal_verification.py',
        'test_pll_removal_simple.py',
        'test_clk_output_removal.py',
        'test_sync_sysref_removal.py',
        'test_initialization_fix.py',
        'test_main_window_init.py',
        'test_port_refresh_fix.py',
        'test_port_display.py',
        'verify_fixes.py',
        'verify_refactor.py',
        'verify_auto_write.py',
        'final_verification.py'
    ]
}

def create_test_inventory():
    """创建测试文件清单"""
    print("=" * 60)
    print("寄存器配置工具测试文件清单")
    print("=" * 60)
    
    # 扫描根目录下的测试文件
    test_files = []
    for file_name in os.listdir(PROJECT_ROOT):
        if file_name.startswith('test_') and file_name.endswith('.py'):
            test_files.append(file_name)
    
    print(f"发现 {len(test_files)} 个测试文件:")
    
    # 按分类统计
    categorized_files = set()
    category_stats = {}
    
    for category, files in TEST_FILE_CATEGORIES.items():
        category_count = 0
        print(f"\n{category.upper()} ({len(files)} 个文件):")
        
        for file_name in files:
            if file_name in test_files:
                print(f"  ✓ {file_name}")
                categorized_files.add(file_name)
                category_count += 1
            else:
                print(f"  ✗ {file_name} (文件不存在)")
        
        category_stats[category] = category_count
    
    # 未分类的文件
    uncategorized_files = set(test_files) - categorized_files
    if uncategorized_files:
        print(f"\n未分类文件 ({len(uncategorized_files)} 个):")
        for file_name in sorted(uncategorized_files):
            print(f"  ? {file_name}")
    
    # 统计信息
    print(f"\n" + "=" * 60)
    print("统计信息:")
    print(f"总测试文件: {len(test_files)}")
    print(f"已分类文件: {len(categorized_files)}")
    print(f"未分类文件: {len(uncategorized_files)}")
    
    for category, count in category_stats.items():
        expected_count = len(TEST_FILE_CATEGORIES[category])
        print(f"{category}: {count}/{expected_count} 个文件")
    
    return test_files, categorized_files, uncategorized_files

def copy_tests_to_categories(dry_run=True):
    """将测试文件复制到对应分类目录"""
    print(f"\n{'=' * 60}")
    print(f"{'模拟' if dry_run else '执行'}测试文件分类整理")
    print(f"{'=' * 60}")
    
    copied_count = 0
    
    for category, files in TEST_FILE_CATEGORIES.items():
        category_dir = os.path.join(TEST_SUITE_DIR, category)
        
        # 确保分类目录存在
        if not dry_run:
            os.makedirs(category_dir, exist_ok=True)
        
        print(f"\n{category.upper()} 目录:")
        
        for file_name in files:
            source_path = os.path.join(PROJECT_ROOT, file_name)
            dest_path = os.path.join(category_dir, file_name)
            
            if os.path.exists(source_path):
                if dry_run:
                    print(f"  将复制: {file_name} -> {category}/")
                else:
                    try:
                        shutil.copy2(source_path, dest_path)
                        print(f"  ✓ 已复制: {file_name}")
                        copied_count += 1
                    except Exception as e:
                        print(f"  ✗ 复制失败: {file_name} - {e}")
            else:
                print(f"  ✗ 源文件不存在: {file_name}")
    
    if not dry_run:
        print(f"\n总共复制了 {copied_count} 个文件")
    
    return copied_count

def create_category_readme_files():
    """为每个分类创建README文件"""
    category_descriptions = {
        'functional': '功能测试 - 测试具体的业务功能',
        'integration': '集成测试 - 测试模块间的集成',
        'ui': 'UI测试 - 测试用户界面相关功能',
        'unit': '单元测试 - 测试独立的代码单元',
        'performance': '性能测试 - 测试系统性能',
        'regression': '回归测试 - 测试功能回归和兼容性'
    }
    
    for category, description in category_descriptions.items():
        category_dir = os.path.join(TEST_SUITE_DIR, category)
        readme_path = os.path.join(category_dir, 'README.md')
        
        os.makedirs(category_dir, exist_ok=True)
        
        readme_content = f"""# {category.upper()} 测试

## 描述
{description}

## 测试文件
"""
        
        # 列出该分类下的测试文件
        files = TEST_FILE_CATEGORIES.get(category, [])
        for file_name in files:
            readme_content += f"- `{file_name}`\n"
        
        readme_content += f"""
## 运行测试
```bash
# 运行该分类的所有测试
python test_suite/run_all_tests.py --category {category}

# 运行特定测试文件
python test_suite/{category}/test_specific_file.py
```

## 注意事项
- 确保测试环境已正确设置
- 某些测试可能需要Qt环境
- 性能测试可能需要较长时间
"""
        
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print(f"✓ 创建README: {category}/README.md")

def analyze_duplicates_and_cleanup():
    """分析重复文件和可清理的文件"""
    print(f"\n{'=' * 60}")
    print("分析重复和可清理的测试文件")
    print(f"{'=' * 60}")

    # 扫描根目录下的测试文件
    root_test_files = []
    for file_name in os.listdir(PROJECT_ROOT):
        if file_name.startswith('test_') and file_name.endswith('.py'):
            root_test_files.append(file_name)

    # 扫描test_suite目录下已整理的文件
    organized_files = set()
    for category in TEST_FILE_CATEGORIES.keys():
        category_dir = os.path.join(TEST_SUITE_DIR, category)
        if os.path.exists(category_dir):
            for file_name in os.listdir(category_dir):
                if file_name.endswith('.py'):
                    organized_files.add(file_name)

    # 分析重复文件
    duplicates = []
    for file_name in root_test_files:
        if file_name in organized_files:
            duplicates.append(file_name)

    print(f"发现 {len(duplicates)} 个重复文件（根目录和test_suite都存在）:")
    for file_name in sorted(duplicates):
        print(f"  - {file_name}")

    # 分析过时的测试文件（基于文件名模式）
    obsolete_patterns = [
        'test_clkin_control_fix.py',
        'test_clkin_initialization_fix.py',
        'test_combobox_initialization_fix.py',
        'test_constructor_fix.py',
        'test_initialization_order.py',
        'test_layout_modifications.py',
        'test_modern_handlers_fix.py',
        'test_pll2_calibration.py',
        'test_pll2c1_c3_fix.py',
        'test_pll_register_jump_fix.py',
        'test_spinbox_minimum.py',
        'test_spinbox_range.py',
        'test_spinbox_setvalue.py',
        'test_tool_window_factory_migration.py'
    ]

    obsolete_files = []
    for file_name in root_test_files:
        if file_name in obsolete_patterns:
            obsolete_files.append(file_name)

    print(f"\n发现 {len(obsolete_files)} 个可能过时的测试文件:")
    for file_name in sorted(obsolete_files):
        print(f"  - {file_name}")

    # 分析可合并的文件
    merge_candidates = {
        'initialization_tests': [
            'test_clkin_initialization_fix.py',
            'test_combobox_initialization_fix.py',
            'test_initialization_fix_comprehensive.py',
            'test_initialization_order.py'
        ],
        'spinbox_tests': [
            'test_spinbox_minimum.py',
            'test_spinbox_range.py',
            'test_spinbox_setvalue.py'
        ],
        'pll_calibration_tests': [
            'test_pll2_calibration.py',
            'test_pll2c1_c3_fix.py'
        ]
    }

    print(f"\n可合并的测试文件组:")
    for group_name, files in merge_candidates.items():
        existing_files = [f for f in files if f in root_test_files]
        if existing_files:
            print(f"  {group_name}:")
            for file_name in existing_files:
                print(f"    - {file_name}")

    return duplicates, obsolete_files, merge_candidates

def suggest_cleanup_actions():
    """建议清理操作"""
    duplicates, obsolete_files, merge_candidates = analyze_duplicates_and_cleanup()

    print(f"\n{'=' * 60}")
    print("建议的清理操作")
    print(f"{'=' * 60}")

    print("1. 删除重复文件（根目录中已在test_suite中存在的文件）:")
    for file_name in sorted(duplicates):
        print(f"   rm {file_name}")

    print(f"\n2. 删除过时的测试文件:")
    for file_name in sorted(obsolete_files):
        print(f"   rm {file_name}")

    print(f"\n3. 合并相关测试文件:")
    for group_name, files in merge_candidates.items():
        existing_files = [f for f in files if os.path.exists(os.path.join(PROJECT_ROOT, f))]
        if len(existing_files) > 1:
            print(f"   合并 {group_name}: {', '.join(existing_files)}")

    # 计算可清理的文件总数
    total_cleanup = len(duplicates) + len(obsolete_files)
    print(f"\n总计可清理文件: {total_cleanup} 个")

    return duplicates, obsolete_files

def main():
    """主函数"""
    print("寄存器配置工具测试文件整理工具")

    # 创建测试清单
    test_files, categorized_files, uncategorized_files = create_test_inventory()

    # 分析重复和可清理的文件
    duplicates, obsolete_files = suggest_cleanup_actions()

    # 处理未分类文件
    if uncategorized_files:
        print(f"\n⚠️  发现 {len(uncategorized_files)} 个未分类的测试文件:")
        for file_name in sorted(uncategorized_files):
            print(f"  - {file_name}")
        print("\n建议手动检查这些文件并添加到适当的分类中")

if __name__ == "__main__":
    main()
