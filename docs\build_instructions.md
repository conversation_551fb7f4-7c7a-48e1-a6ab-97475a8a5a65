# 构建说明文档

## 概述

本文档说明如何使用 `build_exe.py` 脚本进行自动版本管理和打包。

## 版本管理系统

### 版本号格式

版本号采用四段式格式：`主版本.次版本.补丁版本.构建号`

- **主版本 (Major)**: 重大功能更新或架构变更
- **次版本 (Minor)**: 新功能添加
- **补丁版本 (Patch)**: Bug修复和小改进
- **构建号 (Build)**: 每次构建自动递增

### 版本配置文件

版本信息存储在 `version.json` 文件中：

```json
{
  "version": {
    "major": 1,
    "minor": 0,
    "patch": 0,
    "build": 0
  },
  "app_info": {
    "name": "FSJ04832 寄存器配置工具",
    "description": "用于配置和管理FSJ04832寄存器的专业工具",
    "company": "FSJ Technology",
    "copyright": "© 2024 FSJ Technology",
    "author": "开发团队"
  },
  "build_info": {
    "last_build_date": "",
    "build_type": "release",
    "target_platform": "windows"
  }
}
```

## 使用方法

### 基本用法

```bash
# 默认构建（自动增加构建号）
python build_exe.py

# 使用指定的spec文件
python build_exe.py --spec-file build.spec
```

### 版本号管理

```bash
# 增加构建号（默认）
python build_exe.py --version-type build

# 增加补丁版本号
python build_exe.py --version-type patch

# 增加次版本号
python build_exe.py --version-type minor

# 增加主版本号
python build_exe.py --version-type major

# 不增加版本号
python build_exe.py --no-version-increment
```

### 命令行参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--version-type` | 版本号增加类型 (build/patch/minor/major) | build |
| `--no-version-increment` | 不增加版本号 | False |
| `--spec-file` | 指定spec文件路径 | build.spec |

## 构建流程

1. **版本管理**
   - 读取当前版本信息
   - 根据参数增加相应的版本号
   - 更新构建时间
   - 保存版本信息

2. **更新spec文件**
   - 自动更新spec文件中的可执行文件名
   - 文件名格式：`FSJConfigTool{major}.{minor}.{patch}`

3. **清理构建目录**
   - 删除 `build/`、`dist/`、`__pycache__/` 目录

4. **执行构建**
   - 使用PyInstaller和指定的spec文件进行构建
   - 如果spec文件不存在，回退到简单构建方式

5. **复制额外文件**
   - 复制配置文件和图像文件到输出目录

## 输出文件

构建完成后，可执行文件将位于 `dist/` 目录中，文件名包含版本信息：

```
dist/
└── FSJConfigTool1.0.0.exe  # 示例：版本1.0.0
```

## 版本号策略建议

### 何时增加不同类型的版本号

- **构建号 (build)**: 
  - 日常开发构建
  - Bug修复
  - 小的代码改进

- **补丁版本 (patch)**:
  - 重要Bug修复
  - 安全更新
  - 小功能改进

- **次版本 (minor)**:
  - 新功能添加
  - API扩展
  - 向后兼容的更改

- **主版本 (major)**:
  - 重大架构变更
  - 不兼容的API更改
  - 重大功能重写

## 示例工作流

### 日常开发构建
```bash
# 每次构建时自动增加构建号
python build_exe.py
```

### 发布补丁版本
```bash
# 修复Bug后发布补丁
python build_exe.py --version-type patch
```

### 发布新功能
```bash
# 添加新功能后发布次版本
python build_exe.py --version-type minor
```

### 重大版本发布
```bash
# 重大更新后发布主版本
python build_exe.py --version-type major
```

## 注意事项

1. **版本文件备份**: 建议将 `version.json` 文件加入版本控制系统
2. **构建环境**: 确保已安装PyInstaller：`pip install pyinstaller`
3. **spec文件**: 确保 `build.spec` 文件存在且配置正确
4. **权限问题**: 在某些系统上可能需要管理员权限来执行构建

## 故障排除

### 常见问题

1. **PyInstaller未安装**
   ```
   错误: PyInstaller未安装
   请运行: pip install pyinstaller
   ```
   解决方案：安装PyInstaller

2. **spec文件不存在**
   ```
   警告: spec文件 build.spec 不存在，使用简单构建方式
   ```
   解决方案：确保spec文件存在或使用 `--spec-file` 参数指定正确路径

3. **版本文件损坏**
   ```
   警告: 无法加载版本文件 version.json
   ```
   解决方案：删除损坏的版本文件，脚本会自动创建默认版本

## 自定义配置

可以通过修改 `VersionManager` 类来自定义版本管理行为：

- 修改默认版本信息
- 更改可执行文件命名规则
- 添加额外的版本信息字段
- 自定义构建时间格式

## 集成到CI/CD

可以将此脚本集成到持续集成/持续部署流水线中：

```yaml
# GitHub Actions 示例
- name: Build Application
  run: python build_exe.py --version-type patch
```

这样可以实现自动化的版本管理和构建流程。
