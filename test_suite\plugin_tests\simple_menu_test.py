#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的菜单点击测试
专门测试菜单点击响应问题
"""

import sys
import os
from functools import partial

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import (QApplication, QMainWindow, QMenuBar, QAction, 
                            QWidget, QVBoxLayout, QLabel, QPushButton, QTextEdit)
from PyQt5.QtCore import QTimer, pyqtSignal


class SimpleTestWindow(QMainWindow):
    """简单测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("菜单点击响应测试")
        self.resize(800, 600)
        
        # 创建中央窗口部件
        self.setup_central_widget()
        
        # 创建菜单栏
        self.create_test_menus()
        
        # 设置自动关闭定时器
        self.close_timer = QTimer()
        self.close_timer.timeout.connect(self.close)
        self.close_timer.start(60000)  # 60秒后自动关闭
    
    def setup_central_widget(self):
        """设置中央窗口部件"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # 添加说明标签
        info_label = QLabel("""
菜单点击响应测试

这个测试专门用来验证菜单点击的响应问题。

测试步骤：
1. 直接点击"模拟插件"菜单中的各个项目
2. 观察是否需要从右边向左滑动才能触发
3. 查看下方的日志输出

如果修复成功：
- 直接点击就能触发
- 日志会显示点击事件
- 不需要滑动操作

程序将在1分钟后自动关闭
        """)
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # 添加日志显示
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        layout.addWidget(self.log_text)
        
        # 添加清除日志按钮
        clear_button = QPushButton("清除日志")
        clear_button.clicked.connect(self.log_text.clear)
        layout.addWidget(clear_button)
    
    def create_test_menus(self):
        """创建测试菜单"""
        menu_bar = self.menuBar()
        
        # 创建模拟插件菜单
        plugin_menu = menu_bar.addMenu("模拟插件(&P)")
        
        # 添加普通菜单项
        normal_action = QAction("普通菜单项", self)
        normal_action.triggered.connect(lambda: self.log_click("普通菜单项"))
        plugin_menu.addAction(normal_action)
        
        # 添加可选中菜单项（模拟插件菜单）
        checkable_action1 = QAction("选择性寄存器操作", self)
        checkable_action1.setCheckable(True)
        checkable_action1.triggered.connect(partial(self.handle_plugin_click, "选择性寄存器操作"))
        plugin_menu.addAction(checkable_action1)
        
        checkable_action2 = QAction("示例工具插件", self)
        checkable_action2.setCheckable(True)
        checkable_action2.triggered.connect(partial(self.handle_plugin_click, "示例工具插件"))
        plugin_menu.addAction(checkable_action2)
        
        checkable_action3 = QAction("性能监控插件", self)
        checkable_action3.setCheckable(True)
        checkable_action3.triggered.connect(partial(self.handle_plugin_click, "性能监控插件"))
        plugin_menu.addAction(checkable_action3)
        
        # 添加分隔符
        plugin_menu.addSeparator()
        
        # 添加插件管理器
        manager_action = QAction("插件管理器", self)
        manager_action.triggered.connect(lambda: self.log_click("插件管理器"))
        plugin_menu.addAction(manager_action)
        
        # 创建测试菜单
        test_menu = menu_bar.addMenu("测试(&T)")
        
        test_action = QAction("测试点击", self)
        test_action.triggered.connect(lambda: self.log_click("测试点击"))
        test_menu.addAction(test_action)
        
        # 强制更新菜单
        plugin_menu.update()
        test_menu.update()
        menu_bar.update()
        
        self.log_click("菜单创建完成")
    
    def log_click(self, item_name):
        """记录点击事件"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S.%f")[:-3]
        message = f"[{timestamp}] 🎯 点击: {item_name}"
        print(message)
        self.log_text.append(message)
    
    def handle_plugin_click(self, plugin_name, checked):
        """处理插件点击"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S.%f")[:-3]
        message = f"[{timestamp}] 🔧 插件点击: {plugin_name}, 选中={checked}"
        print(message)
        self.log_text.append(message)
        
        # 模拟插件窗口打开/关闭
        if checked:
            self.log_text.append(f"[{timestamp}] ✅ 模拟打开插件窗口: {plugin_name}")
        else:
            self.log_text.append(f"[{timestamp}] ❌ 模拟关闭插件窗口: {plugin_name}")


def main():
    """主函数"""
    app = QApplication([])
    
    # 创建测试窗口
    window = SimpleTestWindow()
    window.show()
    
    print("🚀 简单菜单点击测试开始")
    print("请测试以下操作：")
    print("1. 直接点击'模拟插件'菜单中的各个项目")
    print("2. 观察是否需要从右边向左滑动才能触发")
    print("3. 查看窗口中的日志输出")
    print("4. 对比'测试'菜单的点击响应")
    print("程序将在1分钟后自动关闭")
    
    # 运行应用
    app.exec_()
    
    print("✅ 测试完成")


if __name__ == "__main__":
    main()
