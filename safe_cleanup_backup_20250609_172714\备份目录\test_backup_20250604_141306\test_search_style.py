#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试搜索结果列表的样式和显示
验证是否正确设置了弹出窗口样式
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
from PyQt5.QtCore import QTimer, Qt
from ui.handlers.ModernRegisterIOHandler import ModernRegisterIOHandler
from utils.Log import logger

class TestSearchStyleWindow(QMainWindow):
    """测试搜索样式的主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("测试搜索结果列表样式")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 添加说明标签
        info_label = QLabel("测试搜索结果列表的弹出窗口样式")
        layout.addWidget(info_label)
        
        # 创建现代化IO处理器
        try:
            self.io_handler = ModernRegisterIOHandler.create_for_testing(self)
            layout.addWidget(self.io_handler)
            
            # 连接信号
            self.io_handler.search_requested.connect(self.on_search_requested)
            self.io_handler.bit_field_selected.connect(self.on_bit_field_selected)
            
            logger.info("现代化IO处理器创建成功")
            
            # 添加测试按钮
            test_btn = QPushButton("触发搜索测试")
            test_btn.clicked.connect(self.trigger_search_test)
            layout.addWidget(test_btn)
            
        except Exception as e:
            logger.error(f"创建现代化IO处理器失败: {str(e)}")
            error_label = QLabel(f"创建失败: {str(e)}")
            layout.addWidget(error_label)
    
    def trigger_search_test(self):
        """触发搜索测试"""
        logger.info("触发搜索测试")
        # 模拟在搜索框中输入文本
        if hasattr(self.io_handler, 'search_edit'):
            self.io_handler.search_edit.setText("sclk")
            # 手动触发搜索
            self.io_handler._handle_search_click("sclk")
            
            # 检查搜索结果列表的窗口标志
            if hasattr(self.io_handler, 'search_results_list'):
                flags = self.io_handler.search_results_list.windowFlags()
                logger.info(f"搜索结果列表窗口标志: {flags}")
                logger.info(f"是否为弹出窗口: {flags & Qt.Popup}")
                logger.info(f"搜索结果列表可见性: {self.io_handler.search_results_list.isVisible()}")
                logger.info(f"搜索结果列表项目数: {self.io_handler.search_results_list.count()}")
    
    def on_search_requested(self, search_text):
        """处理搜索请求"""
        logger.info(f"搜索请求: '{search_text}'")
    
    def on_bit_field_selected(self, reg_addr):
        """处理位字段选择"""
        logger.info(f"位字段选择: 寄存器 {reg_addr}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestSearchStyleWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
