#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
选择性寄存器插件配置管理器
负责加载和管理插件的配置信息
"""

import json
import os
from typing import Dict, List, Any, Optional
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class SelectiveRegisterConfig:
    """选择性寄存器插件配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """初始化配置管理器
        
        Args:
            config_path: 配置文件路径，如果为None则使用默认路径
        """
        self.config_path = config_path or self._get_default_config_path()
        self.config_data = {}
        self._load_config()
        
    def _get_default_config_path(self) -> str:
        """获取默认配置文件路径"""
        # 获取项目根目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(os.path.dirname(current_dir))
        return os.path.join(project_root, "config", "selective_register_plugin.json")
        
    def _load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self.config_data = json.load(f)
                logger.info(f"成功加载插件配置文件: {self.config_path}")
            else:
                logger.warning(f"配置文件不存在: {self.config_path}，使用默认配置")
                self._create_default_config()
        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}，使用默认配置")
            self._create_default_config()
            
    def _create_default_config(self):
        """创建默认配置"""
        self.config_data = {
            "plugin_info": {
                "name": "选择性寄存器操作",
                "version": "1.0.1",
                "description": "允许用户选择特定的寄存器进行批量读写操作"
            },
            "ui_config": {
                "window": {
                    "title": "选择性寄存器操作",
                    "width": 900,
                    "height": 700,
                    "min_width": 600,
                    "min_height": 500
                }
            },
            "register_groups": {"group_definitions": []},
            "templates": {"preset_templates": []},
            "operation_config": {
                "confirmation_required": {"read": True, "write": True},
                "auto_switch_to_monitor": True
            }
        }
        
    def get_plugin_info(self) -> Dict[str, str]:
        """获取插件信息"""
        return self.config_data.get("plugin_info", {})
        
    def get_window_config(self) -> Dict[str, Any]:
        """获取窗口配置"""
        return self.config_data.get("ui_config", {}).get("window", {})
        
    def get_ui_texts(self) -> Dict[str, Any]:
        """获取UI文本配置"""
        return self.config_data.get("ui_config", {}).get("texts", {})
        
    def get_button_text(self, button_key: str, default: str = "") -> str:
        """获取按钮文本"""
        texts = self.get_ui_texts()
        return texts.get("buttons", {}).get(button_key, default)
        
    def get_label_text(self, label_key: str, default: str = "", **kwargs) -> str:
        """获取标签文本，支持格式化"""
        texts = self.get_ui_texts()
        text = texts.get("labels", {}).get(label_key, default)
        if kwargs:
            try:
                return text.format(**kwargs)
            except (KeyError, ValueError):
                return text
        return text
        
    def get_message_text(self, message_key: str, default: str = "", **kwargs) -> str:
        """获取消息文本，支持格式化"""
        texts = self.get_ui_texts()
        text = texts.get("messages", {}).get(message_key, default)
        if kwargs:
            try:
                return text.format(**kwargs)
            except (KeyError, ValueError):
                return text
        return text
        
    def get_tab_text(self, tab_key: str, default: str = "") -> str:
        """获取标签页文本"""
        texts = self.get_ui_texts()
        return texts.get("tabs", {}).get(tab_key, default)
        
    def get_group_text(self, group_key: str, default: str = "") -> str:
        """获取分组文本"""
        texts = self.get_ui_texts()
        return texts.get("groups", {}).get(group_key, default)
        
    def get_table_headers(self, table_key: str) -> List[str]:
        """获取表格标题"""
        texts = self.get_ui_texts()
        return texts.get("table_headers", {}).get(table_key, [])
        
    def get_register_groups(self) -> List[Dict[str, Any]]:
        """获取寄存器分组定义"""
        return self.config_data.get("register_groups", {}).get("group_definitions", [])
        
    def get_preset_templates(self) -> List[Dict[str, Any]]:
        """获取预设模板"""
        return self.config_data.get("templates", {}).get("preset_templates", [])
        
    def get_operation_config(self) -> Dict[str, Any]:
        """获取操作配置"""
        return self.config_data.get("operation_config", {})
        
    def is_confirmation_required(self, operation: str) -> bool:
        """检查操作是否需要确认"""
        op_config = self.get_operation_config()
        return op_config.get("confirmation_required", {}).get(operation, True)
        
    def should_auto_switch_to_monitor(self) -> bool:
        """是否自动切换到监控标签页"""
        op_config = self.get_operation_config()
        return op_config.get("auto_switch_to_monitor", True)
        
    def get_log_timestamp_format(self) -> str:
        """获取日志时间戳格式"""
        op_config = self.get_operation_config()
        return op_config.get("log_timestamp_format", "%H:%M:%S")
        
    def get_progress_update_interval(self) -> int:
        """获取进度更新间隔"""
        op_config = self.get_operation_config()
        return op_config.get("progress_update_interval", 100)
        
    def save_config(self):
        """保存配置到文件"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, ensure_ascii=False, indent=2)
            logger.info(f"配置已保存到: {self.config_path}")
        except Exception as e:
            logger.error(f"保存配置文件失败: {str(e)}")
            
    def update_config(self, section: str, key: str, value: Any):
        """更新配置项"""
        try:
            if section not in self.config_data:
                self.config_data[section] = {}
            self.config_data[section][key] = value
            logger.debug(f"配置已更新: {section}.{key} = {value}")
        except Exception as e:
            logger.error(f"更新配置失败: {str(e)}")
            
    def get_config_value(self, section: str, key: str, default: Any = None) -> Any:
        """获取配置值"""
        return self.config_data.get(section, {}).get(key, default)


class RegisterGroupMatcher:
    """寄存器分组匹配器"""
    
    def __init__(self, group_definitions: List[Dict[str, Any]]):
        """初始化分组匹配器
        
        Args:
            group_definitions: 分组定义列表
        """
        self.group_definitions = group_definitions
        
    def match_register_to_group(self, register: Dict[str, Any]) -> Optional[str]:
        """将寄存器匹配到分组
        
        Args:
            register: 寄存器信息字典
            
        Returns:
            匹配的分组名称，如果没有匹配则返回None
        """
        addr = register.get('address', '')
        if not addr:
            return None
            
        try:
            addr_int = int(addr, 16)
        except ValueError:
            return None
            
        for group_def in self.group_definitions:
            group_name = group_def.get('name', '')
            rules = group_def.get('rules', [])
            
            if self._match_rules(addr, addr_int, rules):
                return group_name
                
        return "其他"  # 默认分组
        
    def _match_rules(self, addr: str, addr_int: int, rules: List[Dict[str, Any]]) -> bool:
        """检查地址是否匹配规则
        
        Args:
            addr: 地址字符串
            addr_int: 地址整数值
            rules: 规则列表
            
        Returns:
            是否匹配
        """
        for rule in rules:
            rule_type = rule.get('type', '')
            
            if rule_type == 'exact':
                addresses = rule.get('addresses', [])
                if addr in addresses:
                    return True
                    
            elif rule_type == 'range':
                start_addr = rule.get('start', '')
                end_addr = rule.get('end', '')
                
                try:
                    start_int = int(start_addr, 16)
                    end_int = int(end_addr, 16)
                    
                    if start_int <= addr_int <= end_int:
                        return True
                except ValueError:
                    continue
                    
        return False
        
    def create_register_groups(self, registers: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """创建寄存器分组
        
        Args:
            registers: 寄存器列表
            
        Returns:
            分组字典
        """
        groups = {}
        
        # 初始化所有分组
        for group_def in self.group_definitions:
            group_name = group_def.get('name', '')
            if group_name:
                groups[group_name] = []
                
        # 将寄存器分配到分组
        for register in registers:
            group_name = self.match_register_to_group(register)
            if group_name:
                if group_name not in groups:
                    groups[group_name] = []
                groups[group_name].append(register)
                
        # 移除空分组
        return {k: v for k, v in groups.items() if v}


# 全局配置实例
_config_instance = None


def get_config() -> SelectiveRegisterConfig:
    """获取全局配置实例"""
    global _config_instance
    if _config_instance is None:
        _config_instance = SelectiveRegisterConfig()
    return _config_instance


def reload_config():
    """重新加载配置"""
    global _config_instance
    _config_instance = None
    return get_config()
