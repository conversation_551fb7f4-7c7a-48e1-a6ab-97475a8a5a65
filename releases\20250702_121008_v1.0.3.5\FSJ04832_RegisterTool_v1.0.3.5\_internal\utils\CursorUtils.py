"""光标工具模块

提供光标相关的工具函数
"""

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


def force_restore_cursor():
    """强制恢复光标状态
    
    该函数会清空整个光标栈，并确保光标恢复为默认的箭头形状。
    在拖拽操作结束、窗口关闭等情况下使用，确保光标状态正确。
    """
    try:
        # 多次调用restoreOverrideCursor确保清空光标栈
        for _ in range(10):  # 最多尝试10次
            try:
                QApplication.restoreOverrideCursor()
            except Exception:
                break  # 如果出错就停止尝试
                
        # 最后设置为默认光标
        QApplication.setOverrideCursor(Qt.ArrowCursor)
        QApplication.restoreOverrideCursor()
        logger.debug("已强制恢复光标状态")
    except Exception as e:
        logger.error(f"强制恢复光标状态时出错: {str(e)}")