# 工具窗口滚动功能使用指南

## 概述

本指南介绍了如何为工具窗口添加滚动条支持，使界面内容超出窗口大小时能够滚动查看全部内容。

## 功能特性

- **自动滚动检测**: 根据处理器类型自动启用滚动功能
- **手动滚动控制**: 支持动态启用/禁用滚动区域
- **水平和垂直滚动**: 支持双向滚动条
- **智能尺寸管理**: 根据内容类型设置合适的最小尺寸
- **插件集成**: 自动为插件窗口添加滚动支持

## 使用方法

### 1. 现代化处理器自动滚动

对于继承自 `ModernBaseHandler` 的处理器，滚动功能会自动启用：

```python
from ui.handlers.ModernBaseHandler import ModernBaseHandler

class MyHandler(ModernBaseHandler):
    def __init__(self, parent=None, register_manager=None):
        # 自动检测是否需要滚动（基于类名）
        super().__init__(parent, register_manager)
        
        # 创建UI
        self.ui = MyUI()
        self.ui.setupUi(self.content_widget)
```

### 2. 手动控制滚动

#### 初始化时指定滚动状态

```python
# 强制启用滚动
handler = ModernBaseHandler(enable_scroll=True)

# 强制禁用滚动
handler = ModernBaseHandler(enable_scroll=False)

# 自动检测（默认）
handler = ModernBaseHandler(enable_scroll=None)
```

#### 动态启用滚动

```python
# 启用滚动区域，使用默认尺寸
handler.enable_scroll_area()

# 启用滚动区域，指定自定义尺寸
handler.enable_scroll_area(width=800, height=600)
```

#### 动态禁用滚动

```python
# 禁用滚动区域
handler.disable_scroll_area()
```

### 3. 插件窗口滚动

插件窗口的滚动功能由 `PluginIntegrationService` 自动管理：

```python
class MyToolPlugin(IToolWindowPlugin):
    def create_window(self, parent=None):
        # 创建窗口，滚动功能会自动添加
        return MyToolWindow(parent)
```

支持滚动的插件类型：
- `clkin_control_plugin`
- `pll_control_plugin`
- `sync_sysref_plugin`
- `clk_output_plugin`
- `set_modes_plugin`
- `示例工具`

## 配置选项

### 自动滚动检测

以下处理器类型会自动启用滚动：
- `ModernPLLHandler`
- `ModernClkOutputsHandler`
- `ModernSetModesHandler`
- `ModernClkinControlHandler`
- `ModernSyncSysRefHandler`

### 默认尺寸配置

不同处理器类型的默认最小尺寸：

| 处理器类型 | 宽度 | 高度 |
|-----------|------|------|
| ModernPLLHandler | 1400 | 1600 |
| ModernClkOutputsHandler | 1400 | 1600 |
| ModernSetModesHandler | 800 | 600 |
| ModernClkinControlHandler | 800 | 600 |
| ModernSyncSysRefHandler | 800 | 600 |
| ExampleToolWindow | 500 | 400 |
| 默认 | 600 | 500 |

## 最佳实践

### 1. 内容布局设计

- 使用合适的布局管理器（QVBoxLayout, QHBoxLayout等）
- 设置合理的控件最小尺寸
- 避免固定尺寸，使用可扩展的尺寸策略

### 2. 滚动区域优化

- 只在需要时启用滚动功能
- 根据内容量设置合适的最小尺寸
- 考虑用户的屏幕分辨率

### 3. 用户体验

- 提供清晰的视觉指示表明内容可滚动
- 确保重要控件在初始视图中可见
- 考虑添加快速导航功能

## 示例代码

### 完整的滚动窗口示例

```python
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QTabWidget
from ui.handlers.ModernBaseHandler import ModernBaseHandler

class ScrollableToolWindow(ModernBaseHandler):
    def __init__(self, parent=None):
        # 启用滚动功能
        super().__init__(parent, enable_scroll=True)
        
        self.setWindowTitle("可滚动工具窗口")
        self._setup_ui()
    
    def _setup_ui(self):
        layout = QVBoxLayout(self.content_widget)
        
        # 添加标签页来组织大量内容
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)
        
        # 创建多个标签页
        for i in range(5):
            tab = QWidget()
            tab_layout = QVBoxLayout(tab)
            
            # 每个标签页添加大量控件
            for j in range(20):
                label = QLabel(f"标签页 {i+1} - 内容 {j+1}")
                tab_layout.addWidget(label)
            
            tab_widget.addTab(tab, f"标签页 {i+1}")
```

## 测试

运行测试脚本验证滚动功能：

```bash
python test_scroll_functionality.py
```

该脚本提供了多种测试场景：
- 自动滚动的示例插件窗口
- 手动启用滚动的窗口
- 无滚动的普通窗口

## 故障排除

### 常见问题

1. **滚动条不显示**
   - 检查内容尺寸是否超出窗口大小
   - 确认滚动功能已正确启用
   - 验证内容widget的最小尺寸设置

2. **滚动不流畅**
   - 减少布局复杂度
   - 优化控件数量
   - 使用虚拟化技术处理大量数据

3. **布局错乱**
   - 检查布局管理器设置
   - 确认控件的尺寸策略
   - 验证边距和间距设置

### 调试技巧

- 启用详细日志查看滚动区域创建过程
- 使用Qt Designer预览布局效果
- 测试不同屏幕分辨率下的显示效果

## 更新日志

- **v1.0.0**: 初始版本，支持基本滚动功能
- **v1.1.0**: 添加自动检测和插件集成
- **v1.2.0**: 支持动态启用/禁用滚动
