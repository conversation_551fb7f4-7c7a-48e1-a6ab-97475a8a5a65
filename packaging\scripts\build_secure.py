#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
FSJ04832 安全打包脚本
专门用于生成客户发布版本，最大化代码保护
"""

import os
import sys
import json
import shutil
import subprocess
import tempfile
from pathlib import Path
from datetime import datetime

def setup_paths():
    """设置路径"""
    script_dir = Path(__file__).parent
    packaging_root = script_dir.parent
    project_root = packaging_root.parent
    
    return {
        'script_dir': script_dir,
        'packaging_root': packaging_root,
        'project_root': project_root,
        'spec_file': script_dir / 'build_secure.spec'
    }

def load_version_info(packaging_root):
    """加载版本信息"""
    version_file = packaging_root / 'config' / 'version.json'
    try:
        with open(version_file, 'r', encoding='utf-8') as f:
            version_data = json.load(f)
            version = version_data.get('version', {})
            if isinstance(version, dict):
                return f"{version.get('major', 1)}.{version.get('minor', 0)}.{version.get('patch', 0)}.{version.get('build', 0)}"
            return str(version)
    except Exception as e:
        print(f"警告: 无法读取版本信息: {e}")
        return "*******"

def create_secure_build_environment(paths):
    """创建安全构建环境"""
    print("🔒 创建安全构建环境...")
    
    # 创建临时构建目录
    temp_dir = Path(tempfile.mkdtemp(prefix='fsj_secure_build_'))
    print(f"   临时构建目录: {temp_dir}")
    
    try:
        # 复制必要文件到临时目录
        essential_dirs = ['core', 'ui', 'utils', 'plugins', 'config', 'images', 'lib', 'gui']
        essential_files = ['main.py']
        
        for dir_name in essential_dirs:
            src_dir = paths['project_root'] / dir_name
            if src_dir.exists():
                dst_dir = temp_dir / dir_name
                shutil.copytree(src_dir, dst_dir, ignore=shutil.ignore_patterns(
                    '*.pyc', '__pycache__', '*.log', '*.tmp', 'test_*', '*_test.py'
                ))
                print(f"   ✅ 复制目录: {dir_name}")
        
        for file_name in essential_files:
            src_file = paths['project_root'] / file_name
            if src_file.exists():
                shutil.copy2(src_file, temp_dir / file_name)
                print(f"   ✅ 复制文件: {file_name}")
        
        # 复制打包配置
        packaging_dir = temp_dir / 'packaging'
        packaging_dir.mkdir(exist_ok=True)
        shutil.copytree(paths['packaging_root'] / 'config', packaging_dir / 'config')
        shutil.copy2(paths['spec_file'], packaging_dir / 'build_secure.spec')
        
        return temp_dir
        
    except Exception as e:
        print(f"❌ 创建安全构建环境失败: {e}")
        shutil.rmtree(temp_dir, ignore_errors=True)
        return None

def run_secure_build(temp_dir, paths, version):
    """运行安全构建"""
    print("🔨 开始安全构建...")
    
    try:
        # 切换到临时目录
        original_cwd = os.getcwd()
        os.chdir(temp_dir)
        
        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONPATH'] = str(temp_dir)
        
        # 构建命令
        spec_file = temp_dir / 'packaging' / 'build_secure.spec'
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--clean',  # 清理缓存
            '--noconfirm',  # 不询问覆盖
            '--log-level', 'INFO',
            str(spec_file)
        ]
        
        print(f"   执行命令: {' '.join(cmd)}")
        
        # 执行构建
        result = subprocess.run(cmd, env=env, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("   ✅ 构建成功!")
            return True
        else:
            print(f"   ❌ 构建失败:")
            print(f"   错误输出: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False
    finally:
        os.chdir(original_cwd)

def copy_secure_build_result(temp_dir, paths, version):
    """复制安全构建结果"""
    print("📦 复制构建结果...")
    
    try:
        # 查找构建结果
        dist_dir = temp_dir / 'dist'
        exe_name = f'FSJ04832_RegisterTool_v{version}_Release.exe'
        exe_file = dist_dir / exe_name
        
        if not exe_file.exists():
            print(f"❌ 找不到构建结果: {exe_file}")
            return False
        
        # 创建发布目录
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        release_dir = paths['project_root'] / 'releases' / f'{timestamp}_v{version}_Release'
        release_dir.mkdir(parents=True, exist_ok=True)
        
        # 复制exe文件
        target_exe = release_dir / exe_name
        shutil.copy2(exe_file, target_exe)
        
        # 创建版本信息文件
        version_info = {
            'version': version,
            'build_type': 'Release',
            'build_time': timestamp,
            'exe_name': exe_name,
            'security_features': [
                'Single file executable',
                'Code obfuscation',
                'UPX compression',
                'Debug info stripped',
                'Minimal file exposure'
            ]
        }
        
        version_file = release_dir / 'version_info.json'
        with open(version_file, 'w', encoding='utf-8') as f:
            json.dump(version_info, f, indent=2, ensure_ascii=False)
        
        # 更新latest链接
        latest_dir = paths['project_root'] / 'releases' / 'latest'
        if latest_dir.exists():
            shutil.rmtree(latest_dir)
        shutil.copytree(release_dir, latest_dir)
        
        print(f"   ✅ 构建结果已保存到: {release_dir}")
        print(f"   📁 可执行文件: {target_exe}")
        print(f"   📄 版本信息: {version_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 复制构建结果失败: {e}")
        return False

def cleanup_temp_dir(temp_dir):
    """清理临时目录"""
    if temp_dir and temp_dir.exists():
        try:
            shutil.rmtree(temp_dir)
            print(f"🧹 已清理临时目录: {temp_dir}")
        except Exception as e:
            print(f"警告: 清理临时目录失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("🔒 FSJ04832 安全打包工具")
    print("=" * 60)
    print()
    
    # 设置路径
    paths = setup_paths()
    
    # 加载版本信息
    version = load_version_info(paths['packaging_root'])
    print(f"📋 当前版本: {version}")
    print()
    
    temp_dir = None
    try:
        # 创建安全构建环境
        temp_dir = create_secure_build_environment(paths)
        if not temp_dir:
            return False
        
        print()
        
        # 运行安全构建
        if not run_secure_build(temp_dir, paths, version):
            return False
        
        print()
        
        # 复制构建结果
        if not copy_secure_build_result(temp_dir, paths, version):
            return False
        
        print()
        print("🎉 安全打包完成!")
        print(f"📦 客户发布版本已生成: FSJ04832_RegisterTool_v{version}_Release.exe")
        print()
        print("🔒 安全特性:")
        print("   ✅ 单文件可执行程序")
        print("   ✅ 代码混淆保护")
        print("   ✅ UPX压缩")
        print("   ✅ 调试信息移除")
        print("   ✅ 最小文件暴露")
        
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        return False
    except Exception as e:
        print(f"❌ 安全打包失败: {e}")
        return False
    finally:
        cleanup_temp_dir(temp_dir)

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
