#!/usr/bin/env python3
"""
演示COM端口布局改进效果
展示最终的布局优化结果
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QLabel
from PyQt5.QtCore import Qt

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.Log import get_module_logger

logger = get_module_logger(__name__)


def create_demo_window():
    """创建演示窗口"""
    try:
        print("🎯 COM端口布局改进演示")
        print("=" * 50)
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("COM端口布局改进演示")
        main_window.resize(1400, 300)  # 更宽的窗口以展示改进效果
        
        # 创建中央控件
        central_widget = QWidget()
        main_window.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 添加说明标签
        title_label = QLabel("COM端口布局改进演示")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2E86AB;
                padding: 10px;
                background-color: #F0F8FF;
                border: 2px solid #E0E0E0;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # 创建现代化RegisterIOHandler
        print("创建现代化RegisterIOHandler...")
        from ui.handlers.ModernRegisterIOHandler import ModernRegisterIOHandler
        io_handler = ModernRegisterIOHandler.create_for_testing(main_window)
        
        # 模拟真实的端口数据
        demo_ports = [
            {'device': 'COM3', 'description': 'USB Serial Port (COM3) - Silicon Labs CP210x USB to UART Bridge'},
            {'device': 'COM4', 'description': 'Bluetooth Serial Port (COM4) - Standard Serial over Bluetooth link'},
            {'device': 'COM5', 'description': 'Virtual Serial Port (COM5) - USB-SERIAL CH340 (COM5)'},
            {'device': 'COM6', 'description': 'Arduino Uno (COM6) - USB Serial Device'}
        ]
        
        # 更新端口列表
        io_handler._update_port_combo(demo_ports)
        
        # 设置一些示例数据
        io_handler.set_address_display(0x57)
        io_handler.set_value_display(0x1300)
        
        # 获取IO控件并添加到布局
        io_widget = io_handler.get_io_widget()
        layout.addWidget(io_widget)
        
        # 添加改进说明
        improvements_label = QLabel("""
        <h3>🎯 布局改进效果：</h3>
        <ul>
        <li><b>COM端口下拉框</b>：宽度从400px增加到600-800px，能完整显示端口信息</li>
        <li><b>地址输入框</b>：宽度从140px增加到160px，居中显示</li>
        <li><b>值输入框</b>：宽度从180px增加到200px，居中显示，绿色边框</li>
        <li><b>间隔优化</b>：刷新按钮和Address标签间隔25px，地址和值之间间隔20px</li>
        <li><b>整体右移</b>：添加20px左侧间隔，布局更平衡</li>
        <li><b>样式美化</b>：输入框有圆角边框，标签加粗，视觉效果更佳</li>
        </ul>
        """)
        improvements_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                padding: 15px;
                background-color: #F9F9F9;
                border: 1px solid #DDD;
                border-radius: 5px;
                margin-top: 10px;
            }
        """)
        layout.addWidget(improvements_label)
        
        print("✅ 演示窗口创建完成")
        print("\n请观察以下改进效果：")
        print("1. COM端口下拉框现在足够宽，能完整显示端口描述")
        print("2. 地址和值输入框居中显示，有美观的边框")
        print("3. 控件之间的间隔更合理")
        print("4. 整体布局向右移动，更加平衡")
        
        # 显示窗口
        main_window.show()
        
        return app, main_window
        
    except Exception as e:
        logger.error(f"创建演示窗口时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None


if __name__ == "__main__":
    app, window = create_demo_window()
    
    if app and window:
        print("\n🚀 演示窗口已打开，请查看布局改进效果")
        print("按 Ctrl+C 或关闭窗口退出演示")
        
        try:
            sys.exit(app.exec_())
        except KeyboardInterrupt:
            print("\n演示结束")
    else:
        print("❌ 演示窗口创建失败")
