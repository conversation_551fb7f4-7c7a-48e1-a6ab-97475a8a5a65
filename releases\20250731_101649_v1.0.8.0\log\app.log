2025-07-31 10:22:32,031 - main - [main.py:50] - INFO - 中文支持设置完成，使用字体: Microsoft YaHei
2025-07-31 10:22:32,032 - ConfigurationManager - [ConfigurationManager.py:59] - INFO - 已加载配置文件: C:\Users\<USER>\AppData\Local\Temp\_MEI86482\config\default.json
2025-07-31 10:22:32,032 - ConfigurationManager - [ConfigurationManager.py:85] - DEBUG - 跳过可选配置文件: app.json
2025-07-31 10:22:32,032 - ConfigurationManager - [ConfigurationManager.py:85] - DEBUG - 跳过可选配置文件: local.json
2025-07-31 10:22:32,032 - ConfigurationManager - [ConfigurationManager.py:87] - INFO - 配置加载完成，已加载文件: default.json
2025-07-31 10:22:32,032 - RegisterMainWindow - [RegisterMainWindow.py:178] - INFO - 配置加载完成
2025-07-31 10:22:32,033 - VersionService - [VersionService.py:75] - INFO - 成功加载版本文件: C:\Users\<USER>\AppData\Local\Temp\_MEI86482\packaging\config\version.json
2025-07-31 10:22:32,033 - RegisterMainWindow - [RegisterMainWindow.py:220] - INFO - 窗口标题已设置为: FSJ04832 寄存器配置工具 v1.0.8.0
2025-07-31 10:22:32,033 - RegisterMainWindow - [RegisterMainWindow.py:189] - INFO - 窗口大小设置为: 1840x1100
2025-07-31 10:22:32,033 - RegisterMainWindow - [RegisterMainWindow.py:206] - INFO - 操作常量已加载: TIMEOUT=5000ms, BATCH_SIZE=50
2025-07-31 10:22:32,033 - DIContainer - [DIContainer.py:46] - INFO - 注册单例实例: main_window
2025-07-31 10:22:32,040 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: spi_service
2025-07-31 10:22:32,040 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: register_manager
2025-07-31 10:22:32,040 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: event_bus
2025-07-31 10:22:32,040 - DIContainer - [DIContainer.py:181] - INFO - 核心服务配置完成
2025-07-31 10:22:32,056 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: initialization_manager
2025-07-31 10:22:32,056 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: register_operation_manager
2025-07-31 10:22:32,057 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: display_manager
2025-07-31 10:22:32,057 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: event_coordinator
2025-07-31 10:22:32,057 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: tool_window_factory
2025-07-31 10:22:32,057 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: register_update_processor
2025-07-31 10:22:32,057 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: lifecycle_manager
2025-07-31 10:22:32,057 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: ui_utility_manager
2025-07-31 10:22:32,057 - DIContainer - [DIContainer.py:210] - INFO - UI管理器服务配置完成
2025-07-31 10:22:32,059 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: batch_manager
2025-07-31 10:22:32,059 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: spi_coordinator
2025-07-31 10:22:32,059 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: ui_event_handler
2025-07-31 10:22:32,059 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: tool_window_manager
2025-07-31 10:22:32,059 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: status_config_manager
2025-07-31 10:22:32,059 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: resource_utility_manager
2025-07-31 10:22:32,059 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: tab_window_manager
2025-07-31 10:22:32,059 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: global_event_manager
2025-07-31 10:22:32,059 - DIContainer - [DIContainer.py:54] - INFO - 注册单例服务: spi_signal_manager
2025-07-31 10:22:32,059 - DIContainer - [DIContainer.py:243] - INFO - 额外管理器服务配置完成
2025-07-31 10:22:32,059 - RegisterMainWindow - [RegisterMainWindow.py:75] - INFO - 依赖注入容器设置完成
2025-07-31 10:22:32,059 - RegisterMainWindow - [RegisterMainWindow.py:93] - INFO - 管理器依赖注入设置完成
2025-07-31 10:22:32,060 - InitializationManager - [InitializationManager.py:31] - INFO - 正在初始化寄存器配置工具...
2025-07-31 10:22:32,062 - InitializationManager - [InitializationManager.py:40] - INFO - 成功加载寄存器配置文件，包含 125 个寄存器
2025-07-31 10:22:32,063 - spi_service - [spi_service.py:67] - INFO - SPI配置已加载: TIMEOUT=5000ms, RETRY_COUNT=3, RETRY_DELAY=1000ms
2025-07-31 10:22:32,063 - spi_service - [spi_service.py:54] - INFO - SPIService initialized
2025-07-31 10:22:32,063 - spi_service - [spi_service.py:84] - INFO - SPIService initializing...
2025-07-31 10:22:32,063 - spi_service - [spi_service.py:421] - DEBUG - Cleaning up SPI thread...
2025-07-31 10:22:32,063 - spi_service - [spi_service.py:455] - DEBUG - SPI thread resources cleaned.
2025-07-31 10:22:32,063 - spi_service_impl - [spi_service_impl.py:79] - INFO - 已启用模拟模式
2025-07-31 10:22:32,063 - spi_service - [spi_service.py:387] - DEBUG - SPI thread components initialized and signals connected.
2025-07-31 10:22:32,063 - spi_service - [spi_service.py:395] - INFO - Starting SPI thread...
2025-07-31 10:22:32,063 - spi_service - [spi_service.py:111] - INFO - Scanning for available COM ports during initialization...
2025-07-31 10:22:32,064 - spi_service_impl - [spi_service_impl.py:411] - INFO - SPI操作处理线程已启动
2025-07-31 10:22:32,070 - spi_service - [spi_service.py:119] - DEBUG - Found port during init: COM4 - USB 串行设备 (COM4)
2025-07-31 10:22:32,070 - spi_service - [spi_service.py:128] - INFO - Attempting to connect to first available port: COM4
2025-07-31 10:22:32,074 - port_manager - [port_manager.py:124] - INFO - 成功打开端口: COM4
2025-07-31 10:22:32,074 - spiPrivacy - [spiPrivacy.py:75] - INFO - 通过端口管理器成功打开串口: COM4
2025-07-31 10:22:32,074 - spi_service_impl - [spi_service_impl.py:347] - INFO - 成功连接到SPI端口: COM4
2025-07-31 10:22:32,074 - spi_service - [spi_service.py:134] - INFO - Successfully connected to COM4 during initialization. Using hardware mode.
2025-07-31 10:22:32,074 - spi_service_impl - [spi_service_impl.py:279] - INFO - SPI接口已连接并可用
2025-07-31 10:22:32,074 - spi_service_impl - [spi_service_impl.py:81] - INFO - 已禁用模拟模式，使用实际SPI硬件
2025-07-31 10:22:32,074 - spi_service - [spi_service.py:318] - INFO - Simulation mode disabled.
2025-07-31 10:22:32,074 - spi_service - [spi_service.py:357] - INFO - SPI连接状态已更新: 已连接
2025-07-31 10:22:32,074 - spi_service - [spi_service.py:96] - INFO - SPIService initialization complete.
2025-07-31 10:22:32,074 - InitializationManager - [InitializationManager.py:92] - INFO - SPI服务初始化成功，当前模式：硬件模式
2025-07-31 10:22:32,075 - RegisterOperationService - [RegisterOperationService.py:68] - INFO - 寄存器状态监控器初始化完成
2025-07-31 10:22:32,075 - RegisterUpdateBus - [RegisterUpdateBus.py:84] - INFO - RegisterUpdateBus: Initial clock source set based on register default (0x57, CLKin_SEL_MANUAL): ClkIn1
2025-07-31 10:22:32,075 - ModernBaseHandler - [ModernBaseHandler.py:79] - DEBUG - 成功连接到RegisterUpdateBus
2025-07-31 10:22:32,075 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:77] - INFO - 表格配置初始化完成
2025-07-31 10:22:32,335 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:142] - INFO - 位域表格创建完成
2025-07-31 10:22:32,335 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:102] - INFO - 表格UI创建完成
2025-07-31 10:22:32,335 - ModernBaseHandler - [ModernBaseHandler.py:452] - WARNING - _post_init方法已废弃，请使用新的初始化流程
2025-07-31 10:22:32,335 - ModernBaseHandler - [ModernBaseHandler.py:97] - INFO - ModernBaseHandler: 开始完成初始化
2025-07-31 10:22:32,335 - ModernBaseHandler - [ModernBaseHandler.py:112] - DEBUG - 此处理器不需要UI映射，跳过控件映射构建
2025-07-31 10:22:32,335 - ModernBaseHandler - [ModernBaseHandler.py:273] - INFO - ModernRegisterTableHandler: 窗口已居中显示在 (585, 190)，窗口尺寸: 750x700
2025-07-31 10:22:32,336 - ModernBaseHandler - [ModernBaseHandler.py:289] - DEBUG - ModernRegisterTableHandler: 已设置窗口激活功能
2025-07-31 10:22:32,336 - ModernBaseHandler - [ModernBaseHandler.py:242] - INFO - ModernRegisterTableHandler: 设置窗口默认尺寸为 750 x 700，已居中显示
2025-07-31 10:22:32,336 - ModernBaseHandler - [ModernBaseHandler.py:121] - INFO - ModernBaseHandler: 初始化完成
2025-07-31 10:22:32,336 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:51] - INFO - 现代化寄存器表格处理器初始化完成
2025-07-31 10:22:32,336 - ModernBaseHandler - [ModernBaseHandler.py:79] - DEBUG - 成功连接到RegisterUpdateBus
2025-07-31 10:22:32,337 - ModernBaseHandler - [ModernBaseHandler.py:452] - WARNING - _post_init方法已废弃，请使用新的初始化流程
2025-07-31 10:22:32,337 - ModernBaseHandler - [ModernBaseHandler.py:97] - INFO - ModernBaseHandler: 开始完成初始化
2025-07-31 10:22:32,337 - ModernBaseHandler - [ModernBaseHandler.py:112] - DEBUG - 此处理器不需要UI映射，跳过控件映射构建
2025-07-31 10:22:32,338 - ModernBaseHandler - [ModernBaseHandler.py:273] - INFO - ModernRegisterIOHandler: 窗口已居中显示在 (585, 190)，窗口尺寸: 750x700
2025-07-31 10:22:32,338 - ModernBaseHandler - [ModernBaseHandler.py:289] - DEBUG - ModernRegisterIOHandler: 已设置窗口激活功能
2025-07-31 10:22:32,338 - ModernBaseHandler - [ModernBaseHandler.py:242] - INFO - ModernRegisterIOHandler: 设置窗口默认尺寸为 750 x 700，已居中显示
2025-07-31 10:22:32,338 - ModernBaseHandler - [ModernBaseHandler.py:121] - INFO - ModernBaseHandler: 初始化完成
2025-07-31 10:22:32,339 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:78] - INFO - 现代化寄存器IO处理器初始化完成
2025-07-31 10:22:32,339 - ModernBaseHandler - [ModernBaseHandler.py:79] - DEBUG - 成功连接到RegisterUpdateBus
2025-07-31 10:22:32,340 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:82] - DEBUG - 现代化TreeWidget设置最小宽度: 250px，防止布局抖动
2025-07-31 10:22:32,340 - ModernBaseHandler - [ModernBaseHandler.py:452] - WARNING - _post_init方法已废弃，请使用新的初始化流程
2025-07-31 10:22:32,340 - ModernBaseHandler - [ModernBaseHandler.py:97] - INFO - ModernBaseHandler: 开始完成初始化
2025-07-31 10:22:32,340 - ModernBaseHandler - [ModernBaseHandler.py:112] - DEBUG - 此处理器不需要UI映射，跳过控件映射构建
2025-07-31 10:22:32,340 - ModernBaseHandler - [ModernBaseHandler.py:273] - INFO - ModernRegisterTreeHandler: 窗口已居中显示在 (585, 190)，窗口尺寸: 750x700
2025-07-31 10:22:32,340 - ModernBaseHandler - [ModernBaseHandler.py:289] - DEBUG - ModernRegisterTreeHandler: 已设置窗口激活功能
2025-07-31 10:22:32,340 - ModernBaseHandler - [ModernBaseHandler.py:242] - INFO - ModernRegisterTreeHandler: 设置窗口默认尺寸为 750 x 700，已居中显示
2025-07-31 10:22:32,340 - ModernBaseHandler - [ModernBaseHandler.py:121] - INFO - ModernBaseHandler: 初始化完成
2025-07-31 10:22:32,340 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:51] - INFO - 现代化寄存器树处理器初始化完成
2025-07-31 10:22:32,340 - InitializationManager - [InitializationManager.py:145] - INFO - 现代化处理器创建成功
2025-07-31 10:22:32,340 - InitializationManager - [InitializationManager.py:113] - INFO - 使用现代化处理器创建核心处理器
2025-07-31 10:22:32,340 - BatchOperationManager - [BatchOperationManager.py:100] - INFO - 批量操作配置已加载: BATCH_SIZE=50, TIMEOUT=5000ms, UI_UPDATE_INTERVAL=10
2025-07-31 10:22:32,340 - BatchOperationManager - [BatchOperationManager.py:87] - INFO - 使用传统批量操作方式（已优化性能）
2025-07-31 10:22:32,340 - SPIOperationCoordinator - [SPIOperationCoordinator.py:301] - DEBUG - 已连接SPI操作完成信号
2025-07-31 10:22:32,340 - InitializationManager - [InitializationManager.py:182] - INFO - 使用依赖注入创建管理器完成
2025-07-31 10:22:32,617 - MainWindowUI - [MainWindowUI.py:137] - INFO - MainWindowUI: 检查现代化处理器 - IO: True, Table: True
2025-07-31 10:22:32,617 - MainWindowUI - [MainWindowUI.py:138] - INFO - MainWindowUI: IO处理器类型: <class 'ui.handlers.ModernRegisterIOHandler.ModernRegisterIOHandler'>
2025-07-31 10:22:32,617 - MainWindowUI - [MainWindowUI.py:139] - INFO - MainWindowUI: Table处理器类型: <class 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler'>
2025-07-31 10:22:32,617 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:741] - INFO - ModernRegisterIOHandler: get_io_widget() 被调用
2025-07-31 10:22:32,619 - MainWindowUI - [MainWindowUI.py:69] - INFO - MainWindowUI: 使用QSplitter创建稳定的TreeWidget和TableWidget分割布局
2025-07-31 10:22:32,622 - ProgressBarStyleManager - [ProgressBarStyleManager.py:135] - DEBUG - 已为进度条应用绿色样式: default
2025-07-31 10:22:32,624 - MenuManager - [MenuManager.py:350] - INFO - 插件服务尚未可用，跳过工具插件集成（将在插件系统设置完成后执行）
2025-07-31 10:22:32,626 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:129] - INFO - 填充了 125 个寄存器到树视图
2025-07-31 10:22:32,627 - RegisterUpdateBus - [RegisterUpdateBus.py:372] - INFO - 从寄存器0x57初始化时钟源: 寄存器值=0x001A, CLKin_SEL_MANUAL=1, 时钟源=ClkIn1 (原值: ClkIn1)
2025-07-31 10:22:32,627 - RegisterUpdateBus - [RegisterUpdateBus.py:394] - DEBUG - 从寄存器0x63更新CLKin0分频值: 120
2025-07-31 10:22:32,627 - RegisterUpdateBus - [RegisterUpdateBus.py:402] - DEBUG - 从寄存器0x65更新CLKin1分频值: 120
2025-07-31 10:22:32,627 - RegisterUpdateBus - [RegisterUpdateBus.py:410] - DEBUG - 从寄存器0x67更新CLKin2分频值: 150
2025-07-31 10:22:32,627 - InitializationManager - [InitializationManager.py:248] - INFO - RegisterUpdateBus时钟源缓存已从寄存器管理器初始化
2025-07-31 10:22:32,627 - SPISignalManager - [SPISignalManager.py:41] - DEBUG - SPI信号连接完成
2025-07-31 10:22:32,627 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:165] - INFO - 现代化TreeHandler: 选择默认寄存器: 'R0 (0x00)'
2025-07-31 10:22:32,628 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:177] - INFO - 现代化TreeHandler: 树项点击 - 项: 'R0 (0x00)', 地址: 0x00, 列: 0
2025-07-31 10:22:32,628 - ModernRegisterTreeHandler - [ModernRegisterTreeHandler.py:186] - INFO - 现代化TreeHandler: 选择寄存器地址: 0x00 从项 'R0 (0x00)'
2025-07-31 10:22:32,628 - EventCoordinator - [EventCoordinator.py:169] - DEBUG - EventCoordinator: 开始处理寄存器选择 0x00
2025-07-31 10:22:32,628 - EventCoordinator - [EventCoordinator.py:172] - DEBUG - EventCoordinator: 找到显示管理器，调用 handle_register_selection(0x00)
2025-07-31 10:22:32,628 - RegisterDisplayManager - [RegisterDisplayManager.py:209] - DEBUG - DisplayManager: 开始处理寄存器选择 0x00
2025-07-31 10:22:32,628 - RegisterOperationService - [RegisterOperationService.py:605] - INFO - RegisterOperationService: 处理寄存器选择, addr='0x00'
2025-07-31 10:22:32,629 - RegisterOperationService - [RegisterOperationService.py:419] - INFO - RegisterOperationService: 更新UI显示 - 地址: 0x00, 值: 0x1300
2025-07-31 10:22:32,629 - RegisterOperationService - [RegisterOperationService.py:427] - DEBUG - RegisterOperationService: 跳过表格更新，避免重复调用 - 0x00
2025-07-31 10:22:32,630 - RegisterDisplayManager - [RegisterDisplayManager.py:213] - DEBUG - DisplayManager: 寄存器操作服务返回: success=True, reg_num=0, value=0x1300
2025-07-31 10:22:32,630 - RegisterDisplayManager - [RegisterDisplayManager.py:218] - INFO - DisplayManager: 已更新主窗口选中寄存器地址为: 0x00
2025-07-31 10:22:32,630 - RegisterDisplayManager - [RegisterDisplayManager.py:71] - INFO - DisplayManager: 开始更新位字段显示 - 地址: 0x00, 值: 0x1300
2025-07-31 10:22:32,630 - RegisterDisplayManager - [RegisterDisplayManager.py:75] - INFO - DisplayManager: 找到 table_handler: <class 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler'>
2025-07-31 10:22:32,630 - RegisterDisplayManager - [RegisterDisplayManager.py:76] - INFO - DisplayManager: 调用 table_handler.show_bit_fields(0x00, 4864, from_global_update=True)
2025-07-31 10:22:32,630 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:817] - DEBUG - ModernTableHandler: 尝试获取主窗口实例...
2025-07-31 10:22:32,630 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:818] - DEBUG - ModernTableHandler: self类型: <class 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler'>
2025-07-31 10:22:32,630 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:819] - DEBUG - ModernTableHandler: hasattr(self, 'main_window'): False
2025-07-31 10:22:32,630 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:820] - DEBUG - ModernTableHandler: hasattr(self, 'parent'): True
2025-07-31 10:22:32,630 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:829] - DEBUG - ModernTableHandler: parent类型: <class 'ui.windows.RegisterMainWindow.RegisterMainWindow'>
2025-07-31 10:22:32,630 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:836] - DEBUG - ModernTableHandler: parent本身就是主窗口
2025-07-31 10:22:32,630 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:233] - INFO - ModernTableHandler: 开始显示位域信息 - 地址: 0x00, 值: 0x1300
2025-07-31 10:22:32,630 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:246] - INFO - ModernTableHandler: 设置当前寄存器 - 地址: 0x00, 值: 0x1300
2025-07-31 10:22:32,630 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:251] - INFO - ModernTableHandler: 获取到 1 个位域
2025-07-31 10:22:32,630 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:265] - DEBUG - ModernTableHandler: 断开表格信号连接
2025-07-31 10:22:32,630 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:269] - INFO - ModernTableHandler: 开始更新表格内容
2025-07-31 10:22:32,630 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:817] - DEBUG - ModernTableHandler: 尝试获取主窗口实例...
2025-07-31 10:22:32,630 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:818] - DEBUG - ModernTableHandler: self类型: <class 'ui.handlers.ModernRegisterTableHandler.ModernRegisterTableHandler'>
2025-07-31 10:22:32,630 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:819] - DEBUG - ModernTableHandler: hasattr(self, 'main_window'): False
2025-07-31 10:22:32,631 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:820] - DEBUG - ModernTableHandler: hasattr(self, 'parent'): True
2025-07-31 10:22:32,631 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:829] - DEBUG - ModernTableHandler: parent类型: <class 'ui.windows.RegisterMainWindow.RegisterMainWindow'>
2025-07-31 10:22:32,631 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:836] - DEBUG - ModernTableHandler: parent本身就是主窗口
2025-07-31 10:22:32,631 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:273] - DEBUG - ModernTableHandler: 重新连接表格信号
2025-07-31 10:22:32,631 - ModernRegisterTableHandler - [ModernRegisterTableHandler.py:280] - INFO - ModernTableHandler: 成功显示寄存器 0x00 的 1 个位域
2025-07-31 10:22:32,631 - RegisterDisplayManager - [RegisterDisplayManager.py:78] - INFO - DisplayManager: 位字段显示更新完成
2025-07-31 10:22:32,631 - EventCoordinator - [EventCoordinator.py:174] - DEBUG - EventCoordinator: 显示管理器返回结果: True
2025-07-31 10:22:32,631 - InitializationManager - [InitializationManager.py:339] - INFO - 主窗口初始化完成
2025-07-31 10:22:32,631 - RegisterMainWindow - [RegisterMainWindow.py:253] - INFO - 主窗口点击置顶功能已设置
2025-07-31 10:22:32,634 - DIContainer - [DIContainer.py:46] - INFO - 注册单例实例: plugin_service
2025-07-31 10:22:32,635 - PluginManager - [PluginManager.py:151] - INFO - 添加插件目录: C:\Users\<USER>\AppData\Local\Temp\_MEI86482\plugins
2025-07-31 10:22:32,635 - PluginManager - [PluginManager.py:154] - DEBUG - 跳过不存在的插件目录: _internal/plugins (尝试了 3 个路径)
2025-07-31 10:22:32,635 - PluginManager - [PluginManager.py:154] - DEBUG - 跳过不存在的插件目录: ui/tools (尝试了 3 个路径)
2025-07-31 10:22:32,636 - PluginManager - [PluginManager.py:154] - DEBUG - 跳过不存在的插件目录: _internal/ui/tools (尝试了 3 个路径)
2025-07-31 10:22:32,636 - PluginManager - [PluginManager.py:209] - DEBUG - 尝试加载插件模块: plugins.clkin_control_plugin (文件: C:\Users\<USER>\AppData\Local\Temp\_MEI86482\plugins\clkin_control_plugin.py)
2025-07-31 10:22:32,637 - PluginManager - [PluginManager.py:239] - DEBUG - 成功导入模块: plugins.clkin_control_plugin
2025-07-31 10:22:32,637 - PluginManager - [PluginManager.py:267] - INFO - 发现插件: 时钟输入控制 v1.0.0
2025-07-31 10:22:32,637 - PluginManager - [PluginManager.py:209] - DEBUG - 尝试加载插件模块: plugins.clk_output_plugin (文件: C:\Users\<USER>\AppData\Local\Temp\_MEI86482\plugins\clk_output_plugin.py)
2025-07-31 10:22:32,638 - PluginManager - [PluginManager.py:239] - DEBUG - 成功导入模块: plugins.clk_output_plugin
2025-07-31 10:22:32,638 - PluginManager - [PluginManager.py:267] - INFO - 发现插件: 时钟输出 v1.0.0
2025-07-31 10:22:32,638 - PluginManager - [PluginManager.py:209] - DEBUG - 尝试加载插件模块: plugins.data_analysis_plugin (文件: C:\Users\<USER>\AppData\Local\Temp\_MEI86482\plugins\data_analysis_plugin.py)
2025-07-31 10:22:32,639 - PluginManager - [PluginManager.py:239] - DEBUG - 成功导入模块: plugins.data_analysis_plugin
2025-07-31 10:22:32,639 - PluginManager - [PluginManager.py:267] - INFO - 发现插件: 数据分析器 v1.0.0
2025-07-31 10:22:32,639 - PluginManager - [PluginManager.py:209] - DEBUG - 尝试加载插件模块: plugins.example_tool_plugin (文件: C:\Users\<USER>\AppData\Local\Temp\_MEI86482\plugins\example_tool_plugin.py)
2025-07-31 10:22:32,641 - PluginManager - [PluginManager.py:239] - DEBUG - 成功导入模块: plugins.example_tool_plugin
2025-07-31 10:22:32,641 - PluginManager - [PluginManager.py:267] - INFO - 发现插件: 示例工具 v1.0.0
2025-07-31 10:22:32,641 - PluginManager - [PluginManager.py:209] - DEBUG - 尝试加载插件模块: plugins.performance_monitor_plugin (文件: C:\Users\<USER>\AppData\Local\Temp\_MEI86482\plugins\performance_monitor_plugin.py)
2025-07-31 10:22:32,673 - performance_monitor_plugin - [performance_monitor_plugin.py:28] - INFO - psutil库可用，将提供完整的系统监控功能
2025-07-31 10:22:32,673 - PluginManager - [PluginManager.py:239] - DEBUG - 成功导入模块: plugins.performance_monitor_plugin
2025-07-31 10:22:32,673 - PluginManager - [PluginManager.py:267] - INFO - 发现插件: 性能监控器 v1.0.0
2025-07-31 10:22:32,673 - PluginManager - [PluginManager.py:209] - DEBUG - 尝试加载插件模块: plugins.pll_control_plugin (文件: C:\Users\<USER>\AppData\Local\Temp\_MEI86482\plugins\pll_control_plugin.py)
2025-07-31 10:22:32,674 - PluginManager - [PluginManager.py:239] - DEBUG - 成功导入模块: plugins.pll_control_plugin
2025-07-31 10:22:32,674 - PluginManager - [PluginManager.py:267] - INFO - 发现插件: PLL控制 v1.0.0
2025-07-31 10:22:32,674 - PluginManager - [PluginManager.py:209] - DEBUG - 尝试加载插件模块: plugins.selective_register_plugin (文件: C:\Users\<USER>\AppData\Local\Temp\_MEI86482\plugins\selective_register_plugin.py)
2025-07-31 10:22:32,676 - PluginManager - [PluginManager.py:239] - DEBUG - 成功导入模块: plugins.selective_register_plugin
2025-07-31 10:22:32,676 - selective_register_config - [selective_register_config.py:45] - WARNING - 配置文件不存在: C:\Users\<USER>\AppData\Local\Temp\_MEI86482\config\selective_register_plugin.json，使用默认配置
2025-07-31 10:22:32,676 - PluginManager - [PluginManager.py:267] - INFO - 发现插件: 选择性寄存器操作 v1.0.1
2025-07-31 10:22:32,676 - PluginManager - [PluginManager.py:209] - DEBUG - 尝试加载插件模块: plugins.set_modes_plugin (文件: C:\Users\<USER>\AppData\Local\Temp\_MEI86482\plugins\set_modes_plugin.py)
2025-07-31 10:22:32,677 - PluginManager - [PluginManager.py:239] - DEBUG - 成功导入模块: plugins.set_modes_plugin
2025-07-31 10:22:32,677 - PluginManager - [PluginManager.py:267] - INFO - 发现插件: 模式设置 v1.0.0
2025-07-31 10:22:32,677 - PluginManager - [PluginManager.py:209] - DEBUG - 尝试加载插件模块: plugins.sync_sysref_plugin (文件: C:\Users\<USER>\AppData\Local\Temp\_MEI86482\plugins\sync_sysref_plugin.py)
2025-07-31 10:22:32,677 - PluginManager - [PluginManager.py:239] - DEBUG - 成功导入模块: plugins.sync_sysref_plugin
2025-07-31 10:22:32,678 - PluginManager - [PluginManager.py:267] - INFO - 发现插件: 同步系统参考 v1.0.0
2025-07-31 10:22:32,678 - PluginManager - [PluginManager.py:209] - DEBUG - 尝试加载插件模块: plugins.config.selective_register_config (文件: C:\Users\<USER>\AppData\Local\Temp\_MEI86482\plugins\config\selective_register_config.py)
2025-07-31 10:22:32,678 - PluginManager - [PluginManager.py:239] - DEBUG - 成功导入模块: plugins.config.selective_register_config
2025-07-31 10:22:32,678 - clkin_control_plugin - [clkin_control_plugin.py:59] - INFO - 插件 '时钟输入控制' 初始化完成
2025-07-31 10:22:32,678 - PluginManager - [PluginManager.py:310] - INFO - 插件初始化成功: 时钟输入控制
2025-07-31 10:22:32,678 - clk_output_plugin - [clk_output_plugin.py:59] - INFO - 插件 '时钟输出' 初始化完成
2025-07-31 10:22:32,678 - PluginManager - [PluginManager.py:310] - INFO - 插件初始化成功: 时钟输出
2025-07-31 10:22:32,679 - data_analysis_plugin - [data_analysis_plugin.py:712] - INFO - 插件 '数据分析器' 初始化完成
2025-07-31 10:22:32,679 - PluginManager - [PluginManager.py:310] - INFO - 插件初始化成功: 数据分析器
2025-07-31 10:22:32,679 - example_tool_plugin - [example_tool_plugin.py:308] - INFO - 插件 '示例工具' 初始化完成
2025-07-31 10:22:32,679 - PluginManager - [PluginManager.py:310] - INFO - 插件初始化成功: 示例工具
2025-07-31 10:22:32,679 - performance_monitor_plugin - [performance_monitor_plugin.py:835] - INFO - 插件 '性能监控器' 初始化完成
2025-07-31 10:22:32,679 - performance_monitor_plugin - [performance_monitor_plugin.py:854] - DEBUG - 已连接批量读取开始信号
2025-07-31 10:22:32,679 - performance_monitor_plugin - [performance_monitor_plugin.py:857] - DEBUG - 已连接批量读取完成信号
2025-07-31 10:22:32,679 - performance_monitor_plugin - [performance_monitor_plugin.py:860] - DEBUG - 已连接批量读取进度信号
2025-07-31 10:22:32,679 - performance_monitor_plugin - [performance_monitor_plugin.py:865] - DEBUG - 已连接批量写入开始信号
2025-07-31 10:22:32,679 - performance_monitor_plugin - [performance_monitor_plugin.py:868] - DEBUG - 已连接批量写入完成信号
2025-07-31 10:22:32,679 - performance_monitor_plugin - [performance_monitor_plugin.py:871] - DEBUG - 已连接批量写入进度信号
2025-07-31 10:22:32,679 - performance_monitor_plugin - [performance_monitor_plugin.py:874] - INFO - ✅ 已成功连接到批量操作管理器，性能监控功能已激活
2025-07-31 10:22:32,679 - PluginManager - [PluginManager.py:310] - INFO - 插件初始化成功: 性能监控器
2025-07-31 10:22:32,679 - pll_control_plugin - [pll_control_plugin.py:54] - DEBUG - PLLControlPlugin: 初始化时context是有效对象: RegisterMainWindow
2025-07-31 10:22:32,679 - pll_control_plugin - [pll_control_plugin.py:64] - INFO - 插件 'PLL控制' 初始化完成
2025-07-31 10:22:32,679 - PluginManager - [PluginManager.py:310] - INFO - 插件初始化成功: PLL控制
2025-07-31 10:22:32,679 - selective_register_plugin - [selective_register_plugin.py:977] - INFO - 插件 '选择性寄存器操作' 初始化完成
2025-07-31 10:22:32,679 - PluginManager - [PluginManager.py:310] - INFO - 插件初始化成功: 选择性寄存器操作
2025-07-31 10:22:32,679 - set_modes_plugin - [set_modes_plugin.py:59] - INFO - 插件 '模式设置' 初始化完成
2025-07-31 10:22:32,679 - PluginManager - [PluginManager.py:310] - INFO - 插件初始化成功: 模式设置
2025-07-31 10:22:32,679 - sync_sysref_plugin - [sync_sysref_plugin.py:59] - INFO - 插件 '同步系统参考' 初始化完成
2025-07-31 10:22:32,679 - PluginManager - [PluginManager.py:310] - INFO - 插件初始化成功: 同步系统参考
2025-07-31 10:22:32,679 - PluginManager - [PluginManager.py:349] - DEBUG - 添加工具窗口插件: 时钟输入控制
2025-07-31 10:22:32,679 - PluginManager - [PluginManager.py:349] - DEBUG - 添加工具窗口插件: 时钟输出
2025-07-31 10:22:32,679 - PluginManager - [PluginManager.py:349] - DEBUG - 添加工具窗口插件: 数据分析器
2025-07-31 10:22:32,679 - PluginManager - [PluginManager.py:349] - DEBUG - 添加工具窗口插件: 示例工具
2025-07-31 10:22:32,680 - PluginManager - [PluginManager.py:349] - DEBUG - 添加工具窗口插件: 性能监控器
2025-07-31 10:22:32,680 - PluginManager - [PluginManager.py:349] - DEBUG - 添加工具窗口插件: PLL控制
2025-07-31 10:22:32,680 - PluginManager - [PluginManager.py:349] - DEBUG - 添加工具窗口插件: 选择性寄存器操作
2025-07-31 10:22:32,680 - PluginManager - [PluginManager.py:349] - DEBUG - 添加工具窗口插件: 模式设置
2025-07-31 10:22:32,680 - PluginManager - [PluginManager.py:349] - DEBUG - 添加工具窗口插件: 同步系统参考
2025-07-31 10:22:32,680 - PluginManager - [PluginManager.py:353] - INFO - 找到 9 个工具窗口插件
2025-07-31 10:22:32,680 - PluginMenuService - [PluginMenuService.py:157] - INFO - 找到现有的工具菜单
2025-07-31 10:22:32,680 - PluginMenuService - [PluginMenuService.py:216] - DEBUG - 为插件 '模式设置' 设置快捷键: Ctrl+M
2025-07-31 10:22:32,680 - PluginMenuService - [PluginMenuService.py:242] - INFO - 已将插件 '模式设置' 的动作设置为主窗口属性: set_modes_action
2025-07-31 10:22:32,680 - PluginMenuService - [PluginMenuService.py:253] - INFO - 插件 '模式设置' 已添加到菜单
2025-07-31 10:22:32,680 - MenuClickFixer - [MenuClickFixer.py:43] - INFO - 开始修复动作点击响应: 模式设置(&M)
2025-07-31 10:22:32,681 - MenuClickFixer - [MenuClickFixer.py:80] - INFO - 动作点击响应修复完成: 模式设置(&M)
2025-07-31 10:22:32,681 - PluginMenuService - [PluginMenuService.py:265] - INFO - 已为插件 '模式设置' 应用菜单点击修复
2025-07-31 10:22:32,681 - PluginMenuService - [PluginMenuService.py:72] - INFO - 核心工具插件 '模式设置' 已添加到工具菜单
2025-07-31 10:22:32,681 - PluginMenuService - [PluginMenuService.py:216] - DEBUG - 为插件 '时钟输入控制' 设置快捷键: Ctrl+I
2025-07-31 10:22:32,681 - PluginMenuService - [PluginMenuService.py:242] - INFO - 已将插件 '时钟输入控制' 的动作设置为主窗口属性: clkin_control_action
2025-07-31 10:22:32,681 - PluginMenuService - [PluginMenuService.py:253] - INFO - 插件 '时钟输入控制' 已添加到菜单
2025-07-31 10:22:32,681 - MenuClickFixer - [MenuClickFixer.py:43] - INFO - 开始修复动作点击响应: 时钟输入(&I)
2025-07-31 10:22:32,681 - MenuClickFixer - [MenuClickFixer.py:80] - INFO - 动作点击响应修复完成: 时钟输入(&I)
2025-07-31 10:22:32,681 - PluginMenuService - [PluginMenuService.py:265] - INFO - 已为插件 '时钟输入控制' 应用菜单点击修复
2025-07-31 10:22:32,681 - PluginMenuService - [PluginMenuService.py:72] - INFO - 核心工具插件 '时钟输入控制' 已添加到工具菜单
2025-07-31 10:22:32,681 - PluginMenuService - [PluginMenuService.py:216] - DEBUG - 为插件 'PLL控制' 设置快捷键: Ctrl+P
2025-07-31 10:22:32,681 - PluginMenuService - [PluginMenuService.py:242] - INFO - 已将插件 'PLL控制' 的动作设置为主窗口属性: pll_control_action
2025-07-31 10:22:32,681 - PluginMenuService - [PluginMenuService.py:253] - INFO - 插件 'PLL控制' 已添加到菜单
2025-07-31 10:22:32,681 - MenuClickFixer - [MenuClickFixer.py:43] - INFO - 开始修复动作点击响应: PLL控制(&P)
2025-07-31 10:22:32,682 - MenuClickFixer - [MenuClickFixer.py:80] - INFO - 动作点击响应修复完成: PLL控制(&P)
2025-07-31 10:22:32,682 - PluginMenuService - [PluginMenuService.py:265] - INFO - 已为插件 'PLL控制' 应用菜单点击修复
2025-07-31 10:22:32,682 - PluginMenuService - [PluginMenuService.py:72] - INFO - 核心工具插件 'PLL控制' 已添加到工具菜单
2025-07-31 10:22:32,682 - PluginMenuService - [PluginMenuService.py:216] - DEBUG - 为插件 '同步系统参考' 设置快捷键: Ctrl+R
2025-07-31 10:22:32,682 - PluginMenuService - [PluginMenuService.py:242] - INFO - 已将插件 '同步系统参考' 的动作设置为主窗口属性: sync_sysref_action
2025-07-31 10:22:32,682 - PluginMenuService - [PluginMenuService.py:253] - INFO - 插件 '同步系统参考' 已添加到菜单
2025-07-31 10:22:32,682 - MenuClickFixer - [MenuClickFixer.py:43] - INFO - 开始修复动作点击响应: 同步系统参考(&R)
2025-07-31 10:22:32,682 - MenuClickFixer - [MenuClickFixer.py:80] - INFO - 动作点击响应修复完成: 同步系统参考(&R)
2025-07-31 10:22:32,682 - PluginMenuService - [PluginMenuService.py:265] - INFO - 已为插件 '同步系统参考' 应用菜单点击修复
2025-07-31 10:22:32,682 - PluginMenuService - [PluginMenuService.py:72] - INFO - 核心工具插件 '同步系统参考' 已添加到工具菜单
2025-07-31 10:22:32,682 - PluginMenuService - [PluginMenuService.py:216] - DEBUG - 为插件 '时钟输出' 设置快捷键: Ctrl+O
2025-07-31 10:22:32,682 - PluginMenuService - [PluginMenuService.py:242] - INFO - 已将插件 '时钟输出' 的动作设置为主窗口属性: clk_output_action
2025-07-31 10:22:32,682 - PluginMenuService - [PluginMenuService.py:253] - INFO - 插件 '时钟输出' 已添加到菜单
2025-07-31 10:22:32,682 - MenuClickFixer - [MenuClickFixer.py:43] - INFO - 开始修复动作点击响应: 时钟输出(&O)
2025-07-31 10:22:32,682 - MenuClickFixer - [MenuClickFixer.py:80] - INFO - 动作点击响应修复完成: 时钟输出(&O)
2025-07-31 10:22:32,682 - PluginMenuService - [PluginMenuService.py:265] - INFO - 已为插件 '时钟输出' 应用菜单点击修复
2025-07-31 10:22:32,682 - PluginMenuService - [PluginMenuService.py:72] - INFO - 核心工具插件 '时钟输出' 已添加到工具菜单
2025-07-31 10:22:32,683 - PluginMenuService - [PluginMenuService.py:364] - DEBUG - 已将动作 '模式设置(&M)' 添加到工具栏
2025-07-31 10:22:32,684 - PluginMenuService - [PluginMenuService.py:364] - DEBUG - 已将动作 '时钟输入(&I)' 添加到工具栏
2025-07-31 10:22:32,684 - PluginMenuService - [PluginMenuService.py:364] - DEBUG - 已将动作 'PLL控制(&P)' 添加到工具栏
2025-07-31 10:22:32,684 - PluginMenuService - [PluginMenuService.py:364] - DEBUG - 已将动作 '同步系统参考(&R)' 添加到工具栏
2025-07-31 10:22:32,684 - PluginMenuService - [PluginMenuService.py:364] - DEBUG - 已将动作 '时钟输出(&O)' 添加到工具栏
2025-07-31 10:22:32,684 - PluginMenuService - [PluginMenuService.py:366] - INFO - 已将 5 个动作添加到工具栏
2025-07-31 10:22:32,684 - PluginMenuService - [PluginMenuService.py:79] - INFO - 已将 5 个核心工具插件添加到工具栏
2025-07-31 10:22:32,684 - PluginMenuService - [PluginMenuService.py:81] - INFO - 已将 5 个核心工具插件添加到工具菜单和工具栏
2025-07-31 10:22:32,685 - PluginMenuService - [PluginMenuService.py:127] - INFO - 在设置菜单前创建新的插件菜单
2025-07-31 10:22:32,823 - PluginMenuService - [PluginMenuService.py:188] - INFO - 添加插件管理器菜单项
2025-07-31 10:22:32,823 - PluginMenuService - [PluginMenuService.py:144] - INFO - 插件菜单设置完成
2025-07-31 10:22:32,825 - PluginMenuService - [PluginMenuService.py:253] - INFO - 插件 '数据分析器' 已添加到菜单
2025-07-31 10:22:32,825 - MenuClickFixer - [MenuClickFixer.py:43] - INFO - 开始修复动作点击响应: 数据分析器
2025-07-31 10:22:32,825 - MenuClickFixer - [MenuClickFixer.py:80] - INFO - 动作点击响应修复完成: 数据分析器
2025-07-31 10:22:32,825 - PluginMenuService - [PluginMenuService.py:265] - INFO - 已为插件 '数据分析器' 应用菜单点击修复
2025-07-31 10:22:32,826 - PluginMenuService - [PluginMenuService.py:253] - INFO - 插件 '示例工具' 已添加到菜单
2025-07-31 10:22:32,826 - MenuClickFixer - [MenuClickFixer.py:43] - INFO - 开始修复动作点击响应: 示例工具
2025-07-31 10:22:32,826 - MenuClickFixer - [MenuClickFixer.py:80] - INFO - 动作点击响应修复完成: 示例工具
2025-07-31 10:22:32,826 - PluginMenuService - [PluginMenuService.py:265] - INFO - 已为插件 '示例工具' 应用菜单点击修复
2025-07-31 10:22:32,828 - PluginMenuService - [PluginMenuService.py:253] - INFO - 插件 '性能监控器' 已添加到菜单
2025-07-31 10:22:32,828 - MenuClickFixer - [MenuClickFixer.py:43] - INFO - 开始修复动作点击响应: 性能监控器
2025-07-31 10:22:32,828 - MenuClickFixer - [MenuClickFixer.py:80] - INFO - 动作点击响应修复完成: 性能监控器
2025-07-31 10:22:32,828 - PluginMenuService - [PluginMenuService.py:265] - INFO - 已为插件 '性能监控器' 应用菜单点击修复
2025-07-31 10:22:32,829 - PluginMenuService - [PluginMenuService.py:253] - INFO - 插件 '选择性寄存器操作' 已添加到菜单
2025-07-31 10:22:32,830 - MenuClickFixer - [MenuClickFixer.py:43] - INFO - 开始修复动作点击响应: 选择性寄存器操作
2025-07-31 10:22:32,830 - MenuClickFixer - [MenuClickFixer.py:80] - INFO - 动作点击响应修复完成: 选择性寄存器操作
2025-07-31 10:22:32,830 - PluginMenuService - [PluginMenuService.py:265] - INFO - 已为插件 '选择性寄存器操作' 应用菜单点击修复
2025-07-31 10:22:32,830 - PluginMenuService - [PluginMenuService.py:94] - INFO - 已将 4 个插件添加到插件菜单
2025-07-31 10:22:32,830 - PluginIntegrationService - [PluginIntegrationService.py:86] - INFO - 插件系统初始化完成
2025-07-31 10:22:32,830 - RegisterMainWindow - [RegisterMainWindow.py:140] - INFO - 插件系统设置完成
2025-07-31 10:22:32,831 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x00 = 0x1300
2025-07-31 10:22:32,831 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x02 = 0x0000
2025-07-31 10:22:32,831 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x03 = 0x0006
2025-07-31 10:22:32,831 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x04 = 0xD163
2025-07-31 10:22:32,831 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x05, 值=0000
2025-07-31 10:22:32,831 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x05 = 0x0000
2025-07-31 10:22:32,832 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x06 = 0x0070
2025-07-31 10:22:32,832 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x0C = 0x5104
2025-07-31 10:22:32,832 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x10, 值=0005
2025-07-31 10:22:32,832 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x10 = 0x0005
2025-07-31 10:22:32,832 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x11, 值=0002
2025-07-31 10:22:32,832 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x11 = 0x0002
2025-07-31 10:22:32,832 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x12, 值=0010
2025-07-31 10:22:32,832 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x12 = 0x0010
2025-07-31 10:22:32,832 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x13 = 0x0000
2025-07-31 10:22:32,832 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x14, 值=0020
2025-07-31 10:22:32,832 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x14 = 0x0020
2025-07-31 10:22:32,832 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x15 = 0x0000
2025-07-31 10:22:32,832 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x16, 值=0000
2025-07-31 10:22:32,832 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x16 = 0x0000
2025-07-31 10:22:32,832 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x17, 值=00F9
2025-07-31 10:22:32,832 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x17 = 0x00F9
2025-07-31 10:22:32,832 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x18, 值=0005
2025-07-31 10:22:32,832 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x18 = 0x0005
2025-07-31 10:22:32,832 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x19, 值=0002
2025-07-31 10:22:32,832 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x19 = 0x0002
2025-07-31 10:22:32,832 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x1A, 值=0010
2025-07-31 10:22:32,832 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x1A = 0x0010
2025-07-31 10:22:32,832 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x1B = 0x0000
2025-07-31 10:22:32,832 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x1C, 值=0020
2025-07-31 10:22:32,832 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x1C = 0x0020
2025-07-31 10:22:32,832 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x1D = 0x0000
2025-07-31 10:22:32,832 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x1E, 值=0000
2025-07-31 10:22:32,832 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x1E = 0x0000
2025-07-31 10:22:32,832 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x1F, 值=00F9
2025-07-31 10:22:32,832 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x1F = 0x00F9
2025-07-31 10:22:32,832 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x20, 值=0005
2025-07-31 10:22:32,832 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x20 = 0x0005
2025-07-31 10:22:32,833 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x21, 值=0002
2025-07-31 10:22:32,833 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x21 = 0x0002
2025-07-31 10:22:32,833 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x22, 值=0010
2025-07-31 10:22:32,833 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x22 = 0x0010
2025-07-31 10:22:32,833 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x23 = 0x0000
2025-07-31 10:22:32,833 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x24, 值=0020
2025-07-31 10:22:32,833 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x24 = 0x0020
2025-07-31 10:22:32,833 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x25 = 0x0000
2025-07-31 10:22:32,833 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x26, 值=0000
2025-07-31 10:22:32,833 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x26 = 0x0000
2025-07-31 10:22:32,833 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x27, 值=00F9
2025-07-31 10:22:32,833 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x27 = 0x00F9
2025-07-31 10:22:32,833 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x28, 值=0006
2025-07-31 10:22:32,833 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x28 = 0x0006
2025-07-31 10:22:32,833 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x29, 值=0002
2025-07-31 10:22:32,833 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x29 = 0x0002
2025-07-31 10:22:32,833 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x2A, 值=0010
2025-07-31 10:22:32,833 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x2A = 0x0010
2025-07-31 10:22:32,833 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x2B = 0x0000
2025-07-31 10:22:32,833 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x2C, 值=0020
2025-07-31 10:22:32,833 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x2C = 0x0020
2025-07-31 10:22:32,833 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x2D = 0x0000
2025-07-31 10:22:32,833 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x2E, 值=0000
2025-07-31 10:22:32,833 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x2E = 0x0000
2025-07-31 10:22:32,833 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x2F, 值=00F9
2025-07-31 10:22:32,833 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x2F = 0x00F9
2025-07-31 10:22:32,833 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x30, 值=0005
2025-07-31 10:22:32,833 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x30 = 0x0005
2025-07-31 10:22:32,833 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x31, 值=0002
2025-07-31 10:22:32,833 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x31 = 0x0002
2025-07-31 10:22:32,834 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x32, 值=0010
2025-07-31 10:22:32,834 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x32 = 0x0010
2025-07-31 10:22:32,834 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x33 = 0x0000
2025-07-31 10:22:32,834 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x34, 值=0020
2025-07-31 10:22:32,834 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x34 = 0x0020
2025-07-31 10:22:32,834 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x35 = 0x0000
2025-07-31 10:22:32,834 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x36, 值=0000
2025-07-31 10:22:32,834 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x36 = 0x0000
2025-07-31 10:22:32,834 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x37, 值=00F9
2025-07-31 10:22:32,834 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x37 = 0x00F9
2025-07-31 10:22:32,834 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x38, 值=0005
2025-07-31 10:22:32,834 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x38 = 0x0005
2025-07-31 10:22:32,834 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x39, 值=0002
2025-07-31 10:22:32,834 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x39 = 0x0002
2025-07-31 10:22:32,834 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x3A, 值=0010
2025-07-31 10:22:32,834 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x3A = 0x0010
2025-07-31 10:22:32,834 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x3B = 0x0000
2025-07-31 10:22:32,834 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x3C, 值=0020
2025-07-31 10:22:32,834 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x3C = 0x0020
2025-07-31 10:22:32,834 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x3D = 0x0000
2025-07-31 10:22:32,834 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x3E, 值=0000
2025-07-31 10:22:32,834 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x3E = 0x0000
2025-07-31 10:22:32,834 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x3F, 值=00F9
2025-07-31 10:22:32,834 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x3F = 0x00F9
2025-07-31 10:22:32,834 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x40, 值=0005
2025-07-31 10:22:32,834 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x40 = 0x0005
2025-07-31 10:22:32,835 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x41, 值=0002
2025-07-31 10:22:32,835 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x41 = 0x0002
2025-07-31 10:22:32,835 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x42, 值=0010
2025-07-31 10:22:32,835 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x42 = 0x0010
2025-07-31 10:22:32,835 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x43 = 0x0000
2025-07-31 10:22:32,835 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x44, 值=0020
2025-07-31 10:22:32,835 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x44 = 0x0020
2025-07-31 10:22:32,835 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x45 = 0x0000
2025-07-31 10:22:32,835 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x46, 值=0000
2025-07-31 10:22:32,835 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x46 = 0x0000
2025-07-31 10:22:32,835 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x47, 值=00F9
2025-07-31 10:22:32,835 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x47 = 0x00F9
2025-07-31 10:22:32,835 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x48, 值=0004
2025-07-31 10:22:32,835 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x48 = 0x0004
2025-07-31 10:22:32,835 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x49, 值=0001
2025-07-31 10:22:32,835 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x49 = 0x0001
2025-07-31 10:22:32,835 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x4A, 值=0005
2025-07-31 10:22:32,835 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x4A = 0x0005
2025-07-31 10:22:32,835 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x4C = 0x0008
2025-07-31 10:22:32,835 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x4E = 0x0043
2025-07-31 10:22:32,835 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x4F, 值=0025
2025-07-31 10:22:32,835 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x4F = 0x0025
2025-07-31 10:22:32,835 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x50, 值=0002
2025-07-31 10:22:32,835 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x50 = 0x0002
2025-07-31 10:22:32,835 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x51 = 0x0000
2025-07-31 10:22:32,835 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x52 = 0x0100
2025-07-31 10:22:32,835 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x53, 值=0031
2025-07-31 10:22:32,835 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x53 = 0x0031
2025-07-31 10:22:32,836 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x54, 值=0080
2025-07-31 10:22:32,836 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x54 = 0x0080
2025-07-31 10:22:32,836 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x55, 值=0004
2025-07-31 10:22:32,836 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x55 = 0x0004
2025-07-31 10:22:32,836 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x56 = 0x0018
2025-07-31 10:22:32,836 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x57 = 0x001A
2025-07-31 10:22:32,836 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x58 = 0x0002
2025-07-31 10:22:32,836 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x59 = 0x0042
2025-07-31 10:22:32,836 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x5A, 值=0002
2025-07-31 10:22:32,836 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x5A = 0x0002
2025-07-31 10:22:32,836 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x5B, 值=0024
2025-07-31 10:22:32,836 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x5B = 0x0024
2025-07-31 10:22:32,836 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x5C = 0x0200
2025-07-31 10:22:32,836 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x5D = 0x0000
2025-07-31 10:22:32,836 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x5E = 0x00C0
2025-07-31 10:22:32,836 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x5F = 0x007F
2025-07-31 10:22:32,836 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x60, 值=1693
2025-07-31 10:22:32,836 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x60 = 0x1693
2025-07-31 10:22:32,836 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x61 = 0x0200
2025-07-31 10:22:32,836 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x63 = 0x0078
2025-07-31 10:22:32,836 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x65, 值=0010
2025-07-31 10:22:32,836 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x65 = 0x0010
2025-07-31 10:22:32,836 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x67 = 0x0096
2025-07-31 10:22:32,836 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x69 = 0x0078
2025-07-31 10:22:32,836 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x6B, 值=0090
2025-07-31 10:22:32,836 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x6B = 0x0090
2025-07-31 10:22:32,836 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x6C = 0x2000
2025-07-31 10:22:32,836 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x6F = 0x000B
2025-07-31 10:22:32,836 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x70, 值=0002
2025-07-31 10:22:32,836 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x70 = 0x0002
2025-07-31 10:22:32,836 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x72, 值=0029
2025-07-31 10:22:32,836 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x72 = 0x0029
2025-07-31 10:22:32,836 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x76 = 0x0000
2025-07-31 10:22:32,836 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x77, 值=0006
2025-07-31 10:22:32,837 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x77 = 0x0006
2025-07-31 10:22:32,837 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x79, 值=0019
2025-07-31 10:22:32,837 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x79 = 0x0019
2025-07-31 10:22:32,837 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x7A, 值=2000
2025-07-31 10:22:32,837 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x7A = 0x2000
2025-07-31 10:22:32,837 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x7E, 值=0013
2025-07-31 10:22:32,837 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x7E = 0x0013
2025-07-31 10:22:32,837 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x83 = 0x0010
2025-07-31 10:22:32,837 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x87 = 0x0000
2025-07-31 10:22:32,837 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x92 = 0x0000
2025-07-31 10:22:32,837 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x93, 值=006C
2025-07-31 10:22:32,837 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x93 = 0x006C
2025-07-31 10:22:32,837 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x94, 值=001A
2025-07-31 10:22:32,837 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x94 = 0x001A
2025-07-31 10:22:32,837 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x95, 值=0219
2025-07-31 10:22:32,837 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x95 = 0x0219
2025-07-31 10:22:32,837 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x98, 值=FFB4
2025-07-31 10:22:32,837 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0x98 = 0xFFB4
2025-07-31 10:22:32,837 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0xA0, 值=3280
2025-07-31 10:22:32,837 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0xA0 = 0x3280
2025-07-31 10:22:32,837 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0xA1, 值=8419
2025-07-31 10:22:32,837 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0xA1 = 0x8419
2025-07-31 10:22:32,837 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0xA2, 值=0CF9
2025-07-31 10:22:32,837 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0xA2 = 0x0CF9
2025-07-31 10:22:32,837 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0xA3 = 0x8080
2025-07-31 10:22:32,837 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0xA4, 值=0BA0
2025-07-31 10:22:32,837 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0xA4 = 0x0BA0
2025-07-31 10:22:32,837 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0xA5 = 0x0780
2025-07-31 10:22:32,838 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0xA6, 值=0010
2025-07-31 10:22:32,838 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0xA6 = 0x0010
2025-07-31 10:22:32,838 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0xA7 = 0x0080
2025-07-31 10:22:32,838 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0xA8, 值=0036
2025-07-31 10:22:32,838 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0xA8 = 0x0036
2025-07-31 10:22:32,838 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0xA9 = 0x0000
2025-07-31 10:22:32,838 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0xAA, 值=1F24
2025-07-31 10:22:32,838 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0xAA = 0x1F24
2025-07-31 10:22:32,838 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0xAB, 值=19FC
2025-07-31 10:22:32,838 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0xAB = 0x19FC
2025-07-31 10:22:32,838 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0xAC, 值=3264
2025-07-31 10:22:32,838 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0xAC = 0x3264
2025-07-31 10:22:32,838 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0xAD, 值=3264
2025-07-31 10:22:32,838 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0xAD = 0x3264
2025-07-31 10:22:32,838 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0xAE, 值=0080
2025-07-31 10:22:32,838 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0xAE = 0x0080
2025-07-31 10:22:32,838 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0xD8, 值=0014
2025-07-31 10:22:32,838 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0xD8 = 0x0014
2025-07-31 10:22:32,838 - UserConfigurationService - [UserConfigurationService.py:650] - DEBUG - 应用寄存器配置: 0xDA = 0x8000
2025-07-31 10:22:32,838 - UserConfigurationService - [UserConfigurationService.py:655] - INFO - 成功应用 125 个寄存器配置
2025-07-31 10:22:32,838 - UserConfigurationService - [UserConfigurationService.py:622] - INFO - 配置应用完成，成功: True
2025-07-31 10:22:32,838 - UserConfigurationService - [UserConfigurationService.py:592] - INFO - 最近配置恢复成功
2025-07-31 10:22:32,838 - RegisterMainWindow - [RegisterMainWindow.py:1007] - INFO - 用户配置服务初始化完成
2025-07-31 10:22:32,838 - RegisterOperationService - [RegisterOperationService.py:80] - INFO - 开始状态监控，间隔: 5000ms
2025-07-31 10:22:32,838 - RegisterMainWindow - [RegisterMainWindow.py:692] - INFO - 已启动寄存器状态监控
2025-07-31 10:22:32,868 - InitializationManager - [InitializationManager.py:279] - INFO - InitializationManager: 执行延迟端口刷新，确保UI同步端口状态
2025-07-31 10:22:32,868 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:537] - INFO - ModernRegisterIOHandler: 请求SPI服务刷新端口
2025-07-31 10:22:32,868 - spi_service - [spi_service.py:179] - INFO - Refreshing available COM ports...
2025-07-31 10:22:32,871 - spi_service - [spi_service.py:186] - DEBUG - Found port: COM4 - USB 串行设备 (COM4)
2025-07-31 10:22:32,872 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:545] - DEBUG - ModernRegisterIOHandler: _update_port_combo 被调用，port_combo: <PyQt5.QtWidgets.QComboBox object at 0x0000020553507550>, 类型: <class 'PyQt5.QtWidgets.QComboBox'>
2025-07-31 10:22:32,872 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:564] - INFO - ModernRegisterIOHandler: SPI服务已连接到端口: COM4
2025-07-31 10:22:32,872 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:573] - INFO - ModernRegisterIOHandler: 添加COM端口到下拉框: COM4
2025-07-31 10:22:32,872 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:582] - INFO - ModernRegisterIOHandler: 选择端口: COM4
2025-07-31 10:22:32,872 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:597] - INFO - ModernRegisterIOHandler: SPI服务已连接，UI已同步端口显示
2025-07-31 10:22:32,872 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:615] - INFO - ModernRegisterIOHandler: 触发状态栏更新
2025-07-31 10:22:32,872 - StatusAndConfigManager - [StatusAndConfigManager.py:25] - INFO - StatusAndConfigManager.update_status_bar() 被调用
2025-07-31 10:22:32,872 - StatusAndConfigManager - [StatusAndConfigManager.py:30] - INFO - 获取到SPI服务状态: {'connected': True, 'port': 'COM4', 'mode': 'hardware', 'last_error': None, 'retry_count': 0}
2025-07-31 10:22:32,872 - StatusAndConfigManager - [StatusAndConfigManager.py:33] - INFO - 原始模式文本: 'hardware'
2025-07-31 10:22:32,872 - StatusAndConfigManager - [StatusAndConfigManager.py:60] - INFO - 状态栏已更新: 模式=硬件模式, 端口=COM4
2025-07-31 10:22:32,872 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:545] - DEBUG - ModernRegisterIOHandler: _update_port_combo 被调用，port_combo: <PyQt5.QtWidgets.QComboBox object at 0x0000020553507550>, 类型: <class 'PyQt5.QtWidgets.QComboBox'>
2025-07-31 10:22:32,872 - ModernRegisterIOHandler - [ModernRegisterIOHandler.py:552] - INFO - ModernRegisterIOHandler: 端口信息未变化，跳过重复更新
2025-07-31 10:22:32,873 - InitializationManager - [InitializationManager.py:282] - INFO - InitializationManager: 端口刷新完成
2025-07-31 10:22:32,928 - RegisterMainWindow - [RegisterMainWindow.py:393] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-31 10:22:32,929 - RegisterMainWindow - [RegisterMainWindow.py:393] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-31 10:22:32,936 - RegisterMainWindow - [RegisterMainWindow.py:576] - DEBUG - 未找到打开的工具窗口
2025-07-31 10:22:32,937 - RegisterMainWindow - [RegisterMainWindow.py:412] - DEBUG - 主窗口强制置顶完成
2025-07-31 10:22:32,937 - RegisterMainWindow - [RegisterMainWindow.py:317] - DEBUG - 主窗口已通过焦点置顶
2025-07-31 10:22:32,951 - RegisterMainWindow - [RegisterMainWindow.py:576] - DEBUG - 未找到打开的工具窗口
2025-07-31 10:22:32,951 - RegisterMainWindow - [RegisterMainWindow.py:412] - DEBUG - 主窗口强制置顶完成
2025-07-31 10:22:32,951 - RegisterMainWindow - [RegisterMainWindow.py:321] - DEBUG - 主窗口已通过激活事件置顶
2025-07-31 10:22:33,129 - InitializationManager - [InitializationManager.py:294] - INFO - InitializationManager: 执行延迟状态栏更新
2025-07-31 10:22:33,129 - StatusAndConfigManager - [StatusAndConfigManager.py:25] - INFO - StatusAndConfigManager.update_status_bar() 被调用
2025-07-31 10:22:33,129 - StatusAndConfigManager - [StatusAndConfigManager.py:30] - INFO - 获取到SPI服务状态: {'connected': True, 'port': 'COM4', 'mode': 'hardware', 'last_error': None, 'retry_count': 0}
2025-07-31 10:22:33,129 - StatusAndConfigManager - [StatusAndConfigManager.py:33] - INFO - 原始模式文本: 'hardware'
2025-07-31 10:22:33,130 - StatusAndConfigManager - [StatusAndConfigManager.py:60] - INFO - 状态栏已更新: 模式=硬件模式, 端口=COM4
2025-07-31 10:22:33,632 - RegisterMainWindow - [RegisterMainWindow.py:287] - INFO - 已为寄存器树安装点击事件过滤器
2025-07-31 10:22:33,633 - RegisterMainWindow - [RegisterMainWindow.py:292] - INFO - 已为寄存器表格安装点击事件过滤器
2025-07-31 10:22:37,291 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 模式设置(&M)
2025-07-31 10:22:37,845 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x93
2025-07-31 10:22:37,846 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x93, 值=006A
2025-07-31 10:22:37,846 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x93 = 0x006A
2025-07-31 10:22:37,846 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x94
2025-07-31 10:22:37,846 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x94, 值=0016
2025-07-31 10:22:37,846 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x94 = 0x0016
2025-07-31 10:22:37,846 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x95
2025-07-31 10:22:37,847 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x95, 值=0217
2025-07-31 10:22:37,847 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x95 = 0x0217
2025-07-31 10:22:37,847 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x98
2025-07-31 10:22:37,847 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x98, 值=FFB4
2025-07-31 10:22:37,847 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x98 = 0xFFB4
2025-07-31 10:22:39,976 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:39,986 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:39,994 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:40,002 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:40,010 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:40,018 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:40,026 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:40,034 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 示例工具
2025-07-31 10:22:40,045 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 示例工具
2025-07-31 10:22:40,055 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 示例工具
2025-07-31 10:22:40,058 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 示例工具
2025-07-31 10:22:40,068 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 示例工具
2025-07-31 10:22:40,076 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 示例工具
2025-07-31 10:22:40,085 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 性能监控器
2025-07-31 10:22:40,095 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 性能监控器
2025-07-31 10:22:40,130 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 性能监控器
2025-07-31 10:22:40,136 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 性能监控器
2025-07-31 10:22:40,140 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 性能监控器
2025-07-31 10:22:40,149 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 性能监控器
2025-07-31 10:22:40,181 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 性能监控器
2025-07-31 10:22:40,186 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-07-31 10:22:40,195 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-07-31 10:22:40,203 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-07-31 10:22:40,212 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-07-31 10:22:40,222 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-07-31 10:22:40,226 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-07-31 10:22:40,236 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-07-31 10:22:40,243 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-07-31 10:22:40,255 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-07-31 10:22:40,780 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-07-31 10:22:40,789 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-07-31 10:22:40,797 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-07-31 10:22:40,805 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-07-31 10:22:40,812 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-07-31 10:22:40,822 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-07-31 10:22:40,829 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-07-31 10:22:40,839 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-07-31 10:22:40,847 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-07-31 10:22:40,855 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-07-31 10:22:40,864 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 性能监控器
2025-07-31 10:22:40,873 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 性能监控器
2025-07-31 10:22:40,878 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 性能监控器
2025-07-31 10:22:40,887 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 性能监控器
2025-07-31 10:22:40,898 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 性能监控器
2025-07-31 10:22:40,906 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 性能监控器
2025-07-31 10:22:40,913 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 性能监控器
2025-07-31 10:22:40,921 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 性能监控器
2025-07-31 10:22:40,929 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 性能监控器
2025-07-31 10:22:40,938 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 性能监控器
2025-07-31 10:22:40,946 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 性能监控器
2025-07-31 10:22:40,956 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 性能监控器
2025-07-31 10:22:40,966 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 示例工具
2025-07-31 10:22:40,970 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 示例工具
2025-07-31 10:22:40,983 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 示例工具
2025-07-31 10:22:40,991 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 示例工具
2025-07-31 10:22:40,998 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 示例工具
2025-07-31 10:22:41,004 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 示例工具
2025-07-31 10:22:41,013 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 示例工具
2025-07-31 10:22:41,028 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 示例工具
2025-07-31 10:22:41,047 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 示例工具
2025-07-31 10:22:41,062 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 示例工具
2025-07-31 10:22:41,100 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 示例工具
2025-07-31 10:22:41,105 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 示例工具
2025-07-31 10:22:41,117 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:41,120 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:41,141 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:41,149 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:41,182 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:41,208 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:41,233 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:41,306 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:41,314 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:41,323 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:41,330 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:41,339 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:41,348 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:41,363 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:41,371 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:41,379 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:41,386 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:41,395 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:41,406 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:41,425 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:41,433 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:41,440 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:41,449 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:41,458 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:41,464 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:41,481 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:41,490 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:41,496 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:41,511 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:41,519 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:41,552 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:41,568 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:42,586 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:42,602 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:42,610 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:42,617 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:42,625 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 示例工具
2025-07-31 10:22:42,634 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 示例工具
2025-07-31 10:22:42,643 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 示例工具
2025-07-31 10:22:42,651 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 示例工具
2025-07-31 10:22:42,660 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 示例工具
2025-07-31 10:22:42,668 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 示例工具
2025-07-31 10:22:42,678 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 示例工具
2025-07-31 10:22:42,687 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 示例工具
2025-07-31 10:22:42,704 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 性能监控器
2025-07-31 10:22:42,712 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 性能监控器
2025-07-31 10:22:42,720 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 性能监控器
2025-07-31 10:22:42,727 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 性能监控器
2025-07-31 10:22:42,736 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 性能监控器
2025-07-31 10:22:42,744 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 性能监控器
2025-07-31 10:22:42,750 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 性能监控器
2025-07-31 10:22:42,759 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-07-31 10:22:42,765 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-07-31 10:22:42,774 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-07-31 10:22:42,782 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-07-31 10:22:42,791 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-07-31 10:22:42,837 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x93
2025-07-31 10:22:42,837 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x93, 值=006A
2025-07-31 10:22:42,837 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x93 = 0x006A
2025-07-31 10:22:42,837 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x94
2025-07-31 10:22:42,837 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x94, 值=0011
2025-07-31 10:22:42,837 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x94 = 0x0011
2025-07-31 10:22:42,837 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x95
2025-07-31 10:22:42,837 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x95, 值=0212
2025-07-31 10:22:42,838 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x95 = 0x0212
2025-07-31 10:22:42,838 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x98
2025-07-31 10:22:42,838 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x98, 值=FFB4
2025-07-31 10:22:42,838 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x98 = 0xFFB4
2025-07-31 10:22:44,287 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-07-31 10:22:44,290 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-07-31 10:22:44,299 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 性能监控器
2025-07-31 10:22:44,308 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 性能监控器
2025-07-31 10:22:44,316 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 性能监控器
2025-07-31 10:22:44,324 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 性能监控器
2025-07-31 10:22:44,332 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 性能监控器
2025-07-31 10:22:44,339 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 示例工具
2025-07-31 10:22:44,348 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 示例工具
2025-07-31 10:22:44,356 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 示例工具
2025-07-31 10:22:44,363 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 示例工具
2025-07-31 10:22:44,371 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 示例工具
2025-07-31 10:22:44,376 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 示例工具
2025-07-31 10:22:44,386 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:44,396 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:44,402 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:44,411 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:44,419 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:44,430 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 数据分析器
2025-07-31 10:22:44,873 - RegisterMainWindow - [RegisterMainWindow.py:393] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-31 10:22:44,878 - RegisterMainWindow - [RegisterMainWindow.py:576] - DEBUG - 未找到打开的工具窗口
2025-07-31 10:22:44,878 - RegisterMainWindow - [RegisterMainWindow.py:412] - DEBUG - 主窗口强制置顶完成
2025-07-31 10:22:44,878 - RegisterMainWindow - [RegisterMainWindow.py:317] - DEBUG - 主窗口已通过焦点置顶
2025-07-31 10:22:45,101 - PluginManagerGUI - [PluginManagerGUI.py:757] - INFO - 插件管理器: 刷新插件列表完成，发现 9 个插件
2025-07-31 10:22:45,124 - PluginMenuService - [PluginMenuService.py:327] - INFO - 显示插件管理器
2025-07-31 10:22:47,835 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x93
2025-07-31 10:22:47,836 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x93, 值=0065
2025-07-31 10:22:47,836 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x93 = 0x0065
2025-07-31 10:22:47,836 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x94
2025-07-31 10:22:47,836 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x94, 值=0014
2025-07-31 10:22:47,836 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x94 = 0x0014
2025-07-31 10:22:47,836 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x95
2025-07-31 10:22:47,836 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x95, 值=0214
2025-07-31 10:22:47,836 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x95 = 0x0214
2025-07-31 10:22:47,836 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x98
2025-07-31 10:22:47,836 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x98, 值=FFB1
2025-07-31 10:22:47,836 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x98 = 0xFFB1
2025-07-31 10:22:52,831 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x93
2025-07-31 10:22:52,832 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x93, 值=0067
2025-07-31 10:22:52,832 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x93 = 0x0067
2025-07-31 10:22:52,832 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x94
2025-07-31 10:22:52,832 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x94, 值=0017
2025-07-31 10:22:52,832 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x94 = 0x0017
2025-07-31 10:22:52,832 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x95
2025-07-31 10:22:52,832 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x95, 值=0210
2025-07-31 10:22:52,832 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x95 = 0x0210
2025-07-31 10:22:52,832 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x98
2025-07-31 10:22:52,832 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x98, 值=FFB6
2025-07-31 10:22:52,833 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x98 = 0xFFB6
2025-07-31 10:22:57,833 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x93
2025-07-31 10:22:57,835 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x93, 值=006B
2025-07-31 10:22:57,835 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x93 = 0x006B
2025-07-31 10:22:57,835 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x94
2025-07-31 10:22:57,835 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x94, 值=0016
2025-07-31 10:22:57,835 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x94 = 0x0016
2025-07-31 10:22:57,835 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x95
2025-07-31 10:22:57,835 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x95, 值=0215
2025-07-31 10:22:57,835 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x95 = 0x0215
2025-07-31 10:22:57,835 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x98
2025-07-31 10:22:57,835 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x98, 值=FFB1
2025-07-31 10:22:57,835 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x98 = 0xFFB1
2025-07-31 10:23:02,831 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x93
2025-07-31 10:23:02,832 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x93, 值=0070
2025-07-31 10:23:02,832 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x93 = 0x0070
2025-07-31 10:23:02,832 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x94
2025-07-31 10:23:02,832 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x94, 值=0011
2025-07-31 10:23:02,833 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x94 = 0x0011
2025-07-31 10:23:02,833 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x95
2025-07-31 10:23:02,833 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x95, 值=0216
2025-07-31 10:23:02,833 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x95 = 0x0216
2025-07-31 10:23:02,833 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x98
2025-07-31 10:23:02,833 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x98, 值=FFAC
2025-07-31 10:23:02,833 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x98 = 0xFFAC
2025-07-31 10:23:08,358 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x93
2025-07-31 10:23:08,358 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x93, 值=006E
2025-07-31 10:23:08,358 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x93 = 0x006E
2025-07-31 10:23:08,358 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x94
2025-07-31 10:23:08,358 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x94, 值=0014
2025-07-31 10:23:08,358 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x94 = 0x0014
2025-07-31 10:23:08,359 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x95
2025-07-31 10:23:08,359 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x95, 值=0218
2025-07-31 10:23:08,359 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x95 = 0x0218
2025-07-31 10:23:08,359 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x98
2025-07-31 10:23:08,359 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x98, 值=FFAB
2025-07-31 10:23:08,359 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x98 = 0xFFAB
2025-07-31 10:23:08,369 - RegisterMainWindow - [RegisterMainWindow.py:393] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-31 10:23:08,370 - RegisterMainWindow - [RegisterMainWindow.py:393] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-31 10:23:08,375 - RegisterMainWindow - [RegisterMainWindow.py:576] - DEBUG - 未找到打开的工具窗口
2025-07-31 10:23:08,375 - RegisterMainWindow - [RegisterMainWindow.py:412] - DEBUG - 主窗口强制置顶完成
2025-07-31 10:23:08,375 - RegisterMainWindow - [RegisterMainWindow.py:317] - DEBUG - 主窗口已通过焦点置顶
2025-07-31 10:23:08,382 - RegisterMainWindow - [RegisterMainWindow.py:576] - DEBUG - 未找到打开的工具窗口
2025-07-31 10:23:08,382 - RegisterMainWindow - [RegisterMainWindow.py:412] - DEBUG - 主窗口强制置顶完成
2025-07-31 10:23:08,382 - RegisterMainWindow - [RegisterMainWindow.py:321] - DEBUG - 主窗口已通过激活事件置顶
2025-07-31 10:23:12,836 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x93
2025-07-31 10:23:12,837 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x93, 值=0070
2025-07-31 10:23:12,837 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x93 = 0x0070
2025-07-31 10:23:12,837 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x94
2025-07-31 10:23:12,837 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x94, 值=0011
2025-07-31 10:23:12,837 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x94 = 0x0011
2025-07-31 10:23:12,837 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x95
2025-07-31 10:23:12,837 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x95, 值=021A
2025-07-31 10:23:12,837 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x95 = 0x021A
2025-07-31 10:23:12,837 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x98
2025-07-31 10:23:12,838 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x98, 值=FFAF
2025-07-31 10:23:12,838 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x98 = 0xFFAF
2025-07-31 10:23:17,844 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x93
2025-07-31 10:23:17,844 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x93, 值=006C
2025-07-31 10:23:17,844 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x93 = 0x006C
2025-07-31 10:23:17,845 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x94
2025-07-31 10:23:17,845 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x94, 值=0011
2025-07-31 10:23:17,845 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x94 = 0x0011
2025-07-31 10:23:17,845 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x95
2025-07-31 10:23:17,845 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x95, 值=0217
2025-07-31 10:23:17,845 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x95 = 0x0217
2025-07-31 10:23:17,846 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x98
2025-07-31 10:23:17,846 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x98, 值=FFAA
2025-07-31 10:23:17,846 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x98 = 0xFFAA
2025-07-31 10:23:22,834 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x93
2025-07-31 10:23:22,834 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x93, 值=0070
2025-07-31 10:23:22,834 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x93 = 0x0070
2025-07-31 10:23:22,834 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x94
2025-07-31 10:23:22,834 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x94, 值=000C
2025-07-31 10:23:22,834 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x94 = 0x000C
2025-07-31 10:23:22,834 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x95
2025-07-31 10:23:22,834 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x95, 值=0217
2025-07-31 10:23:22,835 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x95 = 0x0217
2025-07-31 10:23:22,835 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x98
2025-07-31 10:23:22,835 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x98, 值=FFA9
2025-07-31 10:23:22,835 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x98 = 0xFFA9
2025-07-31 10:23:27,836 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x93
2025-07-31 10:23:27,837 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x93, 值=0072
2025-07-31 10:23:27,837 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x93 = 0x0072
2025-07-31 10:23:27,837 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x94
2025-07-31 10:23:27,837 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x94, 值=000A
2025-07-31 10:23:27,837 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x94 = 0x000A
2025-07-31 10:23:27,837 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x95
2025-07-31 10:23:27,837 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x95, 值=0217
2025-07-31 10:23:27,837 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x95 = 0x0217
2025-07-31 10:23:27,837 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x98
2025-07-31 10:23:27,837 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x98, 值=FFAE
2025-07-31 10:23:27,837 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x98 = 0xFFAE
2025-07-31 10:23:30,178 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 模式设置(&M)
2025-07-31 10:23:31,225 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 时钟输入(&I)
2025-07-31 10:23:31,629 - MenuClickFixer - [MenuClickFixer.py:93] - DEBUG - 动作切换: 时钟输入(&I), checked=True
2025-07-31 10:23:31,629 - PluginMenuService - [PluginMenuService.py:285] - INFO - 🎯 插件菜单动作触发: 时钟输入控制, checked=True
2025-07-31 10:23:31,630 - PluginIntegrationService - [PluginIntegrationService.py:93] - INFO - 🎯 插件菜单动作触发: 时钟输入控制, checked=True
2025-07-31 10:23:31,630 - MenuClickFixer - [MenuClickFixer.py:52] - INFO - 🎯 增强处理器触发: 时钟输入(&I), checked=True
2025-07-31 10:23:31,631 - ModernBaseHandler - [ModernBaseHandler.py:186] - INFO - ModernClkinControlHandler: 已设置滚动区域，最小尺寸: (2100, 750)
2025-07-31 10:23:31,631 - ModernBaseHandler - [ModernBaseHandler.py:79] - DEBUG - 成功连接到RegisterUpdateBus
2025-07-31 10:23:31,650 - ModernClkinControlHandler - [ModernClkinControlHandler.py:436] - INFO - 开始设置SpinBox控件范围...
2025-07-31 10:23:31,650 - ModernClkinControlHandler - [ModernClkinControlHandler.py:449] - INFO - 已设置 PLL1R0Div 范围: 0-16383
2025-07-31 10:23:31,651 - ModernClkinControlHandler - [ModernClkinControlHandler.py:449] - INFO - 已设置 PLL1R1Div 范围: 0-16383
2025-07-31 10:23:31,651 - ModernClkinControlHandler - [ModernClkinControlHandler.py:449] - INFO - 已设置 PLL1R2Div 范围: 0-16383
2025-07-31 10:23:31,651 - ModernClkinControlHandler - [ModernClkinControlHandler.py:455] - INFO - 已设置MANDAC 控件范围: 0-1023
2025-07-31 10:23:31,651 - ModernClkinControlHandler - [ModernClkinControlHandler.py:459] - INFO - SpinBox控件范围设置完成
2025-07-31 10:23:31,651 - RegisterUpdateBus - [RegisterUpdateBus.py:512] - DEBUG - RegisterUpdateBus: 已缓存PLL1PFDFreq值: 1.024 MHz
2025-07-31 10:23:31,651 - ModernClkinControlHandler - [ModernClkinControlHandler.py:114] - DEBUG - 初始化默认PLL1PFDFreq值: 1.02400 MHz (OSCin: 122.88, R: 120)
2025-07-31 10:23:31,651 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1156] - INFO - 已连接PLL1PFDFreq更新信号
2025-07-31 10:23:31,651 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1160] - INFO - 已设置PLL更新监听器
2025-07-31 10:23:31,651 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1217] - WARNING - 无法获取插件集成服务，跳过PLL窗口状态监听器设置
2025-07-31 10:23:31,651 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1219] - DEBUG - 主窗口类型: builtin_function_or_method
2025-07-31 10:23:31,651 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1220] - DEBUG - 主窗口属性: []
2025-07-31 10:23:31,651 - ModernClkinControlHandler - [ModernClkinControlHandler.py:82] - INFO - 现代化时钟输入控制处理器初始化完成
2025-07-31 10:23:31,651 - clkin_control_plugin - [clkin_control_plugin.py:105] - INFO - ✅ 已设置主窗口的clkin_control_window属性
2025-07-31 10:23:31,651 - clkin_control_plugin - [clkin_control_plugin.py:110] - DEBUG - 已连接 '时钟输入控制' 窗口关闭信号
2025-07-31 10:23:31,651 - clkin_control_plugin - [clkin_control_plugin.py:114] - INFO - 创建 '时钟输入控制' 工具窗口
2025-07-31 10:23:31,651 - PluginWindowService - [PluginWindowService.py:562] - DEBUG - 已为插件窗口 时钟输入控制 添加点击置顶功能
2025-07-31 10:23:31,652 - PluginWindowService - [PluginWindowService.py:473] - INFO - 已调整窗口大小: 1250x980
2025-07-31 10:23:31,652 - PluginDockService - [PluginDockService.py:500] - DEBUG - 已启动拖拽监控: 时钟输入控制
2025-07-31 10:23:31,652 - PluginDockService - [PluginDockService.py:482] - INFO - 已为插件窗口添加拖拽停靠支持: 时钟输入控制
2025-07-31 10:23:31,652 - PluginWindowService - [PluginWindowService.py:343] - DEBUG - 已为插件窗口添加拖拽停靠支持: 时钟输入控制
2025-07-31 10:23:31,652 - PluginWindowService - [PluginWindowService.py:321] - INFO - 已配置插件窗口: 时钟输入控制
2025-07-31 10:23:31,657 - ModernClkinControlHandler - [ModernClkinControlHandler.py:92] - DEBUG - 窗口显示，将重新计算DAC Update Rate
2025-07-31 10:23:31,662 - PluginWindowService - [PluginWindowService.py:94] - INFO - 创建并显示插件窗口: 时钟输入控制
2025-07-31 10:23:31,662 - PluginMenuService - [PluginMenuService.py:261] - INFO - 🔧 菜单修复器回调: 时钟输入控制, checked=True
2025-07-31 10:23:31,662 - PluginMenuService - [PluginMenuService.py:285] - INFO - 🎯 插件菜单动作触发: 时钟输入控制, checked=True
2025-07-31 10:23:31,662 - PluginIntegrationService - [PluginIntegrationService.py:93] - INFO - 🎯 插件菜单动作触发: 时钟输入控制, checked=True
2025-07-31 10:23:31,678 - ModernBaseHandler - [ModernBaseHandler.py:97] - INFO - ModernBaseHandler: 开始完成初始化
2025-07-31 10:23:31,678 - ModernClkinControlHandler - [ModernClkinControlHandler.py:121] - INFO - 设置时钟输入控制的寄存器默认值...
2025-07-31 10:23:31,678 - ModernClkinControlHandler - [ModernClkinControlHandler.py:152] - INFO - 寄存器 0x63[CLKin0_R[13:0]] 已有值: 120，将使用此值
2025-07-31 10:23:31,678 - ModernClkinControlHandler - [ModernClkinControlHandler.py:152] - INFO - 寄存器 0x65[CLKin1_R[13:0]] 已有值: 16，将使用此值
2025-07-31 10:23:31,678 - ModernClkinControlHandler - [ModernClkinControlHandler.py:152] - INFO - 寄存器 0x67[CLKin2_R[13:0]] 已有值: 150，将使用此值
2025-07-31 10:23:31,678 - RegisterManager - [RegisterManager.py:153] - DEBUG - RegisterManager更新位字段: 0x5A.CLKin_SEL_MANUAL[1:0], 寄存器值=0002
2025-07-31 10:23:31,678 - ModernClkinControlHandler - [ModernClkinControlHandler.py:159] - INFO - 设置寄存器默认值: 0x5A[CLKin_SEL_MANUAL[1:0]] = 1
2025-07-31 10:23:31,678 - RegisterManager - [RegisterManager.py:153] - DEBUG - RegisterManager更新位字段: 0x5A.CLKin0_DEMUX[1:0], 寄存器值=0002
2025-07-31 10:23:31,678 - ModernClkinControlHandler - [ModernClkinControlHandler.py:159] - INFO - 设置寄存器默认值: 0x5A[CLKin0_DEMUX[1:0]] = 2
2025-07-31 10:23:31,678 - RegisterManager - [RegisterManager.py:153] - DEBUG - RegisterManager更新位字段: 0x5A.CLKin1_DEMUX[1:0], 寄存器值=0002
2025-07-31 10:23:31,678 - ModernClkinControlHandler - [ModernClkinControlHandler.py:159] - INFO - 设置寄存器默认值: 0x5A[CLKin1_DEMUX[1:0]] = 2
2025-07-31 10:23:31,678 - ModernClkinControlHandler - [ModernClkinControlHandler.py:170] - INFO - 寄存器默认值设置完成: CLKin0=120, CLKin1=16, CLKin2=150
2025-07-31 10:23:31,678 - ModernBaseHandler - [ModernBaseHandler.py:104] - INFO - UI已设置，开始构建控件映射
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: CLKin0Demux (QComboBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: CLKin1Demux (QComboBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: CLKinSel0Mux (QComboBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: CLKinSel0Type (QComboBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: CLKinSel1Mux (QComboBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: CLKinSel1Type (QComboBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: CLKinSelManual (QComboBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: DACClkCntr (QComboBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: DACClkMult (QComboBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: DACHighTrip (QSpinBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: DACLowTrip (QSpinBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: DACUpdateRate (QLineEdit)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: HOLDOVER_FORCE (QCheckBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: HoldOverEn (QCheckBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: HoldOverPLL1Det (QCheckBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: HoldoverExitMode (QCheckBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: LOSTimeout (QComboBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: LosExternalInput (QCheckBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: MANDAC (QSpinBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: ManDacEN (QCheckBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: OSCoutClockFormat (QComboBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: OSCoutMux (QComboBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: PLL1R0Div (QSpinBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: PLL1R1Div (QSpinBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: PLL1R2Div (QSpinBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: PLL1RRst (QCheckBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: PLL1RSyncEn (QCheckBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: PLL1_LD_MUX (QComboBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: PLL1_LD_TYPE (QComboBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: PLL2SyncEn (QCheckBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: PLL2_LD_MUX (QComboBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: PLL2_LD_TYPE (QComboBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: RB_DAC_HIGH (QCheckBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: RB_DAC_LOCKED (QCheckBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: RB_DAC_LOW (QCheckBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: RB_DAC_RAIL (QCheckBox)
2025-07-31 10:23:31,679 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: RB_DAC_VALUE (QLineEdit)
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: RGHOExitDacassist (QCheckBox)
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: RGHOExitDacassistStep (QComboBox)
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: RGHoFastEnterEn (QCheckBox)
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: RGVtunedetRelativeEn (QCheckBox)
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: RGZpsEn (QCheckBox)
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: ResetMux (QComboBox)
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: ResetType (QComboBox)
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: TrackEn (QCheckBox)
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: clkin0En (QCheckBox)
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: clkin0LOS (QCheckBox)
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: clkin0SEL (QCheckBox)
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: clkin1En (QCheckBox)
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: clkin1LOS (QCheckBox)
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: clkin1SEL (QCheckBox)
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: clkin2En (QCheckBox)
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: clkin2LOS (QCheckBox)
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: clkin2SEL (QCheckBox)
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: clkinSelAutoEn (QCheckBox)
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: clkinSelAutoRevertEn (QCheckBox)
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: clkinSelPinEn (QCheckBox)
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: clkinSelPinPol (QCheckBox)
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: label (QLabel)
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: lineEditClkin0 (QLineEdit)
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: lineEditClkin1 (QLineEdit)
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: lineEditClkin2Oscout (QLineEdit)
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: lineEditClkinSelOut (QLineEdit)
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: losEn (QCheckBox)
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:498] - DEBUG - 发现UI控件: syncSource (QComboBox)
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:502] - INFO - UI中发现 65 个控件
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x02 的控件 powerDown 在UI中未找到
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x10 的控件 DCLK0_1DIV 在UI中未找到
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x11 的控件 DCLK0_1DDLY 在UI中未找到
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x12 的控件 CLKout0_1PD 在UI中未找到
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x12 的控件 DCLK0_1DDLYPD 在UI中未找到
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x13 的控件 CLKout0_SRCMUX 在UI中未找到
2025-07-31 10:23:31,680 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x13 的控件 DCLK0_1PD 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x13 的控件 DCLK0_1BYPASS 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x13 的控件 DCLK0_1POL 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x13 的控件 DCLK0_1HSTrig 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x14 的控件 CLKout1_SRCMUX 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x14 的控件 SCLK0_1PD 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x14 的控件 SCLK0_1POL 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x14 的控件 SCLK0_1HSTrig 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x15 的控件 SCLK0_1ADLYEnb 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x15 的控件 SCLK0_1ADLY 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x16 的控件 SCLK0_1DDLY 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x17 的控件 CLKout1FMT 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x17 的控件 CLKout0FMT 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x18 的控件 DCLK2_3DIV 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x19 的控件 DCLK2_3DDLY 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x1A 的控件 CLKout2_3PD 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x1A 的控件 DCLK2_3DDLYPD 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x1B 的控件 CLKout2_SRCMUX 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x1B 的控件 DCLK2_3PD 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x1B 的控件 DCLK2_3BYPASS 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x1B 的控件 DCLK2_3POL 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x1B 的控件 DCLK2_3HSTrig 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x1C 的控件 CLKout3_SRCMUX 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x1C 的控件 SCLK2_3PD 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x1C 的控件 SCLK2_3POL 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x1C 的控件 SCLK2_3HSTrig 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x1D 的控件 SCLK2_3ADLYEnb 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x1D 的控件 SCLK2_3ADLY 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x1E 的控件 SCLK2_3DDLY 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x1F 的控件 CLKout3FMT 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x1F 的控件 CLKout2FMT 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x20 的控件 DCLK4_5DIV 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x21 的控件 DCLK4_5DDLY 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x22 的控件 CLKout4_5PD 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x22 的控件 DCLK4_5DDLYPD 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x23 的控件 CLKout4_SRCMUX 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x23 的控件 DCLK4_5PD 在UI中未找到
2025-07-31 10:23:31,681 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x23 的控件 DCLK4_5BYPASS 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x23 的控件 DCLK4_5POL 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x23 的控件 DCLK4_5HSTrig 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x24 的控件 CLKout5_SRCMUX 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x24 的控件 SCLK4_5PD 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x24 的控件 SCLK4_5POL 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x24 的控件 SCLK4_5HSTrig 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x25 的控件 SCLK4_5ADLYEnb 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x25 的控件 SCLK4_5ADLY 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x26 的控件 SCLK4_5DDLY 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x27 的控件 CLKout5FMT 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x27 的控件 CLKout4FMT 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x28 的控件 DCLK6_7DIV 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x29 的控件 DCLK6_7DDLY 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x2A 的控件 CLKout6_7PD 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x2A 的控件 DCLK6_7DDLYPD 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x2B 的控件 CLKout6_SRCMUX 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x2B 的控件 DCLK6_7PD 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x2B 的控件 DCLK6_7BYPASS 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x2B 的控件 DCLK6_7POL 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x2B 的控件 DCLK6_7HSTrig 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x2C 的控件 CLKout7_SRCMUX 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x2C 的控件 SCLK6_7PD 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x2C 的控件 SCLK6_7POL 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x2C 的控件 SCLK6_7HSTrig 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x2D 的控件 SCLK6_7ADLYEnb 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x2D 的控件 SCLK6_7ADLY 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x2E 的控件 SCLK6_7DDLY 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x2F 的控件 CLKout7FMT 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x2F 的控件 CLKout6FMT 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x30 的控件 DCLK8_9DIV 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x31 的控件 DCLK8_9DDLY 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x32 的控件 CLKout8_9PD 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x32 的控件 DCLK8_9DDLYPD 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x33 的控件 CLKout8_SRCMUX 在UI中未找到
2025-07-31 10:23:31,682 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x33 的控件 DCLK8_9PD 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x33 的控件 DCLK8_9BYPASS 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x33 的控件 DCLK8_9POL 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x33 的控件 DCLK8_9HSTrig 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x34 的控件 CLKout9_SRCMUX 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x34 的控件 SCLK8_9PD 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x34 的控件 SCLK8_9POL 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x34 的控件 SCLK8_9HSTrig 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x35 的控件 SCLK8_9ADLYEnb 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x35 的控件 SCLK8_9ADLY 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x36 的控件 SCLK8_9DDLY 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x37 的控件 CLKout9FMT 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x37 的控件 CLKout8FMT 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x38 的控件 DCLK10_11DIV 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x39 的控件 DCLK10_11DDLY 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x3A 的控件 CLKout10_11PD 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x3A 的控件 DCLK10_11DDLYPD 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x3B 的控件 CLKout10_SRCMUX 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x3B 的控件 DCLK10_11PD 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x3B 的控件 DCLK10_11BYPASS 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x3B 的控件 DCLK10_11POL 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x3B 的控件 DCLK10_11HSTrig 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x3C 的控件 CLKout11_SRCMUX 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x3C 的控件 SCLK10_11PD 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x3C 的控件 SCLK10_11POL 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x3C 的控件 SCLK10_11HSTrig 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x3D 的控件 SCLK10_11ADLYEnb 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x3D 的控件 SCLK10_11ADLY 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x3E 的控件 SCLK10_11DDLY 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x3F 的控件 CLKout11FMT 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x3F 的控件 CLKout10FMT 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x40 的控件 DCLK12_13DIV 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x41 的控件 DCLK12_13DDLY 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x42 的控件 CLKout12_13PD 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x42 的控件 DCLK12_13DDLYPD 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x43 的控件 CLKout12_SRCMUX 在UI中未找到
2025-07-31 10:23:31,683 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x43 的控件 DCLK12_13PD 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x43 的控件 DCLK12_13BYPASS 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x43 的控件 DCLK12_13POL 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x43 的控件 DCLK12_13HSTrig 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x44 的控件 CLKout13_SRCMUX 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x44 的控件 SCLK12_13PD 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x44 的控件 SCLK12_13POL 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x44 的控件 SCLK12_13HSTrig 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x45 的控件 SCLK12_13ADLYEnb 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x45 的控件 SCLK12_13ADLY 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x46 的控件 SCLK12_13DDLY 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x47 的控件 CLKout13FMT 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x47 的控件 CLKout12FMT 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x48 的控件 comboVcoMode 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 OSCoutMux 到寄存器 0x48
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 OSCoutClockFormat 到寄存器 0x48
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x49 的控件 SysrefReqEn 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x49 的控件 comboSysrefMux 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x4A 的控件 spinBoxSysrefDIV 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x4C 的控件 spinBoxSysrefDDLY 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x4E 的控件 comboSysrefPulseCnt 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x4F 的控件 PLL2RclkMux 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x4F 的控件 PLL2NclkMux 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x4F 的控件 PLL1NclkMux 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x4F 的控件 FBMUX 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x4F 的控件 FBMuxEn 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x50 的控件 PLL1PD 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x50 的控件 VCOLdoPD 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x50 的控件 VCOPD 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x50 的控件 OSCinPD 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x50 的控件 SysrefGBLPD 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x50 的控件 sysrefPD 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x50 的控件 sysrefDDLYPD 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x50 的控件 SysrefPlsrPd 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x51 的控件 sysrefDDLYdEn 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x51 的控件 DDLYd12EN 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x51 的控件 DDLYd10EN 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x51 的控件 DDLYd8EN 在UI中未找到
2025-07-31 10:23:31,684 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x51 的控件 DDLYd6EN 在UI中未找到
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x51 的控件 DDLYd4EN 在UI中未找到
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x51 的控件 DDLYd2EN 在UI中未找到
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x51 的控件 DDLYd0EN 在UI中未找到
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x52 的控件 comboDDLYdSysrefStep 在UI中未找到
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x52 的控件 DDLYdStepCNT_1 在UI中未找到
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x53 的控件 SysrefCLR 在UI中未找到
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x53 的控件 Sync1SHOTEn 在UI中未找到
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x53 的控件 SyncPOL 在UI中未找到
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x53 的控件 SyncEn 在UI中未找到
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x53 的控件 SyncPLL2DLD 在UI中未找到
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x53 的控件 SyncPLL1DLD 在UI中未找到
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x53 的控件 comboSyncMode 在UI中未找到
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x54 的控件 sysrefDissysref 在UI中未找到
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x54 的控件 SYNCDIS12 在UI中未找到
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x54 的控件 SYNCDIS10 在UI中未找到
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x54 的控件 SYNCDIS8 在UI中未找到
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x54 的控件 SYNCDIS6 在UI中未找到
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x54 的控件 SYNCDIS4 在UI中未找到
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x54 的控件 SYNCDIS2 在UI中未找到
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x54 的控件 SYNCDIS0 在UI中未找到
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 PLL1RSyncEn 到寄存器 0x55
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 syncSource 到寄存器 0x55
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 PLL2SyncEn 到寄存器 0x55
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x55 的控件 Div2 在UI中未找到
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x55 的控件 Fin0InputType 在UI中未找到
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 clkinSelPinEn 到寄存器 0x56
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 clkinSelPinPol 到寄存器 0x56
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 clkin2En 到寄存器 0x56
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 clkin1En 到寄存器 0x56
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 clkin0En 到寄存器 0x56
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 clkinSelAutoRevertEn 到寄存器 0x57
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 clkinSelAutoEn 到寄存器 0x57
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 CLKinSelManual 到寄存器 0x57
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 CLKin1Demux 到寄存器 0x57
2025-07-31 10:23:31,685 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 CLKin0Demux 到寄存器 0x57
2025-07-31 10:23:31,686 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 CLKinSel0Mux 到寄存器 0x58
2025-07-31 10:23:31,686 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 CLKinSel0Type 到寄存器 0x58
2025-07-31 10:23:31,686 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x59 的控件 SDIO_RDBK_TYPE 在UI中未找到
2025-07-31 10:23:31,686 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 CLKinSel1Mux 到寄存器 0x59
2025-07-31 10:23:31,686 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 CLKinSel1Type 到寄存器 0x59
2025-07-31 10:23:31,686 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 ResetMux 到寄存器 0x5A
2025-07-31 10:23:31,686 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 ResetType 到寄存器 0x5A
2025-07-31 10:23:31,686 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 LOSTimeout 到寄存器 0x5B
2025-07-31 10:23:31,686 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 losEn 到寄存器 0x5B
2025-07-31 10:23:31,686 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 TrackEn 到寄存器 0x5B
2025-07-31 10:23:31,686 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 HOLDOVER_FORCE 到寄存器 0x5B
2025-07-31 10:23:31,686 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 ManDacEN 到寄存器 0x5B
2025-07-31 10:23:31,686 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 MANDAC 到寄存器 0x5C
2025-07-31 10:23:31,686 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 RGVtunedetRelativeEn 到寄存器 0x5D
2025-07-31 10:23:31,686 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 DACLowTrip 到寄存器 0x5D
2025-07-31 10:23:31,686 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 DACClkMult 到寄存器 0x5E
2025-07-31 10:23:31,686 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 DACHighTrip 到寄存器 0x5E
2025-07-31 10:23:31,686 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 DACClkCntr 到寄存器 0x5F
2025-07-31 10:23:31,686 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 RGHoFastEnterEn 到寄存器 0x60
2025-07-31 10:23:31,686 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 RGHOExitDacassistStep 到寄存器 0x60
2025-07-31 10:23:31,686 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 RGHOExitDacassist 到寄存器 0x60
2025-07-31 10:23:31,686 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x60 的控件 RGCycleslipEn 在UI中未找到
2025-07-31 10:23:31,686 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 RGZpsEn 到寄存器 0x60
2025-07-31 10:23:31,686 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 HoldoverExitMode 到寄存器 0x60
2025-07-31 10:23:31,686 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 HoldOverPLL1Det 到寄存器 0x60
2025-07-31 10:23:31,686 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 LosExternalInput 到寄存器 0x60
2025-07-31 10:23:31,686 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 HoldOverEn 到寄存器 0x60
2025-07-31 10:23:31,686 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 PLL1R0Div 到寄存器 0x63
2025-07-31 10:23:31,686 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 PLL1R1Div 到寄存器 0x65
2025-07-31 10:23:31,686 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 PLL1R2Div 到寄存器 0x67
2025-07-31 10:23:31,686 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x69 的控件 PLL1NDivider 在UI中未找到
2025-07-31 10:23:31,686 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x6B 的控件 comboPLL1WindSize 在UI中未找到
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x6B 的控件 PLL1CPState 在UI中未找到
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x6B 的控件 PLL1PFDPolarity 在UI中未找到
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x6B 的控件 PLL1CPGain 在UI中未找到
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x6C 的控件 spinBoxPLL1DLDCNT 在UI中未找到
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 PLL1_LD_MUX 到寄存器 0x6F
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 PLL1_LD_TYPE 到寄存器 0x6F
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x70 的控件 PLL2RDivider 在UI中未找到
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x72 的控件 PLL2Prescaler 在UI中未找到
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x72 的控件 OSCin_FREQ 在UI中未找到
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x72 的控件 Doubler 在UI中未找到
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x79 的控件 PLL2WINDSIZE 在UI中未找到
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x79 的控件 PLL2CPGain 在UI中未找到
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x79 的控件 PLL2PFDPolarity 在UI中未找到
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x79 的控件 PLL2CPState 在UI中未找到
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x7A 的控件 spinBoxPLL2DLDCNT 在UI中未找到
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 PLL2_LD_MUX 到寄存器 0x7E
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 PLL2_LD_TYPE 到寄存器 0x7E
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x83 的控件 PLL2PrePD 在UI中未找到
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x83 的控件 PLL2PD 在UI中未找到
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x83 的控件 Fin0PD 在UI中未找到
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 PLL1RRst 到寄存器 0x87
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x93 的控件 RBPLL1DLD 在UI中未找到
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0x93 的控件 RBPLL2DLD 在UI中未找到
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 clkin2SEL 到寄存器 0x94
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 clkin1SEL 到寄存器 0x94
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 clkin0SEL 到寄存器 0x94
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 clkin2LOS 到寄存器 0x94
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 clkin1LOS 到寄存器 0x94
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 clkin0LOS 到寄存器 0x94
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 RB_DAC_RAIL 到寄存器 0x98
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 RB_DAC_HIGH 到寄存器 0x98
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 RB_DAC_LOW 到寄存器 0x98
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:510] - INFO - 映射控件 RB_DAC_LOCKED 到寄存器 0x98
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0xA1 的控件 PLL2C1 在UI中未找到
2025-07-31 10:23:31,687 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0xA1 的控件 PLL2C3 在UI中未找到
2025-07-31 10:23:31,688 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0xA1 的控件 PLL2R3 在UI中未找到
2025-07-31 10:23:31,688 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0xA2 的控件 RBPLL2VtuneADC 在UI中未找到
2025-07-31 10:23:31,688 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0xA2 的控件 AD_TSENSOR_OUT 在UI中未找到
2025-07-31 10:23:31,688 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0xA4 的控件 PLL2OpenLoop 在UI中未找到
2025-07-31 10:23:31,688 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0xA4 的控件 FCALEN 在UI中未找到
2025-07-31 10:23:31,688 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0xA4 的控件 FCALM1 在UI中未找到
2025-07-31 10:23:31,688 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0xA5 的控件 FCALM2 在UI中未找到
2025-07-31 10:23:31,688 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0xA6 的控件 RBFcalCAPMODE 在UI中未找到
2025-07-31 10:23:31,688 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0xA9 的控件 RGDCLK12_13HSEN 在UI中未找到
2025-07-31 10:23:31,688 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0xA9 的控件 RGDCLK10_11HSEN 在UI中未找到
2025-07-31 10:23:31,688 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0xA9 的控件 RGDCLK8_9HSEN 在UI中未找到
2025-07-31 10:23:31,688 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0xA9 的控件 RGDCLK6_7HSEN 在UI中未找到
2025-07-31 10:23:31,688 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0xA9 的控件 RGDCLK4_5HSEN 在UI中未找到
2025-07-31 10:23:31,688 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0xA9 的控件 RGDCLK2_3HSEN 在UI中未找到
2025-07-31 10:23:31,688 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0xA9 的控件 RGDCLK0_1HSEN 在UI中未找到
2025-07-31 10:23:31,688 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0xD8 的控件 SPI3WrireDis 在UI中未找到
2025-07-31 10:23:31,688 - ModernBaseHandler - [ModernBaseHandler.py:512] - DEBUG - 寄存器 0xDA 的控件 spiReset 在UI中未找到
2025-07-31 10:23:31,688 - ModernBaseHandler - [ModernBaseHandler.py:514] - INFO - 成功构建 58 个控件映射
2025-07-31 10:23:31,688 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 OSCoutMux 的信号
2025-07-31 10:23:31,688 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 OSCoutClockFormat 的信号
2025-07-31 10:23:31,688 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 PLL1RSyncEn 的信号
2025-07-31 10:23:31,688 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 syncSource 的信号
2025-07-31 10:23:31,688 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 PLL2SyncEn 的信号
2025-07-31 10:23:31,688 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 clkinSelPinEn 的信号
2025-07-31 10:23:31,688 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 clkinSelPinPol 的信号
2025-07-31 10:23:31,688 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 clkin2En 的信号
2025-07-31 10:23:31,688 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 clkin1En 的信号
2025-07-31 10:23:31,688 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 clkin0En 的信号
2025-07-31 10:23:31,688 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 clkinSelAutoRevertEn 的信号
2025-07-31 10:23:31,688 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 clkinSelAutoEn 的信号
2025-07-31 10:23:31,689 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 CLKinSelManual 的信号
2025-07-31 10:23:31,689 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 CLKin1Demux 的信号
2025-07-31 10:23:31,689 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 CLKin0Demux 的信号
2025-07-31 10:23:31,689 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 CLKinSel0Mux 的信号
2025-07-31 10:23:31,689 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 CLKinSel0Type 的信号
2025-07-31 10:23:31,689 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 CLKinSel1Mux 的信号
2025-07-31 10:23:31,689 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 CLKinSel1Type 的信号
2025-07-31 10:23:31,689 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 ResetMux 的信号
2025-07-31 10:23:31,689 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 ResetType 的信号
2025-07-31 10:23:31,689 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 LOSTimeout 的信号
2025-07-31 10:23:31,689 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 losEn 的信号
2025-07-31 10:23:31,689 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 TrackEn 的信号
2025-07-31 10:23:31,689 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 HOLDOVER_FORCE 的信号
2025-07-31 10:23:31,689 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 ManDacEN 的信号
2025-07-31 10:23:31,689 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 MANDAC 的信号
2025-07-31 10:23:31,689 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 RGVtunedetRelativeEn 的信号
2025-07-31 10:23:31,689 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 DACLowTrip 的信号
2025-07-31 10:23:31,689 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 DACClkMult 的信号
2025-07-31 10:23:31,689 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 DACHighTrip 的信号
2025-07-31 10:23:31,689 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 DACClkCntr 的信号
2025-07-31 10:23:31,689 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 RGHoFastEnterEn 的信号
2025-07-31 10:23:31,689 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 RGHOExitDacassistStep 的信号
2025-07-31 10:23:31,689 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 RGHOExitDacassist 的信号
2025-07-31 10:23:31,689 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 RGZpsEn 的信号
2025-07-31 10:23:31,689 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 HoldoverExitMode 的信号
2025-07-31 10:23:31,689 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 HoldOverPLL1Det 的信号
2025-07-31 10:23:31,689 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 LosExternalInput 的信号
2025-07-31 10:23:31,689 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 HoldOverEn 的信号
2025-07-31 10:23:31,689 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 PLL1R0Div 的信号
2025-07-31 10:23:31,689 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 PLL1R1Div 的信号
2025-07-31 10:23:31,690 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 PLL1R2Div 的信号
2025-07-31 10:23:31,690 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 PLL1_LD_MUX 的信号
2025-07-31 10:23:31,690 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 PLL1_LD_TYPE 的信号
2025-07-31 10:23:31,690 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 PLL2_LD_MUX 的信号
2025-07-31 10:23:31,690 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 PLL2_LD_TYPE 的信号
2025-07-31 10:23:31,690 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 PLL1RRst 的信号
2025-07-31 10:23:31,690 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 clkin2SEL 的信号
2025-07-31 10:23:31,690 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 clkin1SEL 的信号
2025-07-31 10:23:31,690 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 clkin0SEL 的信号
2025-07-31 10:23:31,690 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 clkin2LOS 的信号
2025-07-31 10:23:31,690 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 clkin1LOS 的信号
2025-07-31 10:23:31,690 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 clkin0LOS 的信号
2025-07-31 10:23:31,690 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 RB_DAC_RAIL 的信号
2025-07-31 10:23:31,690 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 RB_DAC_HIGH 的信号
2025-07-31 10:23:31,690 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 RB_DAC_LOW 的信号
2025-07-31 10:23:31,690 - ModernBaseHandler - [ModernBaseHandler.py:534] - DEBUG - 已连接控件 RB_DAC_LOCKED 的信号
2025-07-31 10:23:31,690 - ModernBaseHandler - [ModernBaseHandler.py:792] - DEBUG - 控件 OSCoutMux 还没有选项，延迟设置值 0
2025-07-31 10:23:31,690 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 OSCoutMux 的UI状态为 0
2025-07-31 10:23:31,690 - ModernBaseHandler - [ModernBaseHandler.py:792] - DEBUG - 控件 OSCoutClockFormat 还没有选项，延迟设置值 4
2025-07-31 10:23:31,690 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 OSCoutClockFormat 的UI状态为 4
2025-07-31 10:23:31,690 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 PLL1RSyncEn 的UI状态为 0
2025-07-31 10:23:31,690 - ModernBaseHandler - [ModernBaseHandler.py:792] - DEBUG - 控件 syncSource 还没有选项，延迟设置值 0
2025-07-31 10:23:31,690 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 syncSource 的UI状态为 0
2025-07-31 10:23:31,690 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 PLL2SyncEn 的UI状态为 0
2025-07-31 10:23:31,690 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 clkinSelPinEn 的UI状态为 0
2025-07-31 10:23:31,690 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 clkinSelPinPol 的UI状态为 0
2025-07-31 10:23:31,690 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 clkin2En 的UI状态为 0
2025-07-31 10:23:31,691 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 clkin1En 的UI状态为 1
2025-07-31 10:23:31,691 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 clkin0En 的UI状态为 1
2025-07-31 10:23:31,691 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 clkinSelAutoRevertEn 的UI状态为 0
2025-07-31 10:23:31,691 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 clkinSelAutoEn 的UI状态为 0
2025-07-31 10:23:31,691 - ModernBaseHandler - [ModernBaseHandler.py:792] - DEBUG - 控件 CLKinSelManual 还没有选项，延迟设置值 1
2025-07-31 10:23:31,691 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 CLKinSelManual 的UI状态为 1
2025-07-31 10:23:31,691 - ModernBaseHandler - [ModernBaseHandler.py:792] - DEBUG - 控件 CLKin1Demux 还没有选项，延迟设置值 2
2025-07-31 10:23:31,691 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 CLKin1Demux 的UI状态为 2
2025-07-31 10:23:31,691 - ModernBaseHandler - [ModernBaseHandler.py:792] - DEBUG - 控件 CLKin0Demux 还没有选项，延迟设置值 2
2025-07-31 10:23:31,691 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 CLKin0Demux 的UI状态为 2
2025-07-31 10:23:31,691 - ModernBaseHandler - [ModernBaseHandler.py:792] - DEBUG - 控件 CLKinSel0Mux 还没有选项，延迟设置值 0
2025-07-31 10:23:31,691 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 CLKinSel0Mux 的UI状态为 0
2025-07-31 10:23:31,691 - ModernBaseHandler - [ModernBaseHandler.py:792] - DEBUG - 控件 CLKinSel0Type 还没有选项，延迟设置值 2
2025-07-31 10:23:31,691 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 CLKinSel0Type 的UI状态为 2
2025-07-31 10:23:31,691 - ModernBaseHandler - [ModernBaseHandler.py:792] - DEBUG - 控件 CLKinSel1Mux 还没有选项，延迟设置值 0
2025-07-31 10:23:31,692 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 CLKinSel1Mux 的UI状态为 0
2025-07-31 10:23:31,692 - ModernBaseHandler - [ModernBaseHandler.py:792] - DEBUG - 控件 CLKinSel1Type 还没有选项，延迟设置值 2
2025-07-31 10:23:31,692 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 CLKinSel1Type 的UI状态为 2
2025-07-31 10:23:31,692 - ModernBaseHandler - [ModernBaseHandler.py:792] - DEBUG - 控件 ResetMux 还没有选项，延迟设置值 0
2025-07-31 10:23:31,692 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 ResetMux 的UI状态为 0
2025-07-31 10:23:31,692 - ModernBaseHandler - [ModernBaseHandler.py:792] - DEBUG - 控件 ResetType 还没有选项，延迟设置值 2
2025-07-31 10:23:31,692 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 ResetType 的UI状态为 2
2025-07-31 10:23:31,692 - ModernBaseHandler - [ModernBaseHandler.py:792] - DEBUG - 控件 LOSTimeout 还没有选项，延迟设置值 0
2025-07-31 10:23:31,692 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 LOSTimeout 的UI状态为 0
2025-07-31 10:23:31,692 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 losEn 的UI状态为 1
2025-07-31 10:23:31,692 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 TrackEn 的UI状态为 0
2025-07-31 10:23:31,692 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 HOLDOVER_FORCE 的UI状态为 0
2025-07-31 10:23:31,692 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 ManDacEN 的UI状态为 1
2025-07-31 10:23:31,692 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 MANDAC 的UI状态为 512
2025-07-31 10:23:31,692 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 RGVtunedetRelativeEn 的UI状态为 0
2025-07-31 10:23:31,692 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 DACLowTrip 的UI状态为 0
2025-07-31 10:23:31,692 - ModernBaseHandler - [ModernBaseHandler.py:792] - DEBUG - 控件 DACClkMult 还没有选项，延迟设置值 3
2025-07-31 10:23:31,692 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 DACClkMult 的UI状态为 3
2025-07-31 10:23:31,693 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 DACHighTrip 的UI状态为 0
2025-07-31 10:23:31,693 - ModernBaseHandler - [ModernBaseHandler.py:792] - DEBUG - 控件 DACClkCntr 还没有选项，延迟设置值 127
2025-07-31 10:23:31,693 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 DACClkCntr 的UI状态为 127
2025-07-31 10:23:31,693 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 RGHoFastEnterEn 的UI状态为 1
2025-07-31 10:23:31,693 - ModernBaseHandler - [ModernBaseHandler.py:792] - DEBUG - 控件 RGHOExitDacassistStep 还没有选项，延迟设置值 1
2025-07-31 10:23:31,693 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 RGHOExitDacassistStep 的UI状态为 1
2025-07-31 10:23:31,693 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 RGHOExitDacassist 的UI状态为 1
2025-07-31 10:23:31,693 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 RGZpsEn 的UI状态为 1
2025-07-31 10:23:31,693 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 HoldoverExitMode 的UI状态为 0
2025-07-31 10:23:31,693 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 HoldOverPLL1Det 的UI状态为 1
2025-07-31 10:23:31,693 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 LosExternalInput 的UI状态为 0
2025-07-31 10:23:31,693 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 HoldOverEn 的UI状态为 1
2025-07-31 10:23:31,694 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 PLL1R0Div 的UI状态为 120
2025-07-31 10:23:31,694 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 PLL1R1Div 的UI状态为 16
2025-07-31 10:23:31,694 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 PLL1R2Div 的UI状态为 150
2025-07-31 10:23:31,694 - ModernBaseHandler - [ModernBaseHandler.py:792] - DEBUG - 控件 PLL1_LD_MUX 还没有选项，延迟设置值 1
2025-07-31 10:23:31,694 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 PLL1_LD_MUX 的UI状态为 1
2025-07-31 10:23:31,694 - ModernBaseHandler - [ModernBaseHandler.py:792] - DEBUG - 控件 PLL1_LD_TYPE 还没有选项，延迟设置值 3
2025-07-31 10:23:31,694 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 PLL1_LD_TYPE 的UI状态为 3
2025-07-31 10:23:31,694 - ModernBaseHandler - [ModernBaseHandler.py:792] - DEBUG - 控件 PLL2_LD_MUX 还没有选项，延迟设置值 2
2025-07-31 10:23:31,694 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 PLL2_LD_MUX 的UI状态为 2
2025-07-31 10:23:31,694 - ModernBaseHandler - [ModernBaseHandler.py:792] - DEBUG - 控件 PLL2_LD_TYPE 还没有选项，延迟设置值 3
2025-07-31 10:23:31,694 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 PLL2_LD_TYPE 的UI状态为 3
2025-07-31 10:23:31,695 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 PLL1RRst 的UI状态为 0
2025-07-31 10:23:31,695 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 clkin2SEL 的UI状态为 0
2025-07-31 10:23:31,695 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 clkin1SEL 的UI状态为 0
2025-07-31 10:23:31,695 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 clkin0SEL 的UI状态为 1
2025-07-31 10:23:31,695 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 clkin2LOS 的UI状态为 0
2025-07-31 10:23:31,695 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 clkin1LOS 的UI状态为 1
2025-07-31 10:23:31,695 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 clkin0LOS 的UI状态为 0
2025-07-31 10:23:31,695 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 RB_DAC_RAIL 的UI状态为 1
2025-07-31 10:23:31,695 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 RB_DAC_HIGH 的UI状态为 1
2025-07-31 10:23:31,696 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 RB_DAC_LOW 的UI状态为 1
2025-07-31 10:23:31,696 - ModernBaseHandler - [ModernBaseHandler.py:819] - DEBUG - 已更新控件 RB_DAC_LOCKED 的UI状态为 0
2025-07-31 10:23:31,696 - ModernClkinControlHandler - [ModernClkinControlHandler.py:179] - INFO - 执行时钟输入控制的后初始化设置...
2025-07-31 10:23:31,696 - ModernClkinControlHandler - [ModernClkinControlHandler.py:470] - INFO - 开始设置ComboBox选项，共有 20 个控件
2025-07-31 10:23:31,696 - ModernClkinControlHandler - [ModernClkinControlHandler.py:474] - DEBUG - 处理控件: CLKin0Demux
2025-07-31 10:23:31,696 - ModernClkinControlHandler - [ModernClkinControlHandler.py:499] - INFO - 已为 CLKin0Demux 设置 4 个选项
2025-07-31 10:23:31,696 - ModernClkinControlHandler - [ModernClkinControlHandler.py:474] - DEBUG - 处理控件: CLKin1Demux
2025-07-31 10:23:31,696 - ModernClkinControlHandler - [ModernClkinControlHandler.py:499] - INFO - 已为 CLKin1Demux 设置 4 个选项
2025-07-31 10:23:31,696 - ModernClkinControlHandler - [ModernClkinControlHandler.py:474] - DEBUG - 处理控件: OSCoutMux
2025-07-31 10:23:31,696 - ModernClkinControlHandler - [ModernClkinControlHandler.py:499] - INFO - 已为 OSCoutMux 设置 2 个选项
2025-07-31 10:23:31,696 - ModernClkinControlHandler - [ModernClkinControlHandler.py:474] - DEBUG - 处理控件: CLKinSelManual
2025-07-31 10:23:31,696 - ModernClkinControlHandler - [ModernClkinControlHandler.py:499] - INFO - 已为 CLKinSelManual 设置 4 个选项
2025-07-31 10:23:31,696 - ModernClkinControlHandler - [ModernClkinControlHandler.py:474] - DEBUG - 处理控件: CLKinSel0Type
2025-07-31 10:23:31,696 - ModernClkinControlHandler - [ModernClkinControlHandler.py:499] - INFO - 已为 CLKinSel0Type 设置 7 个选项
2025-07-31 10:23:31,696 - ModernClkinControlHandler - [ModernClkinControlHandler.py:474] - DEBUG - 处理控件: CLKinSel1Type
2025-07-31 10:23:31,697 - ModernClkinControlHandler - [ModernClkinControlHandler.py:499] - INFO - 已为 CLKinSel1Type 设置 7 个选项
2025-07-31 10:23:31,697 - ModernClkinControlHandler - [ModernClkinControlHandler.py:474] - DEBUG - 处理控件: ResetMux
2025-07-31 10:23:31,697 - ModernClkinControlHandler - [ModernClkinControlHandler.py:499] - INFO - 已为 ResetMux 设置 7 个选项
2025-07-31 10:23:31,697 - ModernClkinControlHandler - [ModernClkinControlHandler.py:474] - DEBUG - 处理控件: ResetType
2025-07-31 10:23:31,697 - ModernClkinControlHandler - [ModernClkinControlHandler.py:499] - INFO - 已为 ResetType 设置 7 个选项
2025-07-31 10:23:31,697 - ModernClkinControlHandler - [ModernClkinControlHandler.py:474] - DEBUG - 处理控件: DACClkMult
2025-07-31 10:23:31,697 - ModernClkinControlHandler - [ModernClkinControlHandler.py:499] - INFO - 已为 DACClkMult 设置 4 个选项
2025-07-31 10:23:31,697 - ModernClkinControlHandler - [ModernClkinControlHandler.py:474] - DEBUG - 处理控件: DACClkCntr
2025-07-31 10:23:31,698 - ModernClkinControlHandler - [ModernClkinControlHandler.py:499] - INFO - 已为 DACClkCntr 设置 256 个选项
2025-07-31 10:23:31,698 - ModernClkinControlHandler - [ModernClkinControlHandler.py:474] - DEBUG - 处理控件: RGHOExitDacassistStep
2025-07-31 10:23:31,698 - ModernClkinControlHandler - [ModernClkinControlHandler.py:499] - INFO - 已为 RGHOExitDacassistStep 设置 4 个选项
2025-07-31 10:23:31,698 - ModernClkinControlHandler - [ModernClkinControlHandler.py:474] - DEBUG - 处理控件: PLL1_LD_MUX
2025-07-31 10:23:31,698 - ModernClkinControlHandler - [ModernClkinControlHandler.py:499] - INFO - 已为 PLL1_LD_MUX 设置 15 个选项
2025-07-31 10:23:31,698 - ModernClkinControlHandler - [ModernClkinControlHandler.py:474] - DEBUG - 处理控件: PLL1_LD_TYPE
2025-07-31 10:23:31,698 - ModernClkinControlHandler - [ModernClkinControlHandler.py:499] - INFO - 已为 PLL1_LD_TYPE 设置 7 个选项
2025-07-31 10:23:31,698 - ModernClkinControlHandler - [ModernClkinControlHandler.py:474] - DEBUG - 处理控件: PLL2_LD_MUX
2025-07-31 10:23:31,698 - ModernClkinControlHandler - [ModernClkinControlHandler.py:499] - INFO - 已为 PLL2_LD_MUX 设置 16 个选项
2025-07-31 10:23:31,698 - ModernClkinControlHandler - [ModernClkinControlHandler.py:474] - DEBUG - 处理控件: PLL2_LD_TYPE
2025-07-31 10:23:31,698 - ModernClkinControlHandler - [ModernClkinControlHandler.py:499] - INFO - 已为 PLL2_LD_TYPE 设置 7 个选项
2025-07-31 10:23:31,698 - ModernClkinControlHandler - [ModernClkinControlHandler.py:474] - DEBUG - 处理控件: CLKinSel0Mux
2025-07-31 10:23:31,699 - ModernClkinControlHandler - [ModernClkinControlHandler.py:499] - INFO - 已为 CLKinSel0Mux 设置 8 个选项
2025-07-31 10:23:31,699 - ModernClkinControlHandler - [ModernClkinControlHandler.py:474] - DEBUG - 处理控件: CLKinSel1Mux
2025-07-31 10:23:31,699 - ModernClkinControlHandler - [ModernClkinControlHandler.py:499] - INFO - 已为 CLKinSel1Mux 设置 8 个选项
2025-07-31 10:23:31,699 - ModernClkinControlHandler - [ModernClkinControlHandler.py:474] - DEBUG - 处理控件: OSCoutClockFormat
2025-07-31 10:23:31,699 - ModernClkinControlHandler - [ModernClkinControlHandler.py:499] - INFO - 已为 OSCoutClockFormat 设置 15 个选项
2025-07-31 10:23:31,699 - ModernClkinControlHandler - [ModernClkinControlHandler.py:474] - DEBUG - 处理控件: syncSource
2025-07-31 10:23:31,699 - ModernClkinControlHandler - [ModernClkinControlHandler.py:499] - INFO - 已为 syncSource 设置 4 个选项
2025-07-31 10:23:31,699 - ModernClkinControlHandler - [ModernClkinControlHandler.py:474] - DEBUG - 处理控件: LOSTimeout
2025-07-31 10:23:31,699 - ModernClkinControlHandler - [ModernClkinControlHandler.py:499] - INFO - 已为 LOSTimeout 设置 8 个选项
2025-07-31 10:23:31,699 - ModernClkinControlHandler - [ModernClkinControlHandler.py:505] - INFO - ComboBox选项设置完成
2025-07-31 10:23:31,699 - ModernBaseHandler - [ModernBaseHandler.py:1111] - INFO - 已应用待设置的ComboBox值: OSCoutMux = 0
2025-07-31 10:23:31,702 - ModernBaseHandler - [ModernBaseHandler.py:1111] - INFO - 已应用待设置的ComboBox值: OSCoutClockFormat = 4
2025-07-31 10:23:31,703 - ModernBaseHandler - [ModernBaseHandler.py:1111] - INFO - 已应用待设置的ComboBox值: syncSource = 0
2025-07-31 10:23:31,703 - ModernBaseHandler - [ModernBaseHandler.py:1111] - INFO - 已应用待设置的ComboBox值: CLKinSelManual = 1
2025-07-31 10:23:31,703 - ModernBaseHandler - [ModernBaseHandler.py:1111] - INFO - 已应用待设置的ComboBox值: CLKin1Demux = 2
2025-07-31 10:23:31,703 - ModernBaseHandler - [ModernBaseHandler.py:1111] - INFO - 已应用待设置的ComboBox值: CLKin0Demux = 2
2025-07-31 10:23:31,703 - ModernBaseHandler - [ModernBaseHandler.py:1111] - INFO - 已应用待设置的ComboBox值: CLKinSel0Mux = 0
2025-07-31 10:23:31,705 - ModernBaseHandler - [ModernBaseHandler.py:1111] - INFO - 已应用待设置的ComboBox值: CLKinSel0Type = 2
2025-07-31 10:23:31,705 - ModernBaseHandler - [ModernBaseHandler.py:1111] - INFO - 已应用待设置的ComboBox值: CLKinSel1Mux = 0
2025-07-31 10:23:31,705 - ModernBaseHandler - [ModernBaseHandler.py:1111] - INFO - 已应用待设置的ComboBox值: CLKinSel1Type = 2
2025-07-31 10:23:31,705 - ModernBaseHandler - [ModernBaseHandler.py:1111] - INFO - 已应用待设置的ComboBox值: ResetMux = 0
2025-07-31 10:23:31,705 - ModernBaseHandler - [ModernBaseHandler.py:1111] - INFO - 已应用待设置的ComboBox值: ResetType = 2
2025-07-31 10:23:31,705 - ModernBaseHandler - [ModernBaseHandler.py:1111] - INFO - 已应用待设置的ComboBox值: LOSTimeout = 0
2025-07-31 10:23:31,705 - ModernBaseHandler - [ModernBaseHandler.py:1111] - INFO - 已应用待设置的ComboBox值: DACClkMult = 3
2025-07-31 10:23:31,705 - ModernBaseHandler - [ModernBaseHandler.py:1111] - INFO - 已应用待设置的ComboBox值: DACClkCntr = 127
2025-07-31 10:23:31,706 - ModernBaseHandler - [ModernBaseHandler.py:1111] - INFO - 已应用待设置的ComboBox值: RGHOExitDacassistStep = 1
2025-07-31 10:23:31,706 - ModernBaseHandler - [ModernBaseHandler.py:1111] - INFO - 已应用待设置的ComboBox值: PLL1_LD_MUX = 1
2025-07-31 10:23:31,706 - ModernBaseHandler - [ModernBaseHandler.py:1111] - INFO - 已应用待设置的ComboBox值: PLL1_LD_TYPE = 3
2025-07-31 10:23:31,706 - ModernBaseHandler - [ModernBaseHandler.py:1111] - INFO - 已应用待设置的ComboBox值: PLL2_LD_MUX = 2
2025-07-31 10:23:31,706 - ModernBaseHandler - [ModernBaseHandler.py:1111] - INFO - 已应用待设置的ComboBox值: PLL2_LD_TYPE = 3
2025-07-31 10:23:31,706 - ModernBaseHandler - [ModernBaseHandler.py:1126] - INFO - 所有待设置的ComboBox值已应用完成
2025-07-31 10:23:31,706 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1388] - INFO - 已将手动初始化的控件 lineEditClkin0 添加到映射表
2025-07-31 10:23:31,706 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1388] - INFO - 已将手动初始化的控件 lineEditClkin1 添加到映射表
2025-07-31 10:23:31,706 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1388] - INFO - 已将手动初始化的控件 lineEditClkin2Oscout 添加到映射表
2025-07-31 10:23:31,706 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1388] - INFO - 已将手动初始化的控件 lineEditClkinSelOut 添加到映射表
2025-07-31 10:23:31,706 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1390] - INFO - 已注册 4 个手动初始化的控件
2025-07-31 10:23:31,706 - RegisterUpdateBus - [RegisterUpdateBus.py:743] - DEBUG - RegisterUpdateBus: 窗口 ClkinControl 没有缓存的LineEdit值
2025-07-31 10:23:31,706 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1440] - DEBUG - 没有缓存的值: lineEditClkin0
2025-07-31 10:23:31,706 - RegisterUpdateBus - [RegisterUpdateBus.py:743] - DEBUG - RegisterUpdateBus: 窗口 ClkinControl 没有缓存的LineEdit值
2025-07-31 10:23:31,706 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1440] - DEBUG - 没有缓存的值: lineEditClkin1
2025-07-31 10:23:31,706 - RegisterUpdateBus - [RegisterUpdateBus.py:743] - DEBUG - RegisterUpdateBus: 窗口 ClkinControl 没有缓存的LineEdit值
2025-07-31 10:23:31,706 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1440] - DEBUG - 没有缓存的值: lineEditClkin2Oscout
2025-07-31 10:23:31,706 - RegisterUpdateBus - [RegisterUpdateBus.py:743] - DEBUG - RegisterUpdateBus: 窗口 ClkinControl 没有缓存的LineEdit值
2025-07-31 10:23:31,706 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1440] - DEBUG - 没有缓存的值: lineEditClkinSelOut
2025-07-31 10:23:31,706 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1447] - DEBUG - 没有从缓存恢复任何LineEdit控件的值
2025-07-31 10:23:31,707 - ModernClkinControlHandler - [ModernClkinControlHandler.py:831] - INFO - 【现代化时钟输入窗口】时钟源切换: 索引=1
2025-07-31 10:23:31,707 - ModernClkinControlHandler - [ModernClkinControlHandler.py:848] - INFO - 【现代化时钟输入窗口】时钟源切换调试: 索引=1, 源=CLKin1
2025-07-31 10:23:31,707 - ModernClkinControlHandler - [ModernClkinControlHandler.py:849] - INFO -   显示值(clkin_value): '122.88'
2025-07-31 10:23:31,707 - ModernClkinControlHandler - [ModernClkinControlHandler.py:850] - INFO -   信号值(freq_value): 122.88
2025-07-31 10:23:31,707 - ModernClkinControlHandler - [ModernClkinControlHandler.py:851] - INFO -   分频值(divider_value): 16
2025-07-31 10:23:31,707 - RegisterUpdateBus - [RegisterUpdateBus.py:720] - DEBUG - RegisterUpdateBus: 已缓存LineEdit值: ClkinControl.lineEditClkinSelOut = 'CLKin1'
2025-07-31 10:23:31,707 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1460] - DEBUG - 已缓存LineEdit值: lineEditClkinSelOut = 'CLKin1'
2025-07-31 10:23:31,707 - RegisterUpdateBus - [RegisterUpdateBus.py:448] - INFO - 发送时钟源选择: 源=CLKin1, 频率=122.88MHz, 分频比=16
2025-07-31 10:23:31,707 - RegisterUpdateBus - [RegisterUpdateBus.py:138] - INFO - 时钟源选择: 源=CLKin1, 频率=122.88MHz, 分频比=16
2025-07-31 10:23:31,707 - RegisterUpdateBus - [RegisterUpdateBus.py:281] - INFO - 设置时钟源 ClkIn1 的频率为 122.88MHz
2025-07-31 10:23:31,707 - RegisterUpdateBus - [RegisterUpdateBus.py:309] - INFO - 设置时钟源 ClkIn1 的分频比为 16
2025-07-31 10:23:31,707 - RegisterUpdateBus - [RegisterUpdateBus.py:281] - INFO - 设置时钟源 ClkIn1 的频率为 122.88MHz
2025-07-31 10:23:31,707 - RegisterUpdateBus - [RegisterUpdateBus.py:309] - INFO - 设置时钟源 ClkIn1 的分频比为 16
2025-07-31 10:23:31,707 - RegisterUpdateBus - [RegisterUpdateBus.py:166] - INFO - 已更新时钟源配置: ClkIn1, 频率=122.88MHz, 分频比=16
2025-07-31 10:23:31,707 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1329] - INFO - 【现代化时钟输入窗口】已发布时钟源更新: 源=CLKin1, 频率=122.88MHz, 分频比=16
2025-07-31 10:23:31,707 - ModernClkinControlHandler - [ModernClkinControlHandler.py:831] - INFO - 【现代化时钟输入窗口】时钟源切换: 索引=1
2025-07-31 10:23:31,707 - ModernClkinControlHandler - [ModernClkinControlHandler.py:848] - INFO - 【现代化时钟输入窗口】时钟源切换调试: 索引=1, 源=CLKin1
2025-07-31 10:23:31,707 - ModernClkinControlHandler - [ModernClkinControlHandler.py:849] - INFO -   显示值(clkin_value): '122.88'
2025-07-31 10:23:31,707 - ModernClkinControlHandler - [ModernClkinControlHandler.py:850] - INFO -   信号值(freq_value): 122.88
2025-07-31 10:23:31,707 - ModernClkinControlHandler - [ModernClkinControlHandler.py:851] - INFO -   分频值(divider_value): 16
2025-07-31 10:23:31,707 - RegisterUpdateBus - [RegisterUpdateBus.py:720] - DEBUG - RegisterUpdateBus: 已缓存LineEdit值: ClkinControl.lineEditClkinSelOut = 'CLKin1'
2025-07-31 10:23:31,707 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1460] - DEBUG - 已缓存LineEdit值: lineEditClkinSelOut = 'CLKin1'
2025-07-31 10:23:31,707 - RegisterUpdateBus - [RegisterUpdateBus.py:448] - INFO - 发送时钟源选择: 源=CLKin1, 频率=122.88MHz, 分频比=16
2025-07-31 10:23:31,707 - RegisterUpdateBus - [RegisterUpdateBus.py:138] - INFO - 时钟源选择: 源=CLKin1, 频率=122.88MHz, 分频比=16
2025-07-31 10:23:31,707 - RegisterUpdateBus - [RegisterUpdateBus.py:281] - INFO - 设置时钟源 ClkIn1 的频率为 122.88MHz
2025-07-31 10:23:31,707 - RegisterUpdateBus - [RegisterUpdateBus.py:309] - INFO - 设置时钟源 ClkIn1 的分频比为 16
2025-07-31 10:23:31,707 - RegisterUpdateBus - [RegisterUpdateBus.py:281] - INFO - 设置时钟源 ClkIn1 的频率为 122.88MHz
2025-07-31 10:23:31,707 - RegisterUpdateBus - [RegisterUpdateBus.py:309] - INFO - 设置时钟源 ClkIn1 的分频比为 16
2025-07-31 10:23:31,707 - RegisterUpdateBus - [RegisterUpdateBus.py:166] - INFO - 已更新时钟源配置: ClkIn1, 频率=122.88MHz, 分频比=16
2025-07-31 10:23:31,707 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1329] - INFO - 【现代化时钟输入窗口】已发布时钟源更新: 源=CLKin1, 频率=122.88MHz, 分频比=16
2025-07-31 10:23:31,707 - RegisterUpdateBus - [RegisterUpdateBus.py:448] - INFO - 发送时钟源选择: 源=CLKin1, 频率=122.88MHz, 分频比=16
2025-07-31 10:23:31,707 - RegisterUpdateBus - [RegisterUpdateBus.py:138] - INFO - 时钟源选择: 源=CLKin1, 频率=122.88MHz, 分频比=16
2025-07-31 10:23:31,707 - RegisterUpdateBus - [RegisterUpdateBus.py:281] - INFO - 设置时钟源 ClkIn1 的频率为 122.88MHz
2025-07-31 10:23:31,707 - RegisterUpdateBus - [RegisterUpdateBus.py:309] - INFO - 设置时钟源 ClkIn1 的分频比为 16
2025-07-31 10:23:31,707 - RegisterUpdateBus - [RegisterUpdateBus.py:281] - INFO - 设置时钟源 ClkIn1 的频率为 122.88MHz
2025-07-31 10:23:31,707 - RegisterUpdateBus - [RegisterUpdateBus.py:309] - INFO - 设置时钟源 ClkIn1 的分频比为 16
2025-07-31 10:23:31,707 - RegisterUpdateBus - [RegisterUpdateBus.py:166] - INFO - 已更新时钟源配置: ClkIn1, 频率=122.88MHz, 分频比=16
2025-07-31 10:23:31,709 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1329] - INFO - 【现代化时钟输入窗口】已发布时钟源更新: 源=CLKin1, 频率=122.88MHz, 分频比=16
2025-07-31 10:23:31,709 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1349] - INFO - 初始化时发送时钟源信息: 源=CLKin1, 频率=122.88MHz, 分频比=16
2025-07-31 10:23:31,709 - ModernClkinControlHandler - [ModernClkinControlHandler.py:204] - INFO - 时钟输入控制后初始化设置完成
2025-07-31 10:23:31,734 - ModernBaseHandler - [ModernBaseHandler.py:273] - INFO - ModernClkinControlHandler: 窗口已居中显示在 (0, 115)，窗口尺寸: 2150x850
2025-07-31 10:23:31,734 - ModernBaseHandler - [ModernBaseHandler.py:289] - DEBUG - ModernClkinControlHandler: 已设置窗口激活功能
2025-07-31 10:23:31,734 - ModernBaseHandler - [ModernBaseHandler.py:242] - INFO - ModernClkinControlHandler: 设置窗口默认尺寸为 2150 x 850，已居中显示
2025-07-31 10:23:31,734 - ModernBaseHandler - [ModernBaseHandler.py:121] - INFO - ModernBaseHandler: 初始化完成
2025-07-31 10:23:31,735 - PluginWindowService - [PluginWindowService.py:230] - DEBUG - 窗口parent()方法调用失败，但这不影响窗口有效性
2025-07-31 10:23:31,735 - PluginWindowService - [PluginWindowService.py:48] - INFO - 显示现有插件窗口: 时钟输入控制
2025-07-31 10:23:31,754 - ModernClkinControlHandler - [ModernClkinControlHandler.py:994] - DEBUG - 开始计算DAC Update Rate...
2025-07-31 10:23:31,754 - RegisterUpdateBus - [RegisterUpdateBus.py:525] - DEBUG - RegisterUpdateBus: 获取缓存的PLL1PFDFreq值: 1.024 MHz
2025-07-31 10:23:31,754 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1109] - DEBUG - 从RegisterUpdateBus缓存获取PLL1PFDFreq值: 1.024 MHz
2025-07-31 10:23:31,754 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1020] - DEBUG - DAC Update Rate计算参数: mult=16384, cntr=127, pfd_freq=1.024 MHz
2025-07-31 10:23:31,755 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1033] - INFO - ✅ DAC Update Rate计算完成: 16384 * 127 / 1024000.0 Hz = 2.0 (PLL1PFDFreq: 1.024 MHz)
2025-07-31 10:23:31,759 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 时钟输入(&I)
2025-07-31 10:23:31,858 - ModernClkinControlHandler - [ModernClkinControlHandler.py:994] - DEBUG - 开始计算DAC Update Rate...
2025-07-31 10:23:31,858 - RegisterUpdateBus - [RegisterUpdateBus.py:525] - DEBUG - RegisterUpdateBus: 获取缓存的PLL1PFDFreq值: 1.024 MHz
2025-07-31 10:23:31,858 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1109] - DEBUG - 从RegisterUpdateBus缓存获取PLL1PFDFreq值: 1.024 MHz
2025-07-31 10:23:31,858 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1020] - DEBUG - DAC Update Rate计算参数: mult=16384, cntr=127, pfd_freq=1.024 MHz
2025-07-31 10:23:31,858 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1033] - INFO - ✅ DAC Update Rate计算完成: 16384 * 127 / 1024000.0 Hz = 2.0 (PLL1PFDFreq: 1.024 MHz)
2025-07-31 10:23:32,158 - ModernClkinControlHandler - [ModernClkinControlHandler.py:994] - DEBUG - 开始计算DAC Update Rate...
2025-07-31 10:23:32,158 - RegisterUpdateBus - [RegisterUpdateBus.py:525] - DEBUG - RegisterUpdateBus: 获取缓存的PLL1PFDFreq值: 1.024 MHz
2025-07-31 10:23:32,158 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1109] - DEBUG - 从RegisterUpdateBus缓存获取PLL1PFDFreq值: 1.024 MHz
2025-07-31 10:23:32,158 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1020] - DEBUG - DAC Update Rate计算参数: mult=16384, cntr=127, pfd_freq=1.024 MHz
2025-07-31 10:23:32,158 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1033] - INFO - ✅ DAC Update Rate计算完成: 16384 * 127 / 1024000.0 Hz = 2.0 (PLL1PFDFreq: 1.024 MHz)
2025-07-31 10:23:32,226 - MenuClickFixer - [MenuClickFixer.py:87] - DEBUG - 鼠标悬停: 模式设置(&M)
2025-07-31 10:23:32,657 - ModernClkinControlHandler - [ModernClkinControlHandler.py:994] - DEBUG - 开始计算DAC Update Rate...
2025-07-31 10:23:32,657 - RegisterUpdateBus - [RegisterUpdateBus.py:525] - DEBUG - RegisterUpdateBus: 获取缓存的PLL1PFDFreq值: 1.024 MHz
2025-07-31 10:23:32,657 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1109] - DEBUG - 从RegisterUpdateBus缓存获取PLL1PFDFreq值: 1.024 MHz
2025-07-31 10:23:32,658 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1020] - DEBUG - DAC Update Rate计算参数: mult=16384, cntr=127, pfd_freq=1.024 MHz
2025-07-31 10:23:32,658 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1033] - INFO - ✅ DAC Update Rate计算完成: 16384 * 127 / 1024000.0 Hz = 2.0 (PLL1PFDFreq: 1.024 MHz)
2025-07-31 10:23:32,832 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x93
2025-07-31 10:23:32,833 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x93, 值=0073
2025-07-31 10:23:32,833 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x93 = 0x0073
2025-07-31 10:23:32,833 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x94
2025-07-31 10:23:32,833 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x94, 值=0009
2025-07-31 10:23:32,833 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x94 = 0x0009
2025-07-31 10:23:32,833 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x95
2025-07-31 10:23:32,833 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x95, 值=021A
2025-07-31 10:23:32,833 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x95 = 0x021A
2025-07-31 10:23:32,833 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x98
2025-07-31 10:23:32,833 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x98, 值=FFB0
2025-07-31 10:23:32,833 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x98 = 0xFFB0
2025-07-31 10:23:33,395 - PluginDockService - [PluginDockService.py:542] - DEBUG - 🔧 [拖拽监控] 检测到鼠标按下 - 时钟输入控制, 位置: PyQt5.QtCore.QPoint(1216, 147)
2025-07-31 10:23:33,458 - PluginDockService - [PluginDockService.py:549] - INFO - 🔧 [拖拽监控] 检测到拖拽开始 - 时钟输入控制, 距离: 351
2025-07-31 10:23:33,458 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-31 10:23:33,524 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-31 10:23:33,583 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-31 10:23:33,648 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-31 10:23:33,708 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-31 10:23:33,775 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-31 10:23:33,833 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-31 10:23:33,895 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-31 10:23:33,959 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-31 10:23:34,022 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-31 10:23:34,085 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-31 10:23:34,144 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-31 10:23:34,213 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-31 10:23:34,278 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-31 10:23:34,341 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-31 10:23:34,395 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-31 10:23:34,457 - PluginDockService - [PluginDockService.py:556] - INFO - 🔧 [拖拽监控] 检测到鼠标释放 - 时钟输入控制, 位置: PyQt5.QtCore.QPoint(3725, 305)
2025-07-31 10:23:34,458 - PluginDockService - [PluginDockService.py:626] - DEBUG - ❌ [拖拽停靠] 不在停靠区域，保持悬浮: 时钟输入控制
2025-07-31 10:23:34,458 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-31 10:23:34,458 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-31 10:23:36,535 - PluginDockService - [PluginDockService.py:542] - DEBUG - 🔧 [拖拽监控] 检测到鼠标按下 - 时钟输入控制, 位置: PyQt5.QtCore.QPoint(4647, 769)
2025-07-31 10:23:36,588 - PluginDockService - [PluginDockService.py:549] - INFO - 🔧 [拖拽监控] 检测到拖拽开始 - 时钟输入控制, 距离: 46
2025-07-31 10:23:36,588 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-31 10:23:37,646 - PluginDockService - [PluginDockService.py:556] - INFO - 🔧 [拖拽监控] 检测到鼠标释放 - 时钟输入控制, 位置: PyQt5.QtCore.QPoint(4537, 781)
2025-07-31 10:23:37,647 - PluginDockService - [PluginDockService.py:626] - DEBUG - ❌ [拖拽停靠] 不在停靠区域，保持悬浮: 时钟输入控制
2025-07-31 10:23:37,647 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-31 10:23:37,647 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-31 10:23:37,839 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x93
2025-07-31 10:23:37,839 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x93, 值=0077
2025-07-31 10:23:37,839 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x93 = 0x0077
2025-07-31 10:23:37,839 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x94
2025-07-31 10:23:37,840 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x94, 值=000C
2025-07-31 10:23:37,840 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x94 = 0x000C
2025-07-31 10:23:37,840 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x95
2025-07-31 10:23:37,840 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x95, 值=021C
2025-07-31 10:23:37,841 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x95 = 0x021C
2025-07-31 10:23:37,841 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x98
2025-07-31 10:23:37,841 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x98, 值=FFB4
2025-07-31 10:23:37,841 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x98 = 0xFFB4
2025-07-31 10:23:39,405 - PluginDockService - [PluginDockService.py:542] - DEBUG - 🔧 [拖拽监控] 检测到鼠标按下 - 时钟输入控制, 位置: PyQt5.QtCore.QPoint(3458, 1146)
2025-07-31 10:23:42,836 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x93
2025-07-31 10:23:42,837 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x93, 值=007B
2025-07-31 10:23:42,837 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x93 = 0x007B
2025-07-31 10:23:42,837 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x94
2025-07-31 10:23:42,837 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x94, 值=0010
2025-07-31 10:23:42,837 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x94 = 0x0010
2025-07-31 10:23:42,837 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x95
2025-07-31 10:23:42,838 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x95, 值=021E
2025-07-31 10:23:42,838 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x95 = 0x021E
2025-07-31 10:23:42,838 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x98
2025-07-31 10:23:42,838 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x98, 值=FFB5
2025-07-31 10:23:42,838 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x98 = 0xFFB5
2025-07-31 10:23:45,332 - PluginDockService - [PluginDockService.py:556] - INFO - 🔧 [拖拽监控] 检测到鼠标释放 - 时钟输入控制, 位置: PyQt5.QtCore.QPoint(4113, 1049)
2025-07-31 10:23:45,332 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-31 10:23:47,836 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x93
2025-07-31 10:23:47,837 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x93, 值=0078
2025-07-31 10:23:47,837 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x93 = 0x0078
2025-07-31 10:23:47,837 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x94
2025-07-31 10:23:47,837 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x94, 值=000D
2025-07-31 10:23:47,837 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x94 = 0x000D
2025-07-31 10:23:47,837 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x95
2025-07-31 10:23:47,837 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x95, 值=0219
2025-07-31 10:23:47,837 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x95 = 0x0219
2025-07-31 10:23:47,838 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x98
2025-07-31 10:23:47,838 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x98, 值=FFB9
2025-07-31 10:23:47,838 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x98 = 0xFFB9
2025-07-31 10:23:52,838 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x93
2025-07-31 10:23:52,838 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x93, 值=007B
2025-07-31 10:23:52,838 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x93 = 0x007B
2025-07-31 10:23:52,838 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x94
2025-07-31 10:23:52,838 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x94, 值=0012
2025-07-31 10:23:52,838 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x94 = 0x0012
2025-07-31 10:23:52,838 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x95
2025-07-31 10:23:52,838 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x95, 值=021D
2025-07-31 10:23:52,838 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x95 = 0x021D
2025-07-31 10:23:52,838 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x98
2025-07-31 10:23:52,838 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x98, 值=FFB5
2025-07-31 10:23:52,838 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x98 = 0xFFB5
2025-07-31 10:23:57,842 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x93
2025-07-31 10:23:57,843 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x93, 值=0077
2025-07-31 10:23:57,843 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x93 = 0x0077
2025-07-31 10:23:57,843 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x94
2025-07-31 10:23:57,843 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x94, 值=000D
2025-07-31 10:23:57,843 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x94 = 0x000D
2025-07-31 10:23:57,843 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x95
2025-07-31 10:23:57,843 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x95, 值=0221
2025-07-31 10:23:57,843 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x95 = 0x0221
2025-07-31 10:23:57,844 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x98
2025-07-31 10:23:57,844 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x98, 值=FFB1
2025-07-31 10:23:57,844 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x98 = 0xFFB1
2025-07-31 10:24:02,833 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x93
2025-07-31 10:24:02,834 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x93, 值=007B
2025-07-31 10:24:02,834 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x93 = 0x007B
2025-07-31 10:24:02,834 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x94
2025-07-31 10:24:02,834 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x94, 值=000E
2025-07-31 10:24:02,835 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x94 = 0x000E
2025-07-31 10:24:02,835 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x95
2025-07-31 10:24:02,835 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x95, 值=0221
2025-07-31 10:24:02,835 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x95 = 0x0221
2025-07-31 10:24:02,835 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x98
2025-07-31 10:24:02,835 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x98, 值=FFB2
2025-07-31 10:24:02,836 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x98 = 0xFFB2
2025-07-31 10:24:07,836 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x93
2025-07-31 10:24:07,836 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x93, 值=0078
2025-07-31 10:24:07,837 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x93 = 0x0078
2025-07-31 10:24:07,837 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x94
2025-07-31 10:24:07,837 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x94, 值=0013
2025-07-31 10:24:07,837 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x94 = 0x0013
2025-07-31 10:24:07,837 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x95
2025-07-31 10:24:07,837 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x95, 值=0221
2025-07-31 10:24:07,837 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x95 = 0x0221
2025-07-31 10:24:07,838 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x98
2025-07-31 10:24:07,838 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x98, 值=FFB6
2025-07-31 10:24:07,838 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x98 = 0xFFB6
2025-07-31 10:24:12,909 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x93
2025-07-31 10:24:12,909 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x93, 值=007B
2025-07-31 10:24:12,909 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x93 = 0x007B
2025-07-31 10:24:12,909 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x94
2025-07-31 10:24:12,909 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x94, 值=0016
2025-07-31 10:24:12,909 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x94 = 0x0016
2025-07-31 10:24:12,909 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x95
2025-07-31 10:24:12,909 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x95, 值=0223
2025-07-31 10:24:12,909 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x95 = 0x0223
2025-07-31 10:24:12,910 - RegisterOperationService - [RegisterOperationService.py:122] - DEBUG - 状态监控: 静默读取寄存器 0x98
2025-07-31 10:24:12,910 - RegisterManager - [RegisterManager.py:78] - DEBUG - RegisterManager更新寄存器: 0x98, 值=FFB5
2025-07-31 10:24:12,910 - RegisterOperationService - [RegisterOperationService.py:164] - DEBUG - 状态监控: 静默更新寄存器数据 0x98 = 0xFFB5
2025-07-31 10:24:12,910 - RegisterUpdateBus - [RegisterUpdateBus.py:720] - DEBUG - RegisterUpdateBus: 已缓存LineEdit值: ClkinControl.lineEditClkin0 = '122.88'
2025-07-31 10:24:12,910 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1460] - DEBUG - 已缓存LineEdit值: lineEditClkin0 = '122.88'
2025-07-31 10:24:12,910 - RegisterUpdateBus - [RegisterUpdateBus.py:720] - DEBUG - RegisterUpdateBus: 已缓存LineEdit值: ClkinControl.lineEditClkin1 = '122.88'
2025-07-31 10:24:12,910 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1460] - DEBUG - 已缓存LineEdit值: lineEditClkin1 = '122.88'
2025-07-31 10:24:12,910 - RegisterUpdateBus - [RegisterUpdateBus.py:720] - DEBUG - RegisterUpdateBus: 已缓存LineEdit值: ClkinControl.lineEditClkin2Oscout = '150.0'
2025-07-31 10:24:12,910 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1460] - DEBUG - 已缓存LineEdit值: lineEditClkin2Oscout = '150.0'
2025-07-31 10:24:12,910 - RegisterUpdateBus - [RegisterUpdateBus.py:720] - DEBUG - RegisterUpdateBus: 已缓存LineEdit值: ClkinControl.lineEditClkinSelOut = 'CLKin1'
2025-07-31 10:24:12,910 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1460] - DEBUG - 已缓存LineEdit值: lineEditClkinSelOut = 'CLKin1'
2025-07-31 10:24:12,910 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1482] - DEBUG - 已缓存 4 个LineEdit控件的值
2025-07-31 10:24:12,910 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1492] - INFO - 时钟输入窗口关闭，已缓存所有LineEdit控件的值
2025-07-31 10:24:12,911 - RegisterUpdateBus - [RegisterUpdateBus.py:720] - DEBUG - RegisterUpdateBus: 已缓存LineEdit值: ClkinControl.lineEditClkin0 = '122.88'
2025-07-31 10:24:12,911 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1460] - DEBUG - 已缓存LineEdit值: lineEditClkin0 = '122.88'
2025-07-31 10:24:12,911 - RegisterUpdateBus - [RegisterUpdateBus.py:720] - DEBUG - RegisterUpdateBus: 已缓存LineEdit值: ClkinControl.lineEditClkin1 = '122.88'
2025-07-31 10:24:12,911 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1460] - DEBUG - 已缓存LineEdit值: lineEditClkin1 = '122.88'
2025-07-31 10:24:12,911 - RegisterUpdateBus - [RegisterUpdateBus.py:720] - DEBUG - RegisterUpdateBus: 已缓存LineEdit值: ClkinControl.lineEditClkin2Oscout = '150.0'
2025-07-31 10:24:12,911 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1460] - DEBUG - 已缓存LineEdit值: lineEditClkin2Oscout = '150.0'
2025-07-31 10:24:12,911 - RegisterUpdateBus - [RegisterUpdateBus.py:720] - DEBUG - RegisterUpdateBus: 已缓存LineEdit值: ClkinControl.lineEditClkinSelOut = 'CLKin1'
2025-07-31 10:24:12,911 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1460] - DEBUG - 已缓存LineEdit值: lineEditClkinSelOut = 'CLKin1'
2025-07-31 10:24:12,911 - ModernClkinControlHandler - [ModernClkinControlHandler.py:1482] - DEBUG - 已缓存 4 个LineEdit控件的值
2025-07-31 10:24:12,911 - ModernBaseHandler - [ModernBaseHandler.py:1281] - DEBUG - ModernClkinControlHandler 窗口关闭时已缓存LineEdit值
2025-07-31 10:24:12,911 - clkin_control_plugin - [clkin_control_plugin.py:155] - INFO - ✅ 已清理主窗口的clkin_control_window属性
2025-07-31 10:24:12,911 - clkin_control_plugin - [clkin_control_plugin.py:168] - INFO - '时钟输入控制' 工具窗口已关闭
2025-07-31 10:24:12,911 - PluginWindowService - [PluginWindowService.py:152] - INFO - 处理插件窗口关闭: 时钟输入控制, from_tab_close=False
2025-07-31 10:24:12,911 - PluginWindowService - [PluginWindowService.py:195] - DEBUG - 无法获取插件管理器，跳过通知插件: 时钟输入控制
2025-07-31 10:24:12,912 - PluginWindowService - [PluginWindowService.py:161] - DEBUG - 已移除插件窗口引用: 时钟输入控制
2025-07-31 10:24:12,912 - MenuClickFixer - [MenuClickFixer.py:93] - DEBUG - 动作切换: 时钟输入(&I), checked=False
2025-07-31 10:24:12,912 - PluginDockService - [PluginDockService.py:318] - DEBUG - 未找到插件 时钟输入控制 对应的标签页
2025-07-31 10:24:12,912 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-31 10:24:12,912 - PluginDockService - [PluginDockService.py:789] - DEBUG - 已停止拖拽监控: 时钟输入控制
2025-07-31 10:24:12,912 - PluginIntegrationService - [PluginIntegrationService.py:160] - INFO - 插件窗口关闭处理完成: 时钟输入控制, from_tab_close=False
2025-07-31 10:24:12,912 - PluginWindowService - [PluginWindowService.py:166] - INFO - 插件窗口关闭处理完成: 时钟输入控制
2025-07-31 10:24:12,912 - ModernBaseHandler - [ModernBaseHandler.py:1287] - INFO - ModernClkinControlHandler 窗口关闭，已发送关闭信号
2025-07-31 10:24:12,924 - RegisterMainWindow - [RegisterMainWindow.py:393] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-31 10:24:12,925 - RegisterMainWindow - [RegisterMainWindow.py:393] - DEBUG - 使用增强Windows API强制激活主窗口
2025-07-31 10:24:12,931 - RegisterMainWindow - [RegisterMainWindow.py:576] - DEBUG - 未找到打开的工具窗口
2025-07-31 10:24:12,931 - RegisterMainWindow - [RegisterMainWindow.py:412] - DEBUG - 主窗口强制置顶完成
2025-07-31 10:24:12,931 - RegisterMainWindow - [RegisterMainWindow.py:317] - DEBUG - 主窗口已通过焦点置顶
2025-07-31 10:24:12,938 - RegisterMainWindow - [RegisterMainWindow.py:576] - DEBUG - 未找到打开的工具窗口
2025-07-31 10:24:12,938 - RegisterMainWindow - [RegisterMainWindow.py:412] - DEBUG - 主窗口强制置顶完成
2025-07-31 10:24:12,938 - RegisterMainWindow - [RegisterMainWindow.py:321] - DEBUG - 主窗口已通过激活事件置顶
2025-07-31 10:24:14,019 - ApplicationLifecycleManager - [ApplicationLifecycleManager.py:41] - INFO - 应用程序正在关闭...
2025-07-31 10:24:14,022 - BatchOperationManager - [BatchOperationManager.py:498] - DEBUG - 断开读取信号时出现异常（可能未连接）: 'method' object is not connected
2025-07-31 10:24:14,022 - BatchOperationManager - [BatchOperationManager.py:510] - DEBUG - 断开写入信号时出现异常（可能未连接）: 'method' object is not connected
2025-07-31 10:24:14,022 - BatchOperationManager - [BatchOperationManager.py:548] - INFO - 已强制取消所有批量操作
2025-07-31 10:24:14,023 - ApplicationLifecycleManager - [ApplicationLifecycleManager.py:69] - INFO - 已强制取消所有批量操作并清理资源
2025-07-31 10:24:14,023 - PluginWindowService - [PluginWindowService.py:513] - INFO - 所有插件窗口已关闭
2025-07-31 10:24:14,023 - WindowManagementService - [WindowManagementService.py:200] - INFO - 已通过插件服务关闭所有插件窗口
2025-07-31 10:24:14,023 - WindowManagementService - [WindowManagementService.py:250] - INFO - 已清空所有标签页并隐藏标签页容器
2025-07-31 10:24:14,023 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-31 10:24:14,023 - WindowManagementService - [WindowManagementService.py:227] - INFO - 所有管理的窗口已关闭
2025-07-31 10:24:14,023 - CursorUtils - [CursorUtils.py:30] - DEBUG - 已强制恢复光标状态
2025-07-31 10:24:14,023 - UserConfigurationService - [UserConfigurationService.py:72] - DEBUG - 收集到 125 个寄存器值
2025-07-31 10:24:14,023 - PluginManager - [PluginManager.py:349] - DEBUG - 添加工具窗口插件: 时钟输入控制
2025-07-31 10:24:14,023 - PluginManager - [PluginManager.py:349] - DEBUG - 添加工具窗口插件: 时钟输出
2025-07-31 10:24:14,024 - PluginManager - [PluginManager.py:349] - DEBUG - 添加工具窗口插件: 数据分析器
2025-07-31 10:24:14,024 - PluginManager - [PluginManager.py:349] - DEBUG - 添加工具窗口插件: 示例工具
2025-07-31 10:24:14,024 - PluginManager - [PluginManager.py:349] - DEBUG - 添加工具窗口插件: 性能监控器
2025-07-31 10:24:14,024 - PluginManager - [PluginManager.py:349] - DEBUG - 添加工具窗口插件: PLL控制
2025-07-31 10:24:14,024 - PluginManager - [PluginManager.py:349] - DEBUG - 添加工具窗口插件: 选择性寄存器操作
2025-07-31 10:24:14,024 - PluginManager - [PluginManager.py:349] - DEBUG - 添加工具窗口插件: 模式设置
2025-07-31 10:24:14,024 - PluginManager - [PluginManager.py:349] - DEBUG - 添加工具窗口插件: 同步系统参考
2025-07-31 10:24:14,024 - PluginManager - [PluginManager.py:353] - INFO - 找到 9 个工具窗口插件
2025-07-31 10:24:14,024 - UserConfigurationService - [UserConfigurationService.py:118] - INFO - 成功收集 0 个工具窗口的配置
2025-07-31 10:24:14,024 - UserConfigurationService - [UserConfigurationService.py:81] - INFO - 成功收集当前配置信息
2025-07-31 10:24:14,026 - UserConfigurationService - [UserConfigurationService.py:571] - INFO - 最近配置已保存到: C:\Users\<USER>\.fsj_register_tool\last_config.json
2025-07-31 10:24:14,026 - RegisterMainWindow - [RegisterMainWindow.py:1037] - INFO - 已保存最近配置
2025-07-31 10:24:14,026 - ConfigurationService - [ConfigurationService.py:283] - DEBUG - 设置已更新: simulation_mode = False
2025-07-31 10:24:14,026 - ConfigurationService - [ConfigurationService.py:333] - INFO - 模拟模式设置已保存: False
2025-07-31 10:24:14,026 - spi_service - [spi_service.py:156] - INFO - SPIService cleaning up...
2025-07-31 10:24:14,026 - spi_service - [spi_service.py:421] - DEBUG - Cleaning up SPI thread...
2025-07-31 10:24:14,027 - port_manager - [port_manager.py:150] - INFO - 已关闭端口: COM4
2025-07-31 10:24:14,027 - spiPrivacy - [spiPrivacy.py:466] - INFO - 通过端口管理器关闭串口: COM4
2025-07-31 10:24:14,027 - spi_service - [spi_service.py:428] - DEBUG - Worker stop requested
2025-07-31 10:24:14,027 - spi_service - [spi_service.py:437] - INFO - Requesting SPI thread termination...
2025-07-31 10:24:14,031 - spi_service - [spi_service.py:443] - INFO - SPI thread stopped.
2025-07-31 10:24:14,031 - spi_service - [spi_service.py:455] - DEBUG - SPI thread resources cleaned.
2025-07-31 10:24:14,031 - spi_service - [spi_service.py:166] - INFO - SPIService cleanup finished.
2025-07-31 10:24:14,031 - ApplicationLifecycleManager - [ApplicationLifecycleManager.py:106] - INFO - 应用程序关闭完成
2025-07-31 10:24:14,091 - PluginWindowService - [PluginWindowService.py:174] - INFO - 插件窗口对象被销毁: 时钟输入控制
2025-07-31 10:24:14,110 - port_manager - [port_manager.py:212] - INFO - 已清理所有COM端口连接
