#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
窗口功能测试
测试各个工具窗口的基本功能是否正常
"""

import sys
import os
import unittest
from PyQt5.QtWidgets import QApplication
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class TestWindowFunctionality(unittest.TestCase):
    """测试窗口功能"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        if not QApplication.instance():
            cls.app = QApplication(sys.argv)
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """每个测试前的设置"""
        logger.info(f"开始测试: {self._testMethodName}")
        
        # 创建测试用的RegisterManager
        try:
            from core.services.register.RegisterManager import RegisterManager
            import json
            
            config_path = os.path.join(project_root, 'lib', 'register.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                registers_config = json.load(f)
            
            self.register_manager = RegisterManager(registers_config)
            
        except Exception as e:
            self.skipTest(f"无法创建RegisterManager: {str(e)}")
    
    def test_01_modern_set_modes_handler(self):
        """测试现代化模式设置处理器"""
        print("\n🔍 测试1: 现代化模式设置处理器")
        
        try:
            from ui.handlers.ModernSetModesHandler import ModernSetModesHandler
            
            # 创建实例
            handler = ModernSetModesHandler(parent=None, register_manager=self.register_manager)
            self.assertIsNotNone(handler)
            
            # 检查基本属性
            self.assertTrue(hasattr(handler, 'register_manager'))
            self.assertEqual(handler.register_manager, self.register_manager)
            
            # 检查窗口标题
            self.assertIn("模式设置", handler.windowTitle())
            self.assertIn("现代化", handler.windowTitle())
            
            print("✅ ModernSetModesHandler 功能正常")
            
            # 清理
            handler.close()
            
        except Exception as e:
            self.fail(f"ModernSetModesHandler 测试失败: {str(e)}")
    
    def test_02_modern_clkin_control_handler(self):
        """测试现代化时钟输入控制处理器"""
        print("\n🔍 测试2: 现代化时钟输入控制处理器")
        
        try:
            from ui.handlers.ModernClkinControlHandler import ModernClkinControlHandler
            
            # 创建实例
            handler = ModernClkinControlHandler(parent=None, register_manager=self.register_manager)
            self.assertIsNotNone(handler)
            
            # 检查基本属性
            self.assertTrue(hasattr(handler, 'register_manager'))
            self.assertEqual(handler.register_manager, self.register_manager)
            
            # 检查窗口标题
            self.assertIn("时钟输入控制", handler.windowTitle())
            self.assertIn("现代化", handler.windowTitle())
            
            print("✅ ModernClkinControlHandler 功能正常")
            
            # 清理
            handler.close()
            
        except Exception as e:
            self.fail(f"ModernClkinControlHandler 测试失败: {str(e)}")
    
    def test_03_modern_pll_handler(self):
        """测试现代化PLL处理器"""
        print("\n🔍 测试3: 现代化PLL处理器")
        
        try:
            from ui.handlers.ModernPLLHandler import ModernPLLHandler
            
            # 创建实例
            handler = ModernPLLHandler(parent=None, register_manager=self.register_manager)
            self.assertIsNotNone(handler)
            
            # 检查基本属性
            self.assertTrue(hasattr(handler, 'register_manager'))
            self.assertEqual(handler.register_manager, self.register_manager)
            
            # 检查窗口标题
            self.assertIn("PLL", handler.windowTitle())
            self.assertIn("现代化", handler.windowTitle())
            
            print("✅ ModernPLLHandler 功能正常")
            
            # 清理
            handler.close()
            
        except Exception as e:
            self.fail(f"ModernPLLHandler 测试失败: {str(e)}")
    
    def test_04_modern_sync_sysref_handler(self):
        """测试现代化同步系统参考处理器"""
        print("\n🔍 测试4: 现代化同步系统参考处理器")
        
        try:
            from ui.handlers.ModernSyncSysRefHandler import ModernSyncSysRefHandler
            
            # 创建实例
            handler = ModernSyncSysRefHandler(parent=None, register_manager=self.register_manager)
            self.assertIsNotNone(handler)
            
            # 检查基本属性
            self.assertTrue(hasattr(handler, 'register_manager'))
            self.assertEqual(handler.register_manager, self.register_manager)
            
            # 检查窗口标题
            self.assertIn("同步系统参考", handler.windowTitle())
            self.assertIn("现代化", handler.windowTitle())
            
            print("✅ ModernSyncSysRefHandler 功能正常")
            
            # 清理
            handler.close()
            
        except Exception as e:
            self.fail(f"ModernSyncSysRefHandler 测试失败: {str(e)}")
    
    def test_05_modern_clk_outputs_handler(self):
        """测试现代化时钟输出处理器"""
        print("\n🔍 测试5: 现代化时钟输出处理器")
        
        try:
            from ui.handlers.ModernClkOutputsHandler import ModernClkOutputsHandler
            
            # 创建实例
            handler = ModernClkOutputsHandler(parent=None, register_manager=self.register_manager)
            self.assertIsNotNone(handler)
            
            # 检查基本属性
            self.assertTrue(hasattr(handler, 'register_manager'))
            self.assertEqual(handler.register_manager, self.register_manager)
            
            # 检查窗口标题
            self.assertIn("时钟输出", handler.windowTitle())
            self.assertIn("现代化", handler.windowTitle())
            
            print("✅ ModernClkOutputsHandler 功能正常")
            
            # 清理
            handler.close()
            
        except Exception as e:
            self.fail(f"ModernClkOutputsHandler 测试失败: {str(e)}")
    
    def test_06_modern_register_table_handler(self):
        """测试现代化寄存器表格处理器"""
        print("\n🔍 测试6: 现代化寄存器表格处理器")
        
        try:
            from ui.handlers.ModernRegisterTableHandler import ModernRegisterTableHandler
            
            # 创建实例
            handler = ModernRegisterTableHandler(parent=None, register_manager=self.register_manager)
            self.assertIsNotNone(handler)
            
            # 检查基本属性
            self.assertTrue(hasattr(handler, 'register_manager'))
            self.assertEqual(handler.register_manager, self.register_manager)
            
            # 检查窗口标题
            self.assertIn("寄存器位域表格", handler.windowTitle())
            self.assertIn("现代化", handler.windowTitle())
            
            print("✅ ModernRegisterTableHandler 功能正常")
            
            # 清理
            handler.close()
            
        except Exception as e:
            self.fail(f"ModernRegisterTableHandler 测试失败: {str(e)}")
    
    def test_07_modern_ui_event_handler(self):
        """测试现代化UI事件处理器"""
        print("\n🔍 测试7: 现代化UI事件处理器")
        
        try:
            from ui.handlers.ModernUIEventHandler import ModernUIEventHandler
            
            # 创建实例
            handler = ModernUIEventHandler(parent=None, register_manager=self.register_manager)
            self.assertIsNotNone(handler)
            
            # 检查基本属性
            self.assertTrue(hasattr(handler, 'register_manager'))
            self.assertEqual(handler.register_manager, self.register_manager)
            
            # 检查窗口标题
            self.assertIn("UI事件处理器", handler.windowTitle())
            self.assertIn("现代化", handler.windowTitle())
            
            print("✅ ModernUIEventHandler 功能正常")
            
            # 清理
            handler.close()
            
        except Exception as e:
            self.fail(f"ModernUIEventHandler 测试失败: {str(e)}")
    
    def test_08_modern_register_io_handler(self):
        """测试现代化寄存器IO处理器"""
        print("\n🔍 测试8: 现代化寄存器IO处理器")
        
        try:
            from ui.handlers.ModernRegisterIOHandler import ModernRegisterIOHandler
            
            # 创建实例
            handler = ModernRegisterIOHandler(parent=None, register_manager=self.register_manager)
            self.assertIsNotNone(handler)
            
            # 检查基本属性
            self.assertTrue(hasattr(handler, 'register_manager'))
            self.assertEqual(handler.register_manager, self.register_manager)
            
            # 检查窗口标题
            self.assertIn("寄存器IO控制", handler.windowTitle())
            self.assertIn("现代化", handler.windowTitle())
            
            print("✅ ModernRegisterIOHandler 功能正常")
            
            # 清理
            handler.close()
            
        except Exception as e:
            self.fail(f"ModernRegisterIOHandler 测试失败: {str(e)}")
    
    def test_09_modern_register_tree_handler(self):
        """测试现代化寄存器树处理器"""
        print("\n🔍 测试9: 现代化寄存器树处理器")
        
        try:
            from ui.handlers.ModernRegisterTreeHandler import ModernRegisterTreeHandler
            
            # 创建实例
            handler = ModernRegisterTreeHandler(parent=None, register_manager=self.register_manager)
            self.assertIsNotNone(handler)
            
            # 检查基本属性
            self.assertTrue(hasattr(handler, 'register_manager'))
            self.assertEqual(handler.register_manager, self.register_manager)
            
            # 检查窗口标题
            self.assertIn("寄存器树", handler.windowTitle())
            self.assertIn("现代化", handler.windowTitle())
            
            print("✅ ModernRegisterTreeHandler 功能正常")
            
            # 清理
            handler.close()
            
        except Exception as e:
            self.fail(f"ModernRegisterTreeHandler 测试失败: {str(e)}")


if __name__ == '__main__':
    print("🚀 开始窗口功能测试")
    print("=" * 60)
    
    # 运行测试
    unittest.main(verbosity=2, exit=False)
    
    print("\n" + "=" * 60)
    print("🎯 窗口功能测试完成")
