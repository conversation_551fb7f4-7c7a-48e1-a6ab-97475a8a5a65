# -*- coding: utf-8 -*-
"""
UI处理器模块初始化文件
确保所有处理器都能被正确导入，特别是在PyInstaller打包环境中
"""

# 导入所有现代化处理器
from .ModernBaseHandler import ModernBaseHandler
from .ModernSetModesHandler import ModernSetModesHandler
from .ModernClkinControlHandler import ModernClkinControlHandler
from .ModernPLLHandler import ModernPLLHandler
from .ModernSyncSysRefHandler import ModernSyncSysRefHandler
from .ModernClkOutputsHandler import ModernClkOutputsHandler
from .ModernRegisterTableHandler import ModernRegisterTableHandler
from .ModernUIEventHandler import ModernUIEventHandler
from .ModernRegisterIOHandler import ModernRegisterIOHandler
from .ModernRegisterTreeHandler import ModernRegisterTreeHandler

# 导入传统处理器（保持兼容性）
from .BaseHandler import BaseClockHandler
from .SetModesHandler import SetModesHandler
from .ClkinControlHandler import ClkinControlHandler
from .RegisterTableHandler import Register<PERSON>ableHandler
from .UIEventHandler import UIEventHandler
from .RegisterIOHandler import <PERSON><PERSON><PERSON>andler
from .RegisterTreeHandler import RegisterTreeHandler

# 导出所有处理器类
__all__ = [
    # 现代化处理器
    'ModernBaseHandler',
    'ModernSetModesHandler',
    'ModernClkinControlHandler',
    'ModernPLLHandler',
    'ModernSyncSysRefHandler',
    'ModernClkOutputsHandler',
    'ModernRegisterTableHandler',
    'ModernUIEventHandler',
    'ModernRegisterIOHandler',
    'ModernRegisterTreeHandler',
    
    # 传统处理器
    'BaseClockHandler',
    'SetModesHandler',
    'ClkinControlHandler',
    'RegisterTableHandler',
    'UIEventHandler',
    'RegisterIOHandler',
    'RegisterTreeHandler',
]
