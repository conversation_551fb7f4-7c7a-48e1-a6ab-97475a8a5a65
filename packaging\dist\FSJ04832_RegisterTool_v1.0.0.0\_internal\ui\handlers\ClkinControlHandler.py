# -*- coding: utf-8 -*-

from PyQt5 import QtCore, QtWidgets
from ..forms.Ui_clkinControl import Ui_ClkinControl
from utils.Log import get_module_logger

logger = get_module_logger(__name__)
from ui.handlers.BaseHandler import BaseClockHandler
import traceback

# 常量定义
CLOCK_SOURCES = ["CLKin0", "CLKin1", "CLKin2", "Holdover"]
MAX_DIVIDER_VALUE = 16383
DEFAULT_HOLDOVER_DIVIDER = 1

class ClkinControlHandler(BaseClockHandler):
    """时钟输入控制配置界面处理类"""

    # 添加一个关闭信号
    window_closed = QtCore.pyqtSignal()
    
    def __init__(self, parent=None, registers=None):
        # 解决registers处理问题
        reg_objects = registers if registers is not None and hasattr(registers, 'keys') else {}
            
        # 调用父类构造器
        super().__init__(parent, reg_objects)

        # 设置窗口标题
        self.setWindowTitle("时钟输入控制配置")
        
        # 定义格式名称映射字典
        self._init_combobox_options()
        
        # 分频比默认值（与寄存器配置文件中的默认值一致）
        self.clkin0_divider = 120  # 对应寄存器0x63的默认值
        self.clkin1_divider = 120  # 对应寄存器0x65的默认值
        self.clkin2_divider = 150  # 对应寄存器0x67的默认值
        
        # 频率默认值
        self.clkin0 = 122.88
        self.clkin1 = 122.88
        self.clkin2 = 153.6
        
        # 设置UI到内容widget
        self.ui = Ui_ClkinControl()
        self.ui.setupUi(self.content_widget)

        # 添加一个实例变量来存储上一次的值
        self.previous_clkin_value = ""

        # 初始化特定配置
        self.init_clkin_config()
    
    @classmethod
    def create_for_testing(cls, parent=None):
        """创建一个用于测试的实例，使用模拟的寄存器数据"""
        # 创建模拟的寄存器对象
        mock_registers = cls._create_mock_registers()
        
        # 创建实例
        instance = cls(parent, mock_registers)
        
        # 禁用一些可能导致错误的功能
        instance._testing_mode = True
        
        return instance
    
    @staticmethod
    def _create_mock_registers():
        """创建模拟的寄存器数据用于测试"""
        class MockRegister:
            def __init__(self, addr, bits=None):
                self.addr = addr
                self.bits = bits or []
                self.value = 0
            
            def get_bit_field_value(self, field_name):
                """模拟获取位域值的方法"""
                return 0
        
        # 创建模拟的寄存器映射
        mock_registers = {}
        
        # 为常用的控件创建位定义
        common_bits = [
            {"widget_name": "CLKin0Demux", "widget_type": "combobox", "default": "00", "options": "0:3"},
            {"widget_name": "CLKin1Demux", "widget_type": "combobox", "default": "00", "options": "0:3"},
            {"widget_name": "CLKin1Type", "widget_type": "combobox", "default": "0", "options": "0:1"},
            {"widget_name": "OSCoutMux", "widget_type": "combobox", "default": "0", "options": "0:1"},
            {"widget_name": "CLKinSelManual", "widget_type": "combobox", "default": "00", "options": "0:3"},
            {"widget_name": "CLKinSel0Type", "widget_type": "combobox", "default": "000", "options": "0:6"},
            {"widget_name": "CLKinSel1Type", "widget_type": "combobox", "default": "000", "options": "0:6"},
            {"widget_name": "CLKinSel0Mux", "widget_type": "combobox", "default": "000", "options": "0:7"},
            {"widget_name": "CLKinSel1Mux", "widget_type": "combobox", "default": "000", "options": "0:7"},
            {"widget_name": "OSCoutClockFormat", "widget_type": "combobox", "default": "0000", "options": "0:14"},
            {"widget_name": "syncSource", "widget_type": "combobox", "default": "00", "options": "0:3"},
            {"widget_name": "PLL1R0Div", "widget_type": "spinbox", "default": "0000000001111000", "options": "1:16383"},
            {"widget_name": "PLL1R1Div", "widget_type": "spinbox", "default": "0000000001111000", "options": "1:16383"},
            {"widget_name": "PLL1R2Div", "widget_type": "spinbox", "default": "0000000010010110", "options": "1:16383"},
        ]
        
        # 创建几个模拟的寄存器
        for i in range(0x70, 0x80):
            addr = f"0x{i:02x}"
            mock_registers[addr] = MockRegister(addr, common_bits[:])
        
        return mock_registers
        
    def _init_combobox_options(self):
        """初始化ComboBox选项映射"""
        self.custom_combobox_options = {
            "CLKin0Demux": {
                0: "Fin",
                1: "Feedback Mux",
                2: "PLL1",
                3: "Off"
            },
            "CLKin1Demux": {
                0: "Fin",
                1: "Feedback Mux",
                2: "PLL1",
                3: "Off"
            },
            "CLKin1Type": {
                0: "Bipolar",
                1: "MOS",
            },
            "OSCoutMux": {
                0: "Buffered OSCin",
                1: "Feedback Mux",
            },
            "CLKinSelManual": {
                0: "CLKin0",
                1: "CLKin1",
                2: "CLKin2",
                3: "Holdover"
            },
            "CLKinSel0Type": {
                0: "Input",
                1: "Input w/ Pull Up",
                2: "Input w/ Pull Down",
                3: "Output(push-up)",
                4: "forbidden",
                5: "forbidden",
                6: "forbidden",
            },
            "CLKinSel1Type": {
                0: "Input",
                1: "Input w/ Pull Up",
                2: "Input w/ Pull Down",
                3: "Output(push-up)",
                4: "forbidden",
                5: "forbidden",
                6: "forbidden",
            },
            "CLKinSel0Mux": {
                0: "Logic Low",
                1: "CLKin0 LOS",
                2: "CLKin0 Selected",
                3: "DAC Locked",
                4: "DAC Low",
                5: "DAC High",
                6: "SPI Readback",
                7: "Reserved",
            },
            "CLKinSel1Mux": {
                0: "Logic Low",
                1: "CLKin0 LOS",
                2: "CLKin0 Selected",
                3: "DAC Locked",
                4: "DAC Low",
                5: "DAC High",
                6: "SPI Readback",
                7: "Reserved",
            },
            "OSCoutClockFormat": {
                0: "Power down (CLKin2)",
                1: "LVDS",
                2: "Reserved",
                3: "Reserved",
                4: "LVPECL 1600 mV",
                5: "LVPECL 2000 mV",
                6: "LVCMOS (Norm / Inv)",
                7: "LVCMOS (Inv / Norm)",
                8: "LVCMOS (Norm / Norm)",
                9: "LVCMOS (Inv / Inv)",
                10: "LVCMOS (Off / Norm)",
                11: "LVCMOS (Off / Inv)",
                12: "LVCMOS (Norm / Off)",
                13: "LVCMOS (Inv / Off)",
                14: "LVCMOS (Off / Off)",
            },
            "syncSource": {
                0: "Reserved",
                1: "SYNC Pin",
                2: "CLKin0",
                3: "Reserved",
            }
        }
        
        # 将选项映射添加到基类的combobox_options_map中
        for option_name, options in self.custom_combobox_options.items():
            # 根据控件名称获取映射类型
            map_type = self._get_combobox_type(option_name)
            # 更新基类映射
            self.combobox_options_map[map_type] = options

    def init_clkin_config(self):
        """初始化时钟输入特定配置"""
        try:
            # 创建控件与寄存器的映射表
            self.create_widget_register_map()

            # 注册手动初始化的文本控件，防止未初始化控件检查报错
            # 必须在initialize_widgets之前调用
            self._register_manually_initialized_controls()

            # 初始化界面控件
            self.initialize_widgets()

            # 初始化分频比控件
            self.init_divider_values()

            # 初始化时钟频率
            self.init_clkin_frequency()

            # 初始化时也调用一次 update_clkin_sel_out 方法
            self.update_clkin_sel_out()

            # 连接自定义信号
            self.connect_signals()

            # 连接控件信号
            self.connect_widget_signals()

            # 发送初始时钟源信息
            self._send_initial_clock_source_info()
        except Exception as e:
            logger.error(f"初始化时钟输入配置时发生错误: {str(e)}")
            traceback.print_exc()
            
    def _register_manually_initialized_controls(self):
        """注册手动初始化的控件，避免未初始化控件检查警告"""
        # 定义手动初始化的文本控件，避免未初始化控件检查警告
        manual_controls = [
            "lineEditClkin0", 
            "lineEditClkin1", 
            "lineEditClkin2Oscout",
            "lineEditClkinSelOut"
        ]
        
        # 为每个手动初始化的控件创建一个虚拟映射
        for control_name in manual_controls:
            if hasattr(self.ui, control_name) and control_name not in self.widget_register_map:
                # 创建一个虚拟的bit_def，标记为手动初始化
                bit_def = {
                    "name": f"{control_name}_manual", 
                    "default": "", 
                    "options": "",
                    "manually_initialized": True
                }
                
                # 获取当前文本值作为默认值
                control = getattr(self.ui, control_name)
                default_value = control.text() if hasattr(control, "text") else ""
                
                # 将控件添加到映射表中
                self.widget_register_map[control_name] = {
                    "register_addr": "manual",  # 使用特殊标记表示手动初始化
                    "widget_type": "lineedit", 
                    "default_value": default_value,
                    "bit_def": bit_def
                }
                
                logger.info(f"已将手动初始化的控件 {control_name} 添加到映射表")
        
        logger.info(f"已注册 {len(manual_controls)} 个手动初始化的控件")

    def _send_initial_clock_source_info(self):
        """发送初始时钟源信息"""
        try:
            # 获取当前选择的时钟源
            index = self.ui.CLKinSelManual.currentIndex()
            
            # 获取时钟源、频率和分频比
            selected_source, freq_value, divider_value = self._get_clock_info_by_index(index)
            
            # 设置当前时钟源并发送信息
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            RegisterUpdateBus.instance()._current_clock_source = selected_source
            RegisterUpdateBus.instance().emit_clock_source_selected(selected_source, freq_value, divider_value)
            logger.info(f"初始化时发送时钟源信息: 源={selected_source}, 频率={freq_value}MHz, 分频比={divider_value}")
        except Exception as e:
            logger.error(f"初始化时发送时钟源信息失败: {str(e)}")
            traceback.print_exc()

    def _get_clock_info_by_index(self, index):
        """根据索引获取时钟信息"""
        selected_source = CLOCK_SOURCES[index] if index < len(CLOCK_SOURCES) else "Holdover"
        
        if index == 0:  # CLKin0
            freq_value = float(self.ui.lineEditClkin0.text() or "0")
            divider_value = self.ui.PLL1R0Div.value()
        elif index == 1:  # CLKin1
            freq_value = float(self.ui.lineEditClkin1.text() or "0")
            divider_value = self.ui.PLL1R1Div.value()
        elif index == 2:  # CLKin2
            freq_value = float(self.ui.lineEditClkin2Oscout.text() or "0")
            divider_value = self.ui.PLL1R2Div.value()
        else:  # Holdover
            freq_value = 0.0
            divider_value = DEFAULT_HOLDOVER_DIVIDER
            
        return selected_source, freq_value, divider_value

    def _get_clock_info_by_name(self, source_name):
        """根据名称获取时钟信息"""
        if source_name == "CLKin0":
            freq_value = float(self.ui.lineEditClkin0.text() or "0")
            divider_value = self.ui.PLL1R0Div.value()
        elif source_name == "CLKin1":
            freq_value = float(self.ui.lineEditClkin1.text() or "0")
            divider_value = self.ui.PLL1R1Div.value()
        elif source_name == "CLKin2":
            freq_value = float(self.ui.lineEditClkin2Oscout.text() or "0")
            divider_value = self.ui.PLL1R2Div.value()
        else:  # Holdover or unknown
            freq_value = 0.0
            divider_value = DEFAULT_HOLDOVER_DIVIDER
            
        return freq_value, divider_value

    def init_divider_values(self):
        """初始化分频比控件的默认值"""
        try:
            # 分频控件与实例变量的映射
            divider_controls = {
                "PLL1R0Div": {"source": "CLKin0", "var": "clkin0_divider"},
                "PLL1R1Div": {"source": "CLKin1", "var": "clkin1_divider"},
                "PLL1R2Div": {"source": "CLKin2", "var": "clkin2_divider"}
            }
            
            # 设置所有分频比控件的最大值
            for control_name in divider_controls:
                getattr(self.ui, control_name).setMaximum(MAX_DIVIDER_VALUE)
            
            # 初始化分频比控件的值
            for control_name, info in divider_controls.items():
                self._init_divider_control(control_name, info)
            
            logger.info(f"初始化分频比控件完成: CLKin0={self.clkin0_divider}, CLKin1={self.clkin1_divider}, CLKin2={self.clkin2_divider}")
        except Exception as e:
            logger.error(f"初始化分频比控件时发生错误: {str(e)}")
            traceback.print_exc()

    def _init_divider_control(self, control_name, info):
        """初始化单个分频比控件"""
        # 首先检查控件是否已经被BaseHandler正确初始化
        control = getattr(self.ui, control_name)
        current_control_value = control.value()

        # 从基类的映射表中获取控件信息
        widget_info = super().get_widget_info(control_name)

        if widget_info:
            # 获取默认值
            binary_default = widget_info.get("default_value")

            # 将二进制默认值转换为整数
            expected_value = 1  # 默认值，以防无法转换
            if binary_default and all(c in '01' for c in binary_default):
                expected_value = int(binary_default, 2)

            logger.info(f"从映射表中获取{info['source']}的期望分频比: {expected_value}")
            logger.info(f"控件{control_name}当前值: {current_control_value}")

            # 如果BaseHandler已经正确设置了值，就使用它
            if current_control_value == expected_value:
                default_value = current_control_value
                logger.info(f"BaseHandler已正确初始化{control_name}，使用当前值: {default_value}")
            else:
                # 如果值不匹配，使用映射表中的正确值
                default_value = expected_value
                logger.info(f"控件值不匹配，使用映射表值: {default_value}")
                control.setValue(default_value)
        else:
            # 如果在映射表中找不到控件，使用默认值
            default_value = self._get_default_divider_value(info['source'])
            logger.info(f"未在映射表中找到{control_name}，使用默认分频比: {default_value}")
            control.setValue(default_value)

        # 设置实例变量
        setattr(self, info["var"], default_value)

    def _get_default_divider_value(self, source_name):
        """获取默认的分频比值（与寄存器配置文件中的默认值一致）"""
        # 根据寄存器配置文件中的默认值：
        # 0x63 (CLKin0): "00000001111000" = 120
        # 0x65 (CLKin1): "00000001111000" = 120
        # 0x67 (CLKin2): "00000010010110" = 150
        if source_name == "CLKin0":
            return 120  # 对应寄存器0x63的默认值
        elif source_name == "CLKin1":
            return 120  # 对应寄存器0x65的默认值
        elif source_name == "CLKin2":
            return 150  # 对应寄存器0x67的默认值
        else:
            return 1

    def connect_signals(self):
        """连接自定义信号与槽"""
        # 时钟源选择信号
        self.ui.CLKinSelManual.currentIndexChanged.connect(self.update_clkin_sel_out)
        
        # 时钟频率输入信号 - 使用更简洁的循环连接
        for source, lineedit in [
            ("CLKin0", self.ui.lineEditClkin0),
            ("CLKin1", self.ui.lineEditClkin1),
            ("CLKin2", self.ui.lineEditClkin2Oscout)
        ]:
            lineedit.returnPressed.connect(self.update_clkin_sel_out)
            lineedit.editingFinished.connect(lambda s=source: self.update_clkin_value(s))
        
        # 分频比控件信号 - 使用更简洁的循环连接
        for source, spinner in [
            ("CLKin0", self.ui.PLL1R0Div),
            ("CLKin1", self.ui.PLL1R1Div),
            ("CLKin2", self.ui.PLL1R2Div)
        ]:
            spinner.valueChanged.connect(lambda value, s=source: self.update_divider_value(s, value))

    def init_clkin_frequency(self):
        """设置时钟输入频率"""
        self.ui.lineEditClkin0.setText(str(self.clkin0))
        self.ui.lineEditClkin1.setText(str(self.clkin1))
        self.ui.lineEditClkin2Oscout.setText(str(self.clkin2))

        # 动态设置 lineEditClkinSelOut 的初始值
        default_index = self.ui.CLKinSelManual.currentIndex()
        self.update_clkin_sel_out(default_index)

    def update_clkin_sel_out(self, index=None):
        """根据 CLKinSelManual 的选择更新 lineEditClkinSelOut 的值并发布更新"""
        if index is None:
            index = self.ui.CLKinSelManual.currentIndex()

        # 获取时钟源信息
        selected_source, freq_value, divider_value = self._get_clock_info_by_index(index)

        # 获取显示值
        clkin_value = ""
        if index == 0:  # CLKin0
            clkin_value = self.ui.lineEditClkin0.text()
        elif index == 1:  # CLKin1
            clkin_value = self.ui.lineEditClkin1.text()
        elif index == 2:  # CLKin2
            clkin_value = self.ui.lineEditClkin2Oscout.text()
        else:  # Holdover
            clkin_value = self.previous_clkin_value  # 保持上一次的值

        # 调试日志：检查频率值是否一致
        logger.info(f"时钟源切换调试: 索引={index}, 源={selected_source}")
        logger.info(f"  显示值(clkin_value): '{clkin_value}'")
        logger.info(f"  信号值(freq_value): {freq_value}")
        logger.info(f"  分频值(divider_value): {divider_value}")

        # 确保信号发送的频率值与显示值一致
        try:
            display_freq = float(clkin_value) if clkin_value else 0.0
            if abs(display_freq - freq_value) > 0.001:  # 如果差异超过0.001
                logger.warning(f"频率值不一致! 显示值={display_freq}, 信号值={freq_value}, 使用显示值")
                freq_value = display_freq
        except (ValueError, TypeError):
            logger.warning(f"无法解析显示频率值: '{clkin_value}', 使用信号值: {freq_value}")

        # 更新 lineEditClkinSelOut 的值
        self.ui.lineEditClkinSelOut.setText(clkin_value)

        # 更新 previous_clkin_value
        self.previous_clkin_value = clkin_value

        # 发布时钟源选择更新通知
        self._emit_clock_source_update(selected_source, freq_value, divider_value)

    def _emit_clock_source_update(self, source_name, freq_value, divider_value):
        """发布时钟源更新通知"""
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            # 设置当前时钟源
            RegisterUpdateBus.instance()._current_clock_source = source_name
            # 发布时钟源选择更新
            RegisterUpdateBus.instance().emit_clock_source_selected(source_name, freq_value, divider_value)
            logger.info(f"已发布时钟源更新: 源={source_name}, 频率={freq_value}MHz, 分频比={divider_value}")
        except Exception as e:
            logger.error(f"发布时钟源更新时发生错误: {str(e)}")
            traceback.print_exc()

    def handle_register_value_change(self, widget_name):
        """处理寄存器值改变事件"""
        pass

    def _get_combobox_type(self, widget_name):
        """根据控件名称获取ComboBox类型，重写基类方法"""
        # 直接映射表，优先级高于基类的规则
        widget_type_map = {
            "CLKin0Demux": "CLKDEMUX",
            "CLKin1Demux": "CLKDEMUX",
            "CLKin1Type": "CLKin1Type",
            "OSCoutMux": "OSCoutMux",
            "CLKinSelManual": "CLKinSelManual",
            "CLKinSel0Type": "CLKinSelType",
            "CLKinSel1Type": "CLKinSelType",
            "CLKinSel0Mux": "CLKinSelMux",
            "CLKinSel1Mux": "CLKinSelMux",
            "OSCoutClockFormat": "OSCoutClockFormat",
            "syncSource": "syncSource",
        }
        
        # 如果widget_name在映射表中，直接返回映射的类型
        return widget_type_map.get(widget_name, super()._get_combobox_type(widget_name))

    def update_clkin_value(self, source_name):
        """处理时钟输入频率编辑完成事件"""
        try:
            # 获取对应的频率控件
            if source_name == "CLKin0":
                freq_control = self.ui.lineEditClkin0
                divider_control = self.ui.PLL1R0Div
                self.clkin0 = float(freq_control.text())
            elif source_name == "CLKin1":
                freq_control = self.ui.lineEditClkin1
                divider_control = self.ui.PLL1R1Div 
                self.clkin1 = float(freq_control.text())
            elif source_name == "CLKin2":
                freq_control = self.ui.lineEditClkin2Oscout
                divider_control = self.ui.PLL1R2Div
                self.clkin2 = float(freq_control.text())
            else:
                return
                
            # 获取频率和分频比
            freq_value = float(freq_control.text())
            divider_value = divider_control.value()
                
            # 如果当前选择的是这个时钟源，则更新lineEditClkinSelOut的值
            current_index = self.ui.CLKinSelManual.currentIndex()
            selected_source = CLOCK_SOURCES[current_index] if current_index < len(CLOCK_SOURCES) else "Holdover"
            
            if selected_source == source_name:
                self.ui.lineEditClkinSelOut.setText(str(freq_value))
                self.previous_clkin_value = str(freq_value)
            
            # 无论是否是当前选择的时钟源，都发布更新通知
            self._emit_clock_source_update(source_name, freq_value, divider_value)
            
        except (ValueError, TypeError) as e:
            logger.error(f"更新时钟输入频率时发生错误: {str(e)}")
        except Exception as e:
            logger.error(f"处理时钟输入频率编辑事件时发生错误: {str(e)}")
            traceback.print_exc()

    def update_divider_value(self, source_name, divider_value):
        """更新时钟源的分频比值并发送更新信号"""
        try:
            logger.info(f"更新时钟源 {source_name} 的分频比为 {divider_value}")
            
            # 存储分频比
            if source_name == "CLKin0":
                self.clkin0_divider = divider_value
            elif source_name == "CLKin1":
                self.clkin1_divider = divider_value
            elif source_name == "CLKin2":
                self.clkin2_divider = divider_value
            
            # 获取频率值
            freq_value, _ = self._get_clock_info_by_name(source_name)
            
            # 发送时钟源更新信号
            self._emit_clock_source_update(source_name, freq_value, divider_value)
            
            # 如果当前选择的是这个时钟源，更新输出显示
            self.update_clkin_sel_out()
        except Exception as e:
            logger.error(f"更新时钟源分频比时发生错误: {str(e)}")
            traceback.print_exc()

    def closeEvent(self, event):
        """重写closeEvent方法，发出window_closed信号"""
        self.window_closed.emit()
        event.accept()

if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    
    # 使用测试模式创建实例
    clkin_control = ClkinControlHandler.create_for_testing()
    clkin_control.show()
    
    sys.exit(app.exec_())