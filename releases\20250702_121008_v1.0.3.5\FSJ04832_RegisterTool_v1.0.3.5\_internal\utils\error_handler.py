# -*- coding: utf-8 -*-

from PyQt5.QtWidgets import QMessageBox
from utils.Log import get_module_logger

logger = get_module_logger(__name__)  # 假设 Log.py 在根目录或者 PYTHONPATH 中
from .message_strings import MessageStrings # 从同级目录导入

class ErrorHandler:
    """错误处理类，提供统一的错误处理机制"""
    
    @staticmethod
    def show_error(parent, error_code, details=None, show_suggestion=True):
        """显示错误消息
        
        Args:
            parent: 父窗口
            error_code: 错误代码
            details: 错误详情
            show_suggestion: 是否显示建议
        """
        # 获取错误消息
        error_msg = MessageStrings.get_error(error_code)
        
        # 构建完整错误消息
        full_msg = error_msg
        
        # 添加详情
        if details:
            full_msg += f"\n\n详细信息: {details}"
        
        # 添加建议
        if show_suggestion:
            suggestion = MessageStrings.get_suggestion(error_code)
            if suggestion:
                full_msg += f"\n\n建议: {suggestion}"
        
        # 记录日志
        logger.error(f"错误: {error_code} - {full_msg}")
        
        # 显示消息框
        QMessageBox.critical(parent, error_msg, full_msg)
    
    @staticmethod
    def show_warning(parent, warning_code, details=None, show_suggestion=True):
        """显示警告消息
        
        Args:
            parent: 父窗口
            warning_code: 警告代码 (注意: MessageStrings 中可能需要添加 warning_messages)
            details: 警告详情
            show_suggestion: 是否显示建议
        """
        # 获取警告消息 (假设 MessageStrings 有 get_warning 方法)
        # warning_msg = MessageStrings.get_warning(warning_code) 
        # 暂时使用 info_messages 代替，如果需要区分，请在 MessageStrings 中添加
        warning_msg = MessageStrings.get_info(warning_code) 
        
        # 构建完整警告消息
        full_msg = warning_msg
        
        # 添加详情
        if details:
            full_msg += f"\n\n详细信息: {details}"
            
        # 添加建议 (假设 MessageStrings 有 get_suggestion 方法)
        if show_suggestion:
            suggestion = MessageStrings.get_suggestion(warning_code)
            if suggestion:
                full_msg += f"\n\n建议: {suggestion}"

        # 记录日志
        logger.warning(f"警告: {warning_code} - {full_msg}")
        
        # 显示消息框
        QMessageBox.warning(parent, warning_msg, full_msg)