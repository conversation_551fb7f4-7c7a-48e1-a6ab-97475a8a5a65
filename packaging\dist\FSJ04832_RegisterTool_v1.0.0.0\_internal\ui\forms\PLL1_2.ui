<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>PLL1_2</class>
 <widget class="QWidget" name="PLL1_2">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>2012</width>
    <height>1206</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QLabel" name="label">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>2041</width>
     <height>1234</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="pixmap">
    <pixmap resource="../qrc/PLL1_2.qrc">:/pll1_2/PLL1and2.bmp</pixmap>
   </property>
   <property name="scaledContents">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QComboBox" name="comboPLL1WindSize">
   <property name="geometry">
    <rect>
     <x>700</x>
     <y>181</y>
     <width>136</width>
     <height>31</height>
    </rect>
   </property>
  </widget>
  <widget class="QSpinBox" name="spinBoxPLL1DLDCNT">
   <property name="geometry">
    <rect>
     <x>701</x>
     <y>267</y>
     <width>140</width>
     <height>28</height>
    </rect>
   </property>
  </widget>
  <widget class="QCheckBox" name="PLL2PD">
   <property name="geometry">
    <rect>
     <x>501</x>
     <y>190</y>
     <width>31</width>
     <height>41</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 27px;  /* 设置方框宽度 */
                height: 27px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QComboBox" name="PLL2WINDSIZE">
   <property name="geometry">
    <rect>
     <x>904</x>
     <y>184</y>
     <width>137</width>
     <height>28</height>
    </rect>
   </property>
  </widget>
  <widget class="QCheckBox" name="FCALEN">
   <property name="geometry">
    <rect>
     <x>1068</x>
     <y>149</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 27px;  /* 设置方框宽度 */
                height: 27px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QSpinBox" name="spinBoxPLL2DLDCNT">
   <property name="geometry">
    <rect>
     <x>905</x>
     <y>267</y>
     <width>139</width>
     <height>32</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="PLL1CPState">
   <property name="geometry">
    <rect>
     <x>994</x>
     <y>509</y>
     <width>116</width>
     <height>26</height>
    </rect>
   </property>
  </widget>
  <widget class="QCheckBox" name="RBPLL1DLD">
   <property name="geometry">
    <rect>
     <x>1383</x>
     <y>150</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 27px;  /* 设置方框宽度 */
                height: 27px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QComboBox" name="PLL1NclkMux">
   <property name="geometry">
    <rect>
     <x>520</x>
     <y>688</y>
     <width>111</width>
     <height>28</height>
    </rect>
   </property>
  </widget>
  <widget class="QSpinBox" name="PLL1NDivider">
   <property name="geometry">
    <rect>
     <x>731</x>
     <y>677</y>
     <width>101</width>
     <height>30</height>
    </rect>
   </property>
  </widget>
  <widget class="QSpinBox" name="PLL1RDividerSetting">
   <property name="geometry">
    <rect>
     <x>732</x>
     <y>494</y>
     <width>101</width>
     <height>31</height>
    </rect>
   </property>
  </widget>
  <widget class="QLineEdit" name="PLL1PFDFreq">
   <property name="geometry">
    <rect>
     <x>994</x>
     <y>482</y>
     <width>116</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="PLL1PFDPolarity">
   <property name="geometry">
    <rect>
     <x>994</x>
     <y>536</y>
     <width>116</width>
     <height>30</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="PLL1CPGain">
   <property name="geometry">
    <rect>
     <x>994</x>
     <y>568</y>
     <width>116</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QLineEdit" name="ExternalVCXOFreq">
   <property name="geometry">
    <rect>
     <x>1758</x>
     <y>574</y>
     <width>131</width>
     <height>49</height>
    </rect>
   </property>
  </widget>
  <widget class="QCheckBox" name="VCOPD">
   <property name="geometry">
    <rect>
     <x>1582</x>
     <y>823</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 22px;  /* 设置方框宽度 */
                height: 22px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="Div2">
   <property name="geometry">
    <rect>
     <x>1562</x>
     <y>1112</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 22px;  /* 设置方框宽度 */
                height: 22px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QComboBox" name="comboVcoMode">
   <property name="geometry">
    <rect>
     <x>1725</x>
     <y>1036</y>
     <width>108</width>
     <height>30</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
     <pointsize>8</pointsize>
    </font>
   </property>
  </widget>
  <widget class="QCheckBox" name="OSCinPD">
   <property name="geometry">
    <rect>
     <x>197</x>
     <y>838</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 22px;  /* 设置方框宽度 */
                height: 22px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="FBMuxEn">
   <property name="geometry">
    <rect>
     <x>153</x>
     <y>923</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 22px;  /* 设置方框宽度 */
                height: 22px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QComboBox" name="FBMUX">
   <property name="geometry">
    <rect>
     <x>191</x>
     <y>1090</y>
     <width>120</width>
     <height>31</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="PLL2NclkMux">
   <property name="geometry">
    <rect>
     <x>624</x>
     <y>1064</y>
     <width>131</width>
     <height>28</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
     <pointsize>8</pointsize>
    </font>
   </property>
  </widget>
  <widget class="QComboBox" name="PLL2Prescaler">
   <property name="geometry">
    <rect>
     <x>914</x>
     <y>1143</y>
     <width>114</width>
     <height>41</height>
    </rect>
   </property>
  </widget>
  <widget class="QLineEdit" name="PLL2PFDFreq">
   <property name="geometry">
    <rect>
     <x>1020</x>
     <y>893</y>
     <width>116</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="PLL2CPState">
   <property name="geometry">
    <rect>
     <x>1020</x>
     <y>920</y>
     <width>116</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="PLL2PFDPolarity">
   <property name="geometry">
    <rect>
     <x>1020</x>
     <y>948</y>
     <width>116</width>
     <height>29</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="PLL2CPGain">
   <property name="geometry">
    <rect>
     <x>1020</x>
     <y>978</y>
     <width>116</width>
     <height>26</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="PLL2C1">
   <property name="geometry">
    <rect>
     <x>1341</x>
     <y>896</y>
     <width>106</width>
     <height>26</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="PLL2R3">
   <property name="geometry">
    <rect>
     <x>1341</x>
     <y>929</y>
     <width>106</width>
     <height>26</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="PLL2C3">
   <property name="geometry">
    <rect>
     <x>1341</x>
     <y>960</y>
     <width>106</width>
     <height>26</height>
    </rect>
   </property>
  </widget>
  <widget class="QLineEdit" name="Fin0Freq">
   <property name="geometry">
    <rect>
     <x>1126</x>
     <y>1048</y>
     <width>97</width>
     <height>28</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtG</family>
     <pointsize>8</pointsize>
    </font>
   </property>
  </widget>
  <widget class="QLineEdit" name="OSCinFreq">
   <property name="geometry">
    <rect>
     <x>197</x>
     <y>796</y>
     <width>113</width>
     <height>30</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="Doubler">
   <property name="geometry">
    <rect>
     <x>471</x>
     <y>815</y>
     <width>81</width>
     <height>32</height>
    </rect>
   </property>
  </widget>
  <widget class="QSpinBox" name="PLL2RDivider">
   <property name="geometry">
    <rect>
     <x>850</x>
     <y>882</y>
     <width>95</width>
     <height>29</height>
    </rect>
   </property>
  </widget>
  <widget class="QSpinBox" name="PLL2NDivider">
   <property name="geometry">
    <rect>
     <x>848</x>
     <y>992</y>
     <width>97</width>
     <height>29</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="FCALM1">
   <property name="geometry">
    <rect>
     <x>1062</x>
     <y>192</y>
     <width>107</width>
     <height>30</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="FCALM2">
   <property name="geometry">
    <rect>
     <x>1062</x>
     <y>235</y>
     <width>107</width>
     <height>30</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="OSCin_FREQ">
   <property name="geometry">
    <rect>
     <x>1062</x>
     <y>280</y>
     <width>107</width>
     <height>31</height>
    </rect>
   </property>
  </widget>
  <widget class="QCheckBox" name="RBPLL2DLD">
   <property name="geometry">
    <rect>
     <x>1383</x>
     <y>192</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 27px;  /* 设置方框宽度 */
                height: 27px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QLineEdit" name="RBFcalCAPMODE">
   <property name="geometry">
    <rect>
     <x>1371</x>
     <y>235</y>
     <width>132</width>
     <height>31</height>
    </rect>
   </property>
  </widget>
  <widget class="QLineEdit" name="RBPLL2VtuneADC">
   <property name="geometry">
    <rect>
     <x>1371</x>
     <y>281</y>
     <width>132</width>
     <height>28</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="PLL2RclkMux">
   <property name="geometry">
    <rect>
     <x>634</x>
     <y>884</y>
     <width>131</width>
     <height>28</height>
    </rect>
   </property>
  </widget>
  <widget class="QCheckBox" name="PLL2PrePD">
   <property name="geometry">
    <rect>
     <x>1046</x>
     <y>1136</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 22px;  /* 设置方框宽度 */
                height: 22px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QComboBox" name="Fin0InputType">
   <property name="geometry">
    <rect>
     <x>1130</x>
     <y>1080</y>
     <width>139</width>
     <height>28</height>
    </rect>
   </property>
  </widget>
  <widget class="QCheckBox" name="Fin0PD">
   <property name="geometry">
    <rect>
     <x>1310</x>
     <y>1110</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 22px;  /* 设置方框宽度 */
                height: 22px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QLineEdit" name="FreFin">
   <property name="geometry">
    <rect>
     <x>435</x>
     <y>460</y>
     <width>101</width>
     <height>32</height>
    </rect>
   </property>
  </widget>
  <widget class="QLineEdit" name="AD_TSENSOR_OUT">
   <property name="geometry">
    <rect>
     <x>1737</x>
     <y>182</y>
     <width>152</width>
     <height>32</height>
    </rect>
   </property>
  </widget>
  <widget class="QLineEdit" name="SDIO_RDBK_TYPE">
   <property name="geometry">
    <rect>
     <x>208</x>
     <y>271</y>
     <width>81</width>
     <height>29</height>
    </rect>
   </property>
  </widget>
  <widget class="QCheckBox" name="powerDown">
   <property name="geometry">
    <rect>
     <x>208</x>
     <y>185</y>
     <width>31</width>
     <height>41</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 27px;  /* 设置方框宽度 */
                height: 27px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="VCOLdoPD">
   <property name="geometry">
    <rect>
     <x>501</x>
     <y>232</y>
     <width>31</width>
     <height>41</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 27px;  /* 设置方框宽度 */
                height: 27px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="PLL2OpenLoop">
   <property name="geometry">
    <rect>
     <x>501</x>
     <y>273</y>
     <width>31</width>
     <height>41</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 27px;  /* 设置方框宽度 */
                height: 27px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="PLL1PD">
   <property name="geometry">
    <rect>
     <x>501</x>
     <y>142</y>
     <width>31</width>
     <height>41</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 27px;  /* 设置方框宽度 */
                height: 27px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="spiReset">
   <property name="geometry">
    <rect>
     <x>208</x>
     <y>141</y>
     <width>31</width>
     <height>41</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 27px;  /* 设置方框宽度 */
                height: 27px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SPI3WrireDis">
   <property name="geometry">
    <rect>
     <x>208</x>
     <y>229</y>
     <width>31</width>
     <height>41</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 27px;  /* 设置方框宽度 */
                height: 27px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
 </widget>
 <resources>
  <include location="../qrc/PLL1_2.qrc"/>
  <include location="../qrc/PLL1_2.qrc"/>
 </resources>
 <connections/>
</ui>
