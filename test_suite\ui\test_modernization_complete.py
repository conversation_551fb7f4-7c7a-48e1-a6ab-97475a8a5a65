#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化迁移完整性测试
验证所有工具窗口是否已完全迁移到现代化架构
"""

import sys
import os
import unittest
from PyQt5.QtWidgets import QApplication
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class TestModernizationComplete(unittest.TestCase):
    """测试现代化迁移完整性"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        if not QApplication.instance():
            cls.app = QApplication(sys.argv)
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """每个测试前的设置"""
        logger.info(f"开始测试: {self._testMethodName}")
    
    def test_01_manager_imports(self):
        """测试管理器导入是否正常"""
        print("\n🔍 测试1: 管理器导入测试")
        
        try:
            from ui.managers.TabWindowManager import TabWindowManager
            print("✅ TabWindowManager导入成功")
            
            from ui.managers.ToolWindowManager import ToolWindowManager
            print("✅ ToolWindowManager导入成功")
            
            print("🎉 所有管理器导入测试通过")
            
        except Exception as e:
            self.fail(f"管理器导入失败: {str(e)}")
    
    def test_02_modern_handlers_import(self):
        """测试所有现代化处理器导入"""
        print("\n🔍 测试2: 现代化处理器导入测试")
        
        modern_handlers = [
            ('ModernSetModesHandler', 'ui.handlers.ModernSetModesHandler'),
            ('ModernClkinControlHandler', 'ui.handlers.ModernClkinControlHandler'),
            ('ModernPLLHandler', 'ui.handlers.ModernPLLHandler'),
            ('ModernSyncSysRefHandler', 'ui.handlers.ModernSyncSysRefHandler'),
            ('ModernClkOutputsHandler', 'ui.handlers.ModernClkOutputsHandler'),
            ('ModernRegisterTableHandler', 'ui.handlers.ModernRegisterTableHandler'),
            ('ModernUIEventHandler', 'ui.handlers.ModernUIEventHandler'),
            ('ModernRegisterIOHandler', 'ui.handlers.ModernRegisterIOHandler'),
            ('ModernRegisterTreeHandler', 'ui.handlers.ModernRegisterTreeHandler'),
        ]
        
        for handler_name, module_path in modern_handlers:
            try:
                module = __import__(module_path, fromlist=[handler_name])
                handler_class = getattr(module, handler_name)
                print(f"✅ {handler_name} 导入成功")
            except Exception as e:
                self.fail(f"{handler_name} 导入失败: {str(e)}")
        
        print("🎉 所有现代化处理器导入测试通过")
    
    def test_03_legacy_handlers_status(self):
        """测试传统处理器状态（应该仍可导入但不应被使用）"""
        print("\n🔍 测试3: 传统处理器状态检查")
        
        legacy_handlers = [
            ('SetModesHandler', 'ui.handlers.SetModesHandler'),
            ('ClkinControlHandler', 'ui.handlers.ClkinControlHandler'),
            ('RegisterTableHandler', 'ui.handlers.RegisterTableHandler'),
            ('UIEventHandler', 'ui.handlers.UIEventHandler'),
            ('RegisterIOHandler', 'ui.handlers.RegisterIOHandler'),
            ('RegisterTreeHandler', 'ui.handlers.RegisterTreeHandler'),
        ]
        
        for handler_name, module_path in legacy_handlers:
            try:
                module = __import__(module_path, fromlist=[handler_name])
                handler_class = getattr(module, handler_name)
                print(f"ℹ️  {handler_name} 仍可导入（保持兼容性）")
            except Exception as e:
                print(f"⚠️  {handler_name} 导入失败: {str(e)} (可能已被移除)")
        
        # 检查PLLHandler是否已被移除
        try:
            from ui.handlers.PLLHandler import PLLHandler
            self.fail("❌ PLLHandler仍然存在，应该已被移除")
        except ImportError:
            print("✅ PLLHandler已正确移除")
        
        print("🎉 传统处理器状态检查完成")
    
    def test_04_factory_configuration(self):
        """测试现代化工厂配置"""
        print("\n🔍 测试4: 现代化工厂配置测试")
        
        try:
            from ui.factories.ModernToolWindowFactory import ModernToolWindowFactory
            
            # 检查所有处理器配置
            configs = ModernToolWindowFactory.HANDLER_CONFIGS
            
            expected_handlers = [
                'set_modes', 'clkin_control', 'pll_control', 
                'sync_sysref', 'clk_output', 'register_table',
                'ui_event', 'register_io', 'register_tree'
            ]
            
            for handler_type in expected_handlers:
                self.assertIn(handler_type, configs, f"缺少处理器配置: {handler_type}")
                config = configs[handler_type]
                
                # 检查是否配置为使用现代化版本
                use_modern = config.get('use_modern', False)
                self.assertTrue(use_modern, f"{handler_type} 未配置为使用现代化版本")
                
                # 检查是否有现代化处理器
                modern_handler = config.get('modern_handler')
                self.assertIsNotNone(modern_handler, f"{handler_type} 缺少现代化处理器配置")
                
                print(f"✅ {handler_type}: use_modern={use_modern}, handler={modern_handler}")
            
            print("🎉 现代化工厂配置测试通过")
            
        except Exception as e:
            self.fail(f"现代化工厂配置测试失败: {str(e)}")
    
    def test_05_window_creation_simulation(self):
        """测试窗口创建模拟（不实际创建UI）"""
        print("\n🔍 测试5: 窗口创建模拟测试")
        
        try:
            # 创建模拟的RegisterManager
            from core.services.register.RegisterManager import RegisterManager
            import json
            
            # 加载寄存器配置
            config_path = os.path.join(project_root, 'lib', 'register.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                registers_config = json.load(f)
            
            register_manager = RegisterManager(registers_config)
            
            # 测试现代化处理器创建
            modern_handlers_to_test = [
                ('ModernSetModesHandler', 'ui.handlers.ModernSetModesHandler'),
                ('ModernClkinControlHandler', 'ui.handlers.ModernClkinControlHandler'),
                ('ModernPLLHandler', 'ui.handlers.ModernPLLHandler'),
                ('ModernSyncSysRefHandler', 'ui.handlers.ModernSyncSysRefHandler'),
                ('ModernClkOutputsHandler', 'ui.handlers.ModernClkOutputsHandler'),
            ]
            
            for handler_name, module_path in modern_handlers_to_test:
                try:
                    module = __import__(module_path, fromlist=[handler_name])
                    handler_class = getattr(module, handler_name)
                    
                    # 尝试创建实例（使用现代化参数）
                    instance = handler_class(parent=None, register_manager=register_manager)
                    self.assertIsNotNone(instance)
                    
                    print(f"✅ {handler_name} 创建成功")
                    
                    # 清理
                    if hasattr(instance, 'close'):
                        instance.close()
                    del instance
                    
                except Exception as e:
                    print(f"⚠️  {handler_name} 创建失败: {str(e)}")
                    # 不让单个处理器失败影响整个测试
                    continue
            
            print("🎉 窗口创建模拟测试完成")
            
        except Exception as e:
            self.fail(f"窗口创建模拟测试失败: {str(e)}")


    def test_06_manager_window_methods(self):
        """测试管理器窗口方法是否使用现代化处理器"""
        print("\n🔍 测试6: 管理器窗口方法测试")

        # 创建模拟主窗口
        class MockMainWindow:
            def __init__(self):
                from core.services.register.RegisterManager import RegisterManager
                import json

                config_path = os.path.join(project_root, 'lib', 'register.json')
                with open(config_path, 'r', encoding='utf-8') as f:
                    registers_config = json.load(f)

                self.register_manager = RegisterManager(registers_config)

            def _create_window_in_tab(self, **kwargs):
                """模拟标签页窗口创建"""
                window_class = kwargs.get('window_class')
                register_manager = kwargs.get('register_manager')

                if window_class and register_manager:
                    return window_class(parent=None, register_manager=register_manager)
                return None

        try:
            mock_main_window = MockMainWindow()

            # 测试TabWindowManager
            from ui.managers.TabWindowManager import TabWindowManager
            tab_manager = TabWindowManager(mock_main_window)

            window_methods = [
                ('show_set_modes_window', '模式设置'),
                ('show_clkin_control_window', '时钟输入控制'),
                ('show_pll_control_window', 'PLL控制'),
                ('show_sync_sysref_window', '同步系统参考'),
                ('show_clk_output_window', '时钟输出'),
            ]

            for method_name, window_name in window_methods:
                try:
                    method = getattr(tab_manager, method_name)
                    # 尝试调用方法（可能会因为UI依赖而失败，但至少能检查导入）
                    try:
                        result = method()
                        print(f"✅ TabWindowManager.{method_name} ({window_name}) 调用成功")
                        if result and hasattr(result, 'close'):
                            result.close()
                    except Exception as e:
                        # 检查错误是否与现代化处理器相关
                        if "Modern" in str(e) or "register_manager" in str(e):
                            print(f"✅ TabWindowManager.{method_name} ({window_name}) 使用现代化处理器")
                        else:
                            print(f"⚠️  TabWindowManager.{method_name} ({window_name}) 调用失败: {str(e)}")

                except AttributeError:
                    self.fail(f"TabWindowManager缺少方法: {method_name}")

            print("🎉 管理器窗口方法测试完成")

        except Exception as e:
            self.fail(f"管理器窗口方法测试失败: {str(e)}")

    def test_07_integration_consistency(self):
        """测试集成一致性 - 工厂配置与管理器实现的一致性"""
        print("\n🔍 测试7: 集成一致性测试")

        try:
            from ui.factories.ModernToolWindowFactory import ModernToolWindowFactory

            # 获取工厂配置
            factory_configs = ModernToolWindowFactory.HANDLER_CONFIGS

            # 检查管理器实现与工厂配置的一致性
            consistency_checks = [
                ('set_modes', 'ModernSetModesHandler'),
                ('clkin_control', 'ModernClkinControlHandler'),
                ('pll_control', 'ModernPLLHandler'),
                ('sync_sysref', 'ModernSyncSysRefHandler'),
                ('clk_output', 'ModernClkOutputsHandler'),
            ]

            for config_key, expected_handler in consistency_checks:
                # 检查工厂配置
                if config_key in factory_configs:
                    config = factory_configs[config_key]
                    modern_handler_path = config.get('modern_handler', '')

                    if expected_handler in modern_handler_path:
                        print(f"✅ {config_key}: 工厂配置使用 {expected_handler}")
                    else:
                        self.fail(f"❌ {config_key}: 工厂配置不匹配，期望 {expected_handler}，实际 {modern_handler_path}")
                else:
                    self.fail(f"❌ 工厂配置缺少 {config_key}")

            print("🎉 集成一致性测试通过")

        except Exception as e:
            self.fail(f"集成一致性测试失败: {str(e)}")


if __name__ == '__main__':
    print("🚀 开始现代化迁移完整性测试")
    print("=" * 60)

    # 运行测试
    unittest.main(verbosity=2, exit=False)

    print("\n" + "=" * 60)
    print("🎯 现代化迁移完整性测试完成")
