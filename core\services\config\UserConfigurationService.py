"""
用户配置管理服务
负责处理默认配置保存/恢复和软件记忆功能
"""

import json
import os
from typing import Dict, Any, Optional
from PyQt5.QtCore import QSettings
from PyQt5.QtWidgets import QMessageBox
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class UserConfigurationService:
    """用户配置管理服务"""
    
    def __init__(self, main_window):
        """初始化用户配置服务
        
        Args:
            main_window: 主窗口实例
        """
        self.main_window = main_window
        self.settings = QSettings("FSJ", "FSJRead")
        
        # 配置文件路径
        self.config_dir = os.path.join(os.path.expanduser("~"), ".fsj_register_tool")
        self.default_config_file = os.path.join(self.config_dir, "default_config.json")
        self.last_config_file = os.path.join(self.config_dir, "last_config.json")
        
        # 确保配置目录存在
        self._ensure_config_directory()
        
    def _ensure_config_directory(self):
        """确保配置目录存在"""
        try:
            if not os.path.exists(self.config_dir):
                os.makedirs(self.config_dir)
                logger.info(f"创建配置目录: {self.config_dir}")
        except Exception as e:
            logger.error(f"创建配置目录失败: {str(e)}")
    
    def _safe_show_message(self, title: str, message: str, icon=QMessageBox.Information):
        """安全地显示消息框"""
        try:
            msg_box = QMessageBox(self.main_window)
            msg_box.setWindowTitle(title)
            msg_box.setText(message)
            msg_box.setIcon(icon)
            msg_box.exec_()
        except Exception as e:
            logger.error(f"显示消息框时出错: {str(e)}")
    
    def _collect_current_configuration(self) -> Dict[str, Any]:
        """收集当前的配置信息
        
        Returns:
            包含当前配置的字典
        """
        config = {
            "registers": {},
            "tool_windows": {},
            "timestamp": None
        }
        
        try:
            # 收集寄存器值
            if hasattr(self.main_window, 'register_manager') and self.main_window.register_manager:
                config["registers"] = self.main_window.register_manager.get_all_register_values()
                logger.debug(f"收集到 {len(config['registers'])} 个寄存器值")
            
            # 收集工具窗口的配置
            config["tool_windows"] = self._collect_tool_window_configurations()
            
            # 添加时间戳
            from datetime import datetime
            config["timestamp"] = datetime.now().isoformat()
            
            logger.info("成功收集当前配置信息")
            return config
            
        except Exception as e:
            logger.error(f"收集当前配置时出错: {str(e)}")
            return config
    
    def _collect_tool_window_configurations(self) -> Dict[str, Any]:
        """收集工具窗口的配置信息
        
        Returns:
            包含工具窗口配置的字典
        """
        tool_configs = {}
        
        try:
            # 通过插件系统获取工具窗口
            if hasattr(self.main_window, 'plugin_integration_service'):
                plugin_service = self.main_window.plugin_integration_service
                
                # 获取所有工具窗口插件
                from core.services.plugin.PluginManager import plugin_manager
                tool_plugins = plugin_manager.get_tool_window_plugins()
                
                for plugin in tool_plugins:
                    try:
                        # 获取工具窗口实例
                        tool_window = plugin_service.get_plugin_window(plugin.name)
                        if tool_window and hasattr(tool_window, 'ui'):
                            config = self._extract_tool_window_config(plugin.name, tool_window)
                            if config:
                                tool_configs[plugin.name] = config
                                logger.debug(f"收集到工具窗口 {plugin.name} 的配置")
                    except Exception as e:
                        logger.warning(f"收集工具窗口 {plugin.name} 配置时出错: {str(e)}")
                        continue
            
            logger.info(f"成功收集 {len(tool_configs)} 个工具窗口的配置")
            return tool_configs
            
        except Exception as e:
            logger.error(f"收集工具窗口配置时出错: {str(e)}")
            return tool_configs
    
    def _extract_tool_window_config(self, window_name: str, tool_window) -> Optional[Dict[str, Any]]:
        """提取单个工具窗口的配置
        
        Args:
            window_name: 工具窗口名称
            tool_window: 工具窗口实例
            
        Returns:
            工具窗口的配置字典
        """
        config = {}
        
        try:
            ui = tool_window.ui
            
            # 根据不同的工具窗口类型提取配置
            if window_name == "时钟输入控制":
                config.update(self._extract_clkin_control_config(ui))
            elif window_name == "PLL控制":
                config.update(self._extract_pll_control_config(ui))
            elif window_name == "时钟输出":
                config.update(self._extract_clk_outputs_config(ui))
            elif window_name == "同步系统参考":
                config.update(self._extract_sync_system_config(ui))
            elif window_name == "模式设置":
                config.update(self._extract_mode_setting_config(ui))
            
            return config if config else None
            
        except Exception as e:
            logger.error(f"提取工具窗口 {window_name} 配置时出错: {str(e)}")
            return None
    
    def _extract_clkin_control_config(self, ui) -> Dict[str, Any]:
        """提取时钟输入控制窗口的配置"""
        config = {}
        
        try:
            # 提取频率值
            frequency_controls = [
                "lineEditClkin0Oscout", "lineEditClkin1Oscout", "lineEditClkin2Oscout",
                "lineEditClkinSelOut", "DACUpdateRate"
            ]
            
            for control_name in frequency_controls:
                if hasattr(ui, control_name):
                    control = getattr(ui, control_name)
                    if hasattr(control, 'text'):
                        config[control_name] = control.text()
            
            # 提取分频值
            divider_controls = ["PLL1R0Div", "PLL1R1Div", "PLL1R2Div"]
            for control_name in divider_controls:
                if hasattr(ui, control_name):
                    control = getattr(ui, control_name)
                    if hasattr(control, 'value'):
                        config[control_name] = control.value()
            
            # 提取ComboBox选择
            combo_controls = ["CLKinSelManual"]
            for control_name in combo_controls:
                if hasattr(ui, control_name):
                    control = getattr(ui, control_name)
                    if hasattr(control, 'currentIndex'):
                        config[control_name] = control.currentIndex()
            
            logger.debug(f"提取时钟输入控制配置: {len(config)} 个控件")
            
        except Exception as e:
            logger.error(f"提取时钟输入控制配置时出错: {str(e)}")
        
        return config
    
    def _extract_pll_control_config(self, ui) -> Dict[str, Any]:
        """提取PLL控制窗口的配置"""
        config = {}
        
        try:
            # 提取频率值
            frequency_controls = [
                "OSCinFreq", "ExternalVCXOFreq", "FreFin", "PLL1PFDFreq", "PLL2PFDFreq",
                "VCODistFreq", "InternalVCOFreq", "Fin0Freq"
            ]
            
            for control_name in frequency_controls:
                if hasattr(ui, control_name):
                    control = getattr(ui, control_name)
                    if hasattr(control, 'text'):
                        config[control_name] = control.text()
            
            # 提取SpinBox值
            spinbox_controls = [
                "PLL1NDivider", "PLL1RDivider", "PLL2NDivider", "PLL2RDivider", "PLL2Prescaler"
            ]
            
            for control_name in spinbox_controls:
                if hasattr(ui, control_name):
                    control = getattr(ui, control_name)
                    if hasattr(control, 'value'):
                        config[control_name] = control.value()
            
            logger.debug(f"提取PLL控制配置: {len(config)} 个控件")
            
        except Exception as e:
            logger.error(f"提取PLL控制配置时出错: {str(e)}")

        return config

    def _extract_clk_outputs_config(self, ui) -> Dict[str, Any]:
        """提取时钟输出窗口的配置"""
        config = {}

        try:
            # 提取分频值
            divider_controls = [
                "DCLK0_1DIV", "DCLK2_3DIV", "DCLK4_5DIV", "DCLK6_7DIV",
                "DCLK8_9DIV", "DCLK10_11DIV", "DCLK12_13DIV"
            ]

            for control_name in divider_controls:
                if hasattr(ui, control_name):
                    control = getattr(ui, control_name)
                    if hasattr(control, 'value'):
                        config[control_name] = control.value()

            # 提取频率显示值
            frequency_displays = [
                "lineEditFvco", "lineEditDCLK0", "lineEditDCLK1", "lineEditDCLK2",
                "lineEditDCLK3", "lineEditDCLK4", "lineEditDCLK5", "lineEditDCLK6",
                "lineEditDCLK7", "lineEditDCLK8", "lineEditDCLK9", "lineEditDCLK10",
                "lineEditDCLK11", "lineEditDCLK12", "lineEditDCLK13"
            ]

            for control_name in frequency_displays:
                if hasattr(ui, control_name):
                    control = getattr(ui, control_name)
                    if hasattr(control, 'text'):
                        config[control_name] = control.text()

            logger.debug(f"提取时钟输出配置: {len(config)} 个控件")

        except Exception as e:
            logger.error(f"提取时钟输出配置时出错: {str(e)}")

        return config

    def _extract_sync_system_config(self, ui) -> Dict[str, Any]:
        """提取同步系统参考窗口的配置"""
        config = {}

        try:
            # 根据同步系统参考窗口的具体控件来提取配置
            # 这里需要根据实际的UI控件来调整
            logger.debug("提取同步系统参考配置")

        except Exception as e:
            logger.error(f"提取同步系统参考配置时出错: {str(e)}")

        return config

    def _extract_mode_setting_config(self, ui) -> Dict[str, Any]:
        """提取模式设置窗口的配置"""
        config = {}

        try:
            # 根据模式设置窗口的具体控件来提取配置
            # 这里需要根据实际的UI控件来调整
            logger.debug("提取模式设置配置")

        except Exception as e:
            logger.error(f"提取模式设置配置时出错: {str(e)}")

        return config

    def save_default_configuration(self):
        """从register.json文件生成默认配置"""
        try:
            # 从register.json文件中提取默认配置
            default_config = self._extract_default_configuration_from_register_json()

            with open(self.default_config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=2, ensure_ascii=False)

            self._safe_show_message(
                "生成成功",
                f"已从register.json文件生成默认配置\n保存位置: {self.default_config_file}",
                QMessageBox.Information
            )

            logger.info(f"默认配置已从register.json生成并保存到: {self.default_config_file}")
            return True

        except Exception as e:
            logger.error(f"生成默认配置时出错: {str(e)}")
            self._safe_show_message(
                "生成失败",
                f"生成默认配置时出错: {str(e)}",
                QMessageBox.Critical
            )
            return False

    def _extract_default_configuration_from_register_json(self) -> Dict[str, Any]:
        """从register.json文件中提取默认配置

        Returns:
            包含默认配置的字典
        """
        config = {
            "registers": {},
            "tool_windows": {},
            "timestamp": None,
            "source": "register.json"
        }

        try:
            # 获取register.json文件路径
            register_json_path = self._get_register_json_path()

            if not register_json_path or not os.path.exists(register_json_path):
                logger.error("未找到register.json文件")
                return config

            # 读取register.json文件
            with open(register_json_path, 'r', encoding='utf-8') as f:
                registers_data = json.load(f)

            # 从每个寄存器的位段默认值计算寄存器默认值
            for addr_str, register_info in registers_data.items():
                if "bits" in register_info:
                    register_value = 0

                    for bit_info in register_info["bits"]:
                        if "default" in bit_info and "bit" in bit_info:
                            try:
                                # 解析位段范围
                                bit_range = bit_info["bit"]
                                default_value = bit_info["default"]

                                # 计算位段的起始位置和掩码
                                start_bit, mask = self._parse_bit_range(bit_range)

                                # 将默认值转换为整数（假设是二进制字符串）
                                if isinstance(default_value, str) and all(c in '01' for c in default_value):
                                    bit_value = int(default_value, 2)
                                else:
                                    bit_value = int(default_value)

                                # 确保值不超过位段范围
                                bit_value = bit_value & mask

                                # 将位段值放置到寄存器的正确位置
                                register_value |= (bit_value << start_bit)

                            except Exception as e:
                                logger.warning(f"解析寄存器 {addr_str} 位段 {bit_info.get('bit', 'unknown')} 默认值时出错: {str(e)}")
                                continue

                    # 保存计算出的寄存器默认值
                    config["registers"][addr_str] = register_value
                    logger.debug(f"寄存器 {addr_str} 默认值: 0x{register_value:04X}")

            # 添加工具窗口的默认配置（基于硬编码的默认频率值）
            config["tool_windows"] = self._get_default_tool_window_configurations()

            # 添加时间戳
            from datetime import datetime
            config["timestamp"] = datetime.now().isoformat()

            logger.info(f"成功从register.json提取 {len(config['registers'])} 个寄存器的默认配置")
            return config

        except Exception as e:
            logger.error(f"从register.json提取默认配置时出错: {str(e)}")
            return config

    def _get_register_json_path(self) -> Optional[str]:
        """获取register.json文件路径"""
        try:
            # 尝试多个可能的路径
            possible_paths = [
                'lib/register.json',
                os.path.join(os.path.dirname(__file__), '../../../lib/register.json'),
                os.path.join(os.path.dirname(__file__), '../../lib/register.json'),
                os.path.join(os.getcwd(), 'lib/register.json')
            ]

            for path in possible_paths:
                if os.path.exists(path):
                    logger.debug(f"找到register.json文件: {path}")
                    return path

            logger.warning("未找到register.json文件")
            return None

        except Exception as e:
            logger.error(f"查找register.json文件时出错: {str(e)}")
            return None

    def _parse_bit_range(self, bit_range: str) -> tuple:
        """解析位段范围，返回起始位和掩码

        Args:
            bit_range: 位段范围字符串，如 "15:0" 或 "7"

        Returns:
            tuple: (起始位, 掩码)
        """
        try:
            if ':' in bit_range:
                # 范围格式，如 "15:0"
                high_str, low_str = bit_range.split(':')
                high_bit = int(high_str)
                low_bit = int(low_str)
                start_bit = low_bit
                bit_width = high_bit - low_bit + 1
                mask = (1 << bit_width) - 1
            else:
                # 单个位，如 "7"
                start_bit = int(bit_range)
                mask = 1

            return start_bit, mask

        except Exception as e:
            logger.error(f"解析位段范围 '{bit_range}' 时出错: {str(e)}")
            return 0, 1

    def _get_default_tool_window_configurations(self) -> Dict[str, Any]:
        """获取工具窗口的默认配置

        这些默认值基于代码中的硬编码默认值，确保与工具窗口初始化时的值一致
        """
        return {
            "时钟输入控制": {
                # 时钟输入频率（基于代码中的默认值）
                "lineEditClkin0Oscout": "122.88",
                "lineEditClkin1Oscout": "122.88",
                "lineEditClkin2Oscout": "150.0",
                "lineEditClkinSelOut": "122.88",
                # DAC更新率
                "DACUpdateRate": "0.00",
                # 分频器默认值（基于寄存器默认值）
                "PLL1R0Div": 120,  # 对应0x63寄存器CLKin0_R[13:0]默认值
                "PLL1R1Div": 120,  # 对应0x65寄存器CLKin1_R[13:0]默认值
                "PLL1R2Div": 150,  # 对应0x67寄存器CLKin2_R[13:0]默认值
                # 时钟源选择
                "CLKinSelManual": 1  # 默认选择CLKin1
            },
            "PLL控制": {
                # 输入频率控件（这些是用户输入的基础频率）
                "OSCinFreq": "122.88",
                "ExternalVCXOFreq": "122.88",
                "FreFin": "122.88",
                # PLL分频器默认值（基于寄存器默认值或常用值）
                "PLL1NDivider": 40,    # 常用的N分频值
                "PLL1RDivider": 120,   # 对应R分频器默认值
                "PLL2NDivider": 40,    # 常用的N分频值
                "PLL2RDivider": 120,   # 对应R分频器默认值
                "PLL2Prescaler": 4,    # 常用的预分频值
                # 计算结果显示（初始值，实际会根据输入计算）
                "PLL1PFDFreq": "0.00",
                "PLL2PFDFreq": "0.00",
                "VCODistFreq": "0.00",
                "InternalVCOFreq": "0.00",
                "Fin0Freq": "0.00"
            },
            "时钟输出": {
                # VCO频率显示
                "lineEditFvco": "0.00",
                # 分频器默认值（基于寄存器默认值）
                "DCLK0_1DIV": 1,
                "DCLK2_3DIV": 1,
                "DCLK4_5DIV": 1,
                "DCLK6_7DIV": 1,
                "DCLK8_9DIV": 1,
                "DCLK10_11DIV": 1,
                "DCLK12_13DIV": 1,
                # 输出频率显示（初始值）
                "lineEditDCLK0": "0.00",
                "lineEditDCLK1": "0.00",
                "lineEditDCLK2": "0.00",
                "lineEditDCLK3": "0.00",
                "lineEditDCLK4": "0.00",
                "lineEditDCLK5": "0.00",
                "lineEditDCLK6": "0.00",
                "lineEditDCLK7": "0.00",
                "lineEditDCLK8": "0.00",
                "lineEditDCLK9": "0.00",
                "lineEditDCLK10": "0.00",
                "lineEditDCLK11": "0.00",
                "lineEditDCLK12": "0.00",
                "lineEditDCLK13": "0.00"
            }
        }

    def restore_default_configuration(self):
        """恢复默认配置"""
        try:
            if not os.path.exists(self.default_config_file):
                self._safe_show_message(
                    "恢复失败",
                    "默认配置文件不存在，请先保存当前配置为默认配置",
                    QMessageBox.Warning
                )
                return False

            with open(self.default_config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 应用配置
            success = self._apply_configuration(config)

            if success:
                self._safe_show_message(
                    "恢复成功",
                    "默认配置已恢复",
                    QMessageBox.Information
                )
                logger.info("默认配置恢复成功")
            else:
                self._safe_show_message(
                    "恢复失败",
                    "恢复默认配置时出现部分错误，请检查日志",
                    QMessageBox.Warning
                )
                logger.warning("默认配置恢复时出现部分错误")

            return success

        except Exception as e:
            logger.error(f"恢复默认配置时出错: {str(e)}")
            self._safe_show_message(
                "恢复失败",
                f"恢复默认配置时出错: {str(e)}",
                QMessageBox.Critical
            )
            return False

    def save_last_configuration(self):
        """保存最近一次的配置（软件记忆功能）"""
        try:
            config = self._collect_current_configuration()

            with open(self.last_config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

            logger.info(f"最近配置已保存到: {self.last_config_file}")
            return True

        except Exception as e:
            logger.error(f"保存最近配置时出错: {str(e)}")
            return False

    def restore_last_configuration(self):
        """恢复最近一次的配置（软件记忆功能）"""
        try:
            if not os.path.exists(self.last_config_file):
                logger.info("最近配置文件不存在，跳过恢复")
                return True  # 不存在配置文件不算错误

            with open(self.last_config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 应用配置
            success = self._apply_configuration(config)

            if success:
                logger.info("最近配置恢复成功")
            else:
                logger.warning("最近配置恢复时出现部分错误")

            return success

        except Exception as e:
            logger.error(f"恢复最近配置时出错: {str(e)}")
            return False

    def _apply_configuration(self, config: Dict[str, Any]) -> bool:
        """应用配置到系统

        Args:
            config: 配置字典

        Returns:
            是否成功应用配置
        """
        success = True

        try:
            # 应用寄存器配置
            if "registers" in config and config["registers"]:
                success &= self._apply_register_configuration(config["registers"])

            # 应用工具窗口配置
            if "tool_windows" in config and config["tool_windows"]:
                success &= self._apply_tool_window_configurations(config["tool_windows"])

            logger.info(f"配置应用完成，成功: {success}")
            return success

        except Exception as e:
            logger.error(f"应用配置时出错: {str(e)}")
            return False

    def _apply_register_configuration(self, register_config: Dict[str, Any]) -> bool:
        """应用寄存器配置

        Args:
            register_config: 寄存器配置字典

        Returns:
            是否成功应用
        """
        try:
            if not hasattr(self.main_window, 'register_manager') or not self.main_window.register_manager:
                logger.warning("寄存器管理器不可用，跳过寄存器配置应用")
                return True

            register_manager = self.main_window.register_manager

            # 应用每个寄存器的值
            for addr_str, value in register_config.items():
                try:
                    addr = int(addr_str, 16) if addr_str.startswith('0x') else int(addr_str)
                    register_manager.set_register_value(addr, value)
                    logger.debug(f"应用寄存器配置: 0x{addr:02X} = 0x{value:04X}")
                except Exception as e:
                    logger.warning(f"应用寄存器 {addr_str} 配置时出错: {str(e)}")
                    continue

            logger.info(f"成功应用 {len(register_config)} 个寄存器配置")
            return True

        except Exception as e:
            logger.error(f"应用寄存器配置时出错: {str(e)}")
            return False

    def _apply_tool_window_configurations(self, tool_configs: Dict[str, Any]) -> bool:
        """应用工具窗口配置

        Args:
            tool_configs: 工具窗口配置字典

        Returns:
            是否成功应用
        """
        success = True

        try:
            if not hasattr(self.main_window, 'plugin_integration_service'):
                logger.warning("插件集成服务不可用，跳过工具窗口配置应用")
                return True

            plugin_service = self.main_window.plugin_integration_service

            for window_name, window_config in tool_configs.items():
                try:
                    # 获取工具窗口实例
                    tool_window = plugin_service.get_plugin_window(window_name)
                    if tool_window and hasattr(tool_window, 'ui'):
                        window_success = self._apply_single_tool_window_config(
                            window_name, tool_window, window_config
                        )
                        success &= window_success
                        if window_success:
                            logger.debug(f"成功应用工具窗口 {window_name} 的配置")
                        else:
                            logger.warning(f"应用工具窗口 {window_name} 配置时出现错误")
                    else:
                        logger.warning(f"工具窗口 {window_name} 不可用，跳过配置应用")
                except Exception as e:
                    logger.warning(f"应用工具窗口 {window_name} 配置时出错: {str(e)}")
                    success = False
                    continue

            logger.info(f"工具窗口配置应用完成，成功: {success}")
            return success

        except Exception as e:
            logger.error(f"应用工具窗口配置时出错: {str(e)}")
            return False

    def _apply_single_tool_window_config(self, window_name: str, tool_window, config: Dict[str, Any]) -> bool:
        """应用单个工具窗口的配置

        Args:
            window_name: 工具窗口名称
            tool_window: 工具窗口实例
            config: 配置字典

        Returns:
            是否成功应用
        """
        try:
            ui = tool_window.ui

            # 根据不同的工具窗口类型应用配置
            if window_name == "时钟输入控制":
                return self._apply_clkin_control_config(ui, config)
            elif window_name == "PLL控制":
                return self._apply_pll_control_config(ui, config)
            elif window_name == "时钟输出":
                return self._apply_clk_outputs_config(ui, config)
            elif window_name == "同步系统参考":
                return self._apply_sync_system_config(ui, config)
            elif window_name == "模式设置":
                return self._apply_mode_setting_config(ui, config)

            return True

        except Exception as e:
            logger.error(f"应用工具窗口 {window_name} 配置时出错: {str(e)}")
            return False

    def _apply_clkin_control_config(self, ui, config: Dict[str, Any]) -> bool:
        """应用时钟输入控制窗口的配置"""
        try:
            # 应用频率值
            frequency_controls = [
                "lineEditClkin0Oscout", "lineEditClkin1Oscout", "lineEditClkin2Oscout",
                "lineEditClkinSelOut", "DACUpdateRate"
            ]

            for control_name in frequency_controls:
                if control_name in config and hasattr(ui, control_name):
                    control = getattr(ui, control_name)
                    if hasattr(control, 'setText'):
                        control.setText(str(config[control_name]))

            # 应用分频值
            divider_controls = ["PLL1R0Div", "PLL1R1Div", "PLL1R2Div"]
            for control_name in divider_controls:
                if control_name in config and hasattr(ui, control_name):
                    control = getattr(ui, control_name)
                    if hasattr(control, 'setValue'):
                        control.setValue(int(config[control_name]))

            # 应用ComboBox选择
            combo_controls = ["CLKinSelManual"]
            for control_name in combo_controls:
                if control_name in config and hasattr(ui, control_name):
                    control = getattr(ui, control_name)
                    if hasattr(control, 'setCurrentIndex'):
                        control.setCurrentIndex(int(config[control_name]))

            logger.debug("成功应用时钟输入控制配置")
            return True

        except Exception as e:
            logger.error(f"应用时钟输入控制配置时出错: {str(e)}")
            return False

    def _apply_pll_control_config(self, ui, config: Dict[str, Any]) -> bool:
        """应用PLL控制窗口的配置"""
        try:
            # 应用频率值
            frequency_controls = [
                "OSCinFreq", "ExternalVCXOFreq", "FreFin", "PLL1PFDFreq", "PLL2PFDFreq",
                "VCODistFreq", "InternalVCOFreq", "Fin0Freq"
            ]

            for control_name in frequency_controls:
                if control_name in config and hasattr(ui, control_name):
                    control = getattr(ui, control_name)
                    if hasattr(control, 'setText'):
                        control.setText(str(config[control_name]))

            # 应用SpinBox值
            spinbox_controls = [
                "PLL1NDivider", "PLL1RDivider", "PLL2NDivider", "PLL2RDivider", "PLL2Prescaler"
            ]

            for control_name in spinbox_controls:
                if control_name in config and hasattr(ui, control_name):
                    control = getattr(ui, control_name)
                    if hasattr(control, 'setValue'):
                        control.setValue(int(config[control_name]))

            logger.debug("成功应用PLL控制配置")
            return True

        except Exception as e:
            logger.error(f"应用PLL控制配置时出错: {str(e)}")
            return False

    def _apply_clk_outputs_config(self, ui, config: Dict[str, Any]) -> bool:
        """应用时钟输出窗口的配置"""
        try:
            # 应用分频值
            divider_controls = [
                "DCLK0_1DIV", "DCLK2_3DIV", "DCLK4_5DIV", "DCLK6_7DIV",
                "DCLK8_9DIV", "DCLK10_11DIV", "DCLK12_13DIV"
            ]

            for control_name in divider_controls:
                if control_name in config and hasattr(ui, control_name):
                    control = getattr(ui, control_name)
                    if hasattr(control, 'setValue'):
                        control.setValue(int(config[control_name]))

            # 应用频率显示值
            frequency_displays = [
                "lineEditFvco", "lineEditDCLK0", "lineEditDCLK1", "lineEditDCLK2",
                "lineEditDCLK3", "lineEditDCLK4", "lineEditDCLK5", "lineEditDCLK6",
                "lineEditDCLK7", "lineEditDCLK8", "lineEditDCLK9", "lineEditDCLK10",
                "lineEditDCLK11", "lineEditDCLK12", "lineEditDCLK13"
            ]

            for control_name in frequency_displays:
                if control_name in config and hasattr(ui, control_name):
                    control = getattr(ui, control_name)
                    if hasattr(control, 'setText'):
                        control.setText(str(config[control_name]))

            logger.debug("成功应用时钟输出配置")
            return True

        except Exception as e:
            logger.error(f"应用时钟输出配置时出错: {str(e)}")
            return False

    def _apply_sync_system_config(self, ui, config: Dict[str, Any]) -> bool:
        """应用同步系统参考窗口的配置"""
        try:
            # 根据同步系统参考窗口的具体控件来应用配置
            # 这里需要根据实际的UI控件来调整
            logger.debug("应用同步系统参考配置")
            return True

        except Exception as e:
            logger.error(f"应用同步系统参考配置时出错: {str(e)}")
            return False

    def _apply_mode_setting_config(self, ui, config: Dict[str, Any]) -> bool:
        """应用模式设置窗口的配置"""
        try:
            # 根据模式设置窗口的具体控件来应用配置
            # 这里需要根据实际的UI控件来调整
            logger.debug("应用模式设置配置")
            return True

        except Exception as e:
            logger.error(f"应用模式设置配置时出错: {str(e)}")
            return False

    def has_default_configuration(self) -> bool:
        """检查是否存在默认配置"""
        return os.path.exists(self.default_config_file)

    def has_last_configuration(self) -> bool:
        """检查是否存在最近配置"""
        return os.path.exists(self.last_config_file)
