#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单测试寄存器表格写入功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.Log import get_module_logger

logger = get_module_logger(__name__)

def test_table_write_logic():
    """测试表格写入逻辑"""
    logger.info("=== 测试寄存器表格写入逻辑 ===")
    
    try:
        # 导入必要的模块
        from ui.handlers.ModernRegisterTableHandler import ModernRegisterTableHandler
        
        # 创建模拟主窗口
        class MockMainWindow:
            def __init__(self):
                self.register_repo = MockRegisterRepo()
                self.register_service = MockRegisterService()
        
        class MockRegisterRepo:
            def write_register(self, addr, value):
                logger.info(f"🔥 MockRegisterRepo: 写入寄存器 {addr} = 0x{value:04X}")
                return True
        
        class MockRegisterService:
            def write_register(self, addr, value):
                logger.info(f"🚀 MockRegisterService: 写入寄存器 {addr} = 0x{value:04X}")
                return True
        
        # 创建表格处理器实例
        mock_main_window = MockMainWindow()
        table_handler = ModernRegisterTableHandler.create_for_testing(mock_main_window)
        table_handler.main_window = mock_main_window
        
        logger.info("表格处理器创建成功")
        
        # 测试写入方法
        logger.info("测试写入方法...")
        table_handler._write_register_to_chip(0x14, 0x1234)
        
        logger.info("✅ 写入测试完成")
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_original_vs_modern():
    """对比原来的实现和现代化版本"""
    logger.info("=== 对比原来的实现和现代化版本 ===")
    
    # 原来的实现逻辑（RegisterTableHandler.py 第268行）
    logger.info("原来的实现:")
    logger.info("  if hasattr(self.parent, 'register_repo'):")
    logger.info("      self.parent.register_repo.write_register(self.current_register_addr, new_register_value)")
    
    # 现代化版本的实现
    logger.info("现代化版本:")
    logger.info("  # 表格直接修改后必须写入芯片（参考原RegisterTableHandler实现）")
    logger.info("  self._write_register_to_chip(self.current_register_addr, new_register_value)")
    
    logger.info("关键差异:")
    logger.info("  ✅ 原来版本: 直接写入，不检查自动写入设置")
    logger.info("  ✅ 现代化版本: 也是直接写入，不依赖自动写入设置")
    logger.info("  ✅ 修改成功: 表格修改后立即写入芯片")

def main():
    """主函数"""
    logger.info("开始测试寄存器表格写入功能")
    
    test_original_vs_modern()
    test_table_write_logic()
    
    logger.info("测试完成")

if __name__ == "__main__":
    main()
