# FSJ04832 打包管理系统

## 📁 目录结构

```
packaging/                          # 打包管理根目录
├── README.md                       # 本文件 - 打包系统说明
├── scripts/                        # 构建脚本
│   ├── build_exe.py               # 主构建脚本
│   ├── build_safe.py              # 编码安全构建脚本
│   └── build.spec                 # PyInstaller配置文件
├── tools/                          # 版本管理工具
│   ├── version_manager.py         # 版本管理入口
│   ├── start_gui.py               # GUI启动器
│   ├── list_versions.py           # 版本历史查看
│   └── clean_old_versions.py      # 版本清理工具
├── config/                         # 配置文件
│   ├── version.json               # 版本配置
│   └── packaging_config.json      # 打包配置
├── launchers/                      # 启动器
│   ├── 启动工具.bat               # 主启动器
│   ├── 启动版本管理工具.bat       # 版本管理启动器
│   └── 启动说明.txt               # 使用说明
├── tests/                          # 测试脚本
│   ├── test_version_display.py    # 版本显示测试
│   ├── test_versioned_build.py    # 版本化构建测试
│   └── test_packaging.py          # 打包功能测试
├── docs/                           # 文档
│   ├── build_instructions.md      # 构建说明
│   ├── version_management.md      # 版本管理说明
│   ├── encoding_fix.md            # 编码问题解决
│   └── troubleshooting.md         # 故障排除
└── releases/                       # 发布版本 (符号链接到根目录)
```

## 🚀 快速开始

### 1. 图形界面构建（推荐）
```bash
cd packaging
python tools/start_gui.py
```

### 2. 命令行构建
```bash
cd packaging
python scripts/build_exe.py --version-type build
```

### 3. 查看版本历史
```bash
cd packaging
python tools/list_versions.py
```

## 📋 主要功能

### 🔧 构建脚本
- **build_exe.py**: 主构建脚本，支持版本自动增加
- **build_safe.py**: 编码安全的构建脚本，解决Windows编码问题
- **build.spec**: PyInstaller配置文件，自动更新版本信息

### 🎨 版本管理工具
- **start_gui.py**: 图形界面版本管理工具
- **version_manager.py**: 命令行版本管理入口
- **list_versions.py**: 版本历史查看工具
- **clean_old_versions.py**: 版本清理工具

### 🧪 测试工具
- **test_version_display.py**: 测试版本信息显示
- **test_versioned_build.py**: 测试版本化构建功能
- **test_packaging.py**: 测试打包系统完整性

## 📊 版本管理

### 版本号格式
```
主版本.次版本.补丁版本.构建号
   1   .  0   .   2   .  0
```

### 版本增加类型
- **build**: 构建号增加 (******* → *******)
- **patch**: 补丁版本增加 (******* → 1.0.3.0)
- **minor**: 次版本增加 (1.0.3.0 → 1.1.0.0)
- **major**: 主版本增加 (1.1.0.0 → 2.0.0.0)

### 输出文件
- **可执行文件**: `FSJConfigTool*******.exe`
- **版本目录**: `releases/20250604_165228_v*******/`
- **版本信息**: `version_info.json`

## 🛠️ 使用说明

### 构建新版本
1. 选择版本增加类型
2. 运行构建脚本
3. 检查输出目录
4. 验证版本信息

### 版本管理
1. 查看版本历史
2. 清理旧版本
3. 备份重要版本
4. 管理存储空间

### 故障排除
1. 检查依赖安装
2. 验证配置文件
3. 查看构建日志
4. 测试版本显示

## 📞 技术支持

如有问题，请参考：
1. `docs/troubleshooting.md` - 故障排除指南
2. `docs/build_instructions.md` - 详细构建说明
3. `tests/` 目录下的测试脚本

---

**🎯 目标**: 提供统一、便捷、可靠的打包管理解决方案
