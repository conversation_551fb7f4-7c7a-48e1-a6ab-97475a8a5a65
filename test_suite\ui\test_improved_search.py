#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试改进后的搜索功能
验证移除字符长度限制后的搜索体验
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
from PyQt5.QtCore import QTimer, Qt
from ui.handlers.ModernRegisterIOHandler import ModernRegisterIOHandler
from utils.Log import get_module_logger

logger = get_module_logger(__name__)

class ImprovedSearchTestWindow(QMainWindow):
    """改进搜索功能测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("改进搜索功能测试 - 无字符长度限制")
        self.setGeometry(100, 100, 1000, 700)
        
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 添加说明标签
        info_label = QLabel("""
        改进搜索功能测试：
        
        ✅ 已移除字符长度限制
        ✅ 任意长度输入都会立即搜索
        ✅ 支持单字符搜索（如 's'）
        ✅ 支持多字符搜索（如 'sclk'）
        ✅ 支持长字符串搜索（如 'sclk0_1_hs'）
        
        测试步骤：
        1. 输入单个字符 's' - 应该立即显示搜索结果
        2. 继续输入 'cl' - 应该实时更新搜索结果
        3. 继续输入 'k' - 应该进一步筛选结果
        4. 测试删除字符 - 应该实时更新结果
        """)
        layout.addWidget(info_label)
        
        # 创建现代化IO处理器
        try:
            self.io_handler = ModernRegisterIOHandler.create_for_testing(self)
            layout.addWidget(self.io_handler)
            
            # 连接信号
            self.io_handler.search_requested.connect(self.on_search_requested)
            self.io_handler.bit_field_selected.connect(self.on_bit_field_selected)
            
            logger.info("现代化IO处理器创建成功")
            
            # 添加测试按钮
            test_layout = QVBoxLayout()
            
            single_char_btn = QPushButton("测试单字符搜索 ('s')")
            single_char_btn.clicked.connect(self.test_single_char)
            test_layout.addWidget(single_char_btn)
            
            multi_char_btn = QPushButton("测试多字符搜索 ('sclk')")
            multi_char_btn.clicked.connect(self.test_multi_char)
            test_layout.addWidget(multi_char_btn)
            
            long_search_btn = QPushButton("测试长字符串搜索 ('sclk0_1_hs')")
            long_search_btn.clicked.connect(self.test_long_search)
            test_layout.addWidget(long_search_btn)
            
            clear_btn = QPushButton("清空搜索")
            clear_btn.clicked.connect(self.clear_search)
            test_layout.addWidget(clear_btn)
            
            layout.addLayout(test_layout)
            
        except Exception as e:
            logger.error(f"创建现代化IO处理器失败: {str(e)}")
            error_label = QLabel(f"创建失败: {str(e)}")
            layout.addWidget(error_label)
    
    def test_single_char(self):
        """测试单字符搜索"""
        logger.info("=== 测试单字符搜索 ===")
        if hasattr(self.io_handler, 'search_edit'):
            self.io_handler.search_edit.setText("s")
            logger.info("输入单字符 's'，应该立即显示搜索结果")
    
    def test_multi_char(self):
        """测试多字符搜索"""
        logger.info("=== 测试多字符搜索 ===")
        if hasattr(self.io_handler, 'search_edit'):
            self.io_handler.search_edit.setText("sclk")
            logger.info("输入多字符 'sclk'，应该显示更精确的搜索结果")
    
    def test_long_search(self):
        """测试长字符串搜索"""
        logger.info("=== 测试长字符串搜索 ===")
        if hasattr(self.io_handler, 'search_edit'):
            self.io_handler.search_edit.setText("sclk0_1_hs")
            logger.info("输入长字符串 'sclk0_1_hs'，应该显示最精确的搜索结果")
    
    def clear_search(self):
        """清空搜索"""
        logger.info("=== 清空搜索 ===")
        if hasattr(self.io_handler, 'search_edit'):
            self.io_handler.search_edit.setText("")
            logger.info("清空搜索框，搜索结果列表应该隐藏")
    
    def on_search_requested(self, search_text):
        """处理搜索请求"""
        logger.info(f"搜索请求: '{search_text}' (长度: {len(search_text)})")
        # 显示搜索结果统计
        if hasattr(self.io_handler, 'register_manager') and self.io_handler.register_manager:
            results = self.io_handler.register_manager.search_bit_fields(search_text)
            logger.info(f"  找到 {len(results)} 个结果")
            if len(results) <= 10:  # 只显示前10个结果
                for i, (bit_name, reg_addr, bit_range) in enumerate(results):
                    logger.info(f"    {i+1}. {bit_name} (寄存器: {reg_addr})")
            else:
                # 显示前5个和后5个
                for i in range(5):
                    bit_name, reg_addr, bit_range = results[i]
                    logger.info(f"    {i+1}. {bit_name} (寄存器: {reg_addr})")
                logger.info(f"    ... (省略 {len(results)-10} 个结果)")
                for i in range(len(results)-5, len(results)):
                    bit_name, reg_addr, bit_range = results[i]
                    logger.info(f"    {i+1}. {bit_name} (寄存器: {reg_addr})")
    
    def on_bit_field_selected(self, reg_addr):
        """处理位字段选择"""
        logger.info(f"位字段选择: 寄存器 {reg_addr}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = ImprovedSearchTestWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
