#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
现代化的时钟输入控制处理器
使用ModernBaseHandler作为基类，展示新的架构模式
这是重构的第二个示例，包含更多的寄存器控件
"""

from ui.handlers.ModernBaseHandler import ModernBaseHandler
from ui.forms.Ui_clkinControl import Ui_ClkinControl
from utils.Log import get_module_logger
from PyQt5 import QtCore
import traceback

logger = get_module_logger(__name__)


# 常量定义
CLOCK_SOURCES = ["CLKin0", "CLKin1", "CLKin2", "Holdover"]
DEFAULT_HOLDOVER_DIVIDER = 1
MAX_DIVIDER_VALUE = 16383  # 修复：PLL1 R Dividers的正确范围是0-16383


class ModernClkinControlHandler(ModernBaseHandler):
    """现代化的时钟输入控制处理器"""
    
    def __init__(self, parent=None, register_manager=None, **kwargs):
        """初始化现代化时钟输入控制处理器

        Args:
            parent: 父窗口
            register_manager: RegisterManager实例
            **kwargs: 其他参数（如register_repo等，用于兼容性）
        """
        super().__init__(parent, register_manager, **kwargs)

        # 设置窗口标题
        self.setWindowTitle("时钟输入控制 (现代化版本)")

        # 分频比默认值（与寄存器配置文件中的默认值一致）
        self.clkin0_divider = 120  # 对应寄存器0x63的默认值
        self.clkin1_divider = 120  # 对应寄存器0x65的默认值
        self.clkin2_divider = 150  # 对应寄存器0x67的默认值

        # 频率默认值
        self.clkin0 = 122.88
        self.clkin1 = 122.88
        self.clkin2 = 150.0

        # 缓存PLL1PFDFreq值
        self._cached_pll1_pfd_freq = None

        # 添加一个实例变量来存储上一次的值
        self.previous_clkin_value = ""

        # 创建UI实例
        self.ui = Ui_ClkinControl()
        self.ui.setupUi(self.content_widget)

        # 设置SpinBox控件的范围（在UI创建后立即设置）
        self._setup_spinbox_ranges()

        # 初始化ComboBox选项映射
        self._init_combobox_options()

        # 初始化时计算一次DAC Update Rate
        QtCore.QTimer.singleShot(100, self.calculate_dac_update_rate)

        # 尝试从默认配置初始化PLL1PFDFreq值
        self._initialize_pll1_pfd_freq_from_defaults()

        # 监听PLL窗口的更新事件
        self._setup_pll_update_listener()
        
        # 监听PLL窗口的打开/关闭事件
        self._setup_pll_window_listener()

        # 窗口名称，用于缓存标识
        self._window_name = "ClkinControl"

        logger.info("现代化时钟输入控制处理器初始化完成")

    def showEvent(self, event):
        """窗口显示事件处理"""
        super().showEvent(event)

        # 窗口显示时重新计算DAC Update Rate，确保能获取到最新的PLL1PFDFreq值
        QtCore.QTimer.singleShot(200, self.calculate_dac_update_rate)
        QtCore.QTimer.singleShot(500, self.calculate_dac_update_rate)  # 再次计算确保准确
        QtCore.QTimer.singleShot(1000, self.calculate_dac_update_rate)  # 第三次计算确保准确
        logger.debug("窗口显示，将重新计算DAC Update Rate")

    def _initialize_pll1_pfd_freq_from_defaults(self):
        """从默认配置初始化PLL1PFDFreq值"""
        try:
            # 计算默认的PLL1PFDFreq值
            # 假设默认OSCin频率为122.88MHz，默认PLL1 R Divider为120
            default_oscin_freq = 122.88  # MHz
            default_r_divider = 120

            if default_r_divider > 0:
                default_pll1_pfd_freq = default_oscin_freq / default_r_divider

                # 缓存这个默认值
                self._cached_pll1_pfd_freq = default_pll1_pfd_freq

                # 同时缓存到RegisterUpdateBus
                from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
                bus = RegisterUpdateBus.instance()
                if hasattr(bus, 'cache_pll1_pfd_freq'):
                    bus.cache_pll1_pfd_freq(default_pll1_pfd_freq)

                logger.debug(f"初始化默认PLL1PFDFreq值: {default_pll1_pfd_freq:.5f} MHz (OSCin: {default_oscin_freq}, R: {default_r_divider})")

        except Exception as e:
            logger.error(f"初始化默认PLL1PFDFreq值时发生错误: {str(e)}")

    def _setup_register_defaults(self):
        """设置寄存器默认值（重写基类方法）"""
        logger.info("设置时钟输入控制的寄存器默认值...")

        try:
            if not self.register_manager:
                logger.warning("RegisterManager未设置，跳过寄存器默认值设置")
                return

            # 定义关键寄存器的默认值
            register_defaults = {
                # PLL R Dividers
                "0x63": {"CLKin0_R[13:0]": 120},  # CLKin0分频比
                "0x65": {"CLKin1_R[13:0]": 120},  # CLKin1分频比
                "0x67": {"CLKin2_R[13:0]": 150},  # CLKin2分频比

                # Clock Input Selection
                "0x5A": {
                    "CLKin_SEL_MANUAL[1:0]": 1,  # 手动选择CLKin1
                    "CLKin0_DEMUX[1:0]": 2,      # CLKin0路由到PLL1
                    "CLKin1_DEMUX[1:0]": 2,      # CLKin1路由到PLL1
                },
            }

            # 设置每个寄存器的默认值
            for reg_addr, bit_fields in register_defaults.items():
                for bit_name, default_value in bit_fields.items():
                    try:
                        # 尝试从register_manager获取当前值
                        current_value = self.register_manager.get_bit_field_value(reg_addr, bit_name)
                        
                        # 如果当前值有效（例如，不是None），则使用它，并且不覆盖
                        if current_value is not None:
                            logger.info(f"寄存器 {reg_addr}[{bit_name}] 已有值: {current_value}，将使用此值")
                            # 如果需要，可以在这里更新UI或内部状态以反映此现有值
                            # 例如，如果这个值直接对应某个UI控件，确保控件显示的是 current_value
                        else:
                            # 如果register_manager中没有值，则设置代码中定义的默认值
                            success = self.register_manager.set_bit_field_value(reg_addr, bit_name, default_value)
                            if success:
                                logger.info(f"设置寄存器默认值: {reg_addr}[{bit_name}] = {default_value}")
                            else:
                                logger.warning(f"设置寄存器默认值失败: {reg_addr}[{bit_name}] = {default_value}")
                    except Exception as e:
                        logger.warning(f"处理寄存器默认值时出错 {reg_addr}[{bit_name}]: {str(e)}")

            # 同步实例变量
            self.clkin0_divider = self.register_manager.get_bit_field_value("0x63", "CLKin0_R[13:0]") or 120
            self.clkin1_divider = self.register_manager.get_bit_field_value("0x65", "CLKin1_R[13:0]") or 120
            self.clkin2_divider = self.register_manager.get_bit_field_value("0x67", "CLKin2_R[13:0]") or 150

            logger.info(f"寄存器默认值设置完成: CLKin0={self.clkin0_divider}, CLKin1={self.clkin1_divider}, CLKin2={self.clkin2_divider}")

        except Exception as e:
            logger.error(f"设置寄存器默认值时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _post_initialization_setup(self):
        """后初始化设置（重写基类方法）"""
        logger.info("执行时钟输入控制的后初始化设置...")

        try:
            # 1. 设置ComboBox选项
            self._setup_combobox_options()

            # 2. 注册手动初始化的控件
            self._register_manually_initialized_controls()

            # 3. 从缓存恢复lineEdit值
            self._restore_lineedit_values_from_cache()

            # 4. 初始化时钟频率（使用辅助方法简化信号阻塞）
            self._block_signals_and_execute(['CLKin0Freq', 'CLKin1Freq', 'CLKin2Freq'],
                                          self.init_clkin_frequency)

            # 5. 更新时钟选择输出
            self._block_signals_and_execute(['CLKinSelOut'], self.update_clkin_sel_out)

            # 6. 连接自定义信号
            self.connect_signals()

            # 7. 发送初始时钟源信息
            self._send_initial_clock_source_info()

            logger.info("时钟输入控制后初始化设置完成")

        except Exception as e:
            logger.error(f"后初始化设置时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _init_combobox_options(self):
        """初始化ComboBox选项映射"""
        self.custom_combobox_options = {
            "CLKin0Demux": {
                0: "Fin",
                1: "Feedback Mux",
                2: "PLL1",
                3: "Off"
            },
            "CLKin1Demux": {
                0: "Fin",
                1: "Feedback Mux",
                2: "PLL1",
                3: "Off"
            },
            "OSCoutMux": {
                0: "Buffered OSCin",
                1: "Feedback Mux",
            },
            "CLKinSelManual": {
                0: "CLKin0",
                1: "CLKin1",
                2: "CLKin2",
                3: "Holdover"
            },
            "CLKinSel0Type": {
                0: "Input",
                1: "Input w/ Pull Up",
                2: "Input w/ Pull Down",
                3: "Output(push-up)",
                4: "forbidden",
                5: "forbidden",
                6: "forbidden",
            },
            "CLKinSel1Type": {
                0: "Input",
                1: "Input w/ Pull Up",
                2: "Input w/ Pull Down",
                3: "Output(push-up)",
                4: "forbidden",
                5: "forbidden",
                6: "forbidden",
            },
            "ResetMux": {
                0: "Logic Low",
                1: "Reserved",
                2: "CLKin2 Selected",
                3: "DAC Locked",
                4: "DAC Low",
                5: "DAC High",
                6: "SPI Readback",
            },
            "ResetType": {
                0: "Input",
                1: "Input with pullup resistor",
                2: "Input with pulldown resistor",
                3: "Output (push-pull)",
                4: "forbidden",
                5: "forbidden",
                6: "forbidden",
            },
            "DACClkMult": {
                0: "4",
                1: "64",
                2: "1024",
                3: "16384"
            },
            "DACClkCntr": {i: str(i) for i in range(256)},
            "RGHOExitDacassistStep": {
                0: "Slowest",
                1: "Medium",
                2: "Fast (Sim)",
                3: "Fastest (Sim)"
            },
            "PLL1_LD_MUX": {
                0: "PLL1 Lock",
                1: "PLL2 Lock",
                2: "PLL1 DLD",
                3: "PLL2 DLD",
                4: "Holdover",
                5: "LOS",
                6: "CLKin0 LOS",
                7: "CLKin1 LOS",
                8: "CLKin2 LOS",
                9: "Feedback Mux LOS",
                10: "VCO LDO",
                11: "VCO Cal",
                12: "Sync Pulses",
                13: "Reserved",
                14: "Reserved"
            },
            "PLL1_LD_TYPE": {
                0: "Input",
                1: "Input w/ Pull Up",
                2: "Input w/ Pull Down",
                3: "Output (Push-Pull)",
                4: "Output (Open Drain)",
                5: "Output (LVDS)",
                6: "Output (LVPECL)"
            },
            "PLL2_LD_MUX": {
                0: "PLL1 Lock",
                1: "PLL2 Lock",
                2: "PLL1 DLD",
                3: "PLL2 DLD",
                4: "Holdover",
                5: "LOS",
                6: "CLKin0 LOS",
                7: "CLKin1 LOS",
                8: "CLKin2 LOS",
                9: "Feedback Mux LOS",
                10: "VCO LDO",
                11: "VCO Cal",
                12: "Sync Pulses",
                13: "Reserved",
                14: "Reserved",
                15: "Reserved"
            },
            "PLL2_LD_TYPE": {
                0: "Input",
                1: "Input w/ Pull Up",
                2: "Input w/ Pull Down",
                3: "Output (Push-Pull)",
                4: "Output (Open Drain)",
                5: "Output (LVDS)",
                6: "Output (LVPECL)"
            },
            "CLKinSel0Mux": {
                0: "Logic Low",
                1: "CLKin0 LOS",
                2: "CLKin0 Selected",
                3: "DAC Locked",
                4: "DAC Low",
                5: "DAC High",
                6: "SPI Readback",
                7: "Reserved",
            },
            "CLKinSel1Mux": {
                0: "Logic Low",
                1: "CLKin0 LOS",
                2: "CLKin0 Selected",
                3: "DAC Locked",
                4: "DAC Low",
                5: "DAC High",
                6: "SPI Readback",
                7: "Reserved",
            },
            "OSCoutClockFormat": {
                0: "Power down (CLKin2)",
                1: "LVDS",
                2: "Reserved",
                3: "Reserved",
                4: "LVPECL 1600 mV",
                5: "LVPECL 2000 mV",
                6: "LVCMOS (Norm / Inv)",
                7: "LVCMOS (Inv / Norm)",
                8: "LVCMOS (Norm / Norm)",
                9: "LVCMOS (Inv / Inv)",
                10: "LVCMOS (Off / Norm)",
                11: "LVCMOS (Off / Inv)",
                12: "LVCMOS (Norm / Off)",
                13: "LVCMOS (Inv / Off)",
                14: "LVCMOS (Off / Off)",
            },
            "syncSource": {
                0: "Reserved",
                1: "SYNC Pin",
                2: "CLKin0",
                3: "Reserved",
            },
            "LOSTimeout": {
                0: "0",
                1: "1",
                2: "2",
                3: "3",
                4: "4",
                5: "5",
                6: "6",
                7: "7",
            }
        }

        # 初始化combobox_options_map（如果不存在）
        if not hasattr(self, 'combobox_options_map'):
            self.combobox_options_map = {}

        # 将选项映射添加到combobox_options_map中
        for option_name, options in self.custom_combobox_options.items():
            # 根据控件名称获取映射类型
            map_type = self._get_combobox_type(option_name)
            # 更新映射
            self.combobox_options_map[map_type] = options

    def _block_signals_and_execute(self, widget_names, func, *args, **kwargs):
        """阻塞指定控件的信号，执行函数，然后恢复信号

        Args:
            widget_names: 控件名称列表
            func: 要执行的函数
            *args, **kwargs: 传递给函数的参数
        """
        blocked_widgets = []

        try:
            # 阻塞信号
            for widget_name in widget_names:
                if hasattr(self.ui, widget_name):
                    widget = getattr(self.ui, widget_name)
                    if hasattr(widget, 'blockSignals'):
                        widget.blockSignals(True)
                        blocked_widgets.append(widget)

            # 执行函数
            return func(*args, **kwargs)

        finally:
            # 恢复信号
            for widget in blocked_widgets:
                widget.blockSignals(False)



    def _setup_spinbox_ranges(self):
        """设置SpinBox控件的范围（在基类初始化之前调用，防止值被限制）"""
        try:
            logger.info("开始设置SpinBox控件范围...")

            # PLL R Dividers控件列表
            divider_controls = ["PLL1R0Div", "PLL1R1Div", "PLL1R2Div"]

            for control_name in divider_controls:
                if hasattr(self.ui, control_name):
                    control = getattr(self.ui, control_name)

                    # 设置正确的范围：0-16383（根据寄存器配置文件中的options）
                    control.setMinimum(0)
                    control.setMaximum(MAX_DIVIDER_VALUE)

                    logger.info(f"已设置 {control_name} 范围: 0-{MAX_DIVIDER_VALUE}")
                else:
                    logger.warning(f"未找到SpinBox控件: {control_name}")
            if hasattr(self.ui, "MANDAC"):
                self.ui.MANDAC.setMinimum(0)
                self.ui.MANDAC.setMaximum(1023)
                logger.info("已设置MANDAC 控件范围: 0-1023")
            else:
                logger.warning("未找到MANDAC 控件")
                
            logger.info("SpinBox控件范围设置完成")

        except Exception as e:
            logger.error(f"设置SpinBox控件范围时发生错误: {str(e)}")
            traceback.print_exc()

    def _setup_combobox_options(self):
        """设置ComboBox选项"""
        try:
            from PyQt5.QtWidgets import QComboBox

            logger.info(f"开始设置ComboBox选项，共有 {len(self.custom_combobox_options)} 个控件")

            # 为所有ComboBox控件设置选项
            for widget_name, options in self.custom_combobox_options.items():
                logger.debug(f"处理控件: {widget_name}")
                if hasattr(self.ui, widget_name):
                    combo_box = getattr(self.ui, widget_name)
                    if isinstance(combo_box, QComboBox):
                        combo_box.blockSignals(True) # 阻止信号
                        # 清空并设置选项
                        combo_box.clear()

                        # 添加选项
                        for value, text in sorted(options.items()):
                            combo_box.addItem(text, value)  # 存储实际值为userData
                        # 此处可以根据 register_manager 的值来设置 currentIndex，如果需要的话
                        # 示例：
                        # initial_value_from_reg = self.register_manager.get_bit_field_value(reg_addr, bit_name)
                        # if initial_value_from_reg is not None:
                        #     index = combo_box.findData(initial_value_from_reg)
                        #     if index != -1:
                        #         combo_box.setCurrentIndex(index)
                        #     else:
                        #         logger.warning(f"无法在 {widget_name} 中找到与寄存器值 {initial_value_from_reg} 对应的选项")
                        # else:
                        #     # 如果寄存器中没有值，可以设置一个默认的currentIndex，或者不设置
                        #     pass 
                        combo_box.blockSignals(False) # 恢复信号

                        logger.info(f"已为 {widget_name} 设置 {len(options)} 个选项")
                    else:
                        logger.warning(f"控件 {widget_name} 不是ComboBox类型: {type(combo_box)}")
                else:
                    logger.warning(f"未找到控件 {widget_name}")

            logger.info("ComboBox选项设置完成")

            # 应用之前延迟设置的ComboBox值
            if hasattr(self, 'apply_pending_combobox_values'):
                self.apply_pending_combobox_values()

        except Exception as e:
            logger.error(f"设置ComboBox选项时发生错误: {str(e)}")
            traceback.print_exc()

    def _get_combobox_type(self, widget_name):
        """根据控件名称获取ComboBox类型"""
        # 直接映射表
        widget_type_map = {
            "CLKin0Demux": "CLKDEMUX",
            "CLKin1Demux": "CLKDEMUX",
            "OSCoutMux": "OSCoutMux",
            "CLKinSelManual": "CLKinSelManual",
            "CLKinSel0Type": "CLKinSelType",
            "CLKinSel1Type": "CLKinSelType",
            "CLKinSel0Mux": "CLKinSelMux",
            "CLKinSel1Mux": "CLKinSelMux",
            "OSCoutClockFormat": "OSCoutClockFormat",
            "syncSource": "syncSource",
            "LOSTimeout": "LOSTimeout",
        }

        # 如果widget_name在映射表中，直接返回映射的类型
        # 否则返回widget_name本身作为类型
        return widget_type_map.get(widget_name, widget_name)

    def on_register_value_changed(self, widget_name, reg_addr, reg_value):
        """处理寄存器值变化"""
        logger.info(f"时钟输入控制: 寄存器 {reg_addr} 值变化 (控件: {widget_name}) -> 0x{reg_value:04X}")
        
        # 处理特定控件的业务逻辑
        if widget_name == "losEn":
            self._handle_los_enable_change(reg_value)
        elif widget_name == "powerDown":
            self._handle_power_down_change(reg_value)
        elif widget_name in ["clkin0SEL", "clkin1SEL", "clkin2SEL"]:
            self._handle_clkin_selection_change(widget_name, reg_value)
        elif widget_name in ["clkin0En", "clkin1En", "clkin2En"]:
            self._handle_clkin_enable_change(widget_name, reg_value)
        elif widget_name == "clkinSelAutoEn":
            self._handle_auto_selection_change(reg_value)
    
    def on_global_register_update(self, reg_addr, reg_value):
        """处理全局寄存器更新"""
        logger.debug(f"时钟输入控制: 收到全局更新 {reg_addr} = 0x{reg_value:04X}")
        
        # 处理特定寄存器的全局更新逻辑
        if reg_addr == "0x5B":  # LOS相关寄存器
            self._handle_los_register_update(reg_value)
        elif reg_addr == "0x02":  # 电源管理寄存器
            self._handle_power_register_update(reg_value)
    
    def _handle_los_enable_change(self, _):
        """处理LOS使能变化

        Args:
            _: 寄存器值（未直接使用，通过RegisterManager获取具体位值）
        """
        try:
            # 获取LOS_EN位的值
            los_en_bit = self.register_manager.get_bit_field_value("0x5B", "LOS_EN")

            if los_en_bit == 1:
                logger.info("LOS检测已启用")
                # 可以在这里添加LOS启用的特殊处理
                # 例如：启用相关的超时控件，显示状态指示等
                self._update_los_related_controls(True)
            else:
                logger.info("LOS检测已禁用")
                self._update_los_related_controls(False)

        except Exception as e:
            logger.error(f"处理LOS使能变化时出错: {str(e)}")

    def _handle_power_down_change(self, _):
        """处理电源管理变化

        Args:
            _: 寄存器值（未直接使用，通过RegisterManager获取具体位值）
        """
        try:
            # 获取POWERDOWN位的值
            powerdown_bit = self.register_manager.get_bit_field_value("0x02", "POWERDOWN")

            if powerdown_bit == 1:
                logger.info("设备进入省电模式")
                # 省电模式的特殊处理
                self._show_power_down_warning(True)
                self._disable_non_essential_controls(True)
            else:
                logger.info("设备退出省电模式")
                self._show_power_down_warning(False)
                self._disable_non_essential_controls(False)

        except Exception as e:
            logger.error(f"处理电源管理变化时出错: {str(e)}")

    def _handle_clkin_selection_change(self, widget_name, reg_value):
        """处理时钟输入选择变化"""
        try:
            logger.info(f"时钟输入选择变化: {widget_name} (寄存器值: 0x{reg_value:04X})")

            # 这里可以添加时钟选择的特殊处理
            # 例如：更新频率显示，检查时钟冲突等

        except Exception as e:
            logger.error(f"处理时钟输入选择变化时出错: {str(e)}")

    def _handle_clkin_enable_change(self, widget_name, reg_value):
        """处理时钟输入使能变化"""
        try:
            logger.info(f"时钟输入使能变化: {widget_name} (寄存器值: 0x{reg_value:04X})")

            # 这里可以添加时钟使能的特殊处理
            # 例如：更新时钟状态显示，检查时钟配置等

        except Exception as e:
            logger.error(f"处理时钟输入使能变化时出错: {str(e)}")

    def _handle_auto_selection_change(self, reg_value):
        """处理自动选择变化"""
        try:
            logger.info(f"自动时钟选择设置变化 (寄存器值: 0x{reg_value:04X})")

            # 这里可以添加自动选择的特殊处理
            # 例如：启用/禁用手动选择控件等

        except Exception as e:
            logger.error(f"处理自动选择变化时出错: {str(e)}")

    def _handle_los_register_update(self, reg_value):
        """处理LOS寄存器更新"""
        try:
            logger.debug(f"LOS寄存器更新: 0x{reg_value:04X}")
            # 这里可以添加LOS寄存器更新的特殊处理
        except Exception as e:
            logger.error(f"处理LOS寄存器更新时出错: {str(e)}")

    def _handle_power_register_update(self, reg_value):
        """处理电源寄存器更新"""
        try:
            logger.debug(f"电源寄存器更新: 0x{reg_value:04X}")
            # 这里可以添加电源寄存器更新的特殊处理
        except Exception as e:
            logger.error(f"处理电源寄存器更新时出错: {str(e)}")

    def _update_los_related_controls(self, enabled):
        """更新LOS相关控件状态"""
        try:
            logger.debug(f"更新LOS相关控件状态: {enabled}")
            # 这里可以添加LOS相关控件的状态更新
            # 例如：启用/禁用LOSTimeout控件等
        except Exception as e:
            logger.error(f"更新LOS相关控件时出错: {str(e)}")

    def _show_power_down_warning(self, show):
        """显示或隐藏省电模式警告"""
        try:
            logger.debug(f"省电模式警告: {show}")
            # 这里可以添加UI提示逻辑
            # 例如：改变某些控件的颜色，显示警告标签等
        except Exception as e:
            logger.error(f"显示省电模式警告时出错: {str(e)}")

    def _disable_non_essential_controls(self, disable):
        """禁用或启用非必要控件"""
        try:
            logger.debug(f"禁用非必要控件: {disable}")
            # 这里可以添加控件状态管理逻辑
            # 例如：在省电模式下禁用某些控件
        except Exception as e:
            logger.error(f"管理控件状态时出错: {str(e)}")
    
    # === 公共接口方法 ===
    
    def get_current_clkin_status(self):
        """获取当前时钟输入状态
        
        Returns:
            dict: 当前时钟输入状态信息
        """
        try:
            status = {}
            
            if self.register_manager:
                # 获取各种时钟输入状态
                status["los_enabled"] = self.register_manager.get_bit_field_value("0x5B", "LOS_EN")
                status["power_down"] = self.register_manager.get_bit_field_value("0x02", "POWERDOWN")
                # 可以添加更多状态的获取
                
            return status
            
        except Exception as e:
            logger.error(f"获取时钟输入状态时出错: {str(e)}")
            return {}
    
    def set_clkin_preset(self, preset_name):
        """设置时钟输入预设

        Args:
            preset_name: 预设名称
        """
        try:
            logger.info(f"应用时钟输入预设: {preset_name}")

            # 定义各种预设
            presets = {
                "single_clkin0": {
                    # 单时钟输入0配置
                },
                "dual_clkin": {
                    # 双时钟输入配置
                },
                "auto_switch": {
                    # 自动切换配置
                }
            }

            if preset_name not in presets:
                logger.warning(f"未知的预设: {preset_name}")
                return

            preset = presets[preset_name]

            # 应用预设
            if preset:  # 检查预设是否为空
                for bit_name, value in preset.items():
                    logger.debug(f"应用预设值: {bit_name} = {value}")
                    # 这里需要通过RegisterManager的API来设置值
            else:
                logger.info(f"预设 {preset_name} 为空，跳过应用")

        except Exception as e:
            logger.error(f"设置时钟输入预设时出错: {str(e)}")



    def init_clkin_frequency(self):
        """设置时钟输入频率"""
        try:
            if hasattr(self.ui, 'lineEditClkin0'):
                self.ui.lineEditClkin0.setText(str(self.clkin0))
            if hasattr(self.ui, 'lineEditClkin1'):
                self.ui.lineEditClkin1.setText(str(self.clkin1))
            if hasattr(self.ui, 'lineEditClkin2Oscout'):
                self.ui.lineEditClkin2Oscout.setText(str(self.clkin2))

            # 动态设置 lineEditClkinSelOut 的初始值
            if hasattr(self.ui, 'CLKinSelManual'):
                default_index = self.ui.CLKinSelManual.currentIndex()
                self.update_clkin_sel_out(default_index)
        except Exception as e:
            logger.error(f"初始化时钟频率时发生错误: {str(e)}")

    def connect_signals(self):
        """连接自定义信号与槽"""
        try:
            # 时钟源选择信号
            self._connect_widget_signal('CLKinSelManual', 'currentIndexChanged', self.update_clkin_sel_out)

            # 时钟频率输入信号
            frequency_mappings = [
                ("CLKin0", "lineEditClkin0"),
                ("CLKin1", "lineEditClkin1"),
                ("CLKin2", "lineEditClkin2Oscout")
            ]

            for source, lineedit_name in frequency_mappings:
                if hasattr(self.ui, lineedit_name):
                    lineedit = getattr(self.ui, lineedit_name)
                    lineedit.returnPressed.connect(self.update_clkin_sel_out)
                    lineedit.editingFinished.connect(lambda s=source: self.update_clkin_value(s))

            # 分频比控件信号
            divider_mappings = [
                ("CLKin0", "PLL1R0Div"),
                ("CLKin1", "PLL1R1Div"),
                ("CLKin2", "PLL1R2Div")
            ]

            for source, spinner_name in divider_mappings:
                if hasattr(self.ui, spinner_name):
                    spinner = getattr(self.ui, spinner_name)
                    spinner.valueChanged.connect(lambda value, s=source: self.update_divider_value(s, value))

            # DAC Update Rate 计算相关信号
            self._connect_widget_signal('DACClkMult', 'currentIndexChanged', self.calculate_dac_update_rate)
            self._connect_widget_signal('DACClkCntr', 'currentIndexChanged', self.calculate_dac_update_rate)

            # 添加更多触发DAC Update Rate计算的信号
            self._connect_widget_signal('PLL1R0Div', 'valueChanged', lambda: QtCore.QTimer.singleShot(100, self.calculate_dac_update_rate))
            self._connect_widget_signal('PLL1R1Div', 'valueChanged', lambda: QtCore.QTimer.singleShot(100, self.calculate_dac_update_rate))
            self._connect_widget_signal('PLL1R2Div', 'valueChanged', lambda: QtCore.QTimer.singleShot(100, self.calculate_dac_update_rate))

        except Exception as e:
            logger.error(f"连接信号时发生错误: {str(e)}")

    def _connect_widget_signal(self, widget_name, signal_name, slot):
        """连接单个控件的信号到槽

        Args:
            widget_name: 控件名称
            signal_name: 信号名称
            slot: 槽函数
        """
        if hasattr(self.ui, widget_name):
            widget = getattr(self.ui, widget_name)
            if hasattr(widget, signal_name):
                signal = getattr(widget, signal_name)
                signal.connect(slot)

    def update_clkin_sel_out(self, index=None):
        """根据 CLKinSelManual 的选择更新 lineEditClkinSelOut 的值并发布更新"""
        try:
            if not hasattr(self.ui, 'CLKinSelManual') or not hasattr(self.ui, 'lineEditClkinSelOut'):
                logger.warning("【现代化时钟输入窗口】缺少必要的UI控件")
                return

            if index is None:
                index = self.ui.CLKinSelManual.currentIndex()

            logger.info(f"【现代化时钟输入窗口】时钟源切换: 索引={index}")

            # 获取时钟源信息
            selected_source, freq_value, divider_value = self._get_clock_info_by_index(index)

            # 获取显示值
            clkin_value = ""
            if index == 0 and hasattr(self.ui, 'lineEditClkin0'):  # CLKin0
                clkin_value = self.ui.lineEditClkin0.text()
            elif index == 1 and hasattr(self.ui, 'lineEditClkin1'):  # CLKin1
                clkin_value = self.ui.lineEditClkin1.text()
            elif index == 2 and hasattr(self.ui, 'lineEditClkin2Oscout'):  # CLKin2
                clkin_value = self.ui.lineEditClkin2Oscout.text()
            else:  # Holdover
                clkin_value = self.previous_clkin_value  # 保持上一次的值

            # 调试日志：检查频率值是否一致
            logger.info(f"【现代化时钟输入窗口】时钟源切换调试: 索引={index}, 源={selected_source}")
            logger.info(f"  显示值(clkin_value): '{clkin_value}'")
            logger.info(f"  信号值(freq_value): {freq_value}")
            logger.info(f"  分频值(divider_value): {divider_value}")

            # 确保信号发送的频率值与显示值一致
            try:
                display_freq = float(clkin_value) if clkin_value else 0.0
                if abs(display_freq - freq_value) > 0.001:  # 如果差异超过0.001
                    logger.warning(f"【现代化时钟输入窗口】频率值不一致! 显示值={display_freq}, 信号值={freq_value}, 使用显示值")
                    freq_value = display_freq
            except (ValueError, TypeError):
                logger.warning(f"【现代化时钟输入窗口】无法解析显示频率值: '{clkin_value}', 使用信号值: {freq_value}")

            # 更新 lineEditClkinSelOut 的值
            # self.ui.lineEditClkinSelOut.setText(clkin_value)
            self.ui.lineEditClkinSelOut.setText(selected_source)

            # 缓存选择输出值
            self._cache_lineedit_value("lineEditClkinSelOut", selected_source)

            # 更新 previous_clkin_value
            self.previous_clkin_value = clkin_value

            # 发布时钟源选择更新通知
            self._emit_clock_source_update(selected_source, freq_value, divider_value)
        except Exception as e:
            logger.error(f"【现代化时钟输入窗口】更新时钟选择输出时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def _get_clock_info_by_index(self, index):
        """根据索引获取时钟信息"""
        try:
            selected_source = CLOCK_SOURCES[index] if index < len(CLOCK_SOURCES) else "Holdover"

            if index == 0:  # CLKin0
                freq_value = float(self.ui.lineEditClkin0.text() or "0") if hasattr(self.ui, 'lineEditClkin0') else 0.0
                # 优先使用实例变量，如果没有则从控件读取
                divider_value = getattr(self, 'clkin0_divider', None)
                if divider_value is None:
                    divider_value = self.ui.PLL1R0Div.value() if hasattr(self.ui, 'PLL1R0Div') else 1
            elif index == 1:  # CLKin1
                freq_value = float(self.ui.lineEditClkin1.text() or "0") if hasattr(self.ui, 'lineEditClkin1') else 0.0
                # 优先使用实例变量，如果没有则从控件读取
                divider_value = getattr(self, 'clkin1_divider', None)
                if divider_value is None:
                    divider_value = self.ui.PLL1R1Div.value() if hasattr(self.ui, 'PLL1R1Div') else 1
            elif index == 2:  # CLKin2
                freq_value = float(self.ui.lineEditClkin2Oscout.text() or "0") if hasattr(self.ui, 'lineEditClkin2Oscout') else 0.0
                # 优先使用实例变量，如果没有则从控件读取
                divider_value = getattr(self, 'clkin2_divider', None)
                if divider_value is None:
                    divider_value = self.ui.PLL1R2Div.value() if hasattr(self.ui, 'PLL1R2Div') else 1
            else:  # Holdover
                freq_value = 0.0
                divider_value = DEFAULT_HOLDOVER_DIVIDER

            return selected_source, freq_value, divider_value
        except Exception as e:
            logger.error(f"获取时钟信息时发生错误: {str(e)}")
            return "Holdover", 0.0, DEFAULT_HOLDOVER_DIVIDER

    def update_clkin_value(self, source_name):
        """处理时钟输入频率编辑完成事件"""
        try:
            # 获取对应的频率控件
            if source_name == "CLKin0" and hasattr(self.ui, 'lineEditClkin0') and hasattr(self.ui, 'PLL1R0Div'):
                freq_control = self.ui.lineEditClkin0
                divider_control = self.ui.PLL1R0Div
                self.clkin0 = float(freq_control.text())
            elif source_name == "CLKin1" and hasattr(self.ui, 'lineEditClkin1') and hasattr(self.ui, 'PLL1R1Div'):
                freq_control = self.ui.lineEditClkin1
                divider_control = self.ui.PLL1R1Div
                self.clkin1 = float(freq_control.text())
            elif source_name == "CLKin2" and hasattr(self.ui, 'lineEditClkin2Oscout') and hasattr(self.ui, 'PLL1R2Div'):
                freq_control = self.ui.lineEditClkin2Oscout
                divider_control = self.ui.PLL1R2Div
                self.clkin2 = float(freq_control.text())
            else:
                return

            # 获取频率和分频比
            freq_value = float(freq_control.text())
            divider_value = divider_control.value()

            # 缓存更新的频率值
            if source_name == "CLKin0":
                self._cache_lineedit_value("lineEditClkin0", str(freq_value))
            elif source_name == "CLKin1":
                self._cache_lineedit_value("lineEditClkin1", str(freq_value))
            elif source_name == "CLKin2":
                self._cache_lineedit_value("lineEditClkin2Oscout", str(freq_value))

            # 如果当前选择的是这个时钟源，则更新lineEditClkinSelOut的值
            if hasattr(self.ui, 'CLKinSelManual'):
                current_index = self.ui.CLKinSelManual.currentIndex()
                selected_source = CLOCK_SOURCES[current_index] if current_index < len(CLOCK_SOURCES) else "Holdover"

                if selected_source == source_name and hasattr(self.ui, 'lineEditClkinSelOut'):
                    # 显示时钟源名称而不是数值
                    self.ui.lineEditClkinSelOut.setText(selected_source)
                    self.previous_clkin_value = str(freq_value)
                    # 缓存选择输出值
                    self._cache_lineedit_value("lineEditClkinSelOut", selected_source)

            # 无论是否是当前选择的时钟源，都发布更新通知
            self._emit_clock_source_update(source_name, freq_value, divider_value)

        except (ValueError, TypeError) as e:
            logger.error(f"更新时钟输入频率时发生错误: {str(e)}")
        except Exception as e:
            logger.error(f"处理时钟输入频率编辑事件时发生错误: {str(e)}")
            traceback.print_exc()

    def update_divider_value(self, source_name, divider_value):
        """更新时钟源的分频比值并发送更新信号"""
        try:
            logger.info(f"更新时钟源 {source_name} 的分频比为 {divider_value}")

            # 存储分频比
            if source_name == "CLKin0":
                self.clkin0_divider = divider_value
            elif source_name == "CLKin1":
                self.clkin1_divider = divider_value
            elif source_name == "CLKin2":
                self.clkin2_divider = divider_value

            # 获取频率值
            freq_value, _ = self._get_clock_info_by_name(source_name)

            # 发送时钟源更新信号
            self._emit_clock_source_update(source_name, freq_value, divider_value)

            # 如果当前选择的是这个时钟源，更新输出显示
            self.update_clkin_sel_out()
        except Exception as e:
            logger.error(f"更新分频比时发生错误: {str(e)}")

    def calculate_dac_update_rate(self):
        """计算DAC Update Rate

        公式: DAC Update Rate = DAC_CLK_MULT * DAC_CLK_CNTR / PLL1_PFD_Freq
        结果保留一位小数
        """
        try:
            logger.debug("开始计算DAC Update Rate...")

            # 获取DAC_CLK_MULT值
            dac_clk_mult = self._get_dac_clk_mult_value()
            if dac_clk_mult is None:
                logger.warning("无法获取DAC_CLK_MULT值，设置DAC Update Rate为0.0")
                if hasattr(self.ui, 'DACUpdateRate'):
                    self.ui.DACUpdateRate.setText("0.0")
                return

            # 获取DAC_CLK_CNTR值
            dac_clk_cntr = self._get_dac_clk_cntr_value()
            if dac_clk_cntr is None:
                logger.warning("无法获取DAC_CLK_CNTR值，设置DAC Update Rate为0.0")
                if hasattr(self.ui, 'DACUpdateRate'):
                    self.ui.DACUpdateRate.setText("0.0")
                return

            # 获取PLL1PFDFreq值
            pll1_pfd_freq = self._get_pll1_pfd_freq()
            if pll1_pfd_freq is None or pll1_pfd_freq <= 0:
                logger.warning(f"无法获取PLL1PFDFreq值或值无效: {pll1_pfd_freq}，设置DAC Update Rate为0.0")
                if hasattr(self.ui, 'DACUpdateRate'):
                    self.ui.DACUpdateRate.setText("0.0")
                return

            logger.debug(f"DAC Update Rate计算参数: mult={dac_clk_mult}, cntr={dac_clk_cntr}, pfd_freq={pll1_pfd_freq} MHz")

            # 计算DAC Update Rate
            # PLL1PFDFreq的单位是MHz，需要转换为Hz (乘以1,000,000)
            pll1_pfd_freq_hz = pll1_pfd_freq * 1_000_000
            dac_update_rate = (dac_clk_mult * dac_clk_cntr) / pll1_pfd_freq_hz

            # 保留一位小数
            dac_update_rate_rounded = round(dac_update_rate, 1)

            # 更新UI显示
            if hasattr(self.ui, 'DACUpdateRate'):
                self.ui.DACUpdateRate.setText(str(dac_update_rate_rounded))
                logger.info(f"✅ DAC Update Rate计算完成: {dac_clk_mult} * {dac_clk_cntr} / {pll1_pfd_freq_hz} Hz = {dac_update_rate_rounded} (PLL1PFDFreq: {pll1_pfd_freq} MHz)")
            else:
                logger.error("❌ DACUpdateRate控件不存在")

        except Exception as e:
            logger.error(f"❌ 计算DAC Update Rate时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            if hasattr(self.ui, 'DACUpdateRate'):
                self.ui.DACUpdateRate.setText("Error")

    def force_refresh_dac_update_rate(self):
        """强制刷新DAC Update Rate计算

        这个方法可以在需要时手动调用，确保DAC Update Rate显示正确的值
        """
        try:
            logger.info("🔄 强制刷新DAC Update Rate...")

            # 清除缓存，强制重新获取所有值
            if hasattr(self, '_cached_pll1_pfd_freq'):
                old_cached = self._cached_pll1_pfd_freq
                self._cached_pll1_pfd_freq = None
                logger.debug(f"清除本地PLL1PFDFreq缓存: {old_cached}")

            # 重新初始化默认值
            self._initialize_pll1_pfd_freq_from_defaults()

            # 多次计算确保准确
            for i in range(3):
                QtCore.QTimer.singleShot(i * 100, self.calculate_dac_update_rate)

            logger.info("✅ 强制刷新DAC Update Rate完成")

        except Exception as e:
            logger.error(f"❌ 强制刷新DAC Update Rate时出错: {str(e)}")

    def _get_dac_clk_mult_value(self):
        """获取DAC_CLK_MULT的数值"""
        try:
            if not hasattr(self.ui, 'DACClkMult'):
                return None
                
            current_index = self.ui.DACClkMult.currentIndex()
            # 根据DACClkMult的选项映射获取实际值
            mult_values = {0: 4, 1: 64, 2: 1024, 3: 16384}
            return mult_values.get(current_index, None)
            
        except Exception as e:
            logger.error(f"获取DAC_CLK_MULT值时发生错误: {str(e)}")
            return None

    def _get_dac_clk_cntr_value(self):
        """获取DAC_CLK_CNTR的数值"""
        try:
            if not hasattr(self.ui, 'DACClkCntr'):
                return None
                
            current_index = self.ui.DACClkCntr.currentIndex()
            # DAC_CLK_CNTR的值就是索引值（0-255）
            return current_index
            
        except Exception as e:
            logger.error(f"获取DAC_CLK_CNTR值时发生错误: {str(e)}")
            return None

    def _get_pll1_pfd_freq(self):
        """获取PLL1PFDFreq的值"""
        try:
            # 首先尝试从RegisterUpdateBus的缓存获取
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            bus = RegisterUpdateBus.instance()

            if hasattr(bus, 'get_cached_pll1_pfd_freq'):
                cached_freq = bus.get_cached_pll1_pfd_freq()
                if cached_freq is not None and cached_freq > 0:
                    logger.debug(f"从RegisterUpdateBus缓存获取PLL1PFDFreq值: {cached_freq} MHz")
                    return cached_freq

            # 其次尝试从本地缓存获取
            if hasattr(self, '_cached_pll1_pfd_freq') and self._cached_pll1_pfd_freq is not None:
                logger.debug(f"从本地缓存获取PLL1PFDFreq值: {self._cached_pll1_pfd_freq} MHz")
                return self._cached_pll1_pfd_freq

            # 如果缓存中没有，尝试从PLL窗口获取
            main_window = self._get_main_window()
            if main_window:
                # 查找PLL窗口
                for child in main_window.findChildren(QtCore.QObject):
                    if hasattr(child, 'ui') and hasattr(child.ui, 'PLL1PFDFreq'):
                        pfd_freq_text = child.ui.PLL1PFDFreq.text()
                        try:
                            pfd_freq = float(pfd_freq_text)
                            # 缓存这个值到本地和RegisterUpdateBus
                            self._cached_pll1_pfd_freq = pfd_freq
                            if hasattr(bus, 'cache_pll1_pfd_freq'):
                                bus.cache_pll1_pfd_freq(pfd_freq)
                            logger.debug(f"从PLL窗口获取并缓存PLL1PFDFreq值: {pfd_freq} MHz")
                            return pfd_freq
                        except ValueError:
                            logger.warning(f"无法解析PLL1PFDFreq值: {pfd_freq_text}")
                            return None

            # 如果无法获取，返回None
            logger.warning("无法获取PLL1PFDFreq值")
            return None

        except Exception as e:
            logger.error(f"获取PLL1PFDFreq值时发生错误: {str(e)}")
            return None

    def _setup_pll_update_listener(self):
        """设置PLL更新监听器"""
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            bus = RegisterUpdateBus.instance()

            # 监听寄存器更新事件，当PLL相关寄存器更新时重新计算DAC Update Rate
            bus.register_updated.connect(self._on_register_updated)

            # 监听PLL1PFDFreq更新信号
            if hasattr(bus, 'pll1_pfd_freq_updated'):
                bus.pll1_pfd_freq_updated.connect(self._on_pll1_pfd_freq_updated)
                logger.info("已连接PLL1PFDFreq更新信号")
            else:
                logger.warning("RegisterUpdateBus中缺少pll1_pfd_freq_updated信号")

            logger.info("已设置PLL更新监听器")

        except Exception as e:
            logger.error(f"设置PLL更新监听器时发生错误: {str(e)}")

    def _on_pll1_pfd_freq_updated(self, pfd_freq):
        """处理PLL1PFDFreq更新事件"""
        try:
            logger.info(f"收到PLL1PFDFreq更新信号: {pfd_freq} MHz")

            # 缓存PLL1PFDFreq值
            self._cached_pll1_pfd_freq = pfd_freq

            # 立即重新计算DAC Update Rate
            self.calculate_dac_update_rate()

        except Exception as e:
            logger.error(f"处理PLL1PFDFreq更新事件时发生错误: {str(e)}")
            traceback.print_exc()

    def _on_register_updated(self, register_addr, new_value):
        """当寄存器更新时的回调函数"""
        try:
            # 检查是否是PLL相关的寄存器更新
            # PLL1 R Divider相关寄存器地址
            pll_related_registers = ['0x63', '0x65', '0x67']  # PLL1 R Divider寄存器
            
            if register_addr in pll_related_registers:
                # 延迟一点时间再计算，确保PLL窗口已经更新了PLL1PFDFreq的显示
                QtCore.QTimer.singleShot(50, self.calculate_dac_update_rate)
                logger.debug(f"检测到PLL相关寄存器更新: {register_addr}，将重新计算DAC Update Rate")
                
        except Exception as e:
            logger.error(f"处理寄存器更新事件时发生错误: {str(e)}")
            traceback.print_exc()

    def _setup_pll_window_listener(self):
        """设置PLL窗口状态监听器"""
        try:
            # 获取主窗口实例
            main_window = self._get_main_window()
            if main_window and hasattr(main_window, 'plugin_integration_service'):
                plugin_service = main_window.plugin_integration_service
                
                # 监听插件窗口打开事件
                plugin_service.plugin_window_opened.connect(self._on_plugin_window_opened)
                
                # 监听插件窗口关闭事件
                plugin_service.plugin_window_closed.connect(self._on_plugin_window_closed)
                
                logger.info("已设置PLL窗口状态监听器")
                
                # 添加调试信息
                logger.debug(f"插件集成服务类型: {type(plugin_service).__name__}")
                logger.debug(f"信号连接状态 - plugin_window_opened: {hasattr(plugin_service, 'plugin_window_opened')}")
                logger.debug(f"信号连接状态 - plugin_window_closed: {hasattr(plugin_service, 'plugin_window_closed')}")
            else:
                logger.warning("无法获取插件集成服务，跳过PLL窗口状态监听器设置")
                if main_window:
                    logger.debug(f"主窗口类型: {type(main_window).__name__}")
                    logger.debug(f"主窗口属性: {[attr for attr in dir(main_window) if 'plugin' in attr.lower()]}")
                
        except Exception as e:
            logger.error(f"设置PLL窗口状态监听器时发生错误: {str(e)}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")

    def _on_plugin_window_opened(self, plugin_name, window):
        """当插件窗口打开时的回调函数"""
        try:
            logger.debug(f"插件窗口打开事件: {plugin_name}")
            # 检查是否是PLL窗口
            if plugin_name.lower() in ['pll', 'pll1_2', 'pll_control', 'pll_control_plugin', 'pll控制']:
                logger.info(f"检测到PLL窗口打开: {plugin_name}，将重新计算DAC Update Rate")
                # 延迟一点时间再计算，确保窗口完全初始化
                QtCore.QTimer.singleShot(200, self.calculate_dac_update_rate)
                # 添加调试信息，验证PLL窗口是否可以访问
                QtCore.QTimer.singleShot(300, self._debug_pll_window_access)
            else:
                logger.debug(f"非PLL窗口打开，忽略: {plugin_name}")
                
        except Exception as e:
            logger.error(f"处理插件窗口打开事件时发生错误: {str(e)}")

    def _on_plugin_window_closed(self, plugin_name):
        """当插件窗口关闭时的回调函数"""
        try:
            logger.debug(f"插件窗口关闭事件: {plugin_name}")
            # 检查是否是PLL窗口
            if plugin_name.lower() in ['pll', 'pll1_2', 'pll_control', 'pll_control_plugin', 'pll控制']:
                logger.info(f"检测到PLL窗口关闭: {plugin_name}，将重新计算DAC Update Rate")
                # PLL窗口关闭后，DAC Update Rate可能无法计算，显示为0或错误状态
                QtCore.QTimer.singleShot(100, self.calculate_dac_update_rate)
            else:
                logger.debug(f"非PLL窗口关闭，忽略: {plugin_name}")
                
        except Exception as e:
            logger.error(f"处理插件窗口关闭事件时发生错误: {str(e)}")

    def _debug_pll_window_access(self):
        """调试PLL窗口访问情况"""
        try:
            logger.info("=== PLL窗口访问调试信息 ===")
            main_window = self._get_main_window()
            if not main_window:
                logger.error("无法获取主窗口")
                return
                
            logger.info(f"主窗口类型: {type(main_window).__name__}")
            
            # 查找所有子窗口
            all_children = main_window.findChildren(QtCore.QObject)
            logger.info(f"主窗口子对象总数: {len(all_children)}")
            
            # 查找PLL相关的窗口
            pll_windows = []
            for child in all_children:
                if hasattr(child, 'ui') and hasattr(child.ui, 'PLL1PFDFreq'):
                    pll_windows.append(child)
                    pfd_freq_text = child.ui.PLL1PFDFreq.text()
                    logger.info(f"找到PLL窗口: {type(child).__name__}, PLL1PFDFreq值: '{pfd_freq_text}'")
                    
            if not pll_windows:
                logger.warning("未找到包含PLL1PFDFreq控件的窗口")
                # 列出一些可能相关的窗口
                for child in all_children:
                    if hasattr(child, 'ui'):
                        ui_attrs = [attr for attr in dir(child.ui) if 'PLL' in attr.upper()]
                        if ui_attrs:
                            logger.debug(f"窗口 {type(child).__name__} 包含PLL相关控件: {ui_attrs}")
            else:
                logger.info(f"成功找到 {len(pll_windows)} 个PLL窗口")
                
            logger.info("=== PLL窗口访问调试结束 ===")
            
        except Exception as e:
            logger.error(f"PLL窗口访问调试时发生错误: {str(e)}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")

    def _get_clock_info_by_name(self, source_name):
        """根据名称获取时钟信息"""
        try:
            if source_name == "CLKin0" and hasattr(self.ui, 'lineEditClkin0') and hasattr(self.ui, 'PLL1R0Div'):
                freq_value = float(self.ui.lineEditClkin0.text() or "0")
                divider_value = self.ui.PLL1R0Div.value()
            elif source_name == "CLKin1" and hasattr(self.ui, 'lineEditClkin1') and hasattr(self.ui, 'PLL1R1Div'):
                freq_value = float(self.ui.lineEditClkin1.text() or "0")
                divider_value = self.ui.PLL1R1Div.value()
            elif source_name == "CLKin2" and hasattr(self.ui, 'lineEditClkin2Oscout') and hasattr(self.ui, 'PLL1R2Div'):
                freq_value = float(self.ui.lineEditClkin2Oscout.text() or "0")
                divider_value = self.ui.PLL1R2Div.value()
            else:  # Holdover or unknown
                freq_value = 0.0
                divider_value = DEFAULT_HOLDOVER_DIVIDER

            return freq_value, divider_value
        except Exception as e:
            logger.error(f"获取时钟信息时发生错误: {str(e)}")
            return 0.0, DEFAULT_HOLDOVER_DIVIDER

    def _emit_clock_source_update(self, source_name, freq_value, divider_value):
        """发布时钟源更新通知"""
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            # 设置当前时钟源
            RegisterUpdateBus.instance()._current_clock_source = source_name
            # 发布时钟源选择更新
            RegisterUpdateBus.instance().emit_clock_source_selected(source_name, freq_value, divider_value)
            logger.info(f"【现代化时钟输入窗口】已发布时钟源更新: 源={source_name}, 频率={freq_value}MHz, 分频比={divider_value}")
        except Exception as e:
            logger.error(f"【现代化时钟输入窗口】发布时钟源更新时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def _send_initial_clock_source_info(self):
        """发送初始时钟源信息"""
        try:
            if not hasattr(self.ui, 'CLKinSelManual'):
                return

            # 获取当前选择的时钟源
            index = self.ui.CLKinSelManual.currentIndex()

            # 获取时钟源、频率和分频比
            selected_source, freq_value, divider_value = self._get_clock_info_by_index(index)

            # 发送信息
            self._emit_clock_source_update(selected_source, freq_value, divider_value)
            logger.info(f"初始化时发送时钟源信息: 源={selected_source}, 频率={freq_value}MHz, 分频比={divider_value}")
        except Exception as e:
            logger.error(f"初始化时发送时钟源信息失败: {str(e)}")
            traceback.print_exc()

    def _register_manually_initialized_controls(self):
        """注册手动初始化的控件，避免未初始化控件检查警告"""
        try:
            # 定义手动初始化的文本控件，避免未初始化控件检查警告
            manual_controls = [
                "lineEditClkin0",
                "lineEditClkin1",
                "lineEditClkin2Oscout",
                "lineEditClkinSelOut"
            ]

            # 为每个手动初始化的控件创建一个虚拟映射
            for control_name in manual_controls:
                if hasattr(self.ui, control_name) and hasattr(self, 'widget_register_map') and control_name not in self.widget_register_map:
                    # 创建一个虚拟的bit_def，标记为手动初始化
                    bit_def = {
                        "name": f"{control_name}_manual",
                        "default": "",
                        "options": "",
                        "manually_initialized": True
                    }

                    # 获取当前文本值作为默认值
                    control = getattr(self.ui, control_name)
                    default_value = control.text() if hasattr(control, "text") else ""

                    # 将控件添加到映射表中
                    self.widget_register_map[control_name] = {
                        "register_addr": "manual",  # 使用特殊标记表示手动初始化
                        "widget_type": "lineedit",
                        "default_value": default_value,
                        "bit_def": bit_def
                    }

                    logger.info(f"已将手动初始化的控件 {control_name} 添加到映射表")

            logger.info(f"已注册 {len(manual_controls)} 个手动初始化的控件")
        except Exception as e:
            logger.error(f"注册手动初始化控件时发生错误: {str(e)}")

    def _restore_lineedit_values_from_cache(self):
        """从缓存恢复LineEdit控件的值"""
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            bus = RegisterUpdateBus.instance()

            # 定义需要缓存的lineEdit控件
            lineedit_controls = [
                "lineEditClkin0",
                "lineEditClkin1",
                "lineEditClkin2Oscout",
                "lineEditClkinSelOut"
            ]

            restored_count = 0
            for control_name in lineedit_controls:
                if hasattr(self.ui, control_name):
                    # 从缓存获取值
                    cached_value = bus.get_cached_lineedit_value(self._window_name, control_name)
                    if cached_value is not None:
                        control = getattr(self.ui, control_name)
                        # 阻塞信号以避免触发不必要的事件
                        control.blockSignals(True)
                        control.setText(cached_value)
                        control.blockSignals(False)

                        # 更新对应的实例变量
                        if control_name == "lineEditClkin0":
                            try:
                                self.clkin0 = float(cached_value)
                            except (ValueError, TypeError):
                                pass
                        elif control_name == "lineEditClkin1":
                            try:
                                self.clkin1 = float(cached_value)
                            except (ValueError, TypeError):
                                pass
                        elif control_name == "lineEditClkin2Oscout":
                            try:
                                self.clkin2 = float(cached_value)
                            except (ValueError, TypeError):
                                pass

                        restored_count += 1
                        logger.info(f"从缓存恢复LineEdit值: {control_name} = '{cached_value}'")
                    else:
                        logger.debug(f"没有缓存的值: {control_name}")
                else:
                    logger.warning(f"未找到控件: {control_name}")

            if restored_count > 0:
                logger.info(f"成功从缓存恢复了 {restored_count} 个LineEdit控件的值")
            else:
                logger.debug("没有从缓存恢复任何LineEdit控件的值")

        except Exception as e:
            logger.error(f"从缓存恢复LineEdit值时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def _cache_lineedit_value(self, control_name, value):
        """缓存单个LineEdit控件的值"""
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            bus = RegisterUpdateBus.instance()
            bus.cache_lineedit_value(self._window_name, control_name, value)
            logger.debug(f"已缓存LineEdit值: {control_name} = '{value}'")
        except Exception as e:
            logger.error(f"缓存LineEdit值时发生错误: {str(e)}")

    def _cache_all_lineedit_values(self):
        """缓存所有LineEdit控件的当前值"""
        try:
            lineedit_controls = [
                "lineEditClkin0",
                "lineEditClkin1",
                "lineEditClkin2Oscout",
                "lineEditClkinSelOut"
            ]

            cached_count = 0
            for control_name in lineedit_controls:
                if hasattr(self.ui, control_name):
                    control = getattr(self.ui, control_name)
                    current_value = control.text()
                    self._cache_lineedit_value(control_name, current_value)
                    cached_count += 1

            logger.debug(f"已缓存 {cached_count} 个LineEdit控件的值")

        except Exception as e:
            logger.error(f"缓存所有LineEdit值时发生错误: {str(e)}")

    def closeEvent(self, event):
        """重写closeEvent方法，在窗口关闭时缓存所有LineEdit值"""
        try:
            # 缓存所有LineEdit控件的当前值
            self._cache_all_lineedit_values()
            logger.info("时钟输入窗口关闭，已缓存所有LineEdit控件的值")
        except Exception as e:
            logger.error(f"窗口关闭时缓存LineEdit值失败: {str(e)}")

        # 调用父类的closeEvent方法
        super().closeEvent(event)

    @classmethod
    def create_for_testing(cls, parent=None):
        """创建测试实例"""
        try:
            # 创建模拟的RegisterManager
            from core.services.register.RegisterManager import RegisterManager
            import json
            import os

            # 加载寄存器配置
            config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'lib', 'register.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                registers_config = json.load(f)

            register_manager = RegisterManager(registers_config)

            # 创建实例（新的基类会自动处理初始化）
            instance = cls(parent, register_manager)

            logger.info("创建现代化时钟输入控制测试实例成功")
            return instance

        except Exception as e:
            logger.error(f"创建测试实例时出错: {str(e)}")
            raise


if __name__ == "__main__":
    import sys
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    # 创建测试实例
    handler = ModernClkinControlHandler.create_for_testing()
    handler.show()
    
    sys.exit(app.exec_())
