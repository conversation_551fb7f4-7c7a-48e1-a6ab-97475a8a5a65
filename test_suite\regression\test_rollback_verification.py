#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证回退是否成功
检查代码是否已回退到之前的状态
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_rollback_verification():
    """验证回退是否成功"""
    print("=" * 60)
    print("🔄 验证代码回退状态")
    print("=" * 60)
    
    test_results = []
    
    # 1. 检查RegisterMainWindow.py的回退
    print("\n1. 检查RegisterMainWindow.py的回退...")
    try:
        with open('ui/windows/RegisterMainWindow.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否使用ModernToolWindowFactory（回退后的正确状态）
        if 'from ui.factories.ModernToolWindowFactory import ModernToolWindowFactory' in content:
            print("   ✅ RegisterMainWindow.py已回退到使用ModernToolWindowFactory")
            test_results.append("RegisterMainWindow.py回退成功")
        elif 'from ui.factories.ToolWindowFactory import ToolWindowFactory' in content:
            print("   ❌ RegisterMainWindow.py仍在使用ToolWindowFactory")
            test_results.append("RegisterMainWindow.py回退失败")
        else:
            print("   ⚠️  RegisterMainWindow.py状态不明确")
            test_results.append("RegisterMainWindow.py状态不明确")
            
    except Exception as e:
        print(f"   ❌ 检查RegisterMainWindow.py时出错: {str(e)}")
        test_results.append(f"RegisterMainWindow.py检查失败: {str(e)}")
    
    # 2. 检查InitializationManager.py的回退
    print("\n2. 检查InitializationManager.py的回退...")
    try:
        with open('ui/managers/InitializationManager.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否回退到传递register_repo参数
        if 'register_repo=register_repo' in content:
            print("   ✅ InitializationManager.py已回退到传递register_repo参数")
            test_results.append("InitializationManager.py回退成功")
        else:
            print("   ❌ InitializationManager.py未回退到传递register_repo参数")
            test_results.append("InitializationManager.py回退失败")
            
        # 检查是否回退到直接访问tool_window_factory
        if 'tool_window_factory.modern_factory' not in content:
            print("   ✅ InitializationManager.py已回退到直接访问tool_window_factory")
            test_results.append("InitializationManager.py访问方式回退成功")
        else:
            print("   ❌ InitializationManager.py仍在使用tool_window_factory.modern_factory")
            test_results.append("InitializationManager.py访问方式回退失败")
            
    except Exception as e:
        print(f"   ❌ 检查InitializationManager.py时出错: {str(e)}")
        test_results.append(f"InitializationManager.py检查失败: {str(e)}")
    
    # 3. 检查现代化处理器构造函数
    print("\n3. 检查现代化处理器构造函数...")
    try:
        # 检查ModernRegisterTableHandler
        with open('ui/handlers/ModernRegisterTableHandler.py', 'r', encoding='utf-8') as f:
            table_content = f.read()
        
        # 检查ModernRegisterTreeHandler
        with open('ui/handlers/ModernRegisterTreeHandler.py', 'r', encoding='utf-8') as f:
            tree_content = f.read()
        
        # 这些处理器的构造函数应该不接受register_repo参数
        if 'def __init__(self, parent=None, register_manager=None):' in tree_content:
            print("   ✅ ModernRegisterTreeHandler构造函数正确（不接受register_repo）")
            test_results.append("ModernRegisterTreeHandler构造函数正确")
        else:
            print("   ⚠️  ModernRegisterTreeHandler构造函数可能有变化")
            test_results.append("ModernRegisterTreeHandler构造函数状态不明确")
        
        # ModernRegisterIOHandler应该接受register_repo参数
        with open('ui/handlers/ModernRegisterIOHandler.py', 'r', encoding='utf-8') as f:
            io_content = f.read()
        
        if 'def __init__(self, parent=None, register_manager=None, register_repo=None):' in io_content:
            print("   ✅ ModernRegisterIOHandler构造函数正确（接受register_repo）")
            test_results.append("ModernRegisterIOHandler构造函数正确")
        else:
            print("   ⚠️  ModernRegisterIOHandler构造函数可能有变化")
            test_results.append("ModernRegisterIOHandler构造函数状态不明确")
            
    except Exception as e:
        print(f"   ❌ 检查现代化处理器构造函数时出错: {str(e)}")
        test_results.append(f"现代化处理器构造函数检查失败: {str(e)}")
    
    # 4. 总结回退状态
    print("\n" + "=" * 60)
    print("📊 回退验证结果")
    print("=" * 60)
    
    success_count = 0
    total_count = len(test_results)
    
    for i, result in enumerate(test_results, 1):
        if "失败" in result or "错误" in result:
            print(f"{i}. ❌ {result}")
        elif "不明确" in result:
            print(f"{i}. ⚠️  {result}")
        else:
            print(f"{i}. ✅ {result}")
            success_count += 1
    
    print(f"\n回退成功率: {success_count}/{total_count} ({success_count/total_count:.1%})")
    
    if success_count >= total_count * 0.8:  # 80%以上认为回退成功
        print("\n🎉 代码已成功回退到之前的状态！")
        print("\n📝 回退说明：")
        print("   - RegisterMainWindow现在直接使用ModernToolWindowFactory")
        print("   - InitializationManager直接访问tool_window_factory")
        print("   - 现代化处理器构造函数参数已恢复")
        print("   - 这应该能解决界面显示问题")
        return True
    else:
        print(f"\n⚠️  回退可能不完整，有 {total_count - success_count} 项未成功回退。")
        return False

if __name__ == "__main__":
    success = test_rollback_verification()
    sys.exit(0 if success else 1)
