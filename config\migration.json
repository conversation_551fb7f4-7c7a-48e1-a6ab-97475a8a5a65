{"migration_config": {"version": "1.0", "description": "现代化处理器迁移配置", "last_updated": "2024-06-02", "migration_strategy": "gradual"}, "handlers": {"set_modes": {"use_modern": true, "migration_status": "completed", "migration_date": "2024-06-02", "test_status": "passed", "notes": "完全迁移，所有功能正常"}, "clkin_control": {"use_modern": true, "migration_status": "completed", "migration_date": "2024-06-02", "test_status": "passed", "notes": "完全迁移，35个控件映射正常"}, "pll_control": {"use_modern": true, "migration_status": "completed", "migration_date": "2024-06-02", "test_status": "passed", "notes": "完全迁移，59个控件映射，频率计算正常"}, "sync_sysref": {"use_modern": true, "migration_status": "completed", "migration_date": "2024-06-02", "test_status": "passed", "notes": "完全迁移，28个控件映射，批量控制正常"}, "clk_output": {"use_modern": true, "migration_status": "completed", "migration_date": "2024-06-02", "test_status": "passed", "notes": "完全迁移，150个控件映射，14路输出正常"}, "register_table": {"use_modern": true, "migration_status": "completed", "migration_date": "2024-06-02", "test_status": "passed", "notes": "完全迁移，位域表格处理，二进制编辑功能正常"}, "ui_event": {"use_modern": true, "migration_status": "completed", "migration_date": "2024-06-02", "test_status": "passed", "notes": "完全迁移，事件处理系统，信号机制正常"}, "register_io": {"use_modern": true, "migration_status": "completed", "migration_date": "2024-06-02", "test_status": "passed", "notes": "完全迁移，IO控制功能，搜索功能，批量操作正常"}, "register_tree": {"use_modern": true, "migration_status": "completed", "migration_date": "2024-06-02", "test_status": "passed", "notes": "完全迁移，树视图管理，选择处理，搜索过滤正常"}}, "fallback_config": {"enable_fallback": true, "fallback_timeout": 5000, "auto_retry": true, "max_retries": 2}, "testing_config": {"enable_testing_mode": false, "test_both_versions": false, "performance_monitoring": true, "error_reporting": true}, "feature_flags": {"enable_migration_ui": true, "show_migration_status": true, "allow_runtime_switching": true, "enable_performance_metrics": true}}