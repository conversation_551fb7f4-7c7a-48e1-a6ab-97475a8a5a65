#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试版本集成功能
验证窗口标题和关于对话框是否正确显示版本信息
"""

import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from ui.windows.RegisterMainWindow import RegisterMainWindow
from core.services.spi.spi_service_impl import SPIServiceImpl
from core.repositories.register_repository import RegisterRepository
from core.services.version.VersionService import VersionService
from utils.Log import get_module_logger

logger = get_module_logger(__name__)

def test_version_integration():
    """测试版本集成功能"""
    print("=" * 60)
    print("测试版本集成功能")
    print("=" * 60)
    
    try:
        # 创建QApplication
        app = QApplication(sys.argv)
        
        print("1. 测试版本服务...")
        
        # 测试版本服务
        version_service = VersionService.instance()
        version_info = version_service.get_all_version_info()
        
        print("✓ 版本服务创建成功")
        print(f"   版本字符串: {version_info['version_string']}")
        print(f"   应用名称: {version_info['app_name']}")
        print(f"   应用标题: {version_info['app_title']}")
        print(f"   完整标题: {version_info['full_app_title']}")
        print(f"   公司: {version_info['company']}")
        print(f"   版权: {version_info['copyright']}")
        print(f"   构建日期: {version_info['build_date']}")
        
        print("\n2. 测试主窗口标题...")
        
        # 初始化SPI服务
        spi_service = SPIServiceImpl()
        if spi_service.initialize():
            print("✓ SPI服务初始化成功")
        
        # 创建寄存器仓库
        register_repo = RegisterRepository(spi_service)
        print("✓ 寄存器仓库创建成功")
        
        # 创建主窗口
        main_window = RegisterMainWindow(register_repo)
        print("✓ 主窗口创建成功")
        
        # 检查窗口标题
        window_title = main_window.windowTitle()
        print(f"   窗口标题: '{window_title}'")
        
        # 验证标题是否包含版本信息
        expected_title = version_service.get_app_title_with_version()
        if window_title == expected_title:
            print("✅ 窗口标题版本信息正确")
        else:
            print(f"❌ 窗口标题版本信息不匹配")
            print(f"   期望: '{expected_title}'")
            print(f"   实际: '{window_title}'")
        
        # 显示窗口
        main_window.show()
        app.processEvents()
        time.sleep(1)
        
        print("\n3. 测试关于对话框...")
        
        # 测试关于对话框
        if hasattr(main_window, 'tool_window_manager'):
            print("✓ 工具窗口管理器可用")
            
            # 这里我们不实际显示对话框，只测试版本信息获取
            try:
                tool_manager = main_window.tool_window_manager
                
                # 模拟关于对话框的版本信息获取
                app_name = version_service.get_app_name()
                version = version_service.get_version_string()
                description = version_service.get_app_description()
                company = version_service.get_company()
                copyright_info = version_service.get_copyright()
                build_date = version_service.get_build_date()
                
                print(f"   应用名称: {app_name}")
                print(f"   版本: {version}")
                print(f"   描述: {description}")
                print(f"   公司: {company}")
                print(f"   版权: {copyright_info}")
                print(f"   构建日期: {build_date}")
                
                print("✅ 关于对话框版本信息获取成功")
                
            except Exception as e:
                print(f"❌ 关于对话框版本信息获取失败: {str(e)}")
        else:
            print("❌ 工具窗口管理器不可用")
        
        print("\n4. 测试版本号格式...")
        
        # 测试版本号格式
        version_string = version_service.get_version_string()
        short_version = version_service.get_short_version_string()
        
        # 验证版本号格式
        import re
        full_version_pattern = r'^\d+\.\d+\.\d+\.\d+$'
        short_version_pattern = r'^\d+\.\d+\.\d+$'
        
        if re.match(full_version_pattern, version_string):
            print(f"✅ 完整版本号格式正确: {version_string}")
        else:
            print(f"❌ 完整版本号格式错误: {version_string}")
        
        if re.match(short_version_pattern, short_version):
            print(f"✅ 短版本号格式正确: {short_version}")
        else:
            print(f"❌ 短版本号格式错误: {short_version}")
        
        print("\n5. 测试开发构建检测...")
        
        # 测试开发构建检测
        is_dev_build = version_service.is_development_build()
        print(f"   是否为开发构建: {is_dev_build}")
        
        if is_dev_build:
            print("✅ 开发构建检测正常（构建号 > 0）")
        else:
            print("ℹ️  当前为发布构建（构建号 = 0）")
        
        print("\n6. 测试完成")
        print("=" * 60)
        print("测试结果总结:")
        print("- 版本服务功能正常")
        print("- 主窗口标题包含版本信息")
        print("- 关于对话框版本信息完整")
        print("- 版本号格式符合规范")
        print("=" * 60)
        
        # 关闭应用程序
        main_window.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_version_service_standalone():
    """独立测试版本服务"""
    print("\n" + "=" * 60)
    print("独立测试版本服务")
    print("=" * 60)
    
    try:
        # 测试版本服务单例
        vs1 = VersionService.instance()
        vs2 = VersionService.instance()
        
        if vs1 is vs2:
            print("✅ 版本服务单例模式正常")
        else:
            print("❌ 版本服务单例模式失败")
        
        # 测试版本信息重新加载
        original_version = vs1.get_version_string()
        vs1.reload_version()
        reloaded_version = vs1.get_version_string()
        
        if original_version == reloaded_version:
            print("✅ 版本信息重新加载正常")
        else:
            print("❌ 版本信息重新加载异常")
        
        # 测试所有版本信息方法
        methods_to_test = [
            'get_version_string',
            'get_short_version_string',
            'get_app_name',
            'get_app_title_with_version',
            'get_full_app_title_with_version',
            'get_app_description',
            'get_company',
            'get_copyright',
            'get_build_date',
            'get_build_type'
        ]
        
        print("\n版本服务方法测试:")
        for method_name in methods_to_test:
            try:
                method = getattr(vs1, method_name)
                result = method()
                print(f"✓ {method_name}: {result}")
            except Exception as e:
                print(f"✗ {method_name}: 错误 - {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 独立测试过程中出现错误: {str(e)}")
        return False

if __name__ == "__main__":
    success1 = test_version_service_standalone()
    success2 = test_version_integration()
    
    if success1 and success2:
        print("\n🎉 所有版本集成测试通过！")
    else:
        print("\n💥 版本集成测试失败！")
    
    sys.exit(0 if (success1 and success2) else 1)
