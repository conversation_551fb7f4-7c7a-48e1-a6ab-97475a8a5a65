#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
插件窗口服务
负责管理插件窗口的创建、显示、隐藏和生命周期
"""

from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from PyQt5.QtWidgets import QApplication
from core.services.plugin.PluginManager import IToolWindowPlugin
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class PluginWindowService(QObject):
    """插件窗口服务"""
    
    # 信号定义
    plugin_window_opened = pyqtSignal(str, object)  # plugin_name, window
    plugin_window_closed = pyqtSignal(str)  # plugin_name
    
    def __init__(self, main_window):
        """初始化插件窗口服务
        
        Args:
            main_window: 主窗口实例
        """
        super().__init__()
        self.main_window = main_window
        self.plugin_windows = {}  # 存储插件窗口实例
        self._closing_plugins = set()  # 正在关闭的插件集合，防止重复关闭
        
    def show_plugin_window(self, plugin: IToolWindowPlugin):
        """显示插件窗口"""
        try:
            # 检查窗口是否已存在且有效
            if plugin.name in self.plugin_windows:
                window = self.plugin_windows[plugin.name]

                # 检查窗口是否仍然有效
                if self._is_window_valid(window):
                    try:
                        window.show()
                        window.raise_()
                        window.activateWindow()
                        logger.info(f"显示现有插件窗口: {plugin.name}")
                        return
                    except Exception as e:
                        logger.warning(f"显示现有窗口时出错 {plugin.name}: {str(e)}")
                        self._cleanup_invalid_plugin_window(plugin.name)
                        # 通知插件清理其窗口实例
                        self._notify_plugin_window_closed(plugin.name)
                else:
                    logger.info(f"插件窗口 {plugin.name} 已无效，将重新创建")
                    self._cleanup_invalid_plugin_window(plugin.name)
                    # 通知插件清理其窗口实例
                    self._notify_plugin_window_closed(plugin.name)

            # 创建新窗口
            window = plugin.create_window(self.main_window)
            if window:
                # 配置窗口属性
                self._configure_plugin_window(window, plugin.name)

                # 连接窗口关闭信号
                if hasattr(window, 'window_closed'):
                    window.window_closed.connect(lambda: self._on_plugin_window_closed(plugin.name))
                elif hasattr(window, 'closeEvent'):
                    # 如果没有自定义信号，重写closeEvent
                    original_close = window.closeEvent
                    def close_wrapper(event):
                        self._on_plugin_window_closed(plugin.name)
                        original_close(event)
                    window.closeEvent = close_wrapper

                # 连接destroyed信号来处理对象被删除的情况
                if hasattr(window, 'destroyed'):
                    window.destroyed.connect(lambda: self._on_plugin_window_destroyed(plugin.name))
                
                # 存储窗口引用
                self.plugin_windows[plugin.name] = window
                
                # 显示窗口
                window.show()
                
                # 发送信号
                self.plugin_window_opened.emit(plugin.name, window)

                # 设置插件窗口之间的连接
                self._setup_plugin_window_connections(plugin.name, window)

                logger.info(f"创建并显示插件窗口: {plugin.name}")
            else:
                logger.error(f"插件窗口创建失败: {plugin.name}")
                
        except Exception as e:
            logger.error(f"显示插件窗口失败 {plugin.name}: {str(e)}")

    def _setup_plugin_window_connections(self, plugin_name: str, window):
        """设置插件窗口之间的连接

        Args:
            plugin_name: 插件名称
            window: 窗口实例
        """
        try:
            # 处理时钟输出和同步系统参考之间的连接
            if plugin_name == "时钟输出":
                # 如果同步系统参考窗口已存在，连接它们
                sync_window = self.plugin_windows.get("同步系统参考")
                if sync_window and hasattr(window, 'set_sync_sysref_handler'):
                    window.set_sync_sysref_handler(sync_window)
                    logger.info("已连接时钟输出窗口与同步系统参考窗口")

            elif plugin_name == "同步系统参考":
                # 如果时钟输出窗口已存在，连接它们
                clk_output_window = self.plugin_windows.get("时钟输出")
                if clk_output_window and hasattr(clk_output_window, 'set_sync_sysref_handler'):
                    clk_output_window.set_sync_sysref_handler(window)
                    logger.info("已连接同步系统参考窗口与时钟输出窗口")

        except Exception as e:
            logger.error(f"设置插件窗口连接失败 {plugin_name}: {str(e)}")

    def hide_plugin_window(self, plugin: IToolWindowPlugin):
        """隐藏插件窗口"""
        if plugin.name in self.plugin_windows:
            window = self.plugin_windows[plugin.name]

            # 检查窗口是否仍然有效
            if self._is_window_valid(window):
                try:
                    window.hide()
                    logger.info(f"隐藏插件窗口: {plugin.name}")
                except Exception as e:
                    logger.warning(f"隐藏插件窗口时出错 {plugin.name}: {str(e)}")
                    del self.plugin_windows[plugin.name]
            else:
                logger.warning(f"插件窗口 {plugin.name} 已无效，清理引用")
                del self.plugin_windows[plugin.name]
    
    def _on_plugin_window_closed(self, plugin_name: str, from_tab_close: bool = False):
        """处理插件窗口关闭"""
        # 防止重复关闭
        if plugin_name in self._closing_plugins:
            logger.debug(f"插件 {plugin_name} 正在关闭中，跳过重复处理")
            return

        self._closing_plugins.add(plugin_name)
        logger.info(f"处理插件窗口关闭: {plugin_name}, from_tab_close={from_tab_close}")

        try:
            # 通知插件清理窗口实例
            self._notify_plugin_window_closed(plugin_name)

            # 移除窗口引用
            if plugin_name in self.plugin_windows:
                del self.plugin_windows[plugin_name]
                logger.debug(f"已移除插件窗口引用: {plugin_name}")

            # 发送信号
            self.plugin_window_closed.emit(plugin_name)

            logger.info(f"插件窗口关闭处理完成: {plugin_name}")

        finally:
            # 确保从关闭集合中移除
            self._closing_plugins.discard(plugin_name)

    def _on_plugin_window_destroyed(self, plugin_name: str):
        """处理插件窗口对象被销毁"""
        logger.info(f"插件窗口对象被销毁: {plugin_name}")

        # 移除窗口引用
        if plugin_name in self.plugin_windows:
            del self.plugin_windows[plugin_name]
            logger.debug(f"已移除被销毁的插件窗口引用: {plugin_name}")

        # 从关闭集合中移除（如果存在）
        self._closing_plugins.discard(plugin_name)

    def _notify_plugin_window_closed(self, plugin_name: str):
        """通知插件其窗口已关闭，让插件清理窗口实例"""
        try:
            # 获取插件管理器
            plugin_service = getattr(self.main_window, 'plugin_service', None)
            if not plugin_service:
                logger.debug(f"无法获取插件服务，跳过通知插件: {plugin_name}")
                return

            plugin_manager = getattr(plugin_service, 'plugin_manager', None)
            if not plugin_manager:
                logger.debug(f"无法获取插件管理器，跳过通知插件: {plugin_name}")
                return

            # 获取插件实例
            plugin = plugin_manager.get_plugin(plugin_name)
            if plugin and hasattr(plugin, '_on_window_closed'):
                try:
                    plugin._on_window_closed()
                    logger.debug(f"已通知插件 {plugin_name} 清理窗口实例")
                except Exception as e:
                    logger.warning(f"通知插件 {plugin_name} 清理窗口实例时出错: {str(e)}")
            else:
                logger.debug(f"插件 {plugin_name} 没有 _on_window_closed 方法")

        except Exception as e:
            logger.warning(f"通知插件窗口关闭失败 {plugin_name}: {str(e)}")

    def _is_window_valid(self, window) -> bool:
        """检查窗口是否仍然有效"""
        if not window:
            return False

        try:
            # 检查是否是已删除的C++对象
            if hasattr(window, '__class__'):
                _ = window.__class__.__name__

            # 尝试访问窗口的基本属性来验证其有效性
            window.isVisible()
            window.windowTitle()

            # 额外检查：尝试获取窗口的父对象
            try:
                _ = window.parent()
            except (TypeError, RuntimeError):
                logger.debug("窗口parent()方法调用失败，但这不影响窗口有效性")

            # 检查窗口是否被标记为已删除
            if hasattr(window, '_is_deleted') and window._is_deleted:
                logger.debug("窗口已被标记为已删除")
                return False

            return True
        except (RuntimeError, AttributeError) as e:
            # 特别检查"wrapped C/C++ object has been deleted"错误
            error_msg = str(e).lower()
            if "wrapped c/c++ object" in error_msg and "deleted" in error_msg:
                logger.debug(f"检测到已删除的C++对象: {str(e)}")
                return False
            logger.debug(f"窗口无效 (RuntimeError/AttributeError): {str(e)}")
            return False
        except Exception as e:
            logger.debug(f"窗口有效性检查时出现异常: {str(e)}")
            return False

    def _cleanup_invalid_plugin_window(self, plugin_name: str):
        """清理无效的插件窗口引用"""
        try:
            # 移除窗口引用
            if plugin_name in self.plugin_windows:
                del self.plugin_windows[plugin_name]
                logger.debug(f"已移除无效插件窗口引用: {plugin_name}")

            logger.info(f"无效插件窗口清理完成: {plugin_name}")

        except Exception as e:
            logger.error(f"清理无效插件窗口时出错 {plugin_name}: {str(e)}")

    def _configure_plugin_window(self, window, plugin_name: str):
        """配置插件窗口以改善用户体验"""
        try:
            from PyQt5.QtCore import Qt

            # 为插件窗口添加滚动支持
            self._add_scroll_support_to_window(window, plugin_name)

            # 设置窗口标志，使其可以独立移动和调整大小
            window.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint |
                                Qt.WindowMinimizeButtonHint | Qt.WindowMaximizeButtonHint)

            # 设置窗口为非模态（仅对QDialog有效）
            if hasattr(window, 'setModal'):
                window.setModal(False)

            # 添加点击置顶功能
            self._add_click_to_front_support(window, plugin_name)

            # 获取主窗口位置和大小
            main_window = self.main_window
            if main_window:
                main_geometry = main_window.geometry()

                # 计算插件窗口的初始位置（主窗口右侧）
                plugin_x = main_geometry.x() + main_geometry.width() + 10
                plugin_y = main_geometry.y()

                # 获取屏幕尺寸
                screen = QApplication.primaryScreen()
                screen_geometry = screen.availableGeometry()

                # 确保窗口不会超出屏幕边界
                if plugin_x + window.width() > screen_geometry.right():
                    plugin_x = main_geometry.x() - window.width() - 10  # 放在左侧
                    if plugin_x < 0:
                        plugin_x = 50  # 如果左侧也放不下，就放在屏幕左边

                if plugin_y + window.height() > screen_geometry.bottom():
                    plugin_y = screen_geometry.bottom() - window.height() - 50

                # 设置窗口位置
                window.move(plugin_x, plugin_y)

            # 设置窗口最小尺寸
            if not window.minimumSize().isValid() or window.minimumSize().isEmpty():
                window.setMinimumSize(300, 200)

            # 智能调整窗口大小
            self._smart_resize_window(window, plugin_name)

            # 设置窗口图标（如果主窗口有图标的话）
            if hasattr(main_window, 'windowIcon') and not main_window.windowIcon().isNull():
                window.setWindowIcon(main_window.windowIcon())

            # 添加拖拽停靠支持
            self._add_drag_dock_support(window, plugin_name)

            logger.info(f"已配置插件窗口: {plugin_name}")

        except Exception as e:
            logger.warning(f"配置插件窗口失败 {plugin_name}: {str(e)}")

    def _add_drag_dock_support(self, window, plugin_name: str):
        """为插件窗口添加拖拽停靠支持"""
        try:
            # 获取插件服务
            plugin_service = getattr(self.main_window, 'plugin_service', None)
            if not plugin_service:
                logger.warning(f"无法获取插件服务，跳过拖拽停靠支持: {plugin_name}")
                return

            # 获取停靠服务
            dock_service = getattr(plugin_service, 'dock_service', None)
            if not dock_service:
                logger.warning(f"无法获取停靠服务，跳过拖拽停靠支持: {plugin_name}")
                return

            # 添加拖拽停靠支持
            dock_service.add_drag_dock_support(window, plugin_name)
            logger.debug(f"已为插件窗口添加拖拽停靠支持: {plugin_name}")

        except Exception as e:
            logger.warning(f"添加拖拽停靠支持失败 {plugin_name}: {str(e)}")

    def _add_scroll_support_to_window(self, window, plugin_name: str):
        """为插件窗口添加滚动支持"""
        try:
            # 检查窗口是否是ModernBaseHandler的子类
            if hasattr(window, 'enable_scroll_area'):
                # 如果是现代化处理器，检查是否需要启用滚动
                if not hasattr(window, 'scroll_area') or window.scroll_area is None:
                    # 根据插件类型决定是否启用滚动
                    if self._should_enable_scroll_for_plugin(plugin_name):
                        window.enable_scroll_area()
                        logger.info(f"为插件 {plugin_name} 启用了滚动区域")
                return

            # 对于非现代化处理器，手动添加滚动支持
            self._add_manual_scroll_support(window, plugin_name)

        except Exception as e:
            logger.warning(f"为插件窗口添加滚动支持失败 {plugin_name}: {str(e)}")

    def _should_enable_scroll_for_plugin(self, plugin_name: str) -> bool:
        """判断插件是否需要滚动支持"""
        scroll_required_plugins = {
            'clkin_control_plugin',
            'pll_control_plugin',
            'sync_sysref_plugin',
            'clk_output_plugin',
            'set_modes_plugin',
            '示例工具'
        }
        return plugin_name in scroll_required_plugins

    def _add_manual_scroll_support(self, window, plugin_name: str):
        """为非现代化处理器手动添加滚动支持"""
        try:
            from PyQt5.QtWidgets import QScrollArea, QVBoxLayout, QWidget
            from PyQt5.QtCore import Qt

            # 检查窗口是否已经有滚动区域
            if hasattr(window, '_scroll_area_added'):
                return

            # 获取窗口的当前布局和内容
            current_layout = window.layout()
            if not current_layout:
                logger.warning(f"插件窗口 {plugin_name} 没有布局，无法添加滚动支持")
                return

            # 创建新的主布局
            new_main_layout = QVBoxLayout()
            new_main_layout.setContentsMargins(0, 0, 0, 0)

            # 创建滚动区域
            scroll_area = QScrollArea()
            scroll_area.setWidgetResizable(True)
            scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
            scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

            # 创建内容容器
            content_widget = QWidget()
            content_widget.setLayout(current_layout)

            # 设置内容区域的最小尺寸
            min_size = self._get_plugin_minimum_size(plugin_name)
            content_widget.setMinimumSize(min_size[0], min_size[1])

            # 将内容设置到滚动区域
            scroll_area.setWidget(content_widget)

            # 将滚动区域添加到新布局
            new_main_layout.addWidget(scroll_area)

            # 设置新布局到窗口
            window.setLayout(new_main_layout)

            # 标记已添加滚动支持
            window._scroll_area_added = True
            window._scroll_area = scroll_area

            logger.info(f"为插件 {plugin_name} 手动添加了滚动支持")

        except Exception as e:
            logger.error(f"手动添加滚动支持失败 {plugin_name}: {str(e)}")

    def _get_plugin_minimum_size(self, plugin_name: str):
        """获取插件的最小尺寸"""
        size_mapping = {
            'clkin_control_plugin': (1650, 950),
            'pll_control_plugin': (2550, 1875),
            'sync_sysref_plugin': (1350, 1050),
            'clk_output_plugin': (2250, 2550),
            'set_modes_plugin': (1350, 1050),
            '示例工具': (900, 1200)
        }
        return size_mapping.get(plugin_name, (1250, 980))

    def _smart_resize_window(self, window, plugin_name: str):
        """智能调整窗口大小，自适应内容"""
        try:
            # 获取屏幕尺寸
            screen = QApplication.primaryScreen()
            screen_geometry = screen.availableGeometry()
            screen_width = screen_geometry.width()
            screen_height = screen_geometry.height()

            # 获取插件的最优大小
            optimal_size = self._get_plugin_optimal_size(plugin_name)
            if optimal_size:
                optimal_width, optimal_height = optimal_size
            else:
                optimal_width, optimal_height = (1200, 900)

            # 应用屏幕大小限制
            max_width = int(screen_width * 0.8)
            max_height = int(screen_height * 0.8)

            # 获取最小尺寸限制
            min_size = self._get_plugin_minimum_size(plugin_name)
            min_width, min_height = min_size

            # 计算最终尺寸
            final_width = max(min_width, min(optimal_width, max_width))
            final_height = max(min_height, min(optimal_height, max_height))

            # 应用新尺寸
            window.resize(final_width, final_height)
            logger.info(f"已调整窗口大小: {final_width}x{final_height}")

        except Exception as e:
            logger.error(f"智能调整窗口大小失败 {plugin_name}: {str(e)}")

    def _get_plugin_optimal_size(self, plugin_name: str):
        """获取插件的最优大小"""
        optimal_size_mapping = {
            'clkin_control_plugin': (1025, 925),
            'pll_control_plugin': (1800, 1350),
            'sync_sysref_plugin': (1425, 1125),
            'clk_output_plugin': (1650, 1500),
            'set_modes_plugin': (1425, 1125),
            '示例工具': (975, 750),
            'performance_monitor_plugin': (1200, 900),
            'selective_register_plugin': (1350, 1050)
        }
        return optimal_size_mapping.get(plugin_name, None)

    def get_plugin_window(self, plugin_name: str):
        """获取插件窗口实例"""
        return self.plugin_windows.get(plugin_name)

    def close_all_plugin_windows(self):
        """关闭所有插件窗口"""
        try:
            # 创建窗口名称列表的副本，避免在迭代过程中修改字典
            plugin_names = list(self.plugin_windows.keys())
            
            for plugin_name in plugin_names:
                try:
                    window = self.plugin_windows.get(plugin_name)
                    if window and self._is_window_valid(window):
                        window.close()
                        logger.info(f"已关闭插件窗口: {plugin_name}")
                except Exception as e:
                    logger.error(f"关闭插件窗口失败 {plugin_name}: {str(e)}")
            
            # 清理所有引用
            self.plugin_windows.clear()
            logger.info("所有插件窗口已关闭")
            
        except Exception as e:
            logger.error(f"关闭所有插件窗口失败: {str(e)}")

    def cleanup(self):
        """清理资源"""
        try:
            self.close_all_plugin_windows()
            self._closing_plugins.clear()
            logger.info("插件窗口服务清理完成")
        except Exception as e:
            logger.error(f"插件窗口服务清理失败: {str(e)}")

    def _add_click_to_front_support(self, window, plugin_name: str):
        """为插件窗口添加点击置顶功能"""
        try:
            from PyQt5.QtCore import Qt, QEvent, QObject

            # 设置窗口属性，使其能够接收焦点
            window.setFocusPolicy(Qt.ClickFocus)

            # 创建事件过滤器类
            class ClickToFrontFilter(QObject):
                def __init__(self, target_window):
                    super().__init__()
                    self.target_window = target_window

                def eventFilter(self, obj, event):
                    try:
                        # 当窗口接收到鼠标按下事件时，将其置顶
                        if obj == self.target_window and event.type() == QEvent.MouseButtonPress:
                            self.target_window.raise_()
                            self.target_window.activateWindow()
                            logger.debug(f"插件窗口 {plugin_name} 已通过点击置顶")

                        return False  # 不拦截事件，让其继续传播

                    except Exception as e:
                        logger.error(f"处理插件窗口点击事件时出错: {str(e)}")
                        return False

            # 创建并安装事件过滤器
            click_filter = ClickToFrontFilter(window)
            window.installEventFilter(click_filter)

            # 将过滤器存储在窗口对象中，防止被垃圾回收
            window._click_to_front_filter = click_filter

            logger.debug(f"已为插件窗口 {plugin_name} 添加点击置顶功能")

        except Exception as e:
            logger.error(f"为插件窗口 {plugin_name} 添加点击置顶功能时出错: {str(e)}")
