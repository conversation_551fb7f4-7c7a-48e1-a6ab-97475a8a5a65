{".class": "MypyFile", "_fullname": "unittest.runner", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Never": {".class": "SymbolTableNode", "cross_ref": "typing.Never", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SupportsFlush": {".class": "SymbolTableNode", "cross_ref": "_typeshed.SupportsFlush", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SupportsWrite": {".class": "SymbolTableNode", "cross_ref": "_typeshed.SupportsWrite", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TextTestResult": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["unittest.result.TestResult"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "unittest.runner.TextTestResult", "name": "TextTestResult", "type_vars": [{".class": "TypeVarType", "default": "unittest.runner._WritelnDecorator", "fullname": "unittest.runner._StreamT", "id": 1, "name": "_StreamT", "namespace": "unittest.runner.TextTestResult", "upper_bound": "unittest.runner._TextTestStream", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "unittest.runner.TextTestResult", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "unittest.runner", "mro": ["unittest.runner.TextTestResult", "unittest.result.TestResult", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["self", "stream", "descriptions", "verbosity", "durations"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "unittest.runner.TextTestResult.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["self", "stream", "descriptions", "verbosity", "durations"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "unittest.runner._WritelnDecorator", "fullname": "unittest.runner._StreamT", "id": 1, "name": "_StreamT", "namespace": "unittest.runner.TextTestResult", "upper_bound": "unittest.runner._TextTestStream", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "unittest.runner.TextTestResult"}, {".class": "TypeVarType", "default": "unittest.runner._WritelnDecorator", "fullname": "unittest.runner._StreamT", "id": 1, "name": "_StreamT", "namespace": "unittest.runner.TextTestResult", "upper_bound": "unittest.runner._TextTestStream", "values": [], "variance": 0}, "builtins.bool", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TextTestResult", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "descriptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "unittest.runner.TextTestResult.descriptions", "name": "descriptions", "type": "builtins.bool"}}, "dots": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "unittest.runner.TextTestResult.dots", "name": "dots", "type": "builtins.bool"}}, "durations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "unittest.runner.TextTestResult.durations", "name": "durations", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "getDescription": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "test"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "unittest.runner.TextTestResult.getDescription", "name": "getDescription", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "test"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "unittest.runner._WritelnDecorator", "fullname": "unittest.runner._StreamT", "id": 1, "name": "_StreamT", "namespace": "unittest.runner.TextTestResult", "upper_bound": "unittest.runner._TextTestStream", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "unittest.runner.TextTestResult"}, "unittest.case.TestCase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getDescription of TextTestResult", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "printErrorList": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "flavour", "errors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "unittest.runner.TextTestResult.printErrorList", "name": "printErrorList", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "flavour", "errors"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "unittest.runner._WritelnDecorator", "fullname": "unittest.runner._StreamT", "id": 1, "name": "_StreamT", "namespace": "unittest.runner.TextTestResult", "upper_bound": "unittest.runner._TextTestStream", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "unittest.runner.TextTestResult"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["unittest.case.TestCase", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "printErrorList of TextTestResult", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "separator1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "unittest.runner.TextTestResult.separator1", "name": "separator1", "type": "builtins.str"}}, "separator2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "unittest.runner.TextTestResult.separator2", "name": "separator2", "type": "builtins.str"}}, "showAll": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "unittest.runner.TextTestResult.showAll", "name": "showAll", "type": "builtins.bool"}}, "stream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "unittest.runner.TextTestResult.stream", "name": "stream", "type": {".class": "TypeVarType", "default": "unittest.runner._WritelnDecorator", "fullname": "unittest.runner._StreamT", "id": 1, "name": "_StreamT", "namespace": "unittest.runner.TextTestResult", "upper_bound": "unittest.runner._TextTestStream", "values": [], "variance": 0}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "unittest.runner.TextTestResult.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "unittest.runner._WritelnDecorator", "fullname": "unittest.runner._StreamT", "id": 1, "name": "_StreamT", "namespace": "unittest.runner.TextTestResult", "upper_bound": "unittest.runner._TextTestStream", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "unittest.runner.TextTestResult"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_StreamT"], "typeddict_type": null}}, "TextTestRunner": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "unittest.runner.TextTestRunner", "name": "TextTestRunner", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "unittest.runner.TextTestRunner", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "unittest.runner", "mro": ["unittest.runner.TextTestRunner", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 5, 5], "arg_names": ["self", "stream", "descriptions", "verbosity", "failfast", "buffer", "resultclass", "warnings", "tb_locals", "durations"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "unittest.runner.TextTestRunner.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 5, 5], "arg_names": ["self", "stream", "descriptions", "verbosity", "failfast", "buffer", "resultclass", "warnings", "tb_locals", "durations"], "arg_types": ["unittest.runner.TextTestRunner", {".class": "UnionType", "items": ["unittest.runner._SupportsWriteAndFlush", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.int", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "unittest.runner._ResultClassType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TextTestRunner", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_makeResult": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "unittest.runner.TextTestRunner._makeResult", "name": "_makeResult", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["unittest.runner.TextTestRunner"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_makeResult of TextTestRunner", "ret_type": {".class": "Instance", "args": ["unittest.runner._WritelnDecorator"], "extra_attrs": null, "type_ref": "unittest.runner.TextTestResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "unittest.runner.TextTestRunner.buffer", "name": "buffer", "type": "builtins.bool"}}, "descriptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "unittest.runner.TextTestRunner.descriptions", "name": "descriptions", "type": "builtins.bool"}}, "durations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "unittest.runner.TextTestRunner.durations", "name": "durations", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "failfast": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "unittest.runner.TextTestRunner.failfast", "name": "failfast", "type": "builtins.bool"}}, "resultclass": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "unittest.runner.TextTestRunner.resultclass", "name": "resultclass", "type": {".class": "TypeAliasType", "args": [], "type_ref": "unittest.runner._ResultClassType"}}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "test"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "unittest.runner.TextTestRunner.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "test"], "arg_types": ["unittest.runner.TextTestRunner", {".class": "UnionType", "items": ["unittest.suite.TestSuite", "unittest.case.TestCase"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of TextTestRunner", "ret_type": {".class": "Instance", "args": ["unittest.runner._WritelnDecorator"], "extra_attrs": null, "type_ref": "unittest.runner.TextTestResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "unittest.runner.TextTestRunner.stream", "name": "stream", "type": "unittest.runner._WritelnDecorator"}}, "tb_locals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "unittest.runner.TextTestRunner.tb_locals", "name": "tb_locals", "type": "builtins.bool"}}, "verbosity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "unittest.runner.TextTestRunner.verbosity", "name": "verbosity", "type": "builtins.int"}}, "warnings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "unittest.runner.TextTestRunner.warnings", "name": "warnings", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "unittest.runner.TextTestRunner.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "unittest.runner.TextTestRunner", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ActionKind": {".class": "SymbolTableNode", "cross_ref": "warnings._ActionKind", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ResultClassType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "unittest.runner._ResultClassType", "line": 11, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["unittest.runner._TextTestStream", "builtins.bool", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "unittest.runner.TextTestResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_StreamT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": "unittest.runner._WritelnDecorator", "fullname": "unittest.runner._StreamT", "name": "_StreamT", "upper_bound": "unittest.runner._TextTestStream", "values": [], "variance": 0}}, "_SupportsWriteAndFlush": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "_typeshed.SupportsWrite"}, "_typeshed.SupportsFlush"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "unittest.runner._SupportsWriteAndFlush", "name": "_SupportsWriteAndFlush", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "unittest.runner._SupportsWriteAndFlush", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "unittest.runner", "mro": ["unittest.runner._SupportsWriteAndFlush", "_typeshed.SupportsWrite", "_typeshed.SupportsFlush", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "unittest.runner._SupportsWriteAndFlush.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "unittest.runner._SupportsWriteAndFlush", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_TextTestStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["unittest.runner._SupportsWriteAndFlush"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "unittest.runner._TextTestStream", "name": "_TextTestStream", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "unittest.runner._TextTestStream", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "unittest.runner", "mro": ["unittest.runner._TextTestStream", "unittest.runner._SupportsWriteAndFlush", "_typeshed.SupportsWrite", "_typeshed.SupportsFlush", "builtins.object"], "names": {".class": "SymbolTable", "writeln": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "unittest.runner._TextTestStream.writeln", "name": "writeln", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": [null, null], "arg_types": ["unittest.runner._TextTestStream", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "writeln of _TextTestStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "unittest.runner._TextTestStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "unittest.runner._TextTestStream", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_WritelnDecorator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "unittest.runner._WritelnDecorator", "name": "_WritelnDecorator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "unittest.runner._WritelnDecorator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "unittest.runner", "mro": ["unittest.runner._WritelnDecorator", "builtins.object"], "names": {".class": "SymbolTable", "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "unittest.runner._WritelnDecorator.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["unittest.runner._WritelnDecorator", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__ of _WritelnDecorator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "unittest.runner._WritelnDecorator.__getstate__", "name": "__getstate__", "type": {".class": "UninhabitedType"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "unittest.runner._WritelnDecorator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "arg_types": ["unittest.runner._WritelnDecorator", "unittest.runner._SupportsWriteAndFlush"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _WritelnDecorator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "flush": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "unittest.runner._WritelnDecorator.flush", "name": "flush", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["unittest.runner._WritelnDecorator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "flush of _WritelnDecorator", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "unittest.runner._WritelnDecorator.stream", "name": "stream", "type": {".class": "UninhabitedType"}}}, "write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "unittest.runner._WritelnDecorator.write", "name": "write", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["unittest.runner._WritelnDecorator", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write of _WritelnDecorator", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "writeln": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "unittest.runner._WritelnDecorator.writeln", "name": "writeln", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "arg"], "arg_types": ["unittest.runner._WritelnDecorator", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "writeln of _WritelnDecorator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "unittest.runner._WritelnDecorator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "unittest.runner._WritelnDecorator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unittest.runner.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unittest.runner.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unittest.runner.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unittest.runner.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unittest.runner.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unittest.runner.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "unittest": {".class": "SymbolTableNode", "cross_ref": "unittest", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\unittest\\runner.pyi"}