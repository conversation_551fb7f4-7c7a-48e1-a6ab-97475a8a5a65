#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试时钟输入控制窗口修复
验证现代化版本的控件初始化是否正常工作
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PyQt5.QtCore import QTimer

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.Log import logger


def test_clkin_control_initialization():
    """测试时钟输入控制窗口的初始化"""
    print("=" * 60)
    print("测试时钟输入控制窗口初始化")
    print("=" * 60)
    
    try:
        # 1. 测试传统版本
        print("\n1. 测试传统版本...")
        from ui.handlers.ClkinControlHandler import ClkinControlHandler
        
        traditional_handler = ClkinControlHandler.create_for_testing()
        print("   ✓ 传统版本创建成功")
        
        # 检查关键控件是否初始化
        key_controls = [
            'lineEditClkin0', 'lineEditClkin1', 'lineEditClkin2Oscout',
            'PLL1R0Div', 'PLL1R1Div', 'PLL1R2Div',
            'CLKinSelManual', 'lineEditClkinSelOut'
        ]
        
        for control_name in key_controls:
            if hasattr(traditional_handler.ui, control_name):
                control = getattr(traditional_handler.ui, control_name)
                if hasattr(control, 'text'):
                    value = control.text()
                    print(f"   ✓ {control_name}: '{value}'")
                elif hasattr(control, 'value'):
                    value = control.value()
                    print(f"   ✓ {control_name}: {value}")
                elif hasattr(control, 'currentText'):
                    value = control.currentText()
                    print(f"   ✓ {control_name}: '{value}'")
                else:
                    print(f"   ✓ {control_name}: 存在")
            else:
                print(f"   ❌ {control_name}: 不存在")
        
        # 2. 测试现代化版本
        print("\n2. 测试现代化版本...")
        from ui.handlers.ModernClkinControlHandler import ModernClkinControlHandler
        
        modern_handler = ModernClkinControlHandler.create_for_testing()
        print("   ✓ 现代化版本创建成功")
        
        # 检查关键控件是否初始化
        for control_name in key_controls:
            if hasattr(modern_handler.ui, control_name):
                control = getattr(modern_handler.ui, control_name)
                if hasattr(control, 'text'):
                    value = control.text()
                    print(f"   ✓ {control_name}: '{value}'")
                elif hasattr(control, 'value'):
                    value = control.value()
                    print(f"   ✓ {control_name}: {value}")
                elif hasattr(control, 'currentText'):
                    value = control.currentText()
                    print(f"   ✓ {control_name}: '{value}'")
                else:
                    print(f"   ✓ {control_name}: 存在")
            else:
                print(f"   ❌ {control_name}: 不存在")
        
        # 3. 对比初始化值
        print("\n3. 对比初始化值...")
        
        # 检查频率值
        freq_controls = [
            ('lineEditClkin0', 'CLKin0频率'),
            ('lineEditClkin1', 'CLKin1频率'),
            ('lineEditClkin2Oscout', 'CLKin2频率')
        ]
        
        for control_name, desc in freq_controls:
            if hasattr(traditional_handler.ui, control_name) and hasattr(modern_handler.ui, control_name):
                trad_value = getattr(traditional_handler.ui, control_name).text()
                modern_value = getattr(modern_handler.ui, control_name).text()
                
                if trad_value == modern_value:
                    print(f"   ✓ {desc}: 传统='{trad_value}', 现代='{modern_value}' (一致)")
                else:
                    print(f"   ⚠ {desc}: 传统='{trad_value}', 现代='{modern_value}' (不一致)")
        
        # 检查分频比值
        divider_controls = [
            ('PLL1R0Div', 'CLKin0分频比'),
            ('PLL1R1Div', 'CLKin1分频比'),
            ('PLL1R2Div', 'CLKin2分频比')
        ]
        
        for control_name, desc in divider_controls:
            if hasattr(traditional_handler.ui, control_name) and hasattr(modern_handler.ui, control_name):
                trad_value = getattr(traditional_handler.ui, control_name).value()
                modern_value = getattr(modern_handler.ui, control_name).value()
                
                if trad_value == modern_value:
                    print(f"   ✓ {desc}: 传统={trad_value}, 现代={modern_value} (一致)")
                else:
                    print(f"   ⚠ {desc}: 传统={trad_value}, 现代={modern_value} (不一致)")
        
        # 4. 测试ComboBox选项
        print("\n4. 测试ComboBox选项...")
        
        combo_controls = [
            'CLKinSelManual', 'CLKin0Demux', 'CLKin1Demux', 
            'OSCoutMux', 'OSCoutClockFormat'
        ]
        
        for control_name in combo_controls:
            if hasattr(traditional_handler.ui, control_name) and hasattr(modern_handler.ui, control_name):
                trad_combo = getattr(traditional_handler.ui, control_name)
                modern_combo = getattr(modern_handler.ui, control_name)
                
                trad_count = trad_combo.count()
                modern_count = modern_combo.count()
                
                if trad_count == modern_count:
                    print(f"   ✓ {control_name}: 传统={trad_count}项, 现代={modern_count}项 (一致)")
                    
                    # 检查选项内容
                    for i in range(min(trad_count, modern_count)):
                        trad_text = trad_combo.itemText(i)
                        modern_text = modern_combo.itemText(i)
                        if trad_text != modern_text:
                            print(f"     ⚠ 第{i}项: 传统='{trad_text}', 现代='{modern_text}'")
                else:
                    print(f"   ⚠ {control_name}: 传统={trad_count}项, 现代={modern_count}项 (不一致)")
        
        print("\n5. 显示窗口进行视觉验证...")
        
        # 创建主窗口来显示两个版本
        main_window = QMainWindow()
        main_window.setWindowTitle("时钟输入控制窗口对比")
        main_window.resize(1200, 800)
        
        central_widget = QWidget()
        main_window.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 添加标签和按钮
        layout.addWidget(QLabel("时钟输入控制窗口修复验证"))
        
        traditional_btn = QPushButton("显示传统版本")
        modern_btn = QPushButton("显示现代化版本")
        
        def show_traditional():
            traditional_handler.show()
            traditional_handler.raise_()
            traditional_handler.activateWindow()
        
        def show_modern():
            modern_handler.show()
            modern_handler.raise_()
            modern_handler.activateWindow()
        
        traditional_btn.clicked.connect(show_traditional)
        modern_btn.clicked.connect(show_modern)
        
        layout.addWidget(traditional_btn)
        layout.addWidget(modern_btn)
        
        # 显示主窗口
        main_window.show()
        
        # 自动显示两个窗口
        QTimer.singleShot(500, show_traditional)
        QTimer.singleShot(1000, show_modern)
        
        print("   ✓ 窗口已显示，请进行视觉对比验证")
        print("\n测试完成！请检查两个窗口的控件初始化是否一致。")
        
        return main_window, traditional_handler, modern_handler
        
    except Exception as e:
        print(f"   ❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None, None


def main():
    """主函数"""
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    
    # 运行测试
    main_window, traditional_handler, modern_handler = test_clkin_control_initialization()
    
    if main_window:
        # 运行应用程序
        sys.exit(app.exec_())
    else:
        print("测试失败，无法启动应用程序")
        sys.exit(1)


if __name__ == "__main__":
    main()
