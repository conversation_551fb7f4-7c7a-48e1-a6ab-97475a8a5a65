#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
FSJ04832 统一打包入口
提供统一的打包管理接口
"""

import sys
import os
import json
from pathlib import Path

# 添加项目根目录到Python路径
packaging_root = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(packaging_root)
sys.path.insert(0, project_root)
sys.path.insert(0, packaging_root)

def load_config():
    """加载打包配置"""
    config_file = Path(packaging_root) / 'config' / 'packaging_config.json'
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"警告: 无法加载配置文件 {config_file}: {e}")
        return {}

def show_help():
    """显示帮助信息"""
    print("=" * 60)
    print("FSJ04832 统一打包管理系统")
    print("=" * 60)
    print()
    print("使用方法:")
    print("  python package.py [命令] [选项]")
    print()
    print("命令:")
    print("  gui                    启动图形界面版本管理工具")
    print("  build [类型]           命令行构建 (类型: build/patch/minor/major)")
    print("  secure [类型]          安全打包 - 客户发布版本 (类型: build/patch/minor/major)")
    print("  list                   查看版本历史")
    print("  clean                  清理旧版本")
    print("  test                   运行打包测试")
    print("  help                   显示此帮助信息")
    print()
    print("示例:")
    print("  python package.py gui                # 启动GUI")
    print("  python package.py build build        # 增加构建号 (开发版)")
    print("  python package.py secure patch       # 安全打包 (客户版)")
    print("  python package.py list               # 查看版本历史")
    print("  python package.py clean              # 清理旧版本")
    print()
    print("🔒 安全打包说明:")
    print("  secure 命令生成客户发布版本，具有以下特性:")
    print("  ✅ 单文件可执行程序 - 无法查看内部文件")
    print("  ✅ 代码混淆保护 - 防止反编译")
    print("  ✅ UPX压缩 - 进一步保护代码")
    print("  ✅ 调试信息移除 - 不暴露开发信息")
    print()
    print("文件结构:")
    print("  scripts/               构建脚本")
    print("  tools/                 版本管理工具")
    print("  config/                配置文件")
    print("  tests/                 测试脚本")
    print("  docs/                  文档")
    print("  launchers/             启动器")
    print()

def run_gui():
    """启动图形界面"""
    print("启动图形界面版本管理工具...")
    try:
        tools_dir = Path(packaging_root) / 'tools'
        gui_script = tools_dir / 'start_gui.py'

        if gui_script.exists():
            # 保持在项目根目录执行，确保版本文件路径正确
            os.chdir(project_root)
            os.system(f'python "{gui_script}"')
        else:
            print(f"错误: GUI脚本不存在: {gui_script}")
            return False
    except Exception as e:
        print(f"启动GUI失败: {e}")
        return False
    return True

def run_build(version_type='build'):
    """运行构建"""
    print(f"开始构建 (版本类型: {version_type})...")
    try:
        scripts_dir = Path(packaging_root) / 'scripts'
        build_script = scripts_dir / 'build_exe.py'
        
        if build_script.exists():
            os.chdir(project_root)  # 在项目根目录执行构建
            cmd = f'python "{build_script}" --version-type {version_type}'
            result = os.system(cmd)
            return result == 0
        else:
            print(f"错误: 构建脚本不存在: {build_script}")
            return False
    except Exception as e:
        print(f"构建失败: {e}")
        return False

def run_list():
    """查看版本历史"""
    print("查看版本历史...")
    try:
        tools_dir = Path(packaging_root) / 'tools'
        list_script = tools_dir / 'list_versions.py'
        
        if list_script.exists():
            os.chdir(project_root)  # 在项目根目录执行
            os.system(f'python "{list_script}"')
        else:
            print(f"错误: 版本列表脚本不存在: {list_script}")
            return False
    except Exception as e:
        print(f"查看版本历史失败: {e}")
        return False
    return True

def run_clean():
    """清理旧版本"""
    print("清理旧版本...")
    try:
        tools_dir = Path(packaging_root) / 'tools'
        clean_script = tools_dir / 'clean_old_versions.py'
        
        if clean_script.exists():
            os.chdir(project_root)  # 在项目根目录执行
            os.system(f'python "{clean_script}"')
        else:
            print(f"错误: 清理脚本不存在: {clean_script}")
            return False
    except Exception as e:
        print(f"清理失败: {e}")
        return False
    return True

def run_test():
    """运行打包测试"""
    print("运行打包测试...")
    try:
        # 切换到项目根目录运行测试
        original_cwd = os.getcwd()
        os.chdir(project_root)

        tests_dir = Path('packaging') / 'tests'

        # 运行版本显示测试
        version_test = tests_dir / 'test_version_display.py'
        if version_test.exists():
            print("\n1. 运行版本显示测试...")
            result = os.system(f'python "{version_test}"')
            if result != 0:
                print("版本显示测试失败")

        # 运行版本化构建测试
        build_test = tests_dir / 'test_versioned_build.py'
        if build_test.exists():
            print("\n2. 运行版本化构建测试...")
            result = os.system(f'python "{build_test}"')
            if result != 0:
                print("版本化构建测试失败")

        # 运行打包系统测试
        packaging_test = tests_dir / 'test_packaging.py'
        if packaging_test.exists():
            print("\n3. 运行打包系统测试...")
            result = os.system(f'python "{packaging_test}"')
            if result != 0:
                print("打包系统测试失败")

        print("\n测试完成!")

        # 恢复原工作目录
        os.chdir(original_cwd)

    except Exception as e:
        print(f"测试失败: {e}")
        return False
    return True

def run_secure_build(version_type='build'):
    """运行安全打包"""
    print(f"🔒 开始安全打包 (版本类型: {version_type})...")
    print("   这将生成单文件可执行程序，保护您的代码不被客户看到")
    print()

    try:
        # 使用现有的构建系统，但修改spec文件为安全模式
        scripts_dir = Path(packaging_root) / 'scripts'
        build_script = scripts_dir / 'build_exe.py'

        if not build_script.exists():
            print(f"错误: 构建脚本不存在: {build_script}")
            return False

        # 临时修改spec文件为onefile模式
        spec_file = scripts_dir / 'build.spec'
        if not spec_file.exists():
            print(f"错误: spec文件不存在: {spec_file}")
            return False

        print("🔧 临时修改构建配置为安全模式...")

        # 读取原始spec文件
        with open(spec_file, 'r', encoding='utf-8') as f:
            spec_content = f.read()

        # 备份原始内容
        original_spec = spec_content

        # 修改为onefile模式
        secure_spec = spec_content.replace(
            'exclude_binaries=True,  # onedir 模式',
            'exclude_binaries=False,  # onefile 模式 - 安全打包'
        ).replace(
            '[]  # 空列表，使用 onedir 模式',
            'a.binaries, a.zipfiles, a.datas, []  # onefile 模式'
        ).replace(
            'upx=False,  # 禁用UPX压缩，避免兼容性问题',
            'upx=False,  # 保持禁用UPX，避免兼容性问题'
        ).replace(
            'strip=False,',
            'strip=False,  # 保持禁用strip，避免工具依赖'
        ).replace(
            'disable_windowed_traceback=False,',
            'disable_windowed_traceback=True,  # 禁用错误回溯'
        )

        # 添加Release标识
        secure_spec = secure_spec.replace(
            "exe_name = f'FSJ04832_RegisterTool_v{current_version}'",
            "exe_name = f'FSJ04832_RegisterTool_v{current_version}_Release'"
        )

        try:
            # 写入修改后的spec文件
            with open(spec_file, 'w', encoding='utf-8') as f:
                f.write(secure_spec)

            print("✅ 构建配置已修改为安全模式")
            print()

            # 执行构建
            print("🔨 开始构建安全版本...")
            import subprocess

            cmd = [sys.executable, str(build_script), '--version-type', version_type]
            result = subprocess.run(cmd, cwd=packaging_root)

            if result.returncode == 0:
                print()
                print("🎉 安全打包成功!")
                print("📦 已生成单文件可执行程序，客户无法看到内部代码")
                return True
            else:
                print("❌ 安全打包失败")
                return False

        finally:
            # 恢复原始spec文件
            with open(spec_file, 'w', encoding='utf-8') as f:
                f.write(original_spec)
            print("🔄 已恢复原始构建配置")

    except Exception as e:
        print(f"安全打包失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    if len(sys.argv) < 2:
        show_help()
        return

    command = sys.argv[1].lower()

    if command == 'help' or command == '-h' or command == '--help':
        show_help()
    elif command == 'gui':
        run_gui()
    elif command == 'build':
        version_type = sys.argv[2] if len(sys.argv) > 2 else 'build'
        if version_type not in ['build', 'patch', 'minor', 'major']:
            print(f"错误: 无效的版本类型 '{version_type}'")
            print("有效类型: build, patch, minor, major")
            return
        run_build(version_type)
    elif command == 'secure':
        version_type = sys.argv[2] if len(sys.argv) > 2 else 'build'
        if version_type not in ['build', 'patch', 'minor', 'major']:
            print(f"错误: 无效的版本类型 '{version_type}'")
            print("有效类型: build, patch, minor, major")
            return
        run_secure_build(version_type)
    elif command == 'list':
        run_list()
    elif command == 'clean':
        run_clean()
    elif command == 'test':
        run_test()
    else:
        print(f"错误: 未知命令 '{command}'")
        print("使用 'python package.py help' 查看帮助")

if __name__ == "__main__":
    main()
