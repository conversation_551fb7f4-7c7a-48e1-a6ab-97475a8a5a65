#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
插件菜单服务
负责管理插件菜单的创建、更新和事件处理
"""

from PyQt5.QtWidgets import QAction, QMenu
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from core.services.plugin.PluginManager import plugin_manager, IToolWindowPlugin
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class PluginMenuService(QObject):
    """插件菜单服务"""
    
    # 信号定义
    plugin_action_triggered = pyqtSignal(object, bool)  # plugin, checked
    
    def __init__(self, main_window):
        """初始化插件菜单服务
        
        Args:
            main_window: 主窗口实例
        """
        super().__init__()
        self.main_window = main_window
        self.plugin_actions = {}  # 存储插件菜单动作
        
    def integrate_tool_window_plugins(self):
        """将工具窗口插件集成到菜单中"""
        tool_plugins = plugin_manager.get_tool_window_plugins()

        if not tool_plugins:
            logger.info("没有发现工具窗口插件")
            return

        # 分离核心工具窗口插件和其他插件，并按指定顺序排列
        core_tool_plugins = []
        other_plugins = []

        # 定义期望的工具顺序
        desired_order = ['模式设置', '时钟输入控制', 'PLL控制', '同步系统参考', '时钟输出']
        core_tool_names = set(desired_order)

        # 创建插件名称到插件对象的映射
        plugin_map = {plugin.name: plugin for plugin in tool_plugins}

        # 按指定顺序收集核心工具插件
        for tool_name in desired_order:
            if tool_name in plugin_map:
                core_tool_plugins.append(plugin_map[tool_name])

        # 收集其他插件
        for plugin in tool_plugins:
            if plugin.name not in core_tool_names:
                other_plugins.append(plugin)

        # 将核心工具插件添加到工具菜单和工具栏
        if core_tool_plugins:
            tools_menu = self._get_tools_menu()
            if tools_menu:
                core_actions = []
                for plugin in core_tool_plugins:
                    try:
                        action = self._add_plugin_to_menu(plugin, tools_menu)
                        if action:
                            core_actions.append(action)
                            logger.info(f"核心工具插件 '{plugin.name}' 已添加到工具菜单")
                    except Exception as e:
                        logger.error(f"添加核心工具插件到菜单失败 {plugin.name}: {str(e)}")

                # 将核心工具插件的action也添加到工具栏
                if core_actions:
                    self._add_actions_to_toolbar(core_actions)
                    logger.info(f"已将 {len(core_actions)} 个核心工具插件添加到工具栏")

                logger.info(f"已将 {len(core_tool_plugins)} 个核心工具插件添加到工具菜单和工具栏")
            else:
                logger.warning("未找到工具菜单，无法添加核心工具插件")

        # 将其他插件添加到插件菜单
        if other_plugins:
            plugin_menu = self._get_or_create_plugin_menu()
            if plugin_menu:
                for plugin in other_plugins:
                    try:
                        self._add_plugin_to_menu(plugin, plugin_menu)
                    except Exception as e:
                        logger.error(f"添加插件到菜单失败 {plugin.name}: {str(e)}")
                logger.info(f"已将 {len(other_plugins)} 个插件添加到插件菜单")

    def _get_or_create_plugin_menu(self):
        """获取或创建插件菜单"""
        # 检查主窗口是否有菜单栏
        if not hasattr(self.main_window, 'menuBar'):
            logger.warning("主窗口没有菜单栏，无法添加插件菜单")
            return None

        menu_bar = self.main_window.menuBar()

        # 检查是否已经存在插件菜单
        existing_menu = None
        for action in menu_bar.actions():
            if action.menu() and "插件" in action.text():
                existing_menu = action.menu()
                logger.info("找到现有插件菜单，将重用")
                break

        if existing_menu:
            plugins_menu = existing_menu
        else:
            # 找到设置菜单的位置，在其前面插入插件菜单
            settings_menu_action = None
            for action in menu_bar.actions():
                if action.menu() and "设置" in action.text():
                    settings_menu_action = action
                    break

            if settings_menu_action:
                # 在设置菜单前插入插件菜单
                plugins_menu = QMenu("插件(&P)", self.main_window)
                menu_bar.insertMenu(settings_menu_action, plugins_menu)
                logger.info("在设置菜单前创建新的插件菜单")
            else:
                # 如果找不到设置菜单，就添加到末尾
                plugins_menu = menu_bar.addMenu("插件(&P)")
                logger.info("未找到设置菜单，在末尾创建新的插件菜单")

        # 确保菜单可见和可用
        plugins_menu.setEnabled(True)
        plugins_menu.setVisible(True)

        # 添加插件管理器菜单项（如果还没有）
        self._add_plugin_manager_menu_item(plugins_menu)

        # 强制更新菜单
        plugins_menu.update()
        menu_bar.update()

        logger.info("插件菜单设置完成")
        return plugins_menu

    def _get_tools_menu(self):
        """获取工具菜单"""
        menu_bar = self.main_window.menuBar()
        if not menu_bar:
            logger.warning("主窗口没有菜单栏")
            return None

        # 查找工具菜单
        for action in menu_bar.actions():
            if action.menu() and "工具" in action.text():
                logger.info("找到现有的工具菜单")
                return action.menu()

        logger.warning("未找到工具菜单")
        return None

    def _add_plugin_manager_menu_item(self, menu):
        """添加插件管理器菜单项"""
        # 检查是否已经添加过
        for action in menu.actions():
            if action.text() == "插件管理器":
                return

        # 创建插件管理器菜单项
        plugin_manager_action = QAction("插件管理器", self.main_window)
        plugin_manager_action.setStatusTip("管理和配置插件")

        # 使用QTimer.singleShot确保事件处理不被阻塞
        plugin_manager_action.triggered.connect(
            lambda: QTimer.singleShot(0, self._show_plugin_manager)
        )

        # 确保动作可见和可用
        plugin_manager_action.setVisible(True)
        plugin_manager_action.setEnabled(True)

        menu.addAction(plugin_manager_action)

        # 添加分隔符
        menu.addSeparator()

        logger.info("添加插件管理器菜单项")

    def _add_plugin_to_menu(self, plugin: IToolWindowPlugin, menu):
        """将插件添加到菜单

        Args:
            plugin: 工具窗口插件
            menu: 菜单对象

        Returns:
            QAction: 创建的菜单动作，如果失败则返回None
        """
        if not menu:
            return None

        # 创建菜单动作
        action = QAction(plugin.menu_text, self.main_window)
        action.setCheckable(True)

        # 设置状态提示
        action.setStatusTip(f"打开/关闭 {plugin.name}")

        # 设置快捷键（如果插件支持）
        if hasattr(plugin, 'get_shortcut'):
            try:
                shortcut = plugin.get_shortcut()
                if shortcut:
                    action.setShortcut(shortcut)
                    logger.debug(f"为插件 '{plugin.name}' 设置快捷键: {shortcut}")
            except Exception as e:
                logger.warning(f"设置插件快捷键失败 {plugin.name}: {str(e)}")

        # 设置图标（如果有）
        if plugin.icon_path:
            try:
                from PyQt5.QtGui import QIcon
                action.setIcon(QIcon(plugin.icon_path))
            except Exception as e:
                logger.warning(f"设置插件图标失败 {plugin.name}: {str(e)}")

        # 连接信号
        from functools import partial
        action.triggered.connect(partial(self._on_plugin_action_triggered, plugin))

        # 添加到菜单
        menu.addAction(action)

        # 存储动作引用
        self.plugin_actions[plugin.name] = action

        # 设置主窗口的 action 属性
        action_attr_name = self._get_action_attr_name(plugin.name)
        if action_attr_name:
            setattr(self.main_window, action_attr_name, action)
            logger.info(f"已将插件 '{plugin.name}' 的动作设置为主窗口属性: {action_attr_name}")

        # 强制刷新菜单
        menu.update()
        if hasattr(menu, 'repaint'):
            menu.repaint()

        # 确保动作可见和可用
        action.setVisible(True)
        action.setEnabled(True)

        logger.info(f"插件 '{plugin.name}' 已添加到菜单")

        # 使用菜单修复器增强点击响应
        try:
            from core.services.plugin.MenuClickFixer import menu_click_fixer

            def plugin_callback(action_obj, checked):
                """插件动作回调"""
                logger.info(f"🔧 菜单修复器回调: {plugin.name}, checked={checked}")
                self._on_plugin_action_triggered(plugin, checked)

            menu_click_fixer.fix_action(action, plugin_callback)
            logger.info(f"已为插件 '{plugin.name}' 应用菜单点击修复")

        except Exception as e:
            logger.warning(f"应用菜单点击修复失败 {plugin.name}: {str(e)}")

        return action

    def _get_action_attr_name(self, plugin_name: str) -> str:
        """根据插件名称生成对应的 action 属性名"""
        plugin_to_action_map = {
            '模式设置': 'set_modes_action',
            '时钟输入控制': 'clkin_control_action',
            'PLL控制': 'pll_control_action',
            '同步系统参考': 'sync_sysref_action',
            '时钟输出': 'clk_output_action',
        }
        return plugin_to_action_map.get(plugin_name)

    def _on_plugin_action_triggered(self, plugin: IToolWindowPlugin, checked: bool):
        """处理插件菜单动作触发"""
        logger.info(f"🎯 插件菜单动作触发: {plugin.name}, checked={checked}")
        
        try:
            # 发送信号给主服务处理
            self.plugin_action_triggered.emit(plugin, checked)
        except Exception as e:
            logger.error(f"处理插件动作失败 {plugin.name}: {str(e)}")
            # 重置动作状态
            if plugin.name in self.plugin_actions:
                try:
                    self.plugin_actions[plugin.name].setChecked(False)
                except Exception as reset_error:
                    logger.error(f"重置动作状态失败: {reset_error}")

    def update_action_state(self, plugin_name: str, checked: bool):
        """更新插件动作状态"""
        if plugin_name in self.plugin_actions:
            try:
                self.plugin_actions[plugin_name].setChecked(checked)
            except Exception as e:
                logger.error(f"更新动作状态失败 {plugin_name}: {str(e)}")

    def _show_plugin_manager(self):
        """显示插件管理器"""
        try:
            from ui.tools.PluginManagerGUI import PluginManagerWindow

            # 检查是否已经有插件管理器窗口打开
            if hasattr(self, '_plugin_manager_window') and self._plugin_manager_window:
                try:
                    self._plugin_manager_window.show()
                    self._plugin_manager_window.raise_()
                    self._plugin_manager_window.activateWindow()
                    return
                except RuntimeError:
                    # 窗口已被删除
                    self._plugin_manager_window = None

            # 创建新的插件管理器窗口
            self._plugin_manager_window = PluginManagerWindow(self.main_window)
            self._plugin_manager_window.show()

            logger.info("显示插件管理器")

        except Exception as e:
            logger.error(f"显示插件管理器失败: {str(e)}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")

    def _add_actions_to_toolbar(self, actions):
        """将动作添加到工具栏"""
        try:
            # 获取主工具栏
            toolbar = None
            from PyQt5.QtWidgets import QToolBar

            # 查找现有的主工具栏
            for toolbar_obj in self.main_window.findChildren(QToolBar):
                if toolbar_obj.windowTitle() == '主工具栏':
                    toolbar = toolbar_obj
                    break

            if not toolbar:
                # 如果找不到主工具栏，获取第一个工具栏或创建新的
                toolbars = self.main_window.findChildren(QToolBar)
                if toolbars:
                    toolbar = toolbars[0]
                    logger.info(f"使用现有工具栏: {toolbar.windowTitle()}")
                else:
                    # 创建新的工具栏
                    toolbar = self.main_window.addToolBar('主工具栏')
                    toolbar.setMovable(False)
                    toolbar.setFloatable(False)
                    logger.info("创建了新的主工具栏")

            # 添加动作到工具栏
            for action in actions:
                if action:
                    toolbar.addAction(action)
                    logger.debug(f"已将动作 '{action.text()}' 添加到工具栏")

            logger.info(f"已将 {len(actions)} 个动作添加到工具栏")

        except Exception as e:
            logger.error(f"添加动作到工具栏失败: {str(e)}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")

    def cleanup(self):
        """清理资源"""
        try:
            # 清理插件管理器窗口
            if hasattr(self, '_plugin_manager_window') and self._plugin_manager_window:
                try:
                    self._plugin_manager_window.close()
                except RuntimeError:
                    pass
                self._plugin_manager_window = None

            # 清理动作引用
            self.plugin_actions.clear()

            logger.info("插件菜单服务清理完成")

        except Exception as e:
            logger.error(f"插件菜单服务清理失败: {str(e)}")
