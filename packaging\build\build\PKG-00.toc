('E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\packaging\\build\\build\\FSJ04832_RegisterTool_v1.0.0.0.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\packaging\\build\\build\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\packaging\\build\\build\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\packaging\\build\\build\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\packaging\\build\\build\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\packaging\\build\\build\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\packaging\\build\\build\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('main',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\main.py',
   'PYSOURCE')],
 'python38.dll',
 True,
 False,
 False,
 [],
 None,
 None,
 None)
