#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
自动化拖拽停靠功能测试
"""

import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


def test_auto_dock_functionality():
    """自动化测试拖拽停靠功能"""
    try:
        print("🧪 开始自动化拖拽停靠功能测试...")
        
        # 1. 启用强制悬浮模式
        print("1. 启用强制悬浮模式...")
        from core.services.config.ConfigurationManager import set_config
        set_config('plugins.force_floating_mode', True)
        print("   ✅ 强制悬浮模式已启用")
        
        # 2. 创建主窗口
        print("2. 创建主窗口...")
        from ui.windows.RegisterMainWindow import RegisterMainWindow
        main_window = RegisterMainWindow()
        main_window.show()
        print("   ✅ 主窗口创建成功")
        
        # 3. 获取插件服务
        print("3. 获取插件服务...")
        if hasattr(main_window, 'plugin_service'):
            plugin_service = main_window.plugin_service
            print("   ✅ 插件服务获取成功")
        else:
            print("   ❌ 插件服务未找到")
            return False
            
        # 4. 检查主窗口的标签页容器
        print("4. 检查主窗口标签页容器...")
        if hasattr(main_window, 'tools_tab_widget'):
            tab_widget = main_window.tools_tab_widget
            print(f"   ✅ 标签页容器存在，当前标签数: {tab_widget.count()}")
            print(f"   📊 标签页容器可见性: {tab_widget.isVisible()}")
        else:
            print("   ❌ 主窗口没有标签页容器")
            return False
            
        # 5. 获取可用插件
        print("5. 获取可用插件...")
        from core.services.plugin.PluginManager import plugin_manager
        available_plugins = plugin_manager.get_tool_window_plugins()
        
        if not available_plugins:
            print("   ❌ 没有可用的插件")
            return False
            
        test_plugin = available_plugins[0]
        print(f"   ✅ 使用插件: {test_plugin.name}")
        
        # 6. 创建测试窗口
        print("6. 创建测试窗口...")
        test_window = test_plugin.create_window(main_window)
        if test_window:
            print("   ✅ 测试窗口创建成功")
        else:
            print("   ❌ 测试窗口创建失败")
            return False
            
        # 7. 配置为悬浮窗口
        print("7. 配置拖拽停靠功能...")
        try:
            plugin_service._configure_plugin_window(test_window, test_plugin.name)
            plugin_service.plugin_windows[test_plugin.name] = test_window
            test_window.show()
            print("   ✅ 拖拽停靠功能配置成功")
        except Exception as e:
            print(f"   ❌ 拖拽停靠功能配置失败: {str(e)}")
            return False
            
        # 8. 等待窗口完全显示
        print("8. 等待窗口完全显示...")
        QApplication.processEvents()
        time.sleep(1)
        print("   ✅ 窗口已完全显示")
        
        # 9. 检查停靠前的状态
        print("9. 检查停靠前的状态...")
        initial_tab_count = tab_widget.count()
        print(f"   📊 停靠前标签数: {initial_tab_count}")
        print(f"   📊 插件窗口可见性: {test_window.isVisible()}")
        print(f"   📊 插件窗口是否为悬浮窗口: {test_window.isWindow()}")
        
        # 10. 执行自动拖拽停靠测试
        print("10. 执行自动拖拽停靠测试...")
        success = plugin_service.test_drag_dock_functionality(test_plugin.name)
        if success:
            print("   ✅ 拖拽停靠测试执行成功")
        else:
            print("   ❌ 拖拽停靠测试执行失败")
            return False
            
        # 11. 等待停靠操作完成
        print("11. 等待停靠操作完成...")
        QApplication.processEvents()
        time.sleep(2)
        print("   ✅ 停靠操作处理完成")
        
        # 12. 检查停靠后的状态
        print("12. 检查停靠后的状态...")
        final_tab_count = tab_widget.count()
        print(f"   📊 停靠后标签数: {final_tab_count}")
        print(f"   📊 标签页容器可见性: {tab_widget.isVisible()}")
        
        if final_tab_count > initial_tab_count:
            print(f"   ✅ 检测到新标签页！标签数从 {initial_tab_count} 增加到 {final_tab_count}")
            
            # 检查新标签页的内容
            for i in range(tab_widget.count()):
                tab_text = tab_widget.tabText(i)
                print(f"   📋 标签页 {i}: {tab_text}")
                
            print("   🎉 拖拽停靠功能测试成功！插件已成功停靠到主界面")
            return True
        else:
            print(f"   ❌ 停靠失败！标签数没有增加 (前: {initial_tab_count}, 后: {final_tab_count})")
            
            # 检查插件窗口是否仍然存在
            if test_window.isVisible():
                print("   📊 插件窗口仍然可见，可能仍为悬浮状态")
            else:
                print("   📊 插件窗口不可见")
                
            return False
            
    except Exception as e:
        print(f"❌ 自动化测试失败: {str(e)}")
        logger.error(f"自动化拖拽停靠功能测试失败: {str(e)}")
        return False


def main():
    """主函数"""
    try:
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 设置定时器执行测试
        def run_test():
            success = test_auto_dock_functionality()
            if success:
                print("\n🎉 自动化测试成功！拖拽停靠功能正常工作")
            else:
                print("\n❌ 自动化测试失败！拖拽停靠功能存在问题")
            
            # 延迟退出，让用户看到结果
            QTimer.singleShot(3000, app.quit)
        
        # 延迟启动测试，确保应用程序完全初始化
        QTimer.singleShot(1000, run_test)
        
        # 运行应用程序
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ 程序运行失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()