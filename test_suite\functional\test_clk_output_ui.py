#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试时钟输出界面显示
验证频率计算和格式下拉框初始化
"""

import sys
import os
import json
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ui.handlers.ModernClkOutputsHandler import ModernClkOutputsHandler
from core.services.register.RegisterManager import RegisterManager
from utils.Log import get_module_logger

logger = get_module_logger(__name__)

def test_clk_output_ui():
    """测试时钟输出界面"""
    print("=" * 60)
    print("测试时钟输出界面显示")
    print("=" * 60)
    
    try:
        # 创建QApplication
        app = QApplication(sys.argv)
        
        print("1. 加载寄存器配置...")
        config_path = os.path.join('lib', 'register.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        print(f"   ✓ 加载了 {len(registers_config)} 个寄存器配置")
        
        print("2. 创建RegisterManager...")
        register_manager = RegisterManager(registers_config)
        print(f"   ✓ 创建了RegisterManager")
        
        print("3. 创建现代化时钟输出处理器...")
        modern_handler = ModernClkOutputsHandler(None, register_manager)
        print("   ✓ 成功创建ModernClkOutputsHandler")
        
        print("4. 检查界面状态...")
        
        # 检查VCO频率
        if hasattr(modern_handler.ui, "lineEditFvco"):
            vco_text = modern_handler.ui.lineEditFvco.text()
            print(f"   ✓ VCO频率显示: {vco_text} MHz")
        else:
            print("   ❌ VCO频率控件不存在")
        
        # 检查格式下拉框
        format_count = 0
        for output_num in range(14):
            fmt_widget_name = f"CLKout{output_num}FMT"
            if hasattr(modern_handler.ui, fmt_widget_name):
                fmt_widget = getattr(modern_handler.ui, fmt_widget_name)
                if fmt_widget.count() > 0:
                    format_count += 1
                    current_text = fmt_widget.currentText()
                    print(f"   ✓ {fmt_widget_name}: {current_text} ({fmt_widget.count()} 选项)")
        
        print(f"   ✓ 成功初始化 {format_count}/14 个格式下拉框")
        
        # 检查输出频率显示
        freq_count = 0
        for output_num in range(14):
            output_attr = f"lineEditFout{output_num}Output"
            if hasattr(modern_handler.ui, output_attr):
                output_widget = getattr(modern_handler.ui, output_attr)
                freq_text = output_widget.text()
                if freq_text and freq_text != "0.00":
                    freq_count += 1
                    print(f"   ✓ 输出{output_num}: {freq_text} MHz")
        
        print(f"   ✓ 成功显示 {freq_count}/14 个输出频率")
        
        print("5. 测试频率计算...")
        # 修改VCO频率并测试
        modern_handler.ui.lineEditFvco.setText("3000.0")
        modern_handler.calculate_output_frequencies()
        
        # 检查更新后的频率
        updated_count = 0
        for output_num in range(4):  # 只检查前4个
            output_attr = f"lineEditFout{output_num}Output"
            if hasattr(modern_handler.ui, output_attr):
                output_widget = getattr(modern_handler.ui, output_attr)
                freq_text = output_widget.text()
                if freq_text and freq_text != "0.00":
                    updated_count += 1
                    print(f"   ✓ 更新后输出{output_num}: {freq_text} MHz")
        
        print(f"   ✓ 成功更新 {updated_count}/4 个输出频率")
        
        print("6. 显示窗口...")
        modern_handler.show()
        print("   ✓ 窗口已显示")
        
        # 设置定时器自动关闭
        def close_window():
            print("   ✓ 自动关闭窗口")
            modern_handler.close()
            app.quit()
        
        timer = QTimer()
        timer.timeout.connect(close_window)
        timer.start(3000)  # 3秒后关闭
        
        print("   ✓ 窗口将在3秒后自动关闭")
        print("\n" + "=" * 60)
        print("🎉 时钟输出界面测试完成！")
        print("   - 格式下拉框已正确初始化")
        print("   - 输出频率已正确计算和显示")
        print("   - 界面功能正常工作")
        print("=" * 60)
        
        # 运行应用
        app.exec_()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_clk_output_ui()
