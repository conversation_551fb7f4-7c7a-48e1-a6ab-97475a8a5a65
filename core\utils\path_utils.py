"""
路径工具模块

提供项目路径相关的实用工具函数
"""

import os
from typing import Optional, List


def get_project_root(start_path: Optional[str] = None) -> str:
    """获取项目根目录路径
    
    通过查找特征文件来确定项目根目录，比硬编码路径层级更可靠
    
    Args:
        start_path: 开始查找的路径，默认为调用文件的目录
        
    Returns:
        str: 项目根目录的绝对路径
        
    Raises:
        RuntimeError: 如果无法找到项目根目录
    """
    if start_path is None:
        # 获取调用者的文件路径
        import inspect
        caller_frame = inspect.currentframe().f_back
        caller_file = caller_frame.f_globals.get('__file__')
        if caller_file:
            start_path = os.path.abspath(caller_file)
        else:
            start_path = os.getcwd()
    
    current_dir = os.path.dirname(os.path.abspath(start_path))
    
    # 按优先级排序的特征文件列表
    characteristic_files = [
        'version.json',      # 项目版本配置文件（最可靠）
        'main.py',          # 主程序入口
        'requirements.txt', # Python依赖文件
        '.git',            # Git仓库目录
        'setup.py',        # Python包配置文件
        'pyproject.toml',  # 现代Python项目配置
    ]
    
    max_levels = 8  # 最多向上查找8层
    for level in range(max_levels):
        # 检查当前目录是否包含特征文件
        for char_file in characteristic_files:
            char_path = os.path.join(current_dir, char_file)
            if os.path.exists(char_path):
                return current_dir
        
        # 向上一级目录
        parent_dir = os.path.dirname(current_dir)
        if parent_dir == current_dir:  # 已到达文件系统根目录
            break
        current_dir = parent_dir
    
    # 如果找不到，抛出异常
    raise RuntimeError(
        f"无法找到项目根目录。从 {start_path} 开始，"
        f"向上查找了 {max_levels} 层目录，都没有找到特征文件: {characteristic_files}"
    )


def get_relative_path(target_path: str, base_path: Optional[str] = None) -> str:
    """获取相对于项目根目录的相对路径
    
    Args:
        target_path: 目标路径
        base_path: 基准路径，默认为项目根目录
        
    Returns:
        str: 相对路径
    """
    if base_path is None:
        base_path = get_project_root()
    
    return os.path.relpath(target_path, base_path)


def ensure_dir_exists(dir_path: str) -> str:
    """确保目录存在，如果不存在则创建
    
    Args:
        dir_path: 目录路径
        
    Returns:
        str: 目录的绝对路径
    """
    abs_path = os.path.abspath(dir_path)
    os.makedirs(abs_path, exist_ok=True)
    return abs_path


def join_project_path(*paths: str) -> str:
    """连接项目根目录和给定的路径组件
    
    Args:
        *paths: 路径组件
        
    Returns:
        str: 完整的绝对路径
    """
    project_root = get_project_root()
    return os.path.join(project_root, *paths)


def is_subpath(path: str, parent: str) -> bool:
    """检查路径是否是另一个路径的子路径
    
    Args:
        path: 要检查的路径
        parent: 父路径
        
    Returns:
        bool: 如果path是parent的子路径则返回True
    """
    path = os.path.abspath(path)
    parent = os.path.abspath(parent)
    
    try:
        os.path.relpath(path, parent)
        return not os.path.relpath(path, parent).startswith('..')
    except ValueError:
        # 在Windows上，不同驱动器的路径会抛出ValueError
        return False


# 缓存项目根目录，避免重复计算
_cached_project_root: Optional[str] = None


def get_cached_project_root() -> str:
    """获取缓存的项目根目录路径
    
    第一次调用时计算并缓存结果，后续调用直接返回缓存值
    
    Returns:
        str: 项目根目录的绝对路径
    """
    global _cached_project_root
    if _cached_project_root is None:
        _cached_project_root = get_project_root()
    return _cached_project_root


def clear_project_root_cache():
    """清除项目根目录缓存
    
    在测试或项目结构发生变化时可能需要调用
    """
    global _cached_project_root
    _cached_project_root = None
