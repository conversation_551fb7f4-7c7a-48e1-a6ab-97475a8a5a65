# UNIT 测试

## 描述
单元测试 - 测试独立的代码单元

## 测试文件
- `test_modern_register_table.py`
- `test_modern_set_modes.py`
- `test_modern_ui_event.py`
- `test_register_bit_update.py`
- `test_register_update_fix.py`
- `test_register_navigation.py`
- `test_register_bus_fix.py`
- `test_widget_register_jump.py`
- `test_table_jump.py`
- `test_simple_table_write.py`

## 运行测试
```bash
# 运行该分类的所有测试
python test_suite/run_all_tests.py --category unit

# 运行特定测试文件
python test_suite/unit/test_specific_file.py
```

## 注意事项
- 确保测试环境已正确设置
- 某些测试可能需要Qt环境
- 性能测试可能需要较长时间
