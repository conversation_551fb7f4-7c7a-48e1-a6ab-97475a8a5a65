#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试默认行为：验证在没有明确设置模式时使用传统计算方法
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from ui.handlers.ModernPLLHandler import ModernPLLHandler
from core.RegisterManager import RegisterManager
from utils.Log import get_module_logger

logger = get_module_logger(__name__)

def test_default_behavior():
    """测试默认行为"""
    print("="*60)
    print("测试PLL计算的默认行为")
    print("="*60)
    
    app = QApplication(sys.argv)
    
    try:
        # 创建RegisterManager实例
        register_manager = RegisterManager()
        
        # 创建PLL处理器实例（不创建ModernSetModesHandler）
        pll_handler = ModernPLLHandler(register_manager=register_manager)
        
        # 设置测试频率
        if hasattr(pll_handler.ui, "FreFin"):
            pll_handler.ui.FreFin.setText("122.88")
        if hasattr(pll_handler.ui, "OSCinFreq"):
            pll_handler.ui.OSCinFreq.setText("122.88")
        
        print("\n--- 测试1: 默认情况下的计算行为 ---")
        
        # 检查是否启用模式化计算
        is_mode_based = pll_handler._is_mode_based_calculation_enabled()
        print(f"是否启用模式化计算: {is_mode_based}")
        
        # 获取当前模式
        current_mode = pll_handler._get_current_mode()
        print(f"当前模式: {current_mode}")
        
        # 执行频率计算
        print("执行频率计算...")
        pll_handler.calculate_output_frequencies()
        
        # 显示计算结果
        print("计算结果:")
        results = {}
        if hasattr(pll_handler.ui, "PLL1PFDFreq"):
            results["PLL1PFDFreq"] = pll_handler.ui.PLL1PFDFreq.text()
        if hasattr(pll_handler.ui, "PLL2PFDFreq"):
            results["PLL2PFDFreq"] = pll_handler.ui.PLL2PFDFreq.text()
        if hasattr(pll_handler.ui, "VCODistFreq"):
            results["VCODistFreq"] = pll_handler.ui.VCODistFreq.text()
        if hasattr(pll_handler.ui, "Fin0Freq"):
            results["Fin0Freq"] = pll_handler.ui.Fin0Freq.text()
        
        for freq_name, freq_value in results.items():
            print(f"  {freq_name}: {freq_value} MHz")
        
        print("✓ 默认行为测试完成")
        
        print("\n--- 测试2: 模拟设置模式后的行为 ---")
        
        # 模拟设置模式（通过缓存）
        pll_handler._current_mode_cache = "SingleLoop"
        print("设置模式缓存为: SingleLoop")
        
        # 再次检查是否启用模式化计算
        is_mode_based = pll_handler._is_mode_based_calculation_enabled()
        print(f"是否启用模式化计算: {is_mode_based}")
        
        # 获取当前模式
        current_mode = pll_handler._get_current_mode()
        print(f"当前模式: {current_mode}")
        
        # 执行频率计算
        print("执行频率计算...")
        pll_handler.calculate_output_frequencies()
        
        # 显示计算结果
        print("计算结果:")
        results = {}
        if hasattr(pll_handler.ui, "PLL1PFDFreq"):
            results["PLL1PFDFreq"] = pll_handler.ui.PLL1PFDFreq.text()
        if hasattr(pll_handler.ui, "PLL2PFDFreq"):
            results["PLL2PFDFreq"] = pll_handler.ui.PLL2PFDFreq.text()
        if hasattr(pll_handler.ui, "VCODistFreq"):
            results["VCODistFreq"] = pll_handler.ui.VCODistFreq.text()
        if hasattr(pll_handler.ui, "Fin0Freq"):
            results["Fin0Freq"] = pll_handler.ui.Fin0Freq.text()
        
        for freq_name, freq_value in results.items():
            print(f"  {freq_name}: {freq_value} MHz")
        
        print("✓ 模式化计算测试完成")
        
        print("\n--- 测试3: 清除模式缓存后的行为 ---")
        
        # 清除模式缓存
        pll_handler._current_mode_cache = None
        print("清除模式缓存")
        
        # 再次检查是否启用模式化计算
        is_mode_based = pll_handler._is_mode_based_calculation_enabled()
        print(f"是否启用模式化计算: {is_mode_based}")
        
        # 获取当前模式
        current_mode = pll_handler._get_current_mode()
        print(f"当前模式: {current_mode}")
        
        # 执行频率计算
        print("执行频率计算...")
        pll_handler.calculate_output_frequencies()
        
        # 显示计算结果
        print("计算结果:")
        results = {}
        if hasattr(pll_handler.ui, "PLL1PFDFreq"):
            results["PLL1PFDFreq"] = pll_handler.ui.PLL1PFDFreq.text()
        if hasattr(pll_handler.ui, "PLL2PFDFreq"):
            results["PLL2PFDFreq"] = pll_handler.ui.PLL2PFDFreq.text()
        if hasattr(pll_handler.ui, "VCODistFreq"):
            results["VCODistFreq"] = pll_handler.ui.VCODistFreq.text()
        if hasattr(pll_handler.ui, "Fin0Freq"):
            results["Fin0Freq"] = pll_handler.ui.Fin0Freq.text()
        
        for freq_name, freq_value in results.items():
            print(f"  {freq_name}: {freq_value} MHz")
        
        print("✓ 回退到默认行为测试完成")
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        app.quit()

def print_behavior_summary():
    """打印行为总结"""
    print("\n" + "="*60)
    print("PLL计算行为总结")
    print("="*60)
    print("""
默认行为（向后兼容）：
- 如果没有明确设置模式，使用传统的计算方法
- 传统方法相当于DUAL MODE的行为
- PLL1和PLL2按照原有逻辑独立计算

模式化计算（新功能）：
- 当通过ModernSetModesHandler设置模式后启用
- 根据不同模式调整计算策略
- 支持单环路、双环路、分配等多种模式

切换机制：
- 系统自动检测是否有模式设置
- 有模式设置时使用模式化计算
- 没有模式设置时使用传统计算
- 保证完全的向后兼容性

优势：
- 现有代码无需修改即可正常工作
- 新功能可选择性启用
- 计算结果保持一致性
""")
    print("="*60)

if __name__ == "__main__":
    print_behavior_summary()
    test_default_behavior()
