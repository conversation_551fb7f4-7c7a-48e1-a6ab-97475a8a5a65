2025-06-08 01:27:42,311 - PluginManager - [PluginManager.py:125] - INFO - 添加插件目录: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins
2025-06-08 01:27:42,312 - Plug<PERSON><PERSON>ana<PERSON> - [PluginManager.py:158] - WARNING - 无法处理插件文件路径: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\data_analysis_plugin.py
2025-06-08 01:27:42,312 - PluginManager - [PluginManager.py:158] - WARNING - 无法处理插件文件路径: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\example_tool_plugin.py
2025-06-08 01:27:42,312 - PluginManager - [PluginManager.py:158] - WARNING - 无法处理插件文件路径: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\performance_monitor_plugin.py
2025-06-08 01:27:42,313 - PluginManager - [PluginManager.py:158] - WARNING - 无法处理插件文件路径: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\selective_register_plugin.py
2025-06-08 01:27:42,313 - PluginManager - [PluginManager.py:158] - WARNING - 无法处理插件文件路径: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\config\selective_register_config.py
2025-06-08 01:27:42,313 - PluginManager - [PluginManager.py:127] - WARNING - 插件目录不存在: plugins
2025-06-08 01:27:42,314 - PluginManager - [PluginManager.py:158] - WARNING - 无法处理插件文件路径: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\data_analysis_plugin.py
2025-06-08 01:27:42,314 - PluginManager - [PluginManager.py:158] - WARNING - 无法处理插件文件路径: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\example_tool_plugin.py
2025-06-08 01:27:42,314 - PluginManager - [PluginManager.py:158] - WARNING - 无法处理插件文件路径: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\performance_monitor_plugin.py
2025-06-08 01:27:42,314 - PluginManager - [PluginManager.py:158] - WARNING - 无法处理插件文件路径: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\selective_register_plugin.py
2025-06-08 01:27:42,314 - PluginManager - [PluginManager.py:158] - WARNING - 无法处理插件文件路径: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\config\selective_register_config.py
2025-06-08 01:27:42,315 - PluginIntegrationService - [PluginIntegrationService.py:68] - INFO - 没有发现工具窗口插件
2025-06-08 01:27:42,315 - PluginIntegrationService - [PluginIntegrationService.py:58] - INFO - 插件系统初始化完成
2025-06-08 01:30:18,220 - PluginManager - [PluginManager.py:125] - INFO - 添加插件目录: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins
2025-06-08 01:30:18,226 - PluginManager - [PluginManager.py:158] - WARNING - 无法处理插件文件路径: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\data_analysis_plugin.py
2025-06-08 01:30:18,226 - PluginManager - [PluginManager.py:158] - WARNING - 无法处理插件文件路径: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\example_tool_plugin.py
2025-06-08 01:30:18,226 - PluginManager - [PluginManager.py:158] - WARNING - 无法处理插件文件路径: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\performance_monitor_plugin.py
2025-06-08 01:30:18,226 - PluginManager - [PluginManager.py:158] - WARNING - 无法处理插件文件路径: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\selective_register_plugin.py
2025-06-08 01:30:18,226 - PluginManager - [PluginManager.py:158] - WARNING - 无法处理插件文件路径: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\config\selective_register_config.py
2025-06-08 01:30:18,226 - PluginManager - [PluginManager.py:127] - WARNING - 插件目录不存在: plugins
2025-06-08 01:30:18,226 - PluginManager - [PluginManager.py:158] - WARNING - 无法处理插件文件路径: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\data_analysis_plugin.py
2025-06-08 01:30:18,226 - PluginManager - [PluginManager.py:158] - WARNING - 无法处理插件文件路径: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\example_tool_plugin.py
2025-06-08 01:30:18,226 - PluginManager - [PluginManager.py:158] - WARNING - 无法处理插件文件路径: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\performance_monitor_plugin.py
2025-06-08 01:30:18,226 - PluginManager - [PluginManager.py:158] - WARNING - 无法处理插件文件路径: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\selective_register_plugin.py
2025-06-08 01:30:18,226 - PluginManager - [PluginManager.py:158] - WARNING - 无法处理插件文件路径: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\config\selective_register_config.py
2025-06-08 01:30:18,226 - PluginIntegrationService - [PluginIntegrationService.py:68] - INFO - 没有发现工具窗口插件
2025-06-08 01:30:18,226 - PluginIntegrationService - [PluginIntegrationService.py:58] - INFO - 插件系统初始化完成
2025-06-08 01:35:17,172 - MenuClickFixer - [MenuClickFixer.py:130] - INFO - 开始修复菜单点击响应: 修复菜单(&F)
2025-06-08 01:35:17,261 - MenuClickFixer - [MenuClickFixer.py:140] - INFO - 菜单点击响应修复完成: 修复菜单(&F)
2025-06-08 01:35:17,261 - MenuClickFixer - [MenuClickFixer.py:43] - INFO - 开始修复动作点击响应: 修复点击1
2025-06-08 01:35:17,261 - MenuClickFixer - [MenuClickFixer.py:83] - INFO - 动作点击响应修复完成: 修复点击1
2025-06-08 01:35:17,261 - MenuClickFixer - [MenuClickFixer.py:182] - INFO - 创建强健动作: 修复点击1
2025-06-08 01:35:17,466 - MenuClickFixer - [MenuClickFixer.py:43] - INFO - 开始修复动作点击响应: 修复可选中
2025-06-08 01:35:17,466 - MenuClickFixer - [MenuClickFixer.py:83] - INFO - 动作点击响应修复完成: 修复可选中
2025-06-08 01:35:17,466 - MenuClickFixer - [MenuClickFixer.py:182] - INFO - 创建强健动作: 修复可选中
2025-06-08 01:35:17,471 - MenuClickFixer - [MenuClickFixer.py:43] - INFO - 开始修复动作点击响应: 选择性寄存器操作
2025-06-08 01:35:17,471 - MenuClickFixer - [MenuClickFixer.py:83] - INFO - 动作点击响应修复完成: 选择性寄存器操作
2025-06-08 01:35:17,471 - MenuClickFixer - [MenuClickFixer.py:43] - INFO - 开始修复动作点击响应: 示例工具插件
2025-06-08 01:35:17,471 - MenuClickFixer - [MenuClickFixer.py:83] - INFO - 动作点击响应修复完成: 示例工具插件
2025-06-08 01:35:17,495 - MenuClickFixer - [MenuClickFixer.py:158] - DEBUG - 菜单即将隐藏: 修复菜单(&F)
2025-06-08 01:35:22,535 - MenuClickFixer - [MenuClickFixer.py:154] - DEBUG - 菜单即将显示: 修复菜单(&F)
2025-06-08 01:35:23,157 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 修复点击1
2025-06-08 01:35:23,167 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 修复点击1
2025-06-08 01:35:23,173 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 修复点击1
2025-06-08 01:35:23,188 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 修复点击1
2025-06-08 01:35:23,193 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 修复点击1
2025-06-08 01:35:23,209 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 修复点击1
2025-06-08 01:35:23,219 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 修复点击1
2025-06-08 01:35:23,230 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 修复点击1
2025-06-08 01:35:23,242 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 修复点击1
2025-06-08 01:35:23,258 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 修复点击1
2025-06-08 01:35:23,315 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 修复点击1
2025-06-08 01:35:23,327 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 修复点击1
2025-06-08 01:35:23,346 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 修复点击1
2025-06-08 01:35:23,358 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 修复点击1
2025-06-08 01:35:23,362 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 修复点击1
2025-06-08 01:35:23,383 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 修复点击1
2025-06-08 01:35:23,391 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 修复点击1
2025-06-08 01:35:23,398 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 修复点击1
2025-06-08 01:35:23,405 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 修复点击1
2025-06-08 01:35:23,412 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 修复点击1
2025-06-08 01:35:23,427 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 修复点击1
2025-06-08 01:35:23,452 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 修复点击1
2025-06-08 01:35:23,467 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 修复点击1
2025-06-08 01:35:23,483 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 修复点击1
2025-06-08 01:35:23,488 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 修复点击1
2025-06-08 01:35:23,494 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 修复点击1
2025-06-08 01:35:23,499 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 修复点击1
2025-06-08 01:35:23,509 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 修复点击1
2025-06-08 01:35:23,518 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 修复点击1
2025-06-08 01:35:23,520 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 修复点击1
2025-06-08 01:35:23,530 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 修复点击1
2025-06-08 01:35:24,175 - MenuClickFixer - [MenuClickFixer.py:158] - DEBUG - 菜单即将隐藏: 修复菜单(&F)
2025-06-08 01:35:24,364 - MenuClickFixer - [MenuClickFixer.py:154] - DEBUG - 菜单即将显示: 修复菜单(&F)
2025-06-08 01:35:24,628 - MenuClickFixer - [MenuClickFixer.py:158] - DEBUG - 菜单即将隐藏: 修复菜单(&F)
2025-06-08 01:35:25,055 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-06-08 01:35:25,065 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-06-08 01:35:25,071 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-06-08 01:35:25,079 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-06-08 01:35:25,085 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-06-08 01:35:25,091 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-06-08 01:35:25,099 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-06-08 01:35:25,112 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-06-08 01:35:25,117 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-06-08 01:35:25,127 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-06-08 01:35:25,148 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-06-08 01:35:25,163 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-06-08 01:35:25,184 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-06-08 01:35:25,200 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-06-08 01:35:25,662 - MenuClickFixer - [MenuClickFixer.py:90] - DEBUG - 鼠标悬停: 选择性寄存器操作
2025-06-08 01:35:25,887 - MenuClickFixer - [MenuClickFixer.py:96] - DEBUG - 动作切换: 选择性寄存器操作, checked=True
2025-06-08 01:35:25,893 - MenuClickFixer - [MenuClickFixer.py:55] - INFO - 🎯 增强处理器触发: 选择性寄存器操作, checked=True
2025-06-08 11:23:09,642 - PluginManager - [PluginManager.py:125] - INFO - 添加插件目录: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins
2025-06-08 11:23:09,643 - PluginManager - [PluginManager.py:158] - WARNING - 无法处理插件文件路径: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\clkin_control_plugin.py
2025-06-08 11:23:09,643 - PluginManager - [PluginManager.py:158] - WARNING - 无法处理插件文件路径: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\clk_output_plugin.py
2025-06-08 11:23:09,643 - PluginManager - [PluginManager.py:158] - WARNING - 无法处理插件文件路径: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\data_analysis_plugin.py
2025-06-08 11:23:09,643 - PluginManager - [PluginManager.py:158] - WARNING - 无法处理插件文件路径: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\example_tool_plugin.py
2025-06-08 11:23:09,644 - PluginManager - [PluginManager.py:158] - WARNING - 无法处理插件文件路径: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\performance_monitor_plugin.py
2025-06-08 11:23:09,644 - PluginManager - [PluginManager.py:158] - WARNING - 无法处理插件文件路径: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\pll_control_plugin.py
2025-06-08 11:23:09,644 - PluginManager - [PluginManager.py:158] - WARNING - 无法处理插件文件路径: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\selective_register_plugin.py
2025-06-08 11:23:09,644 - PluginManager - [PluginManager.py:158] - WARNING - 无法处理插件文件路径: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\set_modes_plugin.py
2025-06-08 11:23:09,644 - PluginManager - [PluginManager.py:158] - WARNING - 无法处理插件文件路径: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\sync_sysref_plugin.py
2025-06-08 11:23:09,644 - PluginManager - [PluginManager.py:158] - WARNING - 无法处理插件文件路径: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\config\selective_register_config.py
2025-06-08 11:24:01,176 - PluginManager - [PluginManager.py:125] - INFO - 添加插件目录: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins
2025-06-08 11:24:01,176 - PluginManager - [PluginManager.py:182] - DEBUG - 尝试加载插件模块: plugins.clkin_control_plugin (文件: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\clkin_control_plugin.py)
2025-06-08 11:24:01,177 - PluginManager - [PluginManager.py:211] - INFO - 发现插件: 时钟输入控制 v1.0.0
2025-06-08 11:24:01,178 - PluginManager - [PluginManager.py:182] - DEBUG - 尝试加载插件模块: plugins.clk_output_plugin (文件: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\clk_output_plugin.py)
2025-06-08 11:24:01,178 - PluginManager - [PluginManager.py:211] - INFO - 发现插件: 时钟输出 v1.0.0
2025-06-08 11:24:01,178 - PluginManager - [PluginManager.py:182] - DEBUG - 尝试加载插件模块: plugins.data_analysis_plugin (文件: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\data_analysis_plugin.py)
2025-06-08 11:24:01,179 - PluginManager - [PluginManager.py:211] - INFO - 发现插件: 数据分析器 v1.0.0
2025-06-08 11:24:01,179 - PluginManager - [PluginManager.py:182] - DEBUG - 尝试加载插件模块: plugins.example_tool_plugin (文件: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\example_tool_plugin.py)
2025-06-08 11:24:01,180 - PluginManager - [PluginManager.py:211] - INFO - 发现插件: 示例工具 v1.0.0
2025-06-08 11:24:01,180 - PluginManager - [PluginManager.py:182] - DEBUG - 尝试加载插件模块: plugins.performance_monitor_plugin (文件: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\performance_monitor_plugin.py)
2025-06-08 11:24:01,181 - PluginManager - [PluginManager.py:211] - INFO - 发现插件: 性能监控器 v1.0.0
2025-06-08 11:24:01,181 - PluginManager - [PluginManager.py:182] - DEBUG - 尝试加载插件模块: plugins.pll_control_plugin (文件: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\pll_control_plugin.py)
2025-06-08 11:24:01,182 - PluginManager - [PluginManager.py:211] - INFO - 发现插件: PLL控制 v1.0.0
2025-06-08 11:24:01,182 - PluginManager - [PluginManager.py:182] - DEBUG - 尝试加载插件模块: plugins.selective_register_plugin (文件: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\selective_register_plugin.py)
2025-06-08 11:24:01,183 - selective_register_config - [selective_register_config.py:43] - INFO - 成功加载插件配置文件: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\config\selective_register_plugin.json
2025-06-08 11:24:01,184 - PluginManager - [PluginManager.py:211] - INFO - 发现插件: 选择性寄存器操作 v1.0.1
2025-06-08 11:24:01,184 - PluginManager - [PluginManager.py:182] - DEBUG - 尝试加载插件模块: plugins.set_modes_plugin (文件: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\set_modes_plugin.py)
2025-06-08 11:24:01,184 - PluginManager - [PluginManager.py:211] - INFO - 发现插件: 模式设置 v1.0.0
2025-06-08 11:24:01,184 - PluginManager - [PluginManager.py:182] - DEBUG - 尝试加载插件模块: plugins.sync_sysref_plugin (文件: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\sync_sysref_plugin.py)
2025-06-08 11:24:01,185 - PluginManager - [PluginManager.py:211] - INFO - 发现插件: 同步系统参考 v1.0.0
2025-06-08 11:24:01,185 - PluginManager - [PluginManager.py:182] - DEBUG - 尝试加载插件模块: plugins.config.selective_register_config (文件: E:\FSJ04832\FSJReadOutput\version2\anotherCore2\plugins\config\selective_register_config.py)
2025-06-08 11:24:01,187 - clkin_control_plugin - [clkin_control_plugin.py:59] - INFO - 插件 '时钟输入控制' 初始化完成
2025-06-08 11:24:01,188 - PluginManager - [PluginManager.py:254] - INFO - 插件初始化成功: 时钟输入控制
2025-06-08 11:24:01,188 - clk_output_plugin - [clk_output_plugin.py:59] - INFO - 插件 '时钟输出' 初始化完成
2025-06-08 11:24:01,188 - PluginManager - [PluginManager.py:254] - INFO - 插件初始化成功: 时钟输出
2025-06-08 11:24:01,188 - data_analysis_plugin - [data_analysis_plugin.py:579] - INFO - 插件 '数据分析器' 初始化完成
2025-06-08 11:24:01,188 - PluginManager - [PluginManager.py:254] - INFO - 插件初始化成功: 数据分析器
2025-06-08 11:24:01,188 - example_tool_plugin - [example_tool_plugin.py:141] - INFO - 插件 '示例工具' 初始化完成
2025-06-08 11:24:01,188 - PluginManager - [PluginManager.py:254] - INFO - 插件初始化成功: 示例工具
2025-06-08 11:24:01,188 - performance_monitor_plugin - [performance_monitor_plugin.py:524] - INFO - 插件 '性能监控器' 初始化完成
2025-06-08 11:24:01,188 - PluginManager - [PluginManager.py:254] - INFO - 插件初始化成功: 性能监控器
2025-06-08 11:24:01,188 - pll_control_plugin - [pll_control_plugin.py:59] - INFO - 插件 'PLL控制' 初始化完成
2025-06-08 11:24:01,188 - PluginManager - [PluginManager.py:254] - INFO - 插件初始化成功: PLL控制
2025-06-08 11:24:01,188 - selective_register_plugin - [selective_register_plugin.py:942] - INFO - 插件 '选择性寄存器操作' 初始化完成
2025-06-08 11:24:01,189 - PluginManager - [PluginManager.py:254] - INFO - 插件初始化成功: 选择性寄存器操作
2025-06-08 11:24:01,189 - set_modes_plugin - [set_modes_plugin.py:59] - INFO - 插件 '模式设置' 初始化完成
2025-06-08 11:24:01,189 - PluginManager - [PluginManager.py:254] - INFO - 插件初始化成功: 模式设置
2025-06-08 11:24:01,189 - sync_sysref_plugin - [sync_sysref_plugin.py:59] - INFO - 插件 '同步系统参考' 初始化完成
2025-06-08 11:24:01,189 - PluginManager - [PluginManager.py:254] - INFO - 插件初始化成功: 同步系统参考
