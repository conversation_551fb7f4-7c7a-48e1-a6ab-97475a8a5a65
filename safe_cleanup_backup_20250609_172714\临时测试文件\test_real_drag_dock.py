#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实际项目拖拽停靠功能测试脚本
测试工具窗口和插件窗口的拖拽停靠功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
import logging

# 设置日志级别为INFO以查看拖拽调试信息
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_drag_dock_functionality():
    """测试拖拽停靠功能"""
    try:
        # 导入主窗口类
        from ui.windows.RegisterMainWindow import RegisterMainWindow

        # 创建应用实例
        app = QApplication(sys.argv)
        app.setStyle('Fusion')  # 设置统一的Fusion风格

        # 创建主窗口
        main_window = RegisterMainWindow()
        main_window.show()

        # 等待主窗口完全加载
        QTimer.singleShot(2000, lambda: create_test_windows(main_window))

        logger.info("=== 拖拽停靠功能测试启动 ===")
        logger.info("测试说明:")
        logger.info("1. 等待主窗口加载完成")
        logger.info("2. 自动创建测试工具窗口")
        logger.info("3. 手动拖拽窗口到主窗口底部30%区域进行测试")
        logger.info("4. 观察控制台日志输出")

        sys.exit(app.exec_())

    except Exception as e:
        logger.error(f"测试启动失败: {str(e)}")
        import traceback
        traceback.print_exc()

def create_test_windows(main_window):
    """创建测试窗口"""
    try:
        logger.info("=== 开始创建测试窗口 ===")

        # 获取窗口管理服务
        window_service = getattr(main_window, 'window_management_service', None)
        plugin_service = getattr(main_window, 'plugin_integration_service', None)

        if not window_service:
            logger.error("未找到窗口管理服务")
            return

        if not plugin_service:
            logger.error("未找到插件集成服务")
            return

        # 测试工具窗口拖拽停靠
        test_tool_window_drag_dock(window_service)

        # 测试插件窗口拖拽停靠
        test_plugin_window_drag_dock(plugin_service)

    except Exception as e:
        logger.error(f"创建测试窗口失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_tool_window_drag_dock(window_service):
    """测试工具窗口拖拽停靠"""
    try:
        logger.info("--- 测试工具窗口拖拽停靠 ---")
        
        # 获取可用的工具窗口列表
        available_tools = []
        if hasattr(window_service, 'managed_windows'):
            available_tools = list(window_service.managed_windows.keys())
        
        logger.info(f"可用工具窗口: {available_tools}")
        
        # 如果没有工具窗口，尝试创建一个
        if not available_tools:
            logger.info("没有可用的工具窗口，尝试创建测试窗口")
            # 这里可以添加创建特定工具窗口的代码
            return
        
        # 选择第一个工具窗口进行测试
        test_window_attr = available_tools[0]
        logger.info(f"选择工具窗口进行拖拽测试: {test_window_attr}")
        
        # 分离窗口（如果已停靠）
        window_service.detach_tool_window(test_window_attr)
        
        logger.info(f"工具窗口 {test_window_attr} 已分离，可以进行拖拽测试")
        logger.info("请手动拖拽该窗口到主窗口底部30%区域进行测试")
        
    except Exception as e:
        logger.error(f"测试工具窗口拖拽停靠失败: {str(e)}")

def test_plugin_window_drag_dock(plugin_service):
    """测试插件窗口拖拽停靠"""
    try:
        logger.info("--- 测试插件窗口拖拽停靠 ---")
        
        # 获取可用的插件列表
        available_plugins = []
        if hasattr(plugin_service, 'plugin_windows'):
            available_plugins = list(plugin_service.plugin_windows.keys())
        
        logger.info(f"当前活动插件窗口: {available_plugins}")
        
        # 尝试打开一个测试插件
        test_plugins = [
            'example_tool_plugin',
            'performance_monitor_plugin', 
            'selective_register_plugin',
            'data_analysis_plugin'
        ]
        
        opened_plugin = None
        for plugin_name in test_plugins:
            try:
                logger.info(f"尝试打开插件: {plugin_name}")
                plugin_service.open_plugin_window(plugin_name)
                opened_plugin = plugin_name
                break
            except Exception as e:
                logger.warning(f"无法打开插件 {plugin_name}: {str(e)}")
                continue
        
        if opened_plugin:
            logger.info(f"成功打开插件 {opened_plugin}，可以进行拖拽测试")
            logger.info("请手动拖拽该插件窗口到主窗口底部30%区域进行测试")
        else:
            logger.warning("无法打开任何测试插件")
        
    except Exception as e:
        logger.error(f"测试插件窗口拖拽停靠失败: {str(e)}")

def print_drag_dock_instructions():
    """打印拖拽停靠测试说明"""
    print("\n" + "="*60)
    print("拖拽停靠功能测试说明")
    print("="*60)
    print("1. 测试步骤:")
    print("   - 用鼠标左键点击并拖拽窗口")
    print("   - 将窗口拖拽到主窗口底部30%区域")
    print("   - 观察窗口标题是否变为'释放鼠标停靠到主界面'")
    print("   - 观察鼠标光标是否变为手型指针")
    print("   - 在停靠区域释放鼠标")
    print()
    print("2. 预期结果:")
    print("   - 窗口应该自动停靠到主界面标签页中")
    print("   - 控制台应该显示拖拽和停靠的调试信息")
    print()
    print("3. 调试信息标识:")
    print("   - [工具窗口拖拽] - 工具窗口拖拽相关信息")
    print("   - [拖拽调试] - 插件窗口拖拽相关信息")
    print("   - [停靠区域] - 停靠区域检测信息")
    print("   - 🎯 - 重要的拖拽停靠事件")
    print("="*60)

if __name__ == "__main__":
    print_drag_dock_instructions()
    test_drag_dock_functionality()
