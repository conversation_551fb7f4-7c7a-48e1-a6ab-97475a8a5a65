#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试现代化模式设置处理器
验证ModernSetModesHandler是否正常工作
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_modern_set_modes():
    """测试现代化模式设置处理器"""
    try:
        print("=" * 60)
        print("测试现代化模式设置处理器")
        print("=" * 60)
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 导入必要的模块
        from core.services.register.RegisterManager import RegisterManager
        from ui.handlers.ModernSetModesHandler import ModernSetModesHandler
        import json
        
        print("1. 加载寄存器配置...")
        # 加载寄存器配置
        config_path = os.path.join(os.path.dirname(__file__), 'lib', 'register.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        print(f"   ✓ 加载了 {len(registers_config)} 个寄存器配置")
        
        print("2. 创建RegisterManager...")
        register_manager = RegisterManager(registers_config)
        print(f"   ✓ 创建了RegisterManager，包含 {len(register_manager.register_objects)} 个寄存器对象")
        
        print("3. 创建现代化模式设置处理器...")
        try:
            modern_handler = ModernSetModesHandler(None, register_manager)
            print("   ✓ 成功创建ModernSetModesHandler")
            
            # 等待初始化完成
            import time
            time.sleep(0.2)
            
            # 检查控件映射
            print(f"   ✓ 构建了 {len(modern_handler.widget_register_map)} 个控件映射")
            
        except Exception as e:
            print(f"   ❌ 创建ModernSetModesHandler失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
        print("4. 测试模式设置功能...")
        
        # 获取初始状态
        initial_status = modern_handler.get_current_status()
        print(f"   初始状态: {initial_status}")
        
        # 测试DualLoop模式
        print("   测试DualLoop模式...")
        try:
            modern_handler.set_mode("DualLoop")
            
            # 检查关键寄存器值
            pll1_pd = register_manager.get_bit_field_value("0x50", "PLL1_PD")
            pll2_pd = register_manager.get_bit_field_value("0x83", "PLL2_PD")
            vco_pd = register_manager.get_bit_field_value("0x50", "VCO_PD")
            
            print(f"   DualLoop模式设置后:")
            print(f"     PLL1_PD: {pll1_pd} (期望: 0)")
            print(f"     PLL2_PD: {pll2_pd} (期望: 0)")
            print(f"     VCO_PD: {vco_pd} (期望: 0)")
            
            if pll1_pd == 0 and pll2_pd == 0 and vco_pd == 0:
                print("   ✓ DualLoop模式设置成功")
            else:
                print("   ❌ DualLoop模式设置失败")
                
        except Exception as e:
            print(f"   ❌ DualLoop模式设置出错: {str(e)}")
        
        # 测试SingleLoop模式
        print("   测试SingleLoop模式...")
        try:
            modern_handler.set_mode("SingleLoop")
            
            # 检查关键寄存器值
            pll1_pd = register_manager.get_bit_field_value("0x50", "PLL1_PD")
            pll2_pd = register_manager.get_bit_field_value("0x83", "PLL2_PD")
            vco_pd = register_manager.get_bit_field_value("0x50", "VCO_PD")
            
            print(f"   SingleLoop模式设置后:")
            print(f"     PLL1_PD: {pll1_pd} (期望: 1)")
            print(f"     PLL2_PD: {pll2_pd} (期望: 0)")
            print(f"     VCO_PD: {vco_pd} (期望: 0)")
            
            if pll1_pd == 1 and pll2_pd == 0 and vco_pd == 0:
                print("   ✓ SingleLoop模式设置成功")
            else:
                print("   ❌ SingleLoop模式设置失败")
                
        except Exception as e:
            print(f"   ❌ SingleLoop模式设置出错: {str(e)}")
        
        print("5. 测试状态获取...")
        try:
            final_status = modern_handler.get_current_status()
            print(f"   最终状态: {final_status}")
            
            # 验证状态与寄存器值一致
            expected_pll1_enabled = not register_manager.get_bit_field_value("0x50", "PLL1_PD")
            expected_pll2_enabled = not register_manager.get_bit_field_value("0x83", "PLL2_PD")
            expected_vco_enabled = not register_manager.get_bit_field_value("0x50", "VCO_PD")
            
            if (final_status.get("pll1_enabled") == expected_pll1_enabled and
                final_status.get("pll2_enabled") == expected_pll2_enabled and
                final_status.get("vco_enabled") == expected_vco_enabled):
                print("   ✓ 状态获取正确")
            else:
                print("   ❌ 状态获取不正确")
                
        except Exception as e:
            print(f"   ❌ 状态获取出错: {str(e)}")
        
        print("6. 测试信号连接...")
        try:
            # 测试信号是否正确连接
            signal_connected = False
            
            def on_mode_changed(mode_name):
                nonlocal signal_connected
                signal_connected = True
                print(f"   收到模式变化信号: {mode_name}")
            
            modern_handler.mode_changed.connect(on_mode_changed)
            
            # 触发一个模式变化
            modern_handler.set_mode("DualLoop")
            
            # 等待信号处理
            app.processEvents()
            
            if signal_connected:
                print("   ✓ 模式变化信号正常")
            else:
                print("   ❌ 模式变化信号未触发")
                
        except Exception as e:
            print(f"   ❌ 信号测试出错: {str(e)}")
        
        print("\n" + "=" * 60)
        print("🎉 现代化模式设置处理器测试完成！")
        print("=" * 60)
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_modern_set_modes()
    sys.exit(0 if success else 1)
