"""
主窗口UI组件管理器
负责创建和管理主窗口的UI布局和组件
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QStatusBar,
                           QTabWidget, QProgressBar, QLabel, QPushButton, QSplitter)
from PyQt5.QtCore import Qt
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class MainWindowUI:
    """主窗口UI组件管理器"""
    
    def __init__(self, main_window):
        """初始化UI管理器
        
        Args:
            main_window: RegisterMainWindow实例
        """
        self.main_window = main_window
        
    def setup_ui(self):
        """设置主窗口UI"""
        self._create_central_widget()
        self._create_status_bar()
        self._create_progress_bar()
        self._create_menu_and_toolbar()
        
    def _create_central_widget(self):
        """创建中央窗口部件"""
        # 创建中央窗口部件
        central_widget = QWidget()
        self.main_window.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)

        # 创建上半部分分割器（寄存器树和寄存器详情）
        top_splitter = self._create_top_layout()
        main_layout.addWidget(top_splitter, 1)  # 调整占比为1，给上半部分更少空间

        # 创建工具标签页容器
        self._create_tools_tab_widget(main_layout)
        
    def _create_top_layout(self):
        """创建顶部布局（寄存器树和详情区域）"""
        # 使用QSplitter替代QHBoxLayout，提供稳定的分割条，防止寄存器切换时布局抖动
        splitter = QSplitter(Qt.Horizontal)

        # 创建左侧树区域
        left_widget = self._create_left_widget()
        splitter.addWidget(left_widget)

        # 创建右侧区域
        right_widget = self._create_right_widget()
        splitter.addWidget(right_widget)

        # 设置分割比例：左侧树区域占1份，右侧详情区域占4份
        # 这样可以确保TreeWidget有足够的宽度显示完整内容，同时给TableWidget更多空间
        splitter.setSizes([200, 800])  # 具体像素值，更稳定

        # 设置分割条的一些属性，提高用户体验
        splitter.setChildrenCollapsible(False)  # 防止子控件被完全折叠
        splitter.setHandleWidth(6)  # 设置分割条宽度，便于用户拖拽

        logger.info("MainWindowUI: 使用QSplitter创建稳定的TreeWidget和TableWidget分割布局")

        return splitter
        
    def _create_left_widget(self):
        """创建左侧树区域"""
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建寄存器树
        tree_widget = self.main_window.tree_handler.create_tree_widget()
        left_layout.addWidget(tree_widget)
        
        return left_widget
        
    def _create_right_widget(self):
        """创建右侧详情区域"""
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)

        # 检查是否使用现代化处理器
        if self._is_using_modern_handlers():
            # 分别添加IO控制区域和寄存器表格，设置合适的权重

            # 添加IO控制区域（固定高度，权重0）
            io_widget = self.main_window.io_handler.get_io_widget()
            right_layout.addWidget(io_widget, 0)  # 权重0，不拉伸
            self.main_window.io_handler.show()
            io_widget.show()

            # 添加寄存器表格（占用大部分空间，权重1）
            table_widget = self.main_window.table_handler
            right_layout.addWidget(table_widget, 1)  # 权重1，占用主要空间
            self.main_window.table_handler.show()

        else:
            # 使用传统处理器
            # 添加寄存器值输入/输出控件
            rx_value_widget = self.main_window.io_handler.create_rx_value_layout()
            right_layout.addWidget(rx_value_widget, 0)  # 固定高度

            # 直接添加寄存器表格
            bit_field_table = self.main_window.table_handler.create_bit_field_table()
            right_layout.addWidget(bit_field_table, 1)  # 占用主要空间

        # 无论使用哪种处理器，都需要底部的按钮布局（固定高度，权重0）
        button_layout = self._create_button_layout()
        right_layout.addLayout(button_layout, 0)  # 权重0，不拉伸

        return right_widget



    def _is_using_modern_handlers(self):
        """检查是否使用现代化处理器"""
        try:
            # 检查IO处理器类型
            from ui.handlers.ModernRegisterIOHandler import ModernRegisterIOHandler
            from ui.handlers.ModernRegisterTableHandler import ModernRegisterTableHandler
            from utils.Log import get_module_logger

            logger = get_module_logger(__name__)

            io_is_modern = isinstance(self.main_window.io_handler, ModernRegisterIOHandler)
            table_is_modern = isinstance(self.main_window.table_handler, ModernRegisterTableHandler)

            logger.info(f"MainWindowUI: 检查现代化处理器 - IO: {io_is_modern}, Table: {table_is_modern}")
            logger.info(f"MainWindowUI: IO处理器类型: {type(self.main_window.io_handler)}")
            logger.info(f"MainWindowUI: Table处理器类型: {type(self.main_window.table_handler)}")

            return io_is_modern and table_is_modern
        except Exception as e:
            logger.error(f"MainWindowUI: 检查现代化处理器时出错: {str(e)}")
            return False

    def _create_button_layout(self):
        """创建按钮布局"""
        layout = QHBoxLayout()
        
        # 定义按钮配置
        buttons = [
            ("read", "Read", self.main_window._handle_read_button_click),
            ("write", "Write", self.main_window._handle_write_button_click),
            ("read_all", "Read All", self.main_window._handle_read_all_requested),
            ("write_all", "Write All", self.main_window._handle_write_all_requested),
            ("dumpall", "DumpAll", self.main_window.dumpall_button_clicked),
            ("save", "Save", self.main_window._handle_save_requested),
            ("load", "Load", self.main_window._handle_load_requested)
        ]
        
        # 创建并配置按钮
        for attr_name, text, slot in buttons:
            button = QPushButton(text)
            button.clicked.connect(slot)
            setattr(self.main_window, f"{attr_name}_btn", button)
            layout.addWidget(button)
        
        # 存储按钮引用供io_handler使用（如果支持的话）
        if hasattr(self.main_window.io_handler, 'register_buttons'):
            self.main_window.io_handler.register_buttons(
                self.main_window.read_btn, self.main_window.write_btn,
                self.main_window.read_all_btn, self.main_window.write_all_btn,
                self.main_window.dumpall_btn, self.main_window.save_btn,
                self.main_window.load_btn
            )
        
        return layout
        
    def _create_tools_tab_widget(self, main_layout):
        """创建工具标签页容器"""
        self.main_window.tools_tab_widget = QTabWidget()
        self.main_window.tools_tab_widget.setTabsClosable(True)
        self.main_window.tools_tab_widget.tabCloseRequested.connect(
            self.main_window.close_tool_tab
        )

        # 添加右键菜单支持
        self._add_tab_context_menu()

        self.main_window.tools_tab_widget.setVisible(False)
        main_layout.addWidget(self.main_window.tools_tab_widget, 2)  # 调整占比为2，给工具标签页更多空间

    def _add_tab_context_menu(self):
        """为标签页添加右键菜单"""
        try:
            from PyQt5.QtCore import Qt
            from PyQt5.QtWidgets import QMenu, QAction

            # 设置标签页的上下文菜单策略
            self.main_window.tools_tab_widget.setContextMenuPolicy(Qt.CustomContextMenu)

            def show_tab_context_menu(position):
                """显示标签页右键菜单"""
                try:
                    # 获取点击的标签页索引
                    tab_bar = self.main_window.tools_tab_widget.tabBar()
                    tab_index = tab_bar.tabAt(position)

                    if tab_index == -1:
                        return  # 没有点击在标签页上

                    # 获取标签页信息
                    tab_text = self.main_window.tools_tab_widget.tabText(tab_index)
                    container = self.main_window.tools_tab_widget.widget(tab_index)

                    # 检查是否是工具窗口
                    if hasattr(container, 'window_attr'):
                        window_attr = container.window_attr

                        # 创建右键菜单
                        menu = QMenu(self.main_window.tools_tab_widget)

                        # 分离窗口
                        undock_action = QAction('分离窗口', self.main_window.tools_tab_widget)
                        undock_action.triggered.connect(lambda: self._undock_tool_window(window_attr))
                        menu.addAction(undock_action)

                        menu.addSeparator()

                        # 关闭窗口
                        close_action = QAction('关闭窗口', self.main_window.tools_tab_widget)
                        close_action.triggered.connect(lambda: self.main_window.close_tool_tab(tab_index))
                        menu.addAction(close_action)

                        # 显示菜单
                        menu.exec_(self.main_window.tools_tab_widget.mapToGlobal(position))

                except Exception as e:
                    from utils.Log import get_module_logger
                    logger = get_module_logger(__name__)
                    logger.error(f"显示标签页右键菜单失败: {str(e)}")

            # 连接右键菜单信号
            self.main_window.tools_tab_widget.customContextMenuRequested.connect(show_tab_context_menu)

        except Exception as e:
            from utils.Log import get_module_logger
            logger = get_module_logger(__name__)
            logger.error(f"添加标签页右键菜单失败: {str(e)}")

    def _undock_tool_window(self, window_attr):
        """分离工具窗口"""
        try:
            if hasattr(self.main_window, 'window_service'):
                self.main_window.window_service.undock_tool_window(window_attr)
            else:
                from utils.Log import get_module_logger
                logger = get_module_logger(__name__)
                logger.warning("窗口管理服务不可用，无法分离工具窗口")
        except Exception as e:
            from utils.Log import get_module_logger
            logger = get_module_logger(__name__)
            logger.error(f"分离工具窗口失败: {str(e)}")
        
    def _create_status_bar(self):
        """创建状态栏"""
        self.main_window.status_bar = QStatusBar()
        self.main_window.setStatusBar(self.main_window.status_bar)

        # 创建状态栏标签
        self.main_window.status_mode_label = QLabel("模式: 未知")
        self.main_window.status_port_label = QLabel("端口: 未知")
        self.main_window.status_bar.addPermanentWidget(self.main_window.status_mode_label)
        self.main_window.status_bar.addPermanentWidget(self.main_window.status_port_label)
        
    def _create_progress_bar(self):
        """创建进度条"""
        self.main_window.loading_progress = QProgressBar()
        self.main_window.loading_progress.setRange(0, 0)
        self.main_window.loading_progress.setVisible(False)

        # 应用绿色样式
        self._apply_progress_bar_style(self.main_window.loading_progress)

        # 将进度条添加到中央窗口的主布局中
        central_widget = self.main_window.centralWidget()
        if central_widget and central_widget.layout():
            central_widget.layout().addWidget(self.main_window.loading_progress)
        
    def _create_menu_and_toolbar(self):
        """创建菜单和工具栏"""
        # 导入菜单管理器并创建菜单
        from ui.components.MenuManager import MenuManager
        self.menu_manager = MenuManager(self.main_window)
        self.menu_manager.create_menu_and_toolbar()

    def _apply_progress_bar_style(self, progress_bar):
        """为进度条应用绿色样式"""
        try:
            from ui.styles.ProgressBarStyleManager import apply_green_progress_style
            apply_green_progress_style(progress_bar, "default")
        except ImportError:
            # 如果样式管理器不可用，使用内联样式
            progress_bar.setStyleSheet("""
                QProgressBar {
                    border: 2px solid #C0C0C0;
                    border-radius: 5px;
                    background-color: #F0F0F0;
                    text-align: center;
                    font-weight: bold;
                    color: #333333;
                }

                QProgressBar::chunk {
                    background-color: qlineargradient(
                        x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #7ED321,
                        stop: 0.5 #5CB85C,
                        stop: 1 #4CAF50
                    );
                    border-radius: 3px;
                    margin: 1px;
                }
            """)
        except Exception as e:
            logger.warning(f"应用进度条样式失败: {str(e)}")
