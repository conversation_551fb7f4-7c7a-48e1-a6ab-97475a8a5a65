#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
打包系统测试脚本
测试打包管理系统的完整性
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
packaging_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
project_root = os.path.dirname(packaging_root)
sys.path.insert(0, project_root)

def test_directory_structure():
    """测试目录结构"""
    print("=" * 60)
    print("测试打包目录结构")
    print("=" * 60)
    
    packaging_dir = Path(packaging_root)
    
    required_dirs = [
        'scripts',
        'tools', 
        'config',
        'launchers',
        'tests',
        'docs'
    ]
    
    required_files = [
        'README.md',
        'package.py',
        'scripts/build_exe.py',
        'scripts/build_safe.py',
        'scripts/build.spec',
        'tools/start_gui.py',
        'tools/list_versions.py',
        'tools/clean_old_versions.py',
        'config/version.json',
        'config/packaging_config.json',
        'launchers/启动打包工具.bat'
    ]
    
    print("检查目录结构...")
    
    # 检查目录
    for dir_name in required_dirs:
        dir_path = packaging_dir / dir_name
        if dir_path.exists():
            print(f"✓ 目录存在: {dir_name}")
        else:
            print(f"❌ 目录缺失: {dir_name}")
    
    print("\n检查关键文件...")
    
    # 检查文件
    for file_path in required_files:
        full_path = packaging_dir / file_path
        if full_path.exists():
            print(f"✓ 文件存在: {file_path}")
        else:
            print(f"❌ 文件缺失: {file_path}")
    
    return True

def test_config_files():
    """测试配置文件"""
    print("\n" + "=" * 60)
    print("测试配置文件")
    print("=" * 60)
    
    try:
        import json
        
        # 测试version.json
        version_file = Path(packaging_root) / 'config' / 'version.json'
        if version_file.exists():
            with open(version_file, 'r', encoding='utf-8') as f:
                version_data = json.load(f)
            print("✓ version.json 格式正确")
            print(f"  当前版本: {version_data.get('version', {})}")
        else:
            print("❌ version.json 不存在")
        
        # 测试packaging_config.json
        config_file = Path(packaging_root) / 'config' / 'packaging_config.json'
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            print("✓ packaging_config.json 格式正确")
            print(f"  打包系统版本: {config_data.get('packaging_info', {}).get('version', 'N/A')}")
        else:
            print("❌ packaging_config.json 不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件测试失败: {str(e)}")
        return False

def test_scripts():
    """测试脚本文件"""
    print("\n" + "=" * 60)
    print("测试脚本文件")
    print("=" * 60)
    
    scripts_dir = Path(packaging_root) / 'scripts'
    
    scripts = [
        'build_exe.py',
        'build_safe.py'
    ]
    
    for script_name in scripts:
        script_path = scripts_dir / script_name
        if script_path.exists():
            try:
                # 尝试导入脚本检查语法
                with open(script_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查关键函数
                if 'def main(' in content:
                    print(f"✓ {script_name} 语法正确，包含main函数")
                else:
                    print(f"⚠️  {script_name} 可能缺少main函数")
                    
            except Exception as e:
                print(f"❌ {script_name} 语法错误: {str(e)}")
        else:
            print(f"❌ {script_name} 不存在")
    
    return True

def test_tools():
    """测试工具脚本"""
    print("\n" + "=" * 60)
    print("测试工具脚本")
    print("=" * 60)
    
    tools_dir = Path(packaging_root) / 'tools'
    
    tools = [
        'start_gui.py',
        'list_versions.py', 
        'clean_old_versions.py'
    ]
    
    for tool_name in tools:
        tool_path = tools_dir / tool_name
        if tool_path.exists():
            try:
                with open(tool_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if 'def main(' in content or 'if __name__ == "__main__"' in content:
                    print(f"✓ {tool_name} 可执行")
                else:
                    print(f"⚠️  {tool_name} 可能不可执行")
                    
            except Exception as e:
                print(f"❌ {tool_name} 读取失败: {str(e)}")
        else:
            print(f"❌ {tool_name} 不存在")
    
    return True

def test_package_entry():
    """测试打包入口"""
    print("\n" + "=" * 60)
    print("测试打包入口")
    print("=" * 60)
    
    package_script = Path(packaging_root) / 'package.py'
    
    if package_script.exists():
        try:
            with open(package_script, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查关键函数
            required_functions = [
                'def show_help(',
                'def run_gui(',
                'def run_build(',
                'def run_list(',
                'def run_clean(',
                'def main('
            ]
            
            missing_functions = []
            for func in required_functions:
                if func not in content:
                    missing_functions.append(func)
            
            if not missing_functions:
                print("✓ package.py 包含所有必需函数")
            else:
                print(f"⚠️  package.py 缺少函数: {missing_functions}")
            
            return True
            
        except Exception as e:
            print(f"❌ package.py 读取失败: {str(e)}")
            return False
    else:
        print("❌ package.py 不存在")
        return False

def test_project_integration():
    """测试项目集成"""
    print("\n" + "=" * 60)
    print("测试项目集成")
    print("=" * 60)
    
    try:
        # 测试能否导入项目模块
        from core.services.version.VersionService import VersionService
        print("✓ 可以导入版本服务")
        
        # 测试版本服务
        vs = VersionService.instance()
        version = vs.get_version_string()
        print(f"✓ 版本服务正常: {version}")
        
        # 测试能否找到主文件
        main_file = Path(project_root) / 'main.py'
        if main_file.exists():
            print("✓ 主程序文件存在")
        else:
            print("❌ 主程序文件不存在")
        
        # 测试releases目录
        releases_dir = Path(project_root) / 'releases'
        if releases_dir.exists():
            print("✓ releases目录存在")
        else:
            print("⚠️  releases目录不存在 (首次构建时会创建)")
        
        return True
        
    except Exception as e:
        print(f"❌ 项目集成测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔍 打包系统完整性测试")
    print("测试打包管理系统的各个组件...")
    
    tests = [
        ("目录结构", test_directory_structure),
        ("配置文件", test_config_files),
        ("脚本文件", test_scripts),
        ("工具脚本", test_tools),
        ("打包入口", test_package_entry),
        ("项目集成", test_project_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 出现异常: {str(e)}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20s} : {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！打包系统完整可用。")
        print("\n📋 使用方法:")
        print("- cd packaging")
        print("- python package.py gui          # 启动图形界面")
        print("- python package.py build build  # 命令行构建")
        print("- python package.py list         # 查看版本历史")
    else:
        print(f"\n💥 {total - passed} 个测试失败！请检查打包系统配置。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
