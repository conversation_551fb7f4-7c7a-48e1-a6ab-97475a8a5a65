('E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\build\\build\\FSJ04832_RegisterTool_v1.0.9.0.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\build\\build\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\build\\build\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\build\\build\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\build\\build\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\build\\build\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\build\\build\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('main',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\main.py',
   'PYSOURCE'),
  ('python38.dll', 'd:\\program files\\python38\\python38.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libegl.dll',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libegl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libglesv2.dll',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libglesv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\printsupport\\windowsprintersupport.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\printsupport\\windowsprintersupport.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('_ctypes.pyd',
   'd:\\program files\\python38\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'd:\\program files\\python38\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd', 'd:\\program files\\python38\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd',
   'd:\\program files\\python38\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'd:\\program files\\python38\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('pyexpat.pyd',
   'd:\\program files\\python38\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'd:\\program files\\python38\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_lzma.pyd', 'd:\\program files\\python38\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'd:\\program files\\python38\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_hashlib.pyd',
   'd:\\program files\\python38\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_queue.pyd', 'd:\\program files\\python38\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp38-win_amd64.pyd',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\sip.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtPrintSupport.pyd',
   'd:\\program files\\python38\\lib\\site-packages\\PyQt5\\QtPrintSupport.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'd:\\program files\\python38\\lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'd:\\program files\\python38\\lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'd:\\program files\\python38\\lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'd:\\program files\\python38\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'D:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5PrintSupport.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5PrintSupport.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('libffi-7.dll', 'd:\\program files\\python38\\DLLs\\libffi-7.dll', 'BINARY'),
  ('libssl-1_1.dll',
   'd:\\program files\\python38\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'd:\\program files\\python38\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('python3.dll', 'd:\\program files\\python38\\python3.dll', 'BINARY'),
  ('ucrtbase.dll', 'C:\\windows\\system32\\ucrtbase.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'd:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\windows\\system32\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\windows\\system32\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('config\\default.json',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\config\\default.json',
   'DATA'),
  ('gui\\cascaded_0_delay_dual_loop.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\gui\\cascaded_0_delay_dual_loop.jpg',
   'DATA'),
  ('gui\\clkInput.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\gui\\clkInput.jpg',
   'DATA'),
  ('gui\\dual_loop_0_delay_cascaded.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\gui\\dual_loop_0_delay_cascaded.jpg',
   'DATA'),
  ('gui\\dual_loop_mode.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\gui\\dual_loop_mode.jpg',
   'DATA'),
  ('gui\\dual_loop_mode_calculate.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\gui\\dual_loop_mode_calculate.jpg',
   'DATA'),
  ('gui\\dualloop0_delay_nested.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\gui\\dualloop0_delay_nested.jpg',
   'DATA'),
  ('gui\\nested_0_delay_dual_loop_mode.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\gui\\nested_0_delay_dual_loop_mode.jpg',
   'DATA'),
  ('gui\\pll.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\gui\\pll.jpg',
   'DATA'),
  ('gui\\singleLoop.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\gui\\singleLoop.jpg',
   'DATA'),
  ('gui\\systemREF.jpg',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\gui\\systemREF.jpg',
   'DATA'),
  ('images\\logo.ico',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\images\\logo.ico',
   'DATA'),
  ('lib\\register.json',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\lib\\register.json',
   'DATA'),
  ('packaging\\config\\version.json',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\packaging\\config\\version.json',
   'DATA'),
  ('plugins\\__pycache__\\IOsPagePlugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\__pycache__\\IOsPagePlugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\clk_output_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\__pycache__\\clk_output_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\clkin_control_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\__pycache__\\clkin_control_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\data_analysis_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\__pycache__\\data_analysis_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\drag_dock_test_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\__pycache__\\drag_dock_test_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\example_tool_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\__pycache__\\example_tool_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\info_panel_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\__pycache__\\info_panel_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\performance_monitor_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\__pycache__\\performance_monitor_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\pll_control_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\__pycache__\\pll_control_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\selective_register_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\__pycache__\\selective_register_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\set_modes_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\__pycache__\\set_modes_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\__pycache__\\sync_sysref_plugin.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\__pycache__\\sync_sysref_plugin.cpython-38.pyc',
   'DATA'),
  ('plugins\\clk_output_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\clk_output_plugin.py',
   'DATA'),
  ('plugins\\clkin_control_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\clkin_control_plugin.py',
   'DATA'),
  ('plugins\\config\\__pycache__\\selective_register_config.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\config\\__pycache__\\selective_register_config.cpython-38.pyc',
   'DATA'),
  ('plugins\\config\\selective_register_config.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\config\\selective_register_config.py',
   'DATA'),
  ('plugins\\data_analysis_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\data_analysis_plugin.py',
   'DATA'),
  ('plugins\\example_tool_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\example_tool_plugin.py',
   'DATA'),
  ('plugins\\info_panel_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\info_panel_plugin.py',
   'DATA'),
  ('plugins\\performance_monitor_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\performance_monitor_plugin.py',
   'DATA'),
  ('plugins\\pll_control_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\pll_control_plugin.py',
   'DATA'),
  ('plugins\\selective_register_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\selective_register_plugin.py',
   'DATA'),
  ('plugins\\set_modes_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\set_modes_plugin.py',
   'DATA'),
  ('plugins\\sync_sysref_plugin.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\plugins\\sync_sysref_plugin.py',
   'DATA'),
  ('set_log_level.bat',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\set_log_level.bat',
   'DATA'),
  ('ui\\forms\\ClkOutputs.ui',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\ClkOutputs.ui',
   'DATA'),
  ('ui\\forms\\PLL1_2.ui',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\PLL1_2.ui',
   'DATA'),
  ('ui\\forms\\Ui_ClkOutputs.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\Ui_ClkOutputs.py',
   'DATA'),
  ('ui\\forms\\Ui_PLL1_2.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\Ui_PLL1_2.py',
   'DATA'),
  ('ui\\forms\\Ui_clkinControl.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\Ui_clkinControl.py',
   'DATA'),
  ('ui\\forms\\Ui_setModes.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\Ui_setModes.py',
   'DATA'),
  ('ui\\forms\\Ui_syncSysref.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\Ui_syncSysref.py',
   'DATA'),
  ('ui\\forms\\__pycache__\\Ui_ClkOutputs.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\__pycache__\\Ui_ClkOutputs.cpython-313.pyc',
   'DATA'),
  ('ui\\forms\\__pycache__\\Ui_ClkOutputs.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\__pycache__\\Ui_ClkOutputs.cpython-38.pyc',
   'DATA'),
  ('ui\\forms\\__pycache__\\Ui_PLL1_2.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\__pycache__\\Ui_PLL1_2.cpython-313.pyc',
   'DATA'),
  ('ui\\forms\\__pycache__\\Ui_PLL1_2.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\__pycache__\\Ui_PLL1_2.cpython-38.pyc',
   'DATA'),
  ('ui\\forms\\__pycache__\\Ui_clkinControl.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\__pycache__\\Ui_clkinControl.cpython-313.pyc',
   'DATA'),
  ('ui\\forms\\__pycache__\\Ui_clkinControl.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\__pycache__\\Ui_clkinControl.cpython-38.pyc',
   'DATA'),
  ('ui\\forms\\__pycache__\\Ui_setModes.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\__pycache__\\Ui_setModes.cpython-313.pyc',
   'DATA'),
  ('ui\\forms\\__pycache__\\Ui_setModes.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\__pycache__\\Ui_setModes.cpython-38.pyc',
   'DATA'),
  ('ui\\forms\\__pycache__\\Ui_syncSysref.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\__pycache__\\Ui_syncSysref.cpython-313.pyc',
   'DATA'),
  ('ui\\forms\\__pycache__\\Ui_syncSysref.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\__pycache__\\Ui_syncSysref.cpython-38.pyc',
   'DATA'),
  ('ui\\forms\\clkinControl.ui',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\clkinControl.ui',
   'DATA'),
  ('ui\\forms\\log\\app.log',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\log\\app.log',
   'DATA'),
  ('ui\\forms\\setModes.ui',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\setModes.ui',
   'DATA'),
  ('ui\\forms\\syncSysref.ui',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\forms\\syncSysref.ui',
   'DATA'),
  ('ui\\resources\\PLL1_2_rc.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\resources\\PLL1_2_rc.py',
   'DATA'),
  ('ui\\resources\\__pycache__\\PLL1_2_rc.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\resources\\__pycache__\\PLL1_2_rc.cpython-313.pyc',
   'DATA'),
  ('ui\\resources\\__pycache__\\PLL1_2_rc.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\resources\\__pycache__\\PLL1_2_rc.cpython-38.pyc',
   'DATA'),
  ('ui\\resources\\__pycache__\\clkinControl_rc.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\resources\\__pycache__\\clkinControl_rc.cpython-313.pyc',
   'DATA'),
  ('ui\\resources\\__pycache__\\clkinControl_rc.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\resources\\__pycache__\\clkinControl_rc.cpython-38.pyc',
   'DATA'),
  ('ui\\resources\\__pycache__\\clkoutput_rc.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\resources\\__pycache__\\clkoutput_rc.cpython-313.pyc',
   'DATA'),
  ('ui\\resources\\__pycache__\\clkoutput_rc.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\resources\\__pycache__\\clkoutput_rc.cpython-38.pyc',
   'DATA'),
  ('ui\\resources\\__pycache__\\setModes_rc.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\resources\\__pycache__\\setModes_rc.cpython-313.pyc',
   'DATA'),
  ('ui\\resources\\__pycache__\\setModes_rc.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\resources\\__pycache__\\setModes_rc.cpython-38.pyc',
   'DATA'),
  ('ui\\resources\\__pycache__\\syncSysref_rc.cpython-313.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\resources\\__pycache__\\syncSysref_rc.cpython-313.pyc',
   'DATA'),
  ('ui\\resources\\__pycache__\\syncSysref_rc.cpython-38.pyc',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\resources\\__pycache__\\syncSysref_rc.cpython-38.pyc',
   'DATA'),
  ('ui\\resources\\clkinControl_rc.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\resources\\clkinControl_rc.py',
   'DATA'),
  ('ui\\resources\\clkoutput_rc.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\resources\\clkoutput_rc.py',
   'DATA'),
  ('ui\\resources\\setModes_rc.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\resources\\setModes_rc.py',
   'DATA'),
  ('ui\\resources\\syncSysref_rc.py',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\resources\\syncSysref_rc.py',
   'DATA'),
  ('version.json',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\packaging\\config\\version.json',
   'DATA'),
  ('base_library.zip',
   'E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\build\\build\\base_library.zip',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'D:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA')],
 'python38.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
