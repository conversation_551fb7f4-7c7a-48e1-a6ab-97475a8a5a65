{"mcpServers": {"gitee": {"command": "npx", "args": ["-y", "@gitee/mcp-gitee@latest"], "env": {"GITEE_API_BASE": "https://gitee.com/api/v5", "GITEE_ACCESS_TOKEN": "<your personal access token>"}}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": "6000"}}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {"MEMORY_FILE_PATH": "/path/to/custom/memory.json"}}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}}}