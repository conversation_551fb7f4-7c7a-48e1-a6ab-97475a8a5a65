#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
FSJ04832寄存器配置工具 - 打包和部署测试
测试exe打包、配置文件处理、版本管理等部署相关功能
"""

import sys
import os
import unittest
import json
import shutil
import subprocess
from pathlib import Path
from unittest.mock import Mock, patch

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from test_config import TestConfig
from test_utils import TestUtils

class PackagingDeploymentTest(unittest.TestCase):
    """打包和部署测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        cls.app = TestUtils.get_test_app()
        cls.test_config = TestConfig()
        cls.project_root = Path(__file__).parent.parent
        
    def setUp(self):
        """每个测试方法前的初始化"""
        self.temp_dir = Path("test_temp")
        self.temp_dir.mkdir(exist_ok=True)
        
    def tearDown(self):
        """每个测试方法后的清理"""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir, ignore_errors=True)
            
    def test_01_packaging_structure_validation(self):
        """测试打包结构验证"""
        print("\n=== 测试打包结构验证 ===")
        
        try:
            # 检查打包相关文件
            packaging_files = [
                "packaging/build_exe.py",
                "packaging/setup.py", 
                "packaging/requirements.txt",
                "packaging/launcher/launcher.bat"
            ]
            
            existing_files = []
            missing_files = []
            
            for file_path in packaging_files:
                full_path = self.project_root / file_path
                if full_path.exists():
                    existing_files.append(file_path)
                    print(f"✅ {file_path} - 存在")
                else:
                    missing_files.append(file_path)
                    print(f"❌ {file_path} - 缺失")
                    
            print(f"\n📊 打包文件统计:")
            print(f"   存在: {len(existing_files)}/{len(packaging_files)}")
            print(f"   缺失: {len(missing_files)}")
            
            # 至少应该有基本的打包文件
            self.assertGreaterEqual(len(existing_files), 2, "缺少关键打包文件")
            
            print("✅ 打包结构验证测试通过")
            
        except Exception as e:
            print(f"❌ 打包结构验证测试失败: {str(e)}")
            raise
            
    def test_02_configuration_file_handling(self):
        """测试配置文件处理"""
        print("\n=== 测试配置文件处理 ===")
        
        try:
            # 检查配置文件
            config_files = [
                "config/default.json",
                "config/register.json",
                "config/user_settings.json"
            ]
            
            valid_configs = []
            
            for config_file in config_files:
                config_path = self.project_root / config_file
                if config_path.exists():
                    try:
                        with open(config_path, 'r', encoding='utf-8') as f:
                            config_data = json.load(f)
                            
                        # 验证JSON格式有效
                        self.assertIsInstance(config_data, dict)
                        valid_configs.append(config_file)
                        print(f"✅ {config_file} - JSON格式有效")
                        
                    except json.JSONDecodeError as e:
                        print(f"❌ {config_file} - JSON格式错误: {str(e)}")
                        
                else:
                    print(f"⚠️  {config_file} - 文件不存在")
                    
            print(f"\n📊 配置文件统计:")
            print(f"   有效配置: {len(valid_configs)}/{len(config_files)}")
            
            # 至少应该有一个有效的配置文件
            self.assertGreaterEqual(len(valid_configs), 1, "没有有效的配置文件")
            
            print("✅ 配置文件处理测试通过")
            
        except Exception as e:
            print(f"❌ 配置文件处理测试失败: {str(e)}")
            raise
            
    def test_03_version_management(self):
        """测试版本管理"""
        print("\n=== 测试版本管理 ===")
        
        try:
            # 检查版本信息文件
            version_files = [
                "version.py",
                "__init__.py",
                "main.py"
            ]
            
            version_info = {}
            
            for version_file in version_files:
                file_path = self.project_root / version_file
                if file_path.exists():
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            
                        # 查找版本信息
                        if '__version__' in content:
                            # 简单的版本提取
                            lines = content.split('\n')
                            for line in lines:
                                if '__version__' in line and '=' in line:
                                    version = line.split('=')[1].strip().strip('"\'')
                                    version_info[version_file] = version
                                    print(f"✅ {version_file} - 版本: {version}")
                                    break
                                    
                    except Exception as e:
                        print(f"❌ {version_file} - 读取失败: {str(e)}")
                        
            if not version_info:
                # 尝试从其他地方获取版本信息
                try:
                    # 检查是否有setup.py
                    setup_path = self.project_root / "setup.py"
                    if setup_path.exists():
                        with open(setup_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        if 'version' in content:
                            version_info['setup.py'] = "从setup.py检测到版本信息"
                            print("✅ setup.py - 包含版本信息")
                            
                except Exception:
                    pass
                    
            print(f"\n📊 版本管理统计:")
            print(f"   版本信息源: {len(version_info)}")
            
            if version_info:
                print("✅ 版本管理测试通过")
            else:
                print("⚠️  未找到明确的版本信息，但不影响基本功能")
                
        except Exception as e:
            print(f"❌ 版本管理测试失败: {str(e)}")
            raise
            
    def test_04_dependency_analysis(self):
        """测试依赖分析"""
        print("\n=== 测试依赖分析 ===")
        
        try:
            # 检查requirements文件
            req_files = [
                "requirements.txt",
                "packaging/requirements.txt"
            ]
            
            dependencies = set()
            
            for req_file in req_files:
                req_path = self.project_root / req_file
                if req_path.exists():
                    try:
                        with open(req_path, 'r', encoding='utf-8') as f:
                            lines = f.readlines()
                            
                        for line in lines:
                            line = line.strip()
                            if line and not line.startswith('#'):
                                # 提取包名（去掉版本号）
                                pkg_name = line.split('==')[0].split('>=')[0].split('<=')[0]
                                dependencies.add(pkg_name)
                                
                        print(f"✅ {req_file} - 找到 {len(lines)} 行依赖")
                        
                    except Exception as e:
                        print(f"❌ {req_file} - 读取失败: {str(e)}")
                        
            # 检查核心依赖
            core_dependencies = ['PyQt5', 'pyserial', 'psutil']
            found_core = []
            
            for core_dep in core_dependencies:
                if any(core_dep.lower() in dep.lower() for dep in dependencies):
                    found_core.append(core_dep)
                    print(f"✅ 核心依赖 {core_dep} - 已声明")
                else:
                    print(f"⚠️  核心依赖 {core_dep} - 未在requirements中找到")
                    
            print(f"\n📊 依赖分析统计:")
            print(f"   总依赖数: {len(dependencies)}")
            print(f"   核心依赖: {len(found_core)}/{len(core_dependencies)}")
            
            if dependencies:
                print("✅ 依赖分析测试通过")
            else:
                print("⚠️  未找到依赖声明文件")
                
        except Exception as e:
            print(f"❌ 依赖分析测试失败: {str(e)}")
            raise
            
    def test_05_build_script_validation(self):
        """测试构建脚本验证"""
        print("\n=== 测试构建脚本验证 ===")
        
        try:
            # 检查构建脚本
            build_scripts = [
                "packaging/build_exe.py",
                "packaging/setup.py",
                "build.py",
                "setup.py"
            ]
            
            valid_scripts = []
            
            for script in build_scripts:
                script_path = self.project_root / script
                if script_path.exists():
                    try:
                        with open(script_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            
                        # 检查是否包含关键构建逻辑
                        build_keywords = ['setup', 'build', 'exe', 'pyinstaller', 'cx_Freeze']
                        found_keywords = [kw for kw in build_keywords if kw.lower() in content.lower()]
                        
                        if found_keywords:
                            valid_scripts.append(script)
                            print(f"✅ {script} - 包含构建逻辑: {', '.join(found_keywords)}")
                        else:
                            print(f"⚠️  {script} - 未检测到构建逻辑")
                            
                    except Exception as e:
                        print(f"❌ {script} - 读取失败: {str(e)}")
                        
            print(f"\n📊 构建脚本统计:")
            print(f"   有效脚本: {len(valid_scripts)}/{len(build_scripts)}")
            
            if valid_scripts:
                print("✅ 构建脚本验证测试通过")
            else:
                print("⚠️  未找到有效的构建脚本")
                
        except Exception as e:
            print(f"❌ 构建脚本验证测试失败: {str(e)}")
            raise
            
    def test_06_resource_file_check(self):
        """测试资源文件检查"""
        print("\n=== 测试资源文件检查 ===")
        
        try:
            # 检查资源文件
            resource_dirs = [
                "ui/resources",
                "resources", 
                "assets",
                "icons"
            ]
            
            resource_files = []
            
            for res_dir in resource_dirs:
                dir_path = self.project_root / res_dir
                if dir_path.exists() and dir_path.is_dir():
                    for file_path in dir_path.rglob('*'):
                        if file_path.is_file():
                            resource_files.append(str(file_path.relative_to(self.project_root)))
                            
            # 检查特定类型的资源文件
            image_files = [f for f in resource_files if f.lower().endswith(('.png', '.jpg', '.jpeg', '.ico', '.svg'))]
            ui_files = [f for f in resource_files if f.lower().endswith('.ui')]
            qrc_files = [f for f in resource_files if f.lower().endswith('.qrc')]
            
            print(f"   图像文件: {len(image_files)}")
            print(f"   UI文件: {len(ui_files)}")
            print(f"   QRC文件: {len(qrc_files)}")
            print(f"   总资源文件: {len(resource_files)}")
            
            if resource_files:
                print("✅ 资源文件检查测试通过")
                
                # 显示部分资源文件
                if len(resource_files) <= 10:
                    for res_file in resource_files:
                        print(f"     - {res_file}")
                else:
                    for res_file in resource_files[:5]:
                        print(f"     - {res_file}")
                    print(f"     ... 还有 {len(resource_files) - 5} 个文件")
                    
            else:
                print("⚠️  未找到资源文件目录")
                
        except Exception as e:
            print(f"❌ 资源文件检查测试失败: {str(e)}")
            raise
            
    def test_07_launcher_script_test(self):
        """测试启动脚本"""
        print("\n=== 测试启动脚本 ===")
        
        try:
            # 检查启动脚本
            launcher_scripts = [
                "packaging/launcher/launcher.bat",
                "start.bat",
                "run.bat",
                "main.py"
            ]
            
            valid_launchers = []
            
            for launcher in launcher_scripts:
                launcher_path = self.project_root / launcher
                if launcher_path.exists():
                    try:
                        with open(launcher_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            
                        # 检查启动逻辑
                        if launcher.endswith('.bat'):
                            if 'python' in content.lower() or '.exe' in content.lower():
                                valid_launchers.append(launcher)
                                print(f"✅ {launcher} - 包含启动逻辑")
                            else:
                                print(f"⚠️  {launcher} - 未检测到启动逻辑")
                        elif launcher.endswith('.py'):
                            if 'if __name__' in content and 'main' in content:
                                valid_launchers.append(launcher)
                                print(f"✅ {launcher} - 包含主入口")
                            else:
                                print(f"⚠️  {launcher} - 未检测到主入口")
                                
                    except Exception as e:
                        print(f"❌ {launcher} - 读取失败: {str(e)}")
                        
            print(f"\n📊 启动脚本统计:")
            print(f"   有效启动器: {len(valid_launchers)}/{len(launcher_scripts)}")
            
            # 至少应该有一个有效的启动方式
            self.assertGreaterEqual(len(valid_launchers), 1, "没有有效的启动脚本")
            
            print("✅ 启动脚本测试通过")
            
        except Exception as e:
            print(f"❌ 启动脚本测试失败: {str(e)}")
            raise
            
    def test_08_deployment_readiness(self):
        """测试部署就绪性"""
        print("\n=== 测试部署就绪性 ===")
        
        try:
            readiness_score = 0
            max_score = 8
            
            # 检查项目结构完整性
            essential_dirs = ['ui', 'core', 'plugins', 'config']
            existing_dirs = [d for d in essential_dirs if (self.project_root / d).exists()]
            if len(existing_dirs) >= 3:
                readiness_score += 1
                print("✅ 项目结构完整")
            else:
                print("❌ 项目结构不完整")
                
            # 检查主入口文件
            if (self.project_root / 'main.py').exists():
                readiness_score += 1
                print("✅ 主入口文件存在")
            else:
                print("❌ 主入口文件缺失")
                
            # 检查配置文件
            config_files = ['config/default.json', 'config/register.json']
            if any((self.project_root / cf).exists() for cf in config_files):
                readiness_score += 1
                print("✅ 配置文件存在")
            else:
                print("❌ 配置文件缺失")
                
            # 检查依赖声明
            if (self.project_root / 'requirements.txt').exists() or (self.project_root / 'packaging/requirements.txt').exists():
                readiness_score += 1
                print("✅ 依赖声明存在")
            else:
                print("❌ 依赖声明缺失")
                
            # 检查构建脚本
            build_files = ['packaging/build_exe.py', 'setup.py', 'packaging/setup.py']
            if any((self.project_root / bf).exists() for bf in build_files):
                readiness_score += 1
                print("✅ 构建脚本存在")
            else:
                print("❌ 构建脚本缺失")
                
            # 检查文档
            doc_files = ['README.md', 'README.txt', 'docs']
            if any((self.project_root / df).exists() for df in doc_files):
                readiness_score += 1
                print("✅ 文档存在")
            else:
                print("❌ 文档缺失")
                
            # 检查版本信息
            version_files = ['version.py', '__init__.py']
            has_version = False
            for vf in version_files:
                vf_path = self.project_root / vf
                if vf_path.exists():
                    try:
                        with open(vf_path, 'r', encoding='utf-8') as f:
                            if '__version__' in f.read():
                                has_version = True
                                break
                    except:
                        pass
                        
            if has_version:
                readiness_score += 1
                print("✅ 版本信息存在")
            else:
                print("❌ 版本信息缺失")
                
            # 检查测试文件
            if (self.project_root / 'test_suite').exists():
                readiness_score += 1
                print("✅ 测试套件存在")
            else:
                print("❌ 测试套件缺失")
                
            readiness_percentage = (readiness_score / max_score) * 100
            
            print(f"\n📊 部署就绪性评估:")
            print(f"   就绪得分: {readiness_score}/{max_score}")
            print(f"   就绪率: {readiness_percentage:.1f}%")
            
            # 验证基本就绪性（至少60%）
            self.assertGreaterEqual(readiness_percentage, 60.0, "部署就绪性不足")
            
            if readiness_percentage >= 80:
                print("🎉 项目部署就绪性优秀")
            elif readiness_percentage >= 60:
                print("✅ 项目部署就绪性良好")
            else:
                print("⚠️  项目部署就绪性需要改进")
                
        except Exception as e:
            print(f"❌ 部署就绪性测试失败: {str(e)}")
            raise

def run_packaging_deployment_tests():
    """运行打包和部署测试"""
    print("📦 开始FSJ04832寄存器配置工具打包和部署测试")
    print("=" * 60)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(PackagingDeploymentTest)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果摘要
    print("\n" + "=" * 60)
    print("📊 打包和部署测试结果摘要:")
    print(f"   总测试数: {result.testsRun}")
    print(f"   成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"   失败: {len(result.failures)}")
    print(f"   错误: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"   - {test}")
            
    if result.errors:
        print("\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"   - {test}")
            
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\n🎯 测试成功率: {success_rate:.1f}%")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_packaging_deployment_tests()
    sys.exit(0 if success else 1)
