#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试现代化处理器修复
验证现代化处理器是否能正确创建和初始化
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_modern_handlers_fix():
    """测试现代化处理器修复"""
    print("=" * 60)
    print("🔧 测试现代化处理器修复")
    print("=" * 60)
    
    # 创建QApplication实例
    app = QApplication(sys.argv)
    
    test_results = []
    
    try:
        # 1. 测试现代化处理器的构造函数
        print("\n1. 测试现代化处理器构造函数...")
        
        # 创建模拟的RegisterManager
        from core.services.register.RegisterManager import RegisterManager
        import json
        
        # 加载寄存器配置
        config_path = os.path.join('lib', 'register.json')
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                registers_config = json.load(f)
        else:
            # 创建最小的模拟配置
            registers_config = {
                "0x00": {"name": "Test Register", "bits": []},
                "0x01": {"name": "Test Register 2", "bits": []}
            }
        
        register_manager = RegisterManager(registers_config)
        
        # 创建模拟的RegisterRepository
        class MockSPIService:
            def __init__(self):
                self.ports_refreshed = None
                
            def refresh_available_ports(self):
                pass
                
            def set_port(self, port):
                return True
        
        class MockRegisterRepository:
            def __init__(self):
                self.spi_service = MockSPIService()
        
        register_repo = MockRegisterRepository()
        
        # 创建模拟主窗口
        class MockMainWindow:
            def __init__(self):
                self.register_manager = register_manager
                self.auto_write_mode = False
        
        mock_main_window = MockMainWindow()
        
        print("   ✓ 模拟环境创建成功")
        test_results.append("模拟环境创建成功")
        
        # 2. 测试ModernRegisterTableHandler
        print("\n2. 测试ModernRegisterTableHandler...")
        try:
            from ui.handlers.ModernRegisterTableHandler import ModernRegisterTableHandler
            table_handler = ModernRegisterTableHandler(
                parent=None,  # 使用None避免类型错误
                register_manager=register_manager
            )
            print("   ✅ ModernRegisterTableHandler创建成功")
            test_results.append("ModernRegisterTableHandler创建成功")
        except Exception as e:
            print(f"   ❌ ModernRegisterTableHandler创建失败: {str(e)}")
            test_results.append(f"ModernRegisterTableHandler创建失败: {str(e)}")

        # 3. 测试ModernRegisterIOHandler
        print("\n3. 测试ModernRegisterIOHandler...")
        try:
            from ui.handlers.ModernRegisterIOHandler import ModernRegisterIOHandler
            io_handler = ModernRegisterIOHandler(
                parent=None,  # 使用None避免类型错误
                register_manager=register_manager,
                register_repo=register_repo
            )
            print("   ✅ ModernRegisterIOHandler创建成功")
            test_results.append("ModernRegisterIOHandler创建成功")
        except Exception as e:
            print(f"   ❌ ModernRegisterIOHandler创建失败: {str(e)}")
            test_results.append(f"ModernRegisterIOHandler创建失败: {str(e)}")

        # 4. 测试ModernRegisterTreeHandler
        print("\n4. 测试ModernRegisterTreeHandler...")
        try:
            from ui.handlers.ModernRegisterTreeHandler import ModernRegisterTreeHandler
            tree_handler = ModernRegisterTreeHandler(
                parent=None,  # 使用None避免类型错误
                register_manager=register_manager
            )
            print("   ✅ ModernRegisterTreeHandler创建成功")
            test_results.append("ModernRegisterTreeHandler创建成功")
        except Exception as e:
            print(f"   ❌ ModernRegisterTreeHandler创建失败: {str(e)}")
            test_results.append(f"ModernRegisterTreeHandler创建失败: {str(e)}")
        
        # 5. 测试InitializationManager的现代化处理器创建
        print("\n5. 测试InitializationManager的现代化处理器创建...")
        try:
            from ui.managers.InitializationManager import InitializationManager
            from ui.factories.ToolWindowFactory import ToolWindowFactory
            
            # 设置工具窗口工厂
            mock_main_window.tool_window_factory = ToolWindowFactory(mock_main_window)
            
            # 创建InitializationManager
            init_manager = InitializationManager(mock_main_window)
            
            # 测试现代化处理器创建
            init_manager._create_modern_handlers(register_repo)
            
            print("   ✅ InitializationManager现代化处理器创建成功")
            test_results.append("InitializationManager现代化处理器创建成功")
            
            # 检查处理器类型
            if hasattr(mock_main_window, 'table_handler'):
                table_type = type(mock_main_window.table_handler).__name__
                print(f"   ✓ Table处理器类型: {table_type}")
                if 'Modern' in table_type:
                    test_results.append("Table处理器使用现代化版本")
                else:
                    test_results.append("Table处理器使用传统版本")
            
            if hasattr(mock_main_window, 'io_handler'):
                io_type = type(mock_main_window.io_handler).__name__
                print(f"   ✓ IO处理器类型: {io_type}")
                if 'Modern' in io_type:
                    test_results.append("IO处理器使用现代化版本")
                else:
                    test_results.append("IO处理器使用传统版本")
            
            if hasattr(mock_main_window, 'tree_handler'):
                tree_type = type(mock_main_window.tree_handler).__name__
                print(f"   ✓ Tree处理器类型: {tree_type}")
                if 'Modern' in tree_type:
                    test_results.append("Tree处理器使用现代化版本")
                else:
                    test_results.append("Tree处理器使用传统版本")
                    
        except Exception as e:
            print(f"   ❌ InitializationManager测试失败: {str(e)}")
            test_results.append(f"InitializationManager测试失败: {str(e)}")
            import traceback
            traceback.print_exc()
        
        # 6. 总结测试结果
        print("\n" + "=" * 60)
        print("📊 测试结果总结")
        print("=" * 60)
        
        success_count = 0
        total_count = len(test_results)
        
        for i, result in enumerate(test_results, 1):
            if "失败" in result or "错误" in result:
                print(f"{i}. ❌ {result}")
            else:
                print(f"{i}. ✅ {result}")
                success_count += 1
        
        print(f"\n成功率: {success_count}/{total_count} ({success_count/total_count:.1%})")
        
        if success_count == total_count:
            print("\n🎉 现代化处理器修复成功！")
            print("现在应该不会再出现构造函数参数错误的警告了。")
            return True
        else:
            print(f"\n⚠️  还有 {total_count - success_count} 项测试未通过。")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        app.quit()

if __name__ == "__main__":
    success = test_modern_handlers_fix()
    sys.exit(0 if success else 1)
