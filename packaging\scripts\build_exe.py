#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyInstaller打包脚本
用于将FSJ04832寄存器配置工具打包为可执行文件
"""

import os
import sys
import subprocess
import shutil
import json
import re
from datetime import datetime
from pathlib import Path

def get_project_root():
    """获取项目根目录的相对路径

    Returns:
        Path: 项目根目录路径
    """
    # 从当前脚本位置计算项目根目录
    script_path = Path(__file__).parent
    return script_path.parent.parent

def get_relative_path_display(path):
    """获取用于显示的相对路径

    Args:
        path: 要转换的路径

    Returns:
        str: 相对路径字符串或目录名
    """
    try:
        current_dir = Path.cwd()
        target_path = Path(path)
        if target_path.is_absolute():
            try:
                return str(target_path.relative_to(current_dir))
            except ValueError:
                # 如果无法计算相对路径，返回目录名
                return target_path.name
        else:
            return str(target_path)
    except Exception:
        return str(Path(path).name)

class VersionManager:
    """版本管理器"""

    def __init__(self, version_file=None):
        """初始化版本管理器

        Args:
            version_file: 版本配置文件路径
        """
        if version_file is None:
            # 优先使用packaging目录下的版本文件
            packaging_root = Path(__file__).parent.parent
            version_file = packaging_root / 'config' / 'version.json'

            # 如果config/version.json不存在，尝试packaging/version.json
            if not version_file.exists():
                version_file = packaging_root / 'version.json'

            # 如果packaging目录下都没有，回退到项目根目录
            if not version_file.exists():
                project_root = get_project_root()
                version_file = project_root / 'version.json'

        self.version_file = Path(version_file)
        self.version_data = self.load_version()

    def load_version(self):
        """加载版本信息"""
        if self.version_file.exists():
            try:
                with open(self.version_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"警告: 无法加载版本文件 {self.version_file}: {e}")

        # 默认版本信息
        return {
            "version": {"major": 1, "minor": 0, "patch": 0, "build": 0},
            "app_info": {
                "name": "FSJ04832 寄存器配置工具",
                "description": "用于配置和管理FSJ04832寄存器的专业工具",
                "company": "FSJ Technology",
                "copyright": "© 2024 FSJ Technology",
                "author": "开发团队"
            },
            "build_info": {
                "last_build_date": "",
                "build_type": "release",
                "target_platform": "windows"
            }
        }

    def save_version(self):
        """保存版本信息"""
        try:
            with open(self.version_file, 'w', encoding='utf-8') as f:
                json.dump(self.version_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"警告: 无法保存版本文件 {self.version_file}: {e}")

    def increment_build(self):
        """增加构建号"""
        self.version_data['version']['build'] += 1
        self.version_data['build_info']['last_build_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"构建号已增加到: {self.get_version_string()}")

    def increment_patch(self):
        """增加补丁版本号"""
        self.version_data['version']['patch'] += 1
        self.version_data['version']['build'] = 0
        self.version_data['build_info']['last_build_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"补丁版本号已增加到: {self.get_version_string()}")

    def increment_minor(self):
        """增加次版本号"""
        self.version_data['version']['minor'] += 1
        self.version_data['version']['patch'] = 0
        self.version_data['version']['build'] = 0
        self.version_data['build_info']['last_build_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"次版本号已增加到: {self.get_version_string()}")

    def increment_major(self):
        """增加主版本号"""
        self.version_data['version']['major'] += 1
        self.version_data['version']['minor'] = 0
        self.version_data['version']['patch'] = 0
        self.version_data['version']['build'] = 0
        self.version_data['build_info']['last_build_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"主版本号已增加到: {self.get_version_string()}")

    def get_version_string(self):
        """获取版本字符串"""
        v = self.version_data['version']
        return f"{v['major']}.{v['minor']}.{v['patch']}.{v['build']}"

    def get_short_version_string(self):
        """获取短版本字符串（不包含构建号）"""
        v = self.version_data['version']
        return f"{v['major']}.{v['minor']}.{v['patch']}"

    def get_app_name(self):
        """获取应用程序名称"""
        return self.version_data['app_info']['name']

    def get_exe_name(self):
        """获取可执行文件名称"""
        version_string = self.get_version_string()
        return f"FSJ04832_RegisterTool_v{version_string}"

    def get_app_title_with_version(self):
        """获取带版本号的应用程序标题"""
        app_name = self.get_app_name()
        version = self.get_version_string()
        return f"{app_name} v{version}"

def update_spec_file(version_manager, spec_file='build.spec'):
    """更新spec文件中的版本信息

    Args:
        version_manager: 版本管理器实例
        spec_file: spec文件路径
    """
    spec_path = Path(spec_file)
    if not spec_path.exists():
        print(f"警告: spec文件 {spec_file} 不存在")
        return False

    try:
        # 读取spec文件内容
        with open(spec_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 更新版本信息（spec文件中使用get_version_info()函数动态获取）
        # 这里不需要手动更新，因为spec文件会自动读取最新的版本信息

        print(f"已更新spec文件: {spec_file}")
        print(f"可执行文件名: {version_manager.get_exe_name()}")
        return True

    except Exception as e:
        print(f"更新spec文件失败: {e}")
        return False

def clean_build_dirs():
    """清理构建目录（但保留历史版本）"""
    dirs_to_clean = ['build', '__pycache__']

    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"清理目录: {dir_name}")
            shutil.rmtree(dir_name)

def create_version_output_dir(version_manager):
    """创建版本化的输出目录

    Args:
        version_manager: 版本管理器实例

    Returns:
        str: 输出目录路径
    """
    # 获取当前时间戳
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    version_string = version_manager.get_version_string()

    # 创建版本目录名：时间戳_版本号
    version_dir_name = f"{timestamp}_v{version_string}"

    # 获取项目根目录路径
    project_root = get_project_root()

    # 创建releases目录（如果不存在）
    releases_dir = project_root / 'releases'
    releases_dir.mkdir(exist_ok=True)

    # 创建版本特定目录
    version_output_dir = releases_dir / version_dir_name
    version_output_dir.mkdir(exist_ok=True)

    print(f"创建版本输出目录: {version_output_dir}")
    return str(version_output_dir)

def build_with_spec(spec_file='build.spec', output_dir=None):
    """使用spec文件构建

    Args:
        spec_file: spec文件路径
        output_dir: 输出目录路径
    """
    print(f"使用spec文件构建: {spec_file}")

    cmd = [
        'pyinstaller',
        '--clean',
        '--noconfirm',
        spec_file
    ]

    # 如果指定了输出目录，添加distpath参数
    if output_dir:
        cmd.extend(['--distpath', output_dir])

    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True, encoding='utf-8', errors='ignore')
        print("构建成功!")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"构建失败: {e}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False
    except Exception as e:
        print(f"构建过程中出现异常: {e}")
        return False

def build_simple(output_dir=None):
    """简单构建方式

    Args:
        output_dir: 输出目录路径
    """
    print("使用简单方式构建...")

    # 定义隐藏导入
    hidden_imports = [
        'ui.handlers.ModernSetModesHandler',
        'ui.handlers.ModernClkinControlHandler',
        'ui.handlers.ModernPLLHandler',
        'ui.handlers.ModernSyncSysRefHandler',
        'ui.handlers.ModernClkOutputsHandler',
        'ui.handlers.ModernRegisterTableHandler',
        'ui.handlers.ModernUIEventHandler',
        'ui.handlers.ModernRegisterIOHandler',
        'ui.handlers.ModernRegisterTreeHandler',
        'ui.handlers.ModernBaseHandler',
        # 添加更多必要的模块
        'PyQt5.QtCore',
        'PyQt5.QtWidgets',
        'PyQt5.QtGui',
        'serial',
        'serial.tools.list_ports',
    ]

    cmd = [
        'pyinstaller',
        '--onedir',  # 创建目录而不是单文件
        '--console',  # 临时显示控制台以查看错误
        '--clean',
        '--noconfirm',
        '--name=FSJ04832_RegisterTool',
        '--debug=all',  # 启用详细调试信息
        '--noupx',  # 禁用UPX压缩
        '--add-data=config;config',
        '--add-data=images;images',
        '--add-data=ui/forms;ui/forms',
        # 添加Python DLL路径
        f'--paths={sys.executable}/../DLLs',
        f'--paths={sys.executable}/..',
    ]

    # 如果指定了输出目录，添加distpath参数
    if output_dir:
        cmd.extend(['--distpath', output_dir])

    # 添加隐藏导入
    for module in hidden_imports:
        cmd.extend(['--hidden-import', module])

    # 添加主文件（使用相对路径，因为已经切换到项目根目录）
    cmd.append('main.py')

    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True, encoding='utf-8', errors='ignore')
        print("构建成功!")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"构建失败: {e}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False
    except Exception as e:
        print(f"构建过程中出现异常: {e}")
        return False

def copy_additional_files(output_dir, exe_name):
    """复制额外的文件到输出目录

    Args:
        output_dir: 输出目录路径
        exe_name: 可执行文件名称
    """
    dist_dir = Path(output_dir) / exe_name
    if not dist_dir.exists():
        print(f"输出目录不存在: {dist_dir}，跳过文件复制")
        return

    # 获取项目根目录路径
    project_root = get_project_root()

    # 复制配置文件
    config_src = project_root / 'config'
    config_dst = dist_dir / 'config'
    if config_src.exists() and not config_dst.exists():
        print("复制配置文件...")
        shutil.copytree(config_src, config_dst)

    # 复制图像文件
    images_src = project_root / 'images'
    images_dst = dist_dir / 'images'
    if images_src.exists() and not images_dst.exists():
        print("复制图像文件...")
        shutil.copytree(images_src, images_dst)

def create_version_info_file(version_manager, output_dir):
    """在输出目录创建版本信息文件

    Args:
        version_manager: 版本管理器实例
        output_dir: 输出目录路径
    """
    version_info = {
        "version": version_manager.get_version_string(),
        "app_name": version_manager.get_app_name(),
        "exe_name": version_manager.get_exe_name(),
        "build_date": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "build_info": version_manager.version_data.get('build_info', {}),
        "app_info": version_manager.version_data.get('app_info', {})
    }

    version_file = Path(output_dir) / 'version_info.json'
    try:
        with open(version_file, 'w', encoding='utf-8') as f:
            json.dump(version_info, f, indent=2, ensure_ascii=False)
        print(f"创建版本信息文件: {version_file}")
    except Exception as e:
        print(f"创建版本信息文件失败: {e}")

def create_latest_link(output_dir, version_manager):
    """创建指向最新版本的链接

    Args:
        output_dir: 输出目录路径
        version_manager: 版本管理器实例
    """
    # 获取项目根目录路径
    project_root = get_project_root()
    releases_dir = project_root / 'releases'
    latest_link = releases_dir / 'latest'

    try:
        # 如果已存在latest链接，删除它
        if latest_link.exists():
            if latest_link.is_symlink():
                latest_link.unlink()
            elif latest_link.is_dir():
                shutil.rmtree(latest_link)

        # 创建指向当前版本的符号链接
        output_path = Path(output_dir)
        relative_path = output_path.relative_to(releases_dir)

        try:
            # 尝试创建符号链接（需要管理员权限）
            latest_link.symlink_to(relative_path, target_is_directory=True)
            print(f"创建符号链接: {latest_link} -> {relative_path}")
        except OSError:
            # 如果无法创建符号链接，复制目录
            shutil.copytree(output_dir, latest_link)
            print(f"复制最新版本到: {latest_link}")

    except Exception as e:
        print(f"创建最新版本链接失败: {e}")

def list_all_versions():
    """列出所有构建的版本"""
    # 获取项目根目录路径
    project_root = get_project_root()
    releases_dir = project_root / 'releases'

    if not releases_dir.exists():
        print("没有找到releases目录")
        return []

    versions = []
    for item in releases_dir.iterdir():
        if item.is_dir() and item.name != 'latest':
            # 解析目录名：时间戳_版本号
            try:
                parts = item.name.split('_v')
                if len(parts) == 2:
                    timestamp_str = parts[0]
                    version_str = parts[1]
                    timestamp = datetime.strptime(timestamp_str, '%Y%m%d_%H%M%S')
                    versions.append({
                        'path': item,
                        'timestamp': timestamp,
                        'version': version_str,
                        'name': item.name
                    })
            except ValueError:
                continue

    # 按时间戳排序
    versions.sort(key=lambda x: x['timestamp'], reverse=True)
    return versions

def verify_version_integration(version_manager, output_dir, exe_name):
    """验证版本信息是否正确集成到构建中

    Args:
        version_manager: 版本管理器实例
        output_dir: 输出目录路径
        exe_name: 可执行文件名
    """
    print("\n[验证] 检查版本集成...")

    # 检查可执行文件是否存在（PyInstaller可能直接输出到根目录）
    exe_path1 = Path(output_dir) / f"{exe_name}.exe"  # 直接在输出目录
    exe_path2 = Path(output_dir) / exe_name / f"{exe_name}.exe"  # 在子目录中

    if exe_path1.exists():
        print(f"[成功] 可执行文件已生成: {exe_name}.exe (在输出根目录)")
    elif exe_path2.exists():
        print(f"[成功] 可执行文件已生成: {exe_name}.exe (在子目录)")
    else:
        print("[警告] 可执行文件未找到")
        print(f"   检查路径1: {exe_path1}")
        print(f"   检查路径2: {exe_path2}")

    # 检查版本信息文件
    version_info_path = Path(output_dir) / 'version_info.json'
    if version_info_path.exists():
        try:
            with open(version_info_path, 'r', encoding='utf-8') as f:
                info = json.load(f)
            print("[成功] 版本信息文件已创建")
            print(f"  版本: {info.get('version', 'N/A')}")
            print(f"  应用名称: {info.get('app_name', 'N/A')}")
            print(f"  构建日期: {info.get('build_date', 'N/A')}")
        except Exception as e:
            print(f"[警告] 版本信息文件读取失败: {str(e)}")
    else:
        print("[警告] 版本信息文件未找到")

    # 验证版本服务是否能正确读取
    try:
        from core.services.version.VersionService import VersionService
        vs = VersionService.instance()
        vs.reload_version()  # 重新加载版本信息

        current_version = vs.get_version_string()
        expected_version = version_manager.get_version_string()

        if current_version == expected_version:
            print(f"[成功] 版本服务读取正确: {current_version}")
        else:
            print(f"[警告] 版本服务读取不一致: 期望 {expected_version}, 实际 {current_version}")

    except Exception as e:
        print(f"[警告] 版本服务验证失败: {str(e)}")

def main():
    """主函数"""
    print("=" * 60)
    print("FSJ04832 Register Configuration Tool - PyInstaller Build Script")
    print("=" * 60)

    # Switch to project root directory
    script_dir = Path(__file__).parent
    project_root = get_project_root()
    print(f"Script directory: {get_relative_path_display(script_dir)}")
    print(f"Project root: {get_relative_path_display(project_root)}")

    os.chdir(str(project_root))
    print(f"Working directory: {Path.cwd().name}")

    # Verify main.py exists
    main_file = Path('main.py')
    if main_file.exists():
        print("[OK] main.py file exists")
    else:
        print("[ERROR] main.py file not found")
        print(f"Current directory contents: {[f.name for f in Path('.').iterdir()]}")
        return False

    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description='FSJ04832 Register Configuration Tool Build Script')
    parser.add_argument('--version-type', choices=['build', 'patch', 'minor', 'major'],
                       default='build', help='Version increment type (default: build)')
    parser.add_argument('--no-version-increment', action='store_true',
                       help='Do not increment version number')
    parser.add_argument('--spec-file', default='build.spec',
                       help='Specify spec file path (default: build.spec)')
    args = parser.parse_args()

    # Ensure spec file path is correct, prioritize packaging/scripts spec file
    spec_candidates = [
        args.spec_file,  # User specified path
        'packaging/scripts/build.spec',  # Packaging directory (preferred)
        'build.spec',    # Project root (fallback)
    ]

    spec_file_found = None
    for candidate in spec_candidates:
        if not os.path.isabs(candidate):
            candidate_path = str(project_root / candidate)
        else:
            candidate_path = candidate

        if os.path.exists(candidate_path):
            spec_file_found = candidate_path
            print(f"Found spec file: {get_relative_path_display(candidate_path)}")
            break

    if spec_file_found:
        args.spec_file = spec_file_found
    else:
        print(f"Warning: Spec file not found, will use simple build mode")
        print(f"Search paths: {[get_relative_path_display(project_root / c) for c in spec_candidates]}")

    # 初始化版本管理器
    version_manager = VersionManager()

    print(f"当前版本: {version_manager.get_version_string()}")
    print(f"应用名称: {version_manager.get_app_name()}")

    # 根据参数增加版本号
    if not args.no_version_increment:
        print(f"\n增加版本号类型: {args.version_type}")
        if args.version_type == 'build':
            version_manager.increment_build()
        elif args.version_type == 'patch':
            version_manager.increment_patch()
        elif args.version_type == 'minor':
            version_manager.increment_minor()
        elif args.version_type == 'major':
            version_manager.increment_major()

        # 保存版本信息
        version_manager.save_version()
        print(f"新版本: {version_manager.get_version_string()}")

    # 检查PyInstaller是否安装
    try:
        subprocess.run(['pyinstaller', '--version'], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("错误: PyInstaller未安装")
        print("请运行: pip install pyinstaller")
        return False

    # 清理构建目录
    clean_build_dirs()

    # 创建版本化输出目录
    output_dir = create_version_output_dir(version_manager)

    # 更新spec文件中的版本信息
    if os.path.exists(args.spec_file):
        print(f"\n更新spec文件: {args.spec_file}")
        update_spec_file(version_manager, args.spec_file)
        success = build_with_spec(args.spec_file, output_dir)
    else:
        print(f"警告: spec文件 {args.spec_file} 不存在，使用简单构建方式")
        success = build_simple(output_dir)

    if success:
        # 复制额外文件
        exe_name = version_manager.get_exe_name()
        copy_additional_files(output_dir, exe_name)

        # 创建版本信息文件
        create_version_info_file(version_manager, output_dir)

        # 创建最新版本的符号链接或快捷方式
        create_latest_link(output_dir, version_manager)

        # 验证版本信息是否正确应用
        verify_version_integration(version_manager, output_dir, exe_name)

        print("\n" + "=" * 60)
        print("[完成] 构建成功!")
        print(f"[版本] {version_manager.get_version_string()}")
        print(f"[应用] {version_manager.get_app_name()}")
        print(f"[标题] {version_manager.get_app_title_with_version()}")
        print(f"[文件] {version_manager.get_exe_name()}")
        print(f"[目录] {output_dir}")
        print(f"[路径] {output_dir}/{exe_name}.exe")

        # 显示历史版本信息
        versions = list_all_versions()
        if versions:
            print(f"\n[历史] 版本历史 (共{len(versions)}个):")
            for i, v in enumerate(versions[:5]):  # 只显示最近5个版本
                print(f"  {i+1}. {v['name']} (v{v['version']})")
            if len(versions) > 5:
                print(f"  ... 还有{len(versions)-5}个更早的版本")

        print("\n[集成] 版本信息已集成到软件中:")
        print(f"   [OK] 可执行文件名包含版本号: {exe_name}.exe")
        print(f"   [OK] 软件窗口标题将显示: {version_manager.get_app_title_with_version()}")
        print("   [OK] 关于对话框将显示完整版本信息")
        print(f"   [OK] 版本信息文件: {output_dir}/version_info.json")
        print("=" * 60)
        return True
    else:
        print("\n" + "=" * 60)
        print("构建失败!")
        print("=" * 60)
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
