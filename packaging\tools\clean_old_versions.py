#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
版本清理工具
帮助清理旧版本以节省磁盘空间
"""

import sys
import os
import shutil
from pathlib import Path

# 添加项目根目录到Python路径
packaging_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
project_root = os.path.dirname(packaging_root)
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(packaging_root, 'scripts'))

from build_exe import list_all_versions

def get_directory_size(path):
    """获取目录大小（字节）"""
    total_size = 0
    try:
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                if os.path.exists(filepath):
                    total_size += os.path.getsize(filepath)
    except:
        pass
    return total_size

def format_size(size_bytes):
    """格式化文件大小"""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes / (1024 * 1024):.1f} MB"
    else:
        return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"

def interactive_cleanup():
    """交互式清理"""
    print("=" * 80)
    print("FSJ04832 版本清理工具")
    print("=" * 80)
    
    try:
        versions = list_all_versions()
        
        if not versions:
            print("📁 没有找到任何版本，无需清理")
            return
        
        if len(versions) <= 1:
            print("📁 只有1个版本，建议保留")
            return
        
        print(f"📁 找到 {len(versions)} 个版本:")
        print()
        
        total_size = 0
        for i, version in enumerate(versions, 1):
            size = get_directory_size(version['path'])
            total_size += size
            timestamp_str = version['timestamp'].strftime('%Y-%m-%d %H:%M:%S')
            
            print(f"{i:2d}. 版本 {version['version']} ({format_size(size)})")
            print(f"    📅 {timestamp_str}")
            print(f"    📂 {version['name']}")
            print()
        
        print(f"💾 总占用空间: {format_size(total_size)}")
        print()
        
        # 提供清理选项
        print("🧹 清理选项:")
        print("1. 保留最近 N 个版本，删除其他")
        print("2. 删除指定版本")
        print("3. 删除早于指定日期的版本")
        print("4. 退出")
        print()
        
        choice = input("请选择清理方式 (1-4): ").strip()
        
        if choice == '1':
            cleanup_keep_recent(versions)
        elif choice == '2':
            cleanup_specific_versions(versions)
        elif choice == '3':
            cleanup_by_date(versions)
        elif choice == '4':
            print("退出清理工具")
        else:
            print("无效选择")
            
    except Exception as e:
        print(f"❌ 清理过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

def cleanup_keep_recent(versions):
    """保留最近N个版本"""
    try:
        keep_count = int(input(f"保留最近几个版本？(建议3-5个): ").strip())
        
        if keep_count <= 0:
            print("❌ 保留数量必须大于0")
            return
        
        if keep_count >= len(versions):
            print(f"⚠️  当前只有{len(versions)}个版本，无需清理")
            return
        
        to_delete = versions[keep_count:]
        
        print(f"\n📋 将删除 {len(to_delete)} 个版本:")
        total_size = 0
        for version in to_delete:
            size = get_directory_size(version['path'])
            total_size += size
            timestamp_str = version['timestamp'].strftime('%Y-%m-%d %H:%M:%S')
            print(f"  ❌ {version['name']} ({format_size(size)}) - {timestamp_str}")
        
        print(f"\n💾 将释放空间: {format_size(total_size)}")
        
        confirm = input(f"\n确认删除这 {len(to_delete)} 个版本？(y/N): ").strip().lower()
        
        if confirm == 'y':
            deleted_count = 0
            for version in to_delete:
                try:
                    shutil.rmtree(version['path'])
                    print(f"✓ 已删除: {version['name']}")
                    deleted_count += 1
                except Exception as e:
                    print(f"❌ 删除失败: {version['name']} - {str(e)}")
            
            print(f"\n🎉 清理完成！删除了 {deleted_count} 个版本，释放了 {format_size(total_size)} 空间")
        else:
            print("取消删除操作")
            
    except ValueError:
        print("❌ 请输入有效的数字")
    except Exception as e:
        print(f"❌ 清理失败: {str(e)}")

def cleanup_specific_versions(versions):
    """删除指定版本"""
    try:
        print("\n📋 选择要删除的版本 (输入版本号，用逗号分隔):")
        for i, version in enumerate(versions, 1):
            size = get_directory_size(version['path'])
            timestamp_str = version['timestamp'].strftime('%Y-%m-%d %H:%M:%S')
            print(f"{i:2d}. 版本 {version['version']} ({format_size(size)}) - {timestamp_str}")
        
        selection = input("\n输入要删除的版本号 (例如: 1,3,5): ").strip()
        
        if not selection:
            print("未选择任何版本")
            return
        
        indices = []
        for s in selection.split(','):
            try:
                idx = int(s.strip()) - 1
                if 0 <= idx < len(versions):
                    indices.append(idx)
                else:
                    print(f"⚠️  忽略无效版本号: {s.strip()}")
            except ValueError:
                print(f"⚠️  忽略无效输入: {s.strip()}")
        
        if not indices:
            print("没有有效的版本选择")
            return
        
        to_delete = [versions[i] for i in indices]
        
        print(f"\n📋 将删除 {len(to_delete)} 个版本:")
        total_size = 0
        for version in to_delete:
            size = get_directory_size(version['path'])
            total_size += size
            timestamp_str = version['timestamp'].strftime('%Y-%m-%d %H:%M:%S')
            print(f"  ❌ {version['name']} ({format_size(size)}) - {timestamp_str}")
        
        print(f"\n💾 将释放空间: {format_size(total_size)}")
        
        confirm = input(f"\n确认删除这 {len(to_delete)} 个版本？(y/N): ").strip().lower()
        
        if confirm == 'y':
            deleted_count = 0
            for version in to_delete:
                try:
                    shutil.rmtree(version['path'])
                    print(f"✓ 已删除: {version['name']}")
                    deleted_count += 1
                except Exception as e:
                    print(f"❌ 删除失败: {version['name']} - {str(e)}")
            
            print(f"\n🎉 清理完成！删除了 {deleted_count} 个版本，释放了 {format_size(total_size)} 空间")
        else:
            print("取消删除操作")
            
    except Exception as e:
        print(f"❌ 清理失败: {str(e)}")

def cleanup_by_date(versions):
    """按日期清理"""
    try:
        from datetime import datetime, timedelta
        
        print("\n📅 按日期清理选项:")
        print("1. 删除7天前的版本")
        print("2. 删除30天前的版本")
        print("3. 自定义日期")
        
        choice = input("选择日期选项 (1-3): ").strip()
        
        cutoff_date = None
        
        if choice == '1':
            cutoff_date = datetime.now() - timedelta(days=7)
        elif choice == '2':
            cutoff_date = datetime.now() - timedelta(days=30)
        elif choice == '3':
            days = int(input("删除多少天前的版本？: ").strip())
            cutoff_date = datetime.now() - timedelta(days=days)
        else:
            print("无效选择")
            return
        
        to_delete = [v for v in versions if v['timestamp'] < cutoff_date]
        
        if not to_delete:
            print(f"📁 没有找到 {cutoff_date.strftime('%Y-%m-%d')} 之前的版本")
            return
        
        print(f"\n📋 将删除 {len(to_delete)} 个版本 (早于 {cutoff_date.strftime('%Y-%m-%d')}):")
        total_size = 0
        for version in to_delete:
            size = get_directory_size(version['path'])
            total_size += size
            timestamp_str = version['timestamp'].strftime('%Y-%m-%d %H:%M:%S')
            print(f"  ❌ {version['name']} ({format_size(size)}) - {timestamp_str}")
        
        print(f"\n💾 将释放空间: {format_size(total_size)}")
        
        confirm = input(f"\n确认删除这 {len(to_delete)} 个版本？(y/N): ").strip().lower()
        
        if confirm == 'y':
            deleted_count = 0
            for version in to_delete:
                try:
                    shutil.rmtree(version['path'])
                    print(f"✓ 已删除: {version['name']}")
                    deleted_count += 1
                except Exception as e:
                    print(f"❌ 删除失败: {version['name']} - {str(e)}")
            
            print(f"\n🎉 清理完成！删除了 {deleted_count} 个版本，释放了 {format_size(total_size)} 空间")
        else:
            print("取消删除操作")
            
    except ValueError:
        print("❌ 请输入有效的数字")
    except Exception as e:
        print(f"❌ 清理失败: {str(e)}")

def main():
    """主函数"""
    interactive_cleanup()

if __name__ == "__main__":
    main()
