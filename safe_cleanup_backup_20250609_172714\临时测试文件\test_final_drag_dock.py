#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终拖拽停靠功能测试
验证修复后的拖拽停靠功能是否正常工作
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication
import time


def test_final_drag_dock():
    """测试最终的拖拽停靠功能"""
    try:
        print("🧪 开始最终拖拽停靠功能测试...")
        
        # 1. 启用强制悬浮模式
        print("1. 启用强制悬浮模式...")
        from core.services.config.ConfigurationManager import set_config, get_config
        set_config('plugins.force_floating_mode', True)
        print(f"   ✅ 强制悬浮模式已启用: {get_config('plugins.force_floating_mode')}")
        
        # 2. 创建主窗口
        print("2. 创建主窗口...")
        from ui.windows.RegisterMainWindow import RegisterMainWindow
        main_window = RegisterMainWindow()
        main_window.show()
        print("   ✅ 主窗口创建成功")
        
        # 3. 获取插件服务
        print("3. 获取插件服务...")
        if hasattr(main_window, 'plugin_service'):
            plugin_service = main_window.plugin_service
            print("   ✅ 插件服务获取成功")
        else:
            print("   ❌ 插件服务未找到")
            return False
        
        # 4. 检查可用插件
        print("4. 检查可用插件...")
        from core.services.plugin.PluginManager import plugin_manager
        available_plugins = plugin_manager.get_tool_window_plugins()
        
        print(f"   发现 {len(available_plugins)} 个可用插件:")
        for i, plugin in enumerate(available_plugins):
            print(f"   {i+1}. {plugin.name} v{plugin.version}")
        
        # 5. 选择测试插件
        print("5. 选择测试插件...")
        test_plugin = None
        
        # 优先选择性能监控器插件
        for plugin in available_plugins:
            if hasattr(plugin, 'name') and '性能监控器' in plugin.name:
                test_plugin = plugin
                break
        
        # 如果没有性能监控器，选择第一个可用插件
        if not test_plugin and available_plugins:
            test_plugin = available_plugins[0]
        
        if not test_plugin:
            print("   ❌ 没有可用的测试插件")
            return False
        
        print(f"   ✅ 使用测试插件: {test_plugin.name}")
        
        # 6. 创建并显示插件窗口
        print("6. 创建并显示插件窗口...")
        test_window = test_plugin.create_window(main_window)
        if not test_window:
            print("   ❌ 创建插件窗口失败")
            return False
        
        # 配置为悬浮窗口
        plugin_service._configure_plugin_window(test_window, test_plugin.name)
        plugin_service.plugin_windows[test_plugin.name] = test_window
        test_window.show()
        print("   ✅ 插件窗口已显示")
        
        # 7. 等待窗口完全显示
        print("7. 等待窗口完全显示...")
        QApplication.processEvents()
        time.sleep(1)
        
        # 8. 检查拖拽功能设置
        print("8. 检查拖拽功能设置...")
        has_event_filter = hasattr(test_window, '_drag_event_filter')
        has_mouse_override = hasattr(test_window, '_original_mousePressEvent')
        print(f"   事件过滤器: {'✅ 已设置' if has_event_filter else '❌ 未设置'}")
        print(f"   鼠标事件重写: {'✅ 已设置' if has_mouse_override else '❌ 未设置'}")
        
        # 9. 测试停靠区域检测
        print("9. 测试停靠区域检测...")
        from PyQt5.QtCore import QPoint
        
        # 获取主窗口几何信息
        main_geometry = main_window.frameGeometry()
        if main_geometry.isEmpty():
            main_geometry = main_window.geometry()
            main_global_pos = main_window.mapToGlobal(main_geometry.topLeft())
            main_geometry.moveTopLeft(main_global_pos)
        
        print(f"   主窗口几何信息: {main_geometry}")
        
        # 计算停靠区域
        dock_area_height = int(main_geometry.height() * 0.3)
        dock_area_top = main_geometry.top() + main_geometry.height() - dock_area_height
        margin = 10
        dock_area_left = main_geometry.left() + margin
        dock_area_right = main_geometry.right() - margin
        dock_area_top += margin
        dock_area_bottom = main_geometry.bottom() - margin
        
        print(f"   停靠区域: X({dock_area_left}-{dock_area_right}), Y({dock_area_top}-{dock_area_bottom})")
        
        # 测试不同位置
        test_points = [
            ("中心", QPoint(main_geometry.center().x(), main_geometry.center().y())),
            ("底部", QPoint(main_geometry.center().x(), dock_area_bottom - 50)),
            ("顶部", QPoint(main_geometry.center().x(), main_geometry.top() + 50)),
            ("外部", QPoint(main_geometry.right() + 100, main_geometry.center().y()))
        ]
        
        for name, point in test_points:
            is_in_dock = plugin_service._is_in_dock_area(test_window, point)
            status = "✅ 在停靠区域" if is_in_dock else "❌ 不在停靠区域"
            print(f"   测试点 {name} {point}: {status}")
        
        # 10. 显示使用说明
        print("\n📋 拖拽停靠功能使用说明:")
        print("1. 主窗口和插件窗口已显示")
        print("2. 强制悬浮模式已启用")
        print("3. 拖拽停靠功能已配置完成")
        print("4. 使用方法:")
        print("   • 点击并拖拽插件窗口的标题栏")
        print("   • 将窗口拖拽到主窗口底部30%区域")
        print("   • 观察窗口标题变化和鼠标光标变化")
        print("   • 在停靠区域释放鼠标完成自动停靠")
        print("5. 视觉反馈:")
        print("   • 窗口标题显示'释放鼠标停靠到主界面'")
        print("   • 鼠标光标变为手型指针")
        print("6. 调试信息:")
        print("   • 查看控制台输出获取详细状态信息")
        print("   • 日志标签: 🎯 [拖拽停靠] 和 🎯 [停靠区域]")
        
        print("\n🎉 拖拽停靠功能测试完成！")
        print("现在可以手动测试拖拽功能了。")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 运行测试
    success = test_final_drag_dock()
    
    if success:
        print("\n🎉 测试启动成功！现在可以手动测试拖拽停靠功能。")
        print("按 Ctrl+C 退出测试。")
        
        try:
            sys.exit(app.exec_())
        except KeyboardInterrupt:
            print("\n👋 测试结束")
    else:
        print("\n❌ 测试启动失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
