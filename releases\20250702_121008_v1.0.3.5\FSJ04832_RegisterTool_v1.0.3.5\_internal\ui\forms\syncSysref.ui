<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>sync_sysref</class>
 <widget class="QWidget" name="sync_sysref">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1733</width>
    <height>915</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QLabel" name="label">
   <property name="geometry">
    <rect>
     <x>-26</x>
     <y>-24</y>
     <width>1791</width>
     <height>978</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>微软雅黑</family>
    </font>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="pixmap">
    <pixmap resource="../qrc/syncSysref.qrc">:/syncSysref/SYNC&amp;SYSREF.bmp</pixmap>
   </property>
   <property name="scaledContents">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QCheckBox" name="SyncEn">
   <property name="geometry">
    <rect>
     <x>1186</x>
     <y>70</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 27px;  /* 设置方框宽度 */
                height: 27px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SysrefCLR">
   <property name="geometry">
    <rect>
     <x>1186</x>
     <y>100</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 27px;  /* 设置方框宽度 */
                height: 27px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QPushButton" name="pBtAllOn">
   <property name="geometry">
    <rect>
     <x>1463</x>
     <y>61</y>
     <width>87</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>All On</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pBtAllOff">
   <property name="geometry">
    <rect>
     <x>1555</x>
     <y>62</y>
     <width>86</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>All Off</string>
   </property>
  </widget>
  <widget class="QCheckBox" name="SYNCDIS0">
   <property name="geometry">
    <rect>
     <x>1467</x>
     <y>91</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 27px;  /* 设置方框宽度 */
                height: 27px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SYNCDIS2">
   <property name="geometry">
    <rect>
     <x>1467</x>
     <y>115</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 27px;  /* 设置方框宽度 */
                height: 27px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SYNCDIS4">
   <property name="geometry">
    <rect>
     <x>1467</x>
     <y>140</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 27px;  /* 设置方框宽度 */
                height: 27px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SYNCDIS6">
   <property name="geometry">
    <rect>
     <x>1467</x>
     <y>165</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 27px;  /* 设置方框宽度 */
                height: 27px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SYNCDIS8">
   <property name="geometry">
    <rect>
     <x>1467</x>
     <y>191</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 27px;  /* 设置方框宽度 */
                height: 27px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SYNCDIS10">
   <property name="geometry">
    <rect>
     <x>1467</x>
     <y>216</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 27px;  /* 设置方框宽度 */
                height: 27px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SYNCDIS12">
   <property name="geometry">
    <rect>
     <x>1468</x>
     <y>243</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 27px;  /* 设置方框宽度 */
                height: 27px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QPushButton" name="pBtSYNCDividers">
   <property name="geometry">
    <rect>
     <x>202</x>
     <y>72</y>
     <width>152</width>
     <height>104</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="text">
    <string>SYNC Dividers</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pBtNormal">
   <property name="geometry">
    <rect>
     <x>370</x>
     <y>72</y>
     <width>121</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="text">
    <string>Normal</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pBtReClocked">
   <property name="geometry">
    <rect>
     <x>515</x>
     <y>72</y>
     <width>121</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="text">
    <string>Re-Clocked</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pBtPulser">
   <property name="geometry">
    <rect>
     <x>658</x>
     <y>72</y>
     <width>117</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="text">
    <string>Pulser</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pBtContinuous">
   <property name="geometry">
    <rect>
     <x>800</x>
     <y>72</y>
     <width>121</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="text">
    <string>Continuous</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pBtSYSREFRequest">
   <property name="geometry">
    <rect>
     <x>940</x>
     <y>72</y>
     <width>121</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="text">
    <string>SYSREF-Request</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pBtNone">
   <property name="geometry">
    <rect>
     <x>410</x>
     <y>136</y>
     <width>151</width>
     <height>40</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="text">
    <string>None</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pBtSYNCPin">
   <property name="geometry">
    <rect>
     <x>590</x>
     <y>136</y>
     <width>152</width>
     <height>40</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="text">
    <string>SYNC Pin</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pBtCLKin0">
   <property name="geometry">
    <rect>
     <x>777</x>
     <y>136</y>
     <width>147</width>
     <height>40</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="text">
    <string>CLKin0</string>
   </property>
  </widget>
  <widget class="QCheckBox" name="SyncPLL1DLD">
   <property name="geometry">
    <rect>
     <x>157</x>
     <y>396</y>
     <width>38</width>
     <height>33</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="minimumSize">
    <size>
     <width>0</width>
     <height>0</height>
    </size>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 27px;  /* 设置方框宽度 */
                height: 27px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SyncPLL2DLD">
   <property name="geometry">
    <rect>
     <x>157</x>
     <y>426</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 27px;  /* 设置方框宽度 */
                height: 27px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SyncPOL">
   <property name="geometry">
    <rect>
     <x>208</x>
     <y>552</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 27px;  /* 设置方框宽度 */
                height: 27px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QComboBox" name="CLKin0Demux">
   <property name="geometry">
    <rect>
     <x>460</x>
     <y>235</y>
     <width>111</width>
     <height>27</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="comboSyncMode">
   <property name="geometry">
    <rect>
     <x>694</x>
     <y>450</y>
     <width>63</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="comboSysrefPulseCnt">
   <property name="geometry">
    <rect>
     <x>872</x>
     <y>665</y>
     <width>121</width>
     <height>27</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="comboSysrefMux">
   <property name="geometry">
    <rect>
     <x>1230</x>
     <y>672</y>
     <width>121</width>
     <height>31</height>
    </rect>
   </property>
  </widget>
  <widget class="QLineEdit" name="SyncSysrefFreq1">
   <property name="geometry">
    <rect>
     <x>1558</x>
     <y>469</y>
     <width>106</width>
     <height>27</height>
    </rect>
   </property>
  </widget>
  <widget class="QLineEdit" name="InternalVCOFreq">
   <property name="geometry">
    <rect>
     <x>29</x>
     <y>804</y>
     <width>121</width>
     <height>33</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="comboDDLYdSysrefStep">
   <property name="geometry">
    <rect>
     <x>638</x>
     <y>780</y>
     <width>111</width>
     <height>27</height>
    </rect>
   </property>
  </widget>
  <widget class="QSpinBox" name="spinBoxSysrefDIV">
   <property name="geometry">
    <rect>
     <x>305</x>
     <y>721</y>
     <width>115</width>
     <height>33</height>
    </rect>
   </property>
  </widget>
  <widget class="QSpinBox" name="spinBoxSysrefDDLY">
   <property name="geometry">
    <rect>
     <x>476</x>
     <y>721</y>
     <width>116</width>
     <height>34</height>
    </rect>
   </property>
  </widget>
  <widget class="QSpinBox" name="DDLYdStepCNT_1">
   <property name="geometry">
    <rect>
     <x>649</x>
     <y>720</y>
     <width>116</width>
     <height>34</height>
    </rect>
   </property>
  </widget>
  <widget class="QComboBox" name="comboVcoMode">
   <property name="geometry">
    <rect>
     <x>52</x>
     <y>685</y>
     <width>111</width>
     <height>31</height>
    </rect>
   </property>
  </widget>
  <widget class="QPushButton" name="SendPulse">
   <property name="geometry">
    <rect>
     <x>447</x>
     <y>540</y>
     <width>101</width>
     <height>33</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>SimSun-ExtB</family>
    </font>
   </property>
   <property name="text">
    <string>Send Pulse</string>
   </property>
  </widget>
  <widget class="QCheckBox" name="sysrefPD">
   <property name="geometry">
    <rect>
     <x>289</x>
     <y>626</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 27px;  /* 设置方框宽度 */
                height: 27px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SysrefReqEn">
   <property name="geometry">
    <rect>
     <x>952</x>
     <y>331</y>
     <width>38</width>
     <height>33</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="minimumSize">
    <size>
     <width>0</width>
     <height>0</height>
    </size>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 27px;  /* 设置方框宽度 */
                height: 27px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="SysrefPlsrPd">
   <property name="geometry">
    <rect>
     <x>849</x>
     <y>609</y>
     <width>38</width>
     <height>33</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="minimumSize">
    <size>
     <width>0</width>
     <height>0</height>
    </size>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 27px;  /* 设置方框宽度 */
                height: 27px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="sysrefDissysref">
   <property name="geometry">
    <rect>
     <x>204</x>
     <y>871</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 27px;  /* 设置方框宽度 */
                height: 27px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="sysrefDDLYPD">
   <property name="geometry">
    <rect>
     <x>439</x>
     <y>753</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 27px;  /* 设置方框宽度 */
                height: 27px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="sysrefDDLYdEn">
   <property name="geometry">
    <rect>
     <x>648</x>
     <y>812</y>
     <width>30</width>
     <height>30</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 27px;  /* 设置方框宽度 */
                height: 27px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QCheckBox" name="Sync1SHOTEn">
   <property name="geometry">
    <rect>
     <x>1421</x>
     <y>545</y>
     <width>38</width>
     <height>33</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="minimumSize">
    <size>
     <width>0</width>
     <height>0</height>
    </size>
   </property>
   <property name="styleSheet">
    <string notr="true">            QCheckBox::indicator {
                width: 29px;  /* 设置方框宽度 */
                height: 29px; /* 设置方框高度 */
            }</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
 </widget>
 <resources>
  <include location="../qrc/syncSysref.qrc"/>
  <include location="../qrc/syncSysref.qrc"/>
 </resources>
 <connections/>
</ui>
