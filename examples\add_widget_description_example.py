#!/usr/bin/env python3
"""
添加新控件说明的示例脚本
演示如何为新控件添加手动说明配置
"""

import json
import os
from datetime import datetime

def add_widget_description(widget_name, widget_config):
    """
    为指定控件添加手动说明配置
    
    Args:
        widget_name (str): 控件名称
        widget_config (dict): 控件配置信息
    """
    config_path = 'lib/widget_descriptions.json'
    
    # 检查配置文件是否存在
    if not os.path.exists(config_path):
        print(f"错误: 配置文件不存在 {config_path}")
        return False
    
    try:
        # 读取现有配置
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        # 添加新的控件配置
        config_data['widgets'][widget_name] = widget_config
        
        # 更新时间戳
        config_data['last_updated'] = datetime.now().strftime('%Y-%m-%d')
        
        # 写回配置文件
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=4)
        
        print(f"✅ 成功为控件 '{widget_name}' 添加手动说明配置")
        return True
        
    except Exception as e:
        print(f"❌ 添加控件说明配置失败: {str(e)}")
        return False

def example_add_new_widget():
    """示例：添加新控件说明"""
    
    # 示例1：添加一个新的频率显示控件
    new_widget_config = {
        "type": "频率显示控件",
        "source": "外部输入",
        "description": "这是一个示例控件，用于显示外部输入的频率值。该控件从外部硬件获取频率信息，并实时更新显示。用户可以通过此控件监控外部信号的频率状态。",
        "calculation": "直接从外部硬件读取",
        "dependencies": [
            "外部硬件连接",
            "信号检测模块"
        ],
        "is_manual_info": True
    }
    
    # 添加配置
    success = add_widget_description("ExampleFreqWidget", new_widget_config)
    
    if success:
        print("\n示例控件配置已添加，配置内容：")
        print(json.dumps(new_widget_config, ensure_ascii=False, indent=2))

def example_add_calculation_widget():
    """示例：添加计算类型控件说明"""
    
    calc_widget_config = {
        "type": "频率计算控件",
        "source": "多源计算",
        "description": "这是一个计算控件示例，用于根据多个输入源计算得出结果频率。该控件会监听多个相关控件的变化，并根据预定义的公式实时计算输出值。",
        "calculation": "输出频率 = (输入频率1 × 系数1 + 输入频率2 × 系数2) / 分频器",
        "dependencies": [
            "输入频率1控件",
            "输入频率2控件",
            "系数1设置",
            "系数2设置",
            "分频器设置"
        ],
        "is_manual_info": True
    }
    
    success = add_widget_description("ExampleCalcWidget", calc_widget_config)
    
    if success:
        print("\n计算控件配置已添加，配置内容：")
        print(json.dumps(calc_widget_config, ensure_ascii=False, indent=2))

def list_existing_widgets():
    """列出现有的控件配置"""
    config_path = 'lib/widget_descriptions.json'
    
    if not os.path.exists(config_path):
        print("配置文件不存在")
        return
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        widgets = config_data.get('widgets', {})
        print(f"\n当前配置的控件数量: {len(widgets)}")
        print("控件列表:")
        
        for widget_name, widget_config in widgets.items():
            widget_type = widget_config.get('type', '未知')
            source = widget_config.get('source', '未知')
            print(f"  - {widget_name}: {widget_type} (来源: {source})")
            
    except Exception as e:
        print(f"读取配置文件失败: {str(e)}")

def main():
    """主函数"""
    print("=== 控件手动说明配置示例 ===\n")
    
    # 列出现有控件
    print("1. 当前已配置的控件:")
    list_existing_widgets()
    
    print("\n" + "="*50)
    print("2. 添加示例控件配置:")
    
    # 添加示例控件
    example_add_new_widget()
    example_add_calculation_widget()
    
    print("\n" + "="*50)
    print("3. 更新后的控件列表:")
    list_existing_widgets()
    
    print("\n" + "="*50)
    print("使用说明:")
    print("1. 修改上述示例代码中的控件配置")
    print("2. 运行脚本添加新的控件说明")
    print("3. 在应用程序中打开信息面板插件")
    print("4. 将鼠标悬停在配置的控件上查看说明")
    print("5. 如需修改，编辑配置文件后点击'重新加载控件说明'")

if __name__ == "__main__":
    main()
