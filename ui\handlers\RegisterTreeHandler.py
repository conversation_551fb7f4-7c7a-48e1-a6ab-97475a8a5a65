from PyQt5.QtWidgets import (QTreeWidget, QTreeWidgetItem)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QColor, QBrush
from PyQt5.QtWidgets import QAbstractItemView # For QAbstractItemView.ScrollHint
from utils.Log import get_module_logger

logger = get_module_logger(__name__)

class RegisterTreeHandler:
    """处理寄存器树视图相关的所有操作"""
    
    def __init__(self, parent=None, register_manager=None):
        self.parent = parent
        self.register_manager = register_manager
        self.tree_widget = None
        self.register_items = {}  # 存储寄存器项的引用
        
    def create_tree_widget(self):
        """创建寄存器树视图"""
        self.tree_widget = QTreeWidget()
        self.tree_widget.setHeaderLabels(["Registers"])
        self.tree_widget.headerItem().setHidden(True)  # 隐藏表头
        self.tree_widget.itemClicked.connect(self.on_tree_item_clicked)

        # 设置最小宽度，确保能完整显示所有寄存器名称，防止布局抖动
        # 计算最长寄存器名称的宽度：如 "R42 (0x2A)" 大约需要 120-150 像素
        # 增加宽度以确保所有寄存器名称都能完整显示
        min_width = 250  # 增加宽度，足够显示完整的寄存器名称和地址
        self.tree_widget.setMinimumWidth(min_width)

        # 设置合适的尺寸策略，防止因内容变化导致布局重新计算
        from PyQt5.QtWidgets import QSizePolicy
        self.tree_widget.setSizePolicy(QSizePolicy.Minimum, QSizePolicy.Expanding)

        logger.debug(f"TreeWidget设置最小宽度: {min_width}px，防止布局抖动")

        return self.tree_widget
        
    def populate_tree_widget(self):
        """填充寄存器树视图"""
        if not self.tree_widget or not self.register_manager:
            return
            
        # 清空树视图
        self.tree_widget.clear()
        self.register_items = {}
        
        # 创建根节点
        root_item = QTreeWidgetItem(["Registers"])
        self.tree_widget.addTopLevelItem(root_item)
        
        # 获取所有寄存器
        registers = self.register_manager.get_all_registers()
        
        # 按地址排序添加寄存器节点
        for register in sorted(registers, key=lambda r: int(r.get("address", 0) if isinstance(r.get("address", 0), int) else 0)):
            register.get("name", "")
            reg_addr = register.get("address", "")
            
            # 格式化显示R0 (0x00)等形式
            addr_int = reg_addr if isinstance(reg_addr, int) else int(reg_addr, 16) if isinstance(reg_addr, str) and reg_addr.startswith("0x") else 0
            display_text = f"R{addr_int} (0x{addr_int:02X})"
            
            # 创建寄存器项
            reg_item = QTreeWidgetItem([display_text])
            
            # 保存寄存器地址作为用户数据
            reg_item.setData(0, Qt.UserRole, reg_addr)
            
            # 添加到根节点
            root_item.addChild(reg_item)
            
            # 保存引用以便后续更新
            self.register_items[reg_addr] = reg_item
                
        # 展开根节点
        root_item.setExpanded(True)
        
    def select_default_register(self):
        """选择默认寄存器"""
        if not self.tree_widget:
            return
            
        # 如果有根节点且有子项，选择第一个寄存器
        if self.tree_widget.topLevelItemCount() > 0:
            root_item = self.tree_widget.topLevelItem(0)
            if root_item.childCount() > 0:
                reg_item = root_item.child(0)
                logger.info(f"TreeHandler: Selecting default register: '{reg_item.text(0)}'")
                self.tree_widget.setCurrentItem(reg_item)
                self.on_tree_item_clicked(reg_item, 0)
    
    def on_tree_item_clicked(self, item, column):
        """处理树项点击事件"""
        if item is None:
            logger.debug("TreeHandler: on_tree_item_clicked called with None item.")
            return

        # 确保处理的是当前实际选中的项
        if self.tree_widget and self.tree_widget.currentItem() != item:
            logger.debug(f"TreeHandler: on_tree_item_clicked - Clicked item '{item.text(0)}' is not the current item '{self.tree_widget.currentItem().text(0) if self.tree_widget.currentItem() else 'None'}'. Ignoring.")
            return

        reg_addr = item.data(0, Qt.UserRole)
        item_text = item.text(0) # Get display text for logging
        logger.info(f"TreeHandler: on_tree_item_clicked - Item: '{item_text}', Addr: {reg_addr}, Column: {column}")
        
        # 只处理寄存器项（有地址的项）
        if reg_addr is not None and self.parent and hasattr(self.parent, "on_register_selected"):
            # 检查是否是相同的寄存器地址，避免重复触发
            if hasattr(self, '_last_selected_addr') and self._last_selected_addr == reg_addr:
                logger.debug(f"TreeHandler: Ignoring duplicate selection for Addr: {reg_addr}")
                return
            
            logger.info(f"TreeHandler: Calling parent.on_register_selected for Addr: {reg_addr} from item '{item_text}'")
            self.parent.on_register_selected(reg_addr)
            self._last_selected_addr = reg_addr  # 记录最后选中的地址
        elif reg_addr is None:
            logger.debug(f"TreeHandler: on_tree_item_clicked - reg_addr is None for item '{item_text}', not calling parent.")
        else: # reg_addr is not None, but parent or method is missing
            logger.warning(f"TreeHandler: on_tree_item_clicked - Parent or on_register_selected method missing for item '{item_text}'.")
    
    def update_register_value(self, addr, value):
        """更新寄存器值显示"""
        # 在批量操作期间跳过树控件更新，防止布局抖动
        if hasattr(self.parent, 'is_batch_reading') and self.parent.is_batch_reading:
            return
        if hasattr(self.parent, 'is_batch_writing') and self.parent.is_batch_writing:
            return
        if hasattr(self.parent, 'is_batch_updating') and self.parent.is_batch_updating:
            return

        if addr in self.register_items:
            reg_item = self.register_items[addr]
            reg_item.setText(2, f"0x{value:04X}" if isinstance(value, int) else str(value))
    
    def get_current_register_addr(self):
        """获取当前选中的寄存器地址"""
        if not self.tree_widget:
            return None
            
        current_item = self.tree_widget.currentItem()
        if not current_item:
            return None
            
        return current_item.data(0, Qt.UserRole)
    
    def select_register_by_addr(self, addr):
        """通过地址选择寄存器"""
        if addr in self.register_items:
            self.tree_widget.setCurrentItem(self.register_items[addr])
            self.on_tree_item_clicked(self.register_items[addr], 0)
    
    def find_register_by_name(self, name):
        """通过名称查找寄存器"""
        if not self.tree_widget:
            return None
            
        # 搜索所有项
        found_items = []
        for group_idx in range(self.tree_widget.topLevelItemCount()):
            group_item = self.tree_widget.topLevelItem(group_idx)
            
            for reg_idx in range(group_item.childCount()):
                reg_item = group_item.child(reg_idx)
                reg_name = reg_item.text(0)
                
                if name.lower() in reg_name.lower():
                    found_items.append(reg_item)
                    
        return found_items

    def filter_registers_by_bit_field_name(self, keyword):
        """根据位段名称关键字过滤树视图中的寄存器项.
        所有寄存器保持可见，匹配的寄存器高亮显示。
        优先选择位段名称与关键字完全匹配的寄存器项；若无完全匹配，则选择第一个部分匹配的项。
        """
        if not self.tree_widget or not self.register_manager:
            logger.warning("TreeHandler: 树控件或寄存器管理器未初始化，无法过滤")
            return

        keyword_lower = keyword.lower().strip()
        logger.info(f"TreeHandler: 开始过滤寄存器树, 关键字: '{keyword_lower}'")

        exact_match_item = None
        first_partial_match_item = None
        found_any_match = False

        default_brush = QBrush(self.tree_widget.palette().base())
        highlight_brush = QBrush(QColor(255, 255, 0))  # Yellow

        # First pass: Reset all backgrounds and ensure all items are visible
        for i in range(self.tree_widget.topLevelItemCount()):
            root_item = self.tree_widget.topLevelItem(i)
            for j in range(root_item.childCount()):
                reg_item = root_item.child(j)
                reg_item.setBackground(0, default_brush)
                reg_item.setHidden(False) 

        if keyword_lower:  # Only proceed if keyword is not empty
            # Second pass: Find matches and highlight
            for i in range(self.tree_widget.topLevelItemCount()):
                root_item = self.tree_widget.topLevelItem(i)
                for j in range(root_item.childCount()):
                    reg_item = root_item.child(j)
                    reg_addr = reg_item.data(0, Qt.UserRole)

                    if reg_addr is None:
                        continue

                    item_is_exact_match = False
                    item_is_partial_match = False
                    
                    register_info = self.register_manager.get_register_info(reg_addr)
                    if register_info:
                        bit_fields = register_info.get("bits", []) or register_info.get("bit_fields", [])
                        for bit_field in bit_fields:
                            field_name = bit_field.get("name", "").lower()
                            if keyword_lower == field_name: 
                                item_is_exact_match = True
                                found_any_match = True
                                break 
                            elif keyword_lower in field_name: 
                                item_is_partial_match = True
                                found_any_match = True
                        
                        if item_is_exact_match:
                            reg_item.setBackground(0, highlight_brush)
                            if exact_match_item is None: 
                                exact_match_item = reg_item
                        elif item_is_partial_match: 
                            reg_item.setBackground(0, highlight_brush)
                            if first_partial_match_item is None:
                                first_partial_match_item = reg_item
                    else:
                        logger.warning(f"TreeHandler: 无法获取寄存器 {reg_addr} 的信息，跳过此寄存器。")
                        continue
            
            item_to_select = None
            if exact_match_item:
                item_to_select = exact_match_item
                logger.info(f"TreeHandler: 找到完全匹配关键字 '{keyword_lower}' 的寄存器，将优先选中。")
            elif first_partial_match_item:
                item_to_select = first_partial_match_item
                logger.info(f"TreeHandler: 未找到完全匹配，选中第一个部分匹配关键字 '{keyword_lower}' 的寄存器。")

            if item_to_select:
                # 检查是否是当前已选中的项，避免重复触发
                current_item = self.tree_widget.currentItem()
                if current_item and current_item.text(0) == item_to_select.text(0):
                    logger.debug(f"TreeHandler: Item '{item_to_select.text(0)}' is already selected, skipping reselection.")
                    return
                
                logger.info(f"TreeHandler: Intending to select item '{item_to_select.text(0)}'.")
                # Disconnect itemClicked to prevent it firing during programmatic selection
                try:
                    self.tree_widget.itemClicked.disconnect(self.on_tree_item_clicked)
                    logger.debug("TreeHandler: itemClicked signal temporarily disconnected.")
                except TypeError:
                    logger.debug("TreeHandler: itemClicked signal was not connected, no action needed for disconnect.")
                except Exception as e:
                    logger.error(f"TreeHandler: Error disconnecting itemClicked signal: {e}")

                self.tree_widget.setCurrentItem(item_to_select)
                self.tree_widget.scrollToItem(item_to_select, QAbstractItemView.ScrollHint.EnsureVisible)
                
                current_item_in_tree = self.tree_widget.currentItem()
                logger.info(f"TreeHandler: Programmatically set currentItem to '{item_to_select.text(0)}'. "
                            f"Actual currentItem in tree: '{current_item_in_tree.text(0) if current_item_in_tree else 'None'}'. "
                            f"Calling on_tree_item_clicked directly with intended item: '{item_to_select.text(0)}'.")
                
                # Explicitly call the handler with the correct item
                self.on_tree_item_clicked(item_to_select, 0)
                
                # Reconnect the itemClicked signal for future user interactions
                try:
                    # To be safe, attempt disconnect again in case of unusual state, then connect.
                    try:
                        self.tree_widget.itemClicked.disconnect(self.on_tree_item_clicked)
                    except TypeError:
                        pass
                    self.tree_widget.itemClicked.connect(self.on_tree_item_clicked)
                    logger.debug("TreeHandler: itemClicked signal reconnected after programmatic selection.")
                except Exception as e:
                    logger.error(f"TreeHandler: Error reconnecting itemClicked signal: {e}")

                logger.info(f"TreeHandler: Filtering complete, item '{item_to_select.text(0)}' processed.")
            elif not found_any_match and keyword_lower: 
                logger.info(f"TreeHandler: 未找到与关键字 '{keyword_lower}' 匹配的位段。")
        else:
            for i in range(self.tree_widget.topLevelItemCount()):
                root_item = self.tree_widget.topLevelItem(i)
                for j in range(root_item.childCount()):
                    reg_item = root_item.child(j)
                    reg_item.setBackground(0, default_brush) 
                    reg_item.setHidden(False)
            self.tree_widget.setCurrentItem(None) 
            if self.parent and hasattr(self.parent, "clear_register_details_view"):
                 self.parent.clear_register_details_view() 
            logger.info("TreeHandler: 关键字为空，显示所有寄存器，已清除高亮和选择。")