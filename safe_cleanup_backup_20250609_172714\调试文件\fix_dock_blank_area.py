#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
停靠空白区域修复脚本
专门用于修复插件窗口停靠后显示空白的问题
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QPushButton, QTextEdit, QLabel
from PyQt5.QtCore import QTimer
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class DockBlankAreaFixWindow(QWidget):
    """停靠空白区域修复窗口"""
    
    def __init__(self):
        super().__init__()
        self.main_window = None
        self.plugin_service = None
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("停靠空白区域修复")
        self.setGeometry(100, 100, 900, 700)
        
        layout = QVBoxLayout()
        
        # 状态标签
        self.status_label = QLabel("准备修复停靠空白区域...")
        layout.addWidget(self.status_label)
        
        # 修复按钮
        self.find_main_window_btn = QPushButton("1. 查找主窗口")
        self.find_main_window_btn.clicked.connect(self.find_main_window)
        layout.addWidget(self.find_main_window_btn)
        
        self.open_test_window_btn = QPushButton("2. 打开测试窗口")
        self.open_test_window_btn.clicked.connect(self.open_test_window)
        layout.addWidget(self.open_test_window_btn)
        
        self.dock_window_btn = QPushButton("3. 停靠窗口")
        self.dock_window_btn.clicked.connect(self.dock_window)
        layout.addWidget(self.dock_window_btn)
        
        self.check_tab_content_btn = QPushButton("4. 检查标签页内容")
        self.check_tab_content_btn.clicked.connect(self.check_tab_content)
        layout.addWidget(self.check_tab_content_btn)
        
        self.fix_blank_area_btn = QPushButton("5. 修复空白区域")
        self.fix_blank_area_btn.clicked.connect(self.fix_blank_area)
        layout.addWidget(self.fix_blank_area_btn)
        
        self.verify_fix_btn = QPushButton("6. 验证修复结果")
        self.verify_fix_btn.clicked.connect(self.verify_fix)
        layout.addWidget(self.verify_fix_btn)
        
        # 日志文本框
        self.log_text = QTextEdit()
        layout.addWidget(self.log_text)
        
        self.setLayout(layout)
        
    def find_main_window(self):
        """查找主窗口"""
        try:
            self.log_text.append("🔍 查找主窗口...")
            
            app = QApplication.instance()
            for widget in app.topLevelWidgets():
                if hasattr(widget, 'plugin_integration_service') or hasattr(widget, 'plugin_service'):
                    self.main_window = widget
                    break
            
            if self.main_window:
                self.plugin_service = getattr(self.main_window, 'plugin_integration_service', None) or \
                                    getattr(self.main_window, 'plugin_service', None)
                
                self.status_label.setText("✅ 找到主窗口和插件服务")
                self.log_text.append("✅ 主窗口和插件服务已找到")
                self.log_text.append(f"   - 窗口类型: {type(self.main_window).__name__}")
                self.log_text.append(f"   - 插件服务类型: {type(self.plugin_service).__name__}")
                
                # 检查标签页容器
                if hasattr(self.main_window, 'tools_tab_widget'):
                    tab_widget = self.main_window.tools_tab_widget
                    self.log_text.append(f"   - 标签页容器: ✅ (当前标签数: {tab_widget.count()})")
                else:
                    self.log_text.append("   - 标签页容器: ❌")
            else:
                self.status_label.setText("❌ 未找到主窗口")
                self.log_text.append("❌ 未找到主窗口")
                
        except Exception as e:
            error_msg = f"查找主窗口失败: {str(e)}"
            self.status_label.setText(error_msg)
            self.log_text.append(f"❌ {error_msg}")
            
    def open_test_window(self):
        """打开测试窗口"""
        try:
            if not self.plugin_service:
                self.log_text.append("❌ 插件服务未找到")
                return
            
            self.log_text.append("🔄 打开测试窗口...")
            
            # 获取插件管理器
            from core.services.plugin.PluginManager import plugin_manager
            tool_plugins = plugin_manager.get_tool_window_plugins()
            
            # 选择时钟输入控制插件进行测试
            test_plugin = None
            for plugin in tool_plugins:
                if plugin.name == '时钟输入控制':
                    test_plugin = plugin
                    break
            
            if test_plugin:
                try:
                    self.plugin_service._on_plugin_action_triggered(test_plugin, True)
                    self.test_plugin = test_plugin
                    self.log_text.append(f"✅ 已打开测试窗口: {test_plugin.name}")
                    
                    # 延迟检查窗口状态
                    QTimer.singleShot(1000, self.check_window_status)
                    
                except Exception as e:
                    self.log_text.append(f"❌ 打开测试窗口失败: {str(e)}")
            else:
                self.log_text.append("❌ 未找到时钟输入控制插件")
                
        except Exception as e:
            self.log_text.append(f"❌ 打开测试窗口失败: {str(e)}")
            
    def check_window_status(self):
        """检查窗口状态"""
        try:
            self.log_text.append("📊 检查窗口状态...")
            
            if hasattr(self, 'test_plugin'):
                plugin_name = self.test_plugin.name
                
                # 检查插件窗口是否存在
                if hasattr(self.plugin_service, 'window_service'):
                    window = self.plugin_service.window_service.get_plugin_window('clkin_control_plugin')
                    if window:
                        self.log_text.append(f"   - 插件窗口: ✅ (类型: {type(window).__name__})")
                        self.log_text.append(f"   - 窗口可见: {window.isVisible()}")
                        self.log_text.append(f"   - 窗口大小: {window.size().width()}x{window.size().height()}")
                        
                        # 检查窗口内容
                        if hasattr(window, 'content_widget'):
                            content = window.content_widget
                            self.log_text.append(f"   - 内容控件: ✅ (大小: {content.size().width()}x{content.size().height()})")
                            self.log_text.append(f"   - 内容可见: {content.isVisible()}")
                            
                            # 检查子控件数量
                            children = content.findChildren(QWidget)
                            self.log_text.append(f"   - 子控件数量: {len(children)}")
                        else:
                            self.log_text.append("   - 内容控件: ❌")
                    else:
                        self.log_text.append("   - 插件窗口: ❌")
                
                # 检查是否已停靠
                if hasattr(self.plugin_service, 'dock_service'):
                    is_docked = self.plugin_service.dock_service._is_window_already_docked('clkin_control_plugin')
                    self.log_text.append(f"   - 已停靠: {is_docked}")
                    
        except Exception as e:
            self.log_text.append(f"❌ 检查窗口状态失败: {str(e)}")
            
    def dock_window(self):
        """停靠窗口"""
        try:
            if not self.plugin_service or not hasattr(self, 'test_plugin'):
                self.log_text.append("❌ 插件服务或测试窗口未准备好")
                return
            
            self.log_text.append("🔄 停靠窗口...")
            
            # 执行停靠操作
            success = self.plugin_service.dock_floating_window('clkin_control_plugin')
            if success:
                self.log_text.append("✅ 窗口停靠成功")
                
                # 延迟检查停靠结果
                QTimer.singleShot(1000, self.check_dock_result)
            else:
                self.log_text.append("❌ 窗口停靠失败")
                
        except Exception as e:
            self.log_text.append(f"❌ 停靠窗口失败: {str(e)}")
            
    def check_dock_result(self):
        """检查停靠结果"""
        try:
            self.log_text.append("📊 检查停靠结果...")
            
            if not hasattr(self.main_window, 'tools_tab_widget'):
                self.log_text.append("❌ 标签页容器未找到")
                return
            
            tab_widget = self.main_window.tools_tab_widget
            tab_count = tab_widget.count()
            
            self.log_text.append(f"   - 标签页数量: {tab_count}")
            self.log_text.append(f"   - 标签页容器可见: {tab_widget.isVisible()}")
            
            if tab_count > 0:
                for i in range(tab_count):
                    tab_text = tab_widget.tabText(i)
                    container = tab_widget.widget(i)
                    
                    self.log_text.append(f"   - 标签页 {i+1}: {tab_text}")
                    
                    if container:
                        self.log_text.append(f"     容器类型: {type(container).__name__}")
                        self.log_text.append(f"     容器大小: {container.size().width()}x{container.size().height()}")
                        self.log_text.append(f"     容器可见: {container.isVisible()}")
                        
                        # 检查容器布局
                        layout = container.layout()
                        if layout:
                            self.log_text.append(f"     布局类型: {type(layout).__name__}")
                            self.log_text.append(f"     布局项数量: {layout.count()}")
                            
                            # 检查布局中的窗口
                            if layout.count() > 0:
                                item = layout.itemAt(0)
                                if item:
                                    widget = item.widget()
                                    if widget:
                                        self.log_text.append(f"     内容窗口: ✅ (类型: {type(widget).__name__})")
                                        self.log_text.append(f"     内容大小: {widget.size().width()}x{widget.size().height()}")
                                        self.log_text.append(f"     内容可见: {widget.isVisible()}")
                                        
                                        # 这里是关键：检查内容是否真的有东西
                                        if hasattr(widget, 'content_widget'):
                                            content = widget.content_widget
                                            children = content.findChildren(QWidget)
                                            self.log_text.append(f"     子控件数量: {len(children)}")
                                            
                                            if len(children) == 0:
                                                self.log_text.append("     ⚠️ 内容为空！这就是空白区域的原因")
                                        else:
                                            self.log_text.append("     ⚠️ 没有content_widget属性")
                                    else:
                                        self.log_text.append("     内容窗口: ❌ (布局项没有窗口)")
                                else:
                                    self.log_text.append("     内容窗口: ❌ (布局项为空)")
                            else:
                                self.log_text.append("     内容窗口: ❌ (布局为空)")
                        else:
                            self.log_text.append("     布局: ❌")
                    else:
                        self.log_text.append("     容器: ❌")
            else:
                self.log_text.append("   - 没有标签页")
                
        except Exception as e:
            self.log_text.append(f"❌ 检查停靠结果失败: {str(e)}")
            
    def check_tab_content(self):
        """检查标签页内容"""
        try:
            self.log_text.append("🔍 详细检查标签页内容...")
            
            if not hasattr(self.main_window, 'tools_tab_widget'):
                self.log_text.append("❌ 标签页容器未找到")
                return
            
            tab_widget = self.main_window.tools_tab_widget
            
            if tab_widget.count() == 0:
                self.log_text.append("❌ 没有标签页")
                return
            
            # 检查当前标签页
            current_index = tab_widget.currentIndex()
            container = tab_widget.widget(current_index)
            
            if not container:
                self.log_text.append("❌ 当前标签页容器为空")
                return
            
            self.log_text.append(f"📊 当前标签页详细信息:")
            self.log_text.append(f"   - 索引: {current_index}")
            self.log_text.append(f"   - 标题: {tab_widget.tabText(current_index)}")
            
            # 检查容器属性
            if hasattr(container, 'plugin_name'):
                self.log_text.append(f"   - 插件名称: {container.plugin_name}")
            
            if hasattr(container, 'plugin_window'):
                plugin_window = container.plugin_window
                self.log_text.append(f"   - 插件窗口: ✅ (类型: {type(plugin_window).__name__})")
                
                # 检查插件窗口的内容
                if hasattr(plugin_window, 'content_widget'):
                    content = plugin_window.content_widget
                    self.log_text.append(f"   - 内容控件: ✅")
                    self.log_text.append(f"   - 内容大小: {content.size().width()}x{content.size().height()}")
                    self.log_text.append(f"   - 内容可见: {content.isVisible()}")
                    
                    # 检查UI是否已设置
                    if hasattr(plugin_window, 'ui'):
                        self.log_text.append(f"   - UI已设置: ✅")
                        
                        # 检查UI的根控件
                        ui_widgets = []
                        for attr_name in dir(plugin_window.ui):
                            if not attr_name.startswith('_'):
                                attr = getattr(plugin_window.ui, attr_name)
                                if isinstance(attr, QWidget):
                                    ui_widgets.append(attr_name)
                        
                        self.log_text.append(f"   - UI控件数量: {len(ui_widgets)}")
                        
                        if len(ui_widgets) > 0:
                            self.log_text.append("   - 部分UI控件:")
                            for i, widget_name in enumerate(ui_widgets[:5]):  # 只显示前5个
                                widget = getattr(plugin_window.ui, widget_name)
                                self.log_text.append(f"     {i+1}. {widget_name}: {type(widget).__name__}")
                    else:
                        self.log_text.append(f"   - UI已设置: ❌ (这可能是空白的原因)")
                else:
                    self.log_text.append(f"   - 内容控件: ❌")
            else:
                self.log_text.append(f"   - 插件窗口: ❌")
                
        except Exception as e:
            self.log_text.append(f"❌ 检查标签页内容失败: {str(e)}")
            
    def fix_blank_area(self):
        """修复空白区域"""
        try:
            self.log_text.append("🔧 开始修复空白区域...")
            
            if not hasattr(self.main_window, 'tools_tab_widget'):
                self.log_text.append("❌ 标签页容器未找到")
                return
            
            tab_widget = self.main_window.tools_tab_widget
            
            if tab_widget.count() == 0:
                self.log_text.append("❌ 没有标签页需要修复")
                return
            
            fixed_count = 0
            
            for i in range(tab_widget.count()):
                tab_text = tab_widget.tabText(i)
                container = tab_widget.widget(i)
                
                self.log_text.append(f"🔧 修复标签页 {i+1}: {tab_text}")
                
                if not container:
                    self.log_text.append(f"   ❌ 容器为空，跳过")
                    continue
                
                # 获取插件窗口
                plugin_window = None
                if hasattr(container, 'plugin_window'):
                    plugin_window = container.plugin_window
                elif container.layout() and container.layout().count() > 0:
                    item = container.layout().itemAt(0)
                    if item:
                        plugin_window = item.widget()
                
                if not plugin_window:
                    self.log_text.append(f"   ❌ 插件窗口未找到，跳过")
                    continue
                
                # 修复步骤1：确保插件窗口可见
                if not plugin_window.isVisible():
                    plugin_window.setVisible(True)
                    self.log_text.append(f"   ✅ 设置插件窗口可见")
                
                # 修复步骤2：确保内容控件可见
                if hasattr(plugin_window, 'content_widget'):
                    content = plugin_window.content_widget
                    if not content.isVisible():
                        content.setVisible(True)
                        self.log_text.append(f"   ✅ 设置内容控件可见")
                    
                    # 修复步骤3：强制更新布局
                    content.updateGeometry()
                    content.update()
                    self.log_text.append(f"   ✅ 更新内容控件几何和显示")
                
                # 修复步骤4：强制更新插件窗口布局
                plugin_window.updateGeometry()
                plugin_window.update()
                
                # 修复步骤5：强制更新容器布局
                container.updateGeometry()
                container.update()
                
                # 修复步骤6：确保标签页容器刷新
                tab_widget.update()
                
                self.log_text.append(f"   ✅ 标签页 {tab_text} 修复完成")
                fixed_count += 1
            
            self.log_text.append(f"🎉 修复完成！共修复 {fixed_count} 个标签页")
            self.status_label.setText(f"修复完成！共修复 {fixed_count} 个标签页")
            
            # 延迟验证修复结果
            QTimer.singleShot(1000, self.verify_fix)
            
        except Exception as e:
            error_msg = f"修复空白区域失败: {str(e)}"
            self.status_label.setText(error_msg)
            self.log_text.append(f"❌ {error_msg}")
            
    def verify_fix(self):
        """验证修复结果"""
        try:
            self.log_text.append("🔍 验证修复结果...")
            
            if not hasattr(self.main_window, 'tools_tab_widget'):
                self.log_text.append("❌ 标签页容器未找到")
                return
            
            tab_widget = self.main_window.tools_tab_widget
            
            if tab_widget.count() == 0:
                self.log_text.append("❌ 没有标签页")
                return
            
            success_count = 0
            total_count = tab_widget.count()
            
            for i in range(total_count):
                tab_text = tab_widget.tabText(i)
                container = tab_widget.widget(i)
                
                if container and container.isVisible():
                    # 检查是否有内容
                    has_content = False
                    
                    if hasattr(container, 'plugin_window'):
                        plugin_window = container.plugin_window
                        if plugin_window and plugin_window.isVisible():
                            if hasattr(plugin_window, 'content_widget'):
                                content = plugin_window.content_widget
                                if content and content.isVisible():
                                    children = content.findChildren(QWidget)
                                    if len(children) > 0:
                                        has_content = True
                    
                    if has_content:
                        self.log_text.append(f"   ✅ {tab_text}: 有内容")
                        success_count += 1
                    else:
                        self.log_text.append(f"   ❌ {tab_text}: 仍然空白")
                else:
                    self.log_text.append(f"   ❌ {tab_text}: 容器不可见")
            
            if success_count == total_count:
                self.status_label.setText("🎉 修复成功！所有标签页都有内容")
                self.log_text.append("🎉 修复成功！所有标签页都显示正常")
            else:
                self.status_label.setText(f"⚠️ 部分修复成功 ({success_count}/{total_count})")
                self.log_text.append(f"⚠️ 部分修复成功，{success_count}/{total_count} 个标签页正常")
                
        except Exception as e:
            self.log_text.append(f"❌ 验证修复结果失败: {str(e)}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建修复窗口
    fix_window = DockBlankAreaFixWindow()
    fix_window.show()
    
    print("🔧 停靠空白区域修复")
    print("请按照界面上的按钮顺序进行修复：")
    print("1. 查找主窗口")
    print("2. 打开测试窗口")
    print("3. 停靠窗口")
    print("4. 检查标签页内容")
    print("5. 修复空白区域")
    print("6. 验证修复结果")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
