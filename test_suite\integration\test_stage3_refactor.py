#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
第三阶段重构验证脚本
验证配置管理、窗口管理和寄存器业务逻辑服务是否正常工作
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_configuration_service():
    """测试配置管理服务"""
    print("=== 测试配置管理服务 ===")
    
    try:
        from core.services.config.ConfigurationService import ConfigurationService
        print("✓ ConfigurationService 导入成功")
        
        # 检查关键方法
        methods = [
            'save_register_config', 'load_register_config',
            'get_setting', 'set_setting', 'toggle_preload',
            'set_preload_count', 'set_language'
        ]
        
        for method in methods:
            if hasattr(ConfigurationService, method):
                print(f"✓ ConfigurationService.{method} 存在")
            else:
                print(f"✗ ConfigurationService.{method} 缺失")
                return False
        
        return True
        
    except ImportError as e:
        print(f"✗ 配置管理服务导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 配置管理服务测试失败: {e}")
        return False

def test_window_management_service():
    """测试窗口管理服务"""
    print("\n=== 测试窗口管理服务 ===")
    
    try:
        from core.services.ui.WindowManagementService import WindowManagementService
        print("✓ WindowManagementService 导入成功")
        
        # 检查关键方法
        methods = [
            'create_window_in_tab', 'handle_window_closed',
            'close_all_windows', 'get_window_count',
            'is_window_open', 'refresh_all_windows'
        ]
        
        for method in methods:
            if hasattr(WindowManagementService, method):
                print(f"✓ WindowManagementService.{method} 存在")
            else:
                print(f"✗ WindowManagementService.{method} 缺失")
                return False
        
        return True
        
    except ImportError as e:
        print(f"✗ 窗口管理服务导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 窗口管理服务测试失败: {e}")
        return False

def test_register_operation_service():
    """测试寄存器操作服务"""
    print("\n=== 测试寄存器操作服务 ===")
    
    try:
        from core.services.register.RegisterOperationService import RegisterOperationService
        print("✓ RegisterOperationService 导入成功")
        
        # 检查关键方法
        methods = [
            'read_register', 'write_register', 'get_all_registers',
            'validate_register_address', 'get_register_info',
            'refresh_register_display'
        ]
        
        for method in methods:
            if hasattr(RegisterOperationService, method):
                print(f"✓ RegisterOperationService.{method} 存在")
            else:
                print(f"✗ RegisterOperationService.{method} 缺失")
                return False
        
        return True
        
    except ImportError as e:
        print(f"✗ 寄存器操作服务导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 寄存器操作服务测试失败: {e}")
        return False

def test_service_integration():
    """测试服务集成"""
    print("\n=== 测试服务集成 ===")
    
    try:
        # 测试主窗口是否能正确导入所有服务
        from ui.components.MainWindowUI import MainWindowUI
        from ui.components.MenuManager import MenuManager
        from ui.controllers.BatchOperationController import BatchOperationController
        from core.services.config.ConfigurationService import ConfigurationService
        from core.services.ui.WindowManagementService import WindowManagementService
        from core.services.register.RegisterOperationService import RegisterOperationService
        
        print("✓ 所有服务组件导入成功")
        
        # 检查服务目录结构
        service_dirs = [
            "core/services/config",
            "core/services/ui", 
            "core/services/register"
        ]
        
        for service_dir in service_dirs:
            if os.path.exists(service_dir):
                print(f"✓ 服务目录存在: {service_dir}")
            else:
                print(f"✗ 服务目录缺失: {service_dir}")
                return False
        
        return True
        
    except ImportError as e:
        print(f"✗ 服务集成测试失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 服务集成测试出错: {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    print("\n=== 测试文件结构 ===")
    
    try:
        # 检查新增的服务文件
        service_files = [
            "core/services/__init__.py",
            "core/services/config/__init__.py",
            "core/services/config/ConfigurationService.py",
            "core/services/ui/__init__.py",
            "core/services/ui/WindowManagementService.py",
            "core/services/register/__init__.py",
            "core/services/register/RegisterOperationService.py"
        ]
        
        for file_path in service_files:
            if os.path.exists(file_path):
                print(f"✓ 服务文件存在: {file_path}")
            else:
                print(f"✗ 服务文件缺失: {file_path}")
                return False
        
        # 检查文件大小
        for file_path in service_files:
            if file_path.endswith('.py') and not file_path.endswith('__init__.py'):
                size = os.path.getsize(file_path)
                if size > 1000:  # 至少1KB
                    print(f"✓ 服务文件有内容: {file_path} ({size} bytes)")
                else:
                    print(f"⚠ 服务文件较小: {file_path} ({size} bytes)")
        
        return True
        
    except Exception as e:
        print(f"✗ 文件结构测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始第三阶段重构验证...")
    print("=" * 60)
    
    # 执行各项测试
    tests = [
        ("配置管理服务", test_configuration_service),
        ("窗口管理服务", test_window_management_service),
        ("寄存器操作服务", test_register_operation_service),
        ("服务集成", test_service_integration),
        ("文件结构", test_file_structure)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} 测试出错: {e}")
            results.append((test_name, False))
    
    # 输出总结
    print("\n" + "=" * 60)
    print("=== 第三阶段重构验证结果 ===")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项验证通过")
    
    if passed == total:
        print("\n🎉 第三阶段重构验证完全成功！")
        print("\n=== 重构成果总结 ===")
        print("✅ 配置管理服务 - 统一处理配置文件和用户设置")
        print("✅ 窗口管理服务 - 统一管理子窗口的生命周期")
        print("✅ 寄存器操作服务 - 统一处理寄存器业务逻辑")
        print("✅ 服务架构完善 - 实现了完整的分层架构")
        print("\n主窗口代码进一步精简，职责更加清晰！")
        return True
    else:
        print(f"\n⚠️ 有 {total - passed} 项验证失败")
        print("需要检查和修复相关问题。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
