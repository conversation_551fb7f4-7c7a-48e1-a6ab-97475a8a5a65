# -*- coding: utf-8 -*-

"""
寄存器更新处理器
负责处理复杂的寄存器更新逻辑和验证
"""

import traceback
from PyQt5.QtWidgets import QMessageBox
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class RegisterUpdateProcessor:
    """寄存器更新处理器类
    
    职责：
    1. 处理寄存器值的更新和验证
    2. 管理只读位检查
    3. 处理模拟模式和硬件模式的写入
    4. 提供统一的寄存器更新接口
    """
    
    def __init__(self, main_window):
        """初始化寄存器更新处理器

        Args:
            main_window: 主窗口实例
        """
        self.main_window = main_window

    def _get_simulation_mode(self):
        """获取当前的模拟模式状态

        优先使用SPI服务的模拟模式状态，确保与实际硬件状态同步

        Returns:
            bool: 是否为模拟模式
        """
        if (hasattr(self.main_window, 'spi_service') and
            self.main_window.spi_service):
            return self.main_window.spi_service.simulation_mode

        # 如果SPI服务不可用，回退到主窗口的状态
        return getattr(self.main_window, 'simulation_mode', True)
        
    def update_register_value_and_display(self, addr, new_value):
        """更新寄存器值并显示
        
        Args:
            addr: 寄存器地址
            new_value: 新的寄存器值
            
        Returns:
            bool: 是否成功更新
        """
        try:
            # 标准化地址
            normalized_addr = self._normalize_register_address(addr)
            
            # 检查是否修改只读位
            if self._check_readonly_bits_modification(normalized_addr, new_value):
                if not self._confirm_readonly_modification():
                    # 如果用户取消，恢复原始值
                    self._restore_original_value(normalized_addr)
                    return False
            
            # 执行寄存器更新
            success = self._execute_register_update(normalized_addr, new_value)
            
            if success:
                # 更新显示
                self._update_display_after_write(normalized_addr, new_value)
                # 保存当前值，用于可能的恢复
                self.main_window.current_register_value = new_value
                
            return success
            
        except Exception as e:
            self._handle_update_error(e)
            return False
            
    def handle_simulation_write(self, addr, new_value):
        """模拟模式下处理写入操作

        Args:
            addr: 寄存器地址
            new_value: 新值
        """
        try:
            normalized_addr = self._normalize_register_address(addr)

            # 获取当前值
            old_value = self.main_window.register_manager.get_register_value(normalized_addr)

            # 检查是否在批量操作中
            is_batch_operation = (hasattr(self.main_window, 'is_batch_writing') and
                                self.main_window.is_batch_writing) or \
                               (hasattr(self.main_window, 'is_batch_reading') and
                                self.main_window.is_batch_reading)

            # 如果不在批量操作中且值未变化，不执行更新
            # 但在批量操作中，即使值相同也要执行写入（用户期望写入到硬件）
            if not is_batch_operation and old_value == new_value:
                return

            # 更新寄存器管理器中的值（批量操作时强制更新）
            self.main_window.register_manager.set_register_value(normalized_addr, new_value, force_update=is_batch_operation)

            if old_value == new_value:
                logger.info(f"模拟模式批量写入: {normalized_addr} = 0x{new_value:04X} (值未变化，但执行写入)")
            else:
                logger.info(f"模拟模式写入: {normalized_addr} = 0x{new_value:04X} (原值: 0x{old_value:04X})")

        except Exception as e:
            logger.error(f"模拟模式写入失败: {str(e)}\n{traceback.format_exc()}")
            
    def update_register_from_input(self):
        """从输入框更新寄存器值"""
        try:
            # 获取当前输入值
            new_value = self.main_window.io_handler.get_current_value()
            
            # 委托给寄存器操作服务
            if hasattr(self.main_window, 'register_service'):
                self.main_window.register_service.update_register_from_input(
                    self.main_window.selected_register_addr, new_value
                )
            else:
                logger.warning("寄存器操作服务不可用")
                
        except Exception as e:
            logger.error(f"从输入框更新寄存器值时出错: {str(e)}\n{traceback.format_exc()}")
            
    def update_register_from_table(self, addr, new_value):
        """从表格更新寄存器值"""
        try:
            # 委托给寄存器操作服务
            if hasattr(self.main_window, 'register_service'):
                self.main_window.register_service.write_register(addr, new_value)
            else:
                logger.warning("寄存器操作服务不可用")
                
        except Exception as e:
            logger.error(f"从表格更新寄存器值时出错: {str(e)}\n{traceback.format_exc()}")
            
    def _normalize_register_address(self, addr):
        """标准化寄存器地址格式
        
        Args:
            addr: 寄存器地址
            
        Returns:
            str: 标准化后的地址
        """
        if hasattr(self.main_window, 'register_manager'):
            return self.main_window.register_manager._normalize_register_address(addr)
        return str(addr)
        
    def _check_readonly_bits_modification(self, addr, new_value):
        """检查是否修改只读位
        
        Args:
            addr: 寄存器地址
            new_value: 新值
            
        Returns:
            bool: 是否修改了只读位
        """
        try:
            if hasattr(self.main_window, 'register_service'):
                return self.main_window.register_service.check_readonly_bits_modification(addr, new_value)
            return False
        except Exception as e:
            logger.error(f"检查只读位时出错: {str(e)}")
            return False
            
    def _confirm_readonly_modification(self):
        """确认是否继续修改只读位
        
        Returns:
            bool: 用户是否确认继续
        """
        response = QMessageBox.warning(
            self.main_window,
            "警告",
            "您正在尝试修改只读位的值。\n\n继续操作可能会导致不可预测的结果。\n\n是否继续？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        return response == QMessageBox.Yes
        
    def _restore_original_value(self, addr):
        """恢复原始值
        
        Args:
            addr: 寄存器地址
        """
        try:
            if hasattr(self.main_window, 'register_service'):
                self.main_window.register_service.restore_original_value(addr)
        except Exception as e:
            logger.error(f"恢复原始值时出错: {str(e)}")
            
    def _execute_register_update(self, addr, new_value):
        """执行寄存器更新
        
        Args:
            addr: 寄存器地址
            new_value: 新值
            
        Returns:
            bool: 是否成功
        """
        try:
            # 检查当前处理模式（使用SPI服务的状态确保同步）
            if self._get_simulation_mode():
                # 模拟模式直接更新本地寄存器
                self.handle_simulation_write(addr, new_value)
            else:
                # 实际模式通过SPI更新寄存器
                self._execute_spi_write(addr, new_value)
                
            return True
        except Exception as e:
            logger.error(f"执行寄存器更新时出错: {str(e)}")
            return False
            
    def _execute_spi_write(self, addr, new_value):
        """通过SPI执行寄存器写入
        
        Args:
            addr: 寄存器地址
            new_value: 新值
        """
        if hasattr(self.main_window, 'spi_coordinator'):
            self.main_window.spi_coordinator.execute_write_operation(addr, new_value)
        else:
            logger.warning("SPI操作协调器不可用")
            
    def _update_display_after_write(self, addr, new_value):
        """写入后更新显示
        
        Args:
            addr: 寄存器地址
            new_value: 新值
        """
        try:
            # 更新显示（使用十六进制显示）
            reg_num = int(addr, 16)
            if hasattr(self.main_window, 'display_manager'):
                self.main_window.display_manager.update_register_display(reg_num, new_value)
            else:
                logger.warning("显示管理器不可用")
                
        except Exception as e:
            logger.error(f"更新显示时出错: {str(e)}")
            
    def _handle_update_error(self, error):
        """处理更新错误
        
        Args:
            error: 异常对象
        """
        error_msg = f"更新寄存器值时出错: {str(error)}"
        logger.error(f"{error_msg}\n{traceback.format_exc()}")
        
        QMessageBox.critical(
            self.main_window, 
            "错误", 
            error_msg
        )
        
    def check_spi_availability(self):
        """检查SPI可用性
        
        Returns:
            bool: SPI是否可用
        """
        if getattr(self.main_window, 'simulation_mode', False):
            return True
        
        if not hasattr(self.main_window, 'spi_service') or not self.main_window.spi_service:
            QMessageBox.warning(self.main_window, "错误", "SPI操作不可用，请检查连接")
            return False
        
        # 使用get_connection_status()方法检查端口状态
        try:
            status = self.main_window.spi_service.get_connection_status()
            if not status.get('connected', False):
                QMessageBox.warning(self.main_window, "错误", "请先选择COM端口")
                return False
        except Exception as e:
            logger.error(f"检查SPI状态时出错: {str(e)}")
            return False
        
        return True
