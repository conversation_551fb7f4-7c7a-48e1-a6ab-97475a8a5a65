#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
配置管理系统
统一管理应用程序的所有配置项
"""

import json
import os
from typing import Any, Dict, Optional
from pathlib import Path
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class ConfigurationManager:
    """配置管理器，支持分层配置和环境变量覆盖"""
    
    _instance = None
    
    def __init__(self):
        """初始化配置管理器"""
        self._config: Dict[str, Any] = {}
        self._config_files: list = []
        self._watchers: Dict[str, list] = {}  # 配置变化监听器
        
    @classmethod
    def instance(cls):
        """获取单例实例"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def load_config_file(self, config_path: str, required: bool = True):
        """加载配置文件

        Args:
            config_path: 配置文件路径
            required: 是否必需文件存在
        """
        config_file = Path(config_path)

        if not config_file.exists():
            if required:
                raise FileNotFoundError(f"配置文件不存在: {config_path}")
            else:
                # 对于可选配置文件，使用debug级别而不是warning
                # 这样可以减少不必要的警告信息
                logger.debug(f"可选配置文件不存在，跳过: {config_file.name}")
                return
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                file_config = json.load(f)
                self._merge_config(file_config)
                self._config_files.append(str(config_file))
                logger.info(f"已加载配置文件: {config_path}")
        except Exception as e:
            logger.error(f"加载配置文件失败 {config_path}: {str(e)}")
            if required:
                raise
    
    def load_default_configs(self):
        """加载默认配置"""
        # 获取项目根目录
        project_root = Path(__file__).parent.parent.parent.parent

        # 加载配置文件（按优先级顺序）
        config_files = [
            (project_root / "config" / "default.json", True),   # 必需的默认配置
            (project_root / "config" / "app.json", False),     # 可选的应用配置
            (project_root / "config" / "local.json", False),   # 可选的本地覆盖配置
        ]

        loaded_files = []
        for config_file, required in config_files:
            if config_file.exists():
                self.load_config_file(str(config_file), required=required)
                loaded_files.append(config_file.name)
            elif required:
                self.load_config_file(str(config_file), required=required)
            else:
                logger.debug(f"跳过可选配置文件: {config_file.name}")

        logger.info(f"配置加载完成，已加载文件: {', '.join(loaded_files)}")
        
        # 应用环境变量覆盖
        self._apply_env_overrides()
    
    def _merge_config(self, new_config: Dict[str, Any]):
        """合并配置"""
        self._deep_merge(self._config, new_config)
    
    def _deep_merge(self, target: Dict, source: Dict):
        """深度合并字典"""
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._deep_merge(target[key], value)
            else:
                target[key] = value
    
    def _apply_env_overrides(self):
        """应用环境变量覆盖"""
        # 支持形如 FSJ_CONFIG_UI_THEME=dark 的环境变量
        prefix = "FSJ_CONFIG_"
        
        for key, value in os.environ.items():
            if key.startswith(prefix):
                config_path = key[len(prefix):].lower().split('_')
                self._set_nested_value(self._config, config_path, value)
                logger.info(f"环境变量覆盖: {'.'.join(config_path)} = {value}")
    
    def _set_nested_value(self, config: Dict, path: list, value: str):
        """设置嵌套配置值"""
        current = config
        for key in path[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        
        # 尝试转换类型
        final_value = self._convert_value(value)
        current[path[-1]] = final_value
    
    def _convert_value(self, value: str) -> Any:
        """转换配置值类型"""
        # 布尔值
        if value.lower() in ('true', 'false'):
            return value.lower() == 'true'
        
        # 数字
        try:
            if '.' in value:
                return float(value)
            else:
                return int(value)
        except ValueError:
            pass
        
        # JSON
        if value.startswith('{') or value.startswith('['):
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                pass
        
        # 字符串
        return value
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值
        
        Args:
            key: 配置键，支持点分隔的嵌套键如 'ui.theme'
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        current = self._config
        
        try:
            for k in keys:
                current = current[k]
            return current
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """设置配置值

        Args:
            key: 配置键
            value: 配置值
        """
        keys = key.split('.')
        current = self._config

        # 创建嵌套结构
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]

        # 设置最终值
        current[keys[-1]] = value

        # 通知监听器
        self._notify_watchers(key, value)
    
    def watch(self, key: str, callback: callable):
        """监听配置变化
        
        Args:
            key: 配置键
            callback: 回调函数
        """
        if key not in self._watchers:
            self._watchers[key] = []
        self._watchers[key].append(callback)
    
    def _notify_watchers(self, key: str, value: Any):
        """通知配置变化监听器"""
        if key in self._watchers:
            for callback in self._watchers[key]:
                try:
                    callback(key, value)
                except Exception as e:
                    logger.error(f"配置监听器回调失败: {str(e)}")
    
    def get_all(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self._config.copy()
    
    def save_to_file(self, config_path: str):
        """保存配置到文件
        
        Args:
            config_path: 配置文件路径
        """
        try:
            config_file = Path(config_path)
            config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=2, ensure_ascii=False)
            
            logger.info(f"配置已保存到: {config_path}")
        except Exception as e:
            logger.error(f"保存配置文件失败: {str(e)}")
            raise


# 全局配置管理器实例
config_manager = ConfigurationManager.instance()


# 便捷函数
def get_config(key: str, default: Any = None) -> Any:
    """获取配置值的便捷函数"""
    return config_manager.get(key, default)


def set_config(key: str, value: Any):
    """设置配置值的便捷函数"""
    config_manager.set(key, value)


def watch_config(key: str, callback: callable):
    """监听配置变化的便捷函数"""
    config_manager.watch(key, callback)
