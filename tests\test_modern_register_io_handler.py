#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ModernRegisterIOHandler测试
验证现代化寄存器IO处理器的基本功能
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt

from ui.handlers.ModernRegisterIOHandler import ModernRegisterIOHandler
from core.services.register.RegisterManager import RegisterManager
from core.repositories.register_repository import RegisterRepository


class TestModernRegisterIOHandler(unittest.TestCase):
    """ModernRegisterIOHandler测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """设置测试"""
        # 创建模拟的RegisterManager
        self.mock_registers = {
            "0x00": {
                "bits": [
                    {
                        "bit": "15:0",
                        "name": "DA_DEVICE_VERSION",
                        "default": "0001001100000000",
                        "widget_name": "deviceVersion",
                        "widget_type": "label",
                        "options": None,
                        "description": "Device version"
                    }
                ]
            },
            "0x02": {
                "bits": [
                    {
                        "bit": "0",
                        "name": "POWERDOWN",
                        "default": "0",
                        "widget_name": "powerDown",
                        "widget_type": "checkbox",
                        "options": None,
                        "description": "Power down control"
                    }
                ]
            }
        }
        
        self.register_manager = RegisterManager(self.mock_registers)
        
        # 创建模拟的RegisterRepository
        self.mock_spi_service = Mock()
        self.mock_spi_service.read_register.return_value = 0x1234
        self.register_repo = RegisterRepository(self.mock_spi_service)
        
        # 创建ModernRegisterIOHandler实例
        self.handler = ModernRegisterIOHandler(
            parent=None,
            register_manager=self.register_manager,
            register_repo=self.register_repo
        )
    
    def tearDown(self):
        """清理测试"""
        if hasattr(self, 'handler'):
            self.handler.close()
    
    def test_initialization(self):
        """测试初始化"""
        self.assertIsNotNone(self.handler)
        self.assertEqual(self.handler.register_manager, self.register_manager)
        self.assertEqual(self.handler.register_repo, self.register_repo)
        self.assertIsNotNone(self.handler.addr_line_edit)
        self.assertIsNotNone(self.handler.value_line_edit)
        self.assertIsNotNone(self.handler.read_btn)
        self.assertIsNotNone(self.handler.write_btn)
    
    def test_address_display(self):
        """测试地址显示"""
        test_addr = 0x02
        self.handler.set_address_display(test_addr)
        
        self.assertEqual(self.handler.addr_line_edit.text(), "0x02")
        self.assertEqual(self.handler.value_label.text(), "R2 Value:")
    
    def test_value_display(self):
        """测试值显示"""
        test_value = 0x1234
        self.handler.set_value_display(test_value)
        
        self.assertEqual(self.handler.value_line_edit.text(), "0x1234")
    
    def test_hex_input_validation(self):
        """测试十六进制输入验证"""
        # 有效输入
        self.assertTrue(self.handler.validate_hex_input("1234"))
        self.assertTrue(self.handler.validate_hex_input("0x1234"))
        self.assertTrue(self.handler.validate_hex_input("ABCD"))
        self.assertTrue(self.handler.validate_hex_input(""))
        
        # 无效输入
        with patch('PyQt5.QtWidgets.QMessageBox.warning'):
            self.assertFalse(self.handler.validate_hex_input("GHIJ"))
            self.assertFalse(self.handler.validate_hex_input("12345"))
    
    def test_get_current_value(self):
        """测试获取当前值"""
        # 测试十六进制值
        self.handler.value_line_edit.setText("0x1234")
        self.assertEqual(self.handler.get_current_value(), 0x1234)
        
        # 测试无前缀的十六进制值
        self.handler.value_line_edit.setText("ABCD")
        self.assertEqual(self.handler.get_current_value(), 0xABCD)
        
        # 测试空值
        self.handler.value_line_edit.setText("")
        self.assertIsNone(self.handler.get_current_value())
    
    def test_button_signals(self):
        """测试按钮信号"""
        # 测试读取按钮
        with patch.object(self.handler, 'read_requested') as mock_signal:
            self.handler.addr_line_edit.setText("0x02")
            self.handler.read_button_clicked()
            mock_signal.emit.assert_called_once_with(0x02)
        
        # 测试写入按钮
        with patch.object(self.handler, 'write_requested') as mock_signal:
            self.handler.addr_line_edit.setText("0x02")
            self.handler.value_line_edit.setText("0x1234")
            self.handler.write_button_clicked()
            mock_signal.emit.assert_called_once_with(0x02, 0x1234)
        
        # 测试读取全部按钮
        with patch.object(self.handler, 'read_all_requested') as mock_signal:
            self.handler.read_all_button_clicked()
            mock_signal.emit.assert_called_once()
    
    def test_search_functionality(self):
        """测试搜索功能"""
        # 设置搜索文本
        self.handler.search_edit.setText("POWERDOWN")
        
        # 执行搜索
        with patch.object(self.handler, 'search_requested') as mock_signal:
            self.handler._perform_search()
            mock_signal.emit.assert_called_once_with("POWERDOWN")
    
    def test_batch_read_registers(self):
        """测试批量读取寄存器"""
        addresses = [0x00, 0x02]
        result = self.handler.batch_read_registers(addresses)
        
        # 验证调用了SPI服务
        self.assertEqual(len(result), 2)
        self.mock_spi_service.read_register.assert_called()
    
    def test_toggle_buttons(self):
        """测试按钮启用/禁用"""
        # 禁用按钮
        self.handler.toggle_buttons(False)
        self.assertFalse(self.handler.read_btn.isEnabled())
        self.assertFalse(self.handler.write_btn.isEnabled())
        
        # 启用按钮
        self.handler.toggle_buttons(True)
        self.assertTrue(self.handler.read_btn.isEnabled())
        self.assertTrue(self.handler.write_btn.isEnabled())
    
    def test_port_management(self):
        """测试端口管理"""
        ports = ["COM1", "COM2", "COM3"]
        self.handler.update_port_list(ports)
        
        # 验证端口列表更新
        self.assertEqual(self.handler.port_combo.count(), 3)
        
        # 测试端口选择
        self.handler.set_selected_port("COM2")
        self.assertEqual(self.handler.get_selected_port(), "COM2")
    
    def test_register_value_changed_callback(self):
        """测试寄存器值变化回调"""
        # 设置当前地址
        self.handler.addr_line_edit.setText("0x02")
        
        # 模拟寄存器值变化
        self.handler.on_register_value_changed("test_widget", 0x02, 0x5678)
        
        # 验证值显示更新
        self.assertEqual(self.handler.value_line_edit.text(), "0x5678")
    
    def test_global_register_update_callback(self):
        """测试全局寄存器更新回调"""
        # 设置当前地址
        self.handler.addr_line_edit.setText("0x02")
        
        # 模拟全局寄存器更新
        self.handler.on_global_register_update(0x02, 0x9ABC)
        
        # 验证显示更新
        self.assertEqual(self.handler.value_line_edit.text(), "0x9ABC")
        self.assertEqual(self.handler.addr_line_edit.text(), "0x02")


def run_tests():
    """运行测试"""
    unittest.main()


if __name__ == '__main__':
    run_tests()
