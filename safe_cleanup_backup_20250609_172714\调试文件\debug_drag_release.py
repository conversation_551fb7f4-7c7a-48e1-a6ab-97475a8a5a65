#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试拖拽释放问题的脚本

这个脚本用于测试和调试拖拽停靠功能中鼠标释放事件的处理问题。
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow
from PyQt5.QtCore import QTimer

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from core.services.DIContainer import DIContainer
from ui.windows.RegisterMainWindow import RegisterMainWindow
from core.services.config.ConfigurationManager import set_config

def test_drag_release_detection():
    """测试拖拽释放检测功能"""
    print("🔍 开始调试拖拽释放检测问题")
    print("=" * 60)
    
    # 创建应用程序
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    try:
        # 启用强制悬浮模式
        set_config('plugins.force_floating_mode', True)
        print("✅ 已启用强制悬浮模式")
        
        # 创建主窗口
        main_window = RegisterMainWindow()
        main_window.show()
        
        # 等待窗口完全加载
        QTimer.singleShot(1000, lambda: open_test_plugin(main_window))
        
        # 运行应用程序
        app.exec_()
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        app.quit()

def open_test_plugin(main_window):
    """打开测试插件并进行拖拽测试"""
    try:
        plugin_service = main_window.plugin_service
        plugin_manager = DIContainer.instance().get('PluginManager')
        
        # 打开时钟输入控制插件
        plugin_name = "时钟输入控制"
        print(f"\n🔧 打开插件: {plugin_name}")
        
        # 获取插件实例
        plugin = plugin_manager.get_plugin(plugin_name)
        if not plugin:
            print(f"❌ 无法获取插件实例: {plugin_name}")
            return
            
        print(f"✅ 获取到插件实例: {type(plugin).__name__}")
        
        # 调用插件服务的_show_plugin_window方法
        plugin_service._show_plugin_window(plugin)
        
        # 获取插件窗口
        window = plugin_service.get_plugin_window(plugin_name)
        if not window:
            print(f"❌ 无法获取插件窗口: {plugin_name}")
            return
            
        print(f"✅ 插件窗口已打开: {window}")
        
        # 检查窗口的事件过滤器和拖拽支持
        check_drag_support(window, plugin_name)
        
        # 设置定时器进行拖拽测试
        QTimer.singleShot(2000, lambda: perform_drag_test(plugin_service, window, plugin_name))
        
    except Exception as e:
        print(f"❌ 打开测试插件失败: {str(e)}")
        import traceback
        traceback.print_exc()

def check_drag_support(window, plugin_name):
    """检查窗口的拖拽支持状态"""
    print(f"\n🔍 检查窗口拖拽支持状态:")
    print(f"   插件名称: {plugin_name}")
    print(f"   窗口类型: {type(window).__name__}")
    print(f"   窗口标题: {window.windowTitle()}")
    print(f"   鼠标跟踪: {window.hasMouseTracking()}")
    
    # 检查拖拽相关属性
    drag_attrs = ['_is_dragging', '_drag_start_position', '_original_title']
    for attr in drag_attrs:
        value = getattr(window, attr, '未设置')
        print(f"   {attr}: {value}")
    
    # 检查事件过滤器
    filters = []
    for child in window.findChildren(object):
        if hasattr(child, 'eventFilter'):
            filters.append(type(child).__name__)
    
    print(f"   事件过滤器数量: {len(filters)}")
    if filters:
        print(f"   过滤器类型: {', '.join(set(filters))}")

def perform_drag_test(plugin_service, window, plugin_name):
    """执行拖拽测试"""
    print(f"\n🧪 开始拖拽测试")
    
    try:
        from PyQt5.QtCore import QPoint, Qt
        from PyQt5.QtGui import QMouseEvent
        
        # 获取窗口位置
        window_pos = window.pos()
        window_center = window.rect().center()
        global_start = window.mapToGlobal(window_center)
        
        print(f"   窗口位置: {window_pos}")
        print(f"   窗口中心: {window_center}")
        print(f"   全局起始位置: {global_start}")
        
        # 模拟鼠标按下事件
        press_event = QMouseEvent(
            QMouseEvent.MouseButtonPress,
            window_center,
            global_start,
            Qt.LeftButton,
            Qt.LeftButton,
            Qt.NoModifier
        )
        
        print(f"\n1. 发送鼠标按下事件")
        window.mousePressEvent(press_event)
        
        # 检查拖拽状态
        is_dragging_after_press = getattr(window, '_is_dragging', None)
        drag_start_pos = getattr(window, '_drag_start_position', None)
        print(f"   按下后拖拽状态: {is_dragging_after_press}")
        print(f"   拖拽起始位置: {drag_start_pos}")
        
        # 模拟鼠标移动事件（移动足够距离触发拖拽）
        move_distance = 20  # 移动20像素，远超过5像素的阈值
        global_move = QPoint(global_start.x() + move_distance, global_start.y() + move_distance)
        local_move = window.mapFromGlobal(global_move)
        
        move_event = QMouseEvent(
            QMouseEvent.MouseMove,
            local_move,
            global_move,
            Qt.NoButton,
            Qt.LeftButton,
            Qt.NoModifier
        )
        
        print(f"\n2. 发送鼠标移动事件 (移动距离: {move_distance}像素)")
        window.mouseMoveEvent(move_event)
        
        # 再次检查拖拽状态
        is_dragging_after_move = getattr(window, '_is_dragging', None)
        print(f"   移动后拖拽状态: {is_dragging_after_move}")
        
        # 模拟鼠标释放事件
        release_event = QMouseEvent(
            QMouseEvent.MouseButtonRelease,
            local_move,
            global_move,
            Qt.LeftButton,
            Qt.NoButton,
            Qt.NoModifier
        )
        
        print(f"\n3. 发送鼠标释放事件")
        window.mouseReleaseEvent(release_event)
        
        # 最终检查拖拽状态
        is_dragging_after_release = getattr(window, '_is_dragging', None)
        print(f"   释放后拖拽状态: {is_dragging_after_release}")
        
        # 检查是否成功停靠
        QTimer.singleShot(1000, lambda: check_dock_result(plugin_service, plugin_name))
        
    except Exception as e:
        print(f"❌ 拖拽测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def check_dock_result(plugin_service, plugin_name):
    """检查停靠结果"""
    print(f"\n📊 检查停靠结果:")
    
    try:
        # 检查插件是否已停靠
        window = plugin_service.get_plugin_window(plugin_name)
        if window:
            print(f"   插件窗口仍然存在: {type(window).__name__}")
            print(f"   窗口可见性: {window.isVisible()}")
            print(f"   窗口标题: {window.windowTitle()}")
        else:
            print(f"   插件窗口已不存在（可能已停靠）")
        
        # 检查主窗口标签页数量
        main_window = plugin_service.main_window
        if hasattr(main_window, 'tab_widget'):
            tab_count = main_window.tab_widget.count()
            print(f"   主窗口标签页数量: {tab_count}")
            
            for i in range(tab_count):
                tab_text = main_window.tab_widget.tabText(i)
                print(f"   标签页 {i}: {tab_text}")
        
        print(f"\n💡 测试完成！")
        print(f"   如果看到标签页数量增加，说明停靠成功")
        print(f"   如果插件窗口仍然存在且可见，说明停靠失败")
        
    except Exception as e:
        print(f"❌ 检查停靠结果失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_drag_release_detection()