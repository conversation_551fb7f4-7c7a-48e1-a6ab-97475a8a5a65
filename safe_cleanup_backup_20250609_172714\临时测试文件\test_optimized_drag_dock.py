#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试优化后的拖拽停靠功能
- 直接调用现有的停靠功能
- 优化停靠区域为底部30%
- 减少调试输出，提高性能
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
import time


def test_optimized_drag_dock():
    """测试优化后的拖拽停靠功能"""
    try:
        print("🧪 开始测试优化后的拖拽停靠功能...")
        
        # 1. 启用强制悬浮模式
        print("1. 启用强制悬浮模式...")
        from core.services.config.ConfigurationManager import set_config, get_config
        set_config('plugins.force_floating_mode', True)
        print(f"   ✅ 强制悬浮模式已启用: {get_config('plugins.force_floating_mode')}")
        
        # 2. 创建主窗口
        print("2. 创建主窗口...")
        from ui.windows.RegisterMainWindow import RegisterMainWindow
        main_window = RegisterMainWindow()
        main_window.show()
        print("   ✅ 主窗口创建成功")
        
        # 3. 获取插件服务
        print("3. 获取插件服务...")
        if hasattr(main_window, 'plugin_service'):
            plugin_service = main_window.plugin_service
            print("   ✅ 插件服务获取成功")
        else:
            print("   ❌ 插件服务未找到")
            return False
        
        # 4. 创建测试插件窗口
        print("4. 创建测试插件窗口...")
        from core.services.plugin.PluginManager import plugin_manager
        available_plugins = plugin_manager.get_tool_window_plugins()
        
        test_plugin = None
        for plugin in available_plugins:
            if hasattr(plugin, 'name') and 'performance_monitor' in plugin.name:
                test_plugin = plugin
                break
        
        if not test_plugin and available_plugins:
            test_plugin = available_plugins[0]
        
        if not test_plugin:
            print("   ❌ 没有可用的测试插件")
            return False
        
        print(f"   ✅ 使用测试插件: {test_plugin.name}")
        
        # 5. 创建并显示插件窗口
        print("5. 创建并显示插件窗口...")
        test_window = test_plugin.create_window(main_window)
        if not test_window:
            print("   ❌ 创建插件窗口失败")
            return False
        
        # 配置为悬浮窗口
        plugin_service._configure_plugin_window(test_window, test_plugin.name)
        plugin_service.plugin_windows[test_plugin.name] = test_window
        test_window.show()
        print("   ✅ 插件窗口已显示")
        
        # 6. 等待窗口完全显示
        print("6. 等待窗口完全显示...")
        QApplication.processEvents()
        time.sleep(1)
        
        # 7. 测试停靠区域检测
        print("7. 测试停靠区域检测...")
        
        def test_dock_area_detection():
            """测试停靠区域检测"""
            try:
                from PyQt5.QtCore import QPoint
                
                # 获取主窗口几何信息
                main_geometry = main_window.frameGeometry()
                if main_geometry.isEmpty():
                    main_geometry = main_window.geometry()
                    main_global_pos = main_window.mapToGlobal(main_geometry.topLeft())
                    main_geometry.moveTopLeft(main_global_pos)
                
                print(f"   主窗口几何信息: {main_geometry}")
                
                # 测试不同位置
                test_points = [
                    # 主窗口中心（不在停靠区域）
                    QPoint(main_geometry.center().x(), main_geometry.center().y()),
                    # 主窗口底部中心（在停靠区域）
                    QPoint(main_geometry.center().x(), main_geometry.bottom() - 50),
                    # 主窗口顶部（不在停靠区域）
                    QPoint(main_geometry.center().x(), main_geometry.top() + 50),
                    # 主窗口外部（不在停靠区域）
                    QPoint(main_geometry.right() + 100, main_geometry.center().y())
                ]
                
                test_names = ["中心", "底部", "顶部", "外部"]
                
                for i, point in enumerate(test_points):
                    is_in_dock = plugin_service._is_in_dock_area(test_window, point)
                    print(f"   测试点 {test_names[i]} {point}: {'✅ 在停靠区域' if is_in_dock else '❌ 不在停靠区域'}")
                
                return True
                
            except Exception as e:
                print(f"   ❌ 停靠区域检测测试失败: {str(e)}")
                return False
        
        # 延迟执行停靠区域检测测试
        QTimer.singleShot(500, test_dock_area_detection)
        
        # 8. 测试手动停靠功能
        print("8. 测试手动停靠功能...")
        
        def test_manual_dock():
            """测试手动停靠功能"""
            try:
                print("   开始手动停靠测试...")
                
                # 直接调用停靠方法
                plugin_service.dock_floating_window(test_plugin.name)
                
                # 检查是否成功停靠
                QApplication.processEvents()
                time.sleep(0.5)
                
                tab_count = main_window.tools_tab_widget.count()
                if tab_count > 0:
                    tab_text = main_window.tools_tab_widget.tabText(0)
                    print(f"   ✅ 手动停靠成功: 标签页 '{tab_text}'")
                    print(f"   标签页容器可见: {main_window.tools_tab_widget.isVisible()}")
                    
                    # 重新设置为悬浮模式以便继续测试
                    plugin_service.undock_window_from_tab(test_plugin.name)
                    QApplication.processEvents()
                    time.sleep(0.5)
                    print("   重新设置为悬浮模式")
                    return True
                else:
                    print("   ❌ 手动停靠失败")
                    return False
                
            except Exception as e:
                print(f"   ❌ 手动停靠测试失败: {str(e)}")
                return False
        
        # 延迟执行手动停靠测试
        QTimer.singleShot(2000, test_manual_dock)
        
        # 9. 显示测试说明
        print("\n📋 测试说明:")
        print("1. 主窗口和插件窗口已显示")
        print("2. 强制悬浮模式已启用")
        print("3. 优化内容:")
        print("   - 停靠区域从底部50%优化为30%，提高精确度")
        print("   - 添加10像素边距，避免边界误触发")
        print("   - 减少调试输出，只在状态变化时输出")
        print("   - 直接调用现有的dock_floating_window功能")
        print("4. 将执行以下测试:")
        print("   - 停靠区域检测测试")
        print("   - 手动停靠功能测试")
        print("5. 请观察控制台输出和窗口变化")
        print("6. 也可以手动测试拖拽功能:")
        print("   - 拖拽插件窗口到主窗口底部30%区域")
        print("   - 释放鼠标完成自动停靠")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 运行测试
    success = test_optimized_drag_dock()
    
    if success:
        print("\n🎉 测试启动成功！请观察测试结果。")
        print("按 Ctrl+C 退出测试。")
        
        try:
            sys.exit(app.exec_())
        except KeyboardInterrupt:
            print("\n👋 测试结束")
    else:
        print("\n❌ 测试启动失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
