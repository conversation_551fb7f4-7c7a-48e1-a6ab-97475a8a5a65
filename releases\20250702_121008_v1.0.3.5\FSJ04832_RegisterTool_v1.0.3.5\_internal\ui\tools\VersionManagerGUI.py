#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
版本管理图形界面工具
提供可视化的版本管理和打包操作界面
"""

import sys
import os
import subprocess

# 现在可以安全导入其他模块
# 导入PyQt5模块
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                           QHBoxLayout, QGridLayout, QLabel, QPushButton,
                           QRadioButton, QButtonGroup, QTextEdit, QProgressBar,
                           QGroupBox, QCheckBox, QLineEdit, QMessageBox,
                           QSplitter)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QIcon
# 现在可以安全导入核心服务
from core.services.version.VersionService import VersionService

# 添加项目根目录到Python路径（必须在其他导入之前）
def _get_project_root_safe():
    """安全获取项目根目录"""
    current_file = os.path.abspath(__file__)
    current_dir = os.path.dirname(current_file)

    # 特征文件列表（按优先级排序）
    characteristic_files = [
        'main.py',                    # 主程序入口
        'packaging/version.json',     # 版本配置文件
        'version.json',              # 备用版本文件
        '.git',                      # Git仓库目录
        'requirements.txt'           # Python依赖文件
    ]

    # 向上查找最多6层
    for _ in range(6):
        for char_file in characteristic_files:
            if os.path.exists(os.path.join(current_dir, char_file)):
                return current_dir

        parent_dir = os.path.dirname(current_dir)
        if parent_dir == current_dir:  # 到达根目录
            break
        current_dir = parent_dir

    # 回退方法
    return os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

project_root = _get_project_root_safe()
sys.path.insert(0, project_root)

# packaging目录路径
packaging_root = os.path.join(project_root, 'packaging')


# 导入版本管理器和版本服务
def import_version_manager():
    """安全导入VersionManager类"""
    try:
        # 方法1: 尝试从packaging.scripts导入
        import importlib.util
        build_exe_path = os.path.join(packaging_root, 'scripts', 'build_exe.py')
        if os.path.exists(build_exe_path):
            spec = importlib.util.spec_from_file_location("build_exe", build_exe_path)
            build_exe_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(build_exe_module)
            return build_exe_module.VersionManager
    except Exception as e:
        print(f"方法1导入失败: {e}")

    try:
        # 方法2: 尝试直接导入（如果在Python路径中）
        from packaging.scripts.build_exe import VersionManager
        return VersionManager
    except ImportError as e:
        print(f"方法2导入失败: {e}")

    # 方法3: 返回简化版本管理器
    print("警告: 无法导入VersionManager，使用简化版本")
    class SimpleVersionManager:
        def __init__(self, version_file=None):
            self.version_file = version_file
            self.version_data = {"version": {"major": 1, "minor": 0, "patch": 0, "build": 0}}
            self.load_version_from_file()

        def load_version_from_file(self):
            """尝试从文件加载版本信息"""
            if self.version_file and os.path.exists(self.version_file):
                try:
                    import json
                    with open(self.version_file, 'r', encoding='utf-8') as f:
                        self.version_data = json.load(f)
                except Exception as e:
                    print(f"加载版本文件失败: {e}")

        def get_version_string(self):
            v = self.version_data.get('version', {"major": 1, "minor": 0, "patch": 0, "build": 0})
            return f"{v['major']}.{v['minor']}.{v['patch']}.{v['build']}"

        def get_app_name(self):
            return self.version_data.get('app_info', {}).get('name', 'FSJ04832 寄存器配置工具')

        def get_app_title_with_version(self):
            return f"{self.get_app_name()} v{self.get_version_string()}"

        def get_exe_name(self):
            return f"FSJConfigTool{self.get_version_string()}"

    return SimpleVersionManager

# 获取VersionManager类
VersionManager = import_version_manager()


class BuildThread(QThread):
    """构建线程"""
    
    # 信号定义
    output_received = pyqtSignal(str)
    progress_updated = pyqtSignal(int)
    build_finished = pyqtSignal(bool, str)
    
    def __init__(self, version_type, no_increment, spec_file):
        super().__init__()
        self.version_type = version_type
        self.no_increment = no_increment
        self.spec_file = spec_file
    
    def run(self):
        """执行构建"""
        try:
            # 构建命令 - 使用完整路径
            build_script = os.path.join(project_root, 'packaging', 'scripts', 'build_exe.py')
            cmd = [sys.executable, build_script]
            
            if self.no_increment:
                cmd.append('--no-version-increment')
            else:
                cmd.extend(['--version-type', self.version_type])
            
            if self.spec_file:
                cmd.extend(['--spec-file', self.spec_file])
            
            self.output_received.emit(f"执行命令: {' '.join(cmd)}\n")
            self.progress_updated.emit(10)
            
            # 执行构建
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                encoding='utf-8',
                errors='ignore',
                cwd=project_root
            )
            
            self.progress_updated.emit(30)
            
            # 读取输出
            output_lines = []
            while True:
                line = process.stdout.readline()
                if not line and process.poll() is not None:
                    break
                if line:
                    output_lines.append(line.strip())
                    self.output_received.emit(line)
                    self.progress_updated.emit(min(90, 30 + len(output_lines)))
            
            # 等待进程完成
            return_code = process.wait()
            self.progress_updated.emit(100)
            
            # 发送完成信号
            if return_code == 0:
                self.build_finished.emit(True, "构建成功完成！")
            else:
                self.build_finished.emit(False, f"构建失败，返回码: {return_code}")
                
        except Exception as e:
            self.build_finished.emit(False, f"构建过程中出现异常: {str(e)}")


class VersionManagerGUI(QMainWindow):
    """版本管理图形界面"""
    
    def __init__(self):
        super().__init__()
        self.version_manager = None
        self.version_service = None
        self.build_thread = None
        self.init_ui()
        self.load_version_info()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("FSJ04832 版本管理和打包工具")
        self.setGeometry(100, 100, 1000, 750)

        # 设置应用程序字体
        app_font = QFont()
        app_font.setFamily("Microsoft YaHei")  # 使用微软雅黑字体
        app_font.setPointSize(10)  # 增大字体大小
        self.setFont(app_font)

        # 设置全局样式
        self.setStyleSheet("""
            QGroupBox {
                font-size: 13px;
                font-weight: bold;
                color: #2E86AB;
                border: 2px solid #E0E0E0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: white;
            }
        """)

        # 设置应用程序图标（如果存在）
        try:
            icon_path = os.path.join(project_root, 'images', 'logo.ico')
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
        except Exception:
            pass
        
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # 创建分割器
        splitter = QSplitter(Qt.Vertical)
        main_layout.addWidget(splitter)
        
        # 上半部分：版本信息和控制
        top_widget = QWidget()
        top_layout = QVBoxLayout()
        top_widget.setLayout(top_layout)
        
        # 版本信息区域
        self.create_version_info_section(top_layout)
        
        # 版本控制区域
        self.create_version_control_section(top_layout)
        
        # 构建选项区域
        self.create_build_options_section(top_layout)
        
        # 操作按钮区域
        self.create_action_buttons_section(top_layout)
        
        splitter.addWidget(top_widget)
        
        # 下半部分：输出和进度
        bottom_widget = QWidget()
        bottom_layout = QVBoxLayout()
        bottom_widget.setLayout(bottom_layout)
        
        # 输出区域
        self.create_output_section(bottom_layout)
        
        splitter.addWidget(bottom_widget)
        
        # 设置分割器比例
        splitter.setSizes([400, 300])
        
        # 状态栏
        self.statusBar().showMessage("就绪")
        
    def create_version_info_section(self, parent_layout):
        """创建版本信息区域"""
        group = QGroupBox("当前版本信息")
        layout = QGridLayout()
        group.setLayout(layout)
        
        # 版本信息标签
        self.version_labels = {}
        labels = [
            ("当前版本:", "current_version"),
            ("应用名称:", "app_name"),
            ("窗口标题:", "window_title"),
            ("可执行文件名:", "exe_name"),
            ("构建日期:", "build_date"),
            ("构建类型:", "build_type")
        ]
        
        for i, (text, key) in enumerate(labels):
            label = QLabel(text)
            label.setStyleSheet("font-size: 12px; font-weight: bold;")

            value_label = QLabel("加载中...")
            value_label.setStyleSheet("""
                color: #2E86AB;
                font-weight: bold;
                font-size: 12px;
                padding: 2px;
                background-color: #F0F8FF;
                border: 1px solid #E0E0E0;
                border-radius: 3px;
            """)

            layout.addWidget(label, i, 0)
            layout.addWidget(value_label, i, 1)
            self.version_labels[key] = value_label
        
        # 刷新按钮
        refresh_btn = QPushButton("刷新版本信息")
        refresh_btn.clicked.connect(self.load_version_info)
        layout.addWidget(refresh_btn, len(labels), 0, 1, 2)
        
        parent_layout.addWidget(group)
        
    def create_version_control_section(self, parent_layout):
        """创建版本控制区域"""
        group = QGroupBox("版本号管理")
        layout = QVBoxLayout()
        group.setLayout(layout)
        
        # 版本类型选择
        self.version_type_group = QButtonGroup()
        
        version_types = [
            ("构建号 (Build)", "build", "日常开发构建，小修复 (x.x.x.N)"),
            ("补丁版本 (Patch)", "patch", "Bug修复，安全更新 (x.x.N.0)"),
            ("次版本 (Minor)", "minor", "新功能添加 (x.N.0.0)"),
            ("主版本 (Major)", "major", "重大更新，架构变更 (N.0.0.0)")
        ]
        
        for i, (text, value, description) in enumerate(version_types):
            radio = QRadioButton(text)
            radio.setProperty("version_type", value)
            radio.setStyleSheet("""
                QRadioButton {
                    font-size: 12px;
                    font-weight: bold;
                    spacing: 8px;
                }
                QRadioButton::indicator {
                    width: 16px;
                    height: 16px;
                }
            """)

            if i == 0:  # 默认选择构建号
                radio.setChecked(True)
            self.version_type_group.addButton(radio)

            # 创建水平布局
            h_layout = QHBoxLayout()
            h_layout.addWidget(radio)

            # 添加描述标签
            desc_label = QLabel(description)
            desc_label.setStyleSheet("""
                color: #666666;
                font-size: 11px;
                margin-left: 10px;
                font-style: italic;
            """)
            h_layout.addWidget(desc_label)
            h_layout.addStretch()

            layout.addLayout(h_layout)
        
        # 不增加版本号选项
        self.no_increment_checkbox = QCheckBox("不增加版本号（仅重新构建）")
        self.no_increment_checkbox.setStyleSheet("""
            QCheckBox {
                font-size: 12px;
                font-weight: bold;
                spacing: 8px;
                color: #D2691E;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
        """)
        layout.addWidget(self.no_increment_checkbox)
        
        parent_layout.addWidget(group)
        
    def create_build_options_section(self, parent_layout):
        """创建构建选项区域"""
        group = QGroupBox("构建选项")
        layout = QGridLayout()
        group.setLayout(layout)
        
        # spec文件路径
        spec_label = QLabel("Spec文件:")
        spec_label.setStyleSheet("font-size: 12px; font-weight: bold;")
        layout.addWidget(spec_label, 0, 0)

        # 设置默认spec文件路径（优先使用项目根目录的build.spec）
        default_spec_path = os.path.join(project_root, 'build.spec')
        self.spec_file_edit = QLineEdit(default_spec_path)
        self.spec_file_edit.setStyleSheet("""
            QLineEdit {
                font-size: 11px;
                padding: 4px;
                border: 2px solid #E0E0E0;
                border-radius: 4px;
            }
            QLineEdit:focus {
                border-color: #4CAF50;
            }
        """)
        layout.addWidget(self.spec_file_edit, 0, 1)

        # 浏览按钮
        browse_btn = QPushButton("浏览...")
        browse_btn.setStyleSheet("""
            QPushButton {
                font-size: 11px;
                padding: 4px 12px;
                background-color: #E3F2FD;
                border: 1px solid #2196F3;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #BBDEFB;
            }
        """)
        browse_btn.clicked.connect(self.browse_spec_file)
        layout.addWidget(browse_btn, 0, 2)
        
        parent_layout.addWidget(group)
        
    def create_action_buttons_section(self, parent_layout):
        """创建操作按钮区域"""
        layout = QHBoxLayout()
        
        # 预览按钮
        self.preview_btn = QPushButton("预览版本变化")
        self.preview_btn.setStyleSheet("""
            QPushButton {
                background-color: #FFC107;
                color: #333333;
                font-weight: bold;
                font-size: 12px;
                padding: 10px 16px;
                border: none;
                border-radius: 6px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #FFB300;
            }
        """)
        self.preview_btn.clicked.connect(self.preview_version_change)
        layout.addWidget(self.preview_btn)

        # 构建按钮
        self.build_btn = QPushButton("开始构建")
        self.build_btn.clicked.connect(self.start_build)
        self.build_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-weight: bold;
                font-size: 12px;
                padding: 10px 16px;
                border: none;
                border-radius: 6px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        layout.addWidget(self.build_btn)

        # 停止按钮
        self.stop_btn = QPushButton("停止构建")
        self.stop_btn.clicked.connect(self.stop_build)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                font-weight: bold;
                font-size: 12px;
                padding: 10px 16px;
                border: none;
                border-radius: 6px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        layout.addWidget(self.stop_btn)
        
        layout.addStretch()
        parent_layout.addLayout(layout)
        
    def create_output_section(self, parent_layout):
        """创建输出区域"""
        group = QGroupBox("构建输出")
        layout = QVBoxLayout()
        group.setLayout(layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)

        # 应用绿色样式
        self._apply_progress_bar_style(self.progress_bar)

        layout.addWidget(self.progress_bar)
        
        # 输出文本区域
        self.output_text = QTextEdit()
        self.output_text.setReadOnly(True)

        # 设置等宽字体用于输出显示
        output_font = QFont()
        output_font.setFamily("Consolas")
        if not output_font.exactMatch():
            output_font.setFamily("Courier New")
        output_font.setPointSize(10)  # 增大字体
        self.output_text.setFont(output_font)

        self.output_text.setStyleSheet("""
            QTextEdit {
                background-color: #1E1E1E;
                color: #FFFFFF;
                border: 2px solid #404040;
                border-radius: 4px;
                padding: 8px;
                font-size: 10px;
                line-height: 1.4;
            }
        """)
        layout.addWidget(self.output_text)

        # 清除按钮
        clear_btn = QPushButton("清除输出")
        clear_btn.setStyleSheet("""
            QPushButton {
                font-size: 11px;
                padding: 6px 12px;
                background-color: #FF9800;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)
        clear_btn.clicked.connect(self.output_text.clear)
        layout.addWidget(clear_btn)
        
        parent_layout.addWidget(group)
        
    def load_version_info(self):
        """加载版本信息"""
        try:
            # 强制重新创建版本服务实例以避免缓存问题
            VersionService._instance = None
            self.version_service = VersionService.instance()

            # 同时重新加载版本管理器
            self.version_manager = VersionManager()

            # 更新显示
            version_info = self.version_service.get_all_version_info()

            self.version_labels["current_version"].setText(version_info["version_string"])
            self.version_labels["app_name"].setText(version_info["app_name"])
            self.version_labels["window_title"].setText(version_info["app_title"])
            self.version_labels["exe_name"].setText(self.version_service.get_exe_name())
            self.version_labels["build_date"].setText(version_info["build_date"] or "未知")
            self.version_labels["build_type"].setText(version_info["build_type"])

            # 更新窗口标题显示当前版本
            self.setWindowTitle(f"FSJ04832 版本管理和打包工具 - 当前版本: {version_info['version_string']}")

            self.statusBar().showMessage(f"版本信息已更新 - 当前版本: {version_info['version_string']}")

        except Exception as e:
            QMessageBox.warning(self, "错误", f"加载版本信息失败: {str(e)}")

    def _apply_progress_bar_style(self, progress_bar):
        """为进度条应用绿色样式"""
        try:
            from ui.styles.ProgressBarStyleManager import apply_green_progress_style
            apply_green_progress_style(progress_bar, "default")
        except ImportError:
            # 如果样式管理器不可用，使用内联样式
            progress_bar.setStyleSheet("""
                QProgressBar {
                    border: 2px solid #C0C0C0;
                    border-radius: 5px;
                    background-color: #F0F0F0;
                    text-align: center;
                    font-weight: bold;
                    color: #333333;
                }

                QProgressBar::chunk {
                    background-color: qlineargradient(
                        x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #7ED321,
                        stop: 0.5 #5CB85C,
                        stop: 1 #4CAF50
                    );
                    border-radius: 3px;
                    margin: 1px;
                }
            """)
        except Exception as e:
            print(f"应用进度条样式失败: {str(e)}")

    def get_selected_version_type(self):
        """获取选中的版本类型"""
        for button in self.version_type_group.buttons():
            if button.isChecked():
                return button.property("version_type")
        return "build"
        
    def preview_version_change(self):
        """预览版本变化"""
        try:
            if self.no_increment_checkbox.isChecked():
                QMessageBox.information(self, "预览", "选择了不增加版本号，版本将保持不变。")
                return

            # 确保使用最新的版本管理器
            if not self.version_manager:
                self.version_manager = VersionManager()

            # 获取当前版本（从已加载的版本管理器）
            current_version = self.version_manager.get_version_string()

            # 创建临时版本管理器用于预览（复制当前状态）
            temp_vm = VersionManager()

            # 模拟版本增加
            version_type = self.get_selected_version_type()
            if version_type == "build":
                temp_vm.increment_build()
            elif version_type == "patch":
                temp_vm.increment_patch()
            elif version_type == "minor":
                temp_vm.increment_minor()
            elif version_type == "major":
                temp_vm.increment_major()

            new_version = temp_vm.get_version_string()
            new_exe_name = temp_vm.get_exe_name()

            # 获取版本类型的中文描述
            version_type_desc = {
                "build": "构建号",
                "patch": "补丁版本",
                "minor": "次版本",
                "major": "主版本"
            }.get(version_type, version_type)

            message = f"""版本变化预览:

当前版本: {current_version}
新版本: {new_version}
版本类型: {version_type_desc} ({version_type})

可执行文件名: {new_exe_name}.exe
窗口标题: {temp_vm.get_app_name()} v{new_version}

确认要进行此版本更新吗？"""

            QMessageBox.information(self, "版本变化预览", message)

        except Exception as e:
            QMessageBox.warning(self, "错误", f"预览版本变化失败: {str(e)}")
            
    def browse_spec_file(self):
        """浏览spec文件"""
        from PyQt5.QtWidgets import QFileDialog
        
        file_path, _ = QFileDialog.getOpenFileName(
            self, 
            "选择Spec文件", 
            project_root, 
            "Spec文件 (*.spec);;所有文件 (*)"
        )
        
        if file_path:
            # 转换为相对路径
            try:
                rel_path = os.path.relpath(file_path, project_root)
                self.spec_file_edit.setText(rel_path)
            except Exception:
                self.spec_file_edit.setText(file_path)
                
    def start_build(self):
        """开始构建"""
        try:
            # 获取参数
            version_type = self.get_selected_version_type()
            no_increment = self.no_increment_checkbox.isChecked()
            spec_file = self.spec_file_edit.text().strip()
            
            # 验证spec文件
            if spec_file:
                spec_path = os.path.join(project_root, spec_file)
                if not os.path.exists(spec_path):
                    QMessageBox.warning(self, "错误", f"Spec文件不存在: {spec_file}")
                    return
            
            # 清除输出
            self.output_text.clear()
            
            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            
            # 禁用按钮
            self.build_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            
            # 创建并启动构建线程
            self.build_thread = BuildThread(version_type, no_increment, spec_file)
            self.build_thread.output_received.connect(self.append_output)
            self.build_thread.progress_updated.connect(self.progress_bar.setValue)
            self.build_thread.build_finished.connect(self.on_build_finished)
            self.build_thread.start()
            
            self.statusBar().showMessage("构建进行中...")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动构建失败: {str(e)}")
            self.reset_ui_state()
            
    def stop_build(self):
        """停止构建"""
        if self.build_thread and self.build_thread.isRunning():
            self.build_thread.terminate()
            self.build_thread.wait(3000)  # 等待3秒
            self.append_output("\n构建已被用户停止。\n")
            self.reset_ui_state()
            
    def append_output(self, text):
        """添加输出文本"""
        self.output_text.append(text.rstrip())
        # 自动滚动到底部
        scrollbar = self.output_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
        
    def on_build_finished(self, success, message):
        """构建完成处理"""
        self.append_output(f"\n{message}\n")

        if success:
            self.statusBar().showMessage("构建成功完成！")
            # 重新加载版本信息
            QTimer.singleShot(1000, self.load_version_info)

            # 显示版本历史信息
            self.show_version_history()

            # 询问是否打开打包目录
            self.ask_open_build_directory()
        else:
            self.statusBar().showMessage("构建失败！")
            # 显示失败消息
            QMessageBox.warning(self, "构建失败", f"构建失败！\n\n{message}")

        self.reset_ui_state()

    def show_version_history(self):
        """显示版本历史信息"""
        try:
            # 尝试导入版本历史功能
            try:
                # 使用安全的导入方式
                import importlib.util
                build_exe_path = os.path.join(packaging_root, 'scripts', 'build_exe.py')
                if os.path.exists(build_exe_path):
                    spec = importlib.util.spec_from_file_location("build_exe", build_exe_path)
                    build_exe_module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(build_exe_module)
                    versions = build_exe_module.list_all_versions()
                else:
                    versions = []
            except Exception as e:
                print(f"导入版本历史功能失败: {e}")
                versions = []

            if versions:
                self.append_output(f"\n[历史] 版本历史 (共{len(versions)}个版本):")
                for i, v in enumerate(versions[:5]):  # 显示最近5个版本
                    timestamp_str = v['timestamp'].strftime('%Y-%m-%d %H:%M:%S')
                    self.append_output(f"  {i+1}. {v['name']} (v{v['version']}) - {timestamp_str}")

                if len(versions) > 5:
                    self.append_output(f"  ... 还有{len(versions)-5}个更早的版本")

                self.append_output("\n[提示] 所有版本保存在 'releases' 目录中")
                self.append_output("[提示] 最新版本链接: releases/latest")
            else:
                self.append_output("\n[信息] 这是第一个构建版本")

        except Exception as e:
            self.append_output(f"\n[警告] 无法获取版本历史: {str(e)}")

    def ask_open_build_directory(self):
        """询问是否打开构建目录"""
        try:
            # 获取最新的构建目录
            releases_dir = os.path.join(project_root, 'releases')
            if not os.path.exists(releases_dir):
                return

            # 查找最新的构建目录
            latest_dir = None
            latest_time = 0

            for item in os.listdir(releases_dir):
                item_path = os.path.join(releases_dir, item)
                if os.path.isdir(item_path) and item != 'latest':
                    # 检查是否是时间戳格式的目录
                    if '_v' in item:
                        try:
                            # 获取目录的修改时间
                            mtime = os.path.getmtime(item_path)
                            if mtime > latest_time:
                                latest_time = mtime
                                latest_dir = item_path
                        except Exception:
                            continue

            if latest_dir:
                # 创建自定义消息框
                msg_box = QMessageBox(self)
                msg_box.setWindowTitle("构建完成")
                msg_box.setIcon(QMessageBox.Information)

                # 获取版本信息
                dir_name = os.path.basename(latest_dir)
                version_part = dir_name.split('_v')[-1] if '_v' in dir_name else "未知"

                msg_box.setText(
                    f"🎉 构建成功完成！\n\n"
                    f"✅ 版本信息已更新\n"
                    f"📦 新版本：v{version_part}\n"
                    f"📁 保存位置：{dir_name}\n\n"
                    f"是否打开构建目录查看结果？"
                )

                # 添加自定义按钮
                open_btn = msg_box.addButton("打开目录", QMessageBox.YesRole)
                msg_box.addButton("稍后查看", QMessageBox.NoRole)

                # 设置默认按钮
                msg_box.setDefaultButton(open_btn)

                # 显示对话框
                msg_box.exec_()

                # 在输出区域显示构建信息
                self.append_output(f"\n[完成] 构建目录: {latest_dir}")
                self.append_output(f"[完成] 版本: v{version_part}")

                # 检查用户选择
                if msg_box.clickedButton() == open_btn:
                    self.open_directory(latest_dir)
            else:
                # 如果找不到最新目录，显示普通完成消息
                QMessageBox.information(self, "构建完成",
                    "构建成功完成！\n\n"
                    "版本信息已更新。\n"
                    "新版本已保存在独立的时间戳文件夹中。\n"
                    "查看输出区域了解详细信息。")

        except Exception as e:
            # 如果出错，显示普通完成消息
            QMessageBox.information(self, "构建完成",
                "构建成功完成！\n\n"
                "版本信息已更新。\n"
                "新版本已保存在独立的时间戳文件夹中。\n"
                "查看输出区域了解详细信息。")
            print(f"打开目录询问失败: {e}")

    def open_directory(self, directory_path):
        """打开指定目录"""
        try:
            import platform
            import subprocess

            # 确保目录存在
            if not os.path.exists(directory_path):
                self.append_output(f"\n[错误] 目录不存在: {directory_path}")
                QMessageBox.warning(self, "错误", f"目录不存在：\n{directory_path}")
                return

            # 转换为绝对路径并规范化
            abs_path = os.path.abspath(directory_path)
            self.append_output(f"\n[信息] 尝试打开目录: {abs_path}")

            system = platform.system()
            if system == "Windows":
                # Windows 使用 os.startfile (最可靠的方法)
                try:
                    os.startfile(abs_path)
                    self.append_output("\n[信息] 已成功打开目录")
                except OSError as e:
                    # 如果 os.startfile 失败，显示友好的错误信息
                    self.append_output(f"\n[警告] 打开目录失败: {str(e)}")
                    # 提供手动打开的提示
                    reply = QMessageBox.question(
                        self,
                        "打开目录失败",
                        f"无法自动打开目录：\n{abs_path}\n\n"
                        f"错误：{str(e)}\n\n"
                        f"是否复制路径到剪贴板，以便手动打开？",
                        QMessageBox.Yes | QMessageBox.No,
                        QMessageBox.Yes
                    )
                    if reply == QMessageBox.Yes:
                        self.copy_path_to_clipboard(abs_path)

            elif system == "Darwin":  # macOS
                # macOS 使用 open
                subprocess.run(['open', abs_path], check=True)
                self.append_output("\n[信息] 已成功打开目录")
            else:  # Linux 和其他
                # Linux 使用 xdg-open
                subprocess.run(['xdg-open', abs_path], check=True)
                self.append_output("\n[信息] 已成功打开目录")

        except subprocess.CalledProcessError as e:
            error_msg = f"命令执行失败 (退出码: {e.returncode})"
            self.append_output(f"\n[警告] 无法打开目录: {error_msg}")
            self.show_manual_open_dialog(directory_path, error_msg)
        except Exception as e:
            self.append_output(f"\n[警告] 无法打开目录: {str(e)}")
            self.show_manual_open_dialog(directory_path, str(e))

    def copy_path_to_clipboard(self, path):
        """复制路径到剪贴板"""
        try:
            from PyQt5.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            clipboard.setText(path)
            self.append_output(f"\n[信息] 路径已复制到剪贴板: {path}")
            QMessageBox.information(self, "路径已复制", f"目录路径已复制到剪贴板：\n{path}\n\n请手动打开文件管理器并粘贴路径。")
        except Exception as e:
            self.append_output(f"\n[警告] 复制路径失败: {str(e)}")

    def show_manual_open_dialog(self, directory_path, error_msg):
        """显示手动打开目录的对话框"""
        reply = QMessageBox.question(
            self,
            "打开目录失败",
            f"无法自动打开目录：\n{directory_path}\n\n"
            f"错误：{error_msg}\n\n"
            f"是否复制路径到剪贴板，以便手动打开？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )
        if reply == QMessageBox.Yes:
            self.copy_path_to_clipboard(directory_path)

    def reset_ui_state(self):
        """重置UI状态"""
        self.build_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.build_thread = None


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')

    # 获取当前版本信息
    try:
        version_service = VersionService.instance()
        current_version = version_service.get_version_string()
        app_name = version_service.get_app_name()
    except Exception:
        current_version = "*******"
        app_name = "FSJ04832 版本管理工具"

    # 设置应用程序信息
    app.setApplicationName(app_name)
    app.setApplicationVersion(current_version)
    app.setOrganizationName("FSJ Technology")

    # 创建主窗口
    window = VersionManagerGUI()
    window.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
