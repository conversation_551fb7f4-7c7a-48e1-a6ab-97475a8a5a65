#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
依赖注入容器
提供统一的服务管理和依赖注入功能
"""

from typing import Dict, Type, Any, Callable
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class DIContainer:
    """依赖注入容器，管理服务的创建和生命周期"""
    
    _instance = None
    
    def __init__(self):
        """初始化容器"""
        self._services: Dict[str, Any] = {}
        self._factories: Dict[str, Callable] = {}
        self._singletons: Dict[str, Any] = {}
        self._configurations: Dict[str, Dict] = {}
        
    @classmethod
    def instance(cls):
        """获取单例实例"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def register_singleton(self, service_name: str, service_class: Type, instance=None, **kwargs):
        """注册单例服务

        Args:
            service_name: 服务名称
            service_class: 服务类
            instance: 已存在的实例（可选）
            **kwargs: 构造参数
        """
        if instance is not None:
            # 直接注册已存在的实例
            self._singletons[service_name] = instance
            logger.info(f"注册单例实例: {service_name}")
        else:
            # 注册类配置，延迟创建
            self._configurations[service_name] = {
                'class': service_class,
                'type': 'singleton',
                'kwargs': kwargs
            }
            logger.info(f"注册单例服务: {service_name}")
    
    def register_transient(self, service_name: str, service_class: Type, **kwargs):
        """注册瞬态服务（每次获取都创建新实例）
        
        Args:
            service_name: 服务名称
            service_class: 服务类
            **kwargs: 构造参数
        """
        self._configurations[service_name] = {
            'class': service_class,
            'type': 'transient',
            'kwargs': kwargs
        }
        logger.info(f"注册瞬态服务: {service_name}")
    
    def register_factory(self, service_name: str, factory_func: Callable):
        """注册工厂方法
        
        Args:
            service_name: 服务名称
            factory_func: 工厂函数
        """
        self._factories[service_name] = factory_func
        logger.info(f"注册工厂服务: {service_name}")
    
    def get(self, service_name: str) -> Any:
        """获取服务实例

        Args:
            service_name: 服务名称

        Returns:
            服务实例
        """
        # 检查是否有工厂方法
        if service_name in self._factories:
            return self._factories[service_name]()

        # 检查是否已有单例实例（通过instance参数注册的）
        if service_name in self._singletons:
            return self._singletons[service_name]

        # 检查配置
        if service_name not in self._configurations:
            raise ValueError(f"服务 '{service_name}' 未注册")

        config = self._configurations[service_name]

        # 单例模式
        if config['type'] == 'singleton':
            if service_name not in self._singletons:
                self._singletons[service_name] = self._create_instance(config)
            return self._singletons[service_name]

        # 瞬态模式
        elif config['type'] == 'transient':
            return self._create_instance(config)

        else:
            raise ValueError(f"未知的服务类型: {config['type']}")
    
    def _create_instance(self, config: Dict) -> Any:
        """创建服务实例
        
        Args:
            config: 服务配置
            
        Returns:
            服务实例
        """
        service_class = config['class']
        kwargs = config.get('kwargs', {})
        
        # 解析依赖注入
        resolved_kwargs = {}
        for key, value in kwargs.items():
            if isinstance(value, str) and value.startswith('@'):
                # 依赖注入标记，获取其他服务
                dependency_name = value[1:]  # 移除@前缀
                resolved_kwargs[key] = self.get(dependency_name)
            else:
                resolved_kwargs[key] = value
        
        return service_class(**resolved_kwargs)
    
    def clear(self):
        """清空容器"""
        self._services.clear()
        self._factories.clear()
        self._singletons.clear()
        self._configurations.clear()
        logger.info("依赖注入容器已清空")


# 全局容器实例
container = DIContainer.instance()


def inject(service_name: str):
    """依赖注入装饰器
    
    Args:
        service_name: 服务名称
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            service = container.get(service_name)
            return func(service, *args, **kwargs)
        return wrapper
    return decorator


# 服务注册配置
def configure_services():
    """配置所有服务"""
    try:
        from core.services.spi.spi_service import SPIService
        from core.services.register.RegisterManager import RegisterManager
        from core.event_bus.RegisterUpdateBus import RegisterUpdateBus

        # 注册核心服务
        container.register_singleton('spi_service', SPIService)
        container.register_singleton('register_manager', RegisterManager)
        container.register_singleton('event_bus', RegisterUpdateBus)

        logger.info("核心服务配置完成")
    except Exception as e:
        logger.error(f"配置核心服务失败: {str(e)}")
        # 继续执行，不中断初始化


def configure_ui_managers():
    """配置UI管理器服务"""
    try:
        # 导入UI管理器类
        from ui.managers.InitializationManager import InitializationManager
        from ui.managers.RegisterOperationManager import RegisterOperationManager
        from ui.managers.RegisterDisplayManager import RegisterDisplayManager
        from ui.coordinators.EventCoordinator import EventCoordinator
        from ui.factories.ModernToolWindowFactory import ModernToolWindowFactory
        from ui.processors.RegisterUpdateProcessor import RegisterUpdateProcessor
        from ui.managers.ApplicationLifecycleManager import ApplicationLifecycleManager
        from ui.managers.UIUtilityManager import UIUtilityManager

        # 注册UI管理器（使用依赖注入）
        container.register_singleton('initialization_manager', InitializationManager, main_window='@main_window')
        container.register_singleton('register_operation_manager', RegisterOperationManager, main_window='@main_window')
        container.register_singleton('display_manager', RegisterDisplayManager, main_window='@main_window')
        container.register_singleton('event_coordinator', EventCoordinator, main_window='@main_window')
        container.register_singleton('tool_window_factory', ModernToolWindowFactory, main_window='@main_window')
        container.register_singleton('register_update_processor', RegisterUpdateProcessor, main_window='@main_window')
        container.register_singleton('lifecycle_manager', ApplicationLifecycleManager, main_window='@main_window')
        container.register_singleton('ui_utility_manager', UIUtilityManager, main_window='@main_window')

        logger.info("UI管理器服务配置完成")
    except Exception as e:
        logger.error(f"配置UI管理器服务失败: {str(e)}")
        # 继续执行，不中断初始化


def configure_additional_managers():
    """配置额外的管理器服务"""
    try:
        # 导入额外管理器类
        from ui.managers.BatchOperationManager import BatchOperationManager
        # 移除 BatchOperationController，统一使用 BatchOperationManager
        from ui.coordinators.SPIOperationCoordinator import SPIOperationCoordinator
        from ui.handlers.UIEventHandler import UIEventHandler
        from ui.managers.ToolWindowManager import ToolWindowManager
        from ui.managers.StatusAndConfigManager import StatusAndConfigManager
        from ui.managers.ResourceAndUtilityManager import ResourceAndUtilityManager
        from ui.managers.TabWindowManager import TabWindowManager
        from ui.managers.GlobalEventManager import GlobalEventManager
        from ui.managers.SPISignalManager import SPISignalManager

        # 注册额外管理器（使用依赖注入）
        container.register_singleton('batch_manager', BatchOperationManager, main_window='@main_window')
        # 移除 batch_operation_controller 注册，统一使用 batch_manager
        container.register_singleton('spi_coordinator', SPIOperationCoordinator, main_window='@main_window')
        container.register_singleton('ui_event_handler', UIEventHandler, main_window='@main_window')
        container.register_singleton('tool_window_manager', ToolWindowManager, main_window='@main_window')
        container.register_singleton('status_config_manager', StatusAndConfigManager, main_window='@main_window')
        container.register_singleton('resource_utility_manager', ResourceAndUtilityManager, main_window='@main_window')
        container.register_singleton('tab_window_manager', TabWindowManager, main_window='@main_window')
        container.register_singleton('global_event_manager', GlobalEventManager, main_window='@main_window')
        container.register_singleton('spi_signal_manager', SPISignalManager, main_window='@main_window')

        logger.info("额外管理器服务配置完成")
    except Exception as e:
        logger.error(f"配置额外管理器服务失败: {str(e)}")
        # 继续执行，不中断初始化


# 使用示例：
# 在主窗口初始化时：
# configure_services()
# container.register_singleton('main_window', RegisterMainWindow)
# display_manager = container.get('display_manager')
