#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证传统PLL处理器移除后的功能
确保现代化版本正常工作，传统版本无法导入
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication
from PyQt5.QtTest import QTest


def test_legacy_handler_removal():
    """测试传统处理器是否已移除"""
    print("=" * 60)
    print("测试传统PLL处理器移除")
    print("=" * 60)
    
    print("1. 测试传统处理器文件是否已移除...")
    legacy_handler_path = os.path.join(project_root, 'ui', 'handlers', 'PLLHandler.py')
    
    if os.path.exists(legacy_handler_path):
        print("❌ 传统处理器文件仍然存在")
        return False
    else:
        print("✓ 传统处理器文件已成功移除")
    
    print("\n2. 测试导入传统处理器是否失败...")
    try:
        from ui.handlers.PLLHandler import PLLHandler
        print("❌ 仍然可以导入传统处理器")
        return False
    except ImportError:
        print("✓ 传统处理器导入失败（预期行为）")
    except Exception as e:
        print(f"✓ 传统处理器导入出错（预期行为）: {str(e)}")
    
    return True


def test_modern_handler_functionality():
    """测试现代化处理器功能"""
    print("\n3. 测试现代化处理器功能...")
    
    try:
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建寄存器管理器
        from core.services.register.RegisterManager import RegisterManager
        config_path = os.path.join(project_root, 'lib', 'register.json')
        
        if not os.path.exists(config_path):
            print("❌ 寄存器配置文件不存在")
            return False

        import json
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        register_manager = RegisterManager(registers_config)
        
        # 测试现代化处理器
        from ui.handlers.ModernPLLHandler import ModernPLLHandler
        modern_handler = ModernPLLHandler(None, register_manager)
        
        print("✓ 现代化PLL处理器创建成功")
        print(f"  - 窗口标题: {modern_handler.windowTitle()}")
        
        # 检查关键功能
        if hasattr(modern_handler, 'calculate_output_frequencies'):
            modern_handler.calculate_output_frequencies()
            print("✓ 频率计算功能正常")
        
        if hasattr(modern_handler, 'ui') and hasattr(modern_handler.ui, 'PLL1PD'):
            print("✓ UI控件正常")
        
        # 测试窗口显示
        modern_handler.resize(800, 600)
        modern_handler.show()
        QTest.qWait(1000)
        print("✓ 窗口显示正常")
        modern_handler.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 现代化处理器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_factory_configuration():
    """测试工厂配置"""
    print("\n4. 测试工厂配置...")
    
    try:
        from ui.factories.ModernToolWindowFactory import ModernToolWindowFactory
        
        # 创建模拟主窗口
        class MockMainWindow:
            def __init__(self):
                pass
        
        mock_main_window = MockMainWindow()
        factory = ModernToolWindowFactory(mock_main_window)
        
        # 检查PLL配置
        pll_config = factory.HANDLER_CONFIGS.get('pll_control')
        if pll_config:
            print("✓ PLL控制配置存在")
            print(f"  - 使用现代化版本: {pll_config.get('use_modern', False)}")
            print(f"  - 现代化处理器: {pll_config.get('modern_handler')}")
            
            # 检查是否还有传统处理器引用
            if 'legacy_handler' in pll_config:
                print("⚠️  配置中仍有传统处理器引用")
                return False
            else:
                print("✓ 配置中已移除传统处理器引用")
            
            if pll_config.get('use_modern', False):
                print("✓ 配置默认使用现代化处理器")
                return True
            else:
                print("❌ 配置未设置使用现代化处理器")
                return False
        else:
            print("❌ PLL控制配置不存在")
            return False
            
    except Exception as e:
        print(f"❌ 工厂配置测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_window_creation():
    """测试窗口创建"""
    print("\n5. 测试窗口创建...")
    
    try:
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建寄存器管理器
        from core.services.register.RegisterManager import RegisterManager
        config_path = os.path.join(project_root, 'lib', 'register.json')
        
        import json
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        register_manager = RegisterManager(registers_config)
        
        # 创建现代化工厂
        from ui.factories.ModernToolWindowFactory import ModernToolWindowFactory
        
        class MockMainWindow:
            def __init__(self):
                self.register_manager = register_manager
        
        mock_main_window = MockMainWindow()
        factory = ModernToolWindowFactory(mock_main_window)
        
        # 测试创建PLL窗口
        pll_window = factory.create_window_by_type('pll_control')
        if pll_window:
            print("✓ 工厂创建PLL窗口成功")
            print(f"  - 窗口类型: {type(pll_window).__name__}")
            
            if 'Modern' in type(pll_window).__name__:
                print("✓ 工厂使用现代化处理器")
            else:
                print("❌ 工厂未使用现代化处理器")
                return False
            
            # 测试窗口显示
            pll_window.show()
            QTest.qWait(1000)
            print("✓ 工厂创建的窗口显示正常")
            pll_window.close()
            
            return True
        else:
            print("❌ 工厂创建PLL窗口失败")
            return False
            
    except Exception as e:
        print(f"❌ 窗口创建测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_managers():
    """测试管理器类"""
    print("\n6. 测试管理器类...")
    
    try:
        # 测试TabWindowManager
        from ui.managers.TabWindowManager import TabWindowManager
        print("✓ TabWindowManager导入成功")
        
        # 测试ToolWindowManager
        from ui.managers.ToolWindowManager import ToolWindowManager
        print("✓ ToolWindowManager导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 管理器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("开始验证传统PLL处理器移除结果...")
    
    tests = [
        ("传统处理器移除", test_legacy_handler_removal),
        ("现代化处理器功能", test_modern_handler_functionality),
        ("工厂配置", test_factory_configuration),
        ("窗口创建", test_window_creation),
        ("管理器类", test_managers),
    ]
    
    success_count = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                success_count += 1
            else:
                print(f"❌ {test_name}测试失败")
        except Exception as e:
            print(f"❌ {test_name}测试出错: {str(e)}")
    
    print("\n" + "=" * 60)
    print("验证结果总结")
    print("=" * 60)
    
    if success_count == len(tests):
        print("🎉 传统PLL处理器移除验证完全成功！")
        print(f"✅ 通过 {success_count}/{len(tests)} 个测试")
        print("\n📋 验证结果:")
        print("   - ✅ 传统处理器文件已移除")
        print("   - ✅ 传统处理器无法导入")
        print("   - ✅ 现代化处理器功能正常")
        print("   - ✅ 工厂配置正确")
        print("   - ✅ 窗口创建正常")
        print("   - ✅ 管理器类正常")
        print("\n🎯 结论:")
        print("   传统PLL处理器已成功移除，")
        print("   现代化版本完全接管了所有功能，")
        print("   系统运行正常！")
        return True
    else:
        print("❌ 传统PLL处理器移除验证未完全通过")
        print(f"⚠️  通过 {success_count}/{len(tests)} 个测试")
        print("\n🔧 建议操作:")
        print("   1. 检查失败的测试")
        print("   2. 修复发现的问题")
        print("   3. 重新运行验证")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
