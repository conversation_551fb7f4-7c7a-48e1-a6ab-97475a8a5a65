#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修复后的拖拽停靠功能
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                           QHBoxLayout, QLabel, QPushButton, QTextEdit)
from PyQt5.QtCore import Qt, QPoint, QTimer
from PyQt5.QtGui import QCursor
from utils.Log import get_module_logger

logger = get_module_logger(__name__)

class FixedDragDockTestWindow(QMainWindow):
    """测试修复后的拖拽停靠功能的窗口"""
    
    def __init__(self):
        super().__init__()
        self.main_window = None
        self._drag_start_position = None
        self._is_dragging = False
        self._drag_confirmed = False
        self.init_ui()
        self.setup_drag_support()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("拖拽停靠功能测试 - 修复版本")
        self.setGeometry(100, 100, 600, 400)
        
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("拖拽停靠功能测试 - 修复版本")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)
        
        # 状态显示
        self.status_label = QLabel("状态: 准备就绪")
        self.status_label.setStyleSheet("padding: 10px; background-color: #f0f0f0; border: 1px solid #ccc;")
        layout.addWidget(self.status_label)
        
        # 说明文本
        instructions = """
修复内容：
1. 降低拖拽阈值到2像素，提高拖拽敏感度
2. 增大停靠区域到主窗口底部50%
3. 减少停靠区域边距到5像素
4. 添加拖拽确认标志，避免状态混乱
5. 改进主窗口几何信息获取方法
6. 增强调试日志输出

测试步骤：
1. 点击"查找主窗口"按钮
2. 拖拽此窗口到主窗口底部50%区域
3. 观察窗口标题和鼠标光标变化
4. 在停靠区域释放鼠标
5. 检查是否成功停靠到标签页

注意：确保主窗口可见且未被遮挡
        """
        
        instructions_text = QTextEdit()
        instructions_text.setPlainText(instructions)
        instructions_text.setMaximumHeight(200)
        instructions_text.setReadOnly(True)
        layout.addWidget(instructions_text)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        find_main_btn = QPushButton("查找主窗口")
        find_main_btn.clicked.connect(self.find_main_window)
        button_layout.addWidget(find_main_btn)
        
        test_dock_btn = QPushButton("测试停靠区域检测")
        test_dock_btn.clicked.connect(self.test_dock_area_detection)
        button_layout.addWidget(test_dock_btn)
        
        simulate_dock_btn = QPushButton("模拟拖拽停靠")
        simulate_dock_btn.clicked.connect(self.simulate_drag_dock)
        button_layout.addWidget(simulate_dock_btn)
        
        layout.addLayout(button_layout)
        
        # 调试信息显示
        self.debug_text = QTextEdit()
        self.debug_text.setMaximumHeight(150)
        self.debug_text.setPlaceholderText("调试信息将显示在这里...")
        layout.addWidget(self.debug_text)
        
    def setup_drag_support(self):
        """设置拖拽支持"""
        # 启用鼠标跟踪
        self.setMouseTracking(True)
        
        # 设置窗口标志
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint |
                          Qt.WindowMinimizeButtonHint | Qt.WindowMaximizeButtonHint)
        
        logger.info("🖱️ [测试] 拖拽支持已设置")
        
    def find_main_window(self):
        """查找主窗口"""
        try:
            # 查找主窗口
            for widget in QApplication.topLevelWidgets():
                if (isinstance(widget, QMainWindow) and 
                    widget != self and 
                    hasattr(widget, 'menuBar')):
                    self.main_window = widget
                    break
            
            if self.main_window:
                main_title = self.main_window.windowTitle()
                self.status_label.setText(f"找到主窗口: {main_title}")
                self.debug_text.append(f"✅ 找到主窗口: {main_title}")
                logger.info(f"找到主窗口: {main_title}")
            else:
                self.status_label.setText("未找到主窗口")
                self.debug_text.append("❌ 未找到主窗口")
                logger.warning("未找到主窗口")
                
        except Exception as e:
            error_msg = f"查找主窗口时出错: {str(e)}"
            self.status_label.setText(error_msg)
            self.debug_text.append(f"❌ {error_msg}")
            logger.error(error_msg)
            
    def test_dock_area_detection(self):
        """测试停靠区域检测"""
        if not self.main_window:
            self.status_label.setText("请先查找主窗口")
            return
            
        current_pos = QCursor.pos()
        is_in_dock = self._is_in_dock_area(current_pos)
        
        result_msg = f"鼠标位置: {current_pos}, 在停靠区域: {is_in_dock}"
        self.status_label.setText(result_msg)
        self.debug_text.append(f"🎯 {result_msg}")
        logger.info(result_msg)
        
    def simulate_drag_dock(self):
        """模拟拖拽停靠"""
        if not self.main_window:
            self.status_label.setText("请先查找主窗口")
            return
            
        try:
            # 获取主窗口几何信息
            main_geometry = self.main_window.frameGeometry()
            if main_geometry.isEmpty():
                main_geometry = self.main_window.geometry()
                main_global_pos = self.main_window.mapToGlobal(QPoint(0, 0))
                main_geometry.moveTopLeft(main_global_pos)
            
            # 计算停靠区域中心点
            dock_area_height = int(main_geometry.height() * 0.5)
            dock_area_top = main_geometry.top() + main_geometry.height() - dock_area_height
            dock_center_x = main_geometry.left() + main_geometry.width() // 2
            dock_center_y = dock_area_top + dock_area_height // 2
            
            target_pos = QPoint(dock_center_x, dock_center_y)
            
            # 移动窗口到停靠区域
            self.move(target_pos.x() - self.width() // 2, target_pos.y() - self.height() // 2)
            
            # 测试停靠区域检测
            is_in_dock = self._is_in_dock_area(target_pos)
            
            result_msg = f"模拟停靠到: {target_pos}, 检测结果: {is_in_dock}"
            self.status_label.setText(result_msg)
            self.debug_text.append(f"🧪 {result_msg}")
            logger.info(result_msg)
            
        except Exception as e:
            error_msg = f"模拟拖拽停靠失败: {str(e)}"
            self.status_label.setText(error_msg)
            self.debug_text.append(f"❌ {error_msg}")
            logger.error(error_msg)
            
    def _is_in_dock_area(self, global_pos):
        """判断是否在停靠区域内（使用修复后的逻辑）"""
        try:
            if not self.main_window:
                return False
                
            # 获取主窗口的全局几何信息 - 使用修复后的方法
            main_geometry = None
            
            # 方法1: 尝试使用frameGeometry()
            try:
                main_geometry = self.main_window.frameGeometry()
                if main_geometry.isEmpty() or main_geometry.width() <= 0 or main_geometry.height() <= 0:
                    main_geometry = None
            except Exception:
                main_geometry = None

            # 方法2: 使用geometry()并转换为全局坐标
            if main_geometry is None:
                try:
                    main_geometry = self.main_window.geometry()
                    main_global_pos = self.main_window.mapToGlobal(QPoint(0, 0))
                    main_geometry.moveTopLeft(main_global_pos)
                except Exception:
                    return False

            # 验证几何信息的有效性
            if (main_geometry.isEmpty() or 
                main_geometry.width() <= 0 or 
                main_geometry.height() <= 0):
                return False

            # 定义停靠区域：主窗口底部50%的区域
            dock_area_height = int(main_geometry.height() * 0.5)
            dock_area_top = main_geometry.top() + main_geometry.height() - dock_area_height
            dock_area_bottom = main_geometry.bottom()

            # 减少边距
            margin = 5
            dock_area_left = main_geometry.left() + margin
            dock_area_right = main_geometry.right() - margin
            dock_area_top += margin
            dock_area_bottom -= margin

            # 检查鼠标是否在停靠区域内
            is_in_area = (global_pos.x() >= dock_area_left and
                         global_pos.x() <= dock_area_right and
                         global_pos.y() >= dock_area_top and
                         global_pos.y() <= dock_area_bottom)

            # 输出调试信息
            debug_info = f"""
停靠区域检测:
- 鼠标位置: ({global_pos.x()}, {global_pos.y()})
- 主窗口几何: {main_geometry}
- 停靠区域: X({dock_area_left}-{dock_area_right}), Y({dock_area_top}-{dock_area_bottom})
- 在停靠区域: {is_in_area}
            """
            self.debug_text.append(debug_info)

            return is_in_area

        except Exception as e:
            logger.error(f"判断停靠区域时出错: {str(e)}")
            return False
            
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self._drag_start_position = event.globalPos()
            self._is_dragging = False
            self._drag_confirmed = False
            self.status_label.setText("状态: 鼠标按下")
            logger.info(f"🖱️ [测试] 鼠标按下 - 位置: {event.globalPos()}")
        super().mousePressEvent(event)
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if (event.buttons() == Qt.LeftButton and 
            self._drag_start_position is not None):
            
            # 计算拖拽距离
            distance = (event.globalPos() - self._drag_start_position).manhattanLength()
            
            # 使用修复后的拖拽阈值
            drag_threshold = 2
            if distance >= drag_threshold:
                if not self._is_dragging:
                    logger.info(f"🎯 [测试] 开始拖拽模式 - 距离: {distance}")
                    self.status_label.setText(f"状态: 拖拽中 (距离: {distance})")
                self._is_dragging = True
                self._drag_confirmed = True
                
                # 检查停靠区域
                self._check_dock_area(event.globalPos())
        
        super().mouseMoveEvent(event)
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton and self._drag_confirmed:
            logger.info(f"🎯 [测试] 鼠标释放 - 位置: {event.globalPos()}")
            
            # 检查是否在停靠区域释放
            in_dock_area = self._is_in_dock_area(event.globalPos())
            
            if in_dock_area:
                self.status_label.setText("状态: 在停靠区域释放 - 应该停靠")
                self.debug_text.append("✅ 在停靠区域释放，应该触发停靠")
                logger.info("✅ 在停靠区域释放，应该触发停靠")
            else:
                self.status_label.setText("状态: 不在停靠区域 - 保持悬浮")
                self.debug_text.append("❌ 不在停靠区域，保持悬浮状态")
                logger.info("❌ 不在停靠区域，保持悬浮状态")
            
            # 恢复窗口状态
            self._restore_window_state()
        
        # 重置拖拽状态
        self._drag_start_position = None
        self._is_dragging = False
        self._drag_confirmed = False
        
        super().mouseReleaseEvent(event)
        
    def _check_dock_area(self, global_pos):
        """检查停靠区域并提供视觉反馈"""
        try:
            if self._is_in_dock_area(global_pos):
                # 提供视觉反馈
                if not hasattr(self, '_original_title'):
                    self._original_title = self.windowTitle()
                self.setWindowTitle(f"{self._original_title} - 释放鼠标停靠到主界面")
                
                # 改变鼠标光标
                QApplication.setOverrideCursor(Qt.PointingHandCursor)
            else:
                # 恢复原始状态
                if hasattr(self, '_original_title'):
                    self.setWindowTitle(self._original_title)
                QApplication.restoreOverrideCursor()
        except Exception as e:
            logger.error(f"检查停靠区域时出错: {str(e)}")
            
    def _restore_window_state(self):
        """恢复窗口状态"""
        try:
            # 恢复窗口标题
            if hasattr(self, '_original_title'):
                self.setWindowTitle(self._original_title)
                delattr(self, '_original_title')
            
            # 恢复鼠标光标
            QApplication.restoreOverrideCursor()
            
        except Exception as e:
            logger.error(f"恢复窗口状态时出错: {str(e)}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    test_window = FixedDragDockTestWindow()
    test_window.show()
    
    logger.info("拖拽停靠功能测试窗口已启动")
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
