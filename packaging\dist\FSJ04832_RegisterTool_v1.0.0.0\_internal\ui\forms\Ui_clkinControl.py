# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file '.\clkinControl.ui'
#
# Created by: PyQt5 UI code generator 5.15.10
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_ClkinControl(object):
    def setupUi(self, ClkinControl):
        ClkinControl.setObjectName("ClkinControl")
        ClkinControl.resize(2031, 694)
        self.label = QtWidgets.QLabel(ClkinControl)
        self.label.setGeometry(QtCore.QRect(-40, -20, 2101, 731))
        self.label.setText("")
        self.label.setPixmap(QtGui.QPixmap(":/clkincontrol/clkControl.bmp"))
        self.label.setScaledContents(True)
        self.label.setObjectName("label")
        self.losEn = QtWidgets.QCheckBox(ClkinControl)
        self.losEn.setGeometry(QtCore.QRect(121, 97, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.losEn.sizePolicy().hasHeightForWidth())
        self.losEn.setSizePolicy(sizePolicy)
        self.losEn.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 24px;  /* 设置方框宽度 */\n"
"                height: 24px; /* 设置方框高度 */\n"
"            }")
        self.losEn.setText("")
        self.losEn.setObjectName("losEn")
        self.clkin0LOS = QtWidgets.QCheckBox(ClkinControl)
        self.clkin0LOS.setGeometry(QtCore.QRect(281, 94, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.clkin0LOS.sizePolicy().hasHeightForWidth())
        self.clkin0LOS.setSizePolicy(sizePolicy)
        self.clkin0LOS.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 24px;  /* 设置方框宽度 */\n"
"                height: 24px; /* 设置方框高度 */\n"
"            }")
        self.clkin0LOS.setText("")
        self.clkin0LOS.setObjectName("clkin0LOS")
        self.clkin0SEL = QtWidgets.QCheckBox(ClkinControl)
        self.clkin0SEL.setGeometry(QtCore.QRect(451, 92, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.clkin0SEL.sizePolicy().hasHeightForWidth())
        self.clkin0SEL.setSizePolicy(sizePolicy)
        self.clkin0SEL.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 24px;  /* 设置方框宽度 */\n"
"                height: 24px; /* 设置方框高度 */\n"
"            }")
        self.clkin0SEL.setText("")
        self.clkin0SEL.setObjectName("clkin0SEL")
        self.clkin1LOS = QtWidgets.QCheckBox(ClkinControl)
        self.clkin1LOS.setGeometry(QtCore.QRect(281, 122, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.clkin1LOS.sizePolicy().hasHeightForWidth())
        self.clkin1LOS.setSizePolicy(sizePolicy)
        self.clkin1LOS.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 24px;  /* 设置方框宽度 */\n"
"                height: 24px; /* 设置方框高度 */\n"
"            }")
        self.clkin1LOS.setText("")
        self.clkin1LOS.setObjectName("clkin1LOS")
        self.clkin2LOS = QtWidgets.QCheckBox(ClkinControl)
        self.clkin2LOS.setGeometry(QtCore.QRect(281, 150, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.clkin2LOS.sizePolicy().hasHeightForWidth())
        self.clkin2LOS.setSizePolicy(sizePolicy)
        self.clkin2LOS.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 24px;  /* 设置方框宽度 */\n"
"                height: 24px; /* 设置方框高度 */\n"
"            }")
        self.clkin2LOS.setText("")
        self.clkin2LOS.setObjectName("clkin2LOS")
        self.clkin1SEL = QtWidgets.QCheckBox(ClkinControl)
        self.clkin1SEL.setGeometry(QtCore.QRect(451, 123, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.clkin1SEL.sizePolicy().hasHeightForWidth())
        self.clkin1SEL.setSizePolicy(sizePolicy)
        self.clkin1SEL.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 24px;  /* 设置方框宽度 */\n"
"                height: 24px; /* 设置方框高度 */\n"
"            }")
        self.clkin1SEL.setText("")
        self.clkin1SEL.setObjectName("clkin1SEL")
        self.clkin2SEL = QtWidgets.QCheckBox(ClkinControl)
        self.clkin2SEL.setGeometry(QtCore.QRect(451, 150, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.clkin2SEL.sizePolicy().hasHeightForWidth())
        self.clkin2SEL.setSizePolicy(sizePolicy)
        self.clkin2SEL.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 24px;  /* 设置方框宽度 */\n"
"                height: 24px; /* 设置方框高度 */\n"
"            }")
        self.clkin2SEL.setText("")
        self.clkin2SEL.setObjectName("clkin2SEL")
        self.clkin0En = QtWidgets.QCheckBox(ClkinControl)
        self.clkin0En.setGeometry(QtCore.QRect(163, 252, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.clkin0En.sizePolicy().hasHeightForWidth())
        self.clkin0En.setSizePolicy(sizePolicy)
        self.clkin0En.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 22px;  /* 设置方框宽度 */\n"
"                height: 22px; /* 设置方框高度 */\n"
"            }")
        self.clkin0En.setText("")
        self.clkin0En.setObjectName("clkin0En")
        self.clkin1En = QtWidgets.QCheckBox(ClkinControl)
        self.clkin1En.setGeometry(QtCore.QRect(163, 369, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.clkin1En.sizePolicy().hasHeightForWidth())
        self.clkin1En.setSizePolicy(sizePolicy)
        self.clkin1En.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 22px;  /* 设置方框宽度 */\n"
"                height: 22px; /* 设置方框高度 */\n"
"            }")
        self.clkin1En.setText("")
        self.clkin1En.setObjectName("clkin1En")
        self.clkin2En = QtWidgets.QCheckBox(ClkinControl)
        self.clkin2En.setGeometry(QtCore.QRect(163, 466, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.clkin2En.sizePolicy().hasHeightForWidth())
        self.clkin2En.setSizePolicy(sizePolicy)
        self.clkin2En.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 22px;  /* 设置方框宽度 */\n"
"                height: 22px; /* 设置方框高度 */\n"
"            }")
        self.clkin2En.setText("")
        self.clkin2En.setObjectName("clkin2En")
        self.clkinSelAutoEn = QtWidgets.QCheckBox(ClkinControl)
        self.clkinSelAutoEn.setGeometry(QtCore.QRect(753, 136, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.clkinSelAutoEn.sizePolicy().hasHeightForWidth())
        self.clkinSelAutoEn.setSizePolicy(sizePolicy)
        self.clkinSelAutoEn.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 24px;  /* 设置方框宽度 */\n"
"                height: 24px; /* 设置方框高度 */\n"
"            }")
        self.clkinSelAutoEn.setText("")
        self.clkinSelAutoEn.setObjectName("clkinSelAutoEn")
        self.clkinSelAutoRevertEn = QtWidgets.QCheckBox(ClkinControl)
        self.clkinSelAutoRevertEn.setGeometry(QtCore.QRect(753, 160, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.clkinSelAutoRevertEn.sizePolicy().hasHeightForWidth())
        self.clkinSelAutoRevertEn.setSizePolicy(sizePolicy)
        self.clkinSelAutoRevertEn.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 24px;  /* 设置方框宽度 */\n"
"                height: 24px; /* 设置方框高度 */\n"
"            }")
        self.clkinSelAutoRevertEn.setText("")
        self.clkinSelAutoRevertEn.setObjectName("clkinSelAutoRevertEn")
        self.clkinSelPinEn = QtWidgets.QCheckBox(ClkinControl)
        self.clkinSelPinEn.setGeometry(QtCore.QRect(1023, 72, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.clkinSelPinEn.sizePolicy().hasHeightForWidth())
        self.clkinSelPinEn.setSizePolicy(sizePolicy)
        self.clkinSelPinEn.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 24px;  /* 设置方框宽度 */\n"
"                height: 24px; /* 设置方框高度 */\n"
"            }")
        self.clkinSelPinEn.setText("")
        self.clkinSelPinEn.setObjectName("clkinSelPinEn")
        self.clkinSelPinPol = QtWidgets.QCheckBox(ClkinControl)
        self.clkinSelPinPol.setGeometry(QtCore.QRect(1024, 95, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.clkinSelPinPol.sizePolicy().hasHeightForWidth())
        self.clkinSelPinPol.setSizePolicy(sizePolicy)
        self.clkinSelPinPol.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 24px;  /* 设置方框宽度 */\n"
"                height: 24px; /* 设置方框高度 */\n"
"            }")
        self.clkinSelPinPol.setText("")
        self.clkinSelPinPol.setObjectName("clkinSelPinPol")
        self.PLL2SyncEn = QtWidgets.QCheckBox(ClkinControl)
        self.PLL2SyncEn.setGeometry(QtCore.QRect(714, 593, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.PLL2SyncEn.sizePolicy().hasHeightForWidth())
        self.PLL2SyncEn.setSizePolicy(sizePolicy)
        self.PLL2SyncEn.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 22px;  /* 设置方框宽度 */\n"
"                height: 22px; /* 设置方框高度 */\n"
"            }")
        self.PLL2SyncEn.setText("")
        self.PLL2SyncEn.setObjectName("PLL2SyncEn")
        self.PLL1RSyncEn = QtWidgets.QCheckBox(ClkinControl)
        self.PLL1RSyncEn.setGeometry(QtCore.QRect(126, 593, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.PLL1RSyncEn.sizePolicy().hasHeightForWidth())
        self.PLL1RSyncEn.setSizePolicy(sizePolicy)
        self.PLL1RSyncEn.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 22px;  /* 设置方框宽度 */\n"
"                height: 22px; /* 设置方框高度 */\n"
"            }")
        self.PLL1RSyncEn.setText("")
        self.PLL1RSyncEn.setObjectName("PLL1RSyncEn")
        self.PLL1RRst = QtWidgets.QCheckBox(ClkinControl)
        self.PLL1RRst.setGeometry(QtCore.QRect(126, 646, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.PLL1RRst.sizePolicy().hasHeightForWidth())
        self.PLL1RRst.setSizePolicy(sizePolicy)
        self.PLL1RRst.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 22px;  /* 设置方框宽度 */\n"
"                height: 22px; /* 设置方框高度 */\n"
"            }")
        self.PLL1RRst.setText("")
        self.PLL1RRst.setObjectName("PLL1RRst")
        self.syncSource = QtWidgets.QComboBox(ClkinControl)
        self.syncSource.setGeometry(QtCore.QRect(120, 631, 111, 20))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(8)
        self.syncSource.setFont(font)
        self.syncSource.setObjectName("syncSource")
        self.lineEditClkin0 = QtWidgets.QLineEdit(ClkinControl)
        self.lineEditClkin0.setGeometry(QtCore.QRect(143, 210, 108, 25))
        self.lineEditClkin0.setObjectName("lineEditClkin0")
        self.lineEditClkin1 = QtWidgets.QLineEdit(ClkinControl)
        self.lineEditClkin1.setGeometry(QtCore.QRect(141, 318, 107, 25))
        self.lineEditClkin1.setObjectName("lineEditClkin1")
        self.CLKin0Demux = QtWidgets.QComboBox(ClkinControl)
        self.CLKin0Demux.setGeometry(QtCore.QRect(369, 204, 107, 25))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(8)
        self.CLKin0Demux.setFont(font)
        self.CLKin0Demux.setObjectName("CLKin0Demux")
        self.CLKin1Demux = QtWidgets.QComboBox(ClkinControl)
        self.CLKin1Demux.setGeometry(QtCore.QRect(371, 314, 108, 25))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(8)
        self.CLKin1Demux.setFont(font)
        self.CLKin1Demux.setObjectName("CLKin1Demux")
        self.OSCoutMux = QtWidgets.QComboBox(ClkinControl)
        self.OSCoutMux.setGeometry(QtCore.QRect(383, 462, 101, 25))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(8)
        self.OSCoutMux.setFont(font)
        self.OSCoutMux.setObjectName("OSCoutMux")
        self.CLKinSelManual = QtWidgets.QComboBox(ClkinControl)
        self.CLKinSelManual.setGeometry(QtCore.QRect(753, 117, 136, 20))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(8)
        self.CLKinSelManual.setFont(font)
        self.CLKinSelManual.setObjectName("CLKinSelManual")
        self.CLKinSel0Type = QtWidgets.QComboBox(ClkinControl)
        self.CLKinSel0Type.setGeometry(QtCore.QRect(1026, 140, 168, 20))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(8)
        self.CLKinSel0Type.setFont(font)
        self.CLKinSel0Type.setObjectName("CLKinSel0Type")
        self.CLKinSel0Mux = QtWidgets.QComboBox(ClkinControl)
        self.CLKinSel0Mux.setGeometry(QtCore.QRect(1026, 161, 168, 20))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(8)
        self.CLKinSel0Mux.setFont(font)
        self.CLKinSel0Mux.setObjectName("CLKinSel0Mux")
        self.CLKinSel1Mux = QtWidgets.QComboBox(ClkinControl)
        self.CLKinSel1Mux.setGeometry(QtCore.QRect(1028, 223, 167, 20))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(8)
        self.CLKinSel1Mux.setFont(font)
        self.CLKinSel1Mux.setObjectName("CLKinSel1Mux")
        self.CLKinSel1Type = QtWidgets.QComboBox(ClkinControl)
        self.CLKinSel1Type.setGeometry(QtCore.QRect(1028, 201, 167, 22))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(8)
        self.CLKinSel1Type.setFont(font)
        self.CLKinSel1Type.setObjectName("CLKinSel1Type")
        self.lineEditClkin2Oscout = QtWidgets.QLineEdit(ClkinControl)
        self.lineEditClkin2Oscout.setGeometry(QtCore.QRect(144, 440, 110, 25))
        self.lineEditClkin2Oscout.setObjectName("lineEditClkin2Oscout")
        self.OSCoutClockFormat = QtWidgets.QComboBox(ClkinControl)
        self.OSCoutClockFormat.setGeometry(QtCore.QRect(161, 512, 124, 22))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(8)
        self.OSCoutClockFormat.setFont(font)
        self.OSCoutClockFormat.setObjectName("OSCoutClockFormat")
        self.LOSTimeout = QtWidgets.QComboBox(ClkinControl)
        self.LOSTimeout.setGeometry(QtCore.QRect(120, 152, 134, 26))
        self.LOSTimeout.setObjectName("LOSTimeout")
        self.PLL1R0Div = QtWidgets.QSpinBox(ClkinControl)
        self.PLL1R0Div.setGeometry(QtCore.QRect(555, 324, 96, 28))
        self.PLL1R0Div.setObjectName("PLL1R0Div")
        self.PLL1R1Div = QtWidgets.QSpinBox(ClkinControl)
        self.PLL1R1Div.setGeometry(QtCore.QRect(555, 356, 96, 28))
        self.PLL1R1Div.setObjectName("PLL1R1Div")
        self.PLL1R2Div = QtWidgets.QSpinBox(ClkinControl)
        self.PLL1R2Div.setGeometry(QtCore.QRect(555, 390, 96, 28))
        self.PLL1R2Div.setObjectName("PLL1R2Div")
        self.lineEditClkinSelOut = QtWidgets.QLineEdit(ClkinControl)
        self.lineEditClkinSelOut.setGeometry(QtCore.QRect(850, 372, 97, 25))
        self.lineEditClkinSelOut.setObjectName("lineEditClkinSelOut")
        self.HoldOverEn = QtWidgets.QCheckBox(ClkinControl)
        self.HoldOverEn.setGeometry(QtCore.QRect(1324, 230, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.HoldOverEn.sizePolicy().hasHeightForWidth())
        self.HoldOverEn.setSizePolicy(sizePolicy)
        self.HoldOverEn.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 22px;  /* 设置方框宽度 */\n"
"                height: 22px; /* 设置方框高度 */\n"
"            }")
        self.HoldOverEn.setText("")
        self.HoldOverEn.setObjectName("HoldOverEn")
        self.LosExternalInput = QtWidgets.QCheckBox(ClkinControl)
        self.LosExternalInput.setGeometry(QtCore.QRect(753, 185, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.LosExternalInput.sizePolicy().hasHeightForWidth())
        self.LosExternalInput.setSizePolicy(sizePolicy)
        self.LosExternalInput.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 24px;  /* 设置方框宽度 */\n"
"                height: 24px; /* 设置方框高度 */\n"
"            }")
        self.LosExternalInput.setText("")
        self.LosExternalInput.setObjectName("LosExternalInput")
        self.RGHoFastEnterEn = QtWidgets.QCheckBox(ClkinControl)
        self.RGHoFastEnterEn.setGeometry(QtCore.QRect(1327, 447, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.RGHoFastEnterEn.sizePolicy().hasHeightForWidth())
        self.RGHoFastEnterEn.setSizePolicy(sizePolicy)
        self.RGHoFastEnterEn.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 24px;  /* 设置方框宽度 */\n"
"                height: 24px; /* 设置方框高度 */\n"
"            }")
        self.RGHoFastEnterEn.setText("")
        self.RGHoFastEnterEn.setObjectName("RGHoFastEnterEn")
        self.HoldOverPLL1Det = QtWidgets.QCheckBox(ClkinControl)
        self.HoldOverPLL1Det.setGeometry(QtCore.QRect(1327, 421, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.HoldOverPLL1Det.sizePolicy().hasHeightForWidth())
        self.HoldOverPLL1Det.setSizePolicy(sizePolicy)
        self.HoldOverPLL1Det.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 24px;  /* 设置方框宽度 */\n"
"                height: 24px; /* 设置方框高度 */\n"
"            }")
        self.HoldOverPLL1Det.setText("")
        self.HoldOverPLL1Det.setObjectName("HoldOverPLL1Det")
        self.RGVtunedetRelativeEn = QtWidgets.QCheckBox(ClkinControl)
        self.RGVtunedetRelativeEn.setGeometry(QtCore.QRect(1659, 540, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.RGVtunedetRelativeEn.sizePolicy().hasHeightForWidth())
        self.RGVtunedetRelativeEn.setSizePolicy(sizePolicy)
        self.RGVtunedetRelativeEn.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 22px;  /* 设置方框宽度 */\n"
"                height: 22px; /* 设置方框高度 */\n"
"            }")
        self.RGVtunedetRelativeEn.setText("")
        self.RGVtunedetRelativeEn.setObjectName("RGVtunedetRelativeEn")
        self.DACClkMult = QtWidgets.QComboBox(ClkinControl)
        self.DACClkMult.setGeometry(QtCore.QRect(1422, 340, 68, 28))
        self.DACClkMult.setObjectName("DACClkMult")
        self.DACClkCntr = QtWidgets.QComboBox(ClkinControl)
        self.DACClkCntr.setGeometry(QtCore.QRect(1598, 340, 69, 28))
        self.DACClkCntr.setObjectName("DACClkCntr")
        self.HoldoverExitMode = QtWidgets.QCheckBox(ClkinControl)
        self.HoldoverExitMode.setGeometry(QtCore.QRect(1659, 422, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.HoldoverExitMode.sizePolicy().hasHeightForWidth())
        self.HoldoverExitMode.setSizePolicy(sizePolicy)
        self.HoldoverExitMode.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 22px;  /* 设置方框宽度 */\n"
"                height: 22px; /* 设置方框高度 */\n"
"            }")
        self.HoldoverExitMode.setText("")
        self.HoldoverExitMode.setObjectName("HoldoverExitMode")
        self.RGZpsEn = QtWidgets.QCheckBox(ClkinControl)
        self.RGZpsEn.setGeometry(QtCore.QRect(1660, 448, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.RGZpsEn.sizePolicy().hasHeightForWidth())
        self.RGZpsEn.setSizePolicy(sizePolicy)
        self.RGZpsEn.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 22px;  /* 设置方框宽度 */\n"
"                height: 22px; /* 设置方框高度 */\n"
"            }")
        self.RGZpsEn.setText("")
        self.RGZpsEn.setObjectName("RGZpsEn")
        self.RGHOExitDacassist = QtWidgets.QCheckBox(ClkinControl)
        self.RGHOExitDacassist.setGeometry(QtCore.QRect(1660, 473, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.RGHOExitDacassist.sizePolicy().hasHeightForWidth())
        self.RGHOExitDacassist.setSizePolicy(sizePolicy)
        self.RGHOExitDacassist.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 22px;  /* 设置方框宽度 */\n"
"                height: 22px; /* 设置方框高度 */\n"
"            }")
        self.RGHOExitDacassist.setText("")
        self.RGHOExitDacassist.setObjectName("RGHOExitDacassist")
        self.ManDacEN = QtWidgets.QCheckBox(ClkinControl)
        self.ManDacEN.setGeometry(QtCore.QRect(1324, 308, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.ManDacEN.sizePolicy().hasHeightForWidth())
        self.ManDacEN.setSizePolicy(sizePolicy)
        self.ManDacEN.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 22px;  /* 设置方框宽度 */\n"
"                height: 22px; /* 设置方框高度 */\n"
"            }")
        self.ManDacEN.setText("")
        self.ManDacEN.setObjectName("ManDacEN")
        self.TrackEn = QtWidgets.QCheckBox(ClkinControl)
        self.TrackEn.setGeometry(QtCore.QRect(1325, 341, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.TrackEn.sizePolicy().hasHeightForWidth())
        self.TrackEn.setSizePolicy(sizePolicy)
        self.TrackEn.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 22px;  /* 设置方框宽度 */\n"
"                height: 22px; /* 设置方框高度 */\n"
"            }")
        self.TrackEn.setText("")
        self.TrackEn.setObjectName("TrackEn")
        self.MANDAC = QtWidgets.QSpinBox(ClkinControl)
        self.MANDAC.setGeometry(QtCore.QRect(1660, 310, 131, 26))
        self.MANDAC.setObjectName("MANDAC")
        self.RGHOExitDacassistStep = QtWidgets.QComboBox(ClkinControl)
        self.RGHOExitDacassistStep.setGeometry(QtCore.QRect(1655, 504, 71, 25))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(8)
        self.RGHOExitDacassistStep.setFont(font)
        self.RGHOExitDacassistStep.setObjectName("RGHOExitDacassistStep")
        self.DACLowTrip = QtWidgets.QSpinBox(ClkinControl)
        self.DACLowTrip.setGeometry(QtCore.QRect(1656, 567, 101, 22))
        self.DACLowTrip.setObjectName("DACLowTrip")
        self.DACHighTrip = QtWidgets.QSpinBox(ClkinControl)
        self.DACHighTrip.setGeometry(QtCore.QRect(1656, 590, 101, 23))
        self.DACHighTrip.setObjectName("DACHighTrip")
        self.DACUpdateRate = QtWidgets.QLineEdit(ClkinControl)
        self.DACUpdateRate.setGeometry(QtCore.QRect(1767, 340, 90, 28))
        self.DACUpdateRate.setObjectName("DACUpdateRate")
        self.RB_DAC_LOCKED = QtWidgets.QCheckBox(ClkinControl)
        self.RB_DAC_LOCKED.setGeometry(QtCore.QRect(1327, 540, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.RB_DAC_LOCKED.sizePolicy().hasHeightForWidth())
        self.RB_DAC_LOCKED.setSizePolicy(sizePolicy)
        self.RB_DAC_LOCKED.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 24px;  /* 设置方框宽度 */\n"
"                height: 24px; /* 设置方框高度 */\n"
"            }")
        self.RB_DAC_LOCKED.setText("")
        self.RB_DAC_LOCKED.setObjectName("RB_DAC_LOCKED")
        self.RB_DAC_VALUE = QtWidgets.QLineEdit(ClkinControl)
        self.RB_DAC_VALUE.setGeometry(QtCore.QRect(1320, 570, 101, 25))
        self.RB_DAC_VALUE.setObjectName("RB_DAC_VALUE")
        self.RB_DAC_HIGH = QtWidgets.QCheckBox(ClkinControl)
        self.RB_DAC_HIGH.setGeometry(QtCore.QRect(1659, 608, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.RB_DAC_HIGH.sizePolicy().hasHeightForWidth())
        self.RB_DAC_HIGH.setSizePolicy(sizePolicy)
        self.RB_DAC_HIGH.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 22px;  /* 设置方框宽度 */\n"
"                height: 22px; /* 设置方框高度 */\n"
"            }")
        self.RB_DAC_HIGH.setText("")
        self.RB_DAC_HIGH.setObjectName("RB_DAC_HIGH")
        self.RB_DAC_LOW = QtWidgets.QCheckBox(ClkinControl)
        self.RB_DAC_LOW.setGeometry(QtCore.QRect(1659, 630, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.RB_DAC_LOW.sizePolicy().hasHeightForWidth())
        self.RB_DAC_LOW.setSizePolicy(sizePolicy)
        self.RB_DAC_LOW.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 22px;  /* 设置方框宽度 */\n"
"                height: 22px; /* 设置方框高度 */\n"
"            }")
        self.RB_DAC_LOW.setText("")
        self.RB_DAC_LOW.setObjectName("RB_DAC_LOW")
        self.RB_DAC_RAIL = QtWidgets.QCheckBox(ClkinControl)
        self.RB_DAC_RAIL.setGeometry(QtCore.QRect(1659, 650, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.RB_DAC_RAIL.sizePolicy().hasHeightForWidth())
        self.RB_DAC_RAIL.setSizePolicy(sizePolicy)
        self.RB_DAC_RAIL.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 22px;  /* 设置方框宽度 */\n"
"                height: 22px; /* 设置方框高度 */\n"
"            }")
        self.RB_DAC_RAIL.setText("")
        self.RB_DAC_RAIL.setObjectName("RB_DAC_RAIL")
        self.ResetMux = QtWidgets.QComboBox(ClkinControl)
        self.ResetMux.setGeometry(QtCore.QRect(753, 252, 168, 20))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(8)
        self.ResetMux.setFont(font)
        self.ResetMux.setObjectName("ResetMux")
        self.ResetType = QtWidgets.QComboBox(ClkinControl)
        self.ResetType.setGeometry(QtCore.QRect(753, 232, 168, 20))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(8)
        self.ResetType.setFont(font)
        self.ResetType.setObjectName("ResetType")
        self.PLL1_LD_MUX = QtWidgets.QComboBox(ClkinControl)
        self.PLL1_LD_MUX.setGeometry(QtCore.QRect(1027, 310, 168, 20))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(8)
        self.PLL1_LD_MUX.setFont(font)
        self.PLL1_LD_MUX.setObjectName("PLL1_LD_MUX")
        self.PLL1_LD_TYPE = QtWidgets.QComboBox(ClkinControl)
        self.PLL1_LD_TYPE.setGeometry(QtCore.QRect(1027, 288, 168, 20))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(8)
        self.PLL1_LD_TYPE.setFont(font)
        self.PLL1_LD_TYPE.setObjectName("PLL1_LD_TYPE")
        self.PLL2_LD_MUX = QtWidgets.QComboBox(ClkinControl)
        self.PLL2_LD_MUX.setGeometry(QtCore.QRect(755, 310, 168, 20))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(8)
        self.PLL2_LD_MUX.setFont(font)
        self.PLL2_LD_MUX.setObjectName("PLL2_LD_MUX")
        self.PLL2_LD_TYPE = QtWidgets.QComboBox(ClkinControl)
        self.PLL2_LD_TYPE.setGeometry(QtCore.QRect(755, 288, 168, 20))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(8)
        self.PLL2_LD_TYPE.setFont(font)
        self.PLL2_LD_TYPE.setObjectName("PLL2_LD_TYPE")
        self.HOLDOVER_FORCE = QtWidgets.QCheckBox(ClkinControl)
        self.HOLDOVER_FORCE.setGeometry(QtCore.QRect(1324, 270, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.HOLDOVER_FORCE.sizePolicy().hasHeightForWidth())
        self.HOLDOVER_FORCE.setSizePolicy(sizePolicy)
        self.HOLDOVER_FORCE.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 22px;  /* 设置方框宽度 */\n"
"                height: 22px; /* 设置方框高度 */\n"
"            }")
        self.HOLDOVER_FORCE.setText("")
        self.HOLDOVER_FORCE.setObjectName("HOLDOVER_FORCE")

        self.retranslateUi(ClkinControl)
        QtCore.QMetaObject.connectSlotsByName(ClkinControl)

    def retranslateUi(self, ClkinControl):
        _translate = QtCore.QCoreApplication.translate
        ClkinControl.setWindowTitle(_translate("ClkinControl", "Form"))
from ..resources import clkinControl_rc
