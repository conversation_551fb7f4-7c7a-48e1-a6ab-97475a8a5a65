#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试菜单状态修复的脚本
验证标签页关闭后菜单状态是否正确重置
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_menu_state_fix():
    """测试菜单状态修复"""
    print("=== 菜单状态修复测试 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication, QAction
        from core.services.plugin.PluginIntegrationService import PluginIntegrationService
        from ui.managers.TabWindowManager import TabWindowManager
        from plugins.example_tool_plugin import ExampleToolPlugin
        
        # 创建应用程序
        app = QApplication([])
        
        print("✓ 模块导入成功")
        
        # 模拟主窗口
        class MockMainWindow:
            def __init__(self):
                from PyQt5.QtWidgets import QTabWidget
                self.tools_tab_widget = QTabWidget()
                self.plugin_integration_service = None
                
                # 模拟菜单动作
                self.clkin_control_action = QAction("时钟输入(&I)")
                self.clkin_control_action.setCheckable(True)
                
        main_window = MockMainWindow()
        
        # 创建插件集成服务
        plugin_service = PluginIntegrationService(main_window)
        main_window.plugin_integration_service = plugin_service
        
        # 创建标签页管理器
        tab_manager = TabWindowManager(main_window)
        
        print("✓ 服务初始化成功")
        
        # 测试插件
        plugin = ExampleToolPlugin()
        plugin.initialize(main_window)
        
        # 注册插件动作
        plugin_service.plugin_actions[plugin.name] = main_window.clkin_control_action
        
        print(f"✓ 插件初始化成功: {plugin.name}")
        
        # 测试场景1: 创建插件窗口并设置菜单状态
        print("\n--- 测试场景1: 创建插件窗口 ---")
        window = plugin.create_window()
        plugin_service.plugin_windows[plugin.name] = window
        
        # 设置菜单为选中状态（模拟用户点击菜单）
        main_window.clkin_control_action.setChecked(True)
        print(f"菜单状态设置为选中: {main_window.clkin_control_action.isChecked()}")
        
        # 测试场景2: 集成到标签页
        print("\n--- 测试场景2: 集成到标签页 ---")
        plugin_service._integrate_window_to_tab(window, plugin.name)
        
        tab_count = main_window.tools_tab_widget.count()
        if tab_count > 0:
            tab_text = main_window.tools_tab_widget.tabText(0)
            print(f"✓ 标签页创建成功: '{tab_text}'")
            print(f"菜单状态: {main_window.clkin_control_action.isChecked()}")
        else:
            print("✗ 标签页创建失败")
            return False
        
        # 测试场景3: 测试标签页文本映射
        print("\n--- 测试场景3: 测试标签页文本映射 ---")
        
        # 测试TabWindowManager的映射
        tab_text = main_window.tools_tab_widget.tabText(0)
        print(f"标签页文本: '{tab_text}'")
        
        # 测试映射表
        tab_to_plugin_map = {
            '时钟输入控制': 'clkin_control_plugin',
            'PLL控制': 'pll_control_plugin', 
            '同步系统参考': 'sync_sysref_plugin',
            '时钟输出': 'clk_output_plugin',
            '模式设置': 'set_modes_plugin'
        }
        
        mapped_plugin = tab_to_plugin_map.get(tab_text)
        if mapped_plugin:
            print(f"✓ 标签页文本映射成功: '{tab_text}' -> '{mapped_plugin}'")
        else:
            print(f"✗ 标签页文本映射失败: '{tab_text}'")
            print(f"可用映射: {list(tab_to_plugin_map.keys())}")
        
        # 测试场景4: 模拟标签页关闭
        print("\n--- 测试场景4: 模拟标签页关闭 ---")
        
        widget = main_window.tools_tab_widget.widget(0)
        tab_text = main_window.tools_tab_widget.tabText(0)
        
        print(f"关闭前菜单状态: {main_window.clkin_control_action.isChecked()}")
        
        # 调用通知方法
        tab_manager._notify_plugin_tab_closed(tab_text, widget)
        
        print(f"关闭后菜单状态: {main_window.clkin_control_action.isChecked()}")
        
        # 验证结果
        if not main_window.clkin_control_action.isChecked():
            print("✓ 菜单状态正确重置为未选中")
        else:
            print("✗ 菜单状态未正确重置")
            return False
        
        # 验证插件窗口引用是否被清理
        if plugin.name not in plugin_service.plugin_windows:
            print("✓ 插件窗口引用已清理")
        else:
            print("✗ 插件窗口引用未清理")
            return False
        
        # 测试场景5: 验证重新打开功能
        print("\n--- 测试场景5: 验证重新打开功能 ---")
        
        # 模拟用户再次点击菜单
        main_window.clkin_control_action.setChecked(True)
        print(f"重新设置菜单为选中: {main_window.clkin_control_action.isChecked()}")
        
        # 尝试显示插件窗口
        plugin_service._show_plugin_window(plugin)
        
        # 检查是否创建了新窗口
        if plugin.name in plugin_service.plugin_windows:
            print("✓ 插件窗口可以重新创建")
            new_window = plugin_service.plugin_windows[plugin.name]
            if new_window != window:
                print("✓ 创建了新的窗口实例")
            else:
                print("? 重用了原窗口实例")
        else:
            print("✗ 插件窗口重新创建失败")
            return False
        
        print("\n🎉 菜单状态修复测试完成")
        
        # 总结修复内容
        print("\n--- 修复内容总结 ---")
        print("1. ✅ 更新了TabWindowManager中的标签页文本映射")
        print("2. ✅ 支持完全匹配和前缀匹配两种方式")
        print("3. ✅ 标签页关闭时正确通知PluginIntegrationService")
        print("4. ✅ 菜单状态正确重置为未选中")
        print("5. ✅ 插件窗口引用正确清理")
        print("6. ✅ 插件窗口可以重新打开")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        if 'app' in locals():
            app.quit()

def test_tab_text_mapping():
    """测试标签页文本映射"""
    print("\n=== 标签页文本映射测试 ===")
    
    # 测试映射表
    tab_to_plugin_map = {
        '时钟输入控制': 'clkin_control_plugin',
        'PLL控制': 'pll_control_plugin', 
        '同步系统参考': 'sync_sysref_plugin',
        '时钟输出': 'clk_output_plugin',
        '模式设置': 'set_modes_plugin'
    }
    
    # 测试用例
    test_cases = [
        '时钟输入控制',
        'PLL控制',
        '同步系统参考',
        '时钟输出',
        '模式设置',
        '时钟输入',  # 前缀匹配测试
        'PLL1 & PLL2 控制',  # 前缀匹配测试
        '时钟输出配置',  # 前缀匹配测试
        '未知标签页'  # 失败测试
    ]
    
    print("测试标签页文本映射:")
    for tab_text in test_cases:
        # 完全匹配
        exact_match = tab_to_plugin_map.get(tab_text)
        
        # 前缀匹配
        prefix_match = None
        for prefix, plugin_name in tab_to_plugin_map.items():
            if tab_text.startswith(prefix):
                prefix_match = plugin_name
                break
        
        result = exact_match or prefix_match
        status = "✓" if result else "✗"
        match_type = "完全匹配" if exact_match else ("前缀匹配" if prefix_match else "无匹配")
        
        print(f"  {status} '{tab_text}' -> '{result}' ({match_type})")

def main():
    """主函数"""
    success1 = test_menu_state_fix()
    test_tab_text_mapping()
    
    return 0 if success1 else 1

if __name__ == "__main__":
    sys.exit(main())
