#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
FSJ04832寄存器配置工具 - 测试运行脚本
快速运行项目测试的便捷脚本
"""

import sys
import os
import argparse
from pathlib import Path

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='FSJ04832寄存器配置工具测试运行器')
    parser.add_argument('--test', '-t', choices=[
        'all', 'core', 'architecture', 'ui', 'integration', 'performance', 'packaging'
    ], default='all', help='选择要运行的测试类型')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    print("🧪 FSJ04832寄存器配置工具 - 测试运行器")
    print("=" * 50)
    
    # 检查测试套件目录
    test_suite_dir = Path("test_suite")
    if not test_suite_dir.exists():
        print("❌ 测试套件目录不存在")
        return 1
        
    # 根据选择运行测试
    if args.test == 'all':
        print("🚀 运行综合测试...")
        return run_comprehensive_tests()
    elif args.test == 'core':
        print("🔧 运行核心功能测试...")
        return run_single_test('comprehensive_core_test.py')
    elif args.test == 'architecture':
        print("🏗️  运行架构组件测试...")
        return run_single_test('architecture_component_test.py')
    elif args.test == 'ui':
        print("🖥️  运行UI界面测试...")
        return run_single_test('ui_interface_test.py')
    elif args.test == 'integration':
        print("🔗 运行集成通信测试...")
        return run_single_test('integration_communication_test.py')
    elif args.test == 'performance':
        print("⚡ 运行性能稳定性测试...")
        return run_single_test('performance_stability_test.py')
    elif args.test == 'packaging':
        print("📦 运行打包部署测试...")
        return run_single_test('packaging_deployment_test.py')
        
def run_comprehensive_tests():
    """运行综合测试"""
    try:
        import subprocess
        result = subprocess.run([
            sys.executable, 'test_suite/comprehensive_test_runner.py'
        ], capture_output=False)
        return result.returncode
    except Exception as e:
        print(f"❌ 运行综合测试失败: {str(e)}")
        return 1
        
def run_single_test(test_file):
    """运行单个测试文件"""
    try:
        import subprocess
        test_path = Path('test_suite') / test_file
        if not test_path.exists():
            print(f"❌ 测试文件不存在: {test_path}")
            return 1
            
        result = subprocess.run([
            sys.executable, str(test_path)
        ], capture_output=False)
        return result.returncode
    except Exception as e:
        print(f"❌ 运行测试失败: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
