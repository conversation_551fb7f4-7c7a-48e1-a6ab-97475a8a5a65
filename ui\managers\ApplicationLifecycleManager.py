# -*- coding: utf-8 -*-

"""
应用程序生命周期管理器
负责管理应用程序的启动、运行和关闭过程
"""

import traceback
from PyQt5.QtCore import QTimer
from utils.Log import get_module_logger
from utils.CursorUtils import force_restore_cursor

logger = get_module_logger(__name__)


class ApplicationLifecycleManager:
    """应用程序生命周期管理器类
    
    职责：
    1. 管理应用程序关闭流程
    2. 处理资源清理
    3. 管理批量操作的强制取消
    4. 处理异常情况下的强制关闭
    """
    
    def __init__(self, main_window):
        """初始化应用程序生命周期管理器
        
        Args:
            main_window: 主窗口实例
        """
        self.main_window = main_window
        self.close_timeout = 5000  # 5秒关闭超时
        
    def handle_close_event(self, event):
        """处理关闭窗口事件
        
        Args:
            event: 关闭事件对象
        """
        logger.info("应用程序正在关闭...")
        
        # 禁用窗口输入，避免多次点击
        self.main_window.setEnabled(False)
        
        try:
            # 执行关闭流程
            self._execute_close_sequence(event)
        except Exception as e:
            logger.error(f"关闭时清理资源失败: {e}")
        finally:
            event.accept()
            
    def force_cancel_batch_operations(self):
        """强制取消所有批量操作，用于程序退出或其他紧急情况"""
        try:
            # 委托给批量操作管理器
            if hasattr(self.main_window, 'batch_manager'):
                self.main_window.batch_manager.force_cancel_all_operations()

            # 停止所有定时器
            if hasattr(self.main_window, 'operation_timer'):
                self.main_window.operation_timer.stop()

            # 确保按钮被重新启用
            if hasattr(self.main_window, 'io_handler'):
                self.main_window.io_handler.toggle_buttons(True)

            logger.info("已强制取消所有批量操作并清理资源")

        except Exception as e:
            logger.error(f"强制取消批量操作时出错: {str(e)}")
            # 确保按钮被重新启用
            if hasattr(self.main_window, 'io_handler'):
                self.main_window.io_handler.toggle_buttons(True)
                
    def _execute_close_sequence(self, event):
        """执行关闭序列
        
        Args:
            event: 关闭事件对象
        """
        # 强制取消所有批量操作
        self.force_cancel_batch_operations()

        # 关闭所有管理的窗口
        self._close_managed_windows()

        # 恢复光标状态
        self._restore_cursor_state()

        # 创建计时器用于超时检测
        close_timer = self._create_close_timer(event)

        # 保存最近配置（软件记忆功能）
        self._save_last_configuration()

        # 保存用户设置
        self._save_user_settings()

        # 清理SPI资源
        self._cleanup_spi_resources()

        # 停止超时计时器，关闭正常
        close_timer.stop()
        logger.info("应用程序关闭完成")
        
    def _close_managed_windows(self):
        """关闭所有管理的窗口"""
        try:
            if hasattr(self.main_window, 'window_service'):
                self.main_window.window_service.close_all_windows()
        except Exception as e:
            logger.error(f"关闭管理窗口时出错: {e}")
            
    def _create_close_timer(self, event):
        """创建关闭超时计时器
        
        Args:
            event: 关闭事件对象
            
        Returns:
            QTimer: 超时计时器
        """
        close_timer = QTimer()
        close_timer.setSingleShot(True)
        close_timer.timeout.connect(lambda: self._force_close(event))
        close_timer.start(self.close_timeout)
        return close_timer

    def _save_last_configuration(self):
        """保存最近配置（软件记忆功能）"""
        try:
            if hasattr(self.main_window, '_save_last_configuration_on_close'):
                self.main_window._save_last_configuration_on_close()
        except Exception as e:
            logger.error(f"保存最近配置时出错: {e}")

    def _save_user_settings(self):
        """保存用户设置"""
        try:
            if hasattr(self.main_window, 'config_service') and hasattr(self.main_window, 'simulation_mode'):
                self.main_window.config_service.save_simulation_mode(self.main_window.simulation_mode)
        except Exception as e:
            logger.error(f"保存用户设置时出错: {e}")
            
    def _cleanup_spi_resources(self):
        """清理SPI资源"""
        try:
            if hasattr(self.main_window, 'spi_service') and self.main_window.spi_service:
                self.main_window.spi_service.cleanup()
        except Exception as e:
            logger.error(f"清理SPI资源时出错: {e}")
            
    def _restore_cursor_state(self):
        """恢复光标状态"""
        force_restore_cursor()
            
    def _force_close(self, event):
        """超时强制关闭处理
        
        Args:
            event: 关闭事件对象
        """
        logger.warning("关闭操作超时，强制关闭应用程序")
        # 确保在强制关闭时也恢复光标状态
        self._restore_cursor_state()
        event.accept()
        
    def setup_global_exception_handler(self):
        """设置全局异常处理器"""
        import sys
        sys.excepthook = self._global_exception_handler
        
    def _global_exception_handler(self, exc_type, exc_value, exc_traceback):
        """全局异常处理器
        
        Args:
            exc_type: 异常类型
            exc_value: 异常值
            exc_traceback: 异常追踪
        """
        from PyQt5.QtWidgets import QMessageBox
        
        error_msg = "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        logger.error(f"未捕获的异常: {error_msg}")
        
        try:
            QMessageBox.critical(
                self.main_window, 
                "程序错误", 
                f"发生未捕获的异常:\n{str(exc_value)}\n\n详细信息已记录到日志文件"
            )
        except Exception as e:
            # 如果连错误对话框都无法显示，至少记录到日志
            logger.error(f"显示错误对话框失败: {e}")
            
    def get_resource_path(self, relative_path):
        """获取资源文件路径
        
        Args:
            relative_path: 相对路径
            
        Returns:
            str: 资源文件的完整路径
        """
        try:
            if hasattr(self.main_window, 'resource_utility_manager'):
                return self.main_window.resource_utility_manager.resource_path(relative_path)
            else:
                # 后备方案
                import os
                return os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', relative_path)
        except Exception as e:
            logger.error(f"获取资源路径时出错: {e}")
            return relative_path
            
    def is_in_batch_operation(self):
        """检查是否正在进行批量操作
        
        Returns:
            bool: 如果正在进行批量读取、写入或更新操作则返回True
        """
        try:
            if hasattr(self.main_window, 'global_event_manager'):
                return self.main_window.global_event_manager.is_in_batch_operation()
            return False
        except Exception as e:
            logger.error(f"检查批量操作状态时出错: {e}")
            return False
            
    def refresh_current_register_after_batch(self):
        """批量操作完成后刷新当前选中的寄存器显示
        
        这确保用户在批量操作完成后看到的是最新的寄存器值
        """
        try:
            if hasattr(self.main_window, 'global_event_manager'):
                self.main_window.global_event_manager.refresh_current_register_after_batch()
        except Exception as e:
            logger.error(f"刷新当前寄存器时出错: {e}")
            
    def handle_bit_field_selected(self, addr):
        """处理位段选中事件，在批量操作期间不跳转
        
        Args:
            addr: 寄存器地址
        """
        try:
            if hasattr(self.main_window, 'global_event_manager'):
                self.main_window.global_event_manager.handle_bit_field_selected(addr)
        except Exception as e:
            logger.error(f"处理位段选中事件时出错: {e}")
            
    def get_register_addr_from_tree_item(self):
        """从树形控件中获取当前选中的寄存器地址
        
        Returns:
            str: 当前选中的寄存器地址
        """
        try:
            if hasattr(self.main_window, 'tree_handler'):
                return self.main_window.tree_handler.get_current_register_addr()
            return None
        except Exception as e:
            logger.error(f"获取寄存器地址时出错: {e}")
            return None
            
    def close_tool_tab(self, index):
        """关闭工具标签页
        
        Args:
            index: 标签页索引
        """
        try:
            if hasattr(self.main_window, 'tab_window_manager'):
                self.main_window.tab_window_manager.close_tool_tab(index)
        except Exception as e:
            logger.error(f"关闭工具标签页时出错: {e}")
            
    def update_registers_from_config(self, loaded_values):
        """根据加载的配置更新寄存器管理器和UI
        
        Args:
            loaded_values: 加载的配置值
            
        Returns:
            bool: 是否成功更新
        """
        try:
            if hasattr(self.main_window, 'global_event_manager'):
                return self.main_window.global_event_manager.update_registers_from_config(loaded_values)
            return False
        except Exception as e:
            logger.error(f"从配置更新寄存器时出错: {e}")
            return False
