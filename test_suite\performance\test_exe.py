#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试打包后的exe文件
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def test_exe_exists():
    """测试exe文件是否存在"""
    exe_path = Path("dist/FSJ04832_RegisterTool/FSJ04832_RegisterTool.exe")
    
    if exe_path.exists():
        print(f"✓ 找到exe文件: {exe_path}")
        print(f"  文件大小: {exe_path.stat().st_size / 1024 / 1024:.1f} MB")
        return True
    else:
        print(f"❌ 未找到exe文件: {exe_path}")
        return False

def test_dll_files():
    """测试DLL文件是否存在"""
    dist_dir = Path("dist/FSJ04832_RegisterTool")
    
    required_dlls = [
        "python38.dll",
        "vcruntime140.dll", 
        "msvcp140.dll",
    ]
    
    print("\n检查DLL文件:")
    all_found = True
    
    for dll_name in required_dlls:
        dll_path = dist_dir / dll_name
        if dll_path.exists():
            print(f"✓ {dll_name}")
        else:
            print(f"❌ {dll_name}")
            all_found = False
    
    # 检查_internal目录中的DLL
    internal_dir = dist_dir / "_internal"
    if internal_dir.exists():
        internal_dlls = list(internal_dir.glob("*.dll"))
        print(f"✓ _internal目录包含 {len(internal_dlls)} 个DLL文件")
    else:
        print("❌ _internal目录不存在")
        all_found = False
    
    return all_found

def test_config_files():
    """测试配置文件是否存在"""
    dist_dir = Path("dist/FSJ04832_RegisterTool/_internal")
    
    required_dirs = ["config", "images"]
    
    print("\n检查配置文件:")
    all_found = True
    
    for dir_name in required_dirs:
        dir_path = dist_dir / dir_name
        if dir_path.exists() and dir_path.is_dir():
            files = list(dir_path.glob("*"))
            print(f"✓ {dir_name}目录 (包含 {len(files)} 个文件)")
        else:
            print(f"❌ {dir_name}目录")
            all_found = False
    
    return all_found

def test_exe_startup():
    """测试exe文件是否能启动"""
    exe_path = Path("dist/FSJ04832_RegisterTool/FSJ04832_RegisterTool.exe")
    
    if not exe_path.exists():
        print("❌ exe文件不存在，无法测试启动")
        return False
    
    print(f"\n测试exe文件启动...")
    print("注意: 程序将在5秒后自动关闭")
    
    try:
        # 启动程序，5秒后终止
        process = subprocess.Popen(
            [str(exe_path)],
            cwd=exe_path.parent,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8',
            errors='ignore'
        )
        
        # 等待5秒
        try:
            stdout, stderr = process.communicate(timeout=5)
            print("✓ 程序正常退出")
            
            if stdout:
                print("标准输出:")
                print(stdout[:500])  # 只显示前500字符
            
            if stderr:
                print("错误输出:")
                print(stderr[:500])  # 只显示前500字符
            
            return process.returncode == 0
            
        except subprocess.TimeoutExpired:
            print("✓ 程序启动成功（5秒后强制终止）")
            process.terminate()
            try:
                process.wait(timeout=2)
            except subprocess.TimeoutExpired:
                process.kill()
            return True
            
    except Exception as e:
        print(f"❌ 启动失败: {str(e)}")
        return False

def test_batch_file():
    """测试批处理文件"""
    batch_path = Path("dist/FSJ04832_RegisterTool/启动程序.bat")
    
    if batch_path.exists():
        print(f"✓ 找到批处理文件: {batch_path}")
        
        # 读取批处理文件内容
        try:
            with open(batch_path, 'r', encoding='gbk') as f:
                content = f.read()
            print("✓ 批处理文件内容正常")
            return True
        except Exception as e:
            print(f"❌ 读取批处理文件失败: {e}")
            return False
    else:
        print(f"❌ 未找到批处理文件: {batch_path}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("FSJ04832寄存器配置工具 - 打包测试")
    print("=" * 60)
    
    tests = [
        ("exe文件存在", test_exe_exists),
        ("DLL文件完整", test_dll_files),
        ("配置文件完整", test_config_files),
        ("批处理文件", test_batch_file),
        ("程序启动测试", test_exe_startup),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "❌ 失败"
        print(f"{test_name:<20}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！")
        print("打包成功，程序可以正常运行。")
        print("\n使用方法:")
        print("1. 进入目录: dist/FSJ04832_RegisterTool/")
        print("2. 运行程序: FSJ04832_RegisterTool.exe")
        print("3. 或使用批处理: 启动程序.bat")
    else:
        print(f"\n⚠️  {total - passed} 个测试失败")
        print("请检查打包过程或依赖项")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
