#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试PLL界面滚动功能的脚本
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_pll_scroll():
    """测试PLL界面滚动功能"""
    print("=== PLL界面滚动功能测试 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from ui.handlers.ModernPLLHandler import ModernPLLHandler
        from ui.forms.Ui_PLL1_2 import Ui_PLL1_2
        
        # 创建应用程序
        app = QApplication([])
        
        print("✓ 模块导入成功")
        
        # 测试UI尺寸信息
        print("\n--- UI尺寸信息 ---")
        
        # 创建一个临时widget来获取UI尺寸
        from PyQt5.QtWidgets import QWidget
        temp_widget = QWidget()
        ui = Ui_PLL1_2()
        ui.setupUi(temp_widget)
        
        # 获取UI的实际尺寸
        ui_size = temp_widget.size()
        print(f"UI默认尺寸: {ui_size.width()} x {ui_size.height()}")
        
        # 计算所有子控件的边界
        max_x, max_y = 0, 0
        widget_count = 0
        
        for child in temp_widget.findChildren(QWidget):
            if child.isVisible() and child != temp_widget:
                geometry = child.geometry()
                child_max_x = geometry.x() + geometry.width()
                child_max_y = geometry.y() + geometry.height()
                
                max_x = max(max_x, child_max_x)
                max_y = max(max_y, child_max_y)
                widget_count += 1
        
        print(f"子控件数量: {widget_count}")
        print(f"内容实际边界: {max_x} x {max_y}")
        
        # 测试ModernPLLHandler
        print("\n--- ModernPLLHandler测试 ---")
        
        # 创建PLL处理器（不显示窗口）
        pll_handler = ModernPLLHandler()
        
        # 检查滚动区域状态
        has_scroll = hasattr(pll_handler, 'scroll_area') and pll_handler.scroll_area is not None
        print(f"滚动区域状态: {'已启用' if has_scroll else '未启用'}")
        
        if has_scroll:
            content_size = pll_handler.content_widget.minimumSize()
            print(f"内容最小尺寸: {content_size.width()} x {content_size.height()}")
            
            # 检查滚动条策略
            h_policy = pll_handler.scroll_area.horizontalScrollBarPolicy()
            v_policy = pll_handler.scroll_area.verticalScrollBarPolicy()
            print(f"水平滚动条策略: {h_policy}")
            print(f"垂直滚动条策略: {v_policy}")
        
        # 测试内容尺寸调整
        print("\n--- 内容尺寸调整测试 ---")
        
        original_size = pll_handler.content_widget.minimumSize()
        print(f"调整前尺寸: {original_size.width()} x {original_size.height()}")
        
        # 调用尺寸调整方法
        pll_handler.adjust_content_size_to_fit()
        
        adjusted_size = pll_handler.content_widget.minimumSize()
        print(f"调整后尺寸: {adjusted_size.width()} x {adjusted_size.height()}")
        
        # 检查是否有改善
        if adjusted_size.height() > original_size.height():
            print("✓ 尺寸已调整以适应内容")
        else:
            print("- 尺寸无需调整")
        
        # 验证内容是否完全可见
        print("\n--- 内容可见性验证 ---")
        
        if has_scroll:
            scroll_area_size = pll_handler.scroll_area.size()
            content_size = pll_handler.content_widget.minimumSize()
            
            print(f"滚动区域尺寸: {scroll_area_size.width()} x {scroll_area_size.height()}")
            print(f"内容尺寸: {content_size.width()} x {content_size.height()}")
            
            # 检查是否需要滚动
            needs_h_scroll = content_size.width() > scroll_area_size.width()
            needs_v_scroll = content_size.height() > scroll_area_size.height()
            
            print(f"需要水平滚动: {'是' if needs_h_scroll else '否'}")
            print(f"需要垂直滚动: {'是' if needs_v_scroll else '否'}")
            
            if needs_v_scroll:
                print("✓ 垂直滚动功能可以解决内容显示问题")
            else:
                print("! 可能仍存在内容显示问题")
        
        print("\n🎉 PLL界面滚动功能测试完成")
        
        # 建议的解决方案
        print("\n--- 建议的解决方案 ---")
        print("1. 确保PLL界面的最小尺寸设置正确")
        print("2. 启用垂直滚动条以查看完整内容")
        print("3. 如果内容仍然被截断，请检查UI文件中的控件位置")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        if 'app' in locals():
            app.quit()

def main():
    """主函数"""
    success = test_pll_scroll()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
