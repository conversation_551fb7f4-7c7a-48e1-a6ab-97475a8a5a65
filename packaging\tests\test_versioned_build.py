#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
版本化构建功能测试脚本
测试新的版本化构建系统是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
packaging_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
project_root = os.path.dirname(packaging_root)
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(packaging_root, 'scripts'))

def test_version_manager():
    """测试版本管理器"""
    print("=" * 60)
    print("测试版本管理器")
    print("=" * 60)
    
    try:
        from build_exe import VersionManager, create_version_output_dir
        
        # 创建版本管理器
        vm = VersionManager()
        print(f"✓ 版本管理器创建成功")
        print(f"  当前版本: {vm.get_version_string()}")
        print(f"  应用名称: {vm.get_app_name()}")
        print(f"  可执行文件名: {vm.get_exe_name()}")
        
        # 测试版本化输出目录创建
        output_dir = create_version_output_dir(vm)
        print(f"✓ 版本化输出目录创建成功")
        print(f"  输出目录: {output_dir}")
        
        # 检查目录是否存在
        if Path(output_dir).exists():
            print(f"✓ 输出目录确实存在")
        else:
            print(f"❌ 输出目录不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 版本管理器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_version_history():
    """测试版本历史功能"""
    print("\n" + "=" * 60)
    print("测试版本历史功能")
    print("=" * 60)
    
    try:
        from build_exe import list_all_versions
        
        versions = list_all_versions()
        print(f"✓ 版本历史获取成功")
        print(f"  找到版本数量: {len(versions)}")
        
        if versions:
            print(f"  最新版本: {versions[0]['version']}")
            print(f"  最新构建时间: {versions[0]['timestamp']}")
            print(f"  最新目录名: {versions[0]['name']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 版本历史测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_releases_directory():
    """测试releases目录结构"""
    print("\n" + "=" * 60)
    print("测试releases目录结构")
    print("=" * 60)
    
    releases_dir = Path('releases')
    
    if releases_dir.exists():
        print(f"✓ releases目录存在: {releases_dir}")
        
        # 列出所有子目录
        subdirs = [d for d in releases_dir.iterdir() if d.is_dir()]
        print(f"  子目录数量: {len(subdirs)}")
        
        for subdir in subdirs:
            print(f"    📁 {subdir.name}")
            
            # 检查版本信息文件
            version_info = subdir / 'version_info.json'
            if version_info.exists():
                print(f"      ✓ 包含版本信息文件")
            else:
                print(f"      ⚠️  缺少版本信息文件")
        
        # 检查latest链接
        latest_link = releases_dir / 'latest'
        if latest_link.exists():
            if latest_link.is_symlink():
                target = latest_link.readlink()
                print(f"  🔗 latest链接: {target}")
            else:
                print(f"  📂 latest目录存在")
        else:
            print(f"  ⚠️  没有latest链接")
        
        return True
    else:
        print(f"⚠️  releases目录不存在: {releases_dir}")
        print(f"  这是正常的，如果还没有进行过构建")
        return True

def test_build_functions():
    """测试构建函数（不实际构建）"""
    print("\n" + "=" * 60)
    print("测试构建函数")
    print("=" * 60)
    
    try:
        from build_exe import (
            create_version_output_dir, 
            create_version_info_file,
            create_latest_link,
            VersionManager
        )
        
        # 创建测试版本管理器
        vm = VersionManager()
        
        # 测试输出目录创建
        output_dir = create_version_output_dir(vm)
        print(f"✓ 输出目录创建函数正常")
        
        # 测试版本信息文件创建
        create_version_info_file(vm, output_dir)
        version_file = Path(output_dir) / 'version_info.json'
        if version_file.exists():
            print(f"✓ 版本信息文件创建成功")
        else:
            print(f"❌ 版本信息文件创建失败")
            return False
        
        # 测试最新版本链接创建
        create_latest_link(output_dir, vm)
        print(f"✓ 最新版本链接创建函数正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 构建函数测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 版本化构建功能测试")
    print("测试新的版本化构建系统...")
    
    tests = [
        ("版本管理器", test_version_manager),
        ("版本历史功能", test_version_history),
        ("releases目录结构", test_releases_directory),
        ("构建函数", test_build_functions),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 出现异常: {str(e)}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20s} : {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！版本化构建功能正常。")
        print("\n📋 功能说明:")
        print("- ✅ 每次构建创建独立的时间戳文件夹")
        print("- ✅ 保留所有历史版本")
        print("- ✅ 自动创建版本信息文件")
        print("- ✅ 维护最新版本链接")
        print("- ✅ 支持版本历史查看")
        
        print("\n🚀 使用方法:")
        print("1. 运行: python start_gui.py")
        print("2. 或运行: python version_manager.py")
        print("3. 查看版本历史: python list_versions.py")
    else:
        print(f"\n💥 {total - passed} 个测试失败！请检查错误信息。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
