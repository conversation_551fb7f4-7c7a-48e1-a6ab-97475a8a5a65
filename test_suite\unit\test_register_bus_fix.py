#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试RegisterUpdateBus生命周期问题修复
专门解决 "wrapped C/C++ object of type RegisterUpdateBus has been deleted" 错误
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication
from PyQt5.QtTest import QTest


def test_register_update_bus_lifecycle():
    """测试RegisterUpdateBus的生命周期管理"""
    print("=" * 80)
    print("测试RegisterUpdateBus生命周期管理")
    print("=" * 80)

    try:
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        print("✓ QApplication创建成功")

        # 测试1: 基本的RegisterUpdateBus创建和访问
        print("\n1. 测试RegisterUpdateBus基本功能...")
        
        from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
        
        # 第一次获取实例
        bus1 = RegisterUpdateBus.instance()
        print(f"✓ 第一次获取实例: {type(bus1).__name__}")
        
        # 第二次获取实例（应该是同一个）
        bus2 = RegisterUpdateBus.instance()
        print(f"✓ 第二次获取实例: {type(bus2).__name__}")
        
        if bus1 is bus2:
            print("✓ 单例模式工作正常")
        else:
            print("❌ 单例模式失败")
            return False

        # 测试信号发送
        print("\n2. 测试信号发送...")
        try:
            bus1.emit_register_updated("0x50", 0x1234)
            print("✓ 信号发送成功")
        except Exception as e:
            print(f"❌ 信号发送失败: {str(e)}")
            return False

        # 测试2: 创建主窗口后的RegisterUpdateBus状态
        print("\n3. 测试主窗口创建后的RegisterUpdateBus状态...")
        
        from ui.windows.RegisterMainWindow import RegisterMainWindow

        # 现在使用依赖注入，不需要repository参数
        main_window = RegisterMainWindow()
        print("✓ 主窗口创建成功")

        # 检查RegisterUpdateBus是否仍然有效
        try:
            bus3 = RegisterUpdateBus.instance()
            if bus3:
                print("✓ 主窗口创建后RegisterUpdateBus仍然有效")
                
                # 测试信号发送
                bus3.emit_register_updated("0x51", 0x5678)
                print("✓ 主窗口创建后信号发送成功")
            else:
                print("❌ 主窗口创建后RegisterUpdateBus为None")
                return False
        except Exception as e:
            print(f"❌ 主窗口创建后RegisterUpdateBus访问失败: {str(e)}")
            return False

        # 测试3: 创建工具窗口后的RegisterUpdateBus状态
        print("\n4. 测试工具窗口创建后的RegisterUpdateBus状态...")
        
        try:
            pll_window = main_window.tool_window_factory.create_window_by_type('pll_control')
            if pll_window:
                print("✓ PLL工具窗口创建成功")
                
                # 再次检查RegisterUpdateBus
                bus4 = RegisterUpdateBus.instance()
                if bus4:
                    print("✓ 工具窗口创建后RegisterUpdateBus仍然有效")
                    
                    # 测试信号发送
                    bus4.emit_register_updated("0x52", 0x9ABC)
                    print("✓ 工具窗口创建后信号发送成功")
                else:
                    print("❌ 工具窗口创建后RegisterUpdateBus为None")
                    return False
            else:
                print("❌ PLL工具窗口创建失败")
                return False
        except Exception as e:
            print(f"❌ 工具窗口创建或RegisterUpdateBus访问失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

        # 测试4: 模拟多次访问（类似测试脚本的情况）
        print("\n5. 测试多次访问RegisterUpdateBus...")
        
        for i in range(5):
            try:
                bus_test = RegisterUpdateBus.instance()
                if bus_test:
                    bus_test.emit_register_updated(f"0x{50+i:02X}", 0x1000 + i)
                    print(f"✓ 第{i+1}次访问成功")
                else:
                    print(f"❌ 第{i+1}次访问失败: 实例为None")
                    return False
            except Exception as e:
                print(f"❌ 第{i+1}次访问失败: {str(e)}")
                return False

        print("\n🎉 所有RegisterUpdateBus生命周期测试通过！")
        return True

    except Exception as e:
        print(f"❌ 测试过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_qt_object_cleanup():
    """测试Qt对象清理问题"""
    print("\n" + "=" * 80)
    print("测试Qt对象清理问题")
    print("=" * 80)

    try:
        # 强制垃圾回收
        import gc
        gc.collect()
        print("✓ 执行垃圾回收")

        # 检查RegisterUpdateBus状态
        from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
        
        try:
            bus = RegisterUpdateBus.instance()
            if bus:
                print("✓ 垃圾回收后RegisterUpdateBus仍然有效")
                
                # 测试基本功能
                bus.emit_register_updated("0x99", 0xFFFF)
                print("✓ 垃圾回收后信号发送成功")
                return True
            else:
                print("❌ 垃圾回收后RegisterUpdateBus为None")
                return False
        except Exception as e:
            print(f"❌ 垃圾回收后RegisterUpdateBus访问失败: {str(e)}")
            
            # 尝试重新创建
            print("尝试重新创建RegisterUpdateBus...")
            try:
                # 重置单例
                RegisterUpdateBus._instance = None
                new_bus = RegisterUpdateBus.instance()
                if new_bus:
                    print("✓ RegisterUpdateBus重新创建成功")
                    new_bus.emit_register_updated("0x99", 0xFFFF)
                    print("✓ 重新创建后信号发送成功")
                    return True
                else:
                    print("❌ RegisterUpdateBus重新创建失败")
                    return False
            except Exception as e2:
                print(f"❌ RegisterUpdateBus重新创建失败: {str(e2)}")
                return False

    except Exception as e:
        print(f"❌ Qt对象清理测试出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 开始RegisterUpdateBus生命周期测试")
    
    # 创建应用程序（只创建一次）
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    try:
        # 测试RegisterUpdateBus生命周期
        success1 = test_register_update_bus_lifecycle()
        
        # 测试Qt对象清理
        success2 = test_qt_object_cleanup()
        
        # 总结结果
        print("\n" + "=" * 80)
        print("📊 测试结果总结")
        print("=" * 80)
        print(f"RegisterUpdateBus生命周期测试: {'✅ 通过' if success1 else '❌ 失败'}")
        print(f"Qt对象清理测试: {'✅ 通过' if success2 else '❌ 失败'}")
        
        if success1 and success2:
            print("\n🎉 所有测试通过！RegisterUpdateBus生命周期问题已解决！")
            sys.exit(0)
        else:
            print("\n❌ 部分测试失败，RegisterUpdateBus仍有问题")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 测试过程中发生严重错误: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
