#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PLL1 N Divider修复验证脚本
测试PLL1 N Divider控件的取值范围是否已修复为2-16383
"""

import sys
import subprocess
import time
from pathlib import Path

def test_pll1_n_divider_range():
    """测试PLL1 N Divider取值范围修复"""
    print("🔍 测试PLL1 N Divider取值范围修复")
    print("=" * 60)
    
    # 查找最新的Release版本
    project_root = Path.cwd().parent
    releases_dir = project_root / 'releases'
    latest_dir = releases_dir / 'latest'
    
    if not latest_dir.exists():
        print("❌ 找不到最新发布版本")
        return False
    
    # 查找Release exe文件
    exe_files = list(latest_dir.glob('*_Release.exe'))
    if not exe_files:
        print("❌ 找不到Release版本的exe文件")
        return False
    
    exe_file = exe_files[0]
    print(f"📦 测试文件: {exe_file.name}")
    print(f"📏 文件大小: {exe_file.stat().st_size / 1024 / 1024:.1f} MB")
    print()
    
    print("🔧 PLL1 N Divider修复验证:")
    print("   问题: 之前最大值只能到99")
    print("   修复: 现在最大值应该是16383 (2^14-1)")
    print("   寄存器: 对应寄存器0x69的PLL1_N[13:0]位域")
    print()
    
    print("🚀 启动程序进行验证...")
    print("   程序将启动，请手动验证以下功能:")
    print()
    print("📋 PLL1 N Divider测试清单:")
    print("   1. 打开PLL控制窗口:")
    print("      菜单栏 → 工具 → PLL控制(&P)")
    print()
    print("   2. 找到PLL1 N Divider控件:")
    print("      在PLL1部分找到标有'PLL1 N Divider'的数值输入框")
    print()
    print("   3. 测试取值范围:")
    print("      • 最小值: 应该能设置为2")
    print("      • 最大值: 应该能设置为16383")
    print("      • 无效值: 超过16383应该被限制")
    print("      • 之前问题: 最大值只能到99")
    print()
    print("   4. 验证方法:")
    print("      • 双击数值框")
    print("      • 输入16383，按回车")
    print("      • 检查是否接受这个值")
    print("      • 尝试输入更大的值(如20000)")
    print("      • 检查是否被限制在16383")
    print()
    print("✅ 如果能设置到16383，说明修复成功!")
    print("❌ 如果最大值仍然是99，说明需要进一步修复")
    print()
    
    try:
        # 启动程序
        print(f"启动程序: {exe_file}")
        process = subprocess.Popen([str(exe_file)], 
                                 cwd=exe_file.parent,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)
        
        print("程序已启动，请进行PLL1 N Divider测试...")
        print("测试完成后，关闭程序窗口即可")
        print()
        print("💡 测试提示:")
        print("   - 确保能打开PLL控制窗口")
        print("   - 找到PLL1 N Divider控件")
        print("   - 测试最大值是否为16383")
        print("   - 验证输入超过16383时的行为")
        
        # 等待程序结束
        process.wait()
        
        print()
        print("程序已关闭")
        
        if process.returncode == 0:
            print("✅ 程序正常退出")
            return True
        else:
            print(f"⚠️  程序退出码: {process.returncode}")
            stderr_output = process.stderr.read().decode('utf-8', errors='ignore')
            if stderr_output:
                print(f"错误输出: {stderr_output}")
            return False
            
    except Exception as e:
        print(f"❌ 启动程序失败: {e}")
        return False

def create_pll1_test_report():
    """创建PLL1 N Divider测试报告模板"""
    report_content = """
# PLL1 N Divider修复验证报告

## 📋 测试信息
- 测试版本: FSJ04832_RegisterTool_v1.0.3.14_Release.exe
- 测试日期: 2025-07-02
- 修复内容: PLL1 N Divider取值范围从99扩展到16383

## 🎯 测试项目

### 1. 控件访问
- [ ] 能够打开PLL控制窗口
- [ ] 能够找到PLL1 N Divider控件
- [ ] 控件响应正常

### 2. 取值范围测试
- [ ] 最小值: 能设置为2
- [ ] 最大值: 能设置为16383
- [ ] 边界测试: 输入16384被限制为16383
- [ ] 无效值处理: 输入0或1被限制为最小值2

### 3. 功能验证
- [ ] 数值输入正常
- [ ] 数值显示正确
- [ ] 与寄存器同步正常
- [ ] 计算结果正确

### 4. 回归测试
- [ ] 其他PLL控件功能正常
- [ ] PLL2 N Divider功能正常
- [ ] 整体PLL窗口功能正常

## 📝 测试结果

### 修复前问题
- 最大值限制: 99
- 寄存器范围: 应该支持2^14-1=16383

### 修复后结果
- 最小值: ___
- 最大值: ___
- 边界处理: ___
- 功能正常: ___

### 问题描述
- 

### 验证状态
- [ ] 完全修复 - 取值范围正确为2-16383
- [ ] 部分修复 - 范围有改善但未达到16383
- [ ] 未修复 - 仍然限制在99
- [ ] 新问题 - 出现其他问题

## 💡 技术细节

### 寄存器映射
- 寄存器地址: 0x69
- 位域: PLL1_N[13:0]
- 理论最大值: 2^14-1 = 16383
- 实际设置: ___

### 代码修改
- 文件: ui/handlers/ModernPLLHandler.py
- 方法: _setup_widget_ranges()
- 修改: 添加PLL1NDivider.setMaximum(16383)

## 📞 技术支持
如有问题，请提供:
1. 具体的数值输入和结果
2. 控件行为截图
3. 错误信息(如有)
"""
    
    report_file = Path(__file__).parent / 'pll1_n_divider_test_report.md'
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"📝 测试报告模板已创建: {report_file}")

def main():
    """主函数"""
    print("🔧 PLL1 N Divider修复验证工具")
    print("=" * 60)
    
    # 创建测试报告模板
    create_pll1_test_report()
    print()
    
    # 运行测试
    success = test_pll1_n_divider_range()
    
    print()
    print("=" * 60)
    if success:
        print("🎉 测试启动成功!")
        print()
        print("💡 下一步:")
        print("   1. 请根据测试清单验证PLL1 N Divider功能")
        print("   2. 填写测试报告: packaging/tests/pll1_n_divider_test_report.md")
        print("   3. 确认最大值是否已修复为16383")
        print("   4. 如果修复成功，客户版本就可以发布了!")
    else:
        print("⚠️  测试启动失败")
        print("   请检查exe文件是否存在和可执行")

if __name__ == '__main__':
    main()
