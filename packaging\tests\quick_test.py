#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速测试脚本
验证最新安全打包版本的版本号和工具窗口功能
"""

import sys
import subprocess
import time
from pathlib import Path

def test_latest_release():
    """测试最新发布版本"""
    print("🔍 快速测试最新安全打包版本")
    print("=" * 50)
    
    # 查找最新的Release版本
    project_root = Path.cwd().parent
    releases_dir = project_root / 'releases'
    latest_dir = releases_dir / 'latest'
    
    if not latest_dir.exists():
        print("❌ 找不到最新发布版本")
        return False
    
    # 查找Release exe文件
    exe_files = list(latest_dir.glob('*_Release.exe'))
    if not exe_files:
        print("❌ 找不到Release版本的exe文件")
        return False
    
    exe_file = exe_files[0]
    print(f"📦 测试文件: {exe_file.name}")
    print(f"📏 文件大小: {exe_file.stat().st_size / 1024 / 1024:.1f} MB")
    print()
    
    print("🚀 启动程序进行快速测试...")
    print("   程序将启动5秒后自动关闭")
    print("   请观察:")
    print("   1. 窗口标题是否显示正确版本号")
    print("   2. 工具菜单是否包含工具窗口选项")
    print()
    
    try:
        # 启动程序
        process = subprocess.Popen([str(exe_file)], 
                                 cwd=exe_file.parent)
        
        print("程序已启动，等待5秒...")
        time.sleep(5)
        
        # 终止程序
        process.terminate()
        time.sleep(1)
        
        if process.poll() is None:
            process.kill()
        
        print("程序已关闭")
        print()
        print("✅ 如果您看到:")
        print("   - 窗口标题显示 'FSJ04832 寄存器配置工具 v1.0.3.10'")
        print("   - 工具菜单包含各种工具窗口选项")
        print("   那么修复成功！")
        print()
        print("❌ 如果仍然显示 v1.0.0.0 或工具菜单为空，请告知我")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 FSJ04832 快速测试工具")
    print("=" * 50)
    
    success = test_latest_release()
    
    print()
    print("=" * 50)
    if success:
        print("🎉 测试完成!")
        print()
        print("💡 下一步:")
        print("   1. 如果版本号和工具窗口都正常，修复成功")
        print("   2. 如果还有问题，请告诉我具体现象")
        print("   3. 您可以将这个exe文件发给客户使用")
    else:
        print("⚠️  测试过程中发现问题")

if __name__ == '__main__':
    main()
