#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
重构验证脚本
验证重构后的代码是否能正常工作
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def verify_imports():
    """验证所有重构后的模块能否正常导入"""
    print("=== 验证模块导入 ===")
    
    try:
        # 验证UI组件
        from ui.components.MainWindowUI import MainWindowUI
        print("✓ MainWindowUI 导入成功")
        
        from ui.components.MenuManager import MenuManager
        print("✓ MenuManager 导入成功")
        
        # 验证控制器
        from ui.controllers.BatchOperationController import BatchOperationController
        print("✓ BatchOperationController 导入成功")
        
        # 验证主窗口仍然可以导入（跳过外部依赖问题）
        try:
            from ui.windows.RegisterMainWindow import RegisterMainWindow
            print("✓ RegisterMainWindow 导入成功")
        except ImportError as e:
            if "serial" in str(e) or "pyserial" in str(e):
                print("⚠ RegisterMainWindow 导入失败（外部依赖serial缺失，非重构问题）")
            else:
                raise e
        
        return True
        
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 验证失败: {e}")
        return False

def verify_class_structure():
    """验证类结构是否正确"""
    print("\n=== 验证类结构 ===")
    
    try:
        from ui.components.MainWindowUI import MainWindowUI
        from ui.components.MenuManager import MenuManager
        from ui.controllers.BatchOperationController import BatchOperationController
        
        # 检查MainWindowUI的关键方法
        ui_methods = ['setup_ui', '_create_central_widget', '_create_top_layout', 
                     '_create_button_layout', '_create_status_bar']
        for method in ui_methods:
            if hasattr(MainWindowUI, method):
                print(f"✓ MainWindowUI.{method} 存在")
            else:
                print(f"✗ MainWindowUI.{method} 缺失")
                return False
        
        # 检查MenuManager的关键方法
        menu_methods = ['create_menu_and_toolbar', '_create_file_menu', 
                       '_create_tools_menu', '_create_settings_menu']
        for method in menu_methods:
            if hasattr(MenuManager, method):
                print(f"✓ MenuManager.{method} 存在")
            else:
                print(f"✗ MenuManager.{method} 缺失")
                return False
        
        # 检查BatchOperationController的关键方法
        batch_methods = ['handle_read_all_requested', 'handle_write_all_requested',
                        'update_read_progress', 'update_write_progress']
        for method in batch_methods:
            if hasattr(BatchOperationController, method):
                print(f"✓ BatchOperationController.{method} 存在")
            else:
                print(f"✗ BatchOperationController.{method} 缺失")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ 类结构验证失败: {e}")
        return False

def verify_file_sizes():
    """验证文件大小变化"""
    print("\n=== 验证文件大小 ===")
    
    try:
        main_window_path = "ui/windows/RegisterMainWindow.py"
        if os.path.exists(main_window_path):
            with open(main_window_path, 'r', encoding='utf-8') as f:
                lines = len(f.readlines())
            print(f"✓ RegisterMainWindow.py: {lines} 行")
            
            if lines < 2000:  # 原来是2333行
                print(f"✓ 主文件代码行数已减少 (< 2000行)")
            else:
                print(f"⚠ 主文件代码行数仍然较多 ({lines}行)")
        
        # 检查新文件
        new_files = [
            "ui/components/MainWindowUI.py",
            "ui/components/MenuManager.py", 
            "ui/controllers/BatchOperationController.py"
        ]
        
        total_new_lines = 0
        for file_path in new_files:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = len(f.readlines())
                print(f"✓ {file_path}: {lines} 行")
                total_new_lines += lines
            else:
                print(f"✗ {file_path} 不存在")
                return False
        
        print(f"✓ 新增文件总计: {total_new_lines} 行")
        return True
        
    except Exception as e:
        print(f"✗ 文件大小验证失败: {e}")
        return False

def verify_dependencies():
    """验证依赖关系是否正确"""
    print("\n=== 验证依赖关系 ===")
    
    try:
        # 检查MainWindowUI是否正确依赖MenuManager
        from ui.components.MainWindowUI import MainWindowUI
        import inspect
        
        source = inspect.getsource(MainWindowUI._create_menu_and_toolbar)
        if "MenuManager" in source:
            print("✓ MainWindowUI 正确依赖 MenuManager")
        else:
            print("✗ MainWindowUI 未正确依赖 MenuManager")
            return False
        
        # 检查BatchOperationController的依赖
        from ui.controllers.BatchOperationController import BatchOperationController
        batch_source = inspect.getsource(BatchOperationController.__init__)
        if "main_window" in batch_source:
            print("✓ BatchOperationController 正确接收 main_window 参数")
        else:
            print("✗ BatchOperationController 未正确接收 main_window 参数")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 依赖关系验证失败: {e}")
        return False

def main():
    """主验证函数"""
    print("开始验证重构结果...")
    print("=" * 50)
    
    # 执行各项验证
    tests = [
        ("模块导入", verify_imports),
        ("类结构", verify_class_structure), 
        ("文件大小", verify_file_sizes),
        ("依赖关系", verify_dependencies)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} 验证出错: {e}")
            results.append((test_name, False))
    
    # 输出总结
    print("\n" + "=" * 50)
    print("=== 验证结果总结 ===")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项验证通过")
    
    if passed == total:
        print("\n🎉 重构验证完全成功！")
        print("所有组件都能正常工作，重构达到预期目标。")
        return True
    else:
        print(f"\n⚠️ 有 {total - passed} 项验证失败")
        print("需要检查和修复相关问题。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
