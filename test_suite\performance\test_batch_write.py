#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量读写性能测试工具
测试模拟模式和硬件模式下的批量操作性能
"""

import sys
import os
import time
import logging
import statistics
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer, QEventLoop
from contextlib import contextmanager

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@contextmanager
def performance_monitor(operation_name):
    """性能监控上下文管理器"""
    start_time = time.time()
    start_memory = get_memory_usage()

    try:
        yield
    finally:
        end_time = time.time()
        end_memory = get_memory_usage()

        duration = end_time - start_time
        memory_delta = end_memory - start_memory

        print(f"📊 {operation_name} 性能数据:")
        print(f"   ⏱️  执行时间: {duration:.3f} 秒")
        print(f"   💾 内存变化: {memory_delta:.2f} MB")
        print(f"   📈 平均速度: {1/duration:.1f} 操作/秒" if duration > 0 else "   📈 平均速度: 无限快")


def get_memory_usage():
    """获取当前内存使用量（MB）"""
    try:
        import psutil
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024
    except ImportError:
        return 0.0


class BatchPerformanceTester:
    """批量操作性能测试器"""

    def __init__(self):
        self.app = None
        self.main_window = None
        self.results = {}

    def setup(self):
        """设置测试环境"""
        print("🔧 设置测试环境...")

        # 创建QApplication
        self.app = QApplication(sys.argv)

        # 导入必要的模块
        from ui.windows.RegisterMainWindow import RegisterMainWindow

        # 创建主窗口
        self.main_window = RegisterMainWindow()
        print("✅ 主窗口创建成功")

        # 确保窗口完全初始化
        self.app.processEvents()

        return True

    def test_simulation_mode_performance(self):
        """测试模拟模式下的批量操作性能"""
        print("\n" + "="*60)
        print("🎯 测试模拟模式批量操作性能")
        print("="*60)

        # 确保处于模拟模式
        if hasattr(self.main_window, 'spi_service'):
            self.main_window.spi_service.set_simulation_mode(True)
            print("✅ 已切换到模拟模式")

        # 测试批量读取
        self._test_batch_read_performance("模拟模式")

        # 测试批量写入
        self._test_batch_write_performance("模拟模式")

    def test_hardware_mode_performance(self):
        """测试硬件模式下的批量操作性能（如果硬件可用）"""
        print("\n" + "="*60)
        print("🔌 测试硬件模式批量操作性能")
        print("="*60)

        # 检查硬件是否可用
        if hasattr(self.main_window, 'spi_service'):
            # 尝试切换到硬件模式
            available_ports = self.main_window.spi_service.get_available_ports()
            if available_ports:
                print(f"✅ 发现可用端口: {available_ports}")
                # 选择第一个可用端口
                success = self.main_window.spi_service.connect_to_port(available_ports[0])
                if success:
                    print("✅ 已连接到硬件")

                    # 测试批量读取
                    self._test_batch_read_performance("硬件模式")

                    # 测试批量写入
                    self._test_batch_write_performance("硬件模式")
                else:
                    print("❌ 硬件连接失败，跳过硬件模式测试")
            else:
                print("❌ 未发现可用硬件端口，跳过硬件模式测试")

    def _test_batch_read_performance(self, mode_name):
        """测试批量读取性能"""
        print(f"\n📖 测试 {mode_name} 批量读取性能...")

        if not hasattr(self.main_window, 'batch_operation_manager'):
            print("❌ 主窗口没有批量操作管理器")
            return

        batch_manager = self.main_window.batch_operation_manager

        # 测试不同批次大小
        batch_sizes = [10, 50, 100, 200]

        for batch_size in batch_sizes:
            print(f"\n🔍 测试批次大小: {batch_size}")

            with performance_monitor(f"{mode_name}_批量读取_{batch_size}个寄存器"):
                try:
                    # 设置批次大小
                    original_batch_size = batch_manager.BATCH_SIZE
                    batch_manager.BATCH_SIZE = min(batch_size, 50)  # 限制单批次大小

                    # 执行批量读取
                    self._execute_batch_read(batch_manager, batch_size)

                    # 恢复原始批次大小
                    batch_manager.BATCH_SIZE = original_batch_size

                except Exception as e:
                    print(f"❌ 批量读取测试失败: {str(e)}")

    def _test_batch_write_performance(self, mode_name):
        """测试批量写入性能"""
        print(f"\n📝 测试 {mode_name} 批量写入性能...")

        if not hasattr(self.main_window, 'batch_operation_manager'):
            print("❌ 主窗口没有批量操作管理器")
            return

        batch_manager = self.main_window.batch_operation_manager

        # 测试不同批次大小
        batch_sizes = [10, 50, 100, 200]

        for batch_size in batch_sizes:
            print(f"\n✏️  测试批次大小: {batch_size}")

            with performance_monitor(f"{mode_name}_批量写入_{batch_size}个寄存器"):
                try:
                    # 设置批次大小
                    original_batch_size = batch_manager.BATCH_SIZE
                    batch_manager.BATCH_SIZE = min(batch_size, 50)  # 限制单批次大小

                    # 执行批量写入
                    self._execute_batch_write(batch_manager, batch_size)

                    # 恢复原始批次大小
                    batch_manager.BATCH_SIZE = original_batch_size

                except Exception as e:
                    print(f"❌ 批量写入测试失败: {str(e)}")

    def _execute_batch_read(self, batch_manager, target_count):
        """执行批量读取操作"""
        # 模拟批量读取
        print(f"   🚀 开始读取 {target_count} 个寄存器...")

        # 等待操作完成的简单方法
        start_time = time.time()
        timeout = 30  # 30秒超时

        # 触发批量读取
        batch_manager.handle_read_all_requested()

        # 等待完成
        while batch_manager.is_batch_reading and (time.time() - start_time) < timeout:
            self.app.processEvents()
            time.sleep(0.01)

        if batch_manager.is_batch_reading:
            print(f"   ⚠️  批量读取超时")
            batch_manager._finish_read_all()  # 强制完成
        else:
            print(f"   ✅ 批量读取完成")

    def _execute_batch_write(self, batch_manager, target_count):
        """执行批量写入操作"""
        # 模拟批量写入
        print(f"   🚀 开始写入 {target_count} 个寄存器...")

        # 等待操作完成的简单方法
        start_time = time.time()
        timeout = 30  # 30秒超时

        # 触发批量写入
        batch_manager.handle_write_all_requested()

        # 等待完成
        while batch_manager.is_batch_writing and (time.time() - start_time) < timeout:
            self.app.processEvents()
            time.sleep(0.01)

        if batch_manager.is_batch_writing:
            print(f"   ⚠️  批量写入超时")
            batch_manager._finish_write_all()  # 强制完成
        else:
            print(f"   ✅ 批量写入完成")


def test_batch_performance():
    """主测试函数"""
    print("=" * 80)
    print("🚀 批量读写性能测试工具")
    print("=" * 80)

    tester = BatchPerformanceTester()

    try:
        # 设置测试环境
        if not tester.setup():
            print("❌ 测试环境设置失败")
            return

        # 测试模拟模式性能
        tester.test_simulation_mode_performance()

        # 测试硬件模式性能（如果可用）
        tester.test_hardware_mode_performance()

        print("\n" + "=" * 80)
        print("✅ 性能测试完成")
        print("=" * 80)

        # 显示建议
        print("\n💡 性能优化建议:")
        print("   1. 模拟模式主要受UI更新频率影响")
        print("   2. 硬件模式额外受SPI通信速度影响")
        print("   3. 可以通过调整批次大小来平衡性能和响应性")
        print("   4. 批量操作期间暂停UI更新可以提高性能")

    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_batch_performance()
