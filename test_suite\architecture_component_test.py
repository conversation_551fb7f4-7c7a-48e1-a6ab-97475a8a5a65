#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
FSJ04832寄存器配置工具 - 架构组件测试
测试现代化处理器、事件总线、插件系统、依赖注入等架构组件
"""

import sys
import os
import unittest
import importlib
from unittest.mock import Mock, patch, MagicMock
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtTest import QTest

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from test_config import TestConfig
from test_utils import TestUtils

class ArchitectureComponentTest(unittest.TestCase):
    """架构组件测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        cls.app = TestUtils.get_test_app()
        cls.test_config = TestConfig()
        
    def setUp(self):
        """每个测试方法前的初始化"""
        self.mock_main_window = Mock()
        
    def tearDown(self):
        """每个测试方法后的清理"""
        self.mock_main_window = None
        
    def test_01_modern_handlers_availability(self):
        """测试现代化处理器可用性"""
        print("\n=== 测试现代化处理器可用性 ===")
        
        modern_handlers = [
            'ui.handlers.ModernRegisterTreeHandler',
            'ui.handlers.ModernRegisterTableHandler', 
            'ui.handlers.ModernRegisterIOHandler',
            'ui.handlers.ModernClkinControlHandler',
            'ui.handlers.ModernClkOutputsHandler',
            'ui.handlers.ModernPLLControlHandler',
            'ui.handlers.ModernSyncSysRefHandler',
            'ui.handlers.ModernSetModesHandler'
        ]
        
        available_handlers = []
        missing_handlers = []
        
        for handler_path in modern_handlers:
            try:
                module = importlib.import_module(handler_path)
                available_handlers.append(handler_path)
                print(f"✅ {handler_path} - 可用")
            except ImportError as e:
                missing_handlers.append(handler_path)
                print(f"❌ {handler_path} - 缺失: {str(e)}")
                
        print(f"\n📊 现代化处理器统计:")
        print(f"   可用: {len(available_handlers)}/{len(modern_handlers)}")
        print(f"   缺失: {len(missing_handlers)}")
        
        # 至少应该有一半的处理器可用
        self.assertGreaterEqual(len(available_handlers), len(modern_handlers) // 2)
        
    def test_02_event_bus_singleton(self):
        """测试事件总线单例模式"""
        print("\n=== 测试事件总线单例模式 ===")
        
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            
            # 获取两个实例
            bus1 = RegisterUpdateBus.instance()
            bus2 = RegisterUpdateBus.instance()
            
            # 验证是同一个实例
            self.assertIs(bus1, bus2)
            
            # 验证基本属性
            self.assertIsInstance(bus1, QObject)
            self.assertTrue(hasattr(bus1, 'register_updated'))
            self.assertTrue(hasattr(bus1, 'clock_source_selected'))
            
            print("✅ 事件总线单例模式测试通过")
            
        except Exception as e:
            print(f"❌ 事件总线单例模式测试失败: {str(e)}")
            raise
            
    def test_03_event_bus_signals(self):
        """测试事件总线信号机制"""
        print("\n=== 测试事件总线信号机制 ===")
        
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            
            bus = RegisterUpdateBus.instance()
            
            # 测试信号连接和发送
            received_data = []
            
            def register_handler(addr, value):
                received_data.append(('register', addr, value))
                
            def clock_handler(source, freq, div):
                received_data.append(('clock', source, freq, div))
                
            # 连接信号
            bus.register_updated.connect(register_handler)
            bus.clock_source_selected.connect(clock_handler)
            
            # 发送信号
            bus.emit_register_updated("0x30", 0x5678)
            bus.emit_clock_source_selected("OSCin", 122.88, 1)
            
            # 处理事件循环
            QTest.qWait(100)
            
            # 验证信号接收
            self.assertEqual(len(received_data), 2)
            self.assertEqual(received_data[0], ('register', "0x30", 0x5678))
            self.assertEqual(received_data[1], ('clock', "OSCin", 122.88, 1))
            
            print("✅ 事件总线信号机制测试通过")
            
        except Exception as e:
            print(f"❌ 事件总线信号机制测试失败: {str(e)}")
            raise
            
    def test_04_plugin_manager_structure(self):
        """测试插件管理器结构"""
        print("\n=== 测试插件管理器结构 ===")
        
        try:
            from core.services.plugin.PluginManager import PluginManager
            
            # 创建插件管理器
            plugin_manager = PluginManager()
            
            # 验证基本方法存在
            required_methods = [
                'add_plugin_directory',
                'scan_plugins', 
                'get_plugins',
                'initialize_plugins'
            ]
            
            for method_name in required_methods:
                self.assertTrue(hasattr(plugin_manager, method_name))
                print(f"✅ 方法 {method_name} 存在")
                
            print("✅ 插件管理器结构测试通过")
            
        except Exception as e:
            print(f"❌ 插件管理器结构测试失败: {str(e)}")
            raise
            
    def test_05_plugin_discovery(self):
        """测试插件发现机制"""
        print("\n=== 测试插件发现机制 ===")
        
        try:
            from core.services.plugin.PluginManager import PluginManager
            
            plugin_manager = PluginManager()
            
            # 添加插件目录
            plugin_manager.add_plugin_directory("plugins")
            
            # 扫描插件
            plugin_manager.scan_plugins()
            
            # 获取发现的插件
            plugins = plugin_manager.get_plugins()
            
            print(f"✅ 插件发现机制测试通过")
            print(f"   发现插件数量: {len(plugins)}")
            
            # 列出发现的插件
            for plugin_name, plugin_info in plugins.items():
                print(f"   - {plugin_name}: {plugin_info.description if hasattr(plugin_info, 'description') else 'N/A'}")
                
        except Exception as e:
            print(f"❌ 插件发现机制测试失败: {str(e)}")
            raise
            
    def test_06_dependency_injection_container(self):
        """测试依赖注入容器"""
        print("\n=== 测试依赖注入容器 ===")
        
        try:
            # 检查依赖注入相关组件
            di_components = [
                'core.services.dependency_injection.DIContainer',
                'core.services.dependency_injection.ServiceRegistry'
            ]
            
            available_components = []
            
            for component_path in di_components:
                try:
                    module = importlib.import_module(component_path)
                    available_components.append(component_path)
                    print(f"✅ {component_path} - 可用")
                except ImportError:
                    print(f"⚠️  {component_path} - 不可用")
                    
            # 如果有依赖注入组件，进行基本测试
            if available_components:
                print("✅ 依赖注入容器测试通过")
            else:
                print("⚠️  依赖注入容器未实现，使用传统方式")
                
        except Exception as e:
            print(f"❌ 依赖注入容器测试失败: {str(e)}")
            raise
            
    def test_07_service_layer_architecture(self):
        """测试服务层架构"""
        print("\n=== 测试服务层架构 ===")
        
        try:
            # 检查核心服务
            core_services = [
                'core.services.register.RegisterManager',
                'core.services.plugin.PluginManager',
                'core.services.config.ConfigurationService'
            ]
            
            available_services = []
            
            for service_path in core_services:
                try:
                    module = importlib.import_module(service_path)
                    available_services.append(service_path)
                    print(f"✅ {service_path} - 可用")
                except ImportError as e:
                    print(f"❌ {service_path} - 不可用: {str(e)}")
                    
            print(f"\n📊 服务层统计:")
            print(f"   可用服务: {len(available_services)}/{len(core_services)}")
            
            # 至少应该有寄存器管理服务
            self.assertGreaterEqual(len(available_services), 1)
            
            print("✅ 服务层架构测试通过")
            
        except Exception as e:
            print(f"❌ 服务层架构测试失败: {str(e)}")
            raise
            
    def test_08_ui_managers_structure(self):
        """测试UI管理器结构"""
        print("\n=== 测试UI管理器结构 ===")
        
        try:
            # 检查UI管理器
            ui_managers = [
                'ui.managers.InitializationManager',
                'ui.managers.RegisterDisplayManager',
                'ui.managers.RegisterOperationManager'
            ]
            
            available_managers = []
            
            for manager_path in ui_managers:
                try:
                    module = importlib.import_module(manager_path)
                    available_managers.append(manager_path)
                    print(f"✅ {manager_path} - 可用")
                except ImportError as e:
                    print(f"❌ {manager_path} - 不可用: {str(e)}")
                    
            print(f"\n📊 UI管理器统计:")
            print(f"   可用管理器: {len(available_managers)}/{len(ui_managers)}")
            
            print("✅ UI管理器结构测试通过")
            
        except Exception as e:
            print(f"❌ UI管理器结构测试失败: {str(e)}")
            raise
            
    def test_09_coordinator_pattern(self):
        """测试协调器模式"""
        print("\n=== 测试协调器模式 ===")
        
        try:
            from ui.coordinators.EventCoordinator import EventCoordinator
            
            # 创建协调器
            coordinator = EventCoordinator(self.mock_main_window)
            
            # 验证基本方法
            required_methods = [
                'handle_read_requested',
                'handle_simulation_mode_toggle',
                'handle_auto_write_mode_toggle'
            ]
            
            for method_name in required_methods:
                self.assertTrue(hasattr(coordinator, method_name))
                print(f"✅ 协调器方法 {method_name} 存在")
                
            print("✅ 协调器模式测试通过")
            
        except Exception as e:
            print(f"❌ 协调器模式测试失败: {str(e)}")
            raise

def run_architecture_component_tests():
    """运行架构组件测试"""
    print("🏗️  开始FSJ04832寄存器配置工具架构组件测试")
    print("=" * 60)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(ArchitectureComponentTest)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果摘要
    print("\n" + "=" * 60)
    print("📊 架构组件测试结果摘要:")
    print(f"   总测试数: {result.testsRun}")
    print(f"   成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"   失败: {len(result.failures)}")
    print(f"   错误: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"   - {test}")
            
    if result.errors:
        print("\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"   - {test}")
            
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\n🎯 测试成功率: {success_rate:.1f}%")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_architecture_component_tests()
    sys.exit(0 if success else 1)
