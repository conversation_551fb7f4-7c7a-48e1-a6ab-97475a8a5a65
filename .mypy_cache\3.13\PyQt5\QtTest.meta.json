{"data_mtime": 1754292378, "dep_lines": [25, 27, 28, 29, 23, 25, 32, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 20, 10, 5, 30, 30, 30], "dependencies": ["PyQt5.sip", "PyQt5.QtCore", "PyQt5.QtGui", "PyQt5.QtWidgets", "typing", "PyQt5", "datetime", "builtins", "_frozen_importlib", "abc", "types"], "hash": "420c356fa1cc5ac0f1d00dc4463b7a5232a5491f", "id": "PyQt5.QtTest", "ignore_all": true, "interface_hash": "4aa515c6a4901d468a5a16749ce2522c9e5cf2ec", "mtime": 1745378112, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\QtTest.pyi", "plugin_data": null, "size": 12275, "suppressed": [], "version_id": "1.15.0"}