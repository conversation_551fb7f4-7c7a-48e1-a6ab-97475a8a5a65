@echo off
title FSJ04832 Package Manager

REM Get the directory where this bat file is located
set BAT_DIR=%~dp0
REM Go up one level to packaging directory
set PACKAGING_DIR=%BAT_DIR%..

echo ============================================================
echo FSJ04832 Package Management System
echo ============================================================
echo.
echo Bat file location: %BAT_DIR%
echo Packaging directory: %PACKAGING_DIR%
echo.

REM Check if Python is available
python --version >NUL 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python and add it to your system PATH
    echo.
    pause
    exit /b 1
)

echo Python version:
python --version
echo.

REM Change to packaging directory
cd /d "%PACKAGING_DIR%"
echo Current working directory: %CD%
echo.

REM Check if package.py exists
if not exist package.py (
    echo ERROR: package.py not found in %CD%
    echo Please make sure you are running this from the correct location
    echo.
    pause
    exit /b 1
)

echo Found package.py, starting menu...
echo.
echo ============================================================
echo Select an option:
echo ============================================================
echo 1. Start GUI Version Manager
echo 2. Build (increment build number)
echo 3. Build (increment patch version)  
echo 4. View version history
echo 5. Clean old versions
echo 6. Run tests
echo 7. Show help
echo 8. Exit
echo.
set /p choice=Enter your choice (1-8): 

if "%choice%"=="1" goto gui
if "%choice%"=="2" goto build_build
if "%choice%"=="3" goto build_patch
if "%choice%"=="4" goto list
if "%choice%"=="5" goto clean
if "%choice%"=="6" goto test
if "%choice%"=="7" goto help
if "%choice%"=="8" goto exit
goto invalid

:gui
echo.
echo Starting GUI Version Manager...
python package.py gui
goto end

:build_build
echo.
echo Building (increment build number)...
python package.py build build
goto end

:build_patch
echo.
echo Building (increment patch version)...
python package.py build patch
goto end

:list
echo.
echo Viewing version history...
python package.py list
goto end

:clean
echo.
echo Cleaning old versions...
python package.py clean
goto end

:test
echo.
echo Running tests...
python package.py test
goto end

:help
echo.
echo Showing help...
python package.py --help
goto end

:invalid
echo.
echo Invalid choice. Please run the script again.
goto end

:exit
echo.
echo Exiting...
exit /b 0

:end
echo.
if errorlevel 1 (
    echo.
    echo Command completed with error code: %errorlevel%
)
echo Press any key to exit...
pause >NUL
