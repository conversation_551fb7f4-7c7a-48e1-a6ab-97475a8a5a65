#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修复后的现代化PLL处理器
验证控件初始化是否正常
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication, QComboBox
from PyQt5.QtTest import QTest


def test_modern_pll_initialization():
    """测试现代化PLL处理器初始化"""
    print("=" * 60)
    print("测试修复后的现代化PLL处理器")
    print("=" * 60)
    
    try:
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建寄存器管理器
        from core.services.register.RegisterManager import RegisterManager
        config_path = os.path.join(project_root, 'lib', 'register.json')
        
        if not os.path.exists(config_path):
            print("❌ 寄存器配置文件不存在")
            return False

        import json
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        register_manager = RegisterManager(registers_config)
        
        print("1. 创建现代化PLL处理器...")
        from ui.handlers.ModernPLLHandler import ModernPLLHandler
        modern_handler = ModernPLLHandler(None, register_manager)
        
        print("✓ 现代化PLL处理器创建成功")
        print(f"  - 窗口标题: {modern_handler.windowTitle()}")
        
        print("\n2. 检查ComboBox控件初始化...")
        
        # 检查是否有combobox_options_map
        if hasattr(modern_handler, 'combobox_options_map'):
            print("✓ combobox_options_map存在")
            print(f"  - 包含 {len(modern_handler.combobox_options_map)} 个控件配置")
        else:
            print("❌ combobox_options_map不存在")
            return False
        
        # 检查关键ComboBox控件
        key_combos = ['comboPLL1WindSize', 'PLL2WINDSIZE', 'PLL1CPState', 'PLL2CPState', 'PLL2R3']
        initialized_count = 0
        
        for combo_name in key_combos:
            if hasattr(modern_handler.ui, combo_name):
                combo_widget = getattr(modern_handler.ui, combo_name)
                if isinstance(combo_widget, QComboBox):
                    item_count = combo_widget.count()
                    current_text = combo_widget.currentText()
                    print(f"✓ {combo_name}: {item_count} 个选项, 当前: '{current_text}'")
                    initialized_count += 1
                else:
                    print(f"❌ {combo_name}: 不是ComboBox控件")
            else:
                print(f"❌ {combo_name}: 控件不存在")
        
        print(f"\n  已初始化 {initialized_count}/{len(key_combos)} 个关键ComboBox控件")
        
        print("\n3. 检查特殊控件PLL2R3...")
        if hasattr(modern_handler.ui, 'PLL2R3'):
            pll2r3 = modern_handler.ui.PLL2R3
            print(f"✓ PLL2R3控件存在，包含 {pll2r3.count()} 个选项")
            
            # 检查选项和数据
            for i in range(pll2r3.count()):
                text = pll2r3.itemText(i)
                data = pll2r3.itemData(i)
                print(f"  选项 {i}: '{text}' -> 值: {data}")
        else:
            print("❌ PLL2R3控件不存在")
        
        print("\n4. 检查频率计算功能...")
        try:
            modern_handler.calculate_output_frequencies()
            print("✓ 频率计算功能正常")
        except Exception as e:
            print(f"❌ 频率计算功能异常: {str(e)}")
            return False
        
        print("\n5. 检查UI控件映射...")
        if hasattr(modern_handler, 'widget_register_map'):
            print(f"✓ 控件映射存在，包含 {len(modern_handler.widget_register_map)} 个映射")
        else:
            print("❌ 控件映射不存在")
            return False
        
        print("\n6. 测试窗口显示...")
        modern_handler.resize(800, 600)
        modern_handler.show()
        QTest.qWait(2000)  # 显示2秒
        print("✓ 窗口显示正常")
        modern_handler.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_factory_creation():
    """测试工厂创建"""
    print("\n" + "=" * 60)
    print("测试工厂创建现代化PLL处理器")
    print("=" * 60)
    
    try:
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建寄存器管理器
        from core.services.register.RegisterManager import RegisterManager
        config_path = os.path.join(project_root, 'lib', 'register.json')
        
        import json
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        register_manager = RegisterManager(registers_config)
        
        # 创建现代化工厂
        from ui.factories.ModernToolWindowFactory import ModernToolWindowFactory
        
        class MockMainWindow:
            def __init__(self):
                self.register_manager = register_manager
        
        mock_main_window = MockMainWindow()
        factory = ModernToolWindowFactory(mock_main_window)
        
        print("1. 测试工厂创建PLL窗口...")
        pll_window = factory.create_window_by_type('pll_control')
        
        if pll_window:
            print("✓ 工厂创建PLL窗口成功")
            print(f"  - 窗口类型: {type(pll_window).__name__}")
            
            if 'Modern' in type(pll_window).__name__:
                print("✓ 工厂使用现代化处理器")
            else:
                print("❌ 工厂未使用现代化处理器")
                return False
            
            # 检查控件初始化
            print("\n2. 检查工厂创建的窗口控件...")
            if hasattr(pll_window, 'combobox_options_map'):
                print(f"✓ ComboBox选项映射存在: {len(pll_window.combobox_options_map)} 个")
            else:
                print("❌ ComboBox选项映射不存在")
                return False
            
            # 测试窗口显示
            pll_window.show()
            QTest.qWait(1000)
            print("✓ 工厂创建的窗口显示正常")
            pll_window.close()
            
            return True
        else:
            print("❌ 工厂创建PLL窗口失败")
            return False
            
    except Exception as e:
        print(f"❌ 工厂测试出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("开始测试修复后的现代化PLL处理器...")
    
    # 测试直接创建
    success1 = test_modern_pll_initialization()
    
    # 测试工厂创建
    success2 = test_factory_creation()
    
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    if success1 and success2:
        print("🎉 现代化PLL处理器修复验证完全成功！")
        print("✅ 通过所有测试")
        print("\n📋 验证结果:")
        print("   - ✅ 现代化处理器创建成功")
        print("   - ✅ ComboBox控件正确初始化")
        print("   - ✅ 特殊控件PLL2R3配置正确")
        print("   - ✅ 频率计算功能正常")
        print("   - ✅ 控件映射完整")
        print("   - ✅ 窗口显示正常")
        print("   - ✅ 工厂创建正常")
        print("\n🎯 结论:")
        print("   现代化PLL处理器已修复，")
        print("   控件初始化问题已解决！")
        return True
    else:
        print("❌ 现代化PLL处理器修复验证未完全通过")
        if not success1:
            print("   - ❌ 直接创建测试失败")
        if not success2:
            print("   - ❌ 工厂创建测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
