#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的打包脚本
使用更新的 build.spec 文件进行打包
"""

import os
import sys
import json
import shutil
import subprocess
import time
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = Path.cwd()
if current_dir.name == 'scripts':
    # 如果当前在scripts目录，向上两级到项目根目录
    project_root = current_dir.parent.parent
else:
    # 否则假设当前就是项目根目录
    project_root = current_dir

# 将项目根目录添加到路径末尾，避免与系统模块冲突
sys.path.append(str(project_root))

def get_version_info():
    """获取版本信息"""
    try:
        version_file = project_root / 'packaging' / 'config' / 'version.json'
        if version_file.exists():
            with open(version_file, 'r', encoding='utf-8') as f:
                version_data = json.load(f)
                return version_data.get('version', '1.0.0')
    except Exception as e:
        print(f"读取版本信息失败: {e}")
    return '1.0.0'

def clean_build_dirs():
    """清理构建目录"""
    print("🧹 清理构建目录...")
    
    dirs_to_clean = [
        project_root / 'build',
        project_root / 'dist',
        project_root / 'packaging' / 'scripts' / 'build',
        project_root / 'packaging' / 'scripts' / 'dist'
    ]
    
    for dir_path in dirs_to_clean:
        if dir_path.exists():
            try:
                shutil.rmtree(dir_path)
                print(f"   ✅ 已清理: {dir_path}")
            except Exception as e:
                print(f"   ⚠️ 清理失败 {dir_path}: {e}")

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    
    required_modules = [
        'PyQt5',
        'serial',
        'PyInstaller'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            if module == 'PyInstaller':
                import PyInstaller
                print(f"   ✅ {module} (v{PyInstaller.__version__})")
            else:
                __import__(module)
                print(f"   ✅ {module}")
        except ImportError as e:
            print(f"   ❌ {module} - 缺失: {e}")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n❌ 缺失依赖: {missing_modules}")
        print("请运行: pip install PyQt5 pyserial pyinstaller")
        return False
    
    return True

def check_required_files():
    """检查必需文件"""
    print("📁 检查必需文件...")

    required_files = [
        project_root / 'main.py',
        project_root / 'images' / 'logo.ico',
        Path(__file__).parent / 'build.spec'  # 当前目录下的build.spec
    ]

    required_dirs = [
        project_root / 'config',
        project_root / 'plugins',
        project_root / 'ui' / 'forms',
        project_root / 'lib'
    ]
    
    missing_items = []
    
    for file_path in required_files:
        if file_path.exists():
            print(f"   ✅ {file_path.name}")
        else:
            print(f"   ❌ {file_path} - 缺失")
            missing_items.append(str(file_path))
    
    for dir_path in required_dirs:
        if dir_path.exists():
            print(f"   ✅ {dir_path.name}/")
        else:
            print(f"   ❌ {dir_path}/ - 缺失")
            missing_items.append(str(dir_path))
    
    if missing_items:
        print(f"\n❌ 缺失文件/目录: {missing_items}")
        return False
    
    return True

def run_pyinstaller():
    """运行PyInstaller"""
    print("🔨 开始打包...")
    
    # 切换到脚本目录
    script_dir = project_root / 'packaging' / 'scripts'
    os.chdir(script_dir)
    
    # 构建命令
    cmd = [
        'pyinstaller',
        '--clean',
        '--noconfirm',
        'build.spec'
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    print(f"工作目录: {script_dir}")
    
    try:
        # 运行PyInstaller
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ PyInstaller 执行成功")
            return True
        else:
            print("❌ PyInstaller 执行失败")
            print(f"错误输出:\n{result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 执行PyInstaller时出错: {e}")
        return False

def copy_to_releases():
    """复制到发布目录"""
    print("📦 复制到发布目录...")
    
    version = get_version_info()
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    release_name = f"{timestamp}_v{version}"
    
    # 源目录和目标目录
    script_dir = project_root / 'packaging' / 'scripts'
    source_dir = script_dir / 'dist'
    target_dir = project_root / 'releases' / release_name
    
    if not source_dir.exists():
        print(f"❌ 源目录不存在: {source_dir}")
        return False
    
    try:
        # 创建发布目录
        target_dir.mkdir(parents=True, exist_ok=True)
        
        # 复制所有内容
        for item in source_dir.iterdir():
            if item.is_dir():
                shutil.copytree(item, target_dir / item.name, dirs_exist_ok=True)
            else:
                shutil.copy2(item, target_dir)
        
        print(f"✅ 已复制到: {target_dir}")
        
        # 更新latest链接
        latest_dir = project_root / 'releases' / 'latest'
        if latest_dir.exists():
            if latest_dir.is_symlink():
                latest_dir.unlink()
            else:
                shutil.rmtree(latest_dir)
        
        # 创建软链接或复制
        try:
            latest_dir.symlink_to(release_name, target_is_directory=True)
            print(f"✅ 已更新latest链接")
        except OSError:
            # Windows上可能无法创建符号链接，直接复制
            shutil.copytree(target_dir, latest_dir, dirs_exist_ok=True)
            print(f"✅ 已复制到latest目录")
        
        return True
        
    except Exception as e:
        print(f"❌ 复制失败: {e}")
        return False

def create_launcher():
    """创建启动器"""
    print("🚀 创建启动器...")
    
    version = get_version_info()
    exe_name = f'FSJ04832_RegisterTool_v{version}'
    
    launcher_content = f"""@echo off
chcp 65001 > nul
title FSJ04832 寄存器配置工具 v{version}

echo.
echo ========================================
echo   FSJ04832 寄存器配置工具 v{version}
echo ========================================
echo.

cd /d "%~dp0"

if exist "{exe_name}.exe" (
    echo 启动程序...
    start "" "{exe_name}.exe"
    echo 程序已启动
) else (
    echo 错误: 找不到程序文件 {exe_name}.exe
    pause
)
"""
    
    try:
        latest_dir = project_root / 'releases' / 'latest'
        if latest_dir.exists():
            launcher_file = latest_dir / '启动程序.bat'
            with open(launcher_file, 'w', encoding='gbk') as f:
                f.write(launcher_content)
            print(f"✅ 已创建启动器: {launcher_file}")
            return True
    except Exception as e:
        print(f"❌ 创建启动器失败: {e}")
    
    return False

def main():
    """主函数"""
    print("🎯 FSJ04832 寄存器配置工具 - 改进打包脚本")
    print("=" * 50)

    # 检查是否只做检查
    check_only = len(sys.argv) > 1 and '--check-only' in sys.argv

    start_time = time.time()

    # 检查依赖
    if not check_dependencies():
        return False

    # 检查必需文件
    if not check_required_files():
        return False

    if check_only:
        print("\n✅ 所有检查通过，可以进行打包")
        return True

    # 清理构建目录
    clean_build_dirs()

    # 运行PyInstaller
    if not run_pyinstaller():
        return False

    # 复制到发布目录
    if not copy_to_releases():
        return False

    # 创建启动器
    create_launcher()

    # 完成
    end_time = time.time()
    duration = end_time - start_time

    print("\n" + "=" * 50)
    print(f"🎉 打包完成! 耗时: {duration:.1f}秒")
    print(f"📁 发布目录: {project_root / 'releases' / 'latest'}")
    print("=" * 50)

    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ 打包失败!")
        sys.exit(1)
    else:
        print("\n✅ 打包成功!")
        sys.exit(0)
