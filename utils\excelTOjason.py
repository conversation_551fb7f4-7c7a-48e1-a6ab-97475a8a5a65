import pandas as pd
import json
import re
import os


def get_bit_length(bit_str):
    """计算位字段的长度"""
    try:
        if ':' in bit_str:
            # 处理类似 "8:5" 的格式
            high, low = map(int, bit_str.split(':'))
            return high - low + 1
        else:
            # 单个位
            return 1
    except Exception as e:
        print(f"Error calculating bit length for '{bit_str}': {str(e)}")
        return 1


def convert_to_binary(value_str, bit_length):
    """将各种格式的数值转换为二进制字符串"""
    try:
        # 如果值是 'NC'，返回对应长度的零
        if isinstance(value_str, str) and value_str.strip().upper() == 'NC':
            return '0' * bit_length

        value_str = str(value_str).strip()

        # 处理 "A0(HEX)" 这样的格式
        if '(HEX)' in value_str:
            hex_value = value_str.split('(')[0].strip()
            return format(int(hex_value, 16), f'0{bit_length}b')

        # 处理 "10(0x0A)" 这样的格式
        elif '(' in value_str and ')' in value_str and '0x' in value_str:
            decimal_value = int(value_str.split('(')[0].strip())
            return format(decimal_value, f'0{bit_length}b')

        # 处理纯数字（假设是十进制）
        else:
            return format(int(value_str), f'0{bit_length}b')

    except (ValueError, TypeError) as e:
        print(f"Warning: Could not convert value '{value_str}' to binary, error: {str(e)}")
        return '0' * bit_length


def clean_value(value):
    """清理值，将空字符串、'nan'、空格等转换为 None"""
    if pd.isna(value):
        return None
    if isinstance(value, str):
        value = value.strip()
        if value == '' or value.lower() == 'nan':
            return None
    return value


def excel_to_register_json(excel_file):
    # 读取指定sheet的Excel文件
    df = pd.read_excel(excel_file, sheet_name='FSJ048xx-16bits addr')

    registers = {}
    current_register = None
    current_bits = []

    # 遍历每一行
    for index, row in df.iterrows():
        # 跳过完全为空的行
        if row.isna().all():
            continue

        # 检查第一列是否包含寄存器标题
        first_cell = str(row.iloc[0])
        if 'Register' in first_cell and '0x' in first_cell:
            # 如果已经有寄存器数据，保存之前的寄存器
            if current_register:
                registers[current_register] = {"bits": current_bits}
                current_bits = []

            # 提取新的寄存器地址
            match = re.search(r'0x[0-9A-Fa-f]+', first_cell)
            if match:
                current_register = match.group()
            continue

        # 跳过列标题行
        if any(str(row.iloc[0]).strip() == x for x in ['BIT', 'Bit']):
            continue

        # 检查是否是有效的数据行
        if pd.notna(row.iloc[0]) and str(row.iloc[0]).strip() != '':
            try:
                # 处理位信息
                bit_value = str(row.iloc[0])
                bit_length = get_bit_length(bit_value)

                # 处理默认值，转换为二进制
                default_value = row.iloc[2]  # POR DEFAULT 列
                if pd.notna(default_value):
                    if isinstance(default_value, str) and default_value.strip().upper() == 'NC':
                        binary_value = '0' * bit_length
                    else:
                        binary_value = convert_to_binary(default_value, bit_length)
                else:
                    binary_value = None

                # 创建基本的位信息字典
                bit_info = {
                    "bit": bit_value,
                    "name": clean_value(row.iloc[1]),  # NAME 列
                    "default": binary_value,
                    "widget_name": clean_value(row.iloc[3]),  # Widget Name 列
                    "widget_type": clean_value(row.iloc[4]),  # Widget Type 列
                    "options": clean_value(row.iloc[5]),  # Options 列
                    "description": clean_value(row.iloc[6])  # DESCRIPTION 列
                }

                # 检查是否有额外的描述表格
                if bit_info["name"] and "SCLK0_1_DIS_MODE[1:0]" in bit_info["name"]:
                    table_data = []
                    next_index = index + 1
                    while next_index < len(df):
                        next_row = df.iloc[next_index]
                        # 如果遇到新的位定义或寄存器，就停止
                        if pd.notna(next_row.iloc[0]) and (
                                str(next_row.iloc[0]).strip() != '' and not str(next_row.iloc[0]).startswith('0(0x')):
                            break
                        # 如果是表格数据
                        if pd.notna(next_row.iloc[0]) and str(next_row.iloc[0]).startswith('0(0x'):
                            table_data.append({
                                "field": str(next_row.iloc[0]),
                                "value": clean_value(next_row.iloc[1])
                            })
                        next_index += 1
                    if table_data:
                        bit_info["mode_table"] = table_data

                current_bits.append(bit_info)

            except Exception as e:
                print(f"Error processing row {index}: {str(e)}")
                continue

    # 保存最后一个寄存器
    if current_register and current_bits:
        registers[current_register] = {"bits": current_bits}

    return registers


# def excel_to_register_json(excel_file):
#     # 读取指定sheet的Excel文件
#     df = pd.read_excel(excel_file, sheet_name='FSJ048xx-16bits addr')
#
#     registers = {}
#     current_register = None
#     current_register_dict = {}  # 建立 current_register_dict 变量
#
#     # 遍历每一行
#     for index, row in df.iterrows():
#         # 跳过完全为空的行
#         if row.isna().all():
#             continue
#
#         # 检查第一列是否包含寄存器标题
#         first_cell = str(row.iloc[0])
#         if 'Register' in first_cell and '0x' in first_cell:
#             # 如果已经有寄存器数据，保存之前的寄存器
#             if current_register:
#                 registers[current_register] = current_register_dict
#                 current_register_dict = {}
#
#             # 提取新的寄存器地址
#             match = re.search(r'0x[0-9A-Fa-f]+', first_cell)
#             if match:
#                 current_register = match.group()
#                 current_register_dict = {}  # 建立 current_register_dict 变量
#
#         # 处理寄存器字段
#         if current_register:
#             bit_value = str(row.iloc[0])
#             if not bit_value.startswith("Register") and bit_value != "BIT":  # 去掉 "Register 0xXX" 键值对
#                 bit_info = {
#                     "name": clean_value(row.iloc[1]),  # NAME 列
#                     "default": clean_value(row.iloc[2]),  # POR DEFAULT 列
#                     "widget_name": clean_value(row.iloc[3]),  # Widget Name 列
#                     "widget_type": clean_value(row.iloc[4]),  # Widget Type 列
#                     "options": clean_value(row.iloc[5]),  # Options 列
#                     "description": clean_value(row.iloc[6])  # DESCRIPTION 列
#                 }
#                 current_register_dict[bit_value] = bit_info
#
#     # 保存最后一个寄存器
#     if current_register:
#         registers[current_register] = current_register_dict
#
#     return registers


def json_to_register_dict(json_file):
    """
    从 JSON 文件生成寄存器字典
    :param json_file: JSON 文件路径
    :return: 寄存器字典
    """
    with open(json_file, 'r', encoding='utf-8') as f:
        json_data = json.load(f)

    register_dict = {}
    for address, register in json_data.items():
        bits = register['bits']
        value = ''
        for bit in bits:
            if 'default' in bit:
                value += bit['default']
            else:
                value += '0'  # 如果没有 default 值，填充 0
        register_dict[address] = value

    return register_dict


def save_to_json(registers, output_file):
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(registers, f, indent=2, ensure_ascii=False)


# 使用示例
if __name__ == "__main__":
    try:
        excel_file = "utils/FSJ048xxx_register.xlsx"
        output_file = os.path.join("lib", "register.json")

        registers = excel_to_register_json(excel_file)
        save_to_json(registers, output_file)
        print(f"Successfully converted Excel to JSON. Output saved to {output_file}")

        registerDict = json_to_register_dict(output_file)
        print(registerDict)
    except Exception as e:
        print(f"Error occurred: {str(e)}")
