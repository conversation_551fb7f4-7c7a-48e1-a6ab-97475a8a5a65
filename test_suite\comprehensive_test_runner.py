#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
FSJ04832寄存器配置工具 - 综合测试运行器
统一运行所有测试模块，生成完整的测试报告
"""

import sys
import os
import time
import json
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入测试模块
from test_suite.comprehensive_core_test import run_core_functionality_tests
from test_suite.architecture_component_test import run_architecture_component_tests
from test_suite.ui_interface_test import run_ui_interface_tests
from test_suite.integration_communication_test import run_integration_communication_tests

class ComprehensiveTestRunner:
    """综合测试运行器"""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = None
        self.end_time = None
        self.report_dir = Path("test_suite/reports")
        self.report_dir.mkdir(exist_ok=True)
        
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 FSJ04832寄存器配置工具 - 综合测试开始")
        print("=" * 80)
        
        self.start_time = time.time()
        
        # 定义测试模块
        test_modules = [
            {
                'name': '核心功能测试',
                'description': '测试寄存器管理、PLL控制、时钟输出等核心业务功能',
                'runner': run_core_functionality_tests,
                'category': 'core'
            },
            {
                'name': '架构组件测试', 
                'description': '测试现代化处理器、事件总线、插件系统等架构组件',
                'runner': run_architecture_component_tests,
                'category': 'architecture'
            },
            {
                'name': 'UI界面测试',
                'description': '测试主窗口、工具窗口、控件交互等UI功能',
                'runner': run_ui_interface_tests,
                'category': 'ui'
            },
            {
                'name': '集成通信测试',
                'description': '测试模块间通信、数据流、事件传递等集成功能',
                'runner': run_integration_communication_tests,
                'category': 'integration'
            }
        ]
        
        # 运行每个测试模块
        for i, test_module in enumerate(test_modules, 1):
            print(f"\n📋 [{i}/{len(test_modules)}] {test_module['name']}")
            print(f"📝 {test_module['description']}")
            print("-" * 60)
            
            module_start_time = time.time()
            
            try:
                success = test_module['runner']()
                module_end_time = time.time()
                duration = module_end_time - module_start_time
                
                self.test_results[test_module['category']] = {
                    'name': test_module['name'],
                    'success': success,
                    'duration': duration,
                    'error': None
                }
                
                status = "✅ 通过" if success else "❌ 失败"
                print(f"\n🎯 {test_module['name']} - {status} (耗时: {duration:.2f}秒)")
                
            except Exception as e:
                module_end_time = time.time()
                duration = module_end_time - module_start_time
                
                self.test_results[test_module['category']] = {
                    'name': test_module['name'],
                    'success': False,
                    'duration': duration,
                    'error': str(e)
                }
                
                print(f"\n💥 {test_module['name']} - 异常: {str(e)}")
                
        self.end_time = time.time()
        
        # 生成测试报告
        self.generate_report()
        
        return self.get_overall_success()
        
    def get_overall_success(self):
        """获取总体测试成功状态"""
        if not self.test_results:
            return False
            
        return all(result['success'] for result in self.test_results.values())
        
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 80)
        print("📊 综合测试报告")
        print("=" * 80)
        
        total_duration = self.end_time - self.start_time
        successful_tests = sum(1 for result in self.test_results.values() if result['success'])
        total_tests = len(self.test_results)
        success_rate = (successful_tests / total_tests * 100) if total_tests > 0 else 0
        
        # 控制台报告
        print(f"🕐 测试开始时间: {datetime.fromtimestamp(self.start_time).strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🕐 测试结束时间: {datetime.fromtimestamp(self.end_time).strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏱️  总耗时: {total_duration:.2f}秒")
        print(f"📈 成功率: {success_rate:.1f}% ({successful_tests}/{total_tests})")
        
        print(f"\n📋 详细结果:")
        for category, result in self.test_results.items():
            status = "✅ 通过" if result['success'] else "❌ 失败"
            duration = result['duration']
            print(f"   {result['name']}: {status} ({duration:.2f}秒)")
            if result['error']:
                print(f"      错误: {result['error']}")
                
        # 生成JSON报告
        self.generate_json_report(total_duration, success_rate)
        
        # 生成HTML报告
        self.generate_html_report(total_duration, success_rate)
        
        print(f"\n📄 详细报告已保存到: {self.report_dir}")
        
    def generate_json_report(self, total_duration, success_rate):
        """生成JSON格式报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        report_data = {
            'test_run_info': {
                'timestamp': timestamp,
                'start_time': datetime.fromtimestamp(self.start_time).isoformat(),
                'end_time': datetime.fromtimestamp(self.end_time).isoformat(),
                'total_duration': total_duration,
                'success_rate': success_rate
            },
            'test_results': self.test_results,
            'summary': {
                'total_test_modules': len(self.test_results),
                'successful_modules': sum(1 for r in self.test_results.values() if r['success']),
                'failed_modules': sum(1 for r in self.test_results.values() if not r['success'])
            }
        }
        
        json_file = self.report_dir / f"test_report_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
            
    def generate_html_report(self, total_duration, success_rate):
        """生成HTML格式报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FSJ04832寄存器配置工具 - 测试报告</title>
    <style>
        body {{ font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; }}
        .header {{ background: #f0f8ff; padding: 20px; border-radius: 8px; margin-bottom: 20px; }}
        .summary {{ background: #f9f9f9; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
        .test-result {{ margin: 10px 0; padding: 10px; border-radius: 5px; }}
        .success {{ background: #d4edda; border-left: 4px solid #28a745; }}
        .failure {{ background: #f8d7da; border-left: 4px solid #dc3545; }}
        .error {{ color: #721c24; font-size: 0.9em; margin-top: 5px; }}
        table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
        th, td {{ padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }}
        th {{ background-color: #f2f2f2; }}
        .metric {{ display: inline-block; margin: 10px 20px 10px 0; }}
        .metric-value {{ font-size: 1.5em; font-weight: bold; color: #007bff; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 FSJ04832寄存器配置工具 - 综合测试报告</h1>
        <p>生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>
    </div>
    
    <div class="summary">
        <h2>📊 测试摘要</h2>
        <div class="metric">
            <div>总耗时</div>
            <div class="metric-value">{total_duration:.2f}秒</div>
        </div>
        <div class="metric">
            <div>成功率</div>
            <div class="metric-value">{success_rate:.1f}%</div>
        </div>
        <div class="metric">
            <div>测试模块</div>
            <div class="metric-value">{len(self.test_results)}</div>
        </div>
    </div>
    
    <h2>📋 详细结果</h2>
"""
        
        for category, result in self.test_results.items():
            css_class = "success" if result['success'] else "failure"
            status_icon = "✅" if result['success'] else "❌"
            
            html_content += f"""
    <div class="test-result {css_class}">
        <h3>{status_icon} {result['name']}</h3>
        <p><strong>类别:</strong> {category}</p>
        <p><strong>耗时:</strong> {result['duration']:.2f}秒</p>
        <p><strong>状态:</strong> {'通过' if result['success'] else '失败'}</p>
"""
            
            if result['error']:
                html_content += f'<div class="error"><strong>错误信息:</strong> {result["error"]}</div>'
                
            html_content += "</div>\n"
            
        html_content += """
    <h2>📈 测试统计</h2>
    <table>
        <tr>
            <th>指标</th>
            <th>数值</th>
        </tr>
        <tr>
            <td>测试开始时间</td>
            <td>{}</td>
        </tr>
        <tr>
            <td>测试结束时间</td>
            <td>{}</td>
        </tr>
        <tr>
            <td>总测试模块数</td>
            <td>{}</td>
        </tr>
        <tr>
            <td>成功模块数</td>
            <td>{}</td>
        </tr>
        <tr>
            <td>失败模块数</td>
            <td>{}</td>
        </tr>
    </table>
</body>
</html>
""".format(
            datetime.fromtimestamp(self.start_time).strftime('%Y-%m-%d %H:%M:%S'),
            datetime.fromtimestamp(self.end_time).strftime('%Y-%m-%d %H:%M:%S'),
            len(self.test_results),
            sum(1 for r in self.test_results.values() if r['success']),
            sum(1 for r in self.test_results.values() if not r['success'])
        )
        
        html_file = self.report_dir / f"test_report_{timestamp}.html"
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)

def main():
    """主函数"""
    runner = ComprehensiveTestRunner()
    success = runner.run_all_tests()
    
    if success:
        print("\n🎉 所有测试通过！")
        sys.exit(0)
    else:
        print("\n⚠️  部分测试失败，请查看详细报告")
        sys.exit(1)

if __name__ == "__main__":
    main()
