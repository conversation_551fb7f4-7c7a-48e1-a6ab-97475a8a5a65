#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试现代化时钟输出处理器
验证ModernClkOutputsHandler是否正常工作
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_modern_clk_outputs():
    """测试现代化时钟输出处理器"""
    try:
        print("=" * 60)
        print("测试现代化时钟输出处理器")
        print("=" * 60)
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 导入必要的模块
        from core.services.register.RegisterManager import RegisterManager
        from ui.handlers.ModernClkOutputsHandler import ModernClkOutputsHandler
        import json
        
        print("1. 加载寄存器配置...")
        # 加载寄存器配置
        config_path = os.path.join(os.path.dirname(__file__), 'lib', 'register.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        print(f"   ✓ 加载了 {len(registers_config)} 个寄存器配置")
        
        print("2. 创建RegisterManager...")
        register_manager = RegisterManager(registers_config)
        print(f"   ✓ 创建了RegisterManager，包含 {len(register_manager.register_objects)} 个寄存器对象")
        
        print("3. 创建现代化时钟输出处理器...")
        try:
            modern_handler = ModernClkOutputsHandler(None, register_manager)
            print("   ✓ 成功创建ModernClkOutputsHandler")
            
            # 等待初始化完成
            import time
            time.sleep(0.2)
            
            # 检查控件映射
            print(f"   ✓ 构建了 {len(modern_handler.widget_register_map)} 个控件映射")
            
            # 显示部分控件映射
            clk_widgets = [name for name in modern_handler.widget_register_map.keys() if "CLK" in name]
            print(f"   CLK相关控件: {clk_widgets[:5]}...")  # 显示前5个
            
        except Exception as e:
            print(f"   ❌ 创建ModernClkOutputsHandler失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
        print("4. 测试时钟输出状态获取...")
        try:
            initial_status = modern_handler.get_current_status()
            print(f"   初始状态: VCO频率={initial_status.get('vco_frequency', 'N/A')}MHz")
            
            # 验证状态字段
            expected_fields = ["vco_frequency", "srcmux_states", "output_frequencies", "divider_values"]
            for field in expected_fields:
                if field in initial_status:
                    if field == "output_frequencies":
                        active_outputs = sum(1 for freq in initial_status[field].values() if freq > 0)
                        print(f"   ✓ {field}: {active_outputs}/14 个输出有效")
                    elif field == "srcmux_states":
                        srcmux_count = sum(1 for state in initial_status[field].values() if state)
                        print(f"   ✓ {field}: {srcmux_count}/14 个输出使用SRCMUX")
                    else:
                        print(f"   ✓ {field}: {initial_status[field]}")
                else:
                    print(f"   ❌ 缺少状态字段: {field}")
                    
        except Exception as e:
            print(f"   ❌ 状态获取出错: {str(e)}")
        
        print("5. 测试频率计算功能...")
        try:
            # 设置VCO频率
            if hasattr(modern_handler.ui, "lineEditFvco"):
                modern_handler.ui.lineEditFvco.setText("2949.12")
                print("   设置VCO频率: 2949.12 MHz")
            
            # 触发频率计算
            modern_handler.calculate_output_frequencies()
            print("   ✓ 频率计算执行完成")
            
            # 检查计算结果
            status_after_calc = modern_handler.get_current_status()
            output_freqs = status_after_calc.get("output_frequencies", {})
            
            # 检查前几个输出的频率
            for i in range(min(4, 14)):
                output_key = f"output_{i}"
                if output_key in output_freqs:
                    freq = output_freqs[output_key]
                    print(f"     输出{i}: {freq:.2f} MHz")
                    
                    # 验证频率计算是否合理（应该等于VCO频率/分频比）
                    if freq > 0:
                        expected_freq = 2949.12 / 1  # 假设分频比为1
                        if abs(freq - expected_freq) < 1.0:  # 允许1MHz误差
                            print(f"       ✓ 频率计算正确")
                        else:
                            print(f"       ⚠️ 频率可能不正确 (期望约: {expected_freq:.2f})")
                    
        except Exception as e:
            print(f"   ❌ 频率计算测试出错: {str(e)}")
        
        print("6. 测试时钟输出预设功能...")
        try:
            # 测试默认预设
            print("   应用默认预设...")
            modern_handler.set_output_preset("default")
            
            # 检查VCO频率
            if hasattr(modern_handler.ui, "lineEditFvco"):
                vco_freq = modern_handler.ui.lineEditFvco.text()
                print(f"   默认预设后VCO频率: {vco_freq} MHz")
                if vco_freq == "2949.12":
                    print("   ✓ 默认预设VCO频率正确")
                else:
                    print("   ❌ 默认预设VCO频率不正确")
            
            # 测试低频预设
            print("   应用低频预设...")
            modern_handler.set_output_preset("low_frequency")
            
            # 检查分频器值
            divider_status = modern_handler.get_current_status().get("divider_values", {})
            print(f"   低频预设后分频器状态: {divider_status}")
            
            # 验证分频器值是否为4
            expected_dividers = ["DCLK0_1DIV", "DCLK2_3DIV"]
            for divider_name in expected_dividers:
                if divider_name in divider_status:
                    if divider_status[divider_name] == 4:
                        print(f"   ✓ {divider_name} 分频器值正确: 4")
                    else:
                        print(f"   ❌ {divider_name} 分频器值不正确: {divider_status[divider_name]}")
                        
        except Exception as e:
            print(f"   ❌ 时钟输出预设测试出错: {str(e)}")
        
        print("7. 测试SRCMUX功能...")
        try:
            # 测试全部SRCMUX预设
            print("   应用全部SRCMUX预设...")
            modern_handler.set_output_preset("all_sysref")
            
            # 检查SRCMUX状态
            srcmux_status = modern_handler.get_current_status().get("srcmux_states", {})
            srcmux_count = sum(1 for state in srcmux_status.values() if state)
            
            print(f"   全部SRCMUX预设后: {srcmux_count}/14 个输出使用SRCMUX")
            
            if srcmux_count == 14:
                print("   ✓ 全部SRCMUX预设应用成功")
            else:
                print(f"   ❌ 全部SRCMUX预设应用失败 (期望14个，实际{srcmux_count}个)")
            
            # 测试单个SRCMUX切换
            print("   测试单个SRCMUX切换...")
            modern_handler.update_srcmux_output(0, 2)  # Qt.Checked = 2
            
            if modern_handler.srcmux_states.get(0, False):
                print("   ✓ 单个SRCMUX切换成功")
            else:
                print("   ❌ 单个SRCMUX切换失败")
                
        except Exception as e:
            print(f"   ❌ SRCMUX功能测试出错: {str(e)}")
        
        print("8. 测试输出统计功能...")
        try:
            output_count = modern_handler.get_output_count()
            print(f"   输出统计: {output_count}")
            
            # 验证统计数据
            expected_fields = ["total_outputs", "active_outputs", "srcmux_outputs", "normal_outputs"]
            for field in expected_fields:
                if field in output_count:
                    print(f"   ✓ {field}: {output_count[field]}")
                else:
                    print(f"   ❌ 缺少统计字段: {field}")
            
            # 验证总数
            if output_count.get("total_outputs") == 14:
                print("   ✓ 总输出数正确")
            else:
                print("   ❌ 总输出数不正确")
                
        except Exception as e:
            print(f"   ❌ 输出统计测试出错: {str(e)}")
        
        print("9. 测试控件值变化...")
        try:
            # 模拟分频器变化
            if "DCLK0_1DIV" in modern_handler.widget_register_map:
                print("   模拟DCLK0_1DIV控件变化...")
                modern_handler._on_widget_changed("DCLK0_1DIV", 2)
                print("   ✓ DCLK0_1DIV控件值变化处理成功")
            else:
                print("   DCLK0_1DIV控件未映射，跳过测试")
            
            # 模拟VCO频率变化
            if hasattr(modern_handler.ui, "lineEditFvco"):
                print("   模拟VCO频率变化...")
                modern_handler.ui.lineEditFvco.setText("3000.0")
                modern_handler.calculate_output_frequencies()
                print("   ✓ VCO频率变化处理成功")
                
        except Exception as e:
            print(f"   ❌ 控件值变化测试出错: {str(e)}")
        
        print("10. 测试最终状态...")
        try:
            final_status = modern_handler.get_current_status()
            print(f"   最终VCO频率: {final_status.get('vco_frequency', 'N/A')} MHz")
            
            # 验证状态一致性
            if final_status.get("vco_frequency") == 3000.0:
                print("   ✓ VCO频率状态一致")
            else:
                print("   ❌ VCO频率状态不一致")
            
            final_output_count = modern_handler.get_output_count()
            print(f"   最终输出统计: {final_output_count}")
            
        except Exception as e:
            print(f"   ❌ 最终状态测试出错: {str(e)}")
        
        print("\n" + "=" * 60)
        print("🎉 现代化时钟输出处理器测试完成！")
        print("=" * 60)
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_modern_clk_outputs()
    sys.exit(0 if success else 1)
