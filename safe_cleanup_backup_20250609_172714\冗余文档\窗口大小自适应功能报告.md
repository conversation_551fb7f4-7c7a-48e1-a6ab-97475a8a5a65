# 窗口大小自适应功能报告

## 🎯 问题描述

用户反馈分离后的窗口宽度太大，希望能够自适应调整窗口大小，提供更好的用户体验。

### 具体问题
1. **窗口过宽**：分离后的窗口可能继承了标签页容器的宽度，比实际需要的宽度大很多
2. **缺少自适应**：窗口大小固定，不根据内容和屏幕大小调整
3. **用户体验差**：过大的窗口占用过多屏幕空间，影响多窗口操作

## 🛠️ 解决方案

### 1. 智能窗口大小调整系统

#### 1.1 核心调整方法
```python
def _smart_resize_window(self, window, plugin_name: str):
    """智能调整窗口大小，自适应内容"""
    try:
        # 获取屏幕信息
        screen = QApplication.primaryScreen()
        screen_geometry = screen.availableGeometry()
        screen_width = screen_geometry.width()
        screen_height = screen_geometry.height()
        
        # 尝试获取内容的实际大小
        optimal_size = self._calculate_optimal_window_size(window, plugin_name)
        optimal_width, optimal_height = optimal_size
        
        # 应用屏幕大小限制
        max_width = int(screen_width * 0.8)  # 最大不超过屏幕宽度的80%
        max_height = int(screen_height * 0.8)  # 最大不超过屏幕高度的80%
        
        # 获取最小尺寸限制
        min_size = self._get_plugin_minimum_size(plugin_name)
        min_width, min_height = min_size
        
        # 计算最终尺寸
        final_width = max(min_width, min(optimal_width, max_width))
        final_height = max(min_height, min(optimal_height, max_height))
        
        # 如果窗口太宽，尝试调整为更合理的比例
        if final_width > screen_width * 0.6:
            reasonable_width = min(int(screen_width * 0.5), max(min_width, int(final_height * 1.2)))
            if reasonable_width < final_width:
                final_width = reasonable_width
        
        # 应用新尺寸
        window.resize(final_width, final_height)
```

#### 1.2 最优大小计算
```python
def _calculate_optimal_window_size(self, window, plugin_name: str):
    """计算窗口的最优大小"""
    try:
        # 方法1: 尝试从窗口内容计算
        content_size = self._get_content_size_hint(window)
        if content_size:
            return content_size
        
        # 方法2: 基于插件类型的预设大小
        preset_size = self._get_plugin_optimal_size(plugin_name)
        if preset_size:
            return preset_size
        
        # 方法3: 基于当前窗口大小的智能调整
        current_size = window.size()
        return self._adjust_current_size(current_size, plugin_name)
```

#### 1.3 内容大小检测
```python
def _get_content_size_hint(self, window):
    """获取窗口内容的大小提示"""
    try:
        # 检查是否有滚动区域
        if hasattr(window, 'scroll_area') and window.scroll_area:
            scroll_area = window.scroll_area
            if scroll_area.widget():
                content_widget = scroll_area.widget()
                size_hint = content_widget.sizeHint()
                if size_hint.isValid():
                    # 添加滚动条和边距的空间
                    width = size_hint.width() + 50
                    height = size_hint.height() + 50
                    return (width, height)
        
        # 检查是否有内容控件
        if hasattr(window, 'content_widget') and window.content_widget:
            content_widget = window.content_widget
            size_hint = content_widget.sizeHint()
            if size_hint.isValid():
                width = size_hint.width() + 20
                height = size_hint.height() + 20
                return (width, height)
```

### 2. 插件特定的最优大小配置

#### 2.1 最优大小映射
```python
def _get_plugin_optimal_size(self, plugin_name: str):
    """获取插件的最优大小（比最小大小更合理的显示大小）"""
    optimal_size_mapping = {
        'clkin_control_plugin': (950, 750),      # 时钟输入控制
        'pll_control_plugin': (1200, 900),       # PLL控制 - 减小宽度
        'sync_sysref_plugin': (950, 750),        # 同步系统参考
        'clk_output_plugin': (1100, 1000),       # 时钟输出 - 减小宽度
        'set_modes_plugin': (950, 750),          # 模式设置
        '示例工具': (650, 500),                   # 示例工具
        'performance_monitor_plugin': (800, 600), # 性能监控
        'selective_register_plugin': (900, 700)   # 选择性寄存器
    }
    
    return optimal_size_mapping.get(plugin_name, None)
```

### 3. 分离窗口专用优化

#### 3.1 分离窗口大小优化
```python
def _optimize_undocked_window_size(self, window, plugin_name: str):
    """优化分离窗口的大小（专门针对从标签页分离的窗口）"""
    try:
        # 获取当前窗口大小
        current_size = window.size()
        current_width = current_size.width()
        current_height = current_size.height()
        
        # 获取屏幕信息
        screen = QApplication.primaryScreen()
        screen_geometry = screen.availableGeometry()
        screen_width = screen_geometry.width()
        screen_height = screen_geometry.height()
        
        # 检查窗口是否过宽（常见问题）
        is_too_wide = current_width > screen_width * 0.6
        is_too_tall = current_height > screen_height * 0.8
        
        if is_too_wide or is_too_tall:
            # 获取最优大小
            optimal_size = self._get_plugin_optimal_size(plugin_name)
            if optimal_size:
                optimal_width, optimal_height = optimal_size
            else:
                # 基于当前大小进行智能缩放
                if is_too_wide:
                    optimal_width = min(current_width, int(screen_width * 0.5))
                else:
                    optimal_width = current_width
                
                if is_too_tall:
                    optimal_height = min(current_height, int(screen_height * 0.7))
                else:
                    optimal_height = current_height
            
            # 应用新大小
            window.resize(optimal_width, optimal_height)
```

#### 3.2 微调和位置优化
```python
def _fine_tune_undocked_window(self, window, plugin_name: str):
    """微调分离窗口（确保内容完全可见且布局合理）"""
    try:
        # 检查窗口是否有滚动区域
        if hasattr(window, 'scroll_area') and window.scroll_area:
            scroll_area = window.scroll_area
            if scroll_area.widget():
                content_widget = scroll_area.widget()
                
                # 获取内容的实际大小需求
                content_size_hint = content_widget.sizeHint()
                if content_size_hint.isValid():
                    content_width = content_size_hint.width()
                    content_height = content_size_hint.height()
                    
                    # 获取当前视口大小
                    viewport_size = scroll_area.viewport().size()
                    viewport_width = viewport_size.width()
                    viewport_height = viewport_size.height()
                    
                    # 检查是否需要调整以避免不必要的滚动条
                    window_size = window.size()
                    new_width = window_size.width()
                    new_height = window_size.height()
                    
                    # 如果内容宽度比视口稍大，尝试调整窗口宽度
                    if content_width > viewport_width and content_width - viewport_width < 100:
                        additional_width = content_width - viewport_width + 30
                        new_width = window_size.width() + additional_width
                    
                    # 如果内容高度比视口稍大，尝试调整窗口高度
                    if content_height > viewport_height and content_height - viewport_height < 100:
                        additional_height = content_height - viewport_height + 30
                        new_height = window_size.height() + additional_height
                    
                    # 应用屏幕大小限制并调整
                    if new_width != window_size.width() or new_height != window_size.height():
                        window.resize(new_width, new_height)
```

### 4. 集成到分离流程

#### 4.1 在分离窗口时调用优化
```python
def undock_window_from_tab(self, plugin_name: str):
    """将窗口从标签页中分离为悬浮窗口"""
    try:
        # ... 分离逻辑 ...
        
        # 如果找到了窗口，重新配置为悬浮窗口
        if window:
            # 确保窗口在插件窗口字典中
            self.plugin_windows[plugin_name] = window
            
            # 重新配置为悬浮窗口
            self._configure_plugin_window(window, plugin_name)
            
            # ✅ 特别针对分离窗口进行智能大小调整
            self._optimize_undocked_window_size(window, plugin_name)
            
            window.show()
```

#### 4.2 在常规窗口配置中应用
```python
def _configure_plugin_window(self, window, plugin_name: str):
    """配置插件窗口以改善用户体验"""
    try:
        # ... 其他配置 ...
        
        # ✅ 智能调整窗口大小
        self._smart_resize_window(window, plugin_name)
        
        # ... 其他配置 ...
```

## 🧪 测试工具

创建了专门的测试工具 `test_window_resize.py`：

### 功能特点
1. **插件服务连接**：自动查找并连接到主程序的插件服务
2. **多插件测试**：支持测试不同类型的插件窗口
3. **分离测试**：测试窗口分离后的大小调整
4. **大小检查**：检查窗口大小是否合理
5. **屏幕占比分析**：分析窗口占屏幕的比例

### 使用方法
```bash
python test_window_resize.py
```

### 测试步骤
1. 点击"查找插件服务"确保连接成功
2. 点击不同的"测试XXX"按钮打开各种插件窗口
3. 点击"分离所有窗口"测试自适应调整
4. 点击"检查窗口大小"查看调整结果
5. 观察窗口大小和位置是否合理

## ✅ 预期效果

### 1. 窗口大小优化
- ✅ 分离后窗口宽度合理，不会过宽
- ✅ 窗口高度适中，不会过高
- ✅ 基于内容自动调整大小
- ✅ 考虑屏幕大小限制

### 2. 用户体验改善
- ✅ 窗口大小更符合使用习惯
- ✅ 多窗口操作更方便
- ✅ 屏幕空间利用更合理
- ✅ 减少手动调整窗口大小的需要

### 3. 智能化特性
- ✅ 不同插件应用不同的最优大小
- ✅ 自动检测和避免不必要的滚动条
- ✅ 智能位置调整，避免超出屏幕
- ✅ 延迟微调确保布局完成

### 4. 兼容性保证
- ✅ 保持原有功能不变
- ✅ 向后兼容现有插件
- ✅ 优雅降级处理异常情况
- ✅ 详细的日志记录便于调试

## 🎯 技术要点

### 1. 多层次大小计算
- **内容检测**：从窗口内容获取实际大小需求
- **插件配置**：基于插件类型应用预设的最优大小
- **智能调整**：基于当前大小和屏幕比例进行调整
- **屏幕限制**：确保窗口不超出屏幕边界

### 2. 时机控制
- **即时调整**：分离时立即应用基本调整
- **延迟微调**：等待布局完成后进行精细调整
- **分阶段优化**：先调整大小，再调整位置

### 3. 用户体验优化
- **渐进式调整**：避免突然的大幅度变化
- **智能预测**：预测用户可能需要的窗口大小
- **上下文感知**：考虑当前屏幕状态和其他窗口

### 4. 错误处理
- **优雅降级**：计算失败时使用合理的默认值
- **异常恢复**：出错时回退到基本调整方案
- **详细日志**：记录调整过程便于问题诊断

## 🚀 后续优化建议

### 1. 用户配置
- 允许用户自定义各插件的首选大小
- 提供窗口大小预设方案
- 支持保存和恢复窗口布局

### 2. 智能学习
- 记录用户的窗口大小调整习惯
- 基于使用频率优化默认大小
- 自适应不同屏幕分辨率

### 3. 高级功能
- 支持多显示器环境
- 窗口大小动画过渡
- 智能窗口排列和层叠

## 📝 结论

通过实现智能窗口大小自适应功能，显著改善了分离窗口的用户体验：

1. **解决核心问题**：彻底解决了分离后窗口过宽的问题
2. **智能化调整**：基于内容、插件类型和屏幕大小智能调整
3. **用户友好**：减少用户手动调整窗口的需要
4. **系统化方案**：提供了完整的窗口大小管理系统

现在分离窗口后，窗口大小会自动调整到最合适的尺寸，提供更好的用户体验和更高效的工作环境。
