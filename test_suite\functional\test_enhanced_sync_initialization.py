#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试增强的同步系统参考和时钟输出初始化
验证所有新增的初始化参数是否正确设置
"""

import sys
import os
import json
from PyQt5.QtWidgets import QApplication

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ui.handlers.ModernSyncSysRefHandler import ModernSyncSysRefHandler
from ui.handlers.ModernClkOutputsHandler import ModernClkOutputsHandler
from core.services.register.RegisterManager import RegisterManager
from utils.Log import get_module_logger

logger = get_module_logger(__name__)

def test_enhanced_sync_initialization():
    """测试增强的同步系统参考初始化"""
    print("=" * 80)
    print("测试增强的同步系统参考初始化")
    print("=" * 80)
    
    try:
        # 创建QApplication
        app = QApplication(sys.argv)
        
        print("1. 加载寄存器配置...")
        config_path = os.path.join('lib', 'register.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        register_manager = RegisterManager(registers_config)
        print("✓ 寄存器配置加载成功")
        
        print("\n2. 创建现代化同步系统参考处理器...")
        sync_handler = ModernSyncSysRefHandler(None, register_manager)
        print("✓ 现代化同步系统参考处理器创建成功")
        
        print("\n3. 验证基本控件初始化...")
        
        # 验证VCO频率
        if hasattr(sync_handler.ui, "InternalVCOFreq"):
            vco_freq = sync_handler.ui.InternalVCOFreq.text()
            print(f"   VCO频率: {vco_freq} MHz")
            if vco_freq == "2949.12":
                print("   ✓ VCO频率初始化正确")
            else:
                print(f"   ❌ VCO频率初始化错误，期望2949.12，实际{vco_freq}")
                return False
        
        # 验证SYSREF分频器
        if hasattr(sync_handler.ui, "spinBoxSysrefDIV"):
            div_value = sync_handler.ui.spinBoxSysrefDIV.value()
            min_val = sync_handler.ui.spinBoxSysrefDIV.minimum()
            max_val = sync_handler.ui.spinBoxSysrefDIV.maximum()
            print(f"   SYSREF分频器: 值={div_value}, 范围=({min_val}-{max_val})")
            if div_value == 3072 and min_val == 0 and max_val == 8191:
                print("   ✓ SYSREF分频器初始化正确")
            else:
                print("   ❌ SYSREF分频器初始化错误")
                return False
        
        # 验证SYSREF延迟
        if hasattr(sync_handler.ui, "spinBoxSysrefDDLY"):
            ddly_value = sync_handler.ui.spinBoxSysrefDDLY.value()
            min_val = sync_handler.ui.spinBoxSysrefDDLY.minimum()
            max_val = sync_handler.ui.spinBoxSysrefDDLY.maximum()
            print(f"   SYSREF延迟: 值={ddly_value}, 范围=({min_val}-{max_val})")
            if ddly_value == 8 and min_val == 2 and max_val == 8191:
                print("   ✓ SYSREF延迟初始化正确")
            else:
                print("   ❌ SYSREF延迟初始化错误")
                return False
        
        print("\n4. 验证ComboBox控件初始化...")
        
        # 验证SYNC模式ComboBox
        if hasattr(sync_handler.ui, "comboSyncMode"):
            current_index = sync_handler.ui.comboSyncMode.currentIndex()
            item_count = sync_handler.ui.comboSyncMode.count()
            current_text = sync_handler.ui.comboSyncMode.currentText()
            print(f"   SYNC模式: 索引={current_index}, 项目数={item_count}, 当前文本='{current_text}'")
            if current_index == 1 and item_count == 4:
                print("   ✓ SYNC模式ComboBox初始化正确")
            else:
                print("   ❌ SYNC模式ComboBox初始化错误")
                return False
        
        # 验证SYSREF脉冲计数ComboBox
        if hasattr(sync_handler.ui, "comboSysrefPulseCnt"):
            current_index = sync_handler.ui.comboSysrefPulseCnt.currentIndex()
            item_count = sync_handler.ui.comboSysrefPulseCnt.count()
            print(f"   SYSREF脉冲计数: 索引={current_index}, 项目数={item_count}")
            if current_index == 3 and item_count == 4:
                print("   ✓ SYSREF脉冲计数ComboBox初始化正确")
            else:
                print("   ❌ SYSREF脉冲计数ComboBox初始化错误")
                return False
        
        # 验证SYSREF MUX ComboBox
        if hasattr(sync_handler.ui, "comboSysrefMux"):
            current_index = sync_handler.ui.comboSysrefMux.currentIndex()
            item_count = sync_handler.ui.comboSysrefMux.count()
            print(f"   SYSREF MUX: 索引={current_index}, 项目数={item_count}")
            if current_index == 0 and item_count == 4:
                print("   ✓ SYSREF MUX ComboBox初始化正确")
            else:
                print("   ❌ SYSREF MUX ComboBox初始化错误")
                return False
        
        print("\n5. 验证频率显示控件...")
        
        frequency_controls = ["SyncSysrefFreq1", "SyncSysrefFreq2"]
        for control_name in frequency_controls:
            if hasattr(sync_handler.ui, control_name):
                control = getattr(sync_handler.ui, control_name)
                freq_text = control.text()
                is_readonly = control.isReadOnly()
                print(f"   {control_name}: 频率='{freq_text}', 只读={is_readonly}")
                if freq_text == "0.96" and is_readonly:
                    print(f"   ✓ {control_name}初始化正确")
                else:
                    print(f"   ❌ {control_name}初始化错误")
                    return False
        
        print("\n6. 验证电源管理控件...")
        
        # 验证SYNC_EN
        if hasattr(sync_handler.ui, "SyncEn"):
            sync_en_state = sync_handler.ui.SyncEn.isChecked()
            print(f"   SYNC_EN: {sync_en_state}")
            if sync_en_state:
                print("   ✓ SYNC_EN初始化正确（启用）")
            else:
                print("   ❌ SYNC_EN初始化错误")
                return False
        
        # 验证电源控制位
        power_controls = {
            "SysrefGBLPD": True,
            "sysrefPD": True,
            "sysrefDDLYPD": True,
            "SysrefPlsrPd": True
        }
        
        for control_name, expected_state in power_controls.items():
            if hasattr(sync_handler.ui, control_name):
                control = getattr(sync_handler.ui, control_name)
                actual_state = control.isChecked()
                print(f"   {control_name}: {actual_state} (期望: {expected_state})")
                if actual_state == expected_state:
                    print(f"   ✓ {control_name}初始化正确")
                else:
                    print(f"   ❌ {control_name}初始化错误")
                    return False
        
        print("\n7. 验证频率计算...")
        
        # 手动触发频率计算
        sync_handler.calculate_output_frequencies()
        
        # 检查计算结果
        expected_freq = 2949.12 / 3072  # ≈ 0.96
        print(f"   期望频率: {expected_freq:.2f} MHz")
        
        # 验证频率显示是否更新
        for control_name in frequency_controls:
            if hasattr(sync_handler.ui, control_name):
                control = getattr(sync_handler.ui, control_name)
                freq_text = control.text()
                try:
                    freq_value = float(freq_text)
                    if abs(freq_value - expected_freq) < 0.01:  # 允许小的误差
                        print(f"   ✓ {control_name}频率计算正确: {freq_text} MHz")
                    else:
                        print(f"   ❌ {control_name}频率计算错误: {freq_text} MHz")
                        return False
                except ValueError:
                    print(f"   ❌ {control_name}频率格式错误: {freq_text}")
                    return False
        
        print("\n8. 验证DDLYd步进计数器...")
        
        if hasattr(sync_handler.ui, "DDLYdStepCNT_1"):
            ddly_step = sync_handler.ui.DDLYdStepCNT_1
            value = ddly_step.value()
            min_val = ddly_step.minimum()
            max_val = ddly_step.maximum()
            print(f"   DDLYd步进计数器: 值={value}, 范围=({min_val}-{max_val})")
            if value == 0 and min_val == 0 and max_val == 255:
                print("   ✓ DDLYd步进计数器初始化正确")
            else:
                print("   ❌ DDLYd步进计数器初始化错误")
                return False
        
        print("\n" + "=" * 80)
        print("🎉 所有增强初始化测试通过！")
        print("=" * 80)
        
        print("\n📋 初始化总结:")
        print("   - ✅ VCO频率: 2949.12 MHz")
        print("   - ✅ SYSREF分频器: 3072 (范围: 0-8191)")
        print("   - ✅ SYSREF延迟: 8 (范围: 2-8191)")
        print("   - ✅ DDLYd步进计数器: 0 (范围: 0-255)")
        print("   - ✅ ComboBox控件: 正确初始化")
        print("   - ✅ 频率显示: 0.96 MHz (只读)")
        print("   - ✅ 电源管理: 按默认值设置")
        print("   - ✅ 频率计算: 正常工作")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_clk_outputs_initialization():
    """测试增强的时钟输出初始化"""
    print("\n" + "=" * 80)
    print("测试增强的时钟输出初始化")
    print("=" * 80)

    try:
        # 创建QApplication
        app = QApplication(sys.argv)

        print("1. 加载寄存器配置...")
        config_path = os.path.join('lib', 'register.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)

        register_manager = RegisterManager(registers_config)
        print("✓ 寄存器配置加载成功")

        print("\n2. 创建现代化时钟输出处理器...")
        clk_handler = ModernClkOutputsHandler(None, register_manager)
        print("✓ 现代化时钟输出处理器创建成功")

        print("\n3. 验证分频器控件初始化...")

        # 验证DCLK分频器（根据register.json的实际默认值）
        dclk_expected_values = {
            "DCLK0_1DIV": 2,    # 0000000010 = 2
            "DCLK2_3DIV": 4,    # 0000000100 = 4
            "DCLK4_5DIV": 8     # 0000001000 = 8
        }

        for div_name, expected_value in dclk_expected_values.items():
            if hasattr(clk_handler.ui, div_name):
                widget = getattr(clk_handler.ui, div_name)
                value = widget.value()
                min_val = widget.minimum()
                max_val = widget.maximum()
                print(f"   {div_name}: 值={value}, 范围=({min_val}-{max_val}), 期望={expected_value}")
                if value == expected_value and min_val == 1 and max_val == 1023:
                    print(f"   ✓ {div_name}初始化正确")
                else:
                    print(f"   ❌ {div_name}初始化错误")
                    return False

        # 验证DCLK延迟
        dclk_delays = ["DCLK0_1DDLY", "DCLK2_3DDLY", "DCLK4_5DDLY"]
        for delay_name in dclk_delays:
            if hasattr(clk_handler.ui, delay_name):
                widget = getattr(clk_handler.ui, delay_name)
                value = widget.value()
                min_val = widget.minimum()
                max_val = widget.maximum()
                print(f"   {delay_name}: 值={value}, 范围=({min_val}-{max_val})")
                if value == 10 and min_val == 2 and max_val == 1023:
                    print(f"   ✓ {delay_name}初始化正确")
                else:
                    print(f"   ❌ {delay_name}初始化错误")
                    return False

        print("\n4. 验证电源控制状态...")

        # 验证DCLK电源控制
        dclk_power_controls = ["DCLK0_1PD", "DCLK2_3PD", "DCLK4_5PD"]
        for control_name in dclk_power_controls:
            if hasattr(clk_handler.ui, control_name):
                control = getattr(clk_handler.ui, control_name)
                is_checked = control.isChecked()
                print(f"   {control_name}: {is_checked} (期望: False)")
                if not is_checked:
                    print(f"   ✓ {control_name}初始化正确（启用状态）")
                else:
                    print(f"   ❌ {control_name}初始化错误")
                    return False

        # 验证DCLK延迟电源控制（根据register.json默认值为0，即启用）
        dclk_delay_power_controls = ["DCLK0_1DDLYPD", "DCLK2_3DDLYPD", "DCLK4_5DDLYPD"]
        for control_name in dclk_delay_power_controls:
            if hasattr(clk_handler.ui, control_name):
                control = getattr(clk_handler.ui, control_name)
                is_checked = control.isChecked()
                print(f"   {control_name}: {is_checked} (期望: False)")
                if not is_checked:
                    print(f"   ✓ {control_name}初始化正确（启用状态）")
                else:
                    print(f"   ❌ {control_name}初始化错误")
                    return False

        print("\n5. 验证DDLYd使能控件...")

        ddlyd_enable_controls = ["DDLYd0EN", "DDLYd2EN", "DDLYd4EN"]
        for control_name in ddlyd_enable_controls:
            if hasattr(clk_handler.ui, control_name):
                control = getattr(clk_handler.ui, control_name)
                is_checked = control.isChecked()
                print(f"   {control_name}: {is_checked} (期望: False)")
                if not is_checked:
                    print(f"   ✓ {control_name}初始化正确（禁用状态）")
                else:
                    print(f"   ❌ {control_name}初始化错误")
                    return False

        print("\n6. 验证SCLK控件...")

        # 验证SCLK电源控制（根据register.json默认值为1，即关闭）
        sclk_power_controls = ["SCLK0_1PD", "SCLK2_3PD", "SCLK4_5PD"]
        for control_name in sclk_power_controls:
            if hasattr(clk_handler.ui, control_name):
                control = getattr(clk_handler.ui, control_name)
                is_checked = control.isChecked()
                print(f"   {control_name}: {is_checked} (期望: True)")
                if is_checked:
                    print(f"   ✓ {control_name}初始化正确（关闭状态）")
                else:
                    print(f"   ❌ {control_name}初始化错误")
                    return False

        # 验证SCLK延迟ComboBox
        sclk_delay_controls = ["SCLK0_1DDLY", "SCLK2_3DDLY", "SCLK4_5DDLY"]
        for control_name in sclk_delay_controls:
            if hasattr(clk_handler.ui, control_name):
                control = getattr(clk_handler.ui, control_name)
                current_index = control.currentIndex()
                item_count = control.count()
                current_text = control.currentText()
                print(f"   {control_name}: 索引={current_index}, 项目数={item_count}, 当前文本='{current_text}'")
                if current_index == 1 and item_count == 4 and "0.5ns" in current_text:
                    print(f"   ✓ {control_name}初始化正确（0.5ns延迟）")
                else:
                    print(f"   ❌ {control_name}初始化错误")
                    return False

        print("\n7. 验证SYNC禁用控件...")

        sync_dis_controls = ["SYNCDIS0", "SYNCDIS2", "SYNCDIS4"]
        for control_name in sync_dis_controls:
            if hasattr(clk_handler.ui, control_name):
                control = getattr(clk_handler.ui, control_name)
                is_checked = control.isChecked()
                print(f"   {control_name}: {is_checked} (期望: False)")
                if not is_checked:
                    print(f"   ✓ {control_name}初始化正确（不禁用同步）")
                else:
                    print(f"   ❌ {control_name}初始化错误")
                    return False

        print("\n" + "=" * 80)
        print("🎉 所有时钟输出增强初始化测试通过！")
        print("=" * 80)

        print("\n📋 时钟输出初始化总结:")
        print("   - ✅ DCLK分频器: 2 (范围: 1-1023)")
        print("   - ✅ DCLK延迟: 10 (范围: 2-1023)")
        print("   - ✅ DCLK电源控制: 启用状态")
        print("   - ✅ DCLK延迟电源控制: 关闭状态")
        print("   - ✅ DDLYd使能: 禁用状态")
        print("   - ✅ SCLK电源控制: 启用状态")
        print("   - ✅ SCLK延迟: 0.0ns")
        print("   - ✅ SYNC禁用控件: 不禁用同步")

        return True

    except Exception as e:
        print(f"❌ 时钟输出测试过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # 测试同步系统参考初始化
    sync_success = test_enhanced_sync_initialization()

    # 测试时钟输出初始化
    clk_success = test_enhanced_clk_outputs_initialization()

    if sync_success and clk_success:
        print("\n🎉 所有增强初始化测试完全成功！")
        print("✅ 同步系统参考初始化: 通过")
        print("✅ 时钟输出初始化: 通过")
        sys.exit(0)
    else:
        print("\n❌ 部分增强初始化测试失败")
        print(f"❌ 同步系统参考初始化: {'通过' if sync_success else '失败'}")
        print(f"❌ 时钟输出初始化: {'通过' if clk_success else '失败'}")
        sys.exit(1)
