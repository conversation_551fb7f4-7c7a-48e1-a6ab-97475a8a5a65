#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试分频器方法
深入调试_get_divider_value_for_output方法
"""

import sys
import os
import json
from PyQt5.QtWidgets import QApplication

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ui.handlers.ModernClkOutputsHandler import ModernClkOutputsHandler
from core.services.register.RegisterManager import RegisterManager
from utils.Log import logger

# 猴子补丁_get_divider_value_for_output方法来追踪调用
original_get_divider_value = None

def traced_get_divider_value(self, output_num):
    """追踪_get_divider_value_for_output调用的包装方法"""
    print(f"\n🔍 _get_divider_value_for_output调用:")
    print(f"   输出编号: {output_num}")
    
    try:
        # 确定分频器名称
        divider_name = self._get_divider_name_for_output(output_num)
        print(f"   分频器名称: {divider_name}")

        # 从RegisterManager获取分频值
        if self.register_manager and divider_name in self.widget_register_map:
            widget_info = self.widget_register_map[divider_name]
            reg_addr = widget_info["register_addr"]
            bit_def = widget_info["bit_def"]
            bit_name = bit_def.get("name", "")
            # 处理寄存器地址格式
            if isinstance(reg_addr, str):
                print(f"   寄存器地址: {reg_addr}")
            else:
                print(f"   寄存器地址: 0x{reg_addr:02X}")
            print(f"   位名称: {bit_name}")

            if bit_name:
                bit_value = self.register_manager.get_bit_field_value(reg_addr, bit_name)
                result = max(1, bit_value)
                print(f"   寄存器位值: {bit_value}")
                print(f"   返回值(寄存器): {result}")
                return result

        # 如果无法从寄存器获取，尝试从UI控件获取
        if hasattr(self.ui, divider_name):
            widget = getattr(self.ui, divider_name)
            print(f"   UI控件: {widget}")
            print(f"   UI控件类型: {type(widget)}")
            if hasattr(widget, 'value'):
                ui_value = widget.value()
                result = max(1, ui_value)
                print(f"   UI控件值: {ui_value}")
                print(f"   返回值(UI): {result}")
                return result
            else:
                print(f"   UI控件没有value方法")

        # 默认返回1
        print(f"   返回默认值: 1")
        return 1

    except Exception as e:
        print(f"   异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

def debug_divider_method():
    """调试分频器方法"""
    print("=" * 60)
    print("调试分频器方法")
    print("=" * 60)
    
    try:
        # 创建QApplication
        app = QApplication(sys.argv)
        
        print("1. 加载寄存器配置...")
        config_path = os.path.join('lib', 'register.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        print("2. 创建RegisterManager...")
        register_manager = RegisterManager(registers_config)
        
        print("3. 创建现代化时钟输出处理器...")
        modern_handler = ModernClkOutputsHandler(None, register_manager)
        
        # 应用猴子补丁
        global original_get_divider_value
        original_get_divider_value = modern_handler._get_divider_value_for_output
        modern_handler._get_divider_value_for_output = traced_get_divider_value.__get__(modern_handler, ModernClkOutputsHandler)
        
        print("4. 手动调用分频器方法...")
        for output_num in range(4):  # 只测试前4个
            print(f"\n--- 测试输出{output_num} ---")
            divider_value = modern_handler._get_divider_value_for_output(output_num)
            print(f"最终返回值: {divider_value}")
        
        print("\n5. 检查UI控件的实际值...")
        divider_names = ["DCLK0_1DIV", "DCLK2_3DIV", "DCLK4_5DIV", "DCLK6_7DIV"]
        for div_name in divider_names:
            if hasattr(modern_handler.ui, div_name):
                widget = getattr(modern_handler.ui, div_name)
                if hasattr(widget, 'value'):
                    ui_value = widget.value()
                    print(f"   {div_name}: {ui_value}")
                else:
                    print(f"   {div_name}: 没有value方法")
            else:
                print(f"   {div_name}: 控件不存在")
        
        print("\n6. 检查寄存器的实际值...")
        for div_name in divider_names:
            if div_name in modern_handler.widget_register_map:
                widget_info = modern_handler.widget_register_map[div_name]
                reg_addr = widget_info["register_addr"]
                bit_def = widget_info["bit_def"]
                bit_name = bit_def.get("name", "")
                
                if bit_name:
                    reg_value = register_manager.get_bit_field_value(reg_addr, bit_name)
                    print(f"   {div_name}: 寄存器值 = {reg_value}")
                else:
                    print(f"   {div_name}: 位名称未找到")
            else:
                print(f"   {div_name}: 寄存器映射不存在")
        
        print("\n" + "=" * 60)
        print("调试完成")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_divider_method()
