"""
SPI信号管理器
负责处理SPI服务的各种信号连接和处理
"""

from PyQt5.QtWidgets import QMessageBox
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class SPISignalManager:
    """SPI信号管理器，专门处理SPI相关的信号"""
    
    def __init__(self, main_window):
        """初始化SPI信号管理器
        
        Args:
            main_window: 主窗口实例
        """
        self.main_window = main_window
    
    def connect_spi_signals(self):
        """连接SPI服务的信号"""
        if hasattr(self.main_window, 'spi_service'):
            # SPI操作相关信号现在由SPI协调器处理
            # 只保留UI相关的信号连接

            # 连接端口刷新信号
            if hasattr(self.main_window.spi_service, 'ports_refreshed'):
                self.main_window.spi_service.ports_refreshed.connect(self.handle_ports_refreshed)

            # 连接模拟模式变化信号
            if hasattr(self.main_window.spi_service, 'simulation_mode_changed'):
                self.main_window.spi_service.simulation_mode_changed.connect(self.handle_simulation_mode_changed)

            # 连接连接状态变化信号
            if hasattr(self.main_window.spi_service, 'connection_status_changed'):
                self.main_window.spi_service.connection_status_changed.connect(self.handle_connection_status_changed)
                
            logger.debug("SPI信号连接完成")

    def handle_ports_refreshed(self, ports_info: list):
        """处理端口刷新信号

        Args:
            ports_info: 包含端口信息的列表，每个元素是一个字典，包含 device 和 description
        """
        try:

            # 防止重复处理相同的端口信息
            if hasattr(self, '_last_ports_info') and self._last_ports_info == ports_info:
                logger.info("SPISignalManager: 端口信息未变化，跳过重复处理")
                return
            self._last_ports_info = ports_info.copy() if ports_info else []

            # 如果IO处理器存在且有刷新端口的方法，更新端口列表
            if hasattr(self.main_window.io_handler, 'update_port_list'):
                self.main_window.io_handler.update_port_list(ports_info)

            # 更新状态栏显示
            if ports_info:
                port_count = len(ports_info)
                self.main_window.show_status_message(f"发现 {port_count} 个可用端口", 3000)
            else:
                self.main_window.show_status_message("未发现可用端口", 3000)

        except Exception as e:
            logger.error(f"处理端口刷新信号时出错: {str(e)}")
            self.main_window.show_status_message("更新端口列表时出错", 3000)

    def handle_simulation_mode_changed(self, enabled: bool):
        """处理模拟模式变化信号

        Args:
            enabled: 是否启用模拟模式
        """
        try:
            logger.info(f"SPISignalManager: 模拟模式已{'启用' if enabled else '禁用'}")
            # 更新本地模拟模式状态
            self.main_window.simulation_mode = enabled

            # 更新UI状态
            if hasattr(self.main_window, 'io_handler'):
                # 更新IO处理器的模拟模式状态（如果有相关方法）
                if hasattr(self.main_window.io_handler, 'set_simulation_mode'):
                    self.main_window.io_handler.set_simulation_mode(enabled)

            # 更新菜单项的选中状态（如果存在）
            self._update_menu_simulation_mode(enabled)

            # 立即更新状态栏以反映模式变化
            if hasattr(self.main_window, 'status_config_manager'):
                self.main_window.status_config_manager.update_status_bar()

            # 显示切换消息
            mode_text = "模拟模式" if enabled else "硬件模式"
            self.main_window.show_status_message(f"已切换到{mode_text}", 3000)

        except Exception as e:
            logger.error(f"处理模拟模式变化信号时出错: {str(e)}")

    def handle_connection_status_changed(self, connected: bool):
        """处理连接状态变化信号

        Args:
            connected: 是否已连接
        """
        try:
            logger.info(f"SPI连接状态变化: {'已连接' if connected else '已断开'}")

            # 获取详细的连接状态信息
            status_info = self.main_window.spi_service.get_connection_status()

            # 更新UI状态
            if hasattr(self.main_window, 'io_handler'):
                # 更新IO处理器的连接状态（如果有相关方法）
                if hasattr(self.main_window.io_handler, 'update_connection_status'):
                    self.main_window.io_handler.update_connection_status(connected)

            # 根据连接状态更新按钮状态
            self.main_window.io_handler.toggle_buttons(connected)

            # 更新状态栏的模式和端口信息
            if hasattr(self.main_window, 'status_config_manager'):
                self.main_window.status_config_manager.update_status_bar()

            # 更新状态栏显示
            if connected:
                port = status_info.get('port', 'Unknown')
                mode = status_info.get('mode', 'Unknown')
                self.main_window.show_status_message(f"已连接到 {port} ({mode} 模式)", 3000)
            else:
                error = status_info.get('last_error', '')
                message = "连接已断开" + (f": {error}" if error else "")
                self.main_window.show_status_message(message, 3000)

                # 如果是因为错误断开，且不在模拟模式下，显示错误对话框
                if error and not self.main_window.simulation_mode:
                    QMessageBox.warning(self.main_window, "连接错误", message)

        except Exception as e:
            logger.error(f"处理连接状态变化信号时出错: {str(e)}")
    
    def _update_menu_simulation_mode(self, enabled):
        """更新菜单中的模拟模式选项"""
        try:
            # 更新菜单项的选中状态（如果存在）
            for action in self.main_window.menuBar().actions():
                if action.text() == '工具(&T)':
                    for sub_action in action.menu().actions():
                        if sub_action.text() == '模拟通信(&W)':
                            sub_action.setChecked(enabled)
                            break
                    break
        except Exception as e:
            logger.warning(f"更新菜单模拟模式状态时出错: {str(e)}")
    
    def disconnect_spi_signals(self):
        """断开SPI信号连接"""
        try:
            if hasattr(self.main_window, 'spi_service') and self.main_window.spi_service:
                # 断开端口刷新信号
                if hasattr(self.main_window.spi_service, 'ports_refreshed'):
                    self.main_window.spi_service.ports_refreshed.disconnect(self.handle_ports_refreshed)

                # 断开模拟模式变化信号
                if hasattr(self.main_window.spi_service, 'simulation_mode_changed'):
                    self.main_window.spi_service.simulation_mode_changed.disconnect(self.handle_simulation_mode_changed)

                # 断开连接状态变化信号
                if hasattr(self.main_window.spi_service, 'connection_status_changed'):
                    self.main_window.spi_service.connection_status_changed.disconnect(self.handle_connection_status_changed)
                    
                logger.debug("SPI信号断开完成")
        except Exception as e:
            logger.warning(f"断开SPI信号时出错: {str(e)}")
    
    def check_spi_availability(self):
        """检查SPI可用性"""
        if self.main_window.simulation_mode:
            return True
        
        if not hasattr(self.main_window, 'spi_service') or not self.main_window.spi_service:
            QMessageBox.warning(self.main_window, "错误", "SPI操作不可用，请检查连接")
            return False
        
        # 使用get_connection_status()方法检查端口状态
        status = self.main_window.spi_service.get_connection_status()
        if not status.get('connected', False):
            QMessageBox.warning(self.main_window, "错误", "请先选择COM端口")
            return False
        
        return True
    
    def get_connection_status(self):
        """获取连接状态信息"""
        if hasattr(self.main_window, 'spi_service') and self.main_window.spi_service:
            return self.main_window.spi_service.get_connection_status()
        return {
            'connected': False,
            'mode': 'Unknown',
            'port': None,
            'last_error': 'SPI服务未初始化'
        }
    
    def cleanup_resources(self):
        """清理资源"""
        try:
            # 断开信号连接
            self.disconnect_spi_signals()
            
            logger.debug("SPI信号管理器资源已清理")
        except Exception as e:
            logger.error(f"清理SPI信号管理器资源时出错: {str(e)}")
