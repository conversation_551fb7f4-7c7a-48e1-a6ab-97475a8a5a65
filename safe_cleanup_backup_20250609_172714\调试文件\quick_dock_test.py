#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

print('快速测试拖拽停靠功能...')

from PyQt5.QtWidgets import QApplication
from core.services.config.ConfigurationManager import set_config

# 启用强制悬浮模式
set_config('plugins.force_floating_mode', True)

from ui.windows.RegisterMainWindow import RegisterMainWindow

# 创建应用和主窗口
app = QApplication([])
main_window = RegisterMainWindow()
main_window.show()

# 获取插件服务
plugin_service = main_window.plugin_service

from core.services.plugin.PluginManager import plugin_manager

# 获取测试插件
plugins = plugin_manager.get_tool_window_plugins()
test_plugin = plugins[0] if plugins else None

if test_plugin:
    print(f'使用插件: {test_plugin.name}')
    
    # 创建测试窗口
    test_window = test_plugin.create_window(main_window)
    
    if test_window:
        # 配置拖拽停靠功能
        plugin_service._configure_plugin_window(test_window, test_plugin.name)
        plugin_service.plugin_windows[test_plugin.name] = test_window
        test_window.show()
        
        # 处理事件
        app.processEvents()
        time.sleep(0.5)
        
        # 检查停靠前状态
        initial_tabs = main_window.tools_tab_widget.count()
        print(f'停靠前标签数: {initial_tabs}')
        
        # 执行拖拽停靠测试
        success = plugin_service.test_drag_dock_functionality(test_plugin.name)
        
        # 处理事件并等待
        app.processEvents()
        time.sleep(1)
        
        # 检查停靠后状态
        final_tabs = main_window.tools_tab_widget.count()
        print(f'停靠后标签数: {final_tabs}')
        
        # 输出结果
        if final_tabs > initial_tabs:
            print(f'拖拽停靠测试成功: 标签数从 {initial_tabs} 增加到 {final_tabs}')
        else:
            print(f'拖拽停靠测试失败: 标签数没有增加 (前: {initial_tabs}, 后: {final_tabs})')
    else:
        print('测试窗口创建失败')
else:
    print('没有可用的插件')

app.quit()
print('测试完成')