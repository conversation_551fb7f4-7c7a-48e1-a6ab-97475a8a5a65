#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单插件测试
快速验证插件系统基本功能
"""

import sys
import os

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)


def test_plugin_imports():
    """测试插件导入"""
    print("🔍 测试插件导入...")
    
    try:
        # 测试插件管理器导入
        from core.services.plugin.PluginManager import plugin_manager
        print("✅ 插件管理器导入成功")
        
        # 测试插件集成服务导入
        from core.services.plugin.PluginIntegrationService import PluginIntegrationService
        print("✅ 插件集成服务导入成功")
        
        # 测试插件GUI导入
        from ui.tools.PluginManagerGUI import PluginManagerWindow
        print("✅ 插件管理器GUI导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 插件导入失败: {str(e)}")
        return False


def test_plugin_files():
    """测试插件文件存在性"""
    print("\n📁 测试插件文件...")
    
    plugin_files = [
        "plugins/performance_monitor_plugin.py",
        "plugins/data_analysis_plugin.py",
        "plugins/example_tool_plugin.py"
    ]
    
    all_exist = True
    
    for plugin_file in plugin_files:
        full_path = os.path.join(project_root, plugin_file)
        if os.path.exists(full_path):
            print(f"✅ {plugin_file} 存在")
        else:
            print(f"❌ {plugin_file} 不存在")
            all_exist = False
    
    return all_exist


def test_plugin_discovery():
    """测试插件发现"""
    print("\n🔍 测试插件发现...")
    
    try:
        from core.services.plugin.PluginManager import plugin_manager
        
        # 添加插件目录
        plugin_dir = os.path.join(project_root, "plugins")
        tools_dir = os.path.join(project_root, "ui", "tools")
        
        plugin_manager.add_plugin_directory(plugin_dir)
        plugin_manager.add_plugin_directory(tools_dir)
        
        # 扫描插件
        plugin_manager.scan_plugins()
        
        # 获取插件列表
        plugins = plugin_manager.get_plugin_list()
        
        print(f"✅ 发现 {len(plugins)} 个插件:")
        for plugin in plugins:
            print(f"   - {plugin.name} v{plugin.version}")
        
        return len(plugins) > 0
        
    except Exception as e:
        print(f"❌ 插件发现失败: {str(e)}")
        return False


def test_plugin_classes():
    """测试插件类定义"""
    print("\n🏗️ 测试插件类定义...")
    
    try:
        # 测试性能监控插件
        from plugins.performance_monitor_plugin import PerformanceMonitorPlugin
        perf_plugin = PerformanceMonitorPlugin()
        print(f"✅ 性能监控插件: {perf_plugin.name}")
        
        # 测试数据分析插件
        from plugins.data_analysis_plugin import DataAnalysisPlugin
        data_plugin = DataAnalysisPlugin()
        print(f"✅ 数据分析插件: {data_plugin.name}")
        
        # 测试示例工具插件
        from plugins.example_tool_plugin import ExampleToolPlugin
        example_plugin = ExampleToolPlugin()
        print(f"✅ 示例工具插件: {example_plugin.name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 插件类测试失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("🚀 简单插件系统测试")
    print("=" * 50)
    
    tests = [
        ("插件导入", test_plugin_imports),
        ("插件文件", test_plugin_files),
        ("插件发现", test_plugin_discovery),
        ("插件类定义", test_plugin_classes)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
    
    print("\n" + "=" * 50)
    print("📊 测试结果")
    print("=" * 50)
    print(f"通过: {passed}/{total} 个测试")
    
    if passed == total:
        print("🎉 所有测试通过！插件系统工作正常")
    else:
        print("⚠️  部分测试失败")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
