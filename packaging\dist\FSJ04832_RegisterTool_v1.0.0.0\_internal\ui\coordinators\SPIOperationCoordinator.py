"""
SPI操作协调器
负责协调和管理所有SPI相关的操作
"""

from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtWidgets import QMessageBox
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class SPIOperationCoordinator(QObject):
    """SPI操作协调器，管理所有SPI相关的操作"""
    
    # 信号定义
    # 注意：移除了重复的operation_completed信号，使用原始的spi_operation_complete信号
    operation_failed = pyqtSignal(str, str)  # 操作失败信号 (地址, 错误信息)
    
    def __init__(self, main_window):
        """初始化SPI操作协调器
        
        Args:
            main_window: 主窗口实例
        """
        super().__init__()
        self.main_window = main_window
        
        # 操作超时设置
        self.operation_timeout = getattr(main_window, 'OPERATION_TIMEOUT', 5000)
        
        # 连接SPI信号
        self._connect_spi_signals()
    
    def execute_read_operation(self, addr):
        """执行SPI读取操作
        
        Args:
            addr: 寄存器地址
            
        Returns:
            bool: 操作是否成功启动
        """
        try:
            # 检查SPI可用性
            if not self._check_spi_availability():
                return False
            
            # 标准化地址
            normalized_addr = self.main_window._normalize_register_address(addr)
            
            logger.info(f"开始SPI读取操作: {normalized_addr}")
            
            # 启动超时计时器
            self.main_window.operation_timer.start(self.operation_timeout)
            
            # 执行读取
            self.main_window.spi_service.read_register(normalized_addr)
            
            # 更新状态栏
            self.main_window.show_status_message(f"正在读取: 地址 {normalized_addr}...", 3000)
            
            return True
            
        except Exception as e:
            logger.error(f"启动SPI读取操作时出错: {str(e)}")
            self._handle_operation_error(addr, f"读取操作启动失败: {str(e)}")
            return False
    
    def execute_write_operation(self, addr, value):
        """执行SPI写入操作
        
        Args:
            addr: 寄存器地址
            value: 要写入的值
            
        Returns:
            bool: 操作是否成功启动
        """
        try:
            # 检查SPI可用性
            if not self._check_spi_availability():
                return False
            
            # 标准化地址
            normalized_addr = self.main_window._normalize_register_address(addr)
            
            logger.info(f"开始SPI写入操作: {normalized_addr} = 0x{value:04X}")
            
            # 启动超时计时器
            self.main_window.operation_timer.start(self.operation_timeout)
            
            # 禁用按钮防止重复操作
            self.main_window.io_handler.toggle_buttons(False)
            
            # 执行写入
            if self.main_window.simulation_mode:
                # 模拟模式下直接处理
                self._handle_simulation_write(normalized_addr, value)
            else:
                # 硬件模式下异步写入
                self.main_window.spi_service.write_register(normalized_addr, value)
            
            # 更新状态栏
            reg_num = int(normalized_addr, 16)
            self.main_window.show_status_message(
                f"正在写入: R{reg_num} (0x{reg_num:02X}) = 0x{value:04X}...", 3000
            )
            
            return True
            
        except Exception as e:
            logger.error(f"启动SPI写入操作时出错: {str(e)}")
            self._handle_operation_error(addr, f"写入操作启动失败: {str(e)}")
            return False
    
    def handle_spi_result(self, addr, value, is_read):
        """处理SPI操作结果
        
        Args:
            addr: 寄存器地址
            value: 操作值
            is_read: 是否为读取操作
        """
        try:
            # 停止超时计时器
            self.main_window.operation_timer.stop()

            # 只有在非批量操作模式下才重新启用按钮
            if not self.main_window.batch_manager.is_in_batch_operation():
                self.main_window.io_handler.toggle_buttons(True)

            # 取消超时定时器
            if hasattr(self.main_window.register_service, '_cancel_timeout_timer'):
                self.main_window.register_service._cancel_timeout_timer(addr)

            # 委托给寄存器操作服务处理业务逻辑
            self.main_window.register_service.handle_spi_operation_result(addr, value, is_read)

            # 标准化地址
            normalized_addr = self.main_window._normalize_register_address(addr)

            # 在批量模式下，只更新数据模型，UI刷新将在批量操作完成后进行
            in_batch_operation = self.main_window.batch_manager.is_in_batch_operation()
            logger.debug(f"handle_spi_result: in_batch_operation={in_batch_operation}, addr={normalized_addr}")

            if not in_batch_operation:
                # 如果是当前选中的寄存器，更新右侧详细视图
                if self.main_window.selected_register_addr == normalized_addr:
                    reg_num = int(normalized_addr, 16)
                    # 仅更新输入框显示，不触发写操作
                    self.main_window._update_rx_value_display(reg_num, value)
                    
                    # 更新位字段表格
                    if hasattr(self.main_window.table_handler, 'show_bit_fields'):
                        self.main_window.table_handler.show_bit_fields(normalized_addr, value)
                    
                    # 刷新视图
                    self.main_window.refresh_view()
                    
                    logger.debug(f"已更新当前选中寄存器 {normalized_addr} 的显示")
                else:
                    logger.debug(f"寄存器 {normalized_addr} 不是当前选中的寄存器，跳过UI更新")
            else:
                logger.debug(f"批量操作期间，跳过寄存器 {normalized_addr} 的UI更新")

            # 注意：移除重复的operation_completed信号发送
            # 原始的spi_operation_complete信号已经足够，避免信号重复
            # self.operation_completed.emit(addr, value, is_read)  # 已注释，避免重复

        except Exception as e:
            logger.error(f"处理SPI结果时出错: {str(e)}, 地址: {addr}, 值: {value}")

            # 如果在批量操作中出错，尝试完成批量操作
            if self.main_window.batch_manager.is_in_batch_operation():
                logger.warning("在批量操作过程中处理SPI结果出错，尝试强制取消批量操作")
                self.main_window.batch_manager.force_cancel_all_operations()

            # 确保按钮被重新启用
            self.main_window.io_handler.toggle_buttons(True)
    
    def handle_operation_timeout(self, addr=None):
        """处理操作超时
        
        Args:
            addr: 超时的寄存器地址（可选）
        """
        logger.warning(f"SPI操作超时: {addr if addr else '未知地址'}")
        
        # 停止计时器
        self.main_window.operation_timer.stop()
        
        # 重新启用按钮
        self.main_window.io_handler.toggle_buttons(True)
        
        # 显示超时消息
        QMessageBox.warning(self.main_window, "超时", "操作超时，请检查设备连接")
        
        # 更新状态栏
        self.main_window.show_status_message("操作超时", 3000)
        
        # 发送操作失败信号
        self.operation_failed.emit(addr if addr else "unknown", "操作超时")
    
    def show_error(self, error_msg):
        """显示SPI错误
        
        Args:
            error_msg: 错误消息
        """
        # 停止超时计时器
        self.main_window.operation_timer.stop()
        
        # 重新启用按钮
        self.main_window.io_handler.toggle_buttons(True)
        
        # 显示错误对话框
        QMessageBox.critical(self.main_window, "SPI错误", error_msg)
        
        # 更新状态栏
        self.main_window.show_status_message(f"SPI错误: {error_msg}", 5000)
        
        logger.error(f"SPI错误: {error_msg}")
    
    def _check_spi_availability(self):
        """检查SPI可用性
        
        Returns:
            bool: SPI是否可用
        """
        if self.main_window.simulation_mode:
            return True
        
        if not hasattr(self.main_window, 'spi_service') or not self.main_window.spi_service:
            QMessageBox.warning(self.main_window, "错误", "SPI操作不可用，请检查连接")
            return False
        
        return True
    
    def _handle_simulation_write(self, addr, value):
        """处理模拟模式下的写入
        
        Args:
            addr: 寄存器地址
            value: 要写入的值
        """
        try:
            # 在模拟模式下，直接调用SPI服务的模拟写入
            self.main_window.spi_service.write_register(addr, value)
            
            # 模拟操作完成，直接调用结果处理
            self.handle_spi_result(addr, value, False)
            
        except Exception as e:
            logger.error(f"模拟写入处理时出错: {str(e)}")
            self._handle_operation_error(addr, f"模拟写入失败: {str(e)}")
    
    def _handle_operation_error(self, addr, error_msg):
        """处理操作错误
        
        Args:
            addr: 寄存器地址
            error_msg: 错误消息
        """
        # 停止计时器
        self.main_window.operation_timer.stop()
        
        # 重新启用按钮
        self.main_window.io_handler.toggle_buttons(True)
        
        # 显示错误
        self.show_error(error_msg)
        
        # 发送操作失败信号
        self.operation_failed.emit(addr, error_msg)
    
    def _connect_spi_signals(self):
        """连接SPI服务的信号"""
        if hasattr(self.main_window, 'spi_service') and self.main_window.spi_service:
            # 连接操作完成信号
            if hasattr(self.main_window.spi_service, 'spi_operation_complete'):
                self.main_window.spi_service.spi_operation_complete.connect(self.handle_spi_result)
                logger.debug("已连接SPI操作完成信号")
            
            # 连接错误信号
            if hasattr(self.main_window.spi_service, 'error_occurred'):
                self.main_window.spi_service.error_occurred.connect(self.show_error)
                logger.debug("已连接SPI错误信号")
    
    def disconnect_spi_signals(self):
        """断开SPI服务的信号连接"""
        if hasattr(self.main_window, 'spi_service') and self.main_window.spi_service:
            try:
                if hasattr(self.main_window.spi_service, 'spi_operation_complete'):
                    self.main_window.spi_service.spi_operation_complete.disconnect(self.handle_spi_result)
                    
                if hasattr(self.main_window.spi_service, 'error_occurred'):
                    self.main_window.spi_service.error_occurred.disconnect(self.show_error)
                    
                logger.debug("已断开SPI信号连接")
            except Exception as e:
                logger.warning(f"断开SPI信号连接时出错: {str(e)}")
