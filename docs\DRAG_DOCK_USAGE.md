# 拖拽停靠功能使用说明

## 功能概述

拖拽停靠功能允许用户通过拖拽插件窗口到主窗口底部区域来自动停靠到标签页中，提供了直观的窗口管理方式。

## 启用拖拽停靠功能

### 方法1：通过配置文件启用强制悬浮模式

1. 在配置文件中添加以下设置：
```json
{
  "plugins": {
    "force_floating_mode": true
  }
}
```

2. 重启应用程序，所有插件窗口将以悬浮模式显示

### 方法2：通过代码动态启用

```python
from core.services.config.ConfigurationManager import set_config
set_config('plugins.force_floating_mode', True)
```

### 方法3：通过右键菜单分离窗口

1. 右键点击已停靠的标签页
2. 选择"分离窗口"
3. 窗口将变为悬浮状态，支持拖拽停靠

## 使用拖拽停靠功能

### 基本操作步骤

1. **打开插件窗口**
   - 通过菜单或工具栏打开任意插件窗口
   - 确保窗口处于悬浮状态（非标签页状态）

2. **开始拖拽**
   - 按住鼠标左键在窗口标题栏或窗口内容区域
   - 拖拽窗口到主窗口底部30%的区域

3. **观察视觉反馈**
   - 当窗口进入停靠区域时：
     - 窗口标题会显示"释放鼠标停靠到主界面"
     - 鼠标光标会变为手型指针

4. **完成停靠**
   - 在停靠区域内释放鼠标
   - 窗口将自动停靠到主界面标签页中

### 停靠区域说明

- **位置**：主窗口底部30%的区域
- **范围**：主窗口左右边界内，底部30%高度的矩形区域
- **视觉提示**：进入区域时会有明显的视觉反馈

## 其他窗口管理功能

### 右键菜单选项

在悬浮窗口上右键点击可以看到以下选项：

1. **停靠到主界面** - 直接停靠窗口到标签页
2. **测试拖拽停靠** - 显示拖拽停靠功能的详细说明
3. **窗口置顶/取消置顶** - 控制窗口是否始终在最前面
4. **最小化** - 最小化窗口
5. **关闭窗口** - 关闭窗口

### 标签页右键菜单

在标签页上右键点击可以看到：

1. **分离窗口** - 将标签页分离为悬浮窗口
2. **关闭窗口** - 关闭标签页

## 测试拖拽停靠功能

### 使用测试脚本

运行以下命令来测试拖拽停靠功能：

```bash
python test_drag_dock_quick.py
```

测试脚本会：
1. 自动启用强制悬浮模式
2. 创建主窗口和测试插件窗口
3. 配置拖拽停靠功能
4. 显示详细的测试说明

### 手动测试步骤

1. 启动应用程序
2. 启用强制悬浮模式（如上所述）
3. 打开任意插件窗口（如"时钟输入控制"）
4. 按照基本操作步骤进行拖拽停靠测试

## 故障排除

### 拖拽停靠不工作

1. **检查悬浮模式**
   - 确保插件窗口处于悬浮状态，而不是标签页状态
   - 可以通过右键菜单"分离窗口"来转换为悬浮状态

2. **检查停靠区域**
   - 确保拖拽到主窗口底部30%的区域
   - 主窗口必须可见且未被其他窗口遮挡

3. **检查拖拽距离**
   - 拖拽距离必须超过系统设定的阈值（通常为10像素）
   - 确保按住鼠标左键进行拖拽

4. **查看调试日志**
   - 控制台会显示详细的拖拽调试信息
   - 查找标签为"[拖拽调试]"和"[停靠区域调试]"的日志

### 常见问题

**Q: 为什么有些插件窗口无法拖拽停靠？**
A: 默认情况下，核心插件（如时钟输入控制、PLL控制等）会直接集成到标签页中。需要启用强制悬浮模式或通过右键菜单分离窗口。

**Q: 拖拽时没有视觉反馈怎么办？**
A: 检查是否正确拖拽到停靠区域，确保主窗口可见且未被遮挡。

**Q: 窗口停靠后如何重新分离？**
A: 右键点击标签页，选择"分离窗口"即可重新变为悬浮状态。

## 技术实现

拖拽停靠功能的核心实现位于：
- `core/services/plugin/PluginIntegrationService.py`
- 主要方法：`_add_drag_dock_support()`, `_setup_drag_event_handling()`
- 停靠区域检测：`_is_in_dock_area()`, `_check_dock_area()`

功能特点：
- 重写窗口鼠标事件处理方法
- 实时检测拖拽距离和停靠区域
- 提供丰富的视觉反馈
- 支持所有类型的插件窗口
