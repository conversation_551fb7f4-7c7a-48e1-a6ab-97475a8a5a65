#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试框架验证脚本
验证测试框架本身是否正常工作
"""

import os
import sys
import subprocess
from datetime import datetime

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"🔍 {title}")
    print("=" * 60)

def check_test_suite_structure():
    """检查测试套件结构"""
    print_header("检查测试套件结构")
    
    required_dirs = [
        'test_suite',
        'test_suite/functional',
        'test_suite/integration', 
        'test_suite/ui',
        'test_suite/unit',
        'test_suite/performance',
        'test_suite/regression',
        'test_suite/data',
        'test_suite/logs',
        'test_suite/reports'
    ]
    
    required_files = [
        'test_suite/README.md',
        'test_suite/test_config.py',
        'test_suite/test_utils.py',
        'test_suite/run_all_tests.py',
        'test_suite/organize_existing_tests.py'
    ]
    
    all_good = True
    
    # 检查目录
    print("检查目录结构:")
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"  ✅ {dir_path}")
        else:
            print(f"  ❌ {dir_path} (缺失)")
            all_good = False
    
    # 检查文件
    print("\n检查核心文件:")
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path} (缺失)")
            all_good = False
    
    return all_good

def count_test_files():
    """统计测试文件数量"""
    print_header("统计测试文件")
    
    categories = ['functional', 'integration', 'ui', 'unit', 'performance', 'regression']
    total_files = 0
    
    for category in categories:
        category_dir = f'test_suite/{category}'
        if os.path.exists(category_dir):
            files = [f for f in os.listdir(category_dir) if f.startswith('test_') and f.endswith('.py')]
            count = len(files)
            total_files += count
            print(f"  {category.ljust(12)}: {count} 个测试文件")
            
            # 显示前几个文件名
            if count > 0:
                for i, file_name in enumerate(files[:3]):
                    print(f"    - {file_name}")
                if count > 3:
                    print(f"    ... 还有 {count - 3} 个文件")
        else:
            print(f"  {category.ljust(12)}: 目录不存在")
    
    print(f"\n总计: {total_files} 个测试文件")
    return total_files

def test_config_module():
    """测试配置模块"""
    print_header("测试配置模块")
    
    try:
        sys.path.insert(0, 'test_suite')
        from test_config import setup_test_environment, TEST_CONFIG, get_test_config
        
        # 测试环境设置
        if setup_test_environment():
            print("  ✅ 测试环境设置成功")
        else:
            print("  ❌ 测试环境设置失败")
            return False
        
        # 测试配置获取
        config = get_test_config('functional')
        if config:
            print(f"  ✅ 功能测试配置: {config}")
        else:
            print("  ❌ 无法获取功能测试配置")
            return False
        
        # 测试超时配置
        timeouts = TEST_CONFIG.get('timeout', {})
        print(f"  ✅ 超时配置: {timeouts}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 配置模块测试失败: {e}")
        return False

def test_utils_module():
    """测试工具模块"""
    print_header("测试工具模块")
    
    try:
        sys.path.insert(0, 'test_suite')
        from test_utils import TestLogger, TestResult, MockSPIService, MockRegisterManager
        
        # 测试日志记录器
        logger = TestLogger("validation_test")
        logger.info("测试日志记录")
        print("  ✅ 日志记录器工作正常")
        
        # 测试结果记录器
        result = TestResult("test_validation")
        result.add_detail("test_key", "test_value")
        result.set_success(True)
        print(f"  ✅ 测试结果记录器: {result.to_dict()}")
        
        # 测试模拟SPI服务
        spi_service = MockSPIService()
        spi_service.initialize()
        spi_service.write_register("0x50", 0x1234)
        value = spi_service.read_register("0x50")
        if value == 0x1234:
            print("  ✅ 模拟SPI服务工作正常")
        else:
            print(f"  ❌ 模拟SPI服务异常: 期望0x1234，得到{hex(value)}")
            return False
        
        # 测试模拟寄存器管理器
        reg_manager = MockRegisterManager()
        reg_manager.set_bit_field_value("0x50", "PLL1_PD", 1)
        pll1_pd = reg_manager.get_bit_field_value("0x50", "PLL1_PD")
        if pll1_pd == 1:
            print("  ✅ 模拟寄存器管理器工作正常")
        else:
            print(f"  ❌ 模拟寄存器管理器异常: 期望1，得到{pll1_pd}")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ 工具模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sample_test_execution():
    """测试示例测试执行"""
    print_header("测试示例测试执行")
    
    # 测试我们创建的示例测试
    sample_tests = [
        'test_suite/functional/test_pll_control.py',
        'test_suite/functional/test_auto_write.py',
        'test_suite/integration/test_module_communication.py'
    ]
    
    success_count = 0
    
    for test_file in sample_tests:
        if os.path.exists(test_file):
            print(f"\n运行: {test_file}")
            try:
                result = subprocess.run([sys.executable, test_file], 
                                      capture_output=True, text=True, timeout=60)
                if result.returncode == 0:
                    print(f"  ✅ 执行成功")
                    success_count += 1
                else:
                    print(f"  ❌ 执行失败")
                    print(f"  错误: {result.stderr[:200]}...")
            except subprocess.TimeoutExpired:
                print(f"  ⏰ 执行超时")
            except Exception as e:
                print(f"  💥 执行出错: {e}")
        else:
            print(f"跳过: {test_file} (文件不存在)")
    
    print(f"\n示例测试执行结果: {success_count}/{len(sample_tests)} 成功")
    return success_count > 0

def test_main_runner():
    """测试主运行器"""
    print_header("测试主运行器")
    
    try:
        # 测试运行器帮助信息
        result = subprocess.run([sys.executable, 'test_suite/run_all_tests.py', '--help'], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print("  ✅ 主运行器帮助信息正常")
        else:
            print("  ❌ 主运行器帮助信息异常")
            return False
        
        # 测试快速运行（不实际执行测试，只检查发现功能）
        print("  测试测试发现功能...")
        # 这里可以添加更多测试
        
        return True
        
    except Exception as e:
        print(f"  ❌ 主运行器测试失败: {e}")
        return False

def generate_validation_report(results):
    """生成验证报告"""
    print_header("验证报告")
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results if result[1])
    
    print("验证结果:")
    for test_name, success, details in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name.ljust(25)}: {status}")
        if details:
            print(f"    {details}")
    
    success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    
    print(f"\n总体结果:")
    print(f"  验证项目: {total_tests}")
    print(f"  通过项目: {passed_tests}")
    print(f"  成功率: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print(f"\n🎉 优秀！测试框架验证通过，可以正常使用")
        return True
    elif success_rate >= 70:
        print(f"\n👍 良好！测试框架基本可用，但有些问题需要修复")
        return True
    else:
        print(f"\n🚨 警告！测试框架存在严重问题，需要修复后再使用")
        return False

def main():
    """主函数"""
    print("🧪 测试框架验证工具")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行各项验证
    validation_results = []
    
    # 1. 检查结构
    structure_ok = check_test_suite_structure()
    validation_results.append(("测试套件结构", structure_ok, None))
    
    # 2. 统计文件
    file_count = count_test_files()
    validation_results.append(("测试文件统计", file_count > 0, f"{file_count} 个文件"))
    
    # 3. 测试配置模块
    config_ok = test_config_module()
    validation_results.append(("配置模块", config_ok, None))
    
    # 4. 测试工具模块
    utils_ok = test_utils_module()
    validation_results.append(("工具模块", utils_ok, None))
    
    # 5. 测试示例执行
    sample_ok = test_sample_test_execution()
    validation_results.append(("示例测试执行", sample_ok, None))
    
    # 6. 测试主运行器
    runner_ok = test_main_runner()
    validation_results.append(("主运行器", runner_ok, None))
    
    # 生成报告
    overall_success = generate_validation_report(validation_results)
    
    print(f"\n完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return 0 if overall_success else 1

if __name__ == "__main__":
    sys.exit(main())
