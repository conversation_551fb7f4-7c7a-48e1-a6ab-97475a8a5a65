from core.models.RegisterModel import Register
from utils.Log import get_module_logger

logger = get_module_logger(__name__)

class RegisterManager:
    """寄存器管理器，负责创建、获取和更新Register对象"""
    
    def __init__(self, registers_json):
        """初始化寄存器管理器
        
        Args:
            registers_json: 从registersNew.json加载的寄存器配置字典
        """
        self.registers_json = registers_json
        self.register_objects = {}
        self._initialize_registers()
    
    def _initialize_registers(self):
        """初始化所有寄存器对象"""
        for addr, info in self.registers_json.items():
            normalized_addr = self._normalize_register_address(addr)
            self.register_objects[normalized_addr] = Register(normalized_addr, info['bits'])

    def get_register_bit_info(self, addr):
        """获取指定寄存器地址的位字段信息

        Args:
            addr: 寄存器地址
        Returns:
            dict: 位字段信息字典
        """
        normalized_addr = self._normalize_register_address(addr)
        if normalized_addr in self.register_objects:
            return self.register_objects[normalized_addr].bits
        else:
            raise ValueError(f"未知的寄存器地址: {normalized_addr}")
    
    def get_register(self, addr):
        """获取指定地址的寄存器对象
        
        Args:
            addr: 寄存器地址，可以是字符串或整数
            
        Returns:
            Register: 寄存器对象
        """
        normalized_addr = self._normalize_register_address(addr)
        return self.register_objects.get(normalized_addr)
    
    def set_register_value(self, addr, value):
        """设置寄存器值
        
        Args:
            addr: 寄存器地址，可以是字符串或整数
            value: 新的寄存器值
            
        Returns:
            str: 标准化后的寄存器地址
        """
        normalized_addr = self._normalize_register_address(addr)
        if normalized_addr not in self.register_objects:
            raise ValueError(f"未知的寄存器地址: {normalized_addr}")
        
        register = self.register_objects[normalized_addr]
        
        # 如果值未变化，则不更新也不发送信号
        if register.value == value & 0xFFFF:
            return normalized_addr
            
        # 更新寄存器值
        register.value = value & 0xFFFF
        
        # 注意：移除了RegisterUpdateBus的直接调用，避免重复信号发送
        # RegisterUpdateBus信号现在统一由RegisterOperationService发送
        # 这样确保每次寄存器更新只发送一次信号
        logger.debug(f"RegisterManager更新寄存器: {normalized_addr}, 值={register.value:04X}")
        
        return normalized_addr
    
    def get_register_value(self, addr):
        """获取寄存器值
        
        Args:
            addr: 寄存器地址，可以是字符串或整数
            
        Returns:
            int: 寄存器值
        """
        normalized_addr = self._normalize_register_address(addr)
        if normalized_addr not in self.register_objects:
            raise ValueError(f"未知的寄存器地址: {normalized_addr}")
        
        return self.register_objects[normalized_addr].value
    
    def get_all_register_addresses(self):
        """获取所有寄存器地址
        
        Returns:
            list: 所有寄存器地址的列表
        """
        return list(self.register_objects.keys())

    def get_all_register_values(self):
        """获取所有寄存器的值
        
        Returns:
            dict: 寄存器地址到值的映射
        """
        return {addr: reg.value for addr, reg in self.register_objects.items()}
    
    def get_all_registers(self):
        """获取所有寄存器信息
        
        Returns:
            list: 寄存器信息列表
        """
        registers = []
        for addr, register in self.register_objects.items():
            # 从register_json获取寄存器名称和描述
            reg_info = self.registers_json.get(addr, {})
            reg_entry = {
                "address": addr,
                "name": reg_info.get("name", f"寄存器 {addr}"),
                "description": reg_info.get("description", ""),
                "default": reg_info.get("default", 0),
                "current_value": register.value
            }
            registers.append(reg_entry)
        return registers
    
    def set_bit_field_value(self, addr, bit_name, value):
        """设置指定寄存器的位字段值
        
        Args:
            addr: 寄存器地址，可以是字符串或整数
            bit_name: 位字段名称
            value: 新的位字段值
            
        Returns:
            int: 更新后的寄存器值
        """
        normalized_addr = self._normalize_register_address(addr)
        if normalized_addr not in self.register_objects:
            raise ValueError(f"未知的寄存器地址: {normalized_addr}")
        
        register = self.register_objects[normalized_addr]
        register.set_bit_field_value(bit_name, value)
        
        # 注意：移除了RegisterUpdateBus的直接调用，避免重复信号发送
        # RegisterUpdateBus信号现在统一由RegisterOperationService发送
        logger.debug(f"RegisterManager更新位字段: {normalized_addr}.{bit_name}, 寄存器值={register.value:04X}")
            
        return register.value

    # === 新增：工具窗口专用API ===

    def get_register_bits(self, addr):
        """获取寄存器的位字段定义

        Args:
            addr: 寄存器地址

        Returns:
            list: 位字段定义列表
        """
        normalized_addr = self._normalize_register_address(addr)
        if normalized_addr not in self.register_objects:
            return []
        return self.register_objects[normalized_addr].bits

    def get_widget_register_mapping(self, addr):
        """获取指定寄存器的控件映射信息

        Args:
            addr: 寄存器地址

        Returns:
            dict: 控件名到寄存器信息的映射
        """
        normalized_addr = self._normalize_register_address(addr)
        if normalized_addr not in self.register_objects:
            return {}

        register = self.register_objects[normalized_addr]
        widget_map = {}

        for bit in register.bits:
            widget_name = bit.get("widget_name")
            if widget_name:
                widget_map[widget_name] = {
                    "register_addr": normalized_addr,
                    "widget_type": bit.get("widget_type"),
                    "default_value": bit.get("default"),
                    "bit_def": bit,
                    "bit_def_name": bit.get("name"),  # 添加bit_def_name字段
                    "options": bit.get("options"),
                    "description": bit.get("description")
                }

        return widget_map

    def update_widget_value(self, widget_name, value, addr_hint=None):
        """通过控件名更新寄存器位字段值

        Args:
            widget_name: 控件名称
            value: 新值
            addr_hint: 寄存器地址提示（可选，用于优化查找）

        Returns:
            tuple: (success, register_addr, new_register_value)
        """
        # 如果提供了地址提示，先尝试该地址
        if addr_hint:
            try:
                normalized_addr = self._normalize_register_address(addr_hint)
                if normalized_addr in self.register_objects:
                    register = self.register_objects[normalized_addr]
                    for bit in register.bits:
                        if bit.get("widget_name") == widget_name:
                            old_value = register.value
                            register.set_bit_field_value(bit["name"], value)
                            new_value = register.value

                            # 发送更新信号（如果值确实变化了）
                            if old_value != new_value:
                                self._emit_register_update_signal(normalized_addr, new_value)

                            return True, normalized_addr, new_value
            except Exception:
                pass  # 如果地址提示无效，继续全局搜索

        # 全局搜索控件名
        for addr, register in self.register_objects.items():
            for bit in register.bits:
                if bit.get("widget_name") == widget_name:
                    old_value = register.value
                    register.set_bit_field_value(bit["name"], value)
                    new_value = register.value

                    # 发送更新信号（如果值确实变化了）
                    if old_value != new_value:
                        self._emit_register_update_signal(addr, new_value)

                    return True, addr, new_value

        return False, None, None

    def _emit_register_update_signal(self, addr, value):
        """发送寄存器更新信号（已禁用）"""
        # 注意：移除了RegisterUpdateBus的直接调用，避免重复信号发送
        # RegisterUpdateBus信号现在统一由RegisterOperationService发送
        logger.debug(f"RegisterManager控件更新: {addr}, 值={value:04X}")

    def get_registers_by_widget_names(self, widget_names):
        """根据控件名列表获取相关的寄存器地址

        Args:
            widget_names: 控件名列表

        Returns:
            dict: 寄存器地址到控件名列表的映射
        """
        register_widgets = {}

        for addr, register in self.register_objects.items():
            for bit in register.bits:
                widget_name = bit.get("widget_name")
                if widget_name and widget_name in widget_names:
                    if addr not in register_widgets:
                        register_widgets[addr] = []
                    register_widgets[addr].append(widget_name)

        return register_widgets
    
    def get_bit_field_value(self, addr, bit_name):
        """获取指定寄存器的位字段值
        
        Args:
            addr: 寄存器地址，可以是字符串或整数
            bit_name: 位字段名称
            
        Returns:
            int: 位字段值
        """
        normalized_addr = self._normalize_register_address(addr)
        if normalized_addr not in self.register_objects:
            raise ValueError(f"未知的寄存器地址: {normalized_addr}")
        
        register = self.register_objects[normalized_addr]
        return register.get_bit_field_value(bit_name)

    def search_bit_fields(self, keyword):
        """根据关键字模糊搜索所有寄存器中的位段名称
        
        Args:
            keyword (str): 搜索关键字
            
        Returns:
            list: 匹配的位段列表，每个元素是 (bit_field_name, register_address, register_name)
        """
        matches = []
        # 确保 keyword 是非空字符串
        if not isinstance(keyword, str) or not keyword:
            return matches
            
        keyword_lower = keyword.lower()
        for addr, register in self.register_objects.items():
            reg_info = self.registers_json.get(addr, {})
            register_name = reg_info.get("name", f"寄存器 {addr}")
            # Handle both list and dict cases for register.bits
            if isinstance(register.bits, list):
                for bit_info in register.bits: # Iterate through list of bit dictionaries
                    # 使用 .get('', '') 确保获取的是字符串，即使键不存在或值为 None
                    bit_name = str(bit_info.get('name', ''))
                    bit_field_desc = str(bit_info.get('description', ''))

                    # 如果 bit_name 为空（例如 JSON 中缺少 name 键），则跳过
                    if not bit_name:
                        continue

                    if keyword_lower in bit_name.lower() or keyword_lower in bit_field_desc.lower():
                        matches.append((bit_name, addr, register_name))
            elif isinstance(register.bits, dict):
                 # Original logic if it's a dictionary
                 for bit_name_key, bit_info in register.bits.items(): # Use bit_name_key to avoid confusion
                    # 优先使用 'name' 键，否则回退到字典的键，并确保是字符串
                    bit_field_name = str(bit_info.get('name', bit_name_key))
                    bit_field_desc = str(bit_info.get('description', '')) # 确保是字符串

                    if keyword_lower in bit_field_name.lower() or keyword_lower in bit_field_desc.lower():
                        matches.append((bit_field_name, addr, register_name))
                    
        # 可以选择按位段名称排序
        matches.sort(key=lambda x: x[0]) 
        return matches
    
    def _normalize_register_address(self, addr):
        """标准化寄存器地址格式
        
        Args:
            addr: 寄存器地址，可以是字符串或整数
            
        Returns:
            str: 标准化后的寄存器地址，格式为0xXX
        """
        if isinstance(addr, str):
            addr = addr.strip().upper().replace(' ', '')
            addr_num = int(addr, 16) if addr.startswith('0X') else int(addr, 16)
            return f"0x{addr_num:02X}" # 返回大写十六进制
        return f"0x{addr:02X}" # 返回大写十六进制

    def get_register_info(self, addr):
        """获取指定地址的寄存器信息
        
        Args:
            addr: 寄存器地址
            
        Returns:
            dict: 寄存器信息字典
        """
        normalized_addr = self._normalize_register_address(addr)
        return self.registers_json.get(normalized_addr)

    def print_register_info(self):
        """获取所有寄存器的信息，包括位信息"""
        for addr, register in self.register_objects.items():
            print(f"Address: {addr}")
            for bit in register.bits:
                print(f"  Bit Name: {bit['name']}")
                print(f"  Bit Range: {bit['bit']}")
                print(f"  Default: {bit['default']}")
                print(f"  Widget Name: {bit.get('widget_name')}")
                print(f"  Widget Type: {bit.get('widget_type')}")
                print(f"  Options: {bit.get('options')}")
                print(f"  Description: {bit.get('description')}")
            print("\n")
    
    def get_adapted_register_objects(self):
        """获取适配后的寄存器对象，用于兼容需要bits属性和get_bit_field_value方法的代码
        
        Returns:
            dict: 地址到适配后寄存器对象的映射
        """
        adapted_registers = {}
        
        for addr, register in self.register_objects.items():
            # 创建适配器对象
            adapted_register = RegisterAdapter(addr, register, self.registers_json.get(addr, {}))
            adapted_registers[addr] = adapted_register
            
        return adapted_registers


class RegisterAdapter:
    """寄存器适配器类，用于兼容原有代码中对寄存器对象的要求"""
    
    def __init__(self, address, register, reg_info=None):
        """初始化寄存器适配器
        
        Args:
            address: 寄存器地址
            register: Register对象
            reg_info: 寄存器信息字典（可选）
        """
        self.address = address
        self.register = register
        self.reg_info = reg_info or {}

        # 如果reg_info有bits字段，使用它；否则从register对象获取
        if hasattr(register, 'bits'):
            self.bits = register.bits
        else:
            self.bits = self.reg_info.get('bits', [])

    @property
    def value(self):
        """获取寄存器值，始终从真正的Register对象获取最新值"""
        return self.register.value

    @value.setter
    def value(self, new_value):
        """设置寄存器值，同步到真正的Register对象"""
        self.register.value = new_value
    
    def get_bit_field_value(self, bit_name):
        """获取位字段值
        
        Args:
            bit_name: 位字段名称
            
        Returns:
            int: 位字段值
        """
        if hasattr(self.register, 'get_bit_field_value'):
            return self.register.get_bit_field_value(bit_name)
        
        # 找到位字段定义
        bit_field = None
        for bit in self.bits:
            if bit['name'] == bit_name:
                bit_field = bit
                break
                
        if not bit_field:
            return 0
            
        # 计算位字段值
        bit_range = bit_field.get('bit', '0')
        if ':' in bit_range:
            high, low = map(int, bit_range.split(':'))
            mask = ((1 << (high - low + 1)) - 1) << low
            return (self.value & mask) >> low
        else:
            bit_pos = int(bit_range)
            return (self.value >> bit_pos) & 1
    
    def set_bit_field_value(self, bit_name, value):
        """设置位字段值
        
        Args:
            bit_name: 位字段名称
            value: 新的位字段值
            
        Returns:
            None
        """
        if hasattr(self.register, 'set_bit_field_value'):
            result = self.register.set_bit_field_value(bit_name, value)
            # 值已经通过property自动同步，无需额外操作
            return result
            
        # 找到位字段定义
        bit_field = None
        for bit in self.bits:
            if bit['name'] == bit_name:
                bit_field = bit
                break
                
        if not bit_field:
            return
            
        # 计算位字段掩码和位置
        bit_range = bit_field.get('bit', '0')
        if ':' in bit_range:
            high, low = map(int, bit_range.split(':'))
            mask = ((1 << (high - low + 1)) - 1) << low
            
            # 清除现有位并设置新值
            self.value = (self.value & ~mask) | ((value << low) & mask)
        else:
            bit_pos = int(bit_range)
            mask = 1 << bit_pos
            
            # 清除现有位并设置新值
            self.value = (self.value & ~mask) | ((value & 1) << bit_pos)
            
        # 更新寄存器对象的值
        if hasattr(self.register, 'value'):
            self.register.value = self.value
