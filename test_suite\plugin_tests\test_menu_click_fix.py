#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试插件菜单点击修复
验证菜单点击响应是否正常
"""

import sys
import os

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication, QMainWindow, QMenuBar, QAction, QWidget, QVBoxLayout, QLabel, QPushButton
from PyQt5.QtCore import QTimer, pyqtSignal
from core.services.plugin.PluginManager import plugin_manager
from core.services.plugin.PluginIntegrationService import PluginIntegrationService


class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    menu_clicked = pyqtSignal(str)  # 菜单点击信号
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("插件菜单点击修复测试")
        self.resize(900, 700)
        
        # 创建中央窗口部件
        self.setup_central_widget()
        
        # 创建菜单栏
        self.menuBar()
        
        # 设置插件系统
        self.setup_plugins()
        
        # 添加测试菜单项
        self.add_test_menu()
        
        # 连接信号
        self.menu_clicked.connect(self.on_menu_clicked)
        
        # 设置自动关闭定时器
        self.close_timer = QTimer()
        self.close_timer.timeout.connect(self.close)
        self.close_timer.start(120000)  # 120秒后自动关闭
    
    def setup_central_widget(self):
        """设置中央窗口部件"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # 添加说明标签
        self.info_label = QLabel("""
插件菜单点击修复测试

测试说明：
1. 直接点击插件菜单项，观察是否有响应
2. 检查控制台输出的调试信息
3. 验证插件窗口是否正确打开
4. 测试菜单项的选中/取消选中状态

如果修复成功，应该：
- 直接点击菜单项就能触发
- 不需要从右边向左滑动
- 控制台有详细的调试信息
- 插件窗口能正常打开和关闭

程序将在2分钟后自动关闭
        """)
        self.info_label.setWordWrap(True)
        layout.addWidget(self.info_label)
        
        # 添加测试按钮
        self.test_button = QPushButton("手动测试菜单响应")
        self.test_button.clicked.connect(self.manual_test)
        layout.addWidget(self.test_button)
        
        # 添加状态显示
        self.status_label = QLabel("状态: 等待测试...")
        layout.addWidget(self.status_label)
    
    def setup_plugins(self):
        """设置插件系统"""
        try:
            # 添加插件目录
            plugin_dir = os.path.join(project_root, "plugins")
            plugin_manager.add_plugin_directory(plugin_dir)
            
            # 扫描并初始化插件
            plugin_manager.scan_plugins()
            plugin_manager.initialize_plugins(self)
            
            # 创建插件集成服务
            self.plugin_service = PluginIntegrationService(self)
            
            # 连接插件服务信号
            if hasattr(self.plugin_service, 'plugin_window_opened'):
                self.plugin_service.plugin_window_opened.connect(self.on_plugin_window_opened)
            if hasattr(self.plugin_service, 'plugin_window_closed'):
                self.plugin_service.plugin_window_closed.connect(self.on_plugin_window_closed)
            
            self.plugin_service.initialize_plugins()
            
            plugin_count = len(plugin_manager.get_plugin_list())
            print(f"🚀 插件系统初始化完成，发现 {plugin_count} 个插件")
            self.status_label.setText(f"状态: 插件系统已初始化，发现 {plugin_count} 个插件")
            
        except Exception as e:
            print(f"❌ 插件系统初始化失败: {str(e)}")
            self.status_label.setText(f"状态: 插件系统初始化失败 - {str(e)}")
    
    def add_test_menu(self):
        """添加测试菜单"""
        test_menu = self.menuBar().addMenu("测试(&T)")
        
        # 添加测试动作
        test_action = QAction("测试普通点击", self)
        test_action.triggered.connect(lambda: self.menu_clicked.emit("测试普通点击"))
        test_menu.addAction(test_action)
        
        # 添加可选中的测试动作
        checkable_action = QAction("测试可选中点击", self)
        checkable_action.setCheckable(True)
        checkable_action.triggered.connect(lambda checked: self.menu_clicked.emit(f"测试可选中点击 (checked={checked})"))
        test_menu.addAction(checkable_action)
        
        print("✅ 添加了测试菜单")
    
    def on_menu_clicked(self, menu_text):
        """菜单点击处理"""
        print(f"🎯 测试菜单被点击: {menu_text}")
        self.status_label.setText(f"状态: 测试菜单被点击 - {menu_text}")
    
    def on_plugin_window_opened(self, plugin_name, window):
        """插件窗口打开处理"""
        print(f"🎉 插件窗口已打开: {plugin_name}")
        self.status_label.setText(f"状态: ✅ 插件窗口已打开 - {plugin_name}")
        
        # 更新信息标签
        self.info_label.setText(f"""
插件菜单点击修复测试

✅ 成功打开插件: {plugin_name}
窗口类型: {type(window).__name__}

测试结果: 插件菜单点击响应正常！

可以继续测试其他插件或关闭当前插件窗口...
        """)
    
    def on_plugin_window_closed(self, plugin_name):
        """插件窗口关闭处理"""
        print(f"🔒 插件窗口已关闭: {plugin_name}")
        self.status_label.setText(f"状态: 🔒 插件窗口已关闭 - {plugin_name}")
    
    def manual_test(self):
        """手动测试"""
        print("🔧 开始手动测试菜单响应...")
        self.status_label.setText("状态: 🔧 手动测试中...")
        
        # 检查插件菜单是否存在
        menu_bar = self.menuBar()
        plugin_menu_found = False
        
        for action in menu_bar.actions():
            if action.menu() and "插件" in action.text():
                plugin_menu_found = True
                plugin_menu = action.menu()
                plugin_actions = [a for a in plugin_menu.actions() if not a.isSeparator()]
                print(f"📋 找到插件菜单，包含 {len(plugin_actions)} 个插件动作")
                
                for plugin_action in plugin_actions:
                    if plugin_action.text() != "插件管理器":
                        print(f"🔍 插件动作: {plugin_action.text()}, 可用: {plugin_action.isEnabled()}, 可见: {plugin_action.isVisible()}")
                break
        
        if not plugin_menu_found:
            print("❌ 未找到插件菜单")
            self.status_label.setText("状态: ❌ 未找到插件菜单")
        else:
            print("✅ 插件菜单检查完成")
            self.status_label.setText("状态: ✅ 插件菜单检查完成，请手动点击测试")


def main():
    """主函数"""
    app = QApplication([])
    
    # 创建测试窗口
    window = TestMainWindow()
    window.show()
    
    print("🚀 插件菜单点击修复测试开始")
    print("请在窗口中测试插件菜单的鼠标点击响应")
    print("特别注意：")
    print("1. 直接点击插件菜单项是否有响应")
    print("2. 是否还需要从右边向左滑动才能触发")
    print("3. 控制台是否输出详细的调试信息")
    print("程序将在2分钟后自动关闭")
    
    # 运行应用
    app.exec_()
    
    print("✅ 测试完成")


if __name__ == "__main__":
    main()
