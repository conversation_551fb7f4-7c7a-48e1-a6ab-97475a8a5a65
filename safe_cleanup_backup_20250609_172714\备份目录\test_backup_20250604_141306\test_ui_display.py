#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试UI显示问题
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import QTimer

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.Log import logger


def test_ui_display():
    """测试UI显示"""
    print("=" * 60)
    print("测试UI显示问题")
    print("=" * 60)
    
    # 创建应用程序
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    
    try:
        # 1. 测试直接创建UI
        print("\n1. 测试直接创建UI...")
        from ui.forms.Ui_ClkOutputs import Ui_ClkOutputs
        
        # 创建主窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("直接UI测试")
        main_window.setGeometry(100, 100, 800, 600)
        
        # 创建中央控件
        central_widget = QWidget()
        main_window.setCentralWidget(central_widget)
        
        # 创建UI并设置
        ui = Ui_ClkOutputs()
        ui.setupUi(central_widget)
        
        print("✓ 直接UI创建成功")
        
        # 检查一些关键控件
        if hasattr(ui, 'lineEditFvco'):
            print(f"✓ lineEditFvco 存在: {ui.lineEditFvco}")
            ui.lineEditFvco.setText("2949.12")
        
        if hasattr(ui, 'lineEditFout0Output'):
            print(f"✓ lineEditFout0Output 存在: {ui.lineEditFout0Output}")
            ui.lineEditFout0Output.setText("1474.56")
        
        # 显示窗口
        main_window.show()
        print("✓ 直接UI窗口显示")
        
        # 2. 测试现代化处理器
        print("\n2. 测试现代化处理器...")
        from ui.handlers.ModernClkOutputsHandler import ModernClkOutputsHandler
        from core.services.register.RegisterManager import RegisterManager
        import json
        
        # 加载寄存器配置
        config_path = os.path.join(os.path.dirname(__file__), 'lib', 'register.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        register_manager = RegisterManager(registers_config)
        
        # 创建现代化处理器
        modern_handler = ModernClkOutputsHandler(None, register_manager)
        modern_handler.setWindowTitle("现代化处理器测试")
        modern_handler.setGeometry(200, 200, 800, 600)
        
        print("✓ 现代化处理器创建成功")
        
        # 检查content_widget
        if hasattr(modern_handler, 'content_widget'):
            print(f"✓ content_widget 存在: {modern_handler.content_widget}")
            print(f"  大小: {modern_handler.content_widget.size()}")
            print(f"  可见: {modern_handler.content_widget.isVisible()}")
            
            # 检查布局
            layout = modern_handler.content_widget.layout()
            if layout:
                print(f"  布局: {type(layout).__name__}")
                print(f"  布局项数量: {layout.count()}")
            else:
                print("  ❌ 没有布局")
        
        # 检查UI
        if hasattr(modern_handler, 'ui'):
            print(f"✓ UI 存在: {modern_handler.ui}")
            
            # 检查关键控件
            if hasattr(modern_handler.ui, 'lineEditFvco'):
                print(f"✓ UI.lineEditFvco 存在: {modern_handler.ui.lineEditFvco}")
                print(f"  父控件: {modern_handler.ui.lineEditFvco.parent()}")
                print(f"  可见: {modern_handler.ui.lineEditFvco.isVisible()}")
        
        # 显示现代化处理器窗口
        modern_handler.show()
        print("✓ 现代化处理器窗口显示")
        
        # 强制更新
        modern_handler.update()
        modern_handler.repaint()
        if hasattr(modern_handler, 'content_widget'):
            modern_handler.content_widget.update()
            modern_handler.content_widget.repaint()
        
        print("✓ 强制更新完成")
        
        # 3. 比较两个窗口
        print("\n3. 比较两个窗口...")
        print(f"直接UI窗口大小: {main_window.size()}")
        print(f"现代化处理器窗口大小: {modern_handler.size()}")
        
        if hasattr(modern_handler, 'content_widget'):
            print(f"content_widget大小: {modern_handler.content_widget.size()}")
        
        # 保持窗口显示
        def close_windows():
            print("关闭测试窗口")
            main_window.close()
            modern_handler.close()
            app.quit()
        
        QTimer.singleShot(5000, close_windows)  # 5秒后关闭
        
        print("\n窗口将显示5秒，请观察两个窗口的显示效果...")
        app.exec_()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    success = test_ui_display()
    
    if success:
        print("\n✅ UI显示测试完成")
        print("请观察两个窗口的显示效果，比较是否有差异")
    else:
        print("\n❌ UI显示测试失败")


if __name__ == '__main__':
    main()
