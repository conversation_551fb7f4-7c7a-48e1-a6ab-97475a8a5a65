#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试底部按钮是否已恢复
验证界面底部有需要的操作按钮，但IO处理器中间没有重复的中文按钮
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QPushButton
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt

def test_bottom_buttons_restored():
    """测试底部按钮是否已恢复"""
    app = QApplication(sys.argv)
    
    try:
        # 创建主窗口
        from ui.windows.RegisterMainWindow import RegisterMainWindow
        from core.services.spi.spi_service_impl import SPIServiceImpl
        from core.repositories.register_repository import RegisterRepository
        
        spi_service = SPIServiceImpl()
        spi_service.initialize()
        register_repo = RegisterRepository(spi_service)
        main_window = RegisterMainWindow(register_repo)
        main_window.show()
        
        # 等待界面完全加载
        QTest.qWait(2000)
        
        # 查找所有按钮
        all_buttons = main_window.findChildren(QPushButton)
        
        # 需要的底部按钮（英文）
        required_buttons = ["Read", "Write", "Read All", "Write All", "DumpAll", "Save", "Load"]
        
        # 不应该存在的中文按钮
        unwanted_buttons = ["读取", "写入", "读取全部", "写入全部", "导出全部", "保存", "加载"]
        
        # 统计按钮文本
        found_buttons = []
        unwanted_found = []
        
        for button in all_buttons:
            text = button.text()
            if text in required_buttons:
                found_buttons.append(text)
            elif text in unwanted_buttons:
                unwanted_found.append(text)
        
        print("所有找到的按钮:")
        for button in all_buttons:
            text = button.text()
            if text:  # 只显示有文本的按钮
                print(f"  {text}")
        
        print(f"\n需要的底部按钮:")
        for text in required_buttons:
            if text in found_buttons:
                print(f"  ✅ {text}")
            else:
                print(f"  ❌ {text} (缺失)")
        
        if unwanted_found:
            print(f"\n❌ 发现不应该存在的中文按钮:")
            for text in unwanted_found:
                print(f"  {text}")
        
        # 检查结果
        missing_buttons = [btn for btn in required_buttons if btn not in found_buttons]
        
        if missing_buttons:
            print(f"\n❌ 缺失的按钮: {missing_buttons}")
            return False
        elif unwanted_found:
            print(f"\n❌ 存在不应该有的中文按钮: {unwanted_found}")
            return False
        else:
            print(f"\n✅ 成功！底部按钮已正确恢复，没有重复的中文按钮")
            return True
            
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        app.quit()

if __name__ == "__main__":
    success = test_bottom_buttons_restored()
    sys.exit(0 if success else 1)
