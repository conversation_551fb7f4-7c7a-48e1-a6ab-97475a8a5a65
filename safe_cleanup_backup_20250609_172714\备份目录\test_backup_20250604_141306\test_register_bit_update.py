#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试Register类的位字段更新功能
验证set_bit_field_value方法是否正确更新寄存器值
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_register_bit_field_update():
    """测试Register类的位字段更新功能"""
    try:
        print("=" * 60)
        print("测试Register类的位字段更新功能")
        print("=" * 60)
        
        # 导入Register类
        from core.models.RegisterModel import Register
        
        # 创建模拟0x5B寄存器的位字段定义
        register_bits = [
            {
                "bit": "15:9",
                "name": "NC",
                "default": "0000000",
                "widget_name": None,
                "widget_type": None,
                "options": None,
                "description": "Reserved"
            },
            {
                "bit": "8:6",
                "name": "LOS_TIMEOUT[2:0]",
                "default": "000",
                "widget_name": "LOSTimeout",
                "widget_type": "combobox",
                "options": "0:7",
                "description": "LOS timeout control"
            },
            {
                "bit": "5",
                "name": "LOS_EN",
                "default": "0",
                "widget_name": "losEn",
                "widget_type": "checkbox",
                "options": None,
                "description": "Enables the LOS timeout control"
            },
            {
                "bit": "4",
                "name": "SOME_BIT_4",
                "default": "0",
                "widget_name": None,
                "widget_type": None,
                "options": None,
                "description": "Some bit at position 4"
            },
            {
                "bit": "3",
                "name": "SOME_BIT_3",
                "default": "0",
                "widget_name": None,
                "widget_type": None,
                "options": None,
                "description": "Some bit at position 3"
            },
            {
                "bit": "2",
                "name": "SOME_BIT_2",
                "default": "1",  # 设置为1，这样初始值会有第2位为1
                "widget_name": None,
                "widget_type": None,
                "options": None,
                "description": "Some bit at position 2"
            },
            {
                "bit": "1:0",
                "name": "SOME_BITS[1:0]",
                "default": "00",
                "widget_name": None,
                "widget_type": None,
                "options": None,
                "description": "Some bits at positions 1:0"
            }
        ]
        
        # 创建寄存器对象
        register = Register("0x5B", register_bits)
        
        print(f"初始寄存器值: 0x{register.value:04X}")
        print(f"初始LOS_EN位值: {register.get_bit_field_value('LOS_EN')}")
        print(f"初始SOME_BIT_2位值: {register.get_bit_field_value('SOME_BIT_2')}")
        
        # 验证初始值是否正确（第2位应该为1，所以值应该是0x4）
        expected_initial = 0x4  # 第2位为1
        if register.value == expected_initial:
            print(f"✅ 初始值正确: 0x{register.value:04X}")
        else:
            print(f"❌ 初始值错误: 期望0x{expected_initial:04X}, 实际0x{register.value:04X}")
            
        print("\n" + "-" * 40)
        print("测试1: 设置LOS_EN位为1")
        print("-" * 40)
        
        # 设置LOS_EN位为1
        register.set_bit_field_value("LOS_EN", 1)
        
        new_value = register.value
        los_en_value = register.get_bit_field_value('LOS_EN')
        
        print(f"设置后寄存器值: 0x{new_value:04X}")
        print(f"设置后LOS_EN位值: {los_en_value}")
        
        # 验证结果
        expected_value = 0x4 | (1 << 5)  # 0x4 + 0x20 = 0x24
        if new_value == expected_value and los_en_value == 1:
            print(f"✅ 测试1通过: 寄存器值正确更新为0x{new_value:04X}")
        else:
            print(f"❌ 测试1失败: 期望0x{expected_value:04X}, 实际0x{new_value:04X}")
            
        print("\n" + "-" * 40)
        print("测试2: 设置LOS_EN位为0")
        print("-" * 40)
        
        # 设置LOS_EN位为0
        register.set_bit_field_value("LOS_EN", 0)
        
        new_value2 = register.value
        los_en_value2 = register.get_bit_field_value('LOS_EN')
        
        print(f"设置后寄存器值: 0x{new_value2:04X}")
        print(f"设置后LOS_EN位值: {los_en_value2}")
        
        # 验证结果
        expected_value2 = 0x4  # 回到原始值
        if new_value2 == expected_value2 and los_en_value2 == 0:
            print(f"✅ 测试2通过: 寄存器值正确更新为0x{new_value2:04X}")
        else:
            print(f"❌ 测试2失败: 期望0x{expected_value2:04X}, 实际0x{new_value2:04X}")
            
        print("\n" + "-" * 40)
        print("测试3: 测试位掩码计算")
        print("-" * 40)
        
        # 测试位掩码计算
        bit_start = register.get_bit_start("5")
        bit_mask = register.get_bit_mask("5")
        
        print(f"LOS_EN位起始位置: {bit_start}")
        print(f"LOS_EN位掩码: 0x{bit_mask:04X}")
        
        if bit_start == 5 and bit_mask == 1:
            print("✅ 位掩码计算正确")
        else:
            print(f"❌ 位掩码计算错误: 期望起始位5, 掩码1, 实际起始位{bit_start}, 掩码{bit_mask}")
            
        print("\n" + "=" * 60)
        print("Register类位字段更新测试完成")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_register_bit_field_update()
    sys.exit(0 if success else 1)
