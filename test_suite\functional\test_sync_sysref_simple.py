#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化的同步系统参考移除测试
专注于核心功能验证
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication


def test_sync_sysref_removal_simple():
    """简化的同步系统参考移除测试"""
    print("=" * 60)
    print("简化的同步系统参考移除测试")
    print("=" * 60)
    
    try:
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 1. 验证传统处理器文件已移除
        print("1. 验证传统处理器文件移除...")
        legacy_handler_path = os.path.join(project_root, 'ui', 'handlers', 'SyncSysRefHandler.py')
        
        if os.path.exists(legacy_handler_path):
            print("❌ 传统处理器文件仍然存在")
            return False
        else:
            print("✓ 传统处理器文件已成功移除")
        
        # 2. 验证传统处理器无法导入
        print("\n2. 验证传统处理器无法导入...")
        try:
            from ui.handlers.SyncSysRefHandler import SyncSysRefHandler
            print("❌ 仍然可以导入传统处理器")
            return False
        except ImportError:
            print("✓ 传统处理器导入失败（预期行为）")
        except Exception as e:
            print(f"✓ 传统处理器导入出错（预期行为）: {str(e)}")
        
        # 3. 验证现代化处理器正常工作
        print("\n3. 验证现代化处理器...")
        
        # 创建寄存器管理器
        from core.services.register.RegisterManager import RegisterManager
        
        # 加载寄存器配置
        config_path = os.path.join(project_root, 'lib', 'register.json')
        if not os.path.exists(config_path):
            print(f"❌ 寄存器配置文件不存在: {config_path}")
            return False
            
        import json
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        register_manager = RegisterManager(registers_config)
        print(f"✓ 寄存器管理器创建成功，包含 {len(register_manager.register_objects)} 个寄存器")
        
        # 创建现代化同步系统参考处理器
        from ui.handlers.ModernSyncSysRefHandler import ModernSyncSysRefHandler
        
        modern_handler = ModernSyncSysRefHandler(None, register_manager)
        print("✓ 现代化同步系统参考处理器创建成功")
        print(f"✓ 处理器类型: {type(modern_handler).__name__}")
        
        # 4. 验证现代化处理器功能
        print("\n4. 验证现代化处理器功能...")
        
        # 检查滚动区域
        if hasattr(modern_handler, 'scroll_area'):
            print("✓ 滚动区域存在")
            print(f"✓ 滚动区域类型: {type(modern_handler.scroll_area).__name__}")
        else:
            print("❌ 滚动区域不存在")
            return False
        
        # 检查content_widget
        if hasattr(modern_handler, 'content_widget'):
            print("✓ content_widget存在")
            print(f"✓ content_widget最小大小: {modern_handler.content_widget.minimumSize()}")
        else:
            print("❌ content_widget不存在")
            return False
        
        # 检查控件映射
        if hasattr(modern_handler, 'widget_register_map'):
            mapping_count = len(modern_handler.widget_register_map)
            print(f"✓ 控件映射构建成功，包含 {mapping_count} 个控件")
            
            # 显示部分映射
            sync_widgets = [name for name in modern_handler.widget_register_map.keys() if "SYNC" in name]
            if sync_widgets:
                print(f"✓ SYNC相关控件: {sync_widgets[:3]}...")  # 显示前3个
        else:
            print("❌ 控件映射构建失败")
            return False
        
        # 检查UI控件
        if hasattr(modern_handler, 'ui'):
            print("✓ UI对象存在")
            
            # 检查关键控件
            key_widgets = ['InternalVCOFreq', 'spinBoxSysrefDIV', 'SYNCDIS0', 'pBtAllOn', 'pBtAllOff']
            for widget_name in key_widgets:
                if hasattr(modern_handler.ui, widget_name):
                    widget = getattr(modern_handler.ui, widget_name)
                    print(f"✓ {widget_name}: 存在 ({type(widget).__name__})")
                else:
                    print(f"❌ {widget_name}: 不存在")
        else:
            print("❌ UI对象不存在")
            return False
        
        # 5. 验证工厂配置
        print("\n5. 验证工厂配置...")
        from ui.factories.ModernToolWindowFactory import ModernToolWindowFactory
        
        sync_config = ModernToolWindowFactory.HANDLER_CONFIGS.get('sync_sysref')
        
        if not sync_config:
            print("❌ 同步系统参考配置不存在")
            return False
        
        print("✓ 同步系统参考配置存在")
        
        # 验证配置正确性
        if sync_config.get('legacy_handler') is not None:
            print("❌ 传统处理器配置应该为None")
            return False
        else:
            print("✓ 传统处理器配置已正确设置为None")
        
        if sync_config.get('use_modern') is not True:
            print("❌ 应该强制使用现代化处理器")
            return False
        else:
            print("✓ 已配置为强制使用现代化处理器")
        
        if not sync_config.get('modern_handler'):
            print("❌ 现代化处理器配置不能为空")
            return False
        else:
            print("✓ 现代化处理器配置正确")
        
        # 6. 测试工厂创建窗口
        print("\n6. 测试工厂创建窗口...")
        
        # 创建一个模拟的主窗口对象
        class MockMainWindow:
            def __init__(self):
                self.register_manager = register_manager
        
        mock_main_window = MockMainWindow()
        factory = ModernToolWindowFactory(mock_main_window)
        
        try:
            sync_window = factory.create_window_by_type('sync_sysref')
            if sync_window:
                print("✓ 工厂成功创建同步系统参考窗口")
                print(f"✓ 创建的窗口类型: {type(sync_window).__name__}")
                
                # 验证是现代化处理器
                if 'Modern' in type(sync_window).__name__:
                    print("✓ 确认使用现代化处理器")
                else:
                    print("❌ 未使用现代化处理器")
                    return False
            else:
                print("❌ 工厂创建窗口失败")
                return False
        except Exception as e:
            print(f"❌ 工厂创建窗口时出错: {str(e)}")
            return False
        
        print("\n✅ 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_sync_sysref_removal_simple()
    
    if success:
        print("\n🎉 同步系统参考传统处理器移除成功！")
        print("📋 移除总结:")
        print("   - ✅ 传统处理器文件已移除")
        print("   - ✅ 传统处理器无法导入")
        print("   - ✅ 现代化处理器正常工作")
        print("   - ✅ 滚动区域功能正常")
        print("   - ✅ 控件映射构建成功")
        print("   - ✅ UI控件加载正常")
        print("   - ✅ 工厂配置正确")
        print("   - ✅ 工厂创建窗口正常")
        print("\n🔄 架构改进:")
        print("   - 移除了传统的SyncSysRefHandler")
        print("   - 只保留现代化的ModernSyncSysRefHandler")
        print("   - 继承了滚动区域功能")
        print("   - 保持了与时钟输出的交互能力")
    else:
        print("\n❌ 测试失败，需要进一步检查")
        sys.exit(1)
