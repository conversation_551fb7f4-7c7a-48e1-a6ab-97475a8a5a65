#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
插件停靠问题修复测试脚本
测试停靠所有窗口和自动停靠功能是否正常工作
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QPushButton, QTextEdit, QLabel
from PyQt5.QtCore import QTimer
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class PluginDockTestWindow(QWidget):
    """插件停靠测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.main_window = None
        self.plugin_service = None
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("插件停靠问题修复测试")
        self.setGeometry(100, 100, 800, 600)
        
        layout = QVBoxLayout()
        
        # 状态标签
        self.status_label = QLabel("准备测试...")
        layout.addWidget(self.status_label)
        
        # 测试按钮
        self.find_service_btn = QPushButton("1. 查找插件服务")
        self.find_service_btn.clicked.connect(self.find_plugin_service)
        layout.addWidget(self.find_service_btn)
        
        self.open_windows_btn = QPushButton("2. 打开测试窗口")
        self.open_windows_btn.clicked.connect(self.open_test_windows)
        layout.addWidget(self.open_windows_btn)
        
        self.dock_all_btn = QPushButton("3. 停靠所有窗口")
        self.dock_all_btn.clicked.connect(self.dock_all_windows)
        layout.addWidget(self.dock_all_btn)
        
        self.undock_all_btn = QPushButton("4. 分离所有窗口")
        self.undock_all_btn.clicked.connect(self.undock_all_windows)
        layout.addWidget(self.undock_all_btn)
        
        self.test_close_btn = QPushButton("5. 测试标签页关闭")
        self.test_close_btn.clicked.connect(self.test_tab_close)
        layout.addWidget(self.test_close_btn)
        
        self.check_status_btn = QPushButton("6. 检查状态")
        self.check_status_btn.clicked.connect(self.check_status)
        layout.addWidget(self.check_status_btn)
        
        # 日志文本框
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(300)
        layout.addWidget(self.log_text)
        
        self.setLayout(layout)
        
    def find_plugin_service(self):
        """查找插件服务"""
        try:
            # 查找主窗口
            app = QApplication.instance()
            for widget in app.topLevelWidgets():
                if hasattr(widget, 'plugin_integration_service'):
                    self.main_window = widget
                    self.plugin_service = widget.plugin_integration_service
                    break
            
            if self.plugin_service:
                self.status_label.setText("✅ 找到插件集成服务")
                self.log_text.append("✅ 插件集成服务已找到")
                self.log_text.append(f"   - 主窗口: {type(self.main_window).__name__}")
                self.log_text.append(f"   - 插件服务: {type(self.plugin_service).__name__}")
                
                # 检查子服务
                if hasattr(self.plugin_service, 'window_service'):
                    self.log_text.append(f"   - 窗口服务: ✅")
                if hasattr(self.plugin_service, 'dock_service'):
                    self.log_text.append(f"   - 停靠服务: ✅")
                if hasattr(self.plugin_service, 'menu_service'):
                    self.log_text.append(f"   - 菜单服务: ✅")
            else:
                self.status_label.setText("❌ 未找到插件集成服务")
                self.log_text.append("❌ 未找到插件集成服务")
                
        except Exception as e:
            error_msg = f"查找插件服务失败: {str(e)}"
            self.status_label.setText(error_msg)
            self.log_text.append(f"❌ {error_msg}")
            logger.error(error_msg)
            
    def open_test_windows(self):
        """打开测试窗口"""
        try:
            if not self.plugin_service:
                self.status_label.setText("请先查找插件服务")
                return
            
            self.log_text.append("🔄 打开测试窗口...")
            
            # 获取插件管理器
            from core.services.plugin.PluginManager import plugin_manager
            
            # 获取工具窗口插件
            tool_plugins = plugin_manager.get_tool_window_plugins()
            test_plugins = ['clkin_control_plugin', 'pll_control_plugin']
            
            opened_count = 0
            for plugin in tool_plugins:
                if plugin.name in test_plugins:
                    try:
                        # 触发插件菜单动作
                        self.plugin_service._on_plugin_action_triggered(plugin, True)
                        self.log_text.append(f"✅ 已打开: {plugin.name}")
                        opened_count += 1
                    except Exception as e:
                        self.log_text.append(f"❌ 打开失败 {plugin.name}: {str(e)}")
            
            self.status_label.setText(f"已打开 {opened_count} 个测试窗口")
            
        except Exception as e:
            error_msg = f"打开测试窗口失败: {str(e)}"
            self.status_label.setText(error_msg)
            self.log_text.append(f"❌ {error_msg}")
            logger.error(error_msg)
            
    def dock_all_windows(self):
        """停靠所有窗口"""
        try:
            if not self.plugin_service:
                self.status_label.setText("请先查找插件服务")
                return
            
            self.log_text.append("🔄 停靠所有悬浮窗口...")
            
            # 获取所有插件窗口
            plugin_windows = self.plugin_service.window_service.plugin_windows
            if not plugin_windows:
                self.status_label.setText("没有打开的窗口")
                self.log_text.append("❌ 没有打开的窗口可以停靠")
                return
            
            docked_count = 0
            for plugin_name in list(plugin_windows.keys()):
                try:
                    success = self.plugin_service.dock_floating_window(plugin_name)
                    if success:
                        self.log_text.append(f"✅ 已停靠: {plugin_name}")
                        docked_count += 1
                    else:
                        self.log_text.append(f"❌ 停靠失败: {plugin_name}")
                except Exception as e:
                    self.log_text.append(f"❌ 停靠失败 {plugin_name}: {str(e)}")
            
            self.status_label.setText(f"停靠操作完成，成功停靠 {docked_count} 个窗口")
            
            # 延迟检查标签页状态
            QTimer.singleShot(1000, self.check_tab_status)
            
        except Exception as e:
            error_msg = f"停靠所有窗口失败: {str(e)}"
            self.status_label.setText(error_msg)
            self.log_text.append(f"❌ {error_msg}")
            logger.error(error_msg)
            
    def undock_all_windows(self):
        """分离所有窗口"""
        try:
            if not self.plugin_service:
                self.status_label.setText("请先查找插件服务")
                return
            
            self.log_text.append("🔄 分离所有窗口...")
            
            plugin_windows = self.plugin_service.window_service.plugin_windows
            if not plugin_windows:
                self.status_label.setText("没有打开的窗口")
                self.log_text.append("❌ 没有打开的窗口可以分离")
                return
            
            undocked_count = 0
            for plugin_name in list(plugin_windows.keys()):
                try:
                    success = self.plugin_service.undock_window_from_tab(plugin_name)
                    if success:
                        self.log_text.append(f"✅ 已分离: {plugin_name}")
                        undocked_count += 1
                    else:
                        self.log_text.append(f"❌ 分离失败: {plugin_name}")
                except Exception as e:
                    self.log_text.append(f"❌ 分离失败 {plugin_name}: {str(e)}")
            
            self.status_label.setText(f"分离操作完成，成功分离 {undocked_count} 个窗口")
            
        except Exception as e:
            error_msg = f"分离所有窗口失败: {str(e)}"
            self.status_label.setText(error_msg)
            self.log_text.append(f"❌ {error_msg}")
            logger.error(error_msg)
            
    def test_tab_close(self):
        """测试标签页关闭"""
        try:
            if not self.main_window or not hasattr(self.main_window, 'tools_tab_widget'):
                self.status_label.setText("未找到标签页容器")
                return
            
            tab_widget = self.main_window.tools_tab_widget
            if tab_widget.count() == 0:
                self.status_label.setText("没有标签页可以关闭")
                self.log_text.append("❌ 没有标签页可以关闭")
                return
            
            self.log_text.append("🔄 测试标签页关闭...")
            
            # 关闭第一个标签页
            if tab_widget.count() > 0:
                tab_text = tab_widget.tabText(0)
                self.log_text.append(f"关闭标签页: {tab_text}")
                
                # 使用TabWindowManager关闭标签页
                if hasattr(self.main_window, 'tab_manager'):
                    self.main_window.tab_manager.close_tool_tab(0)
                    self.log_text.append("✅ 标签页关闭完成")
                else:
                    # 直接关闭
                    tab_widget.removeTab(0)
                    self.log_text.append("✅ 标签页移除完成")
            
            self.status_label.setText("标签页关闭测试完成")
            
            # 延迟检查状态
            QTimer.singleShot(1000, self.check_status)
            
        except Exception as e:
            error_msg = f"测试标签页关闭失败: {str(e)}"
            self.status_label.setText(error_msg)
            self.log_text.append(f"❌ {error_msg}")
            logger.error(error_msg)
            
    def check_tab_status(self):
        """检查标签页状态"""
        try:
            if not self.main_window or not hasattr(self.main_window, 'tools_tab_widget'):
                self.log_text.append("❌ 未找到标签页容器")
                return
            
            tab_widget = self.main_window.tools_tab_widget
            tab_count = tab_widget.count()
            is_visible = tab_widget.isVisible()
            
            self.log_text.append(f"📊 标签页状态:")
            self.log_text.append(f"   - 标签页数量: {tab_count}")
            self.log_text.append(f"   - 容器可见性: {is_visible}")
            
            if tab_count > 0:
                for i in range(tab_count):
                    tab_text = tab_widget.tabText(i)
                    widget = tab_widget.widget(i)
                    self.log_text.append(f"   - 标签页 {i}: {tab_text}")
                    if hasattr(widget, 'plugin_name'):
                        self.log_text.append(f"     插件名称: {widget.plugin_name}")
            else:
                self.log_text.append("   - 没有标签页")
                
        except Exception as e:
            self.log_text.append(f"❌ 检查标签页状态失败: {str(e)}")
            
    def check_status(self):
        """检查整体状态"""
        try:
            self.log_text.append("📊 整体状态检查:")
            
            if self.plugin_service:
                # 检查插件窗口
                plugin_windows = self.plugin_service.window_service.plugin_windows
                self.log_text.append(f"   - 插件窗口数量: {len(plugin_windows)}")
                
                for plugin_name, window in plugin_windows.items():
                    is_valid = self.plugin_service.window_service._is_window_valid(window)
                    is_docked = self.plugin_service.dock_service._is_window_already_docked(plugin_name)
                    self.log_text.append(f"     {plugin_name}: 有效={is_valid}, 已停靠={is_docked}")
            
            # 检查标签页状态
            self.check_tab_status()
            
            self.status_label.setText("状态检查完成")
            
        except Exception as e:
            error_msg = f"检查状态失败: {str(e)}"
            self.status_label.setText(error_msg)
            self.log_text.append(f"❌ {error_msg}")
            logger.error(error_msg)


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    test_window = PluginDockTestWindow()
    test_window.show()
    
    print("🧪 插件停靠问题修复测试")
    print("请按照界面上的按钮顺序进行测试：")
    print("1. 查找插件服务")
    print("2. 打开测试窗口")
    print("3. 停靠所有窗口")
    print("4. 分离所有窗口")
    print("5. 测试标签页关闭")
    print("6. 检查状态")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
