#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试移除旧时钟输出文件后应用程序是否正常运行
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.Log import logger


def test_app_functionality():
    """测试应用程序核心功能"""
    print("=" * 60)
    print("测试移除旧文件后的应用程序功能")
    print("=" * 60)
    
    # 创建应用程序
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    
    test_results = []
    
    # 1. 测试主窗口创建
    print("\n1. 测试主窗口创建...")
    try:
        from core.services.spi.spi_service_impl import SPIServiceImpl
        from core.repositories.register_repository import RegisterRepository
        from ui.windows.RegisterMainWindow import RegisterMainWindow
        
        # 创建SPI服务
        spi_service = SPIServiceImpl()
        spi_service.initialize()
        register_repo = RegisterRepository(spi_service)
        
        # 创建主窗口
        main_window = RegisterMainWindow(register_repo)
        print("   ✓ 主窗口创建成功")
        test_results.append("主窗口创建成功")
        
        # 2. 测试时钟输出窗口创建
        print("\n2. 测试时钟输出窗口创建...")
        try:
            clk_output_window = main_window._show_clk_output_window()
            if clk_output_window:
                print("   ✓ 时钟输出窗口创建成功")
                print(f"   ✓ 窗口类型: {type(clk_output_window).__name__}")
                test_results.append("时钟输出窗口创建成功")
                
                # 检查窗口是否是现代化版本
                if "Modern" in type(clk_output_window).__name__:
                    print("   ✓ 使用现代化处理器")
                    test_results.append("使用现代化处理器")
                else:
                    print("   ⚠ 使用传统处理器")
                    test_results.append("使用传统处理器")
                    
            else:
                print("   ❌ 时钟输出窗口创建失败")
                test_results.append("时钟输出窗口创建失败")
                
        except Exception as e:
            print(f"   ❌ 时钟输出窗口创建出错: {str(e)}")
            test_results.append(f"时钟输出窗口创建出错: {str(e)}")
        
        # 3. 测试工具窗口工厂
        print("\n3. 测试工具窗口工厂...")
        try:
            factory = main_window.tool_window_factory
            if factory.is_using_modern_factory():
                print("   ✓ 正在使用现代化工厂")
                test_results.append("正在使用现代化工厂")
            else:
                print("   ⚠ 使用传统工厂")
                test_results.append("使用传统工厂")
                
            # 测试工厂的时钟输出创建方法
            clk_window = factory.create_clk_output_window()
            if clk_window:
                print("   ✓ 工厂创建时钟输出窗口成功")
                test_results.append("工厂创建时钟输出窗口成功")
            else:
                print("   ❌ 工厂创建时钟输出窗口失败")
                test_results.append("工厂创建时钟输出窗口失败")
                
        except Exception as e:
            print(f"   ❌ 工厂测试出错: {str(e)}")
            test_results.append(f"工厂测试出错: {str(e)}")
        
        # 4. 测试现代化处理器功能
        print("\n4. 测试现代化处理器功能...")
        try:
            from ui.handlers.ModernClkOutputsHandler import ModernClkOutputsHandler
            
            # 创建现代化处理器实例
            modern_handler = ModernClkOutputsHandler(None, main_window.register_manager)
            
            # 检查基本功能
            if hasattr(modern_handler, 'calculate_output_frequencies'):
                print("   ✓ 频率计算功能可用")
                test_results.append("频率计算功能可用")
            
            if hasattr(modern_handler, 'ui') and modern_handler.ui:
                print("   ✓ UI界面已创建")
                test_results.append("UI界面已创建")
            
            if hasattr(modern_handler, 'widget_register_map'):
                print(f"   ✓ 控件映射已创建 ({len(modern_handler.widget_register_map)} 个)")
                test_results.append("控件映射已创建")
                
        except Exception as e:
            print(f"   ❌ 现代化处理器测试出错: {str(e)}")
            test_results.append(f"现代化处理器测试出错: {str(e)}")
        
        # 5. 测试菜单功能
        print("\n5. 测试菜单功能...")
        try:
            # 检查菜单是否正常
            if hasattr(main_window, 'menuBar') and main_window.menuBar():
                print("   ✓ 菜单栏存在")
                test_results.append("菜单栏存在")
                
                # 查找工具菜单
                for action in main_window.menuBar().actions():
                    if "工具" in action.text():
                        print("   ✓ 工具菜单存在")
                        test_results.append("工具菜单存在")
                        break
                        
        except Exception as e:
            print(f"   ❌ 菜单测试出错: {str(e)}")
            test_results.append(f"菜单测试出错: {str(e)}")
        
        # 清理
        main_window.close()
        
    except Exception as e:
        print(f"   ❌ 主窗口创建失败: {str(e)}")
        test_results.append(f"主窗口创建失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print("=" * 60)
    
    for i, result in enumerate(test_results, 1):
        print(f"{i}. {result}")
    
    # 判断测试是否成功
    critical_failures = [r for r in test_results if "失败" in r or "出错" in r]
    
    if not critical_failures:
        print("\n✅ 测试通过: 移除旧文件后应用程序功能正常")
        return True
    else:
        print("\n❌ 测试失败: 发现以下问题:")
        for failure in critical_failures:
            print(f"   - {failure}")
        return False


def main():
    """主函数"""
    success = test_app_functionality()
    
    if success:
        print("\n🎉 结论: 旧的时钟输出文件已成功移除")
        print("   现代化架构完全接管了时钟输出功能")
        print("\n📝 建议:")
        print("   1. 可以删除备份文件 ClkOutputsHandler.py.bak")
        print("   2. 继续移除其他旧的处理器文件")
    else:
        print("\n⚠️ 建议:")
        print("   1. 恢复备份文件进行进一步调试")
        print("   2. 检查现代化处理器的实现")


if __name__ == '__main__':
    main()
