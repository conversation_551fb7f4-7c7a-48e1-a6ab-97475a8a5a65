#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
进度条样式管理器
统一管理软件中所有进度条的样式，设置为绿色主题
"""

from PyQt5.QtWidgets import QProgressBar
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class ProgressBarStyleManager:
    """进度条样式管理器"""
    
    # 绿色主题样式
    GREEN_STYLE = """
        QProgressBar {
            border: 2px solid #C0C0C0;
            border-radius: 5px;
            background-color: #F0F0F0;
            text-align: center;
            font-weight: bold;
            color: #333333;
        }
        
        QProgressBar::chunk {
            background-color: qlineargradient(
                x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #7ED321,
                stop: 0.5 #5CB85C,
                stop: 1 #4CAF50
            );
            border-radius: 3px;
            margin: 1px;
        }
        
        QProgressBar:disabled {
            border: 2px solid #E0E0E0;
            background-color: #F8F8F8;
            color: #A0A0A0;
        }
        
        QProgressBar:disabled::chunk {
            background-color: #E0E0E0;
        }
    """
    
    # 高对比度绿色样式（用于重要操作）
    HIGH_CONTRAST_GREEN_STYLE = """
        QProgressBar {
            border: 2px solid #2E7D32;
            border-radius: 6px;
            background-color: #E8F5E8;
            text-align: center;
            font-weight: bold;
            color: #1B5E20;
            font-size: 12px;
        }
        
        QProgressBar::chunk {
            background-color: qlineargradient(
                x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #66BB6A,
                stop: 0.3 #4CAF50,
                stop: 0.7 #43A047,
                stop: 1 #388E3C
            );
            border-radius: 4px;
            margin: 1px;
        }
        
        QProgressBar:disabled {
            border: 2px solid #C8E6C9;
            background-color: #F1F8E9;
            color: #81C784;
        }
        
        QProgressBar:disabled::chunk {
            background-color: #C8E6C9;
        }
    """
    
    # 小尺寸进度条样式
    COMPACT_GREEN_STYLE = """
        QProgressBar {
            border: 1px solid #4CAF50;
            border-radius: 3px;
            background-color: #F0F0F0;
            text-align: center;
            font-weight: bold;
            color: #333333;
            font-size: 10px;
            max-height: 16px;
        }
        
        QProgressBar::chunk {
            background-color: qlineargradient(
                x1: 0, y1: 0, x2: 1, y2: 0,
                stop: 0 #81C784,
                stop: 0.5 #66BB6A,
                stop: 1 #4CAF50
            );
            border-radius: 2px;
            margin: 0px;
        }
    """
    
    @classmethod
    def apply_green_style(cls, progress_bar: QProgressBar, style_type: str = "default"):
        """为进度条应用绿色样式
        
        Args:
            progress_bar: QProgressBar实例
            style_type: 样式类型 ("default", "high_contrast", "compact")
        """
        try:
            if not isinstance(progress_bar, QProgressBar):
                logger.warning(f"传入的对象不是QProgressBar类型: {type(progress_bar)}")
                return False
            
            # 根据样式类型选择样式
            if style_type == "high_contrast":
                style = cls.HIGH_CONTRAST_GREEN_STYLE
            elif style_type == "compact":
                style = cls.COMPACT_GREEN_STYLE
            else:
                style = cls.GREEN_STYLE
            
            # 应用样式
            progress_bar.setStyleSheet(style)
            
            logger.debug(f"已为进度条应用绿色样式: {style_type}")
            return True
            
        except Exception as e:
            logger.error(f"应用进度条样式失败: {str(e)}")
            return False
    
    @classmethod
    def apply_green_style_to_multiple(cls, progress_bars: list, style_type: str = "default"):
        """为多个进度条应用绿色样式
        
        Args:
            progress_bars: QProgressBar实例列表
            style_type: 样式类型
        """
        success_count = 0
        
        for progress_bar in progress_bars:
            if cls.apply_green_style(progress_bar, style_type):
                success_count += 1
        
        logger.info(f"已为 {success_count}/{len(progress_bars)} 个进度条应用绿色样式")
        return success_count
    
    @classmethod
    def create_styled_progress_bar(cls, parent=None, style_type: str = "default", **kwargs):
        """创建已应用绿色样式的进度条
        
        Args:
            parent: 父控件
            style_type: 样式类型
            **kwargs: QProgressBar的其他参数
            
        Returns:
            QProgressBar: 已应用样式的进度条
        """
        try:
            # 创建进度条
            progress_bar = QProgressBar(parent)
            
            # 设置基本属性
            if 'minimum' in kwargs:
                progress_bar.setMinimum(kwargs['minimum'])
            if 'maximum' in kwargs:
                progress_bar.setMaximum(kwargs['maximum'])
            if 'value' in kwargs:
                progress_bar.setValue(kwargs['value'])
            if 'visible' in kwargs:
                progress_bar.setVisible(kwargs['visible'])
            
            # 应用绿色样式
            cls.apply_green_style(progress_bar, style_type)
            
            logger.debug(f"创建了带绿色样式的进度条: {style_type}")
            return progress_bar
            
        except Exception as e:
            logger.error(f"创建样式化进度条失败: {str(e)}")
            # 返回基本进度条作为备用
            return QProgressBar(parent)
    
    @classmethod
    def update_existing_progress_bars(cls, widget, style_type: str = "default"):
        """更新指定控件及其子控件中的所有进度条样式
        
        Args:
            widget: 要搜索的根控件
            style_type: 样式类型
        """
        try:
            # 查找所有QProgressBar
            progress_bars = widget.findChildren(QProgressBar)
            
            if not progress_bars:
                logger.debug("未找到进度条控件")
                return 0
            
            # 应用样式
            success_count = cls.apply_green_style_to_multiple(progress_bars, style_type)
            
            logger.info(f"在控件 {type(widget).__name__} 中更新了 {success_count} 个进度条样式")
            return success_count
            
        except Exception as e:
            logger.error(f"更新进度条样式失败: {str(e)}")
            return 0
    
    @classmethod
    def get_style_by_type(cls, style_type: str = "default"):
        """获取指定类型的样式字符串
        
        Args:
            style_type: 样式类型
            
        Returns:
            str: 样式字符串
        """
        if style_type == "high_contrast":
            return cls.HIGH_CONTRAST_GREEN_STYLE
        elif style_type == "compact":
            return cls.COMPACT_GREEN_STYLE
        else:
            return cls.GREEN_STYLE


# 便捷函数
def apply_green_progress_style(progress_bar: QProgressBar, style_type: str = "default"):
    """便捷函数：为进度条应用绿色样式"""
    return ProgressBarStyleManager.apply_green_style(progress_bar, style_type)


def create_green_progress_bar(parent=None, style_type: str = "default", **kwargs):
    """便捷函数：创建绿色样式的进度条"""
    return ProgressBarStyleManager.create_styled_progress_bar(parent, style_type, **kwargs)


def update_all_progress_bars(widget, style_type: str = "default"):
    """便捷函数：更新控件中所有进度条的样式"""
    return ProgressBarStyleManager.update_existing_progress_bars(widget, style_type)
