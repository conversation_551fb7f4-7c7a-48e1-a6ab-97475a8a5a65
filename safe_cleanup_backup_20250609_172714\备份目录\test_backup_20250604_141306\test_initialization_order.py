#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试初始化顺序和控件值设置
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_initialization_order():
    """测试初始化顺序和控件值设置"""
    try:
        from PyQt5.QtWidgets import QApplication
        
        # 创建QApplication
        app = QApplication(sys.argv)
        
        logger.info("=== 测试初始化顺序和控件值设置 ===")
        
        # 创建测试实例
        from ui.handlers.ModernClkinControlHandler import ModernClkinControlHandler
        
        # 手动创建实例，逐步观察初始化过程
        from core.services.register.RegisterManager import RegisterManager
        import json
        import os
        
        # 加载寄存器配置
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'lib', 'register.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        register_manager = RegisterManager(registers_config)
        
        # 创建实例但不调用初始化
        handler = ModernClkinControlHandler.__new__(ModernClkinControlHandler)
        
        # 手动调用父类初始化
        from ui.handlers.ModernBaseHandler import ModernBaseHandler
        ModernBaseHandler.__init__(handler, None, register_manager)
        
        # 设置窗口标题
        handler.setWindowTitle("时钟输入控制 (现代化版本)")
        
        # 初始化ComboBox选项映射
        handler._init_combobox_options()
        
        # 分频比默认值
        handler.clkin0_divider = 120
        handler.clkin1_divider = 120
        handler.clkin2_divider = 120
        
        # 频率默认值
        handler.clkin0 = 122.88
        handler.clkin1 = 122.88
        handler.clkin2 = 153.6
        
        # 创建UI实例
        from ui.forms.Ui_clkinControl import Ui_ClkinControl
        handler.ui = Ui_ClkinControl()
        handler.ui.setupUi(handler.content_widget)
        
        # 添加一个实例变量来存储上一次的值
        handler.previous_clkin_value = ""
        
        logger.info("=== 步骤1: UI创建完成，检查控件初始值 ===")
        
        # 检查分频比控件的初始值
        divider_controls = ["PLL1R0Div", "PLL1R1Div", "PLL1R2Div"]
        
        for control_name in divider_controls:
            if hasattr(handler.ui, control_name):
                control = getattr(handler.ui, control_name)
                logger.info(f"{control_name} 初始值: {control.value()}")
            else:
                logger.warning(f"控件 {control_name} 不存在")
        
        logger.info("=== 步骤2: 预设寄存器值 ===")
        
        # 调用预设寄存器值方法
        handler._pre_initialize_divider_registers()
        
        # 再次检查控件值
        for control_name in divider_controls:
            if hasattr(handler.ui, control_name):
                control = getattr(handler.ui, control_name)
                logger.info(f"{control_name} 预设后值: {control.value()}")
        
        logger.info("=== 步骤3: 调用基类_post_init ===")
        
        # 调用基类的后初始化
        handler._post_init()
        
        # 再次检查控件值
        for control_name in divider_controls:
            if hasattr(handler.ui, control_name):
                control = getattr(handler.ui, control_name)
                logger.info(f"{control_name} 基类初始化后值: {control.value()}")
        
        logger.info("=== 步骤4: 调用时钟输入特定初始化 ===")
        
        # 调用时钟输入特定的初始化
        handler._init_clkin_specific_config()
        
        # 最终检查控件值
        for control_name in divider_controls:
            if hasattr(handler.ui, control_name):
                control = getattr(handler.ui, control_name)
                logger.info(f"{control_name} 最终值: {control.value()}")
        
        logger.info("=== 步骤5: 检查寄存器值 ===")
        
        # 检查寄存器值
        register_addrs = ["0x63", "0x65", "0x67"]
        bit_names = ["PLL1_R0_DIV", "PLL1_R1_DIV", "PLL1_R2_DIV"]
        
        for i, (reg_addr, bit_name) in enumerate(zip(register_addrs, bit_names)):
            reg_value = register_manager.get_register_value(reg_addr)
            bit_value = register_manager.get_bit_field_value(reg_addr, bit_name)
            logger.info(f"寄存器 {reg_addr}: 值=0x{reg_value:04X}, 位字段{bit_name}={bit_value}")
        
        logger.info("\n=== 测试完成 ===")
        
    except Exception as e:
        logger.error(f"测试时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_initialization_order()
