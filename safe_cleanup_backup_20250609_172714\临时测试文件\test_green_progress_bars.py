#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试绿色进度条样式应用
验证所有进度条都使用绿色样式
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QProgressBar, QProgressDialog, QPushButton
from PyQt5.QtCore import QTimer
from ui.styles.ProgressBarStyleManager import apply_green_progress_style, apply_green_style_to_progress_dialog


class TestProgressBarsWindow(QMainWindow):
    """测试进度条样式的窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("绿色进度条样式测试")
        self.setGeometry(100, 100, 600, 400)
        
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 创建不同样式的进度条
        self.create_test_progress_bars(layout)
        
        # 创建测试按钮
        self.create_test_buttons(layout)
        
    def create_test_progress_bars(self, layout):
        """创建测试进度条"""
        # 默认样式进度条
        self.progress_default = QProgressBar()
        self.progress_default.setRange(0, 100)
        self.progress_default.setValue(50)
        apply_green_progress_style(self.progress_default, "default")
        layout.addWidget(self.progress_default)
        
        # 平滑样式进度条
        self.progress_smooth = QProgressBar()
        self.progress_smooth.setRange(0, 100)
        self.progress_smooth.setValue(75)
        apply_green_progress_style(self.progress_smooth, "smooth")
        layout.addWidget(self.progress_smooth)
        
        # 紧凑样式进度条
        self.progress_compact = QProgressBar()
        self.progress_compact.setRange(0, 100)
        self.progress_compact.setValue(25)
        apply_green_progress_style(self.progress_compact, "compact")
        layout.addWidget(self.progress_compact)
        
        # 动画进度条
        self.progress_animated = QProgressBar()
        self.progress_animated.setRange(0, 100)
        self.progress_animated.setValue(0)
        apply_green_progress_style(self.progress_animated, "default")
        layout.addWidget(self.progress_animated)
        
        # 启动动画定时器
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self.update_animated_progress)
        self.animation_timer.start(100)
        self.animation_value = 0
        self.animation_direction = 1
        
    def create_test_buttons(self, layout):
        """创建测试按钮"""
        # 测试进度对话框按钮
        self.dialog_button = QPushButton("测试进度对话框")
        self.dialog_button.clicked.connect(self.test_progress_dialog)
        layout.addWidget(self.dialog_button)
        
    def update_animated_progress(self):
        """更新动画进度条"""
        self.animation_value += self.animation_direction * 2
        if self.animation_value >= 100:
            self.animation_direction = -1
        elif self.animation_value <= 0:
            self.animation_direction = 1
            
        self.progress_animated.setValue(self.animation_value)
        
    def test_progress_dialog(self):
        """测试进度对话框"""
        dialog = QProgressDialog("正在测试进度对话框...", "取消", 0, 100, self)
        dialog.setWindowTitle("测试进度对话框")
        
        # 应用绿色样式
        apply_green_style_to_progress_dialog(dialog, "default")
        
        # 模拟进度更新
        timer = QTimer()
        progress_value = [0]
        
        def update_progress():
            progress_value[0] += 5
            dialog.setValue(progress_value[0])
            if progress_value[0] >= 100:
                timer.stop()
                dialog.close()
                
        timer.timeout.connect(update_progress)
        timer.start(200)
        
        dialog.show()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestProgressBarsWindow()
    window.show()
    
    print("绿色进度条样式测试窗口已启动")
    print("测试内容:")
    print("1. 默认样式进度条 (50%)")
    print("2. 平滑样式进度条 (75%)")
    print("3. 紧凑样式进度条 (25%)")
    print("4. 动画进度条 (自动变化)")
    print("5. 点击按钮测试进度对话框")
    print("\n所有进度条都应该显示为绿色")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
