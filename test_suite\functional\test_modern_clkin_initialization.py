#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试现代化时钟输入控制初始化
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_modern_clkin_initialization():
    """测试现代化时钟输入控制初始化"""
    try:
        from PyQt5.QtWidgets import QApplication
        
        # 创建QApplication
        app = QApplication(sys.argv)
        
        logger.info("=== 开始测试现代化时钟输入控制初始化 ===")
        
        # 创建测试实例
        from ui.handlers.ModernClkinControlHandler import ModernClkinControlHandler
        handler = ModernClkinControlHandler.create_for_testing()
        
        # 显示窗口
        handler.show()
        
        # 检查分频比控件的值
        logger.info("=== 检查分频比控件初始化值 ===")
        
        divider_controls = ["PLL1R0Div", "PLL1R1Div", "PLL1R2Div"]
        expected_values = [120, 120, 150]
        
        for i, control_name in enumerate(divider_controls):
            if hasattr(handler.ui, control_name):
                control = getattr(handler.ui, control_name)
                actual_value = control.value()
                expected_value = expected_values[i]
                
                logger.info(f"{control_name}: 期望值={expected_value}, 实际值={actual_value}")
                
                if actual_value == expected_value:
                    logger.info(f"✓ {control_name} 初始化正确")
                else:
                    logger.error(f"✗ {control_name} 初始化错误！期望{expected_value}，实际{actual_value}")
            else:
                logger.warning(f"控件 {control_name} 不存在")
        
        # 检查时钟频率控件的值
        logger.info("=== 检查时钟频率控件初始化值 ===")
        
        freq_controls = [
            ("lineEditClkin0", "122.88"),
            ("lineEditClkin1", "122.88"), 
            ("lineEditClkin2Oscout", "153.6")
        ]
        
        for control_name, expected_text in freq_controls:
            if hasattr(handler.ui, control_name):
                control = getattr(handler.ui, control_name)
                actual_text = control.text()
                
                logger.info(f"{control_name}: 期望值='{expected_text}', 实际值='{actual_text}'")
                
                if actual_text == expected_text:
                    logger.info(f"✓ {control_name} 初始化正确")
                else:
                    logger.error(f"✗ {control_name} 初始化错误！期望'{expected_text}'，实际'{actual_text}'")
            else:
                logger.warning(f"控件 {control_name} 不存在")
        
        # 检查ComboBox控件的初始化
        logger.info("=== 检查ComboBox控件初始化 ===")
        
        combo_controls = ["CLKinSelManual", "CLKin0Demux", "CLKin1Demux"]
        
        for control_name in combo_controls:
            if hasattr(handler.ui, control_name):
                control = getattr(handler.ui, control_name)
                item_count = control.count()
                current_index = control.currentIndex()
                current_text = control.currentText()
                
                logger.info(f"{control_name}: 选项数={item_count}, 当前索引={current_index}, 当前文本='{current_text}'")
                
                if item_count > 0:
                    logger.info(f"✓ {control_name} 有选项")
                else:
                    logger.error(f"✗ {control_name} 没有选项！")
            else:
                logger.warning(f"控件 {control_name} 不存在")
        
        # 检查寄存器管理器状态
        logger.info("=== 检查寄存器管理器状态 ===")
        
        if hasattr(handler, 'register_manager') and handler.register_manager:
            logger.info("✓ RegisterManager已设置")
            
            # 检查控件映射表
            if hasattr(handler, 'widget_register_map'):
                map_count = len(handler.widget_register_map)
                logger.info(f"控件映射表包含 {map_count} 个映射")
                
                # 显示分频比控件的映射信息
                for control_name in divider_controls:
                    if control_name in handler.widget_register_map:
                        widget_info = handler.widget_register_map[control_name]
                        reg_addr = widget_info.get("register_addr", "未知")
                        logger.info(f"  {control_name} -> 寄存器 {reg_addr}")
                    else:
                        logger.warning(f"  {control_name} 未在映射表中找到")
            else:
                logger.error("✗ 控件映射表未设置")
        else:
            logger.error("✗ RegisterManager未设置")
        
        logger.info("=== 测试完成 ===")
        
        # 保持窗口打开以便观察
        logger.info("窗口已打开，请检查UI显示。按Ctrl+C退出。")
        
        # 运行事件循环
        sys.exit(app.exec_())
        
    except Exception as e:
        logger.error(f"测试时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_modern_clkin_initialization()
