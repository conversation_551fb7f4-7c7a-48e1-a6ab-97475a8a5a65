#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
现代化的模式设置处理器
使用ModernBaseHandler作为基类，重构自原SetModesHandler
主要功能：通过按钮设置不同的PLL模式预设
"""

from PyQt5 import QtCore
from ui.handlers.ModernBaseHandler import ModernBaseHandler
from ui.forms.Ui_setModes import Ui_setModes
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class ModernSetModesHandler(ModernBaseHandler):
    """现代化的模式设置处理器"""

    # 添加模式变化信号
    mode_changed = QtCore.pyqtSignal(str)

    def __init__(self, parent=None, register_manager=None, **kwargs):
        """初始化现代化模式设置处理器

        Args:
            parent: 父窗口
            register_manager: RegisterManager实例
            **kwargs: 其他参数（如register_repo等，用于兼容性）
        """
        super().__init__(parent, register_manager, **kwargs)

        # 设置窗口标题
        self.setWindowTitle("模式设置 (现代化版本)")

        # 创建UI实例
        self.ui = Ui_setModes()
        self.ui.setupUi(self.content_widget)

        # 连接按钮信号
        self._connect_mode_buttons()

        # 初始化模式跟踪
        self._last_set_mode = None

        logger.info("现代化模式设置处理器初始化完成")
    
    def _connect_mode_buttons(self):
        """连接模式按钮信号"""
        try:
            self.ui.pBtSetDualLoop.clicked.connect(lambda: self.set_mode("DualLoop"))
            self.ui.pBtSetSingleLoop.clicked.connect(lambda: self.set_mode("SingleLoop"))
            self.ui.pBtSetSingleLoop0Dealy.clicked.connect(lambda: self.set_mode("SingleLoop0Dealy"))
            self.ui.pBtSetDualLoop0DealyCascaded.clicked.connect(lambda: self.set_mode("DualLoop0DealyCascaded"))
            self.ui.pBtSetDualLoop0DealyNested.clicked.connect(lambda: self.set_mode("DualLoop0DealyNested"))
            self.ui.pBtSetDualLoop0DealyNestedandCasc.clicked.connect(lambda: self.set_mode("DualLoop0DealyNestedandCasc"))
            self.ui.pBtSetDistributionFin1.clicked.connect(lambda: self.set_mode("DistributionFin1"))
            logger.info("模式按钮信号连接完成")
        except Exception as e:
            logger.error(f"连接模式按钮信号时出错: {str(e)}")

    def on_register_value_changed(self, widget_name, reg_addr, reg_value):
        """处理寄存器值变化"""
        logger.info(f"模式设置: 寄存器 {reg_addr} 值变化 (控件: {widget_name}) -> 0x{reg_value:04X}")

        # 模式设置界面主要是按钮操作，不需要处理控件值变化

    def on_global_register_update(self, reg_addr, reg_value):
        """处理全局寄存器更新"""
        logger.debug(f"模式设置: 收到全局更新 {reg_addr} = 0x{reg_value:04X}")

        # 可以在这里监控模式相关寄存器的变化

    def set_mode(self, mode_name):
        """设置特定模式

        Args:
            mode_name: 模式名称
        """
        try:
            logger.info(f"正在设置模式: {mode_name}")

            # 获取模式配置
            mode_settings = self._get_mode_settings(mode_name)
            if not mode_settings:
                logger.error(f"未找到模式 {mode_name} 的配置")
                return

            # 应用模式设置
            success_count = 0
            total_count = 0

            for reg_addr, bit_settings in mode_settings.items():
                for bit_name, bit_value in bit_settings.items():
                    total_count += 1

                    # 通过RegisterManager设置位字段值
                    try:
                        # RegisterManager.set_bit_field_value返回更新后的寄存器值(int)，不是boolean
                        updated_value = self.register_manager.set_bit_field_value(reg_addr, bit_name, bit_value)
                        success_count += 1
                        logger.debug(f"设置 {reg_addr}.{bit_name} = {bit_value}, 寄存器值 = 0x{updated_value:04X}")
                    except Exception as e:
                        logger.warning(f"设置 {reg_addr}.{bit_name} = {bit_value} 失败: {str(e)}")

            if success_count == total_count:
                logger.info(f"模式 {mode_name} 设置完成 ({success_count}/{total_count})")

                # 记录最后设置的模式
                self._last_set_mode = mode_name

                # 检查是否需要自动写入到芯片
                if self._should_auto_write_to_chip():
                    logger.info("模式设置完成后自动写入寄存器到芯片...")
                    self._write_mode_registers_to_chip(mode_settings)

                # 发送模式变化信号
                self.mode_changed.emit(mode_name)
                logger.info(f"已发送模式变化信号: {mode_name}")
            else:
                logger.warning(f"模式 {mode_name} 部分设置成功 ({success_count}/{total_count})")

        except Exception as e:
            logger.error(f"设置模式时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def _get_mode_settings(self, mode_name):
        """获取模式设置配置

        Args:
            mode_name: 模式名称

        Returns:
            dict: 模式配置字典
        """
        # 寄存器设置映射（从原SetModesHandler迁移）
        register_settings = {
            "DualLoop": {
                "0x4F": {
                    "PLL1_NCLK_MUX[1:0]": 0b00,  # 4:3位
                    "PLL2_NCLK_MUX": 0,     # 5位
                    "PLL2_RCLK_MUX": 0,     # 7位
                    "FB_MUX_EN": 0,         # 0位
                    "FB_MUX[1:0]": 0b00          # 2:1位
                },
                "0x50": {
                    "OSCin_PD": 0,          # 4位
                    "VCO_LDO_PD": 0,        # 6位
                    "VCO_PD": 0,            # 5位
                    "PLL1_PD": 0            # 7位
                },
                "0x57": {
                    "CLKin0_OUT_MUX": 0b10, # 1:0位
                    "CLKin1_OUT_MUX": 0b10  # 3:2位
                },
                "0x48": {
                    "VCO_MUX": 0b01         # 6:5位
                },
                "0x83": {
                    "PLL2_PRE_PD": 0,       # 6位
                    "PLL2_PD": 0            # 5位
                }
            },
            "SingleLoop": {
                "0x4F": {
                    "PLL1_NCLK_MUX[1:0]": 0b00,
                    "PLL2_NCLK_MUX": 0,
                    "PLL2_RCLK_MUX": 0,
                    "FB_MUX_EN": 0,
                    "FB_MUX[1:0]": 0b00
                },
                "0x50": {
                    "OSCin_PD": 0,
                    "VCO_LDO_PD": 0,
                    "VCO_PD": 0,
                    "PLL1_PD": 1            # 不同：PLL1关闭
                },
                "0x57": {
                    "CLKin0_OUT_MUX": 0b10,
                    "CLKin1_OUT_MUX": 0b10
                },
                "0x48": {
                    "VCO_MUX": 0b01
                },
                "0x83": {
                    "PLL2_PRE_PD": 0,
                    "PLL2_PD": 0
                }
            },
            "SingleLoop0Dealy": {
                "0x4F": {
                    "PLL1_NCLK_MUX[1:0]": 0b00,
                    "PLL2_NCLK_MUX": 1,     # 不同：使用反馈
                    "PLL2_RCLK_MUX": 0,
                    "FB_MUX_EN": 1,         # 不同：启用反馈多路复用
                    "FB_MUX[1:0]": 0b00
                },
                "0x50": {
                    "OSCin_PD": 0,
                    "VCO_LDO_PD": 0,
                    "VCO_PD": 0,
                    "PLL1_PD": 1            # PLL1关闭
                },
                "0x57": {
                    "CLKin0_OUT_MUX": 0b10,
                    "CLKin1_OUT_MUX": 0b10
                },
                "0x48": {
                    "VCO_MUX": 0b01
                },
                "0x83": {
                    "PLL2_PRE_PD": 0,
                    "PLL2_PD": 0
                }
            },
            "DualLoop0DealyCascaded": {
                "0x4F": {
                    "PLL1_NCLK_MUX[1:0]": 0b00,
                    "PLL2_NCLK_MUX": 1,     # 使用反馈
                    "PLL2_RCLK_MUX": 0,
                    "FB_MUX_EN": 1,         # 启用反馈多路复用
                    "FB_MUX[1:0]": 0b00
                },
                "0x50": {
                    "OSCin_PD": 0,
                    "VCO_LDO_PD": 0,
                    "VCO_PD": 0,
                    "PLL1_PD": 0            # PLL1开启
                },
                "0x57": {
                    "CLKin0_OUT_MUX": 0b10,
                    "CLKin1_OUT_MUX": 0b10
                },
                "0x48": {
                    "VCO_MUX": 0b01
                },
                "0x83": {
                    "PLL2_PRE_PD": 0,
                    "PLL2_PD": 0
                }
            },
            "DualLoop0DealyNested": {
                "0x4F": {
                    "PLL1_NCLK_MUX[1:0]": 0b10,  # 不同：PLL1使用PLL2 Prescaler
                    "PLL2_NCLK_MUX": 0,
                    "PLL2_RCLK_MUX": 0,
                    "FB_MUX_EN": 1,         # 启用反馈多路复用
                    "FB_MUX[1:0]": 0b00
                },
                "0x50": {
                    "OSCin_PD": 0,
                    "VCO_LDO_PD": 0,
                    "VCO_PD": 0,
                    "PLL1_PD": 0            # PLL1开启
                },
                "0x57": {
                    "CLKin0_OUT_MUX": 0b10,
                    "CLKin1_OUT_MUX": 0b10
                },
                "0x48": {
                    "VCO_MUX": 0b01
                },
                "0x83": {
                    "PLL2_PRE_PD": 0,
                    "PLL2_PD": 0
                }
            },
            "DualLoop0DealyNestedandCasc": {
                "0x4F": {
                    "PLL1_NCLK_MUX[1:0]": 0b10,  # PLL1使用PLL2 Prescaler
                    "PLL2_NCLK_MUX": 1,     # PLL2使用反馈
                    "PLL2_RCLK_MUX": 0,
                    "FB_MUX_EN": 1,         # 启用反馈多路复用
                    "FB_MUX[1:0]": 0b00
                },
                "0x50": {
                    "OSCin_PD": 0,
                    "VCO_LDO_PD": 0,
                    "VCO_PD": 0,
                    "PLL1_PD": 0            # PLL1开启
                },
                "0x57": {
                    "CLKin0_OUT_MUX": 0b10,
                    "CLKin1_OUT_MUX": 0b10
                },
                "0x48": {
                    "VCO_MUX": 0b01
                },
                "0x83": {
                    "PLL2_PRE_PD": 0,
                    "PLL2_PD": 0
                }
            },
            "DistributionFin1": {
                "0x4F": {
                    "PLL1_NCLK_MUX[1:0]": 0b00,
                    "PLL2_NCLK_MUX": 0,
                    "PLL2_RCLK_MUX": 0,
                    "FB_MUX_EN": 0,
                    "FB_MUX[1:0]": 0b00
                },
                "0x50": {
                    "OSCin_PD": 1,          # 不同：OSCin关闭
                    "VCO_LDO_PD": 1,        # 不同：VCO LDO关闭
                    "VCO_PD": 1,            # 不同：VCO关闭
                    "PLL1_PD": 1            # PLL1关闭
                },
                "0x57": {
                    "CLKin0_OUT_MUX": 0b10,
                    "CLKin1_OUT_MUX": 0b10
                },
                "0x48": {
                    "VCO_MUX": 0b10         # 不同：VCO_MUX设置为CLKin1
                },
                "0x83": {
                    "PLL2_PRE_PD": 1,       # 不同：PLL2预分频器关闭
                    "PLL2_PD": 1            # 不同：PLL2关闭
                }
            }
        }

        return register_settings.get(mode_name)

    def _should_auto_write_to_chip(self):
        """检查是否应该自动写入到芯片

        Returns:
            bool: 是否应该自动写入
        """
        try:
            # 检查是否在测试环境中
            if self._is_in_test_environment():
                logger.debug("模式设置: 测试环境中，跳过自动写入")
                return False

            # 获取主窗口
            main_window = self._get_main_window()
            if not main_window:
                logger.debug("模式设置: 无法获取主窗口，跳过自动写入")
                return False

            # 检查自动写入模式设置
            auto_write_mode = getattr(main_window, 'auto_write_mode', False)
            logger.debug(f"模式设置: 自动写入模式状态: {auto_write_mode}")

            return auto_write_mode

        except Exception as e:
            logger.error(f"模式设置: 检查自动写入状态时出错: {str(e)}")
            return False

    def _write_mode_registers_to_chip(self, mode_settings):
        """将模式相关的寄存器写入到芯片

        Args:
            mode_settings: 模式设置字典
        """
        try:
            main_window = self._get_main_window()
            if not main_window:
                logger.warning("模式设置: 无法获取主窗口，跳过写入操作")
                return

            # 检查是否有寄存器操作服务
            if not hasattr(main_window, 'register_service'):
                logger.warning("模式设置: 主窗口没有 register_service，跳过写入操作")
                return

            # 收集需要写入的寄存器
            registers_to_write = {}
            for reg_addr in mode_settings.keys():
                # 获取当前寄存器的完整值
                current_value = self.register_manager.get_register_value(reg_addr)
                registers_to_write[reg_addr] = current_value

            # 批量写入寄存器
            logger.info(f"模式设置: 开始写入 {len(registers_to_write)} 个寄存器到芯片")
            for reg_addr, reg_value in registers_to_write.items():
                try:
                    result = main_window.register_service.write_register(reg_addr, reg_value)
                    logger.info(f"模式设置: 寄存器 {reg_addr} = 0x{reg_value:04X} 写入完成，结果: {result}")
                except Exception as e:
                    logger.error(f"模式设置: 写入寄存器 {reg_addr} 时出错: {str(e)}")

            logger.info("模式设置: 所有寄存器写入操作完成")

        except Exception as e:
            logger.error(f"模式设置: 写入寄存器到芯片时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def _is_in_test_environment(self):
        """检查是否在测试环境中"""
        try:
            # 检查是否有测试相关的模块被导入
            import sys
            return any('test' in module_name.lower() for module_name in sys.modules.keys())
        except:
            return False

    def _get_main_window(self):
        """获取主窗口实例"""
        try:
            # 尝试从父级获取主窗口
            if hasattr(self, 'main_window'):
                return self.main_window
            elif hasattr(self, 'parent') and hasattr(self.parent, 'main_window'):
                return self.parent.main_window
            elif hasattr(self, 'parent') and hasattr(self.parent, 'parent'):
                return self.parent.parent
            else:
                return None
        except:
            return None

    # === 公共接口方法 ===

    def get_current_status(self):
        """获取当前状态

        Returns:
            dict: 当前状态信息
        """
        try:
            status = {}

            if self.register_manager:
                # 获取关键寄存器状态
                status["pll1_enabled"] = not self.register_manager.get_bit_field_value("0x50", "PLL1_PD")
                status["pll2_enabled"] = not self.register_manager.get_bit_field_value("0x83", "PLL2_PD")
                status["vco_enabled"] = not self.register_manager.get_bit_field_value("0x50", "VCO_PD")

            return status

        except Exception as e:
            logger.error(f"获取状态时出错: {str(e)}")
            return {}
    
    @classmethod
    def create_for_testing(cls, parent=None):
        """创建测试实例"""
        try:
            # 创建模拟的RegisterManager
            from core.services.register.RegisterManager import RegisterManager
            import json
            import os
            
            # 加载寄存器配置
            config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'lib', 'register.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                registers_config = json.load(f)
            
            register_manager = RegisterManager(registers_config)
            
            # 创建实例
            instance = cls(parent, register_manager)
            
            logger.info("创建现代化SetModesHandler测试实例成功")
            return instance
            
        except Exception as e:
            logger.error(f"创建测试实例时出错: {str(e)}")
            raise


if __name__ == "__main__":
    import sys
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    # 创建测试实例
    handler = ModernSetModesHandler.create_for_testing()
    handler.show()
    
    sys.exit(app.exec_())
