#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试控件映射问题
详细检查为什么控件映射构建失败
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_widget_mapping():
    """调试控件映射问题"""
    try:
        print("=" * 60)
        print("调试控件映射问题")
        print("=" * 60)
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 导入必要的模块
        from core.services.register.RegisterManager import RegisterManager
        from ui.forms.Ui_clkinControl import Ui_ClkinControl
        import json
        
        print("1. 加载寄存器配置...")
        # 加载寄存器配置
        config_path = os.path.join(os.path.dirname(__file__), 'lib', 'register.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            registers_config = json.load(f)
        
        print(f"   ✓ 加载了 {len(registers_config)} 个寄存器配置")
        
        print("2. 创建RegisterManager...")
        register_manager = RegisterManager(registers_config)
        print(f"   ✓ 创建了RegisterManager，包含 {len(register_manager.register_objects)} 个寄存器对象")
        
        print("3. 检查特定寄存器的控件映射...")
        # 检查0x5B寄存器（losEn）
        widget_map_5B = register_manager.get_widget_register_mapping("0x5B")
        print(f"   寄存器0x5B的控件映射: {widget_map_5B}")
        
        # 检查0x02寄存器（powerDown）
        widget_map_02 = register_manager.get_widget_register_mapping("0x02")
        print(f"   寄存器0x02的控件映射: {widget_map_02}")
        
        print("4. 创建UI实例...")
        # 创建UI实例
        from PyQt5 import QtWidgets
        content_widget = QtWidgets.QWidget()
        ui = Ui_ClkinControl()
        ui.setupUi(content_widget)
        print("   ✓ UI实例创建成功")
        
        print("5. 检查UI中的控件...")
        # 检查UI中的控件
        all_widgets = []
        for attr_name in dir(ui):
            if not attr_name.startswith('_') and not callable(getattr(ui, attr_name)):
                try:
                    attr = getattr(ui, attr_name)
                    if isinstance(attr, QtWidgets.QWidget):
                        all_widgets.append(attr_name)
                        print(f"   发现控件: {attr_name} ({type(attr).__name__})")
                except Exception as e:
                    print(f"   检查属性 {attr_name} 时出错: {str(e)}")
        
        print(f"\n   总共发现 {len(all_widgets)} 个控件")
        
        print("6. 检查特定控件是否存在...")
        target_widgets = ["losEn", "powerDown", "LOSTimeout"]
        for widget_name in target_widgets:
            if widget_name in all_widgets:
                widget = getattr(ui, widget_name)
                print(f"   ✓ 控件 {widget_name} 存在: {type(widget).__name__}")
            else:
                print(f"   ❌ 控件 {widget_name} 不存在")
        
        print("7. 手动构建控件映射...")
        widget_register_map = {}
        
        # 为每个寄存器构建映射
        for addr in register_manager.register_objects.keys():
            widget_map = register_manager.get_widget_register_mapping(addr)
            for widget_name, widget_info in widget_map.items():
                if widget_name in all_widgets:
                    widget_register_map[widget_name] = widget_info
                    print(f"   ✓ 映射控件 {widget_name} 到寄存器 {addr}")
                else:
                    print(f"   ❌ 寄存器 {addr} 的控件 {widget_name} 在UI中未找到")
        
        print(f"\n   成功构建 {len(widget_register_map)} 个控件映射")
        
        print("8. 详细检查映射结果...")
        for widget_name, widget_info in widget_register_map.items():
            print(f"   {widget_name} -> {widget_info['register_addr']} ({widget_info['widget_type']})")
        
        print("\n" + "=" * 60)
        if len(widget_register_map) > 0:
            print("🎉 控件映射构建成功！")
        else:
            print("💥 控件映射构建失败！")
        print("=" * 60)
        
        app.quit()
        return len(widget_register_map) > 0
        
    except Exception as e:
        print(f"调试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_widget_mapping()
    sys.exit(0 if success else 1)
