#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
架构重构后的清理脚本
清理重构后不再使用的文件和目录
"""

import os
import shutil
import json
from datetime import datetime
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent
CLEANUP_BACKUP_DIR = PROJECT_ROOT / f'cleanup_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}'

class ArchitectureCleanup:
    """架构清理工具"""
    
    def __init__(self):
        self.cleanup_report = {
            'timestamp': datetime.now().isoformat(),
            'categories': {},
            'total_files_removed': 0,
            'total_dirs_removed': 0,
            'backup_location': str(CLEANUP_BACKUP_DIR)
        }
    
    def create_backup_dir(self):
        """创建备份目录"""
        CLEANUP_BACKUP_DIR.mkdir(exist_ok=True)
        print(f"📁 创建备份目录: {CLEANUP_BACKUP_DIR}")
    
    def backup_and_remove_files(self, file_list, category_name, base_path=PROJECT_ROOT):
        """备份并删除文件列表"""
        print(f"\n🧹 清理 {category_name}...")
        
        removed_files = []
        failed_files = []
        
        for file_path in file_list:
            full_path = base_path / file_path if isinstance(file_path, str) else file_path
            
            if full_path.exists():
                try:
                    # 创建备份
                    backup_path = CLEANUP_BACKUP_DIR / category_name / full_path.name
                    backup_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(full_path, backup_path)
                    
                    # 删除原文件
                    if full_path.is_file():
                        full_path.unlink()
                    else:
                        shutil.rmtree(full_path)
                    
                    removed_files.append(str(full_path.relative_to(PROJECT_ROOT)))
                    print(f"  ✅ 删除: {full_path.relative_to(PROJECT_ROOT)}")
                    
                except Exception as e:
                    failed_files.append(f"{full_path.relative_to(PROJECT_ROOT)}: {str(e)}")
                    print(f"  ❌ 删除失败: {full_path.relative_to(PROJECT_ROOT)} - {e}")
            else:
                print(f"  ⚠️  文件不存在: {file_path}")
        
        self.cleanup_report['categories'][category_name] = {
            'removed_files': removed_files,
            'failed_files': failed_files,
            'count': len(removed_files)
        }
        self.cleanup_report['total_files_removed'] += len(removed_files)
        
        print(f"  📊 {category_name}: 删除了 {len(removed_files)} 个文件")
        return len(removed_files)
    
    def cleanup_backup_directories(self):
        """清理备份目录"""
        backup_dirs = [
            'debug_backup_20250604_141758',
            'test_backup_20250604_141306',
            'md_backup_20250604_142235'
        ]
        
        return self.backup_and_remove_files(backup_dirs, "备份目录")
    
    def cleanup_build_cache(self):
        """清理构建缓存"""
        cache_items = [
            '__pycache__',
            'build',
            'dist/log',  # 保留dist目录但清理日志
        ]
        
        # 清理所有__pycache__目录
        pycache_dirs = list(PROJECT_ROOT.rglob('__pycache__'))
        cache_items.extend(pycache_dirs)
        
        return self.backup_and_remove_files(cache_items, "构建缓存")
    
    def cleanup_temporary_files(self):
        """清理临时文件"""
        temp_files = [
            'build.spec',  # 旧的spec文件，新的在packaging目录
            'auto_enable_drag_dock.py',
            'debug_drag_dock.py',
            'debug_drag_release.py',
            'demo_layout_improvements.py',
            'diagnose_mouse_events.py',
            'disable_drag_dock.py',
            'enable_drag_dock.py',
            'fix_dock_blank_area.py',
            'fix_toolbar_tools.py',
            'quick_dock_test.py',
            'quick_drag_test.py',
            'simple_drag_test.py',
            'simple_scroll_test.py',
            'test_auto_dock.py',
            'test_balanced_layout.py',
            'test_com_port_layout_fix.py',
            'test_complete_drag_dock.py',
            'test_complete_resource_cleanup.py',
            'test_config_effects.py',
            'test_coordinate_fix.py',
            'test_current_issues.py',
            'test_drag_dock.py',
            'test_drag_dock_functionality.py',
            'test_drag_dock_quick.py',
            'test_drag_dock_simple.py',
            'test_drag_release_final.py',
            'test_final_drag_dock.py',
            'test_fixed_drag_dock.py',
            'test_green_progress_bars.py',
            'test_menu_state_fix.py',
            'test_optimized_drag_dock.py',
            'test_output.log',
            'test_pll_scroll.py',
            'test_plugin_dock_fix.py',
            'test_plugin_tab_close_fix.py',
            'test_progress_bar_styles.py',
            'test_real_drag_dock.py',
            'test_runtime_error_fix.py',
            'test_scroll_functionality.py',
            'test_tab_cleanup.py',
            'test_ui_layout_fix.py',
            'test_undock_fix.py',
            'test_window_resize.py',
            'test_window_validity_fix.py'
        ]
        
        return self.backup_and_remove_files(temp_files, "临时文件")
    
    def cleanup_legacy_handlers(self):
        """清理传统处理器文件（谨慎操作）"""
        # 注意：这些文件可能仍在作为回退方案使用
        # 只清理确认不再使用的文件
        legacy_files = [
            'ui/handlers/PLLHandler.py.backup_20250603_132234',  # 备份文件可以安全删除
        ]
        
        # 检查是否有其他已确认废弃的处理器
        handlers_dir = PROJECT_ROOT / 'ui' / 'handlers'
        if handlers_dir.exists():
            # 查找.backup文件
            backup_files = list(handlers_dir.glob('*.backup_*'))
            legacy_files.extend([f.relative_to(PROJECT_ROOT) for f in backup_files])
        
        return self.backup_and_remove_files(legacy_files, "传统处理器备份")
    
    def cleanup_redundant_docs(self):
        """清理冗余文档"""
        redundant_docs = [
            'DRAG_DOCK_FIX_SUMMARY.md',
            '专利申请_嵌入式设备寄存器配置的事件驱动管理系统.md',
            '分离窗口修复报告.md',
            '寄存器表格修改四动作实现总结.md',
            '寄存器表格修改重复调用完整解决方案.md',
            '寄存器表格修改重复调用风险修复总结.md',
            '性能监控器使用说明.md',
            '性能监控插件闪退修复总结.md',
            '拖拽停靠功能修复总结报告.md',
            '标签页资源清理修复报告.md',
            '窗口大小自适应功能报告.md',
            '解决方案总结.md'
        ]
        
        return self.backup_and_remove_files(redundant_docs, "冗余文档")

    def cleanup_old_packaging_files(self):
        """清理旧的打包文件（已移动到packaging目录）"""
        old_packaging_files = [
            'packaging/FSJ04832_RegisterTool.spec',  # 旧的spec文件
        ]

        return self.backup_and_remove_files(old_packaging_files, "旧打包文件")

    def cleanup_event_bus_duplicate(self):
        """清理重复的event_bus目录"""
        # 检查是否有重复的event_bus目录
        event_bus_dirs = [
            'event_bus'  # 根目录下的event_bus，应该使用core/event_bus
        ]

        return self.backup_and_remove_files(event_bus_dirs, "重复事件总线")

    def generate_cleanup_report(self):
        """生成清理报告"""
        report_path = PROJECT_ROOT / 'docs' / 'cleanup' / 'ARCHITECTURE_CLEANUP_REPORT.md'
        report_path.parent.mkdir(parents=True, exist_ok=True)

        total_removed = self.cleanup_report['total_files_removed']

        report_content = f"""# 架构重构后清理报告

## 清理时间
{self.cleanup_report['timestamp']}

## 清理总结
- **总计删除文件**: {total_removed} 个
- **备份位置**: `{self.cleanup_report['backup_location']}`

## 清理分类详情

"""

        for category, details in self.cleanup_report['categories'].items():
            report_content += f"### {category}\n"
            report_content += f"- **删除文件数**: {details['count']} 个\n"

            if details['removed_files']:
                report_content += "- **删除的文件**:\n"
                for file in details['removed_files']:
                    report_content += f"  - `{file}`\n"

            if details['failed_files']:
                report_content += "- **删除失败的文件**:\n"
                for file in details['failed_files']:
                    report_content += f"  - `{file}`\n"

            report_content += "\n"

        report_content += f"""## 清理效果

### 清理前后对比
- **清理前**: 项目包含大量重构过程中的临时文件、备份文件和废弃组件
- **清理后**: 项目结构更加清晰，只保留必要的文件和组件

### 保留的重要文件
- 所有现代化处理器 (`ui/handlers/Modern*.py`)
- 核心服务和管理器 (`core/`, `ui/managers/`)
- 配置文件和资源文件
- 测试套件 (`test_suite/`)
- 打包系统 (`packaging/`)

### 架构优化效果
1. **文件数量减少**: 删除了 {total_removed} 个不必要的文件
2. **目录结构清晰**: 移除了重复和临时目录
3. **维护性提升**: 减少了代码库的复杂性
4. **存储空间优化**: 清理了构建缓存和备份文件

## 后续建议

### 1. 传统处理器清理
考虑在系统稳定运行一段时间后，进一步清理传统处理器：
- `ui/handlers/SetModesHandler.py`
- `ui/handlers/ClkinControlHandler.py`
- `ui/handlers/ClkOutputsHandler.py`
- `ui/handlers/RegisterTableHandler.py`
- `ui/handlers/RegisterIOHandler.py`
- `ui/handlers/RegisterTreeHandler.py`
- `ui/handlers/UIEventHandler.py`

### 2. 回退机制简化
当现代化处理器完全稳定后，可以考虑：
- 移除 `InitializationManager` 中的传统处理器回退逻辑
- 简化 `ToolWindowFactory` 的回退机制
- 清理相关的导入和依赖

### 3. 测试覆盖
- 确保所有现代化组件都有充分的测试覆盖
- 添加回归测试防止功能退化
- 定期运行完整的测试套件

## 安全性说明

所有删除的文件都已备份到 `{self.cleanup_report['backup_location']}`，如果需要恢复任何文件，可以从备份目录中找到。

---

**清理完成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**清理状态**: ✅ 成功
**备份状态**: ✅ 已备份
"""

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)

        print(f"📄 清理报告已生成: {report_path}")

    def run_cleanup(self):
        """执行完整的清理流程"""
        print("🚀 开始架构重构后的清理工作...")
        print("=" * 60)

        # 创建备份目录
        self.create_backup_dir()

        # 执行各类清理
        cleanup_tasks = [
            ("备份目录", self.cleanup_backup_directories),
            ("构建缓存", self.cleanup_build_cache),
            ("临时文件", self.cleanup_temporary_files),
            ("传统处理器备份", self.cleanup_legacy_handlers),
            ("冗余文档", self.cleanup_redundant_docs),
            ("旧打包文件", self.cleanup_old_packaging_files),
            ("重复事件总线", self.cleanup_event_bus_duplicate),
        ]

        total_removed = 0
        for task_name, task_func in cleanup_tasks:
            try:
                removed_count = task_func()
                total_removed += removed_count
            except Exception as e:
                print(f"❌ 清理 {task_name} 时出错: {str(e)}")

        # 生成报告
        self.generate_cleanup_report()

        # 总结
        print("\n" + "=" * 60)
        print("🎉 清理完成!")
        print(f"📊 总计删除文件: {total_removed} 个")
        print(f"💾 备份位置: {CLEANUP_BACKUP_DIR}")
        print("📄 详细报告: docs/cleanup/ARCHITECTURE_CLEANUP_REPORT.md")
        print("=" * 60)


def main():
    """主函数"""
    cleanup = ArchitectureCleanup()

    # 确认清理操作
    print("⚠️  即将开始架构清理，这将删除以下类型的文件：")
    print("   - 备份目录 (debug_backup_*, test_backup_*, md_backup_*)")
    print("   - 构建缓存 (__pycache__, build, dist/log)")
    print("   - 临时测试文件 (test_*.py 在根目录)")
    print("   - 传统处理器备份文件")
    print("   - 冗余文档文件")
    print("   - 旧的打包文件")
    print()
    print("所有文件都会先备份再删除。")

    response = input("是否继续？(y/N): ").strip().lower()
    if response in ['y', 'yes']:
        cleanup.run_cleanup()
    else:
        print("❌ 清理操作已取消")


if __name__ == "__main__":
    main()
