# -*- coding: utf-8 -*-

import traceback
import serial.tools.list_ports
from PyQt5.QtCore import QThread, QTimer, pyqtSignal
from utils.Log import get_module_logger
from .spi_interface import ISPIService
from .spi_service_impl import SPIServiceImpl

logger = get_module_logger(__name__)


class SPIService(ISPIService):
    """SPI服务的具体实现, 整合了 SPIOperations 和 SPIWorker 的逻辑"""

    # 信号需要在这里重新声明，因为它们是QObject的一部分
    spi_operation_complete = pyqtSignal(str, int, bool)
    spi_error_occurred = pyqtSignal(str)
    operation_timeout = pyqtSignal()
    ports_refreshed = pyqtSignal(list)
    simulation_mode_changed = pyqtSignal(bool)
    connection_status_changed = pyqtSignal(bool)

    # 类常量（将从配置加载）
    OPERATION_TIMEOUT = None  # 将从配置读取
    THREAD_WAIT_TIMEOUT = None  # 将从配置读取
    MAX_RETRY_COUNT = None  # 将从配置读取
    RETRY_DELAY = None  # 将从配置读取

    def __init__(self, register_manager=None, parent=None):
        super().__init__(parent)
        self.register_manager = register_manager
        self._simulation_mode = True # 默认为模拟模式

        # 从配置加载常量
        self._load_spi_config()
        self.spi_worker = None
        self.spi_thread = None
        self._thread_started = False
        self.timeout_timer = QTimer(self)
        self.timeout_timer.timeout.connect(self._handle_operation_timeout)
        self.active_operations = 0  # 跟踪当前活动的SPI操作数量
        self._cleaning_up = False
        self._current_port = None
        self._retry_count = 0  # 重试计数器
        self._last_error = None  # 记录最后一次错误
        self._connection_status = {
            'connected': False,
            'port': None,
            'mode': 'simulation',
            'last_error': None,
            'retry_count': 0
        }
        logger.info("SPIService initialized")

    def _load_spi_config(self):
        """从配置加载SPI相关配置"""
        try:
            from core.services.config.ConfigurationManager import get_config

            # 加载SPI配置
            SPIService.OPERATION_TIMEOUT = get_config('spi.timeout', 5000)
            SPIService.THREAD_WAIT_TIMEOUT = get_config('spi.thread_wait_timeout', 2000)
            SPIService.MAX_RETRY_COUNT = get_config('spi.retry_count', 3)
            SPIService.RETRY_DELAY = get_config('spi.retry_delay', 1000)

            logger.info(f"SPI配置已加载: TIMEOUT={self.OPERATION_TIMEOUT}ms, "
                       f"RETRY_COUNT={self.MAX_RETRY_COUNT}, "
                       f"RETRY_DELAY={self.RETRY_DELAY}ms")
        except Exception as e:
            logger.error(f"加载SPI配置失败: {str(e)}")
            # 使用默认值
            SPIService.OPERATION_TIMEOUT = 5000
            SPIService.THREAD_WAIT_TIMEOUT = 2000
            SPIService.MAX_RETRY_COUNT = 3
            SPIService.RETRY_DELAY = 1000

    def initialize(self):
        """初始化SPI服务, 创建并启动工作线程

        Returns:
            bool: 初始化是否成功
        """
        logger.info("SPIService initializing...")
        try:
            if self._thread_started and self.spi_thread and self.spi_thread.isRunning():
                logger.warning("SPI Service already initialized.")
                # 检查硬件可用性并更新模式
                self._check_hardware_and_set_mode()
                return True

            self._init_thread_components()
            self._start_spi_thread()
            self._thread_started = True
            self._check_hardware_and_set_mode()
            logger.info("SPIService initialization complete.")
            return True

        except Exception as e:
            logger.error(f"SPIService initialization failed: {str(e)}\n{traceback.format_exc()}")
            self._set_simulation_mode_internal(True) # 失败时强制进入模拟模式
            return False

    def _check_hardware_and_set_mode(self):
        """检查硬件可用性并设置模拟模式"""
        if not self.spi_worker:
            self._set_simulation_mode_internal(True)
            return

        # 先扫描可用端口
        logger.info("Scanning for available COM ports during initialization...")
        ports_info = []
        try:
            import serial.tools.list_ports
            available_ports = list(serial.tools.list_ports.comports())
            if available_ports:
                for port in available_ports:
                    ports_info.append({"device": port.device, "description": port.description})
                    logger.debug(f"Found port during init: {port.device} - {port.description}")
            else:
                logger.debug("No COM ports detected during initialization.")
        except Exception as e:
            logger.error(f"Error scanning COM ports during initialization: {str(e)}")

        # 如果有可用端口，尝试连接第一个端口
        if ports_info:
            first_port = ports_info[0]["device"]
            logger.info(f"Attempting to connect to first available port: {first_port}")

            # 尝试连接到第一个端口
            success = self.spi_worker.set_port(first_port)
            if success:
                self._current_port = first_port
                logger.info(f"Successfully connected to {first_port} during initialization. Using hardware mode.")
                self._set_simulation_mode_internal(False)
                self._update_connection_status(True)
                # 发出端口刷新信号，让UI知道可用端口
                self.ports_refreshed.emit(ports_info)
                return
            else:
                logger.warning(f"Failed to connect to {first_port} during initialization.")

        # 如果没有端口或连接失败，使用模拟模式
        logger.info("No usable hardware ports found. Enabling simulation mode.")
        self._set_simulation_mode_internal(True)
        self._update_connection_status(False)  # 确保连接状态为未连接
        # 仍然发出端口刷新信号，即使是空列表
        self.ports_refreshed.emit(ports_info)

    def cleanup(self):
        """清理SPI资源, 停止线程"""
        if self._cleaning_up:
            logger.warning("Cleanup already in progress.")
            return
        self._cleaning_up = True
        logger.info("SPIService cleaning up...")
        try:
            if self.timeout_timer:
                try:
                    self.timeout_timer.stop()
                except Exception as e:
                    logger.warning(f"Error stopping timeout timer: {e}")

            self.active_operations = 0
            self._cleanup_thread()
            logger.info("SPIService cleanup finished.")
        except RuntimeError as e:
            if "wrapped C/C++ object" in str(e):
                logger.warning(f"Qt object already deleted during cleanup: {e}")
            else:
                logger.error(f"Runtime error during SPIService cleanup: {str(e)}")
        except Exception as e:
            logger.error(f"Error during SPIService cleanup: {str(e)}\n{traceback.format_exc()}")
        finally:
            self._cleaning_up = False

    def refresh_available_ports(self):
        """刷新并获取可用的SPI端口列表, 发出 ports_refreshed 信号"""
        logger.info("Refreshing available COM ports...")
        ports_info = []
        try:
            available_ports = list(serial.tools.list_ports.comports())
            if available_ports:
                for port in available_ports:
                    ports_info.append({"device": port.device, "description": port.description})
                    logger.debug(f"Found port: {port.device} - {port.description}")
            else:
                logger.debug("No COM ports detected.")
        except Exception as e:
            logger.error(f"Error refreshing COM ports: {str(e)}")

        # 如果没有端口且当前不在模拟模式，切换到模拟模式
        if not ports_info and not self._simulation_mode:
            logger.info("No ports available, switching to simulation mode")
            self._set_simulation_mode_internal(True)
            self._update_connection_status(False, "无可用端口")

        self.ports_refreshed.emit(ports_info)
        return ports_info

    def set_port(self, port_name: str) -> bool:
        """设置要使用的SPI端口"""
        logger.info(f"Attempting to set SPI port to: {port_name}")
        if not self._ensure_spi_thread():
            logger.error("SPI thread not running, cannot set port.")
            return False

        if self._current_port == port_name and not self._simulation_mode:
             logger.info(f"Already connected to {port_name}.")
             # 即使端口相同，也检查一下硬件状态
             if self.spi_worker.check_spi_available(silent=True):
                 return True
             else:
                 logger.warning(f"Previously connected port {port_name} is now unavailable. Retrying connection.")

        # 停止当前操作计时
        if self.timeout_timer.isActive():
            self.timeout_timer.stop()
            self.active_operations = 0

        success = self.spi_worker.set_port(port_name)
        if success:
            self._current_port = port_name
            logger.info(f"Successfully set SPI port to {port_name}. Disabling simulation mode.")
            self._set_simulation_mode_internal(False)
            # 更新连接状态为已连接
            self._update_connection_status(True)
        else:
            self._current_port = None
            logger.error(f"Failed to set SPI port to {port_name}. Enabling simulation mode.")
            self._set_simulation_mode_internal(True)
            # 更新连接状态为未连接
            self._update_connection_status(False, f"无法连接到端口 {port_name}")
            self.spi_error_occurred.emit(f"无法连接到端口 {port_name}")
        return success

    def read_register(self, address: str):
        """读取单个寄存器"""
        if not self._ensure_spi_thread():
            return False
        logger.debug(f"Queueing read for address: {address}")
        self.spi_worker.add_operation('read', address)
        self._start_operation_tracking(1)
        return True

    def write_register(self, address: str, value: int):
        """写入单个寄存器"""
        if not self._ensure_spi_thread():
            return False
        logger.debug(f"Queueing write for address: {address}, value: 0x{value:04X}")
        self.spi_worker.add_operation('write', address, value)
        self._start_operation_tracking(1)
        return True

    def batch_read_registers(self, addresses: list):
        """批量读取寄存器"""
        if not self._ensure_spi_thread():
            return False
        count = len(addresses)
        if count == 0:
            return True
        logger.debug(f"Queueing batch read for {count} addresses: {addresses}")
        for addr in addresses:
            self.spi_worker.add_operation('read', addr)
        self._start_operation_tracking(count)
        return True

    def batch_write_registers(self, operations: list):
        """批量写入寄存器 operations: list of (address, value)"""
        if not self._ensure_spi_thread():
            return False
        count = len(operations)
        if count == 0:
            return True
        logger.debug(f"Queueing batch write for {count} operations: {operations}")
        for addr, value in operations:
            self.spi_worker.add_operation('write', addr, value)
        self._start_operation_tracking(count)
        return True

    def clear_operation_queue(self):
        """清空操作队列"""
        if self.spi_worker:
            self.spi_worker.clear_queue()
            # 重置操作计数
            self.active_operations = 0
            if self.timeout_timer.isActive():
                self.timeout_timer.stop()
            logger.info("SPI操作队列已清空")

    def set_simulation_mode(self, enabled: bool):
        """外部调用的设置模拟模式接口"""
        logger.info(f"External request to set simulation mode: {enabled}")

        # 允许用户自由切换模式，不强制检查硬件可用性
        self._set_simulation_mode_internal(enabled)

        # 如果切换到硬件模式，检查硬件是否可用并给出提示
        if not enabled and self.spi_worker:
            if not self.spi_worker.check_spi_available(silent=True):
                logger.warning("SPI hardware not available, but switched to hardware mode as requested.")
                # 发出警告信号，但不阻止切换
                self.spi_error_occurred.emit("已切换到硬件模式，但SPI硬件当前不可用")

    def _set_simulation_mode_internal(self, enabled: bool):
        """内部设置模拟模式状态，并通知worker和发出信号"""
        if self._simulation_mode == enabled:
            return # 状态未改变

        self._simulation_mode = enabled
        if self.spi_worker:
            self.spi_worker.set_simulation_mode(enabled)

        # 更新连接状态以反映模式变化
        current_connected = self._connection_status.get('connected', False)
        self._update_connection_status(current_connected)

        logger.info(f"Simulation mode {'enabled' if enabled else 'disabled'}.")
        self.simulation_mode_changed.emit(enabled)

    @property
    def simulation_mode(self) -> bool:
        """获取当前是否为模拟模式"""
        return self._simulation_mode

    @property
    def is_port_selected(self) -> bool:
        """检查是否已选择端口"""
        if self._simulation_mode:
            return True # 模拟模式下认为端口已选
        return self.spi_worker is not None and self.spi_worker.port_selected()

    @property
    def is_connected(self) -> bool:
        """检查是否已连接到设备"""
        if self.simulation_mode:
            return True
        return self._connection_status['connected']

    def get_connection_status(self) -> dict:
        """获取连接状态信息"""
        return self._connection_status.copy()

    def _update_connection_status(self, connected: bool, error: str = None):
        """更新连接状态"""
        old_status = self._connection_status['connected']
        self._connection_status.update({
            'connected': connected,
            'port': self._current_port,
            'mode': 'simulation' if self.simulation_mode else 'hardware',
            'last_error': error,
            'retry_count': self._retry_count
        })
        
        if old_status != connected:
            self.connection_status_changed.emit(connected)
            logger.info(f"SPI连接状态已更新: {'已连接' if connected else '已断开'}")

    # --- Internal Helper Methods --- #

    def _start_operation_tracking(self, count: int):
        """开始跟踪SPI操作并启动超时定时器"""
        self.active_operations += count
        if not self.timeout_timer.isActive():
            self.timeout_timer.start(self.OPERATION_TIMEOUT)

    def _init_thread_components(self):
        """初始化线程和工作对象"""
        self._cleanup_thread() # 确保清理旧的

        self.spi_thread = QThread()
        self.spi_thread.setObjectName("SPIServiceThread")
        self.spi_worker = SPIServiceImpl() # 创建新的 worker
        self.spi_worker.set_simulation_mode(self._simulation_mode) # 设置初始模式

        self.spi_worker.moveToThread(self.spi_thread)

        # 连接信号
        self.spi_thread.started.connect(self.spi_worker.process_operations)
        # 只连接主要的完成信号，避免重复回调
        self.spi_worker.spi_operation_complete.connect(self._handle_spi_result)
        self.spi_worker.error_occurred.connect(self._handle_thread_error)

        # 线程结束时的清理 - 完全依赖Qt自动处理，不手动连接任何清理信号
        # 移除所有手动清理的信号连接，让Qt自动管理对象生命周期

        logger.debug("SPI thread components initialized and signals connected.")

    # 移除了 _is_object_valid 和 _safe_delete_worker 方法
    # 不再需要这些方法，因为我们完全依赖Qt的自动清理机制

    def _start_spi_thread(self):
        """启动SPI线程"""
        if self.spi_thread and not self.spi_thread.isRunning():
            logger.info("Starting SPI thread...")
            self.spi_thread.start()
        elif not self.spi_thread:
             logger.error("SPI thread object not initialized.")
        else:
             logger.warning("SPI thread is already running.")

    def _ensure_spi_thread(self) -> bool:
        """确保SPI线程正在运行"""
        try:
            if not self._thread_started or not self.spi_thread or not self.spi_thread.isRunning():
                logger.warning("SPI thread not running. Attempting to initialize and start...")
                self.initialize() # 尝试重新初始化
                # 再次检查
                if not self._thread_started or not self.spi_thread or not self.spi_thread.isRunning():
                    logger.error("Failed to start SPI thread after re-initialization.")
                    self.spi_error_occurred.emit("SPI通信线程启动失败")
                    return False
            return True
        except Exception as e:
            logger.error(f"Error ensuring SPI thread is running: {str(e)}\n{traceback.format_exc()}")
            self.spi_error_occurred.emit(f"检查SPI线程时出错: {str(e)}")
            return False

    def _cleanup_thread(self):
        """停止并清理SPI线程和工作对象 - 完全安全版本"""
        logger.debug("Cleaning up SPI thread...")

        # 第一步：安全地停止worker
        if self.spi_worker:
            try:
                # 只尝试调用stop方法，不做任何其他操作
                self.spi_worker.stop()
                logger.debug("Worker stop requested")
            except Exception:
                # 忽略所有错误，包括RuntimeError
                logger.debug("Worker stop failed (ignored)")

        # 第二步：安全地停止线程
        if self.spi_thread:
            try:
                if self.spi_thread.isRunning():
                    logger.info("Requesting SPI thread termination...")
                    self.spi_thread.quit()
                    if not self.spi_thread.wait(self.THREAD_WAIT_TIMEOUT):
                        logger.warning("SPI thread did not exit gracefully. Terminating...")
                        self.spi_thread.terminate()
                        self.spi_thread.wait(500)
                    logger.info("SPI thread stopped.")
                else:
                    logger.debug("Thread exists but not running")
            except Exception:
                # 忽略所有线程相关的错误
                logger.debug("Thread cleanup failed (ignored)")

        # 第三步：清理引用，让Qt自动处理对象删除
        # 不调用任何deleteLater()方法，完全依赖Qt的自动清理
        self.spi_thread = None
        self.spi_worker = None
        self._thread_started = False
        logger.debug("SPI thread resources cleaned.")

    def _handle_spi_result(self, address: str, value: int, is_read: bool):
        """处理来自Worker的成功结果"""
        self.active_operations -= 1
        if self.active_operations <= 0:
            self.timeout_timer.stop()
            self.active_operations = 0 # 确保不会变为负数

        # 注意：移除了RegisterManager的直接更新，避免重复更新
        # RegisterManager的更新现在由RegisterOperationService统一处理
        logger.debug(f"SPI操作完成: {address} = 0x{value:04X} ({'读取' if is_read else '写入'})")

        # 发出外部信号，让上层服务处理寄存器更新逻辑
        self.spi_operation_complete.emit(address, value, is_read)

    def _handle_thread_error(self, error_msg: str):
        """处理来自Worker的错误"""
        logger.error(f"SPI Worker reported error: {error_msg}")
        # 可以选择是否重置活动操作计数
        # self.active_operations = 0
        # self.timeout_timer.stop()
        self.spi_error_occurred.emit(error_msg)

    def _handle_operation_timeout(self):
        """处理操作超时"""
        self.timeout_timer.stop()
        logger.error(f"SPI操作超时: {self.active_operations}个操作未完成")
        self.active_operations = 0
        
        # 更新连接状态
        self._update_connection_status(False, "操作超时")
        
        # 尝试重连
        if self._retry_count < self.MAX_RETRY_COUNT:
            self._retry_count += 1
            logger.info(f"尝试重连 (第{self._retry_count}次)")
            QTimer.singleShot(self.RETRY_DELAY, self._attempt_reconnect)
        else:
            logger.error("达到最大重试次数，切换到模拟模式")
            self.set_simulation_mode(True)
            self._retry_count = 0
        
        self.operation_timeout.emit()

    def _attempt_reconnect(self):
        """尝试重新连接"""
        try:
            if self._current_port:
                success = self.set_port(self._current_port)
                if success:
                    self._retry_count = 0
                    self._update_connection_status(True)
                    logger.info("重连成功")
                else:
                    self._update_connection_status(False, "重连失败")
                    logger.error("重连失败")
        except Exception as e:
            self._update_connection_status(False, str(e))
            logger.error(f"重连过程中出错: {str(e)}")