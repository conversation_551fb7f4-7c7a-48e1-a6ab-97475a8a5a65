#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ModernRegisterTreeHandler测试
验证现代化寄存器树处理器的基本功能
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt

from ui.handlers.ModernRegisterTreeHandler import ModernRegisterTreeHandler
from core.services.register.RegisterManager import RegisterManager


class TestModernRegisterTreeHandler(unittest.TestCase):
    """ModernRegisterTreeHandler测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """设置测试"""
        # 创建模拟的RegisterManager
        self.mock_registers = {
            "0x00": {
                "bits": [
                    {
                        "bit": "15:0",
                        "name": "DA_DEVICE_VERSION",
                        "default": "0001001100000000",
                        "widget_name": "deviceVersion",
                        "widget_type": "label",
                        "options": None,
                        "description": "Device version"
                    }
                ]
            },
            "0x02": {
                "bits": [
                    {
                        "bit": "0",
                        "name": "POWERDOWN",
                        "default": "0",
                        "widget_name": "powerDown",
                        "widget_type": "checkbox",
                        "options": None,
                        "description": "Power down control"
                    }
                ]
            },
            "0x04": {
                "bits": [
                    {
                        "bit": "7:0",
                        "name": "TEST_FIELD",
                        "default": "00000000",
                        "widget_name": "testField",
                        "widget_type": "spinbox",
                        "options": None,
                        "description": "Test field"
                    }
                ]
            }
        }
        
        self.register_manager = RegisterManager(self.mock_registers)
        
        # 创建ModernRegisterTreeHandler实例
        self.handler = ModernRegisterTreeHandler(
            parent=None,
            register_manager=self.register_manager
        )
    
    def tearDown(self):
        """清理测试"""
        if hasattr(self, 'handler'):
            self.handler.close()
    
    def test_initialization(self):
        """测试初始化"""
        self.assertIsNotNone(self.handler)
        self.assertEqual(self.handler.register_manager, self.register_manager)
        self.assertIsNotNone(self.handler.tree_widget)
        self.assertIsInstance(self.handler.register_items, dict)
    
    def test_tree_widget_creation(self):
        """测试树控件创建"""
        tree_widget = self.handler.create_tree_widget()
        self.assertIsNotNone(tree_widget)
        self.assertEqual(tree_widget, self.handler.tree_widget)
        self.assertTrue(tree_widget.headerItem().isHidden())
    
    def test_populate_tree_widget(self):
        """测试填充树控件"""
        self.handler.populate_tree_widget()
        
        # 验证根节点存在
        self.assertEqual(self.handler.tree_widget.topLevelItemCount(), 1)
        root_item = self.handler.tree_widget.topLevelItem(0)
        self.assertEqual(root_item.text(0), "Registers")
        
        # 验证寄存器项数量
        self.assertEqual(root_item.childCount(), 3)  # 0x00, 0x02, 0x04
        
        # 验证寄存器项内容
        reg_item_0 = root_item.child(0)
        self.assertEqual(reg_item_0.text(0), "R0 (0x00)")
        self.assertEqual(reg_item_0.data(0, Qt.UserRole), "0x00")
        
        # 验证register_items字典
        self.assertEqual(len(self.handler.register_items), 3)
        self.assertIn("0x00", self.handler.register_items)
        self.assertIn("0x02", self.handler.register_items)
        self.assertIn("0x04", self.handler.register_items)
    
    def test_address_parsing(self):
        """测试地址解析"""
        # 测试整数地址
        self.assertEqual(self.handler._parse_register_address(0), 0)
        self.assertEqual(self.handler._parse_register_address(2), 2)
        
        # 测试十六进制字符串地址
        self.assertEqual(self.handler._parse_register_address("0x00"), 0)
        self.assertEqual(self.handler._parse_register_address("0x02"), 2)
        self.assertEqual(self.handler._parse_register_address("0x04"), 4)
        
        # 测试十进制字符串地址
        self.assertEqual(self.handler._parse_register_address("0"), 0)
        self.assertEqual(self.handler._parse_register_address("2"), 2)
        
        # 测试无效地址
        self.assertEqual(self.handler._parse_register_address("invalid"), 0)
        self.assertEqual(self.handler._parse_register_address(None), 0)
    
    def test_register_selection(self):
        """测试寄存器选择"""
        self.handler.populate_tree_widget()
        
        # 测试通过地址选择寄存器
        with patch.object(self.handler, 'register_selected') as mock_signal:
            self.handler.select_register_by_addr("0x02")
            mock_signal.emit.assert_called_once_with("0x02")
    
    def test_current_register_addr(self):
        """测试获取当前寄存器地址"""
        self.handler.populate_tree_widget()
        
        # 初始状态应该没有选中项
        self.assertIsNone(self.handler.get_current_register_addr())
        
        # 选择一个寄存器
        self.handler.select_register_by_addr("0x02")
        self.assertEqual(self.handler.get_current_register_addr(), "0x02")
    
    def test_default_register_selection(self):
        """测试默认寄存器选择"""
        self.handler.populate_tree_widget()
        
        with patch.object(self.handler, 'on_tree_item_clicked') as mock_click:
            self.handler.select_default_register()
            mock_click.assert_called_once()
    
    def test_tree_item_click_handling(self):
        """测试树项点击处理"""
        self.handler.populate_tree_widget()
        root_item = self.handler.tree_widget.topLevelItem(0)
        reg_item = root_item.child(0)  # R0 (0x00)
        
        # 设置为当前项
        self.handler.tree_widget.setCurrentItem(reg_item)
        
        with patch.object(self.handler, 'register_selected') as mock_signal:
            self.handler.on_tree_item_clicked(reg_item, 0)
            mock_signal.emit.assert_called_once_with("0x00")
    
    def test_duplicate_selection_prevention(self):
        """测试防止重复选择"""
        self.handler.populate_tree_widget()
        root_item = self.handler.tree_widget.topLevelItem(0)
        reg_item = root_item.child(0)  # R0 (0x00)
        
        # 设置为当前项
        self.handler.tree_widget.setCurrentItem(reg_item)
        
        with patch.object(self.handler, 'register_selected') as mock_signal:
            # 第一次点击
            self.handler.on_tree_item_clicked(reg_item, 0)
            self.assertEqual(mock_signal.emit.call_count, 1)
            
            # 第二次点击相同项（应该被忽略）
            self.handler.on_tree_item_clicked(reg_item, 0)
            self.assertEqual(mock_signal.emit.call_count, 1)  # 没有增加
    
    def test_find_register_by_name(self):
        """测试通过名称查找寄存器"""
        self.handler.populate_tree_widget()
        
        # 查找存在的寄存器
        found_items = self.handler.find_register_by_name("R0")
        self.assertEqual(len(found_items), 1)
        self.assertEqual(found_items[0].text(0), "R0 (0x00)")
        
        # 查找不存在的寄存器
        found_items = self.handler.find_register_by_name("R99")
        self.assertEqual(len(found_items), 0)
        
        # 部分匹配查找
        found_items = self.handler.find_register_by_name("R")
        self.assertEqual(len(found_items), 3)  # R0, R2, R4
    
    def test_bit_field_filtering(self):
        """测试位字段过滤"""
        self.handler.populate_tree_widget()
        
        # 测试精确匹配
        with patch.object(self.handler, 'on_tree_item_clicked') as mock_click:
            self.handler.filter_registers_by_bit_field_name("POWERDOWN")
            mock_click.assert_called_once()
        
        # 测试部分匹配
        with patch.object(self.handler, 'on_tree_item_clicked') as mock_click:
            mock_click.reset_mock()
            self.handler.filter_registers_by_bit_field_name("POWER")
            mock_click.assert_called_once()
        
        # 测试清除过滤
        self.handler.filter_registers_by_bit_field_name("")
        current_item = self.handler.tree_widget.currentItem()
        self.assertIsNone(current_item)
    
    def test_register_value_update(self):
        """测试寄存器值更新"""
        self.handler.populate_tree_widget()
        
        # 测试更新存在的寄存器值
        self.handler.update_register_value("0x00", 0x1234)
        # 注意：当前实现中树视图只有一列，所以这个测试主要验证不会出错
        
        # 测试更新不存在的寄存器值
        self.handler.update_register_value("0xFF", 0x5678)  # 不应该出错
    
    def test_global_register_update_callback(self):
        """测试全局寄存器更新回调"""
        self.handler.populate_tree_widget()
        
        # 测试全局更新回调
        with patch.object(self.handler, 'update_register_value') as mock_update:
            self.handler.on_global_register_update("0x02", 0x1234)
            mock_update.assert_called_once_with("0x02", 0x1234)
    
    def test_parent_callback_compatibility(self):
        """测试父窗口回调兼容性"""
        # 创建模拟父窗口（使用QWidget而不是Mock）
        from PyQt5.QtWidgets import QWidget
        mock_parent = QWidget()
        mock_parent.on_register_selected = Mock()

        handler = ModernRegisterTreeHandler(
            parent=mock_parent,
            register_manager=self.register_manager
        )
        
        handler.populate_tree_widget()
        root_item = handler.tree_widget.topLevelItem(0)
        reg_item = root_item.child(0)
        
        # 设置为当前项
        handler.tree_widget.setCurrentItem(reg_item)
        
        # 触发点击事件
        handler.on_tree_item_clicked(reg_item, 0)
        
        # 验证父窗口回调被调用
        mock_parent.on_register_selected.assert_called_once_with("0x00")
        
        handler.close()


def run_tests():
    """运行测试"""
    unittest.main()


if __name__ == '__main__':
    run_tests()
