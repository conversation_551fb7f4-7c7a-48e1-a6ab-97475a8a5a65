# -*- coding: utf-8 -*-

"""
事件协调器
负责协调和管理主窗口的各种事件处理
"""

import traceback
from PyQt5.QtWidgets import QMessageBox
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class EventCoordinator:
    """事件协调器类
    
    职责：
    1. 协调各种UI事件的处理
    2. 管理事件处理的流程和逻辑
    3. 提供统一的事件处理接口
    4. 处理事件处理过程中的异常
    """
    
    def __init__(self, main_window):
        """初始化事件协调器
        
        Args:
            main_window: 主窗口实例
        """
        self.main_window = main_window
        
    def handle_read_requested(self, addr):
        """处理读取请求
        
        Args:
            addr: 寄存器地址
        """
        try:
            # 委托给UI事件处理器
            if hasattr(self.main_window, 'ui_event_handler'):
                self.main_window.ui_event_handler.handle_read_requested(addr)
            else:
                logger.warning("UI事件处理器不可用")
        except Exception as e:
            logger.error(f"处理读取请求时出错: {str(e)}\n{traceback.format_exc()}")
            
    def handle_write_requested(self, addr, value):
        """处理写入请求
        
        Args:
            addr: 寄存器地址
            value: 要写入的值
        """
        try:
            # 委托给UI事件处理器
            if hasattr(self.main_window, 'ui_event_handler'):
                self.main_window.ui_event_handler.handle_write_requested(addr, value)
            else:
                logger.warning("UI事件处理器不可用")
        except Exception as e:
            logger.error(f"处理写入请求时出错: {str(e)}\n{traceback.format_exc()}")
            
    def handle_read_all_requested(self):
        """处理读取所有寄存器请求"""
        try:
            # 委托给批量操作管理器
            if hasattr(self.main_window, 'batch_manager'):
                self.main_window.batch_manager.handle_read_all_requested()
            else:
                logger.warning("批量操作管理器不可用")
        except Exception as e:
            logger.error(f"处理读取所有寄存器请求时出错: {str(e)}\n{traceback.format_exc()}")
            
    def handle_write_all_requested(self):
        """处理写入所有寄存器请求"""
        try:
            # 委托给批量操作管理器
            if hasattr(self.main_window, 'batch_manager'):
                self.main_window.batch_manager.handle_write_all_requested()
            else:
                logger.warning("批量操作管理器不可用")
        except Exception as e:
            logger.error(f"处理写入所有寄存器请求时出错: {str(e)}\n{traceback.format_exc()}")
            
    def handle_save_requested(self):
        """处理保存配置请求"""
        try:
            # 委托给配置管理服务
            if hasattr(self.main_window, 'config_service'):
                self.main_window.config_service.save_register_config()
            else:
                logger.warning("配置管理服务不可用")
        except Exception as e:
            logger.error(f"处理保存配置请求时出错: {str(e)}\n{traceback.format_exc()}")
            
    def handle_load_requested(self):
        """处理加载配置请求"""
        try:
            # 委托给配置管理服务
            if hasattr(self.main_window, 'config_service'):
                self.main_window.config_service.load_register_config()
            else:
                logger.warning("配置管理服务不可用")
        except Exception as e:
            logger.error(f"处理加载配置请求时出错: {str(e)}\n{traceback.format_exc()}")
            
    def handle_io_write_request(self, addr, value):
        """处理来自 IO Handler 的直接写入请求 (例如，通过回车确认输入)
        
        Args:
            addr: 寄存器地址
            value: 要写入的值
        """
        try:
            # 委托给全局事件管理器
            if hasattr(self.main_window, 'global_event_manager'):
                self.main_window.global_event_manager.handle_io_write_request(addr, value)
            else:
                logger.warning("全局事件管理器不可用")
        except Exception as e:
            logger.error(f"处理IO写入请求时出错: {str(e)}\n{traceback.format_exc()}")
            
    def handle_value_changed(self, addr, new_value):
        """处理输入值变化事件

        Args:
            addr: 寄存器地址
            new_value: 新值
        """
        try:
            # 委托给显示管理器
            if hasattr(self.main_window, 'display_manager'):
                self.main_window.display_manager.handle_value_changed(addr, new_value)
            else:
                logger.warning("显示管理器不可用")
        except Exception as e:
            logger.error(f"处理值变化事件时出错: {str(e)}\n{traceback.format_exc()}")

    def handle_read_button_click(self):
        """处理读取按钮点击事件"""
        try:
            # 委托给UI事件处理器
            if hasattr(self.main_window, 'ui_event_handler'):
                self.main_window.ui_event_handler.handle_read_button_click()
            else:
                logger.warning("UI事件处理器不可用")
        except Exception as e:
            logger.error(f"处理读取按钮点击时出错: {str(e)}\n{traceback.format_exc()}")

    def handle_write_button_click(self):
        """处理写入按钮点击事件"""
        try:
            # 委托给UI事件处理器
            if hasattr(self.main_window, 'ui_event_handler'):
                self.main_window.ui_event_handler.handle_write_button_click()
            else:
                logger.warning("UI事件处理器不可用")
        except Exception as e:
            logger.error(f"处理写入按钮点击时出错: {str(e)}\n{traceback.format_exc()}")
            
    def handle_register_selection(self, reg_addr):
        """处理寄存器被选中事件

        Args:
            reg_addr: 寄存器地址
        """
        try:
            logger.debug(f"EventCoordinator: 开始处理寄存器选择 {reg_addr}")
            # 委托给显示管理器
            if hasattr(self.main_window, 'display_manager'):
                logger.debug(f"EventCoordinator: 找到显示管理器，调用 handle_register_selection({reg_addr})")
                result = self.main_window.display_manager.handle_register_selection(reg_addr)
                logger.debug(f"EventCoordinator: 显示管理器返回结果: {result}")
                return result
            else:
                logger.warning("EventCoordinator: 显示管理器不可用")
                return False
        except Exception as e:
            logger.error(f"EventCoordinator: 处理寄存器选择时出错: {str(e)}\n{traceback.format_exc()}")
            return False
            
    def handle_simulation_mode_toggle(self, checked):
        """处理模拟模式切换

        Args:
            checked: 是否选中模拟模式
        """
        try:
            self.main_window.simulation_mode = checked
            if hasattr(self.main_window, 'spi_service'):
                # 调用正确的公共方法来设置模拟模式
                self.main_window.spi_service.set_simulation_mode(checked)
            else:
                logger.warning("SPI服务不可用")

            # 根据模式切换状态监控
            if hasattr(self.main_window, 'register_service'):
                if checked:
                    # 切换到模拟模式，停止状态监控
                    self.main_window.register_service.stop_status_monitoring()
                    logger.info("模拟模式：停止状态监控")
                else:
                    # 切换到硬件模式，启动状态监控
                    self.main_window.register_service.start_status_monitoring()
                    logger.info("硬件模式：启动状态监控")
            else:
                logger.warning("寄存器服务不可用，无法控制状态监控")

        except Exception as e:
            logger.error(f"切换模拟模式时出错: {str(e)}\n{traceback.format_exc()}")

    def handle_auto_write_mode_toggle(self, checked):
        """处理自动写入模式切换

        Args:
            checked: 是否选中自动写入模式
        """
        try:
            # 更新主窗口的自动写入模式设置
            self.main_window.auto_write_mode = checked

            # 保存设置到配置文件
            if hasattr(self.main_window, 'config_service'):
                self.main_window.config_service.save_auto_write_mode(checked)

            # 显示状态消息
            mode_text = "启用" if checked else "禁用"
            self.main_window.show_status_message(f"自动写入功能已{mode_text}", 3000)

            logger.info(f"自动写入模式已{'启用' if checked else '禁用'}")

        except Exception as e:
            logger.error(f"切换自动写入模式时出错: {str(e)}\n{traceback.format_exc()}")

    def handle_preload_toggle(self, checked):
        """切换是否启用预读取功能
        
        Args:
            checked: 是否选中
        """
        try:
            # 委托给配置管理服务
            if hasattr(self.main_window, 'config_service'):
                self.main_window.config_service.toggle_preload(checked)
            else:
                logger.warning("配置管理服务不可用")
        except Exception as e:
            logger.error(f"切换预读取功能时出错: {str(e)}\n{traceback.format_exc()}")
            
    def handle_preload_count_set(self, action):
        """设置预读取数量
        
        Args:
            action: 触发的动作
        """
        try:
            if action:
                count = action.data()
                # 委托给配置管理服务
                if hasattr(self.main_window, 'config_service'):
                    self.main_window.config_service.set_preload_count(count)
                else:
                    logger.warning("配置管理服务不可用")
        except Exception as e:
            logger.error(f"设置预读取数量时出错: {str(e)}\n{traceback.format_exc()}")
            
    def handle_language_set(self, action):
        """设置语言
        
        Args:
            action: 触发的动作
        """
        try:
            if action:
                lang_code = action.data()
                # 委托给配置管理服务
                if hasattr(self.main_window, 'config_service'):
                    self.main_window.config_service.set_language(lang_code)
                else:
                    logger.warning("配置管理服务不可用")
        except Exception as e:
            logger.error(f"设置语言时出错: {str(e)}\n{traceback.format_exc()}")
            
    def handle_advanced_settings_show(self):
        """显示高级设置对话框"""
        try:
            # 这里可以显示一个更复杂的设置对话框，允许用户配置更多选项
            QMessageBox.information(
                self.main_window, 
                "高级设置", 
                "高级设置功能正在开发中。\n"
                "未来版本将支持更多自定义选项。",
                QMessageBox.Ok
            )
        except Exception as e:
            logger.error(f"显示高级设置时出错: {str(e)}\n{traceback.format_exc()}")
            
    def connect_all_signals(self):
        """连接所有信号"""
        try:
            self._connect_io_handler_signals()
            self._connect_spi_signals()
            self._connect_global_signals()
        except Exception as e:
            logger.error(f"连接信号时出错: {str(e)}\n{traceback.format_exc()}")
            
    def _connect_io_handler_signals(self):
        """连接IO处理器信号"""
        if hasattr(self.main_window, 'io_handler'):
            # 连接IO处理器信号
            self.main_window.io_handler.read_requested.connect(self.handle_read_requested)
            self.main_window.io_handler.write_requested.connect(self.handle_write_requested)
            self.main_window.io_handler.read_all_requested.connect(self.handle_read_all_requested)
            self.main_window.io_handler.write_all_requested.connect(self.handle_write_all_requested)
            self.main_window.io_handler.save_requested.connect(self.handle_save_requested)
            self.main_window.io_handler.load_requested.connect(self.handle_load_requested)
            self.main_window.io_handler.value_changed.connect(self.handle_value_changed)

            # 连接搜索相关信号
            if hasattr(self.main_window.io_handler, 'bit_field_selected'):
                self.main_window.io_handler.bit_field_selected.connect(self.handle_bit_field_selected)
            
    def _connect_spi_signals(self):
        """连接SPI相关信号"""
        if hasattr(self.main_window, 'spi_signal_manager'):
            # Connect signals from the new service
            self.main_window.spi_signal_manager.connect_spi_signals()

        # 注意：connection_status_changed 信号的连接已经在 SPISignalManager 中处理
        # 移除重复的信号连接以避免 update_status_bar() 被调用两次
            
    def _connect_global_signals(self):
        """连接全局信号"""
        # 这里可以添加其他全局信号的连接
        pass

    def handle_bit_field_selected(self, reg_addr):
        """处理位字段选择

        Args:
            reg_addr: 寄存器地址
        """
        try:
            logger.info(f"位字段选择: 寄存器 {reg_addr}")

            # 更新IO处理器显示选中的寄存器
            if hasattr(self.main_window, 'io_handler'):
                try:
                    addr = int(reg_addr, 16) if reg_addr.startswith("0x") else int(reg_addr)
                    self.main_window.io_handler.set_address_display(addr)

                    # 获取寄存器值并更新显示
                    if hasattr(self.main_window, 'register_manager'):
                        value = self.main_window.register_manager.get_register_value(addr)
                        self.main_window.io_handler.set_value_display(value)

                        # 更新表格处理器显示位域信息
                        if hasattr(self.main_window, 'table_handler'):
                            self.main_window.table_handler.show_bit_fields(reg_addr, value)

                except ValueError:
                    logger.error(f"无效的寄存器地址: {reg_addr}")

        except Exception as e:
            logger.error(f"处理位字段选择时出错: {str(e)}\n{traceback.format_exc()}")
