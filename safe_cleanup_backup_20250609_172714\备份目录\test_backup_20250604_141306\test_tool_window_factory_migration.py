#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试工具窗口工厂的现代化迁移状态
验证所有工具窗口是否都已迁移到现代化版本
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_tool_window_factory_migration():
    """测试工具窗口工厂的现代化迁移状态"""
    print("=" * 60)
    print("🔍 测试工具窗口工厂现代化迁移状态")
    print("=" * 60)
    
    test_results = []
    
    # 1. 测试ToolWindowFactory配置
    print("\n1. 检查ToolWindowFactory配置...")
    try:
        from ui.factories.ToolWindowFactory import ToolWindowFactory
        
        # 检查WINDOW_CONFIGS中的处理器类配置
        configs = ToolWindowFactory.WINDOW_CONFIGS
        
        modern_handlers = []
        legacy_handlers = []
        
        for window_type, config in configs.items():
            handler_class = config.get('handler_class', '')
            if 'Modern' in handler_class:
                modern_handlers.append(window_type)
                print(f"   ✓ {window_type}: 使用现代化处理器 - {handler_class}")
            else:
                legacy_handlers.append(window_type)
                print(f"   ⚠️  {window_type}: 仍使用传统处理器 - {handler_class}")
        
        print(f"\n   现代化处理器: {len(modern_handlers)} 个")
        print(f"   传统处理器: {len(legacy_handlers)} 个")
        
        if len(legacy_handlers) == 0:
            print("   ✅ 所有工具窗口配置都已迁移到现代化版本")
            test_results.append("ToolWindowFactory配置完全现代化")
        else:
            print(f"   ❌ 还有 {len(legacy_handlers)} 个工具窗口使用传统处理器")
            test_results.append(f"ToolWindowFactory配置部分现代化，剩余: {legacy_handlers}")
            
    except Exception as e:
        print(f"   ❌ ToolWindowFactory测试失败: {str(e)}")
        test_results.append(f"ToolWindowFactory测试失败: {str(e)}")
    
    # 2. 测试ModernToolWindowFactory配置
    print("\n2. 检查ModernToolWindowFactory配置...")
    try:
        from ui.factories.ModernToolWindowFactory import ModernToolWindowFactory
        
        # 创建模拟主窗口
        class MockMainWindow:
            def __init__(self):
                pass
        
        mock_main_window = MockMainWindow()
        modern_factory = ModernToolWindowFactory(mock_main_window)
        
        # 检查现代化工厂的配置
        modern_configs = modern_factory.HANDLER_CONFIGS
        
        fully_modern = []
        partially_modern = []
        legacy_only = []
        
        for window_type, config in modern_configs.items():
            use_modern = config.get('use_modern', False)
            has_modern = config.get('modern_handler') is not None
            has_legacy = config.get('legacy_handler') is not None
            
            if use_modern and has_modern:
                fully_modern.append(window_type)
                print(f"   ✅ {window_type}: 完全现代化")
            elif has_modern and has_legacy:
                partially_modern.append(window_type)
                status = "使用现代化" if use_modern else "使用传统"
                print(f"   🔄 {window_type}: 支持双版本，当前{status}")
            elif has_legacy:
                legacy_only.append(window_type)
                print(f"   ❌ {window_type}: 仅支持传统版本")
            else:
                print(f"   ⚠️  {window_type}: 配置异常")
        
        print(f"\n   完全现代化: {len(fully_modern)} 个")
        print(f"   支持双版本: {len(partially_modern)} 个")
        print(f"   仅传统版本: {len(legacy_only)} 个")
        
        if len(legacy_only) == 0:
            print("   ✅ 现代化工厂支持所有工具窗口的现代化版本")
            test_results.append("ModernToolWindowFactory完全支持现代化")
        else:
            print(f"   ❌ 还有 {len(legacy_only)} 个工具窗口仅支持传统版本")
            test_results.append(f"ModernToolWindowFactory部分支持，仅传统: {legacy_only}")
            
    except Exception as e:
        print(f"   ❌ ModernToolWindowFactory测试失败: {str(e)}")
        test_results.append(f"ModernToolWindowFactory测试失败: {str(e)}")
    
    # 3. 测试现代化处理器文件是否存在
    print("\n3. 检查现代化处理器文件...")
    
    expected_modern_handlers = [
        'ui.handlers.ModernSetModesHandler',
        'ui.handlers.ModernClkinControlHandler',
        'ui.handlers.ModernPLLHandler',
        'ui.handlers.ModernSyncSysRefHandler',
        'ui.handlers.ModernClkOutputsHandler'
    ]
    
    available_handlers = []
    missing_handlers = []
    
    for handler_module in expected_modern_handlers:
        try:
            module_path = handler_module.replace('.', os.sep) + '.py'
            if os.path.exists(module_path):
                # 尝试导入模块
                __import__(handler_module)
                available_handlers.append(handler_module)
                print(f"   ✓ {handler_module}: 文件存在且可导入")
            else:
                missing_handlers.append(handler_module)
                print(f"   ❌ {handler_module}: 文件不存在")
        except Exception as e:
            missing_handlers.append(handler_module)
            print(f"   ❌ {handler_module}: 导入失败 - {str(e)}")
    
    print(f"\n   可用处理器: {len(available_handlers)} 个")
    print(f"   缺失处理器: {len(missing_handlers)} 个")
    
    if len(missing_handlers) == 0:
        print("   ✅ 所有现代化处理器文件都可用")
        test_results.append("所有现代化处理器文件可用")
    else:
        print(f"   ❌ 缺失 {len(missing_handlers)} 个现代化处理器文件")
        test_results.append(f"缺失现代化处理器: {missing_handlers}")
    
    # 4. 总结测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    for i, result in enumerate(test_results, 1):
        print(f"{i}. {result}")
    
    # 判断整体迁移状态
    all_success = all("失败" not in result and "缺失" not in result and "部分" not in result for result in test_results)
    
    if all_success:
        print("\n🎉 恭喜！所有工具窗口都已成功迁移到现代化版本！")
        return True
    else:
        print("\n⚠️  工具窗口现代化迁移尚未完全完成，请检查上述问题。")
        return False

if __name__ == "__main__":
    success = test_tool_window_factory_migration()
    sys.exit(0 if success else 1)
