# 版本文件配置说明

## 📁 版本文件位置

**主版本文件**: `packaging/config/version.json`

这是项目的唯一版本配置文件，所有版本相关的服务和工具都使用这个文件。

## 🎯 设计理念

### 为什么选择 `packaging/config/version.json`？

1. **目录整洁**: 保持项目根目录简洁，只包含入口文件和说明文档
2. **逻辑分组**: 将所有打包相关的文件集中在 `packaging` 目录下
3. **配置集中**: 版本配置与其他打包配置文件放在同一个 `config` 子目录
4. **易于管理**: 所有构建和版本管理工具都在同一个目录结构下

## 📋 文件结构

```
packaging/
├── config/
│   ├── version.json          # 主版本文件 ✅
│   └── packaging_config.json # 打包配置文件
├── scripts/
│   ├── build_exe.py         # 构建脚本
│   └── ...
└── docs/
    └── version_file_configuration.md  # 本文档
```

## 🔧 版本文件格式

```json
{
  "version": {
    "major": 1,
    "minor": 0,
    "patch": 2,
    "build": 25
  },
  "app_info": {
    "name": "FSJ04832 寄存器配置工具",
    "description": "用于配置和管理FSJ04832寄存器的专业工具",
    "company": "FSJ Technology",
    "copyright": "© 2024 FSJ Technology",
    "author": "开发团队"
  },
  "build_info": {
    "last_build_date": "2025-06-06 14:10:00",
    "build_type": "release",
    "target_platform": "windows"
  }
}
```

## 🛠️ 服务配置

### VersionService 加载优先级

1. `packaging/config/version.json` ← **主版本文件**
2. `packaging/version.json` (已删除)
3. `version.json` (已删除)
4. `../packaging/config/version.json`
5. `../packaging/version.json`
6. `../version.json`
7. `../../version.json`

### VersionManager 配置

- **优先使用**: `packaging/config/version.json`
- **备用路径**: `packaging/version.json`
- **回退路径**: 项目根目录的 `version.json`

## ✅ 验证方法

```bash
# 检查版本服务
python -c "
from core.services.version.VersionService import VersionService
vs = VersionService.instance()
print(f'版本: {vs.get_version_string()}')
print(f'构建日期: {vs.get_build_date()}')
"

# 检查版本管理器
python -c "
from packaging.scripts.build_exe import VersionManager
vm = VersionManager()
print(f'版本文件: {vm.version_file}')
print(f'版本: {vm.get_version_string()}')
"
```

## 🎯 优势

1. **单一数据源**: 只有一个版本文件，避免版本不一致
2. **目录整洁**: 项目根目录保持简洁
3. **逻辑清晰**: 打包相关文件集中管理
4. **易于维护**: 版本更新只需修改一个文件
5. **向后兼容**: 保留多路径查找机制

## 📝 更新历史

- **2025-06-06**: 统一使用 `packaging/config/version.json` 作为主版本文件
- **删除**: `version.json` (项目根目录)
- **删除**: `packaging/version.json` (重复文件)
