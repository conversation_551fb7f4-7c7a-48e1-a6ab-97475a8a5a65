#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复PyInstaller打包中的dis模块问题
解决 "No module named 'dis'" 错误
"""

import os
import re
from pathlib import Path

def fix_spec_files():
    """修复所有spec文件中的模块问题"""

    scripts_dir = Path(__file__).parent / 'scripts'
    spec_files = list(scripts_dir.glob('*.spec'))

    print("🔧 修复PyInstaller spec文件中的模块问题...")

    # 不应该被排除的关键模块（PyInstaller运行时需要）
    critical_modules = [
        'dis', 'email', 'email.mime', 'email.mime.text', 'email.mime.multipart',
        'os', 'sys', 'io', 'codecs', 'encodings', 'encodings.utf_8', 'encodings.cp1252',
        'inspect', 'types', 'typing', 'json', 'pathlib', 'importlib', 'importlib.util'
    ]

    for spec_file in spec_files:
        print(f"  📄 处理文件: {spec_file.name}")

        try:
            # 读取文件内容
            with open(spec_file, 'r', encoding='utf-8') as f:
                content = f.read()

            original_content = content

            # 1. 移除关键模块从排除列表
            for module in critical_modules:
                # 移除单独的模块引用
                content = re.sub(rf"'{module}',?\s*", "", content)
                content = re.sub(rf",\s*'{module}'", "", content)
                # 移除带通配符的模块引用
                content = re.sub(rf"'{module}\.\*',?\s*", "", content)
                content = re.sub(rf",\s*'{module}\.\*'", "", content)

            # 2. 确保关键模块在hiddenimports中
            if 'hiddenimports' in content:
                # 检查是否已经包含这些模块
                missing_modules = []
                for module in critical_modules:
                    if f"'{module}'" not in content:
                        missing_modules.append(f"'{module}'")

                if missing_modules:
                    # 在标准库模块部分添加缺失的模块
                    patterns = [
                        (r"'typing',", f"'typing', {', '.join(missing_modules)},"),
                        (r"'typing'", f"'typing', {', '.join(missing_modules)}")
                    ]

                    for pattern, replacement in patterns:
                        if re.search(pattern, content):
                            content = re.sub(pattern, replacement, content, count=1)
                            break

            # 3. 清理多余的逗号和空行
            content = re.sub(r',\s*,', ',', content)  # 移除连续逗号
            content = re.sub(r',\s*\]', ']', content)  # 移除列表末尾的逗号

            # 4. 保存修改后的文件
            if content != original_content:
                with open(spec_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"    ✅ {spec_file.name} 已修复")
            else:
                print(f"    ℹ️ {spec_file.name} 无需修改")

        except Exception as e:
            print(f"    ❌ 修复 {spec_file.name} 失败: {e}")

def create_test_script():
    """创建测试脚本验证修复效果"""
    
    test_script = Path(__file__).parent / 'test_dis_module.py'
    
    test_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试dis模块是否可用
"""

def test_dis_module():
    """测试dis模块"""
    try:
        import dis
        print("✅ dis模块导入成功")
        
        # 测试dis模块的基本功能
        def sample_function():
            return "Hello, World!"
        
        print("📋 反汇编示例函数:")
        dis.dis(sample_function)
        
        return True
        
    except ImportError as e:
        print(f"❌ dis模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ dis模块测试失败: {e}")
        return False

def test_other_modules():
    """测试其他可能有问题的模块"""
    modules_to_test = [
        'os', 'sys', 'io', 'codecs', 'encodings',
        'inspect', 'types', 'typing'
    ]
    
    failed_modules = []
    
    for module_name in modules_to_test:
        try:
            __import__(module_name)
            print(f"✅ {module_name} 模块正常")
        except ImportError as e:
            print(f"❌ {module_name} 模块失败: {e}")
            failed_modules.append(module_name)
    
    return len(failed_modules) == 0

if __name__ == "__main__":
    print("🧪 测试PyInstaller打包后的模块可用性...")
    print("=" * 50)
    
    dis_ok = test_dis_module()
    print()
    
    other_ok = test_other_modules()
    print()
    
    if dis_ok and other_ok:
        print("🎉 所有模块测试通过！")
    else:
        print("⚠️ 部分模块测试失败，可能需要进一步调整spec文件")
'''
    
    with open(test_script, 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    print(f"📝 测试脚本已创建: {test_script}")

def main():
    """主函数"""
    print("🚀 开始修复PyInstaller dis模块问题...")
    print("=" * 50)
    
    # 修复spec文件
    fix_spec_files()
    
    print()
    
    # 创建测试脚本
    create_test_script()
    
    print()
    print("✅ 修复完成！")
    print()
    print("📋 后续步骤:")
    print("1. 重新运行打包命令")
    print("2. 测试打包后的exe文件")
    print("3. 如果仍有问题，运行 test_dis_module.py 进行诊断")
    print()
    print("🔧 重新打包命令示例:")
    print("   cd packaging")
    print("   python package.py secure patch")

if __name__ == "__main__":
    main()
