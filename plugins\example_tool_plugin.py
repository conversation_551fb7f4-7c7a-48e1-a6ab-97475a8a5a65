#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
示例工具插件
演示如何创建一个工具窗口插件
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                             QTextEdit, QScrollArea, QGroupBox, QSpinBox, QCheckBox,
                             QComboBox, QSlider, QProgressBar, QTabWidget, QTableWidget,
                             QTableWidgetItem, QSplitter)
from PyQt5.QtCore import pyqtSignal, Qt
from core.services.plugin.PluginManager import IToolWindowPlugin
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class ExampleToolWindow(QWidget):
    """示例工具窗口"""
    
    # 定义信号
    window_closed = pyqtSignal()
    
    def __init__(self, parent=None):
        """初始化示例工具窗口"""
        super().__init__(parent)
        self.setWindowTitle("示例工具")
        self.resize(500, 400)
        self.setMinimumSize(350, 250)

        # 设置窗口属性
        from PyQt5.QtCore import Qt
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint |
                           Qt.WindowMinimizeButtonHint | Qt.WindowMaximizeButtonHint)

        self._setup_ui()
        
    def _setup_ui(self):
        """设置UI - 创建一个大尺寸的界面来演示滚动功能"""
        layout = QVBoxLayout(self)

        # 标题标签
        title_label = QLabel("示例工具插件 - 滚动演示")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)

        # 说明文本
        info_label = QLabel("这个插件演示了如何创建工具窗口插件和滚动功能。\n"
                           "界面内容超出窗口大小时会自动显示滚动条。")
        layout.addWidget(info_label)

        # 创建标签页容器来展示更多内容
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)

        # 第一个标签页：基本控件
        self._create_basic_controls_tab(tab_widget)

        # 第二个标签页：表格演示
        self._create_table_demo_tab(tab_widget)

        # 第三个标签页：大量控件
        self._create_many_controls_tab(tab_widget)

        # 底部按钮区域
        self._create_bottom_buttons(layout)

    def _apply_progress_bar_style(self, progress_bar):
        """为进度条应用绿色样式"""
        try:
            from ui.styles.ProgressBarStyleManager import apply_green_progress_style
            apply_green_progress_style(progress_bar, "default")
        except ImportError:
            # 如果样式管理器不可用，使用内联样式
            progress_bar.setStyleSheet("""
                QProgressBar {
                    border: 2px solid #C0C0C0;
                    border-radius: 5px;
                    background-color: #F0F0F0;
                    text-align: center;
                    font-weight: bold;
                    color: #333333;
                }

                QProgressBar::chunk {
                    background-color: qlineargradient(
                        x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #7ED321,
                        stop: 0.5 #5CB85C,
                        stop: 1 #4CAF50
                    );
                    border-radius: 3px;
                    margin: 1px;
                }
            """)
        except Exception as e:
            logger.warning(f"应用进度条样式失败: {str(e)}")

    def _create_basic_controls_tab(self, tab_widget):
        """创建基本控件标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 文本编辑区域
        self.text_edit = QTextEdit()
        self.text_edit.setPlaceholderText("在这里输入一些文本...")
        self.text_edit.setMinimumHeight(200)
        layout.addWidget(self.text_edit)

        # 控件组
        group_box = QGroupBox("控件演示")
        group_layout = QVBoxLayout(group_box)

        # 数值控件
        spin_box = QSpinBox()
        spin_box.setRange(0, 1000)
        spin_box.setValue(50)
        group_layout.addWidget(QLabel("数值输入:"))
        group_layout.addWidget(spin_box)

        # 复选框
        check_box = QCheckBox("启用功能")
        check_box.setChecked(True)
        group_layout.addWidget(check_box)

        # 下拉框
        combo_box = QComboBox()
        combo_box.addItems(["选项1", "选项2", "选项3", "选项4"])
        group_layout.addWidget(QLabel("选择选项:"))
        group_layout.addWidget(combo_box)

        # 滑块
        slider = QSlider(Qt.Horizontal)
        slider.setRange(0, 100)
        slider.setValue(30)
        group_layout.addWidget(QLabel("滑块控制:"))
        group_layout.addWidget(slider)

        # 进度条
        progress_bar = QProgressBar()
        progress_bar.setValue(60)

        # 应用绿色样式
        self._apply_progress_bar_style(progress_bar)

        group_layout.addWidget(QLabel("进度显示:"))
        group_layout.addWidget(progress_bar)

        layout.addWidget(group_box)

        tab_widget.addTab(tab, "基本控件")

    def _create_table_demo_tab(self, tab_widget):
        """创建表格演示标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        layout.addWidget(QLabel("数据表格演示:"))

        # 创建表格
        table = QTableWidget(20, 5)
        table.setHorizontalHeaderLabels(["列1", "列2", "列3", "列4", "列5"])

        # 填充表格数据
        for row in range(20):
            for col in range(5):
                item = QTableWidgetItem(f"数据 {row+1}-{col+1}")
                table.setItem(row, col, item)

        layout.addWidget(table)

        tab_widget.addTab(tab, "表格演示")

    def _create_many_controls_tab(self, tab_widget):
        """创建大量控件标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        layout.addWidget(QLabel("大量控件演示 (测试滚动功能):"))

        # 创建多个控件组
        for i in range(10):
            group_box = QGroupBox(f"控件组 {i+1}")
            group_layout = QVBoxLayout(group_box)

            # 每个组内添加多个控件
            for j in range(5):
                button = QPushButton(f"按钮 {i+1}-{j+1}")
                button.clicked.connect(lambda checked, x=i+1, y=j+1: self._on_button_clicked(x, y))
                group_layout.addWidget(button)

            layout.addWidget(group_box)

        tab_widget.addTab(tab, "大量控件")

    def _create_bottom_buttons(self, layout):
        """创建底部按钮区域"""
        button_layout = QHBoxLayout()

        # 系统信息按钮
        self.info_button = QPushButton("显示系统信息")
        self.info_button.clicked.connect(self._show_system_info)
        button_layout.addWidget(self.info_button)

        # 测试按钮
        self.test_button = QPushButton("测试功能")
        self.test_button.clicked.connect(self._test_function)
        button_layout.addWidget(self.test_button)

        # 清空按钮
        self.clear_button = QPushButton("清空文本")
        self.clear_button.clicked.connect(self._clear_text)
        button_layout.addWidget(self.clear_button)

        # 关闭按钮
        self.close_button = QPushButton("关闭窗口")
        self.close_button.clicked.connect(self.close)
        button_layout.addWidget(self.close_button)

        layout.addLayout(button_layout)

    def _on_button_clicked(self, group_num, button_num):
        """处理按钮点击事件"""
        if hasattr(self, 'text_edit') and self.text_edit:
            current_text = self.text_edit.toPlainText()
            new_text = f"{current_text}\n点击了控件组 {group_num} 的按钮 {button_num}"
            self.text_edit.setPlainText(new_text)

        logger.info(f"点击了控件组 {group_num} 的按钮 {button_num}")

    def _clear_text(self):
        """清空文本"""
        if hasattr(self, 'text_edit') and self.text_edit:
            self.text_edit.clear()
        logger.info("清空文本")

    def _show_system_info(self):
        """显示系统信息"""
        import platform
        import sys

        info = f"""系统信息：
操作系统: {platform.system()} {platform.release()}
Python版本: {sys.version}
架构: {platform.architecture()[0]}
处理器: {platform.processor()}
窗口尺寸: {self.size().width()} x {self.size().height()}
滚动区域: {'已启用' if hasattr(self, '_scroll_area') else '未启用'}
"""

        # 更新文本框内容
        if hasattr(self, 'text_edit') and self.text_edit:
            self.text_edit.setPlainText(info)

        logger.info("显示系统信息")

    def _test_function(self):
        """测试功能"""
        if hasattr(self, 'text_edit') and self.text_edit:
            current_text = self.text_edit.toPlainText()
            self.text_edit.setPlainText(current_text + "\n\n测试功能已执行！滚动功能正常工作。")

        logger.info("执行测试功能")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        self.window_closed.emit()
        super().closeEvent(event)


class ExampleToolPlugin(IToolWindowPlugin):
    """示例工具插件"""
    
    @property
    def name(self) -> str:
        return "示例工具"
    
    @property
    def version(self) -> str:
        return "1.0.0"
    
    @property
    def description(self) -> str:
        return "一个演示插件系统的示例工具"
    
    @property
    def menu_text(self) -> str:
        return "示例工具"
    
    @property
    def icon_path(self) -> str:
        return None  # 没有图标
    
    def __init__(self):
        """初始化插件"""
        self.context = None
        self.window_instance = None
        
    def initialize(self, context):
        """初始化插件
        
        Args:
            context: 应用程序上下文（通常是主窗口）
        """
        self.context = context
        logger.info(f"插件 '{self.name}' 初始化完成")
    
    def create_window(self, parent=None):
        """创建工具窗口
        
        Args:
            parent: 父窗口
            
        Returns:
            工具窗口实例
        """
        if self.window_instance is None:
            self.window_instance = ExampleToolWindow(parent)
            self.window_instance.window_closed.connect(self._on_window_closed)
            logger.info(f"创建 '{self.name}' 工具窗口")
        
        return self.window_instance
    
    def _on_window_closed(self):
        """窗口关闭处理"""
        self.window_instance = None
        logger.info(f"'{self.name}' 工具窗口已关闭")
    
    def cleanup(self):
        """清理插件资源"""
        if self.window_instance:
            self.window_instance.close()
            self.window_instance = None
        
        logger.info(f"插件 '{self.name}' 已清理")


# 插件入口点
# 插件管理器会自动发现这个类
plugin_class = ExampleToolPlugin
