#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
坐标修复测试脚本
验证拖拽停靠功能的坐标系统修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QLabel, QPushButton
from PyQt5.QtCore import Qt, QPoint
from PyQt5.QtGui import QCursor
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_coordinate_systems():
    """测试坐标系统"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("坐标系统测试")
    main_window.setGeometry(200, 200, 800, 600)
    main_window.show()
    
    # 等待窗口显示
    app.processEvents()
    
    logger.info("=== 坐标系统测试 ===")
    
    # 获取窗口几何信息
    frame_geometry = main_window.frameGeometry()
    geometry = main_window.geometry()
    
    logger.info(f"frameGeometry: {frame_geometry}")
    logger.info(f"geometry: {geometry}")
    
    # 测试原始的错误方法
    logger.info("=== 原始方法（错误）===")
    if not geometry.isEmpty():
        main_global_pos_wrong = main_window.mapToGlobal(geometry.topLeft())
        logger.info(f"mapToGlobal(geometry.topLeft()): {main_global_pos_wrong}")
    
    # 测试修复后的方法
    logger.info("=== 修复后的方法（正确）===")
    main_global_pos_correct = main_window.mapToGlobal(QPoint(0, 0))
    logger.info(f"mapToGlobal(QPoint(0, 0)): {main_global_pos_correct}")
    
    # 测试停靠区域计算
    logger.info("=== 停靠区域计算测试 ===")
    
    # 使用frameGeometry（推荐方法）
    if not frame_geometry.isEmpty():
        main_geometry = frame_geometry
        logger.info("使用frameGeometry")
    else:
        main_geometry = geometry
        main_geometry.moveTopLeft(main_global_pos_correct)
        logger.info("使用修复后的geometry")
    
    logger.info(f"最终使用的主窗口几何: {main_geometry}")
    
    # 计算停靠区域
    dock_area_height = int(main_geometry.height() * 0.3)
    dock_area_top = main_geometry.top() + main_geometry.height() - dock_area_height
    dock_area_bottom = main_geometry.bottom()
    
    margin = 10
    dock_area_left = main_geometry.left() + margin
    dock_area_right = main_geometry.right() - margin
    dock_area_top += margin
    dock_area_bottom -= margin
    
    logger.info(f"停靠区域: X({dock_area_left}-{dock_area_right}), Y({dock_area_top}-{dock_area_bottom})")
    
    # 测试当前鼠标位置
    current_mouse_pos = QCursor.pos()
    logger.info(f"当前鼠标位置: {current_mouse_pos}")
    
    is_in_dock_area = (current_mouse_pos.x() >= dock_area_left and
                      current_mouse_pos.x() <= dock_area_right and
                      current_mouse_pos.y() >= dock_area_top and
                      current_mouse_pos.y() <= dock_area_bottom)
    
    logger.info(f"当前鼠标是否在停靠区域: {is_in_dock_area}")
    
    # 生成停靠区域中心点进行测试
    dock_center = QPoint(
        dock_area_left + (dock_area_right - dock_area_left) // 2,
        dock_area_top + (dock_area_bottom - dock_area_top) // 2
    )
    
    logger.info(f"停靠区域中心点: {dock_center}")
    
    is_center_in_dock = (dock_center.x() >= dock_area_left and
                         dock_center.x() <= dock_area_right and
                         dock_center.y() >= dock_area_top and
                         dock_center.y() <= dock_area_bottom)
    
    logger.info(f"停靠区域中心点是否在停靠区域: {is_center_in_dock}")
    
    logger.info("=== 测试完成 ===")
    logger.info("如果停靠区域中心点在停靠区域内，说明坐标计算正确")
    
    # 保持窗口打开一段时间以便观察
    import time
    time.sleep(2)
    
    app.quit()

if __name__ == "__main__":
    test_coordinate_systems()
