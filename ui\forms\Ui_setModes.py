# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'setModes.ui'
#
# Created by: PyQt5 UI code generator 5.15.10
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets
from ..resources import setModes_rc


class Ui_setModes(object):
    def setupUi(self, setModes):
        setModes.setObjectName("setModes")
        setModes.resize(669, 324)
        self.label = QtWidgets.QLabel(setModes)
        self.label.setGeometry(QtCore.QRect(-30, -40, 719, 379))
        self.label.setText("")
        self.label.setPixmap(QtGui.QPixmap(":/setModes/setModes.bmp"))
        self.label.setScaledContents(True)
        self.label.setObjectName("label")
        self.pBtSetDualLoop = QtWidgets.QPushButton(setModes)
        self.pBtSetDualLoop.setGeometry(QtCore.QRect(28, 91, 291, 40))
        self.pBtSetDualLoop.setObjectName("pBtSetDualLoop")
        self.pBtSetSingleLoop = QtWidgets.QPushButton(setModes)
        self.pBtSetSingleLoop.setGeometry(QtCore.QRect(333, 91, 281, 40))
        self.pBtSetSingleLoop.setObjectName("pBtSetSingleLoop")
        self.pBtSetSingleLoop0Dealy = QtWidgets.QPushButton(setModes)
        self.pBtSetSingleLoop0Dealy.setGeometry(QtCore.QRect(333, 142, 281, 40))
        self.pBtSetSingleLoop0Dealy.setObjectName("pBtSetSingleLoop0Dealy")
        self.pBtSetDualLoop0DealyCascaded = QtWidgets.QPushButton(setModes)
        self.pBtSetDualLoop0DealyCascaded.setGeometry(QtCore.QRect(29, 142, 291, 40))
        self.pBtSetDualLoop0DealyCascaded.setObjectName("pBtSetDualLoop0DealyCascaded")
        self.pBtSetDualLoop0DealyNested = QtWidgets.QPushButton(setModes)
        self.pBtSetDualLoop0DealyNested.setGeometry(QtCore.QRect(29, 194, 291, 40))
        self.pBtSetDualLoop0DealyNested.setObjectName("pBtSetDualLoop0DealyNested")
        self.pBtSetDualLoop0DealyNestedandCasc = QtWidgets.QPushButton(setModes)
        self.pBtSetDualLoop0DealyNestedandCasc.setGeometry(QtCore.QRect(29, 245, 381, 40))
        self.pBtSetDualLoop0DealyNestedandCasc.setObjectName("pBtSetDualLoop0DealyNestedandCasc")
        self.pBtSetDistributionFin1 = QtWidgets.QPushButton(setModes)
        self.pBtSetDistributionFin1.setGeometry(QtCore.QRect(333, 194, 281, 41))
        self.pBtSetDistributionFin1.setObjectName("pBtSetDistributionFin1")

        self.retranslateUi(setModes)
        QtCore.QMetaObject.connectSlotsByName(setModes)

    def retranslateUi(self, setModes):
        _translate = QtCore.QCoreApplication.translate
        setModes.setWindowTitle(_translate("setModes", "Form"))
        self.pBtSetDualLoop.setText(_translate("setModes", "Set Dual Loop"))
        self.pBtSetSingleLoop.setText(_translate("setModes", "Set Single Loop"))
        self.pBtSetSingleLoop0Dealy.setText(_translate("setModes", "Set Single Loop 0-Delay"))
        self.pBtSetDualLoop0DealyCascaded.setText(_translate("setModes", "Set Dual Loop 0-Delay Cascaded"))
        self.pBtSetDualLoop0DealyNested.setText(_translate("setModes", "Set Dual Loop 0-Delay Nested"))
        self.pBtSetDualLoop0DealyNestedandCasc.setText(_translate("setModes", "Set Dual Loop 0-Delay Nested+Casc"))
        self.pBtSetDistributionFin1.setText(_translate("setModes", "Set Distribution Fin1"))


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    setModes = QtWidgets.QWidget()
    ui = Ui_setModes()
    ui.setupUi(setModes)
    setModes.show()
    sys.exit(app.exec_())
