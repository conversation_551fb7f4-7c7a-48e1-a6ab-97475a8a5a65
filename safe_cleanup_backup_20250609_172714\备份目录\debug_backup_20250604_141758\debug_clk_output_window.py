#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试时钟输出窗口显示空白的问题
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.Log import logger


def debug_clk_output_creation():
    """调试时钟输出窗口创建过程"""
    print("=" * 60)
    print("调试时钟输出窗口创建过程")
    print("=" * 60)
    
    # 创建应用程序
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    
    try:
        # 1. 测试UI文件是否存在
        print("\n1. 检查UI文件...")
        try:
            from ui.forms.Ui_ClkOutputs import Ui_ClkOutputs
            print("   ✓ Ui_ClkOutputs 导入成功")
            
            # 创建UI实例测试
            ui = Ui_ClkOutputs()
            print("   ✓ Ui_ClkOutputs 实例创建成功")
            
        except Exception as e:
            print(f"   ❌ UI文件问题: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
        # 2. 测试现代化处理器创建
        print("\n2. 测试现代化处理器创建...")
        try:
            from core.services.spi.spi_service_impl import SPIServiceImpl
            from core.repositories.register_repository import RegisterRepository
            from core.managers.RegisterManager import RegisterManager
            from ui.handlers.ModernClkOutputsHandler import ModernClkOutputsHandler
            
            # 创建必要的依赖
            spi_service = SPIServiceImpl()
            spi_service.initialize()
            register_repo = RegisterRepository(spi_service)
            
            # 加载寄存器配置
            import json
            with open('config/registers.json', 'r', encoding='utf-8') as f:
                registers_config = json.load(f)
            
            register_manager = RegisterManager(registers_config)
            print(f"   ✓ RegisterManager 创建成功，包含 {len(register_manager.register_objects)} 个寄存器")
            
            # 创建现代化处理器
            modern_handler = ModernClkOutputsHandler(None, register_manager)
            print("   ✓ ModernClkOutputsHandler 创建成功")
            
            # 检查UI是否正确设置
            if hasattr(modern_handler, 'ui') and modern_handler.ui:
                print("   ✓ UI 已正确设置")
                
                # 检查content_widget
                if hasattr(modern_handler, 'content_widget') and modern_handler.content_widget:
                    print("   ✓ content_widget 存在")
                    print(f"   ✓ content_widget 类型: {type(modern_handler.content_widget)}")
                    
                    # 检查content_widget的子控件
                    children = modern_handler.content_widget.children()
                    print(f"   ✓ content_widget 有 {len(children)} 个子控件")
                    
                    # 检查UI是否有内容
                    if hasattr(modern_handler.ui, 'lineEditFvco'):
                        print("   ✓ lineEditFvco 控件存在")
                    else:
                        print("   ❌ lineEditFvco 控件不存在")
                        
                    # 检查一些关键控件
                    key_widgets = ['lineEditFout0Output', 'DCLK0_1DIV', 'CLKout0_SRCMUX']
                    for widget_name in key_widgets:
                        if hasattr(modern_handler.ui, widget_name):
                            print(f"   ✓ {widget_name} 控件存在")
                        else:
                            print(f"   ❌ {widget_name} 控件不存在")
                            
                else:
                    print("   ❌ content_widget 不存在")
                    
            else:
                print("   ❌ UI 未正确设置")
                
            # 检查控件映射
            if hasattr(modern_handler, 'widget_register_map'):
                print(f"   ✓ 控件映射包含 {len(modern_handler.widget_register_map)} 个映射")
            else:
                print("   ❌ 控件映射不存在")
                
            # 显示窗口测试
            print("\n3. 测试窗口显示...")
            modern_handler.show()
            print("   ✓ 窗口显示命令执行")
            
            # 检查窗口大小
            size = modern_handler.size()
            print(f"   ✓ 窗口大小: {size.width()} x {size.height()}")
            
            # 检查窗口是否可见
            if modern_handler.isVisible():
                print("   ✓ 窗口可见")
            else:
                print("   ❌ 窗口不可见")
                
            return True
            
        except Exception as e:
            print(f"   ❌ 现代化处理器创建失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"调试过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def debug_factory_creation():
    """调试工厂创建过程"""
    print("\n" + "=" * 60)
    print("调试工厂创建过程")
    print("=" * 60)
    
    try:
        # 创建模拟主窗口
        from core.services.spi.spi_service_impl import SPIServiceImpl
        from core.repositories.register_repository import RegisterRepository
        from ui.windows.RegisterMainWindow import RegisterMainWindow
        
        # 创建SPI服务
        spi_service = SPIServiceImpl()
        spi_service.initialize()
        register_repo = RegisterRepository(spi_service)
        
        # 创建主窗口
        main_window = RegisterMainWindow(register_repo)
        print("   ✓ 主窗口创建成功")
        
        # 测试工厂创建
        factory = main_window.tool_window_factory
        print("   ✓ 工具窗口工厂获取成功")
        
        # 检查现代化工厂状态
        if factory.use_modern_factory:
            print("   ✓ 正在使用现代化工厂")
            
            if factory.modern_factory:
                print("   ✓ 现代化工厂实例存在")
                
                # 检查配置
                config = factory.modern_factory.HANDLER_CONFIGS.get('clk_output')
                if config:
                    print(f"   ✓ 时钟输出配置: {config}")
                else:
                    print("   ❌ 时钟输出配置缺失")
                    
            else:
                print("   ❌ 现代化工厂实例不存在")
        else:
            print("   ❌ 未使用现代化工厂")
        
        # 尝试创建时钟输出窗口
        print("\n测试工厂创建时钟输出窗口...")
        try:
            clk_window = factory.create_clk_output_window()
            if clk_window:
                print("   ✓ 工厂创建时钟输出窗口成功")
                print(f"   ✓ 窗口类型: {type(clk_window).__name__}")
                
                # 检查窗口内容
                if hasattr(clk_window, 'ui') and clk_window.ui:
                    print("   ✓ 窗口有UI内容")
                else:
                    print("   ❌ 窗口没有UI内容")
                    
                return True
            else:
                print("   ❌ 工厂创建时钟输出窗口失败，返回None")
                return False
                
        except Exception as e:
            print(f"   ❌ 工厂创建窗口时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"工厂调试过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    success1 = debug_clk_output_creation()
    success2 = debug_factory_creation()
    
    print("\n" + "=" * 60)
    print("调试结果汇总")
    print("=" * 60)
    
    if success1:
        print("✅ 现代化处理器创建正常")
    else:
        print("❌ 现代化处理器创建有问题")
        
    if success2:
        print("✅ 工厂创建过程正常")
    else:
        print("❌ 工厂创建过程有问题")
        
    if success1 and success2:
        print("\n🎯 结论: 代码层面没有问题，可能是UI布局或显示问题")
        print("建议检查:")
        print("1. UI文件是否正确编译")
        print("2. 窗口布局是否正确")
        print("3. 样式表是否影响显示")
    else:
        print("\n⚠️ 结论: 发现代码问题，需要修复")


if __name__ == '__main__':
    main()
