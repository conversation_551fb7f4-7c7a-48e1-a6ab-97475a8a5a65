# 移除PLL2NDivider自动调整功能说明

## 概述

根据用户反馈，PLL2NDivider的自动调整功能在很多情况下计算的值不符合要求，用户更希望自己决定使用什么值。因此，我们移除了PLL2NDivider的自动调整功能，简化了SYSREF频率处理逻辑。

## 移除的功能

### 1. PLL2NDivider自动调整机制
- **移除条件检查**：不再检查`FBMuxEn`、`FBMUX=SYSREF DIVIDER`、`PLL2NclkMux=feedback mux`的组合条件
- **移除自动计算**：不再根据`PLL2Cin × PLL2NDivider = PLL2PFDFreq`公式自动调整PLL2NDivider值
- **移除信号连接**：不再监听SYSREF频率更新信号进行自动调整

### 2. SYSREF频率缓存机制
- **移除事件总线缓存**：不再在RegisterUpdateBus中缓存SYSREF频率
- **移除信号传递**：不再发送`sysref_freq_updated`信号
- **移除跨窗口同步**：不再自动同步SYSREF频率到PLL窗口

## 修改的文件和内容

### 1. ModernPLLHandler.py
#### 移除的方法：
- `_on_sysref_freq_updated()` - SYSREF频率更新处理
- `_should_auto_adjust_pll2_ndivider()` - 自动调整条件检查
- `_auto_adjust_pll2_ndivider_for_sysref()` - 自动调整PLL2NDivider

#### 修改的方法：
- `_connect_special_signals()` - 移除SYSREF频率更新信号连接
- `_init_pll2cin_value()` - 简化初始化逻辑，移除缓存获取
- `_get_sysref_frequency()` - 简化为仅从同步系统参考窗口获取

### 2. RegisterUpdateBus.py
#### 移除的内容：
- `sysref_freq_updated` 信号定义
- `sysref_freq` 缓存字段
- `cache_sysref_freq()` 方法
- `get_cached_sysref_freq()` 方法

### 3. ModernSyncSysRefHandler.py
#### 移除的内容：
- `_cache_sysref_frequency()` 方法
- SYSREF频率缓存调用

## 保留的功能

### 1. PLL2Cin显示功能
- **保留显示逻辑**：PLL2Cin仍然根据FBMUX设置显示相应的频率值
- **保留更新机制**：当FBMUX改变时，PLL2Cin会更新显示值
- **保留SYSREF获取**：当FBMUX=SYSREF时，仍然从同步系统参考窗口获取SYSREF频率

### 2. 手动控制
- **用户完全控制**：用户可以手动设置PLL2NDivider的任何合法值
- **无自动干预**：系统不会自动修改用户设置的PLL2NDivider值
- **灵活配置**：用户可以根据实际需求选择最合适的分频比

### 3. 频率计算
- **VCODistFreq计算**：仍然根据PLL2PFDFreq、PLL2NDivider和相关参数计算VCODistFreq
- **SYSREF频率计算**：同步系统参考窗口仍然正常计算和显示SYSREF频率
- **PLL2Cin更新**：仍然根据FBMUX设置更新PLL2Cin显示值

## 用户体验改进

### 1. 更可预测的行为
- **无意外变化**：PLL2NDivider值不会被系统自动修改
- **用户主导**：所有参数变化都由用户明确操作触发
- **稳定配置**：用户设置的值会保持稳定，不受其他参数变化影响

### 2. 简化的操作流程
- **直接设置**：用户直接设置所需的PLL2NDivider值
- **即时生效**：设置后立即重新计算相关频率
- **清晰反馈**：所有计算结果直接反映用户的设置

### 3. 避免计算冲突
- **无复杂逻辑**：移除了复杂的自动调整条件判断
- **无数值冲突**：避免了自动计算值与用户期望值的冲突
- **无异常行为**：消除了256倍差异等计算异常问题

## 使用方法

### 1. 设置PLL2NDivider
```
1. 打开PLL窗口
2. 手动设置PLL2NDivider控件的值
3. 系统自动重新计算VCODistFreq等相关频率
4. 查看计算结果是否符合要求
```

### 2. 查看PLL2Cin值
```
1. 确保FBMuxEn复选框选中
2. 设置FBMUX为所需的反馈源
3. PLL2Cin会显示相应的频率值
4. 该值仅用于显示，不会自动影响其他参数
```

### 3. 配置SYSREF频率
```
1. 打开同步系统参考窗口
2. 设置VCO频率和SYSREF分频比
3. 查看计算出的SYSREF频率
4. 如需在PLL窗口中使用，手动设置相应参数
```

## 优势总结

### 1. 用户控制权
- **完全自主**：用户对所有参数有完全控制权
- **灵活配置**：可以根据具体应用需求进行精确配置
- **专业友好**：符合专业用户的使用习惯

### 2. 系统稳定性
- **减少复杂性**：移除了复杂的自动调整逻辑
- **提高可靠性**：避免了自动计算可能引入的错误
- **简化维护**：代码更简洁，更容易维护和调试

### 3. 问题解决
- **避免SYSREF问题**：不再需要处理复杂的SYSREF频率同步问题
- **消除计算异常**：避免了256倍差异等计算错误
- **提高用户满意度**：用户可以获得预期的、可控的行为

通过这些改进，系统变得更加简单、可靠和用户友好，同时避免了之前存在的SYSREF频率计算问题。
