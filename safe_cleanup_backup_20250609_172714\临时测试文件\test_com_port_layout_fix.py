#!/usr/bin/env python3
"""
测试COM端口布局修复
验证COM端口下拉框、刷新按钮和地址输入框的布局改进
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout
from PyQt5.QtCore import QTimer

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.Log import get_module_logger

logger = get_module_logger(__name__)


def test_com_port_layout():
    """测试COM端口布局改进"""
    try:
        print("=" * 60)
        print("测试COM端口布局改进")
        print("=" * 60)
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建测试主窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("COM端口布局测试")
        main_window.resize(1200, 800)
        
        # 创建中央控件
        central_widget = QWidget()
        main_window.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        print("1. 创建现代化RegisterIOHandler...")
        
        # 创建现代化RegisterIOHandler
        from ui.handlers.ModernRegisterIOHandler import ModernRegisterIOHandler
        io_handler = ModernRegisterIOHandler.create_for_testing(main_window)
        
        # 获取IO控件并添加到布局
        io_widget = io_handler.get_io_widget()
        layout.addWidget(io_widget)
        
        print("   ✓ 现代化RegisterIOHandler创建成功")
        
        print("2. 测试端口列表更新...")
        
        # 模拟端口列表
        test_ports = [
            {'device': 'COM3', 'description': 'USB Serial Port (COM3) - 这是一个很长的设备描述信息用于测试显示效果'},
            {'device': 'COM4', 'description': 'Bluetooth Serial Port (COM4) - 蓝牙串口设备'},
            {'device': 'COM5', 'description': 'Virtual Serial Port (COM5) - 虚拟串口设备用于调试'}
        ]
        
        # 更新端口列表
        io_handler._update_port_combo(test_ports)
        
        print("   ✓ 端口列表更新完成")
        
        print("3. 检查布局改进...")
        
        # 检查COM端口下拉框宽度
        port_combo = io_handler.port_combo
        if port_combo:
            min_width = port_combo.minimumWidth()
            max_width = port_combo.maximumWidth()
            print(f"   COM端口下拉框宽度: 最小={min_width}, 最大={max_width}")
            
            # 检查端口项目内容
            for i in range(port_combo.count()):
                item_text = port_combo.itemText(i)
                print(f"   端口项目 {i}: {item_text}")
        
        # 检查地址和值输入框宽度
        addr_edit = io_handler.addr_line_edit
        value_edit = io_handler.value_line_edit
        
        if addr_edit:
            addr_width = addr_edit.width()
            print(f"   地址输入框宽度: {addr_width}")
        
        if value_edit:
            value_width = value_edit.width()
            print(f"   值输入框宽度: {value_width}")
        
        print("4. 显示测试窗口...")
        
        # 显示窗口
        main_window.show()
        
        # 设置定时器自动关闭（用于自动化测试）
        def auto_close():
            print("   ✓ 布局测试完成，自动关闭窗口")
            main_window.close()
            app.quit()
        
        timer = QTimer()
        timer.timeout.connect(auto_close)
        timer.start(5000)  # 5秒后自动关闭
        
        print("   ✓ 测试窗口已显示，将在5秒后自动关闭")
        print("   请检查以下改进是否生效：")
        print("   - COM端口下拉框是否足够宽，能完整显示端口信息")
        print("   - 刷新按钮和Address标签之间是否有适当间隔")
        print("   - 地址和值输入框是否更宽，位置更居中")
        print("   - 整体布局是否向右移动了一些")
        
        # 运行应用程序
        return app.exec_()
        
    except Exception as e:
        logger.error(f"测试COM端口布局时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_layout_measurements():
    """测试布局尺寸测量"""
    try:
        print("\n" + "=" * 60)
        print("测试布局尺寸测量")
        print("=" * 60)
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建现代化RegisterIOHandler
        from ui.handlers.ModernRegisterIOHandler import ModernRegisterIOHandler
        io_handler = ModernRegisterIOHandler.create_for_testing()
        
        # 获取控件尺寸
        measurements = {}
        
        if hasattr(io_handler, 'port_combo') and io_handler.port_combo:
            combo = io_handler.port_combo
            measurements['port_combo'] = {
                'min_width': combo.minimumWidth(),
                'max_width': combo.maximumWidth(),
                'current_width': combo.width()
            }
        
        if hasattr(io_handler, 'addr_line_edit') and io_handler.addr_line_edit:
            addr_edit = io_handler.addr_line_edit
            measurements['addr_edit'] = {
                'width': addr_edit.width(),
                'fixed_width': addr_edit.minimumWidth()
            }
        
        if hasattr(io_handler, 'value_line_edit') and io_handler.value_line_edit:
            value_edit = io_handler.value_line_edit
            measurements['value_edit'] = {
                'width': value_edit.width(),
                'fixed_width': value_edit.minimumWidth()
            }
        
        print("布局尺寸测量结果:")
        for widget_name, sizes in measurements.items():
            print(f"  {widget_name}:")
            for size_name, size_value in sizes.items():
                print(f"    {size_name}: {size_value}")
        
        print("   ✓ 布局尺寸测量完成")
        
        return True
        
    except Exception as e:
        logger.error(f"测试布局尺寸时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("开始COM端口布局改进测试...")
    
    # 测试布局改进
    success1 = test_com_port_layout()
    
    # 测试布局尺寸
    success2 = test_layout_measurements()
    
    if success1 and success2:
        print("\n✅ 所有测试通过！")
        print("\n🎯 最新布局改进总结:")
        print("1. COM端口下拉框最小宽度: 200 -> 600像素 (大幅增加)")
        print("2. COM端口下拉框最大宽度: 400 -> 800像素 (大幅增加)")
        print("3. COM端口下拉框大小策略: 设置为可扩展")
        print("4. 端口描述显示长度: 30/50 -> 80字符")
        print("5. 刷新按钮和Address标签间隔: 15 -> 25像素")
        print("6. 地址输入框宽度: 80/120 -> 160像素")
        print("7. 值输入框宽度: 120/160 -> 200像素")
        print("8. 添加了20像素的左侧间隔，整体右移")
        print("9. 地址和值输入框之间间隔: 15 -> 20像素")
        print("10. 增加了弹性空间，让控件更居中")
        print("11. 添加了样式美化，输入框有边框和颜色")
        print("12. 标签文字加粗，提升视觉效果")
        print("13. 修复了初始化管理器，确保使用现代化处理器")
        print("14. 同时修改了传统和现代化处理器，确保兼容性")
    else:
        print("\n❌ 部分测试失败，请检查错误信息")
    
    print("\n测试完成。")
