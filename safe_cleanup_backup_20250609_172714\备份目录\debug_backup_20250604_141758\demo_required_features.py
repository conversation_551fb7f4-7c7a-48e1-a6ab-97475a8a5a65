#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
必需功能演示脚本
实际演示您要求的9个核心功能
"""

import sys
import os
import time
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def print_demo_header(title):
    """打印演示标题"""
    print(f"\n{'='*60}")
    print(f"🎯 {title}")
    print(f"{'='*60}")

def demo_feature_1_register_read_write():
    """演示功能1: 读写寄存器"""
    print_demo_header("功能1: 读写寄存器演示")
    
    try:
        from core.services.register.RegisterOperationService import RegisterOperationService
        from core.services.spi.spi_service_impl import SPIServiceImpl
        
        # 创建SPI服务
        spi_service = SPIServiceImpl()
        if spi_service.initialize():
            print("✅ SPI服务初始化成功")
            
            # 创建寄存器操作服务
            register_service = RegisterOperationService(spi_service)
            
            # 测试写入寄存器
            test_addr = "0x50"
            test_value = 0x1234
            
            print(f"📝 写入寄存器 {test_addr} = {hex(test_value)}")
            write_success = register_service.write_register(test_addr, test_value)
            
            if write_success:
                print("✅ 寄存器写入成功")
                
                # 测试读取寄存器
                print(f"📖 读取寄存器 {test_addr}")
                read_value = register_service.read_register(test_addr)
                
                print(f"📊 读取结果: {hex(read_value)}")
                
                if read_value == test_value:
                    print("✅ 读写一致性验证通过")
                    return True
                else:
                    print(f"❌ 读写不一致: 写入{hex(test_value)}, 读取{hex(read_value)}")
                    return False
            else:
                print("❌ 寄存器写入失败")
                return False
        else:
            print("❌ SPI服务初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ 功能1演示失败: {e}")
        return False

def demo_feature_2_read_write_all():
    """演示功能2: 读写所有寄存器"""
    print_demo_header("功能2: 读写所有寄存器演示")
    
    try:
        from ui.controllers.BatchOperationController import BatchOperationController
        from core.services.spi.spi_service_impl import SPIServiceImpl
        from core.repositories.register_repository import RegisterRepository
        
        # 创建服务
        spi_service = SPIServiceImpl()
        if spi_service.initialize():
            register_repo = RegisterRepository(spi_service)
            
            # 创建模拟主窗口
            class MockMainWindow:
                def __init__(self):
                    self.register_repository = register_repo
                    self.register_service = spi_service
            
            main_window = MockMainWindow()
            batch_controller = BatchOperationController(main_window)
            
            print("📖 执行读取所有寄存器...")
            batch_controller.handle_read_all_requested()
            print("✅ 读取所有寄存器完成")
            
            print("📝 执行写入所有寄存器...")
            batch_controller.handle_write_all_requested()
            print("✅ 写入所有寄存器完成")
            
            return True
        else:
            print("❌ SPI服务初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ 功能2演示失败: {e}")
        return False

def demo_feature_3_dump():
    """演示功能3: dump功能"""
    print_demo_header("功能3: dump功能演示")
    
    try:
        from ui.managers.UIUtilityManager import UIUtilityManager
        from PyQt5.QtWidgets import QApplication
        
        # 创建Qt应用
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建UI工具管理器
        ui_manager = UIUtilityManager(None)
        
        print("📊 创建寄存器dump表格...")
        ui_manager.show_register_dump()
        print("✅ dump功能演示完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 功能3演示失败: {e}")
        return False

def demo_feature_4_5_save_load():
    """演示功能4&5: save和load配置文件功能"""
    print_demo_header("功能4&5: save和load配置文件功能演示")
    
    try:
        from core.services.config.ConfigurationService import ConfigurationService
        
        # 创建配置服务
        config_service = ConfigurationService(None)
        
        # 测试配置数据
        test_config = {
            "0x50": 0x1234,
            "0x51": 0x5678,
            "0x52": 0x9ABC
        }
        
        config_file = "demo_config.json"
        
        print(f"💾 保存配置到文件: {config_file}")
        save_success = config_service.save_register_config(test_config, config_file)
        
        if save_success:
            print("✅ 配置保存成功")
            
            print(f"📂 从文件加载配置: {config_file}")
            loaded_config = config_service.load_register_config(config_file)
            
            if loaded_config:
                print("✅ 配置加载成功")
                print(f"📊 加载的配置: {loaded_config}")
                
                # 验证配置一致性
                if loaded_config == test_config:
                    print("✅ 配置一致性验证通过")
                    return True
                else:
                    print("❌ 配置不一致")
                    return False
            else:
                print("❌ 配置加载失败")
                return False
        else:
            print("❌ 配置保存失败")
            return False
            
    except Exception as e:
        print(f"❌ 功能4&5演示失败: {e}")
        return False

def demo_feature_6_tool_windows():
    """演示功能6: 工具窗口打开"""
    print_demo_header("功能6: 工具窗口打开演示")
    
    try:
        from ui.factories.ModernToolWindowFactory import ModernToolWindowFactory
        from PyQt5.QtWidgets import QApplication
        
        # 创建Qt应用
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建模拟主窗口
        class MockMainWindow:
            def __init__(self):
                self.register_manager = None
        
        main_window = MockMainWindow()
        factory = ModernToolWindowFactory(main_window)
        
        # 测试创建各种工具窗口
        window_types = ['pll_control', 'clk_outputs', 'sync_sysref']
        created_windows = []
        
        for window_type in window_types:
            print(f"🪟 创建工具窗口: {window_type}")
            try:
                window = factory.create_window_by_type(window_type)
                if window:
                    created_windows.append(window_type)
                    print(f"✅ {window_type} 窗口创建成功")
                else:
                    print(f"❌ {window_type} 窗口创建失败")
            except Exception as e:
                print(f"❌ {window_type} 窗口创建出错: {e}")
        
        print(f"📊 成功创建 {len(created_windows)}/{len(window_types)} 个工具窗口")
        return len(created_windows) > 0
        
    except Exception as e:
        print(f"❌ 功能6演示失败: {e}")
        return False

def demo_feature_7_hardware_communication():
    """演示功能7: 模拟硬件通信"""
    print_demo_header("功能7: 模拟硬件通信演示")
    
    try:
        from core.services.spi.spi_service_impl import SPIServiceImpl
        
        # 创建SPI服务
        spi_service = SPIServiceImpl()
        
        print("🔌 初始化硬件通信...")
        if spi_service.initialize():
            print("✅ 硬件通信初始化成功")
            
            # 测试通信
            test_registers = [
                ("0x50", 0x1234),
                ("0x51", 0x5678),
                ("0x52", 0x9ABC)
            ]
            
            print("📡 测试硬件通信...")
            for addr, value in test_registers:
                print(f"  写入 {addr} = {hex(value)}")
                spi_service.write_register(addr, value)
                
                read_value = spi_service.read_register(addr)
                print(f"  读取 {addr} = {hex(read_value)}")
                
                if read_value == value:
                    print(f"  ✅ {addr} 通信正常")
                else:
                    print(f"  ❌ {addr} 通信异常")
            
            print("✅ 硬件通信演示完成")
            return True
        else:
            print("❌ 硬件通信初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ 功能7演示失败: {e}")
        return False

def demo_feature_8_9_widget_auto_write():
    """演示功能8&9: 控件状态修改和自动写入"""
    print_demo_header("功能8&9: 控件状态修改和自动写入演示")
    
    try:
        from ui.handlers.BaseHandler import BaseClockHandler
        from core.services.spi.spi_service_impl import SPIServiceImpl
        
        # 创建模拟主窗口
        class MockMainWindow:
            def __init__(self):
                self.auto_write_mode = True
                self.register_service = SPIServiceImpl()
                self.register_service.initialize()
        
        # 创建测试处理器
        class TestHandler(BaseClockHandler):
            def __init__(self, main_window):
                super().__init__()
                self.main_window = main_window
            
            def test_auto_write(self, addr, value):
                print(f"🔄 检查自动写入模式: {self._is_auto_write_enabled()}")
                if self._is_auto_write_enabled():
                    print(f"📝 自动写入寄存器 {addr} = {hex(value)}")
                    self._auto_write_register_to_chip(addr, value)
                    
                    # 读取验证
                    read_value = self.main_window.register_service.read_register(addr)
                    print(f"📖 读取验证 {addr} = {hex(read_value)}")
                    
                    return read_value == value
                return False
        
        main_window = MockMainWindow()
        test_handler = TestHandler(main_window)
        
        print("🎛️ 模拟控件状态修改...")
        test_addr = "0x50"
        test_value = 0x1234
        
        success = test_handler.test_auto_write(test_addr, test_value)
        
        if success:
            print("✅ 自动写入验证成功")
            return True
        else:
            print("❌ 自动写入验证失败")
            return False
            
    except Exception as e:
        print(f"❌ 功能8&9演示失败: {e}")
        return False

def main():
    """主函数"""
    print("🎪 必需功能演示")
    print("=" * 80)
    print(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\n🎯 演示您要求的9个核心功能:")
    
    # 演示各项功能
    demos = [
        ("1. 读写寄存器", demo_feature_1_register_read_write),
        ("2. 读写所有寄存器", demo_feature_2_read_write_all),
        ("3. dump功能", demo_feature_3_dump),
        ("4&5. save和load配置文件", demo_feature_4_5_save_load),
        ("6. 工具窗口打开", demo_feature_6_tool_windows),
        ("7. 模拟硬件通信", demo_feature_7_hardware_communication),
        ("8&9. 控件状态修改和自动写入", demo_feature_8_9_widget_auto_write)
    ]
    
    results = []
    for demo_name, demo_func in demos:
        print(f"\n🚀 开始演示: {demo_name}")
        try:
            result = demo_func()
            results.append((demo_name, result))
            if result:
                print(f"✅ {demo_name} 演示成功")
            else:
                print(f"❌ {demo_name} 演示失败")
        except Exception as e:
            print(f"💥 {demo_name} 演示出错: {e}")
            results.append((demo_name, False))
        
        time.sleep(1)  # 短暂暂停
    
    # 生成演示报告
    print(f"\n{'='*60}")
    print("📊 演示结果总结")
    print(f"{'='*60}")
    
    success_count = sum(1 for _, result in results if result)
    total_demos = len(results)
    
    print("演示结果:")
    for demo_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"  {demo_name}: {status}")
    
    success_rate = (success_count / total_demos * 100) if total_demos > 0 else 0
    
    print(f"\n📈 总体统计:")
    print(f"  成功演示: {success_count}/{total_demos}")
    print(f"  成功率: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print(f"\n🎉 优秀！您要求的功能演示大部分成功")
        print(f"💡 建议：可以正常使用这些功能")
    elif success_rate >= 60:
        print(f"\n👍 良好！多数功能演示成功")
        print(f"💡 建议：检查失败的功能")
    else:
        print(f"\n⚠️  需要改进！部分功能演示失败")
        print(f"💡 建议：优先修复失败的功能")
    
    return 0 if success_rate >= 70 else 1

if __name__ == "__main__":
    sys.exit(main())
